<?php

namespace app\services;

use Carbon\Carbon;
use Entities\AmsPackage;
use Entities\InvoiceDailyRevenue;
use Entities\ClientPayer;
use Entities\Itemable;
use Entities\ReportClientAmsJob;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use DB;
use Entities\Ams\Job;
use Entities\ClientAmsJob;
use Entities\ClientAmsSearchPackage;
use Entities\ClientAmsService;
use Entities\Item;

defined('BASEPATH') or exit('No direct script access allowed');

class ReportService
{
    public static function syncInvoiceItemsToReport(int $invoiceId)
    {
        $invoiceItems = Itemable::query()
            ->select(['id', 'rel_id', 'rel_type', 'qty', 'rate'])
            ->with(['invoice:id,clientid'])
            ->whereHas('invoice', fn($query) => $query->notDraft()->whereId($invoiceId))
            ->invoiceType()
            ->get();

        if ($invoiceItems->count()) {
            $reportItems = ReportClientAmsJob::where('invoice_id', $invoiceId)->get();
            $missingReportItems = $invoiceItems->filter(function ($item) use ($reportItems) {
                return intval($item->qty) > $reportItems->where('package_id', $item->id)->count();
            });
            $reduceReportItems = $invoiceItems->filter(function ($item) use ($reportItems) {
                return intval($item->qty) < $reportItems->where('package_id', $item->id)->count();
            });
            $deleteReportItems = $reportItems->whereNotIn('package_id', $invoiceItems->pluck('id'));

            // Add missing item from the invoice to report table
            if ($missingReportItems->count()) {
                $missingReportItems->each(function ($item) use ($reportItems) {
                    $missingNumber = intval($item->qty) - $reportItems->where('package_id', $item->id)->count();
                    $reportInsert = [];
                    for ($i = 0; $i < $missingNumber; $i++) {
                        $reportInsert[] = [
                            'client_id' => $item->invoice->clientid,
                            'ams_company_id' => 0,
                            'ams_job_id' => 0,
                            'invoice_id' => $item->rel_id,
                            'used_packages' => '{}',
                            'paid_package_id' => null,
                            'package_id' => $item->id,
                            'free_package' => $item->rate == 0,
                            'status_mapping' => 0,
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now(),
                        ];
                    }
                    ReportClientAmsJob::insert($reportInsert);
                });
            }

            // Remove reduce item from the report table, consider removing report item that is not mapped yet
            if ($reduceReportItems->count()) {
                $reduceReportItems->each(function ($item) use ($reportItems) {
                    $reduceNumber = $reportItems->where('package_id', $item->id)->count() - intval($item->qty);
                    $totalNotMappingYet = ReportClientAmsJob::where([
                        'package_id' => $item->id,
                        'ams_company_id' => 0,
                        'ams_job_id' => 0,
                    ])->count();
                    $reduceAdjustment = $totalNotMappingYet - $reduceNumber;
                    // If item not mapping yet available, just delete all un mapping items
                    if ($reduceAdjustment >= 0) {
                        ReportClientAmsJob::where([
                            'package_id' => $item->id,
                            'ams_company_id' => 0,
                            'ams_job_id' => 0,
                        ])->limit($reduceNumber)->delete();
                    } else { // Otherwise, delete un mapping first
                        ReportClientAmsJob::where([
                            'package_id' => $item->id,
                            'ams_company_id' => 0,
                            'ams_job_id' => 0,
                        ])->delete();

                        // Then remove mapping item
                        ReportClientAmsJob::where([
                            'package_id' => $item->id,
                        ])->orderByDesc('id')->limit(abs($reduceAdjustment))->delete();
                    }
                });
            }

            // Remove deleted item from the report table
            if ($deleteReportItems->count()) {
                ReportClientAmsJob::whereIn('id', $deleteReportItems->pluck('id'))->delete();
            }
        } else {
            ReportClientAmsJob::where('invoice_id', $invoiceId)->delete();
        }
    }

    protected static function setAutoResizeColumns(&$sheet, $start, $end)
    {
        // Auto-size columns (A, B, C in this case)
        foreach (range($start, $end) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }
    protected static function toExcelFile($type, $fileName, $spreadsheet)
    {
        header("Content-Description: $type Export");
        header("Content-Disposition: attachment; filename=$fileName");
        header('Content-Type: application/xlsx;');

        // Create a writer to output the spreadsheet to the browser
        $writer = new Xlsx($spreadsheet);

        // Save the file to the output stream (browser)
        $writer->save('php://output');
        exit();
    }

    public static function exportInvoiceMonthlyReportByIssueDate(string $issueMonth, $reportMonth = '', $isAdvancedAmount = 0, $isInvoiceAmount = 0)
    {
        $type = 'Invoice Monthly Report - ' . $issueMonth . ($reportMonth ? (' - ' . $reportMonth) : '');
        $fileName = 'Invoice-monthly-report-' . $issueMonth . ($reportMonth ? (' - ' . $reportMonth) : '');

        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Set the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->fromArray([
            [
                'Export Date',
                Carbon::now()->format('Y-m-d H:i:s')
            ]
        ], null, 'A1');

        [$invoices, $amsJobs, $credits, $invoicePaidItems] = fetch_monthly_report_by_month($issueMonth);

        // Clone deep to avoid model is modified after ran the get_total_amount_by_month function
        $paidItems = $invoicePaidItems->map(function ($item) {
            return $item->replicate();
        });

        [$reports, $invoiceQuantity, $invoiceAmount, $advancedAmount, $revenueAmount, $unpaidAmount] = get_total_amount_by_month(
            $issueMonth,
            $invoices,
            $paidItems,
            $amsJobs,
            $credits
        );
        $reports = collect($reports);


        $details = collect();
        $detailTitle = '';
        if ($reportMonth) {
            $detailTitle = ' - ' . ($isAdvancedAmount ? _l('monthly_invoice_report_detail_advanced_amount_title') : _l('monthly_invoice_report_detail_revenue_title'));
            if ($isInvoiceAmount) {
                $details = get_advanced_invoice_details_by_report_month(
                    $invoices,
                    $amsJobs,
                    $credits,
                    $invoicePaidItems
                );
                $firstOfIssueMonth = Carbon::createFromFormat('Y.m', $issueMonth)->startOfMonth()->startOfDay();
                $thisMonth = Carbon::now()->startOfMonth()->startOfDay();
                do {
                    $details = $details->merge(get_invoice_details_by_report_month(
                        $invoices,
                        $amsJobs,
                        $credits,
                        $invoicePaidItems,
                        $firstOfIssueMonth->format('Y.m')
                    ));
                    $firstOfIssueMonth = $firstOfIssueMonth->addMonth();
                } while ($firstOfIssueMonth->lte($thisMonth));
            } else {
                $details = $isAdvancedAmount
                    ? get_advanced_invoice_details_by_report_month(
                        $invoices,
                        $amsJobs,
                        $credits,
                        $invoicePaidItems
                    )
                    : get_invoice_details_by_report_month(
                        $invoices,
                        $amsJobs,
                        $credits,
                        $invoicePaidItems,
                        $reportMonth
                    );
            }
        }

        // Summary
        $sheet->mergeCells('A2:E2');

        $headingStyles = [
            'font' => [
                'bold' => true
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ];

        /**
         * Styling
         */
        $sheet->getStyle('A2')
            ->applyFromArray($headingStyles);

        $sheet->getStyle('A3:E3')
            ->applyFromArray($headingStyles);

        $sheet->getStyle('A4:E4')
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->getStyle('A5:E5')
            ->applyFromArray($headingStyles);

        /**
         * End styling
         */
        $sheet->setCellValue('A2', strtoupper(_l('monthly_invoice_report_tab_issue_date_title')));

        // Heading
        $sheet->fromArray([
            [
                _l('monthly_invoice_report_detail_quantity_title'),
                _l('monthly_invoice_report_detail_amount_title'),
                _l('monthly_invoice_report_detail_advanced_amount_title'),
                _l('monthly_invoice_report_detail_revenue_title'),
                _l('monthly_invoice_report_detail_unpaid_title'),
            ],
            [
                $invoiceQuantity ?? '0',
                $invoiceAmount ?? '0',
                $advancedAmount ?? '0',
                $revenueAmount ?? '0',
                $unpaidAmount ?? '0',
            ]
        ], null, 'A3');

        // Purchase Order Summary
        $startAtRow = 5;
        $sheet->setCellValue('A' . $startAtRow, strtoupper(_l('monthly_invoice_report_detail_summary_heading_title')));
        // Summary
        $sheet->mergeCells('A' . $startAtRow . ':E' . $startAtRow . '');
        $sheet->getStyle('A' . $startAtRow)
            ->applyFromArray($headingStyles);

        $startAtRow++;
        $sheet->fromArray(
            [
                [
                    _l('monthly_invoice_report_detail_invoice_month_title'),
                    _l('monthly_invoice_report_detail_month_title'),
                    _l('monthly_invoice_report_detail_amount_title'),
                    _l('monthly_invoice_report_detail_advanced_amount_title'),
                    _l('monthly_invoice_report_detail_revenue_title'),
                ]
            ],
            null,
            'A' . ($startAtRow)
        );
        $sheet->getStyle('A' . $startAtRow . ':E' . $startAtRow . '')
            ->applyFromArray($headingStyles);
        $startAtRow++;

        $summaryReports = $reports->sortKeys()
            ->map(function ($revenueAmount, $key) use ($issueMonth, $advancedAmount, $invoiceAmount) {
                $currentMonth = Carbon::createFromFormat('Ym', $key)->format('Y.m');
                return [
                    $issueMonth == $currentMonth ? Date::PHPToExcel(Carbon::createFromFormat('Y.m', $issueMonth)->format('Y-m-01')) : '',
                    Date::PHPToExcel(Carbon::createFromFormat('Y.m', $currentMonth)->format('Y-m-01')),
                    $issueMonth == $currentMonth ? $invoiceAmount : '',
                    $issueMonth == $currentMonth ? $advancedAmount : '',
                    $revenueAmount
                ];
            })
            ->values();

        $sheet->fromArray($summaryReports->toArray(), null, 'A' . $startAtRow);
        $sheet->getStyle('C' . $startAtRow)
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->fromArray($summaryReports->toArray(), null, 'A' . $startAtRow);
        $sheet->getStyle('A' . $startAtRow)
            ->getNumberFormat()
            ->setFormatCode('YYYY.MM');

        $sheet->getStyle('D' . $startAtRow . ':D' . ($startAtRow + $summaryReports->count()) . '')
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->getStyle('E' . $startAtRow . ':E' . ($startAtRow + $summaryReports->count()) . '')
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->getStyle('B' . $startAtRow . ':B' . ($startAtRow + $summaryReports->count()) . '')
            ->getNumberFormat()
            ->setFormatCode('YYYY.MM');

        $startAtRow += $summaryReports->count();

        $detailTotal = $details->count();
        if ($detailTotal) {
            // Purchase Order Details
            $sheet->setCellValue('A' . $startAtRow, strtoupper(_l('monthly_invoice_report_detail_heading_title') . $detailTitle));
            // Summary
            $sheet->mergeCells('A' . $startAtRow . ':G' . $startAtRow . '');
            $sheet->getStyle('A' . $startAtRow)
                ->applyFromArray($headingStyles);
            $startAtRow++;

            // Heading
            $sheet->fromArray([
                [
                    _l('monthly_invoice_report_detail_status_invoice_title'),
                    _l('monthly_invoice_report_job_posting_job_title'),
                    _l('monthly_invoice_report_detail_invoice_no_title'),
                    _l('monthly_invoice_report_detail_minvoice_no_title'),
                    _l('monthly_invoice_report_detail_invoice_status_title'),
                    _l('monthly_invoice_report_detail_issued_date_title'),
                    _l('monthly_invoice_report_job_posting_start_title'),
                ]
            ], null, 'A' . $startAtRow);
            $sheet->getStyle('A' . $startAtRow . ':G' . $startAtRow . '')
                ->applyFromArray($headingStyles);
            $startAtRow++;

            $ranges = $details->sortBy([
                    ['issued_date', 'desc'],
                    ['posted_date', 'desc'],
                    ['invoice_id', 'desc'],
                ])
                ->values()
                ->map(function ($invoice, $index) {
                    return [
                        $index + 1,
                        $invoice['posted_date'] ? Date::PHPToExcel(Carbon::createFromFormat('Y.m.d', $invoice['posted_date'])->format('Y-m-d')) : '',
                        $invoice['invoice_no'],
                        $invoice['invoice_number'],
                        format_invoice_status($invoice['invoice_status'], '', false),
                        Date::PHPToExcel(Carbon::createFromFormat('Y.m.d', $invoice['issued_date'])->format('Y-m-d')),
                        $invoice['sale_name']
                    ];
                });

            ($isAdvancedAmount != 1) && $sheet->getStyle('B' . $startAtRow . ':B' . ($detailTotal + $startAtRow))
                ->getNumberFormat()
                ->setFormatCode('YYYY.MM.DD');

            $sheet->getStyle('F' . $startAtRow . ':F' . ($detailTotal + $startAtRow))
                ->getNumberFormat()
                ->setFormatCode('YYYY.MM.DD');

            $sheet->fromArray($ranges->toArray(), null, 'A' . $startAtRow);
        }

        self::setAutoResizeColumns($sheet, 'A', 'G');
        self::toExcelFile($type, $fileName, $spreadsheet);
    }

    public static function exportInvoiceMonthlyReportByJobPostingDate(string $month)
    {
        $type = 'Invoice Monthly Report - ' . $month;
        $fileName = 'Invoice-monthly-report-' . $month;

        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Set the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->fromArray([
            [
                'Export Date',
                Carbon::now()->format('Y-m-d H:i:s')
            ]
        ], null, 'A1');

        [$details, $amount] = fetch_monthly_report_by_job_posted_month($month);
        $total = $details->count();

        // Summary
        $sheet->mergeCells('A2:C2');

        $headingStyles = [
            'font' => [
                'bold' => true
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ];

        /**
         * Styling
         */
        $sheet->getStyle('A2')
            ->applyFromArray($headingStyles);

        $sheet->getStyle('A3:C3')
            ->applyFromArray($headingStyles);

        $sheet->getStyle('A4:C4')
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->getStyle('A5:E5')
            ->applyFromArray($headingStyles);

        /**
         * End styling
         */

        $sheet->fromArray([
            [
                strtoupper(_l('monthly_invoice_report_tab_posted_date_title')),
            ]
        ], null, 'A2');

        // Heading
        $sheet->fromArray([
            [
                _l('monthly_invoice_report_detail_quantity_title'),
                _l('monthly_invoice_report_detail_amount_title'),
                _l('monthly_invoice_report_detail_revenue_title'),
            ],
            [
                $amount['posted_total_quantity'] ?? '0',
                $amount['posted_total_amount'] ?? '0',
                $amount['posted_total_revenue'] ?? '0',
            ]
        ], null, 'A3');

        // Purchase Order Details
        $sheet->setCellValue('A5', strtoupper(_l('monthly_invoice_report_detail_heading_title')));
        $sheet->mergeCells('A5:F5');
        $sheet->getStyle('A5')
            ->applyFromArray($headingStyles);

        // Heading
        $sheet->fromArray([
            [
                _l('monthly_invoice_report_detail_status_invoice_title'),
                _l('monthly_invoice_report_job_posting_job_title'),
                _l('monthly_invoice_report_detail_invoice_no_title'),
                _l('monthly_invoice_report_detail_invoice_status_title'),
                _l('monthly_invoice_report_detail_issued_date_title'),
                _l('monthly_invoice_report_job_posting_start_title'),
            ]
        ], null, 'A6');

        $ranges = $details->sortBy([
                ['issued_date', 'desc'],
                ['posted_date', 'desc']
            ])
            ->values()
            ->map(function ($invoice, $index) {
                return [
                    $index + 1,
                    Date::PHPToExcel(Carbon::createFromFormat('Y.m.d', $invoice['posted_date'])->format('Y-m-d')),
                    $invoice['invoice_no'],
                    format_invoice_status($invoice['invoice_status'], '', false),
                    $invoice['issued_date'] ? Date::PHPToExcel(Carbon::createFromFormat('Y.m.d', $invoice['issued_date'])->format('Y-m-d')) : '',
                    $invoice['sale_name']
                ];
            });

        $sheet->getStyle('B7:B' . ($total + 6))
            ->getNumberFormat()
            ->setFormatCode('YYYY.MM.DD');

        $sheet->getStyle('E7:E' . ($total + 6))
            ->getNumberFormat()
            ->setFormatCode('YYYY.MM.DD');

        $sheet->fromArray($ranges->toArray(), null, 'A7');
        self::setAutoResizeColumns($sheet, 'A', 'F');
        self::toExcelFile($type, $fileName, $spreadsheet);
    }

    public static function exportInvoiceReportByUnpaid(string $month)
    {
        $type = 'Invoice Monthly Report - ' . $month;
        $fileName = 'Invoice-monthly-report-' . $month;

        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Set the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->fromArray([
            [
                'Export Date',
                Carbon::now()->format('Y-m-d H:i:s')
            ]
        ], null, 'A1');

        $query = get_unpaid_po_query($month);
        $countQuery = clone $query;
        $amount = $countQuery->selectRaw('COUNT(id) as po_quantity, SUM(total) as po_amount')
            ->first();

        $selects = [
            'id',
            'number',
            'number_format',
            'prefix',
            'date',
            'invoice_closing_date',
            'clientid',
            'total',
            'status',
        ];
        $invoices = $query->select($selects)
            ->orderByDesc('invoice_closing_date')
            ->get();

        // Summary
        $sheet->mergeCells('A2:C2');

        $headingStyles = [
            'font' => [
                'bold' => true
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ];

        /**
         * Styling
         */
        $sheet->getStyle('A2')
            ->applyFromArray($headingStyles);

        $sheet->getStyle('A3:C3')
            ->applyFromArray($headingStyles);

        $sheet->getStyle('A4:C4')
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->getStyle('A5:E5')
            ->applyFromArray($headingStyles);

        /**
         * End styling
         */

        $sheet->fromArray([
            [
                strtoupper('Unpaid PO Report'),
            ]
        ], null, 'A2');

        // Heading
        $sheet->fromArray([
            [
                'PO Quantity',
                'PO Amount'
            ],
            [
                $amount->po_quantity ?? '0',
                $amount->po_amount ?? '0',
            ]
        ], null, 'A3');

        // Purchase Order Details
        $sheet->setCellValue('A5', strtoupper('Unpaid PO Report Details'));
        $sheet->mergeCells('A5:E5');
        $sheet->getStyle('A5')
            ->applyFromArray($headingStyles);

        // Heading
        $sheet->fromArray([
            [
                'Issue Date',
                'Invoice #',
                'Client',
                'Status',
                'Amount',
            ]
        ], null, 'A6');

        $ranges = $invoices->map(function ($invoice) {
            return [
                !empty($invoice->invoice_closing_date) ? Date::PHPToExcel(Carbon::parse($invoice->invoice_closing_date)->format('Y-m-d H:i:s')) : '',
                format_invoice_number($invoice),
                $invoice->client->business_name,
                format_invoice_status($invoice->status, '', false),
                $invoice->total,
            ];
        });

        $total = $invoices->count();

        $sheet->getStyle('A7:A' . ($total + 6))
            ->getNumberFormat()
            ->setFormatCode('YYYY.MM.DD hh:mm:ss');

        $sheet->getStyle('E7:E' . ($total + 6))
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->fromArray($ranges->toArray(), null, 'A7');
        self::setAutoResizeColumns($sheet, 'A', 'E');
        self::toExcelFile($type, $fileName, $spreadsheet);
    }
    public static function syncPaidInvoiceToReport(int $invoiceId)
    {
        $invoiceItems = Itemable::query()
            ->select(['id', 'rel_id', 'rel_type','qty', 'rate'])
            ->with(['invoice:id,clientid'])
            ->whereHas('invoice', fn($query) => $query->paid()->whereId($invoiceId))
            ->invoiceType()
            ->get();

        if ($invoiceItems->count()) {
            $reportItems = ReportClientAmsJob::where('invoice_id', $invoiceId)->get();
            $missingReportItems = $invoiceItems->filter(function ($item) use ($reportItems) {
                return intval($item->qty) > $reportItems->where('package_id', $item->id)->count();
            });
            $reduceReportItems = $invoiceItems->filter(function ($item) use ($reportItems) {
                return intval($item->qty) < $reportItems->where('package_id', $item->id)->count();
            });
            $deleteReportItems = $reportItems->whereNotIn('package_id', $invoiceItems->pluck('id'));

            // Add missing item from the invoice to report table
            if ($missingReportItems->count()) {
                $missingReportItems->each(function ($item) use ($reportItems) {
                    $missingNumber = intval($item->qty) - $reportItems->where('package_id', $item->id)->count();
                    $reportInsert = [];
                    for ($i = 0; $i < $missingNumber; $i++) {
                        $reportInsert[] = [
                            'client_id' => $item->invoice->clientid,
                            'ams_company_id' => 0,
                            'ams_job_id' => 0,
                            'invoice_id' => $item->rel_id,
                            'used_packages' => '{}',
                            'paid_package_id' => null,
                            'package_id' => $item->id,
                            'free_package' => $item->rate == 0,
                            'status_mapping' => 0,
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now(),
                        ];
                    }
                    ReportClientAmsJob::insert($reportInsert);
                });
            }

            // Remove reduce item from the report table, consider removing report item that is not mapped yet
            if ($reduceReportItems->count()) {
                $reduceReportItems->each(function ($item) use ($reportItems) {
                    $reduceNumber = $reportItems->where('package_id', $item->id)->count() - intval($item->qty);
                    $totalNotMappingYet = ReportClientAmsJob::where([
                        'package_id' => $item->id,
                        'ams_company_id' => 0,
                        'ams_job_id' => 0,
                    ])->count();
                    $reduceAdjustment = $totalNotMappingYet - $reduceNumber;
                    // If item not mapping yet available, just delete all un mapping items
                    if ($reduceAdjustment >= 0) {
                        ReportClientAmsJob::where([
                            'package_id' => $item->id,
                            'ams_company_id' => 0,
                            'ams_job_id' => 0,
                        ])->limit($reduceNumber)->delete();
                    } else { // Otherwise, delete un mapping first
                        ReportClientAmsJob::where([
                            'package_id' => $item->id,
                            'ams_company_id' => 0,
                            'ams_job_id' => 0,
                        ])->delete();

                        // Then remove mapping item
                        ReportClientAmsJob::where([
                            'package_id' => $item->id,
                        ])->orderByDesc('id')->limit(abs($reduceAdjustment))->delete();
                    }
                });
            }

            // Remove deleted item from the report table
            if ($deleteReportItems->count()) {
                ReportClientAmsJob::whereIn('id', $deleteReportItems->pluck('id'))->delete();
            }
        }
    }

    public static function exportInvoiceTransactions(string $month)
    {
        $type = 'Invoice Monthly Report - ' . $month;
        $fileName = 'Invoice-monthly-report-' . $month;

        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Set the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->fromArray([
            [
                'Export Date',
                Carbon::now()->format('Y-m-d H:i:s')
            ]
        ], null, 'A1');

        $records = get_infoplus_transactions($month);
        $ecollectionCds = $records->pluck('ecollectionCd')->unique();
        $payers = ClientPayer::whereIn('e_collection_code', $ecollectionCds)
            ->with([
                'client:userid,business_name'
            ])
            ->get()
            ->keyBy('e_collection_code');
        $total = count($records);

        $headingStyles = [
            'font' => [
                'bold' => true
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ];

        $sheet->getStyle('A3:E3')
            ->applyFromArray($headingStyles);

        // Purchase Order Details
        $sheet->setCellValue('A2', strtoupper('InfoPlus\'s Transactions Report'));
        $sheet->mergeCells('A2:E2');
        $sheet->getStyle('A2')
            ->applyFromArray($headingStyles);

        // Heading
        $sheet->fromArray([
            [
                'Datetime',
                'Company Name',
                'Bank account',
                'Amount',
                'Note',
            ]
        ], null, 'A3');

        $ranges = $records->map(function ($tran) use ($payers) {
            $payer = $payers->get($tran['ecollectionCd']);
            return [
                Carbon::createFromFormat('YmdHis', $tran['depositDt'] . $tran['depositTm'])->format('Y-m-d H:i:s'),
                isset($payer) && isset($payer->client) ? $payer->client->business_name : '',
                isset($payer) && isset($payer->client) ? $payer->mother_account_no : '',
                floatval($tran['depositAmt']),
                $tran['depositorNm'],
            ];
        });

        $total = $records->count();

        $sheet->getStyle('A4:A' . ($total + 6))
            ->getNumberFormat()
            ->setFormatCode('YYYY.MM.DD hh:mm:ss');

        $sheet->getStyle('D4:D' . ($total + 6))
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->fromArray($ranges->toArray(), null, 'A4');
        self::setAutoResizeColumns($sheet, 'A', 'E');
        self::toExcelFile($type, $fileName, $spreadsheet);
    }


    /**
     * Fetch jobs with already map invoice from the ams system
     *
     * @param array $amsJobIds
     * @return array
     */
    public static function fetchOpenJobs($page, array $amsJobIds = [], $pageSize = 50, $isAllJobs = false, $published = [], $options = [])
    {
        // Fetch ams companies first
        $queries = array_filter([
            'fields' => [
                'job' => join(',', [
                    'id',
                    'crm_invoice_id',
                    'package_list',
                    'closed',
                    'published',
                    'status'
                ])
            ],
            'has_crm_invoice_id' => true,
            'all_jobs' => $isAllJobs,
            'page' => $page,
            'page_size' => $pageSize,
            'ordering' => 'crm_invoice_id'
        ]);

        // If need sync specific job ids, push to the query
        if (count($amsJobIds)) {
            $queries['ids'] = join(',', $amsJobIds);
        }

        // If need sync specific job ids, push to the query
        if ($isAllJobs) {
            $queries['status'] = join(',', [1, 3]);
        }

        if (count($published)) {
            $queries['published_from'] = $published['from'];
            $queries['published_to'] = $published['to'];
        }

        // More options for the query
        $queries = array_merge($queries, $options);

        $amsJobsRes = AmsService::search('jobs', [
            'query' => $queries
        ]) ?? [];

        $fetchNextPage = ($amsJobsRes['meta']['current_page'] ?? 0) < ($amsJobsRes['meta']['last_page'] ?? 0);
        $amsJobs = collect($amsJobsRes['data'] ?? []);

        $amsJobs = $amsJobs->map(function ($job) {
            $packageList = collect($job['package_list']);

            $package = $packageList->count() > 1
                ? $packageList->filter(fn($job) => $job['id'] != AmsPackage::AMS_PACKAGE_BASIC_ID)->first()
                : $packageList->first();

            $job['package_id'] = $package['id'] ?? null;
            unset($job['package_list']);
            return $job;
        });

        return [$fetchNextPage, $amsJobs];
    }

    /**
     * Get all package that is used the calculate daily revenue based on package types
     * @param string $revenueDate
     * @return int
     */
    public static function importDailyRevenue($revenueDate = '')
    {
        $revenueDate = !empty($revenueDate) ? Carbon::createFromFormat('Ymd', $revenueDate)->subDay() : Carbon::yesterday();
        $start = $revenueDate->clone()->startOfDay()->format('Y-m-d H:i:s');
        $end = $revenueDate->clone()->endOfDay()->format('Y-m-d H:i:s');
        $crmDbName = APP_DB_NAME . '.' . db_prefix();
        $crmDbOnly = APP_DB_NAME . '.';
        $amsDbName = AMS_DB_NAME;
        $sql = <<<EOD
        INSERT IGNORE INTO {$crmDbName}invoice_daily_revenues (invoice_id, itemable_id, ams_job_id, revenue_date, amount, package_type, note, created_at, updated_at)
        With itemables as (
            WITH ItemSeries AS (
                SELECT
                    ib.id AS itemable_id,
                    ib.item_id AS item_id,
                    i.group_id,
                    30 as duration,
                    ib.rel_id as invoice_id,
                    i.description as package_name,
                    ib.rate,
                    ib.discount_table,
                    1 AS row_num,
                    (
                        (t.total - t.total_tax) / (
                            select
                                sum(ibb.qty)
                            from
                                {$crmDbName}itemable ibb
                            where
                                ibb.rel_id = ib.rel_id
                                and ibb.rel_type = "invoice"
                        )
                    ) as sale_price,
                    ib.qty,
                    (
                        select
                            sum(ibb.qty)
                        from
                            {$crmDbName}itemable ibb
                        where
                            ibb.rel_id = ib.rel_id
                            and ibb.rel_type = "invoice"
                    ) as po_qty,
                    t.total - t.total_tax as po_amount
                FROM
                    {$crmDbName}itemable ib
                    join {$crmDbName}items i on i.id = ib.item_id
                    join {$crmDbName}invoices t on t.id = ib.rel_id
                    and ib.rel_type = "invoice"
                where
                    ib.rel_type = "invoice"
            )
            select
                iss.*,
                ROUND(iss.sale_price / iss.duration) as revenue_per_day
            from
                ItemSeries iss
        ),
        job_used as (
            select
                *,
                ROW_NUMBER() OVER (
                    PARTITION BY package_id
                    ORDER BY
                        id
                ) AS row_num,
                package_id as itemable_id
            from
                {$crmDbName}client_ams_jobs caj
            where
                1 = 1
        ),
        service_used as (
            select
                *,
                crm_itemable_id as itemable_id,
                ROW_NUMBER() OVER (
                    PARTITION BY tas.crm_itemable_id
                    ORDER BY
                        id
                ) AS row_num
            from
                {$crmDbName}client_ams_services tas
            where
                1 = 1
        ),
        combo_used as (
            select
                *,
                crm_itemable_id as itemable_id,
                (
                    select
                        MIN(ccl.created_at)
                    from
                        {$amsDbName}.company_credit_logs ccl
                        join {$amsDbName}.company_credit_logs ccl2 on ccl.company_search_package_id = ccl2.id
                    where
                        ccl2.company_search_package_id = casp.ams_company_search_package_id
                        AND ccl.type = 'Out'
                        AND ccl.refund_approved_at IS NULL
                ) first_time_use_at,
                ROW_NUMBER() OVER (
                    PARTITION BY casp.crm_itemable_id
                    ORDER BY
                        id
                ) AS row_num
            from
                {$crmDbName}client_ams_search_packages casp
            where
                1 = 1
        ),
        job_posting as (
            select
                *
            from
                (
                    select
                        invoice_number,
                        invoice_issued_date,
                        package_expires_date,
                        po_id,
                        po_no,
                        po_status,
                        po_amount,
                        po_qty,
                        item_id,
                        itemable_id,
                        package_name,
                        sale_price,
                        revenue_per_day,
                        ams_job_id,
                        duration,
                        published_at,
                        (
                            DATE_ADD(
                                published_at,
                                INTERVAL duration -1 DAY
                            )
                        ) as expires_at,
                        (
                            CASE
                                WHEN ams_job_id IS NULL THEN sale_price
                                ELSE 0
                            END
                        ) as expires_revenue
                    from
                        (
                            SELECT
                                ib.*,
                                j.published_at,
                                ju.ams_job_id,
                                iv.id as po_id,
                                (
                                    case
                                        when iv.status = 1 then 'UNPAID'
                                        when iv.status = 2 then 'PAID'
                                        when iv.status = 3 then 'PARTIALLY'
                                        when iv.status = 4 then 'OVERDUE'
                                        else ''
                                    end
                                ) as po_status,
                                (vp.po_number COLLATE utf8mb4_unicode_ci) as po_no,
                                min.invoice_number,
                                min.invoice_issued_date,
                                DATE_ADD(
                                    DATE_ADD(min.invoice_issued_date, INTERVAL 1 YEAR),
                                    INTERVAL -1 DAY
                                ) as package_expires_date
                            FROM
                                job_used ju
                                join {$crmDbOnly}vv_po vp on vp.id = ju.invoice_id
                                join itemables ib on ib.itemable_id = ju.itemable_id
                                join {$crmDbName}invoices iv on iv.id = ju.invoice_id
                                join {$amsDbName}.jobs j on j.id = ju.ams_job_id
                                left join {$crmDbName}minvoices min on min.invoice_id = ju.invoice_id
                            where
                                1 = 1
                                and min.id is not null
                                and iv.status in (1, 2, 3, 4)
                                and ib.group_id not in(6, 7)
                                and j.status in (1,2,3) -- 1: Closed, 2: Review, 3: Open
                        ) as data
                ) as iv_data
            where
                1 = 1
                and published_at <= '$end'
                and expires_at >= '$start'
        ),
        combo as (
            select
                *
            from
                (
                    select
                        invoice_number,
                        invoice_issued_date,
                        package_expires_date,
                        po_id,
                        po_no,
                        po_status,
                        po_amount,
                        po_qty,
                        item_id,
                        itemable_id,
                        package_name,
                        sale_price,
                        revenue_per_day,
                        ams_job_id,
                        duration,
                        published_at,
                        (
                            DATE_ADD(
                                published_at,
                                INTERVAL duration -1 DAY
                            )
                        ) as expires_at,
                        (
                            CASE
                                WHEN ams_job_id IS NULL THEN sale_price
                                ELSE 0
                            END
                        ) as expires_revenue
                    from
                        (
                            SELECT
                                ib.*,
                                cu.first_time_use_at as published_at,
                                cu.id as ams_job_id,
                                iv.id as po_id,
                                (
                                    case
                                        when iv.status = 1 then 'UNPAID'
                                        when iv.status = 2 then 'PAID'
                                        when iv.status = 3 then 'PARTIALLY'
                                        when iv.status = 4 then 'OVERDUE'
                                        else ''
                                    end
                                ) as po_status,
                                (vp.po_number COLLATE utf8mb4_unicode_ci) as po_no,
                                min.invoice_number,
                                min.invoice_issued_date,
                                DATE_ADD(
                                    DATE_ADD(min.invoice_issued_date, INTERVAL 1 YEAR),
                                    INTERVAL -1 DAY
                                ) as package_expires_date
                            FROM
                                combo_used cu
                                join {$crmDbOnly}vv_po vp on vp.id = cu.invoice_id
                                join itemables ib on ib.itemable_id = cu.itemable_id
                                join {$crmDbName}invoices iv on iv.id = cu.invoice_id
                                left join {$crmDbName}minvoices min on min.invoice_id = cu.invoice_id
                            where
                                1 = 1
                                and min.id is not null
                                and iv.status in (1, 2, 3, 4)
                                and ib.group_id in(7)
                        ) as data
                ) as iv_data
            where
                published_at <= '$end'
                and expires_at >= '$start'
        ),
        service as (
            select
                *
            from
                (
                    select
                        invoice_number,
                        invoice_issued_date,
                        package_expires_date,
                        po_id,
                        po_no,
                        po_status,
                        po_amount,
                        po_qty,
                        item_id,
                        itemable_id,
                        package_name,
                        sale_price,
                        revenue_per_day,
                        ams_job_id,
                        duration,
                        published_at,
                        (
                            DATE_ADD(
                                published_at,
                                INTERVAL duration -1 DAY
                            )
                        ) as expires_at,
                        (
                            CASE
                                WHEN ams_job_id IS NULL THEN sale_price
                                ELSE 0
                            END
                        ) as expires_revenue
                    from
                        (
                            SELECT
                                ib.*,
                                su.used_at as published_at,
                                su.id as ams_job_id,
                                iv.id as po_id,
                                (
                                    case
                                        when iv.status = 1 then 'UNPAID'
                                        when iv.status = 2 then 'PAID'
                                        when iv.status = 3 then 'PARTIALLY'
                                        when iv.status = 4 then 'OVERDUE'
                                        else ''
                                    end
                                ) as po_status,
                                (vp.po_number COLLATE utf8mb4_unicode_ci) as po_no,
                                min.invoice_number,
                                min.invoice_issued_date,
                                DATE_ADD(
                                    DATE_ADD(min.invoice_issued_date, INTERVAL 1 YEAR),
                                    INTERVAL -1 DAY
                                ) as package_expires_date
                            FROM
                                service_used su
                                join {$crmDbOnly}vv_po vp on vp.id = su.invoice_id
                                join itemables ib on ib.itemable_id = su.itemable_id
                                join {$crmDbName}invoices iv on iv.id = su.invoice_id
                                join {$amsDbName}.jobs j on j.id = su.ams_job_id
                                left join {$crmDbName}minvoices min on min.invoice_id = su.invoice_id
                            where
                                1 = 1
                                and min.id is not null
                                and iv.status in (1, 2, 3, 4)
                                and ib.group_id in(6)
                        ) as data
                ) as iv_data
            where
                published_at <= '$end'
                and expires_at >= '$start'
        )
        select
            insert_data.*,
            NOW(),
            NOW()
        from (
            select
                po_id,
                itemable_id,
                ams_job_id,
                {$revenueDate->format('Ymd')},
                (
                    case
                        when DATEDIFF('$start', package_expires_date) = 0 and diff > 0 then revenue_per_day * (diff + 1)
                        when DATEDIFF('$start', package_expires_date) > 0 and diff > 0 then 0
                        WHEN DATEDIFF(published_at, invoice_issued_date) < 0 and DATEDIFF('$start', invoice_issued_date) < 0 then
                            (
                                CASE WHEN DATE_FORMAT(invoice_issued_date, "%Y%m") = DATE_FORMAT('$start', "%Y%m") THEN revenue_per_day
                                ELSE 0
                                END
                            )
                        ELSE revenue_per_day
                    end
                ) as revenue_per_day,
                package_type,
                (
                    case
                        when DATEDIFF('$start', package_expires_date) = 0 and diff > 0 then "adjust to balance"
                        when DATEDIFF('$start', package_expires_date) > 0 and diff > 0 then  "adjust to 0"
                        WHEN DATEDIFF(published_at, invoice_issued_date) < 0 and DATEDIFF('$start', invoice_issued_date) < 0 then
                        (
                            CASE WHEN DATE_FORMAT(invoice_issued_date, "%Y%m") = DATE_FORMAT('$start', "%Y%m") THEN "use b4 issue same month"
                            ELSE 0
                            END
                        )
                        else null
                    end
                ) as note
            from
                (
                    select
                        GREATEST(DATEDIFF(expires_at, package_expires_date), 0) as diff,
                        union_data.*
                    from
                        (
                            select
                                *,
                                "job_posting" as package_type
                            from
                                job_posting
                            union
                            all
                            select
                                *,
                                "combo" as package_type
                            from
                                combo
                            union
                            all
                            select
                                *,
                                "service" as package_type
                            from
                                service
                        ) as union_data
                ) as revenue_data
            ) as insert_data
            where revenue_per_day > 0;
        EOD;
        try {
            $CI = & get_instance();
            $CI->db->query($sql);
            return $CI->db->affected_rows();
        } catch (\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
            return 0;
        }
    }

    public static function fixRoundToLastExpirePackage($revenueDate = '')
    {
        $revenueDate = !empty($revenueDate) ? Carbon::createFromFormat('Ymd', $revenueDate) : Carbon::now();
        $start = $revenueDate->clone()->startOfDay()->format('Y-m-d H:i:s');
        $end = $revenueDate->clone()->endOfDay()->format('Y-m-d H:i:s');
        $crmDbName = APP_DB_NAME . '.' . db_prefix();
        $sql = <<<EOD
            INSERT
                IGNORE INTO {$crmDbName}invoice_daily_revenues (
                    invoice_id,
                    itemable_id,
                    ams_job_id,
                    revenue_date,
                    amount,
                    package_type,
                    note,
                    created_at,
                    updated_at
                ) With invoice_infos as (
                    select
                        t.id as po_id,
                        t.total - t.total_tax as subtotal
                    from
                        {$crmDbName}invoices t
                ),
                update_invoice as (
                    select
                        tdr.invoice_id,
                        DATE_ADD(
                            DATE_ADD(min.invoice_issued_date, INTERVAL 1 YEAR),
                            INTERVAL -1 DAY
                        ) as package_expires_date,
                        SUM(tdr.amount) as total_revenue,
                        ii.subtotal,
                        ii.subtotal - SUM(tdr.amount) as diff,
                        min(tdr.amount) as min_amnt
                    from
                        {$crmDbName}invoice_daily_revenues tdr
                        left join invoice_infos ii on ii.po_id = tdr.invoice_id
                        left join {$crmDbName}minvoices min on min.invoice_id = tdr.invoice_id
                    where
                        1 = 1
                    group by
                        tdr.invoice_id,
                        min.invoice_issued_date
                    having
                        total_revenue > 0
                        and (
                            (
                                diff between 1
                                and min_amnt
                            )
                            OR diff < 0
                        )
                ),
                date_ranges as (
                    select
                        '{$start}' as start_date,
                        '{$end}' as end_date
                ),
                update_items as (
                    select
                        ui.*,
                        (
                            select
                                id
                            from
                                {$crmDbName}itemable ib
                            where
                                ib.rel_id = ui.invoice_id
                                and ib.rel_type = "invoice"
                            limit
                                1
                        ) as itemable_id
                    from
                        update_invoice ui
                )
            SELECT
                ui.invoice_id,
                ui.itemable_id,
                null as ams_job_id,
                DATE_FORMAT(dr.start_date, '%Y%m%d'),
                ui.diff,
                "job_posting" as package_type,
                "move round to package expires" as note,
                NOW(),
                NOW()
            FROM
                update_items ui,
                date_ranges dr
            where
                1 = 1
                and ui.package_expires_date between dr.start_date
                and dr.end_date;
        EOD;

        try {
            $total = 0;
            $CI = & get_instance();
            $CI->db->query($sql);
            $total += $CI->db->affected_rows();
            return $total;
        } catch (\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
            return 0;
        }
    }

    public static function fixRoundUpToLastRevenueRecord()
    {
        $crmDbName = APP_DB_NAME . '.' . db_prefix();
        $sql = <<<EOD
            With invoice_infos as (
                select
                    t.id as po_id,
                    t.total - t.total_tax as subtotal
                from
                    {$crmDbName}invoices t
            ),
            update_invoice as (
                select
                    tdr.invoice_id,
                    DATE_ADD(
                        DATE_ADD(min.invoice_issued_date, INTERVAL 1 YEAR),
                        INTERVAL -1 DAY
                    ) as package_expires_date,
                    SUM(tdr.amount) as total_revenue,
                    ii.subtotal,
                    ii.subtotal - SUM(tdr.amount) as diff,
                    min(tdr.amount) as min_amnt
                from
                    {$crmDbName}invoice_daily_revenues tdr
                    left join invoice_infos ii on ii.po_id = tdr.invoice_id
                    left join {$crmDbName}minvoices min on min.invoice_id = tdr.invoice_id
                where
                    1 = 1
                group by
                    tdr.invoice_id,
                    min.invoice_issued_date
                having
                    total_revenue > 0
                    and diff < 0
            ),
            update_items as (
                select
                    ui.*,
                    (
                        select
                            id
                        from
                            {$crmDbName}itemable ib
                        where
                            ib.rel_id = ui.invoice_id
                            and ib.rel_type = "invoice"
                        limit
                            1
                    ) as itemable_id
                from
                    update_invoice ui
            ),
            last_revenue_item as (
                select ui.itemable_id, max(id) as latest_revenue_id, diff from {$crmDbName}invoice_daily_revenues tdr
                join update_items ui on ui.itemable_id = tdr.itemable_id
                group by ui.itemable_id
            )
            UPDATE
                {$crmDbName}invoice_daily_revenues tdr
                JOIN last_revenue_item saa on saa.latest_revenue_id = tdr.id
            set
                tdr.amount = tdr.amount + saa.diff,
                tdr.note = CONCAT(tdr.note, " adjust round up value")
            ;
        EOD;

        try {
            $total = 0;
            $CI = & get_instance();
            $CI->db->query($sql);
            $total += $CI->db->affected_rows();
            return $total;
        } catch (\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
            return 0;
        }
    }

    /**
     * We have a table tblrevenue_2024_balances has the revenue that made by Accountant
     * We need to adjust daily revenue based on this table
     *   - If not have on this list and issue is 2024 remove it
     *   - If have:
     *        - adjust total revenue to the same with Unearned revenue of the same po
     * @return float|int
     */
    public static function adjust2024Revenues()
    {
        $start = ********;
        $end = ********;
        $crmDbName = APP_DB_NAME . '.' . db_prefix();
        $sql1 = <<<EOD
            DELETE FROM
                {$crmDbName}invoice_daily_revenues tdr
            WHERE
                1 = 1
                and tdr.revenue_date >= {$start}
                and invoice_id in (
                    select
                        iv.id
                    from
                        {$crmDbName}invoices iv
                        join {$crmDbName}minvoices t on t.invoice_id = iv.id
                    where
                        YEAR(`date`) = 2024
                        and YEAR(t.invoice_issued_date) = 2024
                )
                and invoice_id not in (
                    select
                        po_id
                    from
                        {$crmDbName}revenue_2024_balances
                );
        EOD;
        $sql2 = <<<EOD
            With ranked_daily_revenues as (
                SELECT
                    tdr.id,
                    tdr.revenue_date,
                    tdr.invoice_id,
                    tdr.amount,
                    tb.minvoice_amount - tb.earned_revenue_amount as unearn_remain,
                    SUM(tdr.amount) OVER (
                        PARTITION BY tdr.invoice_id
                        ORDER BY
                            tdr.id ROWS BETWEEN UNBOUNDED PRECEDING
                            AND CURRENT ROW
                    ) AS cumulative_sale_price
                FROM
                    {$crmDbName}invoice_daily_revenues tdr
                    JOIN {$crmDbName}revenue_2024_balances tb on tb.po_id = tdr.invoice_id
                where
                    tdr.revenue_date between {$start}
                    and {$end}
            ),
            adjusted AS (
                SELECT
                    id,
                    revenue_date,
                    invoice_id,
                    amount,
                    CASE
                        WHEN cumulative_sale_price > unearn_remain THEN amount - (cumulative_sale_price - unearn_remain)
                        ELSE amount
                    END AS adjusted_amount,
                    cumulative_sale_price,
                    unearn_remain
                FROM
                    ranked_daily_revenues
            ),
            sum_after_adjust as (
                SELECT
                    *,
                    SUM(adj.adjusted_amount) OVER (
                        PARTITION BY adj.invoice_id
                        ORDER BY
                            adj.id ROWS BETWEEN UNBOUNDED PRECEDING
                            AND CURRENT ROW
                    ) AS total_adjusted_amount
                FROM
                    adjusted adj
            )
            DELETE FROM
                {$crmDbName}invoice_daily_revenues
            where
                id in (
                    SELECT
                        id
                    FROM
                        sum_after_adjust
                    WHERE
                        1 = 1
                        and cumulative_sale_price > unearn_remain
                        and total_adjusted_amount < unearn_remain
                );
        EOD;
        $sql3 = <<<EOD
            With ranked_daily_revenues as (
                SELECT
                    tdr.id,
                    tdr.revenue_date,
                    tdr.invoice_id,
                    tdr.amount,
                    tb.minvoice_amount - tb.earned_revenue_amount as unearn_remain,
                    SUM(tdr.amount) OVER (
                        PARTITION BY tdr.invoice_id
                        ORDER BY
                            tdr.id ROWS BETWEEN UNBOUNDED PRECEDING
                            AND CURRENT ROW
                    ) AS cumulative_sale_price
                FROM
                    {$crmDbName}invoice_daily_revenues tdr
                    JOIN {$crmDbName}revenue_2024_balances tb on tb.po_id = tdr.invoice_id
                where
                    tdr.revenue_date between {$start}
                    and {$end}
            ),
            adjusted AS (
                SELECT
                    id,
                    revenue_date,
                    invoice_id,
                    amount,
                    CASE
                        WHEN cumulative_sale_price > unearn_remain THEN amount - (cumulative_sale_price - unearn_remain)
                        ELSE amount
                    END AS adjusted_amount,
                    cumulative_sale_price,
                    unearn_remain
                FROM
                    ranked_daily_revenues
            ),
            sum_after_adjust as (
                SELECT
                    *,
                    SUM(adj.adjusted_amount) OVER (
                        PARTITION BY adj.invoice_id
                        ORDER BY
                            adj.id ROWS BETWEEN UNBOUNDED PRECEDING
                            AND CURRENT ROW
                    ) AS total_adjusted_amount
                FROM
                    adjusted adj
                where
                    cumulative_sale_price > unearn_remain
            )
            UPDATE
                {$crmDbName}invoice_daily_revenues tdr
                JOIN sum_after_adjust saa on saa.id = tdr.id
            set
                tdr.amount = saa.adjusted_amount,
                tdr.note = CONCAT(tdr.note, " ", "case 1")
            where
                1 = 1;
        EOD;
        $sql4 = <<<EOD
            delete from {$crmDbName}invoice_daily_revenues tdr where amount < 1;
        EOD;
        try {
            $total = 0;
            $CI = & get_instance();
            $CI->db->query($sql1);
            $total += $CI->db->affected_rows();
            $CI->db->query($sql2);
            $total += $CI->db->affected_rows();
            $CI->db->query($sql3);
            $total += $CI->db->affected_rows();
            $CI->db->query($sql4);
            $total += $CI->db->affected_rows();
            return $total;
        } catch (\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
            return 0;
        }
    }

    /**
     * Get all expires package and not use then convert to revenue to the expires date
     * @param string $revenueDate
     * @return int
     */
    public static function convertExpiredPackageToRevenue($revenueDate = '')
    {
        $revenueDate = !empty($revenueDate) ? Carbon::createFromFormat('Ymd', $revenueDate) : Carbon::now();
        $revenueDate = $revenueDate->startOfDay();
        $start = $revenueDate->clone()->subYear()->startOfDay()->format('Y-m-d H:i:s');
        $end = $revenueDate->clone()->subYear()->endOfDay()->format('Y-m-d H:i:s');
        $expiresDate = $revenueDate->clone()->startOfDay()->format('Y-m-d H:i:s');
        $crmDbName = APP_DB_NAME . '.' . db_prefix();
        $amsDbName = AMS_DB_NAME;
        $sql = <<<EOD
        INSERT IGNORE INTO {$crmDbName}invoice_daily_revenues (invoice_id, itemable_id, ams_job_id, revenue_date, amount, package_type, note, created_at, updated_at)
        With itemables as (
            WITH ItemSeries AS (
                SELECT
                    ib.id AS itemable_id,
                    ib.item_id AS item_id,
                    i.group_id,
                    30 as duration,
                    ib.rel_id as invoice_id,
                    i.description as package_name,
                    ib.rate,
                    ib.discount_table,
                    1 AS row_num,
                    (
                        (t.total - t.total_tax) / (
                            select
                                sum(ibb.qty)
                            from
                                {$crmDbName}itemable ibb
                            where
                                ibb.rel_id = ib.rel_id
                                and ibb.rel_type = "invoice"
                        )
                    ) as sale_price,
                    ib.qty,
                    (
                        select
                            sum(ibb.qty)
                        from
                            {$crmDbName}itemable ibb
                        where
                            ibb.rel_id = ib.rel_id
                            and ibb.rel_type = "invoice"
                    ) as po_qty,
                    t.total - t.total_tax as po_amount
                FROM
                    {$crmDbName}itemable ib
                    join {$crmDbName}items i on i.id = ib.item_id
                    join {$crmDbName}invoices t on t.id = ib.rel_id
                    and ib.rel_type = "invoice"
                where
                    ib.rel_type = "invoice"
            )
            select
                iss.*,
                ROUND(iss.sale_price / iss.duration) as revenue_per_day
            from
                ItemSeries iss
        ),
        job_used as (
            select
                caj.*,
                j.published_at,
                ROW_NUMBER() OVER (
                    PARTITION BY package_id
                    ORDER BY
                        id
                ) AS row_num,
                package_id as itemable_id
            from
                {$crmDbName}client_ams_jobs caj
                join {$amsDbName}.jobs j on j.id = caj.ams_job_id
            where
                1 = 1
        ),
        service_used as (
            select
                *,
                crm_itemable_id as itemable_id,
                ROW_NUMBER() OVER (
                    PARTITION BY tas.crm_itemable_id
                    ORDER BY
                        id
                ) AS row_num
            from
                {$crmDbName}client_ams_services tas
            where
                1 = 1
        ),
        combo_used as (
            select
                *,
                crm_itemable_id as itemable_id,
                (
                    select
                        MIN(ccl.created_at)
                    from
                        {$amsDbName}.company_credit_logs ccl
                        join {$amsDbName}.company_credit_logs ccl2 on ccl.company_search_package_id = ccl2.id
                    where
                        ccl2.company_search_package_id = casp.ams_company_search_package_id
                        AND ccl.type = 'Out'
                        AND ccl.refund_approved_at IS NULL
                ) first_time_use_at,
                ROW_NUMBER() OVER (
                    PARTITION BY casp.crm_itemable_id
                    ORDER BY
                        id
                ) AS row_num
            from
                {$crmDbName}client_ams_search_packages casp
            where
                1 = 1
        )
        select
            invoice_id,
            itemable_id,
            null,
            {$revenueDate->format('Ymd')},
            expires_sale_price,
            package_type,
            "convertExpiredPackageToRevenue" as note,
            NOW(),
            NOW()
        from
            (
                select
                    *,
                    ((qty - used_qty) * (revenue_per_day * duration)) as expires_sale_price,
                    (qty - used_qty) as expired_qty
                from
                    (
                        select
                            ib.itemable_id,
                            ib.qty,
                            ib.sale_price,
                            ib.revenue_per_day,
                            ib.duration,
                            ib.item_id,
                            DATE_ADD(
                                DATE_ADD(min.invoice_issued_date, INTERVAL 1 YEAR),
                                INTERVAL -1 DAY
                            ) as package_expires_date,
                            (
                                CASE
                                    WHEN ib.group_id in (7) THEN "combo"
                                    WHEN ib.group_id in (6) THEN "service"
                                    ELSE "job_posting"
                                END
                            ) as package_type,
                            ib.group_id,
                            (
                                CASE
                                    WHEN ib.group_id in (7) THEN (
                                        select
                                            COUNT(id)
                                        from
                                            combo_used as cu
                                        where
                                            cu.crm_itemable_id = ib.itemable_id
                                            and cu.first_time_use_at < '$expiresDate'
                                    )
                                    WHEN ib.group_id in (6) THEN (
                                        select
                                            COUNT(id)
                                        from
                                            service_used as su
                                        where
                                            su.crm_itemable_id = ib.itemable_id
                                            and su.used_at <= '$expiresDate'
                                    )
                                    ELSE (
                                        select
                                            count(*)
                                        from
                                            {$amsDbName}.jobs j
                                        where
                                            j.id in (
                                                select
                                                    DISTINCT ju.ams_job_id
                                                from
                                                    job_used as ju
                                                where
                                                    ju.package_id = ib.itemable_id
                                                    and ju.published_at <= '$expiresDate'
                                            )
                                            and j.status in (1, 3) -- 1: closed, 3: open
                                    )
                                END
                            ) as used_qty,
                            iv.id as invoice_id
                        from
                            itemables ib
                            join {$crmDbName}invoices iv on iv.id = ib.invoice_id
                            join {$crmDbName}minvoices min on min.invoice_id = ib.invoice_id
                            left join {$crmDbName}items i on i.id = ib.item_id
                        where
                            exists (
                                select
                                    *
                                from
                                    {$crmDbName}invoices
                                where
                                    ib.invoice_id = {$crmDbName}invoices.id
                                    and exists (
                                        select
                                            *
                                        from
                                            {$crmDbName}minvoices
                                        where
                                            {$crmDbName}invoices.id = {$crmDbName}minvoices.invoice_id
                                            and invoice_issued_date between '$start'
                                            and '$end'
                                    )
                            )
                            and (
                                (
                                    exists (
                                        select
                                            *
                                        from
                                            {$crmDbName}items
                                        where
                                            ib.item_id = {$crmDbName}items.id
                                            and group_id in (7)
                                    )
                                    and qty > (
                                        select
                                            COUNT(id)
                                        from
                                            combo_used as cu
                                        where
                                            cu.crm_itemable_id = ib.itemable_id
                                            and cu.first_time_use_at < '$expiresDate'
                                    )
                                )
                                or (
                                    (
                                        exists (
                                            select
                                                *
                                            from
                                                {$crmDbName}items
                                            where
                                                ib.item_id = {$crmDbName}items.id
                                                and group_id in (6)
                                        )
                                        and qty > (
                                            select
                                                COUNT(id)
                                            from
                                                service_used as su
                                            where
                                                su.crm_itemable_id = ib.itemable_id
                                                and su.used_at <= '$expiresDate'
                                        )
                                    )
                                )
                                or (
                                    (
                                        exists (
                                            select
                                                *
                                            from
                                                {$crmDbName}items
                                            where
                                                ib.item_id = {$crmDbName}items.id
                                                and group_id in (1, 2, 5)
                                        )
                                        and qty > (
                                            select
                                                COUNT(id)
                                            from
                                                job_used as ju
                                            where
                                                ju.package_id = ib.itemable_id
                                                and ju.published_at <= '$expiresDate'
                                        )
                                    )
                                )
                            )
                    ) as fetching_data
            ) as insert_data
            where expires_sale_price > 0
        EOD;
        try {
            $CI = & get_instance();
            $CI->db->query($sql);
            return $CI->db->affected_rows();
        } catch (\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
            return 0;
        }
    }

    /**
     * Get the revenue of the convertExpiredPackageToRevenue then adjust it to the unearned based on the revenue_2024_balances
     * @param string $revenueDate
     * @return int
     */
    public static function adjustExpiredPackageToRevenue($revenueDate = '')
    {
        $revenueDate = !empty($revenueDate) ? Carbon::createFromFormat('Ymd', $revenueDate) : Carbon::now();
        $revenueDate = $revenueDate->startOfDay();
        $start = $revenueDate->clone()->format('Y-m-d H:i:s');
        $end = $revenueDate->clone()->endOfDay()->format('Y-m-d H:i:s');
        $crmDbName = APP_DB_NAME . '.' . db_prefix();
        $sql = <<<EOD
            INSERT
            IGNORE INTO {$crmDbName}invoice_daily_revenues (
                invoice_id,
                itemable_id,
                ams_job_id,
                revenue_date,
                amount,
                package_type,
                note,
                created_at,
                updated_at
            ) WITH RECURSIVE update_items as (
                select
                    MAX(tdrr.id) as last_item_id,
                    tdrr.itemable_id,
                    tdrr.invoice_id,
                    SUM(
                        CASE
                            WHEN tdrr.revenue_date between ********
                            and ******** THEN tdrr.amount
                            else 0
                        end
                    ) as earned_2025,
                    tb.unearned_revenue_amount - SUM(
                        CASE
                            WHEN tdrr.revenue_date between ********
                            and ******** THEN tdrr.amount
                            else 0
                        end
                    ) as unearned_amt,
                    tb.unearned_revenue_amount,
                    (
                        tb.unearned_revenue_amount - SUM(
                            CASE
                                WHEN tdrr.revenue_date between ********
                                and ******** THEN tdrr.amount
                                else 0
                            end
                        )
                    ) as diff_amount
                from
                    {$crmDbName}invoice_daily_revenues tdrr
                    join {$crmDbName}revenue_2024_balances tb on tdrr.invoice_id = tb.po_id
                WHERE
                    1 = 1
                    and tdrr.revenue_date between ********
                    and ********
                    and (
                        -- expires po
                        tdrr.invoice_id in (
                            select
                                t.invoice_id
                            from
                                {$crmDbName}minvoices t
                                join {$crmDbName}revenue_2024_balances t2b on t2b.po_id = t.invoice_id
                            where
                                DATE_ADD(
                                    DATE_ADD(t.invoice_issued_date, INTERVAL 1 YEAR),
                                    INTERVAL -1 DAY
                                ) < NOW()
                        )
                        OR -- Po use all packages
                        tdrr.invoice_id in (
                            select
                                rel_id
                            from
                                (
                                    select
                                        ib.rel_id,
                                        sum(qty) as total_qty,
                                        (
                                            select
                                                count(1)
                                            from
                                                {$crmDbName}client_ams_jobs taj
                                            where
                                                taj.invoice_id = ib.rel_id
                                        ) as job_count,
                                        (
                                            select
                                                count(1)
                                            from
                                                {$crmDbName}client_ams_services taj
                                            where
                                                taj.invoice_id = ib.rel_id
                                        ) as service_count,
                                        (
                                            select
                                                count(1)
                                            from
                                                {$crmDbName}client_ams_search_packages taj
                                            where
                                                taj.invoice_id = ib.rel_id
                                        ) as combo_count
                                    from
                                        {$crmDbName}itemable ib
                                        join {$crmDbName}revenue_2024_balances t2b on t2b.po_id = ib.rel_id
                                        and ib.rel_type = "invoice"
                                    group by
                                        ib.rel_id
                                    having
                                        total_qty = job_count + service_count + combo_count
                                ) as using_data
                        )
                    )
                GROUP BY
                    tdrr.invoice_id,
                    tdrr.itemable_id,
                    tb.unearned_revenue_amount
                HAVING
                    tb.unearned_revenue_amount - unearned_amt >= 0
            ),
            date_ranges as (
                select
                    '{$start}' as start_date,
                    '{$end}' as end_date
            ),
            balances_mapping as (
                select
                    po_id,
                    min.invoice_issued_date,
                    DATE_ADD(
                        DATE_ADD(min.invoice_issued_date, INTERVAL 1 YEAR),
                        INTERVAL -1 DAY
                    ) as package_expires_date,
                    ui.*
                from
                    {$crmDbName}revenue_2024_balances tb
                    left join {$crmDbName}minvoices min on tb.po_id = min.invoice_id
                    left join update_items ui on tb.po_id = ui.invoice_id
            )
        select
            *
        from
            (
                select
                    bm.po_id,
                    bm.itemable_id,
                    null as ams_job_id,
                    DATE_FORMAT(dr.start_date, '%Y%m%d'),
                    bm.diff_amount,
                    "job_posting" as package_type,
                    "adjust expire balance" as note,
                    NOW() as created_at,
                    NOW() as updated_at
                from
                    balances_mapping bm,
                    date_ranges dr
                where
                    1 = 1
                    and bm.package_expires_date between dr.start_date and dr.end_date
                    and bm.diff_amount > 0
            ) as insert_data ON DUPLICATE KEY
        UPDATE
            {$crmDbName}invoice_daily_revenues.amount = {$crmDbName}invoice_daily_revenues.amount + insert_data.diff_amount,
            {$crmDbName}invoice_daily_revenues.updated_at = NOW(),
            {$crmDbName}invoice_daily_revenues.note = CONCAT(
                {$crmDbName}invoice_daily_revenues.note,
                " update on duplicate"
            );
        EOD;
        $sql1 = <<<EOD
            delete from {$crmDbName}invoice_daily_revenues tdr where amount < 1;
        EOD;
        try {
            $total = 0;
            $CI = & get_instance();
            $CI->db->query($sql);
            $total += $CI->db->affected_rows();
            $CI->db->query($sql1);
            $total += $CI->db->affected_rows();
            return $total;
        } catch (\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
            return 0;
        }
    }

    /**
     * For job's published before issue date and month is different, the date before issue date will be converted to revenue at the end of expires date.
     * @param string $revenueDate
     * @return int
     */
    public static function convertJobPublishBeforeIssueToRevenue($revenueDate = '')
    {
        $revenueDate = !empty($revenueDate) ? Carbon::createFromFormat('Ymd', $revenueDate) : Carbon::now();
        $revenueDate = $revenueDate->startOfDay();
        $start = $revenueDate->clone()->startOfDay()->format('Y-m-d H:i:s');
        $end = $revenueDate->clone()->endOfDay()->format('Y-m-d H:i:s');
        $crmDbName = APP_DB_NAME . '.' . db_prefix();
        $amsDbName = AMS_DB_NAME;

        $queryData = <<<EOD
            INSERT IGNORE INTO {$crmDbName}invoice_daily_revenues (
                invoice_id,
                itemable_id,
                ams_job_id,
                revenue_date,
                amount,
                package_type,
                note,
                created_at,
                updated_at
            )
            With date_ranges as (
                select
                    '$start' as start_date,
                    '$end' as end_date
            ),
            itemables as (
                WITH ItemSeries AS (
                    SELECT
                        ib.id AS itemable_id,
                        ib.item_id AS item_id,
                        i.group_id,
                        30 as duration,
                        ib.rel_id as invoice_id,
                        i.description as package_name,
                        ib.rate,
                        ib.discount_table,
                        1 AS row_num,
                        (
                            (t.total - t.total_tax) / (
                                select
                                    sum(ibb1.qty)
                                from
                                    {$crmDbName}itemable ibb1
                                where
                                    ibb1.rel_id = ib.rel_id
                                    and ibb1.rel_type = "invoice"
                            )
                        ) as sale_price,
                        ib.qty,
                        (
                            select
                                sum(ibb2.qty)
                            from
                                {$crmDbName}itemable ibb2
                            where
                                ibb2.rel_id = t.id
                                and ibb2.rel_type = "invoice"
                        ) as po_qty,
                        t.total - t.total_tax as po_amount
                    FROM
                        {$crmDbName}itemable ib
                        join {$crmDbName}items i on i.id = ib.item_id
                        join {$crmDbName}invoices t on t.id = ib.rel_id
                        and ib.rel_type = "invoice"
                    where
                        ib.rel_type = "invoice"
                )
                select
                    iss.*,
                    ROUND(iss.sale_price / iss.duration) as revenue_per_day
                from
                    ItemSeries iss
            ),
            service_used as (
                select
                    *,
                    crm_itemable_id as itemable_id
                from
                    {$crmDbName}client_ams_services tas
                where
                    1 = 1
            ),
            combo_used as (
                select
                    *,
                    crm_itemable_id as itemable_id,
                    (
                        select
                            MIN(ccl.created_at)
                        from
                            {$amsDbName}.company_credit_logs ccl
                            join {$amsDbName}.company_credit_logs ccl2 on ccl.company_search_package_id = ccl2.id
                        where
                            ccl2.company_search_package_id = casp.ams_company_search_package_id
                            AND ccl.type = 'Out'
                            AND ccl.refund_approved_at IS NULL
                    ) used_at
                from
                    {$crmDbName}client_ams_search_packages casp
                where
                    1 = 1
            ),
            job_advanced_revenue as (
                select
                    j.id as ams_job_id,
                    ib.itemable_id,
                    j.published_at,
                    taj.invoice_id,
                    min.invoice_issued_date,
                    taj.package_id,
                    DATE_ADD(
                        DATE_ADD(min.invoice_issued_date, INTERVAL 1 YEAR),
                        INTERVAL -1 DAY
                    ) as package_expires_date,
                    DATEDIFF(j.published_at, min.invoice_issued_date) as advanced_dates,
                    ib.package_name,
                    ib.duration,
                    ib.sale_price,
                    ib.revenue_per_day,
                    ib.revenue_per_day * abs(
                        DATEDIFF(j.published_at, min.invoice_issued_date)
                    ) as expires_revenue,
                    DATE_ADD(j.published_at, INTERVAL ib.duration - 1 DAY) as expired_at
                from
                    {$amsDbName}.jobs j
                    join {$crmDbName}client_ams_jobs taj on taj.ams_job_id = j.id
                    join {$crmDbName}minvoices min on taj.invoice_id = min.invoice_id
                    join itemables ib on ib.itemable_id = taj.package_id,
                    date_ranges dr
                where
                    1 = 1
                    and j.crm_invoice_id is not null
                    and DATEDIFF(j.published_at, min.invoice_issued_date) < 0
            ),
            service_advanced_revenue as (
                select
                    su.id as ams_job_id,
                    su.itemable_id,
                    su.used_at as published_at,
                    su.invoice_id,
                    min.invoice_issued_date,
                    su.ams_service_id,
                    DATE_ADD(
                        DATE_ADD(min.invoice_issued_date, INTERVAL 1 YEAR),
                        INTERVAL -1 DAY
                    ) as package_expires_date,
                    DATEDIFF(su.used_at, min.invoice_issued_date) as advanced_dates,
                    ib.package_name,
                    ib.duration,
                    ib.sale_price,
                    ib.revenue_per_day,
                    ib.revenue_per_day * abs(
                        DATEDIFF(su.used_at, min.invoice_issued_date)
                    ) as expires_revenue,
                    DATE_ADD(su.used_at, INTERVAL ib.duration - 1 DAY) as expired_at
                from
                    service_used su
                    join {$crmDbName}minvoices min on su.invoice_id = min.invoice_id
                    join itemables ib on ib.itemable_id = su.itemable_id,
                    date_ranges dr
                where
                    1 = 1
                    and DATEDIFF(su.used_at, min.invoice_issued_date) < 0
            ),
            combo_advanced_revenue as (
                select
                    cu.id as ams_job_id,
                    cu.itemable_id,
                    cu.used_at as published_at,
                    cu.invoice_id,
                    min.invoice_issued_date,
                    cu.ams_company_search_package_id,
                    DATE_ADD(
                        DATE_ADD(min.invoice_issued_date, INTERVAL 1 YEAR),
                        INTERVAL -1 DAY
                    ) as package_expires_date,
                    DATEDIFF(cu.used_at, min.invoice_issued_date) as advanced_dates,
                    ib.package_name,
                    ib.duration,
                    ib.sale_price,
                    ib.revenue_per_day,
                    ib.revenue_per_day * abs(
                        DATEDIFF(cu.used_at, min.invoice_issued_date)
                    ) as expires_revenue,
                    DATE_ADD(cu.used_at, INTERVAL ib.duration - 1 DAY) as expired_at
                from
                    combo_used cu
                    join {$crmDbName}minvoices min on cu.invoice_id = min.invoice_id
                    join itemables ib on ib.itemable_id = cu.itemable_id,
                    date_ranges dr
                where
                    1 = 1
                    and DATEDIFF(cu.used_at, min.invoice_issued_date) < 0
            )
        EOD;

        // Insert data for job's publish before issue date and expires after issue date
        // Insert to the issue date
        $sql = <<<EOD
            {$queryData}
            select
                *
            FROM
                (
                    select
                        invoice_id,
                        itemable_id,
                        ams_job_id,
                        {$revenueDate->format('Ymd')} as revenue_date,
                        revenue_per_day * (abs(advanced_dates) - DATEDIFF(invoice_issued_date, DATE_FORMAT(invoice_issued_date, '%Y-%m-01 00:00:00'))) as expires_revenue,
                        package_type,
                        "convertJobPublishBeforeIssueToRevenue diff month, issue between published and expires" as note,
                        NOW() as created_at,
                        NOW() as updated_at
                    from
                        (
                            select *, "job_posting" as package_type
                            from job_advanced_revenue
                            union all
                            select *, "combo" as package_type
                            from service_advanced_revenue
                            union all
                            select *, "service" as package_type
                            from combo_advanced_revenue
                        ) as union_data,
                        date_ranges dr
                    WHERE
                        1=1
                        AND DATE_FORMAT(invoice_issued_date, "%Y%m") > DATE_FORMAT(published_at, "%Y%m")
                        AND DATE_FORMAT(invoice_issued_date, "%Y%m%d") <= DATE_FORMAT(expired_at, "%Y%m%d")
                        AND expired_at between dr.start_date and dr.end_date
                ) as insert_data
            ON DUPLICATE KEY
            UPDATE
                {$crmDbName}invoice_daily_revenues.amount = {$crmDbName}invoice_daily_revenues.amount + insert_data.expires_revenue,
                {$crmDbName}invoice_daily_revenues.updated_at = NOW(),
                {$crmDbName}invoice_daily_revenues.note = "convertJobPublishBeforeIssueToRevenue diff month, issue between published and expires"
            ;
        EOD;

        // Insert data for job's expires before issue date
        // Insert to the issue date
        $sql1 = <<<EOD
            {$queryData}
            select
                *
            FROM
                (
                    select
                        invoice_id,
                        itemable_id,
                        ams_job_id,
                        {$revenueDate->format('Ymd')} as revenue_date,
                        revenue_per_day * duration as expires_revenue,
                        package_type,
                        "convertJobPublishBeforeIssueToRevenue diff month, expire b4 issue" as note,
                        NOW() as created_at,
                        NOW() as updated_at
                    from
                        (
                            select *, "job_posting" as package_type
                            from job_advanced_revenue
                            union all
                            select *, "combo" as package_type
                            from service_advanced_revenue
                            union all
                            select *, "service" as package_type
                            from combo_advanced_revenue
                        ) as union_data,
                        date_ranges dr
                    WHERE
                        1=1
                        AND DATE_FORMAT(invoice_issued_date, "%Y%m%d") > DATE_FORMAT(expired_at, "%Y%m%d")
                        AND invoice_issued_date between dr.start_date and dr.end_date
                ) as insert_data
            ON DUPLICATE KEY
            UPDATE
                {$crmDbName}invoice_daily_revenues.amount = {$crmDbName}invoice_daily_revenues.amount + insert_data.expires_revenue,
                {$crmDbName}invoice_daily_revenues.updated_at = NOW(),
                {$crmDbName}invoice_daily_revenues.note = "convertJobPublishBeforeIssueToRevenue, expire b4 issue"
            ;
        EOD;

        try {
            $total = 0;
            $CI = & get_instance();
            $CI->db->query($sql);
            $total +=  $CI->db->affected_rows();

            $CI->db->query($sql1);
            $total +=  $CI->db->affected_rows();

            return $total;
        } catch (\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
            return 0;
        }
    }

    public static function exportJobPostingDailyRevenue($reportDate)
    {
        $type = 'Invoice Monthly Report - ' . $reportDate;
        $fileName = 'Invoice-monthly-report-' . $reportDate;

        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Set the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->fromArray([
            [
                'Export Date',
                Carbon::now()->format('Y-m-d H:i:s')
            ]
        ], null, 'A1');


        $invoiceTotal = InvoiceDailyRevenue::query()
            ->where('revenue_date', Carbon::createFromFormat('Y.m.d', $reportDate)->format('Ymd'))
            ->sum('amount');

        /**
         * End styling
         */
        $sheet->setCellValue('A2', strtoupper('Summary'));

        // Heading
        $sheet->fromArray([
            [
                'Revennue Total Amount (No tax)',
            ],
            [
                $invoiceTotal ?? '0',
            ]
        ], null, 'A3');

        $selects = [
            'id',
            'invoice_id',
            'itemable_id',
            'ams_job_id',
            'revenue_date',
            'amount',
        ];
        $query = get_job_posting_daily_revenue_query($reportDate, $selects);
        $query = $query->groupBy('ams_job_id');
        $records = $query->get();

        $total = count($records);

        $headingStyles = [
            'font' => [
                'bold' => true
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ];


        $startAtRow = 2;
        $sheet->getStyle('A' . $startAtRow . ':A' . $startAtRow . '')
            ->applyFromArray($headingStyles);

        $startAtRow = 3;
        $sheet->getStyle('A' . $startAtRow . ':A' . $startAtRow . '')
        ->applyFromArray($headingStyles);

        // Purchase Order Details
        $startAtRow = 5;
        $sheet->setCellValue('A' . $startAtRow . '', strtoupper(_l('job_revenue_report_daily_tab_title')));
        $sheet->mergeCells('A' . $startAtRow . ':F' . $startAtRow . '');
        $sheet->getStyle('A' . $startAtRow . '')
            ->applyFromArray($headingStyles);

        // Heading
        $startAtRow += 1;
        $sheet->fromArray([
            [
                _l('job_revenue_report_daily_po_no_title'),
                _l('job_revenue_report_daily_invoice_no_title'),
                'Issue Date',
                _l('job_revenue_report_daily_package_title'),
                _l('job_revenue_report_daily_date_title'),
                _l('job_revenue_report_daily_revenue_title'),
            ]
        ], null, 'A' . $startAtRow . '');

        $sheet->getStyle('A' . $startAtRow . ':F' . $startAtRow . '')
            ->applyFromArray($headingStyles);

        $ranges = $records->map(function ($tran) {
            $amount = Carbon::parse($tran->invoice->date)->year < 2024 ? 0 : $tran->amount;

            return [
                format_invoice_number($tran->invoice),
                isset($tran->invoice->minvoice->invoice_number) ? intval($tran->invoice->minvoice->invoice_number) : '',
                isset($tran->invoice->minvoice->invoice_issued_date) ? Date::PHPToExcel(Carbon::parse($tran->invoice->minvoice->invoice_issued_date)->format('Y-m-d')) : '',
                $tran->itemable->item->description ?? '',
                Date::PHPToExcel(Carbon::createFromFormat('Ymd', $tran->revenue_date)->format('Y-m-d')),
                $amount,
            ];
        });

        $total = $records->count();

        $startAtRow += 1;
        $sheet->getStyle('C' . $startAtRow . ':C' . ($total + 6))
            ->getNumberFormat()
            ->setFormatCode('YYYY.MM.DD');

            $sheet->getStyle('E' . $startAtRow . ':E' . ($total + 6))
                ->getNumberFormat()
                ->setFormatCode('YYYY.MM.DD');

        $sheet->getStyle('F' . $startAtRow . ':F' . ($total + 6))
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->fromArray($ranges->toArray(), null, 'A' . $startAtRow . '');
        self::setAutoResizeColumns($sheet, 'A', 'F');
        self::toExcelFile($type, $fileName, $spreadsheet);
    }

    public static function exportJobPostingMonthlyRevenue($reportMonth)
    {
        $type = 'Invoice Monthly Report - ' . $reportMonth;
        $fileName = 'Invoice-monthly-report-' . $reportMonth;

        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Set the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->fromArray([
            [
                'Export Date',
                Carbon::now()->format('Y-m-d H:i:s')
            ]
        ], null, 'A1');

        $selects = [
            DB::raw('count(id) as duration'),
            'invoice_id',
            'itemable_id',
            'ams_job_id',
            // 'revenue_date',
            DB::raw('sum(amount) as amount'),
            'package_type'
        ];
        $query = get_job_posting_monthly_revenue_query($reportMonth, $selects);
        $query = $query->groupBy(['invoice_id', 'itemable_id', 'ams_job_id']);
        $records = $query->get();

        $jobIds = collect($records)->filter(fn($row) => $row->isJobPosting())->pluck('ams_job_id')->filter()->unique();
        $serviceIds = collect($records)->filter(fn($row) => $row->isService())->pluck('ams_job_id')->filter()->unique();
        $comboIds = collect($records)->filter(fn($row) => $row->isCombo())->pluck('ams_job_id')->filter()->unique();
        $jobs = $services = $combos = collect();
        if ($jobIds->count()) {
            [$hasNextPage, $jobs] = ReportService::fetchOpenJobs(1, $jobIds->toArray(), $jobIds->count(), true);
            $jobs = $jobs->keyBy('id');
        }

        if ($serviceIds->count()) {
            $services = ClientAmsService::select('id', 'used_at')->whereIn('id', $serviceIds)->get()->keyBy('id');
        }

        $requestComboIds = collect();
        if ($comboIds->isNotEmpty()) {
            $requestComboIds = ClientAmsSearchPackage::whereIn('id', $comboIds->toArray())->pluck('ams_company_search_package_id', 'id');
            $combos = collect(ReportService::fetchAmsCombo($requestComboIds->values()->toArray()));
        }

        // Heading
        $monthText = Carbon::createFromFormat('Y.m.d', $reportMonth . '.01')->format('F, Y');

        // Summary
        $sheet->mergeCells('A2:D2');

        $headingStyles = [
            'font' => [
                'bold' => true
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ];

        /**
         * Styling
         */
        $sheet->getStyle('A2')
            ->applyFromArray($headingStyles);

        $sheet->getStyle('A3:D3')
            ->applyFromArray($headingStyles);

        $sheet->getStyle('A4:D4')
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->getStyle('A5:E5')
            ->applyFromArray($headingStyles);

        $invoiceTotalByMonth = 0;
        $totals = get_total_amount_job_posting_monthly_revenue_query($reportMonth);
        $totals->each(function ($item) use (&$invoiceTotalByMonth) {
            $amount = Carbon::parse($item->invoice->date)->year < 2024 ? 0 : $item->amount;
            $invoiceTotalByMonth += $amount;
        });

        /**
         * End styling
         */
        $sheet->setCellValue('A2', strtoupper('Summary'));

        // Heading
        $sheet->fromArray([
            [
                'Revenue Amount (No tax) (' . $monthText . ')',
            ],
            [
                $invoiceTotalByMonth ?? '0',
            ]
        ], null, 'A3');


        $total = count($records);

        $headingStyles = [
            'font' => [
                'bold' => true
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ];

        $startAtRow = 2;
        $sheet->getStyle('A' . $startAtRow . ':E' . $startAtRow . '')
            ->applyFromArray($headingStyles);

        // Purchase Order Details
        $startAtRow = 5;
        $sheet->setCellValue('A' . $startAtRow . '', strtoupper(_l('job_revenue_report_monthly_tab_title')) . ' (' . $monthText . ')');
        $sheet->mergeCells('A' . $startAtRow . ':K' . $startAtRow . '');
        $sheet->getStyle('A' . $startAtRow . '')
            ->applyFromArray($headingStyles);

        $startAtRow += 1;
        $sheet->setCellValue('A' . $startAtRow . '', strtoupper(_l('job_revenue_report_monthly_inv_info_title')));
        $sheet->mergeCells('A' . $startAtRow . ':E' . $startAtRow . '');
        $sheet->getStyle('A' . $startAtRow . '')->applyFromArray($headingStyles);

        $sheet->setCellValue('F' . $startAtRow . '', strtoupper(_l('job_revenue_report_monthly_jd_info_title')));
        $sheet->mergeCells('F' . $startAtRow . ':I' . $startAtRow . '');
        $sheet->getStyle('F' . $startAtRow . '')->applyFromArray($headingStyles);

        $sheet->setCellValue('J' . $startAtRow . '', strtoupper(_l('job_revenue_report_monthly_report_month_title') . ' (' . $monthText . ')'));
        $sheet->mergeCells('J' . $startAtRow . ':K' . $startAtRow . '');
        $sheet->getStyle('J' . $startAtRow . '')->applyFromArray($headingStyles);

        // Heading
        $startAtRow += 1;
        $sheet->fromArray([
            [
                _l('job_revenue_report_monthly_po_no_title'),
                _l('job_revenue_report_monthly_invoice_no_title'),
                'Issue Date',
                _l('job_revenue_report_monthly_customer_title'),
                _l('job_revenue_report_daily_package_title'),
                _l('job_revenue_report_monthly_start_title'),
                _l('job_revenue_report_monthly_end_title'),
                _l('job_revenue_report_monthly_package_duration_title'),
                _l('job_revenue_report_monthly_total_amount_title'),
                _l('job_revenue_report_monthly_duration_title') . ' (' . $monthText . ')',
                _l('job_revenue_report_monthly_amount_title') . ' (' . $monthText . ')',
                'ams_job_id'
            ]
        ], null, 'A' . $startAtRow . '');
        $sheet->getStyle('A' . $startAtRow . ':K' . $startAtRow . '')->applyFromArray($headingStyles);

        $ranges = [];
        foreach ($records as $aRow) {
            $invoice = $aRow->invoice;
            $itemable = $aRow->itemable;
            $qty = $itemable->qty;
            // Minus voucher
            $invoiceSubtotal = $invoice->total - $invoice->total_tax;
            $unitPrice = $invoiceSubtotal / intval($invoice->invoice_items_sum_qty);
            $floorSalePrice = floor($unitPrice / 1000) * 1000;
            $salePrice = $floorSalePrice * $qty;

            $start = $end = '';

            if ($aRow->isService()) {
                $service  = $services->get($aRow->ams_job_id);
                $start = $service ? Carbon::parse($service->used_at) : '';
            } else if ($aRow->isCombo()) {
                $serviceId = $requestComboIds->get($aRow->ams_job_id);
                $combo  = $serviceId ? $combos->get($serviceId) : null;
                $start = $combo ? Carbon::parse($combo['created_at']) : '';
            } else if ($aRow->isJobPosting()) {
                $job = $jobs->get($aRow->ams_job_id);
                $start = empty($job['published']['date']) ? '' : Carbon::createFromFormat('d-m-Y', $job['published']['date']);
                $end = empty($job['closed']['date']) ? '' : Carbon::createFromFormat('d-m-Y', $job['closed']['date']);
            }

            $packageDuration = in_array($aRow->itemable->item_id, [4, 23]) ? 60 : 30;

            $amount = Carbon::parse($aRow->invoice->date)->year < 2024 ? 0 : $aRow->amount;

            $ranges[] = [
                format_invoice_number($aRow->invoice),
                isset($aRow->invoice->minvoice->invoice_number) ? intval($aRow->invoice->minvoice->invoice_number) : '',
                isset($aRow->invoice->minvoice->invoice_issued_date) ? Date::PHPToExcel(Carbon::parse($aRow->invoice->minvoice->invoice_issued_date)->format('Y-m-d')) : '',
                $aRow->invoice->client->business_name,
                $aRow->itemable->item->description ?? '',
                $start ? Date::PHPToExcel($start->format('Y-m-d')) : '',
                $end ? Date::PHPToExcel($end->format('Y-m-d')) : '',
                $packageDuration,
                $salePrice,
                !empty($start) ? $aRow->duration : 0,
                $amount,
                $aRow->ams_job_id,
            ];
        }

        $startAtRow += 1;
        $total = count($ranges);

        $sheet->getStyle('C' . $startAtRow . ':C' . ($total + $startAtRow))
            ->getNumberFormat()
            ->setFormatCode('YYYY.MM.DD');

        $sheet->getStyle('F' . $startAtRow . ':G' . ($total + $startAtRow))
            ->getNumberFormat()
            ->setFormatCode('YYYY.MM.DD');

        $sheet->getStyle('I' . $startAtRow . ':K' . ($total + $startAtRow))
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->fromArray($ranges, null, 'A' . $startAtRow . '');
        self::setAutoResizeColumns($sheet, 'A', 'K');
        self::toExcelFile($type, $fileName, $spreadsheet);
    }

    /**
     * Fetch jobs with already map invoice from the ams system
     *
     * @param array $comboIds
     * @param array $options
     * @return array
     */
    public static function fetchAmsCombo(array $comboIds, $options = [])
    {
        // Fetch ams companies first
        $queries = array_filter([
            'ids' => implode(',', $comboIds)
        ]);

        // More options for the query
        $queries = array_merge($queries, $options);

        $amsResponse = AmsService::amsApi('crm/company-credits/first-out-credits', [
            'query' => $queries
        ], 'get') ?? [];

        return $amsResponse['data'] ?? [];
    }
}

<?php

namespace app\services;

use GuzzleHttp\Client;

defined('BASEPATH') or exit('No direct script access allowed');

class GoogleChatService
{
    public static function send($webhookUrl, array $message)
    {
        try {
            $client = new Client();
            $client->post($webhookUrl, [
                'json' => $message
            ]);
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $response = $e->getResponse();
            $responseBodyAsString = $response->getBody()->getContents();
            throw new \Exception($responseBodyAsString);
        } catch (\Exception $ex) {
            throw $ex;
        }
    }
}

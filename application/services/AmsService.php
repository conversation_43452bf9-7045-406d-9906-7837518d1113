<?php

namespace app\services;

use GuzzleHttp\Client;

defined('BASEPATH') or exit('No direct script access allowed');

class AmsService
{
    public static function search($uri, $options, $method = 'get')
    {
        clock()->event('ams-search:'.$uri)->color('purple')->begin();
        try {
            $client = new Client([
                'base_uri' => AMS_API_URL,
                'verify' => false,
                'allow_redirects' => true,
                'connect_timeout' => 30
            ]);

            $response = $client->request($method, $uri, $options);
            clock()->event('ams-search:'.$uri)->end();
            return json_decode((string)$response->getBody(), true);
        } catch (\Exception $ex) {
            clock()->event('ams-search:'.$uri)->end();
            throw $ex;
        }
    }

    public static function amsApi($uri, $options = [], $method = 'post')
    {
        clock()->event('ams-api:'.$uri)->color('purple')->begin();
        try {
            $client = new Client([
                'base_uri' => AMS_API_URL,
                'verify' => false,
                'allow_redirects' => true,
                'connect_timeout' => 30
            ]);
            
            $staffEmail = is_staff_logged_in() ? get_staff()->email : null;

            $options = array_merge($options, [
                'headers' => [
                    'Crm-Auth' => CRM_PUBLIC_KEY,
                    'Crm-Email' => is_cli() && !empty($options['headers']['Crm-Email']) ? $options['headers']['Crm-Email'] : $staffEmail
                ]
            ]);

            $response = $client->request($method, $uri, $options);
            clock()->event('ams-api:'.$uri)->end();
            return json_decode((string)$response->getBody(), true);
        } catch (\Exception $ex) {
            clock()->event('ams-api:'.$uri)->end();
            throw $ex;
        }
    }
}

<?php

namespace app\services;

use Carbon\Carbon;
use Entities\Itemable;
use Entities\ReportClientAmsJob;
use Exception;
use GuzzleHttp\Client;

defined('BASEPATH') or exit('No direct script access allowed');

class CallioService
{
    public static function callioApi($uri, $options = [], $method = 'post')
    {
        clock()->event('callio-api:' . $uri)->color('purple')->begin();
        try {
            $client = new Client([
                'base_uri' => CALLIO_API_DOMAIN,
                'verify' => false,
                'allow_redirects' => true,
                'connect_timeout' => 30
            ]);

            $options = array_merge($options, [
                'headers' => [
                    'token' => CALLIO_API_TOKEN,
                ]
            ]);

            $response = $client->request($method, $uri, $options);
            clock()->event('callio-api:' . $uri)->end();
            return json_decode((string)$response->getBody(), true);
        } catch (\Exception $ex) {
            clock()->event('callio-api:' . $uri)->end();
            throw $ex;
        }
    }

    public static function getUserAuth($email, $ipPhone)
    {
        $response = self::callioApi('user', [
            'query' => [
                'page' => 1,
                'size' => 10,
                'keyword' => $email
            ]
        ], 'GET');
        $docs = collect($response['docs'] ?? []);

        return $docs->filter(function ($user) use ($email, $ipPhone) {
            return $user['ext'] == $ipPhone && $user['email'] == $email;
        })->first();
    }

    public static function getCallLogs($from, $to, $page)
    {
        $calls = self::callioApi('call', [
            'query' => [
                'page' => $page,
                'pageSize' => 500,
                'from' => $from,
                'to' => $to
            ]
        ], 'get');

        return [$calls['docs'] ?? [], $calls['hasNextPage'] ?? false];
    }
}

<?php

namespace app\services;

use Entities\ClientAmsCompany;
use Entities\ClientAmsJob;
use Entities\ClientAmsOpenJob;
use Entities\Invoice;
use Entities\Item;
use Entities\Itemable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

defined('BASEPATH') or exit('No direct script access allowed');

class AmsPackageService
{
    /**
     * Fetch all available invoices that in cluding job posting package which is related to AMS company id
     *
     * @param integer $amsCompanyId
     * @param array $orders list of fields that will be sorted. Each items should have key and direction if defined.
     * @return array
     */
    public static function get_invoice_packages($amsCompanyId, $ranges = [], $page = 1, $perPage = 5, $orders = [])
    {
        // Get paid invoice of these crm clients
        $invoices = Invoice::query()
            ->select('id', 'clientid', 'use_expired_at', 'date')
            ->inAmsCompany($amsCompanyId)
            ->whereHas('invoiceItems', fn(Builder $query) => $query->isNotCredit()->useNotExpired())
            ->when(count($ranges), fn($query) => $query->useExpiredAtBetween($ranges))
            ->with([
                'invoiceItems' => fn ($query) =>
                    $query->select('id', 'rel_id', 'qty', 'item_id', 'rate', 'use_expired_at')
                        ->isNotCredit()
                        ->paidItems()
                        ->withCount('amsOpenJobs')
                        ->with('item:id,description'),
                'paymentRecord:invoiceid,date'
            ])
            ->paid();

        // If having orders, then build it first
        if (count($orders)) {
            foreach ($orders as $order) {
                $invoices->orderBy($order['key'], $order['direction']);
            }
        }
        $invoices->orderBy('id', 'desc');
        $invoices = $invoices->paginate($perPage, ['*'], 'page', $page);

        return $invoices->through(fn($invoice) => [
            'invoice_id' => $invoice->id,
            'invoice_number' => 'PO-' . $invoice->date->format('dmy'),
            'total_packages' => $invoice->invoiceItems->sum('qty'),
            'purchase_date' => ! is_null($invoice->paymentRecord->date) ? $invoice->paymentRecord->date->format('d/m/Y') : null,
            'packages' => $invoice->invoiceItems->map(fn($item) => [
                'id' => $item->id,
                'package_name' => $item->item->description,
                'qty' => intval($item->qty),
                'free_package' => intval($item->rate) == 0,
                'used' => $item->ams_open_jobs_count,
                'remain' => max(0, ($item->isUseNotExpired() ? intval($item->qty) : 0) - $item->ams_open_jobs_count),
                'use_expired_at' => $item->use_expired_at->format('d/m/Y'),
            ])->toArray()
        ]);
    }

    /**
     * Fetch all available invoices
     *
     * @param integer $amsCompanyId
     * @param integer $selectedInvoiceId
     * @param integer $selectedAmsPackageId
     * @param integer $unpaidInvoice
     * @return Collection
     */
    public static function get_available_invoices(
        $amsCompanyId,
        $selectedInvoiceId = null,
        $selectedAmsPackageId = null,
        $unpaidInvoice = null
    ) {
        // Get paid invoice of these crm clients
        return Invoice::query()
            ->select('id', 'number', 'number_format', 'prefix', 'date', 'use_expired_at')
            ->where(
                fn($query)
                => $query->inAmsCompany($amsCompanyId)
                    ->whereHas(
                        'invoiceItems',
                        fn(Builder $query) => $query->isNotCredit()
                            ->hasOpenJobs()
                            ->when(is_null($unpaidInvoice), fn($query) => $query->useNotExpired())
                            ->when($selectedAmsPackageId, fn($query) => $query->hasAmsPackage($selectedAmsPackageId))
                    )
                    ->when(is_null($unpaidInvoice), fn($query) => $query->paid())
            )
            ->when(
                $selectedInvoiceId,
                fn($orWhere)
                => $orWhere->orWhere('id', $selectedInvoiceId)
            )
            ->when($unpaidInvoice, fn($query)=> $query->notDraft())
            ->orderBy('id', 'desc')
            ->get()
            ->map(fn($invoice) => [
                'id' => $invoice->id,
                'invoice_number' => format_invoice_number($invoice)
            ])
            ->pluck('invoice_number', 'id');
    }

    /**
     * Check ams company has available job posting package or not
     * @param mixed $amsCompanyId
     * @return boolean True - they have available package, otherwise, they not have any packages
     */
    public static function has_available_packages($amsCompanyId)
    {
        return Itemable::query()
            ->select('id', 'rel_id', 'qty', 'rate', 'item_id')
            ->isNotCredit()
            ->paidItems()
            ->invoiceType()
            ->withCount('amsOpenJobs')
            ->with('invoice:id,date')
            ->useNotExpired()
            ->whereHas(
                'invoice',
                fn(Builder $query) => $query
                    ->paid()
                    ->inAmsCompany($amsCompanyId)
            )
            ->hasOpenJobs()
            ->exists();
    }

    /**
     * Get available packages of the ams company id
     *
     * @param mixed $amsCompanyId
     * @param string $packageId need fetch this when editing
     * @return boolean|Collection
     */
    public static function get_available_packages($amsCompanyId, $packageId = null, $paidOnly = false)
    {
        $jobPostingItems = Itemable::query()
            ->select('id', 'rel_id', 'qty', 'rate', 'item_id', 'use_expired_at')
            ->isNotCredit()
            ->invoiceType()
            ->withCount('amsOpenJobs')
            ->with('invoice:id,date,number,prefix,number_format')
            ->useNotExpired()
            ->whereHas(
                'invoice',
                fn(Builder $query) => $query
                    ->paid()
                    ->inAmsCompany($amsCompanyId)
            )
            ->paidItems()
            ->hasOpenJobs()
            ->when($paidOnly, fn($query) => $query->where('rate', '>', 0))
            ->orderBy('rel_id')
            ->get();

        // Find this package for editing
        if ($packageId) {
            [$amsPackageId, $invoiceId, $isFreePackage] = explode(':', $packageId);
            $editItemable = Itemable::query()
                ->select('id', 'rel_id', 'qty', 'rate', 'item_id', 'use_expired_at')
                ->whereHas('invoice', fn($query) => $query->where('id', $invoiceId))
                ->whereHas('item.amsPackage', fn($query) => $query->where('ams_package_id', $amsPackageId))
                ->with(['invoice:id,date,number,prefix,number_format'])
                ->invoiceType()
                ->first();

            // Push that item to job posting for build data
            if ($editItemable) {
                $jobPostingItems->add($editItemable);
            }
        }

        $items = Item::query()
            ->select('id', 'description')
            ->with('amsPackage')
            ->whereIn('id', $jobPostingItems->pluck('item_id'))
            ->get()
            ->keyBy('id');

        return $jobPostingItems->map(function ($item) use ($items) {
            $amsItem = $items->get($item->item_id, collect());
            $expiredAt = $item->use_expired_at ?? null;
            $invoice = $item->invoice;
            
            return [
                'id' => $item->id,
                'item_id' => $item->item_id,
                'invoice_id' => $item->rel_id,
                'invoice_number' => 'PO' . $invoice->date->format('dmy'), // Keep the existing format for backward compatibility
                'po_number' => format_invoice_number($invoice->id), // Add the actual PO number
                'use_expired_at' => $expiredAt ? $expiredAt->valueOf() : null,
                'expired_at' => $expiredAt ? $expiredAt->format('d/m/Y') : null,
                'ams_package_id' => $amsItem->amsPackage->ams_package_id,
                'free_package' => intval($item->rate) == 0,
                'description' => $amsItem->description,
                'total_packages' => intval($item->qty),
                'available_packages' => max(0, intval($item->qty) - $item->ams_open_jobs_count),
            ];
        })
        ->unique(fn($item) => $item['ams_package_id'] . $item['invoice_id'] . $item['item_id'] . $item['free_package'])
        ->sortBy([
            ['use_expired_at', 'asc'],
            ['ams_package_id', 'asc'],
        ])
        ->values();
    }

    /**
     * Fetch all available package by that invoice
     *
     * @param integer $invoiceId
     * @param integer $selectedAmsPackageId
     * @param integer $unpaidInvoice
     * @return array
     */
    public static function get_packages_by_invoice($invoiceId, $selectedAmsPackageId = null, $unpaidInvoice = null)
    {
        return Itemable::query()
            ->select('id', 'rel_id', 'item_id')
            ->isNotCredit()
            ->paidItems()
            ->invoiceType()
            ->when(is_null($unpaidInvoice), fn($query) => $query->useNotExpired())
            ->with([
                'item:id',
                'item.amsPackage:crm_item_id,ams_package_id'
            ])
            ->whereHas(
                'invoice',
                fn(Builder $query) => $query
                    ->when(is_null($unpaidInvoice), fn($query) => $query->paid())
                    ->where('id', $invoiceId)
            )
            ->where(fn($query)
            => $query->hasOpenJobs()
                ->when($selectedAmsPackageId, fn($builder)
                => $builder->orWhere(fn($orWhere) => $orWhere->hasAmsPackage($selectedAmsPackageId))))
            ->get()
            ->pluck('item.amsPackage.ams_package_id')
            ->unique()
            ->toArray();
    }

    /**
     * It will store/update ams open job to calculate later, actions
     *  - AMS: Change status from Review => Open in case job has invoice id and package id.
     *  - CRM: CS edit job then select invoice + package
     *
     * @param integer $amsCompanyId
     * @param integer $jobId
     * @param integer|null $invoiceId
     * @param integer|null $itemId from AMS
     * @param integer|null $itemableId from Emp
     * @return void
     */
    public function store_ams_job_open($amsCompanyId, $jobId, $invoiceId = null, $itemId = null, $itemableId = null)
    {
        // In case not found invoice or package, no need to add open jobs
        if (empty($amsCompanyId) || (empty($itemId) && empty($itemableId))) {
            return;
        }

        $invoice = Invoice::query()->paid()->useNotExpired()->where('id', $invoiceId)->first();

        $itemable = Itemable::query()
            ->select('id', 'rel_id', 'qty', 'item_id', 'rate')
            ->isNotCredit()
            ->invoiceType()
            ->useNotExpired()
            ->whereHas('invoice', fn(Builder $query) => $query->paid()->where('id', $invoiceId))
            ->when($itemId, fn($query) => $query->where('item_id', $itemId))
            ->when($itemableId, fn($query) => $query->where('id', $itemableId))
            ->oldest('id')
            ->first();

        // Insert Job mapping

        if ($itemable) {
            ClientAmsJob::insert([
                'client_id' => $invoice->clientid,
                'ams_company_id' => $amsCompanyId,
                'ams_job_id' => $jobId,
                'invoice_id' => $invoiceId,
                'package_id' => $itemable->id,
                'free_package' => $itemable->rate > 0
            ]);

            // Insert Open Job Mapping
            ClientAmsOpenJob::updateOrCreate(
                ['ams_job_id' => $jobId],
                [
                    'client_id' => $invoice->clientid,
                    'ams_company_id' => $amsCompanyId,
                    'invoice_id' => $invoiceId,
                    'itemable_id' => $itemable->id,
                ]
            );
        }
    }

    /**
     * Handle logic sync invoice from AMS
     *
     * @param int $amsCompanyId
     * @param int $amsJobId
     * @param int|null $crmInvoiceId
     * @param int|null $amsPackageId
     * @param bool $isFreePackage
     *
     * @return array
     */
    public static function sync_invoice(
        $amsCompanyId,
        $amsJobId,
        $crmInvoiceId = null,
        $amsPackageId = null,
        $isFreePackage = null,
        $needSyncOpenJob = false
    ) {
        if (!$crmInvoiceId) {
            // Invoice is empty, then create mapping for all clients map with the ams company id
            $mappedClientIds = ClientAmsCompany::where('ams_company_id', $amsCompanyId)->pluck('client_id');
            if ($mappedClientIds->count()) {
                $mappedClientIds->each(function ($id) use ($amsJobId, $amsCompanyId) {
                    ClientAmsJob::updateOrCreate(
                        ['ams_job_id' => $id],
                        [
                            'client_id' => $amsJobId,
                            'ams_company_id' => $amsCompanyId,
                        ]
                    );
                });
            }
            return [];
        }

        $invoice = Invoice::where('id', $crmInvoiceId)
            ->select('id', 'clientid')
            ->with([
                'invoiceItems' => fn($query)
                => $query->select('id', 'rel_id', 'rel_type', 'item_id', 'rate')
                    ->isNotCredit()
                    ->hasOpenJobs()
                    ->when($amsPackageId, fn($query) => $query->hasAmsPackage($amsPackageId))
                    ->when($isFreePackage, fn($query) => $query->isDiscountPackage())
                    ->when($isFreePackage === false, fn($query) => $query->isNotDiscountPackage())
                    // If not define, take free package as high priority if exists
                    ->when($isFreePackage === null, fn($query) => $query->orderBy('rate', 'ASC'))
                    ->limit(1)
            ])
            ->notDraft()
            ->first();

        if ($invoice) {
            $invoiceItem = $invoice->invoiceItems->first() ?? null;
            // Create job mapping
            ClientAmsJob::updateOrCreate(
                ['ams_job_id' => $amsJobId],
                [
                    'client_id' => $invoice->clientid,
                    'ams_company_id' => $amsCompanyId,
                    'invoice_id' => $invoice->id,
                    'package_id' => $invoiceItem->id ?? null,
                    'free_package' => ($invoiceItem->rate ?? 1) == 0
                ]
            );

            if ($invoiceItem) {
                // Assign open job
                $needSyncOpenJob && ClientAmsOpenJob::updateOrCreate(
                    ['ams_job_id' => $amsJobId],
                    [
                        'client_id' => $invoice->clientid,
                        'ams_company_id' => $amsCompanyId,
                        'invoice_id' => $invoice->id,
                        'itemable_id' => $invoiceItem->id,
                    ]
                );
                return [
                    'is_free_package' => $isFreePackage !== null ? ($invoiceItem->rate ?? 1) == 0 : null
                ];
            } else {
                $needSyncOpenJob && ClientAmsOpenJob::where('ams_job_id', $amsJobId)->delete();
            }
        }

        return [];
    }
}

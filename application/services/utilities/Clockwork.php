<?php

namespace app\services\utilities;

use Clockwork\Support\Vanilla\Clockwork as VanillaClockwork;

defined('BASEPATH') or exit('No direct script access allowed');

class Clockwork extends VanillaClockwork
{
    /**
     * @override
     * Since we used set_exception_handler, register_shutdown_function to capture all requests
     * Some ajax request sent header already so it throw exception if clockwork add header to response
     * Set to true for disable clockwork's headers
     */
    protected $headersSent = true;
}

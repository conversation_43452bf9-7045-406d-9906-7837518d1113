<?php

namespace app\services;

use Dto\Infoplus\CreateECollectionDTO;
use Dto\Infoplus\CreateECollectionRecvDTO;
use Dto\Infoplus\CreatePayerDTO;
use Dto\Infoplus\InquiryEcCodeDTO;
use Dto\Infoplus\InquiryEcTransDTO;
use Dto\Infoplus\MapECollectionDTO;
use Dto\Infoplus\QrCodeDTO;
use Dto\Infoplus\UpdateECollectionRecvDTO;
use Dto\Minvoice\AdjustSignedInvoiceDTO;
use Dto\Minvoice\CreateInvoiceDTO;
use Dto\Minvoice\CreateInvoiceTT32DTO;
use Dto\Minvoice\InquiryInvoiceDTO;
use Dto\Minvoice\ReplaceSignedInvoiceDTO;
use Dto\Minvoice\UpdateInvoiceDTO;
use GuzzleHttp\Client;

defined('BASEPATH') or exit('No direct script access allowed');

class AccountingService
{
    private static function makeSignature()
    {
        $privateKey = base64_decode(ACCOUNTING_PRIVATE_KEY);

        // Create a signature using the private key
        openssl_sign(ACCOUNTING_SIGN_DATA, $signature, $privateKey, OPENSSL_ALGO_SHA256);

        // Encode the signature to make it safe for transmission
        return base64_encode($signature);
    }

    protected static function request($uri, $options = [], $method = 'post')
    {
        clock()->event('accounting-api:' . $uri)->color('purple')->begin();
        try {
            $client = new Client([
                'base_uri' => ACCOUNTING_BASE_URL . '/',
                'verify' => false,
                'allow_redirects' => true,
                'connect_timeout' => 30
            ]);

            $options = array_merge($options, [
                'headers' => [
                    'x-service-name' => ACCOUNTING_SERVICE_NAME,
                    'x-signature' => static::makeSignature(),

                ]
            ]);

            $response = $client->request($method, $uri, $options);
            clock()->event('accounting-api:' . $uri)->end();
            return json_decode((string)$response->getBody(), true);
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $response = $e->getResponse();
            $responseBodyAsString = $response->getBody()->getContents();
            throw new \Exception($responseBodyAsString);
        }  catch (\Throwable $ex) {
            clock()->event('accounting-api:' . $uri)->end();
            throw $ex;
        }
    }

    public static function check()
    {
        try {
            $response = static::request('payments/check', [], 'GET');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function getPayer(string $payerNo)
    {
        try {
            $response = static::request('payers/' . $payerNo, [], 'GET');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    /**
     * Create payers
     *
     * @param CreatePayerDTO[] $payers
     * @return array
     */
    public static function createPayer(array $payers)
    {
        try {
            $response = static::request('payers', [
                'json' => array_map(function (CreatePayerDTO $payer) {
                    return $payer->toArray();
                }, $payers)
            ]);
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function updatePayer(string $payerNo, array $payer)
    {
        try {
            $response = static::request('payers/' . $payerNo, [
                'json' => array_filter([
                    'payerName' => $payer['payerName'],
                    'phoneNo' => $payer['phoneNo'],
                    'email' => $payer['email'] ?? null,
                    'remark' => $payer['remark'] ?? null,
                    'smsUseYn' => $payer['smsUseYn'] ?? null,
                    'zaloUseYn' => $payer['zaloUseYn'] ?? null,
                    'autoMapYn' => $payer['autoMapYn'] ?? null,
                    'benefitAccntNo' => $payer['benefitAccntNo'] ?? null,
                    'nationality' => $payer['nationality'] ?? null
                ])
            ], 'PUT');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function deletePayer(string $payerNo)
    {
        try {
            $response = static::request('payers/' . $payerNo, [], 'DELETE');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }
    public static function getInvoice(string $uuid)
    {
        try {
            $response = static::request('invoices/' . $uuid, [], 'GET');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function getInvoices(InquiryInvoiceDTO $query)
    {
        try {
            $response = static::request('invoices', [
                'query' => $query->toArray()
            ], 'GET');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function createDraftInvoice(CreateInvoiceDTO $createInvoice)
    {
        try {
            $response = static::request('invoices', [
                'json' => $createInvoice->toArray()
            ]);
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function createDraftInvoiceTT32(CreateInvoiceTT32DTO $createInvoice)
    {
        try {
            $response = static::request('invoices/tt32', [
                'json' => $createInvoice->toArray()
            ]);
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function updateDraftInvoice(string $invoiceUuid, UpdateInvoiceDTO $updateInvoice)
    {
        try {
            $response = static::request('invoices/' . $invoiceUuid, [
                'json' => $updateInvoice->toArray()
            ], 'PUT');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function updateDraftInvoiceTT32(string $invoiceUuid, CreateInvoiceTT32DTO $updateInvoice)
    {
        try {
            $response = static::request('invoices/' . $invoiceUuid . '/tt32', [
                'json' => $updateInvoice->toArray()
            ], 'PUT');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function deleteDraftInvoice(array $invoiceInfo)
    {
        try {
            $response = static::request('invoices', [
                'json' => [
                    'inv_invoiceSeries' => $invoiceInfo['inv_invoiceSeries'],
                    'inv_invoiceNumber' => $invoiceInfo['inv_invoiceNumber'] ?? null,
                    'inv_invoiceAuth_Id' => $invoiceInfo['inv_invoiceAuth_Id'] ?? null,
                ]
            ], 'DELETE');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function signInvoice(string $invoiceUuid)
    {
        try {
            $response = static::request('invoices/' . $invoiceUuid . '/sign', []);
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    /**
     * Adjust signed invoice but not sign yet
     *
     * @param string $invoiceUuid
     * @param array $invoiceInfo
     * @param array $invoiceItems
     * @return array
     */
    public static function adjustSignedInvoice(string $invoiceUuid, AdjustSignedInvoiceDTO $adjustInvoice)
    {
        try {
            $response = static::request('invoices/signed/' . $invoiceUuid . '/adjust', [
                'json' => $adjustInvoice->toArray()
            ], 'PUT');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    /**
     * Replace signed invoice by using new once
     *
     * @param array $invoiceInfo
     * @param array $invoiceItems
     * @return array
     */
    public static function replaceSignedInvoice(string $invoiceUuid, ReplaceSignedInvoiceDTO $replaceInvoice, array $invoiceItems)
    {
        try {
            $response = static::request('invoices/signed/' . $invoiceUuid . '/replace', [
                'json' => $replaceInvoice->toArray()
            ], 'PUT');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    /**
     * Cancel signed invoice by using new once
     *
     * @param string $invoiceUuid
     * @param array $invoiceInfo
     * @return array
     */
    public static function deleteSignedInvoice(string $invoiceUuid, array $invoiceInfo)
    {
        try {
            $response = static::request('invoices/signed/' . $invoiceUuid, [
                'json' => array_filter([
                    'ngayvb' => $invoiceInfo['ngayvb'],
                    'ghi_chu' => $invoiceInfo['ghi_chu'],
                ])
            ], 'DELETE');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    /**
     * Cancel signed invoice by using new once
     *
     * @param array $invoiceInfo
     * @return array
     */
    public static function sendEmail(array $invoiceInfo)
    {
        try {
            $response = static::request('invoices/send-mails', [
                'json' => array_map(function ($invoice) {
                    return [
                        'id' => $invoice['id'],
                        'nguoi_nhan' => $invoice['email']
                    ];
                }, $invoiceInfo)
            ]);
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    /**
     * Cancel signed invoice by using new once
     *
     * @param string $invoiceUuid
     * @return array
     */
    public static function getPdf(string $invoiceUuid)
    {
        try {
            $response = static::request('invoices/' . $invoiceUuid . '/pdf', [], 'GET');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    /**
     * Cancel signed invoice by using new once
     *
     * @param string $invoiceUuid
     * @return array
     */
    public static function getXml(string $invoiceUuid)
    {
        try {
            $response = static::request('invoices/' . $invoiceUuid . '/xml', [], 'GET');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function getCompanyByTax(string $tax)
    {
        try {
            $response = static::request('invoices/customer/tax', [
                'query' => [
                    'tax' => $tax
                ]
            ], 'get');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function getECollectionCode(string $ecCode)
    {
        try {
            $response = static::request('e-collections/' . $ecCode, [], 'get');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    /**
     * Create e collection code
     *
     * @param CreateECollectionDTO[] $codes
     * @return array
     */
    public static function createECollectionCode(array $codes)
    {
        try {
            $response = static::request('e-collections', [
                'json' => array_map(function (CreateECollectionDTO $code) {
                    return $code->toArray();
                }, $codes)
            ], 'post');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }


    /**
     * Map e collection code
     *
     * @param MapECollectionDTO[] $codes
     * @return array
     */
    public static function mapECollectionCode(array $codes)
    {
        try {
            $response = static::request('e-collections', [
                'json' => array_map(function (MapECollectionDTO $code) {
                    return $code->toArray();
                }, $codes)
            ], 'PUT');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function getECollectionRecvCode(string $ecCode, string $recvId)
    {
        try {
            $response = static::request('e-collections/recv/' . $ecCode . '/' . $recvId, [], 'get');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    /**
     * Create e collection receivable code
     *
     * @param CreateECollectionRecvDTO[] $codes
     * @return array
     */
    public static function createECollectionRecvCode(array $codes)
    {
        try {
            $response = static::request('e-collections/recv', [
                'json' => array_map(function (CreateECollectionRecvDTO $code) {
                    return $code->toArray();
                }, $codes)
            ], 'post');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    /**
     * Create e collection receivable code
     *
     * @param UpdateECollectionRecvDTO[] $codes
     * @return array
     */
    public static function updateECollectionRecvCode(array $codes)
    {
        try {
            $response = static::request('e-collections/recv', [
                'json' => array_map(function (UpdateECollectionRecvDTO $code) {
                    return $code->toArray();
                }, $codes)
            ], 'PUT');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function deleteECollectionRecvCode(string $recvId)
    {
        try {
            $response = static::request('e-collections/recv/' . $recvId, [], 'DELETE');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function createQr(QrCodeDTO $qrCode)
    {
        try {
            $response = static::request('qr', [
                'json' => $qrCode->toArray()
            ], 'post');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function inquiryEcTrans(InquiryEcTransDTO $query)
    {
        try {
            $response = static::request('payments/ec-trans', [
                'query' => $query->toArray()
            ], 'get');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }

    public static function inquiryEcCodes(InquiryEcCodeDTO $query)
    {
        try {
            $response = static::request('payments/ec', [
                'query' => $query->toArray()
            ], 'get');
            return $response ?? [];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return [];
        }
    }
}

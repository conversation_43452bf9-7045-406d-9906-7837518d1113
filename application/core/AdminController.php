<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * AdminController
 * @property App_Input $input
 * @property App_scripts $app_scripts
 * @property Announcements_model $announcements_model
 * @property Authentication_model $authentication_model
 * @property Call_center_model $call_center_model
 * @property Client_groups_model $client_groups_model
 * @property Client_requests_model $client_requests_model
 * @property Client_vault_entries_model $client_vault_entries_model
 * @property Clients_model $clients_model
 * @property Command_model $command_model
 * @property Contract_types_model $contract_types_model
 * @property Contracts_model $contracts_model
 * @property Credit_notes_model $credit_notes_model
 * @property Cron_model $cron_model
 * @property Currencies_model $currencies_model
 * @property Custom_fields_model $custom_fields_model
 * @property Customer_admins_model $customer_admins_model
 * @property Dashboard_model $dashboard_model
 * @property Departments_model $departments_model
 * @property Email_schedule_model $email_schedule_model
 * @property Emails_model $emails_model
 * @property Estimate_request_model $estimate_request_model
 * @property Estimates_model $estimates_model
 * @property Expenses_model $expenses_model
 * @property Gdpr_model $gdpr_model
 * @property Invoice_items_model $invoice_items_model
 * @property Invoices_model $invoices_model
 * @property Knowledge_base_model $knowledge_base_model
 * @property Leads_model $leads_model
 * @property Misc_model $misc_model
 * @property Newsfeed_model $newsfeed_model
 * @property Payment_modes_model $payment_modes_model
 * @property Payments_model $payments_model
 * @property Projects_model $projects_model
 * @property Proposals_model $proposals_model
 * @property Reports_model $reports_model
 * @property Roles_model $roles_model
 * @property Settings_model $settings_model
 * @property Spam_filters_model $spam_filters_model
 * @property Staff_model $staff_model
 * @property Staff_statistics_model $staff_statistics_model
 * @property Staff_teams_model $staff_teams_model
 * @property Statement_model $statement_model
 * @property Subscriptions_model $subscriptions_model
 * @property Tasks_model $tasks_model
 * @property Taxes_model $taxes_model
 * @property Templates_model $templates_model
 * @property Tickets_model $tickets_model
 * @property Todo_model $todo_model
 * @property Twocheckout_model $twocheckout_model
 * @property User_autologin $user_autologin
 * @property Utilities_model $utilities_model
 * @property Invoices_model $Invoices_model
 * @property App $app
 * @property App_tabs $app_tabs
 * @property CI_Config $config
 * @property CI_DB_mysqli_driver $db
 * @property Gdpr_contact $gdpr_contact
 * @property CI_Form_validation $form_validation
 * @property App_Session $session
 * @property App_bulk_pdf_export $app_bulk_pdf_export
 * @property App_import $import
 * @property CI_Encryption $encryption
 * @property CI_URI $uri
 * @property App_menu $app_menu
 * @property App_merge_fields $app_merge_fields
 */
class AdminController extends App_Controller
{
    public function __construct()
    {
        parent::__construct();

        if ($this->app->is_db_upgrade_required_v2()) {
            die(include_once(VIEWPATH . 'admin/includes/db_update_required_v2.php'));
        }

        // if ($this->app->is_db_upgrade_required($this->current_db_version)) {
        //     if ($this->input->post('upgrade_database')) {
        //         hooks()->do_action('pre_upgrade_database');

        //         $this->app->upgrade_database();
        //     }

        //     die(include_once(VIEWPATH . 'admin/includes/db_update_required.php'));
        // }

        hooks()->do_action('pre_admin_init');

        if (!is_staff_logged_in()) {
            if (strpos(current_full_url(), get_admin_uri() . '/authentication') === false) {
                redirect_after_login_to_current_url();
            }

            redirect(admin_url('authentication'));
        }

        if ($this->uri->segment(3) != 'notifications_check') {
            // In case staff have setup logged in as client - This is important don't change it
            foreach (['client_user_id', 'contact_user_id', 'client_logged_in', 'logged_in_as_client'] as $sk) {
                if ($this->session->has_userdata($sk)) {
                    $this->session->unset_userdata($sk);
                }
            }
        }

        // Update staff last activity
        $this->db->where('staffid', get_staff_user_id());
        $this->db->update('staff', ['last_activity' => date('Y-m-d H:i:s')]);

        $this->load->model('staff_model');

        // Do not check on ajax requests
        if (!$this->input->is_ajax_request()) {
            if (ENVIRONMENT == 'production' && is_admin()) {
                if ($this->config->item('encryption_key') === '') {
                    die('<h1>Encryption key not sent in application/config/app-config.php</h1>For more info visit <a href="https://help.perfexcrm.com/encryption-key-explained/">encryption key explained</a>');
                } elseif (strlen($this->config->item('encryption_key')) != 32) {
                    die('<h1>Encryption key length should be 32 charachters</h1>For more info visit <a href="https://help.perfexcrm.com/encryption-key-explained/">encryption key explained</a>');
                }
            }

            _maybe_system_setup_warnings();

            $this->init_quick_actions_links();
        }

        $currentUser = $this->staff_model->get(get_staff_user_id());

        // Deleted or inactive but have session
        if (!$currentUser || $currentUser->active == 0) {
            $this->authentication_model->logout();
            redirect(admin_url('authentication'));
        }

        $GLOBALS['current_user'] = $currentUser;

        init_admin_assets();

        hooks()->do_action('admin_init');

        $vars = [
            'current_user'    => $currentUser,
            'current_version' => $this->current_db_version,
            'task_statuses'   => $this->tasks_model->get_statuses(),
        ];

        if (!$this->input->is_ajax_request()) {
            $vars['sidebar_menu'] = $this->app_menu->get_sidebar_menu_items();
            $vars['setup_menu']   = $this->app_menu->get_setup_menu_items();
        }

        /**
         * Autoloaded view variables
         * @var array
         */
        $vars = hooks()->apply_filters('admin_area_auto_loaded_vars', $vars);
        $this->load->vars($vars);
    }

    private function init_quick_actions_links()
    {
        $this->app->add_quick_actions_link([
            'name'       => _l('invoice'),
            'permission' => 'invoices',
            'url'        => 'invoices/invoice',
            'position'   => 5,
            ]);

        $this->app->add_quick_actions_link([
            'name'       => _l('estimate'),
            'permission' => 'estimates',
            'url'        => 'estimates/estimate',
            'position'   => 10,
            ]);

        $this->app->add_quick_actions_link([
            'name'       => _l('proposal'),
            'permission' => 'proposals',
            'url'        => 'proposals/proposal',
            'position'   => 15,
            ]);

        $this->app->add_quick_actions_link([
            'name'       => _l('credit_note'),
            'permission' => 'credit_notes',
            'url'        => 'credit_notes/credit_note',
            'position'   => 20,
            ]);


        $this->app->add_quick_actions_link([
            'name'       => _l('client'),
            'permission' => 'customers',
            'url'        => 'clients/client',
            'position'   => 25,
            ]);

        $this->app->add_quick_actions_link([
            'name'       => _l('subscription'),
            'permission' => 'subscriptions',
            'url'        => 'subscriptions/create',
            'position'   => 30,
            ]);


        $this->app->add_quick_actions_link([
            'name'       => _l('project'),
            'url'        => 'projects/project',
            'permission' => 'projects',
            'position'   => 35,
            ]);


        $this->app->add_quick_actions_link([
            'name'            => _l('task'),
            'url'             => '#',
            'custom_url'      => true,
            'href_attributes' => [
                'onclick' => 'new_task();return false;',
                ],
            'permission' => 'tasks',
            'position'   => 40,
            ]);

        $this->app->add_quick_actions_link([
            'name'            => _l('lead'),
            'url'             => '#',
            'custom_url'      => true,
            'permission'      => 'is_staff_member',
            'href_attributes' => [
                'onclick' => 'init_lead(); return false;',
                ],
            'position' => 45,
            ]);

        $this->app->add_quick_actions_link([
            'name'       => _l('expense'),
            'permission' => 'expenses',
            'url'        => 'expenses/expense',
            'position'   => 50,
            ]);


        $this->app->add_quick_actions_link([
            'name'       => _l('contract'),
            'permission' => 'contracts',
            'url'        => 'contracts/contract',
            'position'   => 55,
            ]);


        $this->app->add_quick_actions_link([
            'name'       => _l('kb_article'),
            'permission' => 'knowledge_base',
            'url'        => 'knowledge_base/article',
            'position'   => 60,
            ]);

        $tickets = [
            'name'     => _l('ticket'),
            'url'      => 'tickets/add',
            'position' => 65,
            ];

        if (get_option('access_tickets_to_none_staff_members') == 0 && !is_staff_member()) {
            $tickets['permission'] = 'is_staff_member';
        }

        $this->app->add_quick_actions_link($tickets);

        $this->app->add_quick_actions_link([
            'name'       => _l('staff_member'),
            'url'        => 'staff/member',
            'permission' => 'staff',
            'position'   => 70,
            ]);

        $this->app->add_quick_actions_link([
            'name'       => _l('calendar_event'),
            'url'        => 'utilities/calendar?new_event=true&date=' . _d(date('Y-m-d')),
            'permission' => '',
            'position'   => 75,
            ]);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Invoice_merge_fields extends App_merge_fields
{
    public function build()
    {
        return [
            [
                'name'      => 'Invoice Link',
                'key'       => '{invoice_link}',
                'available' => [
                    'invoice',
                ],
                'templates' => [
                    'subscription-payment-succeeded',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],
            ],
            [
                'name'      => 'Invoice Number',
                'key'       => '{invoice_number}',
                'available' => [
                    'invoice',
                ],
                'templates' => [
                    'subscription-payment-succeeded',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],
            ],
            [
                'name'      => 'Invoice Duedate',
                'key'       => '{invoice_duedate}',
                'available' => [
                    'invoice',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],
            ],
            [
                'name'      => 'Invoice Date',
                'key'       => '{invoice_date}',
                'available' => [
                    'invoice',
                ],
                'templates' => [
                    'subscription-payment-succeeded',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],

            ],
            [
                'name'      => 'Invoice Status',
                'key'       => '{invoice_status}',
                'available' => [
                    'invoice',
                ],
                'templates' => [
                    'subscription-payment-succeeded',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],
            ],
            [
                'name'      => 'Invoice Sale Agent',
                'key'       => '{invoice_sale_agent}',
                'available' => [
                    'invoice',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],
            ],
            [
                'name'      => 'Invoice Total',
                'key'       => '{invoice_total}',
                'available' => [
                    'invoice',
                ],
                'templates' => [
                    'subscription-payment-succeeded',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],
            ],
            [
                'name'      => 'Invoice Subtotal',
                'key'       => '{invoice_subtotal}',
                'available' => [
                    'invoice',
                ],
                'templates' => [
                    'subscription-payment-succeeded',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],
            ],
            [
                'name'      => 'Invoice Amount Due',
                'key'       => '{invoice_amount_due}',
                'available' => [
                    'invoice',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],
            ],
            [
                'name'      => 'Invoice Days Overdue',
                'key'       => '{total_days_overdue}',
                'available' => [
                    'invoice',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],
            ],
            [
                'name'      => 'Payment Recorded Total',
                'key'       => '{payment_total}',
                'available' => [

                ],
                'templates' => [
                    'subscription-payment-succeeded',
                    'invoice-payment-recorded-to-staff',
                    'invoice-payment-recorded',
                ],
            ],
            [
                'name'      => 'Payment Recorded Date',
                'key'       => '{payment_date}',
                'available' => [

                ],
                'templates' => [
                    'subscription-payment-succeeded',
                    'invoice-payment-recorded-to-staff',
                    'invoice-payment-recorded',
                ],
            ],
            [
                'name'      => 'Project name',
                'key'       => '{project_name}',
                'available' => [
                    'invoice',
                ],
                'exclude' => [
                    'invoices-batch-payments',
                ],
            ],
        ];
    }

    /**
     * Merge fields for invoices
     * @param  mixed $invoice_id invoice id
     * @param  mixed $payment_id payment id
     * @return array
     */
    public function format($invoice_id, $payment_id = false, $invoice_request = null)
    {
        if ($invoice_request === null) {
            $invoice_request = new stdClass();
        }
       
        $fields = [];
        $this->ci->db->where('id', $invoice_id);
        $invoice = $this->ci->db->get(db_prefix() . 'invoices')->row();

        if (!$invoice) {
            return $fields;
        }

        $currency = get_currency($invoice->currency);

        $fields['{payment_total}'] = '';
        $fields['{payment_date}']  = '';

        if ($payment_id) {
            $this->ci->db->where('id', $payment_id);
            $payment = $this->ci->db->get(db_prefix() . 'invoicepaymentrecords')->row();

            if ($payment) {
                $fields['{payment_total}'] = app_format_money($payment->amount, $currency);
                $fields['{payment_date}']  = _d($payment->date);
            }
        }

        $fields['{invoice_amount_due}'] = app_format_money(get_invoice_total_left_to_pay($invoice_id, $invoice->total), $currency);
        $fields['{invoice_sale_agent}'] = get_staff_full_name($invoice->sale_agent);
        $fields['{invoice_total}']      = app_format_money($invoice->total, $currency);
        $fields['{invoice_subtotal}']   = app_format_money($invoice->subtotal, $currency);

        $fields['{invoice_link}']       = site_url('invoice/' . $invoice_id . '/' . $invoice->hash);
        $fields['{invoice_number}']     = format_invoice_number($invoice_id);
        $fields['{invoice_duedate}']    = _d($invoice->duedate);
        $fields['{total_days_overdue}'] = get_total_days_overdue($invoice->duedate);
        $fields['{invoice_date}']       = _d($invoice->date);
        $fields['{invoice_status}']     = format_invoice_status($invoice->status, '', false);
        $fields['{project_name}']       = get_project_name_by_id($invoice->project_id);
        $fields['{invoice_short_url}']  = get_invoice_shortlink($invoice);
        $fields['{invoice_closing_date}']  = _d($invoice->invoice_closing_date);

        $custom_fields = get_custom_fields('invoice');
        foreach ($custom_fields as $field) {
            $fields['{' . $field['slug'] . '}'] = get_custom_field_value($invoice_id, $field['id'], 'invoice');
        }
        
        if(isset($invoice_request->status)){
            $fields['{invoice_request_status}']  = INVOICE_DRAFT[$invoice_request->status]['text'];
            $fields['{invoice_request_created_at}']  = _dt($invoice_request->created_at);
            $fields['{invoice_request_export_address}']  = $invoice_request->export_address;
            $fields['{invoice_request_content}']  = $invoice_request->content;
            $fields['{invoice_request_contract_status}']  = $invoice_request->contract_status;
        }
        
        return hooks()->apply_filters('invoice_merge_fields', $fields, [
            'id'      => $invoice_id,
            'invoice' => $invoice,
        ]);
    }
}

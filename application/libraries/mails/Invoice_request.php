<?php

defined('BASEPATH') or exit('No direct script access allowed');


class Invoice_request extends App_mail_template
{
    protected $for = 'Invoice request';

    protected $invoice;

    protected $contact;

    protected $invoice_request;

    protected $email;

    protected $to_name;

    public $slug = 'invoice-request';

    public function __construct($invoice, $invoice_request, $email, $to_name)
    {
        parent::__construct();
        $this->invoice = $invoice;
        $this->invoice_request = $invoice_request;
        $this->email = $email;

        // For SMS
        $this->set_merge_fields('client_merge_fields', $this->invoice->clientid);
        $this->set_merge_fields('invoice_merge_fields', $this->invoice->id, false, $this->invoice_request);
        $this->set_merge_fields([
            '{to_name}' => $to_name
        ]);
    }

    public function build()
    {
        $this->to($this->email);
    }
}

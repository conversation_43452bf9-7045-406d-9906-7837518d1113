<?php

defined('BASEPATH') or exit('No direct script access allowed');


class Invoice_request_issue_po extends App_mail_template
{
    protected $for = 'Invoice request';

    protected $invoice;

    protected $contact;

    protected $invoice_request;

    protected $email;

    protected $to_name;

    public $slug = 'invoice-request-issue-po';

    public function __construct($invoice, $invoice_request, $email)
    {
        parent::__construct();
        $this->invoice = $invoice;
        $this->invoice_request = $invoice_request;
        $this->email = $email;

        // For SMS
        $this->set_merge_fields('client_merge_fields', $this->invoice->clientid);
        $this->set_merge_fields('invoice_merge_fields', $this->invoice->id, false, $this->invoice_request);
        $this->set_merge_fields([
            '{staff_name}' => $invoice_request->staff->fullname,
            '{required_date}' => $invoice_request->requested_issue_at->format('d-m-Y'),
            '{required_time}' => $invoice_request->requested_issue_at->format('H:s'),
        ]);
    }

    public function build()
    {
        $this->to($this->email);
    }
}

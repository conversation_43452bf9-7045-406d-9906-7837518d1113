<?php

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');


class Invoice_send_signed_invoice extends App_mail_template
{
    protected $for = 'Send email issue po';

    protected $invoice;
    protected $minvoice;


    protected $email;

    public $slug = 'send-email-issue-po';

    public function __construct($invoice, $minvoice, $email, $cc = [])
    {
        parent::__construct();
        $this->invoice = $invoice;
        $this->email = $email;
        $this->cc = join(',', $cc);

        // For SMS
        $this->set_merge_fields('client_merge_fields', $this->invoice->clientid);
        $this->set_merge_fields('invoice_merge_fields', $this->invoice);
        $this->set_merge_fields([
            '{invoice_number}' => format_invoice_number($invoice),
            '{khieu}' => $minvoice['khieu'] ?? '',
            '{shdon}' => $minvoice['shdon'] ?? '',
            '{nky}' => isset($minvoice['nky']) ? Carbon::parse($minvoice['nky'])->format('Y-m-d H:i:s') : '',
            '{sbmat}' => $minvoice['sbmat'] ?? '',
            '{company_tax}' => get_option('company_vat'),
            '{client_tax}' => $minvoice['mst'] ?? '',
            '{link}' => MINVOICE_CHECK_URL,
        ]);
    }

    public function build()
    {
        $this->to($this->email);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');


class Invoice_request_change_status extends App_mail_template
{
    protected $for = 'Invoice request change status';

    protected $invoice;

    protected $contact;

    protected $invoice_request;
    
    protected $email;

    public $slug = 'Invoice-request-change-status';

    public function __construct($invoice, $invoice_request, $email)
    {
        parent::__construct();
        $this->invoice = $invoice;
        $this->invoice_request = $invoice_request;
        $this->email = $email;
        // For SMS
        $this->set_merge_fields('client_merge_fields', $this->invoice->clientid);
        $this->set_merge_fields('invoice_merge_fields', $this->invoice->id, false, $this->invoice_request);
    }

    public function build()
    {
        $this->to($this->email);
    }
}

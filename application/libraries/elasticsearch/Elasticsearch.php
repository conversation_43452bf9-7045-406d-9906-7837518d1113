<?php

use Elasticsearch\ClientBuilder;

defined('BASEPATH') or exit('No direct script access allowed');

class Elasticsearch
{
    /**
     * Codeigniter instance
     * @var object
     */
    protected $ci;

    public const INDEX_EXCEPTION_FOLDER = FCPATH . 'temp/es_index_result';

    public function __construct()
    {
        $this->ci = &get_instance();
        set_time_limit(0);
        error_reporting(-1);
        ini_set('display_errors', 1);
    }

    /**
     * Run valid command
     * @param string $command name of command
     * @param array $args param of the command
     * @return void
     */
    public function run($command, $args)
    {
        call_user_func(array($this, $command), ...$args);
    }

    /**
     * Create indice by name, it can create multiple indices
     * @param string $indiceName name of the indice
     * @return void
     */
    public function createIndice($indiceName = 'all')
    {
        $esMappigs = $this->ci->load->config('elasticsearch')['indices']['mappings'];
        $indiceNames = explode(':', $indiceName);
        $client = es_client();
        $allowAll = in_array('all', $indiceNames);
        foreach ($esMappigs as $key => $property) {
            if ($allowAll || in_array($key, $indiceNames)) {
                echo "Creating indice " . $key.PHP_EOL;
                $client->indices()->create([
                    'index' => $key,
                    'body' => [
                        'mappings' => $property
                    ]
                ]);
            }
        }
    }

    /**
     * Update mapping of the existing indice
     * @param string $indiceName name of the indice
     * @return void
     */
    public function updateIndice($indiceName = 'all')
    {
        $esMappigs = $this->ci->load->config('elasticsearch')['indices']['mappings'];
        $indiceNames = explode(':', $indiceName);
        $client = es_client();
        $allowAll = in_array('all', $indiceNames);
        foreach ($esMappigs as $key => $property) {
            if ($allowAll || in_array($key, $indiceNames)) {
                $client->indices()->putMapping([
                    'index' => $key,
                    'body' => $property
                ]);
            }
        }
    }

    /**
     * Make .txt file that include ids of the table will be indexed
     * @param string $table table want to prepared, valid value are:
     *    Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
     *    Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
     *    Tag, Task, Ticket. Default is all
     * @param string $prefix prefix of the file will be created, default is "idx". Example idx_clients_1.txt
     * @param integer $chunkSize Record per file, default is 1000
     * @return void
     */
    public function prepareIndexAll($table = 'all', $prefix = 'idx', $chunkSize = 1000)
    {
        $tables = $this->ci->load->config('elasticsearch')['tables'] ?? [];
        $includeTables = explode(':', $table);
        // Filter allow tables if input specific table
        $tables = $table === 'all' ? $tables : array_filter($tables, function ($table) use ($includeTables) {
            return in_array($table, $includeTables);
        });
        $processes = $this->ci->load->config('elasticsearch')['process_prepare_file'] ?? 5;
        $chunkTables = array_chunk($tables, $processes);

        foreach ($chunkTables as $chunkTable) {
            echo "\033[0;33mPrepare for tables: \033[0m\033[0;32m" . join(', ', $chunkTable) ."\033[0m". PHP_EOL;
            // Run 3 commands as sparate thread to speed up process
            array_walk($chunkTable, function ($chunk) use ($prefix, $chunkSize) {
                $command = 'php ' . FCPATH . 'index.php es prepareIndexTable ' . $chunk . ' ' . $prefix . ' ' . $chunkSize . ' > /dev/null 2>&1 & echo $!';
                exec($command);
            });

            $waitingText = "\033[0;33mWaiting.\033[0m";
            echo $waitingText;
            do {
                $return = shell_exec("ps aux | grep '[p]hp /srv/perfex-crm/index.php es prepareIndexTable' | awk '{print $1}'");
                echo '.';
                sleep(1);
            } while (trim($return) != "");
            echo "\033[0;32mDone!\033[0m";
            echo PHP_EOL;
        }
        echo "\033[0;33mPrepared done, output folder: \033[0m\033[0;32m" . EsBaseModel::INDEX_TMP_FOLDER ."\033[0m". PHP_EOL;
    }

    /**
     * Perform get ids and write id to the specific .txt file based on the table
     * @param string $table table will be prepared, valid value are:
     *    Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
     *    Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
     *    Tag, Task, Ticket.
     * @param string $prefix prefix of the file, default is 'idx'. Example idx_client_1.txt
     * @param integer $chunkSize Record per file, default is 1000
     * @return void
     */
    public function prepareIndexTable($table, $prefix = 'idx', $chunkSize = 1000)
    {
        $cls = 'Es' . ucfirst($table) . 'Model';
        $lib = $this->ci->load->library('elasticsearch/esmodels/' . $cls);
        $clss = strtolower($cls);
        $lib->$clss->createIndexFiles($table, $prefix, $chunkSize);
    }

    /**
     * Get all file based on table and then perform index
     * @param string $table table will be indexed, valid value are:
     *    Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
     *    Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
     *    Tag, Task, Ticket. Default is 'all'
     * @param string $prefix prefix of the created file
     * @return void
     */
    public function indexEsAllFiles($table = 'all', $prefix = 'idx')
    {
        $tables = $this->ci->load->config('elasticsearch')['tables'] ?? [];
        $includeTables = explode(':', $table);
        // Filter allow tables if input specific table
        $tables = $table === 'all' ? $tables : array_filter($tables, function ($table) use ($includeTables) {
            return in_array($table, $includeTables);
        });
        $fileIndexes = $this->getFileIndexes($tables, $prefix);

        // Call method indexEsFile to perform indexing
        foreach ($fileIndexes as $table => $fileName) {
            echo "\033[0;33mStart index for the table \033[0m\033[0;32m" . $table ."\033[0m". PHP_EOL;

            $this->indexEsFile($table, join(':', $fileName));

            echo PHP_EOL;
        }

        echo "Completed!" . PHP_EOL;
    }

    /**
     * Prepare file will be got to indexes by table and prefix
     * @param array $tables tables will be indexed
     * @param string $prefix prefix of the file
     * @return array list after prepared
     */
    private function getFileIndexes($tables, $prefix)
    {
        if (!file_exists(EsBaseModel::INDEX_TMP_FOLDER)) {
            throw new Exception("Index Folder is not exsited. Folder: " . EsBaseModel::INDEX_TMP_FOLDER);
        }
        $files = scandir(EsBaseModel::INDEX_TMP_FOLDER);
        $lowercaseTables = array_map('strtolower', $tables);
        $tableFiles = [];

        foreach ($files as $fileName) {
            // Not push to the list if prefix is not the same to avoid old files
            if (!preg_match('/^' . $prefix . '_*/', $fileName)) {
                continue;
            }
            $fileNames = explode('_', $fileName);
            $fileTableName = $fileNames[1];
            // Find file name same as table name then push to the list
            if (($idx = array_search($fileTableName, $lowercaseTables)) !== false) {
                $tableName = $tables[$idx];
                if (!isset($tableFiles[$tableName])) {
                    $tableFiles[$tableName] = [];
                }
                $tableFiles[$tableName][] = $fileName;
            }
        };

        return $tableFiles;
    }

    /**
     * Validate file is valid or not
     * @param array $fileNames list of file name
     * @return void
     * @throws Exception exception if file is not existed
     */
    private function validateFileExsited($fileNames = [])
    {
        foreach ($fileNames as $fileName) {
            if (!file_exists(EsBaseModel::INDEX_TMP_FOLDER . '/' . $fileName)) {
                throw new Exception("File " . $fileName . " is not existed in the directory " . EsBaseModel::INDEX_TMP_FOLDER);
            }
        }
    }

    /**
     * Get prepared file and then perform index data for the table. 5 processes are ran at the same time
     * @param string $table table will be indexed, valid value are:
     *    Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
     *    Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
     *    Tag, Task, Ticket.
     * @param string $fileName name of the file will be read for indexing, sperator by ":"
     * @return void
     */
    public function indexEsFile($table, $fileName)
    {
        $files = explode(':', $fileName) ?? [];
        $totalFiles = count($files);
        echo "\033[0;33mFile total: \033[0m\033[0;32m" . $totalFiles ."\033[0m". PHP_EOL;
        $processes = $this->ci->load->config('elasticsearch')['process_index_data'] ?? 5;
        echo "\033[0;33mIndex \033[0m\033[0;32m" . $processes . "\033[0m \033[0;33mat the same time\033[0m" . PHP_EOL;

        $chunkFiles = array_chunk($files, $processes);

        _maybe_create_upload_path(self::INDEX_EXCEPTION_FOLDER);
        $this->ci->load->helper('string');
        foreach ($chunkFiles as $chunkFile) {
            // Validate file before execute command
            $this->validateFileExsited($chunkFile);
            echo "\033[0;33mIndex files: \033[0m\033[0;32m" . join(', ', $chunkFile) ."\033[0m". PHP_EOL;
            $uuid = random_string('md5');
            // // Run 5 commands as separate thread to speed up process
            foreach ($chunkFile as $fileName) {
                $idStr = explode("\n", trim(file_get_contents(EsBaseModel::INDEX_TMP_FOLDER . '/' . $fileName)));
                $startTime = date('YmdH');
                $errorLog = ELASTICSEARCH_LOG_ENABLE ? self::INDEX_EXCEPTION_FOLDER . '/command_indexEs_' . $startTime . '_' . strtolower($table) . '_trace.log' : '/dev/null';
                $command = 'php ' . FCPATH . 'index.php es indexEs ' . $uuid . ' ' . $table . ' ' . join(':', $idStr) . ' >>' . $errorLog . ' &';
                exec($command);
            };

            $waitingText = "\033[0;33mWaiting.\033[0m";
            echo $waitingText;
            do {
                $return = shell_exec("ps aux | grep '[p]hp " . FCPATH . "index.php es indexEs ".$uuid."' | awk '{print $1}'");
                echo "\033[0;33m.\033[0m";
                sleep(1);
            } while (trim($return) != "");
            echo "\033[0;32mDone!\033[0m";
            echo PHP_EOL;
        }
        echo "\033[0;33mIndex for table \033[0m\033[0;32m" . $table . "\033[0m \033[0;33mis completed!\033[0m". PHP_EOL;
    }

    /**
     * Perform index es by ids
     * @param string $table table will be indexed, valid value are:
     *    Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
     *    Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
     *    Tag, Task, Ticket.
     * @param string $idStr id will be indexed, sperate by ":"
     * @return void
     */
    public function indexEs($uuid, $table, $idStr)
    {
        es_logging('Start at: '.date('Y-m-d H:i:s').PHP_EOL);
        es_logging('UUID: '.$uuid.PHP_EOL);
        es_logging('Table: '.$table.PHP_EOL);
        es_logging('Ids: '.$idStr.PHP_EOL);
        $ids = explode(':', $idStr) ?? [];
        $cls = 'Es' . ucfirst($table) . 'Model';
        $lib = $this->ci->load->library('elasticsearch/esmodels/' . $cls);
        $clss = strtolower($cls);
        $lib->$clss->indexEs($ids);
    }
}

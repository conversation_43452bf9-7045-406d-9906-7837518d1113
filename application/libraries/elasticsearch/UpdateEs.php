<?php

defined('BASEPATH') or exit('No direct script access allowed');

class UpdateEs
{
    /**
     * Codeigniter instance
     * @var object
     */
    protected $ci;

    public const INDEX_EXCEPTION_FOLDER = FCPATH . 'temp/es_index_result';
    public const CREATE_OP = 'c';
    public const UPDATE_OP = 'u';
    public const DELETE_OP = 'd';
    public const SUPPORT_OP = [UpdateEs::CREATE_OP, UpdateEs::UPDATE_OP, UpdateEs::DELETE_OP];
    
    protected $esConfigs;

    public function __construct()
    {
        $this->ci = &get_instance();
        set_time_limit(0);
        error_reporting(-1);
        ini_set('display_errors', 1);
        $this->esConfigs = $this->ci->load->config('elasticsearch');
    }

    /**
     * Execute index when changing data
     * @param array $body message body that parsed from RabbitMQ
     * @throws Exception if have any
     */
    public function execute($body)
    {
        $payload = $body['payload'] ?? [];
        // Validate before perform index data
        $this->validate($payload);

        // Update tables
        $table = str_replace(db_prefix(), '', $payload['source']['table']);
        $tableClass = $this->esConfigs['update_tables'][$table];
        $cls = 'Es' .$tableClass . 'Model';
        $lib = $this->ci->load->library('elasticsearch/esmodels/' . $cls);
        $clss = strtolower($cls);

        // Check table is needed to update index or not
        if (!$lib->$clss->needUpdate($payload)) {
            es_logging("\033[0;33mNo need update for table ".$table."\033[0m".PHP_EOL);
            return;
        }


        $id = $lib->$clss->getPrimaryKeyValue($payload);
        $changePayloads = $lib->$clss->getChangePayload($payload);

        es_logging("\033[0;32mUpdate index\033[0m \033[0;33m".$table.", method: " . $payload['op'] . ", id: ".$id." \033[0m".PHP_EOL);
        // Update relations indexes
        es_logging("\033[0;33mUpdate relation indexes\033[0m".PHP_EOL);
        $this->updateRelationsIndex($changePayloads);

        // When done, perform update index if have
        if (isset(ES_INDEXES[$table])) {
            es_logging("\033[0;33mUpdate it's own\033[0m".PHP_EOL);
            $this->updateIndex($table, $tableClass, $id, self::DELETE_OP === $payload['op']);
            es_logging("\033[0;32mDone!\033[0m".PHP_EOL);
        }
    }

    /**
     * Split payload as smaller to run multiple process
     * @param array $payloads list of payload will be split
     * @return array payload after split
     */
    private function chunkSplitPayload($payloads)
    {
        $newPayloads = [];
        foreach ($payloads as $payload) {
            // Does not chunk split for empty ids
            if (empty($payload['ids'])) {
                continue;
            }
            // Split items per 100 ids to speed up index process
            $chunkIds = array_chunk($payload['ids'], 100);
            $table = $payload['table'];
            $newPayload = array_map(function ($ids) use ($table) {
                return [
                    'table' => $table,
                    'ids' => $ids
                ];
            }, $chunkIds);
            $newPayloads = array_merge($newPayloads, $newPayload);
        }
        return $newPayloads;
    }

    /**
     * Perform insert/update index of the requested table
     * @param array $payload structure of change payload, example
     * [{"ids": ["66", "65"], "table": "Contact"}, {"ids": [], "table": "Ticket"}, {"ids": [], "table": "Invoice"}, {"ids": [], "table": "CreditNote"}, {"ids": [], "table": "Estimate"}, {"ids": [], "table": "Expense"}, {"ids": [], "table": "Project"}]
     */
    protected function updateRelationsIndex($payloads)
    {
        $newPayloads = $this->chunkSplitPayload($payloads);
        unset($payloads);

        // Perform index
        $processes = $this->ci->load->config('elasticsearch')['process_index_data'] ?? 5;
        $chunkList = array_chunk($newPayloads, $processes);
        unset($newPayloads);

        _maybe_create_upload_path(self::INDEX_EXCEPTION_FOLDER);
        $this->ci->load->helper('string');
        foreach ($chunkList as $list) {
            // Run 5 commands as separate thread to speed up process
            $uuid = 'update'.random_string('md5');
            es_logging("\033[0;33UUID: \033[0m" . "\033[0;33m".$uuid."\033[0m" . PHP_EOL);
            foreach ($list as $item) {
                $startTime = date('YmdH');
                $table = $item['table'];
                $errorLog = self::INDEX_EXCEPTION_FOLDER . '/command_updateEs_' . $startTime . '_' . strtolower($table) . '_trace.log';
                $command = 'php ' . FCPATH . 'index.php es indexEs ' . $uuid . ' ' . $table . ' ' . join(':', $item['ids']) . ' >>' . $errorLog . ' &';
                exec($command);
            }

            $waitingText = "\033[0;33mWaiting.\033[0m";
            es_logging($waitingText);
            do {
                $return = shell_exec("ps aux | grep '[p]hp " . FCPATH . "index.php es indexEs ".$uuid."' | awk '{print $1}'");
                es_logging("\033[0;33m.\033[0m");
                sleep(1);
            } while (trim($return) != "");
            es_logging("\033[0;32mDone!\033[0m");
            es_logging(PHP_EOL);
        }
    }

    /**
     * Perform insert/update index of the requested table
     * @param string $index index will be updated
     * @param string $table table will be updated
     * @param integer $id
     */
    protected function updateIndex($index, $table, $id, $isDeleted = false)
    {
        if ($isDeleted) {
            es_delete_index(ES_INDEXES[$index], $id);
        } else {
            $ids = [$id];
            $cls = 'Es' . ucfirst($table) . 'Model';
            $lib = $this->ci->load->library('elasticsearch/esmodels/' . $cls);
            $clss = strtolower($cls);
            $lib->$clss->indexEs($ids);
        }
    }

    /**
     * Validate scheme, tables
     * @param array $body message body that parsed from RabbitMQ
     * @throws InvalidArgumentException throw exception if invalid 
     */
    protected function validate($payload)
    {
        // Validate required values
        if (empty($payload['op']) || empty($payload['source']['table'])) {
            throw new InvalidArgumentException(sprintf(
                'Missing required parameters op: %s, table: %s',
                $payload['op'] ?? null,
                $payload['source']['table'] ?? null,
            ));
        }

        // Check operation is support or not
        if (!in_array($payload['op'], self::SUPPORT_OP)) {
            throw new InvalidArgumentException(sprintf(
                'Operation is not supported this time, given was %s',
                $payload['op'] ?? null
            ));
        }

        // Check table supports
        if (!in_array(str_replace(db_prefix(), '', $payload['source']['table']), array_keys($this->esConfigs['update_tables']))) {
            throw new InvalidArgumentException(sprintf(
                'Table is not supported this time, given was %s',
                $payload['source']['table'] ?? null
            ));
        }
    }
}

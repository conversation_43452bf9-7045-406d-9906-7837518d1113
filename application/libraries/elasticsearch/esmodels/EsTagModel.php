<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsTagModel extends EsBaseModel
{
    protected $table = 'tags';
    protected $primaryKey = 'id';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 200);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // tags
                    $selectTable . '.id as id',
                    $selectTable . '.name as name',
                    // taggables
                    db_prefix() . 'taggables.tag_id as taggables_tag_id',
                    db_prefix() . 'taggables.rel_id as taggables_rel_id',
                    db_prefix() . 'taggables.rel_type as taggables_rel_type',
                    db_prefix() . 'taggables.tag_order as taggables_tag_order',
                ]))
                ->from($selectTable)
                ->join(db_prefix() . 'taggables', db_prefix() . 'taggables.tag_id = ' . $selectTable . '.id', 'left')
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                $groupResults = array_group_by($results, $this->primaryKey);
                // Clean up to free memory
                unset($results);

                foreach ($groupResults as $groupRs) {
                    $firstRecord = $groupRs[0];
                    $recordMap = [
                        'id' => intval($firstRecord['id']),
                        'name' => $firstRecord['name'],
                        'taggables' => array_filter(array_map(function ($taggable) {
                            return array_filter([
                                'tag_id' => intval($taggable['taggables_tag_id']),
                                'rel_id' => intval($taggable['taggables_rel_id']),
                                'rel_type' => $taggable['taggables_rel_type'],
                                'tag_order' => intval($taggable['taggables_tag_order'])
                            ], fn ($val) => isset($val));
                        }, $groupRs), fn ($val) => isset($val))
                    ];
                    $indexRecords[] = $recordMap;
                    // Clean up to free memory
                    unset($recordMap);
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }
}

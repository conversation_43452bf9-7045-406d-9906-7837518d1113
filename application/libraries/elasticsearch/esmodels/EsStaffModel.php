<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsStaffModel extends EsBaseModel
{
    protected $table = 'staff';
    protected $primaryKey = 'staffid';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 500);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // staff
                    $selectTable . '.staffid as staffid',
                    $selectTable . '.firstname as firstname',
                    $selectTable . '.lastname as lastname',
                    $selectTable . '.facebook as facebook',
                    $selectTable . '.linkedin as linkedin',
                    $selectTable . '.phonenumber as phonenumber',
                    $selectTable . '.email as email',
                    $selectTable . '.skype as skype',
                ]))
                ->from($selectTable)
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'staffid' => intval($firstRecord['staffid']),
                        'firstname' => $firstRecord['firstname'],
                        'lastname' => $firstRecord['lastname'],
                        'facebook' => $firstRecord['facebook'],
                        'linkedin' => $firstRecord['linkedin'],
                        'phonenumber' => $firstRecord['phonenumber'],
                        'email' => $firstRecord['email'],
                        'skype' => $firstRecord['skype'],
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }
}

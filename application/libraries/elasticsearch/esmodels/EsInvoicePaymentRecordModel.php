<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsInvoicePaymentRecordModel extends EsBaseModel
{
    protected $table = 'invoicepaymentrecords';
    protected $primaryKey = 'id';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 200);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // creditnotes
                    $selectTable . '.id as id',
                    $selectTable . '.paymentmode as paymentmode',
                    $selectTable . '.note as note',
                    $selectTable . '.date as date',
                    // invoice
                    db_prefix() . 'invoices.id as invoice_id',
                    db_prefix() . 'invoices.number as invoice_number',
                    // For format_invoice_number
                    db_prefix() . 'invoices.status as invoice_status',
                    db_prefix() . 'invoices.prefix as invoice_prefix',
                    db_prefix() . 'invoices.number_format as invoice_number_format',
                    db_prefix() . 'invoices.date as invoice_date',
                    // payment_mode
                    db_prefix() . 'payment_modes.id as payment_mode_id',
                    db_prefix() . 'payment_modes.name as payment_mode_name',
                ]))
                ->from($selectTable)
                ->join(db_prefix() . 'invoices', db_prefix() . 'invoices.id = ' . $selectTable . '.invoiceid', 'left')
                ->join(db_prefix() . 'payment_modes', db_prefix() . 'payment_modes.id = ' . $selectTable . '.paymentmode', 'left')
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'id' => intval($firstRecord['id']),
                        'paymentmode' => $firstRecord['paymentmode'],
                        'note' => $firstRecord['note'],
                        'date' => $firstRecord['date'],
                        'invoice' => array_filter([
                            'id' => intval($firstRecord['invoice_id']),
                            'number' => intval($firstRecord['invoice_number']),
                            'invoice_number' => $this->formatInvoiceNumber($firstRecord),
                        ], fn ($val) => isset($val)),
                        'payment_mode' => array_filter([
                            'id' => intval($firstRecord['payment_mode_id']),
                            'name' => $firstRecord['payment_mode_name'],
                        ], fn ($val) => isset($val)),
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }

    /**
     * Format invoice number based on description
     * @param  mixed $id
     * @return string
     */
    protected function formatInvoiceNumber($invoice)
    {
        if (!class_exists('Invoices_model', false)) {
            $this->ci->load->model('invoices_model');
        }

        if ($invoice['invoice_status'] == Invoices_model::STATUS_DRAFT) {
            $number = $invoice['invoice_prefix'] . 'DRAFT';
        } else {
            $number = sales_number_format($invoice['invoice_number'], $invoice['invoice_number_format'], $invoice['invoice_prefix'], $invoice['invoice_date']);
        }

        return hooks()->apply_filters('format_invoice_number', $number, [
            'id'      => $invoice['invoice_id'],
            'invoice' => $invoice,
        ]);
    }
}

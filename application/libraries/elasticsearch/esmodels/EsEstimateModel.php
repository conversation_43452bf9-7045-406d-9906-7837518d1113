<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsEstimateModel extends EsBaseModel
{
    protected $table = 'estimates';
    protected $primaryKey = 'id';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 200);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // creditnotes
                    $selectTable . '.id as id',
                    $selectTable . '.number as number',
                    $selectTable . '.clientnote as clientnote',
                    $selectTable . '.adminnote as adminnote',
                    $selectTable . '.billing_street as billing_street',
                    $selectTable . '.billing_city as billing_city',
                    $selectTable . '.billing_state as billing_state',
                    $selectTable . '.billing_zip as billing_zip',
                    $selectTable . '.shipping_street as shipping_street',
                    $selectTable . '.shipping_city as shipping_city',
                    $selectTable . '.shipping_state as shipping_state',
                    $selectTable . '.shipping_zip as shipping_zip',
                    $selectTable . '.date as date',
                    // Field for forma estimate number
                    $selectTable . '.number_format as number_format',
                    $selectTable . '.prefix as prefix',
                    // currencies
                    db_prefix() . 'currencies.id as currency_id',
                    db_prefix() . 'currencies.symbol as currency_symbol',
                    db_prefix() . 'currencies.name as currency_name',
                    db_prefix() . 'currencies.isdefault as currency_isdefault',
                    // clients
                    db_prefix() . 'clients.userid as client_userid',
                    db_prefix() . 'clients.company as client_company',
                    db_prefix() . 'clients.vat as client_vat',
                    db_prefix() . 'clients.phonenumber as client_phonenumber',
                    db_prefix() . 'clients.city as client_city',
                    db_prefix() . 'clients.state as client_state',
                    db_prefix() . 'clients.zip as client_zip',
                    db_prefix() . 'clients.address as client_address',
                    db_prefix() . 'clients.billing_street as client_billing_street',
                    db_prefix() . 'clients.billing_city as client_billing_city',
                    db_prefix() . 'clients.billing_state as client_billing_state',
                    db_prefix() . 'clients.billing_zip as client_billing_zip',
                    db_prefix() . 'clients.shipping_street as client_shipping_street',
                    db_prefix() . 'clients.shipping_city as client_shipping_city',
                    db_prefix() . 'clients.shipping_state as client_shipping_state',
                    db_prefix() . 'clients.shipping_zip as client_shipping_zip',
                    // client-contacts
                    db_prefix() . 'contacts.id as client_contacts_id',
                    db_prefix() . 'contacts.fullname as client_contacts_fullname',
                    db_prefix() . 'contacts.is_primary as client_contacts_is_primary',
                ]))
                ->from($selectTable)
                ->join(db_prefix() . 'currencies', db_prefix() . 'currencies.id = ' . $selectTable . '.currency', 'left')
                ->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . $selectTable . '.clientid', 'left')
                ->join(db_prefix() . 'contacts', db_prefix() . 'contacts.userid = ' . db_prefix() . 'clients.userid', 'left')
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                $groupResults = array_group_by($results, $this->primaryKey);
                // Clean up to free memory
                unset($results);

                foreach ($groupResults as $groupRs) {
                    $firstRecord = $groupRs[0];
                    $recordMap = [
                        'id' => intval($firstRecord['id']),
                        'estimate_number' => $this->formatEstimateNumber($firstRecord),
                        'number' => intval($firstRecord['number']),
                        'clientnote' => $firstRecord['clientnote'],
                        'adminnote' => $firstRecord['adminnote'],
                        'billing_street' => $firstRecord['billing_street'],
                        'billing_city' => $firstRecord['billing_city'],
                        'billing_state' => $firstRecord['billing_state'],
                        'billing_zip' => $firstRecord['billing_zip'],
                        'shipping_street' => $firstRecord['shipping_street'],
                        'shipping_city' => $firstRecord['shipping_city'],
                        'shipping_state' => $firstRecord['shipping_state'],
                        'shipping_zip' => $firstRecord['shipping_zip'],
                        'date' => $firstRecord['date'],
                        'currency' => array_filter([
                            'id' => intval($firstRecord['currency_id']),
                            'symbol' => $firstRecord['currency_symbol'],
                            'name' => $firstRecord['currency_name'],
                            'isdefault' => intval($firstRecord['currency_isdefault'])
                        ], fn ($val) => isset($val)),
                        'client' => array_filter([
                            'userid' => intval($firstRecord['client_userid']),
                            'company' => $firstRecord['client_company'],
                            'vat' => $firstRecord['client_vat'],
                            'phonenumber' => $firstRecord['client_phonenumber'],
                            'city' => $firstRecord['client_city'],
                            'state' => $firstRecord['client_state'],
                            'zip' => $firstRecord['client_zip'],
                            'address' => $firstRecord['client_address'],
                            'billing_street' => $firstRecord['client_billing_street'],
                            'billing_city' => $firstRecord['client_billing_city'],
                            'billing_state' => $firstRecord['client_billing_state'],
                            'billing_zip' => $firstRecord['client_billing_zip'],
                            'shipping_street' => $firstRecord['client_shipping_street'],
                            'shipping_city' => $firstRecord['client_shipping_city'],
                            'shipping_state' => $firstRecord['client_shipping_state'],
                            'shipping_zip' => $firstRecord['client_shipping_zip'],
                            'contacts' => array_filter(array_map(function ($contact) {
                                return array_filter([
                                    'id' => intval($contact['client_contacts_id']),
                                    'fullname' => $contact['client_contacts_fullname'],
                                    'is_primary' => intval($contact['client_contacts_is_primary'])
                                ], fn ($val) => isset($val));
                            }, $groupRs))
                        ], fn ($val) => isset($val)),
                    ];
                    $indexRecords[] = $recordMap;
                    // Clean up to free memory
                    unset($recordMap);
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }

    /**
     * Format estimate number based on description
     * @param  mixed $id
     * @return string
     */
    protected function formatEstimateNumber($estimate)
    {
        $number = sales_number_format($estimate['number'], $estimate['number_format'], $estimate['prefix'], $estimate['date']);

        return hooks()->apply_filters('format_estimate_number', $number, [
            'id'       => $estimate['id'],
            'estimate' => $estimate,
        ]);
    }
}

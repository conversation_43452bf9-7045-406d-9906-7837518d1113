<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsItemableModel extends EsBaseModel
{
    protected $table = 'itemable';
    protected $primaryKey = 'id';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 500);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // creditnotes
                    $selectTable . '.id as id',
                    $selectTable . '.rel_type as rel_type',
                    $selectTable . '.description as description',
                    $selectTable . '.long_description as long_description',
                ]))
                ->from($selectTable)
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'id' => intval($firstRecord['id']),
                        'rel_type' => $firstRecord['rel_type'],
                        'description' => $firstRecord['description'],
                        'long_description' => $firstRecord['long_description'],
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }
}

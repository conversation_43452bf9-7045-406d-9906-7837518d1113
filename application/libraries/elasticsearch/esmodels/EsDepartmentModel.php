<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsDepartmentModel extends EsBaseModel
{
    protected $table = 'departments';
    protected $primaryKey = 'departmentid';
    protected $relations = [
        [
            'index' => 'tickets',
            'table' => 'Ticket',
            'path' => 'department',
            'field' => 'department.departmentid'
        ]
    ];
    public function indexEs($ids)
    {
        // Leave it as empty if not index to avoid exception
        // See more in the UpdateEs::execute:55
    }

    /**
     * Fetch relations records from database to index it
     * @param mixed $id
     */
    public function getUpdatePayload($id)
    {
        $results = [];
        $table = db_prefix() . $this->table;

        // Related Ticket records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'tickets.ticketid as id')
            ->from(db_prefix() . 'tickets')
            ->join($table, db_prefix() . 'tickets.department = ' . $table . '.' . $this->primaryKey)
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Ticket',
                'ids' => array_pluck($records, 'id')
            ];
        }
        return $results;
    }
}

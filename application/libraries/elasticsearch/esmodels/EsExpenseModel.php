<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsExpenseModel extends EsBaseModel
{
    protected $table = 'expenses';
    protected $primaryKey = 'id';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 200);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // creditnotes
                    $selectTable . '.id as id',
                    $selectTable . '.paymentmode as paymentmode',
                    $selectTable . '.note as note',
                    $selectTable . '.expense_name as expense_name',
                    $selectTable . '.date as date',
                    // tblpayment_modes
                    db_prefix() . 'payment_modes.id as payment_mode_id',
                    db_prefix() . 'payment_modes.name as payment_mode_name',
                    // expenses_category
                    db_prefix() . 'expenses_categories.id as expenses_category_id',
                    db_prefix() . 'expenses_categories.name as expenses_category_name',
                    // clients
                    db_prefix() . 'clients.userid as client_userid',
                    db_prefix() . 'clients.company as client_company',
                    db_prefix() . 'clients.vat as client_vat',
                    db_prefix() . 'clients.phonenumber as client_phonenumber',
                    db_prefix() . 'clients.city as client_city',
                    db_prefix() . 'clients.zip as client_zip',
                    db_prefix() . 'clients.address as client_address',
                    db_prefix() . 'clients.state as client_state',
                ]))
                ->from($selectTable)
                ->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . $selectTable . '.clientid', 'left')
                ->join(db_prefix() . 'payment_modes', db_prefix() . 'payment_modes.id = ' . $selectTable . '.paymentmode', 'left')
                ->join(db_prefix() . 'expenses_categories', db_prefix() . 'expenses_categories.id = ' . $selectTable . '.category', 'left')
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'id' => intval($firstRecord['id']),
                        'paymentmode' => $firstRecord['paymentmode'],
                        'note' => $firstRecord['note'],
                        'expense_name' => $firstRecord['expense_name'],
                        'date' => $firstRecord['date'],
                        'payment_mode' => array_filter([
                            'id' => intval($firstRecord['payment_mode_id']),
                            'name' => $firstRecord['payment_mode_name'],
                        ]),
                        'expenses_category' => array_filter([
                            'id' => intval($firstRecord['expenses_category_id']),
                            'name' => $firstRecord['expenses_category_name'],
                        ]),
                        'client' => array_filter([
                            'userid' => intval($firstRecord['client_userid']),
                            'company' => $firstRecord['client_company'],
                            'vat' => $firstRecord['client_vat'],
                            'phonenumber' => $firstRecord['client_phonenumber'],
                            'city' => $firstRecord['client_city'],
                            'zip' => $firstRecord['client_zip'],
                            'address' => $firstRecord['client_address'],
                            'state' => $firstRecord['client_state'],
                        ], fn ($val) => isset($val)),
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }
}

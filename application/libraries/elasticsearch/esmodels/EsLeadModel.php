<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsLeadModel extends EsBaseModel
{
    protected $table = 'leads';
    protected $primaryKey = 'id';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 500);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    $selectTable . '.id as id',
                    $selectTable . '.name as name',
                    $selectTable . '.title as title',
                    $selectTable . '.company as company',
                    $selectTable . '.zip as zip',
                    $selectTable . '.city as city',
                    $selectTable . '.state as state',
                    $selectTable . '.address as address',
                    $selectTable . '.email as email',
                    $selectTable . '.phonenumber as phonenumber'
                ]))
                ->from($selectTable)
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'id' => intval($firstRecord['id']),
                        'name' => $firstRecord['name'],
                        'title' => $firstRecord['title'],
                        'company' => $firstRecord['company'],
                        'zip' => $firstRecord['zip'],
                        'city' => $firstRecord['city'],
                        'state' => $firstRecord['state'],
                        'address' => $firstRecord['address'],
                        'email' => $firstRecord['email'],
                        'phonenumber' => $firstRecord['phonenumber'],
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }
}

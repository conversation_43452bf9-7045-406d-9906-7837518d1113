<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsContactModel extends EsBaseModel
{
    protected $table = 'contacts';
    protected $primaryKey = 'id';
    protected $relations = [
        [
            'index' => 'clients',
            'table' => 'Client',
            'path' => 'contacts',
            'field' => 'contacts.id'
        ],
        [
            'index' => 'invoices',
            'table' => 'Invoice',
            'path' => 'client.contacts',
            'field' => 'client.contacts.id'
        ],
        [
            'index' => 'creditnotes',
            'table' => 'CreditNote',
            'path' => 'client.contacts',
            'field' => 'client.contacts.id'
        ],
        [
            'index' => 'estimates',
            'table' => 'Estimate',
            'path' => 'client.contacts',
            'field' => 'client.contacts.id'
        ],
        [
            'index' => 'tickets',
            'table' => 'Ticket',
            'path' => 'contact',
            'field' => 'contact.id'
        ],
    ];

    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 200);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // creditnotes
                    $selectTable . '.id as id',
                    $selectTable . '.fullname as fullname',
                    $selectTable . '.email as email',
                    $selectTable . '.phonenumber as phonenumber',
                    $selectTable . '.title as title',
                    // client
                    db_prefix() . 'clients.userid as client_userid',
                    db_prefix() . 'clients.company as client_company',
                ]))
                ->from($selectTable)
                ->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . $selectTable . '.userid', 'left')
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'id' => intval($firstRecord['id']),
                        'fullname' => $firstRecord['fullname'],
                        'email' => $firstRecord['email'],
                        'phonenumber' => $firstRecord['phonenumber'],
                        'title' => $firstRecord['title'],
                        'client' => array_filter([
                            'userid' => intval($firstRecord['client_userid']),
                            'company' => $firstRecord['client_company'],
                        ], fn ($val) => isset($val)),
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }

    /**
     * Fetch relations records from database to index it
     * @param mixed $id
     */
    public function getUpdatePayload($id)
    {
        $results = [];
        $table = db_prefix() . $this->table;

        // Related Client records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'clients.userid as id')
            ->from(db_prefix() . 'clients')
            ->join($table, db_prefix() . 'clients.userid = ' . $table . '.userid')
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Client',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related Invoice records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'invoices.id as id')
            ->from(db_prefix() . 'invoices')
            ->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . db_prefix() . 'invoices.clientid')
            ->join($table, db_prefix() . 'clients.userid = ' . $table . '.userid')
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Invoice',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related CreditNote records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'creditnotes.id as id')
            ->from(db_prefix() . 'creditnotes')
            ->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . db_prefix() . 'creditnotes.clientid')
            ->join($table, db_prefix() . 'clients.userid = ' . $table . '.userid')
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'CreditNote',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related Estimate records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'estimates.id as id')
            ->from(db_prefix() . 'estimates')
            ->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . db_prefix() . 'estimates.clientid')
            ->join($table, db_prefix() . 'clients.userid = ' . $table . '.userid')
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Estimate',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related Ticket records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'tickets.ticketid as id')
            ->from(db_prefix() . 'tickets')
            ->join($table, db_prefix() . 'tickets.contactid = ' . $table . '.' . $this->primaryKey)
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Ticket',
                'ids' => array_pluck($records, 'id')
            ];
        }

        return $results;
    }
}

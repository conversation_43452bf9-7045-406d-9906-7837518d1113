<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsSurveyModel extends EsBaseModel
{
    protected $table = 'surveys';
    protected $primaryKey = 'surveyid';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 500);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // creditnotes
                    $selectTable . '.surveyid as surveyid',
                    $selectTable . '.subject as subject',
                    $selectTable . '.slug as slug',
                    $selectTable . '.description as description',
                    $selectTable . '.viewdescription as viewdescription',
                ]))
                ->from($selectTable)
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'surveyid' => intval($firstRecord['surveyid']),
                        'subject' => $firstRecord['subject'],
                        'slug' => $firstRecord['slug'],
                        'description' => $firstRecord['description'],
                        'viewdescription' => $firstRecord['viewdescription'],
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES['survey'], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }
}

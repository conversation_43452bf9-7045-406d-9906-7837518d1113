<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsExpensesCategoryModel extends EsBaseModel
{
    protected $table = 'expenses_categories';
    protected $primaryKey = 'id';
    protected $relations = [
        [
            'index' => 'expenses',
            'table' => 'Expense',
            'path' => 'expenses_category',
            'field' => 'expenses_category.id'
        ]
    ];

    public function indexEs($ids)
    {
        // Leave it as empty if not index to avoid exception
        // See more in the UpdateEs::execute:55
    }
    
    /**
     * Fetch relations records from database to index it
     * @param mixed $id
     */
    public function getUpdatePayload($id)
    {
        $results = [];
        $table = db_prefix() . $this->table;

        // Related Expense records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'expenses.id as id')
            ->from(db_prefix() . 'expenses')
            ->join($table, db_prefix() . 'expenses.category = ' . $table . '.' . $this->primaryKey)
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Expense',
                'ids' => array_pluck($records, 'id')
            ];
        }

        return $results;
    }
}

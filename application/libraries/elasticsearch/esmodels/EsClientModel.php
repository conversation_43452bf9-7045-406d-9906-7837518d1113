<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsClientModel extends EsBaseModel
{
    protected $table = 'clients';
    protected $primaryKey = 'userid';
    protected $relations = [
        [
            'index' => 'contacts',
            'table' => 'Contact',
            'path' => 'client',
            'field' => 'client.userid'
        ],
        [
            'index' => 'tickets',
            'table' => 'Ticket',
            'path' => 'client',
            'field' => 'client.userid'
        ],
        [
            'index' => 'invoices',
            'table' => 'Invoice',
            'path' => 'client',
            'field' => 'client.userid'
        ],
        [
            'index' => 'creditnotes',
            'table' => 'CreditNote',
            'path' => 'client',
            'field' => 'client.userid'
        ],
        [
            'index' => 'estimates',
            'table' => 'Estimate',
            'path' => 'client',
            'field' => 'client.userid'
        ],
        [
            'index' => 'expenses',
            'table' => 'Expense',
            'path' => 'client',
            'field' => 'client.userid'
        ],
        [
            'index' => 'projects',
            'table' => 'Project',
            'path' => 'client',
            'field' => 'client.userid'
        ]
    ];

    public function indexEs($ids, $returnOnly = false)
    {
        $chunkIds = array_chunk($ids, 200);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // invoices
                    $selectTable . '.userid as userid',
                    $selectTable . '.company as company',
                    $selectTable . '.business_name as business_name',
                    $selectTable . '.vat as vat',
                    $selectTable . '.phonenumber as phonenumber',
                    $selectTable . '.address as address',
                    // contacts
                    db_prefix() . 'contacts.id as contacts_id',
                    db_prefix() . 'contacts.fullname as contacts_fullname',
                    db_prefix() . 'contacts.phonenumber as contacts_phonenumber',
                    db_prefix() . 'contacts.email as contacts_email',
                    db_prefix() . 'contacts.is_primary as contacts_is_primary',
                ]))
                ->from($selectTable)
                ->join(db_prefix() . 'contacts', db_prefix() . 'contacts.userid = ' . db_prefix() . 'clients.userid', 'left')
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                $groupResults = array_group_by($results, $this->primaryKey);
                // Clean up to free memory
                unset($results);

                foreach ($groupResults as $groupRs) {
                    $firstRecord = $groupRs[0];
                    $recordMap = [
                        'userid' => intval($firstRecord['userid']),
                        'company' => $firstRecord['company'],
                        'business_name' => $firstRecord['business_name'],
                        'vat' => $firstRecord['vat'],
                        'phonenumber' => $firstRecord['phonenumber'],
                        'address' => $firstRecord['address'],
                        'contacts' => array_filter(array_map(function ($contact) {
                            return array_filter([
                                'id' => intval($contact['contacts_id']),
                                'fullname' => $contact['contacts_fullname'],
                                'phonenumber' => $contact['contacts_phonenumber'],
                                'email' => $contact['contacts_email'],
                                'is_primary' => intval($contact['contacts_is_primary'])
                            ], fn ($val) => isset($val));
                        }, $groupRs), fn ($val) => isset($val))
                    ];
                    $indexRecords[] = $recordMap;
                    // Clean up to free memory
                    unset($recordMap);
                }
            }
        }

        if ($returnOnly) {
            return $indexRecords;
        }

        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }

    /**
     * Fetch relations records from database to index it
     * @param mixed $id
     */
    public function getUpdatePayload($id)
    {
        $results = [];
        $table = db_prefix() . $this->table;
        // Related Contact records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'contacts.id as id')
            ->from(db_prefix() . 'contacts')
            ->join($table, $table . '.' . $this->primaryKey . ' = ' . db_prefix() . 'contacts.userid')
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Contact',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related Ticket records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'tickets.ticketid as id')
            ->from(db_prefix() . 'tickets')
            ->join($table, $table . '.' . $this->primaryKey . ' = ' . db_prefix() . 'tickets.userid')
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Ticket',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related Invoice records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'invoices.id as id')
            ->from(db_prefix() . 'invoices')
            ->join($table, $table . '.' . $this->primaryKey . ' = ' . db_prefix() . 'invoices.clientid')
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Invoice',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related CreditNote records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'creditnotes.id as id')
            ->from(db_prefix() . 'creditnotes')
            ->join($table, $table . '.' . $this->primaryKey . ' = ' . db_prefix() . 'creditnotes.clientid')
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'CreditNote',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related Estimate records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'estimates.id as id')
            ->from(db_prefix() . 'estimates')
            ->join($table, $table . '.' . $this->primaryKey . ' = ' . db_prefix() . 'estimates.clientid')
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Estimate',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related Expense records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'expenses.id as id')
            ->from(db_prefix() . 'expenses')
            ->join($table, $table . '.' . $this->primaryKey . ' = ' . db_prefix() . 'expenses.clientid')
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Expense',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related Project records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'projects.id as id')
            ->from(db_prefix() . 'projects')
            ->join($table, $table . '.' . $this->primaryKey . ' = ' . db_prefix() . 'projects.clientid')
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Project',
                'ids' => array_pluck($records, 'id')
            ];
        }

        return $results;
    }
}

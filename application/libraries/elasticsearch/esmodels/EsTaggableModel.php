<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsTaggableModel extends EsBaseModel
{
    protected $table = 'taggables';
    protected $primaryKey = 'tag_id';
    protected $relations = [
        [
            'index' => 'tags',
            'table' => 'Tag',
            'path' => 'taggables',
            'field' => 'taggables.tag_id'
        ]
    ];

    public function indexEs($ids)
    {
        // Leave it as empty if not index to avoid exception
        // See more in the parent class
    }

    /**
     * Fetch relations records from database to index it
     * @param mixed $id
     */
    public function getUpdatePayload($id)
    {
        $results = [];
        $table = db_prefix() . $this->table;

        // Related Tag records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'tags.id as id')
            ->from(db_prefix() . 'tags')
            ->join($table, db_prefix() . 'tags.id = ' . $table . '.tag_id')
            ->where($table . '.' . $this->primaryKey, $id)
            ->group_by('tag_id')
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Tag',
                'ids' => array_pluck($records, 'id')
            ];
        }
        return $results;
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsCustomFieldsValueModel extends EsBaseModel
{
    protected $table = 'customfieldsvalues';
    protected $primaryKey = 'id';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 500);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // creditnotes
                    $selectTable . '.id as id',
                    $selectTable . '.relid as relid',
                    $selectTable . '.fieldid as fieldid',
                    $selectTable . '.fieldto as fieldto',
                    $selectTable . '.value as value',
                ]))
                ->from($selectTable)
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'id' => intval($firstRecord['id']),
                        'relid' => intval($firstRecord['relid']),
                        'fieldid' => intval($firstRecord['fieldid']),
                        'fieldto' => $firstRecord['fieldto'],
                        'value' => $firstRecord['value'],
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsProjectModel extends EsBaseModel
{
    protected $table = 'projects';
    protected $primaryKey = 'id';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 200);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // creditnotes
                    $selectTable . '.id as id',
                    $selectTable . '.name as name',
                    $selectTable . '.description as description',
                    // clients
                    db_prefix() . 'clients.userid as client_userid',
                    db_prefix() . 'clients.company as client_company',
                    db_prefix() . 'clients.vat as client_vat',
                    db_prefix() . 'clients.phonenumber as client_phonenumber',
                    db_prefix() . 'clients.city as client_city',
                    db_prefix() . 'clients.zip as client_zip',
                    db_prefix() . 'clients.address as client_address',
                    db_prefix() . 'clients.state as client_state',
                ]))
                ->from($selectTable)
                ->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . $selectTable . '.clientid', 'left')
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'id' => intval($firstRecord['id']),
                        'name' => $firstRecord['name'],
                        'description' => $firstRecord['description'] ? html_entity_decode(strip_tags($firstRecord['description'])) : null,
                        'client' => array_filter([
                            'userid' => intval($firstRecord['client_userid']),
                            'company' => $firstRecord['client_company'],
                            'vat' => $firstRecord['client_vat'],
                            'phonenumber' => $firstRecord['client_phonenumber'],
                            'city' => $firstRecord['client_city'],
                            'zip' => $firstRecord['client_zip'],
                            'address' => $firstRecord['client_address'],
                            'state' => $firstRecord['client_state'],
                        ], fn ($val) => isset($val)),
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }
}

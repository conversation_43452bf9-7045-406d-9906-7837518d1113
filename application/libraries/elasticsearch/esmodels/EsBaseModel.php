<?php

use Illuminate\Support\Arr;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\Joining\NestedQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;

defined('BASEPATH') or exit('No direct script access allowed');

class EsBaseModel
{
    /**
     * Codeigniter instance
     * @var object
     */
    protected $ci;

    public const INDEX_TMP_FOLDER = FCPATH . 'temp/es_indexes';

    protected $table;
    protected $primaryKey;
    protected $relations = [];
    protected $hasCustomDeleteQuery = false;

    public function __construct()
    {
        $this->ci = &get_instance();
    }

    public function indexEs($ids)
    {
        throw new Exception('Please override it!');
    }

    public function getPrimaryKeyValue($payload)
    {
        $operation = $payload['op'];
        $id = null;
        // Case insert before object is null
        // Case delete after object is null
        // Case update have both before/after objects
        $id = $payload[UpdateEs::DELETE_OP === $operation ? 'before' : 'after'][$this->primaryKey] ?? null;
        if (is_null($id)) {
            throw new Exception('Not found primary key value');
        }
        return $id;
    }

    protected function customDeleteQuery()
    {
        throw new Exception('Please override '.__FUNCTION__.' it in the '.__CLASS__.'!');
    }

    /**
     * Perform search in elastic for deleting records then update related relations
     * @param array $id
     * @return array list of data
     */
    public function getDeletePayload($id)
    {
        $queries = [];
        // If class have own custom delete query then use this function
        if ($this->hasCustomDeleteQuery) {
            $queries = $this->customDeleteQuery($id);
        } else {
            // Build ES query based on defined structure
            foreach ($this->relations as $relation) {
                $baseQuery = new BoolQuery();
                $baseQuery->add(new NestedQuery($relation['path'], new TermQuery($relation['field'], $id)));
                $queries[] = [
                    'table' => $relation['table'],
                    'index' => $relation['index'],
                    'queries' => $baseQuery
                ];
            }
        }

        // Return empty if not have any queries
        if (empty($queries)) {
            return [];
        }
        
        return es_bulk_search($queries);
    }

    public function getUpdatePayload($id)
    {
        return [];
    }

    /**
     * Get ids based on operation
     * @param array $payload change payload
     * @return array change payload from db/es
     */
    public function getChangePayload($payload)
    {
        $operation = $payload['op'];
        $id = $this->getPrimaryKeyValue($payload);
        // Case insert / update
        if (in_array($operation, [UpdateEs::CREATE_OP, UpdateEs::UPDATE_OP])) {
            return $this->getUpdatePayload($id);
        }
        // Case delete
        return $this->getDeletePayload($id);
    }

    /**
     * Fetch ids of the table to prepare index
     * @param integer $fromId next id use to fetch as page
     * @param integer $chunkSize item per pages
     * @return array list of ids
     */
    public function fetchTableIds($fromId, $chunkSize = 1000)
    {
        if (empty($this->table) || empty($this->primaryKey)) {
            throw new Exception('Please set table or primary key in the ' . get_called_class() . ' class');
        }
        $result = $this->ci->db->select($this->primaryKey)
            ->from(db_prefix() . $this->table)
            ->where($this->primaryKey . ' >', $fromId)
            ->limit($chunkSize)
            ->order_by($this->primaryKey, 'asc')
            ->get()
            ->result_array();

        return array_pluck($result, $this->primaryKey);
    }

    public function createIndexFiles($table, $prefix = 'idx', $chunkSize = 1000)
    {
        $hasNext = true;
        $page = 0;
        $nextId = 0;
        _maybe_create_upload_path(self::INDEX_TMP_FOLDER);
        echo "Create index file for table: $table" . PHP_EOL;
        do {
            $ids = $this->fetchTableIds($nextId, $chunkSize);
            if (!count($ids)) {
                break;
            }
            $page++;
            $fileName = $prefix . '_' . strtolower($table) . '_' . ($page) . '.txt';
            file_put_contents(self::INDEX_TMP_FOLDER . '/' . $fileName, join("\n", $ids));
            if (count($ids) >= $chunkSize) {
                $nextId = $ids[count($ids) - 1];
            } else {
                $hasNext = false;
            }
        } while ($hasNext);
        echo 'Created ' . ($page) . ' files' . PHP_EOL;
    }

    /**
     * Check update fields to perform re-index this id or not
     * @param array $payload payload of the changes
     * @return bool true - need update index. Otherwise, do nothing
     */
    public function needUpdate($payload)
    {
        $before = $payload['before'] ?? [];
        $after = $payload['after'] ?? [];
        // Merge both diff to avoid missing field due to before/after can be null based on operation
        $diffKeys = array_keys(array_merge(array_diff($before, $after), array_diff($after, $before)));
        $esMappigs = $this->ci->load->config('elasticsearch')['indices']['mappings'] ?? [];

        $indexKeys = [];
        // Some model only have relationships
        if (isset(ES_INDEXES[$this->table])) {
            // Not get keys that have properties which is relation
            $properties = Arr::get($esMappigs, sprintf("%s.properties", ES_INDEXES[$this->table]), []);
            $indexKeys = array_keys(array_filter($properties, function ($property) {
                return empty($property['properties']);
            }));
        }

        // Get all keys of relations
        $relationKeys = [];
        foreach ($this->relations as $relation) {
            $properties = Arr::get($esMappigs, sprintf("%s.properties.%s.properties", ES_INDEXES[$relation['index']], $relation['path']), []);
            $relationKeys = array_merge($relationKeys, array_keys(array_filter($properties, function ($property) {
                return empty($property['properties']);
            })));
        }
        // Merge unique keys before diff
        $allIndexes = array_values(array_unique(array_merge($indexKeys, array_values(array_unique($relationKeys)))));
        es_logging("\033[0;32mUpdated keys:\033[0m \033[0;33m".implode(', ', $diffKeys)."\033[0m".PHP_EOL);

        $updatedDiff = array_diff($diffKeys, $allIndexes);
        // If have any field that changed then return true. Otherwise, return false
        return count($updatedDiff) != count($diffKeys);
    }
}

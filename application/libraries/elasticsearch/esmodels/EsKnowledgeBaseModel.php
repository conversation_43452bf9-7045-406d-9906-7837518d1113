<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsKnowledgeBaseModel extends EsBaseModel
{
    protected $table = 'knowledge_base';
    protected $primaryKey = 'articleid';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 500);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // creditnotes
                    $selectTable . '.articleid as articleid',
                    $selectTable . '.subject as subject',
                    $selectTable . '.description as description',
                    $selectTable . '.slug as slug',
                ]))
                ->from($selectTable)
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'articleid' => intval($firstRecord['articleid']),
                        'subject' => $firstRecord['subject'],
                        'description' => $firstRecord['description'],
                        'slug' => $firstRecord['slug'],
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }
}

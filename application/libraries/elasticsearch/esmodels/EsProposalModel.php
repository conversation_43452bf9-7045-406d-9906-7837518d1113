<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsProposalModel extends EsBaseModel
{
    protected $table = 'proposals';
    protected $primaryKey = 'id';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 200);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // proposals
                    $selectTable . '.id as id',
                    $selectTable . '.subject as subject',
                    $selectTable . '.content as content',
                    $selectTable . '.proposal_to as proposal_to',
                    $selectTable . '.zip as zip',
                    $selectTable . '.state as state',
                    $selectTable . '.city as city',
                    $selectTable . '.address as address',
                    $selectTable . '.email as email',
                    $selectTable . '.phone as phone',
                    // currencies
                    db_prefix() . 'currencies.id as currency_id',
                    db_prefix() . 'currencies.symbol as currency_symbol',
                    db_prefix() . 'currencies.name as currency_name',
                    db_prefix() . 'currencies.isdefault as currency_isdefault',
                ]))
                ->from($selectTable)
                ->join(db_prefix() . 'currencies', db_prefix() . 'currencies.id = ' . $selectTable . '.currency', 'left')
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'id' => intval($firstRecord['id']),
                        'subject' => $firstRecord['subject'],
                        'content' => $firstRecord['content'],
                        'proposal_to' => $firstRecord['proposal_to'],
                        'zip' => $firstRecord['zip'],
                        'state' => $firstRecord['state'],
                        'city' => $firstRecord['city'],
                        'address' => $firstRecord['address'],
                        'email' => $firstRecord['email'],
                        'phone' => $firstRecord['phone'],
                        'currency' => array_filter([
                            'id' => intval($firstRecord['currency_id']),
                            'symbol' => $firstRecord['currency_symbol'],
                            'name' => $firstRecord['currency_name'],
                            'isdefault' => intval($firstRecord['currency_isdefault']),
                        ], fn ($val) => isset($val)),
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }
}

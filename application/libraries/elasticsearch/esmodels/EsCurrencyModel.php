<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsCurrencyModel extends EsBaseModel
{
    protected $table = 'currencies';
    protected $primaryKey = 'id';
    protected $relations = [
        [
            'index' => 'proposals',
            'table' => 'Proposal',
            'path' => 'currency',
            'field' => 'currency.id'
        ],
        [
            'index' => 'invoices',
            'table' => 'Invoice',
            'path' => 'currency',
            'field' => 'currency.id'
        ],
        [
            'index' => 'creditnotes',
            'table' => 'CreditNote',
            'path' => 'currency',
            'field' => 'currency.id'
        ],
        [
            'index' => 'estimates',
            'table' => 'Estimate',
            'path' => 'currency',
            'field' => 'currency.id'
        ]
    ];

    public function indexEs($ids)
    {
        // Leave it as empty if not index to avoid exception
        // See more in the UpdateEs::execute:55
    }

    /**
     * Fetch relations records from database to index it
     * @param mixed $id
     */
    public function getUpdatePayload($id)
    {
        $results = [];
        $table = db_prefix() . $this->table;

        // Related Proposal records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'proposals.id as id')
            ->from(db_prefix() . 'proposals')
            ->join($table, db_prefix() . 'proposals.currency = ' . $table . '.' . $this->primaryKey)
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Proposal',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related Invoice records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'invoices.id as id')
            ->from(db_prefix() . 'invoices')
            ->join($table, db_prefix() . 'invoices.currency = ' . $table . '.' . $this->primaryKey)
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Invoice',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related CreditNote records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'creditnotes.id as id')
            ->from(db_prefix() . 'creditnotes')
            ->join($table, db_prefix() . 'creditnotes.currency = ' . $table . '.' . $this->primaryKey)
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'CreditNote',
                'ids' => array_pluck($records, 'id')
            ];
        }

        // Related Estimate records to id
        $records = $this->ci->db
            ->select(db_prefix() . 'estimates.id as id')
            ->from(db_prefix() . 'estimates')
            ->join($table, db_prefix() . 'estimates.currency = ' . $table . '.' . $this->primaryKey)
            ->where($table . '.' . $this->primaryKey, $id)
            ->get()
            ->result_array();

        if (count($records)) {
            $results[] = [
                'table' => 'Estimate',
                'ids' => array_pluck($records, 'id')
            ];
        }

        return $results;
    }
}

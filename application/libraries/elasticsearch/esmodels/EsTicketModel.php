<?php

defined('BASEPATH') or exit('No direct script access allowed');

class EsTicketModel extends EsBaseModel
{
    protected $table = 'tickets';
    protected $primaryKey = 'ticketid';
    public function indexEs($ids)
    {
        $chunkIds = array_chunk($ids, 200);
        $indexRecords = [];
        $selectTable = db_prefix() . $this->table;
        $this->ci->load->helper('array');

        foreach ($chunkIds as $chunk) {
            $results = $this->ci->db
                ->select(join(',', [
                    // creditnotes
                    $selectTable . '.ticketid as ticketid',
                    $selectTable . '.subject as subject',
                    $selectTable . '.message as message',
                    // contact
                    db_prefix() . 'contacts.id as contact_id',
                    db_prefix() . 'contacts.email as contact_email',
                    db_prefix() . 'contacts.fullname as contact_fullname',
                    db_prefix() . 'contacts.phonenumber as contact_phonenumber',
                    // client
                    db_prefix() . 'clients.userid as client_userid',
                    db_prefix() . 'clients.company as client_company',
                    db_prefix() . 'clients.vat as client_vat',
                    db_prefix() . 'clients.phonenumber as client_phonenumber',
                    db_prefix() . 'clients.city as client_city',
                    db_prefix() . 'clients.state as client_state',
                    db_prefix() . 'clients.address as client_address',
                    // department
                    db_prefix() . 'departments.departmentid as department_departmentid',
                    db_prefix() . 'departments.name as department_name',
                ]))
                ->from($selectTable)
                ->join(db_prefix() . 'departments', db_prefix() . 'departments.departmentid = ' . $selectTable . '.department', 'left')
                ->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . $selectTable . '.userid', 'left')
                ->join(db_prefix() . 'contacts', db_prefix() . 'contacts.id = ' . $selectTable . '.contactid', 'left')
                ->where_in($selectTable . '.' . $this->primaryKey, $chunk)
                ->order_by($selectTable . '.' . $this->primaryKey)
                ->get()
                ->result_array();

            if (count($results)) {
                foreach ($results as $firstRecord) {
                    $indexRecords[] = [
                        'ticketid' => intval($firstRecord['ticketid']),
                        'subject' => $firstRecord['subject'],
                        'message' => $firstRecord['message'] ? html_entity_decode(strip_tags($firstRecord['message'])) : null,
                        'contact' => array_filter([
                            'id' => intval($firstRecord['contact_id']),
                            'email' => $firstRecord['contact_email'],
                            'fullname' => $firstRecord['contact_fullname'],
                            'phonenumber' => $firstRecord['contact_phonenumber'],
                        ], fn ($val) => isset($val)),
                        'client' => array_filter([
                            'userid' => intval($firstRecord['client_userid']),
                            'company' => $firstRecord['client_company'],
                            'vat' => $firstRecord['client_vat'],
                            'phonenumber' => $firstRecord['client_phonenumber'],
                            'city' => $firstRecord['client_city'],
                            'state' => $firstRecord['client_state'],
                            'address' => $firstRecord['client_address'],
                        ], fn ($val) => isset($val)),
                        'department' => array_filter([
                            'departmentid' => intval($firstRecord['department_departmentid']),
                            'name' => $firstRecord['department_name'],
                        ], fn ($val) => isset($val)),
                    ];
                }
            }
        }
        es_bulk_index(ES_INDEXES[$this->table], $indexRecords, $this->primaryKey);
        // Clean up to free memory
        unset($indexRecords);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
require_once(APPPATH . 'libraries/import/App_import.php');

class Import_customer_admins extends App_import
{
    protected $requiredFields = [
        'company_id',
        'vat_number',
        'staff_id',
        'staff_email'
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function perform()
    {
        $this->initialize();

        $databaseFields = $this->getImportableDatabaseFields();
        $message = 'Success';
        $error = true;

        $totalDatabaseFields = count($databaseFields);
        $dataUpdate = [];
        foreach ($this->getRows() as $row) {
            $rowData = [];

            for ($i = 0; $i < $totalDatabaseFields; $i++) {
                if (!isset($row[$i]) || !$row[$i]) {
                    continue;
                }

                $rowData[$databaseFields[$i]] = $row[$i];
            }

            $rowData = $this->trimInsertValues($rowData);

            // Check if all required fields have been completed
            if (count($rowData) === $totalDatabaseFields) {
                $dataUpdate[] = $rowData;
            } else {
                return [
                    'error' => $error,
                    'message' => 'Data invalid, please update and try again'
                ];
            }
        }

        $totalDataUpdate = count($dataUpdate);

        // Check if the company id is duplicated
        $duplicate = count(array_unique(array_column($dataUpdate, 'company_id'))) < $totalDataUpdate;

        if ($duplicate) {
            return [
                'error' => $error,
                'message' => 'Company ID is duplicate'
            ];
        }

        // Get a list of invalid company ids
        $companyInvalidIds = $this->invalidCompanyList($dataUpdate);

        if (count($companyInvalidIds)) {
            return [
                'error' => $error,
                'message' => 'Company ID: ' . implode(' ,', $companyInvalidIds) . ' is not valid'
            ];
        }

        // Get a list of invalid staff ids
        $staffInvalidIds = $this->invalidStaffList($dataUpdate);

        if (count($staffInvalidIds)) {
            return [
                'error' => $error,
                'message' => 'Staff ID: ' . implode(' ,', $staffInvalidIds) . ' is not valid'
            ];
        }

        // Code execution !!!
        if ($this->isSimulation()) {
            $this->simulationData = $dataUpdate;
        } else {
            foreach ($dataUpdate as $data) {
                $this->ci->clients_model->assign_admins([
                    'customer_admins' => [
                        $data['staff_id']
                    ],
                    'is_sa_assigned' => 1,
                    ],
                    $data['company_id']
                );
                $this->incrementImported();
            }
        }

        $error = false;

        return [
            'error' => $error,
            'message' => $message
        ];
    }

    private function invalidCompanyList(array $data = [])
    {
        $companyInvalidIds = [];

        foreach ($data as $value) {
            $company = $this->ci->clients_model->get($value['company_id'], ['vat' => $value['vat_number']]);
            if (!$company) {
                $companyInvalidIds[] = $value['company_id'];
            }
        }

        return $companyInvalidIds;
    }

    private function invalidStaffList(array $data = [])
    {
        $staffInvalidIds = [];

        foreach ($data as $value) {
            $staff = $this->ci->staff_model->get($value['staff_id'], ['email' => $value['staff_email']]);
            if (!$staff) {
                $staffInvalidIds[] = $value['staff_id'];
            }
        }

        return $staffInvalidIds;
    }

    public function formatFieldNameForHeading($field)
    {
        if (strtolower($field) == 'company_id') {
            return 'Company ID';
        }

        if (strtolower($field) == 'vat_number') {
            return 'VAT number';
        }

        if (strtolower($field) == 'staff_id') {
            return 'Staff ID';
        }

        return parent::formatFieldNameForHeading($field);
    }

    protected function staff_email_formatSampleData()
    {
        return uniqid() . '@example.com';
    }

    protected function failureRedirectURL()
    {
        return admin_url('clients/import_customer_admins');
    }
}

<?php

use app\services\utilities\Arr;

defined('BASEPATH') or exit('No direct script access allowed');
require_once(APPPATH . 'libraries/import/App_import.php');

class Import_customers extends App_import
{
    protected $notImportableFields = [];

    private $countryFields = ['country', 'billing_country', 'shipping_country'];

    protected $requiredFields = [
        // contacts table
        'fullname', 'email', 'title',
        // clients table
        'vat', 'business_name', 'address', 'type_of_customer', 'usage_behavior'
    ];
    protected $clientFieldsOrders = [
        'business_name', 'company', 'vat', 'foreign_vat', 'address',
        'type_of_customer', 'usage_behavior', 'website', 'company_facebook', 'company_linkedin',
        'company_email', 'phonenumber',
        'company_notes', 'num_employees', 'company_types', 'company_status', 'established_date', 'industry', 'nationality',
        'source', 'source_reason', 'source_reference_link'
    ];
    protected $formattedValueKeys = [
        'company_types' => 'formatCompanyTypeToArray'
    ];

    protected $sampleDataText = '';

    protected $taxonomies = [];

    public function __construct()
    {
        $this->notImportableFields = hooks()->apply_filters('not_importable_clients_fields', [
            'userid', 'id', 'is_primary', 'password', 'datecreated', 'last_ip', 'last_login',
            'last_password_change', 'active', 'new_pass_key', 'new_pass_key_requested', 'leadid',
            'default_currency', 'profile_image', 'default_language', 'direction', 'show_primary_contact',
            'invoice_emails', 'estimate_emails', 'project_emails', 'task_emails', 'contract_emails',
            'credit_note_emails', 'ticket_emails', 'addedfrom', 'registration_confirmed', 'last_active_time',
            'email_verified_at', 'email_verification_key', 'email_verification_sent_at',
            'firstname', 'lastname', 'status', 'review_status', 'updated_at',

            // New customer feature
            'billing_street', 'billing_city', 'billing_state', 'billing_zip', 'billing_country',
            'shipping_street', 'shipping_city', 'shipping_state', 'shipping_zip', 'shipping_country',
            'longitude', 'latitude', 'stripe_id', 'salesperson_id','expected_revenue',
            'expected_revenue_type', 'show_expected_revenue', 'zip', 'state', 'country', 'city',

            // New fields
            'approved_at', 'source_attachment', 'last_assigned',

            // Contact fields
            'created_by', 'sa_approved_at',
            'num_of_usage_behavior',
        ]);

        $this->fieldNames = [
            'business_name' => _l('client_company'),
            'company' => _l('client_request_company_short_name'),
            'vat' => _l('client_vat_number'),
            'foreign_vat' => _l('client_request_foreign_vat'),
            'address' => _l('client_address'),
            'website' => _l('client_website'),
            'company_facebook' => _l('client_request_company_facebook'),
            'company_linkedin' => _l('client_request_company_linkedin'),
            'company_email' => _l('client_request_company_email'),
            'phonenumber' => _l('client_phonenumber'),
            'company_notes' => _l('client_request_company_branch_note'),
            'owner' => _l('client_request_owner'),
            'established_date' => _l('client_request_established_date'),
            'company_types' => _l('client_request_company_types'),
            'num_employees' => _l('client_request_num_employees'),
            'company_status' => _l('client_request_company_status'),
            'source' => _l('client_request_create_source'),
            'source_reason' => _l('client_request_create_reason'),
            'source_reference_link' => _l('client_request_reference_link'),
            'industry' => _l('client_request_company_industry'),
            'nationality' => _l('client_request_company_nationality'),
        ];
        parent::__construct();

        $this->importGuidelines = [];

        $this->addImportGuidelinesInfo('Duplicate email rows won\'t be imported.', true);

        $this->addImportGuidelinesInfo('Make sure you configure the default contact permission in <a href="' . admin_url('settings?group=clients') . '" target="_blank">Setup->Settings->Customers</a> to get the best results like auto assigning contact permissions and email notification settings based on the permission.');
        $this->addImportGuidelinesInfo(_l('client_request_import_company_types', _l('client_request_company_types')));
        $this->addImportGuidelinesInfo(_l('import_number_fields'));
        $this->addImportGuidelinesInfo(_l('import_contact_sex_field'));
        $this->addImportGuidelinesInfo('If the column <b>you are trying to import is date make sure that is formatted in format d/m/Y (' . date('d/m/Y') . ').</b>');
        $this->addImportGuidelinesInfo('Your CSV data should be in the format below. The first line of your CSV file should be the column headers as in the table example. Also make sure that your file is <b>UTF-8</b> to avoid unnecessary <b>encoding problems</b>.');

        $this->formattedValueKeys = array_merge($this->formattedValueKeys, [
            'birthday' => fn($value) => !empty($value) ? to_date_format($value, 'd/m/Y') : null,
            'established_date' => fn($value) => !empty($value) ? to_date_format($value, 'd/m/Y') : null,
        ]);
    }

    public function perform()
    {
        $this->initialize();
        $error = true;
        $dataInsert = [];
        $databaseFields = $this->getImportableDatabaseFields();
        $totalDatabaseFields = count($databaseFields);

        $databaseCustomFields = $this->getCustomFields();
        $totalDatabaseCustomFields = count($databaseCustomFields);
        $this->ci->load->helper(['date', 'app_email']);
        $this->taxonomies = get_ams_taxonomies(['industries','nationalities','num_employees']);

        foreach ($this->getRows() as $rowNumber => $row) {
            $insert = [];
            $rowInvalid = [];

            for ($i = 0; $i < $totalDatabaseFields; $i++) {
                if (!isset($row[$i])) {
                    continue;
                }

                $row[$i] = $this->checkNullValueAddedByUser($row[$i]);

                if (in_array($databaseFields[$i], $this->requiredFields) && $row[$i] == '') {
                    return [
                        'error' => $error,
                        'message' => 'Line: ' . ($rowNumber + 1) . ', Errors: ' . str_replace('{field}', $this->formatFieldNameForHeading($databaseFields[$i]), _l('form_validation_required'))
                    ];
                }

                if (
                    in_array($databaseFields[$i], $this->requiredFields) &&
                    $row[$i] == '' &&
                    $databaseFields[$i] != 'company'
                    && $databaseFields[$i] != 'email'
                ) {
                    $row[$i] = '/';
                } elseif (in_array($databaseFields[$i], $this->countryFields)) {
                    $row[$i] = $this->countryValue($row[$i]);
                } elseif ($databaseFields[$i] == 'email') {
                    if (!valid_email($row[$i])) {
                        $rowInvalid[] = $this->formatFieldNameForHeading($databaseFields[$i]).' is invalid';
                    } elseif ($this->isDuplicateContact($row[$i])) {
                        $rowInvalid[] = $this->formatFieldNameForHeading($databaseFields[$i]).' is duplicated';
                    }
                } elseif ($databaseFields[$i] == 'stripe_id') {
                    if (empty($row[$i]) || (!empty($row[$i]) && !startsWith($row[$i], 'cus_'))) {
                        $row[$i] = null;
                    }
                } elseif ($databaseFields[$i] == 'contact_phonenumber') {
                    if (is_automatic_calling_codes_enabled() && !empty($row[$i])) {
                        $customerCountryIndex = array_search('country', $databaseFields);
                        if (isset($row[$customerCountryIndex]) && !empty($row[$customerCountryIndex])) {
                            $customerCountry = $this->getCountry(null, $this->countryValue($row[$customerCountryIndex]));

                            if ($customerCountry) {
                                $callingCode = '+' . ltrim($customerCountry->calling_code, '+');

                                if (startsWith($row[$i], $customerCountry->calling_code)) { // with calling code but without the + prefix
                                    $row[$i] = '+' . $row[$i];
                                } elseif (!startsWith($row[$i], $callingCode)) {
                                    $row[$i] = $callingCode . $row[$i];
                                }
                            }
                        }
                    }
                } elseif ($databaseFields[$i] == 'business_name') {
                    $businessName = $row[$i];
                    if (total_rows(db_prefix() . 'clients', ['business_name' => $businessName, 'approved_at IS NOT NULL' => null]) > 0) {
                        $rowInvalid[] = _l('client_company').' is existed';
                    }
                } elseif ($databaseFields[$i] == 'type_of_customer') {
                    $row[$i] = $this->getCustomerTypeIds($row[$i]);
                    if (!$row[$i]) {
                        $rowInvalid[] = 'Type of customer is invalid';
                    }
                } elseif ($databaseFields[$i] == 'usage_behavior') {
                    $row[$i] = $this->getUsageBehaviorIds($row[$i]);
                    if (!$row[$i]) {
                        $rowInvalid[] = 'Usage behavior is invalid';
                    }
                } elseif ($databaseFields[$i] == 'num_employees') {
                    $mapValue = $this->getNumEmployerIds($row[$i]);
                    if ($row[$i] && !$mapValue) {
                        $rowInvalid[] = _l('client_request_num_employees').' is invalid';
                    }
                    $row[$i] = $mapValue;
                } elseif ($databaseFields[$i] == 'company_types' && $row[$i]) {
                    $values = explode(';', $row[$i]);
                    $mapValues = $this->getCompanyTypeIds($values);
                    if (count($values) != count($mapValues)) {
                        $rowInvalid[] = _l('client_request_company_types').' is invalid';
                    } else {
                        $row[$i] = implode(';', $mapValues);
                    }
                } elseif ($databaseFields[$i] == 'company_status' && $row[$i]) {
                    $mapValue = $this->getCompanyStatusIds($row[$i]);
                    if ($row[$i] && !$mapValue) {
                        $rowInvalid[] = _l('client_request_company_status').' is invalid';
                    }
                    $row[$i] = $mapValue;
                } elseif ($databaseFields[$i] == 'industry' && $row[$i]) {
                    $values = explode(';', $row[$i]);
                    $mapValues = $this->getIndustryIds($values);
                    if (count($values) != count($mapValues)) {
                        $rowInvalid[] = _l('client_request_company_industry').' is invalid';
                    } else {
                        $row[$i] = implode(';', $mapValues);
                    }
                } elseif ($databaseFields[$i] == 'nationality' && $row[$i]) {
                    $values = explode(';', $row[$i]);
                    $mapValues = $this->getNationalityIds($values);
                    if (count($values) != count($mapValues)) {
                        $rowInvalid[] = _l('client_request_company_nationality').' is invalid';
                    } else {
                        $row[$i] = implode(';', $mapValues);
                    }
                } elseif ($databaseFields[$i] == 'source' && $row[$i]) {
                    $mapValue = $this->getSourceIds($row[$i]);
                    if ($row[$i] && !$mapValue) {
                        $rowInvalid[] =_l('client_request_create_source').' is invalid';
                    } else {
                        $row[$i] = $mapValue;
                    }
                } elseif ($databaseFields[$i] == 'company_email') {
                    if ($row[$i] && !valid_email($row[$i])) {
                        $rowInvalid[] = $this->formatFieldNameForHeading($databaseFields[$i]).' is invalid';
                    }
                } elseif ($databaseFields[$i] == 'customer_admin' && $row[$i]) {
                    if (!is_numeric($row[$i])) {
                        $rowInvalid[] = $this->formatFieldNameForHeading($databaseFields[$i]).' is invalid staff id';
                    } elseif (!$this->isExistsStaff($row[$i])) {
                        $rowInvalid[] = $this->formatFieldNameForHeading($databaseFields[$i]).' not found staff id ' . $row[$i];
                    }
                }

                $insert[$databaseFields[$i]] = $row[$i];
            }

            for ($i = $totalDatabaseFields; $i < $totalDatabaseFields + $totalDatabaseCustomFields; $i++) {
                $insert[$databaseCustomFields[$i - $totalDatabaseFields]['name']] = $row[$i];
            }

            if (count($rowInvalid)) {
                return [
                    'error' => $error,
                    'message' => 'Line: ' . ($rowNumber + 1) . ', Errors:<br>&nbsp;&nbsp;-&nbsp' . implode('<br>&nbsp;&nbsp;-&nbsp', $rowInvalid)
                ];
            }

            $insert = $this->trimInsertValues($insert);

            if (count($insert)) {
                $dataInsert[] = $insert;
            }
        }
        
        $rowDataInvalid = $this->rowDataInvalid($dataInsert);

        if (count($rowDataInvalid)) {
            return [
                'error' => $error,
                'message' => implode('<br>', array_map(function ($row) {
                    return 'Line: ' . $row['line'] . ', Errors:<br>&nbsp;&nbsp;-&nbsp' . implode('<br>&nbsp;&nbsp;-&nbsp', $row['errors']);
                }, $rowDataInvalid))
            ];
        }

        // Check if the VAT is unique
        $duplicate = count(array_unique(array_column($dataInsert, 'vat'))) < count($dataInsert);

        if ($duplicate) {
            return [
                'error' => $error,
                'message' => 'VAT is duplicate'
            ];
        }

        // Check if the Business Name is duplicated
        $duplicate = count(array_unique(array_column($dataInsert, 'business_name'))) < count($dataInsert);

        if ($duplicate) {
            return [
                'error' => $error,
                'message' => _l('client_company').' is duplicated'
            ];
        }

        foreach ($dataInsert as $rowNumber => $row) {
            if (!$this->isSimulation()) {
                $customerAdminId = $this->ci->input->post('customer_admin');
                $groupIn = $this->ci->input->post('groups_in[]');

                $insert = array_slice($row, 0, -$totalDatabaseCustomFields);
                $insert = $this->convertValueToDbFormat($insert);
                $insert['is_primary'] = 1;
                $insert['datecreated'] = date('Y-m-d H:i:s');
                $insert['donotsendwelcomeemail'] = true;

                if ($this->ci->input->post('default_pass_all')) {
                    $insert['password'] = $this->ci->input->post('default_pass_all', false);
                }

                if ($this->shouldAddContactUnderCustomer($insert)) {
                    $this->addContactUnderCustomer($insert);

                    continue;
                }
                if ($customerAdminId || empty($insert['customer_admin'])) {
                    unset($insert['customer_admin']);
                }
                $clientData = array_slice($insert, 0, $totalDatabaseFields);
                $industries = Arr::pull($clientData, 'industry');
                $nationalities = Arr::pull($clientData, 'nationality');
                $id = $this->ci->clients_model->add($clientData, true);
                if ($id) {
                    if ($groupIn) {
                        $this->insertCustomerGroups($groupIn, $id);
                    }
                    //assign staff
                    if ($customerAdminId) {
                        $this->insertCustomerAdmins([$customerAdminId], $id);
                    }
                    if (isset($industries)) {
                        $this->ci->clients_model->update_industries($id, explode(';', $industries));
                    }
                    if (isset($nationalities)) {
                        $this->ci->clients_model->update_nationalities($id, explode(';', $nationalities));
                    }
                    if (!has_permission('customers', '', 'view')) {
                        $assign['customer_admins']   = [];
                        $assign['customer_admins'][] = get_staff_user_id();
                        $assign['is_sa_assigned'] = 1;
                        $this->ci->clients_model->assign_admins($assign, $id);
                    }

                    // If this record is not assign to sales, then move to free data
                    if (!$customerAdminId && has_permission('customers', '', 'view') && empty($insert['customer_admin'])) {
                        hooks()->do_action('move_to_free_data', $id);
                    }

                    $fieldNumber = count($databaseFields);
                    $this->handleCustomFieldsInsert($id, array_values($row), $fieldNumber, $rowNumber, 'customers');
                    $this->incrementImported();
                }
            } else {
                $this->simulationData[$rowNumber] = $this->formatValuesForSimulation($row);
            }
        }

        $error = false;

        return [
            'error' => $error,
            'message' => 'Success'
        ];
    }

    public function formatFieldNameForHeading($field)
    {
        if (strtolower($field) == 'title') {
            return 'Position';
        }

        return parent::formatFieldNameForHeading($field);
    }

    protected function email_formatSampleData()
    {
        return uniqid() . '@example.com';
    }

    protected function customer_admin_formatSampleData()
    {
        return 1;
    }

    protected function birthday_formatSampleData()
    {
        return date('d/m/Y');
    }

    protected function sex_formatSampleData()
    {
        return '';
    }

    protected function phonenumber_formatSampleData()
    {
        return '0123456789';
    }

    protected function contact_phonenumber_formatSampleData()
    {
        return '0123456789';
    }

    protected function vat_formatSampleData()
    {
        return '0316974048';
    }

    protected function title_formatSampleData()
    {
        return 'Sample Data';
    }

    protected function business_name_formatSampleData()
    {
        return 'Sample Data';
    }

    protected function address_formatSampleData()
    {
        return 'Sample Data';
    }

    protected function type_of_customer_formatSampleData()
    {
        return 'Sample Data';
    }

    protected function usage_behavior_formatSampleData()
    {
        return 'Sample Data';
    }

    protected function failureRedirectURL()
    {
        return admin_url('clients/import');
    }

    protected function afterSampleTableHeadingText($field)
    {
        $contactFields = [
            'fullname', 'email', 'contact_phonenumber', 'title', 'birthday', 'landline', 'sex', 'facebook', 'zalo', 'linkedin', 'skype', 'other'
        ];

        if (in_array($field, $contactFields)) {
            return '<br /><span class="text-info">' . _l('import_contact_field') . '</span>';
        }
    }

    private function insertCustomerGroups($groups, $customer_id)
    {
        foreach ($groups as $group) {
            $this->ci->db->insert(db_prefix() . 'customer_groups', [
                                                    'customer_id' => $customer_id,
                                                    'groupid'     => $group,
                                                ]);
        }
    }

    private function insertCustomerAdmins($members, $customer_id)
    {
        foreach ($members as $member) {
            $this->ci->db->insert(db_prefix() . 'customer_admins', [
                'customer_id' => $customer_id,
                'staff_id'     => $member,
                'date_assigned' => date('Y-m-d H:i:s'),
                'expired_at' => date('Y-m-d H:i:s', strtotime('+60 days')),
            ]);
        }
    }

    private function shouldAddContactUnderCustomer($data)
    {
        return (isset($data['business_name']) && $data['business_name'] != '' && $data['business_name'] != '/')
        && (total_rows(db_prefix() . 'clients', ['business_name' => $data['business_name']]) === 1);
    }

    private function addContactUnderCustomer($data)
    {
        $contactFields = $this->getContactFields();
        $this->ci->db->where('business_name', $data['business_name']);

        $existingCompany = $this->ci->db->get(db_prefix() . 'clients')->row();
        $tmpInsert       = [];

        foreach ($data as $key => $val) {
            foreach ($contactFields as $tmpContactField) {
                if (isset($data[$tmpContactField])) {
                    $tmpInsert[$tmpContactField] = $data[$tmpContactField];
                }
            }
        }
        $tmpInsert['donotsendwelcomeemail'] = true;

        if (isset($data['contact_phonenumber'])) {
            $tmpInsert['phonenumber'] = $data['contact_phonenumber'];
        }

        $this->ci->clients_model->add_contact($tmpInsert, $existingCompany->userid, true);
    }

    private function getContactFields()
    {
        return $this->ci->db->list_fields(db_prefix() . 'contacts');
    }

    private function isDuplicateContact($email)
    {
        return total_rows(db_prefix() . 'contacts', ['email' => $email]);
    }

    private function isExistsStaff($staffId)
    {
        return total_rows(db_prefix() . 'staff', ['staffid' => $staffId]);
    }

    private function formatValuesForSimulation($values)
    {
        // ATM only country fields
        foreach ($this->countryFields as $country_field) {
            if (array_key_exists($country_field, $values)) {
                if (!empty($values[$country_field]) && is_numeric($values[$country_field])) {
                    $country = $this->getCountry(null, $values[$country_field]);
                    if ($country) {
                        $values[$country_field] = $country->short_name;
                    }
                }
            }
        }

        return $values;
    }

    private function getCountry($search = null, $id = null)
    {
        if ($search) {
            $searchSlug = slug_it($search);

            if (empty($search)) {
                return null;
            }

            if ($country = $this->ci->app_object_cache->get('import-country-search-' . $searchSlug)) {
                return $country;
            }

            $this->ci->db->where('iso2', $search);
            $this->ci->db->or_where('short_name', $search);
            $this->ci->db->or_where('long_name', $search);
        } else {
            if (empty($id)) {
                return null;
            }

            if ($country = $this->ci->app_object_cache->get('import-country-id-' . $id)) {
                return $country;
            }

            $this->ci->db->where('country_id', $id);
        }

        $country = $this->ci->db->get(db_prefix() . 'countries')->row();

        if ($search) {
            $this->ci->app_object_cache->add('import-country-search-' . $searchSlug, $country);
        } else {
            $this->ci->app_object_cache->add('import-country-id-' . $id, $country);
        }

        return $country;
    }

    private function countryValue($value)
    {
        if ($value != '') {
            if (!is_numeric($value)) {
                $country = $this->getCountry($value);
                $value   = $country ? $country->country_id : 0;
            }
        } else {
            $value = 0;
        }

        return $value;
    }

    public function rowDataInvalid(array $data = [])
    {
        $companyInvalidIds = [];

        foreach ($data as $key => $value) {
            $errors = [];
            if (!$this->checkValidVatNumber($value['vat'])) {
                $errors[] = 'Invalid or duplicated Vat number';
            }

            if (trim($value['birthday']) !== '' && !$this->checkValidBirthday($value['birthday'], 'd/m/Y')) {
                $errors[] = 'Invalid Birthday format';
            }

            if (trim($value['established_date']) !== '' && !$this->checkValidBirthday($value['established_date'], 'd/m/Y')) {
                $errors[] = sprintf('Invalid %s format', _l('client_request_established_date'));
            }

            if (trim($value['sex']) !== '' && !in_array($value['sex'], [1, 2])) {
                $errors[] = 'Invalid Sex value';
            }

            if (count($errors)) {
                $companyInvalidIds[] = [
                    'line' => $key + 2,
                    'errors' => $errors
                ];
            }
        }

        return $companyInvalidIds;
    }

    /**
     * Check birthday is valid format or not
     * @param string $value birthday value
     * @param string $format date time format, default is Y-m-d
     * @return bool True is valid format, otherwise is false
     */
    private function checkValidBirthday($value, $format = 'Y-m-d')
    {
        $date = DateTime::createFromFormat($format, $value);
        return $date && $date->format($format) === $value;
    }

    private function checkValidVatNumber($value)
    {
        $pattern = "";

        // Check for 10-digit VAT numbers
        if (strlen($value) === 10) {
            $pattern = '/\d{10}$/';
        }
        // Check for 14-digit VAT numbers
        else if (strlen($value) === 14) {
            $pattern = '/\d{10}-\d{3}$/';
        }

        if ($pattern) {
            // Check if the VAT number matches the pattern
            if (preg_match($pattern, $value)) {
                // Check unique in table
                if (total_rows(db_prefix() . 'clients', [
                    'vat' => $value,
                    'approved_at IS NOT NULL' => null
                ])) {
                    return false;
                }
                return true;
            }
        }
        return false;
    }

    /**
     * Perform sort fields to show in the template
     * @param array $dbFields input fields that will be sorted
     * @return array list fields after sorted
     */
    public function sortClientsFields($dbFields)
    {
        $remain = array_diff($dbFields, $this->clientFieldsOrders);
        return array_merge($this->clientFieldsOrders, $remain);
    }

    /**
     * Format input value from csv to DB format
     * @param string $value CSV's value format
     * @return string DB's value format
     */
    protected function formatCompanyTypeToArray($value)
    {
        return json_encode(explode(';', $value));
    }

    /**
     * Handle logic to convert from CSV's value to DB's value format by defined function
     * @param array $list list data will be formated
     * @return array list data after formmated
     */
    protected function convertValueToDbFormat($list)
    {
        foreach ($this->formattedValueKeys as $key => $func) {
            if (isset($list[$key])) {
                if (is_string($func) && method_exists($this, $func)) {
                    $list[$key] = call_user_func_array([$this, $func], [$list[$key]]);
                } elseif (is_callable($func)) { // For php function like join, split, etc...
                    $list[$key] = call_user_func_array($func, [$list[$key]]);
                }
            }
        }

        return $list;
    }

    protected function getCustomerTypeIds($textValue)
    {
        $types = array_column(get_all_type_of_customers(), 'value');
        return array_search($textValue, $types) > -1 ? $textValue : null;
    }

    protected function getUsageBehaviorIds($textValue)
    {
        $usages = array_column(get_all_usage_behavior(), 'value');
        return array_search($textValue, $usages) > -1 ? $textValue : null;
    }

    protected function getNumEmployerIds($textValue)
    {
        $numEmployees = $this->taxonomies['num_employees'] ?? [];
        $textArr = array_column($numEmployees, 'text');
        $idx = array_search($textValue, $textArr);
        return $idx > -1 ? $numEmployees[$idx]['id'] : null;
    }

    protected function getCompanyTypeIds($textValue)
    {
        $types = array_column(get_all_company_types(), 'value');
        return array_filter(array_map(function ($text) use ($types) {
            return array_search(strtolower($text), $types) > -1 ? strtolower($text) : null;
        }, $textValue));
    }

    protected function getCompanyStatusIds($textValue)
    {
        $statuses = get_all_company_statuses();
        $columns = array_column($statuses, 'text');
        $idx = array_search($textValue, $columns);
        return $idx > -1 ? $statuses[$idx]['value'] : null;
    }

    protected function getIndustryIds($textValues)
    {
        $industries = $this->taxonomies['industries'] ?? [];
        $columns = array_column($industries, 'text');
        return array_filter(array_map(function ($text) use ($columns, $industries) {
            $idx = array_search($text, $columns);
            return $idx > -1 ? $industries[$idx]['id'] : null;
        }, $textValues));
    }

    protected function getNationalityIds($textValues)
    {
        $nationalities = $this->taxonomies['nationalities'] ?? [];
        $columns = array_column($nationalities, 'text');
        return array_filter(array_map(function ($text) use ($columns, $nationalities) {
            $idx = array_search($text, $columns);
            return $idx > -1 ? $nationalities[$idx]['id'] : null;
        }, $textValues));
    }

    protected function getSourceIds($textValue)
    {
        $sources = get_all_company_creation_sources();
        $columns = array_column($sources, 'text');
        $idx = array_search($textValue, $columns);
        return $idx > -1 ? $sources[$idx]['value'] : null;
    }
}

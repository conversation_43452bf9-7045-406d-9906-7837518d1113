<?php

use Entities\Client;
use Illuminate\Support\Collection;

defined('BASEPATH') or exit('No direct script access allowed');
require_once(APPPATH . 'libraries/import/App_import.php');

class Import_num_of_usage_behavior extends App_import
{
    protected $requiredFields = [
        'company_id',
        'num_of_usage_behavior',
    ];

    public function __construct()
    {
        parent::__construct();
        $this->importGuidelines = [];
        $this->addImportGuidelinesInfo('Your CSV data should be in the format below. The first line of your CSV file should be the column headers as in the table example. Also make sure that your file is <b>UTF-8</b> to avoid unnecessary <b>encoding problems</b>.');
    }

    public function perform()
    {
        $this->initialize();

        $databaseFields = $this->getImportableDatabaseFields();
        $message = 'Success';
        $error = true;

        $totalDatabaseFields = count($databaseFields);
        $dataUpdate = [];
        foreach ($this->getRows() as $row) {
            $rowData = [];

            for ($i = 0; $i < $totalDatabaseFields; $i++) {
                if (!isset($row[$i]) || !$row[$i]) {
                    continue;
                }

                $rowData[$databaseFields[$i]] = $row[$i];
            }

            $rowData = $this->trimInsertValues($rowData);

            // Check if all required fields have been completed
            if (count($rowData) !== $totalDatabaseFields) {
                return [
                    'error' => $error,
                    'message' => 'Data invalid, please update and try again'
                ];
            }
            $dataUpdate[] = $rowData;
        }

        $totalDataUpdate = count($dataUpdate);

        // Check if the company id is duplicated
        $duplicate = count(array_unique(array_column($dataUpdate, 'company_id'))) < $totalDataUpdate;

        if ($duplicate) {
            return [
                'error' => $error,
                'message' => 'Company ID is duplicate'
            ];
        }

        $importCollect = collect($dataUpdate);
        // Get a list of invalid company ids
        $companyInvalidIds = $this->invalidCompanyList($importCollect->pluck('company_id'));

        if (count($companyInvalidIds)) {
            return [
                'error' => $error,
                'message' => 'Company ID: ' . implode(' ,', $companyInvalidIds) . ' is not valid'
            ];
        }

        // Get a list of invalid staff ids
        $staffInvalidIds = $importCollect->filter(fn($value) => intval($value['num_of_usage_behavior']) <= 0);

        if (count($staffInvalidIds)) {
            return [
                'error' => $error,
                'message' => 'Num of usage behavior: ' . $staffInvalidIds->pluck('num_of_usage_behavior')->join(', ') . ' is not valid'
            ];
        }

        // Code execution !!!
        if ($this->isSimulation()) {
            $this->simulationData = $dataUpdate;
        } else {
            try {
                DB::beginTransaction();
                $importCollect->each(function ($collect) {
                    Client::where('userid', $collect['company_id'])->update([
                        'num_of_usage_behavior' => $collect['num_of_usage_behavior'],
                        'usage_behavior' => Client::USAGE_BEHAVIOR_FOCUS
                    ]);
                    $this->incrementImported();
                });
                DB::commit();
            } catch (\Exception $ex) {
                DB::rollback();
            }
        }

        $error = false;

        return [
            'error' => $error,
            'message' => $message
        ];
    }

    private function invalidCompanyList(Collection $companyIds)
    {
        $validCompanyIds = Client::select('userid')
            ->whereIn('userid', $companyIds->toArray())
            ->pluck('userid');
        return $companyIds->diff($validCompanyIds)->toArray();
    }

    protected function failureRedirectURL()
    {
        return admin_url('clients/import_num_of_usage_behavior');
    }
}

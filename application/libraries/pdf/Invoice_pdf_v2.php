<?php

defined('BASEPATH') or exit('No direct script access allowed');

include_once(__DIR__ . '/App_pdf.php');

class Invoice_pdf_v2 extends App_pdf
{
    protected $invoice;

    private $invoice_number;

    public function __construct($invoice, $tag = '')
    {
        if (isset($invoice->rel_id) && $invoice->rel_type == 'customer') {
            $this->load_language($invoice->rel_id);
        } else if (isset($invoice->rel_id) && $invoice->rel_type == 'lead') {
            $CI = &get_instance();

            $this->load_language($invoice->rel_id);
            $CI->db->select('default_language')->where('id', $invoice->rel_id);
            $language = $CI->db->get('leads')->row()->default_language;

            load_pdf_language($language);
        }

        $invoice                = hooks()->apply_filters('invoice_html_pdf_data', $invoice);
        $GLOBALS['invoice_pdf'] = $invoice;

        parent::__construct();

        $this->tag      = $tag;
        $this->invoice = $invoice;

        $this->invoice_number = format_invoice_number_v2($this->invoice);

        $this->SetTitle($this->invoice_number);
        $this->SetDisplayMode('default', 'OneColumn');

        # Don't remove these lines - important for the PDF layout
        $this->invoice->content = $this->fix_editor_html(isset($this->invoice->content) ? $this->invoice->content : '');
    }

    public function prepare()
    {
        $number_word_lang_rel_id = 'unknown';
        
        if ( isset($this->invoice->rel_type) && $this->invoice->rel_type == 'customer') {
            $number_word_lang_rel_id = $this->invoice->rel_id;
        }
        
        $this->with_number_to_word($number_word_lang_rel_id);
        
        $total = '';
        if ($this->invoice->total != 0) {
            $total = app_format_money($this->invoice->total, get_currency($this->invoice->currency));
            $total = _l('invoice_total') . ': ' . $total;
        }
        $this->set_view_vars([
            'number'       => $this->invoice_number,
            'invoice'     => $this->invoice,
            'total'        => $total,
            'invoice_url' => site_url('invoice/' . $this->invoice->id . '/' . $this->invoice->hash),
        ]);
        
        return $this->build();
    }

    protected function type()
    {
        return 'invoice';
    }

    protected function file_path()
    {
        $customPath = APPPATH . 'views/themes/' . active_clients_theme() . '/views/my_invoicepdf.php';
        $actualPath = APPPATH . 'views/themes/' . active_clients_theme() . '/views/invoicepdf_v2.php';

        if (file_exists($customPath)) {
            $actualPath = $customPath;
        }

        return $actualPath;
    }
}

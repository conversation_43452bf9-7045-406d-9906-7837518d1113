<?php

defined('BASEPATH') or exit('No direct script access allowed');

class App_Migration extends CI_Migration
{
    public function __construct($config = [])
    {
        foreach ($config as $key => $val) {
            $this->{'_' . $key} = $val;
        }

        log_message('info', 'Migrations Class Initialized');

        // Are they trying to use migrations while it is disabled?
        if (!$this->_migration_enabled) {
            show_error('Migrations has been loaded but is disabled or set up incorrectly.');
        }

        // If not set, set it
        if (!$this->_migration_path) {
            $this->_migration_path = APPPATH . 'migrations/';
        }

        // Add trailing slash if not set
        $this->_migration_path = rtrim($this->_migration_path, '/') . '/';

        // Load migration language
        $this->lang->load('migration');

        // They'll probably be using dbforge
        $this->load->dbforge();

        // Make sure the migration table name was set.
        if (empty($this->_migration_table)) {
            show_error('Migrations configuration file (migration.php) must have "migration_table" set.');
        }

        // Migration basename regex
        $this->_migration_regex = ($this->_migration_type === 'timestamp') ? '/^\d{14}_(\w+)$/' : '/^\d{3}_(\w+)$/';

        // Make sure a valid migration numbering type was set.
        if (!in_array($this->_migration_type, array('sequential', 'timestamp'))) {
            show_error('An invalid migration numbering type was specified: ' . $this->_migration_type);
        }

        if (!$this->db->table_exists($this->_migration_table)) {
            $this->dbforge->add_field(array(
                'id' => [
                    'type' => 'INT',
                    'null' => false,
                    'auto_increment' => true,
                ],
                'version' => [
                    'type' => 'varchar',
                    'constraint' => '100',
                    'null' => false,
                ],
                'name' => [
                    'type' => 'varchar',
                    'constraint' => '255',
                    'null' => false,
                ],
                'run' => [
                    'type' => 'int',
                    'constraint' => '',
                    'null' => false,
                ],
            ));
            $this->dbforge->add_key('id', true);
            $this->dbforge->create_table($this->_migration_table, true);
        }

        // Do we auto migrate to the latest migration?
        if ($this->_migration_auto_latest === true && !$this->latest()) {
            show_error($this->error_string());
        }
    }

    /**
     * Retrieves list of available migration scripts
     *
     * @return	array	list of migration file paths sorted by version
     */
    public function find_migrations()
    {
        $db_migrations = [];
        $query = $this->db->query('SELECT * FROM ' . db_prefix() . $this->_migration_table . ' ORDER BY version ASC');
        $db_migrations = $query->num_rows() ? $query->result_array() : [];
        $tmp = [];

        foreach ($db_migrations as $db_migration) {
            $tmp[$db_migration['version']] = $db_migration;
        }
        $db_migrations = $tmp;

        $migrations = [];

        // Load all *_*.php files in the migrations path
        foreach (glob($this->_migration_path . '*_*.php') as $file) {
            $name = basename($file, '.php');

            // Filter out non-migration files
            if (preg_match($this->_migration_regex, $name)) {
                $number = $this->_get_migration_number($name);

                // There cannot be duplicate migration numbers
                if (isset($migrations[$number])) {
                    $this->_error_string = sprintf($this->lang->line('migration_multiple_version'), $number);
                    show_error($this->_error_string);
                }

                $migrations[$number] = [
                    'version' => $number,
                    'name' => $name,
                    'run' => isset($db_migrations[$number]) ? $db_migrations[$number]['run'] : 0
                ];
            }
        }

        ksort($migrations);
        return $migrations;
    }

    /**
     * Retrieves current schema version
     *
     * @return	string	Current migration version
     */
    protected function _get_version()
    {
        return $this->db->get($this->_migration_table)->order_by('id', 'asc')->result_array();
    }


    /**
     * Stores the current schema version
     *
     * @param	string	$migration	Migration reached
     * @return	void
     */
    public function _update_version($migration, $method = "up")
    {
        $version = $migration['version'];

        if ($method == "up") {
            $this->db->insert($this->_migration_table, [
                'version' => $version,
                'name' => $migration['name'],
                'run' => isset($migration['run']) && $migration['run'] ? 1 : 0,
            ]);
        } else {
            $this->db->where("version", $version)->delete($this->_migration_table);
        }
    }
}

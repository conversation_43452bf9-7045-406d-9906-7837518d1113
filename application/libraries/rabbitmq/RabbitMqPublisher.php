<?php

use PhpAmqpLib\Connection\AbstractConnection;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Exchange\AMQPExchangeType;

defined('BASEPATH') or exit('No direct script access allowed');

class RabbitMqPublisher
{
    protected AbstractConnection $connection;
    protected AMQPChannel $channel;
    protected string $exchange = RABBITMQ_CRM_EXCHANGE;
    protected $callback;

    public function __construct(AbstractConnection $connection)
    {
        $this->connection = $connection;
        $this->channel = $connection->channel();
        register_shutdown_function([$this, 'shutdown'], $this->channel, $this->connection);
    }

    /**
     * Consume message from rabbitMQ
     * @param string $queue name of the queue will be consume
     * @param array $message message
     * @return void
     */
    public function publish($queue, $message)
    {
        $this->channel->queue_declare($queue, false, true, false, false);
        $this->channel->exchange_declare($this->exchange, AMQPExchangeType::DIRECT, false, true, false);
        $this->channel->queue_bind($queue, $this->exchange);
        $amqpMessage = new AMQPMessage(json_encode($message), array('content_type' => 'text/plain', 'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT));
        $this->channel->basic_publish($amqpMessage, $this->exchange);
        $this->channel->close();
        $this->connection->close();
    }

    /**
     * Close conenction when done
     * @param AMQPChannel @channel
     * @param AbstractConnection @connection
     */
    public function shutdown(AMQPChannel $channel, AbstractConnection $connection)
    {
        // Channel might already be closed.
        // Ignoring exceptions.
        try {
            if ($channel) {
                $channel->close();
            }
            if ($channel) {
                $connection->close();
            }
        } catch (\ErrorException $e) {
        }
    }
}

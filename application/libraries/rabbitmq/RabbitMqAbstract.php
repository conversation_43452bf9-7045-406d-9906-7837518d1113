<?php

use PhpAmqpLib\Connection\AMQPConnectionFactory;
use PhpAmqpLib\Connection\AMQPConnectionConfig;
use PhpAmqpLib\Exception\AMQPRuntimeException;

defined('BASEPATH') or exit('No direct script access allowed');

abstract class RabbitMqAbstract
{
    public const RECONNECT_DELAY_SECOND = 1;
    protected $connection;
    protected $ci;

    public function __construct()
    {
        $this->ci = &get_instance();
        $config = $this->ci->load->config('rabbitmq');
        $amqpConfigs = new AMQPConnectionConfig();
        $amqpConfigs->setHost(RABBITMQ_HOST);
        $amqpConfigs->setPort(RABBITMQ_PORT);
        $amqpConfigs->setUser(RABBITMQ_USER);
        $amqpConfigs->setPassword(RABBITMQ_PWD);
        $amqpConfigs->setVhost(RABBITMQ_VHOST);

        $amqpConfigs->setInsist($config['insist']);
        $amqpConfigs->setLoginMethod($config['login_method']);
        $amqpConfigs->setConnectionTimeout($config['connection_timeout']);
        $amqpConfigs->setReadTimeout($config['read_write_timeout']);
        $amqpConfigs->setWriteTimeout($config['read_write_timeout']);
        $amqpConfigs->setKeepalive($config['keepalive']);
        $amqpConfigs->setHeartbeat($config['heartbeat']);

        if (RABBITMQ_IS_SECURE) {
            $amqpConfigs->setIsSecure(true);
            $amqpConfigs->setSslCaCert(RABBITMQ_SSL_CAFILE);
            $amqpConfigs->setSslCert(RABBITMQ_SSL_LOCALCERT);
            $amqpConfigs->setSslKey(RABBITMQ_SSL_LOCALKEY);
            $amqpConfigs->setSslCiphers(RABBITMQ_SSL_VERIFY_PEER);
            $amqpConfigs->setSslPassPhrase(RABBITMQ_SSL_PASSPHRASE);
        } else {
            $amqpConfigs->setStreamContext($config['context']);
        }
        $this->connection =  AMQPConnectionFactory::create($amqpConfigs);
        register_shutdown_function([$this, 'shutdown'], $this->connection);
    }

    abstract public function handleConnection();
    abstract public function postMessage($message);

    /**
     * Watch message from RabbitMQ and delegate to correct handle
     * @return void
     * @throws Exception
     */
    public function watch()
    {
        $watching = defined('RABBITMQ_WATCHING') && true === RABBITMQ_WATCHING;
        do {
            try {
                call_user_func([$this, 'handleConnection']);
            } catch (AMQPRuntimeException $e) {
                echo $e->getMessage() . PHP_EOL;
                $this->shutdown($this->connection);
                sleep(self::RECONNECT_DELAY_SECOND);
            } catch (\RuntimeException $e) {
                echo 'Runtime exception ' . $e->getMessage() .  PHP_EOL;
                $this->shutdown($this->connection);
                sleep(self::RECONNECT_DELAY_SECOND);
            } catch (\ErrorException $e) {
                echo 'Error exception ' . $e->getMessage() .  PHP_EOL;
                $this->shutdown($this->connection);
                sleep(self::RECONNECT_DELAY_SECOND);
            } catch (\Exception $e) {
                echo 'Other exception ' . $e->getMessage() .  PHP_EOL;
                $this->shutdown($this->connection);
                sleep(self::RECONNECT_DELAY_SECOND);
            }
        } while ($watching);
    }

    /**
     * Close conenction when done
     * @param \PhpAmqpLib\Connection\AbstractConnection @connection
     */
    public function shutdown($connection)
    {
        // Connection might already be closed.
        // Ignoring exceptions.
        try {
            if ($connection) {
                $connection->close();
            }
        } catch (\ErrorException $e) {
        }
    }
}

<?php

use PhpAmqpLib\Connection\AbstractConnection;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;

defined('BASEPATH') or exit('No direct script access allowed');

class RabbitMqReceive
{
    protected AbstractConnection $connection;
    protected AMQPChannel $channel;
    protected $callback;

    public function __construct(AbstractConnection $connection, $callback)
    {
        $this->connection = $connection;
        $this->channel = $connection->channel();

        if (!is_callable($callback)) {
            throw new InvalidArgumentException(sprintf(
                'Given argument "%s" should be callable. %s type was given.',
                $callback,
                gettype($callback)
            ));
        }
        $this->callback = $callback;
        register_shutdown_function([$this, 'shutdown'], $this->channel);
    }

    /**
     * Consume message from rabbitMQ
     * @param string $queue name of the queue will be consume
     * @param string $consumerTag name of the consumer
     * @return void
     */
    public function consume($queue, $consumerTag = 'crm')
    {
        $this->channel->queue_declare($queue, false, true, false, false);
        $this->channel->basic_consume($queue, $consumerTag, false, false, false, false, [$this, 'postMessage']);
        // Waiting until message ark or nack by callback function
        // it will close connection based on RABBITMQ_WATCHING configuration
        $this->channel->consume();
    }

    /**
     * Handle consume message
     * @param AMQPMessage $message
     */
    public function postMessage(AMQPMessage $message)
    {
        try {
            call_user_func($this->callback, $message->body);
            $message->ack();
        } catch (Exception $ex) {
            $message->nack();
            throw $ex;
        }
        // End queue consumer
        if (!defined('RABBITMQ_WATCHING') || false === RABBITMQ_WATCHING) {
            $message->getChannel()->basic_cancel($message->getConsumerTag());
        }
    }

    /**
     * Close conenction when done
     * @param AMQPChannel @channel
     */
    public function shutdown(AMQPChannel $channel)
    {
        // Channel might already be closed.
        // Ignoring exceptions.
        try {
            if ($channel) {
                $channel->close();
            }
        } catch (\ErrorException $e) {
        }
    }
}

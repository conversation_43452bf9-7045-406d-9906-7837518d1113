<?php

defined('BASEPATH') or exit('No direct script access allowed');
require_once(APPPATH . 'libraries/rabbitmq/RabbitMqAbstract.php');
require_once(APPPATH . 'libraries/rabbitmq/RabbitMqReceive.php');

class RabbitMqDbChange extends RabbitMqAbstract
{
    private $queue;
    private $consumeTag = null;

    public function __construct()
    {
        parent::__construct();
        $this->queue = RABBITMQ_DB_CHANGE_QUEUE;
    }

    /**
     * @inheritdoc
     * Handle connection for db changes
     * @return void
     */
    public function handleConnection()
    {
        echo "[*] Waiting message for \"$this->queue\". To exit press CTRL+C\n";
        $receive = new RabbitMqReceive($this->connection, [$this, 'postMessage']);
        $receive->consume($this->queue, $this->consumeTag);
    }

    /**
     * @inheritdoc
     * Handle post message of the RabbitMQ
     * @param string $body message that response from RabbitMQ
     * @return void
     */
    public function postMessage($body)
    {
        try {
            $messageBody = json_decode($body, true);
            $esUpdateLib = $this->ci->load->library('elasticsearch/UpdateEs');
            $esUpdateLib->updatees->execute($messageBody);
        } catch (\Exception $ex) {
            log_message('error', 'Handle post message error with message: ' . $ex->getMessage() . PHP_EOL . ', Stack trace: ' . $ex->getTraceAsString());
            throw $ex;
        }
    }
}

<?php

use Entities\Item;

defined('BASEPATH') or exit('No direct script access allowed');

include_once(APPPATH . 'libraries/App_items_table_template.php');

class App_items_table_invoice extends App_items_table_template
{
    public function __construct($transaction, $type, $for = 'html', $admin_preview = false)
    {
        // Required
        $this->type          = strtolower($type);
        $this->admin_preview = $admin_preview;
        $this->for           = $for;

        $this->set_transaction($transaction);
        $this->set_items($transaction->items);

        parent::__construct();
    }

    /**
     * Builds the actual table items rows preview
     * @return string
     */
    public function items()
    {
        $html = '';
        $i = 1;

        $get_with_col_order = $this->get_with_col_order();
        $itemIds = collect($this->items)->pluck('item_id');
        $jobPostingItemIds = $itemIds->count() ? Item::whereIn('id', $itemIds)->isNotCredit()->pluck('id') : collect();

        foreach ($this->items as $item) {
            $itemHTML = '';

            // Open table row
            $itemHTML .= '<tr style="background-color:#0808080f;"' . '>';

            // Table data number
            $itemHTML .= '<td' . $this->td_attributes() . ' align="center" width="' . $get_with_col_order[0] . '%">' . $i . '</td>';

            /**
             * Item description
             */
            $itemHTML .= '<td style="text-align:center;" class="description" align="center;" width="' . $get_with_col_order[1] . '%">';
            if (!empty($item['description'])) {
                $itemHTML .= '<span style="font-size:' . $this->get_pdf_font_size() . 'px;"><strong>'
                    . ($item['description'])
                    . '</strong></span>';
            }

            $itemHTML .= '</td>';

            /**
             * Item Unit
             */
            $itemUnitText = $jobPostingItemIds->contains($item['item_id']) ? 'tin<br>post' : 'gói<br>package';
            $itemHTML .= '<td align="center" width="' . $get_with_col_order[2] . '%">' . $itemUnitText;
            $itemHTML .= '</td>';

            /**
             * Item quantity
             */
            $itemHTML .= '<td align="center" width="' . $get_with_col_order[3] . '%">' . floatVal($item['qty']);

            $itemHTML .= '</td>';

            /**
             * Item rate
             * @var string
             */
            $rate = hooks()->apply_filters(
                'item_preview_rate',
                app_format_money($item['rate'], $this->transaction->currency_name, $this->exclude_currency()),
                ['item' => $item, 'transaction' => $this->transaction]
            );

            $itemHTML .= '<td align="right" width="' . $get_with_col_order[4] . '%">' . $rate . ' đ</td>';
            $discount_table = $item['discount_table'] ?? 0;
            $itemHTML .= '<td align="right" width="' . $get_with_col_order[5] . '%">' . ($discount_table * 100) . '%</td>';

            $amount = $item['qty'] * $item['rate'];
            $amount -= $amount * $discount_table;
            $item_amount_with_quantity = hooks()->apply_filters(
                'item_preview_amount_with_currency',
                app_format_money($amount, $this->transaction->currency_name, $this->exclude_currency()),
                $item,
                $this->transaction,
                $this->exclude_currency()
            );

            $itemHTML .= '<td class="amount" align="right" width="' . $get_with_col_order[6] . '%">' . $item_amount_with_quantity . ' đ</td>';

            // Close table row
            $itemHTML .= '</tr>';

            $html .= $itemHTML;

            $i++;
        }

        //add discount html
        $items_discount_html = $this->get_row_discount();

        //add taxes html
        $taxes_html = $this->get_row_taxes();

        //add total price html
        $total_price_html = $this->get_row_price_total();

        return $html . $items_discount_html . $taxes_html . $total_price_html;
    }

    /**
     * Html headings preview
     * @return string
     */
    public function html_headings()
    {
    }

    /**
     * Get quantity heading
     * @return string
     */
    public function unit_price()
    {
        return $this->headings['unit_price'];
    }

    /**
     * Get quantity heading
     * @return string
     */
    public function discount_table()
    {
        return $this->headings['discount_table'];
    }

    /**
     * Get quantity heading
     * @return string
     */
    public function unit()
    {
        return $this->headings['unit'];
    }

    /**
     * Get quantity heading
     * @return string
     */
    public function total_price()
    {
        return $this->headings['total_price'];
    }


    /**
     * PDF headings preview
     * @return string
     */
    public function pdf_headings()
    {
        $get_with_col_order = $this->get_with_col_order();

        $tblhtml = '<tr>
            <th width="100%" style="border-top:1px solid #000000;border-bottom:1px solid #000000;"></th>
        </tr>';

        $tblhtml .= '<tr style="color:#000000;">';

        $tblhtml .= '<th width="' . $get_with_col_order[0] . '%;" align="center" style="color:#d34127;">' . $this->number_heading() . '</th>';
        $tblhtml .= '<th width="' . $get_with_col_order[1] . '%" align="center" style="color:#d34127;">' . $this->item_heading() . '</th>';

        $tblhtml .= '<th width="' . $get_with_col_order[2] . '%" align="center" style="color:#d34127;">' . $this->unit() . '</th>';

        $tblhtml .= '<th width="' . $get_with_col_order[3] . '%" align="center" style="color:#d34127;">' . $this->qty_heading() . '</th>';
        $tblhtml .= '<th width="' . $get_with_col_order[4] . '%" align="right" style="color:#d34127;">' . $this->unit_price() . '</th>';
        $tblhtml .= '<th width="' . $get_with_col_order[5] . '%" align="right" style="color:#d34127;">' . $this->discount_table() . '</th>';

        $tblhtml .= '<th width="' . $get_with_col_order[6] . '%" align="right" style="color:#d34127;">' . $this->total_price() . '</th>';
        $tblhtml .= '</tr>';

        return $tblhtml;
    }

    /**
     * Set headings for the items
     * Can be used outside this class for example when alias is needed to take the language texts for
     * @param string $alias e.q. estimates and proposals are using the same language text
     */
    public function set_headings($alias = '')
    {
        $this->headings['number'] = '<b>STT<br>No.</b>';
        $this->headings['item']   = '<b>Dịch vụ<br>Type of service</b>';
        $this->headings['unit']    = '<b>Đơn vị tính<br>Unit</b>';
        $this->headings['qty']   = '<b>Số lượng<br>Quantity</b>';
        $this->headings['discount_table'] = '<b>Giảm giá<br>Discount</b>';
        $this->headings['unit_price'] = '<b>Đơn giá<br>Unit price</b>';
        $this->headings['total_price'] = '<b>Tổng giá<br>Total price</b>';

        return $this;
    }

    public function get_row_features_des_table()
    {
        $features_html = '';
        foreach ($this->items as $item) {
            if (!empty($item['long_description'])) {
                $features_html .= '<tr style="background-color:#0808080f;">
                    <td style="border-top:1px solid #000000;border-bottom:1px solid #000000;" align="left" width="100%"><strong>Quyền lợi gói ' . $item['description'] . ' trên TopDev.vn / 
                    Features of '. $item['description'] .' Package on TopDev.vn
                    </strong>' . '</td>
                </tr>';

                $features_html .= '<tr><td align="left" width="100%">' . '<span style="">' . $this->period_merge_field($item['long_description']) . '</span>' . '</td></tr>';
            }
        }

        return $features_html;
    }

    /**
     * Check for period merge field for recurring invoices
     *
     * @return string
     */
    protected function period_merge_field($text)
    {
        if ($this->type != 'invoice') {
            return $text;
        }

        // Is subscription invoice
        if (!property_exists($this->transaction, 'recurring_type')) {
            return $text;
        }

        $startDate       = $this->transaction->date;
        $originalInvoice = $this->transaction->is_recurring_from ?
            $this->ci->invoices_model->get($this->transaction->is_recurring_from) :
            $this->transaction;

        if (!preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $startDate)) {
            $startDate = to_sql_date($startDate);
        }

        if ($originalInvoice->custom_recurring == 0) {
            $originalInvoice->recurring_type = 'month';
        }

        $nextDate = date('Y-m-d', strtotime(
            '+' . $originalInvoice->recurring . ' ' . strtoupper($originalInvoice->recurring_type),
            strtotime($startDate)
        ));

        return str_ireplace('{period}', _d($startDate) . ' - ' . _d(date('Y-m-d', strtotime('-1 day', strtotime($nextDate)))), $text);
    }

    //width in order from left to right
    public function get_with_col_order()
    {
        return [
            6,
            30,
            10,
            10,
            15,
            14,
            15,
        ];
    }

    public function get_row_discount()
    {
        $items_discount_html = '';
        if (is_sale_discount_applied($this->transaction)) {
            $discount_total = $this->transaction->discount_total;
            $discount_fixed_total =  app_format_money($this->transaction->discount_fixed, $this->transaction->currency_name, $this->exclude_currency());
            $discount_percent_total = app_format_money($discount_total - $this->transaction->discount_fixed, $this->transaction->currency_name, $this->exclude_currency());

            $get_with_col_order = $this->get_with_col_order();
            $discount_percent = (is_sale_discount($this->transaction, 'percent')) ? (app_format_number($this->transaction->discount_percent, true) . '%') : '';
            $items_discount_html .=
            ($discount_percent_total ? '<tr>
                <td align="center" width="' . $get_with_col_order[0] . '%">' . '' . '</td>
                <td align="center" width="' . $get_with_col_order[1] . '%">' . '' . '</td>
                <td align="center" width="' . $get_with_col_order[2] . '%">' . '' . '</td>
                <td align="center" width="' . $get_with_col_order[3] . '%">' . '' . '</td>
                <td align="right" style="font-size:' . $this->get_pdf_font_size() . 'px;"  width="' . ($get_with_col_order[4]  + $get_with_col_order[5]) . '%">Chiết khấu thêm ' . $discount_percent . '<br>Addition Discount ' . $discount_percent . '</td>
                <td align="right" style="font-size:' . $this->get_pdf_font_size() . 'px;" width="' . $get_with_col_order[6] . '%">' . '<strong>-' . $discount_percent_total . ' ' . 'đ</strong>' . '</td>
            </tr>': '') .
            ($discount_fixed_total ? '<tr>
                <td align="center" width="' . $get_with_col_order[0] . '%">' . '' . '</td>
                <td align="center" width="' . $get_with_col_order[1] . '%">' . '' . '</td>
                <td align="center" width="' . $get_with_col_order[2] . '%">' . '' . '</td>
                <td align="center" width="' . $get_with_col_order[3] . '%">' . '' . '</td>
                <td align="right" style="font-size:' . $this->get_pdf_font_size() . 'px;"  width="' . ($get_with_col_order[4]  + $get_with_col_order[5]) . '%">Phiếu giảm giá<br>Voucher</td>
                <td align="right" style="font-size:' . $this->get_pdf_font_size() . 'px;" width="' . $get_with_col_order[6] . '%">' . '<strong>-' . $discount_fixed_total . ' ' . 'đ</strong>' . '</td>
            </tr>' : '') .
            '<tr>
                <td align="center" width="' . $get_with_col_order[0] . '%">' . '' . '</td>
                <td align="center" width="' . $get_with_col_order[1] . '%">' . '' . '</td>
                <td align="center" width="' . $get_with_col_order[2] . '%">' . '' . '</td>
                <td align="right" style="font-size:' . $this->get_pdf_font_size() . 'px;"  width="' . ($get_with_col_order[3] + $get_with_col_order[4] + $get_with_col_order[5]) . '%">Tổng đơn hàng sau chiết khấu<br>Subtotal</td>
                <td align="right" style="font-size:' . $this->get_pdf_font_size() . 'px;" width="' . $get_with_col_order[6] . '%">' . '<strong>' . app_format_money($this->transaction->subtotal - $discount_total, $this->transaction->currency_name, $this->exclude_currency()) . ' ' . 'đ</strong>' . '</td>
            </tr>
            ';
        }

        return $items_discount_html;
    }

    public function get_row_taxes()
    {
        $get_with_col_order = $this->get_with_col_order();

        $htmlTaxes = '';
        foreach ($this->taxes() as $tax) {
            $htmlTaxes .= '<tr>
                <td align="center" width="' . $get_with_col_order[0] . '%">' . '' . '</td>
                <td align="center" width="' . $get_with_col_order[1] . '%">' . '' . '</td>
                <td align="center" width="' . $get_with_col_order[2] . '%">' . '' . '</td>
                <td align="right" style="font-size:' . $this->get_pdf_font_size() . 'px;"  width="' . ($get_with_col_order[3] + $get_with_col_order[4] + $get_with_col_order[5]) . '%">' . $tax['taxname'] . ' (' . app_format_number($tax['taxrate']) . '%)' . '</td>
                <td align="right" style="font-size:' . $this->get_pdf_font_size() . 'px;" width="' . $get_with_col_order[6] . '%">' . '<strong>' . app_format_money(number_format($tax['total_tax']), $this->transaction->currency_name, $this->exclude_currency()) . ' ' . 'đ</strong>' . '</td>
            </tr>';
        }

        return $htmlTaxes;
    }

    public function get_row_price_total()
    {
        $get_with_col_order = $this->get_with_col_order();

        $vat = '';
        $taxes = $this->taxes();
        foreach ($taxes as $value) {
            if ($value['total_tax'] != 0 && count($taxes) < 2) {
                $vat = app_format_number($value['taxrate']) . '%';
            }
        }

        $priceTotal = app_format_money(number_format($this->transaction->total), $this->transaction->currency_name, $this->exclude_currency()) . ' đ';
        $totalPriceHtml = '
        <tr>
            <td align="center" width="' . $get_with_col_order[0] . '%">' . '' . '</td>
            <td align="center" width="' . $get_with_col_order[1] . '%">' . '' . '</td> 
            <td align="center" width="' . $get_with_col_order[2] . '%">' . '' . '</td>
            <td align="center" width="' . $get_with_col_order[3] . '%">' . '' . '</td>
            <td colspan="2" align="right" style="font-size:' . $this->get_pdf_font_size() . 'px;" width="' . ($get_with_col_order[4] + $get_with_col_order[5] + $get_with_col_order[6]) . '%"><strong>Tổng đơn hàng sau thuế VAT ' . $vat . '<br>Total price after VAT ' . $vat . '</strong></td>
        </tr>
        <tr>
            <td align="center" width="' . $get_with_col_order[0] . '%">' . '' . '</td>
            <td align="center" width="' . $get_with_col_order[1] . '%">' . '' . '</td>
            <td align="center" width="' . $get_with_col_order[2] . '%">' . '' . '</td>
            <td align="center" width="' . $get_with_col_order[3] . '%">' . '' . '</td>
            <td colspan="2" align="right" style="font-size:' . $this->get_pdf_font_size() . 'px;" style="color:#d34127;" width="' . ($get_with_col_order[4] + $get_with_col_order[5] + $get_with_col_order[6]) . '%"><h2>' . $priceTotal . '</h2></td>
        </tr>';

        $priceTotalInWords = '<tr>
            <td align="left" width="100%">' .
                'Bằng chữ: ' . ucfirst(format_number_to_words(str_replace(',', '', $priceTotal), 'vietnamese', 'đồng')) . '.<br>' .
                'In words: ' . ucfirst(format_number_to_words(str_replace(',', '', $priceTotal), 'english', 'VND')) . 
            '.</td>
        </tr>';

        return $totalPriceHtml . $priceTotalInWords;
    }
}

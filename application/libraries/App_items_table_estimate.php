<?php

defined('BASEPATH') or exit('No direct script access allowed');

include_once(APPPATH . 'libraries/App_items_table_invoice.php');

class App_items_table_estimate extends App_items_table_invoice
{
    protected $ordinalNumber;

    public function __construct($data, $type, $for = 'html', $admin_preview = false)
    {
        ['ordinal_number' => $this->ordinalNumber, 'transaction' => $transaction] = $data;

        parent::__construct($transaction, $type, $for, $admin_preview);
    }

    public function pdf_headings()
    {
        $get_with_col_order = $this->get_with_col_order();

        $tblhtml = '<tr>
            <th width="100%" style="border-bottom:1px solid #000000;font-size: ' . ($this->get_pdf_font_size() + 4) . ' px;"><b>Option ' . $this->ordinalNumber . ': ' . getEstimateOptionName($this->items) . '</b></th>
        </tr>';

        $tblhtml .= '<tr style="color:#000000;">';

        $tblhtml .= '<th width="' . $get_with_col_order[0] . '%;" align="center" style="color:#d34127;">' . $this->number_heading() . '</th>';
        $tblhtml .= '<th width="' . $get_with_col_order[1] . '%" align="center" style="color:#d34127;">' . $this->item_heading() . '</th>';

        $tblhtml .= '<th width="' . $get_with_col_order[2] . '%" align="center" style="color:#d34127;">' . $this->unit() . '</th>';

        $tblhtml .= '<th width="' . $get_with_col_order[3] . '%" align="center" style="color:#d34127;">' . $this->qty_heading() . '</th>';
        $tblhtml .= '<th width="' . $get_with_col_order[4] . '%" align="right" style="color:#d34127;">' . $this->unit_price() . '</th>';
        $tblhtml .= '<th width="' . $get_with_col_order[5] . '%" align="right" style="color:#d34127;">' . $this->discount_table() . '</th>';

        $tblhtml .= '<th width="' . $get_with_col_order[6] . '%" align="right" style="color:#d34127;">' . $this->total_price() . '</th>';
        $tblhtml .= '</tr>';

        return $tblhtml;
    }
}

<?php

namespace Dto;

abstract class BaseDTO
{
    public function toArray(): array
    {
        $result = [];
        $reflectionClass = new \ReflectionClass($this);

        foreach ($reflectionClass->getProperties() as $property) {
            $property->setAccessible(true); // Allow access to private/protected properties
            // Skip uninitialized , excluded properties
            if (!$this->isPropertyInitialized($property) || in_array($property->getName(), $this->getExclusions(), true)) {
                continue;
            }

            $value = $property->getValue($this);

            // If the property value is another DTO, call its toArray method
            if ($value instanceof self) {
                $result[$property->getName()] = $value->toArray();
            } elseif (is_array($value)) {
                // Handle arrays of DTOs
                $result[$property->getName()] = array_map(function ($item) {
                    return $item instanceof self ? $item->toArray() : $item;
                }, $value);
            } else {
                $result[$property->getName()] = $value;
            }
        }

        return $result;
    }

    /**
     * Get the list of properties to exclude from the toArray output.
     * Can be overridden in child classes.
     */
    protected function getExclusions(): array
    {
        return [];
    }

    /**
     * Check if a property has been initialized (set to non-null).
     * You can also add additional logic to exclude properties like `null` from initialization checks.
     */
    protected function isPropertyInitialized(\ReflectionProperty $property): bool
    {
        // You can check if the property is set or not (including null values)
        return isset($this->{$property->getName()});
    }
}

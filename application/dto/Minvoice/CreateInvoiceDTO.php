<?php

namespace Dto\Minvoice;

use Dto\BaseDTO;

defined('BASEPATH') or exit('No direct script access allowed');

class CreateInvoiceDTO extends BaseDTO
{
    protected string $khieu; // "string",
    protected string $dvtte; // "string",
    protected string $ten; // "string",
    protected string $mst; // "string",
    protected string $dchi; // "string",
    protected float $tgtttbso; // 0,
    protected float $ttcktmai; // 0,
    protected float $tgtthue; // 0,
    protected float $tgtcthue; // 0,
    protected CreateInvoiceDetailDTO $details;
    protected ?float $tgia = 0; // 0,
    protected ?string $sdhang = null; // {},
    protected ?string $htttoan = null; // "string",
    protected ?string $tnmua = null; // "string",
    protected ?string $mnmua = null; // "string",
    protected ?string $email = null; // "string",
    protected ?string $stknmua = null; // "string",
    protected ?string $nganhang_ngmua = null; // "string",
    protected ?string $tgtttbchu = null; // "string",
    protected ?float $tlptdoanhthu20 = null; // 0,
    protected ?float $tgtck20 = null; // 0,
    protected ?string $txtck20 = null; // "string",

    /**
     * CreateInvoiceDTO constructor
     *
     * @param string $khieu
     * @param string $dvtte
     * @param string $ten
     * @param string $mst
     * @param string $dchi
     * @param float $tgtttbso
     * @param float $ttcktmai
     * @param float $tgtthue
     * @param float $tgtcthue
     * @param CreateInvoiceDetailDTO $details
     * @param float|null $tgia
     * @param string|null $sdhang
     * @param string|null $htttoan
     * @param string|null $tnmua
     * @param string|null $mnmua
     * @param string|null $email
     * @param string|null $stknmua
     * @param string|null $nganhang_ngmua
     * @param string|null $tgtttbchu
     * @param float|null $tlptdoanhthu20
     * @param float|null $tgtck20
     * @param string|null $txtck20
     */
    public function __construct(
        string $khieu,
        string $dvtte,
        string $ten,
        string $mst,
        string $dchi,
        float $tgtttbso,
        float $ttcktmai,
        float $tgtthue,
        float $tgtcthue,
        CreateInvoiceDetailDTO $details,
        ?float $tgia = 0,
        ?string $sdhang = null,
        ?string $htttoan = null,
        ?string $tnmua = null,
        ?string $mnmua = null,
        ?string $email = null,
        ?string $stknmua = null,
        ?string $nganhang_ngmua = null,
        ?string $tgtttbchu = null,
        ?float $tlptdoanhthu20 = null,
        ?float $tgtck20 = null,
        ?string $txtck20 = null
    ) {
        $this->khieu = $khieu;
        $this->dvtte = $dvtte;
        $this->ten = $ten;
        $this->mst = $mst;
        $this->dchi = $dchi;
        $this->tgtttbso = $tgtttbso;
        $this->ttcktmai = $ttcktmai;
        $this->tgtthue = $tgtthue;
        $this->tgtcthue = $tgtcthue;
        $this->details = $details;
        $this->tgia = $tgia;
        $this->sdhang = $sdhang;
        $this->htttoan = $htttoan;
        $this->tnmua = $tnmua;
        $this->mnmua = $mnmua;
        $this->email = $email;
        $this->stknmua = $stknmua;
        $this->nganhang_ngmua = $nganhang_ngmua;
        $this->tgtttbchu = $tgtttbchu;
        $this->tlptdoanhthu20 = $tlptdoanhthu20;
        $this->tgtck20 = $tgtck20;
        $this->txtck20 = $txtck20;
    }
}

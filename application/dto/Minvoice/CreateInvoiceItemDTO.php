<?php

namespace Dto\Minvoice;

use Dto\BaseDTO;
use Entities\MInvoice;

defined('BASEPATH') or exit('No direct script access allowed');

class CreateInvoiceItemDTO extends BaseDTO
{
    protected string $stt; // "string",
    protected string $ten; // "string",
    protected int $sluong; // 0,
    protected float $dgia; // 0,
    protected float $tlckhau; // 0,
    protected float $stckhau; // 0,
    protected float $thtien; // 0,
    protected float $tsuat; // 0,
    protected float $tthue; // 0,
    protected float $tgtien; // 0
    protected int $tchat; // 0,
    protected ?string $ma; // "string",
    protected ?string $dvtinh; // "string",

    public function __construct(
        string $stt,
        string $ten,
        int $sluong,
        float $dgia,
        float $tlckhau,
        float $stckhau,
        float $thtien,
        float $tsuat,
        float $tthue,
        float $tgtien,
        int $tchat = MInvoice::PRODUCT,
        ?string $ma = null,
        ?string $dvtinh = null
    ) {
        $this->stt  = $stt;
        $this->ten  = $ten;
        $this->sluong  = $sluong;
        $this->dgia  = $dgia;
        $this->tlckhau  = $tlckhau;
        $this->stckhau  = $stckhau;
        $this->thtien  = $thtien;
        $this->tsuat  = $tsuat;
        $this->tthue  = $tthue;
        $this->tgtien  = $tgtien;
        $this->tchat = $tchat;
        $this->ma = $ma;
        $this->dvtinh = $dvtinh;
    }
}

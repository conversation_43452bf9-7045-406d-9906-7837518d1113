<?php

namespace Dto\Minvoice;

use Dto\BaseDTO;

defined('BASEPATH') or exit('No direct script access allowed');

class ReplaceSignedInvoiceDTO extends CreateInvoiceTT32DTO
{
    protected string $inv_invoiceIssuedDate;
    protected string $inv_invoiceSeries;
    protected array $data;

    /**
     * DeleteInvoiceDTO constructor
     *
     * @param string $inv_invoiceIssuedDate
     * @param string $inv_invoiceSeries
     */
    public function __construct(
        string $inv_invoiceIssuedDate,
        string $inv_invoiceSeries,
        string $inv_currencyCode,
        string $inv_buyerLegalName,
        string $inv_buyerTaxCode,
        string $inv_buyerAddressLine,
        float $inv_TotalAmount,
        float $inv_discountAmount,
        CreateInvoiceDetailTT32DTO $details,
        ?float $inv_vatAmount = null,
        ?float $inv_TotalAmountWithoutVat = null,
        ?string $e = null,
        ?string $inv_exchangeRate = null,
        ?string $inv_paymentMethodName = null,
        ?string $inv_buyerDisplayName = null,
        ?string $ma_dt = null,
        ?string $inv_buyerEmail = null,
        ?string $inv_buyerBankAccount = null,
        ?string $inv_buyerBankName = null,
        ?string $amount_to_word = null,
        ?float $tlptdoanhthu20 = null,
        ?float $tgtck20 = null,
        ?string $txtck20 = null,
        ?string $nonTaxZone = null,
        ?string $isDeductionNQ43 = null
    ) {
        $this->inv_invoiceIssuedDate = $inv_invoiceIssuedDate;
        $this->inv_invoiceSeries = $inv_invoiceSeries;

        parent::__construct(
            $inv_invoiceSeries,
            $inv_currencyCode,
            $inv_buyerLegalName,
            $inv_buyerTaxCode,
            $inv_buyerAddressLine,
            $inv_TotalAmount,
            $inv_discountAmount,
            $details,
            $e,
            $inv_exchangeRate,
            $inv_paymentMethodName,
            $inv_buyerDisplayName,
            $ma_dt,
            $inv_buyerEmail,
            $inv_buyerBankAccount,
            $inv_buyerBankName,
            $amount_to_word,
            $inv_vatAmount,
            $inv_TotalAmountWithoutVat,
            $tlptdoanhthu20,
            $tgtck20,
            $txtck20,
            $nonTaxZone,
            $isDeductionNQ43,
        );
    }
}

<?php

namespace Dto\Minvoice;

use Dto\BaseDTO;

defined('BASEPATH') or exit('No direct script access allowed');

class AdjustSignedInvoiceDTO extends BaseDTO
{
    protected string $inv_invoiceIssuedDate;
    protected string $inv_invoiceSeries;
    protected array $data;

    /**
     * DeleteInvoiceDTO constructor
     *
     * @param string $inv_invoiceIssuedDate
     * @param string $inv_invoiceSeries
     * @param AdjustSignedInvoiceItemDTO[] $data
     */
    public function __construct(
        string $inv_invoiceIssuedDate,
        string $inv_invoiceSeries,
        array $data
    ) {
        $this->inv_invoiceIssuedDate = $inv_invoiceIssuedDate;
        $this->inv_invoiceSeries = $inv_invoiceSeries;
        $this->data = $data;
    }
}

<?php

namespace Dto\Minvoice;

use Dto\BaseDTO;

defined('BASEPATH') or exit('No direct script access allowed');

class DeleteInvoiceDTO extends BaseDTO
{
    protected string $inv_invoiceSeries;
    protected ?float $inv_invoiceNumber;
    protected ?string $inv_invoiceAuth_Id;

    /**
     * DeleteInvoiceDTO constructor
     *
     * @param string $shdon
     * @param ?float $inv_invoiceNumber
     * @param ?string $inv_invoiceAuth_Id
     */
    public function __construct(
        string $inv_invoiceSeries,
        ?float $inv_invoiceNumber,
        ?string $inv_invoiceAuth_Id
    ) {
        $this->inv_invoiceSeries = $inv_invoiceSeries;
        $this->inv_invoiceNumber = $inv_invoiceNumber;
        $this->inv_invoiceAuth_Id = $inv_invoiceAuth_Id;
    }
}

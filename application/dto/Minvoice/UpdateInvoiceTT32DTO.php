<?php

namespace Dto\Minvoice;

use Entities\MInvoice;

defined('BASEPATH') or exit('No direct script access allowed');

class UpdateInvoiceTT32DTO extends CreateInvoiceTT32DTO
{
    protected string $inv_invoiceSeries;
    protected string $inv_currencyCode;
    protected string $inv_buyerLegalName;
    protected string $inv_buyerTaxCode;
    protected string $inv_buyerAddressLine;
    protected float $inv_TotalAmount;
    protected float $inv_discountAmount;
    protected array $details;
    protected ?float $inv_vatAmount     = null;
    protected ?float $inv_TotalAmountWithoutVat     = null;
    protected ?string $e = null;
    protected ?string $inv_exchangeRate     = null;
    protected ?string $inv_paymentMethodName     = null;
    protected ?string $inv_buyerDisplayName     = null;
    protected ?string $ma_dt     = null;
    protected ?string $inv_buyerEmail     = null;
    protected ?string $inv_buyerBankAccount     = null;
    protected ?string $inv_buyerBankName     = null;
    protected ?string $amount_to_word     = null;
    protected ?float $tlptdoanhthu20     = null;
    protected ?float $tgtck20     = null;
    protected ?string $txtck20     = null;
    protected ?string $nonTaxZone     = null;
    protected ?bool $isDeductionNQ43     = null;
    protected ?float $inv_invoiceNumber     = null;
    protected ?string $inv_invoiceIssuedDate     = null;

    /**
     * Summary of __construct
     * @param string $inv_invoiceSeries
     * @param string $inv_currencyCode
     * @param string $inv_buyerLegalName
     * @param string $inv_buyerTaxCode
     * @param string $inv_buyerAddressLine
     * @param float $inv_TotalAmount
     * @param float $inv_discountAmount
     * @param CreateInvoiceDetailTT32DTO $details
     * @param mixed $e
     * @param mixed $inv_exchangeRate
     * @param mixed $inv_paymentMethodName
     * @param mixed $inv_buyerDisplayName
     * @param mixed $ma_dt
     * @param mixed $inv_buyerEmail
     * @param mixed $inv_buyerBankAccount
     * @param mixed $inv_buyerBankName
     * @param mixed $amount_to_word
     * @param float $inv_vatAmount
     * @param float $inv_TotalAmountWithoutVat
     * @param float $tlptdoanhthu20
     * @param float $tgtck20
     * @param mixed $txtck20
     * @param mixed $nonTaxZone
     * @param mixed $isDeductionNQ43
     * @param float $inv_invoiceNumber
     * @param ?string $inv_invoiceIssuedDate
     */
    public function __construct(
        string $inv_invoiceSeries,
        string $inv_currencyCode,
        string $inv_buyerLegalName,
        string $inv_buyerTaxCode,
        string $inv_buyerAddressLine,
        float $inv_TotalAmount,
        float $inv_discountAmount,
        CreateInvoiceDetailTT32DTO $details,
        ?float $inv_vatAmount = null,
        ?float $inv_TotalAmountWithoutVat = null,
        ?string $e = null,
        ?string $inv_exchangeRate = null,
        ?string $inv_paymentMethodName = MInvoice::PAYMENT_METHOD_NAME_TMCK,
        ?string $inv_buyerDisplayName = null,
        ?string $ma_dt = null,
        ?string $inv_buyerEmail = null,
        ?string $inv_buyerBankAccount = null,
        ?string $inv_buyerBankName = null,
        ?string $amount_to_word = null,
        ?float $tlptdoanhthu20 = null,
        ?float $tgtck20 = null,
        ?string $txtck20 = null,
        ?string $nonTaxZone = null,
        ?string $isDeductionNQ43 = null,
        ?float $inv_invoiceNumber = null,
        ?string $inv_invoiceIssuedDate = null
    ) {
        $this->inv_invoiceNumber = $inv_invoiceNumber;
        $this->inv_invoiceIssuedDate = $inv_invoiceIssuedDate;

        parent::__construct(
            $inv_invoiceSeries,
            $inv_currencyCode,
            $inv_buyerLegalName,
            $inv_buyerTaxCode,
            $inv_buyerAddressLine,
            $inv_TotalAmount,
            $inv_discountAmount,
            $details,
            $inv_vatAmount,
            $inv_TotalAmountWithoutVat,
            $e,
            $inv_exchangeRate,
            $inv_paymentMethodName,
            $inv_buyerDisplayName,
            $ma_dt,
            $inv_buyerEmail,
            $inv_buyerBankAccount,
            $inv_buyerBankName,
            $amount_to_word,
            $tlptdoanhthu20,
            $tgtck20,
            $txtck20,
            $nonTaxZone,
            $isDeductionNQ43
        );
    }

    public function setEmail(string $emails)
    {
        $this->inv_buyerEmail = $emails;
    }

    public function setInvNumber(float $inv_invoiceNumber)
    {
        $this->inv_invoiceNumber = $inv_invoiceNumber;
    }

    public function setIssueDate(string $issueDate)
    {
        $this->inv_invoiceIssuedDate = $issueDate;
    }
}

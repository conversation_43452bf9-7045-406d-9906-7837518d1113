<?php

namespace Dto\Minvoice;

use Dto\BaseDTO;
use Entities\MInvoice;

defined('BASEPATH') or exit('No direct script access allowed');

class CreateInvoiceItemTT32DTO extends BaseDTO
{
    public int $tchat; // "tchat": 0,
    public string $stt_rec0; // "stt_rec0": "string",
    public string $inv_itemName; // "inv_itemName": "string",
    public int $inv_quantity; // "inv_quantity": 0,
    public float $inv_unitPrice; // "inv_unitPrice": 0,
    public float $inv_discountPercentage; // "inv_discountPercentage": 0,
    public float $inv_discountAmount; // "inv_discountAmount": 0,
    public float $inv_TotalAmountWithoutVat; // "inv_TotalAmountWithoutVat": 0,
    public float $ma_thue; // "ma_thue": 0,
    public float $inv_vatAmount; // "inv_vatAmount": 0,
    public float $inv_TotalAmount; // "inv_TotalAmount": 0
    public ?string $inv_itemCode; // "inv_itemCode": "string",
    public ?string $inv_unitCode; // "inv_unitCode": "string",

    public function __construct(
        string $stt_rec0,
        string $inv_itemName,
        int $inv_quantity,
        float $inv_unitPrice,
        float $inv_discountPercentage,
        float $inv_discountAmount,
        float $inv_TotalAmountWithoutVat,
        float $ma_thue,
        float $inv_vatAmount,
        float $inv_TotalAmount,
        int $tchat = MInvoice::PRODUCT,
        ?string $inv_unitCode = null,
        ?string $inv_itemCode = null
    ) {
        $this->stt_rec0 = $stt_rec0;
        $this->inv_itemCode = $inv_itemCode;
        $this->inv_itemName = $inv_itemName;
        $this->inv_unitCode = $inv_unitCode;
        $this->inv_quantity = $inv_quantity;
        $this->inv_unitPrice = $inv_unitPrice;
        $this->inv_discountPercentage = $inv_discountPercentage;
        $this->inv_discountAmount = $inv_discountAmount;
        $this->inv_TotalAmountWithoutVat = $inv_TotalAmountWithoutVat;
        $this->tchat = $tchat;
        $this->ma_thue = $ma_thue;
        $this->inv_vatAmount = $inv_vatAmount;
        $this->inv_TotalAmount = $inv_TotalAmount;
    }
}

<?php

namespace Dto\Minvoice;

use Dto\BaseDTO;

defined('BASEPATH') or exit('No direct script access allowed');

class CreateInvoiceDetailTT32DTO extends BaseDTO
{
    protected array $data;

    /**
     * CreateInvoiceDetailTT32DTO construct
     *
     * @param CreateInvoiceItemTT32DTO[] $data
     */
    public function __construct(
        array $data
    ) {
        $this->data  = $data;
    }
}

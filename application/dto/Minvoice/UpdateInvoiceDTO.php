<?php

namespace Dto\Minvoice;

defined('BASEPATH') or exit('No direct script access allowed');

class UpdateInvoiceDTO extends CreateInvoiceDTO
{
    protected float $shdon;
    protected string $tdlap;

    /**
     * UpdateInvoiceDTO constructor
     *
     * @param float $shdon
     * @param string $tdlap
     */
    public function __construct(
        float $shdon,
        string $khieu,
        string $dvtte,
        string $ten,
        string $mst,
        string $dchi,
        float $tgtttbso,
        float $ttcktmai,
        float $tgtthue,
        float $tgtcthue,
        CreateInvoiceDetailDTO $details,
        ?float $tgia = 0,
        ?string $sdhang = null,
        ?string $htttoan = null,
        ?string $tnmua = null,
        ?string $mnmua = null,
        ?string $email = null,
        ?string $stknmua = null,
        ?string $nganhang_ngmua = null,
        ?string $tgtttbchu = null,
        ?float $tlptdoanhthu20 = null,
        ?float $tgtck20 = null,
        ?string $txtck20 = null
    ) {
        $this->shdon = $shdon;
        parent::__construct(
            $khieu,
            $dvtte,
            $ten,
            $mst,
            $dchi,
            $tgtttbso,
            $ttcktmai,
            $tgtthue,
            $tgtcthue,
            $details,
            $tgia,
            $sdhang,
            $htttoan,
            $tnmua,
            $mnmua,
            $email,
            $stknmua,
            $nganhang_ngmua,
            $tgtttbchu,
            $tlptdoanhthu20,
            $tgtck20,
            $txtck20
        );
    }
}

<?php

namespace Dto\Minvoice;

use Dto\BaseDTO;
use Entities\MInvoice;

defined('BASEPATH') or exit('No direct script access allowed');

class AdjustSignedInvoiceItemDTO extends BaseDTO
{
    protected string $stt;
    protected string $ma;
    protected int $tchat;
    protected string $inv_itemCode;
    protected string $inv_itemName;
    protected string $inv_unitCode;
    protected int $inv_quantity;
    protected float $inv_unitPrice;
    protected float $inv_discountPercentage;
    protected float $inv_discountAmount;
    protected float $inv_TotalAmountWithoutVat;
    protected int $ma_thue;
    protected float $inv_vatAmount;
    protected float $inv_TotalAmount;

    /**
     * AdjustSignedInvoiceItemDTO constructor
     */
    public function __construct(
        string $stt,
        string $ma,
        ?int $tchat = MInvoice::PRODUCT,
        ?string $inv_itemCode = null,
        ?string $inv_itemName = null,
        ?string $inv_unitCode = null,
        ?int $inv_quantity = null,
        ?float $inv_unitPrice = null,
        ?float $inv_discountPercentage = null,
        ?float $inv_discountAmount = null,
        ?float $inv_TotalAmountWithoutVat = null,
        ?int $ma_thue = 0,
        ?float $inv_vatAmount = null,
        ?float $inv_TotalAmount = null
    ) {
        $this->stt = $stt;
        $this->ma = $ma;
        $this->tchat = $tchat;
        $this->inv_itemCode = $inv_itemCode;
        $this->inv_itemName = $inv_itemName;
        $this->inv_unitCode = $inv_unitCode;
        $this->inv_quantity = $inv_quantity;
        $this->inv_unitPrice = $inv_unitPrice;
        $this->inv_discountPercentage = $inv_discountPercentage;
        $this->inv_discountAmount = $inv_discountAmount;
        $this->inv_TotalAmountWithoutVat = $inv_TotalAmountWithoutVat;
        $this->ma_thue = $ma_thue;
        $this->inv_vatAmount = $inv_vatAmount;
        $this->inv_TotalAmount = $inv_TotalAmount;
    }
}

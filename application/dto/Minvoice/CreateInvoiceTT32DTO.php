<?php

namespace Dto\Minvoice;

use Dto\BaseDTO;
use Entities\MInvoice;

defined('BASEPATH') or exit('No direct script access allowed');

class CreateInvoiceTT32DTO extends BaseDTO
{
    protected string $inv_invoiceSeries;
    protected string $inv_currencyCode;
    protected string $inv_buyerLegalName;
    protected string $inv_buyerTaxCode;
    protected string $inv_buyerAddressLine;
    protected float $inv_TotalAmount;
    protected float $inv_discountAmount;
    protected array $details;
    protected ?float $inv_vatAmount     = null;
    protected ?float $inv_TotalAmountWithoutVat     = null;
    protected ?string $e = null;
    protected ?string $inv_exchangeRate     = null;
    protected ?string $inv_paymentMethodName     = null;
    protected ?string $inv_buyerDisplayName     = null;
    protected ?string $ma_dt     = null;
    protected ?string $inv_buyerEmail     = null;
    protected ?string $inv_buyerBankAccount     = null;
    protected ?string $inv_buyerBankName     = null;
    protected ?string $amount_to_word     = null;
    protected ?float $tlptdoanhthu20     = null;
    protected ?float $tgtck20     = null;
    protected ?string $txtck20     = null;
    protected ?string $nonTaxZone     = null;
    protected ?bool $isDeductionNQ43     = null;
    protected ?bool $isPerson     = false;

    /**
     * Summary of __construct
     * @param string $inv_invoiceSeries
     * @param string $inv_currencyCode
     * @param string $inv_buyerLegalName
     * @param string $inv_buyerTaxCode
     * @param string $inv_buyerAddressLine
     * @param float $inv_TotalAmount
     * @param float $inv_discountAmount
     * @param CreateInvoiceDetailTT32DTO $details
     * @param mixed $e
     * @param mixed $inv_exchangeRate
     * @param mixed $inv_paymentMethodName
     * @param mixed $inv_buyerDisplayName
     * @param mixed $ma_dt
     * @param mixed $inv_buyerEmail
     * @param mixed $inv_buyerBankAccount
     * @param mixed $inv_buyerBankName
     * @param mixed $amount_to_word
     * @param float $inv_vatAmount
     * @param float $inv_TotalAmountWithoutVat
     * @param float $tlptdoanhthu20
     * @param float $tgtck20
     * @param mixed $txtck20
     * @param mixed $nonTaxZone
     * @param mixed $isDeductionNQ43
     */
    public function __construct(
        string $inv_invoiceSeries,
        string $inv_currencyCode,
        string $inv_buyerLegalName,
        string $inv_buyerTaxCode,
        string $inv_buyerAddressLine,
        float $inv_TotalAmount,
        float $inv_discountAmount,
        CreateInvoiceDetailTT32DTO $details,
        ?float $inv_vatAmount = null,
        ?float $inv_TotalAmountWithoutVat = null,
        ?string $e = null,
        ?string $inv_exchangeRate = null,
        ?string $inv_paymentMethodName = MInvoice::PAYMENT_METHOD_NAME_TMCK,
        ?string $inv_buyerDisplayName = null,
        ?string $ma_dt = null,
        ?string $inv_buyerEmail = null,
        ?string $inv_buyerBankAccount = null,
        ?string $inv_buyerBankName = null,
        ?string $amount_to_word = null,
        ?float $tlptdoanhthu20 = null,
        ?float $tgtck20 = null,
        ?string $txtck20 = null,
        ?string $nonTaxZone = null,
        ?string $isDeductionNQ43 = null
    ) {
        $this->inv_invoiceSeries = $inv_invoiceSeries;
        $this->inv_currencyCode = $inv_currencyCode;
        $this->inv_buyerLegalName = $inv_buyerLegalName;
        $this->inv_buyerTaxCode = $inv_buyerTaxCode;
        $this->inv_buyerAddressLine = $inv_buyerAddressLine;
        $this->inv_TotalAmount = $inv_TotalAmount;
        $this->inv_discountAmount = $inv_discountAmount;
        $this->details = [$details];
        $this->e = $e;
        $this->inv_exchangeRate = $inv_exchangeRate;
        $this->inv_paymentMethodName = $inv_paymentMethodName;
        $this->inv_buyerDisplayName = $inv_buyerDisplayName;
        $this->ma_dt = $ma_dt;
        $this->inv_buyerEmail = $inv_buyerEmail;
        $this->inv_buyerBankAccount = $inv_buyerBankAccount;
        $this->inv_buyerBankName = $inv_buyerBankName;
        $this->amount_to_word = $amount_to_word;
        $this->inv_vatAmount = $inv_vatAmount;
        $this->inv_TotalAmountWithoutVat = $inv_TotalAmountWithoutVat;
        $this->tlptdoanhthu20 = $tlptdoanhthu20;
        $this->tgtck20 = $tgtck20;
        $this->txtck20 = $txtck20;
        $this->nonTaxZone = $nonTaxZone;
        $this->isDeductionNQ43 = $isDeductionNQ43;
    }

    public function setEmail(string $emails)
    {
        $this->inv_buyerEmail = $emails;
    }

    public function setPerson(string $name)
    {
        $this->isPerson = true;
        $this->inv_buyerDisplayName = $name;
        $this->inv_buyerTaxCode = '';
    }

    public function maskAsCompany()
    {
        $this->isPerson = false;
        $this->inv_buyerDisplayName = '';
    }

    public function setPersonAddress(string $address)
    {
        $this->inv_buyerAddressLine = $address;
    }
}

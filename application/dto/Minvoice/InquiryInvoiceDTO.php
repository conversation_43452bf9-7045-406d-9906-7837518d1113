<?php

namespace Dto\Minvoice;

use Dto\BaseDTO;

defined('BASEPATH') or exit('No direct script access allowed');

class InquiryInvoiceDTO extends BaseDTO
{
    protected string $from;
    protected string $to;
    protected ?string $serial;
    protected int $page;
    protected int $limit;

    public function __construct(
        string $from,
        string $to,
        int $page,
        int $limit,
        ?string $serial = null
    ) {
        $this->from = $from;
        $this->to = $to;
        $this->serial = $serial;
        $this->page = $page;
        $this->limit = $limit;
    }
}

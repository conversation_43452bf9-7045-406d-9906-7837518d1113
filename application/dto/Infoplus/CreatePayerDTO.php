<?php

namespace Dto\Infoplus;

use Dto\BaseDTO;

defined('BASEPATH') or exit('No direct script access allowed');

class CreatePayerDTO extends BaseDTO
{
    protected string $payerNo;
    protected string $payerName;
    protected string $phoneNo;
    protected ?string $email;
    protected ?string $remark;
    protected ?string $smsUseYn;
    protected ?string $zaloUseYn;
    protected ?string $autoMapYn;
    protected ?string $benefitAccntNo;
    protected ?string $nationality;

    public function __construct(
        string $payerNo,
        string $payerName,
        string $phoneNo,
        ?string $email = null,
        ?string $remark = null,
        ?string $smsUseYn = null,
        ?string $zaloUseYn = null,
        ?string $autoMapYn = null,
        ?string $benefitAccntNo = null,
        ?string $nationality = null
    ) {
        $this->payerNo = $payerNo;
        $this->payerName = $payerName;
        $this->phoneNo = $phoneNo;
        $this->email = $email;
        $this->remark = $remark;
        $this->smsUseYn = $smsUseYn;
        $this->zaloUseYn = $zaloUseYn;
        $this->autoMapYn = $autoMapYn;
        $this->benefitAccntNo = $benefitAccntNo;
        $this->nationality = $nationality;
    }
}

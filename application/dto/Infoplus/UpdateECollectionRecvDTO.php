<?php

namespace Dto\Infoplus;

use Entities\ClientPayerEcRecv;

defined('BASEPATH') or exit('No direct script access allowed');

class UpdateECollectionRecvDTO extends CreateECollectionRecvDTO
{
    protected string $receivableId;

    public function __construct(
        string $receivableId,
        string $ecollectionCd,
        string $depositAmt,
        string $payableStartDt,
        string $payableEndDt,
        string $payableStartTm,
        string $payableEndTm,
        string $restrictionType = ClientPayerEcRecv::SPECIFIED_AMOUNT,
        string $payDtTmCd = ClientPayerEcRecv::PAYABLE_IN_RANGE
    ) {
        $this->receivableId = $receivableId;
        parent::__construct(
            '',
            '',
            $ecollectionCd,
            '',
            $depositAmt,
            $payableStartDt,
            $payableEndDt,
            $payableStartTm,
            $payableEndTm,
            $restrictionType,
            $payDtTmCd
        );
    }

    /**
     * Get the list of properties to exclude from the toArray output.
     * Can be overridden in child classes.
     */
    protected function getExclusions(): array
    {
        return ['motherAccntNo', 'payerNo', 'ecollectionCdHdNm'];
    }
}

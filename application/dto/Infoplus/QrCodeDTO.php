<?php

namespace Dto\Infoplus;

use Dto\BaseDTO;

defined('BASEPATH') or exit('No direct script access allowed');

class QrCodeDTO extends BaseDTO
{
    protected string $receivableId;
    protected string $ecollectionCd;

    public function __construct(
        string $receivableId,
        string $ecollectionCd
    ) {
        $this->receivableId = $receivableId;
        $this->ecollectionCd = $ecollectionCd;
    }
}

<?php

namespace Dto\Infoplus;

use Dto\BaseDTO;

defined('BASEPATH') or exit('No direct script access allowed');

class CreateECollectionDTO extends BaseDTO
{
    protected string $payerNo;
    protected string $motherAccntNo;
    protected ?string $ecollectionCdName;

    public function __construct(
        string $payerNo,
        string $motherAccntNo,
        ?string $ecollectionCdName = null
    ) {
        $this->payerNo = $payerNo;
        $this->motherAccntNo = $motherAccntNo;
        $this->ecollectionCdName = $ecollectionCdName;
    }
}

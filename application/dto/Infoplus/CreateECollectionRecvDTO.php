<?php

namespace Dto\Infoplus;

use Dto\BaseDTO;
use Entities\ClientPayerEcRecv;

defined('BASEPATH') or exit('No direct script access allowed');

class CreateECollectionRecvDTO extends BaseDTO
{
    protected string $motherAccntNo;
    protected string $payerNo;
    protected string $ecollectionCd;
    protected string $ecollectionCdHdNm;
    protected string $restrictionType;
    protected string $depositAmt;
    protected string $payDtTmCd;
    protected string $payableStartDt;
    protected string $payableEndDt;
    protected string $payableStartTm;
    protected string $payableEndTm;

    public function __construct(
        string $motherAccntNo,
        string $payerNo,
        string $ecollectionCd,
        string $ecollectionCdHdNm,
        string $depositAmt,
        string $payableStartDt,
        string $payableEndDt,
        string $payableStartTm,
        string $payableEndTm,
        string $restrictionType = ClientPayerEcRecv::SPECIFIED_AMOUNT,
        string $payDtTmCd = ClientPayerEcRecv::PAYABLE_IN_RANGE
    ) {
        $this->motherAccntNo = $motherAccntNo;
        $this->payerNo = $payerNo;
        $this->ecollectionCd = $ecollectionCd;
        $this->ecollectionCdHdNm = $ecollectionCdHdNm;
        $this->depositAmt = $depositAmt;
        $this->restrictionType = $restrictionType;
        $this->payDtTmCd = $payDtTmCd;
        $this->payableStartDt = $payableStartDt;
        $this->payableEndDt = $payableEndDt;
        $this->payableStartTm = $payableStartTm;
        $this->payableEndTm = $payableEndTm;
    }
}

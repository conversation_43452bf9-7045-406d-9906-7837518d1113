<?php

namespace Dto\Infoplus;

use Dto\BaseDTO;

defined('BASEPATH') or exit('No direct script access allowed');

class InquiryEcCodeDTO extends BaseDTO
{

    public const STATUS_USED = '1';
    public const STATUS_UNUSED = '0';

    protected ?string $payerNo;
    protected ?string $ecollectionCd;
    protected ?string $ecollectionCdName;
    protected ?string $status;
    protected int $pageNum;
    protected int $pageSize;

    public function __construct(
        int $pageNum,
        int $pageSize,
        ?string $status = null,
        ?string $payerNo = null,
        ?string $ecollectionCd = null,
        ?string $ecollectionCdName = null
    ) {
        $this->pageNum = $pageNum;
        $this->pageSize = $pageSize;
        $this->status = $status;
        $this->payerNo = $payerNo;
        $this->ecollectionCd = $ecollectionCd;
        $this->ecollectionCdName = $ecollectionCdName;
    }
}

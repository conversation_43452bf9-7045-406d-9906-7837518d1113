<?php

namespace Dto\Infoplus;

defined('BASEPATH') or exit('No direct script access allowed');

class UpdatePayerDTO extends CreatePayerDTO
{
    public function __construct(
        string $payerName,
        string $phoneNo,
        ?string $email,
        ?string $remark,
        ?string $smsUseYn,
        ?string $zaloUseYn,
        ?string $autoMapYn,
        ?string $benefitAccntNo,
        ?string $nationality
    ) {
        parent::__construct(
            '',
            $payerName,
            $phoneNo,
            $email,
            $remark,
            $smsUseYn,
            $zaloUseYn,
            $autoMapYn,
            $benefitAccntNo,
            $nationality
        );
    }

    /**
     * Get the list of properties to exclude from the toArray output.
     * Can be overridden in child classes.
     */
    protected function getExclusions(): array
    {
        return ['payerNo'];
    }
}

<?php

namespace Dto\Infoplus;

defined('BASEPATH') or exit('No direct script access allowed');

class InquiryEcTransDTO extends CreatePayerDTO
{
    protected string $startDt;
    protected string $endDt;
    protected int $pageNum;
    protected int $pageSize;
    protected ?string $ecollectionCd;

    public function __construct(
        string $startDt,
        string $endDt,
        int $pageNum,
        int $pageSize,
        ?string $ecollectionCd = null
    ) {
        $this->startDt = $startDt;
        $this->endDt = $endDt;
        $this->pageNum = $pageNum;
        $this->pageSize = $pageSize;
        $this->ecollectionCd = $ecollectionCd;
    }
}

<?php

namespace Dto\Infoplus;

use Dto\BaseDTO;

defined('BASEPATH') or exit('No direct script access allowed');

class MapECollectionDTO extends BaseDTO
{
    protected string $payerNo;
    protected string $ecollectionCd;
    protected ?string $ecollectionCdName;

    public function __construct(
        string $payerNo,
        string $ecollectionCd,
        ?string $ecollectionCdName = null
    ) {
        $this->payerNo = $payerNo;
        $this->ecollectionCd = $ecollectionCd;
        $this->ecollectionCdName = $ecollectionCdName;
    }
}

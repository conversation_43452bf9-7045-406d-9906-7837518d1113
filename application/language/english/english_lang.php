<?php

# Version 1.0.0
#
# General
$lang['id'] = 'ID';
$lang['name'] = 'Name';
$lang['options'] = 'Options';
$lang['submit'] = 'Save';
$lang['added_successfully'] = '%s added successfully.';
$lang['updated_successfully'] = '%s updated successfully.';
$lang['assign_failed'] = 'Customer admin cannot be assigned to this Customer due to 30 days contact rule';
$lang['not_allow_assign_free_data'] = 'Customer admin cannot be assigned to this Customer due to Customer in Free Data pool';
$lang['edit'] = 'Edit %s';
$lang['add_new'] = 'Add new %s';
$lang['deleted'] = '%s deleted';
$lang['problem_deleting'] = 'Problem deleting %s';
$lang['is_referenced'] = 'The ID of the %s is already using.';
$lang['close'] = 'Close';
$lang['send'] = 'Send';
$lang['cancel'] = 'Cancel';
$lang['go_back'] = 'Go Back';
$lang['error_uploading_file'] = 'Error uploading file';
$lang['load_more'] = 'Load More';
$lang['cant_delete_default'] = 'Cant delete default %s';
$lang['invoice_status_paid'] = 'Paid';
$lang['invoice_status_unpaid'] = 'Unpaid';
$lang['invoice_status_overdue'] = 'Overdue';
$lang['invoice_status_not_paid_completely'] = 'Partially Paid';
$lang['invoice_pdf_heading'] = 'INVOICE';
$lang['invoice_table_item_heading'] = 'Item';
$lang['invoice_table_quantity_heading'] = 'Qty';
$lang['invoice_table_rate_heading'] = 'Rate';
$lang['invoice_table_discount_table_heading'] = 'Discount';
$lang['invoice_table_tax_heading'] = 'Tax';
$lang['invoice_table_amount_heading'] = 'Amount';
$lang['invoice_subtotal'] = 'Sub Total';
$lang['invoice_adjustment'] = 'Adjustment';
$lang['invoice_total'] = 'Total';
$lang['invoice_bill_to'] = 'Bill To';
$lang['invoice_data_date'] = 'Invoice Date:';
$lang['invoice_data_duedate'] = 'Due Date:';
$lang['invoice_received_payments'] = 'Transactions';
$lang['invoice_no_payments_found'] = 'No payments found for this invoice';
$lang['invoice_note'] = 'Note:';
$lang['invoice_payments_table_number_heading'] = 'Payment #';
$lang['invoice_payments_table_mode_heading'] = 'Payment Mode';
$lang['invoice_payments_table_date_heading'] = 'Date';
$lang['invoice_payments_table_amount_heading'] = 'Amount';
$lang['announcement'] = 'Announcement';
$lang['announcement_lowercase'] = 'announcement';
$lang['announcements'] = 'Announcements';
$lang['announcements_lowercase'] = 'announcements';
$lang['new_announcement'] = 'New Announcement';
$lang['announcement_name'] = 'Subject';
$lang['announcement_message'] = 'Message';
$lang['announcement_show_to_staff'] = 'Show to staff';
$lang['announcement_show_to_clients'] = 'Show to clients';
$lang['announcement_show_my_name'] = 'Show my name';
$lang['clients'] = 'Customers';
$lang['client'] = 'Customer';
$lang['new_client'] = 'New Customer';
$lang['client_lowercase'] = 'customer';
$lang['client_firstname'] = 'First Name';
$lang['client_lastname'] = 'Last Name';
$lang['client_email'] = 'Email';
$lang['client_company'] = 'Registered business name';
$lang['client_vat_number'] = 'VAT Number';
$lang['client_address'] = 'Registered business address';
$lang['client_city'] = 'City';
$lang['client_postal_code'] = 'Zip Code';
$lang['client_state'] = 'State';
$lang['client_password'] = 'Password';
$lang['client_password_change_populate_note'] = 'Note: if you populate this field, password will be changed on this contact.';
$lang['client_password_last_changed'] = 'Password last changed:';
$lang['login_as_client'] = 'Login as client';
$lang['client_invoices_tab'] = 'PO';
$lang['contracts_invoices_tab'] = 'Contracts';
$lang['contracts_tickets_tab'] = 'Tickets';
$lang['contracts_notes_tab'] = 'Notes';
$lang['note_description'] = 'Note description';
$lang['client_do_not_send_welcome_email'] = 'Do not send welcome email';
$lang['clients_notes_table_description_heading'] = 'Description';
$lang['clients_notes_table_addedfrom_heading'] = 'Added From';
$lang['clients_notes_table_dateadded_heading'] = 'Date Added';
$lang['clients_list_full_name'] = 'Full Name';
$lang['clients_list_last_login'] = 'Last Login';
$lang['clients_contact_id'] = 'Contact ID';
$lang['contracts'] = 'Contracts';
$lang['contract'] = 'Contract';
$lang['new_contract'] = 'New Contract';
$lang['contract_lowercase'] = 'contract';
$lang['contract_start_date'] = 'Start Date';
$lang['contract_end_date'] = 'End Date';
$lang['contract_subject'] = 'Subject';
$lang['contract_description'] = 'Description';
$lang['contract_subject_tooltip'] = 'Subject is also visible to customer';
$lang['contract_client_string'] = 'Customer';
$lang['contract_attach'] = 'Attach document';
$lang['contract_list_client'] = 'Customer';
$lang['contract_list_subject'] = 'Subject';
$lang['contract_list_start_date'] = 'Start Date';
$lang['contract_list_end_date'] = 'End Date';
$lang['currencies'] = 'Currencies';
$lang['currency'] = 'Currency';
$lang['new_currency'] = 'New Currency';
$lang['currency_lowercase'] = 'currency';
$lang['base_currency_set'] = 'This is now your base currency.';
$lang['make_base_currency'] = 'Make base currency';
$lang['base_currency_string'] = 'Base Currency';
$lang['currency_list_name'] = 'Name';
$lang['currency_list_symbol'] = 'Symbol';
$lang['currency_add_edit_description'] = 'Currency Code';
$lang['currency_add_edit_rate'] = 'Symbol';
$lang['currency_edit_heading'] = 'Edit Currency';
$lang['currency_add_heading'] = 'Add New Currency';
$lang['departments'] = 'Departments';
$lang['department'] = 'Department';
$lang['new_department'] = 'New Department';
$lang['department_lowercase'] = 'department';
$lang['department_name'] = 'Department Name';
$lang['department_email'] = 'Department Email';
$lang['department_hide_from_client'] = 'Hide from client?';
$lang['department_list_name'] = 'Name';
$lang['email_templates'] = 'Email Templates';
$lang['email_template'] = 'Email Template';
$lang['email_template_lowercase'] = 'email template';
$lang['email_templates_lowercase'] = 'email templates';
$lang['email_template_ticket_fields_heading'] = 'Tickets';
$lang['email_template_invoices_fields_heading'] = 'Invoices';
$lang['email_template_clients_fields_heading'] = 'Customers';
$lang['template_name'] = 'Template Title';
$lang['template_subject'] = 'Subject';
$lang['template_fromname'] = 'From Name';
$lang['template_fromemail'] = 'From Email';
$lang['send_as_plain_text'] = 'Send as Plaintext';
$lang['email_template_disabled'] = 'Disabled';
$lang['email_template_email_message'] = 'Email message';
$lang['available_merge_fields'] = 'Available merge fields';
$lang['dashboard_string'] = 'Dashboard';
$lang['home_latest_todos'] = 'Latest to do\'s';
$lang['home_no_latest_todos'] = 'No todos found';
$lang['home_latest_finished_todos'] = 'Latest finished to do\'s';
$lang['home_no_finished_todos_found'] = 'No finished todos found';
$lang['home_tickets_awaiting_reply_by_department'] = 'Tickets Awaiting Reply by Department';
$lang['home_tickets_awaiting_reply_by_status'] = 'Tickets Awaiting Reply by Status';
$lang['home_this_week_events'] = 'This Week events';
$lang['home_upcoming_events_next_week'] = 'Upcoming events Next Week';
$lang['home_event_added_by'] = 'Event added by';
$lang['home_public_event'] = 'Public event';
$lang['home_weekly_payment_records'] = 'Weekly Payment Records';
$lang['home_weekend_ticket_opening_statistics'] = 'Weekly Ticket Openings Statistics';
$lang['whats_on_your_mind'] = 'Share documents, ideas..';
$lang['new_post'] = 'Post';
$lang['newsfeed_upload_tooltip'] = 'Tip:Drag and drop files to upload';
$lang['newsfeed_all_departments'] = 'All Departments';
$lang['newsfeed_pin_post'] = 'Pin post';
$lang['newsfeed_unpin_post'] = 'Unpin post';
$lang['newsfeed_delete_post'] = 'Delete';
$lang['newsfeed_published_post'] = 'Published';
$lang['newsfeed_you_like_this'] = 'You like this';
$lang['newsfeed_like_this'] = 'like this';
$lang['newsfeed_one_other'] = 'other';
$lang['newsfeed_you'] = 'You';
$lang['newsfeed_and'] = 'and';
$lang['newsfeed_you_and'] = 'You and';
$lang['newsfeed_like_this_saying'] = 'Like this';
$lang['newsfeed_unlike_this_saying'] = 'Unlike this';
$lang['newsfeed_show_more_comments'] = 'Show more comments';
$lang['comment_this_post_placeholder'] = 'Comment this post..';
$lang['newsfeed_post_likes_modal_heading'] = 'Colleagues who like this post';
$lang['newsfeed_comment_likes_modal_heading'] = 'Colleagues who like this comment';
$lang['newsfeed_newsfeed_post_only_visible_to_departments'] = 'This post is only visible to the following departments: %s';
$lang['invoice_items'] = 'Invoice Items';
$lang['invoice_item'] = 'Invoice Item';
$lang['new_invoice_item'] = 'New Item';
$lang['invoice_item_lowercase'] = 'invoice item';
$lang['invoice_items_list_description'] = 'Description';
$lang['invoice_items_list_rate'] = 'Rate';
$lang['invoice_item_add_edit_description'] = 'Description';
$lang['invoice_item_add_edit_rate'] = 'Rate';
$lang['invoice_item_edit_heading'] = 'Edit Item';
$lang['invoice_item_add_heading'] = 'Add New Item';
$lang['invoices'] = 'Purchase Order';
$lang['invoice'] = 'PO';
$lang['invoice_lowercase'] = 'invoice';
$lang['create_new_invoice'] = 'Create New PO';
$lang['view_invoice'] = 'View Invoice';
$lang['invoice_payment_recorded'] = 'Invoice Payment Recorded';
$lang['invoice_payment_record_failed'] = 'Failed to Record Invoice Payment';
$lang['invoice_sent_to_client_success'] = 'The invoice is sent successfully to the client';
$lang['invoice_sent_to_client_fail'] = 'Problem while sending the invoice';
$lang['invoice_reminder_send_problem'] = 'Problem sending invoice overdue reminder';
$lang['invoice_overdue_reminder_sent'] = 'Invoice Overdue Reminder Successfully Sent';
$lang['invoice_details'] = 'Invoice Details';
$lang['invoice_view'] = 'View Invoice';
$lang['invoice_select_customer'] = 'Customer';
$lang['invoice_add_edit_number'] = 'PO Number';
$lang['invoice_add_edit_date'] = 'Invoice Date';
$lang['invoice_add_edit_duedate'] = 'Due Date';
$lang['invoice_add_edit_currency'] = 'Currency';
$lang['invoice_add_edit_client_note'] = 'Client Note';
$lang['invoice_add_edit_admin_note'] = 'Admin Note';
$lang['invoices_toggle_table_tooltip'] = 'Toggle Table';
$lang['edit_invoice_tooltip'] = 'Edit PO';
$lang['delete_invoice_tooltip'] = 'Delete Invoice. Note: All payments regarding to this invoice will be deleted (if any).';
$lang['invoice_sent_to_email_tooltip'] = 'Send to Email';
$lang['invoice_already_send_to_client_tooltip'] = 'This invoice is already sent to the client %s';
$lang['send_overdue_notice_tooltip'] = 'Send Overdue Notice';
$lang['invoice_view_activity_tooltip'] = 'Activity Log';
$lang['invoice_record_payment'] = 'Record Payment';
$lang['invoice_send_to_client_modal_heading'] = 'Send invoice to client';
$lang['invoice_send_to_client_attach_pdf'] = 'Attach Invoice PDF';
$lang['invoice_send_to_client_attach_tnc'] = 'Attach Invoice TnC';
$lang['invoice_send_to_client_preview_template'] = 'Preview Email Template';
$lang['invoice_dt_table_heading_number'] = 'PO #';
$lang['invoice_dt_table_heading_date'] = 'Date';
$lang['invoice_dt_table_heading_client'] = 'Customer';
$lang['invoice_dt_table_heading_duedate'] = 'Due Date';
$lang['invoice_dt_table_heading_amount'] = 'Amount';
$lang['invoice_dt_table_heading_status'] = 'Status';
$lang['invoice_dt_table_heading_total_amount'] = 'Total amount (No tax)';
$lang['invoice_dt_table_heading_id_customer'] = 'ID Customer';
$lang['invoice_dt_table_heading_vat_number'] = 'VAT Number';
$lang['invoice_dt_table_heading_payment_date'] = 'Payment Date';
$lang['invoice_dt_table_heading_customer_admin'] = 'Customer Admin';
$lang['record_payment_for_invoice'] = 'Record Payment for';
$lang['record_payment_amount_received'] = 'Amount Received';
$lang['record_payment_date'] = 'Payment Date';
$lang['record_payment_leave_note'] = 'Leave a note';
$lang['invoice_payments_received'] = 'Payments Received';
$lang['invoice_record_payment_note_placeholder'] = 'Admin Note';
$lang['no_payments_found'] = 'No Payments found for this invoice';
$lang['payments'] = 'Payments';
$lang['payment'] = 'Payment';
$lang['payment_lowercase'] = 'payment';
$lang['payments_table_number_heading'] = 'Payment #';
$lang['payments_table_invoicenumber_heading'] = 'PO #';
$lang['payments_table_mode_heading'] = 'Payment Mode';
$lang['payments_table_date_heading'] = 'Date';
$lang['payments_table_amount_heading'] = 'Amount';
$lang['payments_table_client_heading'] = 'Customer';
$lang['payments_table_creator'] = 'Payment Creator';
$lang['payment_not_exists'] = 'The payment does not exists';
$lang['payment_edit_for_invoice'] = 'Payment for Invoice';
$lang['payment_edit_amount_received'] = 'Amount Received';
$lang['payment_edit_date'] = 'Payment Date';
$lang['kb_article_add_edit_subject'] = 'Subject';
$lang['kb_article_add_edit_group'] = 'Group';
$lang['kb_string'] = 'Knowledge Base';
$lang['kb_article'] = 'Article';
$lang['kb_article_lowercase'] = 'article';
$lang['kb_article_new_article'] = 'New Article';
$lang['kb_article_disabled'] = 'Disabled';
$lang['kb_article_description'] = 'Article description';
$lang['kb_no_articles_found'] = 'No knowledge base articles found';
$lang['kb_dt_article_name'] = 'Article Name';
$lang['kb_dt_group_name'] = 'Group';
$lang['new_group'] = 'New Group';
$lang['kb_group_add_edit_name'] = 'Group Name';
$lang['kb_group_add_edit_description'] = 'Short description';
$lang['kb_group_add_edit_disabled'] = 'Disabled';
$lang['kb_group_add_edit_note'] = 'Note: All articles in this group will be hidden if disabled is checked';
$lang['group_table_name_heading'] = 'Name';
$lang['group_table_isactive_heading'] = 'Active';
$lang['kb_no_groups_found'] = 'No knowledge base groups found';
$lang['media_files'] = 'Files';
$lang['new_payment_mode'] = 'New Payment Mode';
$lang['payment_modes'] = 'Payment Modes';
$lang['payment_mode'] = 'Payment Mode';
$lang['payment_mode_lowercase'] = 'payment mode';
$lang['payment_modes_dt_name'] = 'Payment Mode Name';
$lang['payment_mode_add_edit_name'] = 'Payment Mode Name';
$lang['payment_mode_edit_heading'] = 'Edit Payment Mode';
$lang['payment_mode_add_heading'] = 'Add New Payment Mode';
$lang['new_predefined_reply'] = 'New Predefined Reply';
$lang['predefined_replies'] = 'Predefined Replies';
$lang['predefined_reply'] = 'Predefined Reply';
$lang['predefined_reply_lowercase'] = 'predefined reply';
$lang['predefined_replies_dt_name'] = 'Predefined Reply Name';
$lang['predefined_reply_add_edit_name'] = 'Predefined Reply Name';
$lang['predefined_reply_add_edit_content'] = 'Reply Content';
$lang['new_ticket_priority'] = 'New Priority';
$lang['ticket_priorities'] = 'Ticket Priorities';
$lang['ticket_priority'] = 'Ticket Priority';
$lang['ticket_priority_lowercase'] = 'ticket priority';
$lang['no_ticket_priorities_found'] = 'No Ticket Priorities Found';
$lang['ticket_priority_dt_name'] = 'Ticket Priority Name';
$lang['ticket_priority_add_edit_name'] = 'Priority Name';
$lang['kb_reports'] = 'Knowledge base articles reports';
$lang['sales_reports'] = 'Sales Reports';
$lang['reports_choose_kb_group'] = 'Choose Group';
$lang['report_kb_yes'] = 'Yes';
$lang['report_kb_no'] = 'No';
$lang['report_kb_no_votes'] = 'No votes yet';
$lang['report_this_week_leads_conversions'] = 'This Week Leads Conversions';
$lang['report_leads_sources_conversions'] = 'Sources Conversion';
$lang['report_leads_monthly_conversions'] = 'Monthly';
$lang['sales_report_heading'] = 'Sales Report';
$lang['report_sales_type_income'] = 'Total Income';
$lang['report_sales_type_customer'] = 'Customers Report';
$lang['report_job_posting_package_tracking'] = 'Job Posting Package Tracking Report';
$lang['report_sales_base_currency_select_explanation'] = 'You need to select currency because you have invoices with different currency';
$lang['report_sales_from_date'] = 'From Date';
$lang['report_sales_to_date'] = 'To Date';
$lang['report_sales_months_all_time'] = 'All Time';
$lang['report_sales_months_six_months'] = 'Last 6 months';
$lang['report_sales_months_twelve_months'] = 'Last 12 months';
$lang['reports_sales_generated_report'] = 'Generated Report';
$lang['reports_sales_dt_customers_client'] = 'Customer';
$lang['reports_sales_dt_customers_total_invoices'] = 'Total Invoices';
$lang['reports_sales_dt_items_customers_amount'] = 'Amount';
$lang['reports_sales_dt_items_customers_amount_with_tax'] = 'Amount with Tax';
$lang['job_tracking_report_id_crm'] = 'ID CRM';
$lang['job_tracking_report_invoice_crm'] = 'PO # CRM';
$lang['job_tracking_report_sales'] = 'Sales';
$lang['job_tracking_report_ams_company_id'] = 'AMS ID Company';
$lang['job_tracking_report_ams_company_name'] = 'AMS Company';
$lang['job_tracking_report_package_service_name'] = 'Package/ Service Name';
$lang['job_tracking_report_type'] = 'Type';
$lang['job_tracking_report_payment_date'] = 'Payment Date';
$lang['job_tracking_report_invoice_expiration_date'] = 'Invoice Expiration Date';
$lang['job_tracking_report_status'] = 'Status';
$lang['job_tracking_report_activation_date'] = 'Activation Date';
$lang['job_tracking_report_id_job'] = 'ID Job';
$lang['job_tracking_report_extended_retention_expiry_date'] = 'Extended Retention Expiry Date (if any)';
$lang['job_tracking_report_reactivation_date_for_retention'] = 'Reactivation Date for Retention (if any)';
$lang['job_tracking_report_status_used'] = 'Used';
$lang['job_tracking_report_status_extend'] = 'Extend';
$lang['job_tracking_report_status_expired'] = 'Expired';
$lang['job_tracking_report_status_unused'] = 'Unused';
$lang['filter_job_tracking_report_id_crm'] = 'ID CRM';
$lang['filter_job_tracking_report_ams_company_id'] = 'AMS Company';
$lang['filter_job_tracking_report_sale'] = 'Sale';
$lang['filter_job_tracking_report_invoice'] = 'Invoice';
$lang['filter_job_tracking_report_invoice_expiration_from_date'] = 'Retention Expiry Date from';
$lang['filter_job_tracking_report_invoice_expiration_to_date'] = 'Retention Expiry Date to';
$lang['filter_job_tracking_report_type'] = 'Type';
$lang['filter_job_tracking_report_status'] = 'Status';
$lang['new_role'] = 'New Role';
$lang['all_roles'] = 'All Role';
$lang['roles'] = 'Staff Roles';
$lang['role'] = 'Role';
$lang['role_lowercase'] = 'role';
$lang['roles_total_users'] = 'Total Users: ';
$lang['roles_dt_name'] = 'Role Name';
$lang['role_add_edit_name'] = 'Role Name';
$lang['new_service'] = 'New Service';
$lang['services'] = 'Services';
$lang['service'] = 'Service';
$lang['service_lowercase'] = 'service';
$lang['services_dt_name'] = 'Service Name';
$lang['service_add_edit_name'] = 'Service Name';
$lang['settings'] = 'Settings';
$lang['settings_updated'] = 'Settings Updated';
$lang['settings_save'] = 'Save Settings';
$lang['settings_group_general'] = 'General';
$lang['settings_group_localization'] = 'Localization';
$lang['settings_group_tickets'] = 'Tickets';
$lang['settings_group_sales'] = 'Finance';
$lang['settings_group_email'] = 'Email';
$lang['settings_group_clients'] = 'Customers';
$lang['settings_group_newsfeed'] = 'News feed';
$lang['settings_group_cronjob'] = 'Cron Job';
$lang['settings_yes'] = 'Yes';
$lang['settings_no'] = 'No';
$lang['settings_clients_default_theme'] = 'Default customers theme';
$lang['settings_clients_allow_registration'] = 'Allow customers to register';
$lang['settings_clients_allow_kb_view_without_registration'] = 'Allow knowledge base to be viewed without registration';
$lang['settings_cron_send_overdue_reminder'] = 'Send invoice overdue reminder';
$lang['settings_cron_send_overdue_reminder_tooltip'] = 'Send overdue email to client when invoice status updated to overdue from Cron Job';
$lang['automatically_send_invoice_overdue_reminder_after'] = 'Auto send reminder after (days)';
$lang['automatically_resend_invoice_overdue_reminder_after'] = 'Auto re-send reminder after (days)';
$lang['settings_email_host'] = 'SMTP Host';
$lang['settings_email_port'] = 'SMTP Port';
$lang['settings_email'] = 'Email';
$lang['settings_email_password'] = 'SMTP Password';
$lang['settings_email_charset'] = 'Email Charset';
$lang['settings_email_signature'] = 'Email Signature';
$lang['settings_general_company_logo'] = 'Company Logo';
$lang['settings_general_company_logo_tooltip'] = 'Recommended dimensions: 150 x 34px';
$lang['settings_general_company_remove_logo_tooltip'] = 'Remove company logo';
$lang['settings_general_company_name'] = 'Company Name';
$lang['settings_general_company_main_domain'] = 'Company Main Domain';
$lang['settings_general_use_knowledgebase'] = 'Use Knowledge Base';
$lang['settings_general_use_knowledgebase_tooltip'] = 'If you allow this options knowledge base will be shown also on clients side';
$lang['settings_general_tables_limit'] = 'Tables Pagination Limit';
$lang['settings_general_default_staff_role'] = 'Default Staff Role';
$lang['settings_general_default_staff_role_tooltip'] = 'When you add new staff member this role will be selected by default';
$lang['settings_localization_date_format'] = 'Date Format';
$lang['settings_localization_default_timezone'] = 'Default Timezone';
$lang['settings_localization_default_language'] = 'Default Language';
$lang['settings_newsfeed_max_file_upload_post'] = 'Maximum files upload on post';
$lang['settings_reminders_contracts'] = 'Contract expiration reminder before';
$lang['settings_reminders_contracts_tooltip'] = 'Expiration reminder notification in days';
$lang['settings_tickets_use_services'] = 'Use services';
$lang['settings_tickets_max_attachments'] = 'Maximum ticket attachments';
$lang['settings_tickets_allow_departments_access'] = 'Allow staff to access only ticket that belongs to staff departments';
$lang['settings_tickets_allowed_file_extensions'] = 'Allowed attachments file extensions';
$lang['settings_sales_general'] = 'General';
$lang['settings_sales_general_note'] = 'General settings';
$lang['settings_sales_invoice_prefix'] = 'Invoice Number Prefix';
$lang['settings_sales_decimal_separator'] = 'Decimal Separator';
$lang['settings_sales_thousand_separator'] = 'Thousand Separator';
$lang['settings_sales_currency_placement'] = 'Currency Placement';
$lang['settings_sales_currency_placement_before'] = 'Before Amount';
$lang['settings_sales_currency_placement_after'] = 'After Amount';
$lang['settings_sales_require_client_logged_in_to_view_invoice'] = 'Require client to be logged in to view invoice';
$lang['settings_sales_next_invoice_number'] = 'Next Invoice Number';
$lang['settings_sales_next_invoice_number_tooltip'] = 'Set this field to 1 if you want to start from beginning';
$lang['settings_sales_decrement_invoice_number_on_delete'] = 'Decrement invoice number on delete';
$lang['settings_sales_decrement_invoice_number_on_delete_tooltip'] = 'Do you want to decrement the invoice number when the last invoice is deleted? eq. If is set this option to YES and before invoice delete the next invoice number is 15 the next invoice number will decrement to 14. If is set to NO the number will remain to 15.  If you have setup delete only on last invoice to NO you should set this option to NO too to keep the next invoice number not decremented.';
$lang['settings_sales_invoice_number_format'] = 'Invoice Number Format';
$lang['settings_sales_invoice_number_format_year_based'] = 'Year Based';
$lang['settings_sales_invoice_number_format_number_based'] = 'Number Based (000001)';
$lang['settings_sales_company_info_note'] = 'These information will be displayed on invoices/estimates/payments and other PDF documents where company info is required';
$lang['settings_sales_company_name'] = 'Company Name';
$lang['settings_sales_address'] = 'Address';
$lang['settings_sales_city'] = 'City';
$lang['settings_sales_country_code'] = 'Country Code';
$lang['settings_sales_postal_code'] = 'Zip Code';
$lang['settings_sales_phonenumber'] = 'Phone';
$lang['new_lead'] = 'New Lead';
$lang['leads'] = 'Leads';
$lang['lead'] = 'Lead';
$lang['lead_lowercase'] = 'lead';
$lang['leads_all'] = 'All';
$lang['leads_canban_notes'] = 'Notes: %s';
$lang['leads_canban_source'] = 'Source: %s';
$lang['lead_new_source'] = 'New Source';
$lang['lead_sources'] = 'Lead Sources';
$lang['lead_source'] = 'Lead Source';
$lang['lead_source_lowercase'] = 'lead source';
$lang['leads_sources_not_found'] = 'No leads sources found';
$lang['leads_sources_table_name'] = 'Source Name';
$lang['leads_source_add_edit_name'] = 'Source Name';
$lang['lead_new_status'] = 'New Lead Status';
$lang['lead_status'] = 'Lead Status';
$lang['lead_status_lowercase'] = 'lead status';
$lang['leads_status_table_name'] = 'Status Name';
$lang['leads_status_add_edit_name'] = 'Status Name';
$lang['leads_status_add_edit_order'] = 'Order';
$lang['lead_statuses_not_found'] = 'No leads statuses found';
$lang['leads_search'] = 'Search Leads';
$lang['leads_table_total'] = 'Total Leads: %s';
$lang['leads_dt_name'] = 'Name';
$lang['leads_dt_email'] = 'Email';
$lang['leads_dt_phonenumber'] = 'Phone';
$lang['leads_dt_assigned'] = 'Assigned';
$lang['leads_dt_status'] = 'Status';
$lang['leads_dt_last_contact'] = 'Last Contact';
$lang['lead_add_edit_name'] = 'Name';
$lang['lead_add_edit_email'] = 'Email Address';
$lang['lead_add_edit_phonenumber'] = 'Phone';
$lang['lead_add_edit_source'] = 'Source';
$lang['lead_add_edit_status'] = 'Status';
$lang['lead_add_edit_assigned'] = 'Assigned';
$lang['lead_add_edit_datecontacted'] = 'Date Contacted';
$lang['lead_add_edit_contacted_today'] = 'Contacted Today';
$lang['lead_add_edit_activity'] = 'Activity Log';
$lang['lead_add_edit_notes'] = 'Notes';
$lang['lead_add_edit_add_note'] = 'Add Note';
$lang['lead_not_contacted'] = 'I have not contacted this lead';
$lang['lead_add_edit_contacted_this_lead'] = 'I got in touch with this lead';
$lang['access_denied'] = 'Access denied';
$lang['prev'] = 'Prev';
$lang['next'] = 'next';
$lang['dt_paginate_first'] = 'First';
$lang['dt_paginate_last'] = 'Last';
$lang['dt_paginate_next'] = 'Next';
$lang['dt_paginate_previous'] = 'Previous';
$lang['dt_search'] = 'Search...';
$lang['dt_zero_records'] = 'No matching records found';
$lang['dt_loading_records'] = 'Loading...';
$lang['dt_length_menu'] = 'Show _MENU_ entries';
$lang['dt_info_filtered'] = '(filtered from _MAX_ total {0})';
$lang['dt_info_empty'] = 'Showing 0 to 0 of 0 {0}';
$lang['dt_info'] = 'Showing _START_ to _END_ of _TOTAL_ {0}';
$lang['dt_empty_table'] = 'No {0} found';
$lang['dt_sort_ascending'] = ' activate to sort column ascending';
$lang['dt_sort_descending'] = ' activate to sort column descending';
$lang['user_sent_overdue_reminder'] = '%s sent invoice overdue reminder';
$lang['wd_monday'] = 'Monday';
$lang['wd_tuesday'] = 'Tuesday';
$lang['wd_thursday'] = 'Thursday';
$lang['wd_wednesday'] = 'Wednesday';
$lang['wd_friday'] = 'Friday';
$lang['wd_saturday'] = 'Saturday';
$lang['wd_sunday'] = 'Sunday';
$lang['als_dashboard'] = 'Dashboard';
$lang['als_dashboard_revenue_recognition_summary'] = 'Revenue Summary';
$lang['als_dashboard_revenue_recognition'] = 'Revenue Recognition';
$lang['als_clients'] = 'Customers';
$lang['als_leads'] = 'Leads';
$lang['als_contracts'] = 'Contracts';
$lang['als_sales'] = 'Sales';
$lang['als_staff'] = 'Staff';
$lang['als_tasks'] = 'Tasks';
$lang['als_kb'] = 'Knowledge Base';
$lang['als_media'] = 'Media';
$lang['als_reports'] = 'Reports';
$lang['als_reports_sales_submenu'] = 'Sales';
$lang['als_reports_leads_submenu'] = 'Leads';
$lang['als_kb_articles_submenu'] = 'KB Articles';
$lang['als_utilities'] = 'Utilities';
$lang['als_announcements_submenu'] = 'Announcements';
$lang['als_calendar_submenu'] = 'Calendar';
$lang['als_activity_log_submenu'] = 'Activity Log';
$lang['als_daily_activities'] = 'Daily Activities';
$lang['call_center_logs'] = 'Call Center Logs';
$lang['als_job_crawler'] = 'Jobs Crawler';
$lang['acs_ticket_priority_submenu'] = 'Ticket Priority';
$lang['acs_ticket_statuses_submenu'] = 'Ticket Statuses';
$lang['acs_ticket_predefined_replies_submenu'] = 'Predefined Replies';
$lang['acs_ticket_services_submenu'] = 'Services';
$lang['acs_departments'] = 'Departments';
$lang['acs_leads'] = 'Leads';
$lang['acs_leads_sources_submenu'] = 'Sources';
$lang['acs_leads_statuses_submenu'] = 'Statuses';
$lang['acs_sales_taxes_submenu'] = 'Tax Rates';
$lang['acs_sales_currencies_submenu'] = 'Currencies';
$lang['acs_sales_payment_modes_submenu'] = 'Payment Modes';
$lang['acs_email_templates'] = 'Email Templates';
$lang['acs_roles'] = 'Roles';
$lang['acs_settings'] = 'Settings';
$lang['acs_staff_list'] = 'List';
$lang['acs_staff_teams'] = 'Teams';
$lang['new_ticket'] = 'New Ticket';
$lang['tickets'] = 'Tickets';
$lang['ticket'] = 'Ticket';
$lang['ticket_lowercase'] = 'ticket';
$lang['support_tickets'] = 'Support Tickets';
$lang['support_ticket'] = 'Support Ticket';
$lang['ticket_settings_to'] = 'Name';
$lang['ticket_select_customer'] = 'Customer (ID - Name)';
$lang['ticket_settings_email'] = 'Email address';
$lang['ticket_settings_departments'] = 'Department';
$lang['ticket_settings_service'] = 'Service';
$lang['ticket_settings_priority'] = 'Priority';
$lang['ticket_settings_subject'] = 'Subject';
$lang['ticket_settings_assign_to'] = 'Assign ticket (default is current user)';
$lang['ticket_add_body'] = 'Ticket Body';
$lang['ticket_add_attachments'] = 'Attachments';
$lang['ticket_no_reply_yet'] = 'No Reply Yet';
$lang['new_ticket_added_successfully'] = 'Ticket #%s added successfully';
$lang['replied_to_ticket_successfully'] = 'Replied to ticket #%s successfully';
$lang['ticket_settings_updated_successfully'] = 'Ticket settings updated successfully';
$lang['ticket_settings_updated_successfully_and_reassigned'] = 'Ticket settings updated successfully - reassigned to department %s';
$lang['ticket_dt_subject'] = 'Subject';
$lang['ticket_dt_department'] = 'Department';
$lang['ticket_dt_service'] = 'Service';
$lang['ticket_dt_submitter'] = 'Contact';
$lang['ticket_dt_status'] = 'Status';
$lang['ticket_dt_priority'] = 'Priority';
$lang['ticket_dt_customer'] = 'Customer (ID - Name)';
$lang['ticket_dt_last_reply'] = 'Last Reply';
$lang['ticket_single_add_reply'] = 'Add Reply';
$lang['ticket_single_add_note'] = 'Add Note';
$lang['ticket_single_other_user_tickets'] = 'Other Tickets';
$lang['ticket_single_settings'] = 'Settings';
$lang['ticket_single_priority'] = 'Priority: %s';
$lang['ticket_single_last_reply'] = 'Last Reply: %s';
$lang['ticket_single_ticket_note_by'] = 'Ticket note by %s';
$lang['ticket_single_note_added'] = 'Note added: %s';
$lang['ticket_single_change_status'] = 'Change Status';
$lang['ticket_single_assign_to_me_on_update'] = 'Assign this ticket to me automatically';
$lang['ticket_single_insert_predefined_reply'] = 'Insert predefined reply';
$lang['ticket_single_insert_knowledge_base_link'] = 'Insert knowledge base link';
$lang['ticket_single_attachments'] = 'Attachments';
$lang['ticket_single_add_response'] = 'Add Response';
$lang['ticket_single_note_heading'] = 'Note';
$lang['ticket_settings_none_assigned'] = 'None';
$lang['ticket_status_changed_successfully'] = 'Ticket Status Changed';
$lang['ticket_status_changed_fail'] = 'Problem Changing Ticket Status';
$lang['ticket_staff_string'] = 'Staff';
$lang['ticket_client_string'] = 'Customer';
$lang['ticket_posted'] = 'Posted: %s';
$lang['ticket_access_by_department_denied'] = 'You do not have access to this ticket. This ticket belongs to department that you are not assigned.';
$lang['new_staff'] = 'New Staff Member';
$lang['staff_members'] = 'Staff Members';
$lang['staff_member'] = 'Staff Member';
$lang['staff_member_lowercase'] = 'staff member';
$lang['staff_profile_updated'] = 'Your Profile has Been Updated';
$lang['staff_old_password_incorrect'] = 'Your old password is incorrect';
$lang['staff_password_changed'] = 'Your password has been changed';
$lang['staff_problem_changing_password'] = 'Problem changing your password';
$lang['staff_profile_string'] = 'Profile';
$lang['staff_cant_remove_main_admin'] = 'Cant remove main administrator';
$lang['staff_cant_remove_yourself_from_admin'] = 'You cant remove yourself the administrator role';
$lang['staff_dt_name'] = 'Full Name';
$lang['staff_dt_email'] = 'Email';
$lang['staff_dt_last_Login'] = 'Last Login';
$lang['staff_dt_active'] = 'Active';
$lang['staff_add_edit_firstname'] = 'First Name';
$lang['staff_add_edit_lastname'] = 'Last Name';
$lang['staff_add_edit_email'] = 'Email';
$lang['staff_add_edit_phonenumber'] = 'Phone';
$lang['staff_add_edit_facebook'] = 'Facebook';
$lang['staff_add_edit_linkedin'] = 'LinkedIn';
$lang['staff_add_edit_skype'] = 'Skype';
$lang['staff_add_edit_departments'] = 'Member departments';
$lang['staff_add_edit_role'] = 'Role';
$lang['staff_add_edit_permissions'] = 'Permissions';
$lang['staff_add_edit_administrator'] = 'Administrator';
$lang['staff_add_edit_password'] = 'Password';
$lang['staff_add_edit_password_note'] = 'Note: if you populate this field, password will be changed on this member.';
$lang['staff_add_edit_password_last_changed'] = 'Password last changed';
$lang['staff_add_edit_notes'] = 'Notes';
$lang['staff_add_edit_ipphone'] = 'IPPhone';
$lang['staff_add_edit_note_description'] = 'Note description';
$lang['staff_notes_table_description_heading'] = 'Note';
$lang['staff_notes_table_addedfrom_heading'] = 'Added From';
$lang['staff_notes_table_dateadded_heading'] = 'Date Added';
$lang['staff_admin_profile'] = 'This is admin profile';
$lang['staff_profile_notifications'] = 'Notifications';
$lang['staff_profile_departments'] = 'Departments';
$lang['staff_edit_profile_image'] = 'Profile Image';
$lang['staff_edit_profile_your_departments'] = 'Departments';
$lang['staff_edit_profile_change_your_password'] = 'Change your password';
$lang['staff_edit_profile_change_old_password'] = 'Old password';
$lang['staff_edit_profile_change_new_password'] = 'New password';
$lang['staff_edit_profile_change_repeat_new_password'] = 'Repeat new password';
$lang['new_task'] = 'New Task';
$lang['tasks'] = 'Tasks';
$lang['task'] = 'Task';
$lang['task_lowercase'] = 'task';
$lang['comment_string'] = 'Comment';
$lang['task_marked_as_complete'] = 'Task marked as complete';
$lang['task_follower_removed'] = 'Task follower removed successfully';
$lang['task_assignee_removed'] = 'Task assignee removed successfully';
$lang['task_no_assignees'] = 'No assignees for this task';
$lang['task_no_followers'] = 'No followers for this task';
$lang['task_list_all'] = 'All';
$lang['task_list_not_assigned'] = 'Not Assigned';
$lang['task_list_duedate_passed'] = 'Due Date Passed';
$lang['tasks_dt_name'] = 'Name';
$lang['task_single_priority'] = 'Priority';
$lang['task_single_start_date'] = 'Start Date';
$lang['task_single_due_date'] = 'Due Date';
$lang['task_single_finished'] = 'Finished';
$lang['task_single_mark_as_complete'] = 'Mark as complete';
$lang['task_single_edit'] = 'Edit';
$lang['task_single_delete'] = 'Delete';
$lang['task_single_assignees'] = 'Assignees';
$lang['task_single_assignees_select_title'] = 'Assign task to';
$lang['task_single_followers'] = 'Followers';
$lang['task_single_followers_select_title'] = 'Add Followers';
$lang['task_single_add_new_comment'] = 'Add Comment';
$lang['task_add_edit_subject'] = 'Subject';
$lang['task_add_edit_priority'] = 'Priority';
$lang['task_priority_low'] = 'Low';
$lang['task_priority_medium'] = 'Medium';
$lang['task_priority_high'] = 'High';
$lang['task_priority_urgent'] = 'Urgent';
$lang['task_add_edit_start_date'] = 'Start Date';
$lang['task_add_edit_due_date'] = 'Due Date';
$lang['task_add_edit_description'] = 'Task Description';
$lang['new_tax'] = 'New Tax';
$lang['taxes'] = 'Taxes';
$lang['tax'] = 'Tax';
$lang['tax_lowercase'] = 'tax';
$lang['tax_dt_name'] = 'Tax Name';
$lang['tax_dt_rate'] = 'Rate (percent)';
$lang['tax_add_edit_name'] = 'Tax Name';
$lang['tax_add_edit_rate'] = 'Tax Rate (percent)';
$lang['tax_edit_title'] = 'Edit Tax';
$lang['tax_add_title'] = 'Add New Tax';
$lang['new_ticket_status'] = 'New Ticket Status';
$lang['ticket_statuses'] = 'Ticket Statuses';
$lang['ticket_status'] = 'Ticket Status';
$lang['ticket_status_lowercase'] = 'ticket status';
$lang['ticket_statuses_dt_name'] = 'Ticket Status Name';
$lang['no_ticket_statuses_found'] = 'No ticket statuses found';
$lang['ticket_statuses_table_total'] = 'Total %s';
$lang['ticket_status_add_edit_name'] = 'Ticket Status Name';
$lang['ticket_status_add_edit_color'] = 'Pick Color';
$lang['ticket_status_add_edit_order'] = 'Status Order';
$lang['new_todo'] = 'New To Do';
$lang['my_todos'] = 'My To Do Items';
$lang['todo'] = 'Todo Item';
$lang['todo_lowercase'] = 'todo';
$lang['todo_status_changed'] = 'Todo Status Changed';
$lang['todo_add_title'] = 'Add New Todo';
$lang['add_new_todo_description'] = 'Description';
$lang['no_finished_todos_found'] = 'No finished todos found';
$lang['no_unfinished_todos_found'] = 'No todos found';
$lang['unfinished_todos_title'] = 'Unfinished to do\'s';
$lang['finished_todos_title'] = 'Latest finished to do\'s';
$lang['utility_activity_log'] = 'Activity Log';
$lang['utility_activity_log_filter_by_date'] = 'Filter by date';
$lang['utility_activity_log_dt_description'] = 'Description';
$lang['utility_activity_log_dt_date'] = 'Date';
$lang['utility_activity_log_dt_customer'] = 'Customer';
$lang['utility_activity_log_dt_staff'] = 'Staff';
$lang['utility_activity_log_import_history'] = 'Import history';
$lang['utility_activity_log_import_file'] = 'Import file';
$lang['utility_calendar_new_event_title'] = 'Add new event';
$lang['utility_calendar_new_event_start_date'] = 'Start Date';
$lang['utility_calendar_new_event_end_date'] = 'End Date';
$lang['utility_calendar_new_event_make_public'] = 'Public Event';
$lang['utility_calendar_event_added_successfully'] = 'New event added successfully';
$lang['utility_calendar_event_deleted_successfully'] = 'Event deleted';
$lang['utility_calendar_new_event_placeholder'] = 'Event title';
$lang['nav_notifications'] = 'Notifications';
$lang['nav_my_profile'] = 'My Profile';
$lang['nav_edit_profile'] = 'Edit Profile';
$lang['nav_logout'] = 'Logout';
$lang['nav_no_notifications'] = 'No notifications found';
$lang['nav_view_all_notifications'] = 'View all notifications';
$lang['nav_notifications_tooltip'] = 'View Notifications';
$lang['clients_copyright'] = 'Copyright %s';
$lang['clients_contracts'] = 'Contracts';
$lang['clients_contracts_dt_subject'] = 'Subject';
$lang['clients_contracts_dt_start_date'] = 'Start Date';
$lang['clients_contracts_dt_end_date'] = 'End Date';
$lang['clients_quick_invoice_info'] = 'Quick Invoices Info';
$lang['clients_home_currency_select_tooltip'] = 'You need to select currency because you have invoices with different currency';
$lang['clients_invoice_html_btn_download'] = 'Download';
$lang['clients_my_invoices'] = 'Invoices';
$lang['clients_invoice_dt_number'] = 'PO #';
$lang['clients_invoice_dt_date'] = 'Date';
$lang['clients_invoice_dt_duedate'] = 'Due Date';
$lang['clients_invoice_dt_amount'] = 'Amount';
$lang['clients_invoice_dt_status'] = 'Status';
$lang['clients_profile_heading'] = 'Profile';
$lang['clients_firstname'] = 'First Name';
$lang['clients_lastname'] = 'Last Name';
$lang['clients_email'] = 'Email Address';
$lang['clients_company'] = 'Company';
$lang['clients_vat'] = 'VAT Number';
$lang['clients_phone'] = 'Phone';
$lang['clients_country'] = 'Country';
$lang['clients_city'] = 'City';
$lang['clients_address'] = 'Address';
$lang['clients_zip'] = 'Zip Code';
$lang['clients_state'] = 'State';
$lang['clients_register_password'] = 'Password';
$lang['clients_register_password_repeat'] = 'Repeat Password';
$lang['clients_edit_profile_update_btn'] = 'Update';
$lang['clients_edit_profile_change_password_heading'] = 'Change Password';
$lang['clients_edit_profile_old_password'] = 'Old Password';
$lang['clients_edit_profile_new_password'] = 'New Password';
$lang['clients_edit_profile_new_password_repeat'] = 'Repeat Password';
$lang['clients_edit_profile_change_password_btn'] = 'Change Password';
$lang['clients_profile_last_changed_password'] = 'Password last changed %s';
$lang['clients_knowledge_base'] = 'Knowledge Base';
$lang['clients_knowledge_base_articles_not_found'] = 'No knowledge base articles found';
$lang['clients_knowledge_base_find_useful'] = 'Did you find this article useful?';
$lang['clients_knowledge_base_find_useful_yes'] = 'Yes';
$lang['clients_knowledge_base_find_useful_no'] = 'No';
$lang['clients_article_only_1_vote_today'] = 'You can vote once in 24 hours';
$lang['clients_article_voted_thanks_for_feedback'] = 'Thanks for your feedback';
$lang['clients_ticket_open_subject'] = 'Open Ticket';
$lang['clients_ticket_open_departments'] = 'Department';
$lang['clients_tickets_heading'] = 'Support Tickets';
$lang['clients_ticket_open_service'] = 'Service';
$lang['clients_ticket_open_priority'] = 'Priority';
$lang['clients_ticket_open_body'] = 'Ticket Body';
$lang['clients_ticket_attachments'] = 'Attachments';
$lang['clients_single_ticket_string'] = 'Ticket';
$lang['clients_single_ticket_replied'] = 'Replied: %s';
$lang['clients_single_ticket_information_heading'] = 'Ticket Information';
$lang['clients_tickets_dt_number'] = 'Ticket #';
$lang['clients_tickets_dt_subject'] = 'Subject';
$lang['clients_tickets_dt_department'] = 'Department';
$lang['clients_tickets_dt_service'] = 'Service';
$lang['clients_tickets_dt_status'] = 'Status';
$lang['clients_tickets_dt_last_reply'] = 'Last Reply';
$lang['clients_ticket_single_department'] = 'Department: %s';
$lang['clients_ticket_single_submitted'] = 'Submitted: %s';
$lang['clients_ticket_single_status'] = 'Status:';
$lang['clients_ticket_single_priority'] = 'Priority: %s';
$lang['clients_ticket_single_add_reply_btn'] = 'Add Reply';
$lang['clients_ticket_single_add_reply_heading'] = 'Add reply to this ticket';
$lang['clients_login_heading_no_register'] = 'Please login';
$lang['clients_login_heading_register'] = 'Please login or register';
$lang['clients_login_email'] = 'Email Address';
$lang['clients_login_password'] = 'Password';
$lang['clients_login_remember'] = 'Remember me';
$lang['clients_login_login_string'] = 'Login';
$lang['clients_register_string'] = 'Register';
$lang['clients_register_heading'] = 'Register';
$lang['clients_nav_login'] = 'Login';
$lang['clients_nav_register'] = 'Register';
$lang['clients_nav_invoices'] = 'Invoices';
$lang['clients_nav_contracts'] = 'Contracts';
$lang['clients_nav_kb'] = 'Knowledge Base';
$lang['clients_nav_profile'] = 'Profile';
$lang['clients_nav_logout'] = 'Logout';
$lang['payment_receipt'] = 'Payment Receipt';
$lang['payment_for_string'] = 'Payment For';
$lang['payment_date'] = 'Payment Date:';
$lang['payment_view_mode'] = 'Payment Mode:';
$lang['payment_total_amount'] = 'Total Amount';
$lang['payment_table_invoice_number'] = 'Invoice Number';
$lang['payment_table_invoice_date'] = 'Invoice Date';
$lang['payment_table_invoice_amount_total'] = 'Invoice Amount';
$lang['payment_table_payment_amount_total'] = 'Payment Amount';
$lang['payments_table_transaction_id'] = 'Transaction ID: %s';
$lang['payment_getaway_token_not_found'] = 'Token Not Found';
$lang['online_payment_recorded_success'] = 'Payment recorded successfully';
$lang['online_payment_recorded_success_fail_database'] = 'Payment is successful but failed to insert payment to database, contact administrator';
$lang['lead_convert_to_client'] = 'Convert to customer';
$lang['lead_convert_to_email'] = 'Email';
$lang['lead_convert_to_client_firstname'] = 'First Name';
$lang['lead_convert_to_client_lastname'] = 'Last Name';
$lang['lead_email_already_exists'] = 'Lead email already exists in customers data';
$lang['lead_to_client_base_converted_success'] = 'Lead converted to customer successfully';
$lang['lead_have_client_profile'] = 'This lead have customer profile.';
$lang['lead_converted_edit_client_profile'] = 'Edit Profile';
$lang['view_invoice_as_customer_tooltip'] = 'View invoice as customer';
$lang['invoice_add_edit_recurring'] = 'Recurring Invoice?';
$lang['invoice_add_edit_recurring_no'] = 'No';
$lang['invoice_add_edit_recurring_month'] = 'Every %s month';
$lang['invoice_add_edit_recurring_months'] = 'Every %s months';
$lang['invoices_list_all'] = 'All';
$lang['invoices_list_not_have_payment'] = 'Invoices with no payment records';
$lang['invoices_list_recurring'] = 'Recurring PO';
$lang['invoices_list_made_payment_by'] = 'Made Payment by %s';
$lang['invoices_create_invoice_from_recurring_only_on_paid_invoices'] = 'Create new invoice from recurring invoice only if the invoice is with status paid?';
$lang['invoices_create_invoice_from_recurring_only_on_paid_invoices_tooltip'] = 'If this field is set to YES and the recurring invoices is not with status PAID, the new invoice will NOT be created.';
$lang['view_invoice_pdf_link_pay'] = 'Pay Invoice';
$lang['payment_mode_add_edit_description'] = 'Bank Accounts / Description';
$lang['payment_mode_add_edit_description_tooltip'] = 'You can set here bank accounts information. Will be shown on HTML Invoice';
$lang['payment_modes_dt_description'] = 'Bank Accounts / Description';
$lang['payment_modes_add_edit_announcement'] = 'Note: Payment modes listed below are offline modes. Payment gateways can be configured in Setup-> Settings->Payment Gateways';
$lang['payment_mode_add_edit_active'] = 'Active';
$lang['payment_modes_dt_active'] = 'Active';
$lang['contract_not_found'] = 'Contract not found. Maybe is deleted, check activity log';
$lang['setting_bar_heading'] = 'Setup';
$lang['settings_group_online_payment_modes'] = 'Payment Gateways';
$lang['settings_paymentmethod_mode_label'] = 'Label';
$lang['settings_paymentmethod_active'] = 'Active';
$lang['settings_paymentmethod_currencies'] = 'Currencies (coma separated)';
$lang['settings_paymentmethod_testing_mode'] = 'Enable Test Mode';
$lang['settings_paymentmethod_paypal_username'] = 'PayPal API Username';
$lang['settings_paymentmethod_paypal_password'] = 'PayPal API Password';
$lang['settings_paymentmethod_paypal_signature'] = 'API Signature';
$lang['settings_paymentmethod_stripe_api_secret_key'] = 'Stripe API Secret Key';
$lang['settings_paymentmethod_stripe_api_publishable_key'] = 'Stripe Publishable Key';
$lang['settings_limit_top_search_bar_results'] = 'Limit Top Search Bar Results to';
$lang['client_phonenumber'] = 'Company phone number';
$lang['clients_register'] = 'Register';
$lang['clients_profile_updated'] = 'Your profile has been updated';
$lang['clients_successfully_registered'] = 'Thank your for registering';
$lang['clients_account_created_but_not_logged_in'] = 'Your account has been created but you are not logged in our system automatically. Please try to login';
$lang['payment_for_invoice'] = 'Payment for Invoice';
$lang['payment_total'] = 'Total: %s';
$lang['invoice_html_online_payment'] = 'Online Payment';
$lang['invoice_html_online_payment_button_text'] = 'Pay Now';
$lang['invoice_html_payment_modes_not_selected'] = 'Please Select Payment Mode';
$lang['invoice_html_amount_blank'] = 'Total amount cant be blank or zero';
$lang['invoice_html_offline_payment'] = 'Offline Payment';
$lang['invoice_html_amount'] = 'Amount';
$lang['dt_button_column_visibility'] = 'Visibility';
$lang['dt_button_reload'] = 'Reload';
$lang['dt_button_excel'] = 'Excel';
$lang['dt_button_csv'] = 'CSV';
$lang['dt_button_pdf'] = 'PDF';
$lang['dt_button_print'] = 'Print';
$lang['is_not_active_export'] = 'No';
$lang['is_active_export'] = 'Yes';
$lang['invoice_add_edit_advanced_options'] = 'Advanced Options';
$lang['invoice_add_edit_allowed_payment_modes'] = 'Allowed payment modes for this PO';
$lang['invoice_add_edit_recurring_invoices_from_invoice'] = 'Created invoices from this recurring invoice';
$lang['invoice_add_edit_no_payment_modes_found'] = 'No payment modes found.';
$lang['invoice_html_total_pay'] = 'Total: %s';
$lang['email_templates_table_heading_name'] = 'Template Name';
$lang['discount_type'] = 'Discount Type';
$lang['discount_type_after_tax'] = 'After Tax';
$lang['discount_type_before_tax'] = 'Before Tax';
$lang['terms_and_conditions'] = 'Terms & Conditions';
$lang['reference_no'] = 'Reference #';
$lang['no_discount'] = 'No discount';
$lang['view_stats_tooltip'] = 'View Quick Stats';
$lang['zip_from_date'] = 'From Date:';
$lang['zip_to_date'] = 'To Date:';
$lang['client_zip_payments'] = 'ZIP Payments';
$lang['client_zip_invoices'] = 'ZIP Invoices';
$lang['client_zip_estimates'] = 'ZIP Estimates';
$lang['client_zip_status'] = 'Status';
$lang['client_zip_status_all'] = 'All';
$lang['client_zip_payment_modes'] = 'Payment made by';
$lang['client_zip_no_data_found'] = 'No %s found';
$lang['payment_view_heading'] = 'Payment';
$lang['settings_allow_payment_amount_to_be_modified'] = 'Allow customer to modify the amount to pay (for online payments)';
$lang['settings_delete_only_on_last_invoice'] = 'Delete invoice allowed only on last invoice';
$lang['settings_sales_estimate_prefix'] = 'Estimate Number Prefix';
$lang['settings_sales_next_estimate_number'] = 'Next estimate Number';
$lang['settings_sales_next_estimate_number_tooltip'] = 'Set this field to 1 if you want to start from beginning';
$lang['settings_sales_decrement_estimate_number_on_delete'] = 'Decrement estimate number on delete';
$lang['settings_sales_decrement_estimate_number_on_delete_tooltip'] = 'Do you want to decrement the estimate number when the last estimate is deleted? eq. If is set this option to YES and before estimate delete the next estimate number is 15 the next estimate number will decrement to 14.If is set to NO the number will remain to 15. If you have setup delete only on last estimate to NO you should set this option to NO too to keep the next estimate number not decremented.';
$lang['settings_sales_estimate_number_format'] = 'Estimate Number Format';
$lang['settings_sales_estimate_number_format_year_based'] = 'Year Based';
$lang['settings_sales_estimate_number_format_number_based'] = 'Number Based (000001)';
$lang['settings_delete_only_on_last_estimate'] = 'Delete estimate allowed only on last invoice';
$lang['settings_send_test_email_heading'] = 'Send Test Email';
$lang['settings_send_test_email_subheading'] = 'Send test email to make sure that your SMTP settings is set correctly.';
$lang['settings_send_test_email_string'] = 'Email Address';
$lang['settings_smtp_settings_heading'] = 'SMTP Settings';
$lang['settings_smtp_settings_subheading'] = 'Setup main email';
$lang['settings_sales_heading_general'] = 'General';
$lang['settings_sales_heading_invoice'] = 'Invoice';
$lang['settings_sales_heading_estimates'] = 'Estimates';
$lang['settings_sales_cron_invoice_heading'] = 'Invoice';
$lang['tasks_dt_datestart'] = 'Start Date';
$lang['invoice_discount'] = 'Addition Discount';
$lang['settings_rtl_support_admin'] = 'RTL Admin Area (Right to Left)';
$lang['settings_rtl_support_client'] = 'RTL Customers Area (Right to Left)';
$lang['settings_estimate_auto_convert_to_invoice_on_client_accept'] = 'Auto convert the estimate to invoice after client accept';
$lang['settings_exclude_estimate_from_client_area_with_draft_status'] = 'Exclude estimates with draft status from customers area';
$lang['January'] = 'January';
$lang['February'] = 'February';
$lang['March'] = 'March';
$lang['April'] = 'April';
$lang['May'] = 'May';
$lang['June'] = 'June';
$lang['July'] = 'July';
$lang['August'] = 'August';
$lang['September'] = 'September';
$lang['October'] = 'October';
$lang['November'] = 'November';
$lang['December'] = 'December';
$lang['time_ago_just_now'] = 'Just now';
$lang['time_ago_minute'] = 'one minute ago';
$lang['time_ago_minutes'] = '%s minutes ago';
$lang['time_ago_hour'] = 'an hour ago';
$lang['time_ago_hours'] = '%s hrs ago';
$lang['time_ago_yesterday'] = 'yesterday';
$lang['time_ago_days'] = '%s days ago';
$lang['time_ago_week'] = 'a week ago';
$lang['time_ago_weeks'] = '%s weeks ago';
$lang['time_ago_month'] = 'a month ago';
$lang['time_ago_months'] = '%s months ago';
$lang['time_ago_year'] = 'one year ago';
$lang['time_ago_years'] = '%s years ago';
$lang['estimates'] = 'Estimates';
$lang['estimate'] = 'Estimate';
$lang['estimate_lowercase'] = 'estimate';
$lang['create_new_estimate'] = 'Create New Estimate';
$lang['view_estimate'] = 'View estimate';
$lang['estimate_sent_to_client_success'] = 'The estimate is sent successfully to the client';
$lang['estimate_sent_to_client_fail'] = 'Problem while sending the estimate';
$lang['estimate_view'] = 'View estimate';
$lang['estimate_create_option'] = 'Create option';
$lang['estimate_create_option_success_message'] = 'A new option has been created and successfully linked to the Estimate with ID %s';
$lang['estimate_create_option_fail_message'] = 'Failed to create option into Estimate %s';
$lang['estimate_make_primary'] = 'Make as primary';
$lang['estimate_primary'] = 'Primary';
$lang['estimate_make_primary_confirm_message'] = 'Are you sure you want to mark this option as primary ?';
$lang['estimate_make_primary_success_message'] = 'Option has been successfully marked as primary';
$lang['estimate_make_primary_fail_message'] = 'Failed to mark as primary option';
$lang['estimate_delete_option'] = 'Delete option';
$lang['estimate_delete_option_confirm_message'] = 'You are deleting the option from the estimate with ID %s. Are you sure you want to delete this option ?';
$lang['estimate_delete_option_success_message'] = 'You have successfully deleted the option in the estimate with ID %s';
$lang['estimate_delete_option_fail_message'] = 'Failed to delete the option in the estimate with ID %s';
$lang['estimate_select_customer'] = 'Customer';
$lang['estimate_add_edit_number'] = 'Estimate Number';
$lang['estimate_add_edit_date'] = 'Estimate Date';
$lang['estimate_add_edit_expirydate'] = 'Expiry Date';
$lang['estimate_add_edit_currency'] = 'Currency';
$lang['estimate_add_edit_client_note'] = 'Client Note';
$lang['estimate_add_edit_admin_note'] = 'Admin Note';
$lang['estimates_toggle_table_tooltip'] = 'Toggle Table';
$lang['estimate_add_edit_advanced_options'] = 'Advanced Options';
$lang['estimate_to'] = 'To';
$lang['estimates_list_all'] = 'All';
$lang['estimate_invoiced_date'] = 'Estimate Invoiced on %s';
$lang['edit_estimate_tooltip'] = 'Edit Estimate';
$lang['delete_estimate_tooltip'] = 'Delete Estimate';
$lang['estimate_sent_to_email_tooltip'] = 'Send to Email';
$lang['estimate_already_send_to_client_tooltip'] = 'This estimate is already sent to the client %s';
$lang['estimate_view_activity_tooltip'] = 'Activity Log';
$lang['estimate_send_to_client_modal_heading'] = 'Send estimate to client';
$lang['estimate_send_to_client_attach_pdf'] = 'Attach estimate PDF';
$lang['estimate_send_to_client_preview_template'] = 'Preview Email Template';
$lang['estimate_dt_table_heading_number'] = 'Estimate #';
$lang['estimate_dt_table_heading_date'] = 'Date';
$lang['estimate_dt_table_heading_id_customer'] = 'ID Customer';
$lang['estimate_dt_table_heading_client'] = 'Customer';
$lang['estimate_dt_table_heading_expirydate'] = 'Expiry Date';
$lang['estimate_dt_table_heading_date_flow_up'] = 'Ngày cần follow up';
$lang['estimate_dt_table_heading_amount'] = 'Amount';
$lang['estimate_dt_table_heading_total_amount'] = 'Total amount (No tax)';
$lang['estimate_dt_table_heading_customer_admin'] = 'Customer Admin';
$lang['estimate_dt_table_heading_recent_invoice'] = 'Recent Invoice';
$lang['estimate_dt_table_heading_status'] = 'Status';
$lang['estimate_convert_to_invoice'] = 'Convert to PO';
$lang['client_payments_tab'] = 'Payments';
$lang['estimate_pdf_heading'] = 'ESTIMATE';
$lang['estimate_table_item_heading'] = 'Item';
$lang['estimate_table_quantity_heading'] = 'Qty';
$lang['estimate_table_rate_heading'] = 'Rate';
$lang['estimate_table_discount_table_heading'] = 'Discount';
$lang['estimate_table_tax_heading'] = 'Tax';
$lang['estimate_table_amount_heading'] = 'Amount';
$lang['estimate_subtotal'] = 'Sub Total';
$lang['estimate_adjustment'] = 'Adjustment';
$lang['estimate_discount'] = 'Addition Discount';
$lang['estimate_discount_fixed'] = 'Voucher';
$lang['estimate_total'] = 'Total';
$lang['estimate_data_date'] = 'Estimate Date';
$lang['estimate_data_expiry_date'] = 'Expiry Date';
$lang['estimate_note'] = 'Note:';
$lang['estimate_status_draft'] = 'Draft';
$lang['estimate_status_sent'] = 'Sent';
$lang['estimate_status_declined'] = 'Declined';
$lang['estimate_status_accepted'] = 'Accepted';
$lang['estimate_status_expired'] = 'Expired';
$lang['estimate_declined_reason'] = 'Reject Reason';
$lang['estimate_reason_for_declined'] = 'Description';
$lang['clients_estimate_dt_number'] = 'Estimate #';
$lang['clients_estimate_dt_date'] = 'Date';
$lang['clients_estimate_dt_duedate'] = 'Expiry Date';
$lang['clients_estimate_dt_amount'] = 'Amount';
$lang['clients_estimate_dt_status'] = 'Status';
$lang['clients_nav_estimates'] = 'Estimates';
$lang['clients_decline_estimate'] = 'Decline';
$lang['clients_accept_estimate'] = 'Accept';
$lang['clients_my_estimates'] = 'Estimates';
$lang['clients_estimate_invoiced_successfully'] = 'Thank you for accepting the estimate. Please review the created invoice for the estimate';
$lang['clients_estimate_accepted_not_invoiced'] = 'Thank you for accepting this estimate';
$lang['clients_estimate_declined'] = 'Estimate declined. You can accept the estimate any time before expiry date';
$lang['clients_estimate_failed_action'] = 'Failed to take action on this estimate';
$lang['client_add_edit_profile'] = 'Profile';
$lang['custom_field'] = 'Custom field';
$lang['custom_field_lowercase'] = 'custom field';
$lang['custom_fields'] = 'Custom Fields';
$lang['new_custom_field'] = 'New Custom Field';
$lang['custom_field_name'] = 'Field Name';
$lang['custom_field_add_edit_type'] = 'Type';
$lang['custom_field_add_edit_belongs_top'] = 'Field Belongs to';
$lang['custom_field_add_edit_options'] = 'Options';
$lang['custom_field_add_edit_options_tooltip'] = 'Only use for Select, Checkbox types. Populate the field by separating the options by coma. eq. apple,orange,banana';
$lang['custom_field_add_edit_order'] = 'Order';
$lang['custom_field_dt_field_to'] = 'Belongs to';
$lang['custom_field_dt_field_name'] = 'Name';
$lang['custom_field_dt_field_type'] = 'Type';
$lang['custom_field_add_edit_active'] = 'Active';
$lang['custom_field_add_edit_disabled'] = 'Disabled';
$lang['ticket_reply'] = 'Ticket Reply';
$lang['asc_custom_fields'] = 'Custom Fields';
$lang['contract_types'] = 'Contracts Types';
$lang['contract_type'] = 'Contract type';
$lang['new_contract_type'] = 'New Contract Type';
$lang['contract_type_lowercase'] = 'contract';
$lang['contract_type_name'] = 'Name';
$lang['contract_types_list_name'] = 'Contract Type';
$lang['acs_contracts'] = 'Contracts';
$lang['acs_contract_types'] = 'Contract Types';
$lang['invoice_item_long_description'] = 'Long Description';
$lang['clients_list_phone'] = 'Phone';
$lang['client_expenses_tab'] = 'Expenses';
$lang['customers_summary'] = 'Customers Summary';
$lang['customers_summary_active'] = 'Active Contacts';
$lang['customers_summary_inactive'] = 'Inactive Contacts';
$lang['customers_summary_logged_in_today'] = 'Contacts Logged In Today';
$lang['contacted'] = 'Contacted';
$lang['not_contacted'] = 'Not contacted';
$lang['have_invoice'] = 'Have invoice';
$lang['no_invoice'] = 'No invoice';
$lang['admin_auth_forgot_password_email'] = 'Email Address';
$lang['admin_auth_forgot_password_heading'] = 'Forgot Password';
$lang['admin_auth_login_heading'] = 'Login';
$lang['admin_auth_login_email'] = 'Email Address';
$lang['admin_auth_login_password'] = 'Password';
$lang['admin_auth_login_remember_me'] = 'Remember me';
$lang['admin_auth_login_button'] = 'Login';
$lang['admin_auth_login_fp'] = 'Forgot Password?';
$lang['admin_auth_reset_password_heading'] = 'Reset Password';
$lang['admin_auth_reset_password'] = 'Password';
$lang['admin_auth_reset_password_repeat'] = 'Repeat Password';
$lang['admin_auth_invalid_email_or_password'] = 'Invalid email or password';
$lang['admin_auth_inactive_account'] = 'Inactive Account';
$lang['calendar_estimate'] = 'Estimate';
$lang['calendar_invoice'] = 'Invoice';
$lang['calendar_contract'] = 'Contract';
$lang['calendar_customer_reminder'] = 'Client Reminder';
$lang['calendar_event'] = 'Event';
$lang['calendar_task'] = 'Task';
$lang['lead_edit_delete_tooltip'] = 'Delete Lead';
$lang['lead_attachments'] = 'Attachments';
$lang['acs_finance'] = 'Finance';
$lang['settings_show_sale_agent_on_invoices'] = 'Show Sale Agent On Invoice';
$lang['settings_show_sale_agent_on_estimates'] = 'Show Sale Agent On Estimate';
$lang['settings_predefined_predefined_term'] = 'Predefined Terms & Conditions';
$lang['settings_predefined_clientnote'] = 'Predefined Client Note';
$lang['settings_custom_pdf_logo_image_url'] = 'Custom PDF Company Logo URL';
$lang['settings_custom_pdf_logo_image_url_tooltip'] = 'Probably you will have problems with PNG images with transparency that are handled in different way depending on the php-imagick or php-gd version used. Try to update php-imagick and disable php-gd ◀
. If you leave this field blank the uploaded logo will be used.';
$lang['sale_agent_string'] = 'Sale Agent';
$lang['amount_display_in_base_currency'] = 'Amount is displayed in your base currency - Only use this report if you are using 1 currency for payments and expenses.';
$lang['leads_summary'] = 'Leads Summary';
$lang['contract_value'] = 'Contract Value';
$lang['contract_trash'] = 'Trash';
$lang['contracts_view_trash'] = 'View Trash';
$lang['contracts_view_all'] = 'All';
$lang['contracts_view_exclude_trashed'] = 'Exclude Trashed Contracts';
$lang['contract_value_tooltip'] = 'Base currency will be used.';
$lang['contract_trash_tooltip'] = 'If you add contract to trash, won\'t be shown on client side, won\'t be included in chart and other stats and also by default won\'t be shown when you will list all contracts.';
$lang['contract_summary_active'] = 'Active';
$lang['contract_renew_heading'] = 'Renew Contract';
$lang['contract_summary_heading'] = 'Contract Summary';
$lang['contract_summary_expired'] = 'Expired';
$lang['contract_summary_about_to_expire'] = 'About to Expire';
$lang['contract_summary_recently_added'] = 'Recently Added';
$lang['contract_summary_trash'] = 'Trash';
$lang['contract_summary_by_type'] = 'Contracts by Type';
$lang['contract_summary_by_type_value'] = 'Contracts Value by Type';
$lang['contract_renewed_successfully'] = 'Contract renewed successfully';
$lang['contract_renewed_fail'] = 'Problem while renewing the contract. Contact administrator';
$lang['no_contract_renewals_found'] = 'Renewals for this contract are not found';
$lang['no_contract_renewals_history_heading'] = 'Contract Renewal History';
$lang['contract_renewed_by'] = '%s renewed this contract';
$lang['contract_renewal_deleted'] = 'Renewal successfully deleted';
$lang['contract_renewal_delete_fail'] = 'Failed to delete contract renewal. Contact administrator';
$lang['contract_renewal_new_value'] = 'New Contract Value: %s';
$lang['contract_renewal_old_value'] = 'Old Contract Value: %s';
$lang['contract_renewal_new_start_date'] = 'New Start Date: %s';
$lang['contract_renewal_old_start_date'] = 'Old Contract Start Date was: %s';
$lang['contract_renewal_new_end_date'] = 'New End Date: %s';
$lang['contract_renewal_old_end_date'] = 'Old Contract End Date was: %s';
$lang['contract_attachment'] = 'Attachment';
$lang['als_expenses'] = 'Expenses';
$lang['als_reports_expenses'] = 'Expenses';
$lang['als_expenses_vs_income'] = 'Expenses vs Income';
$lang['invoice_attach_file'] = 'Attach File';
$lang['invoice_mark_as_sent'] = 'Mark as Sent';
$lang['invoice_marked_as_sent'] = 'Invoice marked as sent successfully';
$lang['invoice_marked_as_sent_failed'] = 'Failed to mark invoice as sent';
$lang['payment_transaction_id'] = 'Transaction ID';
$lang['acs_expense_categories'] = 'Expenses Categories';
$lang['expense_category'] = 'Expense Category';
$lang['expense_category_lowercase'] = 'expense category';
$lang['new_expense'] = 'Record Expense';
$lang['expense_add_edit_name'] = 'Category Name';
$lang['expense_add_edit_description'] = 'Category Description';
$lang['expense_categories'] = 'Expense Categories';
$lang['new_expense_category'] = 'New Category';
$lang['dt_expense_description'] = 'Description';
$lang['expense'] = 'Expense';
$lang['expenses'] = 'Expenses';
$lang['expense_lowercase'] = 'expense';
$lang['expense_add_edit_customer'] = 'Customer';
$lang['expense_add_edit_note'] = 'Note';
$lang['expense_add_edit_date'] = 'Expense Date';
$lang['expense_add_edit_amount'] = 'Amount';
$lang['expense_add_edit_billable'] = 'Billable';
$lang['expense_add_edit_attach_receipt'] = 'Attach Receipt';
$lang['expense_add_edit_reference_no'] = 'Reference #';
$lang['expense_receipt'] = 'Expense Receipt';
$lang['expense_receipt_lowercase'] = 'expense receipt';
$lang['expense_dt_table_heading_category'] = 'Category';
$lang['expense_dt_table_heading_amount'] = 'Amount';
$lang['expense_dt_table_heading_date'] = 'Date';
$lang['expense_dt_table_heading_reference_no'] = 'Reference #';
$lang['expense_dt_table_heading_customer'] = 'Customer';
$lang['expense_dt_table_heading_payment_mode'] = 'Payment Mode';
$lang['expense_converted_to_invoice'] = 'Expense successfully converted to invoice';
$lang['expense_converted_to_invoice_fail'] = 'Failed to convert this expense to invoice check error log.';
$lang['expense_copy_success'] = 'The expense is copied successfully.';
$lang['expense_copy_fail'] = 'Failed to copy expense. Please check the required fields and try again';
$lang['expenses_list_all'] = 'All';
$lang['expenses_list_billable'] = 'Billable';
$lang['expenses_list_non_billable'] = 'Non Billable';
$lang['expenses_list_invoiced'] = 'Invoiced';
$lang['expenses_list_unbilled'] = 'Not Invoiced';
$lang['expenses_list_recurring'] = 'Recurring';
$lang['expense_invoice_delete_not_allowed'] = 'You cant delete this expense. The expense is already invoiced.';
$lang['expense_convert_to_invoice'] = 'Convert To Invoice';
$lang['expense_edit'] = 'Edit Expense';
$lang['expense_delete'] = 'Delete';
$lang['expense_copy'] = 'Copy';
$lang['expense_invoice_not_created'] = 'Invoice Not Created';
$lang['expense_billed'] = 'Billed';
$lang['expense_not_billed'] = 'Invoice Not Paid';
$lang['expense_customer'] = 'Customer';
$lang['expense_note'] = 'Note:';
$lang['expense_date'] = 'Date:';
$lang['expense_ref_noe'] = 'Ref #:';
$lang['expense_amount'] = 'Amount:';
$lang['expense_recurring_indicator'] = 'Recurring';
$lang['expense_already_invoiced'] = 'This expense is already invoiced';
$lang['expense_recurring_auto_create_invoice'] = 'Auto Create Invoice';
$lang['expense_recurring_send_custom_on_renew'] = 'Send the invoice to customer email when expense re-created';
$lang['expense_recurring_autocreate_invoice_tooltip'] = 'If this option is checked the invoice for the customer will be auto created when the expense will be renewed.';
$lang['expenses_yearly_by_categories'] = 'Expenses Yearly by Categories';
$lang['total_expenses_for'] = 'Total Expenses for';
$lang['expenses_report_for'] = 'Expenses for';
$lang['custom_field_required'] = 'Required';
$lang['custom_field_show_on_pdf'] = 'Show on PDF';
$lang['custom_field_leads'] = 'Leads';
$lang['custom_field_customers'] = 'Customers';
$lang['custom_field_staff'] = 'Staff';
$lang['custom_field_contracts'] = 'Contracts';
$lang['custom_field_tasks'] = 'Tasks';
$lang['custom_field_expenses'] = 'Expenses';
$lang['custom_field_invoice'] = 'Invoice';
$lang['custom_field_estimate'] = 'Estimate';
$lang['ticket_single_private_staff_notes'] = 'Private Staff Notes';
$lang['business_news'] = 'Business News';
$lang['nav_todo_items'] = 'Todo items';
$lang['clients_contracts_type'] = 'Contract Type';
$lang['no_tax'] = 'No Tax';
$lang['numbers_not_formatted_while_editing'] = 'The number in the input field is not formatted while edit/add item and should remain not formatted do not try to format it manually in here.';
$lang['contracts_view_expired'] = 'Expired';
$lang['contracts_view_without_dateend'] = 'Contracts Without Date End';
$lang['email_template_contracts_fields_heading'] = 'Contracts';
$lang['invoice_estimate_general_options'] = 'General Options';
$lang['invoice_table_item_description'] = 'Description';
$lang['invoice_recurring_indicator'] = 'Recurring';
$lang['estimate_convert_to_invoice_successfully'] = 'Estimate converted to invoice successfully';
$lang['estimate_table_item_description'] = 'Description';
$lang['cant_delete_base_currency'] = 'You cant delete the base currency. You need to assign new base currency then perform delete.';
$lang['invoice_copy'] = 'Copy Invoice';
$lang['invoice_copy_success'] = 'Invoice copied successfully';
$lang['invoice_copy_fail'] = 'Failed to copy invoice';
$lang['invoice_due_after_help'] = 'Set zero to avoid calculation';
$lang['show_shipping_on_invoice'] = 'Show shipping details in invoice';
$lang['show_shipping_on_estimate'] = 'Show shipping details in estimate';
$lang['is_invoiced_estimate_delete_error'] = 'This estimate is invoiced. You cant delete the estimate';
$lang['ship_to'] = 'Ship to';
$lang['customer_profile_details'] = 'Customer Details';
$lang['billing_shipping'] = 'Billing & Shipping';
$lang['billing_address'] = 'Billing Address';
$lang['shipping_address'] = 'Shipping Address';
$lang['billing_street'] = 'Street';
$lang['billing_city'] = 'City';
$lang['billing_state'] = 'State';
$lang['billing_zip'] = 'Zip Code';
$lang['billing_country'] = 'Country';
$lang['shipping_street'] = 'Street';
$lang['shipping_city'] = 'City';
$lang['shipping_state'] = 'State';
$lang['shipping_zip'] = 'Zip Code';
$lang['shipping_country'] = 'Country';
$lang['get_shipping_from_customer_profile'] = 'Get shipping details from customer profile';
$lang['customer_default_currency'] = 'Default Currency';
$lang['customer_update_address_info_on_invoices'] = 'Update the shipping/billing info on all previous invoices/estimates';
$lang['customer_update_address_info_on_invoices_help'] = 'If you check this field shipping and billing info will be updated to all invoices and estimates. Note: Invoices with status paid won\'t be affected.';
$lang['setup_google_api_key_customer_map'] = 'Setup google api key in order to view to customer map';
$lang['customer_attachments_file'] = 'File';
$lang['client_send_set_password_email'] = 'Send SET password email';
$lang['customer_billing_same_as_profile'] = 'Same as Customer Info';
$lang['customer_billing_copy'] = 'Copy Billing Address';
$lang['customer_map'] = 'Map';
$lang['set_password_email_sent_to_client'] = 'Email to set password is successfully sent to contact';
$lang['set_password_email_sent_to_client_and_profile_updated'] = 'Profile updated and email to set password is successfully sent to contact';
$lang['customer_attachments'] = 'Files';
$lang['customer_longitude'] = 'Longitude (Google Maps)';
$lang['customer_latitude'] = 'Latitude (Google Maps)';
$lang['history'] = 'History';
$lang['admin_auth_set_password'] = 'Password';
$lang['admin_auth_set_password_repeat'] = 'Repeat Password';
$lang['admin_auth_set_password_heading'] = 'Set Password';
$lang['apply'] = 'Apply';
$lang['department_calendar_id'] = 'Google Calendar ID';
$lang['localization_default_language'] = 'Default Language';
$lang['system_default_string'] = 'System Default';
$lang['advanced_options'] = 'Advanced Options';
$lang['expense_list_invoice'] = 'Invoiced';
$lang['expense_list_billed'] = 'Billed';
$lang['expense_list_unbilled'] = 'Not Invoiced';
$lang['lead_merge_custom_field'] = 'Merge as custom field';
$lang['lead_merge_custom_field_existing'] = 'Merge with database field';
$lang['lead_dont_merge_custom_field'] = 'Do not merge';
$lang['lost_leads'] = 'Lost Leads';
$lang['junk_leads'] = 'Junk Leads';
$lang['lead_mark_as_lost'] = 'Mark as lost';
$lang['lead_unmark_as_lost'] = 'Unmark Lead as lost';
$lang['lead_marked_as_lost'] = 'Lead marked as lost successfully';
$lang['lead_unmarked_as_lost'] = 'Lead unmarked as lost successfully';
$lang['leads_status_color'] = 'Color';
$lang['lead_mark_as_junk'] = 'Mark as junk';
$lang['lead_unmark_as_junk'] = 'Unmark Lead as junk';
$lang['lead_marked_as_junk'] = 'Lead marked as junk successfully';
$lang['lead_unmarked_as_junk'] = 'Lead unmarked as junk successfully';
$lang['lead_not_found'] = 'Lead Not Found';
$lang['lead_lost'] = 'Lost';
$lang['lead_junk'] = 'Junk';
$lang['leads_not_assigned'] = 'Not Assigned';
$lang['contract_not_visible_to_client'] = 'Hide from customer';
$lang['contract_edit_overview'] = 'Contract Overview';
$lang['contract_attachments'] = 'Attachments';
$lang['task_view_make_public'] = 'Make public';
$lang['task_is_private'] = 'Private Task';
$lang['task_finished'] = 'Finished';
$lang['task_single_related'] = 'Related';
$lang['task_unmark_as_complete'] = 'Unmark as complete';
$lang['task_unmarked_as_complete'] = 'Task unmarked as complete';
$lang['task_relation'] = 'Related';
$lang['task_public'] = 'Public';
$lang['task_public_help'] = 'If you set this task to public will be visible for all staff members. Otherwise will be only visible to members who are assignees,followers,creator or administrators';
$lang['settings_general_favicon'] = 'Favicon';
$lang['settings_output_client_pdfs_from_admin_area_in_client_language'] = 'Output client PDF documents from admin area in client language';
$lang['settings_output_client_pdfs_from_admin_area_in_client_language_help'] = 'If this options is set to yes and eq. the system default language is English and client have setup language french the pdf documents will be outputted in the client language';
$lang['settings_default_tax'] = 'Default Tax';
$lang['setup_calendar_by_departments'] = 'Setup calendar by Departments';
$lang['settings_calendar'] = 'Calendar';
$lang['settings_sales_invoice_due_after'] = 'Invoice due after (days)';
$lang['settings_google_api'] = 'Google API Key';
$lang['settings_gcal_main_calendar_id'] = 'Google Calendar ID';
$lang['settings_gcal_main_calendar_id_help'] = 'This is the main company calendar. All events from this calendar will be shown. If you want to specify a calendar based on departments you can add in the department Google Calendar ID.';
$lang['show_on_calendar'] = 'Show on Calendar';
$lang['show_invoices_on_calendar'] = 'Invoices';
$lang['show_estimates_on_calendar'] = 'Estimates';
$lang['show_contracts_on_calendar'] = 'Contracts';
$lang['show_tasks_on_calendar'] = 'Tasks';
$lang['show_customer_reminders_on_calendar'] = 'Customer Reminders';
$lang['show_customer_notes_on_calendar'] = 'Customer Notes';
$lang['copy_custom_fields_convert_to_customer'] = 'Copy custom fields to customer profile';
$lang['copy_custom_fields_convert_to_customer_help'] = 'If any of the following custom fields do not exists for customer will be auto created with the same name otherwise only the value will be copied from the lead profile.';
$lang['lead_profile'] = 'Profile';
$lang['lead_is_client'] = 'Customer';
$lang['leads_email_integration_folder_no_encryption'] = 'No Encryption';
$lang['leads_email_integration'] = 'Email Integration';
$lang['leads_email_active'] = 'Active';
$lang['leads_email_integration_imap'] = 'IMAP Server';
$lang['leads_email_integration_email'] = 'Email address (Login)';
$lang['leads_email_integration_password'] = 'Password';
$lang['leads_email_integration_default_source'] = 'Default Source';
$lang['leads_email_integration_check_every'] = 'Check Every (minutes)';
$lang['leads_email_integration_default_assigned'] = 'Responsible for new lead';
$lang['leads_email_encryption'] = 'Encryption';
$lang['leads_email_integration_updated'] = 'Email Integration Updated';
$lang['leads_email_integration_default_status'] = 'Default Status';
$lang['leads_email_integration_folder'] = 'Folder';
$lang['leads_email_integration_notify_when_lead_imported'] = 'Notify when lead imported';
$lang['leads_email_integration_only_check_unseen_emails'] = 'Only check non opened emails';
$lang['leads_email_integration_only_check_unseen_emails_help'] = 'The script will auto set the email to opened after check. This is used to prevent checking all the emails again and again. Its not recommended to uncheck this option if you have a lot emails and you have setup a lot forwarding to the email you setup for leads';
$lang['leads_email_integration_notify_when_lead_contact_more_times'] = 'Notify if lead send email multiple times';
$lang['leads_email_integration_test_connection'] = 'Test IMAP Connection';
$lang['lead_email_connection_ok'] = 'IMAP Connection is good';
$lang['lead_email_connection_not_ok'] = 'IMAP Connection is not OK';
$lang['lead_email_activity'] = 'Email Activity';
$lang['leads_email_integration_notify_roles'] = 'Roles to Notify';
$lang['leads_email_integration_notify_staff'] = 'Staff Members to Notify';
$lang['lead_public'] = 'Public';
$lang['kb_group_color'] = 'Color';
$lang['kb_group_order'] = 'Order';
$lang['bulk_pdf_exporter'] = 'Bulk PDF Export';
$lang['bulk_export_pdf_payments'] = 'Payments';
$lang['bulk_export_pdf_estimates'] = 'Estimates';
$lang['bulk_export_pdf_invoices'] = 'Invoices';
$lang['bulk_pdf_export_button'] = 'Export';
$lang['bulk_pdf_export_select_type'] = 'Select Type';
$lang['no_data_found_bulk_pdf_export'] = 'No data found for export';
$lang['bulk_export_status_all'] = 'All';
$lang['bulk_export_status'] = 'Status';
$lang['bulk_export_zip_payment_modes'] = 'Made payments by';
$lang['bulk_export_include_tag'] = 'Include Tag';
$lang['bulk_export_include_tag_help'] = 'eq. Original or Copy. The tag will be outputted in the PDF. Recommended to use only 1 tag';
$lang['clients_nav_proposals'] = 'Proposals';
$lang['clients_nav_support'] = 'Support';
$lang['more'] = 'More';
$lang['add_item'] = 'Add Item';
$lang['goto_admin_area'] = 'Go to admin area';
$lang['delete'] = 'Delete %s';
$lang['welcome_top'] = 'Welcome %s';
$lang['customer_permissions'] = 'Permissions';
$lang['customer_permission_invoice'] = 'Invoices';
$lang['customer_permission_estimate'] = 'Estimates';
$lang['customer_permission_proposal'] = 'Proposals';
$lang['customer_permission_contract'] = 'Contracts';
$lang['customer_permission_support'] = 'Support';
$lang['task_related_to'] = 'Related To';
$lang['custom_file_fail_send'] = 'Failed to send file';
$lang['custom_file_success_send'] = 'The file is successfully send to %s';
$lang['send_file_subject'] = 'Email Subject';
$lang['send_file_email'] = 'Email Address';
$lang['send_file_message'] = 'Message';
$lang['send_file'] = 'Send File';
$lang['add_checklist_item'] = 'Checklist Item';
$lang['task_checklist_items'] = 'Checklist Items';
$lang['default_pass_clients_import'] = 'Default password for all contacts';
$lang['simulate_import'] = 'Simulate Import';
$lang['import_upload_failed'] = 'Upload Failed';
$lang['import_total_imported'] = 'Total Imported: %s';
$lang['import_leads'] = 'Import Leads';
$lang['import_customers'] = 'Import Customers';
$lang['import_customer_admins'] = 'Import Customer Admins';
$lang['import_salesperson'] = 'Import Salesperson';
$lang['choose_csv_file'] = 'Choose CSV File';
$lang['import'] = 'Import';
$lang['lead_import_status'] = 'Status';
$lang['lead_import_source'] = 'Source';
$lang['bulk_export_pdf_proposals'] = 'Proposals';
$lang['delete_invoice'] = 'Delete';
$lang['items'] = 'Items';
$lang['support'] = 'Support';
$lang['calendar_lead_reminder'] = 'Lead Reminder';
$lang['lead_set_reminder_title'] = 'Set Lead Reminder';
$lang['set_reminder_tooltip'] = 'This option allows you to never forget anything about your customers.';
$lang['client_reminders_tab'] = 'Reminders';
$lang['leads_reminders_tab'] = 'Reminders';
$lang['delete_ticket_reply'] = 'Delete Reply';
$lang['ticket_priority_edit'] = 'Edit Priority';
$lang['ticket_priority_add'] = 'Add Priority';
$lang['ticket_status_edit'] = 'Edit Ticket Status';
$lang['ticket_service_edit'] = 'Edit Ticket Service';
$lang['edit_department'] = 'Edit Department';
$lang['edit_expense_category'] = 'Edit Expense Category';
$lang['customer_default_country'] = 'Default Country';
$lang['settings_sales_require_client_logged_in_to_view_estimate'] = 'Require client to be logged in to view estimate';
$lang['set_reminder'] = 'Set Reminder';
$lang['set_reminder_date'] = 'Date to be notified';
$lang['reminder_description'] = 'Description';
$lang['reminder_notify_me_by_email'] = 'Send also an email for this reminder';
$lang['reminder_added_successfully'] = 'Reminder added successfully. You will be notified in time.';
$lang['reminder_date'] = 'Date';
$lang['reminder_staff'] = 'Remind';
$lang['reminder_is_notified'] = 'Is notified?';
$lang['reminder_is_notified_boolean_no'] = 'No';
$lang['reminder_is_notified_boolean_yes'] = 'Yes';
$lang['reminder_set_to'] = 'Set reminder to';
$lang['reminder_deleted'] = 'Reminder deleted successfully';
$lang['reminder_failed_to_delete'] = 'Failed to delete the reminder';
$lang['show_invoice_estimate_status_on_pdf'] = 'Show invoice/estimate status on PDF';
$lang['email_piping_default_priority'] = 'Default priority on piped ticket';
$lang['show_lead_reminders_on_calendar'] = 'Lead Reminders';
$lang['tickets_piping'] = 'Email Piping';
$lang['email_piping_only_replies'] = 'Only Replies Allowed by Email';
$lang['email_piping_only_registered'] = 'Pipe Only on Registered Users';
$lang['view_estimate_as_client'] = 'View estimate as customer';
$lang['estimate_mark_as'] = 'Mark as %s';
$lang['estimate_status_changed_success'] = 'Estimate status changed';
$lang['estimate_status_changed_fail'] = 'Failed to change estimate status';
$lang['proposal_to'] = 'To';
$lang['proposal_date'] = 'Date';
$lang['proposal_address'] = 'Address';
$lang['proposal_phone'] = 'Phone';
$lang['proposal_email'] = 'Email';
$lang['proposal_date_created'] = 'Date Created';
$lang['proposal_open_till'] = 'Open Till';
$lang['proposal_status_open'] = 'Open';
$lang['proposal_status_accepted'] = 'Accepted';
$lang['proposal_status_declined'] = 'Declined';
$lang['proposal_status_sent'] = 'Sent';
$lang['proposal_expired'] = 'Expired';
$lang['proposal_subject'] = 'Subject';
$lang['proposal_total'] = 'Total';
$lang['proposal_status'] = 'Status';
$lang['proposals_list_all'] = 'All';
$lang['proposals_leads_related'] = 'Leads Related';
$lang['proposals_customers_related'] = 'Customers Related';
$lang['proposal_related'] = 'Related';
$lang['proposal_for_lead'] = 'Lead';
$lang['proposal_for_customer'] = 'Customer';
$lang['proposal'] = 'Proposal';
$lang['proposal_lowercase'] = 'proposal';
$lang['proposals'] = 'Proposals';
$lang['proposals_lowercase'] = 'proposals';
$lang['new_proposal'] = 'New Proposal';
$lang['proposal_currency'] = 'Currency';
$lang['proposal_allow_comments'] = 'Allow Comments';
$lang['proposal_allow_comments_help'] = 'If you check this options comments will be allowed when your client view the proposal.';
$lang['proposal_edit'] = 'Edit';
$lang['proposal_pdf'] = 'PDF';
$lang['proposal_send_to_email'] = 'Send to Email';
$lang['proposal_send_to_email_title'] = 'Send Proposal to Email';
$lang['proposal_attach_pdf'] = 'Attach PDF';
$lang['proposal_preview_template'] = 'Preview Template';
$lang['proposal_view'] = 'View Proposal';
$lang['proposal_copy'] = 'Copy';
$lang['proposal_delete'] = 'Delete';
$lang['proposal_add_comment'] = 'Add Comment';
$lang['proposal_sent_to_email_success'] = 'Proposal sent to email successfully';
$lang['proposal_sent_to_email_fail'] = 'Failed to sent proposal to email';
$lang['proposal_copy_fail'] = 'Failed to copy proposal';
$lang['proposal_copy_success'] = 'Proposal copied successfully';
$lang['proposal_status_changed_success'] = 'Proposal status changed successfully';
$lang['proposal_status_changed_fail'] = 'Failed to change proposal status';
$lang['proposal_assigned'] = 'Assigned';
$lang['proposal_comments'] = 'Comments';
$lang['proposal_convert'] = 'Convert';
$lang['proposal_convert_estimate'] = 'Estimate';
$lang['proposal_convert_invoice'] = 'Invoice';
$lang['proposal_convert_to_estimate'] = 'Convert to Estimate';
$lang['proposal_convert_to_invoice'] = 'Convert to Invoice';
$lang['proposal_convert_to_lead_disabled_help'] = 'You need to convert the lead to customer in order to create %s';
$lang['proposal_convert_not_related_help'] = 'The proposal needs to be related to customer in order to convert to %s';
$lang['proposal_converted_to_estimate_success'] = 'Proposal converted to estimate successfully';
$lang['proposal_converted_to_invoice_success'] = 'Proposal converted to invoice successfully';
$lang['proposal_converted_to_estimate_fail'] = 'Failed to convert proposal to estimate';
$lang['proposal_converted_to_invoice_fail'] = 'Failed to convert proposal to invoice';
$lang['proposal_total_info'] = 'Total %s';
$lang['proposal_accept_info'] = 'Accept';
$lang['proposal_decline_info'] = 'Decline';
$lang['proposal_pdf_info'] = 'PDF';
$lang['customer_reset_action'] = 'Reset';
$lang['customer_reset_password_heading'] = 'Reset your password';
$lang['customer_forgot_password_heading'] = 'Forgot Password';
$lang['customer_forgot_password'] = 'Forgot Password?';
$lang['customer_reset_password'] = 'Password';
$lang['customer_reset_password_repeat'] = 'Repeat Password';
$lang['customer_forgot_password_email'] = 'Email Address';
$lang['customer_forgot_password_submit'] = 'Submit';
$lang['customer_ticket_subject'] = 'Subject';
$lang['email_template_proposals_fields_heading'] = 'Proposals';
$lang['add_task_attachments'] = 'Attachment';
$lang['task_view_attachments'] = 'Attachments';
$lang['task_view_description'] = 'Description';
$lang['customer_group_add_heading'] = 'Add New Customer Group';
$lang['customer_group_edit_heading'] = 'Edit Customer Group';
$lang['new_customer_group'] = 'New Customer Group';
$lang['customer_group_name'] = 'Name';
$lang['customer_groups'] = 'Groups';
$lang['customer_group'] = 'Customer Group';
$lang['customer_group_lowercase'] = 'customer group';
$lang['customer_have_invoices_by'] = 'Contains invoices by status %s';
$lang['customer_have_estimates_by'] = 'Contains estimates by status %s';
$lang['customer_have_contracts_by_type'] = 'Having contracts by type %s';
$lang['custom_field_show_on_table'] = 'Show on table';
$lang['custom_field_show_on_client_portal'] = 'Show on client portal';
$lang['custom_field_show_on_client_portal_help'] = 'If this field is checked also will be shown in tables';
$lang['custom_field_visibility'] = 'Visibility';
$lang['view_articles_list'] = 'View Articles';
$lang['view_articles_list_all'] = 'All Articles';
$lang['als_all_articles'] = 'Articles';
$lang['als_kb_groups'] = 'Groups';
$lang['spam_filters'] = 'Spam Filters';
$lang['spam_filter'] = 'Spam Filter';
$lang['new_spam_filter'] = 'New Spam Filter';
$lang['spam_filter_blocked_senders'] = 'Blocked Senders';
$lang['spam_filter_blocked_subjects'] = 'Blocked Subjects';
$lang['spam_filter_blocked_phrases'] = 'Blocked Phrases';
$lang['spam_filter_content'] = 'Content';
$lang['spamfilter_edit_heading'] = 'Edit Spam Filter';
$lang['spamfilter_add_heading'] = 'Add Spam Filter';
$lang['spamfilter_type'] = 'Type';
$lang['spamfilter_type_subject'] = 'Subject';
$lang['spamfilter_type_sender'] = 'Sender';
$lang['spamfilter_type_phrase'] = 'Phrase';
$lang['block_sender'] = 'Block Sender';
$lang['sender_blocked'] = 'Sender Blocked';
$lang['sender_blocked_successfully'] = 'Sender Blocked Successfully';
$lang['ticket_date_created'] = 'Created';
$lang['edit_kb_group'] = 'Edit group';
$lang['edit_source'] = 'Edit Source';
$lang['edit_status'] = 'Edit Status';
$lang['contract_type_edit'] = 'Edit Contract Type';
$lang['report_by_customer_groups'] = 'Total Value By Customer Groups';
$lang['ticket_pipe_log'] = 'Ticket Pipe Log';
$lang['ticket_pipe_name'] = 'From Name';
$lang['ticket_pipe_email_to'] = 'To';
$lang['ticket_pipe_email'] = 'From Email';
$lang['ticket_pipe_subject'] = 'Subject';
$lang['ticket_pipe_message'] = 'Message';
$lang['ticket_pipe_date'] = 'Date';
$lang['ticket_pipe_status'] = 'Status';
$lang['home_latest_activity'] = 'Latest Activity';
$lang['home_my_tasks'] = 'My Tasks';
$lang['home_my_todo_items'] = 'My To Do Items';
$lang['home_widget_view_all'] = 'View All';
$lang['home_stats_full_report'] = 'Full Report';
$lang['form_validation_required'] = 'The {field} field is required.';
$lang['form_validation_valid_email'] = 'The {field} field must contain a valid email address.';
$lang['form_validation_matches'] = 'The {field} field does not match the {param} field.';
$lang['form_validation_is_unique'] = 'The {field} field must contain a unique value.';
$lang['not_event'] = 'Calendar event today - %s ...';
$lang['not_event_public'] = 'Public event start today - %s ...';
$lang['not_contract_expiry_reminder'] = 'Contract expiry reminder - %s ...';
$lang['not_recurring_expense_cron_activity_heading'] = 'Recurring Expenses Cron Job Activity';
$lang['not_recurring_invoices_cron_activity_heading'] = 'Recurring Invoices Cron Job Activity';
$lang['not_recurring_total_renewed'] = 'Total Renewed: %s';
$lang['not_recurring_expenses_action_taken_from'] = 'Action taken from recurring expense';
$lang['not_invoice_created'] = 'Invoice Created:';
$lang['not_invoice_renewed'] = 'Renewed Invoice:';
$lang['not_expense_renewed'] = 'Renewed Expense:';
$lang['not_invoice_sent_to_customer'] = 'Invoice Sent to Customer: %s';
$lang['not_invoice_sent_yes'] = 'Yes';
$lang['not_invoice_sent_not'] = 'No';
$lang['not_action_taken_from_recurring_invoice'] = 'Action taken from recurring invoice:';
$lang['not_new_reminder_for'] = 'New Reminder for %s';
$lang['not_received_one_or_more_messages_lead'] = 'Received one more email message from lead';
$lang['not_received_lead_imported_email_integration'] = 'Lead Imported From Email Integration';
$lang['not_lead_imported_attachment'] = 'Imported attachment from email';
$lang['not_estimate_status_change'] = 'Imported attachment from email';
$lang['not_estimate_status_updated'] = 'Estimate Status Updated: From: %s to %s';
$lang['not_assigned_lead_to_you'] = 'assigned lead %s to you';
$lang['not_lead_activity_assigned_to'] = '%s assigned to %s';
$lang['not_lead_activity_attachment_deleted'] = 'Deleted Attachment';
$lang['not_lead_activity_status_updated'] = '%s updated lead status from %s to %s';
$lang['not_lead_activity_contacted'] = '%s contacted this lead on %s';
$lang['not_lead_activity_created'] = '%s created lead';
$lang['not_lead_activity_marked_lost'] = 'Marked as lost';
$lang['not_lead_activity_unmarked_lost'] = 'Unmarked as lost';
$lang['not_lead_activity_marked_junk'] = 'Marked as junk';
$lang['not_lead_activity_unmarked_junk'] = 'Unmarked as junk';
$lang['not_lead_activity_added_attachment'] = 'Added attachment';
$lang['not_lead_activity_converted_email'] = 'Lead email changed. First lead email was: %s and added as customer with email %s';
$lang['not_lead_activity_converted'] = '%s Converted this lead to customer';
$lang['not_liked_your_post'] = '%s liked your post %s ...';
$lang['not_commented_your_post'] = '%s commented on your post %s ...';
$lang['not_liked_your_comment'] = '%s liked your comment %s ...';
$lang['not_proposal_assigned_to_you'] = 'Proposal assigned to you - %s ...';
$lang['not_proposal_comment_from_client'] = 'New comment from customer on proposal %s ...';
$lang['not_proposal_proposal_accepted'] = 'Proposal Accepted - %s';
$lang['not_proposal_proposal_declined'] = 'Proposal Declined - %s';
$lang['not_task_added_you_as_follower'] = 'added you as follower on task %s ...';
$lang['not_task_added_someone_as_follower'] = 'added %s as follower on task %s ...';
$lang['not_task_added_himself_as_follower'] = 'added himself as follower on task %s ...';
$lang['not_task_assigned_to_you'] = 'assigned a task to you %s ...';
$lang['not_task_assigned_someone'] = 'assigned %s to task %s ...';
$lang['not_task_will_do_user'] = 'will do task %s ...';
$lang['not_task_new_attachment'] = 'New Attachment Added';
$lang['not_task_marked_as_complete'] = 'marked task as complete %s';
$lang['not_task_unmarked_as_complete'] = 'unmarked task as complete %s';
$lang['not_ticket_assigned_to_you'] = 'Ticket assigned to you - %s ...';
$lang['not_ticket_reassigned_to_you'] = 'Ticket reassigned to you - %s ...';
$lang['not_estimate_customer_accepted'] = 'Congratulations! Client accepted estimate with number %s';
$lang['not_estimate_customer_declined'] = 'Client declined estimate with number %s';
$lang['estimate_activity_converted'] = 'converted this estimate to invoice.<br /> %s';
$lang['estimate_activity_created'] = 'Created the estimate';
$lang['invoice_estimate_activity_removed_item'] = 'removed item <b>%s</b>';
$lang['estimate_activity_number_changed'] = 'Estimate number changed from %s to %s';
$lang['invoice_activity_number_changed'] = 'Invoice number changed from %s to %s';
$lang['invoice_estimate_activity_updated_item_short_description'] = 'updated item short description from %s to %s';
$lang['invoice_estimate_activity_updated_item_long_description'] = 'updated item long description from <b>%s</b> to <b>%s</b>';
$lang['invoice_estimate_activity_updated_item_rate'] = 'updated item rate from %s to %s';
$lang['invoice_estimate_activity_updated_qty_item'] = 'updated quantity on item <b>%s</b> from %s to %s';
$lang['invoice_estimate_activity_added_item'] = 'added new item <b>%s</b>';
$lang['invoice_estimate_activity_sent_to_client'] = 'sent estimate to client';
$lang['estimate_activity_client_accepted_and_converted'] = 'Customer accepted this estimate. Estimate is converted to invoice with number %s';
$lang['estimate_activity_client_accepted'] = 'Customer accepted this estimate';
$lang['estimate_activity_client_declined'] = 'Client declined this estimate';
$lang['estimate_activity_marked'] = 'marked estimate as %s';
$lang['invoice_activity_status_updated'] = 'Invoice status updated from %s to %s';
$lang['invoice_activity_created'] = 'created the invoice';
$lang['invoice_activity_from_expense'] = 'converted to invoice from expense';
$lang['invoice_activity_recurring_created'] = '[Recurring] Invoice created by CRON';
$lang['invoice_activity_recurring_from_expense_created'] = '[Invoice From Expense] Invoice created by CRON';
$lang['invoice_activity_sent_to_client_cron'] = 'Invoice sent to customer by CRON';
$lang['invoice_activity_sent_to_client'] = 'sent invoice to customer';
$lang['invoice_activity_marked_as_sent'] = 'marked invoice as sent';
$lang['invoice_activity_payment_deleted'] = 'deleted payment for the invoice. Payment #%s, total amount %s';
$lang['invoice_activity_payment_made_by_client'] = 'Client made payment for the invoice from total <b>%s</b> - %s';
$lang['invoice_activity_payment_made_by_staff'] = 'recorded payment from total <b>%s</b> - %s';
$lang['invoice_activity_added_attachment'] = 'Added attachment';
$lang['top_search_placeholder'] = 'Search...';
$lang['staff_profile_inactive_account'] = 'This staff member account is inactive';
$lang['copy_estimate'] = 'Copy Estimate';
$lang['estimate_copied_successfully'] = 'Estimate copied successfully';
$lang['estimate_copied_fail'] = 'Failed to copy estimate';
$lang['tasks_view_assigned_to_user'] = 'Tasks assigned to me';
$lang['tasks_view_follower_by_user'] = 'Tasks i\'m following';
$lang['no_tasks_found'] = 'No Tasks Found';
$lang['leads_dt_datecreated'] = 'Created';
$lang['leads_sort_by'] = 'Sort By';
$lang['leads_sort_by_datecreated'] = 'Date Created';
$lang['leads_sort_by_kanban_order'] = 'Kan Ban Order';
$lang['check_email_for_resetting_password'] = 'Check your email for further instructions resetting your password';
$lang['inactive_account'] = 'Inactive Account';
$lang['error_setting_new_password_key'] = 'Error setting new password';
$lang['password_reset_message'] = 'Your password has been reset. Please login now!';
$lang['password_reset_message_fail'] = 'Error resetting your password. Try again.';
$lang['password_reset_key_expired'] = 'Password key expired or invalid user';
$lang['auth_reset_pass_email_not_found'] = 'Email not found';
$lang['auth_reset_password_submit'] = 'Reset Password';
$lang['settings_amount_to_words'] = 'Amount to words';
$lang['settings_amount_to_words_desc'] = 'Output total amount to words in invoice/estimate';
$lang['settings_amount_to_words_enabled'] = 'Enable';
$lang['settings_total_to_words_lowercase'] = 'Number words into lowercase';
$lang['settings_show_tax_per_item'] = 'Show TAX per item';
$lang['report_sales_months_three_months'] = 'Last 3 months';
$lang['report_invoice_number'] = 'PO #';
$lang['report_invoice_customer'] = 'Customer';
$lang['report_invoice_date'] = 'Date';
$lang['report_invoice_duedate'] = 'Due Date';
$lang['report_invoice_amount'] = 'Amount';
$lang['report_invoice_amount_with_tax'] = 'Amount with tax';
$lang['report_invoice_amount_open'] = 'Amount open';
$lang['report_invoice_status'] = 'Status';
$lang['report_invoice_amount_without_tax'] = 'Amount without tax';
$lang['home_stats_by_project_status'] = 'Statistics by Project Status';
$lang['home_invoice_overview'] = 'Invoice overview';
$lang['home_estimate_overview'] = 'Estimate overview';
$lang['home_proposal_overview'] = 'Proposal overview';
$lang['home_lead_overview'] = 'Leads Overview';
$lang['home_my_projects'] = 'My Projects';
$lang['home_announcements'] = 'Announcements';
$lang['settings_leads_kanban_limit'] = 'Limit leads kan ban rows per status';
$lang['settings_group_misc'] = 'Misc';
$lang['show_projects_on_calendar'] = 'Projects';
$lang['settings_media_max_file_size_upload'] = 'Max file size upload in Media (MB)';
$lang['settings_client_staff_add_edit_delete_task_comments_first_hour'] = 'Allow customer/staff to add/edit task comments only in the first hour (administrators not applied)';
$lang['email_template_only_domain_email'] = 'Only domain email';
$lang['dismiss_announcement'] = 'Dismiss announcement';
$lang['announcement_from'] = 'From:';
$lang['announcement_date'] = 'Date posted: %s';
$lang['announcement_not_found'] = 'Announcement not found';
$lang['announcements_recent'] = 'Recent Announcements';
$lang['zip_invoices'] = 'Zip PO';
$lang['zip_estimates'] = 'Zip Estimates';
$lang['zip_payments'] = 'Zip Payments';
$lang['setup_help'] = 'Help';
$lang['clients_list_company'] = 'Company';
$lang['dt_button_export'] = 'Export';
$lang['dt_entries'] = 'entries';
$lang['invoice_total_paid'] = 'Total Paid';
$lang['invoice_amount_due'] = 'Amount Due';
$lang['calendar_project'] = 'Project';
$lang['leads_import_assignee'] = 'Responsible (Assignee)';
$lang['customer_from_lead'] = 'Customer from %s';
$lang['lead_kan_ban_attachments'] = 'Attachments: %s';
$lang['leads_sort_by_lastcontact'] = 'Last Contact';
$lang['task_comment_added'] = 'Comment successfully added';
$lang['task_duedate'] = 'Due Date';
$lang['task_view_comments'] = 'Comments';
$lang['task_comment_updated'] = 'Comment updated';
$lang['task_visible_to_client'] = 'Visible to customer';
$lang['task_hourly_rate'] = 'Hourly Rate';
$lang['hours'] = 'Hours';
$lang['seconds'] = 'Seconds';
$lang['minutes'] = 'Minutes';
$lang['task_start_timer'] = 'Start Timer';
$lang['task_stop_timer'] = 'Stop Timer';
$lang['task_billable'] = 'Billable';
$lang['task_billable_yes'] = 'Billable';
$lang['task_billable_no'] = 'Not Billable';
$lang['task_billed'] = 'Billed';
$lang['task_billed_yes'] = 'Billed';
$lang['task_billed_no'] = 'Not Billed';
$lang['task_user_logged_time'] = 'Your logged time:';
$lang['task_total_logged_time'] = 'Total logged time:';
$lang['task_is_billed'] = 'This task is billed on invoice with number %s';
$lang['task_statistics'] = 'Statistics';
$lang['task_milestone'] = 'Milestone';
$lang['ticket_message_updated_successfully'] = 'Message updated successfully';
$lang['invoice_task_item_project_tasks_not_included'] = 'Projects tasks are not included in this list.';
$lang['show_quantity_as'] = 'Show quantity as:';
$lang['quantity_as_qty'] = 'Qty';
$lang['quantity_as_hours'] = 'Hours';
$lang['invoice_table_hours_heading'] = 'Hours';
$lang['bill_tasks'] = 'Bill Tasks';
$lang['invoice_estimate_sent_to_email'] = 'Email to';
$lang['estimate_table_hours_heading'] = 'Hours';
$lang['is_customer_indicator'] = 'Customer';
$lang['print'] = 'Print';
$lang['customer_permission_projects'] = 'Projects';
$lang['no_timers_found'] = 'No started timers found';
$lang['timers_started_confirm_logout'] = 'Started tasks timers found!<br />Are you sure you want to logout without stopping the timers?';
$lang['confirm_logout'] = 'Logout';
$lang['timer_top_started'] = 'Started at %s';
$lang['cant_change_billing_type_billed_tasks_found'] = 'You cant change billing type. Billed tasks already found for this project.';
$lang['project_customer_permission_warning'] = 'The system indicated that the primary contact do not have permission for projects. The primary contact won\'t be able to see the project. Consider add permission in the contact profile.';
$lang['project_invoice_timesheet_start_time'] = 'Start time: %s';
$lang['project_invoice_timesheet_end_time'] = 'End time: %s';
$lang['project_invoice_timesheet_total_logged_time'] = 'Billable time: %s';
$lang['project_view_as_client'] = 'View project as customer';
$lang['project_mark_all_tasks_as_completed'] = 'Mark all tasks as completed and stop all timers (No notifications sent to project members)';
$lang['project_not_started_status_tasks_timers_found'] = 'Task timers found for this project but the project is with status Not Started. Recommended to change the project status to In Progress';
$lang['project_status'] = 'Status';
$lang['project_status_1'] = 'Not Started';
$lang['project_status_2'] = 'In Progress';
$lang['project_status_3'] = 'On Hold';
$lang['project_status_4'] = 'Finished';
$lang['project_file_dateadded'] = 'Date uploaded';
$lang['project_file_filename'] = 'Filename';
$lang['project_file__filetype'] = 'File type';
$lang['project_file_visible_to_customer'] = 'Visible to Customer';
$lang['project_file_uploaded_by'] = 'Uploaded by';
$lang['edit_project'] = 'Edit Project';
$lang['copy_project'] = 'Copy Project';
$lang['delete_project'] = 'Delete Project';
$lang['project_task_assigned_to_user'] = 'Task assigned to you';
$lang['project'] = 'Project';
$lang['project_lowercase'] = 'project';
$lang['projects'] = 'Projects';
$lang['projects_lowercase'] = 'projects';
$lang['project_settings'] = 'Project settings';
$lang['project_invoiced_successfully'] = 'Project Invoiced Successfully';
$lang['new_project'] = 'New Project';
$lang['project_files'] = 'Files';
$lang['project_activity'] = 'Activity';
$lang['project_name'] = 'Project Name';
$lang['project_description'] = 'Description';
$lang['project_customer'] = 'Customer';
$lang['project_start_date'] = 'Start Date';
$lang['project_datecreated'] = 'Date Created';
$lang['project_deadline'] = 'Deadline';
$lang['project_billing_type'] = 'Billing Type';
$lang['project_billing_type_fixed_cost'] = 'Fixed Rate';
$lang['project_billing_type_project_hours'] = 'Project Hours';
$lang['project_billing_type_project_task_hours'] = 'Task Hours';
$lang['project_billing_type_project_task_hours_hourly_rate'] = 'Based on task hourly rate';
$lang['project_rate_per_hour'] = 'Rate Per Hour';
$lang['project_total_cost'] = 'Total Rate';
$lang['project_members'] = 'Members';
$lang['project_member_removed'] = 'Project member removed successfully';
$lang['project_overview'] = 'Overview';
$lang['project_gant'] = 'Gantt';
$lang['project_milestones'] = 'Milestones';
$lang['project_milestone_order'] = 'Order';
$lang['project_milestone_duedate_passed'] = 'Due date passed';
$lang['record_timesheet'] = 'Timesheet';
$lang['new_milestone'] = 'New Milestone';
$lang['edit_milestone'] = 'Edit Milestone';
$lang['milestone_name'] = 'Name';
$lang['milestone_due_date'] = 'Due date';
$lang['project_milestone'] = 'Milestone';
$lang['project_notes'] = 'Notes';
$lang['project_timesheets'] = 'Timesheets';
$lang['project_timesheet'] = 'Timesheet';
$lang['milestone_total_logged_time'] = 'Logged Time';
$lang['project_overview_total_logged_hours'] = 'Total Logged Hours';
$lang['milestones_uncategorized'] = 'Uncategorized';
$lang['milestone_no_tasks_found'] = 'No tasks found';
$lang['project_copied_successfully'] = 'Project data copied successfully';
$lang['failed_to_copy_project'] = 'Failed to copy project';
$lang['copy_project_task_include_check_list_items'] = 'Copy checklist items';
$lang['copy_project_task_include_assignees'] = 'Copy the same assignees';
$lang['copy_project_task_include_followers'] = 'Copy the same followers';
$lang['project_days_left'] = 'Days Left';
$lang['project_open_tasks'] = 'Open Tasks';
$lang['timesheet_stop_timer'] = 'Stop Timer';
$lang['failed_to_add_project_timesheet_end_time_smaller'] = 'Failed to add timesheet. End time is smaller then start time';
$lang['project_timesheet_user'] = 'Member';
$lang['project_timesheet_start_time'] = 'Start Time';
$lang['project_timesheet_end_time'] = 'End Time';
$lang['project_timesheet_time_spend'] = 'Time Spent';
$lang['project_timesheet_task'] = 'Task';
$lang['project_invoices'] = 'Invoices';
$lang['total_logged_hours_by_staff'] = 'Total Logged Time';
$lang['invoice_project'] = 'Invoice Project';
$lang['invoice_project_info'] = 'Project Invoice Info';
$lang['invoice_project_data_single_line'] = 'Single line';
$lang['invoice_project_data_task_per_item'] = 'Task per item';
$lang['invoice_project_data_timesheets_individually'] = 'All timesheets individually';
$lang['invoice_project_item_name_data'] = 'Item name';
$lang['invoice_project_description_data'] = 'Description';
$lang['invoice_project_projectname_taskname'] = 'Project name + Task name';
$lang['invoice_project_all_tasks_total_logged_time'] = 'All tasks + total logged time per task';
$lang['invoice_project_project_name_data'] = 'Project name';
$lang['invoice_project_timesheet_individually_data'] = 'Timesheet start time + end time + total logged time';
$lang['invoice_project_total_logged_time_data'] = 'Total logged time';
$lang['project_allow_client_to'] = 'Allow customer to %s';
$lang['project_setting_view_task_attachments'] = 'view task attachments';
$lang['project_setting_view_task_checklist_items'] = 'view task checklist items';
$lang['project_setting_upload_files'] = 'upload files';
$lang['project_setting_view_task_comments'] = 'view task comments';
$lang['project_setting_upload_on_tasks'] = 'upload attachments on tasks';
$lang['project_setting_view_task_total_logged_time'] = 'view task total logged time';
$lang['project_setting_open_discussions'] = 'open discussions';
$lang['project_setting_comment_on_tasks'] = 'comment on project tasks';
$lang['project_setting_view_tasks'] = 'view tasks';
$lang['project_setting_view_milestones'] = 'view milestones';
$lang['project_setting_view_gantt'] = 'view Gantt';
$lang['project_setting_view_timesheets'] = 'view timesheets';
$lang['project_setting_view_activity_log'] = 'view activity log';
$lang['project_setting_view_team_members'] = 'view team members';
$lang['project_discussion_visible_to_customer_yes'] = 'Visible';
$lang['project_discussion_visible_to_customer_no'] = 'Not Visible';
$lang['project_discussion_posted_on'] = 'Posted on %s';
$lang['project_discussion_posted_by'] = 'Posted by %s';
$lang['project_discussion_failed_to_delete'] = 'Failed to delete discussion';
$lang['project_discussion_deleted'] = 'Discussion deleted successfully';
$lang['project_discussion_no_activity'] = 'No Activity';
$lang['project_discussion'] = 'Discussion';
$lang['project_discussions'] = 'Discussions';
$lang['edit_discussion'] = 'Edit Discussion';
$lang['new_project_discussion'] = 'Create Discussion';
$lang['project_discussion_subject'] = 'Subject';
$lang['project_discussion_description'] = 'Description';
$lang['project_discussion_show_to_customer'] = 'Visible to Customer';
$lang['project_discussion_total_comments'] = 'Total Comments';
$lang['project_discussion_last_activity'] = 'Last Activity';
$lang['discussion_add_comment'] = 'Add comment';
$lang['discussion_newest'] = 'Newest';
$lang['discussion_oldest'] = 'Oldest';
$lang['discussion_attachments'] = 'Attachments';
$lang['discussion_send'] = 'Send';
$lang['discussion_reply'] = 'Answer';
$lang['discussion_edit'] = 'Edit';
$lang['discussion_edited'] = 'Modified';
$lang['discussion_you'] = 'You';
$lang['discussion_save'] = 'Save';
$lang['discussion_delete'] = 'Delete';
$lang['discussion_view_all_replies'] = 'Show all replies';
$lang['discussion_hide_replies'] = 'Hide replies';
$lang['discussion_no_comments'] = 'No comments';
$lang['discussion_no_attachments'] = 'No attachments';
$lang['discussion_attachments_drop'] = 'Drag and drop to upload file';
$lang['project_note'] = 'Note';
$lang['project_note_private'] = 'Personal notes';
$lang['project_save_note'] = 'Save note';
$lang['project_activity_created'] = 'Created the project';
$lang['project_activity_updated'] = 'Updated project';
$lang['project_activity_removed_team_member'] = 'Removed team member';
$lang['project_activity_added_team_member'] = 'Added new team member';
$lang['project_activity_marked_all_tasks_as_complete'] = 'Marked all tasks as complete';
$lang['project_activity_recorded_timesheet'] = 'Recorded timesheet';
$lang['project_activity_task_name'] = 'Task:';
$lang['project_activity_deleted_discussion'] = 'Deleted Discussion';
$lang['project_activity_created_discussion'] = 'Created discussion';
$lang['project_activity_updated_discussion'] = 'Updated discussion';
$lang['project_activity_commented_on_discussion'] = 'Commented on discussion';
$lang['project_activity_deleted_discussion_comment'] = 'Deleted discussion comment';
$lang['project_activity_deleted_milestone'] = 'Deleted milestone';
$lang['project_activity_updated_milestone'] = 'Updated milestone';
$lang['project_activity_created_milestone'] = 'Created new milestone';
$lang['project_activity_invoiced_project'] = 'Invoiced project';
$lang['project_activity_task_marked_complete'] = 'Task marked as complete';
$lang['project_activity_task_unmarked_complete'] = 'Task unmarked as complete';
$lang['project_activity_task_deleted'] = 'Task deleted';
$lang['project_activity_new_task_comment'] = 'Commented on task';
$lang['project_activity_new_task_attachment'] = 'Uploaded attachment on task';
$lang['project_activity_new_task_assignee'] = 'Added new task assignee';
$lang['project_activity_task_assignee_removed'] = 'Removed task assignee';
$lang['project_activity_task_timesheet_deleted'] = 'Removed timesheet';
$lang['project_activity_uploaded_file'] = 'Uploaded project file';
$lang['project_activity_status_updated'] = 'Updated project status';
$lang['project_activity_visible_to_customer'] = 'Visible to customer';
$lang['project_activity_project_file_removed'] = 'Removed project file';
$lang['client_no_reply'] = 'No Reply';
$lang['clients_nav_projects'] = 'Projects';
$lang['clients_my_projects'] = 'Projects';
$lang['client_profile_image'] = 'Profile image';
$lang['sales_report_cancelled_invoices_not_included'] = 'Cancelled invoices are excluded from the report';
$lang['invoices_merge_cancel_merged_invoices'] = 'Mark merged invoices as cancelled instead of deleting';
$lang['invoice_marked_as_cancelled_successfully'] = 'Invoice marked as cancelled successfully';
$lang['invoice_unmarked_as_cancelled'] = 'Invoice unmarked as cancelled successfully';
$lang['tasks_reminder_notification_before'] = 'Task deadline reminder before (Days)';
$lang['not_task_deadline_reminder'] = 'Task deadline reminder';
$lang['dt_length_menu_all'] = 'All';
$lang['task_not_finished'] = 'Not Completed';
$lang['task_billed_cant_start_timer'] = 'Task billed. Timer cant be start';
$lang['invoice_task_billable_timers_found'] = 'Started timers found';
$lang['project_timesheet_not_updated'] = 'Timesheet not affected';
$lang['project_invoice_task_no_timers_found'] = 'No timers found for this task';
$lang['invoice_project_tasks_not_started'] = 'Not yet started | Start Date: %s';
$lang['invoice_project_see_billed_tasks'] = 'See tasks that will be billed on this invoice';
$lang['invoice_project_all_billable_tasks_marked_as_finished'] = 'All billed tasks will be marked as finished';
$lang['invoice_project_nothing_to_bill'] = 'No tasks to bill. Feel free to add whatever you want in the invoice items.';
$lang['invoice_project_start_date_tasks_not_passed'] = 'Tasks with future start date cannot be billed';
$lang['invoice_project_stop_all_timers'] = 'Stop all timers';
$lang['invoice_project_stop_billable_timers_only'] = 'Stop only billable timers';
$lang['project_tasks_total_timers_stopped'] = 'Stopped total %s timers';
$lang['project_invoice_timers_started'] = 'Task timers found running on billable tasks, invoice cannot be created. Please stop task timers to create invoice.';
$lang['task_start_timer_only_assignee'] = 'You need to be assigned on this task to start timer!';
$lang['task_comments'] = 'Comments';
$lang['invoice_total_tax'] = 'Total Tax';
$lang['estimates_total_tax'] = 'Total Tax';
$lang['report_invoice_total_tax'] = 'Total Tax';
$lang['home_tickets'] = 'Tickets';
$lang['home_project_activity'] = 'Latest Project Activity';
$lang['view_tracking'] = 'Views Tracking';
$lang['view_date'] = 'Date';
$lang['view_ip'] = 'IP Address';
$lang['article_total_views'] = 'Total Views';
$lang['leads_source'] = 'Source';
$lang['invoices_available_for_merging'] = 'Invoices Available for Merging';
$lang['invoices_merge_discount'] = 'You will have to apply discount of total %s manually to this invoice';
$lang['invoice_merge_number_warning'] = 'Merging invoices will create gaps in invoice numbers. Please do not merge invoices if you want no gaps in your invoice history. You also have the option of manually adjusting invoice numbers if you want to fill the gaps.';
$lang['invoice_mark_as'] = 'Mark as %s';
$lang['invoice_unmark_as'] = 'Unmark as %s';
$lang['invoice_status_cancelled'] = 'Cancelled';
$lang['tasks_reminder_notification_before_help'] = 'Notify task assignees about deadline before X days. The notification/email is sent only to the assignees. If the difference between task start date and task due date is smaller then the reminders day no notification will be sent.';
$lang['project_invoice_select_all_tasks'] = 'Select all tasks';
$lang['lead_company'] = 'Company';
$lang['admin_auth_forgot_password_button'] = 'Confirm';
$lang['task_assigned'] = 'Assigned to';
$lang['switch_to_pipeline'] = 'Switch to pipeline';
$lang['switch_to_list_view'] = 'Switch to list';
$lang['estimates_pipeline'] = 'Estimates Pipeline';
$lang['estimates_pipeline_sort'] = 'Sort By';
$lang['estimates_sort_expiry_date'] = 'Expiry Date';
$lang['estimates_sort_pipeline'] = 'Pipeline Order';
$lang['estimates_sort_datecreated'] = 'Date Created';
$lang['estimates_sort_estimate_date'] = 'Estimate Date';
$lang['estimate_set_reminder_title'] = 'Set Estimate Reminder';
$lang['invoice_set_reminder_title'] = 'Set Invoice Reminder';
$lang['estimate_reminders'] = 'Reminders';
$lang['invoice_reminders'] = 'Reminders';
$lang['estimate_notes'] = 'Notes';
$lang['estimate_add_note'] = 'Add Note';
$lang['dropdown_non_selected_tex'] = 'Nothing selected';
$lang['auto_close_ticket_after'] = 'Auto close ticket after (Hours)';
$lang['event_description'] = 'Description';
$lang['delete_event'] = 'Delete';
$lang['not_new_ticket_created'] = 'New ticket opened in your department - %s';
$lang['receive_notification_on_new_ticket'] = 'Receive notification on new ticket opened';
$lang['receive_notification_on_new_ticket_help'] = 'All staff members which belong to the ticket department will receive notification that new ticket is opened';
$lang['event_updated'] = 'Event updated successfully';
$lang['customer_contacts'] = 'Contacts';
$lang['new_contact'] = 'New Contact';
$lang['contact'] = 'Contact';
$lang['contact_lowercase'] = 'contact';
$lang['contact_primary'] = 'Primary Contact';
$lang['contact_position'] = 'Position';
$lang['contact_active'] = 'Active';
$lang['client_company_info'] = 'Company details';
$lang['proposal_save'] = 'Save Proposal';
$lang['calendar'] = 'Calendar';
$lang['settings_pdf'] = 'PDF';
$lang['settings_pdf_font'] = 'PDF Font';
$lang['settings_pdf_table_heading_color'] = 'Items table heading color';
$lang['settings_pdf_table_heading_text_color'] = 'Items table heading text color';
$lang['settings_pdf_font_size'] = 'Default font size';
$lang['proposal_status_draft'] = 'Draft';
$lang['custom_field_contacts'] = 'Contacts';
$lang['company_primary_email'] = 'Primary Email';
$lang['client_register_contact_info'] = 'Primary Contact Information';
$lang['client_register_company_info'] = 'Company Information';
$lang['contact_permissions_info'] = 'Make sure to set appropriate permissions for this contact';
$lang['default_leads_kanban_sort'] = 'Default leads kan ban sort';
$lang['default_leads_kanban_sort_type'] = 'Sort';
$lang['order_ascending'] = 'Ascending';
$lang['order_descending'] = 'Descending';
$lang['calendar_expand'] = 'expand';
$lang['proposal_reminders'] = 'Reminders';
$lang['proposal_set_reminder_title'] = 'Set Proposal Reminder';
$lang['settings_allowed_upload_file_types'] = 'Allowed file types';
$lang['no_primary_contact'] = 'This customer does have primary contact. You need to setup primary contact login as customer. Its recommended all customers to have primary contacts.';
$lang['leads_merge_customer'] = 'Customer fields merging';
$lang['leads_merge_contact'] = 'Contact fields merging';
$lang['leads_merge_as_contact_field'] = 'Merge as contact field';
$lang['lead_convert_to_client_phone'] = 'Phone';
$lang['invoice_status_report_all'] = 'All';
$lang['import_contact_field'] = 'Contact field';
$lang['import_contact_sex_field'] = '<b>Sex field</b>: leave empty - Other, 1 - Male, 2 - Female';
$lang['import_number_fields'] = 'To ensure Excel doesn\'t remove the leading zeros, please enter <b>a single quote (\')</b>  before you type the numbers like <b>Phone number, VAT</b>, ...  (Example Phonenumber: <b>\'0123456789</b>)';
$lang['file_uploaded_success'] = 'There is no error, the file uploaded with success';
$lang['file_exceeds_max_filesize'] = 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
$lang['file_exceeds_maxfile_size_in_form'] = 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form';
$lang['file_uploaded_partially'] = 'The uploaded file was only partially uploaded';
$lang['file_not_uploaded'] = 'No file was uploaded';
$lang['file_missing_temporary_folder'] = 'Missing a temporary folder';
$lang['file_failed_to_write_to_disk'] = 'Failed to write file to disk.';
$lang['file_php_extension_blocked'] = 'A PHP extension stopped the file upload.';
$lang['view_pdf'] = 'View PDF';
$lang['expense_repeat_every'] = 'Repeat every';
$lang['leads_switch_to_kanban'] = 'Switch to kan ban';
$lang['contract_content'] = 'Contract';
$lang['contract_save'] = 'Save Contract';
$lang['contract_send_to_email'] = 'Send to email';
$lang['contract_send_to_client_modal_heading'] = 'Send contract to email';
$lang['contract_send_to'] = 'Send to';
$lang['contract_send_to_client_attach_pdf'] = 'Attach PDF';
$lang['contract_send_to_client_preview_template'] = 'Preview Email Template';
$lang['include_attachments_to_email'] = 'Include attachments to email';
$lang['contract_sent_to_client_success'] = 'The contract is successfully sent to the customer';
$lang['contract_sent_to_client_fail'] = 'Failed to send contract';
$lang['client_invalid_username_or_password'] = 'Invalid username or password';
$lang['client_old_password_incorrect'] = 'Your old password is incorrect';
$lang['client_password_changed'] = 'Your password has been changed';
$lang['total_leads_deleted'] = 'Total leads deleted: %s';
$lang['total_clients_deleted'] = 'Total customers deleted: %s';
$lang['confirm_action_prompt'] = 'Are you sure you want to perform this action?';
$lang['mass_delete'] = 'Mass Delete';
$lang['email_protocol'] = 'Email Protocol';
$lang['add_edit_members'] = 'Add/Edit Members';
$lang['project_overview_logged_hours'] = 'Logged Hours:';
$lang['project_overview_billable_hours'] = 'Billable Hours:';
$lang['project_overview_billed_hours'] = 'Billed Hours:';
$lang['project_overview_unbilled_hours'] = 'Unbilled Hours:';
$lang['calendar_first_day'] = 'First Day';
$lang['permission_view'] = 'View';
$lang['permission_edit'] = 'Edit';
$lang['permission_create'] = 'Create';
$lang['permission_delete'] = 'Delete';
$lang['permission_customers_active'] = 'Set Active';
$lang['permission'] = 'Permission';
$lang['permissions'] = 'Permissions';
$lang['proposals_pipeline'] = 'Proposals Pipeline';
$lang['proposals_pipeline_sort'] = 'Sort By';
$lang['proposals_sort_open_till'] = 'Open Till';
$lang['proposals_sort_pipeline'] = 'Pipeline Order';
$lang['proposals_sort_datecreated'] = 'Date Created';
$lang['proposals_sort_proposal_date'] = 'Proposal Date';
$lang['is_not_staff_member'] = 'Not Staff Member';
$lang['lead_created'] = 'Created';
$lang['access_tickets_to_none_staff_members'] = 'Allow access to tickets for non staff members';
$lang['project_expenses'] = 'Expenses';
$lang['expense_currency'] = 'Currency';
$lang['currency_valid_code_help'] = 'Make sure to enter valid currency ISO code.';
$lang['week'] = 'Week';
$lang['weeks'] = 'Weeks';
$lang['month'] = 'Month';
$lang['months'] = 'Months';
$lang['year'] = 'Year';
$lang['years'] = 'Years';
$lang['expense_report_category'] = 'Category';
$lang['expense_paid_via'] = 'Paid Via %s';
$lang['item_as_expense'] = '[Expense]';
$lang['show_help_on_setup_menu'] = 'Show help menu item on setup menu';
$lang['customers_summary_total'] = 'Total Customers';
$lang['filter_by'] = 'Filter by';
$lang['re_captcha'] = 'reCAPTCHA';
$lang['recaptcha_site_key'] = 'Site key';
$lang['recaptcha_secret_key'] = 'Secret key';
$lang['recaptcha_error'] = 'The reCAPTCHA field is telling that you are a robot.';
$lang['smtp_username'] = 'SMTP Username';
$lang['smtp_username_help'] = 'Fill only if your email client use username for SMTP login.';
$lang['pinned_project'] = 'Pinned Project';
$lang['pin_project'] = 'Pin Project';
$lang['unpin_project'] = 'Unpin Project';
$lang['smtp_encryption'] = 'Email Encryption';
$lang['smtp_encryption_none'] = 'None';
$lang['show_proposals_on_calendar'] = 'Proposals';
$lang['invoice_project_see_billed_expenses'] = 'See expenses that will be billed on this invoice';
$lang['project_overview_expenses'] = 'Total Expenses';
$lang['project_overview_expenses_billable'] = 'Billable Expenses';
$lang['project_overview_expenses_billed'] = 'Billed Expenses';
$lang['project_overview_expenses_unbilled'] = 'Unbilled Expenses';
$lang['announcement_date_list'] = 'Date';
$lang['project_setting_view_finance_overview'] = 'view finance overview';
$lang['show_all_tasks_for_project_member'] = 'Allow all staff to see all tasks related to projects (includes non-staff)';
$lang['not_staff_added_as_project_member'] = 'Added you as project member';
$lang['report_expenses_base_currency_select_explanation'] = 'You need to select currency because the system found different currencies used for expenses.';
$lang['project_activity_recorded_expense'] = 'Recorded Expense';
$lang['save_customer_and_add_contact'] = 'Save and create contact';
$lang['tickets_chart_weekly_opening_stats'] = 'Weekly Stats';
$lang['related_knowledgebase_articles'] = 'Related Articles';
$lang['detailed_overview'] = 'Tasks Overview';
$lang['tasks_total_checklists_finished'] = 'Total checklist items marked as finished';
$lang['tasks_total_added_attachments'] = 'Total attachments added';
$lang['tasks_total_comments'] = 'Total comments';
$lang['task_finished_on_time'] = 'Finished on time?';
$lang['task_finished_on_time_indicator'] = 'Yes';
$lang['task_not_finished_on_time_indicator'] = 'No';
$lang['filter'] = 'Filter';
$lang['task_filter_detailed_all_months'] = 'All Months';
$lang['kb_article_slug'] = 'Slug';
$lang['email_template_ticket_warning'] = 'If ticket is imported with email piping and the contact does not exists in the CRM the fields won\'t be replaced.';
$lang['auto_stop_tasks_timers_on_new_timer'] = 'Stop all other started timers when starting new timer';
$lang['notification_when_customer_pay_invoice'] = 'Receive notification when customer pay invoice (built-in)';
$lang['not_invoice_payment_recorded'] = 'New invoice payment - %s';
$lang['login_employer_dash'] = 'Customer %s has logged in to the Employer Dash at %s <br>ID CRM: %s <br> ID AMS: %s <br> Login email: %s ';
$lang['email_template_contact_warning'] = 'If contact is not logged while making action the contact merge fields won\'t be replaced.';
$lang['change_role_permission_warning'] = 'Changing role permissions won\'t affected current staff members permissions that are using this role.';
$lang['task_copied_successfully'] = 'Task copied successfully';
$lang['failed_to_copy_task'] = 'Failed to copy task';
$lang['not_project_file_uploaded'] = 'New project file added';
$lang['settings_calendar_color'] = '%s Color';
$lang['settings_calendar_colors_heading'] = 'Styling';
$lang['reminder'] = 'Reminder';
$lang['back_to_tasks_list'] = 'Back to tasks list';
$lang['copy_task_confirm'] = 'Confirm';
$lang['changing_items_affect_warning'] = 'Changing item info won\'t affect on the created invoices/estimates/proposals/credit notes.';
$lang['tax_is_used_in_expenses_warning'] = 'You can\'t update this tax because the tax is used by expenses transactions.';
$lang['note'] = 'Note';
$lang['leads_staff_report_converted'] = 'Total converted leads';
$lang['leads_staff_report_created'] = 'Total created leads';
$lang['leads_staff_report_lost'] = 'Total lost leads';
$lang['client_go_to_dashboard'] = 'Back to portal';
$lang['show_estimate_reminders_on_calendar'] = 'Estimate Reminders';
$lang['show_invoice_reminders_on_calendar'] = 'Invoice Reminders';
$lang['show_proposal_reminders_on_calendar'] = 'Proposal Reminders';
$lang['calendar_estimate_reminder'] = 'Estimate Reminder';
$lang['calendar_invoice_reminder'] = 'Invoice Reminder';
$lang['calendar_proposal_reminder'] = 'Proposal Reminder';
$lang['proposal_due_after'] = 'Proposal Due After (days)';
$lang['project_progress'] = 'Progress';
$lang['calculate_progress_through_tasks'] = 'Calculate progress through tasks';
$lang['allow_customer_to_change_ticket_status'] = 'Allow customer to change ticket status from customers area';
$lang['switch_to_general_report'] = 'Switch to staff report';
$lang['switch_to_staff_report'] = 'Switch to general report';
$lang['generate'] = 'Generate';
$lang['from_date'] = 'From date';
$lang['to_date'] = 'To date';
$lang['not_results_found'] = 'No results found';
$lang['lead_lock_after_convert_to_customer'] = 'Do not allow leads to be edited after they are converted to customers (administrators not applied)';
$lang['default_pipeline_sort'] = 'Default pipeline sort';
$lang['toggle_full_view'] = 'Toggle full view';
$lang['not_estimate_invoice_deleted'] = 'deleted the created invoice';
$lang['not_task_new_comment'] = 'commented on task %s';
$lang['invoice_number_exists'] = 'This invoice number exists for the ongoing year.';
$lang['estimate_number_exists'] = 'This estimate number exists for the ongoing year.';
$lang['email_exists'] = 'Email already exists';
$lang['not_uploaded_project_file'] = 'New file uploaded';
$lang['not_created_new_project_discussion'] = 'New project discussion created';
$lang['not_commented_on_project_discussion'] = 'New comment on project discussion';
$lang['all_staff_members'] = 'All Staff Members';
$lang['help_project_permissions'] = 'VIEW allows staff member to see ALL projects. If you only want them to see projects they are assigned (added as members), do not give VIEW permissions.';
$lang['help_tasks_permissions'] = 'VIEW allows staff member to see ALL tasks. If you only want them to see tasks they are assigned to or following, do not give VIEW permissions.';
$lang['expense_recurring_days'] = 'Day(s)';
$lang['expense_recurring_weeks'] = 'Week(s)';
$lang['expense_recurring_months'] = 'Month(s)';
$lang['expense_recurring_years'] = 'Years(s)';
$lang['reset_to_default_color'] = 'Reset to default color';
$lang['pdf_logo_width'] = 'Logo Width (PX)';
$lang['drop_files_here_to_upload'] = 'Drop files here to upload';
$lang['browser_not_support_drag_and_drop'] = 'Your browser does not support drag\'n\'drop file uploads';
$lang['remove_file'] = 'Remove file';
$lang['you_can_not_upload_any_more_files'] = 'You can not upload any more files';
$lang['custom_field_only_admin'] = 'Restrict visibility for administrators only';
$lang['leads_default_source'] = 'Default source';
$lang['clear_activity_log'] = 'Clear log';
$lang['default_contact_permissions'] = 'Default contact permissions';
$lang['invoice_activity_marked_as_cancelled'] = 'marked invoice as cancelled';
$lang['invoice_activity_unmarked_as_cancelled'] = 'unmarked invoice as cancelled';
$lang['wait_text'] = 'Please wait...';
$lang['projects_summary'] = 'Projects Summary';
$lang['dept_imap_host'] = 'IMAP Host';
$lang['dept_encryption'] = 'Encryption';
$lang['dept_email_password'] = 'Password';
$lang['dept_email_no_encryption'] = 'No Encryption';
$lang['failed_to_decrypt_password'] = 'Failed to decrypt password';
$lang['delete_mail_after_import'] = 'Delete mail after import?';
$lang['expiry_reminder_enabled'] = 'Send expiration reminder';
$lang['send_expiry_reminder_before'] = 'Send expiration reminder before (DAYS)';
$lang['not_expiry_reminder_sent'] = 'Expiry reminder sent';
$lang['send_expiry_reminder'] = 'Send expiration reminder';
$lang['sent_expiry_reminder_success'] = 'Expiration reminder successfully sent';
$lang['sent_expiry_reminder_fail'] = 'Failed to send expiration reminder';
$lang['leads_default_status'] = 'Default status';
$lang['item_description_placeholder'] = 'Description';
$lang['item_long_description_placeholder'] = 'Long description';
$lang['item_quantity_placeholder'] = 'Quantity';
$lang['item_rate_placeholder'] = 'Rate';
$lang['tickets_summary'] = 'Tickets Summary';
$lang['tasks_list_priority'] = 'Priority';
$lang['ticket_status_db_2'] = 'In Progress';
$lang['ticket_status_db_1'] = 'Open';
$lang['ticket_status_db_3'] = 'Answered';
$lang['ticket_status_db_4'] = 'On Hold';
$lang['ticket_status_db_5'] = 'Closed';
$lang['ticket_priority_db_1'] = 'Low';
$lang['ticket_priority_db_2'] = 'Medium';
$lang['ticket_priority_db_3'] = 'High';
$lang['customer_have_projects_by'] = 'Contains projects by status %s';
$lang['customer_have_proposals_by'] = 'Contains proposals by status %s';
$lang['do_not_redirect_payment'] = 'Do not redirect me to the payment processor';
$lang['project_tickets'] = 'Tickets';
$lang['invoice_report'] = 'Invoices Report';
$lang['payment_modes_report'] = 'Payment Modes (Transactions)';
$lang['customer_admins'] = 'Customer Admins';
$lang['salesperson'] = 'Salesperson';
$lang['salesperson_f1'] = 'Salesperson (F1_Cá nhân)';
$lang['note_creator'] = 'Note Creator';
$lang['contact_status'] = 'Contact Status';
$lang['assign_admin'] = 'Assign admin';
$lang['customer_admin_date_assigned'] = 'Date Assigned';
$lang['customer_admin_login_as_client_message'] = 'Hello %s. You are added as admin to this customer.';
$lang['ticket_form_validation_file_size'] = 'File size must be less than %s';
$lang['has_transactions_currency_base_change'] = 'Changing the base currency is possible only if there are no transactions recorded in that currency. Delete the transactions to change the base currency';
$lang['customers_sort_all'] = 'All';
$lang['use_recaptcha_customers_area'] = 'Enable reCAPTCHA on customers area (Login/Register)';
$lang['project_marked_as_finished'] = 'Project completed';
$lang['project_status_updated'] = 'Project status updated';
$lang['remove_decimals_on_zero'] = 'Remove decimals on numbers/money with zero decimals (2.00 will become 2, 2.25 will stay 2.25)';
$lang['remove_tax_name_from_item_table'] = 'Remove the tax name from item table row';
$lang['not_billable_expenses_by_categories'] = 'Not billable expenses by categories';
$lang['billable_expenses_by_categories'] = 'Billable expenses by categories';
$lang['format_letter_size'] = 'A4 Landscape';
$lang['pdf_formats'] = 'Document formats';
$lang['swap_pdf_info'] = 'Swap Company/Customer Details (company details to right side, customer details to left side)';
$lang['expenses_filter_by_categories'] = 'By Categories';
$lang['task_copy'] = 'Copy';
$lang['estimate_status'] = 'Status';
$lang['expenses_report_exclude_billable'] = 'Exclude Billable Expenses';
$lang['expenses_total'] = 'Total';
$lang['estimate_activity_added_attachment'] = 'Added attachment';
$lang['show_to_customer'] = 'Show to customer';
$lang['hide_from_customer'] = 'Hide from customer';
$lang['expenses_report_total'] = 'Total';
$lang['expenses_report'] = 'Expenses report';
$lang['expenses_report_total_tax'] = 'Total Tax';
$lang['expenses_detailed_report'] = 'Detailed Report';
$lang['expense_not_billable'] = 'Not Billable';
$lang['notification_settings'] = 'Notification settings';
$lang['staff_with_roles'] = 'Staff members with roles';
$lang['specific_staff_members'] = 'Specific Staff Members';
$lang['proposal_mark_as'] = 'Mark as %s';
$lang['kb_report_total_answers'] = 'Total';
$lang['ticket_message_edit'] = 'Edit';
$lang['invoice_files'] = 'Invoice Files';
$lang['estimate_files'] = 'Estimate Files';
$lang['proposal_files'] = 'Proposal Files';
$lang['invoices_awaiting_payment'] = 'Invoices Awaiting Payment';
$lang['tasks_not_finished'] = 'Tasks Not Finished';
$lang['outstanding_invoices'] = 'Outstanding Invoices';
$lang['past_due_invoices'] = 'Past Due Invoices';
$lang['paid_invoices'] = 'Paid Invoices';
$lang['invoice_estimate_year'] = 'Year';
$lang['task_stats_logged_hours'] = 'Logged Hours';
$lang['leads_converted_to_client'] = 'Converted Leads';
$lang['task_assigned_from'] = 'This task is assigned to you by %s';
$lang['new_note'] = 'New Note';
$lang['my_tickets_assigned'] = 'Tickets assigned to me';
$lang['filter_by_assigned'] = 'By Assigned Member';
$lang['staff_stats_total_logged_time'] = 'Total Logged Time';
$lang['staff_stats_last_month_total_logged_time'] = 'Last Month Logged Time';
$lang['staff_stats_this_month_total_logged_time'] = 'This Month Logged Time';
$lang['staff_stats_last_week_total_logged_time'] = 'Last Week Logged Time';
$lang['staff_stats_this_week_total_logged_time'] = 'This Week Logged Time';
$lang['timesheet_user'] = 'Member';
$lang['timesheet_start_time'] = 'Start Time';
$lang['timesheet_end_time'] = 'End Time';
$lang['timesheet_time_spend'] = 'Time Spent';
$lang['task_timesheets'] = 'Timesheets';
$lang['task_log_time_start'] = 'Start Time';
$lang['task_log_time_end'] = 'End Time';
$lang['task_single_log_user'] = 'Member';
$lang['milestone_description'] = 'Description';
$lang['description_visible_to_customer'] = 'Show description to customer';
$lang['upcoming_tasks'] = 'Upcoming Tasks';
$lang['payment_credit_card_number'] = 'Card Number';
$lang['payment_credit_card_expiration_date'] = 'Expiration Date';
$lang['payment_billing_email'] = 'Email';
$lang['submit_payment'] = 'Submit Payment';
$lang['custom_field_disallow_customer_to_edit'] = 'Disallow customer to edit this field';
$lang['project_due_notice'] = 'This project is overdue by %s days';
$lang['not_lead_added_attachment'] = 'added new attachment to lead %s';
$lang['lead_note_date_added'] = 'Note added: %s';
$lang['recurring_custom'] = 'Custom';
$lang['invoice_recurring_months'] = 'Month(s)';
$lang['invoice_recurring_years'] = 'Years(s)';
$lang['invoice_recurring_days'] = 'Day(s)';
$lang['invoice_recurring_weeks'] = 'Week(s)';
$lang['document_direction'] = 'Direction';
$lang['notify_project_members_status_change'] = 'Notify project members that status is changed';
$lang['not_project_status_updated'] = 'Project status updated from %s to %s';
$lang['ticket_not_found'] = 'Ticket not found';
$lang['project_not_found'] = 'Project not found';
$lang['export_project_data'] = 'Export project data';
$lang['total_project_members'] = 'Total Project Members';
$lang['total_project_files'] = 'Files attached';
$lang['total_project_discussions_created'] = 'Discussions created';
$lang['project_member'] = 'Project Member';
$lang['total_project_discussions_comments'] = 'Total discussion comments';
$lang['staff_total_task_assigned'] = 'Total tasks assigned';
$lang['staff_total_comments_on_tasks'] = 'Comments on tasks';
$lang['project_members_overview'] = 'Project members overview';
$lang['project_milestones_overview'] = 'Milestones overview';
$lang['total_tasks_in_milestones'] = 'Total tasks assigned';
$lang['total_task_members_assigned'] = 'Total members assigned';
$lang['total_task_members_followers'] = 'Total followers';
$lang['total_milestones'] = 'Total milestones';
$lang['total_project_worked_days'] = 'Total days worked';
$lang['finance_overview'] = 'Finance Overview';
$lang['project_custom_fields'] = 'Custom fields';
$lang['total_tickets_related_to_project'] = 'Total tickets linked to project';
$lang['projects_total_invoices_created'] = 'Total invoices created';
$lang['do_not_send_invoice_payment_email_template_contact'] = 'Do not send invoice payment recorded email to customer contacts';
$lang['no_preview_available_for_file'] = 'No preview available for this file.';
$lang['project_activity_deleted_file_discussion_comment'] = 'File discussion comment deleted';
$lang['email_template_discussion_info'] = 'This template is used for both project discussion comments emails. (files discussions and regular discussions)';
$lang['format_a4_portrait_size'] = 'Portrait';
$lang['only_show_contact_tickets'] = 'In customers area only show tickets related to the logged in contact (Primary contact not applied)';
$lang['cancel_overdue_reminders_invoice'] = 'Prevent sending overdue reminders for this invoice';
$lang['customer_shipping_address_notice'] = 'Do not fill shipping address information if you won\'t use shipping address on customer invoices';
$lang['timesheets_overview'] = 'Timesheets overview';
$lang['invoice_status_draft'] = 'Draft';
$lang['save_as_draft'] = 'Save as Draft';
$lang['convert_and_save_as_draft'] = 'Convert and save as draft';
$lang['convert'] = 'Convert';
$lang['exclude_invoices_draft_from_client_area'] = 'Exclude invoices with draft status from customers area';
$lang['invoice_draft_status_info'] = 'This invoice is with status Draft, status will be auto changed when you send the invoice to the customer or mark as sent.';
$lang['task_info'] = 'Task Info';
$lang['recurring_tasks'] = 'Recurring';
$lang['task_repeat_every'] = 'Repeat every';
$lang['task_recurring_months'] = 'Month(s)';
$lang['task_recurring_years'] = 'Years(s)';
$lang['task_recurring_days'] = 'Day(s)';
$lang['task_recurring_weeks'] = 'Week(s)';
$lang['todays_tasks'] = 'Today\'s tasks';
$lang['payment_mode_invoices_only'] = 'Invoices Only';
$lang['payment_mode_expenses_only'] = 'Expenses Only';
$lang['task_no_checklist_items_found'] = 'Checklist items not found for this task';
$lang['task_no_description'] = 'No description for this task';
$lang['expenses_reminders'] = 'Reminders';
$lang['expense_set_reminder_title'] = 'Set Expense Reminder';
$lang['calendar_expense_reminder'] = 'Expense Reminders';
$lang['recurring_task'] = 'Recurring Task';
$lang['disable_email_from_being_sent'] = 'Disable this email from being sent';
$lang['not_sent_indicator'] = 'Not Sent';
$lang['proposal_status_revised'] = 'Revised';
$lang['customer_currency_change_notice'] = 'If the customer use other currency then the base currency make sure you select the appropriate currency for this customer. Changing the currency is not possible after transactions are recorded.';
$lang['click_to_add_content'] = 'Click here to add content';
$lang['related_to_project'] = 'This %s is related to %s: %s';
$lang['back_to_lead'] = 'Back to lead';
$lang['add_task_timer_started_warning'] = 'Stop current started timer for this task to be able to add new timer manually.';
$lang['sending_email_contact_permissions_warning'] = 'Failed to auto select customer contacts. Make sure that the customer has active contacts and associated contacts with email notifications for %s enabled.';
$lang['currently_supported_currencies'] = 'Currently supported currencies';
$lang['authorize_notice'] = 'SSL is required if you\'re using the Authorize.Net AIM payment API. Authorize.net only supports 1 currency per account. Make sure you add only 1 currency associated with your Authorize account in the currencies field.';
$lang['settings_paymentmethod_developer_mode'] = 'Developer Mode';
$lang['payment_cardholder_name'] = 'Cardholder\'s Name';
$lang['settings_paymentmethod_authorize_api_login_id'] = 'API Login ID';
$lang['settings_paymentmethod_mollie_api_key'] = 'API Key';
$lang['settings_paymentmethod_authorize_api_transaction_key'] = 'API Transaction ID';
$lang['settings_paymentmethod_authorize_secret_key'] = 'Secret Key';
$lang['leads_report_converted_notice'] = 'Only leads that belongs in the default Customer status will be taken as converted leads, if the leads belongs to the default status client and its not converted to customer will be still counted as converted lead';
$lang['payment_method'] = 'Payment Method';
$lang['payment_method_info'] = 'Some payment gateways support different/multiple payment methods like Credit Card, PayPal, Bank.';
$lang['dropbox_app_key'] = 'Dropbox APP Key';
$lang['project_invoice_select_all_expenses'] = 'Select all expenses';
$lang['role_update_staff_permissions'] = 'Update all staff members permissions that are using this role';
$lang['customer_active'] = 'Active';
$lang['note_updated_successfully'] = 'Note updated successfully';
$lang['update_note'] = 'Update note';
$lang['update_comment'] = 'Update comment';
$lang['comment_updated_successfully'] = 'Comment updated successfully';
$lang['staff_send_welcome_email'] = 'Send welcome email';
$lang['proposal_warning_email_change'] = 'Email changed for %s. This %s is linked to proposal/s. Do you want to update all proposals emails linked to %s?';
$lang['update_proposal_email_yes'] = 'Yes update all linked emails.';
$lang['update_proposal_email_no'] = 'No, i will update manually.';
$lang['proposals_emails_updated'] = 'All proposals emails linked to this %s updated to %s';
$lang['custom_field_company'] = 'Company';
$lang['actions'] = 'Actions';
$lang['project_mark_as'] = 'Mark as %s';
$lang['todo_edit_title'] = 'Edit todo item';
$lang['additional_action_required'] = 'Additional action required!';
$lang['project_mark_tasks_finished_confirm'] = 'Confirm';
$lang['project_marked_as_success'] = 'Project marked as %s successfully';
$lang['project_marked_as_failed'] = 'Failed to mark project as %s';
$lang['auto_assign_customer_admin_after_lead_convert'] = 'Auto assign as admin to customer after convert';
$lang['auto_assign_customer_admin_after_lead_convert_help'] = 'If this option is set to YES the staff member that converted lead to customer will be auto assigned as customer admin. NOTE: This option will apply only on staff members that do not have permission for customers VIEW';
$lang['auto_close_tickets_disable'] = 'Set 0 to disable';
$lang['task_checklist_item_completed_by'] = 'Completed by %s';
$lang['staff_email_signature_help'] = 'If empty default email signature from settings will be used';
$lang['default_task_priority'] = 'Default Priority';
$lang['project_send_created_email'] = 'Send project created email';
$lang['show_transactions_on_invoice_pdf'] = 'Show invoice payments (transactions) on PDF';
$lang['bulk_actions'] = 'Bulk Actions';
$lang['transfer_actions'] = 'Transfer Actions';
$lang['transfer_successfully'] = '%s transferred successfully customer';
$lang['transfer_failed'] = 'Transfer failed';
$lang['additional_filters'] = 'Additional Filters';
$lang['expenses_available_to_bill'] = 'Expenses available to bill';
$lang['bulk_action_customers_groups_warning'] = 'If you do not select any group all groups assigned to the selected customers will be removed.';
$lang['customer_attachments_show_in_customers_area'] = 'Show to customers area';
$lang['customer_attachments_show_notice'] = 'Only files uploaded from customer profile have ability to show/hide in customers area.';
$lang['customer_profile_files'] = 'Files';
$lang['no_files_found'] = 'No Files Found';
$lang['custom_field_column'] = 'Grid (Bootstrap Column eq. 12) - Max is 12';
$lang['task_status'] = 'Status';
$lang['task_status_1'] = 'Not Started';
$lang['task_status_2'] = 'Awaiting Feedback';
$lang['task_status_3'] = 'Testing';
$lang['task_status_4'] = 'In Progress';
$lang['task_status_5'] = 'Complete';
$lang['task_mark_as'] = 'Mark as %s';
$lang['task_marked_as_success'] = 'Task marked as %s successfully';
$lang['search_tasks'] = 'Search Tasks';
$lang['tasks_kanban_limit'] = 'Limit tasks kan ban rows per status';
$lang['show_on_invoice_on_pdf'] = 'Show %s on Invoice PDF';
$lang['show_pay_link_to_invoice_pdf'] = 'Show Pay Invoice link to PDF (Not applied if invoice status is Cancelled)';
$lang['no_leads_found'] = 'No Leads Found';
$lang['created_today'] = 'Created Today';
$lang['total_tasks_deleted'] = 'Total Tasks Deleted: %s';
$lang['total_tickets_delete'] = 'Total Tickets Deleted: %s';
$lang['format_letter_portrait'] = 'Letter Portrait';
$lang['format_letter_landscape'] = 'Letter Landscape';
$lang['period_datepicker'] = 'Period';
$lang['total_by_hourly_rate'] = 'Total By Hourly Rate';
$lang['staff_hourly_rate'] = 'Hourly Rate';
$lang['remove_tax_name_from_item_table_help'] = 'eq. Item TAX 15&#37; will be shown as 15&#37; without the tax name (Not applied if multiple taxes with the same name and tax percent found for item)';
$lang['back_to_project'] = 'Back to Project';
$lang['view_kanban'] = 'View Kan Ban';
$lang['invoice_is_overdue'] = 'This invoice is overdue by %s days';
$lang['time_decimal'] = 'Time (decimal)';
$lang['time_h'] = 'Time (h)';
$lang['proposal_number_prefix'] = 'Proposal Number Prefix';
$lang['settings_number_padding_prefix'] = 'Number padding zero\'s for prefix formats <br /> <small>eq. If this value is 3 the number will be formatted: 005 or 025</small>';
$lang['this_week_payments'] = 'This Week Payments';
$lang['last_week_payments'] = 'Last Week Payments';
$lang['not_published_new_post'] = 'published new post';
$lang['expense_name'] = 'Name';
$lang['expense_name_help'] = 'For personal usage';
$lang['adjustments'] = 'Adjustments';
$lang['payments_received'] = 'Payments Received';
$lang['not_lead_activity_created_proposal'] = 'Created new proposal - %s';
$lang['lead_title'] = 'Position';
$lang['lead_address'] = 'Address';
$lang['lead_city'] = 'City';
$lang['lead_state'] = 'State';
$lang['lead_country'] = 'Country';
$lang['lead_zip'] = 'Zip Code';
$lang['lead_is_public_yes'] = 'Yes';
$lang['lead_is_public_no'] = 'No';
$lang['lead_info'] = 'Lead Information';
$lang['lead_general_info'] = 'General Information';
$lang['lead_latest_activity'] = 'Latest Activity';
$lang['item_description_new_lines_notice'] = 'New lines are not supported for item description. Use the item long description instead.';
$lang['estimates_report'] = 'Estimates Report';
$lang['confirm'] = 'Confirm';
$lang['delete_staff'] = 'Delete Staff Member';
$lang['delete_staff_info'] = 'Some data for this staff member needs to be transferred to another user. Please select user where you want to transfer the data.';
$lang['estimate_items'] = 'Estimate Items';
$lang['no_proposals_found'] = 'No Proposals Found';
$lang['no_estimates_found'] = 'No Estimates Found';
$lang['pipeline_limit_status'] = 'Pipeline limit per status';
$lang['settings_update'] = 'System Update';
$lang['purchase_key'] = 'Purchase Key';
$lang['update_now'] = 'Update Now';
$lang['update_available'] = 'An update is available';
$lang['latest_version'] = 'Latest Version';
$lang['your_version'] = 'Your Version';
$lang['using_latest_version'] = 'You are using the latest version';
$lang['mark_as_active'] = 'Mark as active';
$lang['customer_inactive_message'] = 'This is inactive customer profile and some features may be disabled';
$lang['active_customers'] = 'Active Customers';
$lang['inactive_active_customers'] = 'Inactive Customers';
$lang['include_proposal_items_merge_field_help'] = 'Include proposal items with merge field anywhere in proposal content as %s';
$lang['all_data_synced_successfully'] = 'All data synced successfully';
$lang['sync_now'] = 'Sync Now';
$lang['sync_data'] = 'Sync Data';
$lang['sync_proposals_up_to_date'] = 'All proposals are up to date, nothing to sync';
$lang['proposal_sync_1_info'] = 'All proposal data is stored separately for each proposal after creation. Updating the %s info won\'t affect previous created proposals for this %s.';
$lang['proposal_sync_2_info'] = 'If you recently updated your %s info you can sync all new data to associated proposals. Here is a list of fields you can sync.';
$lang['expense_include_additional_data_on_convert'] = 'Include additional details to item long description taken from this expense.';
$lang['calendar_events_limit'] = 'Calendar Events Limit (Month and Week View)';
$lang['show_page_number_on_pdf'] = 'Show page number on PDF';
$lang['customer_active_inactive_help'] = 'Won\'t be shown in dropdowns when creating new records';
$lang['item_groups'] = 'Groups';
$lang['item_group'] = 'Item Group';
$lang['item_group_name'] = 'Group Name';
$lang['new_item_group'] = 'New Group';
$lang['show_setup_menu_item_only_on_hover'] = 'Show setup menu item only when hover with mouse on main sidebar area';
$lang['internal_article'] = 'Internal Article';
$lang['expenses_created_from_this_recurring_expense'] = 'Created expenses from this recurring expense';
$lang['convert_to_task'] = 'Convert To Task';
$lang['next_invoice_date'] = 'Next Invoice Date: %s';
$lang['next_expense_date'] = 'Next Expense Date: %s';
$lang['invoice_recurring_from'] = 'This invoice is created from recurring invoice with number: %s';
$lang['expense_recurring_from'] = 'This expense is created from the following recurring expense: %s';
$lang['child_invoices'] = 'Child Invoices';
$lang['child_expenses'] = 'Child Expenses';
$lang['no_announcements'] = 'No Announcements';
$lang['unit'] = 'Unit';
$lang['permission_view_own'] = 'View (Own)';
$lang['permission_global'] = 'Global';
$lang['permission_customers_based_on_admins'] = 'Based on customer admins';
$lang['permission_payments_based_on_invoices'] = 'Based on invoices VIEW (Own) permission';
$lang['permission_projects_based_on_assignee'] = 'If staff member do not have permission VIEW (Global) will be shown only projects where staff member is added as project member.';
$lang['permission_tasks_based_on_assignee'] = 'If staff member do not have permission VIEW (Global) will be shown only tasks where staff member is follower,assigned, task is public or in Setup->Settings->Tasks-> Allow all staff to see all tasks related to projects is set to YES when task is linked to project.';
$lang['settings_paymentmethod_default_selected_on_invoice'] = 'Selected by default on invoice';
$lang['paymentmethod_braintree_merchant_id'] = 'Merchant ID';
$lang['paymentmethod_braintree_private_key'] = 'Private Key';
$lang['paymentmethod_braintree_public_key'] = 'Public Key';
$lang['company_requires_vat_number_field'] = 'Company requires the usage of the VAT Number field';
$lang['no_company_view_profile'] = 'Person - View Profile';
$lang['company_is_required'] = 'Company field is required?';
$lang['estimate_invoiced'] = 'Invoiced';
$lang['file_date_uploaded'] = 'Date Uploaded';
$lang['allow_contact_to_delete_files'] = 'Allow contacts to delete own files uploaded from customers area';
$lang['file'] = 'File';
$lang['customer_contact_person_only_one_allowed'] = 'Only 1 contact is allowed when the company field is not filled. The system will cast this customer as person';
$lang['web_to_lead'] = 'Web to Lead';
$lang['web_to_lead_form'] = 'Web to Lead Form';
$lang['new_form'] = 'New Form';
$lang['form_name'] = 'Form Name';
$lang['cf_option_in_use'] = 'An option you removed is in use and cant be removed. The option is auto appended to the existing options.';
$lang['form_builder'] = 'Form Builder';
$lang['form_information'] = 'Form Information & Setup';
$lang['form_builder_create_form_first'] = 'Create form first to be able to use the form builder.';
$lang['notify_assigned_user'] = 'Responsible person';
$lang['form_recaptcha'] = 'Use Google Recaptcha';
$lang['form_lang_validation'] = 'Language';
$lang['form_lang_validation_help'] = 'For validation messages';
$lang['form_btn_submit_text'] = 'Submit button text';
$lang['form_success_submit_msg'] = 'Message to show after form is succcesfully submitted';
$lang['total_submissions'] = 'Total Submissions';
$lang['form_integration_code'] = 'Integration Code';
$lang['not_lead_imported_from_form'] = 'New Lead Imported from Web to Lead Form - %s';
$lang['not_lead_activity_log_attachment'] = 'Attachment Imported from form - %s';
$lang['form_integration_code_help'] = 'Copy & Paste the code anywhere in your site to show the form, additionally you can adjust the width and height px to fit for your website.';
$lang['invoice_not_found'] = 'Invoice not found';
$lang['estimate_not_found'] = 'Estimate not found';
$lang['expense_not_found'] = 'Expense not found';
$lang['proposal_not_found'] = 'Proposal not found';
$lang['new_task_assigned_non_user'] = 'New task is assigned to you - %s';
$lang['no_child_found'] = 'No Child %s Found';
$lang['company_vat_number'] = 'VAT Number';
$lang['not_lead_assigned_from_form'] = 'New lead is assigned to you';
$lang['not_lead_activity_assigned_from_form'] = 'Lead assigned to %s';
$lang['form_allow_duplicate'] = 'Allow duplicate %s to be inserted into database?';
$lang['track_duplicate_by_field'] = 'Prevent duplicate on field';
$lang['and_track_duplicate_by_field'] = '+ field (leave blank to track duplicates only by 1 field)';
$lang['create_the_duplicate_form_data_as_task'] = 'Create duplicate %s data as task and assign to responsible staff member';
$lang['create_the_duplicate_form_data_as_task_help'] = 'Used for further review on the submission and take the necessary action';
$lang['currently_selected'] = 'Currently Selected';
$lang['search_ajax_empty'] = 'Select and begin typing';
$lang['search_ajax_placeholder'] = 'Type to search...';
$lang['search_ajax_searching'] = 'Searching...';
$lang['search_ajax_initialized'] = 'Start typing to search';
$lang['lead_description'] = 'Description';
$lang['lead_website'] = 'Website';
$lang['invoice_activity_auto_converted_from_estimate'] = 'Invoice auto created from estimate with number %s';
$lang['hour_of_day_perform_auto_operations'] = 'Hour of day to perform automatic operations';
$lang['hour_of_day_perform_auto_operations_format'] = '24 hours format eq. 9 for 9am or 15 for 3pm.';
$lang['inv_hour_of_day_perform_auto_operations_help'] = 'Used for recurring invoices, overdue notices etc..';
$lang['use_minified_files'] = 'Use minified files version for css and js (only system files)';
$lang['logo_favicon_changed_notice'] = 'Logo or Favicon change detected. If you still see the original CRM logo try to clear your browser cache';
$lang['kb_search_articles'] = 'Search Knowledge Base Articles';
$lang['kb_search'] = 'Search';
$lang['have_a_question'] = 'Have a question?';
$lang['card_expiration_year'] = 'Expiration Year';
$lang['card_expiration_month'] = 'Expiration Month';
$lang['client_website'] = 'Company Website';
$lang['search_project_members'] = 'Search Project Members...';
$lang['cf_translate_input_link_title'] = 'Title';
$lang['cf_translate_input_link_url'] = 'URL';
$lang['cf_translate_input_link_tip'] = 'Click here to add link';
$lang['task_edit_delete_timesheet_notice'] = 'Timesheet task is %s, you cant %s the timesheet.';
$lang['department_username'] = 'IMAP Username';
$lang['department_username_help'] = 'Only fill this field if your IMAP server use username to login instead email address. Note that you will still need to add email address.';
$lang['total_tickets_deleted'] = 'Total tickets deleted: %s';
$lang['ticket_linked_to_project'] = 'This ticket is linked to project: %s';
$lang['only_own_files_contacts'] = 'Contacts see only own files uploaded in customer area (files uploaded in customer profile)';
$lang['only_own_files_contacts_help'] = 'If you share the file manually from customer profile to other contacts they wil be able to see the file.';
$lang['share_file_with'] = 'Share File With';
$lang['file_share_visibility_notice'] = 'This file is not shared with contacts, toggle visibility again to reload';
$lang['share_file_with_show'] = 'This file is shared with: %s';
$lang['allow_primary_contact_to_view_edit_billing_and_shipping'] = 'Allow primary contact to view/edit billing & shipping details';
$lang['estimate_due_after'] = 'Estimate Due After (days)';
$lang['my_timesheets'] = 'My Timesheets';
$lang['today'] = 'Today';
$lang['open_in_dropbox'] = 'Open In Dropbox';
$lang['show_primary_contact'] = 'Show primary contact full name on %s';
$lang['view_members_timesheets'] = 'View all timesheets';
$lang['priority'] = 'Priority level';
$lang['fetch_from_google'] = 'Fetch from google';
$lang['customer_fetch_lat_lng_usage'] = 'Fill address, city and country before fetching to get best result.';
$lang['g_search_address_not_found'] = 'The address couldn\'t be found, please try again';
$lang['proposals_report'] = 'Proposals Report';
$lang['staff_members_open_tickets_to_all_contacts_help'] = 'If staff member don\'t have permission for customers VIEW only will be able to create new tickets from admin area to customer contacts where is assigned as customer admin.';
$lang['staff_members_open_tickets_to_all_contacts'] = 'Allow staff members to open tickets to all contacts?';
$lang['charts_based_report'] = 'Charts Based Report';
$lang['responsible_admin'] = 'Responsible admin';
$lang['tags'] = 'Tags';
$lang['tag'] = 'Tag';
$lang['customer_map_notice'] = 'Add longitude and latitude to show the map here.';
$lang['default_view'] = 'Default View';
$lang['day'] = 'Day';
$lang['agenda'] = 'Agenda';
$lang['timesheet_tags'] = 'Timesheet Tags';
$lang['show_more'] = 'Show more';
$lang['show_less'] = 'Show Less';
$lang['project_completed_date'] = 'Completed Date';
$lang['language'] = 'Language';
$lang['this_week'] = 'This Week';
$lang['last_week'] = 'Last Week';
$lang['this_month'] = 'This Month';
$lang['last_month'] = 'Last Month';
$lang['no_description_project'] = 'No description for this project';
$lang['sales_string'] = 'Sales';
$lang['no_project_members'] = 'No members for this project';
$lang['search_by_tags'] = 'Use # + tagname to search by tags';
$lang['project_status_5'] = 'Cancelled';
$lang['not_activity_new_reminder_created'] = 'set a new reminder for %s with date %s';
$lang['not_activity_new_task_created'] = 'Created new task - %s';
$lang['recurring_invoice_draft_notice'] = 'This invoice is with status draft, you need to mark this invoice as sent. Recurring invoices with status draft won\'t be recreated by cron job.';
$lang['recurring_recreate_hour_notice'] = '%s will be recreated on specific hour of the day based from the setting located at Setup->Settings-Cron Job';
$lang['invoice_project_include_timesheets_notes'] = 'Include each timesheet note in item description';
$lang['events'] = 'Events';
$lang['clear'] = 'Clear';
$lang['auto_mark_as_public'] = 'Auto mark as public';
$lang['time_format'] = 'Time Format';
$lang['time_format_24'] = '24 hours';
$lang['time_format_12'] = '12 hours';
$lang['delete_activity_log_older_then'] = 'Delete system activity log older then X months';
$lang['mark_as_read'] = 'Mark as Read';
$lang['mark_all_as_read'] = 'Mark all as read';
$lang['tax_1'] = 'Tax 1';
$lang['tax_2'] = 'Tax 2';
$lang['total_with_tax'] = 'Total with tax';
$lang['new_task_auto_assign_current_member'] = 'Auto assign task creator when new task is created';
$lang['new_task_auto_assign_current_member_help'] = 'Not applied if task is linked to project and the creator is not project member';
$lang['copy_project_tasks_status'] = 'Tasks Status';
$lang['tasks_summary'] = 'Tasks Summary';
$lang['vault'] = 'Vault';
$lang['new_vault_entry'] = 'New Vault Entry';
$lang['server_address'] = 'Server Address';
$lang['port'] = 'Port';
$lang['vault_username'] = 'Username';
$lang['vault_password'] = 'Password';
$lang['vault_description'] = 'Short Description';
$lang['vault_entry'] = 'Vault Entry';
$lang['no_port_provided'] = 'Not provided';
$lang['view_password'] = 'View Password';
$lang['security_reasons_re_enter_password'] = 'For security reasons please enter your password below';
$lang['password_change_fill_notice'] = 'Only fill password field if you want to change the password';
$lang['vault_password_user_not_correct'] = 'Your password is not correct, please try again';
$lang['no_vault_entries'] = 'Vault entries not found for this customer.';
$lang['vault_entry_created_from'] = 'This vault entry is created by %s';
$lang['vault_entry_last_update'] = 'Last updated by %s';
$lang['vault_entry_visible_to_all'] = 'Visible to all staff member who have access to this customer';
$lang['vault_entry_visible_creator'] = 'Visible only to me (administrators are not excluded)';
$lang['vault_entry_visible_administrators'] = 'Visible only to administrators';
$lang['my_reminders'] = 'My Reminders';
$lang['reminder_related'] = 'Related to';
$lang['event_notification'] = 'Notification';
$lang['days'] = 'Days';
$lang['reminder_notification_placeholder'] = 'Eq. 30 minutes before';
$lang['event_color'] = 'Event Color';
$lang['group_by_task'] = 'Group by Task';
$lang['save'] = 'Save';
$lang['disable_languages'] = 'Disable Languages';
$lang['not_customer_viewed_invoice'] = 'An invoice with number %s has been viewed';
$lang['not_customer_viewed_estimate'] = 'An estimate with number %s has been viewed';
$lang['not_customer_viewed_proposal'] = 'An proposal with number %s has been viewed';
$lang['display_inline'] = 'Display Inline';
$lang['email_header'] = 'Predefined Header';
$lang['email_footer'] = 'Predefined Footer';
$lang['exclude_proposal_from_client_area_with_draft_status'] = 'Exclude proposals with draft status from customers area';
$lang['pusher_app_key'] = 'APP Key';
$lang['pusher_app_secret'] = 'APP Secret';
$lang['pusher_app_id'] = 'APP ID';
$lang['pusher_cluster_notice'] = 'Leave blank to use default pusher cluster.';
$lang['pusher_enable_realtime_notifications'] = 'Enable Real Time Notifications';
$lang['task_is_overdue'] = 'This task is overdue';
$lang['this_year'] = 'This Year';
$lang['last_year'] = 'Last Year';
$lang['customer_statement'] = 'Statement';
$lang['customer_statement_for'] = 'Customer Statement For %s';
$lang['account_summary'] = 'Account Summary';
$lang['statement_beginning_balance'] = 'Beginning Balance';
$lang['invoiced_amount'] = 'Invoiced Amount';
$lang['amount_paid'] = 'Amount Paid';
$lang['statement_from_to'] = '%s To %s';
$lang['customer_statement_info'] = 'Showing all invoices and payments between %s and %s';
$lang['balance_due'] = 'Balance Due';
$lang['statement_heading_date'] = 'Date';
$lang['statement_heading_details'] = 'Details';
$lang['statement_heading_amount'] = 'Amount';
$lang['statement_heading_payments'] = 'Payments';
$lang['statement_heading_balance'] = 'Balance';
$lang['statement_invoice_details'] = 'Invoice %s - due on %s';
$lang['statement_payment_details'] = 'Payment (%s) to invoice %s';
$lang['statement_bill_to'] = 'To';
$lang['send_to_email'] = 'Send to Email';
$lang['statement_sent_to_client_success'] = 'The statement is sent successfully to the client';
$lang['statement_sent_to_client_fail'] = 'Problem while sending the statement';
$lang['view_account_statement'] = 'View Account Statement';
$lang['text_not_recommended_for_servers_limited_resources'] = 'Not recommended if the server have limited resources. Eq shared hosting';
$lang['tasks_bull_actions_assign_notice'] = 'If the task is linked to project and the staff member you are assigning the task to is not project member this staff will be auto added as member.';
$lang['company_information'] = 'Company Information';
$lang['ticket_form'] = 'Ticket Form';
$lang['ticket_form_subject'] = 'Subject';
$lang['ticket_form_name'] = 'Your name';
$lang['ticket_form_email'] = 'Email Address';
$lang['ticket_form_department'] = 'Department';
$lang['ticket_form_message'] = 'Message';
$lang['ticket_form_priority'] = 'Priority';
$lang['ticket_form_service'] = 'Service';
$lang['ticket_form_submit'] = 'Submit';
$lang['ticket_form_attachments'] = 'Attachments';
$lang['success_submit_msg'] = 'Thank you for contacting us. We will get back to you shortly.';
$lang['vault_entry_share_on_projects'] = 'Share this vault entry in projects with project members';
$lang['project_shared_vault_entry_login_details'] = 'View Customer Site Login Details';
$lang['iso_code'] = 'ISO Code';
$lang['estimates_not_invoiced'] = 'Not Invoiced';
$lang['show_on_ticket_form'] = 'Show on ticket form';
$lang['cancel_upload'] = 'Cancel Upload';
$lang['show_table_export_button'] = 'Show table export button';
$lang['show_table_export_all'] = 'To all staff members';
$lang['show_table_export_admins'] = 'Only to administrators';
$lang['show_table_export_hide'] = 'Hide export button for all staff members';
$lang['task_created_by'] = 'Created by %s';
$lang['validation_extension_not_allowed'] = 'File extension not allowed';
$lang['allow_staff_view_proposals_assigned'] = 'Allow staff members to view proposals where they are assigned to';
$lang['task_users_working_on_tasks_multiple'] = 'Currently %s are working on this task';
$lang['task_users_working_on_tasks_single'] = 'Currently %s is working on this task';
$lang['estimated_hours'] = 'Estimated Hours';
$lang['two_factor_auth_failed_to_send_code'] = 'Failed to send two step authentication code to email, SMTP settings may not be configured properly';
$lang['two_factor_auth_code_sent_successfully'] = 'An email has been sent to %s with verification code to verify your login';
$lang['enable_two_factor_authentication'] = 'Enable Email Two Factor Authentication';
$lang['two_factor_authentication_info'] = 'Two factor authentication is provided by email, before enable two factor authentication make sure that your SMTP settings are properly configured and the system is able to send an email. Unique authentication key will be sent to email upon login.';
$lang['timesheets_overview_all_members_notice_admins'] = 'Timesheets overview for all staff members is only available for administrators.';
$lang['two_factor_authentication'] = 'Two Factor Authentication';
$lang['two_factor_authentication_code'] = 'Code';
$lang['admin_two_factor_auth_heading'] = 'Authentication Code';
$lang['two_factor_code_not_valid'] = 'Authentication code not valid';
$lang['back_to_login'] = 'Go back to login';
$lang['enter_activity'] = 'Enter Activity';
$lang['attach_files'] = 'Attach Files';
$lang['no_tags_used'] = 'No tags used by the system';
$lang['exclude_completed_tasks'] = 'Exclude Completed Tasks';
$lang['modal_width_class'] = 'Modal Width Class';
$lang['contract_copy'] = 'Copy';
$lang['contract_copied_successfully'] = 'Contract copied successfully';
$lang['contract_copied_fail'] = 'Failed to copy contract';
$lang['project_marked_as_finished_to_contacts'] = 'Send <b>Project Marked as Finished</b> email to customer contacts';
$lang['only_admins'] = 'Only administrators';
$lang['new_notification'] = 'New Notification!';
$lang['enable_desktop_notifications'] = 'Enable Desktop Notifications';
$lang['save_and_send'] = 'Save & Send';
$lang['private'] = 'Private';
$lang['task_created_at'] = 'Created at %s';
$lang['hide_notified_reminders_from_calendar'] = 'Hide notified reminders from calendar';
$lang['last_active'] = 'Last Active';
$lang['open_ticket'] = 'Open Ticket';
$lang['task_add_description'] = 'Add Description';
$lang['project_setting_create_tasks'] = 'create tasks';
$lang['project_setting_edit_tasks'] = 'edit tasks (only tasks created from contact)';
$lang['items_report'] = 'Items Report';
$lang['reports_item'] = 'Item';
$lang['quantity_sold'] = 'Quantity Sold';
$lang['total_amount'] = 'Total Amount';
$lang['avg_price'] = 'Average Price';
$lang['item_report_paid_invoices_notice'] = 'Items report is generated only from paid invoices before discounts and taxes.';
$lang['overview'] = 'Overview';
$lang['timer_started_change_status_in_progress'] = 'Change task status to In Progress on timer started (valid only if task status is Not Started)';
$lang['company_info_format'] = 'Company Information Format (PDF and HTML)';
$lang['customer_info_format'] = 'Customer Information Format (PDF and HTML)';
$lang['custom_field_info_format_embed_info'] = 'Custom fields for %s can be easily embedded into PDF and HTML documents by adding the merge fields into the page format at the following page: %s';
$lang['transfer_lead_notes_to_customer'] = 'Transfer lead notes to customer profile';
$lang['authorized_signature_text'] = 'Authorized Signature';
$lang['show_pdf_signature_invoice'] = 'Show PDF Signature on Invoice';
$lang['show_pdf_signature_estimate'] = 'Show PDF Signature on Estimate';
$lang['signature'] = 'Signature';
$lang['signature_image'] = 'Signature Image';
$lang['insert_checklist_templates'] = 'Insert Checklist Templates';
$lang['save_as_template'] = 'Save as Template';
$lang['invoice_item_add_edit_rate_currency'] = 'Rate - %s';
$lang['total_files_deleted'] = 'Total files deleted: %s';
$lang['invalid_transaction'] = 'Invalid Transaction. Please try again.';
$lang['payment_gateway_payu_money_key'] = 'PayU Money Key';
$lang['payment_gateway_payu_money_salt'] = 'PayU Money Salt';
$lang['settings_paymentmethod_description'] = 'Gateway Dashbord Payment Description';
$lang['default_ticket_reply_status'] = 'Default status selected when replying to ticket';
$lang['ticket_add_response_and_back_to_list'] = 'Return to ticket list after response is submitted';
$lang['default_task_status'] = 'Default status when new task is created';
$lang['custom_field_pdf_html_help'] = 'Make sure you check Show on client portal field if you want the custom fields to be visible to customers area and when customer download PDF or receive PDF via email.';
$lang['auto'] = 'Auto';
$lang['email_queue'] = 'Email Queue';
$lang['email_queue_enabled'] = 'Enable Email Queue';
$lang['email_queue_skip_attachments'] = 'Do not add emails with attachments in the queue?';
$lang['disable'] = 'Disable';
$lang['enable'] = 'Enable';
$lang['auto_dismiss_desktop_notifications_after'] = 'Auto Dismiss Desktop Notifications After X Seconds (0 to disable)';
$lang['proposal_info_format'] = 'Proposal Info Format (PDF and HTML)';
$lang['hide_tasks_on_main_tasks_table'] = 'Hide project tasks on main tasks table (admin area)';
$lang['ticket_replies_order'] = 'Ticket Replies Order';
$lang['ticket_replies_order_notice'] = 'The initial ticket message will be always shown as first.';
$lang['invoice_cancelled_email_disabled'] = 'Invoice is cancelled. Unmark as cancelled to enable email to client';
$lang['email_notifications'] = 'Email Notifications';
$lang['invoice_activity_record_payment_email_to_customer'] = 'Payment recorded, email sent to: %s';
$lang['exclude_inactive'] = 'Exclude Inactive';
$lang['disable_all'] = 'Disable All';
$lang['enable_all'] = 'Enable All';
$lang['reccuring_invoice_option_gen_and_send'] = 'Generate and autosend the renewed invoice to the customer';
$lang['reccuring_invoice_option_gen_unpaid'] = 'Generate a Unpaid Invoice';
$lang['reccuring_invoice_option_gen_draft'] = 'Generate a Draft Invoice';
$lang['event_created_by'] = 'This event is created by %s';
$lang['customers_assigned_to_me'] = 'Customers assigned to me';
$lang['bcc_all_emails'] = 'BCC All Emails To';
$lang['confirmation_of_identity'] = 'Confirmation Of Identity';
$lang['accept_identity_confirmation'] = 'Require identity confirmation on accept';
$lang['accepted_identity_info'] = 'This %s is accepted by %s on %s from IP address %s';
$lang['clear_this_information'] = 'Clear This Information';
$lang['new_task_auto_follower_current_member'] = 'Auto add task creator as task follower when new task is created';
$lang['expenses_report_net'] = 'Net Amount (Subtotal)';
$lang['expense_field_billable_help'] = 'If billable, %s can be added to invoice long description.';
$lang['task_biillable_checked_on_creation'] = 'Billable option is by default checked when new task is created? (only from admin area)';
$lang['pause_overdue_reminders'] = 'Pause Overdue Reminders';
$lang['resume_overdue_reminders'] = 'Resume Overdue Reminders';
$lang['credit_notes'] = 'Credit Notes';
$lang['credit_note'] = 'Credit Note';
$lang['credit_note_lowercase'] = 'credit note';
$lang['credit_note_not_found'] = 'Credit note not found';
$lang['credit_note_date'] = 'Credit Note Date';
$lang['credit_date'] = 'Date';
$lang['settings_sales_next_credit_note_number'] = 'Next Credit Note Number';
$lang['credit_note_number_prefix'] = 'Credit Note Number Prefix';
$lang['credit_note_number'] = 'Credit Note #';
$lang['credit_note_number_exists'] = 'Credit note number already exists';
$lang['show_shipping_on_credit_note'] = 'Show shipping details on credit note';
$lang['credit_note_number_decrement_on_delete'] = 'Decrement credit note number on delete.';
$lang['credit_note_number_decrement_on_delete_help'] = 'Number will be decremented only if is last credit note created.';
$lang['credit_note_status'] = 'Status';
$lang['credit_note_status_open'] = 'Open';
$lang['credit_note_status_closed'] = 'Closed';
$lang['credit_note_status_void'] = 'Void';
$lang['credit_note_mark_as_open'] = 'Mark as Open';
$lang['new_credit_note'] = 'New Credit Note';
$lang['credit_note_amount'] = 'Amount';
$lang['credit_note_remaining_credits'] = 'Remaining Amount';
$lang['credit_note_client_note'] = 'Note';
$lang['invoices_credited'] = 'Invoices Credited';
$lang['apply_credits'] = 'Apply Credits';
$lang['x_credits_available'] = '%s credits available.';
$lang['credit_amount'] = 'Credit Amount';
$lang['credits_available'] = 'Credits Available';
$lang['amount_to_credit'] = 'Amount to Credit';
$lang['invoice_credits_applied'] = 'Credits successfully applied to invoice';
$lang['applied_credits'] = 'Applied Credits';
$lang['credit_amount_bigger_then_invoice_balance'] = 'Total credits amount is bigger then invoice balance due';
$lang['credit_amount_bigger_then_credit_note_remaining_credits'] = 'Total credits amount is bigger then remaining credits';
$lang['credited_invoices_not_found'] = 'Credited Invoices Not Found';
$lang['credit_invoice_number'] = 'Invoice Number';
$lang['credits_used'] = 'Credits Used';
$lang['credits_remaining'] = 'Credits Remaining';
$lang['amount_credited'] = 'Amount Credited';
$lang['credits_applied_cant_delete_status_closed'] = 'This credit note is with status Closed, you need first to delete the credits in order to delete the credit note.';
$lang['credits_applied_cant_delete_credit_note'] = 'This credit note has applied credits, you need first to delete the credits in order to delete the credit note.';
$lang['credit_note_pdf_heading'] = 'CREDIT NOTE';
$lang['show_status_on_pdf'] = 'Show %s status on PDF documents';
$lang['show_pdf_signature_credit_note'] = 'Show PDF Signature on Credit Note';
$lang['calendar_credit_note_reminder'] = 'Credit Note Reminder';
$lang['show_credit_note_reminders_on_calendar'] = 'Credit Note Reminders';
$lang['reminders'] = 'Reminders';
$lang['invoice_activity_applied_credits'] = 'applied credits of %s from %s';
$lang['create_credit_note'] = 'Create Credit Note';
$lang['confirm_invoice_credits_from_credit_note'] = 'When creating credit note from non paid invoice, the credit note amount will get applied for this invoice. Are you sure that you want to create the credit note?';
$lang['credit_invoice_date'] = 'Invoice Date';
$lang['apply_to_invoice'] = 'Apply to invoice';
$lang['apply_credits_from'] = 'Apply Credits From %s';
$lang['credits_successfully_applied_to_invoices'] = 'Invoices credits successfully applied';
$lang['credit_note_send_to_client_modal_heading'] = 'Send Credit Note To Customer';
$lang['credit_note_sent_to_client_success'] = 'Credit note is sent successfully to the client';
$lang['credit_note_sent_to_client_fail'] = 'Problem while sending credit note to email';
$lang['credit_note_no_invoices_available'] = 'There are no available invoices for this customer.';
$lang['show_total_paid_on_invoice'] = 'Show Total Paid On Invoice';
$lang['show_credits_applied_on_invoice'] = 'Show Credits Applied On Invoice';
$lang['show_amount_due_on_invoice'] = 'Show Amount Due On Invoice';
$lang['customer_profile_update_credit_notes'] = 'Update the shipping/billing info on all previous credit notes (Closed credit notes not affected)';
$lang['zip_credit_notes'] = 'Zip Credit Notes';
$lang['statement_credit_note_details'] = 'Credit Note %s';
$lang['statement_credits_applied_details'] = 'Credits Applied from Credit Note %s - %s for payment of %s';
$lang['credit_note_files'] = 'Credit Note Files';
$lang['credit_notes_report'] = 'Credit Notes Report';
$lang['credit_note_set_reminder_title'] = 'Set Credit Note Reminder';
$lang['credit_note_add_edit_client_note'] = 'Client Note';
$lang['credit_note_bill_to'] = 'Bill To';
$lang['credit_note_prefix'] = 'Invoice Number Prefix';
$lang['credit_note_admin_note'] = 'Admin Note';
$lang['credit_note_total'] = 'Total';
$lang['credit_note_adjustment'] = 'Adjustment';
$lang['credit_note_discount'] = 'Discount';
$lang['credit_note_subtotal'] = 'Sub Total';
$lang['credit_note_table_quantity_heading'] = 'Qty';
$lang['credit_note_table_hours_heading'] = 'Hours';
$lang['credit_note_table_item_heading'] = 'Item';
$lang['credit_note_table_item_description'] = 'Description';
$lang['credit_note_table_rate_heading'] = 'Rate';
$lang['credit_note_table_tax_heading'] = 'Tax';
$lang['credit_note_table_amount_heading'] = 'Amount';
$lang['credit_notes_list_all'] = 'All';
$lang['ticket_assigned'] = 'Assigned';
$lang['dashboard_options'] = 'Dashboard Options';
$lang['reset_dashboard'] = 'Reset Dashboard';
$lang['widgets'] = 'Widgets';
$lang['s_chart'] = '%s Chart';
$lang['quick_stats'] = 'Quick Statistics';
$lang['user_widget'] = 'User Widget';
$lang['widgets_visibility_help_text'] = 'Widgets that are shown only if they have enough data do not have options to be hidden or shown.';
$lang['show_project_on_estimate'] = 'Show Project Name On Estimate';
$lang['show_project_on_invoice'] = 'Show Project Name On Invoice';
$lang['show_project_on_credit_note'] = 'Show Project Name On Credit Note';
$lang['visible_tabs'] = 'Visible Tabs';
$lang['all'] = 'All';
$lang['view_widgetable_area'] = 'View Widgetable Area';
$lang['hide_widgetable_area'] = 'Hide Widgetable Area';
$lang['no_items_warning'] = 'Enter at least one item.';
$lang['item_forgotten_in_preview'] = 'Have you forgotten to add this item?';
$lang['not_task_status_changed'] = '%s - task status changed to %s';
$lang['not_project_activity_task_status_changed'] = 'Task Status Changed';
$lang['reset'] = 'Reset';
$lang['save_message_as_predefined_reply'] = 'Save Message as Predefined Reply';
$lang['inline_create_option'] = 'Allow non-admin staff members to create %s in %s create/edit area?';
$lang['inline_create'] = 'Inline Create';
$lang['inline_create_option_predefined_replies'] = 'Allow non-admin staff members to save predefined replies from ticket message';
$lang['reminders_view_none_admin'] = 'Showing your reminders and reminders created by you.';
$lang['show_tabs_and_options'] = 'Show Tabs & Options';
$lang['no_milestones_found'] = 'This project has no milestones';
$lang['lead_is_contact_create_task'] = 'Create task if email sender is already customer and assign to responsible staff member.';
$lang['existing_customer'] = 'Existing Customer';
$lang['use_company_name_instead'] = 'Use company name instead';
$lang['customer_delete_transactions_warning'] = 'This customer has transactions, %s, you must delete the transactions or move to another customer in order to perform this action.';
$lang['help_leads_create_permission'] = 'All staff can create leads, except members marked as not staff members';
$lang['help_leads_edit_permission'] = 'Everyone who has access to specific lead can edit most of the lead information';
$lang['triggers'] = 'Triggers';
$lang['notice_only_one_active_sms_gateway'] = 'Only 1 active SMS gateway is allowed';
$lang['sms_trigger_disable_tip'] = 'Leave contents blank to disable specific trigger.';
$lang['tables'] = 'Tables';
$lang['only_project_tasks'] = 'Only project related tasks';
$lang['download_all'] = 'Download All';
$lang['settings_sales_credit_note_number_format'] = 'Credit Note Number Format';
$lang['sms_reminder_sent_to'] = 'SMS reminder sent to %s';
$lang['ideal_customer_statement_descriptor'] = 'Statement Descriptor (shown in customer bank statement)';
$lang['payment_received_awaiting_confirmation'] = 'Your payment was received and is awaiting confirmation.';
$lang['discount_fixed_amount'] = 'Fixed Amount';
$lang['timesheet_duration_instead'] = 'Enter time duration instead';
$lang['timesheet_date_instead'] = 'Set start and end time instead';
$lang['allow_non_admin_members_to_import_leads'] = 'Allow non-admin staff members to import leads';
$lang['project_hide_tasks_settings_info'] = 'Tasks are excluded from the main tasks table for this project, you can view the project tasks only in this area.';
$lang['ticket_create_no_contact'] = 'Ticket without contact';
$lang['ticket_create_to_contact'] = 'Ticket to contact';
$lang['showing_billable_tasks_from_project'] = 'Showing billable tasks from project';
$lang['no_billable_tasks_found'] = 'Billable tasks not found';
$lang['help_leads_permission_view'] = 'If this permission is not checked, a staff member will be only able to view leads to where is assigned, leads created by the staff member and leads that are marked as public';
$lang['customers'] = 'Customers';
$lang['knowledge_base'] = 'Knowledge Base';
$lang['staff'] = 'Staff';
$lang['checklist_templates'] = 'Task Checklist Templates';
$lang['emails_tracking'] = 'Emails Tracking';
$lang['no_tracked_emails_sent'] = 'No tracked emails sent';
$lang['tracked_emails_sent'] = 'Tracked Emails Sent';
$lang['tracked_email_date'] = 'Date';
$lang['tracked_email_subject'] = 'Subject';
$lang['tracked_email_to'] = 'To';
$lang['tracked_email_opened'] = 'Opened';
$lang['tracked_email_not_opened'] = 'Not Opened';
$lang['not_viewed_yet'] = 'This %s is not viewed yet by the customer';
$lang['undo'] = 'Undo';
$lang['sign_document_validation'] = 'Please sign the document.';
$lang['document_customer_signature_text'] = 'Signature (Customer)';
$lang['accept_identity_confirmation_and_signature_sign'] = 'Require digital signature and identity confirmation on accept';
$lang['legal_bound_text'] = 'Legal Bound Text';
$lang['e_signature_sign'] = 'Sign';
$lang['is_signed'] = 'Signed';
$lang['is_not_signed'] = 'Not Signed';
$lang['download'] = 'Download';
$lang['view_pdf_in_new_window'] = 'View PDF in New Tab';
$lang['show_pdf_signature_contract'] = 'Show PDF Signature on Contract';
$lang['document_signed_successfully'] = 'You have successfully signed this document';
$lang['document_signed_info'] = 'This document is signed by %s on %s from IP address %s';
$lang['keep_signature'] = 'Keep Customer Signature';
$lang['view_contract'] = 'View Contract';
$lang['summary'] = 'Summary';
$lang['discussion'] = 'Discussion';
$lang['general_information'] = 'General Information';
$lang['proposal_information'] = 'Proposal Information';
$lang['contract_comments'] = 'Comments';
$lang['not_contract_comment_from_client'] = 'New comment from customer on contract %s ...';
$lang['contract_files'] = 'Contract Files';
$lang['date_signed'] = 'Date Signed';
$lang['clear_signature'] = 'Clear Signature';
$lang['recurring_has_ended'] = 'This recurring %s has ended.';
$lang['cycles_remaining'] = 'Cycles Remaining';
$lang['cycles_infinity'] = 'Infinity';
$lang['recurring_total_cycles'] = 'Total Cycles';
$lang['cycles_passed'] = 'Passed %s';
$lang['api_key_not_set_error_message'] = 'API key not configured, click on the following link to configure API key: %s';
$lang['subscription'] = 'Subscription';
$lang['subscription_lowercase'] = 'subscription';
$lang['subscriptions'] = 'Subscriptions';
$lang['tax_is_used_in_subscriptions_warning'] = 'You can\'t update this tax because is used by subscriptions.';
$lang['credit_card'] = 'Credit Card';
$lang['update_credit_card'] = 'Update Credit Card';
$lang['credit_card_update_info'] = 'Want to update the credit card that we have on file? Provide the new details here. Your card information will never directly touch our server.';
$lang['update_card_details'] = 'Update Card Details';
$lang['update_card_btn'] = 'Update Card';
$lang['subscription_name'] = 'Subscription Name';
$lang['subscriptions_description'] = 'Description';
$lang['subscribe'] = 'Subscribe';
$lang['subscription_date'] = 'Date';
$lang['first_billing_date'] = 'First Billing Date';
$lang['allow_primary_contact_to_update_credit_card'] = 'Allow primary contact to update stored credit card token?';
$lang['show_subscriptions_in_customers_area'] = 'Show subscriptions in customers area?';
$lang['show_subscriptions_in_customers_area_help'] = 'This option is valid only for the customer primary contact.';
$lang['subscription_sent_to_email_success'] = 'Subscription sent to email successfully';
$lang['subscription_sent_to_email_fail'] = 'Failed to sent subscription to email';
$lang['new_subscription'] = 'New Subscription';
$lang['subscription_status'] = 'Status';
$lang['next_billing_cycle'] = 'Next Billing Cycle';
$lang['subscription_not_subscribed'] = 'Not Subscribed';
$lang['send_subscription'] = 'Send Subscription';
$lang['subscription_will_send_to_primary_contact'] = 'The subscription will be sent to the primary contact.';
$lang['subscription_resumed'] = 'Subscription is set to active successfully';
$lang['subscription_canceled'] = 'Canceled';
$lang['no_credit_card_found'] = 'No Credit Card Found';
$lang['cancel_immediately'] = 'Cancel Immediately';
$lang['cancel_at_end_of_billing_period'] = 'Cancel At The End Of Billing Period';
$lang['view_subscription'] = 'View Subscription';
$lang['subscription_future'] = 'Future';
$lang['subscription_active'] = 'Active';
$lang['subscription_past_due'] = 'Past Due';
$lang['subscription_unpaid'] = 'Unpaid';
$lang['billing_plan'] = 'Billing Plan';
$lang['upcoming_invoice'] = 'Upcoming Invoice';
$lang['resume_now'] = 'Resume Now';
$lang['subscription_not_yet_subscribed'] = 'Customer is not yet subscribed to this subscription.';
$lang['subscription_is_canceled_no_resume'] = 'This subscription is canceled and cannot be resumed.';
$lang['subscription_will_be_canceled_at_end_of_billing_period'] = 'This subscription will be canceled at the end of billing period.';
$lang['customer_successfully_subscribed_to_subscription'] = 'Thank you for subscribing to %s';
$lang['date_subscribed'] = 'Date Subscribed';
$lang['reports'] = 'Reports';
$lang['subscriptions_summary'] = 'Subscriptions Summary';
$lang['calendar_only_assigned_tasks'] = 'Show only tasks assigned to the logged in staff member';
$lang['invoice_activity_subscription_payment_succeeded'] = 'Subscription Payment Succeeded, email sent to: %s';
$lang['mail_engine'] = 'Mail Engine';
$lang['settings_require_client_logged_in_to_view_contract'] = 'Require client to be logged in to view contract';
$lang['privacy_policy'] = 'Privacy Policy';
$lang['gdpr_terms_agree'] = 'I agree to the <a href="%s" target="_blank">Terms & Conditions</a>';
$lang['terms_and_conditions_validation'] = 'You must accept the Terms & Conditions in order to continue.';
$lang['gdpr'] = 'General Data Protection Regulation (GDPR)';
$lang['data_removal_request_sent'] = 'Data removal request successfully sent';
$lang['gdpr_consents'] = 'Consents';
$lang['gdpr_consent'] = 'Consent';
$lang['gdpr_consent_purpose'] = 'Purpose';
$lang['gdpr_consent_opt_in'] = 'Opt In';
$lang['gdpr_consent_opt_out'] = 'Opt Out';
$lang['gdpr_consent_agree'] = 'I agree';
$lang['gdpr_consent_disagree'] = 'I disagree';
$lang['view_consent'] = 'View Consent';
$lang['transfer_consent'] = 'Transfer Consent';
$lang['view_public_form'] = 'View Public Form';
$lang['update_consent'] = 'Update Consent';
$lang['consent_last_updated'] = 'Last Updated: %s';
$lang['showing_search_result'] = 'Showing search results for: %s';
$lang['per_page'] = 'Per Page';
$lang['allow_staff_view_invoices_assigned'] = 'Allow staff members to view invoices where they are assigned to';
$lang['allow_staff_view_estimates_assigned'] = 'Allow staff members to view estimates where they are assigned to';
$lang['gdpr_right_to_be_informed'] = 'Right to be informed';
$lang['gdpr_right_of_access'] = 'Right of access';
$lang['gdpr_right_to_data_portability'] = 'Right to data portability';
$lang['gdpr_right_to_erasure'] = 'Right to erasure';
$lang['edit_my_information'] = 'Edit my information';
$lang['export_my_data'] = 'Export my data';
$lang['request_data_removal'] = 'Request data removal';
$lang['explanation_for_data_removal'] = 'Explanation for data removal';
$lang['briefly_describe_why_remove_data'] = 'Briefly describe why you want to remove the data';
$lang['date_published'] = 'Date Published';
$lang['view'] = 'View';
$lang['customer_is_subscribed_to_subscription_info'] = 'The customer is subscribed to this subscription';
$lang['save_last_order_for_tables'] = 'Save last order for tables';
$lang['date_created'] = 'Date Created';
$lang['company_logo_dark'] = 'Company Logo Dark';
$lang['customers_register_require_confirmation'] = 'Require registration confirmation from administrator after customer register';
$lang['customer_requires_registration_confirmation'] = 'Requires Registration Confirmation';
$lang['confirm_registration'] = 'Confirm Registration';
$lang['customer_registration_successfully_confirmed'] = 'Customer registration successfully confirmed';
$lang['customer_register_account_confirmation_approval_notice'] = 'Thank you for registering, your account is pending approval and will be confirmed soon.';
$lang['after_subscription_payment_succeeded'] = 'After subscription payment is succeeded';
$lang['subscription_option_send_invoice'] = 'Send Invoice';
$lang['subscription_option_send_payment_receipt'] = 'Send Payment Receipt';
$lang['subscription_option_send_payment_receipt_and_invoice'] = 'Send Invoice and Payment Receipt';
$lang['subscription_option_do_nothing'] = 'Do Nothing';
$lang['gdpr_not_enabled'] = 'GDPR not enabled';
$lang['enable_gdpr'] = 'Enable GDPR';
$lang['gdpr_right_to_rectification'] = 'Right to rectification';
$lang['test_sms_config'] = 'Test SMS Config';
$lang['test_sms_message'] = 'Test Message';
$lang['send_test_sms'] = 'Send Test SMS';
$lang['gdpr_short'] = 'GDPR';
$lang['allow_non_admin_staff_to_delete_ticket_attachments'] = 'Allow non-admin staff members to delete ticket attachments';
$lang['contract_number'] = 'Contract Number';
$lang['project_changing_status_recurring_tasks_notice'] = 'You are changing the status to {0}, all recurring tasks will be cancelled';
$lang['not_contract_signed'] = 'Contract with subject %s has been signed by the customer';
$lang['the_number_sign'] = '#';
$lang['not_new_ticket_reply'] = 'Customer replied to ticket - %s';
$lang['receive_notification_on_new_ticket_replies'] = 'Receive notification when customer reply to a ticket';
$lang['receive_notification_on_new_ticket_reply_help'] = 'All staff members which belong to the ticket department will receive notification when customer reply to a ticket';
$lang['payment_gateway_enable_paypal'] = 'Enable PayPal Payments';
$lang['contract_notes'] = 'Notes';
$lang['contract_add_note'] = 'Add Note';
$lang['frequency'] = 'Frequency';
$lang['frequency_every'] = 'Every %s';
$lang['last_invoice_date'] = 'Last Invoice Date';
$lang['next_invoice_date_list'] = 'Next Invoice Date';
$lang['enter_new_card'] = 'Enter New Card';
$lang['save_and_record_payment'] = 'Save & Record Payment';
$lang['choose_from_google_drive'] = 'Choose from Google Drive';
$lang['open_in_google'] = 'Open In Google';
$lang['google_picker'] = 'Google Picker';
$lang['enable_google_picker'] = 'Enable Google Picker';
$lang['google_api_client_id'] = 'Google API Client ID';
$lang['subtract_tax_total_from_amount'] = 'Subtract TAX total (%s) from amount';
$lang['expense_subtract_info_text'] = 'Use this option to subtract the total tax amount from the expense amount, useful when you entered the expense amount with tax included (tax inclusive).';
$lang['company_exists_info'] = 'It looks that a customer with name %s already exists, if you still want to create the customer you can ignore this message.';
$lang['import_items'] = 'Import Items';
$lang['total_items_deleted'] = 'Total items deleted: %s';
$lang['billable_amount'] = 'Billable Amount';
$lang['last_child_invoice_date'] = 'Last Child Invoice Date';
$lang['good_morning'] = 'Good Morning';
$lang['good_afternoon'] = 'Good Afternoon';
$lang['good_evening'] = 'Good Evening';
$lang['description_in_invoice_item'] = 'Include description in invoice item';
$lang['description_in_invoice_item_help'] = 'Useful if you want to include additional information on the subscription invoice, e.q. what this subscription includes.';
$lang['ticket_reminders'] = 'Reminders';
$lang['ticket_set_reminder_title'] = 'Set Ticket Reminder';
$lang['calendar_ticket_reminder'] = 'Ticket Reminders';
$lang['email_verification_required'] = 'Email Verification Required';
$lang['email_verification_required_message'] = 'In order to access all the available features in the portal, first you must verify your email.';
$lang['email_verification_required_message_mail'] = 'We have sent you an email with verification instructions, if you haven\'t received the email please check the spam folder or click <a href="%s">here</a> to resend the verification mail.';
$lang['email_already_verified'] = 'Your email is already verified';
$lang['invalid_verification_key'] = 'Invalid verification key';
$lang['verification_key_expired'] = 'Verification Key Expired';
$lang['email_successfully_verified'] = 'Your email has been successfully verified.';
$lang['email_successfully_verified_but_required_admin_confirmation'] = 'Your email has been successfully verified, you will be able to login once administrator confirm your account manually.';
$lang['email_verification_mail_sent_successully'] = 'We sent you an email with verification instructions';
$lang['create_reminder'] = 'Create Reminder';
$lang['no_reminders_for_this_task'] = 'No reminders for this task';
$lang['reminder_for'] = 'Reminder for %s on %s';
$lang['no_description_provided'] = 'No description provided';
$lang['pay_with_card'] = 'Pay With Card';
$lang['not_customer_uploaded_file'] = 'New File(s) Uploaded in Profile';
$lang['customer_files_info_message'] = 'Files from projects and tasks linked to the customer are not shown on this table.';
$lang['ticket_import_reply_only'] = 'Try to import only the actual ticket reply (without quoted/forwarded message)';
$lang['learn_more'] = 'Learn More';
$lang['sales_item'] = 'Item';
$lang['modules'] = 'Modules';
$lang['module'] = 'Module';
$lang['module_description'] = 'Description';
$lang['module_activate'] = 'Activate';
$lang['module_deactivate'] = 'Deactivate';
$lang['module_uninstall'] = 'Uninstall';
$lang['module_upgrade_database'] = 'Upgrade Database';
$lang['module_settings'] = 'Settings';
$lang['module_version'] = 'Version %s';
$lang['module_by'] = 'By %s';
$lang['staff_which_are_using_role'] = 'Staff members which are using this role';
$lang['copy'] = 'Copy';
$lang['read_more'] = 'Read More';
$lang['project_progress_text'] = 'Project Progress';
$lang['timer_not_stopped_yet'] = 'This timer is not yet stopped';
$lang['refunds'] = 'Refunds';
$lang['refund'] = 'Refund';
$lang['refund_amount'] = 'Refunded Amount';
$lang['not_refunds_found'] = 'No refunds found';
$lang['refunds_applied_cant_delete_credit_note'] = 'This credit note has refunds applied, you need first to delete the refunds in order to delete the credit note.';
$lang['create_recurring_from_child_error_message'] = 'You cannot set this %s as recurring because this %s is child from another recurring %s.';
$lang['statement_credit_note_refund'] = 'Credit Note Refund - %s';
$lang['no_validation'] = 'No Validation';
$lang['lead_unique_validation_on'] = 'Perform validation for duplicate lead on the following fields:';
$lang['phonenumber_exists'] = 'Phone number already exists';
$lang['company_exists'] = 'Company already exists';
$lang['website_exists'] = 'Website already exists';
$lang['send_payment_receipt_to_client'] = 'Send Payment Receipt To Client';
$lang['payment_sent_successfully'] = 'Payment receipt sent successfully.';
$lang['payment_sent_failed'] = 'Failed to send payment receipt.';
$lang['tags_update_replace_warning'] = 'Some tags are not updated because the name of the tag already exist';
$lang['attach_statement'] = 'Attach Customer Statement';
$lang['delete_credit_card'] = 'Delete Card';
$lang['delete_credit_card_info'] = 'You cannot delete the credit card as you have active subscriptions.';
$lang['credit_card_successfully_deleted'] = 'Credit card successfully deleted.';
$lang['subscription_incomplete'] = 'Incomplete';
$lang['subscription_incomplete_expired'] = 'Incomplete Expired';
$lang['credit_card_short'] = 'Card';
$lang['webhook_created'] = 'Webhook created successfully.';
$lang['subscriptions_terms_info'] = 'Enter customer terms & conditions to be displayed to the customer before subscribe to the subscription.';
$lang['subscription_complete_payment'] = 'Complete Payment';
$lang['subscription_is_subscription_is_expired'] = 'This subscription is expired.';
$lang['subscription_plan_currency_does_not_match'] = 'Selected plan currency does not match currency selected below.';
$lang['subscription_first_billing_date_info'] = 'Leave blank to use date when the customer is subscribed to the subscription. This field must be future date, if you select date and the date is passed but customer is not yet subscribed, the date when the customer will subscribe will be used.';
$lang['stripe_subscription_select_plan'] = 'Select Stripe plan';
$lang['contract_content_permission_edit_warning'] = 'Your current permissions does not allows you to edit the contract content. Consult with an ◀
                              administrator to allow you permission to edit contracts.';
$lang['mark_as_signed'] = 'Mark as signed';
$lang['unmark_as_signed'] = 'Unmark as signed';
$lang['marked_as_signed'] = 'Marked as signed';
$lang['contract_marked_as_signed_info'] = 'This contract is manually marked as signed.';
$lang['save_and_send_later'] = 'Save and Send Later';
$lang['schedule'] = 'Schedule';
$lang['schedule_email_for'] = 'Schedule Email for %s';
$lang['schedule_date'] = 'When would you like to send the email?';
$lang['email_scheduled_successfully'] = 'Email scheduled successfully';
$lang['invoice_will_be_sent_at'] = 'Invoice will be sent at %s';
$lang['recaptcha_ignore_ips'] = 'Ignored IP Addresses';
$lang['recaptcha_ignore_ips_info'] = 'Enter coma separated IP addresses that you want the reCaptcha to skip validation.';
$lang['show_task_reminders_on_calendar'] = 'Task Reminders';
$lang['contracts_about_to_expire'] = 'Contracts Expiring Soon';
$lang['no_contracts_about_to_expire'] = 'There are no contracts that are going to expire in the next %s days.';
$lang['lead_value'] = 'Lead value';
$lang['lead_value_tooltip'] = 'Base currency will be used.';
$lang['leads_dt_lead_value'] = 'Lead Value';
$lang['leads_canban_lead_value'] = 'Lead Value: %s';
$lang['lead_add_edit_lead_value'] = 'Lead Value';
$lang['gantt_view_day'] = 'Days View';
$lang['gantt_view_week'] = 'Weeks View';
$lang['gantt_view_month'] = 'Months View';
$lang['gantt_view_year'] = 'Years View';
$lang['hour_of_day_perform_tasks_reminder_notification_help'] = '24 hours format eq. 9 for 9am or 15 for 3pm. It is used for recurring Task, Task reminders etc.';
$lang['clients_nav_contacts'] = 'Contacts';
$lang['clients_my_contacts'] = 'Contacts';
$lang['clients_my_contact'] = 'Contact';
$lang['customer_contact'] = 'My Contacts';
$lang['clients_contact_added'] = 'Contact added successfuly';
$lang['clients_contact_updated'] = 'Contact updated successfuly';
$lang['allow_primary_contact_to_manage_other_contacts'] = 'Allow primary contact to manage other customer contacts';
$lang['contact_form_validation_is_unique'] = 'Contact with this {field} already exists in our system';
$lang['invoice_number_not_applied_on_draft'] = 'If the invoice is saved as draft, the number won\'t be applied, instead, the next invoice number will be given when the invoice is sent to the customer or is marked as sent.';
$lang['two_factor_authentication_disabed'] = 'Disabled';
$lang['enable_google_two_factor_authentication'] = 'Enable Google Authenticator';
$lang['set_google_two_factor_authentication_failed'] = 'Saving authentication failed, please try again';
$lang['enter_two_factor_auth_code_from_mobile'] = 'Enter authentication code from the Authenticator app';
$lang['staff_two_factor_authentication'] = 'Two Factor Authentication';
$lang['google_authentication_code'] = 'Enter code from Authenticator app';
$lang['set_two_factor_authentication_successful'] = 'Successfully updated two factor authentication settings';
$lang['set_two_factor_authentication_failed'] = 'Could not update two factor authentication settings';
$lang['google_2fa_code_valid'] = 'Successfuly verified the authentication';
$lang['google_2fa_code_invalid'] = 'Invalid authentication code entered, try again.';
$lang['google_2fa_scan_qr_guide'] = 'Scan the QR below with the Google Authenticator app on your mobile device, after that fill in the field below with the code generated in the app';
$lang['google_2fa_manul_input_secret'] = 'Secret key for manual input';
$lang['templates'] = 'Templates';
$lang['add_template'] = 'Add Template';
$lang['edit_template'] = 'Edit Template';
$lang['template_added'] = 'Template added successfully';
$lang['template_updated'] = 'Template updated successfully';
$lang['template_content'] = 'Template Content';
$lang['insert_template'] = 'Insert';
$lang['items_table_amounts_exclude_currency_symbol'] = 'Exclude currency symbol from items table Amount';
$lang['multiplies_of'] = 'Multiplies of';
$lang['round_off_task_timer_option'] = 'Round off task timer';
$lang['task_timer_dont_round_off'] = 'Don\'t round off';
$lang['task_timer_round_up'] = 'Round up';
$lang['task_timer_round_down'] = 'Round down';
$lang['task_timer_round_nearest'] = 'Round to nearest';
$lang['calendar_task_reminder'] = 'Task Reminder';
$lang['projects_chart'] = 'Projects Chart';
$lang['overdue_by_days'] = 'OVERDUE BY %s DAYS';
$lang['two_checkout_payment_processing'] = 'Payment is been processed,you will be notified if successful';
$lang['two_checkout_payment_cancelled'] = 'Payment Cancelled';
$lang['two_checkout_merchant_code'] = 'Merchant Code';
$lang['two_checkout_secret_Key'] = 'Secret Code';
$lang['two_gateway_webhook_notice'] = 'The IPN Endpoint for 2Checkout is ( %s )';
$lang['something_went_wrong'] = 'Something went wrong. Try again';
$lang['imap_folder'] = 'Folder';
$lang['retrieve_folders'] = 'Retrieve Folders';
$lang['email_to_ticket_config'] = 'Email to ticket configuration';
$lang['enable_support_menu_badges'] = 'Enable support menu item badge';
$lang['item_copy_success'] = 'Item copied successfully';
$lang['item_copy_fail'] = 'Failed to copy item';
$lang['attach_invoice_to_payment_receipt_email'] = 'Attach invoice PDF when sending payment receipt to email';
$lang['estimate_request'] = 'Estimate request';
$lang['estimate_requests'] = 'Estimate request';
$lang['estimate_request_form'] = 'Estimate request form';
$lang['acs_estimate_request'] = 'Estimate request';
$lang['acs_estimate_request_forms'] = 'Forms';
$lang['estimate_request_forms'] = 'Estimate Request Forms';
$lang['estimate_request_notify_staff'] = 'Staff Members to Notify';
$lang['estimate_request_notify_when_submitted'] = 'Notify when estimate request submitted';
$lang['estimate_request_assignee'] = 'Responsible (Assignee)';
$lang['estimate_request_notify_roles'] = 'Roles to Notify';
$lang['custom_field_estimate_request'] = 'Estimate request';
$lang['new_estimate_request_submitted_from_form'] = 'New Estimate Request submitted from Form - %s';
$lang['acs_estimate_request_statuses_submenu'] = 'Statuses';
$lang['estimate_request_dt_email'] = 'Email';
$lang['estimate_request_dt_assigned'] = 'Assigned';
$lang['estimate_request_dt_status'] = 'Status';
$lang['estimate_request_dt_datecreated'] = 'Created';
$lang['estimate_request_attachments'] = 'Attachments';
$lang['estimate_request_new_status'] = 'New Status';
$lang['estimate_request_status_table_name'] = 'Status Name';
$lang['estimate_request_table_total'] = 'Total Request: %s';
$lang['estimate_request_statuses_not_found'] = 'No Estimate Request statuses found';
$lang['estimate_request_status_add_edit_name'] = 'Status Name';
$lang['estimate_request_status_color'] = 'Color';
$lang['estimate_request_status_add_edit_order'] = 'Status Order';
$lang['estimate_request_status'] = 'Status';
$lang['estimate_request_date_added'] = 'Date Created';
$lang['estmate_request_tags_updated'] = 'Tags Updated';
$lang['not_estimate_request_activity_status_updated'] = '%s updated estimate request status from %s to %s';
$lang['estimate_request_lowercase'] = 'estimate request';
$lang['estimate_request_form_email_field_is_required'] = 'Email field is required to be added on the form';
$lang['estimate_request_form_email_field_set_to_required'] = 'Mark the email field as required';
$lang['not_delete_estimate_request_default_status'] = 'Cannot delete core estimate request status';
$lang['mark_estimate_request_as'] = 'Mark as %s';
$lang['estimate_request_updated'] = 'Estimate Request Updated';
$lang['convert_estimate_request'] = 'Convert Estimate Request';
$lang['estimate_request_client_firstname'] = 'First Name';
$lang['estimate_request_client_lastname'] = 'Last Name';
$lang['estimate_request_email'] = 'Email';
$lang['estimate_request_for_lead'] = 'Lead';
$lang['estimate_request_for_customer'] = 'Customer';
$lang['estimate_request_related'] = 'Related to';
$lang['estimate_request_client_created_success'] = 'Estimate Request Customer Created';
$lang['estimate_request_assigned'] = 'Staff Assigned';
$lang['not_estimate_request_activity_assigned_updated'] = 'Estimate Request Assigned to %s';
$lang['estimate_request_status_lowercase'] = 'estimate request status';
$lang['estimate_request_assigned_to_staff'] = 'Estimate Request has been assigned to you';
$lang['activity_due_reminder_is_sent'] = '%s sent invoice becoming due reminder';
$lang['invoice_due_notice_before'] = 'Send due reminder X days before due date';
$lang['overdue_notices'] = 'Overdue Notices';
$lang['invoice_overdue_notices_info'] = 'Overdue notices are sent when the invoice becomes overdue.';
$lang['due_reminders'] = 'Due Reminders';
$lang['due_reminders_for_invoices_info'] = 'Due reminders are sent to unpaid and partially paid invoices as reminder to the customer to pay the invoice before is due.';
$lang['expenses_list_made_payment_by'] = 'Made Payment by %s';
$lang['hide_task_checklist_items_completed'] = 'Hide completed items';
$lang['show_task_checklist_items_completed'] = 'Show completed items %s';
$lang['task_checklist_assign'] = 'Assign staff';
$lang['task_checklist_assigned'] = 'Assigned to %s';
$lang['custom_field_add_edit_default_value'] = 'Default Value';
$lang['projects_send_contact_notification'] = 'Send contacts notifications';
$lang['project_send_all_contacts_with_notifications_enabled'] = 'To all contacts with notifications for projects enabled';
$lang['project_do_not_send_contacts_notifications'] = 'Do not send notifications';
$lang['project_send_specific_contacts_with_notification'] = 'Specific contacts';
$lang['project_contacts_to_notify'] = 'Select contacts to notify';
$lang['contract_signed_by'] = 'Signer Name';
$lang['contract_signed_date'] = 'Signed Date';
$lang['contract_signed_ip'] = 'IP Address';
$lang['show_estimate_request_in_customers_area'] = 'Show Estimate request link in customers area?';
$lang['customers_estimate_request_link_text'] = 'Request Estimate';
$lang['total_expenses_deleted'] = 'Total Expenses Deleted: %s';
$lang['estimate_convert_to_project'] = 'Convert to Project';
$lang['estimate_items_convert_to_tasks'] = 'Items that will be converted to tasks';
$lang['proposal_signed_by'] = 'Signer Name';
$lang['proposal_signed_date'] = 'Signed Date';
$lang['proposal_signed_ip'] = 'IP Address';
$lang['hide_milestone_from_customer'] = 'Hide from customer';
$lang['hide_milestone_from_customer_help'] = 'When you decide to hide milestone from the customer area, the whole milestone and its tasks will be hidden';
$lang['automatically_stop_task_timer_after_hours'] = 'Automaticaly stop task timers after (hours)';
$lang['home_payment_records'] = 'Payment Records';
$lang['weekly'] = 'Weekly';
$lang['monthly'] = 'Monthly';
$lang['failed_to_update_timesheet'] = 'Timesheet was not updated';
$lang['permission_create_timesheets'] = 'Create Timesheets';
$lang['permission_edit_timesheets'] = 'Edit Timesheets (Global)';
$lang['permission_edit_own_timesheets'] = 'Edit Own Timesheets';
$lang['permission_delete_timesheets'] = 'Delete Timesheets (Global)';
$lang['permission_delete_own_timesheets'] = 'Delete own Timesheets';
$lang['permission_edit_milestones'] = 'Edit Milestones';
$lang['permission_delete_milestones'] = 'Delete Milestonea';
$lang['add_timesheet'] = 'Add timesheet';
$lang['submit_button_bg_color'] = 'Submit button background color';
$lang['submit_button_text_color'] = 'Submit button background text';
$lang['automatically_assign_ticket_to_first_staff_responding'] = 'Automatically assign the ticket to the first staff that post a reply?';
$lang['contract_signed_not_all_fields_editable'] = 'This contract is signed, hence not all fields can be edited until the signature is removed.';
$lang['form_submit_success_action'] = 'What should happen after a visitor submits this form';
$lang['form_submit_success_display_thank_you'] = 'Display thank you message';
$lang['form_submit_success_redirect_to_website'] = 'Redirect to another website';
$lang['form_submit_website_url'] = 'Website URL';
$lang['lead_name_prefix'] = 'Lead title prefix';
$lang['lead_name_prefix_help'] = 'For each newly created lead via the form, the lead name will be prefixed with the text added in the field for easier recognition.';
$lang['open_google_map'] = 'Open in Google Map';
$lang['milestone_start_date'] = 'Start Date';
$lang['send_reminder_for_completed_but_not_billed_tasks'] = 'Send an email reminder of billable tasks completed but not billed';
$lang['staff_to_notify_completed_but_not_billed_tasks'] = 'Select which staff members you want to receive the reminder';
$lang['reminder_for_completed_but_not_billed_tasks_days'] = 'Select days of the week reminder should be sent';
$lang['notifications'] = 'Notifications';
$lang['merged'] = 'Merged';
$lang['ticket_merged_notice'] = 'This ticket is merged into the ticket with ID';
$lang['view_primary_ticket'] = 'View primary ticket';
$lang['merge_tickets'] = 'Merge Tickets';
$lang['primary_ticket'] = 'Primary Ticket';
$lang['primary_ticket_status'] = 'Primary Ticket Status';
$lang['tickets_merged'] = 'Tickets Merged Successfully';
$lang['cannot_merge_into_merged_ticket'] = 'Ticket that is merged into another ticket cannot be used as primary ticket';
$lang['merge_ticket_ids_field_label'] = 'Merge Ticket #';
$lang['merge_ticket_ids_field_placeholder'] = 'example: 5 or 5,6';
$lang['cannot_merge_tickets_with_ids'] = 'Ticket %s is already merged in another ticket';
$lang['ticket_merged_tickets_header'] = 'This ticket contains %s tickets that are merged';
$lang['batch_payments_table_invoice_number_heading'] = 'Invoice Number';
$lang['batch_payments_table_payment_date_heading'] = 'Payment Date';
$lang['batch_payments_table_payment_mode_heading'] = 'Payment Mode';
$lang['batch_payments_table_transaction_id_heading'] = 'Transaction Id';
$lang['batch_payments_table_amount_received_heading'] = 'Amount received';
$lang['batch_payments_table_invoice_balance_due'] = 'Invoice Balance Due';
$lang['add_batch_payments'] = 'Add Payments';
$lang['batch_payment_filter_by_customer'] = 'Filter invoices by customer';
$lang['batch_payments'] = 'Batch Payments';
$lang['batch_payment_added_successfully'] = 'You have successfully added %s payments';
$lang['batch_payments_send_invoice_payment_recorded'] = 'Do not send invoice payment recorded email to customer contacts';
$lang['invoice_batch_payments'] = 'Batch Payment';
$lang['staff_is_currently_replying'] = '%s is currently replying to ticket.';
$lang['permission_view_timesheet_report'] = 'View Timesheets Report';
$lang['timesheets_overview_all_members_notice_permission'] = 'Timesheet overview for all staff members is only available for Staff with Permission to view timesheet reports and administrators.';
$lang['show_project_on_proposal'] = 'Show Project Name On Proposal';
$lang['ticket_reports_staff'] = 'Staff Member';
$lang['ticket_reports_total_assigned'] = 'Total Assigned Tickets';
$lang['ticket_reports_open_tickets'] = 'Open Tickets';
$lang['ticket_reports_closed_tickets'] = 'Closed Tickets';
$lang['ticket_reports_replies_to_tickets'] = 'Replies To Tickets';
$lang['ticket_reports_average_reply_time'] = 'Average Reply Time';
$lang['home_tickets_report'] = 'Staff Tickets Report';
$lang['ticket_reports_average_reply_time_help'] = 'Average response time from assigned tickets.';
$lang['created_by'] = 'Created by';
$lang['staff_related_ticket_notification_to_assignee_only'] = 'Send staff-related ticket notifications to the ticket assignee only';
$lang['staff_related_ticket_notification_to_assignee_only_help'] = 'If this option is set to Yes and the ticket does not have an assignee, notification will be sent to all staff that belongs to the ticket department';
$lang['import_expenses'] = 'Import Expenses';
$lang['show_pdf_signature_proposal'] = 'Show PDF Signature on Proposal';
$lang['enable_honeypot_spam_validation'] = 'Enable Honeypot spam validation';
$lang['potential_rate'] = 'Potential rate';
$lang['latest_potential_rate'] = 'Latest potential rate';
$lang['type_of_customer'] = 'Type of Customer';
$lang['type_customer'] = 'Type of Customer';
$lang['usage_behavior'] = 'Usage Behavior';
$lang['invoice_creator'] = 'PO Creator';
$lang['estimate_creator'] = 'Estimate Creator';
$lang['project_tag_reference'] = 'Project/ Tag/ Reference #';
$lang['customer_source'] = 'Invoice (Customer Source)';
$lang['your_target'] = 'Your Target';
$lang['new_favorite'] = 'New Favorite';
$lang['add_feedback'] = 'Add Feedback';
$lang['send_feedback'] = 'Send Feedback';
$lang['feedback_description'] = 'Feedback Description';
$lang['from'] = 'From';
$lang['to'] = 'To';
$lang['not_includes_vat'] = 'Not includes VAT';
$lang['all_invoices'] = 'All invoices';
$lang['all_estimates'] = 'All estimates';
$lang['new_staff_team'] = 'Add New Team';
$lang['staff_team_add_heading'] = 'Add New Staff Team';
$lang['staff_team_edit_heading'] = 'Edit Staff Team';
$lang['staff_team_name'] = 'Name';
$lang['staff_team'] = 'Staff Team';
$lang['staff_teams'] = 'Staff Teams';
$lang['teams'] = 'Teams';
$lang['status'] = 'Status';
$lang['start_time'] = 'Start Time';
$lang['end_time'] = 'End Time';
$lang['talk_time'] = 'Talk Time';
$lang['record'] = 'Record';
$lang['contact_channel'] = 'Contact Channel';
$lang['reminder_calendar'] = 'Reminder Calendar';
$lang['action'] = 'Action';
$lang['follow_up'] = 'Follow Up';
$lang['detail'] = 'Detail';
$lang['detailed_signal'] = 'Detailed Signal';
$lang['attachments'] = 'Attachments';
$lang['link'] = 'Link';
$lang['switch_reminders_to'] = 'Switch Reminders To';
$lang['send_mail_this_reminder'] = 'Send Mail This Reminder';
$lang['internal_note'] = 'Internal Note';
$lang['contact_success_note'] = 'Contact Success Note';
$lang['contact_again_note'] = 'Contact Again Note';
$lang['contact_note'] = 'Contact Note';
$lang['created_at'] = 'Created at';
$lang['last_modified_by'] = 'Last modified by';
$lang['last_modified_at'] = 'Last modified at';
$lang['note_editing_history'] = 'Note editing history';
$lang['landline'] = 'Landline';
$lang['sex'] = 'Sex';
$lang['male'] = 'Male';
$lang['female'] = 'Female';
$lang['birthday'] = 'Birthday';
$lang['other'] = 'Other';
$lang['facebook'] = 'Facebook';
$lang['zalo'] = 'Zalo';
$lang['linkedin'] = 'Linkedin';
$lang['skype'] = 'Skype';
$lang['contact_editing_history'] = 'Contact editing history';
$lang['review_status'] = 'Review Status';
$lang['contact_information'] = 'Contact Information';
$lang['approval_by'] = 'Approval by';
$lang['approval_at'] = 'Approval at';
$lang['request_create_contacts'] = 'Request Create Contacts';
$lang['customers_list'] = 'List';
$lang['permission_review_contact'] = 'Review';
$lang['email_placeholder'] = 'Please fill email with the right format. Ex: <EMAIL>';
$lang['job_position_placeholder'] = 'Ex: HR Manager';
$lang['phone_placeholder'] = '10 numeric letters required';
$lang['product'] = 'Product';
$lang['discount_rate'] = 'Discount Rate';
$lang['fixed_amount_discount'] = 'Fixed Amount Discount';
$lang['discount'] = 'Discount';
$lang['client_request_company_placeholder'] = 'Link for company name search: http://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp';
$lang['client_request_vat_placeholder'] = 'Link for tax identification number search: http://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp';
$lang['client_request_website_placeholder'] = 'If not found, please fill in www.notfound.com';
$lang['client_request_note_placeholder'] = 'Please briefly describe the reason for the new request';
$lang['client_request_company_addr_placeholder'] = 'Registered business address';
$lang['client_request_phone_placeholder'] = 'Enter phone number starting with 0 & without spaces';
$lang['client_request_email_placeholder'] = 'Ex: <EMAIL>, <EMAIL>';
$lang['client_request_reference_link_placeholder'] = 'Update IT recruitment source or contact information source';
$lang['client_request_note'] = 'Note the reason for creating the request';
$lang['client_request_reference_link'] = 'Reference link';
$lang['client_request_reference_file'] = 'Reference image/file';
$lang['client_request_company_email'] = 'Company email';
$lang['client_request_create_source'] = 'Source';
$lang['client_request_create_reason'] = 'Reason for creation';
$lang['client_request_reference_info'] = 'Reference information';
$lang['client_request_created_by'] = 'Requester';
$lang['client_request_created_at'] = 'Request creation date';
$lang['client_request_approve_by_1'] = 'Approver 1';
$lang['client_request_approve_by_2'] = 'Approver 2';
$lang['client_request_approve_time'] = 'Creation time (after being approved by both approvers)';
$lang['client_request_idm_crm'] = 'ID CRM';
$lang['client_request_company_short_name'] = 'Shortened name';
$lang['client_request_foreign_vat'] = 'Foreign tax identification number';
$lang['client_request_company_facebook'] = 'Company Facebook';
$lang['client_request_company_linkedin'] = 'Company Linkedin';
$lang['client_request_another_notes'] = 'Additional notes (if any)';
$lang['client_request_customer_list'] = 'Customers';
$lang['client_request_list'] = 'Request Create Customers';
$lang['client_request_approve_customer'] = 'Approve new customer request';
$lang['client_request_num_employees'] = 'Size';
$lang['client_request_company_types'] = 'Product or outsource';
$lang['client_request_company_status'] = 'Company status';
$lang['client_request_established_date'] = 'Date of company establishment';
$lang['client_request_company_industry'] = 'Industry';
$lang['client_request_company_nationality'] = 'Nationality';
$lang['client_request_import_company_types'] = '<b>%s</b> column\'s valid values: <b>product</b>, <b>outsource</b>. Sperate by ";". Ex: "product" or "product;outsource"';
$lang['client_ams_company_id'] = 'ID AMS';
$lang['client_request_add_office_btn'] = 'Add office';
$lang['client_request_office_address'] = 'Office address';
$lang['client_request_add_branch_btn'] = 'Add Branch';
$lang['client_request_company_branch_name'] = 'Branch name';
$lang['client_request_company_branch_vat'] = 'Dependent tax identification number';
$lang['client_request_company_branch_business_address'] = 'Registered business address';
$lang['client_request_company_branch_office_address'] = 'Office address';
$lang['client_request_company_branch_status'] = 'Status of the branch';
$lang['client_request_company_branch_note'] = 'Additional notes (if any)';
$lang['client_request_add_affiliate_btn'] = 'Add Affiliate';
$lang['client_request_relation_type'] = 'Affiliate Select dependency type';
$lang['client_request_related_vat'] = 'Tax ID of the related company';
$lang['client_request_related_company_name'] = 'Related company\'s name';
$lang['client_request_relation_subsidiaries'] = 'Công ty con';
$lang['client_request_relation_holding_company'] = 'Công ty mẹ';
$lang['client_request_relation_has_same_vat'] = 'Công ty cùng group';
$lang['client_request_company_active'] = 'Đang hoạt động';
$lang['client_request_company_not_active_yet'] = 'Chưa đi vào hoạt động';
$lang['client_request_company_has_no_legal_in_vn'] = 'Chưa có pháp nhân Việt Nam';
$lang['client_request_company_inactive'] = 'Đang tạm ngừng hoạt động, kinh doanh';
$lang['client_request_company_insolvency'] = 'Đang giải thể/ Phá sản';
$lang['client_request_company_bankrupted'] = 'Blacklist - Đã giải thể/ Phá sản';
$lang['client_request_source_hotline'] = 'Hotline';
$lang['client_request_source_recruit_socialite'] = 'Company recruit Website/Fanpage';
$lang['client_request_source_it_recruit_platform'] = 'IT recruit platform';
$lang['client_request_source_general_recruit_platform'] = 'General recruit platform';
$lang['client_request_source_linkedin'] = 'Mạng xã hội - LinkedIn';
$lang['client_request_source_facebook'] = 'Mạng xã hội - Facebook';
$lang['client_request_source_group'] = 'Mạng xã hội - Group Cộng đồng tuyển dụng/ Tìm việc';
$lang['client_request_source_viet_nam_mobile_day'] = 'Hội thảo/Sự kiện - Vietnam Mobile Day';
$lang['client_request_source_viet_nam_web_submit'] = 'Hội thảo/Sự kiện - Vietnam Web Summit';
$lang['client_request_source_networking'] = 'Networking/ Referral';
$lang['request_client_invalid_established_date'] = '<b>Date of company establishment</b> is invalid format';
$lang['client_request_create_request_btn'] = 'Create a new customer';
$lang['client_request_search_input_placeholder'] = 'Search for company (ID, Registered business name)';
$lang['client_request_statuses'] = 'Filter by status';
$lang['client_request_waiting_status'] = 'Waiting';
$lang['client_request_sa_approved_status'] = 'SA approved';
$lang['client_request_rejected_status'] = 'Rejected';
$lang['client_request_approved_status'] = 'Approved';
$lang['client_request_col_id'] = 'Request ID';
$lang['client_request_col_company_name'] = 'Company';
$lang['client_request_col_created_by'] = 'Created by';
$lang['client_request_col_created_at'] = 'Created at';
$lang['client_request_col_reason'] = 'Request creation reason';
$lang['client_request_col_action'] = 'Status';
$lang['client_request_col_approve_notes'] = 'Approve note';
$lang['client_request_col_sa_note'] = '<b>SA note:</b> %s';
$lang['client_request_col_leader_note'] = '<b>Leader note:</b> %s';
$lang['client_request_col_edit_note_link'] = 'Edit note';
$lang['client_request_col_last_action'] = 'Final result';
$lang['client_request_col_admin_action_at'] = 'SA approved at';
$lang['client_request_col_leader_action_at'] = 'Leader approved at';
$lang['client_request_note_header'] = 'Approval note';
$lang['client_request_note_text_label'] = 'Note description';
$lang['client_request_created_at_from'] = 'Created at (from)';
$lang['client_request_created_at_to'] = 'Created at (to)';
$lang['client_request_sa_approved_at_from'] = 'SA approved at (from)';
$lang['client_request_sa_approved_at_to'] = 'SA approved at (to)';
$lang['client_request_leader_approved_at_from'] = 'Leader approved at (from)';
$lang['client_request_leader_approved_at_to'] = 'Leader approved at (to)';
$lang['client_request_row_view_link'] = 'View reference';
$lang['client_request_row_view_attachment'] = 'View File/Image refenrece';
$lang['client_request_sa_rejected_status'] = 'SA rejected';
$lang['client_request_leader_approved_status'] = 'Approved';
$lang['client_request_leader_rejected_status'] = 'Leader rejected';
$lang['client_request_approve_confirm_message'] = 'Are you sure you want to approve __created_by__\'s?';
$lang['client_request_reject_confirm_message'] = 'Are you sure you want to reject __created_by__\'s?';
$lang['client_request_title'] = 'Create a new customer request';
$lang['client_request_vat_invalid'] = 'Tax is invalid or already existed';
$lang['request_client_valid_vat'] = '<b>Tax identification number</b> is invalid or already existed';
$lang['request_client_unique_email'] = '<b>Company email</b> already existed';
$lang['request_client_valid_phonenumer'] = '<b>Company phone number</b> is invalid format';
$lang['request_client_valid_url'] = '<b>Reference link</b> is invalid format';
$lang['client_request_approve_success_message'] = 'You approved request successfully!';
$lang['client_request_reject_success_message'] = 'You rejected request successfully!';
$lang['client_request_update_note_success_message'] = 'You updated note successfully!';
$lang['client_request_exception_request_not_found'] = 'This request is not approved by Sale Admin!';
$lang['client_request_exception_request_aproved_already'] = 'This Company already assigned to other sale member!';
$lang['client_request_exception_request_approve_member_only'] = 'You can approve request of team member only!';
$lang['client_request_exception_request_reject_member_only'] = 'You can reject request of team member only!';
$lang['client_request_exception_duplicate_vat'] = 'This tax identification number aready existed!';
$lang['f2f_meeting_attachment_required'] = 'Please attach a file for F2F Meeting';
$lang['f2f_meeting_note'] = 'Number of notes with contact_channel = 7';
$lang['client_request_exception_request_admin_not_found'] = 'This request already approved by other Sale Admin!';
$lang['client_exception_request_cant_edit_note'] = 'You can not edit note of un approved request';
$lang['bulk_remove_customer_admins'] = 'Bulk remove Customer Admin';
$lang['bulk_remove_customer_admin_company_id'] = 'Company ID';
$lang['remove_customer_admins'] = 'Upload and Remove';
$lang['bulk_remove_customer_admin_note'] = 'Your CSV data should be in the format below. The first line of your CSV file should be the column headers as in the table example. Also make sure that your file is UTF-8 to avoid unnecessary encoding problems.';
$lang['bulk_remove_customer_admin_success'] = 'Bulk remove Customer Admin success';
$lang['bulk_remove_customer_admin_error'] = 'Bulk remove Customer Admin error: %s';
$lang['setting_kpi'] = 'Enter KPI & day off';
$lang['customer_statistic_customer_overall'] = 'Customer<br>Data';
$lang['customer_statistic_total_customers'] = 'Total customer';
$lang['customer_statistic_total_expired_customers_next_10_days'] = 'Customers will be expired in 10 days';
$lang['customer_statistic_total_free_customers'] = 'Free data';
$lang['customer_statistic_business_overall'] = 'Sale<br>this month';
$lang['customer_statistic_total_paid_reve_orders'] = 'Paid Revenue';
$lang['customer_statistic_total_paid_orders'] = 'Paid Invoice';
$lang['customer_statistic_total_delivered_orders'] = 'Closed Revenue';
$lang['customer_statistic_total_orders'] = 'Closed Invoices';
$lang['customer_statistic_working_performance'] = 'Indicators';
$lang['customer_statistic_this_month'] = 'This month';
$lang['customer_statistic_today'] = 'Today';
$lang['customer_statistic_company_contacted_success'] = 'Connected success (Company)';
$lang['customer_statistic_company_contacted_later'] = 'Connected again (Company)';
$lang['customer_statistic_company_contacted'] = 'Total customers are connected';
$lang['customer_statistic_company_not_contacted_yet'] = 'Customers haven’t been connected';
$lang['customer_statistic_valid_created_contact'] = 'New contacts are created';
$lang['customer_statistic_valid_created_company'] = 'New companies are created (approved)';
$lang['customer_statistic_valid_requested_take_care'] = 'Free data requests are approved';
$lang['customer_statistic_calls'] = 'Call';
$lang['customer_statistic_call_durations'] = 'Talk time (minutes)';
$lang['customer_statistic_please_wailt'] = 'Please wait...';
$lang['setting_kpi_title'] = 'Configure KPIs';
$lang['team'] = 'Team';
$lang['title_kpi_process'] = 'KPI Process indicator';
$lang['total_working_days'] = 'Workday/month';
$lang['total_working_days_holder'] = 'Work day';
$lang['total_working_days_note'] = 'Working days (Day/month)';
$lang['call'] = 'Call';
$lang['call_number'] = 'Call';
$lang['call_holder'] = 'Calls/ day';
$lang['talk'] = 'Talk';
$lang['talk_holder'] = 'Minutes/ day';
$lang['connect'] = 'Social Chat';
$lang['connect_holder'] = 'Notes/date';
$lang['kpi_sales'] = 'KPI Sales revenue';
$lang['salespersion'] = 'Salesperson';
$lang['vnd_month'] = ' (VND/ month)';
$lang['days_off'] = 'Day off';
$lang['sales_target'] = 'Sales targets';
$lang['add_day_off'] = 'Add days off';
$lang['select_day'] = 'Select date';
$lang['select_time'] = 'Choose time';
$lang['month_kpi_null'] = 'Error: You have not entered KPI Month';
$lang['team_sales_null'] = 'Error: You have not selected Team KPI';
$lang['success_json'] = 'Processed successfully!';
$lang['failed_json'] = 'Handling failure!';
$lang['result_revenue_title'] = 'Business results table';
$lang['result_activities_title'] = 'Process index table';
$lang['day_null'] = 'Error. No date selected';
$lang['month_null'] = 'Error. No month selected';
$lang['full_team_kpi'] = 'All team';
$lang['full_all_team_kpi'] = 'Total';
$lang['total_new_client_month_holder'] = 'New DB';
$lang['total_new_client_month_note'] = 'New customers/ month';
$lang['total_new_contact_month_holder'] = 'Update contact';
$lang['total_new_contact_month_note'] = 'New contacts/ month';
$lang['total_talk_per_day'] = 'Talk';
$lang['total_talk_per_day_holder'] = 'Calls/ day';
$lang['invoice_closing_date'] = 'Invoice date VAT';
$lang['wieght'] = 'Weight';
$lang['error_day_off'] = 'Error. Ngày nghỉ chưa được chọn';
$lang['error_day_off_time'] = 'Error. Chưa chọn thời gian nghỉ';
$lang['error_day_off_exits'] = 'Error. Day off overlap';
$lang['error_day_off_month_exits'] = 'Error. Day off month is incorrect';
$lang['call_box_title'] = 'You have a new call';
$lang['call_box_company'] = 'Company:';
$lang['call_box_contact_name'] = 'Contact\'s name:';
$lang['call_box_latest_note'] = 'Latest note:';
$lang['call_box_accept'] = 'Trả lời';
$lang['call_box_decline'] = 'Đóng';

// Free Quota Management
$lang['add_free_quota'] = 'Add Free Quota';
$lang['free_quota_summary'] = 'Free Quota Summary';
$lang['total_free_quota'] = 'Total Free Quota';
$lang['used_free_quota'] = 'Used Free Quota';
$lang['remaining_free_quota'] = 'Remaining Free Quota';
$lang['amount'] = 'Amount';
$lang['note'] = 'Note';
$lang['processing'] = 'Processing...';
$lang['free_quota_added_successfully'] = 'Free quota added successfully';
$lang['error_occurred'] = 'An error occurred. Please try again.';
$lang['permission_view_client_creating_request_global'] = 'View request create customer (Global)';
$lang['permission_view_client_creating_request_own'] = 'View request create customer (Own)';
$lang['permission_approve_client_creating_request_global'] = 'Approve request create customer (Global)';
$lang['permission_approve_client_creating_request_own'] = 'Approve request create customer (Own)';
$lang['permission_bulk_remove_customer_admin'] = 'Bulk remove customer admin';
$lang['kpi_revenue_team'] = 'KPI team revenue';
$lang['ext'] = 'Ext';
$lang['client_request_source_signup'] = 'Signup';
$lang['customer_statistic_total_expired_customers_next_x_days'] = 'Customers will be expired in 20 days';
$lang['permission_edit_other'] = 'Edit (Subtract Invoice from Quote||Paid)';
$lang['error_notfavorite'] = 'Processing failure!';
$lang['success_notfavorite'] = 'Successfully processed!';
$lang['inactive'] = 'Turn off the client';
$lang['inactived'] = 'Disabled %s.';
$lang['problem_inactive'] = 'Problem occurs while turning off %s';
$lang['sdt'] = 'Phone';
$lang['date_assigned'] = 'Assigned date';
$lang['by'] = 'By';
$lang['approve_by'] = 'Approved by';
$lang['id_ams'] = 'ID AMS';
$lang['permission_edit_id_ams'] = 'Edit ID AMS';
$lang['ams_companies_title'] = 'AMS Company Manage';
$lang['ams_companies_sync_btn'] = 'Sync AMS';
$lang['ams_filter_title'] = 'Filter';
$lang['ams_companies_placeholder_id_ams'] = 'ID AMS';
$lang['ams_companies_placeholder_company_name'] = 'Company Name';
$lang['ams_companies_placeholder_ams_company_name'] = 'Company Name AMS';
$lang['ams_companies_placeholder_crm_company_name'] = 'Company Name CRM';
$lang['ams_companies_placeholder_employer_account'] = 'Employer Account';
$lang['ams_companies_placeholder_status'] = 'Status';
$lang['ams_companies_placeholder_features'] = 'Service';
$lang['ams_companies_placeholder_expires_in'] = 'Expires in';
$lang['ams_company_status_active'] = 'Active';
$lang['ams_company_status_inactive'] = 'Inactive';
$lang['ams_company_status_review'] = 'Review';
$lang['ams_company_expires_in_null'] = 'Null';
$lang['ams_company_expires_in_zero'] = 'Còn hạn';
$lang['ams_company_expires_in_not_zero'] = 'Hết hạn';
$lang['ams_companies_column_id_ams'] = 'ID AMS';
$lang['ams_companies_column_company'] = 'Company';
$lang['ams_companies_column_employer'] = 'Employer';
$lang['ams_companies_column_additional_info'] = 'Additional Information';
$lang['ams_companies_column_status'] = 'Status';
$lang['ams_companies_row_view_service_detail'] = 'View Service Detail';
$lang['ams_companies_row_phone'] = 'Phone';
$lang['ams_companies_row_email'] = 'Email';
$lang['ams_companies_row_address'] = 'Address';
$lang['ams_companies_row_pic'] = 'Person in charge';
$lang['ams_companies_row_create_by'] = 'Created by';
$lang['ams_companies_row_last_modified_by'] = 'Last modified by';
$lang['ams_companies_row_service_no'] = 'No';
$lang['ams_companies_row_service_name'] = 'Service';
$lang['ams_companies_row_service_activated_at'] = 'Active at';
$lang['ams_companies_row_service_expired_at'] = 'Expires at (days)';
$lang['ams_companies_row_service_unlimited'] = 'Unlimited';
$lang['ams_not_valid_sync_rules'] = 'This company is not valid sync rules or synced already';
$lang['ams_not_found_vat'] = 'Not found AMS company';
$lang['ams_synced_already'] = 'AMS company already mapped to CRM';
$lang['ams_sync_successfully'] = 'AMS companies are mapped to CRM successfully';
$lang['ams_sync_confirm'] = 'Are you sure you want to map the %s AMS ID to CRM?';
$lang['ams_jobs_title'] = 'AMS Job Manage';
$lang['ams_jobs_summary_title'] = 'Summary';
$lang['ams_jobs_total_invoices_title'] = 'Total INV';
$lang['ams_jobs_total_paid_package_title'] = 'Total paid packages';
$lang['ams_jobs_total_job_posted_title'] = 'Total Job Post AMS';
$lang['ams_jobs_column_crm_invoice'] = 'CRM INV';
$lang['ams_jobs_column_crm_status'] = 'CRM Status';
$lang['ams_jobs_column_job'] = 'Job';
$lang['ams_jobs_column_package'] = 'Package';
$lang['ams_jobs_column_service'] = 'Service';
$lang['ams_jobs_column_view_avg'] = 'Views';
$lang['ams_jobs_column_click_apply'] = 'Total CV';
$lang['ams_jobs_column_total_ready_cv'] = 'Total Ready CV';
$lang['ams_jobs_column_published_date'] = 'Published date';
$lang['ams_jobs_column_status'] = 'Status';
$lang['ams_jobs_column_action'] = 'Action';
$lang['ams_jobs_row_location'] = 'Location';
$lang['ams_jobs_row_package_publish_at'] = 'Publish date';
$lang['ams_jobs_row_expires_at'] = 'Expires at';
$lang['ams_jobs_row_service_active_at'] = 'Active date';
$lang['ams_jobs_row_paid_by'] = 'Paid by';
$lang['ams_jobs_row_created_by'] = 'Created by';
$lang['ams_jobs_row_published_date'] = 'Published date';
$lang['ams_jobs_row_expired_date'] = 'Expired date';
$lang['ams_jobs_row_expired_in_days'] = 'Expired in %s days';
$lang['ams_jobs_row_expired_now'] = 'Expired';
$lang['ams_jobs_placeholder_packages'] = 'Package';
$lang['ams_job_status_open'] = 'Open';
$lang['ams_company_status_draft'] = 'Draft';
$lang['ams_company_status_closed'] = 'Closed';
$lang['ams_employees_title'] = 'AMS Employer Account';
$lang['ams_employees_column_account'] = 'Account';
$lang['ams_employees_column_detail'] = 'Detail';
$lang['ams_employees_column_approved_at'] = 'Approved at';
$lang['ams_employees_column_status'] = 'Status';
$lang['ams_employees_row_user_name'] = 'User name';
$lang['ams_employees_row_email'] = 'Email';
$lang['ams_employees_row_phone'] = 'Phone';
$lang['ams_employees_row_lastname'] = 'Last name';
$lang['ams_employees_row_position'] = 'Position';
$lang['permission_view_ams_company'] = 'View AMS Company';
$lang['call_log_call_type'] = 'Call type';
$lang['call_log_call_in_type'] = 'Call In';
$lang['call_log_call_out_type'] = 'Call Out';
$lang['service_add'] = 'Add services';
$lang['services_dt_name_holder'] = 'Enter the service name';
$lang['level_service1'] = 'Level in service';
$lang['des_item_title_level1'] = 'Complete Level 1 before performing Level 2 on the group';
$lang['button_add_level1'] = 'Add level 1';
$lang['button_add_level2'] = 'Add level 2';
$lang['name_service_null'] = 'Service name has not been entered';
$lang['error_name_level1'] = 'The name of Level 1 has not been entered';
$lang['error_name_level2'] = 'The name of the Level 2 subdivision has not been entered';
$lang['expected_completion_1'] = '0.5 - 1 working day';
$lang['expected_completion_2'] = '1 - 4 working hours';
$lang['expected_completion_3'] = '1 - 2 working days';
$lang['expected_completion_4'] = '0.5 - 2 working days';
$lang['expected_completion_5'] = '1 - 3 working days';
$lang['expected_completion_6'] = '3 - 5 working days';
$lang['select_priority_level'] = 'Select priority level';
$lang['select_expected_completion_time'] = 'Select expected completion time';
$lang['level_1_holder'] = 'Name the level 1';
$lang['level_2_holder'] = 'Name the level 2';
$lang['feedback'] = 'Feedback';
$lang['level_1'] = 'Service Level 1';
$lang['level_2'] = 'Service Level 2';
$lang['select_c1'] = 'Select Level 1';
$lang['select_c2'] = 'Select Level 2';
$lang['select_ut'] = 'Select priority level';
$lang['date_handle'] = 'Closed date';
$lang['remove_note_move_free_db_notice'] = 'The company %s has been moved to free data due to the deletion of the Contact Success Note';
$lang['contact_mobile_phone'] = 'Mobile Phone';
$lang['request_invoice_email'] = 'Email address to receive invoices (Maximum of 3 customer emails)';
$lang['request_invoice_email_table'] = 'Email address to receive invoices';
$lang['request_invoice_email_placeholder'] = 'Enter email';
$lang['request_invoice_request_status'] = 'Request status';
$lang['request_invoice_request_status1'] = 'Payment has been collected and invoice must be issued';
$lang['request_invoice_request_status2'] = 'Issue invoices first, collect payment later';
$lang['request_invoice_company_name'] = 'Company issues invoices';
$lang['request_invoice_company_name_holder'] = 'Enter the billing company';
$lang['request_invoice_export_address'] = 'Invoice address';
$lang['request_invoice_export_address_holder'] = 'Enter your billing address';
$lang['request_invoice_vat'] = 'Tax code';
$lang['request_invoice_vat_holder'] = 'Enter tax code';
$lang['request_invoice_contract_status'] = 'Contract status';
$lang['request_invoice_status'] = 'Invoice status';
$lang['request_invoice_output_content'] = 'Description';
$lang['request_invoice_output_content_holder'] = 'Enter description';
$lang['request_invoice_note'] = 'Note';
$lang['request_invoice_note_holder'] = 'Enter note';
$lang['request_invoice_service'] = 'Services provided by APPLANCER';
$lang['contract_payment_date'] = 'Please select the PO contract payment date';
$lang['contract_payment_date_holder'] = 'Select date';
$lang['error_received_email_arr_null'] = 'Email to receive invoice has not been entered';
$lang['error_received_email_arr_format'] = 'Email invalidate';
$lang['error_add_request_invoice'] = 'Invoice request failed';
$lang['error_exists_request_invoice'] = 'Already have the request for this invoice';
$lang['invoice_request_notification'] = 'A new invoice request has been successfully created for %s';
$lang['name_invoice_reques'] = 'Petitioner';
$lang['date_invoice_request'] = 'Request Date';
$lang['account'] = 'Accountant';
$lang['invoice_request'] = 'Request an invoice';
$lang['export_invoice'] = 'Request an invoice';
$lang['export_invoice_requested'] = 'Invoice requested';
$lang['invoice_requested_success'] = 'Successfully created invoice request for invoice code %s';
$lang['invoice_request_notification_pay'] = 'Invoice %s';
$lang['report_invoice_pay_sucess'] = 'Invoice has been issued successfully. Bill date';
$lang['report_invoice_pay_day'] = 'Date of payment';
$lang['payment_to_po'] = 'Payment date according to PO';
$lang['invoice_draft_placeholder'] = 'Request a draft before signing';
$lang['request_invoice_draft_title'] = 'Requirements';
$lang['request_invoice_draft'] = 'Export draft';
$lang['request_status'] = 'Processing status';
$lang['request_status_update_ok'] = 'Status update successful';
$lang['request_status_update_no'] = 'Status update failed';
$lang['request_status_update'] = 'Update status';
$lang['date_invoice_request_from'] = 'Date requested from';
$lang['date_invoice_request_to'] = 'Date requested to';
$lang['invoice_value'] = 'Bill invoice';
$lang['invoice_request_change_notification'] = '[Invoice] %s';
$lang['invoice_data_date_title'] = 'Date of invoice VAT';
$lang['total_invoice_value'] = 'Total invoice value';
$lang['ams_post_job_title'] = 'Post Job';
$lang['ams_post_job_company_list'] = 'Company to post job';
$lang['ams_post_job_invoice_list'] = '<b>ID Invoice</b>';
$lang['ams_post_job_invoice_placeholder'] = 'Invoice';
$lang['ams_post_job_title_label'] = '<b>Job title</b>';
$lang['ams_post_job_title_placeholder'] = 'Enter your job title';
$lang['ams_post_job_description_label'] = '<b>Job description</b>';
$lang['ams_post_job_description_placeholder'] = 'Enter your job description';
$lang['ams_post_job_category_label'] = '<b>Job Category</b>';
$lang['ams_post_job_category_placeholder'] = 'Select the Job Category';
$lang['ams_post_job_role_label'] = '<b>Role</b>';
$lang['ams_post_job_role_placeholder'] = 'Select the role';
$lang['ams_post_job_skill_qualify_label'] = '<b>Your skills & qualifications</b>';
$lang['ams_post_job_skill_qualify_placeholder'] = 'Add skills & qualifications';
$lang['ams_post_job_benefit_title'] = '<b>Benefits</b>';
$lang['ams_post_job_email_label'] = 'Email for Applications';
$lang['ams_post_job_email_placeholder'] = 'Add multiple email addresses and separate them by pressing enter or using comma';
$lang['ams_post_job_note_label'] = '<b>Note for TopDev</b> <small>(This information won’t be displayed)</small>';
$lang['ams_post_job_note_placeholder'] = 'Add note';
$lang['ams_post_job_icon_placeholder'] = 'Pick an icon';
$lang['ams_post_job_benefit_placeholder'] = 'Enter your benefit description';
$lang['ams_post_job_benefit_select'] = 'Select your benefit';
$lang['ams_post_job_design_template_title'] = 'Choose template';
$lang['ams_post_job_design_top_banner_title'] = 'Choose top banner';
$lang['ams_post_job_design_color_title'] = 'Choose color';
$lang['ams_post_job_company_tagline_title'] = 'Company tagline';
$lang['ams_post_job_company_tagline_placeholder'] = 'Enter Company tagline';
$lang['ams_post_job_company_job_logo_title'] = 'Upload company logo';
$lang['ams_post_job_required_notice'] = '<b>Note:</b> You must fill in the required fields <span class="text-danger">(*)</span> to save';
$lang['ams_post_job_localtion_label'] = '<b>Location</b>';
$lang['ams_post_job_localtion_placeholder'] = 'Select location';
$lang['ams_post_job_salary_label'] = '<b>Salary</b>';
$lang['ams_post_job_salary_btn_usd'] = 'USD';
$lang['ams_post_job_salary_btn_vnd'] = 'VND';
$lang['ams_post_job_salary_negotiable_radio'] = 'NEGOTIABLE';
$lang['ams_post_job_salary_negotiable_desciption'] = 'This salary will be displayed with the status "Negotiable" on the website topdev.vn';
$lang['ams_post_job_level_label'] = '<b>Level</b>';
$lang['ams_post_job_level_placeholder'] = 'Select job level';
$lang['ams_post_job_yoe_label'] = '<b>Year of experience</b>';
$lang['ams_post_job_yoe_min_placeholder'] = 'Minimum year of experience';
$lang['ams_post_job_yoe_max_placeholder'] = 'Maximum year of experience';
$lang['ams_post_job_type_label'] = '<b>Job Type</b>';
$lang['ams_post_job_type_placeholder'] = 'Select job type';
$lang['ams_post_contract_type_label'] = '<b>Contract type</b>';
$lang['ams_post_contract_type_placeholder'] = 'Select contract type';
$lang['ams_post_job_skill_label'] = '<b>Skills</b>';
$lang['ams_post_job_skill_placeholder'] = 'Select job skills (maximum 5 skills)';
$lang['ams_post_job_skill_max_options'] = 'You can only select maximum 5 skills';
$lang['ams_post_job_process_label'] = '<b>Recruitment process</b>';
$lang['ams_post_job_process_placeholder'] = 'Enter your Recruitment process';
$lang['ams_post_job_process_select'] = 'Select your Recruitment process';
$lang['ams_post_job_process_round'] = 'Round';
$lang['ams_post_job_process_description'] = 'Maximum 100 characters, DO NOT use ALL CAPITAL letter';
$lang['ams_post_job_from_label'] = '<b>From</b>';
$lang['ams_post_job_to_label'] = '<b>To</b>';
$lang['ams_post_job_btn_add'] = 'Add';
$lang['ams_post_job_btn_save'] = 'Save';
$lang['ams_post_job_published_date_label'] = '<b>Published date</b>';
$lang['ams_post_job_responsibilities_label'] = '<b>Responsibilities</b>';
$lang['ams_post_job_responsibilities_placeholder'] = 'Enter your Responsibilities';
$lang['ams_post_job_responsibilities_select'] = 'Select your Responsibilities';
$lang['ams_post_job_requirements_label'] = '<b>Requirements</b>';
$lang['ams_post_job_requirements_placeholder'] = 'Enter your Requirements';
$lang['ams_post_job_requirements_select'] = 'Select your Requirements';
$lang['ams_post_job_education_degree_label'] = '<b>Degree</b>';
$lang['ams_post_job_education_major_label'] = '<b>Major</b>';
$lang['ams_post_job_education_major_placeholder'] = 'Select your major';
$lang['ams_post_job_education_certification_label'] = '<b>Certification</b>';
$lang['ams_post_job_education_certification_placeholder'] = 'Enter your certification';
$lang['ams_post_job_validate_company_required'] = 'Please select a company';
$lang['ams_post_job_validate_title_required'] = 'Job Title is a required field';
$lang['ams_post_job_validate_category_required'] = 'Job Category is required';
$lang['ams_post_job_validate_role_required'] = 'Role is a required field';
$lang['ams_post_job_validate_skill_qualify_required'] = 'Requirements is a required field';
$lang['ams_post_job_validate_responsibilities_required'] = 'Responsibilities is a required field';
$lang['ams_post_job_validate_recruitment_processes_required'] = 'Recruitment process is a required field';
$lang['ams_post_job_validate_benefits_required'] = 'Benefits is a required field';
$lang['ams_post_job_validate_location_required'] = 'Location field must have at least 1 items';
$lang['ams_post_job_validate_salary_type_required'] = 'Please select a ';
$lang['ams_post_job_validate_salary_required'] = 'One of two salary fields must be filled';
$lang['ams_post_job_validate_published_at_required'] = 'Published at is a required field';
$lang['ams_post_job_validate_published_at_invalid'] = 'Published date is invalid format';
$lang['ams_post_job_validate_salary_value_invalid'] = 'Salary from must be larger than salary to';
$lang['ams_post_job_validate_level_required'] = 'Level field must have at least 1 items';
$lang['ams_post_job_validate_job_type_required'] = 'Job Type field must have at least 1 items';
$lang['ams_post_job_validate_skill_required'] = 'Skills field must have at least 1 items';
$lang['ams_post_job_validate_education_degree_required'] = 'Degree field must have at least 1 items';

// Free Quota
$lang['ams_free_quota'] = 'Free Quota';
$lang['ams_free_quota_used'] = 'Free Quota Used';
$lang['ams_free_quota_remaining'] = 'Free Quota Remaining';
$lang['ams_add_free_quota'] = 'Add Free Quota';
$lang['ams_free_quota_amount'] = 'Free Quota Amount';
$lang['ams_free_quota_amount_placeholder'] = 'Enter free quota amount';
$lang['ams_free_quota_add_success'] = 'Free quota added successfully';
$lang['ams_free_quota_add_failed'] = 'Failed to add free quota';
$lang['ams_free_quota_invalid_amount'] = 'Please enter a valid positive number';
$lang['ams_free_quota_company_required'] = 'Please select a company';
$lang['ams_free_quota_history'] = 'Free Quota History';
$lang['ams_free_quota_date_added'] = 'Date Added';
$lang['ams_free_quota_amount_added'] = 'Amount Added';
$lang['ams_free_quota_added_by'] = 'Added By';
$lang['ams_post_job_validate_education_major_required'] = 'Major is a required field';
$lang['ams_post_job_validate_contract_type_required'] = 'Contract type field must have at least 1 items';
$lang['ams_post_job_validate_yoe_required'] = 'This field cannot be blank';
$lang['ams_post_job_validate_yoe_value_invalid'] = '"Minimum year of experience" must be smaller or equal to "Maximum year of experience"';
$lang['ams_post_job_validate_benefit_required'] = 'This field cannot be blank';
$lang['ams_post_job_validate_invoice_required'] = 'Please select an invoice';
$lang['ams_post_job_validate_invoice_out_date'] = 'Invoice is expired or exceed number';
$lang['ams_post_job_validate_company_belong_to_client'] = 'Ams company is not mapped with this Company';
$lang['ams_post_job_validate_invalid_published_at'] = '<b>Published at</b> is invalid format';
$lang['ams_post_job_validate_invalid_education_degree'] = '<b>Degree</b> is invalid';
$lang['ams_post_job_validate_invalid_education_major'] = '<b>Major</b> is required';
$lang['ams_post_job_validate_job_template_required'] = '<b>Template</b> is required';
$lang['ams_post_job_validate_job_banner_required'] = '<b>Banner</b> is required';
$lang['ams_post_job_validate_job_template_color_required'] = '<b>Color</b> is required';
$lang['ams_post_job_btn_cancel'] = 'Cancel';
$lang['ams_post_job_cancel_confirmation'] = 'This action cannot be undo. Are you sure want to discard your change?';
$lang['ams_post_job_btn_submit'] = 'SUBMIT FOR REVIEW';
$lang['ams_post_job_submit_review_success'] = 'The job posting has been successfully created';
$lang['ams_post_job_submit_updated_success'] = 'Job posting updated successfully';
$lang['ams_job_manage_title'] = 'Jobs Manage';
$lang['ams_job_manage_job_table_title'] = 'Job Manage';
$lang['ams_job_manage_invoice_table_title'] = 'PO Manage';
$lang['ams_job_manage_invoice_th_invoice_id'] = 'ID PO';
$lang['ams_job_manage_invoice_th_paid_packages'] = 'Number of posts purchased';
$lang['ams_job_manage_invoice_th_used_packages'] = 'Number of posts used';
$lang['ams_job_manage_invoice_th_remain_packages'] = 'Number of post remaining';
$lang['ams_job_manage_invoice_th_expired_at'] = 'Expired Date';
$lang['ams_job_manage_invoice_th_invoice_detail'] = 'PO details';
$lang['ams_job_manage_invoice_th_invoice_packacge_name'] = 'Package';
$lang['ams_job_manage_invoice_th_invoice_paid_packages'] = 'Number of posts purchased for  package';
$lang['ams_job_manage_invoice_th_invoice_used_packages'] = 'Number of posts used for package';
$lang['ams_job_manage_invoice_th_invoice_remain_packages'] = 'Number of posts remaining for package';
$lang['ams_job_manage_invoice_total_paid_invoices'] = 'Total PO:';
$lang['ams_job_manage_invoice_total_paid_packages'] = 'Total number of posts purchased:';
$lang['ams_job_manage_invoice_total_used_packages'] = 'Total number of posts used:';
$lang['ams_job_manage_invoice_total_remain_packages'] = 'Total number of post remaining:';
$lang['invoice_permission_edit_use_expired_at'] = 'Edit expire date';
$lang['invoice_edit_use_expired_at_btn'] = 'Extend';
$lang['invoice_edit_use_expired_at_save_btn'] = 'Save';
$lang['invoice_edit_use_expired_at_cancel_btn'] = 'Cancel';
$lang['ams_job_manage_invoice_total_invoice'] = 'PO';
$lang['ams_job_manage_invoice_total_invoices'] = 'PO';
$lang['ams_job_manage_invoice_total_post'] = 'Post';
$lang['ams_job_manage_invoice_total_posts'] = 'Posts';
$lang['ams_job_manage_invoice_update_successfully'] = 'Update package expire date successfully';
$lang['ams_job_manage_invoice_update_failed'] = 'Invoice expire date is not update due to new date is less than previous expire date';
$lang['ams_job_manage_invoice_not_found_invoice'] = 'Not found this package!';
$lang['ams_job_manage_invoice_expired_at_required'] = 'This field cannot be blank';
$lang['ams_job_manage_invoice_not_allow_edit'] = 'You are unable to update package of the other invoice';
$lang['permission_edit_job_global'] = 'Edit Job (Global)';
$lang['permission_edit_job_owned'] = 'Edit Job (Own)';
$lang['total_used_packages'] = 'Number of posts used for package';
$lang['total_used_input_packages'] = 'Number of posts used inputted for package';
$lang['updated_used_input_packages_successfully'] = 'Updated number of posts used successfully';
$lang['permission_edit_used_packages'] = 'Update number of posts used';
$lang['ams_post_paid_package_id'] = 'Package';
$lang['ams_post_job_validate_paid_package_valid'] = 'This package is not belongs to the selected invoice';
$lang['ams_post_job_free_checkbox_title'] = 'Free Job Post';
$lang['ams_post_job_content_image_title'] = 'Content image ';
$lang['ams_post_job_free_checkbox_description'] = 'Note: Please check this box if this is the promotion job';
$lang['ams_job_manage_invoice_free_tag'] = 'Gift';
$lang['num_of_usage_behavior'] = 'Num of usage behavior';
$lang['num_of_usage_behavior_min_validation'] = 'Num of usage behavior is greater than or equal %s';
$lang['permission_update_num_of_usage_behavior'] = 'Update num of Usage Behavior';
$lang['permission_update_usage_behavior'] = 'Update Usage Behavior';
$lang['import_num_of_usage_behavior_btn'] = 'Import num of usage behavior';
$lang['import_num_of_usage_behavior_title'] = 'Import usage behavior';
$lang['detail_request'] = 'Ticket Detail';
$lang['supports'] = 'Support';
$lang['permission_view_team'] = 'View (Team)';
$lang['help_view_note'] = 'View all. If B2B Sales and B2B Sales Lead are allowed to view all, you can tick this permission.';
$lang['help_view_team_note'] = 'For B2B Sales Lead, view items of companies that their team members are assigned as Customer Admin.';
$lang['help_view_own_note'] = 'For B2B Sales, view items of companies that they are assigned as Customer Admin.';
$lang['help_view_own_note1'] = 'Based on creator';
$lang['ams_search_cv_title'] = 'AMS Search CV Manage';
$lang['ams_search_summary_invoice_title'] = 'Invoices';
$lang['ams_search_summary_invoice_purchased'] = 'Purchased';
$lang['ams_search_summary_unlock_title'] = 'Unlocks';
$lang['ams_search_summary_unlock_total'] = 'Total';
$lang['ams_search_summary_unlock_unused'] = 'Unused';
$lang['ams_search_summary_unlock_used'] = 'Used';
$lang['ams_search_summary_unlock_expired'] = 'Expired';
$lang['ams_search_column_invoice'] = '#Invoice';
$lang['ams_search_column_ams_id'] = '#ID AMS';
$lang['ams_search_column_purchased_date'] = 'Product name';
$lang['ams_search_column_product_name'] = 'Purchased date';
$lang['ams_search_column_used_total_unlocks'] = 'Used/Total unlocks';
$lang['ams_search_column_status'] = 'Status';
$lang['ams_search_column_time'] = 'Time';
$lang['ams_search_column_time_active_date_row'] = 'Valid date';
$lang['ams_search_column_time_expired_date_row'] = 'Expired date';
$lang['ams_search_column_action'] = 'Action';
$lang['ams_search_column_action_edit_btn'] = 'Edit';
$lang['ams_search_column_action_save_btn'] = 'Save';
$lang['ams_search_column_action_cancel_btn'] = 'Cancel';
$lang['ams_search_cv_update_invoice_success'] = 'Update invoice successfully';
$lang['ams_search_cv_update_invoice_failed'] = 'Update invoice unsuccessfully';
$lang['permission_change_search_cv_invoice'] = 'Change Search CV\'s invoice';
$lang['ams_post_job_validate_package_required'] = 'Please select an package';
$lang['call_center_logs_not_found_voice_records'] = 'Unable to get voice record url';
$lang['call_center_logs_voice_ai'] = 'Voice AI';
$lang['crawler_from_company'] = 'Company';
$lang['crawler_table_header_url'] = 'Job url';
$lang['crawler_table_header_title'] = 'Job title';
$lang['crawler_table_header_company'] = 'Job\'s Company';
$lang['crawler_table_header_description'] = 'Job description';
$lang['crawler_table_header_from_page'] = 'Company';
$lang['crawler_table_header_posted_at'] = 'Posted At';
$lang['crawler_table_header_location'] = 'Location';
$lang['crawler_table_header_skills'] = 'Skills';
$lang['crawler_table_header_salary'] = 'Salary';
$lang['call_center_statistic_table_header_ca'] = 'Salesperson';
$lang['call_center_statistic_table_header_no_call_out'] = 'N of Call out';
$lang['call_center_statistic_table_header_no_call_out_success'] = 'N of Successful Call out';
$lang['call_center_statistic_table_header_no_call_out_missed'] = 'N of Rejected Call out';
$lang['call_center_statistic_table_header_no_call_in'] = 'N of Call In';
$lang['call_center_statistic_table_header_talktime'] = 'Talk time';
$lang['call_center_statistic_date_today'] = 'Today';
$lang['call_center_statistic_date_yesterday'] = 'Yesterday';
$lang['call_center_statistic_date_last_7_days'] = 'Last 7 days';
$lang['call_center_statistic_date_last_30_days'] = 'Last 30 days';
$lang['call_center_statistic_date_this_week'] = 'This week';
$lang['call_center_statistic_date_last_week'] = 'Last week';
$lang['call_center_statistic_date_this_month'] = 'This month';
$lang['call_center_statistic_date_last_month'] = 'Last month';
$lang['call_center_statistic_date_all'] = 'All';
$lang['permission_view_monthly_report'] = 'View PO Monthly Report';
$lang['als_reports_monthly_invoice_submenu'] = 'Purchase Order Monthly Report';
$lang['monthly_invoice_report_export_btn'] = 'Export';
$lang['monthly_invoice_report_tab_issue_date_title'] = 'Report by Issues Date';
$lang['monthly_invoice_report_tab_posted_date_title'] = 'Report by Job\'s Posting Date';
$lang['monthly_invoice_report_issue_date_picker'] = 'Issue date';
$lang['monthly_invoice_report_job_post_date_picker'] = 'Posted date';
$lang['monthly_invoice_report_detail_invoice_month_title'] = 'Issue Month';
$lang['monthly_invoice_report_detail_month_title'] = 'Month';
$lang['monthly_invoice_report_detail_summary_heading_title'] = 'Invoice Summary';
$lang['monthly_invoice_report_detail_amount_title'] = 'Invoice Amount (No tax)';
$lang['counts_job_postings_by_payment_type'] = 'Count job postings';
$lang['monthly_invoice_report_detail_quantity_title'] = 'Invoice Quantity';
$lang['monthly_invoice_report_detail_advanced_amount_title'] = 'Unused Amount';
$lang['monthly_invoice_report_detail_revenue_title'] = 'Used Amount';
$lang['monthly_invoice_report_detail_unpaid_title'] = 'Unpaid';
$lang['monthly_invoice_report_detail_heading_title'] = 'Invoice Details';
$lang['monthly_invoice_report_detail_status_invoice_title'] = 'No';
$lang['monthly_invoice_report_job_posting_job_title'] = 'Posted Date';
$lang['monthly_invoice_report_detail_invoice_no_title'] = 'PO No';
$lang['monthly_invoice_report_detail_minvoice_no_title'] = 'M-invoice No';
$lang['monthly_invoice_report_detail_invoice_status_title'] = 'PO Status';
$lang['monthly_invoice_report_detail_issued_date_title'] = 'Issue Date';
$lang['monthly_invoice_report_job_posting_start_title'] = 'Sale Agent';
$lang['ams_post_paid_status'] = 'Status';
$lang['ams_post_html_desktop_label'] = 'Html file Desktop';
$lang['ams_post_html_mobile_label'] = 'Html file Mobile';
$lang['job_revenue_report_title'] = 'Job\'s Posting Revenue Report';
$lang['job_revenue_report_daily_tab_title'] = 'Daily Revenue';
$lang['job_revenue_report_monthly_tab_title'] = 'Monthly Revenue';
$lang['job_revenue_report_daily_po_no_title'] = 'PO #';
$lang['job_revenue_report_daily_invoice_no_title'] = 'Invoice #';
$lang['job_revenue_report_daily_package_title'] = 'Package Name';
$lang['job_revenue_report_daily_date_title'] = 'Day';
$lang['job_revenue_report_daily_revenue_title'] = 'Revenue';
$lang['job_revenue_report_monthly_inv_info_title'] = 'Invoice Information';
$lang['job_revenue_report_monthly_po_no_title'] = 'PO #';
$lang['job_revenue_report_monthly_invoice_no_title'] = 'Invoice #';
$lang['job_revenue_report_monthly_customer_title'] = 'Customer Name';
$lang['job_revenue_report_monthly_jd_info_title'] = 'JD Information';
$lang['job_revenue_report_monthly_start_title'] = 'Start';
$lang['job_revenue_report_monthly_end_title'] = 'End';
$lang['job_revenue_report_monthly_total_duration_title'] = 'Total Duration';
$lang['job_revenue_report_monthly_package_duration_title'] = 'Package Duration';
$lang['job_revenue_report_monthly_contract_duration_title'] = 'Contract Duration';
$lang['job_revenue_report_monthly_total_amount_title'] = 'Total Amount';
$lang['job_revenue_report_monthly_report_month_title'] = 'Report Month';
$lang['job_revenue_report_monthly_duration_title'] = 'Duration';
$lang['job_revenue_report_monthly_amount_title'] = 'Amount';
$lang['request_invoice_reject_invoice_btn'] = 'Reject Invoice';
$lang['request_invoice_issue_invoice_btn'] = 'Issue Invoice';
$lang['request_invoice_fix_invoice_btn'] = 'Fix Invoice';
$lang['request_invoice_request_issue_invoice_btn'] = 'Request Issue Invoice';
$lang['request_invoice_view_invoice_pdf_link'] = 'View PDF';
$lang['request_invoice_error_issue_invoice'] = 'Invoice not found or is not requested to issue yet';
$lang['request_invoice_error_request_issue_invoice'] = 'Invoice not found or it is requested to issue already';
$lang['request_invoice_error_reject_invoice'] = 'Invoice not found or it is issued already';
$lang['request_invoice_request_issue_invoice'] = 'Issue Invoice to PO %s';
$lang['request_invoice_view_pdf'] = 'View Invoice PDF';
$lang['request_invoice_issued_invoice'] = 'request_invoice_issued_invoice';
$lang['CC'] = 'CC';
$lang['handle_issue_po'] = 'handle_issue_po';
$lang['invoice_request_issue_po'] = 'Một yêu cầu xuất chính hóa đơn mới đã được tạo thành công cho %s . Người yêu cầu : %s. Ngày yêu cầu %s - Time: %';
$lang['invoice_request_issue_po_confirm'] = 'Are you sure you want to issue the invoice {inv_number}';
$lang['monthly_invoice_report_tab_transactions_title'] = 'InfoPlus\'s Transactions Report';
$lang['infoplus_payer_info'] = 'Payer Info';
$lang['infoplus_payer_info_payer_no'] = 'Payer No';
$lang['infoplus_payer_info_payer_name'] = 'Payer Name';
$lang['infoplus_payer_info_ec'] = 'E Collection Code';
$lang['infoplus_payer_info_table_index'] = '#';
$lang['infoplus_payer_info_table_invoice'] = 'PO #';
$lang['infoplus_payer_info_table_transaction_id'] = 'Transaction Id';
$lang['infoplus_payer_info_table_qr'] = 'QR Code';
$lang['infoplus_payer_info_table_amount'] = 'Amount';
$lang['infoplus_payer_info_table_start_at'] = 'Start At';
$lang['infoplus_payer_info_table_expires_at'] = 'Expires At';
$lang['infoplus_payer_info_table_status'] = 'Status';
$lang['infoplus_payer_info_table_paid_at'] = 'Paid At';
$lang['ticket_change_status_to_closed'] = 'Ticket - %s has been closed';
$lang['job_id'] = 'AMS job id';
$lang['select_job_company'] = 'Select AMS job id';
$lang['is_churn_new_discount_checkbox_label'] = 'Churn & New Customer Program';
$lang['is_discount_50_percent_checkbox_label'] = '15-day package promotion';
$lang['menu_report_monthly_report_title'] = 'PO Monthly Report';
$lang['menu_report_revenue_report_title'] = 'Job Revenue Report';
$lang['permission_view_job_posting_revenue_report'] = 'View Job Posting Revenue Report';
$lang['po_monthly_report_invoice_quantity_tooltip'] = 'Number of POs issued invoices in the month';
$lang['po_monthly_report_invoice_amount_tooltip'] = 'Total value of POs issued invoices during the month (excluding tax)';
$lang['po_monthly_report_invoice_unused_amount_tooltip'] = 'Total value of PO issued invoice in the month that customer has not used job posting';
$lang['po_monthly_report_invoice_used_amount_tooltip'] = 'Total value of PO issued invoice in the month that the customer has been using job post';
$lang['po_monthly_report_invoice_unpaid_tooltip'] = 'Total value of PO issued invoice in the month the customer has not paid';
$lang['dashboard_invoice_amount_tooltip'] = 'Total value of POs issued invoices (excluding tax)';
$lang['dashboard_invoice_unused_amount_tooltip'] = 'Total value of PO issued invoice that customer has not used job posting';
$lang['dashboard_invoice_used_amount_tooltip'] = 'Total value of PO issued invoice that the customer has been using job post';
$lang['dashboard_invoice_unpaid_tooltip'] = 'Total value of PO issued invoice the customer has not paid';
$lang['estimate_convert_expires_estimate_error'] = 'Cannot convert last month\'s estimate to a PO. Please create a new estimate.';
$lang['estimate_convert_old_program_estimate_error'] = 'Cannot convert the estimate applied to the old churn/new program. Please create a new estimate.';
$lang['permission_convert_estimate_to_po'] = 'Convert to PO';
$lang['note_day_counts'] = '%s days';
$lang['day_of_note'] = 'Day of Note';
$lang['po_approval_title'] = 'Waiting approval request';
$lang['po_my_request_title'] = 'My Request';
$lang['po_pending_approval_message'] = 'Your PO is pending approval';
$lang['po_approval_as_leader'] = 'Approve POs as Leader';
$lang['po_approval_as_manager'] = 'Approve POs as Manager';
$lang['approve_addition_discount_confirmation'] = 'Are you sure you want to approve the PO {inv_number}?';
$lang['reject_addition_discount_confirmation'] = 'Are you sure you want to reject the PO {inv_number}?';
$lang['po_pending_addition_discount_approval_message'] = 'Your PO is pending approval';
$lang['reject_addition_discount'] = 'Addition Discount Rejected by %s, at %s';
$lang['approve_addition_discount'] = 'Addition Discount Approved by %s, at %s';
$lang['approve_addition_discount_notification'] = 'Addition Discount Request: %s';

<?php

namespace Validators;

defined('BASEPATH') or exit('No direct script access allowed');

class CreateClientValidator
{
    public static function rules()
    {
        return [
            [
                'field' => 'email',
                'rules' => ['required']
            ],
            [
                'field' => 'business_name',
                'rules' => ['required']
            ],
            [
                'field' => 'business_address',
                'rules' => ['required']
            ],
            [
                'field' => 'tax_number',
                'rules' => [
                    'required',
                    [
                        'request_client_valid_vat',
                        function ($value) {
                            return !!preg_match('/^(\d{10}|\d{10}-\d{3})$/', $value);
                        }
                    ]
                ]
            ],
            [
                'field' => 'contact_phone',
                'rules' => ['required']
            ],
        ];
    }

    public static function rulesForDev2062()
    {
        return [
            [
                'field' => 'email',
                'rules' => ['required']
            ],
            [
                'field' => 'business_name',
                'rules' => ['required']
            ],
            [
                'field' => 'tax_number',
                'rules' => [
                    'required',
                    [
                        'request_client_valid_vat',
                        function ($value) {
                            return !!preg_match('/^(\d{10}|\d{10}-\d{3})$/', $value);
                        }
                    ]
                ]
            ],
            [
                'field' => 'contact_phone',
                'rules' => ['required']
            ],
            [
                'field' => 'contact_name',
                'rules' => ['required']
            ],
        ];
    }
}

<?php

namespace Validators;
use Entities\AmsPackage;
use Entities\Client;

defined('BASEPATH') or exit('No direct script access allowed');

class CreateInvoiceValidator
{
    public static function rules()
    {
        return [
            [
                'field' => 'ams_order_id',
                'rules' => ['required']
            ],
            [
                'field' => 'ams_order_code',
                'rules' => ['required']
            ],
            [
                'field' => 'ams_company_id',
                'rules' => ['required']
            ],
            [
                'field' => 'crm_company_id',
                'rules' => [
                    'required',
                    [
                        'clientExists',
                        function ($crmCompanyId) {
                            return Client::where('userid', $crmCompanyId)->exists();
                        }
                    ],
                ],
                'errors' => [
                    'clientExists' => 'Client does not exists'
                ]
            ],
            [
                'field' => 'paid_at',
                'rules' => [
                    'required',
                    'regex_match[/^(\d{4})\-(\d{2})\-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/i]'
                ]
            ],
            // Employer information
            [
                'field' => 'email',
                'rules' => ['required', 'valid_email']
            ],
            [
                'field' => 'fullname',
                'rules' => ['required']
            ],
            // Products
            [
                'field' => 'products[]',
                'rules' => ['required'],
            ]
        ];
    }
}

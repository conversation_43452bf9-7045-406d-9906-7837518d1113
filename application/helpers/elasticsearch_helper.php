<?php

use Elasticsearch\ClientBuilder;
use ONGR\ElasticsearchDSL\Search;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Elasticsearch\Common\Exceptions\Missing404Exception;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @param string|null $connection
 * @return \Elasticsearch\Client
 */
function es_client($connection = 'default')
{

    static $_client;

    if (empty($_client[$connection]))
    {
        $clientBuilder = ClientBuilder::create()
            ->setHosts(explode(',', ELASTICSEARCH_HOST));

        if (!empty(ELASTICSEARCH_API_KEY)) {
            try {
                list($id, $apiKey) = explode(':', base64_decode(ELASTICSEARCH_API_KEY));
                $clientBuilder->setApiKey($id, $apiKey); /* 7.x */
            } catch (\ArgumentCountError $e) {
                $clientBuilder->setApiKey(ELASTICSEARCH_API_KEY); /* 8.x */
            }
        }

        // log_message('debug', "Elasticsearch client constructed". ELASTICSEARCH_API_KEY);

        $_client[$connection] = $clientBuilder->build();
    }

    return $_client[$connection];
}

/**
 * Perform search in the elasticsearch server
 * @param string $index name of the index will be searched
 * @param ONGR\ElasticsearchDSL\BuilderInterface $queries query object
 * @param integer $size size of the result default is 10000
 * @param integer $from start from index default is 0
 *
 * @return array result based on queries, example
 *
 * Array
 * (
 *     [took] => 1
 *     [timed_out] =>
 *     [_shards] => Array
 *         (
 *             [total] => 1
 *             [successful] => 1
 *             [skipped] => 0
 *             [failed] => 0
 *         )
 *     [hits] => Array
 *         (
 *             [total] => Array
 *                 (
 *                     [value] => 1
 *                     [relation] => eq
 *                 )
 *             [max_score] => 0.9808291
 *             [hits] => Array
 *                 (
 *                      List of data
 *                 (
 *         )
 * )
 * @throws Exception
 */
function es_search($index, $queries, $source = false, $size = 1000, $from = 0)
{
    $client = es_client();

    try {
        $search = new Search();
        $search->addQuery($queries);
        $search->setSource($source);
        $search->setSize($size);
        $search->setFrom($from);
        $search->addSort(new FieldSort('_score', FieldSort::DESC));
        $result = $client->search([
            'index' => $index,
            'body' => $search->toArray()
        ]);
        return isset($result['hits']['hits']) ? $source ? array_pluck($result['hits']['hits'], '_source') : array_pluck($result['hits']['hits'], '_id') : [];
    } catch (Exception $ex) {
        log_message('error', $ex->getMessage());
        throw $ex;
    }
}

/**
 * Get valid taggables which is group by rel_id
 * @param array $taggables list of taggables
 * @param string $rel_type real type will get got
 * @return array valid taggables
 */
function es_get_valid_taggables($taggables, $rel_type)
{
    if (!count($taggables)) {
        return [];
    }

    $validTags = [];

    /**
     * GROUP BY rel_id
     * HAVING count(tag_id) = 1
     */
    foreach ($taggables as $taggable) {
        if ($taggable['rel_type'] === $rel_type) {
            if (isset($validTags[$taggable['rel_id']])) {
                $validTags[$taggable['rel_id']] += 1;
            } else {
                $validTags[$taggable['rel_id']] = 1;
            }
        }
    }

    // Only get keys if count is equal 1
    return array_keys(array_filter($validTags, function ($number) {
        return $number === 1;
    }));
}

/**
 * Use to insert ES
 * @param string $index index will be inserted
 * @param integer $id id of the document
 * @param array $body document values
 * @return boolean
 */
function es_index($index, $id, $body)
{
    $client = es_client();

    try {
        $params = [
            'index' => $index,
            'id' => $id,
            'body' => $body
        ];
        $response = $client->index($params);
        return ($response['result'] ?? '') === 'created';
    } catch (Exception $ex) {
        // eg. network error like NoNodeAvailableException
        log_message('error', $ex->getMessage());
        throw $ex;
    }
}


/**
 * Use to bulk insert ES
 * @param string $index index will be inserted
 * @param array $body document values, it look like
 * [
 *        [
 *            'create' => [
 *                '_index' => 'test_idx',
 *                '_id' => 2,
 *            ]
 *        ],
 *        [
 *            'id' => 2,
 *            'name' => 'bbb'
 *        ],
 *        [
 *            'create' => [
 *                '_index' => 'test_idx',
 *                '_id' => 3,
 *            ]
 *        ],
 *        [
 *            'id' => 3,
 *            'name' => 'ccc'
 *        ]
 * ]
 * @return boolean
 */
function es_bulk_insert($index, $body)
{
    $client = es_client();

    try {
        $params = [
            'index' => $index,
            'body' => $body
        ];
        $response = $client->bulk($params);
        return ($response['result'] ?? '') === 'created';
    } catch (Exception $ex) {
        // eg. network error like NoNodeAvailableException
        log_message('error', $ex->getMessage());
        throw $ex;
    }
}

/**
 * Perform bulk index list of record.
 * It will call bulk per 1k time to speed up index time
 * @param string $index index will be indexed
 * @param array $records list of structured records that will be indexed
 * @param string $docIdField field to get value then set to document id
 */
function es_bulk_index($index, $records, $docIdField)
{
    $indexRecords = [];
    $indexCount = 0;
    $block = 0;
    foreach ($records as $record) {
        $dockIdValue = $record[$docIdField] ?? null;

        // It must have document id value to index correct record
        if (!$dockIdValue) {
            throw new Exception('Document id value is empty or not found. Doc field: ' . $docIdField);
        }

        // Put index id
        $indexRecords[] = [
            'index' => [
                '_index' => $index,
                '_id' => intval($dockIdValue)
            ]
        ];
        // Put index body
        $indexRecords[] = $record;

        $indexCount++;
        $bulkIndexes = &get_instance()->load->config('elasticsearch')['bulk_index_records'] ?? 200;
        // If records are enough to index, perform bulk index then reset array
        if ($indexCount === $bulkIndexes) {
            es_bulk_insert($index, $indexRecords);
            // Reset to start from zero for remain records
            $indexRecords = [];
            $indexCount = 0;
            $block++;
            es_logging('Bulk index block #'.$block.PHP_EOL);
        }
    }

    // For records less than 200 records, index remains
    if (count($indexRecords)) {
        es_bulk_insert($index, $indexRecords);
        // Reset to start from zero for remain records
        $indexRecords = [];
        $indexCount = 0;
        $block++;
        es_logging('Bulk index block #'.$block.PHP_EOL);
    }
}


/**
 * Perform bulk search in the elasticsearch server
 * @param ONGR\ElasticsearchDSL\BuilderInterface $queries query object
 * @param integer $size size of the result default is 10000
 * @param integer $from start from index default is 0
 *
 * @return array result based on bulk queries
 * @throws Exception
 */
function es_bulk_search($queries, $size = 10000, $from = 0)
{
    $client = es_client();

    try {
        $searches = [];
        foreach ($queries as $query) {
            $searches[] = [
                'index' => ES_INDEXES[$query['index']]
            ];
            $search = new Search();
            $search->addQuery($query['queries']);
            $search->setSize($size);
            $search->setFrom($from);
            $search->addSort(new FieldSort('_score', FieldSort::DESC));
            $searches[] = $search->toArray();
        }
        $result = $client->msearch([
            'body' => $searches
        ]);
        $responses = $result['responses'] ?? [];
        $searchIds = [];
        for ($i = 0; $i < count($responses); $i++) {
            $response = $responses[$i];
            $searchIds[] = [
                'table' => $queries[$i]['table'],
                'ids' => !empty($response['hits']['hits']) ? array_pluck($response['hits']['hits'], '_id') : []
            ];
        }
        return $searchIds;
    } catch (Exception $ex) {
        log_message('error', $ex->getMessage());
        throw $ex;
    }
}

/**
 * Perform delete index by document id (_id field)
 * @param string $index name of the index
 * @param integer $id ES document id
 * @throws Exception
 */
function es_delete_index($index, $id)
{
    $client = es_client();

    try {
        $client->delete([
            'index' => $index,
            'id' => $id
        ]);
    } catch (Missing404Exception $ex) {
        log_message('info', $ex->getMessage());
    }
    catch (Exception $ex) {
        log_message('error', $ex->getMessage());
        throw $ex;
    }
}

/**
 * Use to logging when perform index. It can disable by config
 */
function es_logging($message)
{
    if (defined('ELASTICSEARCH_LOG_ENABLE') && true === ELASTICSEARCH_LOG_ENABLE) {
        print($message);
    }
}


/**
 * Check integer type of number
 * A signed 32-bit integer with a minimum value of -2^31 and a maximum value of 2^31-1.
 * https://www.elastic.co/guide/en/elasticsearch/reference/current/number.html#number
 */
function es_is_integer($value)
{
    return pow(-2, 31) <= $value && $value <= pow(2, 31) - 1;
}

<?php

use app\services\AmsService;
use Entities\Ams\Job;
use Entities\Client;
use Entities\ClientAmsCompany;
use Entities\ClientAmsJob;

defined('BASEPATH') or exit('No direct script access allowed');

function ams_get_statuses()
{
    return [
        [
            'id' => 1,
            'text' => _l('ams_company_status_active'),
        ],
        [
            'id' => 2,
            'text' => _l('ams_company_status_inactive'),
        ],
        [
            'id' => 3,
            'text' => _l('ams_company_status_review'),
        ],
    ];
}

function map_ams_job_status($crmStatus)
{
    $amsStatuses = [
        -1,
        Job::STATUS_OPEN,
        Job::STATUS_DRAFT,
        JOB::STATUS_REVIEW,
        JOB::STATUS_CLOSED
    ];

    return $amsStatuses[$crmStatus] ?? null;
}

function ams_get_job_statuses()
{
    return [
        [
            'id' => 1,
            'text' => _l('ams_job_status_open'),
        ],
        [
            'id' => 2,
            'text' => _l('ams_company_status_draft'),
        ],
        [
            'id' => 3,
            'text' => _l('ams_company_status_review'),
        ],
        [
            'id' => 4,
            'text' => _l('ams_company_status_closed'),
        ],
    ];
}

function get_crm_companies($userId)
{
    return Client::select('userid', DB::raw('CONCAT(business_name, " (",userid, ")") as company'))
        ->whereHas('clientAmsCompanies', function ($builder) use ($userId) {
            $builder->whereIn('ams_company_id', function ($subQuery) use ($userId) {
                $subQuery->select('ams_company_id')
                    ->from('client_ams_companies')
                    ->where('client_id', $userId);
            })->where('client_id', '<>', $userId);
        })
        ->get()
        ->toArray();
}

function getAmsCompaniesById($clientId)
{
    $amsClient = ClientAmsCompany::select('ams_company_id')->whereClientId($clientId)->get()->pluck('ams_company_id')->filter(fn($id) => $id > 0);
    if (!$amsClient->count()) {
        return [];
    }

    $options = [
        'query' => array_filter([
            'ids' => $amsClient->join(','),
            'page_size' => 100
        ])
    ];
    $response = AmsService::search('companies/search', $options);
    $data = $response['data']['data'] ?? [];
    return count($data) ? array_map(fn ($dt) => ['id' => $dt['id'], 'name' => $dt['display_name'] . ' ('.$dt['id'].')'], $data) : [];
}


function getAmsJobInfo(int $amsJobId)
{
    $response = AmsService::amsApi('crm/jobs/' . $amsJobId, [], 'GET');
    $job = $response['data'];
    $experiencesIds = $job['experiences_ids'] ?? [];
    $salary = $job['salary'] ?? [];
    return array_merge($job, [
        'company_id' => $job['ams_company_id'],
        'crm_invoice_id' => $job['crm_invoice_id'],
        'yoe_from' => $experiencesIds[0] ?? null,
        'yoe_to' => $experiencesIds[1] ?? null,
        
        // Salary
        'currency' => $salary['currency'] ?? '',
        'range_to' => $salary['max'] ?? 0,
        'negotiable_to' => $salary['max_estimate'] ?? 0,
        'range_from' => $salary['min'] ?? 0,
        'negotiable_from' => $salary['min_estimate'] ?? 0,
        'salary' => $salary['is_negotiable'] == 0 ? 'range' : 'negotiable',
    ]);
}

if (!function_exists('get_job_categories')) {
    function get_job_categories()
    {
        $response = AmsService::amsApi('crm/jobs/categories', [], 'GET');
        return $response['data'];
    }
}

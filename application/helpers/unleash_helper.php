<?php

defined('BASEPATH') or exit('No direct script access allowed');

use Unleash\Client\UnleashBuilder;

if (!function_exists('is_feature_enabled')) {
    /**
     * Checks if a feature is enabled via Unleash feature flags.
     *
     * @param string $feature The feature name to check.
     *
     * @return bool True if the feature is enabled, false otherwise.
     */
    function is_feature_enabled($feature)
    {
        /** @var CI $CI */
        $CI =& get_instance();
        $CI->load->config('unleash');

        $unleash = UnleashBuilder::create()
            ->withGitlabEnvironment(config_item('unleash_gitlab_environment'))
            ->withAppUrl(config_item('unleash_app_url'))
            ->withInstanceId(config_item('unleash_instance_id'))
            ->build();

        return $unleash->isEnabled($feature);
    }
}

if (!function_exists('is_feature_free_post_enabled')) {
    /**
     * Checks if the free post feature is enabled.
     *
     * @return bool True if the feature is enabled, false otherwise.
     */
    function is_feature_free_post_enabled()
    {
        return is_feature_enabled('dev-2062-free-post');
    }
}

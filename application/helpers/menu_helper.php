<?php

defined('BASEPATH') or exit('No direct script access allowed');

function app_init_admin_sidebar_menu_items()
{
    $CI = &get_instance();

    $CI->app_menu->add_sidebar_menu_item('dashboard', [
        'collapse' => true,
        'name'     => _l('als_dashboard'),
        'href'     => admin_url(),
        'position' => 1,
        'icon'     => 'fa fa-home',
        'badge'    => [],
    ]);

    $CI->app_menu->add_sidebar_children_item('dashboard', [
        'slug'     => 'dashboard-0',
        'name'     => _l('als_dashboard'),
        'href'     => admin_url('dashboard'),
        'position' => 5,
        'badge'    => [],
    ]);

    $CI->app_menu->add_sidebar_children_item('dashboard', [
        'slug'     => 'dashboard-revenue',
        'name'     => _l('als_dashboard_revenue_recognition_summary'),
        'href'     => admin_url('dashboard/revenue_recognition_summary'),
        'position' => 6,
        'badge'    => [],
    ]);

    $CI->app_menu->add_sidebar_children_item('dashboard', [
        'slug'     => 'dashboard-service-usage',
        'name'     => _l('als_dashboard_revenue_recognition'),
        'href'     => admin_url('dashboard/revenue_recognition'),
        'position' => 10,
        'badge'    => [],
    ]);

    $CI->app_menu->add_sidebar_menu_item('activity', [
        'name'     => _l('als_daily_activities'),
        'href'     => admin_url('activity_notes'),
        'position' => 2,
        'icon'     => 'fa fa-file-excel-o',
        'badge'    => [],
    ]);

    $CI->app_menu->add_sidebar_menu_item('crawler', [
        'name'     => _l('als_job_crawler'),
        'href'     => admin_url('crawler'),
        'position' => 2,
        'icon'     => 'fa fa-cubes',
        'badge'    => [],
    ]);

    if (has_permission('customers', '', 'view') || has_permission('customers', '', 'view_team') || has_permission('customers', '', 'view_own') || has_permission('customers', '', 'create')) {
        $CI->app_menu->add_sidebar_menu_item('customers', [
            'collapse' => true,
            'name'     => _l('als_clients'),
            'position' => 5,
            'icon'     => 'fa fa-user-o',
            'badge'    => [],
        ]);

        $CI->app_menu->add_sidebar_children_item('customers', [
            'slug'     => 'customers-list',
            'name'     => _l('client_request_customer_list'),
            'href'     => admin_url('clients'),
            'position' => 5,
            'badge'    => [],
        ]);

        // Only sale roles can see this
        if (has_permission('customers', '', 'view_client_creating_request_global') ||
            (has_permission('customers', '', 'view_client_creating_request_own') && is_sales_leader())) {
            $CI->app_menu->add_sidebar_children_item('customers', [
                'slug'     => 'request_reviewing',
                'name'     => _l('client_request_approve_customer'),
                'href'     => admin_url('clients/request_reviewing'),
                'position' => 10,
                'badge'    => [],
            ]);
        }

        if (has_permission('customers', '', 'view_client_creating_request_own') && is_sales_member()) {
            $CI->app_menu->add_sidebar_children_item('customers', [
                'slug'     => 'request_list',
                'name'     => _l('client_request_list'),
                'href'     => admin_url('clients/request_list'),
                'position' => 10,
                'badge'    => [],
            ]);
        }

        $CI->app_menu->add_sidebar_children_item('customers', [
            'slug'     => 'request_create_contacts',
            'name'     => _l('request_create_contacts'),
            'href'     => admin_url('clients/request_create_contacts'),
            'position' => 15,
            'badge'    => [],
        ]);
    }
    if (has_permission('call_center_logs', '', 'view')) {
        $CI->app_menu->add_sidebar_menu_item('call_center', [
            'name'     => _l('call_center_logs'),
            'href'     => admin_url('call_center_logs'),
            'position' => 10,
            'icon'     => 'fa fa-phone',
            'badge'    => [],
        ]);
    }

    $CI->app_menu->add_sidebar_menu_item('sales', [
        'collapse' => true,
        'name'     => _l('als_sales'),
        'position' => 10,
        'icon'     => 'fa fa-balance-scale',
        'badge'    => [],
    ]);

    // (staff_has_assigned_proposals() && get_option('allow_staff_view_proposals_assigned') == 1)
    if ((has_permission('proposals', '', 'view') || has_permission('proposals', '', 'view_own'))) {
        $CI->app_menu->add_sidebar_children_item('sales', [
            'slug'     => 'proposals',
            'name'     => _l('proposals'),
            'href'     => admin_url('proposals'),
            'position' => 5,
            'badge'    => [],
        ]);
    }

    // (staff_has_assigned_estimates() && get_option('allow_staff_view_estimates_assigned') == 1)
    if ((has_permission('estimates', '', 'view') || has_permission('estimates', '', 'view_own') || has_permission('estimates', '', 'view_team'))) {
        $CI->app_menu->add_sidebar_children_item('sales', [
            'slug'     => 'estimates',
            'name'     => _l('estimates'),
            'href'     => admin_url('estimates'),
            'position' => 10,
            'badge'    => [],
        ]);
    }

    // (staff_has_assigned_invoices() && get_option('allow_staff_view_invoices_assigned') == 1)
    if ((has_permission('invoices', '', 'view') || has_permission('invoices', '', 'view_own') || has_permission('invoices', '', 'view_team'))) {
        $CI->app_menu->add_sidebar_children_item('sales', [
            'slug'     => 'invoices',
            'name'     => _l('invoices'),
            'href'     => admin_url('invoices'),
            'position' => 15,
            'badge'    => [],
        ]);
    }

    // (get_option('allow_staff_view_invoices_assigned') == 1 && staff_has_assigned_invoices())
    if (has_permission('payments', '', 'view') || has_permission('payments', '', 'view_own') || has_permission('payments', '', 'view_team')) {
        $CI->app_menu->add_sidebar_children_item('sales', [
            'slug'     => 'payments',
            'name'     => _l('payments'),
            'href'     => admin_url('payments'),
            'position' => 20,
            'badge'    => [],
        ]);
    }

    if (has_permission('credit_notes', '', 'view') || has_permission('credit_notes', '', 'view_own') || has_permission('credit_notes', '', 'view_team')) {
        $CI->app_menu->add_sidebar_children_item('sales', [
            'slug'     => 'credit_notes',
            'name'     => _l('credit_notes'),
            'href'     => admin_url('credit_notes'),
            'position' => 25,
            'badge'    => [],
        ]);
    }

    if (has_permission('items', '', 'view')) {
        $CI->app_menu->add_sidebar_children_item('sales', [
            'slug'     => 'items',
            'name'     => _l('items'),
            'href'     => admin_url('invoice_items'),
            'position' => 30,
            'badge'    => [],
        ]);
    }

    if (has_permission('subscriptions', '', 'view') || has_permission('subscriptions', '', 'view_own')) {
        $CI->app_menu->add_sidebar_menu_item('subscriptions', [
            'name'     => _l('subscriptions'),
            'href'     => admin_url('subscriptions'),
            'icon'     => 'fa fa-repeat',
            'position' => 15,
            'badge'    => [],
        ]);
    }

    if (has_permission('invoices', '', 'view') ||
        has_permission('invoices', '', 'view_own') ||
        has_permission('invoices', '', 'po_approval_as_leader') ||
        has_permission('invoices', '', 'po_approval_as_manager')) {
        $CI->app_menu->add_sidebar_menu_item('po', [
            'name'     => _l('invoices'),
            'href'     => admin_url('po'),
            'icon'     => 'fa fa-shopping-basket',
            'position' => 17,
            'badge'    => [],
        ]);

        $CI->app_menu->add_sidebar_children_item('po', [
            'slug'     => 'approval',
            'name'     => (has_permission('invoices', '', 'po_approval_as_leader') ||
                            has_permission('invoices', '', 'po_approval_as_manager'))
                                ? _l('po_approval_title')
                                : _l('po_my_request_title'),
            'href'     => admin_url('invoices/approval'),
            'position' => 15,
            'badge'    => [],
        ]);

        $CI->app_menu->add_sidebar_children_item('po', [
            'slug'     => 'po_balance',
            'name'     => _l('PO Balance'),
            'href'     => admin_url('invoices/po_balance'),
            'position' => 16,
            'badge'    => [],
        ]);
    }

    if (has_permission('expenses', '', 'view') || has_permission('expenses', '', 'view_own') || has_permission('expenses', '', 'view_team')) {
        $CI->app_menu->add_sidebar_menu_item('expenses', [
            'name'     => _l('expenses'),
            'href'     => admin_url('expenses'),
            'icon'     => 'fa fa-file-text-o',
            'position' => 20,
            'badge'    => [],
        ]);
    }

    if (has_permission('account', '', 'view') || has_permission('account', '', 'view_own')) {
        $CI->app_menu->add_sidebar_menu_item('account', [
            'name'     => _l('account'),
            'href'     => admin_url('account'),
            'position' => 30,
            'icon'     => 'fa fa-money',
            'badge'    => [],
        ]);

        $CI->app_menu->add_sidebar_children_item('account', [
            'slug'     => 'invoice_request',
            'name'     => _l('invoice_request'),
            'href'     => admin_url('invoices/invoice_request'),
            'position' => 1,
            'badge'    => [],
    ]);
    }

    if (has_permission('contracts', '', 'view') || has_permission('contracts', '', 'view_own') || has_permission('contracts', '', 'view_team')) {
        $CI->app_menu->add_sidebar_menu_item('contracts', [
            'name'     => _l('contracts'),
            'href'     => admin_url('contracts'),
            'icon'     => 'fa fa-file',
            'position' => 25,
            'badge'    => [],
        ]);
    }

    $CI->app_menu->add_sidebar_menu_item('projects', [
        'name'     => _l('projects'),
        'href'     => admin_url('projects'),
        'icon'     => 'fa fa-bars',
        'position' => 30,
        'badge'    => [],
    ]);

    $CI->app_menu->add_sidebar_menu_item('tasks', [
        'name'     => _l('als_tasks'),
        'href'     => admin_url('tasks'),
        'icon'     => 'fa fa-tasks',
        'position' => 35,
        'badge'    => [],
    ]);

    if ((!is_staff_member() && get_option('access_tickets_to_none_staff_members') == 1) || is_staff_member()) {
        $enable_badge = get_option('enable_support_menu_badges');
        $CI->app_menu->add_sidebar_menu_item('support', [
            'collapse' => $enable_badge,
            'name'     => _l('support'),
            'href'     => admin_url('tickets'),
            'icon'     => 'fa fa-ticket',
            'position' => 40,
            'badge'    => [],
        ]);

        $CI->load->model('tickets_model');
        $statuses = $CI->tickets_model->get_ticket_status();

        if (has_permission('supports', '', 'view') || has_permission('supports', '', 'create') || has_permission('supports', '', 'edit') || has_permission('supports', '', 'delete')) {
            $CI->app_menu->add_sidebar_children_item('support', [
                   'slug'     => 'support-services',
                   'name'     => _l('services'),
                   'href'     => admin_url('tickets/services'),
                   'position' => 112,
               ]);
       }

        if ($enable_badge) {
            foreach ($statuses as $status) {
                $CI->app_menu->add_sidebar_children_item('support', [
                    'slug'     => 'support-' . $status['ticketstatusid'],
                    'name'     => ticket_status_translate($status['ticketstatusid']),
                    'href'     => admin_url('tickets/index/' . $status['ticketstatusid']),
                    'position' => $status['statusorder'],
                    'badge'    => [
                        'value' => $CI->tickets_model->ticket_count($status['ticketstatusid']),
                        'color' => $status['statuscolor'],
                    ],
                ]);
            }
        }
    }

    if (is_staff_member()) {
        $CI->app_menu->add_sidebar_menu_item('leads', [
            'name'     => _l('als_leads'),
            'href'     => admin_url('leads'),
            'icon'     => 'fa fa-tty',
            'position' => 45,
            'badge'    => [],
        ]);
    }

    if ((has_permission('estimate_request', '', 'view') || has_permission('estimate_request', '', 'view_own'))) {
        $CI->app_menu->add_sidebar_menu_item('estimate_request', [
            'name'     => _l('estimate_request'),
            'href'     => admin_url('estimate_request'),
            'position' => 46,
            'icon'     => 'fa fa-list-alt',
            'badge'    => [],
        ]);
    }

    if (has_permission('knowledge_base', '', 'view')) {
        $CI->app_menu->add_sidebar_menu_item('knowledge-base', [
            'name'     => _l('als_kb'),
            'href'     => admin_url('knowledge_base'),
            'icon'     => 'fa fa-folder-open-o',
            'position' => 50,
            'badge'    => [],
        ]);
    }

    // Utilities
    $CI->app_menu->add_sidebar_menu_item('utilities', [
        'collapse' => true,
        'name'     => _l('als_utilities'),
        'position' => 55,
        'icon'     => 'fa fa-cogs',
        'badge'    => [],
    ]);

    $CI->app_menu->add_sidebar_children_item('utilities', [
        'slug'     => 'media',
        'name'     => _l('als_media'),
        'href'     => admin_url('utilities/media'),
        'position' => 5,
        'badge'    => [],
    ]);

    if (has_permission('bulk_pdf_exporter', '', 'view')) {
        $CI->app_menu->add_sidebar_children_item('utilities', [
            'slug'     => 'bulk-pdf-exporter',
            'name'     => _l('bulk_pdf_exporter'),
            'href'     => admin_url('utilities/bulk_pdf_exporter'),
            'position' => 10,
            'badge'    => [],
        ]);
    }

    $CI->app_menu->add_sidebar_children_item('utilities', [
        'slug'     => 'calendar',
        'name'     => _l('als_calendar_submenu'),
        'href'     => admin_url('utilities/calendar'),
        'position' => 15,
        'badge'    => [],
    ]);


    if (is_admin()) {
        $CI->app_menu->add_sidebar_children_item('utilities', [
            'slug'     => 'announcements',
            'name'     => _l('als_announcements_submenu'),
            'href'     => admin_url('announcements'),
            'position' => 20,
            'badge'    => [],
        ]);

        $CI->app_menu->add_sidebar_children_item('utilities', [
            'slug'     => 'activity-log',
            'name'     => _l('als_activity_log_submenu'),
            'href'     => admin_url('utilities/activity_log'),
            'position' => 25,
            'badge'    => [],
        ]);

        $CI->app_menu->add_sidebar_children_item('utilities', [
            'slug'     => 'ticket-pipe-log',
            'name'     => _l('ticket_pipe_log'),
            'href'     => admin_url('utilities/pipe_log'),
            'position' => 30,
            'badge'    => [],
        ]);
    }

    if (staff_can('view-timesheets', 'reports') || staff_can('view', 'reports')) {
        $CI->app_menu->add_sidebar_menu_item('reports', [
            'collapse' => true,
            'name'     => _l('als_reports'),
            'href'     => admin_url('reports'),
            'icon'     => 'fa fa-area-chart',
            'position' => 60,
            'badge'    => [],
        ]);
    }

    if (staff_can('view-timesheets', 'reports')) {
        $CI->app_menu->add_sidebar_children_item('reports', [
            'slug'     => 'timesheets-reports',
            'name'     => _l('timesheets_overview'),
            'href'     => admin_url('staff/timesheets?view=all'),
            'position' => 25,
            'badge'    => [],
        ]);
    }

    if (has_permission('reports', '', 'view')) {
        $CI->app_menu->add_sidebar_children_item('reports', [
            'slug'     => 'sales-reports',
            'name'     => _l('als_reports_sales_submenu'),
            'href'     => admin_url('reports/sales'),
            'position' => 5,
            'badge'    => [],
        ]);
        $CI->app_menu->add_sidebar_children_item('reports', [
            'slug'     => 'expenses-reports',
            'name'     => _l('als_reports_expenses'),
            'href'     => admin_url('reports/expenses'),
            'position' => 10,
            'badge'    => [],
        ]);
        $CI->app_menu->add_sidebar_children_item('reports', [
            'slug'     => 'expenses-vs-income-reports',
            'name'     => _l('als_expenses_vs_income'),
            'href'     => admin_url('reports/expenses_vs_income'),
            'position' => 15,
            'badge'    => [],
        ]);
        $CI->app_menu->add_sidebar_children_item('reports', [
            'slug'     => 'leads-reports',
            'name'     => _l('als_reports_leads_submenu'),
            'href'     => admin_url('reports/leads'),
            'position' => 20,
            'badge'    => [],
        ]);
        $CI->app_menu->add_sidebar_children_item('reports', [
            'slug'     => 'knowledge-base-reports',
            'name'     => _l('als_kb_articles_submenu'),
            'href'     => admin_url('reports/knowledge_base_articles'),
            'position' => 30,
            'badge'    => [],
        ]);
    }

    if (has_permission('reports', '', 'view_monthly_report')) {
        $CI->app_menu->add_sidebar_children_item('reports', [
            'slug'     => 'reports-invoices',
            'name'     => _l('menu_report_monthly_report_title'),
            'href'     => admin_url('reports/invoices'),
            'position' => 40,
            'badge'    => [],
        ]);
    }

    if (has_permission('reports', '', 'view_report_revenue')) {
        $CI->app_menu->add_sidebar_children_item('reports', [
            'slug'     => 'reports-revenue',
            'name'     => _l('menu_report_revenue_report_title'),
            'href'     => admin_url('reports_revenue'),
            'position' => 50,
            'badge'    => [],
        ]);
    }

    // Setup menu
    if (has_permission('staff', '', 'view')) {
        $CI->app_menu->add_setup_menu_item('staff', [
            'collapse' => true,
            'name'     => _l('als_staff'),
            'position' => 5,
            'badge'    => [],
        ]);

        $CI->app_menu->add_setup_children_item('staff', [
            'slug'     => 'staff-lists',
            'name'     => _l('acs_staff_list'),
            'href'     => admin_url('staff'),
            'position' => 5,
            'badge'    => [],
        ]);

        // $CI->app_menu->add_setup_children_item('staff', [
        //     'slug'     => 'staff-teams',
        //     'name'     => _l('acs_staff_teams'),
        //     'href'     => admin_url('staff/teams'),
        //     'position' => 10,
        //     'badge'    => [],
        // ]);
    }

    if (is_admin()) {
        $CI->app_menu->add_setup_menu_item('customers', [
            'collapse' => true,
            'name'     => _l('clients'),
            'position' => 10,
            'badge'    => [],
        ]);

        $CI->app_menu->add_setup_children_item('customers', [
            'slug'     => 'customer-groups',
            'name'     => _l('customer_groups'),
            'href'     => admin_url('clients/groups'),
            'position' => 5,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_menu_item('support', [
            'collapse' => true,
            'name'     => _l('support'),
            'position' => 15,
            'badge'    => [],
        ]);

        $CI->app_menu->add_setup_children_item('support', [
            'slug'     => 'departments',
            'name'     => _l('acs_departments'),
            'href'     => admin_url('departments'),
            'position' => 5,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('support', [
            'slug'     => 'tickets-predefined-replies',
            'name'     => _l('acs_ticket_predefined_replies_submenu'),
            'href'     => admin_url('tickets/predefined_replies'),
            'position' => 10,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('support', [
            'slug'     => 'tickets-priorities',
            'name'     => _l('acs_ticket_priority_submenu'),
            'href'     => admin_url('tickets/priorities'),
            'position' => 15,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('support', [
            'slug'     => 'tickets-statuses',
            'name'     => _l('acs_ticket_statuses_submenu'),
            'href'     => admin_url('tickets/statuses'),
            'position' => 20,
            'badge'    => [],
        ]);

        $CI->app_menu->add_setup_children_item('support', [
            'slug'     => 'tickets-services',
            'name'     => _l('acs_ticket_services_submenu'),
            'href'     => admin_url('tickets/services'),
            'position' => 25,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('support', [
            'slug'     => 'tickets-spam-filters',
            'name'     => _l('spam_filters'),
            'href'     => admin_url('spam_filters/view/tickets'),
            'position' => 30,
            'badge'    => [],
        ]);

        $CI->app_menu->add_setup_menu_item('leads', [
            'collapse' => true,
            'name'     => _l('acs_leads'),
            'position' => 20,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('leads', [
            'slug'     => 'leads-sources',
            'name'     => _l('acs_leads_sources_submenu'),
            'href'     => admin_url('leads/sources'),
            'position' => 5,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('leads', [
            'slug'     => 'leads-statuses',
            'name'     => _l('acs_leads_statuses_submenu'),
            'href'     => admin_url('leads/statuses'),
            'position' => 10,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('leads', [
            'slug'     => 'leads-email-integration',
            'name'     => _l('leads_email_integration'),
            'href'     => admin_url('leads/email_integration'),
            'position' => 15,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('leads', [
            'slug'     => 'web-to-lead',
            'name'     => _l('web_to_lead'),
            'href'     => admin_url('leads/forms'),
            'position' => 20,
            'badge'    => [],
        ]);

        $CI->app_menu->add_setup_menu_item('finance', [
            'collapse' => true,
            'name'     => _l('acs_finance'),
            'position' => 25,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('finance', [
            'slug'     => 'taxes',
            'name'     => _l('acs_sales_taxes_submenu'),
            'href'     => admin_url('taxes'),
            'position' => 5,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('finance', [
            'slug'     => 'currencies',
            'name'     => _l('acs_sales_currencies_submenu'),
            'href'     => admin_url('currencies'),
            'position' => 10,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('finance', [
            'slug'     => 'payment-modes',
            'name'     => _l('acs_sales_payment_modes_submenu'),
            'href'     => admin_url('paymentmodes'),
            'position' => 15,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('finance', [
            'slug'     => 'expenses-categories',
            'name'     => _l('acs_expense_categories'),
            'href'     => admin_url('expenses/categories'),
            'position' => 20,
            'badge'    => [],
        ]);

        $CI->app_menu->add_setup_menu_item('contracts', [
            'collapse' => true,
            'name'     => _l('acs_contracts'),
            'position' => 30,
            'badge'    => [],
        ]);
        $CI->app_menu->add_setup_children_item('contracts', [
            'slug'     => 'contracts-types',
            'name'     => _l('acs_contract_types'),
            'href'     => admin_url('contracts/types'),
            'position' => 5,
            'badge'    => [],
        ]);

        $modules_name = _l('modules');

        if ($modulesNeedsUpgrade = $CI->app_modules->number_of_modules_that_require_database_upgrade()) {
            $modules_name .= '<span class="badge menu-badge bg-warning">' . $modulesNeedsUpgrade . '</span>';
        }

        $CI->app_menu->add_setup_menu_item('modules', [
            'href'     => admin_url('modules'),
            'name'     => $modules_name,
            'position' => 35,
            'badge'    => [],
        ]);

        $CI->app_menu->add_setup_menu_item('custom-fields', [
            'href'     => admin_url('custom_fields'),
            'name'     => _l('asc_custom_fields'),
            'position' => 45,
            'badge'    => [],
        ]);

        $CI->app_menu->add_setup_menu_item('gdpr', [
            'href'     => admin_url('gdpr'),
            'name'     => _l('gdpr_short'),
            'position' => 50,
            'badge'    => [],
        ]);

        $CI->app_menu->add_setup_menu_item('roles', [
            'href'     => admin_url('roles'),
            'name'     => _l('acs_roles'),
            'position' => 55,
            'badge'    => [],
        ]);

        /*             $CI->app_menu->add_setup_menu_item('api', [
                          'href'     => admin_url('api'),
                          'name'     => 'API',
                          'position' => 65,
                  ]);*/
    }

    if (has_permission('settings', '', 'view')) {
        $CI->app_menu->add_setup_menu_item('settings', [
            'href'     => admin_url('settings'),
            'name'     => _l('acs_settings'),
            'position' => 200,
            'badge'    => [],
        ]);
    }

    if (has_permission('email_templates', '', 'view')) {
        $CI->app_menu->add_setup_menu_item('email-templates', [
            'href'     => admin_url('emails'),
            'name'     => _l('acs_email_templates'),
            'position' => 40,
            'badge'    => [],
        ]);
    }

    if (has_permission('settings', '', 'view')) {
        $CI->app_menu->add_setup_menu_item('estimate_request', [
            'collapse' => true,
            'name'     => _l('acs_estimate_request'),
            'position' => 34,
            'badge'    => [],
        ]);
    }

    $CI->app_menu->add_setup_children_item('estimate_request', [
        'slug'     => 'estimate-request-forms',
        'name'     => _l('acs_estimate_request_forms'),
        'href'     => admin_url('estimate_request/forms'),
        'position' => 5,
        'badge'    => [],
    ]);

    $CI->app_menu->add_setup_children_item('estimate_request', [
        'slug'     => 'estimate-request-statuses',
        'name'     => _l('acs_estimate_request_statuses_submenu'),
        'href'     => admin_url('estimate_request/statuses'),
        'position' => 10,
        'badge'    => [],
    ]);
}

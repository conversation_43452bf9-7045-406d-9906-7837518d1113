<?php

use app\services\CustomerProfileBadges;

defined('BASEPATH') or exit('No direct script access allowed');

// Array Chọn thời gian dự kiến hoàn thành
function arr_expected_completion()
{
    return [
        '1' => [
            'value' => 1,
            'hour' => 4,
            'text' => _l('expected_completion_2'),
        ],
        '2' => [
            'value' => 2,
            'hour' => 8,
            'text' => _l('expected_completion_1'),
        ],
        '3' => [
            'value' => 3,
            'hour' => 16,
            'text' => _l('expected_completion_4'),
        ],
        '4' => [
            'value' => 4,
            'hour' => 16,
            'text' => _l('expected_completion_3'),
        ],        
        '5' => [
            'value' => 5,
            'hour' => 24,
            'text' => _l('expected_completion_5'),
        ],
        '6' => [
            'value' => 6,
            'hour' => 40,
            'text' => _l('expected_completion_6'),
        ],
    ];
}

function get_client_request_statuses()
{
    return [
        [
            'value' => CLIENT_REQUEST_STATUS_WAITING,
            'text' => _l('client_request_waiting_status'),
        ],
        [
            'value' => CLIENT_REQUEST_STATUS_SA_APPROVED,
            'text' => _l('client_request_sa_approved_status'),
        ],
        [
            'value' => CLIENT_REQUEST_STATUS_REJECTED,
            'text' => _l('client_request_rejected_status'),
        ],
        [
            'value' => CLIENT_REQUEST_STATUS_LEADER_APPROVED,
            'text' => _l('client_request_approved_status'),
        ]
    ];
}

function get_group_team_sale()
{
    return [
        [
            'value' => 0,
            'text' => _l('leads_all'),
        ],
        [
            'value' => 8,
            'text' => 'HCM Sales Team 1',
        ],
        [
            'value' => 9,
            'text' => 'HCM Sales Team 2'
        ],
        [
            'value' => 10,
            'text' => 'Thợ Săn Tiền - Hốt Sạch Tiền',
        ]
    ];
}

function array_time_kpi()
{
    return [
        [
            'value' => 0,
            'text' => _l('select_time'),
        ],
        [
            'value' => 0.5,
            'text' => '0.5',
        ],
        [
            'value' => 1,
            'text' => '1'
        ],
    ];
}

function get_group_team_id()
{
    return [0,8,9,10];
}
function get_group_team_id_new()
{
    return [8,9,10];
}



function client_requests_grid_headers()
{
    return [
        _l('client_request_col_id'),
        _l('client_request_col_company_name'),
        _l('client_request_col_created_at'),
        _l('client_request_col_reason'),
        _l('client_request_col_approve_notes'),
        _l('client_request_col_action'),
    ];
}

function client_approve_requests_grid_headers()
{
    return [
        _l('client_request_col_id'),
        _l('client_request_col_company_name'),
        _l('client_request_col_created_by'),
        _l('client_request_col_created_at'),
        _l('client_request_col_reason'),
        _l('client_request_col_approve_notes'),
        _l('client_request_col_action'),
        _l('client_request_col_last_action'),
        _l('client_request_col_admin_action_at'),
        _l('client_request_col_leader_action_at'),
    ];
}

function client_request_actions($requestId, $createdBy, $status)
{
    $statuses = [
        CLIENT_REQUEST_STATUS_WAITING => _l('client_request_waiting_status'),
        CLIENT_REQUEST_STATUS_SA_APPROVED => _l('client_request_sa_approved_status'),
        CLIENT_REQUEST_STATUS_SA_REJECTED => _l('client_request_rejected_status'),
        CLIENT_REQUEST_STATUS_LEADER_APPROVED => _l('client_request_approved_status'),
        CLIENT_REQUEST_STATUS_LEADER_REJECTED => _l('client_request_rejected_status'),
    ];

    // Just show Waiting if record is approved by sale admin
    if (is_sales_leader()) {
        $statuses[CLIENT_REQUEST_STATUS_SA_APPROVED] = _l('client_request_waiting_status');
    }

    $statusColors = [
        CLIENT_REQUEST_STATUS_WAITING => 'default',
        CLIENT_REQUEST_STATUS_SA_APPROVED => 'default',
        CLIENT_REQUEST_STATUS_SA_REJECTED => 'danger',
        CLIENT_REQUEST_STATUS_LEADER_APPROVED => 'success',
        CLIENT_REQUEST_STATUS_LEADER_REJECTED => 'danger',
    ];

    // If waiting or sa_approve, then show option button instead
    if (in_array($status, [CLIENT_REQUEST_STATUS_WAITING, CLIENT_REQUEST_STATUS_SA_APPROVED]) && is_sales_leader()) {
        return client_request_action_dropdown($requestId, $createdBy);
    }

    // If waiting or sa_approve, then show option button instead
    if (in_array($status, [CLIENT_REQUEST_STATUS_WAITING])) {
        return client_request_action_dropdown($requestId, $createdBy);
    }

    return $statuses[$status] ? '<button class="btn btn-'.$statusColors[$status].'">' . $statuses[$status] . '</button>' : '';
}

function client_request_action_dropdown($requestId, $createdBy)
{
    return '<div class="btn-group">
      <button type="button" class="btn btn-default dropdown-toggle request-action" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="fa fa-spinner fa-spin hide" style="margin-right: 2px;"></i>'._l('client_request_waiting_status').' <span class="caret"></span>
      </button>
      <ul class="dropdown-menu">
      <li><a onclick="changeRequestStatus('.$requestId.',\''.$createdBy.'\', \'reject\')" href="javascript:;">'._l('client_request_rejected_status').'</a></li>
      <li><a onclick="changeRequestStatus('.$requestId.',\''.$createdBy.'\', \'approve\')" href="javascript:;">'._l('client_request_approved_status').'</a></li>
      </ul>
    </div>';
}

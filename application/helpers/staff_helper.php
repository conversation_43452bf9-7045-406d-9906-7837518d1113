<?php

use Entities\Staff;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @since  2.3.3
 * Get available staff permissions, modules can use the filter too to hook permissions
 * @param  array  $data additional data passed from view role.php and member.php
 * @return array
 */
function get_available_staff_permissions($data = [])
{
    $viewGlobalName = _l('permission_view') . '(' . _l('permission_global') . ')';

    $allPermissionsArray = [
        'view_own' => _l('permission_view_own'),
        'view_team' => _l('permission_view_team'),
        'view'     => $viewGlobalName,
        'create'   => _l('permission_create'),
        'edit'     => _l('permission_edit'),
        'delete'   => _l('permission_delete'),
    ];

    $allPermissionsArray_note = [
        'view_own' => _l('help_view_own_note'),
        'view_team' => _l('help_view_team_note'),
        'view'     => _l('help_view_note'),
    ];

    $allPermissionsArraySubViewTeam = [
        'view_own' => _l('permission_view_own'),
        'view'     => $viewGlobalName,
        'create'   => _l('permission_create'),
        'edit'     => _l('permission_edit'),
        'delete'   => _l('permission_delete'),
    ];

    $allPermissionsCustomerSource = [
        'edit' => _l('permission_edit'),
        'edit_other'   => _l('permission_edit_other'),
    ];

    $allPermissionAccountArray = [
        'view_own' => _l('permission_view_own'),
        'view'     => $viewGlobalName,
        'change_status' => _l('request_status_update'),
    ];

    $withoutViewOwnPermissionsArray = [
        'view'   => $viewGlobalName,
        'create' => _l('permission_create'),
        'edit'   => _l('permission_edit'),
        'delete' => _l('permission_delete'),
    ];

    $withNotApplicableViewOwn = $allPermissionsArray;

    $withNotApplicableViewOwn_old = array_merge(['view_own' => ['not_applicable' => true, 'name' => _l('permission_view_own')]], $withoutViewOwnPermissionsArray);

    $corePermissions = [
        'bulk_pdf_exporter' => [
            'name'         => _l('bulk_pdf_exporter'),
            'capabilities' => [
                'view' => $viewGlobalName,
            ],
        ],
        'contracts' => [
            'entity' => Entities\Contract::class,
            'name'         => _l('contracts'),
            'capabilities' => $allPermissionsArray,
            'help' => $allPermissionsArray_note,
        ],
        'credit_notes' => [
            'entity' => Entities\CreditNote::class,
            'name'         => _l('credit_notes'),
            'capabilities' => $allPermissionsArray,
            'help' => $allPermissionsArray_note,
        ],
        'customers' => [
            'entity' => Entities\Client::class,
            'name'         => _l('clients'),
            'capabilities' => array_merge($allPermissionsArray, [
                'active' => _l('permission_customers_active'),
                'view_client_creating_request_global' => _l('permission_view_client_creating_request_global'),
                'view_client_creating_request_own' => _l('permission_view_client_creating_request_own'),
                'approve_client_creating_request_global' => _l('permission_approve_client_creating_request_global'),
                'approve_client_creating_request_own' => _l('permission_approve_client_creating_request_own'),
                'bulk_remove_customer_admin' => _l('permission_bulk_remove_customer_admin'),
                'edit_id_ams' => _l('permission_edit_id_ams'),
                'view_ams_company' => _l('permission_view_ams_company'),
                'edit_job_global' => _l('permission_edit_job_global'),
                'edit_job_owned' => _l('permission_edit_job_owned'),
                'edit_used_packages' => _l('permission_edit_used_packages'),
                'update_num_of_usage_behavior' => _l('permission_update_num_of_usage_behavior'),
                'update_usage_behavior' => _l('permission_update_usage_behavior'),
                'change_search_cv_invoice' => _l('permission_change_search_cv_invoice'),
            ]),
            'help'         => $allPermissionsArray_note,
        ],
        'contacts' => [
            'entity' => Entities\Contact::class,
            'name'         => _l('customer_contacts'),
            'capabilities' => array_merge($allPermissionsArray, ['review' => _l('permission_review_contact')]),
            'help' => $allPermissionsArray_note,
        ],
        'email_templates' => [
            'name'         => _l('email_templates'),
            'capabilities' => [
                'view' => $viewGlobalName,
                'edit' => _l('permission_edit'),
            ],
        ],
        'estimates' => [
            'entity' => Entities\Estimate::class,
            'name'         => _l('estimates'),
            'capabilities' => array_merge($allPermissionsArray, [
                'convert_to_po' => _l('permission_convert_estimate_to_po')
            ]),
            'help' => $allPermissionsArray_note,
        ],
        'expenses' => [
            'entity' => Entities\Expense::class,
            'name'         => _l('expenses'),
            'capabilities' => $allPermissionsArray,
            'help' => $allPermissionsArray_note,
        ],
        'invoices' => [
            'entity' => Entities\Invoice::class,
            'name'         => _l('invoices'),
            'capabilities' => array_merge($allPermissionsArray, [
                'invoice_closing_date' => _l('invoice_closing_date'),
                'edit_use_expired_at' => _l('invoice_permission_edit_use_expired_at'),
                'edit_addfrom_field' => 'Edit Invoice Creator',
                'po_approval_as_leader' => _l('po_approval_as_leader'),
                'po_approval_as_manager' => _l('po_approval_as_manager'),
            ]),
            'help' => $allPermissionsArray_note,
        ],
        'invoices_customer_source' => [
            'name'         => _l('customer_source'),
            'capabilities' => $allPermissionsCustomerSource,
            'help' => $allPermissionsArray_note,
        ],
        'account' => [
            'name'         => _l('export_invoice'),
            'capabilities' => $allPermissionAccountArray,
        ],
        'items' => [
            'name'         => _l('items'),
            'capabilities' => $withoutViewOwnPermissionsArray,
        ],
        'knowledge_base' => [
            'name'         => _l('knowledge_base'),
            'capabilities' => $withoutViewOwnPermissionsArray,
        ],
        'payments' => [
            'entity' => Entities\Payment::class,
            'name'         => _l('payments'),
            'capabilities' => $withNotApplicableViewOwn,
            'help' => $allPermissionsArray_note,
        ],
        'projects' => [
            'entity' => Entities\Project::class,
            'name'         => _l('projects'),
            'capabilities' => array_merge($withNotApplicableViewOwn_old, [ 'create_milestones' => _l('permission_create_timesheets'),
                'edit_milestones'=> _l('permission_edit_milestones'), 'delete_milestones'=> _l('permission_delete_milestones')]),
            'help'         => [
                    'view'     => _l('help_project_permissions'),
                    'view_own' => _l('permission_projects_based_on_assignee'),
                ],
        ],
        'proposals' => [
            'name'         => _l('proposals'),
            'capabilities' => $allPermissionsArraySubViewTeam,
        ],
        'reports' => [
            'name'         => _l('reports'),
            'capabilities' => [
                'view' => $viewGlobalName,
                'view-timesheets' => _l('permission_view_timesheet_report'),
                'view_monthly_report' => _l('permission_view_monthly_report'),
                'view_report_revenue' => _l('permission_view_job_posting_revenue_report'),
            ],
        ],
        'roles' => [
            'name'         => _l('roles'),
            'capabilities' => $withoutViewOwnPermissionsArray,
        ],
        'settings' => [
            'name'         => _l('settings'),
            'capabilities' => [
                'view' => $viewGlobalName,
                'edit' => _l('permission_edit'),
            ],
        ],
        'kpi' => [
            'name'         => _l('setting_kpi'),
            'capabilities' => [
                'edit' => _l('permission_edit'),
            ],
        ],
        'staff' => [
            'name'         => _l('staff'),
            'capabilities' => $withoutViewOwnPermissionsArray,
        ],
        'subscriptions' => [
            'name'         => _l('subscriptions'),
            'capabilities' => $allPermissionsArraySubViewTeam,
        ],
        'tasks' => [
            'name'         => _l('tasks'),
            'capabilities' => array_merge($withNotApplicableViewOwn_old, [
                'edit_timesheet' => _l('permission_edit_timesheets'),
                'edit_own_timesheet' => _l('permission_edit_own_timesheets'),
                'delete_timesheet' => _l('permission_delete_timesheets'),
                'delete_own_timesheet' => _l('permission_delete_own_timesheets'),
            ]),
            'help'        => [
                'view'     => _l('help_tasks_permissions'),
                'view_own' => _l('permission_tasks_based_on_assignee'),
            ],
        ],
        'checklist_templates' => [
            'name'         => _l('checklist_templates'),
            'capabilities' => [
                'create' => _l('permission_create'),
                'delete' => _l('permission_delete'),
            ],
        ],
        'estimate_request' => [
            'name'         => _l('estimate_request'),
            'capabilities' => $allPermissionsArraySubViewTeam,
        ],
        'call_center_logs' => [
            'name'         => _l('call_center_logs'),
            'capabilities' => [
                'view' => $viewGlobalName,
            ],
        ],
        'supports' => [
            'name'         => _l('services'),
            'capabilities' => $withoutViewOwnPermissionsArray,
        ],
    ];

    $addLeadsPermission = true;
    if (isset($data['staff_id']) && $data['staff_id']) {
        $is_staff_member = is_staff_member($data['staff_id']);
        if (!$is_staff_member) {
            $addLeadsPermission = false;
        }
    }

    if ($addLeadsPermission) {
        $corePermissions['leads'] = [
            'name'         => _l('leads'),
            'capabilities' => [
                'view'   => $viewGlobalName,
                'delete' => _l('permission_delete'),
            ],
            'help' => [
                'view' => _l('help_leads_permission_view'),
            ],
        ];
    }

    return hooks()->apply_filters('staff_permissions', $corePermissions, $data);
}
/**
 * Get staff by ID or current logged in staff
 * @param  mixed $id staff id
 * @return mixed
 */
function get_staff($id = null)
{
    if (empty($id) && isset($GLOBALS['current_user'])) {
        return $GLOBALS['current_user'];
    }

    // Staff not logged in
    if (empty($id)) {
        return null;
    }

    if (!class_exists('staff_model', false)) {
        get_instance()->load->model('staff_model');
    }

    return get_instance()->staff_model->get($id);
}

/**
 * Return staff profile image url
 * @param  mixed $staff_id
 * @param  string $type
 * @return string
 */
function staff_profile_image_url($staff_id, $type = 'small')
{
    $url = base_url('assets/images/user-placeholder.jpg');

    if ((string) $staff_id === (string) get_staff_user_id() && isset($GLOBALS['current_user'])) {
        $staff = $GLOBALS['current_user'];
    } else {
        $CI = & get_instance();
        $CI->db->select('profile_image')
        ->where('staffid', $staff_id);

        $staff = $CI->db->get(db_prefix() . 'staff')->row();
    }

    if ($staff) {
        if (!empty($staff->profile_image)) {
            $profileImagePath = 'uploads/staff_profile_images/' . $staff_id . '/' . $type . '_' . $staff->profile_image;
            if (file_exists($profileImagePath)) {
                $url = base_url($profileImagePath);
            }
        }
    }

    return $url;
}

/**
 * Staff profile image with href
 * @param  boolean $id        staff id
 * @param  array   $classes   image classes
 * @param  string  $type
 * @param  array   $img_attrs additional <img /> attributes
 * @return string
 */
function staff_profile_image($id, $classes = ['staff-profile-image'], $type = 'small', $img_attrs = [])
{
    $url = base_url('assets/images/user-placeholder.jpg');

    $id = trim($id);

    $_attributes = '';
    foreach ($img_attrs as $key => $val) {
        $_attributes .= $key . '=' . '"' . html_escape($val) . '" ';
    }

    $blankImageFormatted = '<img src="' . $url . '" ' . $_attributes . ' class="' . implode(' ', $classes) . '" />';

    if ((string) $id === (string) get_staff_user_id() && isset($GLOBALS['current_user'])) {
        $result = $GLOBALS['current_user'];
    } else {
        $CI     = & get_instance();
        $result = $CI->app_object_cache->get('staff-profile-image-data-' . $id);

        if (!$result) {
            $CI->db->select('profile_image,firstname,lastname');
            $CI->db->where('staffid', $id);
            $result = $CI->db->get(db_prefix() . 'staff')->row();
            $CI->app_object_cache->add('staff-profile-image-data-' . $id, $result);
        }
    }

    if (!$result) {
        return $blankImageFormatted;
    }

    if ($result && $result->profile_image !== null) {
        $profileImagePath = 'uploads/staff_profile_images/' . $id . '/' . $type . '_' . $result->profile_image;
        if (file_exists($profileImagePath)) {
            $profile_image = '<img ' . $_attributes . ' src="' . base_url($profileImagePath) . '" class="' . implode(' ', $classes) . '" />';
        } else {
            return $blankImageFormatted;
        }
    } else {
        $profile_image = '<img src="' . $url . '" ' . $_attributes . ' class="' . implode(' ', $classes) . '" />';
    }

    return $profile_image;
}

/**
 * Get staff full name
 * Customer admin by - staffid add customer admin
 * @param  string $userid Optional
 * @return string Firstname and Lastname
 */
function get_customer_admin_staff_id_by($staffid)
{
    $CI = & get_instance();
    $CI->db->select('staffid');
    $CI->db->from(db_prefix() . 'activity_log');
    $CI->db->where('description', 'Add Customer Admin (#'. $staffid .')');
    $staff_by = $CI->db->get()->row();

    if ($staff_by) {
        return $staff_by->staffid;
    }
    return '';
}

/**
 * Get staff full name
 * @param  string $userid Optional
 * @return string Firstname and Lastname
 */
function get_staff_full_name($userid = '')
{
    $tmpStaffUserId = get_staff_user_id();
    if ($userid == '' || $userid == $tmpStaffUserId) {
        if (isset($GLOBALS['current_user'])) {
            return $GLOBALS['current_user']->firstname . ' ' . $GLOBALS['current_user']->lastname;
        }
        $userid = $tmpStaffUserId;
    }

    $CI = & get_instance();

    $staff = $CI->app_object_cache->get('staff-full-name-data-' . $userid);

    if (!$staff) {
        $CI->db->where('staffid', $userid);
        $staff = $CI->db->select('firstname,lastname')->from(db_prefix() . 'staff')->get()->row();
        $CI->app_object_cache->add('staff-full-name-data-' . $userid, $staff);
    }

    return html_escape($staff ? $staff->firstname . ' ' . $staff->lastname : '');
}


/**
 * Get all staff
 * @return array staff
 */
function get_staff_all()
{
    $CI = & get_instance();

    $data = $CI->db->select("staffid, CONCAT(firstname, ' ', lastname) AS full_name")->from(db_prefix() . 'staff')->get()->result_array();

    // Sử dụng array_column() để tạo một mảng chỉ bao gồm giá trị staff_id
    $staffIds = array_column($data, 'staffid');

    // Kết hợp mảng $staffIds và $data để tạo mảng mới
    $result = array_combine($staffIds, $data);

    return $result;
}


/**
 * Get staff default language
 * @param  mixed $staffid
 * @return mixed
 */
function get_staff_default_language($staffid = '')
{
    if (!is_numeric($staffid)) {
        // checking for current user if is admin
        if (isset($GLOBALS['current_user'])) {
            return $GLOBALS['current_user']->default_language;
        }

        $staffid = get_staff_user_id();
    }
    $CI = & get_instance();
    $CI->db->select('default_language');
    $CI->db->from(db_prefix() . 'staff');
    $CI->db->where('staffid', $staffid);
    $staff = $CI->db->get()->row();
    if ($staff) {
        return $staff->default_language;
    }

    return '';
}

function get_staff_recent_search_history($staff_id = null)
{
    $recentSearches = get_staff_meta($staff_id ? $staff_id : get_staff_user_id(), 'recent_searches');

    if ($recentSearches == '') {
        $recentSearches = [];
    } else {
        $recentSearches = json_decode($recentSearches);
    }

    return $recentSearches;
}

function update_staff_recent_search_history($history, $staff_id = null)
{
    $totalRecentSearches = hooks()->apply_filters('total_recent_searches', 5);
    $history             = array_reverse($history);
    $history             = array_unique($history);
    $history             = array_splice($history, 0, $totalRecentSearches);

    update_staff_meta($staff_id ? $staff_id : get_staff_user_id(), 'recent_searches', json_encode($history));

    return $history;
}


/**
 * Check if user is staff member
 * In the staff profile there is option to check IS NOT STAFF MEMBER eq like contractor
 * Some features are disabled when user is not staff member
 * @param  string  $staff_id staff id
 * @return boolean
 */
function is_staff_member($staff_id = '')
{
    $CI = & get_instance();
    if ($staff_id == '') {
        if (isset($GLOBALS['current_user'])) {
            return $GLOBALS['current_user']->is_not_staff === '0';
        }
        $staff_id = get_staff_user_id();
    }

    $CI->db->where('staffid', $staff_id)
    ->where('is_not_staff', 0);

    return $CI->db->count_all_results(db_prefix() . 'staff') > 0 ? true : false;
}


if (!function_exists('is_b2b_sale')) {
    function is_b2b_sale()
    {
        return Staff::isB2BSale();
    }
}

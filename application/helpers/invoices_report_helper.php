<?php

use app\services\AccountingService;
use app\services\AmsService;
use Illuminate\Support\Collection;
use Entities\ClientAmsJob;
use Entities\ClientAmsSearchPackage;
use Entities\Invoice;
use Carbon\Carbon;
use Dto\Infoplus\InquiryEcTransDTO;
use Dto\Minvoice\InquiryInvoiceDTO;
use Entities\AmsPackage;
use Entities\InvoiceDailyRevenue;
use Entities\Itemable;
use Entities\MInvoice;
use Illuminate\Database\Eloquent\Builder;

defined('BASEPATH') or exit('No direct script access allowed');

if (!function_exists('get_ams_job_package')) {
    function get_ams_job_package($packages)
    {
        return last($packages) ?? [];
    }
}

if (!function_exists('search_ams_job_itemable_index')) {
    function search_ams_job_itemable_index($invoicePaidItems, $jobInvoiceId, $package, $clientAmsPackages)
    {
        return $invoicePaidItems->search(
            fn($item) =>
            $item['rel_id'] == $jobInvoiceId &&
                isset($package['id']) &&
                optional($clientAmsPackages->get($item['item_id']))->ams_package_id == $package['id']
        );
    }
}

if (!function_exists('get_items_by_qty')) {
    function get_items_by_qty($invoiceItem)
    {
        return $invoiceItem->flatMap(function ($item) {
            return collect(range(1, $item['qty']))->map(function () use ($item) {
                $item->qty = 1;
                return $item;
            });
        });
    }
}

if (!function_exists('fetch_minvoice_invoices')) {
    function fetch_minvoice_invoices($startDayOfMonth, $endDayOfMonth)
    {
        return MInvoice::query()
            ->where('invoice_issued_date', '>=', $startDayOfMonth)
            ->where('invoice_issued_date', '<=', $endDayOfMonth)
            ->get();
    }
}

if (!function_exists('fetch_monthly_report_by_month')) {
    /**
     * fetch data of monthly report by month
     * @param string $month
     *
     * @return mixed
     */
    function fetch_monthly_report_by_month(string $month)
    {
        $startDayOfMonth = Carbon::createFromFormat('Y.m', $month)->startOfMonth()->startOfDay()->format('Y-m-d H:i:s');
        $endDayOfMonth = Carbon::createFromFormat('Y.m', $month)->endOfMonth()->endOfDay()->format('Y-m-d H:i:s');

        $invoices = Invoice::query()
            ->select([
                'id',
                'number',
                'number_format',
                'prefix',
                'date',
                'status',
                'clientid',
                'subtotal',
                'discount_total',
                'discount_fixed',
                'discount_percent',
                'total',
                'total_tax',
                'addedfrom',
            ])
            ->with([
                'paymentRecord',
                'invoiceItems:id,rel_id,rel_type,qty,rate,item_order,item_id,use_expired_at,discount_table',
                'creator:staffid,firstname,lastname',
                'minvoice',
            ])
            ->whereHas(
                'minvoice',
                fn($query) =>
                $query
                    ->where('invoice_issued_date', '>=', $startDayOfMonth)
                    ->where('invoice_issued_date', '<=', $endDayOfMonth)
            )
            ->whereIn('status', [
                Invoice::STATUS_PAID,
                Invoice::STATUS_PARTIALLY,
                Invoice::STATUS_UNPAID,
                Invoice::STATUS_OVERDUE,
            ])
            ->get();

        $invoiceIds = $invoices->pluck('id');
        $amsJobs = $credits = $invoicePaidItems = collect();

        if ($invoiceIds->count()) {
            $invoicePaidItems = $invoices->flatMap(function ($invoice) {
                return $invoice->invoiceItems;
            });

            $clientAmsJobs = ClientAmsJob::whereIn('invoice_id', $invoiceIds)->get()->groupBy('invoice_id');
            $amsJobIds = $clientAmsJobs->pluck('*.ams_job_id')->flatMap(fn($val) => $val);
            $amsSearchCvs = ClientAmsSearchPackage::whereIn('invoice_id', $invoiceIds)->get()->groupBy('invoice_id');
            $searchPackageIds = $amsSearchCvs->pluck('*.ams_company_search_package_id')->flatMap(fn($val) => $val);
            $amsJobs = get_ams_job_by_ams_job_ids($amsJobIds);
            $credits = get_ams_search_cv_by_ams_search_cv_ids($searchPackageIds);
        }

        return [$invoices, $amsJobs, $credits, $invoicePaidItems];
    }
}

if (!function_exists('fetch_monthly_report_by_job_posted_month')) {
    /**
     * fetch data of monthly report by month
     * @param string $month
     *
     * @return mixed
     */
    function fetch_monthly_report_by_job_posted_month(string $month)
    {
        $details = collect();
        $amounts = [
            'posted_total_quantity' => 0,
            'posted_total_amount' => 0,
            'posted_total_revenue' => 0,
        ];

        $startDayOfMonth = Carbon::createFromFormat('Y.m', $month)->startOfMonth()->startOfDay()->format('Y-m-d H:i:s');
        $endDayOfMonth = Carbon::createFromFormat('Y.m', $month)->endOfMonth()->endOfDay()->format('Y-m-d H:i:s');
        $amsJobs = get_ams_job_by_posted_date($startDayOfMonth, $endDayOfMonth);
        $invoiceIds = $amsJobs->pluck('crm_invoice_id')->unique();
        $amsSearchCvs = get_ams_search_cv_by_ams_search_cv_active_date($startDayOfMonth, $endDayOfMonth);
        $amsSearchItemableIds = $amsSearchCvs->pluck('crm_itemable_id')->unique();

        if ($amsSearchItemableIds->isNotEmpty()) {
            $invoiceIds = $invoiceIds->merge(Itemable::whereIn('id', $amsSearchItemableIds)->pluck('rel_id'))
                ->unique();
        }

        if ($invoiceIds->isEmpty()) {
            return [$details, $amounts];
        }

        $invoices = Invoice::query()
            ->select([
                'id',
                'number',
                'number_format',
                'prefix',
                'date',
                'status',
                'clientid',
                'subtotal',
                'discount_total',
                'discount_fixed',
                'discount_percent',
                'total',
                'addedfrom',
            ])
            ->with([
                'paymentRecord:invoiceid,date',
                'invoiceItems:id,rel_id,rel_type,qty,rate,item_order,item_id,use_expired_at,discount_table',
                'creator:staffid,firstname,lastname',
                'minvoice'
            ])
            ->whereHas('minvoice', fn($query) => $query->issued())
            ->whereIn('status', [
                Invoice::STATUS_PAID,
                Invoice::STATUS_PARTIALLY,
                Invoice::STATUS_UNPAID,
                Invoice::STATUS_OVERDUE,
            ])
            ->whereIn('id', $invoiceIds)
            ->get()
            ->keyBy('id');

        if ($invoices->isEmpty()) {
            return [$details, $amounts];
        }

        $invoicePaidItems = $invoices->flatMap(function ($invoice) {
            return get_items_by_qty($invoice->invoiceItems);
        });

        // Add discountPerUnit to the existing invoice
        $invoices = $invoices->map(function ($invoice) use ($invoicePaidItems) {
            $invoicePaidItems = $invoicePaidItems->filter(fn($item) => $item->rate > 0);
            $invoice->discountPerUnit = get_invoice_unit_discount($invoice, $invoicePaidItems);
            return $invoice;
        });

        $clientAmsPackages = AmsPackage::get()->keyBy('crm_item_id');
        $revenue = 0;
        $invoiceQuantity = $invoices->count();
        $invoiceAmount = $invoices->sum('subtotal') - $invoices->sum('discount_total');
        $amsJobs->each(function ($job) use (&$details, &$invoicePaidItems, &$revenue, $invoices, $clientAmsPackages) {
            $jobInvoiceId = $job['crm_invoice_id'];
            $package = get_ams_job_package($job['package_list']);

            $foundPaidItemIndex = search_ams_job_itemable_index($invoicePaidItems, $jobInvoiceId, $package, $clientAmsPackages);

            if ($foundPaidItemIndex !== false) {
                $invoice = $invoices->get($jobInvoiceId);

                if ($invoice) {
                    $foundItem = $invoicePaidItems->splice($foundPaidItemIndex, 1)->first();
                    $details->push([
                        // Posted Date
                        'posted_date' => Carbon::createFromFormat('d-m-Y', $job['published']['date'])->format('Y.m.d'),
                        // Invoice No
                        'invoice_id' => $invoice->id,
                        'invoice_no' => format_invoice_number($invoice),
                        // Issue date
                        'issued_date' => $invoice->minvoice->invoice_issued_date ? $invoice->minvoice->invoice_issued_date->format('Y.m.d') : '',
                        'invoice_status' => $invoice->status,
                        // Sale man name
                        'staff_id' => $invoice->creator->staffid ?? '',
                        'sale_name' => $invoice->creator->fullname ?? '',
                        'type' => 'job_posting',
                        'rate' => $foundItem->rate,
                    ]);

                    if ($foundItem->rate) {
                        $revenue += $foundItem->getItemSaleRate($invoice->discount_percent) - ($invoice->discountPerUnit ?? 0);
                    }
                }
            }
        });

        $amsSearchCvs->each(function ($credit) use (&$details, &$invoicePaidItems, &$revenue, $invoices) {
            $foundPaidItemIndex = $invoicePaidItems->search(fn($item) => $item->id == $credit['crm_itemable_id']);

            if ($foundPaidItemIndex !== false) {
                $foundItem = $invoicePaidItems->splice($foundPaidItemIndex, 1)->first();
                $invoice = $foundItem ? $invoices->get($foundItem->rel_id) : null;

                if ($invoice) {
                    $details->push([
                        // Posted Date
                        'posted_date' => Carbon::createFromFormat('Y-m-d', $credit['valid_at'])->format('Y.m.d'),
                        // Invoice No
                        'invoice_id' => $invoice->id,
                        'invoice_no' => format_invoice_number($invoice),
                        // Issue date
                        'issued_date' => $invoice->minvoice->invoice_issued_date ? $invoice->minvoice->invoice_issued_date->format('Y.m.d') : '',
                        'invoice_status' => $invoice->status,
                        // Sale man name
                        'staff_id' => $invoice->creator->staffid ?? '',
                        'sale_name' => $invoice->creator->fullname ?? '',
                        'type' => 'search_cv',
                        'rate' => $foundItem->rate,
                    ]);

                    if ($foundItem->rate) {
                        $revenue += $foundItem->getItemSaleRate($invoice->discount_percent) - ($invoice->discountPerUnit ?? 0);
                    }
                }
            }
        });

        $amounts = [
            'posted_total_quantity' => $invoiceQuantity,
            'posted_total_amount' => round($invoiceAmount),
            'posted_total_revenue' => round($revenue),
        ];

        return [$details, $amounts];
    }
}

if (!function_exists('get_invoice_unit_discount')) {
    function get_invoice_unit_discount($invoice, $paidItems)
    {
        $paidItems = $paidItems->filter(fn($item) => $item['rel_id'] == $invoice->id);

        if ($paidItems->isEmpty()) {
            return 0;
        }

        return $invoice->discount_fixed  ? $invoice->discount_fixed / $paidItems->count() : 0;
    }
}

if (!function_exists('get_total_amount_by_month')) {
    function get_total_amount_by_month($month, $invoices, $invoicePaidItems, $amsJobs, $amsCredits)
    {
        $monthObj = Carbon::createFromFormat('Y.m', $month)->startOfMonth();
        $thisMonth = Carbon::now()->startOfMonth();
        $table = [];
        $amsPackages = AmsPackage::get()->keyBy('crm_item_id');
        $invoices = $invoices->keyBy('id');
        $invoiceAmount = $invoices->sum('subtotal') - $invoices->sum('discount_total');
        $invoiceQuantity = $invoices->count();
        $advancedAmount = 0;
        $revenueAmount = 0;
        $unpaidAmount = [];

        // Split items to flat array based on it's qty. Example item's qty is 3, then split to 3 items
        $invoicePaidItems = $invoicePaidItems->groupBy('rel_id')->flatMap(fn($invoiceItem) => get_items_by_qty($invoiceItem));

        // Add discountPerUnit to the existing invoice
        $invoices = $invoices->map(function ($invoice) use ($invoicePaidItems, &$unpaidAmount) {
            $invoice->discountPerUnit = get_invoice_unit_discount($invoice, $invoicePaidItems);
            $unpaidAmount[$invoice->id] = $invoice->isPaid() ? 0 : ($invoice->total - $invoice->total_tax);
            return $invoice;
        });

        $jobs = $amsJobs->groupBy(fn($job) => Carbon::createFromFormat('d-m-Y', $job['published']['date'])->format('Ym'));
        $credits = $amsCredits->groupBy(fn($credit) => Carbon::createFromFormat('Y-m-d', $credit['valid_at'] ?? Carbon::now()->addMonth()->startOfMonth()->format('Y-m-d'))->format('Ym'));

        do {
            $currentMonth = $monthObj->format('Ym');
            $table[$currentMonth] = 0;
            $revenue = 0;

            $jobs->get($currentMonth, collect())
                ->each(function ($job) use (&$invoicePaidItems, &$revenue, $amsPackages, $invoices) {
                    $jobInvoiceId = $job['crm_invoice_id'];
                    $package = get_ams_job_package($job['package_list']);

                    $foundPaidItemIndex = $invoicePaidItems->search(
                        fn($item) =>
                        $item['rel_id'] == $jobInvoiceId &&
                            isset($package['id']) &&
                            optional($amsPackages->get($item['item_id']))->ams_package_id == $package['id']
                    );

                    if ($foundPaidItemIndex !== false) {
                        $invoice = $invoices->get($jobInvoiceId);
                        $foundItem = $invoicePaidItems->splice($foundPaidItemIndex, 1)->first();
                        $revenue += $foundItem->getItemSaleRate($invoice->discount_percent) - ($invoice->discountPerUnit ?? 0);
                    }
                });

            $credits->get($currentMonth, collect())
                ->each(function ($credit) use (&$invoicePaidItems, &$revenue, $invoices) {
                    $foundPaidItemIndex = $invoicePaidItems->search(fn($item) => $item->id == $credit['crm_itemable_id']);

                    if ($foundPaidItemIndex !== false) {
                        $foundItem = $invoicePaidItems->splice($foundPaidItemIndex, 1)->first();
                        $invoice = $invoices->get($foundItem->rel_id);
                        $revenue += $foundItem->getItemSaleRate($invoice->discount_percent) - ($invoice->discountPerUnit ?? 0);
                    }
                });

            $table[$currentMonth] = $revenue;
            $revenueAmount += $revenue;

            // Add 1 month for checking
            $monthObj = $monthObj->addMonth();
        } while ($monthObj->diffInMonths($thisMonth, false) >= 0);

        $invoicePaidItems->each(function ($item) use (&$advancedAmount, $invoices) {
            $invoice = $invoices->get($item->rel_id);
            $itemRate = $item->getItemSaleRate($invoice->discount_percent) - ($invoice->discountPerUnit ?? 0);
            $advancedAmount += $itemRate;
        });

        return [$table, $invoiceQuantity, $invoiceAmount, $advancedAmount, $revenueAmount, array_sum($unpaidAmount)];
    }
}


if (!function_exists('get_ams_job_by_ams_job_ids')) {
    /**
     * Request AMS project via service to get jobs
     *
     * @param Collection $amsJobIds
     * @return Collection
     */
    function get_ams_job_by_ams_job_ids($amsJobIds)
    {
        if (!$amsJobIds->count()) {
            return collect();
        }

        $amsJobs = collect();
        $response = AmsService::search('jobs', [
            'query' => [
                'ids' => $amsJobIds->join(','),
                'fields' => [
                    'job' => join(',', [
                        'id',
                        'title',
                        'detail_url',
                        'crm_invoice_id',
                        'package_list',
                        'published',
                        'closed',
                        'from_crm'
                    ])
                ],
                'all_jobs' => true,
                'page' => 1,
                'page_size' => 1000,
                'ordering' => 'crm_invoice_id',
            ]
        ], 'get');

        if (isset($response['data'])) {
            $amsJobs = collect($response['data'])->keyBy('id');
        }

        return $amsJobs;
    }
}

if (!function_exists('get_ams_job_by_posted_date')) {
    /**
     * Request AMS project via service to get jobs
     *
     * @param string $from
     * @param string $to
     * @return Collection
     */
    function get_ams_job_by_posted_date($from, $to)
    {
        $amsJobs = collect();
        $response = AmsService::search('jobs', [
            'query' => [
                'published_from' => $from,
                'published_to' => $to,
                'has_crm_invoice_id' => true,
                'fields' => [
                    'job' => join(',', [
                        'id',
                        'crm_invoice_id',
                        'package_list',
                        'published',
                    ])
                ],
                'all_jobs' => true,
                'page' => 1,
                'page_size' => 1000,
                'ordering' => 'crm_invoice_id',
            ]
        ], 'get');

        if (isset($response['data'])) {
            $amsJobs = collect($response['data'])->keyBy('id');
        }

        return $amsJobs;
    }
}

if (!function_exists('get_ams_search_cv_by_ams_search_cv_ids')) {
    /**
     * Request AMS project via service to get jobs
     *
     * @param Collection $searchPackageIds
     * @return Collection
     */
    function get_ams_search_cv_by_ams_search_cv_ids($searchPackageIds)
    {
        if (!$searchPackageIds->count()) {
            return collect();
        }

        $credits = collect();
        $response = AmsService::amsApi('crm/company-credits', [
            'query' => [
                'ids' => $searchPackageIds->join(',')
            ]
        ], 'get');

        if (isset($response['data'])) {
            $credits = collect($response['data'])->filter(fn($credit) => isset($credit['crm_itemable_id']));
        }

        return $credits;
    }
}

if (!function_exists('get_ams_search_cv_by_ams_search_cv_active_date')) {
    /**
     * Request AMS project via service to get jobs
     *
     * @param string $from
     * @param string $to
     * @return Collection
     */
    function get_ams_search_cv_by_ams_search_cv_active_date($from, $to)
    {
        $credits = collect();
        $response = AmsService::amsApi('crm/company-credits', [
            'query' => [
                'activated_from' => $from,
                'activated_to' => $to,
                'has_crm_itemable_id' => true,
            ]
        ], 'get');

        if (isset($response['data'])) {
            $credits = collect($response['data'])->filter(fn($credit) => isset($credit['crm_itemable_id']));
        }

        return $credits;
    }
}

if (!function_exists('get_invoice_details_by_report_month')) {
    /**
     * Get job posting / search cv used
     *
     * @param Collection $invoices
     * @param Collection $amsJobs
     * @param Collection $credits
     * @param Collection $invoicePaidItems
     * @param string $reportMonth
     * @param int $advancedAmount
     * @return Collection
     */
    function get_invoice_details_by_report_month(
        $invoices,
        $amsJobs,
        $credits,
        $invoicePaidItems,
        $reportMonth
    ) {
        $details = collect();
        $invoices = $invoices->keyBy('id');
        $invoicePaidItems = $invoicePaidItems->keyBy('id');
        $reportMonthAmsJobs = $amsJobs->filter(fn($job) => Carbon::createFromFormat('d-m-Y', $job['published']['date'])->format('Y.m') == $reportMonth);
        $reportMonthCredits = $credits->filter(fn($credit) => !empty($credit['valid_at']) && Carbon::createFromFormat('Y-m-d', $credit['valid_at'])->format('Y.m') == $reportMonth);


        $reportMonthAmsJobs->each(function ($job) use (&$details, $invoices) {
            $invoice = $invoices->get($job['crm_invoice_id']);
            if ($invoice) {
                $details->push([
                    // Posted Date
                    'posted_date' => Carbon::createFromFormat('d-m-Y', $job['published']['date'])->format('Y.m.d'),
                    // Invoice No
                    'invoice_id' => $invoice->id,
                    'invoice_no' => format_invoice_number($invoice),
                    'invoice_number' => $invoice->minvoice->invoice_number ?? '',
                    // Issue date
                    'issued_date' => $invoice->minvoice->invoice_issued_date->format('Y.m.d'),
                    'invoice_status' => $invoice->status,
                    // Sale man name
                    'staff_id' => $invoice->creator->staffid ?? '',
                    'sale_name' => $invoice->creator->fullname ?? '',
                    'type' => 'job_posting',
                ]);
            }
        });

        $reportMonthCredits->each(function ($credit) use (&$details, $invoices, $invoicePaidItems) {
            $invoicePaidItem = $invoicePaidItems->get($credit['crm_itemable_id']);
            $invoice = $invoicePaidItem ? $invoices->get($invoicePaidItem->rel_id) : null;

            if ($invoice) {
                $details->push([
                    // Posted Date
                    'posted_date' => Carbon::createFromFormat('Y-m-d', $credit['valid_at'])->format('Y.m.d'),
                    // Invoice No
                    'invoice_id' => $invoice->id,
                    'invoice_no' => format_invoice_number($invoice),
                    // Issue date
                    'issued_date' => $invoice->minvoice->invoice_issued_date->format('Y.m.d'),
                    'invoice_status' => $invoice->status,
                    // Sale man name
                    'staff_id' => $invoice->creator->staffid ?? '',
                    'sale_name' => $invoice->creator->fullname ?? '',
                    'type' => 'search_cv',
                ]);
            }
        });

        return $details;
    }
}

if (!function_exists('get_advanced_invoice_details_by_report_month')) {
    /**
     * Get job posting / search cv used
     *
     * @param Collection $invoices
     * @param Collection $amsJobs
     * @param Collection $credits
     * @param Collection $invoicePaidItems
     * @param string $reportMonth
     * @return Collection
     */
    function get_advanced_invoice_details_by_report_month(
        $invoices,
        $amsJobs,
        $credits,
        $invoicePaidItems
    ) {
        $details = collect();
        $invoices = $invoices->keyBy('id');
        $invoicePaidItems = $invoicePaidItems->filter(fn($item) => $item->rate > 0)->values();
        $invoicePaidItems = get_items_by_qty($invoicePaidItems);
        $amsPackages = AmsPackage::get()->keyBy('crm_item_id');

        // Remove used job posting
        $amsJobs->each(function ($job) use (&$invoicePaidItems, $amsPackages) {
            $jobInvoiceId = $job['crm_invoice_id'];
            $package = get_ams_job_package($job['package_list']);

            $foundPaidItemIndex = $invoicePaidItems->search(
                fn($item) =>
                $item['rel_id'] == $jobInvoiceId &&
                    isset($package['id']) &&
                    optional($amsPackages->get($item['item_id']))->ams_package_id == $package['id']
            );

            if ($foundPaidItemIndex !== false) {
                $invoicePaidItems->splice($foundPaidItemIndex, 1);
            }
        });

        // Remove used search cv
        $credits = $credits->filter(fn($credit) => empty($credit['valid_at']));
        $credits->each(function ($credit) use (&$invoicePaidItems) {
            $foundPaidItemIndex = $invoicePaidItems->search(fn($item) => $item->id == $credit['crm_itemable_id']);

            if ($foundPaidItemIndex !== false) {
                $invoicePaidItems->splice($foundPaidItemIndex, 1);
            }
        });

        $details = $invoicePaidItems->map(function ($item) use ($invoices) {
            $invoice = $invoices->get($item['rel_id']);
            return [
                // Posted Date
                'posted_date' => '',
                // Invoice No
                'invoice_id' => $invoice->id,
                'invoice_no' => format_invoice_number($invoice),
                'invoice_number' => $invoice->minvoice->invoice_number ?? '',
                // Issue date
                'issued_date' => $invoice->minvoice->invoice_issued_date->format('Y.m.d'),
                'invoice_status' => $invoice->status,
                // Sale man name
                'staff_id' => $invoice->creator->staffid ?? '',
                'sale_name' => $invoice->creator->fullname ?? '',
            ];
        });

        return $details;
    }
}

if (!function_exists('get_infoplus_transactions')) {
    function get_infoplus_transactions($month)
    {
        $startDayOfMonth = Carbon::createFromFormat('Y.m', $month)
            ->startOfMonth()
            ->startOfDay()
            ->format('Ymd');

        $endDayOfMonth = Carbon::createFromFormat('Y.m', $month)
            ->endOfMonth()
            ->endOfDay()
            ->format('Ymd');

        $hasNextRecords = false;
        $records = [];
        $startPage = 1;
        $limit = 100;
        do {
            $inquiry = new InquiryEcTransDTO(
                $startDayOfMonth,
                $endDayOfMonth,
                $startPage,
                $limit
            );
            $response = AccountingService::inquiryEcTrans($inquiry);
            $records = array_merge($records, $response['data']['records'] ?? []);
            $hasNextRecords = $response['data']['hasNextPage'] ?? false;
            $startPage++;
        } while ($hasNextRecords);

        return collect($records);
    }
}

if (!function_exists('get_unpaid_po')) {
    function get_unpaid_po_query($month): Builder
    {
        $startDayOfMonth = Carbon::createFromFormat('Y.m', $month)
            ->startOfMonth()
            ->startOfDay()
            ->format('Y-m-d H:i:s');

        $endDayOfMonth = Carbon::createFromFormat('Y.m', $month)
            ->endOfMonth()
            ->endOfDay()
            ->format('Y-m-d H:i:s');

        return Invoice::query()
            ->whereHas(
                'minvoice',
                fn($query)
                    => $query->issued()
                        ->whereBetween('invoice_issued_date', [$startDayOfMonth, $endDayOfMonth])
            )
            ->notPaidYet()
            ->with([
                'client:userid,business_name',
            ]);
    }
}

if (!function_exists('get_job_posting_daily_revenue_query')) {
    function get_job_posting_daily_revenue_query($date, $selects): Builder
    {
        return InvoiceDailyRevenue::select($selects)
            ->with([
                'invoice:id,number,number_format,prefix,date',
                'itemable:id,rel_id,rel_type,item_id',
                'itemable.item:id,description',
                'invoice.invoiceNumber'
            ])
            ->where('revenue_date', Carbon::createFromFormat('Y.m.d', $date)->format('Ymd'));
    }
}

if (!function_exists('get_job_posting_monthly_revenue_query')) {
    function get_job_posting_monthly_revenue_query($month, $selects): Builder
    {
        $startDayOfMonth = Carbon::createFromFormat('Y.m.d', $month . '.01')->startOfMonth()->startOfDay()->format('Ymd');
        $endDayOfMonth = Carbon::createFromFormat('Y.m.d', $month . '.01')->endOfMonth()->endOfDay()->format('Ymd');
        return InvoiceDailyRevenue::select($selects)
            ->with([
                'itemable:id,rel_id,rel_type,item_id,rate,discount_table,qty',
                'itemable.item:id,description',
                'invoice' => function ($query) {
                    return $query->select('id', 'number', 'number_format', 'prefix', 'date', 'clientid', 'discount_total', 'discount_fixed', 'discount_percent', 'total', 'total_tax')
                        ->with([
                            'client:userid,business_name',
                            'minvoice',
                            'invoiceNumber',
                            'invoiceItems' => function ($query) {
                                return $query->select('id', 'rel_id', 'rel_type', 'qty', 'rate', 'item_id')
                                    ->isNotDiscountPackage()
                                    ->with([
                                        'item:id,description',
                                        'item.amsPackage:id,crm_item_id,ams_package_id',
                                    ]);
                            },
                        ])
                        ->withSum('invoiceItems', 'qty');
                }
            ])
            ->where('revenue_date', '>=', $startDayOfMonth)
            ->where('revenue_date', '<=', $endDayOfMonth)
            // ->groupBy('ams_job_id')
            ;
    }
}

if (!function_exists('get_total_amount_job_posting_monthly_revenue_query')) {
    function get_total_amount_job_posting_monthly_revenue_query($month)
    {
        $startDayOfMonth = Carbon::createFromFormat('Y.m.d', $month . '.01')->startOfMonth()->startOfDay()->format('Ymd');
        $endDayOfMonth = Carbon::createFromFormat('Y.m.d', $month . '.01')->endOfMonth()->endOfDay()->format('Ymd');
        return InvoiceDailyRevenue::select(['invoice_id', 'itemable_id', 'amount'])
            ->with([
                'itemable:id,rel_id,rel_type,item_id,rate,discount_table,qty',
                'invoice' => function ($query) {
                    return $query->select('id', 'discount_total', 'discount_fixed', 'discount_percent', 'total', 'total_tax')
                        ->with([
                            'invoiceItems' => function ($query) {
                                return $query->select('id', 'rel_id', 'rel_type', 'qty', 'rate', 'item_id')->isNotDiscountPackage();
                            },
                            'minvoice'
                        ]);
                }
            ])
            ->where('revenue_date', '>=', $startDayOfMonth)
            ->where('revenue_date', '<=', $endDayOfMonth)
            ->get();
    }
}

<?php

use Entities\Estimate;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Check if company using invoice with different currencies
 * @param  string  $table table to check
 * @return boolean
 */
function is_using_multiple_currencies($table = null)
{
    if (!$table) {
        $table = db_prefix() . 'invoices';
    }

    $CI = &get_instance();
    $CI->load->model('currencies_model');
    $currencies            = $CI->currencies_model->get();
    $total_currencies_used = 0;
    $other_then_base       = false;
    $base_found            = false;
    foreach ($currencies as $currency) {
        $CI->db->where('currency', $currency['id']);
        $total = $CI->db->count_all_results($table);
        if ($total > 0) {
            $total_currencies_used++;
            if ($currency['isdefault'] == 0) {
                $other_then_base = true;
            } else {
                $base_found = true;
            }
        }
    }

    if ($total_currencies_used > 1 && $base_found == true && $other_then_base == true) {
        return true;
    } elseif ($total_currencies_used == 1 && $base_found == false && $other_then_base == true) {
        return true;
    } elseif ($total_currencies_used == 0 || $total_currencies_used == 1) {
        return false;
    }

    return true;
}
/**
 * Custom format number function for the app
 * @param  mixed  $total
 * @param  boolean $foce_check_zero_decimals whether to force check
 * @return mixed
 */
function app_format_number($total, $foce_check_zero_decimals = false)
{
    if (!is_numeric($total)) {
        return $total;
    }

    $decimal_separator  = get_option('decimal_separator');
    $thousand_separator = get_option('thousand_separator');

    $d = get_decimal_places();
    if (get_option('remove_decimals_on_zero') == 1 || $foce_check_zero_decimals == true) {
        if (!is_decimal($total)) {
            $d = 0;
        }
    }

    $formatted = number_format($total, $d, $decimal_separator, $thousand_separator);

    return hooks()->apply_filters('number_after_format', $formatted, [
        'total'              => $total,
        'decimal_separator'  => $decimal_separator,
        'thousand_separator' => $thousand_separator,
        'decimal_places'     => $d,
    ]);
}

/**
 * Format money/amount based on currency settings
 * @since  2.3.2
 * @param  mixed   $amount          amount to format
 * @param  mixed   $currency        currency db object or currency name (ISO code)
 * @param  boolean $excludeSymbol   whether to exclude to symbol from the format
 * @return string
 */
function app_format_money($amount, $currency, $excludeSymbol = false)
{
    /**
     *  Check ewhether the amount is numeric and valid
     */

    if (!is_numeric($amount) && $amount != 0) {
        return $amount;
    }

    if (is_null($amount)) {
        $amount = 0;
    }

    /**
     * Check if currency is passed as Object from database or just currency name e.q. USD
     */
    if (is_string($currency)) {
        $dbCurrency = get_currency($currency);

        // Check of currency found in case does not exists in database
        if ($dbCurrency) {
            $currency = $dbCurrency;
        } else {
            $currency = [
                'symbol'             => $currency,
                'name'               => $currency,
                'placement'          => 'before',
                'decimal_separator'  => get_option('decimal_separator'),
                'thousand_separator' => get_option('thousand_separator'),
            ];
            $currency = (object) $currency;
        }
    }

    /**
     * Determine the symbol
     * @var string
     */
    $symbol = !$excludeSymbol ? $currency->symbol : '';

    /**
     * Check decimal places
     * @var mixed
     */
    $d = get_option('remove_decimals_on_zero') == 1 && !is_decimal($amount) ? 0 : get_decimal_places();

    /**
     * Format the amount
     * @var string
     */

    $amountFormatted = number_format($amount, $d, $currency->decimal_separator, $currency->thousand_separator);

    /**
     * Maybe add the currency symbol
     * @var string
     */
    $formattedWithCurrency = $currency->placement === 'after' ? $amountFormatted . '' . $symbol : $symbol . '' . $amountFormatted;

    return hooks()->apply_filters('app_format_money', $formattedWithCurrency, [
        'amount'         => $amount,
        'currency'       => $currency,
        'exclude_symbol' => $excludeSymbol,
        'decimal_places' => $d,
    ]);
}

/**
 * Check if passed number is decimal
 * @param  mixed  $val
 * @return boolean
 */
function is_decimal($val)
{
    return is_numeric($val) && floor($val) != $val;
}
/**
 * Function that will loop through taxes and will check if there is 1 tax or multiple
 * @param  array $taxes
 * @return boolean
 */
function multiple_taxes_found_for_item($taxes)
{
    $names = [];
    foreach ($taxes as $t) {
        array_push($names, $t['taxname']);
    }
    $names = array_map('unserialize', array_unique(array_map('serialize', $names)));
    if (count($names) == 1) {
        return false;
    }

    return true;
}

/**
 * If there is more then 200 items in the script the search when creating eq invoice, estimate, proposal
 * will be ajax based
 * @return int
 */
function ajax_on_total_items()
{
    return hooks()->apply_filters('ajax_on_total_items', 200);
}

/**
 * Helper function to get tax by passedid
 * @param  integer $id taxid
 * @return object
 */
function get_tax_by_id($id)
{
    $CI = &get_instance();
    $CI->db->where('id', $id);

    return $CI->db->get(db_prefix() . 'taxes')->row();
}
/**
 * Helper function to get tax by passed name
 * @param  string $name tax name
 * @return object
 */
function get_tax_by_name($name)
{
    $CI = &get_instance();
    $CI->db->where('name', $name);

    return $CI->db->get(db_prefix() . 'taxes')->row();
}
/**
 * This function replace <br /> only nothing exists in the line and first line other then <br />
 *  Replace first <br /> lines to prevent new spaces
 * @param  string $text The text to perform the action
 * @return string
 */
function _maybe_remove_first_and_last_br_tag($text)
{
    $text = preg_replace('/^<br ?\/?>/is', '', $text);
    // Replace last <br /> lines to prevent new spaces while there is new line
    while (preg_match('/<br ?\/?>$/', $text)) {
        $text = preg_replace('/<br ?\/?>$/is', '', $text);
    }

    return $text;
}

/**
 * Helper function to replace info format merge fields
 * Info format = Address formats for customers, proposals, company information
 * @param  string $mergeCode merge field to check
 * @param  mixed $val       value to replace
 * @param  string $txt       from format
 * @return string
 */
function _info_format_replace($mergeCode, $val, $txt)
{
    $tmpVal = strip_tags($val);

    if ($tmpVal != '') {
        $result = preg_replace('/({' . $mergeCode . '})/i', $val, $txt);
    } else {
        $re     = '/\s{0,}{' . $mergeCode . '}(<br ?\/?>(\n))?/i';
        $result = preg_replace($re, '', $txt);
    }

    return $result;
}

/**
 * Helper function to replace info format custom field merge fields
 * Info format = Address formats for customers, proposals, company information
 * @param  mixed $id    custom field id
 * @param  string $label custom field label
 * @param  mixed $value custom field value
 * @param  string $txt   from format
 * @return string
 */
function _info_format_custom_field($id, $label, $value, $txt)
{
    if ($value != '') {
        $result = preg_replace('/({cf_' . $id . '})/i', $label . ': ' . $value, $txt);
    } else {
        $re     = '/\s{0,}{cf_' . $id . '}(<br ?\/?>(\n))?/i';
        $result = preg_replace($re, '', $txt);
    }

    return hooks()->apply_filters('info_format_custom_field', $result, [
        'id'    => $id,
        'label' => $label,
        'txt'   => $txt,
    ]);
}

/**
 * Perform necessary checking for custom fields info format
 * @param  array $custom_fields custom fields
 * @param  string $txt           info format text
 * @return string
 */
function _info_format_custom_fields_check($custom_fields, $txt)
{
    if (count($custom_fields) == 0 || preg_match_all('/({cf_[0-9]{1,}})/i', $txt, $matches, PREG_SET_ORDER, 0) > 0) {
        $txt = preg_replace('/\s{0,}{cf_[0-9]{1,}}(<br ?\/?>(\n))?/i', '', $txt);
    }

    return $txt;
}

if (!function_exists('format_customer_info')) {
    /**
     * Format customer address info
     * @param  object  $data        customer object from database
     * @param  string  $for         where this format will be used? Eq statement invoice etc
     * @param  string  $type        billing/shipping
     * @param  boolean $companyLink company link to be added on customer company/name, this is used in admin area only
     * @return string
     */
    function format_customer_info($data, $for, $type, $companyLink = false)
    {
        $format   = get_option('customer_info_format');
        $clientId = '';

        if ($for == 'statement') {
            $clientId = $data->userid;
        } elseif ($type == 'billing') {
            $clientId = $data->clientid;
        }

        $filterData = [
            'data'         => $data,
            'for'          => $for,
            'type'         => $type,
            'client_id'    => $clientId,
            'company_link' => $companyLink,
        ];

        $companyName = '';
        if ($for == 'statement') {
            $companyName = get_company_name($clientId);
        } elseif ($type == 'billing') {
            $companyName = $data->client->company;
        }

        $acceptsPrimaryContactDisplay = ['invoice', 'estimate', 'payment', 'credit_note'];

        if (
            in_array($for, $acceptsPrimaryContactDisplay) &&
            isset($data->client->show_primary_contact) &&
            $data->client->show_primary_contact == 1 &&
            $primaryContactId = get_primary_contact_user_id($clientId)
        ) {
            $companyName = get_contact_full_name($primaryContactId) . '<br />' . $companyName;
        }

        $companyName = hooks()->apply_filters('customer_info_format_company_name', $companyName, $filterData);

        $street  = in_array($type, ['billing', 'shipping']) ? $data->{$type . '_street'} : '';
        $city    = in_array($type, ['billing', 'shipping']) ? $data->{$type . '_city'} : '';
        $state   = in_array($type, ['billing', 'shipping']) ? $data->{$type . '_state'} : '';
        $zipCode = in_array($type, ['billing', 'shipping']) ? $data->{$type . '_zip'} : '';

        $countryCode = '';
        $countryName = '';

        if ($country = in_array($type, ['billing', 'shipping']) ? get_country($data->{$type . '_country'}) : '') {
            $countryCode = $country->iso2;
            $countryName = $country->short_name;
        }

        $phone = '';
        if ($for == 'statement' && isset($data->phonenumber)) {
            $phone = $data->phonenumber;
        } elseif ($type == 'billing' && isset($data->client->phonenumber)) {
            $phone = $data->client->phonenumber;
        }

        $vat = '';
        if ($for == 'statement' && isset($data->vat)) {
            $vat = $data->vat;
        } elseif ($type == 'billing' && isset($data->client->vat)) {
            $vat = $data->client->vat;
        }

        if ($companyLink && (!isset($data->deleted_customer_name) ||
            (isset($data->deleted_customer_name) &&
                empty($data->deleted_customer_name)))) {
            $companyName = '<a href="' . admin_url('clients/client/' . $clientId) . '" target="_blank"><b>' . $companyName . '</b></a>';
        } elseif ($companyName != '') {
            $companyName = '<b>' . $companyName . '</b>';
        }

        $format = _info_format_replace('company_name', $companyName, $format);
        $format = _info_format_replace('customer_id', $clientId, $format);
        $format = _info_format_replace('street', $street, $format);
        $format = _info_format_replace('city', $city, $format);
        $format = _info_format_replace('state', $state, $format);
        $format = _info_format_replace('zip_code', $zipCode, $format);
        $format = _info_format_replace('country_code', $countryCode, $format);
        $format = _info_format_replace('country_name', $countryName, $format);
        $format = _info_format_replace('phone', $phone, $format);
        $format = _info_format_replace('vat_number', $vat, $format);
        $format = _info_format_replace('vat_number_with_label', $vat == '' ? '' : _l('client_vat_number') . ': ' . $vat, $format);

        $customFieldsCustomer = [];

        // On shipping address no custom fields are shown
        if ($type != 'shipping') {
            $whereCF = [];

            if (is_custom_fields_for_customers_portal()) {
                $whereCF['show_on_client_portal'] = 1;
            }

            $customFieldsCustomer = get_custom_fields('customers', $whereCF);
        }

        foreach ($customFieldsCustomer as $field) {
            $value  = get_custom_field_value($clientId, $field['id'], 'customers');
            $format = _info_format_custom_field($field['id'], $field['name'], $value, $format);
        }

        // If no custom fields found replace all custom fields merge fields to empty
        $format = _info_format_custom_fields_check($customFieldsCustomer, $format);
        $format = _maybe_remove_first_and_last_br_tag($format);

        // Remove multiple white spaces
        $format = preg_replace('/\s+/', ' ', $format);
        // Remove multiple coma
        $format = preg_replace('/,{2,}/m', '', $format);

        $format = trim($format);

        return hooks()->apply_filters('customer_info_text', $format, $filterData);
    }
}

if (!function_exists('format_proposal_info')) {
    /**
     * Format proposal info format
     * @param  object $proposal proposal from database
     * @param  string $for      where this info will be used? Admin area, HTML preview?
     * @return string
     */
    function format_proposal_info($proposal, $for = '')
    {
        $format = get_option('proposal_info_format');

        $countryCode = '';
        $countryName = '';

        if ($country = get_country($proposal->country)) {
            $countryCode = $country->iso2;
            $countryName = $country->short_name;
        }

        $proposalTo = '<b>' . $proposal->proposal_to . '</b>';
        $phone      = $proposal->phone;
        $email      = $proposal->email;

        if ($for == 'admin') {
            $hrefAttrs = '';
            if ($proposal->rel_type == 'lead') {
                $hrefAttrs = ' href="#" onclick="init_lead(' . $proposal->rel_id . '); return false;" data-toggle="tooltip" data-title="' . _l('lead') . '"';
            } else {
                $hrefAttrs = ' href="' . admin_url('clients/client/' . $proposal->rel_id) . '" data-toggle="tooltip" data-title="' . _l('client') . '"';
            }
            $proposalTo = '<a' . $hrefAttrs . '>' . $proposalTo . '</a>';
        }

        if ($for == 'html' || $for == 'admin') {
            $phone = '<a href="tel:' . $proposal->phone . '">' . $proposal->phone . '</a>';
            $email = '<a href="mailto:' . $proposal->email . '">' . $proposal->email . '</a>';
        }

        $format = _info_format_replace('proposal_to', $proposalTo, $format);
        $format = _info_format_replace('address', $proposal->address, $format);
        $format = _info_format_replace('city', $proposal->city, $format);
        $format = _info_format_replace('state', $proposal->state, $format);

        $format = _info_format_replace('country_code', $countryCode, $format);
        $format = _info_format_replace('country_name', $countryName, $format);

        $format = _info_format_replace('zip_code', $proposal->zip, $format);
        $format = _info_format_replace('phone', $phone, $format);
        $format = _info_format_replace('email', $email, $format);

        $whereCF = [];
        if (is_custom_fields_for_customers_portal()) {
            $whereCF['show_on_client_portal'] = 1;
        }
        $customFieldsProposals = get_custom_fields('proposal', $whereCF);

        foreach ($customFieldsProposals as $field) {
            $value  = get_custom_field_value($proposal->id, $field['id'], 'proposal');
            $format = _info_format_custom_field($field['id'], $field['name'], $value, $format);
        }

        // If no custom fields found replace all custom fields merge fields to empty
        $format = _info_format_custom_fields_check($customFieldsProposals, $format);
        $format = _maybe_remove_first_and_last_br_tag($format);

        // Remove multiple white spaces
        $format = preg_replace('/\s+/', ' ', $format);
        $format = trim($format);

        return hooks()->apply_filters('proposal_info_text', $format, ['proposal' => $proposal, 'for' => $for]);
    }
}

if (!function_exists('format_organization_info')) {
    /**
     * Format company info/address format
     * @return string
     */
    function format_organization_info()
    {
        $format = get_option('company_info_format');
        $vat    = get_option('company_vat');

        $format = _info_format_replace('company_name', '<b style="color:black" class="company-name-formatted">' . get_option('invoice_company_name') . '</b>', $format);
        $format = _info_format_replace('address', get_option('invoice_company_address'), $format);
        $format = _info_format_replace('city', get_option('invoice_company_city'), $format);
        $format = _info_format_replace('state', get_option('company_state'), $format);

        $format = _info_format_replace('zip_code', get_option('invoice_company_postal_code'), $format);
        $format = _info_format_replace('country_code', get_option('invoice_company_country_code'), $format);
        $format = _info_format_replace('phone', get_option('invoice_company_phonenumber'), $format);
        $format = _info_format_replace('vat_number', $vat, $format);
        $format = _info_format_replace('vat_number_with_label', $vat == '' ? '' : _l('company_vat_number') . ': ' . $vat, $format);

        $custom_company_fields = get_company_custom_fields();

        foreach ($custom_company_fields as $field) {
            $format = _info_format_custom_field($field['id'], $field['label'], $field['value'], $format);
        }

        $format = _info_format_custom_fields_check($custom_company_fields, $format);
        $format = _maybe_remove_first_and_last_br_tag($format);

        // Remove multiple white spaces
        $format = preg_replace('/\s+/', ' ', $format);
        $format = trim($format);

        return hooks()->apply_filters('organization_info_text', $format);
    }
}

/**
 * Return decimal places
 * The srcipt do not support more then 2 decimal places but developers can use action hook to change the decimal places
 * @return [type] [description]
 */
function get_decimal_places()
{
    return hooks()->apply_filters('app_decimal_places', 2);
}

/**
 * Get all items by type eq. invoice, proposal, estimates, credit note
 * @param  string $type rel_type value
 * @return array
 */
function get_items_by_type($type, $id)
{
    $CI = &get_instance();
    $CI->db->select();
    $CI->db->from(db_prefix() . 'itemable');
    $CI->db->where('rel_id', $id);
    $CI->db->where('rel_type', $type);
    $CI->db->order_by('item_order', 'asc');

    return $CI->db->get()->result_array();
}
/**
 * Function that update total tax in sales table eq. invoice, proposal, estimates, credit note
 * @param  mixed $id
 * @param  string $type
 * @param  string $table
 * @param  bool $is_round_number should round up/down for numbers
 * @return void
 */
function update_sales_total_tax_column($id, $type, $table, $is_round_number = false)
{
    $CI = &get_instance();
    $CI->db->select('discount_percent, discount_type, discount_total, discount_fixed, subtotal');
    $CI->db->from($table);
    $CI->db->where('id', $id);

    $data = $CI->db->get()->row();

    $items = get_items_by_type($type, $id);

    $total_tax         = 0;
    $taxes             = [];
    $_calculated_taxes = [];

    $func_taxes = 'get_' . $type . '_item_taxes';

    foreach ($items as $item) {
        $item_taxes = call_user_func($func_taxes, $item['id']);
        if (count($item_taxes) > 0) {
            foreach ($item_taxes as $tax) {
                $calc_tax     = 0;
                $tax_not_calc = false;
                if (!in_array($tax['taxname'], $_calculated_taxes)) {
                    array_push($_calculated_taxes, $tax['taxname']);
                    $tax_not_calc = true;
                }
                $amount = ($item['qty'] * $item['rate']);
                $amount -= $amount * ($item['discount_table'] ?? 0);
                $calc_tax = ($amount / 100 * $tax['taxrate']);
                $calc_tax = $is_round_number ? round($calc_tax) : $calc_tax;
                if ($tax_not_calc == true) {
                    $taxes[$tax['taxname']]          = [];
                    $taxes[$tax['taxname']]['total'] = [];
                    array_push($taxes[$tax['taxname']]['total'], $calc_tax);
                    $taxes[$tax['taxname']]['tax_name'] = $tax['taxname'];
                    $taxes[$tax['taxname']]['taxrate']  = $tax['taxrate'];
                } else {
                    array_push($taxes[$tax['taxname']]['total'], $calc_tax);
                }
            }
        }
    }

    foreach ($taxes as $tax) {
        $total = array_sum($tax['total']);
        if ($data->discount_type == 'before_tax') {
            $total_tax_calculated = ($total * $data->discount_percent) / 100 + ($total * ($data->discount_fixed / $data->subtotal) * 100) / 100;
            $total -= $is_round_number ? round($total_tax_calculated) : $total_tax_calculated;
        }
        $total_tax += $total;
    }

    $CI->db->where('id', $id);
    $CI->db->update($table, [
        'total_tax' => $total_tax,
    ]);
}

/**
 * Function used for sales eq. invoice, estimate, proposal, credit note
 * @param  mixed $item_id   item id
 * @param  array $post_item $item from $_POST
 * @param  mixed $rel_id    rel_id
 * @param  string $rel_type  where this item tax is related
 */
function _maybe_insert_post_item_tax($item_id, $post_item, $rel_id, $rel_type)
{
    $affectedRows = 0;
    if (isset($post_item['taxname']) && is_array($post_item['taxname'])) {
        $CI = &get_instance();
        foreach ($post_item['taxname'] as $taxname) {
            if ($taxname != '') {
                $tax_array = explode('|', $taxname);
                if (isset($tax_array[0]) && isset($tax_array[1])) {
                    $tax_name = trim($tax_array[0]);
                    $tax_rate = trim($tax_array[1]);
                    if (total_rows(db_prefix() . 'item_tax', [
                        'itemid' => $item_id,
                        'taxrate' => $tax_rate,
                        'taxname' => $tax_name,
                        'rel_id' => $rel_id,
                        'rel_type' => $rel_type,
                    ]) == 0) {
                        $CI->db->insert(db_prefix() . 'item_tax', [
                            'itemid'   => $item_id,
                            'taxrate'  => $tax_rate,
                            'taxname'  => $tax_name,
                            'rel_id'   => $rel_id,
                            'rel_type' => $rel_type,
                        ]);
                        $affectedRows++;
                    }
                }
            }
        }
    }

    return $affectedRows > 0 ? true : false;
}

/**
 * Add new item do database, used for proposals,estimates,credit notes,invoices
 * This is repetitive action, that's why this function exists
 * @param array $item     item from $_POST
 * @param mixed $rel_id   relation id eq. invoice id
 * @param string $rel_type relation type eq invoice
 */
function add_new_sales_item_post($item, $rel_id, $rel_type)
{
    $custom_fields = false;

    if (isset($item['custom_fields'])) {
        $custom_fields = $item['custom_fields'];
    }

    $CI = &get_instance();

    $CI->db->insert(db_prefix() . 'itemable', [
        'description'      => $item['description'],
        'long_description' => nl2br($item['long_description']),
        'qty'              => $item['qty'],
        'rate'             => number_format($item['rate'], get_decimal_places(), '.', ''),
        'rel_id'           => $rel_id,
        'rel_type'         => $rel_type,
        'item_order'       => $item['order'],
        'unit'             => $item['unit'],
        'item_id'          => $item['item_id'],
        'discount_table'   => $item['discount_table'],
    ]);

    $id = $CI->db->insert_id();

    if ($custom_fields !== false) {
        handle_custom_fields_post($id, $custom_fields);
    }

    return $id;
}

/**
 * Update sales item from $_POST, eq invoice item, estimate item
 * @param  mixed $item_id item id to update
 * @param  array $data    item $_POST data
 * @param  string $field   field is require to be passed for long_description,rate,item_order to do some additional checkings
 * @return boolean
 */
function update_sales_item_post($item_id, $data, $field = '')
{
    $update = [];
    if ($field !== '') {
        if ($field == 'long_description') {
            $update[$field] = nl2br($data[$field]);
        } elseif ($field == 'rate') {
            $update[$field] = number_format($data[$field], get_decimal_places(), '.', '');
        } elseif ($field == 'item_order') {
            $update[$field] = $data['order'];
        } else {
            $update[$field] = $data[$field];
        }
    } else {
        $update = [
            'item_order'       => $data['order'],
            'description'      => $data['description'],
            'long_description' => nl2br($data['long_description']),
            'rate'             => number_format($data['rate'], get_decimal_places(), '.', ''),
            'qty'              => $data['qty'],
            'unit'             => $data['unit'],
            'discount_table'   => $data['discount_table'],
        ];
    }

    $CI = &get_instance();
    $CI->db->where('id', $item_id);
    $CI->db->update(db_prefix() . 'itemable', $update);

    return $CI->db->affected_rows() > 0 ? true : false;
}

/**
 * When item is removed eq from invoice will be stored in removed_items in $_POST
 * With foreach loop this function will remove the item from database and it's taxes
 * @param  mixed $id       item id to remove
 * @param  string $rel_type item relation eq. invoice, estimate
 * @return boolena
 */
function handle_removed_sales_item_post($id, $rel_type)
{
    $CI = &get_instance();

    $CI->db->where('id', $id);
    $CI->db->delete(db_prefix() . 'itemable');
    if ($CI->db->affected_rows() > 0) {
        delete_taxes_from_item($id, $rel_type);

        $CI->db->where('relid', $id);
        $CI->db->where('fieldto', 'items');
        $CI->db->delete(db_prefix() . 'customfieldsvalues');

        return true;
    }

    return false;
}

/**
 * Remove taxes from item
 * @param  mixed $item_id  item id
 * @param  string $rel_type relation type eq. invoice, estimate etc.
 * @return boolean
 */
function delete_taxes_from_item($item_id, $rel_type)
{
    $CI = &get_instance();
    $CI->db->where('itemid', $item_id)
        ->where('rel_type', $rel_type)
        ->delete(db_prefix() . 'item_tax');

    return $CI->db->affected_rows() > 0 ? true : false;
}

function is_sale_discount_applied($data)
{
    return $data->discount_total > 0;
}

function is_sale_discount($data, $is)
{
    if ($data->discount_percent == 0 && $data->discount_total == 0) {
        return false;
    }

    $discount_type = 'fixed';
    if ($data->discount_percent != 0) {
        $discount_type = 'percent';
    }

    return $discount_type == $is;
}

/**
 * Get items table for preview
 * @param  object  $transaction   e.q. invoice, estimate from database result row
 * @param  string  $type          type, e.q. invoice, estimate, proposal
 * @param  string  $for           where the items will be shown, html or pdf
 * @param  boolean $admin_preview is the preview for admin area
 * @return object
 */
function get_items_table_data($transaction, $type, $for = 'html', $admin_preview = false)
{
    include_once(APPPATH . 'libraries/App_items_table.php');
    $class = new App_items_table($transaction, $type, $for, $admin_preview);

    $class = hooks()->apply_filters('items_table_class', $class, $transaction, $type, $for, $admin_preview);

    if (!$class instanceof App_items_table_template) {
        show_error(get_class($class) . ' must be instance of "App_items_template"');
    }

    return $class;
}

function sales_number_format($number, $format, $applied_prefix, $date)
{
    $originalNumber = $number;
    $prefixPadding  = get_option('number_padding_prefixes');

    if ($format == 1) {
        // Number based
        $number = $applied_prefix . str_pad($number, $prefixPadding, '0', STR_PAD_LEFT);
    } elseif ($format == 2) {
        // Year based
        $number = $applied_prefix . date('Y', strtotime($date)) . '/' . str_pad($number, $prefixPadding, '0', STR_PAD_LEFT);
    } elseif ($format == 3) {
        // Number-yy based
        $number = $applied_prefix . str_pad($number, $prefixPadding, '0', STR_PAD_LEFT) . '-' . date('y', strtotime($date));
    } elseif ($format == 4) {
        // Number-mm-yyyy based
        $number = $applied_prefix . str_pad($number, $prefixPadding, '0', STR_PAD_LEFT) . '/' . date('m', strtotime($date)) . '/' . date('Y', strtotime($date));
    }

    return hooks()->apply_filters('sales_number_format', $number, [
        'format'         => $format,
        'date'           => $date,
        'number'         => $originalNumber,
        'prefix_padding' => $prefixPadding,
    ]);
}

/**
 * Helper function to get currency by ID or by Name
 * @since  2.3.2
 * @param  mixed $id_or_name
 * @return object
 */
function get_currency($id_or_name)
{
    $CI = &get_instance();
    if (!class_exists('currencies_model', false)) {
        $CI->load->model('currencies_model');
    }

    if (is_numeric($id_or_name)) {
        return $CI->currencies_model->get($id_or_name);
    }

    return $CI->currencies_model->get_by_name($id_or_name);
}

/**
 * Get base currency
 * @since  2.3.2
 * @return object
 */
function get_base_currency()
{
    $CI = &get_instance();

    if (!class_exists('currencies_model', false)) {
        $CI->load->model('currencies_model');
    }

    return $CI->currencies_model->get_base_currency();
}

/**
 * Helper function to format number to words
 * @number  int
 * @locale string
 * @currency string
 * @return string
 */
function format_number_to_words($number, $locale = 'vietnamese', $currency = 'đồng')
{
    $locales = [
        'vietnamese' => 'vi',
        'english' => 'en',
    ];

    $numberFormat = new NumberFormatter($locales[$locale], NumberFormatter::SPELLOUT);

    return $numberFormat->format($number) . ' ' . $currency;
}

/**
 * $format =   '{company_name_vi} - {company_name_en}<br />
 *              {address_vi}<br />
 *              {address_en}<br />
 *              {invoice_email}<br />
 *              {invoice_phone}<br />
 *              {url}';
 */
function show_info_header_of_invoice($invoice)
{
    $sale_agent = get_staff($invoice->sale_agent);
    $format = '<strong>Công ty TNHH Applancer - Applancer Limited Liability</strong><br />';
    $format .= 'Tầng 13, Tòa nhà AP, 518B Điện Biên Phủ, Phường Thạnh Mỹ Tây, Thành Phố Hồ Chí Minh, Việt Nam<br />
        Level 13, AP Tower, 518B Dien Bien Phu Street, Thanh My Tay Ward, Ho Chi Minh City, Vietnam<br />';

    $format .= join(' | ', array_filter([$sale_agent->firstname . ' ' .  $sale_agent->lastname, $sale_agent->email, $sale_agent->phonenumber])) . '<br />';

    $format .= '<u>' . TOPDEV_WEBSITE . '</u>';

    $format = _maybe_remove_first_and_last_br_tag($format);

    // Remove multiple white spaces
    $format = preg_replace('/\s+/', ' ', $format);
    $format = trim($format);

    return hooks()->apply_filters('organization_info_text', $format);
}

function show_info_header_of_estimate($estimate)
{
    $sale_agent = get_staff($estimate->sale_agent);
    $format = '<strong>Công ty TNHH Applancer - Applancer Limited Liability</strong><br />';
    $format .= 'Tầng 13, Tòa nhà AP, 518B Điện Biên Phủ, Phường Thạnh Mỹ Tây, Thành Phố Hồ Chí Minh, Việt Nam<br />
        Level 13, AP Tower, 518B Dien Bien Phu Street, Thanh My Tay Ward, Ho Chi Minh City, Vietnam<br />';

    $format .= TOPDEV_WEBSITE . '<br/ >';

    if ($sale_agent) {
        $format .= 'Báo giá bởi / Proposed by: ' . join(' | ', array_filter([$sale_agent->firstname . ' ' .  $sale_agent->lastname, $sale_agent->email, $sale_agent->phonenumber]));
    }

    $format = _maybe_remove_first_and_last_br_tag($format);

    // Remove multiple white spaces
    $format = preg_replace('/\s+/', ' ', $format);
    $format = trim($format);

    return hooks()->apply_filters('organization_info_text', $format);
}

function get_header_content_invoice_info($invoice, $font_size)
{
    return '<tr>
                <td align="left" width="40%" style="color:#d34127;font-size:' . $font_size . 'px;"><h1>XÁC NHẬN ĐƠN HÀNG</h1></td>
                <td align="right" width="30%"><strong>Ngày đặt hàng<br>Date of purchase</strong></td>
                <td align="right" width="30%"><strong>Số đơn hàng<br>No. purchase order</strong></td>
            </tr>
            <tr>
                <td align="left" width="40%" style="font-size:' . $font_size . 'px;color:#d34127;"><h1 style="font-style: italic !important;">PURCHASE ORDER</h1></td>
                <td align="right" width="30%" style="">' . date('d/m/Y', strtotime($invoice->date)) . '</td>
                <td align="right" width="30%">' . format_invoice_number_v2($invoice) . '</td>
            </tr>';
}

function get_header_content_estimate_info($estimate, $font_size)
{
    return '<tr>
                <td align="left" width="40%" style="color:#d34127;font-size:' . $font_size . 'px;"><h1>BÁO GIÁ</h1></td>
                <td align="right" width="30%"><strong>Ngày báo giá<br>Proposal Date</strong></td>
                <td align="right" width="30%"><strong>Ngày hết hạn<br>Expiry Date</strong></td>
            </tr>
            <tr>
                <td align="left" width="40%" style="font-size:' . $font_size . 'px;color:#d34127;"><h1>PROPOSAL</h1></td>
                <td align="right" width="30%" style="">' . date('d/m/Y', strtotime($estimate->date)) . '</td>
                <td align="right" width="30%">' . date('d/m/Y', strtotime($estimate->expirydate ?? $estimate->date . ' + 7 days')) . '</td>
            </tr>';
}

function get_row_header_content_invoice_info($invoice)
{
    return '
    <tr>
        <td align="left" width="50%" style="border-top:1px solid #000000;"><strong>Thông tin khách hàng / Client information</strong></td>
        <td align="left" width="50%" style="border-top:1px solid #000000;"><strong>Thông tin xuất hóa đơn VAT / Billing information for VAT-invoice</strong></td>
    </tr>
    <tr>
        <td align="left" width="50%">Tên khách hàng / Client name: <b>' . $invoice->contact_name . '</b></td>
        <td align="left" width="50%">Tên công ty / Company name: <b>' .  $invoice->client->company . '</b></td>
    </tr>
    <tr>
        <td align="left" width="50%">Email: ' . $invoice->contact_email . '</td>
        <td align="left" width="50%">Địa chỉ ĐKKD/ Billing Address: ' . $invoice->client->address . '</td>
    </tr>
    <tr>
        <td align="left" width="50%">Số điện thoại / Phone number: ' . $invoice->contact_phone . '</td>
        <td align="left" width="50%">Mã số thuế/ Tax code: ' . $invoice->client->vat . '</td>
    </tr>';
}

function get_row_header_content_estimate_info($estimate)
{
    return '
    <tr>
        <td align="left" width="50%" style="border-top:1px solid #000000;"><strong>Thông tin khách hàng / Client information</strong></td>
        <td align="left" width="50%" style="border-top:1px solid #000000;"><strong>Thông tin xuất hóa đơn VAT / Billing information for VAT-invoice</strong></td>
    </tr>
    <tr>
        <td align="left" width="50%">Tên khách hàng / Client name: <b>' . $estimate->contact->fullname . '</b></td>
        <td align="left" width="50%">Tên công ty / Company name: <b>' .  $estimate->client->company . '</b></td>
    </tr>
    <tr>
        <td align="left" width="50%">Email: ' . $estimate->contact->email . '</td>
        <td align="left" width="50%">Địa chỉ ĐKKD/ Billing Address: ' . $estimate->client->address . '</td>
    </tr>
    <tr>
        <td align="left" width="50%">Số điện thoại / Phone number: ' . $estimate->contact->phonenumber . '</td>
        <td align="left" width="50%">Mã số thuế/ Tax code: ' . $estimate->client->vat . '</td>
    </tr>';
}

/**
 * Get items table for preview invoice version 2
 * @param  object  $transaction   e.q. invoice, estimate from database result row
 * @param  string  $type          type, e.q. invoice, estimate, invoice
 * @param  string  $for           where the items will be shown, html or pdf
 * @param  boolean $admin_preview is the preview for admin area
 * @return object
 */
function get_items_invoice_table_data($transaction, $type, $for = 'html', $admin_preview = false)
{
    include_once(APPPATH . 'libraries/App_items_table_invoice.php');
    $class = new App_items_table_invoice($transaction, $type, $for, $admin_preview);

    $class = hooks()->apply_filters('items_table_class_v2', $class, $transaction, $type, $for, $admin_preview);

    if (!$class instanceof App_items_table_template) {
        show_error(get_class($class) . ' must be instance of "App_items_template"');
    }

    return $class;
}

function get_items_estimate_table_data($transaction, $type, $for = 'html', $admin_preview = false)
{
    include_once(APPPATH . 'libraries/App_items_table_estimate.php');
    $class = new App_items_table_estimate($transaction, $type, $for, $admin_preview);

    $class = hooks()->apply_filters('items_table_class', $class, $transaction, $type, $for, $admin_preview);

    if (!$class instanceof App_items_table_template) {
        show_error(get_class($class) . ' must be instance of "App_items_template"');
    }

    return $class;
}

function get_row_gift($invoice)
{
    if (isset($invoice->custom_field['invoice_qua_tang']) && !empty($invoice->custom_field['invoice_qua_tang'])) {
        return '<tr>
                <td align="left" width="100%"><strong>Quà tặng miễn phí / Gift (free)</strong></td>
            </tr>
            <tr>
                <td align="left" width="100%">' . $invoice->custom_field['invoice_qua_tang'] . '</td>
            </tr>';
    }
    return '';
}
function get_row_general_features_of_Job()
{
        return '
        <tr style="background-color:#0808080f;">
            <td align="left" width="100%" style="border-top:1px solid #000000;border-bottom:1px solid #000000;">
                <strong>Quyền lợi chung các gói tin đăng trên TopDev.vn / General features of Job Posting Packages on TopDev.vn</strong>
            </td>
        </tr>
        <tr>
            <td align="left" width="100%">
                <ol>
                    <li>Thời gian đăng tuyển: 60 ngày/ tin (30 ngày chính thức + 30 ngày bảo hành). Posting period: 60 days (30 official days + 30 days warranty).</li>
                    <li>Thời gian sử dụng quyền lợi bảo lưu/ đổi tin 1 lần: 15 ngày đầu tiên kể từ ngày kích hoạt. 01 time to change job post before the 15th day of job post or post reservation for 2 months.</li>
                    <li>Cam kết chất lượng CV, tất cả CV được sàng lọc trước khi gửi sang nhà tuyển dụng (hiển thị ở Trang quản lý nhà tuyển dụng). Commitment to CV quality, all CVs are screened before sending to employers (displayed on the Employers dashboard).</li>
                    <li>Bộ phận CSKH theo dõi tình hình đăng tuyển, nhận phản hồi cụ thể chất lượng CV và đề xuất phương án hỗ trợ tin đăng kịp thời. Customer Service monitors the situation of posting, receives specific feedback of CVs quality, and proposes timely solutions.</li>
                    <li>Tin đăng đặt mua chưa dùng được bảo lưu trong 01 năm.Unused postings orders are reserved for a year.</li>
                </ol>
            </td>
        </tr>
        ';
}

/**
 * Get Term of service content of invoice
 *
 * @param $taxes
 * @return string
 */
function get_row_terms_of_service($taxes): string
{
    $vat = '0';
        foreach ($taxes as $value) {
            if ($value['total_tax'] != 0 && count($taxes) < 2) {
                $vat = app_format_number($value['taxrate']) . '%';
            }
        }
    return '<tr style="background-color:#0808080f;">
                <td align="left" width="100%" style="border-top:1px solid #000000;border-bottom:1px solid #000000;"><strong>Điều kiện sử dụng dịch vụ / Terms of Service</strong></td>
            </tr>
            <tr>
                <td align="justify" width="100%">
                    <br />1. Giá đã bao gồm ' . $vat . ' thuế VAT. <br />
                    <span style="color: #434343; font-style: italic">Price is inclusive of ' . $vat . ' VAT.</span>

                    <br />2. Khách hàng chịu mọi trách nhiệm về tính chính xác của thông tin xuất hóa đơn được nêu trong đơn hàng như Tên công ty, Địa chỉ xuất và nhận hóa đơn, Mã số thuế.<br />
                    <span style="color: #434343; font-style: italic">Customer is responsible for the accuracy of the invoice information stated in the order, such as Official Company Name, billing address and invoice, tax code.</span>

                    <br />3. Khách hàng vui lòng thanh toán đơn hàng trong vòng 1-3 ngày làm việc kể từ ngày xác nhận đơn đặt hàng, phí chuyển khoản (nếu có) do khách hàng chi trả.<br />
                    <span style="color: #434343; font-style: italic">Customers should pay the service within 1-3 working days from the date of service order confirmation. The transfer fee (if any) shall be paid by the customer.</span>

                    <br />4. Dịch vụ được kích hoạt sau khi khách hàng thanh toán đơn hàng & hóa đơn cho đơn hàng được xuất.<br />
                    <span style="color: #434343; font-style: italic">The service will be activated/ served after the customer completes the service payment & the invoice is issued.</span>

                    <br />5. Sau khi khách hàng đã xác nhận sử dụng dịch vụ, mọi yêu cầu hủy bỏ hóa đơn hoặc hoàn phí, hoặc thay đổi dịch vụ sẽ không được chấp nhận.<br />
                    <span style="color: #434343; font-style: italic">After the customer confirms to purchase service, any invoice of cancellation or refund/ service change will not be accepted.</span>

                    <br />6. Thời hạn dịch vụ:<br />
                    <span style="color: #434343; font-style: italic">Service term:</span>

                    <br />- Đối với dịch vụ tin đăng: Dịch vụ đã mua phải được kích hoạt trong vòng 12 tháng kể từ ngày xuất hóa đơn.<br />
                    <span style="color: #434343; font-style: italic">- Job posting service: The purchased service must be activated within 12 months for Enterprise packages from the date invoice is issued.</span>

                    <br />- Đối với dịch vụ tìm kiếm hồ sơ: Dịch vụ đã mua phải được kích hoạt trong vòng 12 tháng kể từ ngày xuất hóa đơn.<br />
                    <span style="color: #434343; font-style: italic">- Search CV service: The purchased service must be activated within 12 months for Enterprise packages from the date invoice is issued.</span>

                    <br />Thời gian trên không áp dụng cho các trường hợp tin đăng thuộc chương trình ưu đãi với quy định khác về thời gian sử dụng.<br />
                    <span style="color: #434343; font-style: italic">The above time does not apply to cases of job posts applied to promotional programs with different regulations on usage time.</span>

                    <br />Sau thời gian tương ứng nêu trên, bất kỳ dịch vụ nào đã mua nhưng chưa được kích hoạt sẽ không còn giá trị sử dụng nếu không có thoả thuận khác được xác nhận.<br />
                    <span style="color: #434343; font-style: italic">After corresponding above period, any service purchased but not activated will no longer be valid unless other agreements have been confirmed.</span>

                    <br />7. Thời hạn sử dụng sau khi kích hoạt dịch vụ (được áp dụng cho cả dịch vụ đặt mua và dịch vụ tặng kèm):<br />
                    <span style="color: #434343; font-style: italic">Usage period after activating the service (applicable to both service and bonus service):</span>

                    <br />- Dịch vụ đăng tin: 30 ngày đăng tin chính thức.<br />
                    <span style="color: #434343; font-style: italic">Job Posting service: 30 days for official posting.</span>

                    <br />- Quà tặng kèm: theo ghi chú quà tặng phía trên.<br />
                    <span style="color: #434343; font-style: italic">Employer Branding gift: according to note information above.</span>

                    <br />- Tìm kiếm hồ sơ: 30 ngày hoặc 90 ngày tương ứng với dịch vụ đặt mua.<br />
                    <span style="color: #434343; font-style: italic">Search CV: 30 days or 90 days corresponding to the ordered service.</span>

                    <br />8. Các cam kết dịch vụ từ TopDev:<br />
                    <span style="color: #434343; font-style: italic">Service commitment from TopDev:</span>

                    <br />8.1 Cam kết chất lượng CV: tất cả CV là CV chủ động ứng tuyển & trong lĩnh vực liên quan Công nghệ thông tin.<br />
                    <span style="color: #434343; font-style: italic">Commitment to CV quality: all CVs are CVs that are actively applied and working in Information Technology field.</span>

                    <br />8.2 Chuyên viên tư vấn phụ trách tài khoản theo dõi tình hình đăng tuyển, nhận phản hồi chất lượng CV và đề xuất phương án hỗ trợ tin đăng kịp thời để đảm bảo hiệu quả tuyển dụng.<br />
                    <span style="color: #434343; font-style: italic">Consultant in charge monitors the job posting situation, receives feedback on CVs quality and proposes timely support/ solutions to ensure recruitment efficiency.</span>

                    <br />8.3 Chính sách cam kết CV, bảo hành, hỗ trợ & gia hạn<br />
                    <span style="color: #434343; font-style: italic">8.3 CV commitment policy, warranty, support & extension.</span>
                    <br />- Số lượng CV cam kết thay đổi theo tính chất đặc thù & độ phổ biến của vị trí công việc đăng tuyển, được nhân viên tư vấn chia sẻ cụ thể trong quá trình đăng tin.<br />
                    <span style="color: #434343; font-style: italic">- The number of committed CVs varies according to the specific requirement and popularity of the job position, and is specifically shared by consultants during the recruitment process.</span>
                    <br />- Số lượng ngày gia hạn thay đổi theo tính chất vị trí và nhu cầu tuyển dụng của Khách hàng tại từng thời điểm khác nhau.<br />
                    <span style="color: #434343; font-style: italic">- The number of extension days varies depending on the nature of the position and the Customer\'s recruitment needs at different times.</span>

                    <br />9. Các điều khoản khác liên quan đến dịch vụ “Tìm kiếm hồ sơ”:<br />
                    <span style="color: #434343; font-style: italic">Other terms related to the “Search CV” service:</span>

                    <br />- Mỗi hồ sơ ứng viên được quy định tương đương một (01) lượt mở có hạn dùng tương ứng với thời hạn gói dịch vụ đang sử dụng.<br />
                    <span style="color: #434343; font-style: italic">- Each candidate profile is equivalent to one (01) unlock with an expiry date corresponding to the term of the service package being used.</span>

                    <br />- Trong cùng một thời điểm, chỉ duy nhất 01 gói dịch vụ “tìm kiếm hồ sơ” được kích hoạt cho đến khi sử dụng hết lượt mở hợp lệ.<br />
                    <span style="color: #434343; font-style: italic">- At the same time, only 01 "Search CV" service packages can be activated until all valid unlocks are used.</span>

                    <br />- Số lượng hồ sơ được mở tối đa trong một tháng: 200, bất kể số lượng gói dịch vụ đặt mua. Khi đạt mốc 200 hồ sơ đã mở trong tháng, khách hàng cần đợi đến ngày đầu tiên của tháng tiếp theo để tiếp tục sử dụng số lượt mở còn lại.<br />
                    <span style="color: #434343; font-style: italic">- Maximum number of CVs can be unlocked in a month: 200, regardless of the number of service packages ordered. When reaching the quota of 200 unlock records in a month, customers need to wait until the first day of the next month to continue using the remaining valid unlocks.</span>

                    <br />- Thời gian sử dụng gói dịch vụ sau kích hoạt là liên tục, không ngắt quãng và không được gia hạn. Các gói dịch vụ không được chuyển đổi hay gộp với gói dịch vụ mua (nếu có).<br />
                    <span style="color: #434343; font-style: italic">- The period of use of the service package after activation is continuous, without interruption and cannot be extended. Service packages cannot be converted or combined with purchased service packages (if any).</span>

                    <br />- Các hồ sơ đã mở và được lưu trữ trên hệ thống TopDev sẽ được bảo lưu trong 30 ngày kể từ ngày dịch vụ hết hạn.<br />
                    <span style="color: #434343; font-style: italic">- Unlocked & saved CVs will be saved on the employer dashboard within 30 days after service expires.</span>

                    <br />- Các biện pháp cảnh báo và hạn chế tiếp cận dữ liệu được áp dụng với các hành động đáng nghi mang rủi ro về bảo mật dữ liệu. Sau hai (02) lần cảnh báo, hành động được xác định là vi phạm bảo mật, hệ thống tiến hành các biện pháp bảo vệ dữ liệu cần thiết như khoá tài khoản và truy cập, kiểm tra nhân dạng & tài khoản sử dụng đồng thời thông báo ngừng cung cấp dịch vụ mà không tiến hành bồi hoàn dịch vụ do vi phạm trách nhiệm bảo mật thông tin.<br />
                    <span style="color: #434343; font-style: italic">- Warming measures and data access restrictions are applied to suspicious actions that pose data security risks. After two (02) warnings, the action is determined to be a security violation, the system takes necessary data protection measures such as locking accounts and access, checking identities and user accounts. At the same time, we shall notify to stop providing the service without refunding the service due to violation of information security responsibility.</span>
                </td>
            </tr>';
}

function getEstimateTermsOfService($taxes)
{
    $vat = '0';
        foreach ($taxes as $value) {
            if ($value['total_tax'] != 0 && count($taxes) < 2) {
                $vat = app_format_number($value['taxrate']) . '%';
            }
        }
    return '<tr style="background-color:#0808080f;">
                <td align="left" width="100%" style="border-top:1px solid #000000;border-bottom:1px solid #000000;"><strong>Điều khoản và điều kiện / Terms & Conditions</strong></td>
            </tr>
            <tr>
                <td align="justify" width="100%">
                    <br />1. Giá đã bao gồm ' . $vat . ' thuế VAT. <br />
                    <span style="color: #434343; font-style: italic">Price is inclusive of ' . $vat . ' VAT.</span>

                    <br />2. Phạm vi các vị trí đăng tuyển giới hạn trong ngành Công nghệ thông tin.<br />
                    <span style="color: #434343; font-style: italic">The scope of job postings is limited to the Information Technology industry.</span>

                    <br />3. Phạm vi các hồ sơ được tìm kiếm giới hạn trong ngành Công nghệ thông tin.<br />
                    <span style="color: #434343; font-style: italic">Searchable CVs in Search CV service are limited to the Information Technology industry.</span>

                    <br />4. Báo giá bao gồm chính sách chiết khấu, ưu đãi và quà tặng có hiệu lực áp dụng cho khách hàng đến hết ngày hết hạn được đề cập phía trên. Sau thời gian này, các chính sách có thể thay đổi dựa trên các chương trình khách hàng chính thức khác do TopDev áp dụng.<br />
                    <span style="color: #434343; font-style: italic">The quote includes discounts, incentives and gifts valid for customers until the expiration date mentioned above. After this time, policies may change based on other official promotions programs applied by TopDev.</span>

                    <br />5. Dịch vụ được kích hoạt sau khi khách hàng thanh toán đơn hàng & hóa đơn cho đơn hàng được xuất.<br />
                    <span style="color: #434343; font-style: italic">The service will be activated after the customer completes the payment & the invoice is issued.</span>

                    <br />6. Thời hạn dịch vụ:<br />
                    <span style="color: #434343; font-style: italic">Service term:</span>

                    <br />- Đối với dịch vụ tin đăng: Dịch vụ đã mua phải được kích hoạt trong vòng 12 tháng kể từ ngày xuất hóa đơn.<br />
                    <span style="color: #434343; font-style: italic">- Job posting service: The purchased service must be activated within 12 months for Enterprise packages from the date invoice is issued.</span>

                    <br />- Đối với dịch vụ tìm kiếm hồ sơ: Dịch vụ đã mua phải được kích hoạt trong vòng 12 tháng kể từ ngày xuất hóa đơn.<br />
                    <span style="color: #434343; font-style: italic">- Search CV service: The purchased service must be activated within 12 months for Enterprise packages from the date invoice is issued.</span>

                    <br />- Thời gian trên không áp dụng cho các trường hợp tin đăng thuộc chương trình ưu đãi với quy định khác về thời gian sử dụng.<br />
                    <span style="color: #434343; font-style: italic">- The above time does not apply to cases of job posts applied to promotional programs with different regulations on usage time.</span>

                    <br />- Sau thời gian tương ứng nêu trên, bất kỳ dịch vụ nào đã mua nhưng chưa được kích hoạt sẽ không còn giá trị sử dụng nếu không có thoả thuận khác được xác nhận. <br />
                    <span style="color: #434343; font-style: italic">- After this period, any service purchased but not activated will no longer be valid unless other agreements have been confirmed.</span>

                    <br />7. Thời hạn sử dụng sau khi kích hoạt dịch vụ (được áp dụng cho cả dịch vụ đặt mua và dịch vụ tặng kèm):<br />
                    <span style="color: #434343; font-style: italic">Usage period after activating the service (applicable to both service and bonus service):</span>

                    <br />- Dịch vụ đăng tin: 30 ngày đăng tin chính thức.<br />
                    <span style="color: #434343; font-style: italic">Job Posting service: 30 days for official posting.</span>

                    <br />- Tìm kiếm hồ sơ: 30 ngày hoặc 90 ngày tương ứng với dịch vụ đặt mua.<br />
                    <span style="color: #434343; font-style: italic">Search CV: 30 days or 90 days corresponding to the ordered service.</span>

                    <br />- Quà tặng kèm: theo ghi chú quà tặng phía trên.<br />
                    <span style="color: #434343; font-style: italic">Employer Branding gift: according to note information above.</span>

                    <br />8. TopDev cam kết chính sách giá & ưu đãi tại thời điểm báo giá là tốt nhất dành cho khách hàng theo chương trình khách hàng thân thiết & chính sách hiện hành (trừ trường hợp thay đổi thuế suất VAT theo quy định Nhà nước). Liên hệ hỗ trợ: <EMAIL>.<br />
                    <span style="color: #434343; font-style: italic">We commit that the price policy & incentives at the time of quotation are the best offer for you according to the loyalty program & current policies (except for changes in VAT rates according to State regulations). Contact support: <EMAIL>.</span>
                </td>
            </tr>';
}

function get_row_payment_info($invoice)
{
    return '<tr width="100%">
            <td></td>
            </tr>
            <tr>
                <td align="center" width="100%">
                    Khách hàng có thể thực hiện thanh toán bằng <b>tiền mặt</b> hoặc <b>chuyển khoản</b> thông qua tài khoản ngân hàng sau đây:<br>Please kindly follow the payment information below to process your payment:
                </td>
            </tr>

            <tr>
                <td align="center" width="100%" >
                    Tên Tài Khoản: Cong ty Co Phan Applancer<br>
                    Account Name: Cong ty Co Phan Applancer<br>
                    Ngân hàng: Techcombank Hồ Chí Minh - Số TK: ' . BANK_ACCOUNT_TOPDEV . '<br>
                    Bank name: Techcombank Ho Chi Minh - No. Account: ' . BANK_ACCOUNT_TOPDEV . '<br>
                    Mã Swift: ' . SWIFT_CODE . '<br>
                    Swift code: ' . SWIFT_CODE . '<br>
                    *Nội dung chuyển khoản: THANH TOAN DON HANG TUYEN DUNG ' . format_invoice_number_v2($invoice) . '<br>
                    *Note of transfer: PAYMENT NO.' . format_invoice_number_v2($invoice) . ' <br>
                </td>
            </tr>';
}

function get_row_contract_signature($invoice)
{
    $dateTimeRepresentTOPDEV = date('\N\g\à\y\ d \t\h\á\n\g\ m \n\ă\m\ Y',strtotime($invoice->date)).' / ' . date('F jS, Y',strtotime($invoice->date));
    $dateTime = 'Ngày __ tháng __ năm ____ / Date:____________';

    return  '
            <tr align="centor" width="100%">
                <td colspan=2>
                    <b>TRANG KÝ, dành cho đại diện của khách hàng và TopDev</b>
                    <br>
                    để xác nhận sử dụng dịch vụ và chấp thuận các điều kiện & điều khoản dịch vụ đã nêu.</td>
                </tr>
            <tr>
                <td colspan=2><br><br></td>
            </tr>
            <tr>
                <td align="centor" width="55%" >
                    <b>Đại diện TopDev / TopDev</b><br>' .
                    $dateTimeRepresentTOPDEV . '<br>
                    <b>' . representsInvoice($invoice->total ?? 0) . '</b>
                </td>
                <td align="centor" width="45%" >
                    <b>Đại diện Khách hàng / Customer</b><br>' .
                    $dateTime . '<br>
                </td>
            </tr>
            <tr>
                <td align="left" width="100%">' . '<br><br><br>' . '</td>
            </tr>
            <tr>
                <td align="centor" width="50%" >' . '<strong> </strong>' . '</td>
                <td align="centor" width="50%" >' . '<strong></strong>' . '</td>
            </tr>';
}

function getRowContractSignatureEstimate($estimate)
{
    $dateTimeRepresentTOPDEV = date('\N\g\à\y\ d \t\h\á\n\g\ m \n\ă\m\ Y', strtotime($estimate->date)) . ' / ' . date('F jS, Y', strtotime($estimate->date));

    return  '<tr>
                <td align="centor" width="50%">
                    <b>Đại diện TopDev / TopDev</b><br>' .
        $dateTimeRepresentTOPDEV . '<br>
                    <b>' . representsEstimate($estimate->highest_total ?? 0) . '</b>
                </td>
                <td align="centor" width="50%">
                </td>
            </tr>
            <tr>
                <td align="left" width="100%">' . '<br><br><br>' . '</td>
            </tr>';
}

function representsEstimate($value)
{
    /*
    Estimate <20tr: Trưởng nhóm Tư Vấn Tuyển dụng / Recruitment Consultant Leader
    Estimate từ 20.000.000 - 100.000.000 VNĐ:  Trưởng phòng Tư Vấn Tuyển dụng / Recruitment Consultant Manager
    Estimate dưới 200tr: Phó giám đốc / COO
    Estimate trên 200tr: Giám đốc / CEO
    */
    $represents = "";

    // if ($value < Estimate::REPRESENT_RECRUITMENT_CONSULTANT_LEADER_VALUE) {
    //     $represents = "Trưởng nhóm Tư Vấn Tuyển dụng / Recruitment Consultant Leader";
    // } elseif ($value >= Estimate::REPRESENT_RECRUITMENT_CONSULTANT_LEADER_VALUE && $value <= Estimate::REPRESENT_RECRUITMENT_CONSULTANT_MANAGER_VALUE) {
    //     $represents = "Trưởng phòng Tư Vấn Tuyển dụng / Recruitment Consultant Manager";
    // } elseif ($value > Estimate::REPRESENT_RECRUITMENT_CONSULTANT_MANAGER_VALUE && $value <= Estimate::REPRESENT_COO_VALUE) {
    //     $represents = "";
    // } else {
    //     $represents = "Giám đốc / CEO";
    // }

    return $represents;
}

function representsInvoice($value)
{
    // Similar to Estimate
    return representsEstimate($value);
}

function mappingEstimatePDFData(object $estimate): object
{
    return collect([$estimate])
        ->merge($estimate->options)
        ->map(function ($value) {
            $value->items = $value->itemables;
            $value->currency_name = $value->currencyModel->name;
            unset(
                $value->itemables,
                $value->currencyModel
            );
            return $value;
        });
}

function getEstimateOptionName(object $estimateOptions): string
{
    return $estimateOptions->pluck('description')->implode(' + ');
}

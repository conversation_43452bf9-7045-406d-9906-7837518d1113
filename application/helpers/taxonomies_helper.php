<?php

use GuzzleHttp\Client;

defined('BASEPATH') or exit('No direct script access allowed');

function get_ams_taxonomies($names)
{
    $taxonomies = file_get_contents(TAXONOMIES_JSON_FILE);
    $taxonomies = json_decode($taxonomies, true);
    $neededTaxonomies = [];
    foreach ($names as $name) {
        $neededTaxonomies[$name] = $taxonomies[$name] ?? [];
    }
    unset($taxonomies);
    return $neededTaxonomies;
}


function pull_ams_taxonomies($names)
{
    try {
        $client = new Client(
            [
                'verify'          => false,
                'allow_redirects' => true,
            ]
        );
        $taxonomies = $client->get(PUBLIC_API_URL . 'taxonomies?fields=' . implode(',', $names), [
            'headers' => [
                'Accept'        => 'application/json',
            ],
        ]);
        return json_decode($taxonomies->getBody(), true)['data'] ?? [];
    } catch (\Exception $ex) {
        throw $ex;
    }
}

function get_ams_job_statuses()
{
    return [
        [
            'value' => 'Open',
            'label' => 'Open',
        ],
        [
            'value' => 'Open *',
            'label' => 'Open *',
        ],
        [
            'value' => 'Close',
            'label' => 'Close',
        ],
        [
            'value' => 'Close *',
            'label' => 'Close *',
        ],
        [
            'value' => 'Draft',
            'label' => 'Draft',
        ],
        [
            'value' => 'Review',
            'label' => 'Review',
        ],
    ];
}

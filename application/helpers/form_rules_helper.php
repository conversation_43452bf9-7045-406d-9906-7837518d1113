<?php

use Carbon\Carbon;
use Entities\ClientAmsCompany;
use Entities\ClientAmsJob;
use Entities\Invoice;
use Entities\Itemable;

defined('BASEPATH') or exit('No direct script access allowed');

// Reference: https://codeigniter.com/userguide3/libraries/form_validation.html

function create_request_form_rules()
{
    return [
        [
            'field' => 'business_name',
            'label' => _l('client_company') . '</b>',
            'rules' => 'required'
        ],
        [
            'field' => 'vat',
            'label' => _l('client_vat_number'),
            'rules' => [
                'required',
                [
                    'request_client_valid_vat',
                    function ($value) {
                        $pattern = '/^(\d{10}|\d{10}-\d{3})$/';
                        if (preg_match($pattern, $value)) {
                            return total_rows(db_prefix() . 'clients', [
                                'vat' => $value,
                                'approved_at IS NOT NULL' => null
                            ]) == 0;
                        }
                        return false;
                    }
                ]
            ]
        ],
        [
            'field' => 'website',
            'label' => _l('client_website'),
            'rules' => 'required'
        ],
        [
            'field' => 'source_reason',
            'label' => _l('client_request_note'),
            'rules' => 'required'
        ],
        [
            'field' => 'address',
            'label' => _l('client_address'),
            'rules' => 'required'
        ],
        [
            'field' => 'source_reference_link',
            'label' => _l('client_request_reference_link'),
            'rules' => 'valid_url'
        ],
        [
            'field' => 'company_email',
            'label' => _l('client_request_company_email'),
            'rules' => [
                'valid_email',
                [
                    'request_client_unique_email',
                    function ($value) {
                        if (!empty($value)) {
                            return total_rows(db_prefix() . 'clients', ['company_email' => $value]) == 0;
                        }
                        return true;
                    }
                ]
            ]
        ]
    ];
}

if (!function_exists('contact_form_rules')) {
    function contact_form_rules()
    {
        return [
            [
                'field' => 'email',
                'rules' => [
                    'valid_email',
                ]
            ],
            [
                'field' => 'phonenumber',
                'rules' => [
                    'numeric',
                    'exact_length[10]',
                ]
            ],
            [
                'field' => 'landline',
                'rules' => [
                    'numeric'
                ]
            ],
            [
                'field' => 'ext',
                'rules' => [
                    'numeric',
                ]
            ],
        ];
    }
}

if (!function_exists('ams_post_job_form_rules')) {
    function ams_post_job_form_rules($post)
    {
        return [
            [
                'field' => 'company_id',
                'label' => '',
                'rules' => [
                    [
                        'requiredCompanyIf',
                        function ($amsCompanyId) use ($post) {
                            return (empty($post['client_ams_job_id']) && $amsCompanyId) || !empty($post['client_ams_job_id']) || !empty($post['ams_job_id']);
                        }
                    ],
                    [
                        'checkAmsIdBelongToCrmId',
                        function ($amsCompanyId) use ($post) {
                            return !$amsCompanyId || ClientAmsCompany::where(['client_id' => $post['client_id'], 'ams_company_id' => $amsCompanyId])->count() > 0;
                        }
                    ],
                ],
                'errors' => [
                    'requiredCompanyIf' => _l('ams_post_job_validate_company_required'),
                    'checkAmsIdBelongToCrmId' => _l('ams_post_job_validate_company_belong_to_client')
                ]
            ],
            [
                'field' => 'crm_invoice_id',
                'label' => '',
                'rules' => [
                    'required',
                    [
                        'checkInvoiceOutdate',
                        function ($invoiceId) use ($post) {
                            // For editing and no need to check invoice id since it's read only
                            if (!$invoiceId) {
                                return true;
                            }
                            $clientAmsJob = ClientAmsJob::where(array_merge([], empty($post['client_ams_job_id']) ? [
                                'client_id' => $post['client_id'],
                                'ams_company_id' => $post['ams_company_id'],
                                'ams_job_id' => $post['ams_job_id'],
                            ] : ['id' => $post['client_ams_job_id']]))->first();

                            if ($clientAmsJob && $clientAmsJob->invoice_id == $invoiceId) {
                                return true;
                            }
                            $CI = &get_instance();
                            $CI->load->model('Invoices_model');
                            $ids = $CI->Invoices_model->getAvailableInvoices($post['client_id'], $invoiceId);
                            return count($ids) > 0;
                        }
                    ]
                ],
                'errors' => [
                    'required' => _l('ams_post_job_validate_invoice_required'),
                    'checkInvoiceOutdate' => _l('ams_post_job_validate_invoice_out_date'),
                ]
            ],
            [
                'field' => 'package_id',
                'label' => '',
                'rules' => [
                    'required',
                    [
                        'validPaidPackage',
                        function ($paidPackageId) use ($post) {
                            // Optional field, no need to check
                            if (!$paidPackageId || empty($post['crm_invoice_id'])) {
                                return true;
                            }
                            $availableItems = Itemable::getAvailableItems($post['crm_invoice_id']);
                            // If editing, allow set previous value although it's exceed value
                            if (!empty($id = $post['client_ams_job_id'])) {
                                $availableItems->add(ClientAmsJob::whereId($id)->value('package_id') ?? 0);
                            }
                            return $availableItems->contains($paidPackageId);
                        }
                    ]
                ],
                'errors' => [
                    'required' => _l('ams_post_job_validate_package_required'),
                    'validPaidPackage' => _l('ams_post_job_validate_paid_package_valid')
                ]
            ],
            [
                'field' => 'title',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_title_required')
                ]
            ],
            [
                'field' => 'addresses_id[]',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_location_required')
                ]
            ],
            [
                'field' => 'benefitsIds[]',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_benefits_required')
                ]
            ],
            [
                'field' => 'responsibilitiesIds[]',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_responsibilities_required')
                ]
            ],
            [
                'field' => 'requirementsIds[]',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_skill_qualify_required')
                ]
            ],
            [
                'field' => 'recruitment_processesIds[]',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_recruitment_processes_required')
                ]
            ],
            [
                'field' => 'job_levels[]',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_level_required')
                ]
            ],
            [
                'field' => 'job_types[]',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_job_type_required')
                ]
            ],
            [
                'field' => 'contract_type[]',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_contract_type_required')
                ]
            ],
            [
                'field' => 'skills_ids[]',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_skill_required')
                ]
            ],
            [
                'field' => 'education_degree[]',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_invalid_education_degree')
                ]
            ],
            [
                'field' => 'education_major[]',
                'label' => '',
                'rules' => 'required',
                'errors' => [
                    'required' => _l('ams_post_job_validate_invalid_education_major')
                ]
            ],
            [
                'field' => 'currency',
                'label' => '',
                'rules' => [
                    'required'
                ],
                'errors' => [
                    'required' => _l('ams_post_job_validate_salary_type_required'),
                ]
            ],
            [
                'field' => 'salary',
                'label' => '',
                'rules' => [
                    'required'
                ],
                'errors' => [
                    'required' => _l('ams_post_job_validate_salary_required'),
                ]
            ],
            [
                'field' => 'range_from',
                'label' => '',
                'rules' => [
                    [
                        'requiredSalaryRange',
                        function ($rangeFromValue) use ($post) {
                            $salaryValue = $post['salary'];
                            $rangeToValue = $post['range_to'] ?? 0;

                            return ($salaryValue != 'range') || $rangeFromValue || $rangeToValue || $rangeFromValue == 0 || $rangeToValue == 0;
                        }
                    ],
                    [
                        'lessThanOrEqualSalaryRange',
                        function ($rangeFromValue) use ($post) {
                            $salaryValue = $post['salary'];
                            $rangeToValue = $post['range_to'] ?? 0;

                            if ($salaryValue == 'range') {
                                if ($rangeFromValue && !$rangeToValue) {
                                    return false;
                                }

                                if ($rangeFromValue && $rangeToValue && $rangeFromValue > $rangeToValue) {
                                    return false;
                                }
                            }

                            return true;
                        }
                    ]
                ],
                'errors' => [
                    'requiredSalaryRange' => _l('ams_post_job_validate_salary_required'),
                    'lessThanOrEqualSalaryRange' => _l('ams_post_job_validate_salary_value_invalid')
                ]
            ],
            [
                'field' => 'negotiable_from',
                'label' => '',
                'rules' => [
                    [
                        'requiredSalaryNegotiable',
                        function ($fromValue) use ($post) {
                            $salaryValue = $post['salary'];
                            $toValue = $post['negotiable_to'];

                            return ($salaryValue != 'negotiable') || $fromValue || $toValue || $fromValue == 0 || $toValue == 0;
                        }
                    ],
                    [
                        'lessThanOrEqualSalaryNegotiable',
                        function ($fromValue) use ($post) {
                            $salaryValue = $post['salary'];
                            $toValue = $post['negotiable_to'];

                            if ($salaryValue == 'negotiable') {
                                if ($fromValue && !$toValue) {
                                    return false;
                                }

                                if ($fromValue && $toValue && $fromValue > $toValue) {
                                    return false;
                                }
                            }

                            return true;
                        }
                    ]
                ],
                'errors' => [
                    'requiredSalaryNegotiable' => _l('ams_post_job_validate_salary_required'),
                    'lessThanOrEqualSalaryNegotiable' => _l('ams_post_job_validate_salary_value_invalid')
                ]
            ],
            [
                'field' => 'yoe_from',
                'label' => '',
                'rules' => [
                    'required',
                    [
                        'validateYoeValue',
                        function ($yoeFrom) use ($post) {
                            $yoeTo = $post['yoe_to'];
                            $taxonomies = get_ams_taxonomies(['experiences']);
                            $expOrders = array_map(fn ($exp) => $exp['id'], $taxonomies['experiences']);
                            if ($yoeTo && $yoeFrom) {
                                return array_search($yoeFrom, $expOrders) <= array_search($yoeTo, $expOrders);
                            }
                            return true;
                        }
                    ]
                ],
                'errors' => [
                    'required' => _l('ams_post_job_validate_yoe_required'),
                    'validateYoeValue' => _l('ams_post_job_validate_yoe_value_invalid')
                ]
            ],
            [
                'field' => 'job_template',
                'label' => '',
                'rules' => [
                    'required'
                ],
                'errors' => [
                    'required' => _l('ams_post_job_validate_job_template_required'),
                ]
            ],
            [
                'field' => 'job_banner',
                'label' => '',
                'rules' => [
                    'required'
                ],
                'errors' => [
                    'required' => _l('ams_post_job_validate_job_banner_required'),
                ]
            ],
            [
                'field' => 'job_template_color',
                'label' => '',
                'rules' => [
                    'required'
                ],
                'errors' => [
                    'required' => _l('ams_post_job_validate_job_template_color_required'),
                ]
            ],
        ];
    }
}

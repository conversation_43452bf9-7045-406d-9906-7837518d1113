<?php

defined('BASEPATH') or exit('No direct script access allowed');


if (!function_exists('capture_exception')) {
    function capture_exception($exception)
    {
        // Should be captured as silent mode to avoid blocking other flow
        $_error = &load_class('Exceptions', 'core');
        if (defined('SENTRY_DSN') && constant('SENTRY_DSN')) {
            try {
                \Sentry\captureException($exception);
            } catch (\Exception $ex) {
                $_error->log_exception('error', 'Sentry Exception: ' . $ex->getMessage(), $ex->getFile(), $ex->getLine());
            }
        } else {
            $_error->log_exception('error', 'Sentry Exception: ' . $exception->getMessage(), $exception->getFile(), $exception->getLine());
        }
    }
}

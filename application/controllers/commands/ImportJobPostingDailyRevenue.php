<?php

defined('BASEPATH') or exit('No direct script access allowed');

use app\services\ReportService;
use Carbon\Carbon;

class ImportJobPostingDailyRevenue extends App_Controller
{
    public function __construct()
    {
        if (!is_cli()) {
            access_denied('command');
        }
        parent::__construct();
    }

    public function run($from, $to)
    {
        $from = Carbon::createFromFormat('Ymd', $from)->startOfDay();
        $to = Carbon::createFromFormat('Ymd', $to)->endOfDay();
        $revenueStartDate = $from->clone();
        $revenueEndDate = $to->format('Ymd');
        echo "Fetching revenue range: , " . $revenueStartDate->format('Ymd') . " - $revenueEndDate" . PHP_EOL;
        do {
            $fetchDate = $revenueStartDate->format('Ymd');
            $startFrom = Carbon::createFromFormat('Ymd', $fetchDate)->startOfDay()->format('Y-m-d H:i:s');
            $endFrom = Carbon::createFromFormat('Ymd', $fetchDate)->endOfDay()->format('Y-m-d H:i:s');
            echo "$startFrom - $endFrom" . PHP_EOL;
            $importTotal = ReportService::importDailyRevenue($fetchDate);
            echo "Revenue Date: " . $revenueStartDate->format('Ymd') . ", Imported: $importTotal" . PHP_EOL;
            $revenueStartDate = $revenueStartDate->addDay();
        } while ($revenueStartDate->format('Ymd') <= $revenueEndDate);

        ReportService::adjust2024Revenues();
    }

    public function convert($startFromStr, $endToStr)
    {
        $from = Carbon::createFromFormat('Ymd', $startFromStr)->startOfDay();
        $to = Carbon::createFromFormat('Ymd', $endToStr)->endOfDay();
        $revenueStartDate = $from->clone();
        $revenueEndDate = $to->format('Ymd');
        echo "Fetching revenue range: , " . $revenueStartDate->format('Ymd') . " - $revenueEndDate" . PHP_EOL;
        do {
            $fetchDate = $revenueStartDate->format('Ymd');
            $startFrom = Carbon::createFromFormat('Ymd', $fetchDate)->startOfDay()->format('Y-m-d H:i:s');
            $endFrom = Carbon::createFromFormat('Ymd', $fetchDate)->endOfDay()->format('Y-m-d H:i:s');
            echo "$startFrom - $endFrom" . PHP_EOL;
            $importTotal = ReportService::convertExpiredPackageToRevenue($fetchDate);
            echo "Revenue Date: " . $revenueStartDate->format('Ymd') . ", Imported: $importTotal" . PHP_EOL;

            $adjustTotal = ReportService::adjustExpiredPackageToRevenue($fetchDate);
            echo "Convert Revenue Date: " . $revenueStartDate->format('Ymd') . ", Converted: $adjustTotal" . PHP_EOL;

            $convertTotal = ReportService::convertJobPublishBeforeIssueToRevenue($fetchDate);
            echo "Convert Job Published before Issue Date: " . $revenueStartDate->format('Ymd') . ", Converted: $convertTotal" . PHP_EOL;
            $revenueStartDate = $revenueStartDate->addDay();
        } while ($revenueStartDate->format('Ymd') <= $revenueEndDate);
        $this->fix_adjust_expires_to_revenue($startFromStr, $endToStr);
        ReportService::adjust2024Revenues();
        ReportService::fixRoundUpToLastRevenueRecord();
    }

    public function fix_adjust_expires_to_revenue($from, $to)
    {
        $from = Carbon::createFromFormat('Ymd', $from)->startOfDay();
        $to = Carbon::createFromFormat('Ymd', $to)->endOfDay();
        $revenueStartDate = $from->clone();
        $revenueEndDate = $to->format('Ymd');
        echo "Fetching revenue range: , " . $revenueStartDate->format('Ymd') . " - $revenueEndDate" . PHP_EOL;
        do {
            $fetchDate = $revenueStartDate->format('Ymd');
            $startFrom = Carbon::createFromFormat('Ymd', $fetchDate)->startOfDay()->format('Y-m-d H:i:s');
            $endFrom = Carbon::createFromFormat('Ymd', $fetchDate)->endOfDay()->format('Y-m-d H:i:s');
            echo "$startFrom - $endFrom" . PHP_EOL;

            $adjustTotal = ReportService::adjustExpiredPackageToRevenue($fetchDate);
            echo "Convert Revenue Date: " . $revenueStartDate->format('Ymd') . ", Converted: $adjustTotal" . PHP_EOL;

            $adjustTotal = ReportService::fixRoundToLastExpirePackage($fetchDate);
            echo "fixRoundToLastExpirePackage: " . $revenueStartDate->format('Ymd') . ", Converted: $adjustTotal" . PHP_EOL;

            $revenueStartDate = $revenueStartDate->addDay();
        } while ($revenueStartDate->format('Ymd') <= $revenueEndDate);
        ReportService::adjust2024Revenues();
    }
}

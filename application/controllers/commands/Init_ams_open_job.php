<?php

defined('BASEPATH') or exit('No direct script access allowed');

use Entities\ClientAmsOpenJob;
use Entities\Invoice;
use Entities\Itemable;
use Entities\Option;

class Init_ams_open_job extends App_Controller
{
    public function __construct()
    {
        if (!is_cli()) {
            access_denied('command');
        }
        parent::__construct();
        $this->load->model('command_model');
    }

    public function run($csvFile)
    {
        set_time_limit(0);
        $dbPrefix = db_prefix();
        $this->db->query("
            INSERT INTO ".$dbPrefix."client_ams_open_jobs(client_id,ams_company_id,ams_job_id,invoice_id,itemable_id,created_at,updated_at)
            SELECT client_id,ams_company_id,ams_job_id,invoice_id,package_id,created_at,updated_at
                FROM ".$dbPrefix."client_ams_jobs
                WHERE package_id is not null
        ");
        $csvPath = FCPATH . 'temp/' . $csvFile;
        if (file_exists($csvPath)) {
            $amsJobFile = fopen(FCPATH . 'temp/create_ams_open_job.csv', 'w');
            $skipFile = fopen(FCPATH . 'temp/skip_create_ams_open_job.csv', 'w');
            $this->writeCsvLogFile(['Line', 'ID CRM', 'Invoice # CRM', 'ID Company', 'Gói dịch vụ', 'ID Job (Đầu tiên)'], $amsJobFile);
            $this->writeCsvLogFile(['Line', 'ID CRM', 'Invoice # CRM', 'ID Company', 'Gói dịch vụ', 'ID Job (Đầu tiên)', 'Skip Code', 'Lý do skip'], $skipFile);
            if (($handle = fopen($csvPath, 'r')) !== false) {
                $paddingPrefix = Option::select('value')->where('name', 'number_padding_prefixes')->first();
                $padding = intval($paddingPrefix->value);
                $line = 0;
                while (($data = fgetcsv($handle, 1000)) !== false) {
                    $lineData = [
                        // Line
                        ($line + 1),
                        // ID CRM
                        $data[0] ?? '',
                        // Invoice # CRM
                        $data[1] ?? '',
                        // ID AMS Company
                        $data[4] ?? '',
                        // Gói dịch vụ
                        $data[7] ?? '',
                        // ID Job (Đầu tiên)
                        $data[14] ?? ''
                    ];
                    echo "Line: $line" . PHP_EOL;
                    if (
                        !empty($data[0]) && is_numeric($data[0]) &&
                        !empty($data[1]) &&
                        strpos($data[1], 'INV') > -1 &&
                        !empty($data[4]) && is_numeric($data[4]) &&
                        !empty($data[7]) &&
                        !empty($data[14]) && is_numeric($data[14])
                    ) {
                        $invoiceNumber = trim($data[1]);
                        $crmId = intval($data[0]);
                        $amsId = intval($data[4]);
                        $amsJobId = intval($data[14]);
                        $packageName = trim($data[7]);

                        $invoice = Invoice::paid()
                            ->whereRaw("
                                (
                                    CASE 
                                    WHEN `number_format` = 1 THEN CONCAT(prefix, LPAD(`number`, ?, '0'))
                                    WHEN `number_format` = 2 THEN CONCAT(prefix, DATE_FORMAT(`date`, '%Y'), '/', LPAD(`number`, ?, '0'))
                                    WHEN `number_format` = 3 THEN CONCAT(prefix, LPAD(`number`, ?, '0'), '-', DATE_FORMAT(`date`, '%y'))
                                    WHEN `number_format` = 4 THEN CONCAT(prefix, LPAD(`number`, ?, '0'), '/', DATE_FORMAT(`date`, '%m'), '/', DATE_FORMAT(`date`, '%Y'))
                                    ELSE number_format
                                    END
                                ) = ?
                            ", [$padding, $padding, $padding, $padding, $invoiceNumber])
                            ->where('clientid', $crmId)
                            ->first();

                        if ($invoice) {
                            $invoiceItem = Itemable::select('id', 'qty')
                                ->whereRelId($invoice->id)
                                ->invoiceType()
                                ->whereHas('item', fn ($builder) => $builder->whereDescription($packageName)->paidItems())
                                ->first();
                            if ($invoiceItem) {
                                // Check if open job is enough then no need to insert
                                if (ClientAmsOpenJob::where('itemable_id', $invoiceItem->id)->count() < $invoiceItem->qty) {
                                    ClientAmsOpenJob::create([
                                        'client_id' => $crmId,
                                        'ams_company_id' => $amsId,
                                        'ams_job_id' => $amsJobId,
                                        'invoice_id' => $invoice->id,
                                        'itemable_id' => $invoiceItem->id,
                                    ]);
                                    $this->writeCsvLogFile($lineData, $amsJobFile);
                                } else {
                                    $this->writeCsvLogFile(array_merge($lineData, [
                                        // Failed code
                                        'ERR_ENOUGHT_CLIENT_AMS_OPEN_JOB',
                                        // Failed reason
                                        'Không tìm thấy tên gói tương ứng'
                                    ]), $skipFile);
                                }
                            } else {
                                $this->writeCsvLogFile(array_merge($lineData, [
                                    // Failed code
                                    'ERR_NOT_FOUND_ITEMABLE',
                                    // Failed reason
                                    'Không tìm thấy tên gói tương ứng'
                                ]), $skipFile);
                            }
                        } else {
                            $this->writeCsvLogFile(array_merge($lineData, [
                                // Failed code
                                'ERR_NOT_FOUND_INVOICE',
                                // Failed reason
                                'Không tìm thấy invoice của công ty tương ứng với công ty CRM'
                            ]), $skipFile);
                        }
                    } else {
                        $this->writeCsvLogFile(array_merge($lineData, [
                            // Failed code
                            'ERR_NOT_FOUND_REQUIRED_FIELD',
                            // Failed reason
                            'Các trường bắt buộc[ID CRM, Invoice # CRM, ID Company, ID Job (Đầu tiên)] bị thiếu hoặc không đúng format'
                        ]), $skipFile);
                    }
                    // Next line
                    $line++;
                }
                fclose($handle);
            }
            fclose($amsJobFile);
            fclose($skipFile);
        } else {
            echo "File is not exists!" . PHP_EOL;
        }
    }

    private function writeCsvLogFile($data, $fileHandler)
    {
        fputcsv($fileHandler, $data);
    }
}

<?php

use Carbon\Carbon;
use Entities\Service;
use Entities\ServiceLevel1;
use Entities\ServiceLevel2;

defined('BASEPATH') or exit('No direct script access allowed');

class SyncTicketSystem extends App_Controller
{
    /**
     * Run command as
     * php index.php commands/SyncTicketSystem run [file.csv]
     */

    public const NAME_ID_MAPPING = [
        'Hà Trần' => 65,
        '<PERSON><PERSON> Nguyễn' => 108,
        '<PERSON><PERSON> Nguyễn' => 149,
        'Hiệp Lê' => 49,
        '<PERSON><PERSON> <PERSON>h<PERSON>ê<PERSON>' => 16,
        '<PERSON>h Phan' => 126,
        '<PERSON>' => 32,
        'Sale Admin' => 34,
        '<PERSON>yền <PERSON>' => 36,
        'Cs Team' => 108,
        'Phụng Đặng' => 108,
        'Ngọc Tú' => 149,
        '<PERSON>à Trần <PERSON>' => 36,
    ];

    public const PRIORITY_LEVELS = [
        'High' => 1,
        'Normal' => 2,
        'Urgent' => 4,
        'Low' => 5,
    ];

    public function __construct()
    {
        if (!is_cli()) {
            access_denied('command');
        }
        parent::__construct();
        $this->load->model('command_model');
    }

    public function run(string $csvFile)
    {
        set_time_limit(0);

        $levels1 = ServiceLevel1::get()->pluck('id', 'name');
        $levels2 = ServiceLevel2::get()->pluck('id', 'name');
        $services = Service::get()->pluck('id', 'name');

        $csvPath = FCPATH . 'temp/' . $csvFile;

        if (file_exists($csvPath)) {
            if (($handle = fopen($csvPath, 'rb')) !== false) {
                $batchSize = 1000;
                $batchData = [];
                $rowCount = 0;

                fgetcsv($handle);
                $line = 0;
                while (($row = fgetcsv($handle, 10000)) !== false) {
                    try {
                        echo "Line $line" . PHP_EOL;
                        if ($line == 0) {
                            $line++;
                            continue;
                        }
                        $line++;
                        $subject = $row[1] ?? '';
                        $status = $row[3] ?? '';
                        $note_description = $message = $row[4] ?? '';
                        $serviceText = $row[6] ?? '';
                        $level1Text = $row[7] ?? '';
                        $level2Text = $row[8] ?? '';
                        $userId = $row[9] ?? '';
                        $jobsIdText = $row[10] ?? '';
                        $caText = $row[11] ?? '';
                        $replyMessageText = $row[12] ?? '';
                        $createdAtText = $row[13] ?? '';
                        $emailText = $row[21] ?? '';
                        $priorityText = $row[24] ?? '';

                        if (empty($subject)) {
                            continue;
                        }

                        $rowCount++;
                        $status = explode(':', $status);
                        $service = $services->get($serviceText, 0);
                        $priority = self::PRIORITY_LEVELS[$priorityText] ?? 0;
                        $admin = self::NAME_ID_MAPPING[$caText] ?? 0;
                        $level1 = $levels1->get($level1Text, 0);
                        $level2 = $levels2->get($level2Text, 0);

                        $date = null;

                        if (!empty($createdAtText)) {
                            $date = Carbon::createFromFormat('d/m/Y', $createdAtText);
                        }
                        $dateCreate = Carbon::now();
                        if (!empty($replyMessageText)) {
                            $batchReplyData[] = [
                                'ticketid' => null,
                                'userid' => 0,
                                'contactid' => 0,
                                'name' => null,
                                'email' => null,
                                'date' => $dateCreate,
                                'message' => '<p>' . $replyMessageText . '</p>',
                                'attachment' => null,
                                'admin' => $admin,
                            ];
                        }
                        $batchData[] = [
                            'adminreplying' => 0,
                            'userid' => $userId ?? 0,
                            'contactid' => 0,
                            'merged_ticket_id' => null,
                            'email' => $this->replaceNewlinesAndSpaces($emailText),
                            'name' => $serviceText ?? null,
                            'department' => 5, // Customer service
                            'priority' => $priority,
                            'status' => empty($status) ? 0 : $status[0],
                            'service' => $service,
                            'ticketkey' => app_generate_hash(),
                            'subject' => $subject,
                            'message' => $message ?? null,
                            'admin' => $admin,
                            'date' => $date,
                            'project_id' => 0, // set 0
                            'lastreply' => !empty($replyMessageText) ? $dateCreate : null,
                            'clientread' => 0,
                            'adminread' => 1,
                            'assigned' => $admin,
                            'staff_id_replying' => null,
                            'cc' => null,
                            'first_level_id' => $level1,
                            'second_level_id' => $level2,
                            'type_id' => 1, // Set type_id ticket = 1
                            'job_id' => $this->extractNumbersFromString($jobsIdText),
                            'note_description' => $note_description ?? null,
                        ];

                        if ($rowCount % $batchSize === 0) {
                            echo "Import tickets " . count($batchData) . PHP_EOL;
                            $firstInsertId = $this->importBatch('tickets', $batchData);
                            $batchData = [];
                            if (!empty($batchReplyData)) {
                                foreach ($batchReplyData as $key => $reply) {
                                    $batchReplyData[$key]['ticketid'] = $firstInsertId + $key;
                                }
                                echo "Import ticket_replies " . count($batchReplyData) . PHP_EOL;
                                $this->importBatch('ticket_replies', $batchReplyData);
                                $batchReplyData = [];
                            }
                            echo "Done" . PHP_EOL;
                        }
                    } catch (\Exception $ex) {
                        dump(
                            [
                                '$subject' => $subject,
                                '$status' => $status,
                                '$note_description' => $note_description,
                                '$serviceText' => $serviceText,
                                '$level1Text' => $level1Text,
                                '$level2Text' => $level2Text,
                                '$userId' => $userId,
                                '$jobsIdText' => $jobsIdText,
                                '$caText' => $caText,
                                '$replyMessageText' => $replyMessageText,
                                '$createdAtText' => $createdAtText,
                                '$emailText' => $emailText,
                                '$priorityText' => $priorityText
                            ],
                            $row
                        );
                        echo PHP_EOL;
                        throw $ex;
                    }
                }

                if (!empty($batchData)) {
                    $this->importBatch('tickets', $batchData);
                }
                if (!empty($batchReplyData)) {
                    $this->importBatch('ticket_replies', $batchReplyData);
                }

                fclose($handle);

                echo "Data import completed successfully!" . PHP_EOL;
            } else {
                echo "Unable to open CSV file!" . PHP_EOL;
            }
        } else {
            echo "File does not exist!" . PHP_EOL;
        }
    }

    private function importBatch(string $db, array $data)
    {
        $this->db->insert_batch(db_prefix() . $db, $data);

        return $this->db->insert_id();
    }

    private function replaceNewlinesAndSpaces($input)
    {
        if (empty($input)) {
            return null;
        }
        $output = preg_replace('/[\r\n\t]+/', ',', $input);


        $output = preg_replace('/\s+/', ' ', $output);
        $output = preg_replace('/,+/', ',', $output);

        $output = trim($output, ',');

        return $output;
    }

    private function extractNumbersFromString($inputString)
    {
        if (empty($inputString)) {
            return json_encode([]);
        }
        // Step 1: Use regex to find all numbers in the input string
        preg_match_all('/[\d]+/', $inputString, $matches);

        // $matches[0] contains all the matched numbers
        $result = $matches[0];

        // Step 2: Convert the array to JSON format
        return json_encode($result);
    }
}

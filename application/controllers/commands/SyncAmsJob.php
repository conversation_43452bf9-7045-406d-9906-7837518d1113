<?php

defined('BASEPATH') or exit('No direct script access allowed');

use Entities\Ams\Job;
use Entities\ClientAmsCompany;
use Entities\ClientAmsJob;
use Entities\Invoice;
use Entities\Itemable;
use Entities\Option;

class SyncAmsJob extends App_Controller
{
    public function __construct()
    {
        if (!is_cli()) {
            access_denied('command');
        }
        parent::__construct();
        $this->load->model('command_model');
    }

    public function run($csvFile)
    {
        set_time_limit(0);
        $csvPath = FCPATH . 'temp/' . $csvFile;
        if (file_exists($csvPath)) {
            $amsJobFile = fopen(FCPATH . 'temp/amsJobFiles.csv', 'w');
            $successFile = fopen(FCPATH . 'temp/syncAmsSuccess.csv', 'w');
            $failedFile = fopen(FCPATH . 'temp/importFailedReports.csv', 'w');
            $skipFile = fopen(FCPATH . 'temp/importSkipReports.csv', 'w');
            $freePackagesName = ['Gói quà tặng kèm PO/HĐ', 'Quà tặng tháng', 'Gói quà tặng theo CT CSKH'];
            $this->writeCsvLogFile(['Line', 'ID CRM', 'Invoice # CRM', 'ID Company', 'Gói dịch vụ', 'Phân loại', 'ID Job (Đầu tiên)'], $successFile);
            $this->writeCsvLogFile(['Line', 'ID CRM', 'Invoice # CRM', 'ID Company', 'Gói dịch vụ', 'Phân loại', 'ID Job (Đầu tiên)', 'Mã lỗi', 'Mô tả lỗi'], $failedFile);
            $this->writeCsvLogFile(['Line', 'ID CRM', 'Invoice # CRM', 'ID Company', 'Gói dịch vụ', 'Phân loại', 'ID Job (Đầu tiên)'], $skipFile);
            $this->writeCsvLogFile(['Update SQL'], $amsJobFile);
            if (($handle = fopen($csvPath, 'r')) !== false) {
                $paddingPrefix = Option::select('value')->where('name', 'number_padding_prefixes')->first();
                $padding = intval($paddingPrefix->value);
                $line = 0;
                while (($data = fgetcsv($handle, 1000)) !== false) {
                    $packageType = trim($data[8] ?? '');
                    $lineData = [
                        // Line
                        ($line + 1),
                        // ID CRM
                        $data[0] ?? '',
                        // Invoice # CRM
                        $data[1] ?? '',
                        // ID Company
                        $data[4] ?? '',
                        // Gói dịch vụ
                        $data[7] ?? '',
                        // Phân loại
                        $packageType,
                        // ID Job (Đầu tiên)
                        $data[14] ?? ''
                    ];
                    echo "Line: $line" . PHP_EOL;
                    if (strtolower(($packageType)) !== strtolower('Gói dịch vụ')) {
                        // Next line
                        echo "Line: $line, skip, package type: " . $packageType . PHP_EOL;
                        $line++;
                        $this->writeCsvLogFile($lineData, $skipFile);
                        continue;
                    }
                    if (!empty($data[0]) && is_numeric($data[0]) &&
                        !empty($data[1]) &&
                        strpos($data[1], 'INV') > -1 &&
                        !empty($data[4]) && is_numeric($data[4]) &&
                        !empty($data[14]) && is_numeric($data[14])) {
                        $invoiceNumber = trim($data[1]);
                        $crmId = intval($data[0]);
                        $amsId = intval($data[4]);
                        $amsJobId = intval($data[14]);
                        $packageName = trim($data[7]);

                        $invoice = Invoice::paid()
                            ->whereRaw("
                                (
                                    CASE 
                                    WHEN `number_format` = 1 THEN CONCAT(prefix, LPAD(`number`, ?, '0'))
                                    WHEN `number_format` = 2 THEN CONCAT(prefix, DATE_FORMAT(`date`, '%Y'), '/', LPAD(`number`, ?, '0'))
                                    WHEN `number_format` = 3 THEN CONCAT(prefix, LPAD(`number`, ?, '0'), '-', DATE_FORMAT(`date`, '%y'))
                                    WHEN `number_format` = 4 THEN CONCAT(prefix, LPAD(`number`, ?, '0'), '/', DATE_FORMAT(`date`, '%m'), '/', DATE_FORMAT(`date`, '%Y'))
                                    ELSE number_format
                                    END
                                ) = ?
                            ", [$padding, $padding, $padding, $padding, $invoiceNumber])
                            ->where('clientid', $crmId)
                            ->first();
                        if ($invoice) {
                            // Default is admin if not get staff's email due to this client was moved to free db
                            $staffEmail = $invoice->client->customerAdmin->staff->email ?? '<EMAIL>';
                            $amsJob = Job::select('id')->where([
                                'id' => $amsJobId,
                                'owned_id' => $amsId
                            ])->first();
                            if ($amsJob) {
                                // Check existing client_id, ams_company_id, ams_job_id
                                $clientAmsJob = ClientAmsJob::select('id')->where([
                                    'client_id' => $crmId,
                                    'ams_company_id' => $amsId,
                                    'ams_job_id' => $amsJobId,
                                ])->first();
                                if (!$clientAmsJob) {
                                    // Sync invoice_id to ams->jobs table via update API
                                    $options = [
                                        'headers' => [
                                            'Crm-Email' => $staffEmail
                                        ]
                                    ];
                                    try {
                                        $response = [];
                                        if (empty($response['error'])) {
                                            $invoiceItem = Itemable::select('id')
                                                ->whereRelId($invoice->id)
                                                ->invoiceType()
                                                ->whereHas('item', fn ($builder) => $builder->whereDescription($packageName)->paidItems())
                                                ->first();
                                            ClientAmsCompany::upsert([
                                                [
                                                    'client_id' => $crmId,
                                                    'ams_company_id' => $amsId,
                                                ]
                                            ], ['client_id', 'ams_company_id']);
                                            ClientAmsJob::create([
                                                'client_id' => $crmId,
                                                'ams_company_id' => $amsId,
                                                'ams_job_id' => $amsJobId,
                                                'invoice_id' => $invoice->id,
                                                'package_id' => $invoiceItem->id ?? null,
                                                'free_package' => in_array($packageType, $freePackagesName)
                                            ]);
                                            $this->writeCsvLogFile($lineData, $successFile);
                                            // crm_invoice_id
                                            $this->writeCsvLogFile(['UPDATE `jobs` SET `crm_invoice_id` = '.$invoice->id.' WHERE `id` = ' . $amsJobId . ';'], $amsJobFile);
                                        } else {
                                            $this->writeCsvLogFile(array_merge($lineData, [
                                                // Failed code
                                                'ERR_IMPORT_API_ERROR',
                                                // Failed reason
                                                $response['error']
                                            ]), $failedFile);
                                        }
                                    } catch (\Exception $ex) {
                                        $this->writeCsvLogFile(array_merge($lineData, [
                                            // Failed code
                                            'ERR_IMPORT_EXCEPTION',
                                            // Failed reason
                                            $ex->getMessage()
                                        ]), $failedFile);
                                    }
                                    // Create / Update in client_ams_jobs table
                                } else {
                                    $this->writeCsvLogFile(array_merge($lineData, [
                                        // Failed code
                                        'ERR_DUPLICATE_MAP_RECORD',
                                        // Failed reason
                                        'Bị trùng ID CRM, ID Company, ID Job đã map trước đó'
                                    ]), $failedFile);
                                }
                            } else {
                                $this->writeCsvLogFile(array_merge($lineData, [
                                    // Failed code
                                    'ERR_NOT_FOUND_JOB',
                                    // Failed reason
                                    'Không tìm thấy job tương ứng với công ty AMS'
                                ]), $failedFile);
                            }
                        } else {
                            $this->writeCsvLogFile(array_merge($lineData, [
                                // Failed code
                                'ERR_NOT_FOUND_INVOICE',
                                // Failed reason
                                'Không tìm thấy invoice của công ty tương ứng với công ty CRM'
                            ]), $failedFile);
                        }
                    } else {
                        $this->writeCsvLogFile(array_merge($lineData, [
                            // Failed code
                            'ERR_NOT_FOUND_REQUIRED_FIELD',
                            // Failed reason
                            'Các trường bắt buộc[ID CRM, Invoice # CRM, ID Company, ID Job (Đầu tiên)] bị thiếu hoặc không đúng format'
                        ]), $failedFile);
                    }
                    // Next line
                    $line++;
                }
                fclose($handle);
            }
            fclose($successFile);
            fclose($failedFile);
            fclose($amsJobFile);
        } else {
            echo "File is not exists!" . PHP_EOL;
        }
    }

    private function writeCsvLogFile($data, $fileHandler)
    {
        fputcsv($fileHandler, $data);
    }
}

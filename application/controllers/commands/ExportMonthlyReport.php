<?php

defined('BASEPATH') or exit('No direct script access allowed');

use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class ExportMonthlyReport extends App_Controller
{
    public function __construct()
    {
        if (!is_cli()) {
            access_denied('command');
        }
        parent::__construct();
        $this->load->model('command_model');
        $this->load->helper('invoices_report');
    }

    public function advanced()
    {
        $issueMonth = '2025.02';
        $reportMonth = '2025.02';
        $isAdvancedAmount = 1;
        $this->export($issueMonth, $reportMonth, $isAdvancedAmount);
    }

    public function revenue02()
    {
        $issueMonth = '2025.02';
        $reportMonth = '2025.02';
        $isAdvancedAmount = 0;
        $this->export($issueMonth, $reportMonth, $isAdvancedAmount);
    }

    public function export($issueMonth, $reportMonth, $isAdvancedAmount)
    {
        $type = $isAdvancedAmount ? 'advanced' : 'revenue';
        $fileName = 'Invoice-monthly-report-' . $issueMonth . ($reportMonth ? (' - ' . $reportMonth) : '');

        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Set the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->fromArray([
            [
                'Export Date',
                Carbon::now()->format('Y-m-d H:i:s')
            ]
        ], null, 'A1');

        [$invoices, $amsJobs, $credits, $invoicePaidItems] = fetch_monthly_report_by_month($issueMonth);

        // Clone deep to avoid model is modified after ran the get_total_amount_by_month function
        $paidItems = $invoicePaidItems->map(function ($item) {
            return $item->replicate();
        });

        [
            $reports,
            $invoiceQuantity,
            $invoiceAmount,
            $advancedAmount,
            $revenueAmount
        ] = get_total_amount_by_month(
            $issueMonth,
            $invoices,
            $paidItems,
            $amsJobs,
            $credits
        );
        $reports = collect($reports);


        $details = collect();
        $detailTitle = '';
        if ($reportMonth) {
            $detailTitle = ' - ' . ($isAdvancedAmount ? _l('monthly_invoice_report_detail_advanced_amount_title') : _l('monthly_invoice_report_detail_revenue_title'));
            $details = $isAdvancedAmount
                ? get_advanced_invoice_details_by_report_month(
                    $invoices,
                    $amsJobs,
                    $credits,
                    $invoicePaidItems
                )
                : get_invoice_details_by_report_month(
                    $invoices,
                    $amsJobs,
                    $credits,
                    $invoicePaidItems,
                    $reportMonth
                );
        }

        // Summary
        $sheet->mergeCells('A2:E2');

        $headingStyles = [
            'font' => [
                'bold' => true
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ];

        /**
         * Styling
         */
        $sheet->getStyle('A2')
            ->applyFromArray($headingStyles);

        $sheet->getStyle('A3:E3')
            ->applyFromArray($headingStyles);

        $sheet->getStyle('A4:E4')
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->getStyle('A5:E5')
            ->applyFromArray($headingStyles);

        /**
         * End styling
         */
        $sheet->setCellValue('A2', strtoupper(_l('monthly_invoice_report_tab_issue_date_title')));

        // Heading
        $sheet->fromArray([
            [
                _l('monthly_invoice_report_detail_quantity_title'),
                _l('monthly_invoice_report_detail_amount_title'),
                _l('monthly_invoice_report_detail_advanced_amount_title'),
                _l('monthly_invoice_report_detail_revenue_title'),
                _l('monthly_invoice_report_detail_unpaid_title'),
            ],
            [
                $invoiceQuantity ?? '0',
                $invoiceAmount ?? '0',
                $advancedAmount ?? '0',
                $revenueAmount ?? '0',
                $unpaidAmount ?? '0',
            ]
        ], null, 'A3');

        // Purchase Order Summary
        $startAtRow = 5;
        $sheet->setCellValue('A' . $startAtRow, strtoupper(_l('monthly_invoice_report_detail_summary_heading_title')));
        // Summary
        $sheet->mergeCells('A' . $startAtRow . ':D' . $startAtRow . '');
        $sheet->getStyle('A' . $startAtRow)
            ->applyFromArray($headingStyles);

        $startAtRow++;
        $sheet->fromArray(
            [
                [
                    _l('monthly_invoice_report_detail_invoice_month_title'),
                    _l('monthly_invoice_report_detail_month_title'),
                    _l('monthly_invoice_report_detail_advanced_amount_title'),
                    _l('monthly_invoice_report_detail_revenue_title'),
                ]
            ],
            null,
            'A' . ($startAtRow)
        );
        $sheet->getStyle('A' . $startAtRow . ':D' . $startAtRow . '')
            ->applyFromArray($headingStyles);
        $startAtRow++;

        $summaryReports = $reports->sortKeys()
            ->map(function ($revenueAmount, $key) use ($issueMonth, $advancedAmount) {
                $currentMonth = Carbon::createFromFormat('Ym', $key)->format('Y.m');
                return [
                    $issueMonth == $currentMonth ? Date::PHPToExcel(Carbon::createFromFormat('Y.m', $issueMonth)->format('Y-m-01')) : '',
                    Date::PHPToExcel(Carbon::createFromFormat('Y.m', $currentMonth)->format('Y-m-01')),
                    $issueMonth == $currentMonth ? $advancedAmount : '',
                    $revenueAmount
                ];
            })
            ->values();

        $sheet->fromArray($summaryReports->toArray(), null, 'A' . $startAtRow);
        $sheet->getStyle('C' . $startAtRow)
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->fromArray($summaryReports->toArray(), null, 'A' . $startAtRow);
        $sheet->getStyle('A' . $startAtRow)
            ->getNumberFormat()
            ->setFormatCode('YYYY.MM');

        $sheet->getStyle('D' . $startAtRow . ':D' . ($startAtRow + $summaryReports->count()) . '')
            ->getNumberFormat()
            ->setFormatCode('#,##0');

        $sheet->getStyle('B' . $startAtRow . ':B' . ($startAtRow + $summaryReports->count()) . '')
            ->getNumberFormat()
            ->setFormatCode('YYYY.MM');

        $startAtRow += $summaryReports->count();

        $detailTotal = $details->count();
        if ($detailTotal) {
            // Purchase Order Details
            $sheet->setCellValue('A' . $startAtRow, strtoupper(_l('monthly_invoice_report_detail_heading_title') . $detailTitle));
            // Summary
            $sheet->mergeCells('A' . $startAtRow . ':G' . $startAtRow . '');
            $sheet->getStyle('A' . $startAtRow)
                ->applyFromArray($headingStyles);
            $startAtRow++;

            // Heading
            $sheet->fromArray([
                [
                    _l('monthly_invoice_report_detail_status_invoice_title'),
                    _l('monthly_invoice_report_job_posting_job_title'),
                    _l('monthly_invoice_report_detail_invoice_no_title'),
                    'M-minvoice No',
                    _l('monthly_invoice_report_detail_invoice_status_title'),
                    _l('monthly_invoice_report_detail_issued_date_title'),
                    _l('monthly_invoice_report_job_posting_start_title'),
                ]
            ], null, 'A' . $startAtRow);
            $sheet->getStyle('A' . $startAtRow . ':G' . $startAtRow . '')
                ->applyFromArray($headingStyles);
            $startAtRow++;

            $ranges = $details->sortBy([
                    ['issued_date', 'desc'],
                    ['posted_date', 'desc'],
                    ['invoice_id', 'desc'],
                ])
                ->values()
                ->map(function ($invoice, $index) {
                    return [
                        $index + 1,
                        $invoice['posted_date'] ? Date::PHPToExcel(Carbon::createFromFormat('Y.m.d', $invoice['posted_date'])->format('Y-m-d')) : '',
                        $invoice['invoice_no'],
                        $invoice['invoice_number'],
                        format_invoice_status($invoice['invoice_status'], '', false),
                        Date::PHPToExcel(Carbon::createFromFormat('Y.m.d', $invoice['issued_date'])->format('Y-m-d')),
                        $invoice['sale_name']
                    ];
                });

            ($isAdvancedAmount != 1) && $sheet->getStyle('B' . $startAtRow . ':B' . ($detailTotal + $startAtRow))
                ->getNumberFormat()
                ->setFormatCode('YYYY.MM.DD');

            $sheet->getStyle('F' . $startAtRow . ':F' . ($detailTotal + $startAtRow))
                ->getNumberFormat()
                ->setFormatCode('YYYY.MM.DD');

            $sheet->fromArray($ranges->toArray(), null, 'A' . $startAtRow);
        }

        $this->setAutoResizeColumns($sheet, 'A', 'G');
        $this->toExcelFile($type, $fileName, $spreadsheet);
    }

    protected function setAutoResizeColumns(&$sheet, $start, $end)
    {
        // Auto-size columns (A, B, C in this case)
        foreach (range($start, $end) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }

    protected function toExcelFile($type, $fileName, $spreadsheet)
    {
        // Create a writer to output the spreadsheet to the browser
        $writer = new Xlsx($spreadsheet);

        // Save the file to the output stream (browser)
        $writer->save(TEMP_FOLDER . $fileName . '-' . $type . '.xlsx');
        echo "Done" . PHP_EOL;
    }
}

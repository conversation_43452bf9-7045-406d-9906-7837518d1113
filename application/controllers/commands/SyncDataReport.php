<?php

use Entities\ClientAmsJob;
use Entities\Itemable;
use Entities\ReportClientAmsJob;
use Illuminate\Support\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

// handle table
class SyncDataReport extends App_Controller
{
    public function __construct()
    {
        if (!is_cli()) {
            access_denied('command');
        }
        parent::__construct();
        $this->load->model('command_model');
    }

    public function run($data = [])
    {
        Itemable::with(['invoice' => function ($query) {
            $query->select('id', 'clientid');
        }])->whereHas('invoice')->chunk(1000, function ($items) {
            foreach ($items as $item) {
                $reportInsert = [];
                for ($i = 0; $i < round($item->qty); $i++) {
                    $reportInsert[] = [
                        'client_id' => $item->invoice->clientid,
                        'ams_company_id' => 0,
                        'ams_job_id' => 0,
                        'invoice_id' => $item->rel_id,
                        'used_packages' => '{}',
                        'paid_package_id' => null,
                        'package_id' => $item->id,
                        'free_package' => 0,
                        'status_mapping' => 0,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ];
                }

                $client = ClientAmsJob::where([
                    'client_id' => $item->invoice->clientid,
                    'invoice_id' => $item->rel_id,
                    'package_id' => $item->id,
                ])->get();
                if ($client->isNotEmpty()) {
                    $total = $client->count();
                    for ($i = 0; $i < $total; $i++) {
                        $reportInsert[$i]['ams_company_id'] = $client->get($i)->ams_company_id;
                        $reportInsert[$i]['ams_job_id'] = $client->get($i)->ams_job_id;
                        $reportInsert[$i]['created_at'] = $client->get($i)->created_at;
                        $reportInsert[$i]['updated_at'] = $client->get($i)->updated_at;
                        $reportInsert[$i]['used_packages'] = empty($client->get($i)->used_packages) ? '{}' : json_encode($client->get($i)->used_packages);
                        $reportInsert[$i]['paid_package_id'] = $client->get($i)->paid_package_id;
                        $reportInsert[$i]['free_package'] = $client->get($i)->free_package;
                        $reportInsert[$i]['status_mapping'] = 1;
                    }
                }
                try {
                    ReportClientAmsJob::insert($reportInsert);
                } catch (Exception $e) {
                    log_message('ReportClientAmsJob::insert', $e->getMessage());
                }

            }
        });
        print_r('Done!');
        exit;
    }
}

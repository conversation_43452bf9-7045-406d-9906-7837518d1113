<?php

use Entities\ClientAmsOpenJob;

defined('BASEPATH') or exit('No direct script access allowed');

use Entities\ClientAmsJob;
use Entities\Invoice;
use Entities\Itemable;
use Entities\Option;

class UpdateSyncAmsJob extends App_Controller
{
    public function __construct()
    {
        if (!is_cli()) {
            access_denied('command');
        }
        parent::__construct();
        $this->load->model('command_model');
    }

    public function run($csvFile)
    {
        set_time_limit(0);
        $csvPath = FCPATH . 'temp/' . $csvFile;
        if (file_exists($csvPath)) {
            $successFile = fopen(FCPATH . 'temp/syncAmsSuccess.csv', 'w');
            $failedFile = fopen(FCPATH . 'temp/importFailedReports.csv', 'w');
            $skipFile = fopen(FCPATH . 'temp/importSkipReports.csv', 'w');
            $paidPackageName = '<PERSON>ói dịch vụ';
            $this->writeCsvLogFile(['Line', 'ID CRM', 'Invoice # CRM', 'ID Company', 'Gói dịch vụ', 'Phân loại', 'ID Job (Đầu tiên)'], $successFile);
            $this->writeCsvLogFile(['Line', 'ID CRM', 'Invoice # CRM', 'ID Company', 'Gói dịch vụ', 'Phân loại', 'ID Job (Đầu tiên)', 'Mã lỗi', 'Mô tả lỗi'], $failedFile);
            $this->writeCsvLogFile(['Line', 'ID CRM', 'Invoice # CRM', 'ID Company', 'Gói dịch vụ', 'Phân loại', 'ID Job (Đầu tiên)', 'Mã lỗi', 'Free Package', 'Mô tả lỗi'], $skipFile);
            if (($handle = fopen($csvPath, 'r')) !== false) {
                $paddingPrefix = Option::select('value')->where('name', 'number_padding_prefixes')->first();
                $padding = intval($paddingPrefix->value);
                $line = 0;
                while (($data = fgetcsv($handle, 1000)) !== false) {
                    $crmId = $data[0] ?? '';
                    $invoiceNumber = $data[1] ?? '';
                    $amsId = $data[2] ?? '';
                    $packageName = $data[5] ?? '';
                    $packageType = trim($data[6] ?? '');
                    $amsJobId = $data[9] ?? '';
                    $isFreePackage = $packageType != $paidPackageName;
                    $lineData = [
                        // Line
                        ($line + 1),
                        // ID CRM
                        $crmId,
                        // Invoice # CRM
                        $invoiceNumber,
                        // ID Company
                        $amsId,
                        // Gói dịch vụ
                        $packageName,
                        // Phân loại
                        $packageType,
                        // ID Job (Đầu tiên)
                        $amsJobId,
                    ];
                    echo "Line: $line" . PHP_EOL;
                    if (
                        !empty($crmId) && is_numeric($crmId) &&
                        !empty($invoiceNumber) && strpos($invoiceNumber, 'INV') > -1 &&
                        !empty($amsId) && is_numeric($amsId) &&
                        !empty($amsJobId) && is_numeric($amsJobId) &&
                        !empty($packageName)
                    ) {
                        $invoice = Invoice::paid()
                            ->whereRaw("
                                (
                                    CASE 
                                    WHEN `number_format` = 1 THEN CONCAT(prefix, LPAD(`number`, ?, '0'))
                                    WHEN `number_format` = 2 THEN CONCAT(prefix, DATE_FORMAT(`date`, '%Y'), '/', LPAD(`number`, ?, '0'))
                                    WHEN `number_format` = 3 THEN CONCAT(prefix, LPAD(`number`, ?, '0'), '-', DATE_FORMAT(`date`, '%y'))
                                    WHEN `number_format` = 4 THEN CONCAT(prefix, LPAD(`number`, ?, '0'), '/', DATE_FORMAT(`date`, '%m'), '/', DATE_FORMAT(`date`, '%Y'))
                                    ELSE number_format
                                    END
                                ) = ?
                            ", [$padding, $padding, $padding, $padding, $invoiceNumber])
                            ->where('clientid', $crmId)
                            ->first();
                        if ($invoice) {
                            $invoiceItem = Itemable::select('id')
                                ->whereRelId($invoice->id)
                                ->invoiceType()
                                ->whereHas('item', fn($builder) => $builder->whereDescription($packageName)->paidItems())
                                ->when($isFreePackage, fn($query) => $query->where('rate', 0))
                                ->first();

                            if ($invoiceItem) {
                                ClientAmsJob::upsert(
                                    [
                                        'client_id' => $crmId,
                                        'ams_company_id' => $amsId,
                                        'ams_job_id' => $amsJobId,
                                        'invoice_id' => $invoice->id,
                                        'package_id' => $invoiceItem->id,
                                        'free_package' => $isFreePackage,
                                    ],
                                    ['client_id', 'ams_company_id', 'ams_job_id'],
                                    ['invoice_id', 'package_id', 'free_package']
                                );
                                ClientAmsOpenJob::upsert(
                                    [
                                        'client_id' => $crmId,
                                        'ams_company_id' => $amsId,
                                        'ams_job_id' => $amsJobId,
                                        'invoice_id' => $invoice->id,
                                        'itemable_id' => $invoiceItem->id,
                                    ],
                                    ['client_id', 'ams_company_id', 'ams_job_id'],
                                    ['invoice_id', 'itemable_id']
                                );
                                if ($isFreePackage) {
                                    error_log("$amsJobId,", 3, FCPATH . 'temp/free_job.txt');
                                    $this->writeCsvLogFile($lineData, $successFile);
                                }
                            } else {
                                $this->writeCsvLogFile(array_merge($lineData, [
                                    // Failed code
                                    'ERR_NOT_FOUND_PACKAGE_RECORD',
                                    $isFreePackage ? 'yes' : 'no',
                                    // Failed reason
                                    'Không tìm thấy package này theo invoice chỉ định'
                                ]), $skipFile);

                                // Remove mapping for this job and package
                                if ($isFreePackage) {
                                    ClientAmsJob::where('ams_job_id', $amsJobId)->delete();
                                    ClientAmsOpenJob::where('ams_job_id', $amsJobId)->delete();
                                }
                            }
                        } else {
                            $this->writeCsvLogFile(array_merge($lineData, [
                                // Failed code
                                'ERR_NOT_FOUND_INVOICE',
                                // Failed reason
                                'Không tìm thấy invoice của công ty tương ứng với công ty CRM'
                            ]), $failedFile);
                        }
                    } else {
                        $this->writeCsvLogFile(array_merge($lineData, [
                            // Failed code
                            'ERR_NOT_FOUND_REQUIRED_FIELD',
                            // Failed reason
                            'Các trường bắt buộc[ID CRM, Invoice # CRM, ID Company, ID Job (Đầu tiên)] bị thiếu hoặc không đúng format'
                        ]), $failedFile);
                    }
                    // Next line
                    $line++;
                }
                fclose($handle);
            }
            fclose($successFile);
            fclose($failedFile);
            fclose($skipFile);
        } else {
            echo "File is not exists!" . PHP_EOL;
        }
    }

    private function writeCsvLogFile($data, $fileHandler)
    {
        fputcsv($fileHandler, $data);
    }
}

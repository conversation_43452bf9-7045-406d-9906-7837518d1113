<?php

defined('BASEPATH') or exit('No direct script access allowed');

class BulkAssignCA extends App_Controller
{
    public function __construct()
    {
        if (!is_cli()) {
            access_denied('command');
        }
        parent::__construct();
        $this->load->model('Clients_model');
    }

    public function run($csvFile)
    {
        set_time_limit(0);
        $csvPath = FCPATH . 'temp/' . $csvFile;
        if (file_exists($csvPath)) {
            $successFile = fopen(FCPATH . 'temp/bulk_assign_ca_success.csv', 'w');
            $failedFile = fopen(FCPATH . 'temp/bulk_assign_ca_failed.csv', 'w');
            $skipFile = fopen(FCPATH . 'temp/bulk_assign_ca_skip.csv', 'w');
            $this->writeCsvLogFile(['Line', 'ID CRM', 'Sale ID', 'Sale Name'], $successFile);
            $this->writeCsvLogFile(['Line', 'ID CRM', 'Sale ID', 'Sale Name', 'Mã lỗi', 'Mô tả lỗi'], $failedFile);
            $this->writeCsvLogFile(['Line', 'ID CRM', 'Sale ID', 'Sale Name'], $skipFile);

            if (($handle = fopen($csvPath, 'r')) !== false) {
                $line = 0;

                while (($data = fgetcsv($handle, 1000)) !== false) {
                    $crmId = $data[0];
                    $saleId = $data[1] ?? null;
                    $saleName = $data[2] ?? null;
                    echo "Line: $line, CRM ID: $crmId, Sale ID: $saleId, Sale Name: $saleName" . PHP_EOL;
                    $lineData = [
                        // Line
                        ($line + 1),
                        // ID CRM
                        $crmId ?? '',
                        // Invoice # CRM
                        $saleId ?? '',
                        // ID Company
                        $saleName ?? '',
                    ];
                    if (
                        !empty($crmId) && is_numeric($crmId)
                    ) {
                        $assignData = [
                            'customer_admins' => $saleId ? [$saleId] : [],
                            'is_sa_assigned' => 1,
                        ];
                        $success = $this->clients_model->assign_admins($assignData, $crmId);
                        if ($success) {
                            $this->writeCsvLogFile($lineData, $successFile);
                        } else {
                            $this->writeCsvLogFile(array_merge($lineData, [
                                // Failed code
                                'ERR_IMPORT_EXCEPTION',
                                // Failed reason
                                'Dont know'
                            ]), $failedFile);
                        }
                    } else {
                        $this->writeCsvLogFile(array_merge($lineData, [
                            // Failed code
                            'ERR_NOT_FOUND_REQUIRED_FIELD',
                            // Failed reason
                            'Các trường bắt buộc[ID CRM, Sale ID, Sale Name] bị thiếu hoặc không đúng format'
                        ]), $failedFile);
                    }
                    // Next line
                    $line++;
                }
                fclose($handle);
            }
            fclose($successFile);
            fclose($failedFile);
        } else {
            echo "File is not exists!" . PHP_EOL;
        }
    }

    private function writeCsvLogFile($data, $fileHandler)
    {
        fputcsv($fileHandler, $data);
    }
}

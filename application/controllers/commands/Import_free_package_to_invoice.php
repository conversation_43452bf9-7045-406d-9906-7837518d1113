<?php

defined('BASEPATH') or exit('No direct script access allowed');

use Carbon\Carbon;
use Entities\ClientAmsOpenJob;
use Entities\Invoice;
use Entities\Item;
use Entities\Itemable;
use Entities\Option;
use Entities\ReportClientAmsJob;

class Import_free_package_to_invoice extends App_Controller
{
    public function run($csvFile)
    {
        $csvPath = FCPATH . 'temp/' . $csvFile;
        if (file_exists($csvPath)) {
            if (($handle = fopen($csvPath, 'r')) !== false) {
                $paddingPrefix = Option::select('value')->where('name', 'number_padding_prefixes')->first();
                $padding = intval($paddingPrefix->value);
                $line = 0;
                $this->load->model('invoices_model');
                while (($data = fgetcsv($handle, 1000)) !== false) {
                    if ($line === 0) {
                        $line++;
                        continue;
                    }
                    $crmId = $data[0] ?? null;
                    $invoiceNumber = $data[1] ?? null;
                    $packageName = $data[6] ?? null;
                    $expiredAt = $data[9] ?? null;
                    $expired = Carbon::createFromFormat('d/m/Y', $expiredAt);

                    $invoice = Invoice::whereRaw("
                            (
                                CASE 
                                WHEN `number_format` = 1 THEN CONCAT(prefix, LPAD(`number`, ?, '0'))
                                WHEN `number_format` = 2 THEN CONCAT(prefix, DATE_FORMAT(`date`, '%Y'), '/', LPAD(`number`, ?, '0'))
                                WHEN `number_format` = 3 THEN CONCAT(prefix, LPAD(`number`, ?, '0'), '-', DATE_FORMAT(`date`, '%y'))
                                WHEN `number_format` = 4 THEN CONCAT(prefix, LPAD(`number`, ?, '0'), '/', DATE_FORMAT(`date`, '%m'), '/', DATE_FORMAT(`date`, '%Y'))
                                ELSE number_format
                                END
                            ) = ?
                        ", [$padding, $padding, $padding, $padding, $invoiceNumber])
                        ->where('clientid', $crmId)
                        ->withCount('invoiceItems')
                        ->first();

                    if ($invoice) {
                        echo "Line $line, invoice #: $invoiceNumber, invoice ID: $invoice->id" . PHP_EOL;
                        $item = Item::whereDescription($packageName)->where('group_id', Item::NEW_2024_GROUP_ID)->first();
                        if ($item) {
                            echo "Line $line, invoice #: $invoiceNumber, invoice ID: $invoice->id, item id: $item->id" . PHP_EOL;
                            $newItem = [
                                "item_id" => $item->id,
                                "order" => $invoice->invoice_items_count,
                                "description" => $item->description,
                                "long_description" => $item->long_description,
                                "qty" => 1,
                                "unit" => "tin",
                                "rate" => 0.00,
                                "taxname" => []
                            ];
                            if ($new_item_added = add_new_sales_item_post($newItem, $invoice->id, 'invoice')) {
                                _maybe_insert_post_item_tax($new_item_added, $newItem, $invoice->id, 'invoice');
                                $this->invoices_model->log_invoice_activity($invoice->id, 'invoice_estimate_activity_added_item', false, serialize([
                                    $newItem['description'],
                                ]));
                                Itemable::whereId($new_item_added)->update(['use_expired_at' => $expired->format('Y-m-d')]);
                                ReportClientAmsJob::insert([
                                    'client_id' => $crmId,
                                    'ams_company_id' => 0,
                                    'ams_job_id' => 0,
                                    'invoice_id' => $invoice->id,
                                    'used_packages' => '{}',
                                    'paid_package_id' => null,
                                    'package_id' => $new_item_added,
                                    'free_package' => 1,
                                    'status_mapping' => 0,
                                    'created_at' => Carbon::now(),
                                    'updated_at' => Carbon::now(),
                                ]);
                            } else {
                                echo "Line $line, invoice #: $invoiceNumber, package $packageName add item failed" . PHP_EOL;
                            }
                        } else {
                            echo "Line $line, invoice #: $invoiceNumber, package $packageName not found" . PHP_EOL;
                        }
                    } else {
                        echo "Line $line, invoice #: $invoiceNumber not found" . PHP_EOL;
                    }

                    $line++;
                }
            }
        } else {
            echo "File $csvPath is not exists!" . PHP_EOL;
        }
    }
}

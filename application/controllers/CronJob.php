<?php defined('BASEPATH') or exit('No direct script access allowed');

class CronJob extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->helper(array('form', 'url'));
        $this->load->library("pagination");
    }

    function resetStatusFavorite()
    {
        if (is_cli()) {
            $this->load->model('cron_model');
            $this->cron_model->resetStatusFavorite();
        }
    }

    // function syncData3cCall($time = 0, $from = '', $to = '')
    // {
    //     $this->load->model('Customer_admins_model');
    //     if ($time || ($from && $to)) {
    //         $this->load->helper('call_center');
    //         $data = getCallLogs($time, $from, $to);

    //         if (!empty($data) && !empty($data->calls)) {
    //             $this->load->model('Call_center_model');
    //             $calls = $data->calls;
    //             $this->Call_center_model->syncCallLogs($calls);
    //         }

//            $this->db
//                ->where('contacted', $this->Customer_admins_model->STATUS_WAITING)
//                ->where("max between DATE_FORMAT(NOW(),'%Y-%m-%d') and admin.expired_at")
//                ->set([
//                    'contacted' => $this->Customer_admins_model->STATUS_CONTACTED,
//                    'expired_at' => date('Y-m-d H:i:s', strtotime('+60 days'))
//                ])
//                ->update(
//                    db_prefix() . 'customer_admins admin
//                        join ' . db_prefix() . 'staff staff on admin.customer_id = staff.staffid
//                        join (
//                            select agent_id, max(start_time) max
//                            from ' . db_prefix() . '3c_call
//                            where call_status != "miss"
//                            group by agent_id
//                        ) 3c on 3c.agent_id = staff.ipphone'
//                );
    //     }
    // }
}

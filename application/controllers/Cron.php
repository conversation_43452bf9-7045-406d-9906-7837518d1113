<?php

use app\services\CallioService;
use app\services\ReportService;
use Carbon\Carbon;
use Entities\M3cCall;
use Entities\M3cCallLogFailedJob;
use Entities\Client;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Cron_model $cron_model
 */
class Cron extends App_Controller
{
    public function index($key = '')
    {
        update_option('cron_has_run_from_cli', 1);

        if (defined('APP_CRON_KEY') && (APP_CRON_KEY != $key)) {
            header('HTTP/1.0 401 Unauthorized');
            die('Passed cron job key is not correct. The cron job key should be the same like the one defined in APP_CRON_KEY constant.');
        }

        $last_cron_run                  = get_option('last_cron_run');
        $seconds = hooks()->apply_filters('cron_functions_execute_seconds', 300);

        if ($last_cron_run == '' || (time() > ($last_cron_run + $seconds))) {
            $this->load->model('cron_model');
            $this->cron_model->run();
        }
    }

    /**
     * Get Staff statistics based on range. Assume this command will be ran at 0h everyday
     * @param datetime $from from datetime
     * @param datetime $to to datetime
     * @return void
     */
    public function getStaffStatistics($from = '', $to = '')
    {
        if (empty($from) && empty($to)) {
            $from = $to = date('Y-m-d', strtotime('-1 day'));
        } elseif (empty($to)) {
            $to = $from;
        }
        $this->load->model('Staff_statistics_model');
        $this->Staff_statistics_model->getNoteContacted($from, $to);
    }

    public function pullTaxonomies()
    {
        if (is_cli()) {
            $retry = 3;
            do {
                try {
                    // get all taxonomies from API
                    $fields = [
                        'skills',
                        'extra_skills',
                        'job_types',
                        'job_levels',
                        'industries',
                        'nationalities',
                        'experiences',
                        'num_employees',
                        'status_works',
                        'salary_range',
                        'bottom_cv_questions',
                        'language_levels',
                        'languages',
                        'schools',
                        'contract_types',
                        'categories',
                        'positions',
                        'qa_skills',
                        'services',
                        'packages',
                        'role',
                        'responsibilities',
                        'requirements',
                        'recruitment_processes',
                        'job_banner',
                        'job_template',
                        'job_template_color',
                        'education',
                        'education_major',
                        'benefits',
                    ];
                    $taxonomies = pull_ams_taxonomies($fields);
                    file_put_contents(TAXONOMIES_JSON_FILE, json_encode($taxonomies, JSON_UNESCAPED_UNICODE));
                    echo "Done!";
                    return;
                } catch (\Exception $ex) {
                    log_message('error', $ex->getTraceAsString());
                    $retry -= 1;
                }
            } while ($retry > 0);
        }
    }

    public function update_client_type_of_customer_value()
    {
        echo date("Y-m-d H:i:s") . PHP_EOL;
        $this->db->query('
            update tblclients c
            join (
                select
                    cc.userid, count(DISTINCT i.id) as i_total, CASE
                        WHEN max(p.`date`) IS NOT NULL THEN max(p.`date`)
                        ELSE max(i.`date`)
                    END as max_date
                from
                    tblclients cc
                    left join tblinvoices i on i.clientid = cc.userid
                    and i.status = 2
                    left join tblinvoicepaymentrecords p on p.invoiceid = i.id
                    and p.`date` >= \'2020-01-01\'
                GROUP BY
                    cc.userid
            ) as cl on c.userid = cl.userid
            SET
                type_of_customer = (
                    CASE
                        WHEN (cl.max_date IS NOT NULL AND (YEAR(NOW()) - YEAR(cl.max_date)) <= 1 AND cl.i_total = 1) THEN "NEW"
                        WHEN (cl.max_date IS NOT NULL AND (YEAR(NOW()) - YEAR(cl.max_date)) <= 1 AND cl.i_total > 1 ) THEN "RETURN"
                        WHEN cl.i_total > 0 THEN "CHURN"
                        ELSE "FRESH"
                    END
                )
            where
                c.userid > 0;
        ');
        echo date("Y-m-d H:i:s") . PHP_EOL;
    }

    /**
     * Sync callio transcript from Callio. Run at 8:00/18:00 daily
     *
     * @return void
     */
    public function sync_callio_transcripts()
    {
        M3cCall::query()
            ->select('call_id', 'transcripts')
            ->where('vendor', M3cCall::CALL_LOG_VENDOR_CALLIO)
            ->notSyncTranscriptYet()
            ->orderBy('id')
            ->lazy()
            ->each(function ($callLog) {
                try {
                    $call = CallioService::callioApi('call/' . $callLog->call_id, [], 'get');
                    if (!empty($call['transcripts'])) {
                        $call->transcripts = $call['transcripts'];
                        $call->transcript_synced = 1;
                        $call->save();
                    }
                } catch (\Exception $ex) {
                    _exception_handler($ex);
                }
            });
    }

    /**
     * Sync callio transcript from Callio. Run at 8:00/18:00 daily
     *
     * @return void
     */
    public function sync_callio_call_logs($from = null, $to = null)
    {
        try {
            $this->load->model('Call_center_model');
            $from = $from ? Carbon::parse($from) : Carbon::now()->subDays(2)->startOfDay();
            $to = $to ? Carbon::parse($to) : Carbon::now();
            $hasNextPage = true;
            $page = 1;
            do {
                [$calls, $hasNextPage] = CallioService::getCallLogs($from->getTimestampMs(), $to->getTimestampMs(), $page);
                $calls = collect($calls);
                $callIds = $calls->pluck('id');
                $ids = M3cCall::whereIn('call_id', $callIds->toArray())->pluck('call_id');
                if ($ids->count()) {
                    $calls = $calls->filter(fn($call) => !in_array($call['id'], $ids->toArray()));
                }

                $callLogs = [];

                $calls->each(function ($callLog) use (&$callLogs) {
                    $isCallOut = $callLog['direction'] == 'outbound';
                    $callLogs[] = json_decode(
                        json_encode(
                            [
                                'agent_id' => $isCallOut ? $callLog['fromExt'] : $callLog['toExt'],
                                'user_id' => $isCallOut ? ($callLog['fromUser']['_id'] ?? ($callLog['fromUser'] ?? '')) : ($callLog['toUser']['_id'] ?? $callLog['fromUser']['_id'] ?? ($callLog['fromUser'] ?? '')),
                                'id' => $callLog['id'],
                                'call_id' => $callLog['id'],
                                'path' => '',
                                'path_download' => '',
                                'customer_id' => 0,
                                'caller' => $callLog['fromNumber'],
                                'called' => $callLog['toNumber'],
                                'group_id' => $isCallOut ? ($callLog['fromGroup']['_id'] ?? ($callLog['fromGroup'] ?? '')) : ($callLog['toGroup']['_id'] ?? ($callLog['toGroup'] ?? '')),
                                'call_type' => $isCallOut ? 1 : 2,
                                'wait_time' => gmdate("H:i:s", $callLog['duration'] - $callLog['billDuration']),
                                'hold_time' => '00:00:00',
                                'talk_time' => gmdate("H:i:s", $callLog['billDuration']),
                                'end_status' => $callLog['hangupCause'],
                                'ticket_id' => 0,
                                'last_user_id' => 0,
                                'last_agent_id' => 0,
                                'call_survey' => '',
                                'start_time' => Carbon::createFromTimestampMs($callLog['startTime'])->format('Y-m-d H:i:s'),
                                'end_time' => Carbon::createFromTimestampMs($callLog['endTime'])->format('Y-m-d H:i:s'),
                                'call_status' => $callLog['hangupCause'] != M3cCall::CALL_IN_NORMAL_CALL ? 'miss' : 'meetAgent',
                                'vendor' => M3cCall::CALL_LOG_VENDOR_CALLIO,
                                'transcripts' => $callLog['transcripts'],
                                'transcript_synced' => count($callLog['transcripts']) ? 1 : 0,
                            ]
                        )
                    );

                    if (count($callLogs) % 10 == 0) {
                        $this->Call_center_model->syncCallLogs($callLogs);
                        $callLogs = [];
                    }
                });
                if (count($callLogs)) {
                    $this->Call_center_model->syncCallLogs($callLogs);
                    $callLogs = [];
                }
                $page++;
            } while ($hasNextPage);
        } catch (\Exception $ex) {
            _exception_handler($ex);
        }
    }

    public function import_daily_revenue()
    {
        if (is_cli()) {
            ReportService::importDailyRevenue();
            ReportService::adjust2024Revenues();
        }
    }

    public function convert_expires_to_daily_revenue()
    {
        if (is_cli()) {
            ReportService::convertExpiredPackageToRevenue();
            ReportService::adjustExpiredPackageToRevenue();
            ReportService::convertJobPublishBeforeIssueToRevenue();
            ReportService::adjust2024Revenues();
            ReportService::fixRoundToLastExpirePackage();
            ReportService::fixRoundUpToLastRevenueRecord();
        }
    }

    /**
     * Some invoice is signed but tax agency does not process to generate code yet.
     * Need to push to queue if does not have this and then send to customer after that
     * @return void
     */
    public function send_email_issue_invoice()
    {
        $this->load->model('cron_model');
        // For checking and send email to the invoice that is not ready for sending 
        // due to the invoice is not generate code by tax agency
        $this->cron_model->sendEmailIssueInvoice();
    }
}

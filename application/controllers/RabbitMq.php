<?php

defined('BASEPATH') or exit('No direct script access allowed');

class RabbitMq extends App_Controller
{
    public function index()
    {
        if (is_cli()) {
            $lib = $this->load->library('rabbitmq/RabbitMqDbChange');
            $lib->rabbitmqdbchange->watch();
        }
    }

    /**
     * Perform testing
     */
    public function test()
    {
        if (is_cli() && 'production' !== ENVIRONMENT) {
            $lib = $this->load->library('elasticsearch/UpdateEs');
            $testJson = file_get_contents(FCPATH . 'temp/test_db_changes.json') ?? '{}';
            $list = json_decode($testJson, true);
            foreach ($list as $body) {
                $lib->updatees->execute($body);
            }
        }
    }
}

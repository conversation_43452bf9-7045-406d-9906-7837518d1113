<?php

use <PERSON><PERSON>\Crypto\Rsa\KeyPair;

defined('BASEPATH') or exit('No direct script access allowed');

class ApiKey extends App_Controller
{
    public function key_gen()
    {
        [$privateKey, $publicKey] = (new KeyPair(OPENSSL_ALGO_SHA512, 512))->generate();
        echo "Private key: " . base64_encode($privateKey) . PHP_EOL;
        echo "Public key: " . base64_encode($publicKey) . PHP_EOL;
    }
}

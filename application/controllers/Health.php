<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Health extends App_Controller
{
    /**
     * livenessProbe
     */
    public function index()
    {
        $this->success('OK',
            ['ip_address' => $this->input->ip_address()]
        );
    }

    /**
     * readinessProbe
     */
    public function readiness()
    {
        $this->success([
            'OK',
            'elasticsearch' => es_client()->cluster()->health(),
        ]);
    }

    protected function success($message, $data = [])
    {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }

    public function error($message, $code = null)
    {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $message,
            'code' => $code,
        ]);
    }


}


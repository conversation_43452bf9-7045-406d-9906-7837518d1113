<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Es extends App_Controller
{
    public const COMMANDS = [
        'createIndice' => [
            'title' => 'Create elasticsearch indices',
            'args' => [
                [
                    'key' => '[Indice Name]',
                    'hint' => 'name of the indice will be created sperator by ":", default all indices',
                ]
            ],
            'example' => [
                'php index.php es createIndice',
                'php index.php es createIndice crm_clients:crm_staff',
            ]
        ],
        'updateIndice' => [
            'title' => 'Update mapping of exsiting indices',
            'args' => [
                [
                    'key' => '[indices name]',
                    'hint' => 'name of the indice will be updated sperator by ":", default all indices',
                ]
            ],
            'example' => [
                'php index.php es updateIndice',
                'php index.php es updateIndice crm_clients:crm_staff'
            ]
        ],
        'prepareIndexAll' => [
            'title' => 'Prepare .txt file before index',
            'args' => [
                [
                    'key' => '[Table]',
                    'hint' => 'Table will be indexed sperator by ":", available table values:
                        Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
                        Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
                        Tag, Task, Ticket, InvoicePaymentRecord. Default is all',
                ],
                [
                    'key' => '[File prefix]',
                    'hint' => 'Prefix of the file will be created. Ex: idx_client_001.txt. Default is idx',
                ],
                [
                    'key' => '[Chunk size]',
                    'hint' => 'Number of id for each files, default is 1000',
                ],
            ],
            'example' => [
                'php index.php es prepareIndexAll',
                'php index.php es prepareIndexAll all idx 500'
            ]
        ],
        'prepareIndexTable' => [
            'title' => 'Create .txt for specify table before index',
            'args' => [
                [
                    'key' => '[Table]',
                    'hint' => 'Table will be indexed, available table values:
                        Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
                        Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
                        Tag, Task, Ticket, InvoicePaymentRecord',
                ],
                [
                    'key' => '[File prefix]',
                    'hint' => 'Prefix of the file will be created. Ex: idx_client_001.txt. Default is idx_',
                ],
                [
                    'key' => '[Chunk size]',
                    'hint' => 'Number of id for each files, default is 1000',
                ],
            ],
            'example' => [
                'php index.php es prepareIndexTable Client',
                'php index.php es prepareIndexTable Client:Contact 500',
            ]
        ],
        'indexEsAllFiles' => [
            'title' => 'Pick all files that generated to index',
            'args' => [
                [
                    'key' => '[Table]',
                    'hint' => 'Table will be indexed sperator by ":", available table values:
                        Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
                        Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
                        Tag, Task, Ticket, InvoicePaymentRecord. Default is "all"',
                ],
                [
                    'key' => '[File prefix]',
                    'hint' => '.txt prefix file use to lookup',
                ],
            ],
            'example' => [
                'php index.php es indexEsAllFiles Client:Contact'
            ]
        ],
        'indexEsFile' => [
            'title' => 'Pick file that include id to index after file is created',
            'args' => [
                [
                    'key' => '[Table]',
                    'hint' => 'Table will be indexed, available table values:
                        Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
                        Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
                        Tag, Task, Ticket, InvoicePaymentRecord',
                ],
                [
                    'key' => '[File name]',
                    'hint' => '.txt file that created after run command prepare sperator by ":"',
                ],
            ],
            'example' => [
                'php index.php es indexEsFile Client client01.txt:client02.txt'
            ]
        ],
        'indexEs' => [
            'title' => 'Index data by record id and table',
            'args' => [
                [
                    'key' => '[Table]',
                    'hint' => 'Table will be indexed, available table values: Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
                        Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
                        Tag, Task, Ticket, InvoicePaymentRecord',
                ],
                [
                    'key' => '[Record id]',
                    'hint' => 'record id, sperator by ":"',
                ]
            ],
            'example' => [
                'php index.php es indexEs Client 1:2:3:4'
            ]
        ],
    ];

    public function __construct()
    {
        parent::__construct();
        if (!is_cli()) {
            exit("\033[0;31mIt can be ran via command line only!\033[0m" . PHP_EOL);
        }
    }

    public function index()
    {
        foreach (self::COMMANDS as $key => $command) {
            echo "\033[0;33m" . str_pad($key, 24) . "\033[0m" . $command['title'] . PHP_EOL;
            echo "\033[0;32mArgs:\033[0m" . PHP_EOL;
            $args = $command['args'];
            foreach ($args as $arg) {
                echo str_pad("", 2);
                echo "\033[0;32m" . str_pad($arg['key'], 5) . "\033[0m\t\t" . $arg['hint'];
                echo PHP_EOL;
                echo PHP_EOL;
            }
            echo "\033[0;32mExamples:\033[0m" . PHP_EOL;
            echo str_pad("", 2);
            echo "\033[0;32m" . implode(PHP_EOL . "\t", $command['example']) . "\033[0m";
            echo PHP_EOL;
        }
    }

    /**
     * Create indice by name, it can create multiple indices
     * @param string $indiceName name of the indice
     * @return void
     */
    public function createIndice($indiceName = 'all')
    {
        $args = func_get_args();
        $command = __FUNCTION__;
        $this->run($command, $args);
    }

    /**
     * Update mapping of the existing indice
     * @param string $indiceName name of the indice
     * @return void
     */
    public function updateIndice($indiceName = 'all')
    {
        $args = func_get_args();
        $command = __FUNCTION__;
        $this->run($command, $args);
    }

    /**
     * Make .txt file that include ids of the table will be indexed
     * @param string $table table want to prepared, valid value are:
     *    Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
     *    Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
     *    Tag, Task, Ticket. Default is all
     * @param string $prefix prefix of the file will be created, default is "idx". Example idx_clients_1.txt
     * @param integer $chunkSize Record per file, default is 1000
     * @return void
     */
    public function prepareIndexAll($table = 'all', $prefix = 'idx', $chunkSize = 1000)
    {
        $args = func_get_args();
        $command = __FUNCTION__;
        $this->run($command, $args);
    }

    /**
     * Perform get ids and write id to the specific .txt file based on the table
     * @param string $table table will be prepared, valid value are:
     *    Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
     *    Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
     *    Tag, Task, Ticket.
     * @param string $prefix prefix of the file, default is 'idx'. Example idx_client_1.txt
     * @param integer $chunkSize Record per file, default is 1000
     * @return void
     */
    public function prepareIndexTable($table, $prefix = 'idx', $chunkSize = 1000)
    {
        $args = func_get_args();
        $command = __FUNCTION__;
        $this->run($command, $args);
    }

    /**
     * Get all file based on table and then perform index
     * @param string $table table will be indexed, valid value are:
     *    Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
     *    Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
     *    Tag, Task, Ticket. Default is 'all'
     * @param string $prefix prefix of the created file
     * @return void
     */
    public function indexEsAllFiles($table = 'all', $prefix = 'idx')
    {
        $args = func_get_args();
        $command = __FUNCTION__;
        $this->run($command, $args);
    }

    /**
     * Get prepared file and then perform index data for the table. 5 processes are ran at the same time
     * @param string $table table will be indexed, valid value are:
     *    Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
     *    Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
     *    Tag, Task, Ticket.
     * @param string $fileName name of the file will be read for indexing, sperator by ":"
     * @return void
     */
    public function indexEsFile($table, $fileName)
    {
        $args = func_get_args();
        $command = __FUNCTION__;
        $this->run($command, $args);
    }

    /**
     * Perform index es by ids
     * @param string $table table will be indexed, valid value are:
     *    Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
     *    Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
     *    Tag, Task, Ticket.
     * @param string $idStr id will be indexed, sperate by ":"
     * @return void
     */
    public function indexEs($table, $idStr)
    {
        $args = func_get_args();
        $command = __FUNCTION__;
        $this->run($command, $args);
    }

    protected function run($command, $args)
    {
        $lib = $this->load->library('elasticsearch/Elasticsearch');
        $lib->elasticsearch->run($command, $args);
    }
}

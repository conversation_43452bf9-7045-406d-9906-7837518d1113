<?php

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

class Crawler extends AdminController
{
    public function index()
    {
        if (!has_permission('jobs_crawler', '', 'view')) {
            access_denied('jobs_crawler');
        }

        $this->load->helper('crawler');

        $data = [
            'title' => _l('als_job_crawler'),
            'today' => Carbon::now()->format('Y-m-d')
        ];
        $this->load->view('admin/crawler/jobs_crawler', $data);
    }

    public function table()
    {
        $this->load->helper('crawler');
        $this->app->get_table_data('jobs_crawler_table');
    }
}

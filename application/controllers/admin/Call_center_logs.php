<?php

use app\services\CallioService;
use Carbon\Carbon;
use Entities\M3cCall;

defined('BASEPATH') or exit('No direct script access allowed');

class Call_center_logs extends AdminController
{
    public function index()
    {
        if (!has_permission('call_center_logs', '', 'view')) {
            access_denied('call_center_logs');
        }

        $this->load->helper('call_center');
        $data = [
            'staff' => get_list_staff(),
            'min_date' => M3cCall::min('start_time') ?? Carbon::now()->startOfDay()->format('Y-m-d H:i:s'),
        ];
        $this->load->helper('call_center');
        $this->load->view('admin/call_center_logs/call_center_logs', $data);
    }

    public function table()
    {
        $this->app->get_table_data('call_center_logs');
    }

    public function statistic_table()
    {
        $this->load->helper('call_center');
        $this->app->get_table_data('call_center_statistics');
    }

    public function voice_record(string $callId)
    {
        $callLog = M3cCall::where('call_id', $callId)->first();

        if ($callLog && $callLog->talk_time != '00:00:00') {
            $call = CallioService::callioApi('call/' . $callId . '/recording', [], 'GET');
            if (isset($call['success']) && $call['success']) {
                redirect($call['url']);
            }
        }

        set_alert('danger', _l('call_center_logs_not_found_voice_records'));
        redirect(admin_url('call_center_logs'));
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Ranking extends AdminController
{
    public $team_sales;

    public function __construct()
    {
        parent::__construct();
        $this->load->helper(array('client_requests'));
        $this->team_sales = get_group_team_id();
        $this->load->model('dashboard_model');

        if (!has_permission('kpi', '', 'edit')) {
            access_denied('staff');
        }
    }



    public function kpi()
    {
        $data = [];
        $data['title'] = _l('setting_kpi_title');

        $this->load->view('admin/dashboard/kpi', $data);
    }


    public function reponse_error($mess, $field = '')
    {
        print_r(json_encode(array(
            'error' => 1,
            'mess' => $mess,
            'field' => $field
        )));
        die;
    }

    public function reponse_json($mess)
    {
        print_r(json_encode(array(
            'error' => 0,
            'mess' => $mess
        )));
        die;
    }

    public function confirm_kpi()
    {
        $data = $this->input->post();

        if (!$data['month_kpi']) {
            $this->reponse_error(_l('month_kpi_null'), 'month_kpi');
        }

        $result = $this->dashboard_model->insert_update_kpi($data);

        if ($result) {
            $this->reponse_json(_l('success_json'));
        } else {
            $this->reponse_error(_l('failed_json'));
        }

        print_r($result);
        die;
    }



    public function get_kpi_team()
    {
        $data = $this->input->get();

        if (!$data['month_kpi'] || !in_array($data['team_id'], $this->team_sales))
            return false;


        // Xử lý lấy nhân viên tháng đó của team
        // Lấy tháng
        $array_date = explode('.', $data['month_kpi']);
        $month = (int)$array_date[0];

        if (!$month)
            return false;


        // Load dữ liệu cũ lên + dữ liệu nhân viên mới không có trong data cũ
        // Lấy danh sách nhân viên trong cấu hình ra
        $list_agent_setting = $this->dashboard_model->get_staff_kpi($data, $this->team_sales);

        // Danh sách nhân viên active trước tháng $data['month_kpi'] 
        $list_agent_active = $this->dashboard_model->get_staff_active($data, $this->team_sales);

        //  Merge 2 array lại.
        $mergedArray = array_merge($list_agent_setting, $list_agent_active);
        $list_agent = array_unique($mergedArray);

        // Lấy thông tin cấu hình tất cả nhân viên
        $data['result'] = $this->dashboard_model->list_setting_kpi($data, $list_agent);

        $data['kpi'] = $this->dashboard_model->get_setting_kpi($data);

        $data['kpi']['kpi_revenue_team'] = $this->dashboard_model->get_kpi_revenue_team($data['kpi']['id'], $data['team_id']);

        $data['array_time_kpi'] = array_time_kpi();

        $result = json_encode(array(
            'error' => 0,
            'html' => $this->load->view('admin/dashboard/search_kpi', $data, true),
            'kpi' => $data['kpi'],
            'mess' => _l('success_json')
        ));

        print_r($result);
        die;
    }
}

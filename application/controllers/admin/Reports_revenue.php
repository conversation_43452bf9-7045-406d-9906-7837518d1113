<?php

use app\services\ReportService;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Estimates_model $estimates_model
 */
class Reports_revenue extends AdminController
{
    /**
     * Codeigniter Instance
     * Expenses detailed report filters use $ci
     * @var object
     */
    private $ci;

    public function __construct()
    {
        parent::__construct();
        if (!has_permission('reports', '', 'view')) {
            access_denied('reports');
        }
        $this->ci = &get_instance();
        $this->load->model('reports_model');
    }

    /* No access on this url */
    public function index()
    {
        if (!has_permission('reports', '', 'view_report_revenue')) {
            access_denied('reports');
        }
        $this->load->view('admin/reports/revenues');
    }

    public function daily_report_table()
    {
        if (!has_permission('reports', '', 'view_report_revenue')) {
            ajax_access_denied();
        }
        $this->load->helper('invoices_report');
        $this->app->get_table_data('job_posting_revenue_daily_report_table');
    }

    public function export_revenue_report_daily($reportDate)
    {
        if (!has_permission('reports', '', 'view_report_revenue')) {
            access_denied('reports');
        }
        $this->load->helper('invoices_report');
        ReportService::exportJobPostingDailyRevenue($reportDate);
    }

    public function monthly_report_table()
    {
        if (!has_permission('reports', '', 'view_report_revenue')) {
            ajax_access_denied();
        }
        $this->load->helper('invoices_report');
        $this->app->get_table_data('job_posting_revenue_monthly_report_table');
    }

    public function export_revenue_report_monthly($reportMonth)
    {
        if (!has_permission('reports', '', 'view_report_revenue')) {
            access_denied('reports');
        }
        $this->load->helper('invoices_report');
        ReportService::exportJobPostingMonthlyRevenue($reportMonth);
    }
}

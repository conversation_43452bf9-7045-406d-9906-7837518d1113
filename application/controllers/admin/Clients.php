<?php

use app\services\AmsPackageService;
use app\services\utilities\Arr;
use Entities\ClientPayer;
use Entities\FavoriteClient;
use Entities\Ams\Company;
use Entities\Client;
use Entities\ClientAmsCompany;
use Entities\ClientAmsJob;
use Entities\Invoice;
use Entities\Item;
use app\services\AmsService;
use Entities\AmsPackage;
use Entities\ClientAmsSearchPackage;
use Entities\Itemable;

defined('BASEPATH') or exit('No direct script access allowed');

class Clients extends AdminController
{
    /* List all clients */
    public function index()
    {
        $this->load->model([
            'contracts_model',
            'proposals_model',
            'invoices_model',
            'estimates_model',
            'projects_model'
        ]);

        $whereContactsLoggedIn = '';

        if (!has_permission('customers', '', 'view') && !has_permission('customers', '', 'view_team') && !has_permission('customers', '', 'view_own')) {
            access_denied('customers');
            $whereContactsLoggedIn = ' AND userid IN (SELECT customer_id FROM ' . db_prefix() . 'customer_admins WHERE staff_id=' . get_staff_user_id() . ')';
        }

        $data['title'] = _l('clients');
        $data['contacts_logged_in_today'] = $this->clients_model->get_contacts('', 'last_login LIKE "' . date('Y-m-d') . '%"' . $whereContactsLoggedIn);
        $data['contract_types'] = $this->contracts_model->get_contract_types();
        $data['groups'] = $this->clients_model->get_groups();
        $data['proposal_statuses'] = $this->proposals_model->get_statuses();
        $data['invoice_statuses'] = $this->invoices_model->get_statuses();
        $data['estimate_statuses'] = $this->estimates_model->get_statuses();
        $data['project_statuses'] = $this->projects_model->get_project_statuses();
        $data['customer_admins'] = $this->clients_model->get_customers_admin_unique_ids();
        $data['countries'] = $this->clients_model->get_clients_distinct_countries();
        $data['staff_active'] = get_list_staff();
        $data['staff'] = $this->staff_model->get();

        $this->load->view('admin/clients/manage', $data);
    }

    public function table()
    {
        if (!has_permission('customers', '', 'view') && !has_permission('customers', '', 'view_team') && !has_permission('customers', '', 'view_own')) {
            ajax_access_denied();
        }

        $this->app->get_table_data('clients');
    }

    public function all_contacts()
    {
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data('all_contacts');
        }

        if (is_gdpr() && get_option('gdpr_enable_consent_for_contacts') == '1') {
            $this->load->model('gdpr_model');
            $data['consent_purposes'] = $this->gdpr_model->get_consent_purposes();
        }

        $data['title'] = _l('customer_contacts');
        $this->load->view('admin/clients/all_contacts', $data);
    }

    /* Edit client or add new client*/
    public function client($id = '')
    {
        if (!has_permission('customers', '', 'view') && !has_permission('customers', '', 'view_team') && !has_permission('customers', '', 'view_own')) {
            if ($id != '' && !is_customer_admin($id)) {
                access_denied('customers');
            }
        }

        if ($this->input->post() && !$this->input->is_ajax_request()) {
            $data = $this->input->post();

            if (!is_leader_member()) {
                unset($data['salesperson_id']);
            }

            $relationsData = [
                'offices' => [
                    'ids' => Arr::pull($data, 'company_office_ids'),
                    'addresses' => Arr::pull($data, 'company_office_addresses'),
                ],
                'branches' => [
                    'ids' => Arr::pull($data, 'company_branch_ids'),
                    'name' => Arr::pull($data, 'company_branch_name'),
                    'vat' => Arr::pull($data, 'company_branch_vat'),
                    'business_address' => Arr::pull($data, 'company_branch_business_address'),
                    'office_address' => Arr::pull($data, 'company_branch_office_address'),
                    'note' => Arr::pull($data, 'company_branch_note'),
                    'status' => Arr::pull($data, 'company_branch_status'),
                ],
                'affiliates' => [
                    'ids' => Arr::pull($data, 'company_affiliate_ids'),
                    'relation_type' => Arr::pull($data, 'company_affiliate_relation_type'),
                    'related_vat' => Arr::pull($data, 'company_affiliate_related_vat'),
                    'related_company_name' => Arr::pull($data, 'company_affiliate_related_company_name'),
                ],
                'industries' => Arr::pull($data, 'company_industries'),
                'nationalities' => Arr::pull($data, 'company_nationalities'),
                'ams_company_ids' => array_filter(Arr::pull($data, 'ams_company_ids') ?? []),
            ];
            $data['established_date'] = !empty($data['established_date']) ? date_create_from_format('d/m/Y', $data['established_date'])->format('Y-m-d') : null;
            if (!empty($data['company_types'])) {
                $data['company_types'] = json_encode($data['company_types']);
            }

            if ($id == '') {
                if (!has_permission('customers', '', 'create')) {
                    access_denied('customers');
                }

                $save_and_add_contact = false;
                if (isset($data['save_and_add_contact'])) {
                    unset($data['save_and_add_contact']);
                    $save_and_add_contact = true;
                }
                $id = $this->clients_model->add($data);
                if (!has_permission('customers', '', 'view')) {
                    $assign['customer_admins']   = [];
                    $assign['customer_admins'][] = get_staff_user_id();
                    $this->clients_model->assign_admins($assign, $id);
                }
                if ($id) {
                    $this->clients_model->updateRelationsData($id, $relationsData);
                    set_alert('success', _l('added_successfully', _l('client')));
                    if ($save_and_add_contact == false) {
                        redirect(admin_url('clients/client/' . $id));
                    } else {
                        redirect(admin_url('clients/client/' . $id . '?group=contacts&new_contact=true'));
                    }
                }
            } else {
                if (!has_permission('customers', '', 'edit', $id)) {
                    access_denied('customers');
                }
                $success = $this->clients_model->update($data, $id);
                $relationSuccess = $this->clients_model->updateRelationsData($id, $relationsData);
                if ($success == true || $relationSuccess) {
                    set_alert('success', _l('updated_successfully', _l('client')));
                }
                redirect(admin_url('clients/client/' . $id));
            }
        }

        $group         = !$this->input->get('group') ? 'profile' : $this->input->get('group');
        $data['group'] = $group;

        if ($group != 'contacts' && $contact_id = $this->input->get('contactid')) {
            redirect(admin_url('clients/client/' . $id . '?group=contacts&contactid=' . $contact_id));
        }

        // Customer groups
        $data['groups'] = $this->clients_model->get_groups();

        $data['staff'] = get_list_staff();

        $this->load->helper('call_center');

        if ($group == 'profile') {
            $data['taxonomies'] = get_ams_taxonomies(['industries','nationalities','num_employees']);
        }

        if ($id == '') {
            $title = _l('add_new', _l('client_lowercase'));
        } else {
            $client                = $this->clients_model->get($id, [], '', false);
            $data['customer_tabs'] = get_customer_profile_tabs($id);
            $this->load->helper('ams');

            if (!$client) {
                show_404();
            }

            $data['contacts'] = $this->clients_model->get_contacts($id);
            $data['tab']  = $this->app_tabs->filter_tab($data['customer_tabs'], $group);

            if (!$data['tab']) {
                show_404();
            }

            $data['customer_admins'] = $this->clients_model->get_admins($id);

            // Fetch data based on groups
            if ($group == 'profile') {
                $data['customer_groups'] = $this->clients_model->get_customer_groups($id);
                $data['offices'] = $this->clients_model->get_offices($id);
                $data['branches'] = $this->clients_model->get_branches($id);
                $data['affiliates'] = $this->clients_model->get_affiliates($id);
                $data['client_request'] = $this->clients_model->get_client_request($id);
                $data['industries'] = $this->clients_model->get_client_industries($id);
                $data['nationalities'] = $this->clients_model->get_client_nationalities($id);
                $data['request_file'] = $this->clients_model->get_source_attachment($id);
                $data['ams_company_ids'] = $this->clients_model->getAmsCompanyIds($id);
                $data['classify_2024'] = isset($client->classify_2024) ? get_client_classcification()[$client->classify_2024] : '';
                $data['classify_2025'] = isset($client->classify_2025) ? get_client_classcification()[$client->classify_2025] : '';
                $data['payer'] = ClientPayer::query()
                    ->where('client_id', $id)
                    ->with([
                        'ecRecvs' => fn($qr) => $qr->orderByDesc('id'),
                        'ecRecvs.invoice',
                        'ecRecvs.trans',
                    ])
                    ->first();
            } elseif ($group == 'attachments') {
                $data['attachments'] = get_all_customer_attachments($id);
            } elseif ($group == 'vault') {
                $data['vault_entries'] = hooks()->apply_filters('check_vault_entries_visibility', $this->clients_model->get_vault_entries($id));

                if ($data['vault_entries'] === -1) {
                    $data['vault_entries'] = [];
                }
            } elseif ($group == 'estimates') {
                $this->load->model('estimates_model');
                $data['estimate_statuses'] = $this->estimates_model->get_statuses();
            } elseif ($group == 'invoices') {
                $this->load->model('invoices_model');
                $data['invoice_statuses'] = $this->invoices_model->get_statuses();
            } elseif ($group == 'credit_notes') {
                $this->load->model('credit_notes_model');
                $data['credit_notes_statuses'] = $this->credit_notes_model->get_statuses();
                $data['credits_available']     = $this->credit_notes_model->total_remaining_credits_by_customer($id);
            } elseif ($group == 'payments') {
                $this->load->model('payment_modes_model');
                $data['payment_modes'] = $this->payment_modes_model->get();
            } elseif ($group == 'contact_success_notes') {
                $dataNotes = $this->misc_model->get_client_notes_by_types($id, [$this->misc_model->CUSTOMER_TYPE_CONTACT_SUCCESS], '', ['last_modified', 'feedback']);
                $data['type_note'] = $this->misc_model->CUSTOMER_TYPE_CONTACT_SUCCESS;
            } elseif ($group == 'contact_again_notes') {
                $dataNotes = $this->misc_model->get_client_notes_by_types($id, [$this->misc_model->CUSTOMER_TYPE_CONTACT_AGAIN], '', ['last_modified', 'feedback']);
                $data['type_note'] = $this->misc_model->CUSTOMER_TYPE_CONTACT_AGAIN;
            } elseif ($group == 'internal_notes') {
                $dataNotes = $this->misc_model->get_client_notes_by_types($id, [$this->misc_model->CUSTOMER_TYPE_INTERNAL], '', ['last_modified', 'feedback']);
                $data['type_note'] = $this->misc_model->CUSTOMER_TYPE_INTERNAL;
            } elseif ($group == 'projects') {
                $this->load->model('projects_model');
                $data['project_statuses'] = $this->projects_model->get_project_statuses();
            } elseif ($group == 'statement') {
                if (!has_permission('invoices', '', 'view') && !has_permission('payments', '', 'view')) {
                    set_alert('danger', _l('access_denied'));
                    redirect(admin_url('clients/client/' . $id));
                }

                $data = array_merge($data, prepare_mail_preview_data('customer_statement', $id));
            } elseif ($group == 'map') {
                if (get_option('google_api_key') != '' && !empty($client->latitude) && !empty($client->longitude)) {
                    $this->app_scripts->add('map-js', base_url($this->app_scripts->core_file('assets/js', 'map.js')) . '?v=' . $this->app_css->core_version());

                    $this->app_scripts->add('google-maps-api-js', [
                        'path'       => 'https://maps.googleapis.com/maps/api/js?key=' . get_option('google_api_key') . '&callback=initMap',
                        'attributes' => [
                            'async',
                            'defer',
                            'latitude'       => "$client->latitude",
                            'longitude'      => "$client->longitude",
                            'mapMarkerTitle' => "$client->company",
                        ],
                        ]);
                }
            } elseif ($group == 'history') {
                $data['activity_logs'] = $this->clients_model->get_activity_logs($id, 'description, date, ' . db_prefix() . 'activity_log.staffid, company, concat(firstname, " ", lastname) staff_name');
            } elseif ($group == 'ams_companies') {
                $title = _l('ams_companies_title');
                $data['taxonomies'] = get_ams_taxonomies(['services']);
                $data['ams_companies'] = getAmsCompaniesById($id);
            } elseif ($group == 'ams_jobs') {
                $title = _l('ams_jobs_title');
                $data['taxonomies'] = get_ams_taxonomies(['services', 'packages']);
                $data['crm_companies'] = get_crm_companies($id);
                $data['synced_already'] = Client::has('clientAmsCompanies')->where('userid', $id)->exists();
                $data['ams_companies'] = getAmsCompaniesById($id);
            } elseif ($group == 'ams_search_cvs') {
                $title = _l('ams_search_cv_title');
                $data['crm_companies'] = get_crm_companies($id);
                $data['ams_companies'] = getAmsCompaniesById($id);
                $data['invoice_search_cvs'] = Invoice::where('clientid', $id)
                    ->select('id', 'date', 'number', 'prefix', 'number_format', 'status')
                    ->whereHas('invoiceItems', fn ($query) => $query->isCredit())
                    ->has('clientAmsSearchCvs')
                    ->get()
                    ->map(fn($invoice) => ['id' => $invoice->id, 'text' => format_invoice_number($invoice)])
                    ->toArray();
            } elseif ($group == 'ams_employees') {
                $data['ams_companies'] = getAmsCompaniesById($id);
                $title = _l('ams_employees_title');
            } elseif ($group == 'ams_post_job') {
                if (!has_permission('customers', '', 'edit_job_owned') && !has_permission('customers', '', 'edit_job_global')) {
                    access_denied('customers');
                }
                $data['ams_companies'] = getAmsCompaniesById($id);
                $amsJobId = $this->input->get('ams_job_id');
                $data['ams_job_id'] = $amsJobId;
                $data['client_ams_job_id'] = null;
                // Job hết số lượng
                // Lấy toàn bộ invoice của clientid
                // Đếm paid packages => Đã mua
                // Đếm used packages
                // - Records trong table tblclient_ams_jobs
                // - Sum column tblclient_ams_jobs.used_packages
                $this->load->model('Invoices_model');
                // Default fetch invoices that have paid items
                $validInvoiceIds = $this->Invoices_model->getAvailableInvoices($id, null, Itemable::PAID_ITEM);
                $invoice = Invoice::select('id', 'date', 'number', 'prefix', 'number_format', 'status');
                $isFreePackage = false;
                $selectedAmsCompanyId = $data['ams_companies'][0]['id'];
                if ($amsJobId) {
                    $data['job'] = getAmsJobInfo($amsJobId);
                    $clientAmsJob = ClientAmsJob::select('id', 'package_id', 'free_package', 'invoice_id')
                        ->where(['ams_job_id' => $amsJobId, 'client_id' => $id])
                        ->first();

                    if ($clientAmsJob) {
                        $data['client_ams_job_id'] = $clientAmsJob->id;
                        $data['package_id'] = $clientAmsJob->package_id;
                        $data['free_package'] = $clientAmsJob->free_package;
                        $isFreePackage = $data['free_package'];
                    }
                    if (!empty($data['job']['crm_invoice_id'])) {
                        $invoice->where(fn($qr) => $qr->where('id', $data['job']['crm_invoice_id'])->orWhere(fn($sub) => $sub->whereIn('id', $validInvoiceIds)));
                    } else {
                        $invoice->whereIn('id', $validInvoiceIds);
                    }
                    $packageId = null;
                    if (isset($data['job']['package_id']) && isset($data['job']['crm_invoice_id'])) {
                        $packageId = $data['job']['package_id'] . ':' . $data['job']['crm_invoice_id'] . ':' . ($clientAmsJob->free_package ? 1 : 0);
                    }
                    $availablePackages = AmsPackageService::get_available_packages($selectedAmsCompanyId, $packageId, !$clientAmsJob->free_package);
                    if (!empty($amsPackageId = $data['job']['package_id']) && empty($data['package_id'])) {
                        $data['package_id'] = $availablePackages->where('ams_package_id', $amsPackageId)
                            ->when(!empty($data['job']['crm_invoice_id']), fn($builder) => $builder->where('invoice_id', $data['job']['crm_invoice_id']))
                            ->first()['id'];
                        $invoice->hasAmsPackageId($amsPackageId);
                    }
                } else {
                    $availablePackages = AmsPackageService::get_available_packages($selectedAmsCompanyId, null, true);
                    $invoice->whereIn('id', $validInvoiceIds);
                }
                $invoice->whereHas('invoiceItems', fn ($query) => $query->where('rate', $isFreePackage ? '=' : '>', 0)->isNotCredit());
                $data['paid_packages'] = $availablePackages->map(fn($item) => ['id' => $item['id'], 'text' => $item['description']]);
                $data['job_statuses'] = get_ams_job_statuses();
                $data['invoices'] = $invoice->get()->map(fn($invoice) => ['id' => $invoice->id, 'name' => format_invoice_number($invoice)])->toArray();
                $data['taxonomies'] = get_ams_taxonomies([
                    'job_levels',
                    'experiences',
                    'job_types',
                    'contract_types',
                    'skills',
                    'role',
                    'responsibilities',
                    'requirements',
                    'recruitment_processes',
                    'benefits',
                    'job_banner',
                    'job_template',
                    'job_template_color',
                    'education',
                    'education_major'
                ]);
                $data['job_categories'] = get_job_categories();
                $this->load->config('benefit_icons');
                $data['benefits'] = $this->config->item('benefit_icons');
                $data['client_id'] = $id;
            }

            if (isset($dataNotes)) {
                $data['user_notes'] = $dataNotes;
            }

            $data['client'] = $client;
            $title          = $client->business_name;

            // Get all active staff members (used to add reminder)
            $data['members'] = $data['staff'];

            if (!empty($data['client']->company)) {
                // Check if is realy empty client company so we can set this field to empty
                // The query where fetch the client auto populate firstname and lastname if company is empty
                if (is_empty_customer_company($data['client']->userid)) {
                    $data['client']->company = '';
                }
            }
        }

        $this->load->model('currencies_model');
        $data['currencies'] = $this->currencies_model->get();

        if ($id != '') {
            $customer_currency = $data['client']->default_currency;

            foreach ($data['currencies'] as $currency) {
                if ($customer_currency != 0) {
                    if ($currency['id'] == $customer_currency) {
                        $customer_currency = $currency;

                        break;
                    }
                } else {
                    if ($currency['isdefault'] == 1) {
                        $customer_currency = $currency;

                        break;
                    }
                }
            }

            if (is_array($customer_currency)) {
                $customer_currency = (object) $customer_currency;
            }

            $data['customer_currency'] = $customer_currency;

            $slug_zip_folder = (
                $client->company != ''
                ? $client->company
                : get_contact_full_name(get_primary_contact_user_id($client->userid))
            );

            $data['zip_in_folder'] = slug_it($slug_zip_folder);
        }

        $data['bodyclass'] = 'customer-profile dynamic-create-groups';
        $data['title']     = $title;

        $this->load->view('admin/clients/client', $data);
    }

    public function favorite($id)
    {
        $isExist = $this->db->where([
            'client_id' => $id,
            'staff_id' => $this->input->post('staff_id'),
            'status' => 1,
        ])->get(db_prefix() . 'favorite_clients')->num_rows();

        if (!$isExist) {
            $this->db->insert(db_prefix() . 'favorite_clients', array_merge([
                'client_id' => $id,
                'status' => 1
            ], $this->input->post()));
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function not_favorite()
    {
        $data = $this->input->get();

        $item = FavoriteClient::select('id')
        ->where('client_id', $data['clientId'])
        ->where('staff_id', $data['staffId'])
        ->where('status', 1)
        ->first();

        if ($item) {
            $item->delete();
        }else{
            ajax_error_response(_l('error_notfavorite'));
        }

        ajax_success_response(_l('success_notfavorite'));

    }

    public function export($contact_id)
    {
        if (is_admin()) {
            $this->load->library('gdpr/gdpr_contact');
            $this->gdpr_contact->export($contact_id);
        }
    }

    // Used to give a tip to the user if the company exists when new company is created
    public function check_duplicate_customer_name()
    {
        $companyName = trim($this->input->post('company'));
        $response    = [
            'exists'  => (bool) total_rows(db_prefix() . 'clients', ['business_name' => $companyName, 'approved_at IS NOT NULL' => null]) > 0,
            'message' => _l('company_exists_info', '<b>' . $companyName . '</b>'),
        ];
        echo json_encode($response);
    }

    public function check_duplicate_vat()
    {
        $vatNumber = trim($this->input->post('vat'));

        $client = $this->db
            ->where_not_in('userid', $this->input->post('customer_id'))
            ->where('vat', $vatNumber)
            ->where('approved_at IS NOT NULL')
            ->get(db_prefix() . 'clients')
            ->row();

        $response = [
            'exists'  => $client ? true : false
        ];
        echo json_encode($response);
    }

    public function save_longitude_and_latitude($client_id)
    {
        if (!has_permission('customers', '', 'edit')) {
            if (!is_customer_admin($client_id)) {
                ajax_access_denied();
            }
        }

        $this->db->where('userid', $client_id);
        $this->db->update(db_prefix() . 'clients', [
            'longitude' => $this->input->post('longitude'),
            'latitude'  => $this->input->post('latitude'),
        ]);
        if ($this->db->affected_rows() > 0) {
            echo 'success';
        } else {
            echo 'false';
        }
    }

    public function form_contact($customer_id, $contact_id = '')
    {
        if (!has_permission('contacts', '', 'view', $customer_id)) {
            if (!is_customer_admin($customer_id)) {
                access_denied('contacts');
            }
        }
        $data['customer_id'] = $customer_id;
        $data['contactid']   = $contact_id;

        if (is_automatic_calling_codes_enabled()) {
            $clientCountryId = $this->db->select('country')
                ->where('userid', $customer_id)
                ->get('clients')->row()->country ?? null;

            $clientCountry = get_country($clientCountryId);
            $callingCode   = $clientCountry ? '+' . ltrim($clientCountry->calling_code, '+') : null;
        } else {
            $callingCode = null;
        }

        if ($this->input->post()) {
            $data             = $this->input->post();
            $data['password'] = $this->input->post('password', false);

            if ($callingCode && !empty($data['phonenumber']) && $data['phonenumber'] == $callingCode) {
                $data['phonenumber'] = '';
            }

            unset($data['contactid']);

            $this->load->helper(['form_rules']);
            $this->load->library('form_validation');
            $this->form_validation->set_rules(contact_form_rules());

            if (!$this->form_validation->run()) {
                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad error');
                echo json_encode([
                    'success' => false,
                    'message' => validation_errors(),
                ]);
                die;
            }

            if ($contact_id == '') {
                if (!has_permission('contacts', '', 'create')) {
                    if (!is_customer_admin($customer_id)) {
                        header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad error');
                        echo json_encode([
                            'success' => false,
                            'message' => _l('access_denied'),
                        ]);
                        die;
                    }
                }
                $id      = $this->clients_model->add_contact($data, $customer_id);
                $message = '';
                $success = false;
                if ($id) {
                    handle_contact_profile_image_upload($id);
                    $success = true;
                    $message = _l('added_successfully', _l('contact'));
                }
                echo json_encode([
                    'success'             => $success,
                    'message'             => $message,
                    'has_primary_contact' => (total_rows(db_prefix() . 'contacts', ['userid' => $customer_id, 'is_primary' => 1]) > 0 ? true : false),
                    'is_individual'       => is_empty_customer_company($customer_id) && total_rows(db_prefix() . 'contacts', ['userid' => $customer_id]) == 1,
                ]);
                die;
            }
            if (!has_permission('contacts', '', 'edit')) {
                if (!is_customer_admin($customer_id)) {
                    header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad error');
                    echo json_encode([
                            'success' => false,
                            'message' => _l('access_denied'),
                        ]);
                    die;
                }
            }
            if (!has_permission('contacts', '', 'review')) {
                unset($data['review_status']);
            }
            $original_contact = $this->clients_model->get_contact($contact_id);
            $success          = $this->clients_model->update_contact($data, $contact_id);
            $message          = '';
            $proposal_warning = false;
            $original_email   = '';
            $updated          = false;
            if (is_array($success)) {
                if (isset($success['set_password_email_sent'])) {
                    $message = _l('set_password_email_sent_to_client');
                } elseif (isset($success['set_password_email_sent_and_profile_updated'])) {
                    $updated = true;
                    $message = _l('set_password_email_sent_to_client_and_profile_updated');
                }
            } else {
                if ($success == true) {
                    $updated = true;
                    $message = _l('updated_successfully', _l('contact'));
                }
            }
            if (handle_contact_profile_image_upload($contact_id) && !$updated) {
                $message = _l('updated_successfully', _l('contact'));
                $success = true;
            }
            if ($updated == true) {
                $contact = $this->clients_model->get_contact($contact_id);
                if (total_rows(db_prefix() . 'proposals', [
                        'rel_type' => 'customer',
                        'rel_id' => $contact->userid,
                        'email' => $original_contact->email,
                    ]) > 0 && ($original_contact->email != $contact->email)) {
                    $proposal_warning = true;
                    $original_email   = $original_contact->email;
                }
            }
            echo json_encode([
                    'success'             => $success,
                    'proposal_warning'    => $proposal_warning,
                    'message'             => $message,
                    'original_email'      => $original_email,
                    'has_primary_contact' => (total_rows(db_prefix() . 'contacts', ['userid' => $customer_id, 'is_primary' => 1]) > 0 ? true : false),
                ]);
            die;
        }


        $data['calling_code'] = $callingCode;

        if ($contact_id == '') {
            $title = _l('add_new', _l('contact_lowercase'));
        } else {
            $data['contact'] = $this->clients_model->get_contact($contact_id);

            if (!$data['contact']) {
                header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad error');
                echo json_encode([
                    'success' => false,
                    'message' => 'Contact Not Found',
                ]);
                die;
            }
            $title = $data['contact']->fullname;
        }

        $data['customer_permissions'] = get_contact_permissions();
        $data['title']                = $title;
        $this->load->view('admin/clients/modals/contact_v2', $data);
    }

    public function confirm_registration($client_id)
    {
        if (!is_admin()) {
            access_denied('Customer Confirm Registration, ID: ' . $client_id);
        }
        $this->clients_model->confirm_registration($client_id);
        set_alert('success', _l('customer_registration_successfully_confirmed'));
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function update_file_share_visibility()
    {
        if ($this->input->post()) {
            $file_id           = $this->input->post('file_id');
            $share_contacts_id = [];

            if ($this->input->post('share_contacts_id')) {
                $share_contacts_id = $this->input->post('share_contacts_id');
            }

            $this->db->where('file_id', $file_id);
            $this->db->delete(db_prefix() . 'shared_customer_files');

            foreach ($share_contacts_id as $share_contact_id) {
                $this->db->insert(db_prefix() . 'shared_customer_files', [
                    'file_id'    => $file_id,
                    'contact_id' => $share_contact_id,
                ]);
            }
        }
    }

    public function delete_contact_profile_image($contact_id)
    {
        $this->clients_model->delete_contact_profile_image($contact_id);
    }

    public function mark_as_active($id)
    {
        if (!has_permission('customers', '', 'active')) {
            access_denied('customers');
        }
        $this->db->where('userid', $id);
        $this->db->update(db_prefix() . 'clients', [
            'active' => 1,
        ]);
        redirect(admin_url('clients/client/' . $id));
    }

    public function consents($id)
    {
        if (!has_permission('customers', '', 'view')) {
            if (!is_customer_admin(get_user_id_by_contact_id($id))) {
                echo _l('access_denied');
                die;
            }
        }

        $this->load->model('gdpr_model');
        $data['purposes']   = $this->gdpr_model->get_consent_purposes($id, 'contact');
        $data['consents']   = $this->gdpr_model->get_consents(['contact_id' => $id]);
        $data['contact_id'] = $id;
        $this->load->view('admin/gdpr/contact_consent', $data);
    }

    public function update_all_proposal_emails_linked_to_customer($contact_id)
    {
        $success = false;
        $email   = '';
        if ($this->input->post('update')) {
            $this->load->model('proposals_model');

            $this->db->select('email,userid');
            $this->db->where('id', $contact_id);
            $contact = $this->db->get(db_prefix() . 'contacts')->row();

            $proposals = $this->proposals_model->get('', [
                'rel_type' => 'customer',
                'rel_id'   => $contact->userid,
                'email'    => $this->input->post('original_email'),
            ]);
            $affected_rows = 0;

            foreach ($proposals as $proposal) {
                $this->db->where('id', $proposal['id']);
                $this->db->update(db_prefix() . 'proposals', [
                    'email' => $contact->email,
                ]);
                if ($this->db->affected_rows() > 0) {
                    $affected_rows++;
                }
            }

            if ($affected_rows > 0) {
                $success = true;
            }
        }
        echo json_encode([
            'success' => $success,
            'message' => _l('proposals_emails_updated', [
                _l('contact_lowercase'),
                $contact->email,
            ]),
        ]);
    }

    public function assign_admins($id)
    {
        if (!has_permission('customers', '', 'create') && !has_permission('customers', '', 'edit')) {
            access_denied('customers');
        }
        $data = $this->input->post();
        $data['customer_admins'] = array_filter($data['customer_admins']);
        $data['is_sa_assigned'] = 1;

        // Tmp disable: Do giờ chưa kịp sắp xếp người để duyệt, nên đối với công ty đang ở trong free db vẫn cho phép sale admin assign tay hoặc/ bulk import qua
        // $notAllowAssign = hooks()->apply_filters('before_assign_customer_admin', $id);
        // // If hook can't be called, it return $id's value
        // // Check bool to make sure it called and use correct value
        // if ($notAllowAssign === false) {
        //     set_alert('danger', _l('not_allow_assign_free_data'));
        //     redirect(admin_url('clients/client/' . $id . '?tab=customer_admins'));
        // }

        $success = $this->clients_model->assign_admins($data, $id);
        if ($success == true) {
            set_alert('success', _l('updated_successfully', _l('client')));
        } else {
            set_alert('danger', _l('assign_failed'));
        }

        redirect(admin_url('clients/client/' . $id . '?tab=customer_admins'));
    }

    public function delete_contact($customer_id, $id)
    {
        if (!has_permission('contacts', '', 'delete', $id)) {
            if (!is_customer_admin($customer_id)) {
                access_denied('contacts');
            }
        }
        $contact      = $this->clients_model->get_contact($id);
        $hasProposals = false;
        if ($contact && is_gdpr()) {
            if (total_rows(db_prefix() . 'proposals', ['email' => $contact->email]) > 0) {
                $hasProposals = true;
            }
        }

        $this->clients_model->delete_contact($id);
        if ($hasProposals) {
            $this->session->set_flashdata('gdpr_delete_warning', true);
        }
        redirect(admin_url('clients/client/' . $customer_id . '?group=contacts'));
    }

    public function contacts($client_id)
    {
        $this->app->get_table_data('contacts', [
            'client_id' => $client_id,
        ]);
    }

    public function request_create_contacts()
    {
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data('request_create_contacts');
        }

        $data['title'] = _l('request_create_contacts');
        $this->load->view('admin/clients/request_create_contacts', $data);
    }

    public function upload_attachment($id)
    {
        handle_client_attachments_upload($id);
    }

    public function add_external_attachment()
    {
        if ($this->input->post()) {
            $this->misc_model->add_attachment_to_database($this->input->post('clientid'), 'customer', $this->input->post('files'), $this->input->post('external'));
        }
    }

    public function delete_attachment($customer_id, $id)
    {
        if (has_permission('customers', '', 'delete') || is_customer_admin($customer_id)) {
            $this->clients_model->delete_attachment($id);
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    /* Delete client */
    public function delete($id)
    {
        if (!has_permission('customers', '', 'delete')) {
            access_denied('customers');
        }
        if (!$id) {
            redirect(admin_url('clients'));
        }
        $response = $this->clients_model->delete($id);
        if (is_array($response) && isset($response['referenced'])) {
            set_alert('warning', _l('customer_delete_transactions_warning', _l('invoices') . ', ' . _l('estimates') . ', ' . _l('credit_notes')));
        } elseif ($response == true) {
            set_alert('success', _l('deleted', _l('client')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('client_lowercase')));
        }
        redirect(admin_url('clients'));
    }

    /* Inactive client */ //
    public function inactive($id)
    {
        if (!has_permission('customers', '', 'active')) {
            access_denied('customers');
        }
        if (!$id) {
            redirect(admin_url('clients'));
        }

        $response = $this->clients_model->change_client_status($id, 0);
        if ($response == true) {
            set_alert('success', _l('inactived', _l('client')));
        } else {
            set_alert('warning', _l('problem_inactive', _l('client_lowercase')));
        }
        redirect(admin_url('clients'));
    }

    /* Staff can login as client */
    public function login_as_client($id)
    {
        if (is_admin()) {
            login_as_client($id);
        }
        hooks()->do_action('after_contact_login');
        redirect(site_url());
    }

    public function get_customer_billing_and_shipping_details($id)
    {
        echo json_encode($this->clients_model->get_customer_billing_and_shipping_details($id));
    }

    /* Change client status / active / inactive */
    public function change_contact_status($id, $status)
    {
        if (has_permission('customers', '', 'edit') || is_customer_admin(get_user_id_by_contact_id($id))) {
            if ($this->input->is_ajax_request()) {
                $this->clients_model->change_contact_status($id, $status);
            }
        }
    }

    /* Change client status / active / inactive */
    public function change_client_status($id, $status)
    {
        if (has_permission('customers', '', 'active')) {
            if ($this->input->is_ajax_request()) {
                $this->clients_model->change_client_status($id, $status);
            }
        }

    }

    public function client_change_data($id)
    {
        if ($this->input->is_ajax_request()) {
            $data['contact_data'] = $this->clients_model->get_contacts($id);

            echo json_encode($data);
        }
    }

    /* Zip function for credit notes */
    public function zip_credit_notes($id)
    {
        $has_permission_view = has_permission('credit_notes', '', 'view');

        if (!$has_permission_view && !has_permission('credit_notes', '', 'view_own')) {
            access_denied('Zip Customer Credit Notes');
        }

        if ($this->input->post()) {
            $this->load->library('app_bulk_pdf_export', [
                'export_type'       => 'credit_notes',
                'status'            => $this->input->post('credit_note_zip_status'),
                'date_from'         => $this->input->post('zip-from'),
                'date_to'           => $this->input->post('zip-to'),
                'redirect_on_error' => admin_url('clients/client/' . $id . '?group=credit_notes'),
            ]);

            $this->app_bulk_pdf_export->set_client_id($id);
            $this->app_bulk_pdf_export->in_folder($this->input->post('file_name'));
            $this->app_bulk_pdf_export->export();
        }
    }

    public function zip_invoices($id)
    {
        $has_permission_view = has_permission('invoices', '', 'view');
        if (!$has_permission_view && !has_permission('invoices', '', 'view_own')
            && get_option('allow_staff_view_invoices_assigned') == '0') {
            access_denied('Zip Customer Invoices');
        }

        if ($this->input->post()) {
            $this->load->library('app_bulk_pdf_export', [
                'export_type'       => 'invoices',
                'status'            => $this->input->post('invoice_zip_status'),
                'date_from'         => $this->input->post('zip-from'),
                'date_to'           => $this->input->post('zip-to'),
                'redirect_on_error' => admin_url('clients/client/' . $id . '?group=invoices'),
            ]);

            $this->app_bulk_pdf_export->set_client_id($id);
            $this->app_bulk_pdf_export->in_folder($this->input->post('file_name'));
            $this->app_bulk_pdf_export->export();
        }
    }

    /* Since version 1.0.2 zip client estimates */
    public function zip_estimates($id)
    {
        $has_permission_view = has_permission('estimates', '', 'view');
        if (!$has_permission_view && !has_permission('estimates', '', 'view_own')
            && get_option('allow_staff_view_estimates_assigned') == '0') {
            access_denied('Zip Customer Estimates');
        }

        if ($this->input->post()) {
            $this->load->library('app_bulk_pdf_export', [
                'export_type'       => 'estimates',
                'status'            => $this->input->post('estimate_zip_status'),
                'date_from'         => $this->input->post('zip-from'),
                'date_to'           => $this->input->post('zip-to'),
                'redirect_on_error' => admin_url('clients/client/' . $id . '?group=estimates'),
            ]);

            $this->app_bulk_pdf_export->set_client_id($id);
            $this->app_bulk_pdf_export->in_folder($this->input->post('file_name'));
            $this->app_bulk_pdf_export->export();
        }
    }

    public function zip_payments($id)
    {
        $has_permission_view = has_permission('payments', '', 'view');

        if (!$has_permission_view && !has_permission('invoices', '', 'view_own')
            && get_option('allow_staff_view_invoices_assigned') == '0') {
            access_denied('Zip Customer Payments');
        }

        $this->load->library('app_bulk_pdf_export', [
                'export_type'       => 'payments',
                'payment_mode'      => $this->input->post('paymentmode'),
                'date_from'         => $this->input->post('zip-from'),
                'date_to'           => $this->input->post('zip-to'),
                'redirect_on_error' => admin_url('clients/client/' . $id . '?group=payments'),
            ]);

        $this->app_bulk_pdf_export->set_client_id($id);
        $this->app_bulk_pdf_export->set_client_id_column(db_prefix() . 'clients.userid');
        $this->app_bulk_pdf_export->in_folder($this->input->post('file_name'));
        $this->app_bulk_pdf_export->export();
    }

    public function import()
    {
        if (!has_permission('customers', '', 'create')) {
            access_denied('customers');
        }
        $dbFields = $this->db->list_fields(db_prefix() . 'contacts');
        // Put fullname to the first of the list
        $fullnameIdx = array_search('fullname', $dbFields);
        if ($fullnameIdx !== false) {
            unset($dbFields[$fullnameIdx]);
            array_unshift($dbFields, 'fullname');
        }
        foreach ($dbFields as $key => $contactField) {
            if ($contactField == 'phonenumber') {
                $dbFields[$key] = 'contact_phonenumber';
            }
        }
        $this->load->library('import/import_customers', [], 'import');

        $clientFields = $this->db->list_fields(db_prefix() . 'clients');
        $clientFields = $this->import->sortClientsFields($clientFields);
        $dbFields = array_merge($dbFields, $clientFields);
        $dbFields = array_merge($dbFields, ['customer_admin']);


        $this->import->setDatabaseFields($dbFields)
                     ->setCustomFields(get_custom_fields('customers'));

        if ($this->input->post('download_sample') === 'true') {
            $this->import->downloadSample();
        }

        if ($this->input->post()
            && isset($_FILES['file_csv']['name']) && $_FILES['file_csv']['name'] != '') {
            $response = $this->import->setSimulation($this->input->post('simulate'))
                          ->setTemporaryFileLocation($_FILES['file_csv']['tmp_name'])
                          ->setFilename($_FILES['file_csv']['name'])
                          ->perform();

            if ($response['error']) {
                $message = $response['message'];
                set_alert('danger', $message);
            } else {
                $data['total_rows_post'] = $this->import->totalRows();

                if (!$this->import->isSimulation()) {
                    set_alert('success', _l('import_total_imported', $this->import->totalImported()));
                }
            }
        }

        $data['groups']    = $this->clients_model->get_groups();
        $data['title']     = _l('import');
        $data['bodyclass'] = 'dynamic-create-groups';
        $data['members']  = $this->staff_model->get('', ['is_not_staff' => 0, 'active' => 1]);
        $this->load->view('admin/clients/import', $data);
    }

    public function import_customer_admins()
    {
        if (!has_permission('customers', '', 'create')) {
            access_denied('customers');
        }

        $this->load->library('import/import_customer_admins', [], 'import');

        $this->import->setDatabaseFields([
            'company_id',
            'vat_number',
            'staff_id',
            'staff_email'
        ]);

        if ($this->input->post('download_sample') === 'true') {
            $this->import->downloadSample();
        }

        if ($this->input->post() && isset($_FILES['file_csv']['name']) && $_FILES['file_csv']['name'] != '') {
            $fileName = $this->save_file_import(get_upload_path_by_type('import_customer_admin'), $_FILES['file_csv']['name']);

            $response = $this->import
                ->setSimulation($this->input->post('simulate'))
                ->setTemporaryFileLocation($_FILES['file_csv']['tmp_name'])
                ->setFilename($_FILES['file_csv']['name'])
                ->perform();

            if ($response['error']) {
                $message = $response['message'];

                if (!$this->import->isSimulation()) {
                    log_activity('Import Customer Admin Failed: ' . $message, '', '', [
                            'file_name' => $fileName,
                            'type' => 'import_customer_admin'
                        ]
                    );
                }

                set_alert('danger', $message);
            } else {
                $data['total_rows_post'] = $this->import->totalRows();

                if (!$this->import->isSimulation()) {
                    $totalImported = $this->import->totalImported();

                    set_alert('success', _l('import_total_imported', $totalImported));
                    log_activity('Import Customer Admin Successfully: Total (' . $totalImported . ')', '', '',
                        [
                            'file_name' => $fileName,
                            'type' => 'import_customer_admin'
                        ]
                    );
                }
            }
        }

        $data['history'] = $this->clients_model->getHistoryImport('customer_admin');
        $data['title'] = _l('import_customer_admins');
        $this->load->view('admin/clients/import_customer_admin', $data);
    }

    public function import_salesperson()
    {
        if (!has_permission('customers', '', 'create')) {
            access_denied('customers');
        }

        $this->load->library('import/import_salesperson', [], 'import');

        $this->import->setDatabaseFields([
            'company_id',
            'vat_number',
            'staff_id',
            'staff_email'
        ]);

        if ($this->input->post('download_sample') === 'true') {
            $this->import->downloadSample();
        }

        if ($this->input->post() && isset($_FILES['file_csv']['name']) && $_FILES['file_csv']['name'] != '') {
            $fileName = $this->save_file_import(get_upload_path_by_type('import_salesperson'), $_FILES['file_csv']['name']);

            $response = $this->import
                ->setSimulation($this->input->post('simulate'))
                ->setTemporaryFileLocation($_FILES['file_csv']['tmp_name'])
                ->setFilename($_FILES['file_csv']['name'])
                ->perform();

            if ($response['error']) {
                $message = $response['message'];

                if (!$this->import->isSimulation()) {
                    log_activity('Import Salesperson Failed: ' . $message, '', '', [
                            'file_name' => $fileName,
                            'type' => 'import_salesperson'
                        ]
                    );
                }

                set_alert('danger', $message);
            } else {
                $data['total_rows_post'] = $this->import->totalRows();

                if (!$this->import->isSimulation()) {
                    $totalImported = $this->import->totalImported();

                    set_alert('success', _l('import_total_imported', $totalImported));
                    log_activity('Import Salesperson Successfully: Total (' . $totalImported . ')', '', '',
                        [
                            'file_name' => $fileName,
                            'type' => 'import_salesperson'
                        ]
                    );
                }
            }
        }

        $data['history'] = $this->clients_model->getHistoryImport('salesperson');
        $data['title'] = _l('import_salesperson');
        $this->load->view('admin/clients/import_salesperson', $data);
    }

    public function import_num_of_usage_behavior()
    {
        if (!has_permission('customers', '', 'update_num_of_usage_behavior')) {
            access_denied('customers');
        }

        $this->load->library('import/import_num_of_usage_behavior', [], 'import');

        $this->import->setDatabaseFields([
            'company_id',
            'num_of_usage_behavior',
        ]);

        if ($this->input->post('download_sample') === 'true') {
            $this->import->downloadSample();
        }

        if ($this->input->post() && isset($_FILES['file_csv']['name']) && $_FILES['file_csv']['name'] != '') {
            $fileName = $this->save_file_import(get_upload_path_by_type('import_num_of_usage_behavior'), $_FILES['file_csv']['name']);

            $response = $this->import
                ->setSimulation($this->input->post('simulate'))
                ->setTemporaryFileLocation($_FILES['file_csv']['tmp_name'])
                ->setFilename($_FILES['file_csv']['name'])
                ->perform();

            if ($response['error']) {
                $message = $response['message'];

                if (!$this->import->isSimulation()) {
                    log_activity('Import num of usage behavior Failed: ' . $message, '', '', [
                            'file_name' => $fileName,
                            'type' => 'import_num_of_usage_behavior'
                        ]
                    );
                }

                set_alert('danger', $message);
            } else {
                $data['total_rows_post'] = $this->import->totalRows();

                if (!$this->import->isSimulation()) {
                    $totalImported = $this->import->totalImported();

                    set_alert('success', _l('import_total_imported', $totalImported));
                    log_activity('Import num of usage behavior Successfully: Total (' . $totalImported . ')', '', '',
                        [
                            'file_name' => $fileName,
                            'type' => 'import_num_of_usage_behavior'
                        ],
                        null,
                        'import_num_of_usage_behavior'
                    );
                }
            }
        }

        $data['history'] = $this->clients_model->getHistoryImport('num_of_usage_behavior');
        $data['title'] = _l('import_num_of_usage_behavior_btn');
        $this->load->view('admin/clients/import_num_of_usage_behavior', $data);
    }

    public function save_file_import($path, $fileName)
    {
        _maybe_create_upload_path($path);
        $fileName = unique_filename($path, date('Ymd') . '_' . $fileName);
        copy($_FILES['file_csv']['tmp_name'], $path . $fileName);

        return $fileName;
    }

    public function download_file_csv($id)
    {
        $dataLog = $this->db->select('data')
            ->from(db_prefix() . 'activity_log')
            ->where('id', $id)
            ->get()
            ->first_row();

        if ($dataLog) {
            $dataJson = json_decode($dataLog->data);
            $fileName = $dataJson->file_name ?? '';
            $type = $dataJson->type ?? '';

            $path = get_upload_path_by_type($type) . $fileName;

            if ($fileName && file_exists($path)) {
                $data = file_get_contents($path);
                $name = $fileName;
                $this->load->helper('download');
                force_download($name, $data);

                return true;
            }

            return false;
        }

        return false;
    }

    public function groups()
    {
        if (!is_admin()) {
            access_denied('Customer Groups');
        }
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data('customers_groups');
        }
        $data['title'] = _l('customer_groups');
        $this->load->view('admin/clients/groups_manage', $data);
    }

    public function group()
    {
        if (!is_admin() && get_option('staff_members_create_inline_customer_groups') == '0') {
            access_denied('Customer Groups');
        }

        if ($this->input->is_ajax_request()) {
            $data = $this->input->post();
            if ($data['id'] == '') {
                $id      = $this->clients_model->add_group($data);
                $message = $id ? _l('added_successfully', _l('customer_group')) : '';
                echo json_encode([
                    'success' => $id ? true : false,
                    'message' => $message,
                    'id'      => $id,
                    'name'    => $data['name'],
                ]);
            } else {
                $success = $this->clients_model->edit_group($data);
                $message = '';
                if ($success == true) {
                    $message = _l('updated_successfully', _l('customer_group'));
                }
                echo json_encode([
                    'success' => $success,
                    'message' => $message,
                ]);
            }
        }
    }

    public function delete_group($id)
    {
        if (!is_admin()) {
            access_denied('Delete Customer Group');
        }
        if (!$id) {
            redirect(admin_url('clients/groups'));
        }
        $response = $this->clients_model->delete_group($id);
        if ($response == true) {
            set_alert('success', _l('deleted', _l('customer_group')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('customer_group_lowercase')));
        }
        redirect(admin_url('clients/groups'));
    }

    public function bulk_action()
    {
        hooks()->do_action('before_do_bulk_action_for_customers');
        $total_deleted = 0;
        if ($this->input->post()) {
            $ids    = $this->input->post('ids');
            $groups = $this->input->post('groups');

            if (is_array($ids)) {
                foreach ($ids as $id) {
                    if ($this->input->post('mass_delete')) {
                        if ($this->clients_model->delete($id)) {
                            $total_deleted++;
                        }
                    } else {
                        if (!is_array($groups)) {
                            $groups = false;
                        }
                        $this->client_groups_model->sync_customer_groups($id, $groups);
                    }
                }
            }
        }

        if ($this->input->post('mass_delete')) {
            set_alert('success', _l('total_clients_deleted', $total_deleted));
        }
    }

    public function transfer_customer()
    {
        $this->load->library('form_validation');
        $validate = 'required|numeric';
        $data = $this->input->post();
        $count = 0;

        if ($data['type_transfer'] == 'private') {
            $this->form_validation->set_rules('customer_admin_from', 'customer_admin_from', $validate);
            $this->form_validation->set_rules('customer_admin_to', 'customer_admin_to', 'numeric');
        } else {
            $this->form_validation->set_rules('number_transfer', 'number_transfer', $validate);
            $this->form_validation->set_rules('customer_admin', 'customer_admin', $validate);
        }

        if ($this->form_validation->run()) {
            if ($data['type_transfer'] == 'private') {
                $data_customers = $this->clients_model->get_customer_of_admins($data['customer_admin_from']);
                $customer_ids = array_column($data_customers, 'customer_id');

                foreach ($customer_ids as $customer_id) {
                    $result = [];
                    foreach ($this->clients_model->get_admins($customer_id) as $value) {
                        $result[] = $value['staff_id'];
                        $data_admins['customer_admins'] = array_filter(array_map(function ($item) use ($data) {
                            return $item == $data['customer_admin_from'] ? $data['customer_admin_to'] : $item;
                        }, $result));
                    }
                    $success = $this->clients_model->assign_admins($data_admins, $customer_id);

                    if ($success) {
                        $count++;
                    }
                }
                log_activity('Transfer Customer [ClientID: (' . implode(', ', $customer_ids) . '), Staff Id: From '. $data['customer_admin_from'] . ' To ' . $data['customer_admin_to'] . ']');
            } else {
                $data_clients = $this->clients_model->get_client_has_no_admin('', [], $data['number_transfer']);
                $customer_ids = array_column($data_clients, 'userid');
                $count = count($data_clients);
                foreach ($customer_ids as $customer_id) {
                    $data_admins['customer_admins'] = [
                        $data['customer_admin']
                    ];
                    $this->clients_model->assign_admins($data_admins, $customer_id);
                }
                log_activity('Transfer Customer [ClientID: (' . implode(', ', $customer_ids) . '), Staff Id: '. $data['customer_admin'] . ']');
            }
            set_alert('success', _l('transfer_successfully', $count));
        } else {
            set_alert('danger', _l('transfer_failed'));
        }

        redirect(admin_url('clients'));
    }

    public function vault_entry_create($customer_id)
    {
        $data = $this->input->post();

        if (isset($data['fakeusernameremembered'])) {
            unset($data['fakeusernameremembered']);
        }

        if (isset($data['fakepasswordremembered'])) {
            unset($data['fakepasswordremembered']);
        }

        unset($data['id']);
        $data['creator']      = get_staff_user_id();
        $data['creator_name'] = get_staff_full_name($data['creator']);
        $data['description']  = nl2br($data['description']);
        $data['password']     = $this->encryption->encrypt($this->input->post('password', false));

        if (empty($data['port'])) {
            unset($data['port']);
        }

        $this->clients_model->vault_entry_create($data, $customer_id);
        set_alert('success', _l('added_successfully', _l('vault_entry')));
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function vault_entry_update($entry_id)
    {
        $entry = $this->clients_model->get_vault_entry($entry_id);

        if ($entry->creator == get_staff_user_id() || is_admin()) {
            $data = $this->input->post();

            if (isset($data['fakeusernameremembered'])) {
                unset($data['fakeusernameremembered']);
            }
            if (isset($data['fakepasswordremembered'])) {
                unset($data['fakepasswordremembered']);
            }

            $data['last_updated_from'] = get_staff_full_name(get_staff_user_id());
            $data['description']       = nl2br($data['description']);

            if (!empty($data['password'])) {
                $data['password'] = $this->encryption->encrypt($this->input->post('password', false));
            } else {
                unset($data['password']);
            }

            if (empty($data['port'])) {
                unset($data['port']);
            }

            $this->clients_model->vault_entry_update($entry_id, $data);
            set_alert('success', _l('updated_successfully', _l('vault_entry')));
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function vault_entry_delete($id)
    {
        $entry = $this->clients_model->get_vault_entry($id);
        if ($entry->creator == get_staff_user_id() || is_admin()) {
            $this->clients_model->vault_entry_delete($id);
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function vault_encrypt_password()
    {
        $id            = $this->input->post('id');
        $user_password = $this->input->post('user_password', false);
        $user          = $this->staff_model->get(get_staff_user_id());

        if (!app_hasher()->CheckPassword($user_password, $user->password)) {
            header('HTTP/1.1 401 Unauthorized');
            echo json_encode(['error_msg' => _l('vault_password_user_not_correct')]);
            die;
        }

        $vault    = $this->clients_model->get_vault_entry($id);
        $password = $this->encryption->decrypt($vault->password);

        $password = html_escape($password);

        // Failed to decrypt
        if (!$password) {
            header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad error');
            echo json_encode(['error_msg' => _l('failed_to_decrypt_password')]);
            die;
        }

        echo json_encode(['password' => $password]);
    }

    public function get_vault_entry($id)
    {
        $entry = $this->clients_model->get_vault_entry($id);
        unset($entry->password);
        $entry->description = clear_textarea_breaks($entry->description);
        echo json_encode($entry);
    }

    public function statement_pdf()
    {
        $customer_id = $this->input->get('customer_id');

        if (!has_permission('invoices', '', 'view') && !has_permission('payments', '', 'view')) {
            set_alert('danger', _l('access_denied'));
            redirect(admin_url('clients/client/' . $customer_id));
        }

        $from = $this->input->get('from');
        $to   = $this->input->get('to');

        $data['statement'] = $this->clients_model->get_statement($customer_id, to_sql_date($from), to_sql_date($to));

        try {
            $pdf = statement_pdf($data['statement']);
        } catch (Exception $e) {
            $message = $e->getMessage();
            echo $message;
            if (strpos($message, 'Unable to get the size of the image') !== false) {
                show_pdf_unable_to_get_image_size_error();
            }
            die;
        }

        $type = 'D';
        if ($this->input->get('print')) {
            $type = 'I';
        }

        $pdf->Output(slug_it(_l('customer_statement') . '-' . $data['statement']['client']->company) . '.pdf', $type);
    }

    public function send_statement()
    {
        $customer_id = $this->input->get('customer_id');

        if (!has_permission('invoices', '', 'view') && !has_permission('payments', '', 'view')) {
            set_alert('danger', _l('access_denied'));
            redirect(admin_url('clients/client/' . $customer_id));
        }

        $from = $this->input->get('from');
        $to   = $this->input->get('to');

        $send_to = $this->input->post('send_to');
        $cc      = $this->input->post('cc');

        $success = $this->clients_model->send_statement_to_email($customer_id, $send_to, $from, $to, $cc);
        // In case client use another language
        load_admin_language();
        if ($success) {
            set_alert('success', _l('statement_sent_to_client_success'));
        } else {
            set_alert('danger', _l('statement_sent_to_client_fail'));
        }

        redirect(admin_url('clients/client/' . $customer_id . '?group=statement'));
    }

    public function statement()
    {
        if (!has_permission('invoices', '', 'view') && !has_permission('payments', '', 'view')) {
            header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad error');
            echo _l('access_denied');
            die;
        }

        $customer_id = $this->input->get('customer_id');
        $from        = $this->input->get('from');
        $to          = $this->input->get('to');

        $data['statement'] = $this->clients_model->get_statement($customer_id, to_sql_date($from), to_sql_date($to));

        $data['from'] = $from;
        $data['to']   = $to;

        $viewData['html'] = $this->load->view('admin/clients/groups/_statement', $data, true);

        echo json_encode($viewData);
    }

    public function fetch_companies($phoneNumber)
    {
        if ($this->input->get() && $this->input->is_ajax_request()) {
            $companies = $this->clients_model->fetchCompanies($phoneNumber);
            echo json_encode([
                'data' => $companies
            ]);
        }
    }

    public function create_client_request()
    {
        if (!is_sales()) {
            access_denied('clients');
        }
        $this->load->helper(array('form', 'form_rules'));
        $this->load->library('form_validation');
        $this->form_validation->set_rules(create_request_form_rules());
        $data = [];
        if ($this->input->post() && !$this->input->is_ajax_request()) {
            $data = $this->input->post();

            if ($this->form_validation->run() == FALSE) {
                $this->load->view('admin/clients/create_request', $data);
                return;
            }
            $this->load->helper(['array', 'upload']);
            unset($data['attachment']);
            $data['usage_behavior'] = 'OTHER';
            $data['active'] = 0;
            $data['registration_confirmed'] = 0;
            $id = $this->clients_model->add($data);
            if ($id) {
                $this->load->model('client_requests_model');
                $requestId = $this->client_requests_model->createRequest($id);
                if (isset($_FILES['attachment'])) {
                    $attachmentUuid = handle_client_request_attachment($id);
                    $this->clients_model->updateRequestAttachment($id, $attachmentUuid);
                }
                if ($requestId) {
                    set_alert('success', _l('added_successfully', _l('client_request_title')));
                    redirect(admin_url('clients/client/' . $id . '?group=contacts&new_contact=true'));
                }
            }
        }
        $this->load->view('admin/clients/create_request', $data);
    }

    public function request_list()
    {
        if (!(has_permission('customers', '', 'view_client_creating_request_own') && is_sales_member())) {
            access_denied('clients');
        }
        $this->load->helper(array('client_requests'));
        $data = [];

        $this->load->view('admin/clients/request_list', $data);
    }

    public function request_reviewing()
    {
        if (!has_permission('customers', '', 'view_client_creating_request_global') &&
            !(has_permission('customers', '', 'view_client_creating_request_own') && is_sales_leader())) {
            access_denied('clients');
        }
        $this->load->helper(array('client_requests'));
        $data = [];
        $this->load->view('admin/clients/request_reviewing', $data);
    }

    public function client_request_table()
    {
        if (!has_permission('customers', '', 'view_client_creating_request_global') &&
            !has_permission('customers', '', 'view_client_creating_request_own')) {
            ajax_access_denied();
        }

        $this->app->get_table_data('client_requests');
    }

    /**
     * Handle approve request for admin/leader
     * @param integer $requestId id of the request
     * @return json
     */
    public function client_approve_request($requestId, $silent = 1)
    {
        if (!(has_permission('customers', '', 'approve_client_creating_request_global') ||
            has_permission('customers', '', 'approve_client_creating_request_own'))) {
            ajax_access_denied();
        }

        try {
            $message = '';
            $this->load->model('client_requests_model');
            $requestNote = $this->input->post('note');
            if (is_sales_leader()) {
                $request = $this->client_requests_model->leader_can_approve_request($requestId);
                $message = $this->client_requests_model->process_leader_approve($requestId, $request, $requestNote);
            } else {
                $this->client_requests_model->admin_can_approve_request($requestId);
                $message = $this->client_requests_model->process_admin_approve($requestId, $requestNote);
            }
            if (!$silent) {
                set_alert('success', $message);
            }
            ajax_success($message);
        } catch (Exception $ex) {
            if (!$silent) {
                set_alert('error', $ex->getMessage());
            }
            ajax_error_validation($ex->getMessage());
        }
    }

    /**
     * Handle reject request by sale admin/leader
     * @param integer $requestId
     * @return json
     */
    public function client_reject_request($requestId, $silent = 1)
    {
        if (!(has_permission('customers', '', 'approve_client_creating_request_global') ||
            has_permission('customers', '', 'approve_client_creating_request_own'))) {
            ajax_access_denied();
        }

        try {
            $message = '';
            $this->load->model('client_requests_model');
            $requestNote = $this->input->post('note');
            if (is_sales_leader()) {
                $this->client_requests_model->leader_can_reject_request($requestId);
                $message = $this->client_requests_model->process_reject_request($requestId, $requestNote, CLIENT_REQUEST_STATUS_LEADER_REJECTED);
            } else {
                $this->client_requests_model->admin_can_approve_request($requestId);
                $message = $this->client_requests_model->process_reject_request($requestId, $requestNote);
            }
            if (!$silent) {
                set_alert('success', $message);
            }
            ajax_success($message);
        } catch (Exception $ex) {
            if (!$silent) {
                set_alert('error', $ex->getMessage());
            }
            ajax_error_validation($ex->getMessage());
        }
    }

    /**
     * Edit note after approved
     */
    public function client_edit_approve_note($requestId, $silent = 1)
    {
        if (!(has_permission('customers', '', 'approve_client_creating_request_global') ||
            has_permission('customers', '', 'approve_client_creating_request_own'))) {
            ajax_access_denied();
        }

        try {
            $message = '';
            $this->load->model('client_requests_model');
            $requestNote = $this->input->post('note');
            $this->client_requests_model->can_edit_request_note($requestId, is_sales_leader());
            $message = $this->client_requests_model->edit_request_note($requestId, $requestNote, is_sales_leader());
            if (!$silent) {
                set_alert('success', $message);
            }
            ajax_success($message);
        } catch (Exception $ex) {
            if (!$silent) {
                set_alert('error', $ex->getMessage());
            }
            ajax_error_validation($ex->getMessage());
        }
    }

    public function remove_customer_admins()
    {
        if (!has_permission('customers', '', 'bulk_remove_customer_admin')) {
            access_denied('customers');
        }

        if ($this->input->post('download_sample') === 'true') {
            header('Pragma: public');
            header('Expires: 0');
            header('Content-Type: application/csv');
            header('Content-Disposition: attachment; filename="sample_remove_file.csv";');
            header('Content-Transfer-Encoding: binary');

            // Header
            echo "Company ID\n";
            // Body
            echo "123456\n";
            exit;
        }
        if ($this->input->post() && isset($_FILES['file_csv']['name']) && $_FILES['file_csv']['name'] != '') {
            try {
                $displayFileName = 'remove_'.$_FILES['file_csv']['name'];
                $fileName = $this->save_file_import(get_upload_path_by_type('import_customer_admin'), $displayFileName);

                $fd   = fopen(get_upload_path_by_type('import_customer_admin') . $fileName, 'r');
                $bulkCount = 0;
                $rows = [];
                $this->load->model('customer_admins_model');
                while ($row = fgetcsv($fd)) {
                    // Ignore csv header
                    if ($bulkCount == 0) {
                        $bulkCount++;
                        continue;
                    }
                    $rows[] = $row[0];
                    $bulkCount++;
                    if ($bulkCount % 500 == 0) {
                        // Perfrom remove
                        $this->customer_admins_model->bulk_remove_cutomer_admins($rows);
                        // Reset data
                        $rows = [];
                    }
                }

                if (count($rows)) {
                    // Perfrom remove
                    $this->customer_admins_model->bulk_remove_cutomer_admins($rows);
                    $rows = [];
                }

                log_activity(_l('bulk_remove_customer_admin_success'), null, null, ['csv_file' => $fileName], null, 'rm_customer_admin');
                set_alert('success', _l('bulk_remove_customer_admin_success'));
            } catch (\Exception $ex) {
                log_activity(_l('bulk_remove_customer_admin_error', $ex->getMessage()), '', '', ['csv_file' => $fileName], null, 'rm_customer_admin');
                set_alert('danger', _l('bulk_remove_customer_admin_error', $ex->getMessage()));
            }
        }

        $data['title'] = _l('bulk_remove_customer_admins');
        $this->load->view('admin/clients/remove_customer_admin', $data);
    }

    public function remove_customer_admin_table()
    {
        if (!has_permission('customers', '', 'bulk_remove_customer_admin')) {
            ajax_access_denied();
        }

        $this->app->get_table_data('remove_customer_admin_logs');
    }

    /* TMP disable this feature until have final decision
    public function check_sync_company($userId, $checkPermissionOnly = 0)
    {
        if (!has_permission('customers', '', 'edit') && !is_customer_admin($userId)) {
            ajax_access_denied();
        }

        if (!allow_sync_ams_ids($userId)) {
            ajax_error_validation(_l('ams_not_valid_sync_rules'));
        } elseif ($checkPermissionOnly) {
            ajax_success_response('OK');
        }
        $client = Client::find($userId);
        $amsCompanies = Company::where('tax_number', $client->vat)->select('id')->get();

        if (!$amsCompanies->count()) {
            ajax_error_response(_l('ams_not_found_vat'));
        }
        $companyIds = $amsCompanies->pluck('id');

        // 'ams_company_id'
        $syncedClients = ClientAmsCompany::select('ams_company_id')
            ->whereIn('ams_company_id', $companyIds->toArray())
            ->where('client_id', $userId)
            ->get();

        // Total AMS companies = total synced cllients
        if ($syncedClients->count() == $amsCompanies->count()) {
            ajax_error_response(_l('ams_synced_already'));
        }
        $notSyncedYet = $companyIds->diff($syncedClients->pluck('ams_company_id'));

        ajax_success_response(
            _l('ams_sync_confirm', $notSyncedYet->join(', ')),
            ['ids' => $notSyncedYet->join(',')]
        );
    }*/

    public function sync_ams_ids($userId)
    {
        if (!has_permission('customers', '', 'edit') && !is_customer_admin($userId)) {
            ajax_access_denied();
        }

        if ($this->input->is_ajax_request()) {
            $idStr = $this->input->post('ids') ?? '';
            $ids = explode(',', $idStr);
            if (count($ids)) {
                try {
                    ClientAmsCompany::insert(array_map(fn($id) => ['client_id' => intval($userId), 'ams_company_id' => intval($id)], $ids));
                    log_activity(
                        'Sync AMS ID [ID: '.$userId.', AMS IDs: ['.$this->input->post('ids').']]',
                        get_staff_user_id(),
                        $userId,
                        [],
                        $userId,
                        'sync_ams_id'
                    );
                    ajax_success_response(_l('ams_sync_successfully'));
                } catch (\Exception $ex) {
                    ajax_error_response($ex->getMessage());
                }
            } else {
                ajax_error_response(_l('ams_not_found_vat'));
            }
        }
    }

    public function get_ams_company($clientIds)
    {
        if ($this->input->is_ajax_request()) {
            $clientIds = explode('-', $clientIds);
            if ($clientIds) {
                $companyIds = ClientAmsCompany::whereIn('client_id', $clientIds)->pluck('ams_company_id');

                $response = AmsService::search('companies/search', [
                    'query' => array_filter([
                        'ids' => $companyIds->implode(','),
                        'page_size' => $companyIds->count()
                    ])
                ]);
                $data = $response['data']['data'] ?? [];

                ajax_success_response(
                    '',
                    array_map(
                        fn ($company) => ['id' => $company['id'], 'text' => $company['display_name']],
                        $data
                    )
                );
            } else {
                ajax_error_response('Ids Not Found');
            }
        }
    }

    public function get_invoice($clientIds)
    {
        if ($this->input->is_ajax_request()) {
            $clientIds = explode('-', $clientIds);
            if ($clientIds) {
                $data = Invoice::select('id', 'number', 'number_format', 'prefix', 'date')
                ->paid()
                ->whereIn('clientid', $clientIds)
                ->get();

                ajax_success_response(
                    '',
                    $data->map(fn ($invoice) => ['id' => $invoice->id, 'text' => format_invoice_number($invoice)])
                );
            } else {
                ajax_error_response('Ids Not Found');
            }
        }
    }

    public function change_search_cv_invoice($id)
    {
        if ($this->input->post() && $this->input->is_ajax_request()) {
            if (!has_permission('customers', '', 'change_search_cv_invoice')) {
                ajax_access_denied();
            }
            $invoiceId = $this->input->post('invoice_id');
            $invoice = Invoice::whereId($invoiceId)
                ->paid()
                ->useNotExpired()
                ->first();

            if ($invoice) {
                $paidAt = $invoice->paymentRecord->date;
                $mappedSearchCvs = ClientAmsSearchPackage::query()
                    ->select('crm_itemable_id', DB::raw('count(id) as qty'))
                    ->where('invoice_id', $invoiceId)
                    ->whereNotNull('crm_itemable_id')
                    ->groupBy('crm_itemable_id')
                    ->get()
                    ->keyBy('crm_itemable_id');

                $searchCvInvoiceItems = Itemable::query()
                    ->select('id', 'qty')
                    ->where('rel_id', $invoiceId)
                    ->invoiceType()
                    ->get();

                $availableSearchCvItems = $searchCvInvoiceItems->map(function ($searchCvItem) use ($mappedSearchCvs) {
                    $mappedSearchCv = $mappedSearchCvs->get($searchCvItem->id);
                    if (!$mappedSearchCv || $mappedSearchCv->qty < $searchCvItem->qty) {
                        return $searchCvItem->id;
                    }
                    return 0;
                })->filter(fn($id) => $id > 0);

                if ($paidAt && $availableSearchCvItems->count()) {
                    ClientAmsSearchPackage::whereId($id)->update([
                        'paid_at' => $paidAt,
                        'crm_itemable_id' => $availableSearchCvItems->first(),
                        'invoice_id' => $invoiceId
                    ]);
                    ajax_success_response(_l('ams_search_cv_update_invoice_success'), ['updated' => true]);
                }
                ajax_success_response(_l('ams_search_cv_update_invoice_failed'), ['updated' => false]);
            } else {
                ajax_error_response(_l('ams_job_manage_invoice_not_found_invoice'));
            }
        }
    }

    public function is_churn_new_customer($clientId = null, $type = 'invoice', $relId = 0)
    {
        if ($this->input->is_ajax_request()) {
            ajax_success_response(
                '',
                $clientId
                    ? Client::query()
                        ->where('userid', $clientId)
                        ->isChurnNewType()
                        ->when(
                            $type == 'invoice',
                            fn($query) => $query->doesntHaveInvoiceNewDiscountProgram($relId),
                            fn($query) => $query->doesntHaveEstimateNewDiscountProgram($relId),
                        )
                        ->exists()
                      || is_po_estimate_new_churn($relId, $type)
                    : false
            );
        }
    }
}

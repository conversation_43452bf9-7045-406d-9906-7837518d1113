<?php

use Carbon\Carbon;
use Entities\CrawlerJob;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property App_Form_validation $form_validation
 * @property App_Input $input
 */
class Crawler extends App_Controller
{

    public function _remap($method, $params = array())
    {
        $method = str_replace('-', '_', $method);
        if (method_exists($this, str_replace('-', '_', $method))) {
            return call_user_func_array(array($this, $method), $params);
        }
        return $this->error('Not found!');
    }

    protected function validate($requests, $rules)
    {
        $this->form_validation->set_data($requests)->set_rules($rules);
        if ($this->form_validation->run() == false) {
            return $this->form_validation->errors_array();
        }
        return false;
    }

    protected function success($message, $data = [])
    {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }

    public function error($message, $code = null)
    {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $message,
            'code' => $code,
        ]);
    }

    public function error_validation($message)
    {
        header('Content-Type: application/json');
        header('HTTP/1.0 422 Unprocessable Content');
        echo json_encode([
            'success' => false,
            'message' => $message
        ]);
    }

    private function parsePostedAtValue(string $postedAt)
    {
        try {
            return Carbon::parse($postedAt)->unix();
        } catch (\Exception $ex) {
            return null;
        }
    }

    public function store_jobs()
    {
        $requestBody = json_decode($this->input->raw_input_stream, true);
        CrawlerJob::upsert(collect($requestBody)->map(function ($job) {
            return [
                'job_id' => $job['job_id'] ?? '',
                'title' => $job['title'] ?? '',
                'company' => $job['company'] ?? '',
                'type' => $job['type'] ?? '',
                'from_page' => $job['from_page'] ?? '',
                'posted_at' => time(), // $this->parsePostedAtValue($job['posted_at']),
                'working_model' => $job['working_model'] ?? '',
                'location' => $job['location'] ?? '',
                'skills' => $job['skills'] ?? '',
                'salary' => $job['salary'] ?? '',
                'description' => $job['description'] ?? '',
                'url' => $job['url'] ?? '',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
        })->toArray(), ['url', 'from_page'], ['job_id', 'title', 'company', 'type', 'posted_at', 'working_model', 'location', 'skills', 'salary', 'description', 'updated_at']);

        return $this->success('OK', []);
    }
}

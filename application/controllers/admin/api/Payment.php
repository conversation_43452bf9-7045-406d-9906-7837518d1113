<?php

use app\services\ReportService;
use Carbon\Carbon;
use Entities\AmsPackage;
use Entities\AmsSearchPackage;
use Entities\ClientAmsSearchPackage;
use Entities\Contact;
use Entities\Item;
use Entities\Client;
use Entities\ClientPayerEcRecv;
use Entities\ClientPayerEcRecvTrans;
use Entities\Invoice;
use Entities\Itemable;
use Entities\PaymentMode;
use Validators\CreateInvoiceValidator;

defined('BASEPATH') or exit('No direct script access allowed');

require_once(APPPATH . 'controllers/admin/api/Base.php');

/**
 * @property Payments_model $payments_model
 */
class Payment extends Base
{
    protected function create_invoice()
    {
        try {
            $requestData = $this->input->post();
            if ($errors = $this->validate($requestData, CreateInvoiceValidator::rules())) {
                return $this->error_validation($errors);
            }

            $products = $requestData['products'] ?? [];
            if (!is_array($products)) {
                return $this->error_validation('product is not an array');
            }
            $amsPackagesIds = AmsPackage::pluck('ams_package_id')->toArray();
            foreach ($products as $index => $product) {
                if (!isset($product['id'], $product['type'], $product['quantity'], $product['unit_price'])) {
                    return $this->error_validation('product[' . $index . '] Empty required fields');
                }

                if (
                    $product['type'] == AmsPackage::PACKAGE_TYPE_PACKAGE &&
                    (!isset($product['taxonomy_id']) || !in_array($product['taxonomy_id'], $amsPackagesIds))
                ) {
                    return $this->error_validation('product[' . $index . '] taxonomy_id is required for product package');
                }
            }

            $crmClientId = $requestData['crm_company_id'];

            // Check client's contact then create new contact if not exists
            $clientContactId = Contact::where([
                'email' => $requestData['email'],
                'userid' => $crmClientId
            ])->value('id');

            if (!$clientContactId) {
                $clientContactId = $this->clients_model->add_contact([
                    "fullname" => $requestData['fullname'],
                    "title" => "n/a",
                    "email" => $requestData['email'],
                    "phonenumber" => $requestData['phone'] ?? '',
                    "password" => null,
                    'donotsendwelcomeemail' => true,
                    'birthday' => null
                ], $crmClientId);
            }

            $packageItems = AmsPackage::query()
                ->whereHas('item', fn($query) => $query->where(['group_id' => Item::NEW_2024_GROUP_ID]))
                ->with([
                    'item:id,description,long_description,tax,unit',
                    'item.taxable'
                ])
                ->isOnlinePayment()
                ->get()
                ->keyBy('ams_package_id');

            $creditItems = AmsSearchPackage::query()
                ->whereHas('item', fn($query) => $query->where(['group_id' => Item::CREDIT_GROUP_ID]))
                ->with([
                    'item:id,description,long_description,tax,unit',
                    'item.taxable'
                ])
                ->isOnlinePayment()
                ->get()
                ->keyBy('ams_search_package_id');

            // Prepare invoice items
            $invoiceItems = [];
            $subTotal = $total = 0;

            foreach ($products as $index => $product) {
                $invoiceItem = $product['type'] == AmsPackage::PACKAGE_TYPE_PACKAGE
                    // Packages
                    ? $packageItems->get($product['taxonomy_id'])
                    // Credits
                    : $creditItems->get($product['id']);

                $item = $invoiceItem['item'];

                $taxRate = $item['taxable']['taxrate'];
                $unitPrice = round($product['unit_price']);

                $invoiceItems[] = [
                    "item_id" => $item['id'],
                    "order" => $index + 1,
                    "description" => $item['description'],
                    "long_description" => $item['long_description'],
                    "qty" => $product['quantity'],
                    "unit" => $item['unit'],
                    "rate" => $unitPrice,
                    "taxname" => [
                        $item['taxable']['name'] . '|' . $taxRate
                    ],
                ];

                $itemTotal = $product['quantity'] * $unitPrice;
                $subTotal += $itemTotal;
                $total += $itemTotal + round(($itemTotal * ($taxRate / 100)));
            }

            $paidAt = Carbon::parse($requestData['paid_at']);
            $client = Client::select('userid', 'company')->where('userid', $crmClientId)->first();
            $paymentModeId = PaymentMode::where("name", PaymentMode::PAYMENT_NAME_MEGAPAY)->value('id');
            $invoice = [
                'clientid' => $crmClientId,
                'contact_id' => $clientContactId,
                'customer_source' => 'Online Payment',
                'show_shipping_on_invoice' => 'on',
                'number' => str_pad(get_option('next_invoice_number'), get_option('number_padding_prefixes'), '0', STR_PAD_LEFT),
                'date' => $paidAt->format('d.m.Y'),
                'duedate' => '',
                'invoice_closing_date' => '',
                'billing_street' => '',
                'custom_fields' => [
                    'invoice' => [
                        // * Ngày xuất - customfield
                        14 => $paidAt->format('d.m.Y'),
                        // * Shorten name - customfield
                        19 => $client->company
                    ]
                ],
                "allowed_payment_modes" => [$paymentModeId],
                'currency' => '3',
                'sale_agent' => 0,
                'recurring' => '0',
                'discount_type' => '',
                'show_quantity_as' => '1',
                'newitems' => $invoiceItems,
                "subtotal" => $subTotal,
                'discount_percent' => 0,
                'discount_total' => 0.00,
                'discount_fixed' => 0,
                "total" => $total,
                "ams_order_id" => $requestData['ams_order_id'],
                "ams_order_code" => $requestData['ams_order_code'],
            ];
            if ($client->customerAdmin) {
                $invoice["addedfrom"] = $client->customerAdmin->staff_id;
            }
            $this->load->model('invoices_model');
            $id = $this->invoices_model->add($invoice, false, true);
            $mapItems = [];

            if ($id) {
                $paymentData = [
                    "invoiceid" => $id,
                    "amount" => $total,
                    "date" => $paidAt->format('d.m.Y'),
                    "paymentmode" => $paymentModeId,
                    "do_not_redirect" => "on",
                    'do_not_send_email_template' => true,
                    'auto_record_payment' => true,
                ];
                $this->load->model('payments_model');
                $this->payments_model->process_payment($paymentData, '', true);

                $amsCompanyId = $requestData['ams_company_id'];

                // Fetch all created credit items
                $creditItems = Itemable::select('id', 'item_id')
                    ->where('rel_id', $id)
                    ->invoiceType()
                    ->isCredit()
                    ->with([
                        'item:id',
                        'item.amsSearchPackage:crm_item_id,ams_search_package_id'
                    ])
                    ->get()
                    ->map(function ($itemable) use ($id, $crmClientId, $amsCompanyId) {
                        return [
                            'client_id' => $crmClientId,
                            'ams_company_id' => $amsCompanyId,
                            'invoice_id' => $id,
                            'ams_search_package_id' => $itemable->item->amsSearchPackage->ams_search_package_id,
                            'crm_itemable_id' => $itemable->id,
                        ];
                    })
                    ->keyBy('ams_search_package_id');

                if ($creditItems->count()) {
                    $mapItems = [];

                    collect($products)->each(function ($product) use ($creditItems, &$mapItems, $paidAt) {
                        $creditItem = $creditItems->get($product['id']);
                        if ($creditItem) {
                            $searchPackageIds = $product['company_search_package_ids'];
                            foreach ($searchPackageIds as $id) {
                                $mapItems[] = array_merge($creditItem, [
                                    'ams_company_search_package_id' => $id,
                                    'paid_at' => $paidAt,
                                ]);
                            }
                        }
                    });
                    count($mapItems) && ClientAmsSearchPackage::insert($mapItems);
                }
            }
            return $this->success('Ok', ['crm_invoice_id' => $id]);
        } catch (\Throwable $ex) {
            _exception_handler($ex);
            return $this->error($ex->getMessage());
        }
    }

    protected function paid_purchase_order()
    {
        try {
            $requestData = collect(json_decode($this->input->raw_input_stream, true));
            $receivableId = $requestData['receivableIdList'][0]['receivableId'] ?? null;

            if (!$receivableId) {
                return $this->success('Ok', []);
            }

            $receivedDateTime = Carbon::createFromFormat('YmdHis', $requestData['recvDtm']);
            $receivable = ClientPayerEcRecv::where('receivable_id', $receivableId)->firstOrFail();
            $invoice = $receivable->invoice;

            if (!$invoice || $invoice->status === Invoice::STATUS_PAID) {
                return $this->success('Ok', []);
            }

            $paymentData = [
                'invoiceid' => $invoice->id,
                'amount' => $requestData['depositAmt'],
                'date' => $receivedDateTime->format('d.m.Y'),
                'paymentmode' => PAYMENT_MODE_ID, // Techcombank
                'transactionid' => $requestData['bankTransId'],
                'do_not_redirect' => 'on',
                'do_not_send_email_template' => true,
                'auto_record_payment' => true,
                'note' => $requestData['remark'] ?? '',
            ];

            $this->load->model('payments_model');
            $paymentId = $this->payments_model->process_payment($paymentData, '', true);

            ClientPayerEcRecvTrans::create([
                'client_payer_ec_recv_id' => $receivable->id,
                'receivable_id' => $receivableId,
                'trans_id' => $requestData['transId'],
                'remark' => $requestData['remark'],
                'recv_at' => $receivedDateTime,
                'bank_code' => $requestData['bankCode'],
                'deposit_amount' => $requestData['depositAmt'],
                'deposit_at' => Carbon::createFromFormat('YmdHis', $requestData['depositDtm']),
                'bank_trans_id' => $requestData['bankTransId'],
            ]);

            $invoice->refresh();
            if ($paymentId && $invoice->isPaid()) {
                ReportService::syncInvoiceItemsToReport($invoice->id);
                ClientAmsSearchPackage::syncPaidSearchCv($invoice->id);
            }

            return $this->success('Ok', []);
        } catch (\Throwable $ex) {
            _exception_handler($ex);
            return $this->error($ex->getMessage());
        }
    }
}

<?php

use Entities\AmsSearchPackage;
use Entities\Client as EntitiesClient;
use Entities\ClientAmsCompany;
use Entities\ClientAmsSearchPackage;
use Validators\CreateClientValidator;
use Illuminate\Support\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

require_once(APPPATH . 'controllers/admin/api/Base.php');

class Client extends Base
{
    public const ERROR_NOT_COMPANY_PAID_ALREADY = 'ERR_PAID_ALREADY';
    public const WARN_NOT_COMPANY_NOT_PAID_YET = 'WARN_NOT_PAID_YET';

    protected function get()
    {
        $rules = [
            [
                'field' => 'ams_company_id',
                'rules' => ['required', 'integer']
            ],
            [
                'field' => 'crm_company_id',
                'rules' => ['integer']
            ],
        ];
        $this->load->library('form_validation');
        $requestData = $this->input->get();
        $this->form_validation
            ->set_data($requestData)
            ->set_rules($rules);

        if ($this->form_validation->run() == FALSE) {
            return $this->error_validation($this->form_validation->errors_array());
        }

        $clients = EntitiesClient::where(function ($query) use ($requestData) {
            return $query->whereHas('clientAmsCompanies', fn ($query) => $query->where('ams_company_id', $requestData['ams_company_id']))
                ->when(!empty($requestData['ams_company_vat']), fn ($query) => $query->orWhere('vat', $requestData['ams_company_vat']));
        })
        ->when(!empty($requestData['crm_company_id']), fn ($query) => $query->where('userid', $requestData['crm_company_id']))
        ->active()
        ->selectRaw('userid as id, business_name, `vat` as tax_number, phonenumber as phone_number')
        ->get();

        return $this->success('Ok', $clients->toArray());
    }

    protected function store()
    {
        $this->load->helper('unleash');

        // Get all post data
        $requestData = $this->input->post();

        // Set form validation rules base on feature enabled
        $this->form_validation
            ->set_data($requestData)
            ->set_rules(is_feature_free_post_enabled() ? CreateClientValidator::rulesForDev2062() : CreateClientValidator::rules());

        // Validate request data
        if ($this->form_validation->run() == false) {
            return $this->error_validation($this->form_validation->errors_array());
        }

        // Check tax_number is paid or not
        $clientHasInvoice = EntitiesClient::select('userid')
            ->withCount(['invoices as paidInvoices' => fn($query) => $query->paid()])
            ->where('vat', $requestData['tax_number'])
            ->first();

        // If already have this tax number
        if ($clientHasInvoice) {
            if ($clientHasInvoice->paidInvoices) {
                return $this->error('MST này đã có ít nhất 1 đơn hàng ở trạng thái đã thanh toán', self::ERROR_NOT_COMPANY_PAID_ALREADY);
            }
            return $this->success('MST này chưa có đơn hàng nào ở trạng thái đã thanh toán', self::WARN_NOT_COMPANY_NOT_PAID_YET);
        }

        $this->load->model('clients_model');

        // Create client
        // Create contact
        $clientData = [
            // Client data
            'vat' =>  $requestData['tax_number'],
            'business_name' => $requestData['business_name'],
            'address' => $requestData['business_address'] ?? '',
            'company' => $requestData['company_name'] ?? null,
            'type_of_customer' => 'FRESH',
            'usage_behavior' => 'FRESH',
            'source' => 'signup',
            'approved_at' => date('Y-m-d H:i:s'),
            // Contact data
            'email' => $requestData['email'],
            'fullname' => is_feature_free_post_enabled() ? $requestData['contact_name'] : 'n/a',
            'title' => 'n/a',
            'contact_phonenumber' => $requestData['contact_phone'],
            'is_primary' => true,
            'birthday' => null
        ];
        $userId = $this->clients_model->add($clientData, true);

        // Move to free DB
        if ($userId) {
            hooks()->do_action('move_to_free_data', $userId);
        }

        return $this->success('Ok', ['id' => $userId]);
    }

    protected function map($amsCompanyId, $crmCompanyId)
    {
        $clientAmsCompany = ClientAmsCompany::firstOrCreate([
            'client_id' => $crmCompanyId,
            'ams_company_id' => $amsCompanyId,
        ]);

        return $this->success('Ok', $clientAmsCompany->toArray());
    }

    protected function get_mapped()
    {
        $rules = [
            [
                'field' => 'companies[]',
                'rules' => [
                    'required',
                    [
                        'valid_array',
                        function ($company) {
                            $companyArr = explode(',', $company);
                            // Make sure each item have these keys's value
                            if (empty($companyArr[0]) || empty($companyArr[1]))
                            {
                                return false;
                            }
                            return true;
                        }
                    ]
                ]
            ],
        ];
        $this->load->library('form_validation');
        $requestData = $this->input->post();
        $this->form_validation
            ->set_data($requestData)
            ->set_rules($rules)
            ->set_message([
                'valid_array' => 'Must have format crm_id,ams_id'
            ]);

        if ($this->form_validation->run() == FALSE) {
            return $this->error_validation($this->form_validation->errors_array());
        }

        $clientAmsCompanies = ClientAmsCompany::select('client_id', 'ams_company_id');
        $companies = $requestData['companies'] ?? [];
        foreach ($companies as $company) {
            $companyArr = explode(',', $company);
            $clientAmsCompanies = $clientAmsCompanies->orWhere(fn($query) =>
                $query->where('client_id', $companyArr[0])
                    ->where('ams_company_id', $companyArr[1])
            );
        }
        $mappedClients = $clientAmsCompanies->get()->map(fn($company) => $company->client_id . ',' . $company->ams_company_id);
        return $this->success('Ok', $mappedClients->toArray());
    }

    protected function get_email_customer_admin()
    {
        $requestData = $this->input->post();
        $companyId = $requestData['company_id'];
        $customerAdmin = ClientAmsCompany::where('ams_company_id', $companyId)->with('client.customerAdmin.staff')->get();
        $data = $customerAdmin->pluck('client.customerAdmin.staff.email');

        return $this->success('Ok', $data);
    }

    /**
     * Handle sync ams search cv when ams perform ADD/EDIT/DELETE credits
     *
     * @return void
     */
    protected function sync_ams_search_cv()
    {
        try {
            $request_data = $this->input->post();
            $rules = [
                [

                    'field' => 'ams_company_id',
                    'rules' => [
                        'required',
                        [
                            'exists_mapping_company',
                            function ($ams_company_id) {
                                return ClientAmsCompany::where('ams_company_id', $ams_company_id)->exists();
                            }
                        ]
                    ]
                ],
                [
                    'field' => 'ams_company_search_package_id',
                    'rules' => ['required'],
                ]
            ];
            if (!isset($request_data['delete'])) {
                $rules = array_merge($rules, [
                    [
                        'field' => 'search_package_id',
                        'rules' => [
                            'required',
                            [
                                'exists_search_package',
                                function ($search_package_id) {
                                    return AmsSearchPackage::where('ams_search_package_id', $search_package_id)->exists();
                                }
                            ]
                        ],
                        [
                            'field' => 'created_at',
                            'rules' => ['required'],
                        ]
                    ],
                ]);
            }
            $this->load->library('form_validation');
            $this->form_validation
                ->set_data($request_data)
                ->set_rules($rules)
                ->set_message([
                    'exists_search_package' => 'Package does not exists in CRM system',
                    'exists_mapping_company' => 'Does not have any CRM company match with this company',
                ]);

            if ($this->form_validation->run() == FALSE) {
                return $this->error_validation($this->form_validation->errors_array());
            }

            $ams_company_search_package_id = $request_data['ams_company_search_package_id'];
            if (isset($request_data['delete'])) {
                $result = ClientAmsSearchPackage::where('ams_company_search_package_id', $ams_company_search_package_id)->delete();
                return $result ? $this->success('delete success') : $this->error('Deleted failed');
            }

            $ams_company_id = $request_data['ams_company_id'];
            $search_package_id = $request_data['search_package_id'];
            $created_at = $request_data['created_at'];
            $client_ids = ClientAmsCompany::where('ams_company_id', $ams_company_id)->pluck('client_id');
            $clientAmsSearchPackages = $client_ids->map(fn($clientId) => [
                'client_id' => $clientId,
                'ams_company_id' => $ams_company_id,
                'ams_search_package_id' => $search_package_id,
                'ams_company_search_package_id' => $ams_company_search_package_id,
                'paid_at' => $created_at,
            ])->toArray();
            ClientAmsSearchPackage::upsert($clientAmsSearchPackages, ['client_id', 'ams_company_id', 'ams_company_search_package_id'], ['ams_search_package_id', 'paid_at']);
            return $this->success('Create or update success');
        } catch (\Throwable $ex) {
            return $this->error($ex->getMessage());
        }
    }

    protected function send_notification_ca()
    {
        $requestData = $this->input->post();
        $companyId = $requestData['company_id'];
        $email = $requestData['email'];
        $amsCompanies = ClientAmsCompany::where('ams_company_id', $companyId)->with('client.customerAdmin.staff')->get();
        $notifiedUsers = [];
        foreach ($amsCompanies as $amsCompany) {
            $client = $amsCompany->client;
            $clientName = $client->company;
            $clientId = $amsCompany->client_id;
            $staff = $client->customerAdmin->staff;
            if ($staff) {
                if (add_notification([
                    'description' => 'login_employer_dash',
                    'touserid' => $staff['staffid'],
                    'fromcompany' => 1,
                    'fromuserid' => 0,
                    'additional_data' => serialize([
                        $clientName,
                        Carbon::now()->format('H:i:s d/m/Y'),
                        $clientId,
                        $companyId,
                        $email
                    ]),
                    'link' => '/clients/client/' . $clientId,
                ])) {
                    array_push($notifiedUsers, $staff['staffid']);
                }
            }
        }
        pusher_trigger_notification($notifiedUsers);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');

use <PERSON><PERSON>\Crypto\Rsa\PrivateKey;
use <PERSON>tie\Crypto\Rsa\PublicKey;

/**
 * @property App_Form_validation $form_validation
 * @property App_Input $input
 */
class Base extends App_Controller
{
    private const VERIFY_TEXT = 'CRM API AUTH';
    public function __construct()
    {
        parent::__construct();
        $this->auth_check();
        $this->load->library('form_validation');
    }

    public function _remap($method, $params = array())
    {
        $method = str_replace('-', '_', $method);
        if (method_exists($this, str_replace('-', '_', $method))) {
            return call_user_func_array(array($this, $method), $params);
        }
        return $this->error('Not found!');
    }

    /**
     * Check api auth before doing any action
     */
    private function auth_check()
    {
        try {
            $apiAuthCode = $this->input->get_request_header('api-auth-code');
            // Verify api auth code before doing any action from API
            if ($apiAuthCode) {
                $signature = PrivateKey::fromString(base64_decode(API_PRIVATE_KEY))->sign(self::VERIFY_TEXT);
                $publicKey = PublicKey::fromString(base64_decode($apiAuthCode));
                if (!$publicKey->verify(self::VERIFY_TEXT, $signature)) {
                    $this->error_un_authorized('UNAUTHORIZED');
                    exit;
                }
            } else {
                $this->error_un_authorized('UNAUTHORIZED');
                exit;
            }
        } catch (\Exception $ex) {
            _exception_handler($ex);
            $this->error_un_authorized('UNAUTHORIZED');
            exit;
        }
    }

    protected function validate($requests, $rules)
    {
        $this->form_validation->set_data($requests)->set_rules($rules);
        if ($this->form_validation->run() == false) {
            return $this->form_validation->errors_array();
        }
        return false;
    }

    protected function success($message, $data = [])
    {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }

    public function error($message, $code = null)
    {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $message,
            'code' => $code,
        ]);
    }

    public function error_validation($message)
    {
        header('Content-Type: application/json');
        header('HTTP/1.0 422 Unprocessable Content');
        echo json_encode([
            'success' => false,
            'message' => $message
        ]);
    }

    public function error_un_authorized($message)
    {
        header('Content-Type: application/json');
        header('HTTP/1.0 401 UNAUTHORIZED');
        echo json_encode([
            'success' => false,
            'message' => $message
        ]);
    }
}

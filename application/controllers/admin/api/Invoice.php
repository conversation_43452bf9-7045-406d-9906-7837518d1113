<?php

use app\services\AmsPackageService;
use app\services\ReportService;

defined('BASEPATH') or exit('No direct script access allowed');

require_once(APPPATH . 'controllers/admin/api/Base.php');

class Invoice extends Base
{
    protected function get_invoices($amsCompanyId)
    {
        $requestData = $this->input->get();
        $invoicePackages = AmsPackageService::get_invoice_packages(
            $amsCompanyId,
            array_filter([
                $requestData['from_date'] ?? null,
                $requestData['to_date'] ?? null,
            ]) ?? [],
            $requestData['page'] ?? 1,
            $requestData['size'] ?? 5,
            $requestData['orders'] ?? []
        );
        $availablePackages = AmsPackageService::get_available_packages($amsCompanyId);
        $availableByPackage = $availablePackages->groupBy('ams_package_id')->map(fn($items) => $items->sum('available_packages'));

        return $this->success('', [
            'invoices' => $invoicePackages,
            'summary' => [
                'total' => $availableByPackage->sum(),
                'packages' => $availableByPackage
            ]
        ]);
    }

    /**
     * Fetch or count available package to make decision in relevation system
     *
     * @param mixed $amsCompanyId
     * @return mixed
     */
    protected function get_available_packages($amsCompanyId)
    {
        return $this->success('', AmsPackageService::get_available_packages($amsCompanyId, $this->input->get('package_id')));
    }

    /**
     * Check available package for ams company id
     *
     * @param mixed $amsCompanyId
     * @return mixed
     */
    protected function check_has_available_packages($amsCompanyId)
    {
        return $this->success('', AmsPackageService::has_available_packages($amsCompanyId));
    }

    /**
     * Fetch or count available package to make decision in relevation system
     *
     * @param mixed $amsCompanyId
     * @return mixed
     */
    protected function get_available_invoices($amsCompanyId)
    {
        $selectedInvoiceId = $this->input->get('selected_invoice_id');
        $selectedAmsPackageId = $this->input->get('selected_ams_package_id');
        $unpaidInvoice = $this->input->get('unpaid_invoice');
        $availablePackages = AmsPackageService::get_available_invoices(
            $amsCompanyId,
            $selectedInvoiceId,
            $selectedAmsPackageId,
            $unpaidInvoice
        );
        return $this->success('', $availablePackages);
    }

    /**
     * Fetch or count available package to make decision in relevation system
     *
     * @param mixed $amsCompanyId
     * @return mixed
     */
    protected function get_packages_by_invoice($invoiceId)
    {
        $selectedAmsPackageId = $this->input->get('selected_ams_package_id');
        $unpaidInvoice = $this->input->get('unpaid_invoice');
        $packagesIds = AmsPackageService::get_packages_by_invoice($invoiceId, $selectedAmsPackageId, $unpaidInvoice);
        return $this->success('', $packagesIds);
    }

    /**
     * Sync invoice from AMS to CRM
     *
     * @param int $amsCompanyId
     */
    protected function sync_invoice($amsCompanyId)
    {
        $crmInvoiceId = $this->input->post('crm_invoice_id');
        $amsJobId = $this->input->post('ams_job_id');
        $amsPackageId = $this->input->post('ams_package_id');
        $isFreePackage = $this->input->post('is_free_package');
        $needSyncOpenJob = $this->input->post('need_sync_open_job');

        if (!$amsJobId) {
            return $this->error_validation([
                'ams_job_id' => 'ams_job_id is required!'
            ]);
        }

        $syncResult = AmsPackageService::sync_invoice(
            $amsCompanyId,
            $amsJobId,
            $crmInvoiceId,
            $amsPackageId,
            $isFreePackage,
            $needSyncOpenJob
        );
        return $this->success('Synced!', $syncResult);
    }

    protected function create_job_daily_revenue()
    {
        $amsJobId = $this->input->post('job_id');
        $revenueDate = $this->input->post('revenue_date');

        if ($amsJobId && $revenueDate) {
            ReportService::importDailyRevenue([$amsJobId], $revenueDate);
        }
    }
}

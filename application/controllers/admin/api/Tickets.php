<?php

use Entities\ClientAmsCompany;
use Entities\CustomerAdmin;

defined('BASEPATH') or exit('No direct script access allowed');

require_once(APPPATH . 'controllers/admin/api/Base.php');

/**
 * API Tickets
 *
 * @property CI_Input $input
 * @property Tickets_model $tickets_model
 */
class Tickets extends Base
{
    protected function get($id)
    {
        // Load model
        $this->load->model('tickets_model');

        $ticket = $this->tickets_model->get_ticket_by_id($id);

        return $this->success('Ok', ['ticket' => $ticket]);
    }

    public function store()
    {
        // Load model
        $this->load->model('tickets_model');

        // Handle store data
        $data = $this->input->post();
        $data['message'] = html_purify($this->input->post('message', false));
        $data['note_description'] = $this->input->post('message', false);

        $amsCompanyId = $data['ams_company_id'] ?? null;
        if ($amsCompanyId) {
            $clientId = ClientAmsCompany::where('ams_company_id', $amsCompanyId)->value('client_id');
            if ($clientId) {
                $staffid = CustomerAdmin::where('customer_id', $clientId)->value('staff_id');
                $data['userid']    = $clientId;
                $data['assigned'] = $staffid ?? 0;
            }
        }
        unset($data['ams_company_id']);

        $id = $this->tickets_model->add($data, 0); // .0 mean system created

        if (! $id) {
            return $this->error('Error');
        }

        return $this->success('Ok', ['id' => $id]);
    }

    public function reply($id)
    {
        $this->load->model('tickets_model');

        $ticket = $this->tickets_model->get_ticket_by_id($id);
        if (!$ticket) {
            return $this->error('Ticket not found');
        }

        $data = $this->input->post();
        $data['message'] = html_purify($this->input->post('message', false));

        $this->tickets_model->add_reply($data, $id);

        return $this->success('Ok', ['ticketid' => $id]);
    }
}

<?php

use app\services\AccountingService;
use Entities\InvoiceRequest;
use Entities\StaffPermission;
use app\services\ReportService;
use Carbon\Carbon;
use Entities\ClientAmsSearchPackage;
use Entities\Invoice;
use Entities\Itemable;
use Entities\MInvoice;
use Entities\Staff;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Invoices_model $invoices_model
 * @property Invoice_items_model $invoice_items_model
 */
class Invoices extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('invoices_model');
        $this->load->model('credit_notes_model');
    }

    /* Get all invoices in case user go on index page */
    public function index($id = '')
    {
        $this->list_invoices($id);
    }

    /* List all invoices datatables */
    public function list_invoices($id = '')
    {
        if (
            !has_permission('invoices', '', 'view')
            && !has_permission('invoices', '', 'view_own')
            && get_option('allow_staff_view_invoices_assigned') == '0'
        ) {
            access_denied('invoices');
        }

        close_setup_menu();

        $this->load->model('payment_modes_model');
        $data['payment_modes']        = $this->payment_modes_model->get('', [], true);
        $data['invoiceid']            = $id;
        $data['title']                = _l('invoices');
        $data['invoices_years']       = $this->invoices_model->get_invoices_years();
        $data['invoices_sale_agents'] = $this->invoices_model->get_sale_agents();
        $data['invoices_statuses']    = $this->invoices_model->get_statuses();
        $data['staff'] = get_list_staff();
        $data['bodyclass']            = 'invoices-total-manual';
        $this->load->view('admin/invoices/manage', $data);
    }

    /* List invoice request*/
    public function approval()
    {
        $this->load->model('Invoices_model');
        // Danh sách invoice request
        $data['list_invoice'] = $this->invoices_model->list_invoice_request();
        $data['invoices_statuses']    = $this->invoices_model->get_statuses();
        $this->load->view('admin/invoices/invoice_addition_discount_approval', $data);
    }

    /* List invoice request*/
    public function addition_discount_approval_table()
    {
        $data = [];
        $this->app->get_table_data('invoice_addition_discount_approval', [
            'data'     => $data,
        ]);
    }

    /**
     * PO balance
     *
     * URL: admin/invoices/po_balance
     */
    public function po_balance()
    {
        $data = [
            'staff' => get_list_staff(),
            'invoices_statuses' => $this->invoices_model->get_statuses(),
        ];

        $this->load->view('admin/invoices/invoices_po_balance', $data);
    }

    /**
     * PO balance table
     *
     * URL: admin/invoices/po_balance_table
     */
    public function po_balance_table()
    {
        $data = [];
        $this->app->get_table_data('invoices_po_balance', [
            'data' => $data,
        ]);
    }


    public function reject_addition_discount($invoiceId)
    {
        if (
            !has_permission('invoices', '', 'po_approval_as_manager')
            && !has_permission('invoices', '', 'po_approval_as_leader')
        ) {
            ajax_access_denied();
        }

        try {
            $invoice = Invoice::find($invoiceId);
            $today = Carbon::now();
            $invoice->fill([
                'rejected_by' => get_staff_user_id(),
                'addition_discount_rejected_at' => $today,
            ])->save();
            $this->invoices_model->log_invoice_activity(
                $invoiceId,
                'reject_addition_discount',
                !is_staff_logged_in() ? true : false,
                serialize([
                    get_staff_full_name(get_staff_user_id()),
                    $today->format('Y-m-d H:i:s'),
                ])
            );

            $notification = add_notification([
                'fromcompany'     => true,
                'touserid'        => $invoice->addedfrom,
                'description'     => 'approve_addition_discount_notification',
                'link'            => 'invoices/approval',
                'additional_data' => serialize([
                    format_invoice_number($invoice) . ': ' . _l('reject_addition_discount', [get_staff_full_name(), $today->format('Y-m-d H:i:s')])
                ]),
            ]);

            if ($notification) {
                pusher_trigger_notification([$invoice->addedfrom]);
            }

            ajax_success_response('OK', [$invoiceId]);
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            ajax_error_response($ex->getMessage());
        }
    }

    public function approve_addition_discount($invoiceId)
    {
        if (
            !has_permission('invoices', '', 'po_approval_as_manager')
            && !has_permission('invoices', '', 'po_approval_as_leader')
        ) {
            ajax_access_denied();
        }

        try {
            $invoice = Invoice::find($invoiceId);
            $today = Carbon::now();
            $invoice->fill([
                'approved_by' => get_staff_user_id(),
                'addition_discount_approved_at' => $today,
            ])->save();
            $this->invoices_model->log_invoice_activity(
                $invoiceId,
                'approve_addition_discount',
                !is_staff_logged_in() ? true : false,
                serialize([
                    get_staff_full_name(get_staff_user_id()),
                    $today->format('Y-m-d H:i:s'),
                ])
            );

            $notification = add_notification([
                'fromcompany'     => true,
                'touserid'        => $invoice->addedfrom,
                'description'     => 'approve_addition_discount_notification',
                'link'            => 'invoices/list_invoices/' . $invoice->id,
                'additional_data' => serialize([
                    format_invoice_number($invoice) . ': ' . _l('approved_addition_discount', [get_staff_full_name(), $today->format('Y-m-d H:i:s')])
                ]),
            ]);

            if ($notification) {
                pusher_trigger_notification([$invoice->addedfrom]);
            }
            ajax_success_response('OK', [$invoiceId]);
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            ajax_error_response($ex->getMessage());
        }
    }

    /* List invoice request*/
    public function invoice_request()
    {
        $this->load->model('Invoices_model');
        // Danh sách invoice request
        $data['list_invoice'] = $this->invoices_model->list_invoice_request();
        $data['invoices_statuses']    = $this->invoices_model->get_statuses();
        $this->load->view('admin/invoices/invoice_request', $data);
    }

    /* ajax get_total_price_invoice */
    public function get_total_price_invoice()
    {
        $data = $this->input->post();
        $this->load->model('Invoices_model');
        $result = $this->invoices_model->get_total_price_invoice($data);
        ajax_success_response(_l('success_notfavorite'), $result);
    }

    /* List all recurring invoices */
    public function recurring($id = '')
    {
        if (
            !has_permission('invoices', '', 'view')
            && !has_permission('invoices', '', 'view_own')
            && get_option('allow_staff_view_invoices_assigned') == '0'
        ) {
            access_denied('invoices');
        }

        close_setup_menu();

        $data['invoiceid']            = $id;
        $data['title']                = _l('invoices_list_recurring');
        $data['invoices_years']       = $this->invoices_model->get_invoices_years();
        $data['invoices_sale_agents'] = $this->invoices_model->get_sale_agents();
        $this->load->view('admin/invoices/recurring/list', $data);
    }

    public function table($clientid = '')
    {
        if (
            !has_permission('invoices', '', 'view')
            && !has_permission('invoices', '', 'view_own')
            && get_option('allow_staff_view_invoices_assigned') == '0'
        ) {
            ajax_access_denied();
        }

        $this->load->model('payment_modes_model');
        $data['payment_modes'] = $this->payment_modes_model->get('', [], true);

        $this->app->get_table_data(($this->input->get('recurring') ? 'recurring_invoices' : 'invoices'), [
            'clientid' => $clientid,
            'data'     => $data,
        ]);
    }

    public function table_request()
    {
        $this->load->model('payment_modes_model');
        $data['payment_modes'] = $this->payment_modes_model->get('', [], true);

        $this->app->get_table_data('invoice_request', [
            'data'     => $data,
        ]);
    }

    public function client_change_data($customer_id, $current_invoice = '')
    {
        if ($this->input->is_ajax_request()) {
            $this->load->model('projects_model');
            $data                     = [];
            $data['billing_shipping'] = $this->clients_model->get_customer_billing_and_shipping_details($customer_id);
            $data['client_currency']  = $this->clients_model->get_customer_default_currency($customer_id);

            $data['customer_has_projects'] = customer_has_projects($customer_id);
            $data['billable_tasks']        = $this->tasks_model->get_billable_tasks($customer_id);

            if ($current_invoice != '') {
                $this->db->select('status');
                $this->db->where('id', $current_invoice);
                $current_invoice_status = $this->db->get(db_prefix() . 'invoices')->row()->status;
            }

            $_data['invoices_to_merge'] = !isset($current_invoice_status) || (isset($current_invoice_status) && $current_invoice_status != Invoices_model::STATUS_CANCELLED) ? $this->invoices_model->check_for_merge_invoice($customer_id, $current_invoice) : [];

            $data['merge_info'] = $this->load->view('admin/invoices/merge_invoice', $_data, true);

            $this->load->model('currencies_model');

            $__data['expenses_to_bill'] = !isset($current_invoice_status) || (isset($current_invoice_status) && $current_invoice_status != Invoices_model::STATUS_CANCELLED) ? $this->invoices_model->get_expenses_to_bill($customer_id) : [];

            $data['expenses_bill_info'] = $this->load->view('admin/invoices/bill_expenses', $__data, true);

            $data['contact_data'] = $this->clients_model->get_contacts($customer_id);

            echo json_encode($data);
        }
    }

    public function update_number_settings($id)
    {
        $response = [
            'success' => false,
            'message' => '',
        ];
        if (has_permission('invoices', '', 'edit')) {
            $affected_rows = 0;

            $this->db->where('id', $id);
            $this->db->update(db_prefix() . 'invoices', [
                'prefix' => $this->input->post('prefix'),
            ]);
            if ($this->db->affected_rows() > 0) {
                $affected_rows++;
            }

            if ($affected_rows > 0) {
                $response['success'] = true;
                $response['message'] = _l('updated_successfully', _l('invoice'));
            }
        }
        echo json_encode($response);
        die;
    }

    public function validate_invoice_number()
    {
        $isedit          = $this->input->post('isedit');
        $number          = $this->input->post('number');
        $date            = $this->input->post('date');
        $original_number = $this->input->post('original_number');
        $number          = trim($number);
        $number          = ltrim($number, '0');

        if ($isedit == 'true') {
            if ($number == $original_number) {
                echo json_encode(true);
                die;
            }
        }

        if (total_rows(db_prefix() . 'invoices', [
            'YEAR(date)' => date('Y', strtotime(to_sql_date($date))),
            'number' => $number,
            'status !=' => Invoices_model::STATUS_DRAFT,
        ]) > 0) {
            echo 'false';
        } else {
            echo 'true';
        }
    }

    public function add_note($rel_id)
    {
        if ($this->input->post() && user_can_view_invoice($rel_id)) {
            $this->misc_model->add_note($this->input->post(), 'invoice', $rel_id);
            echo $rel_id;
        }
    }

    public function get_notes($id)
    {
        if (user_can_view_invoice($id)) {
            $data['notes'] = $this->misc_model->get_notes($id, 'invoice');
            $this->load->view('admin/includes/sales_notes_template', $data);
        }
    }

    public function pause_overdue_reminders($id)
    {
        if (has_permission('invoices', '', 'edit')) {
            $this->db->where('id', $id);
            $this->db->update(db_prefix() . 'invoices', ['cancel_overdue_reminders' => 1]);
        }
        redirect(admin_url('invoices/list_invoices/' . $id));
    }

    public function resume_overdue_reminders($id)
    {
        if (has_permission('invoices', '', 'edit')) {
            $this->db->where('id', $id);
            $this->db->update(db_prefix() . 'invoices', ['cancel_overdue_reminders' => 0]);
        }
        redirect(admin_url('invoices/list_invoices/' . $id));
    }

    public function mark_as_cancelled($id)
    {
        if (!has_permission('invoices', '', 'edit') && !has_permission('invoices', '', 'create')) {
            access_denied('invoices');
        }

        $success = $this->invoices_model->mark_as_cancelled($id);

        if ($success) {
            ReportService::syncInvoiceItemsToReport($id);
            set_alert('success', _l('invoice_marked_as_cancelled_successfully'));
        }

        redirect(admin_url('invoices/list_invoices/' . $id));
    }

    public function unmark_as_cancelled($id)
    {
        if (!has_permission('invoices', '', 'edit') && !has_permission('invoices', '', 'create')) {
            access_denied('invoices');
        }
        $success = $this->invoices_model->unmark_as_cancelled($id);
        if ($success) {
            ReportService::syncInvoiceItemsToReport($id);
            set_alert('success', _l('invoice_unmarked_as_cancelled'));
        }
        redirect(admin_url('invoices/list_invoices/' . $id));
    }

    public function copy($id)
    {
        if (!$id) {
            redirect(admin_url('invoices'));
        }
        if (!has_permission('invoices', '', 'create')) {
            access_denied('invoices');
        }

        $invoice = Invoice::select(['clientid', 'is_churn_new_discount'])->where('id', $id)->first();
        if ($invoice && $invoice->clientid && $invoice->is_churn_new_discount && !does_not_have_discount_program($invoice->clientid, 'invoice')) {
            set_alert('danger', 'Unable to copy this PO due to already have PO with churn/new discount program!');
            redirect($_SERVER['HTTP_REFERER']);
        }

        $new_id = $this->invoices_model->copy($id);
        if ($new_id) {
            set_alert('success', _l('invoice_copy_success'));
            redirect(admin_url('invoices/invoice/' . $new_id));
        } else {
            set_alert('success', _l('invoice_copy_fail'));
        }
        redirect(admin_url('invoices/invoice/' . $id));
    }

    public function get_merge_data($id)
    {
        $invoice = $this->invoices_model->get($id);
        $cf      = get_custom_fields('items');

        $i = 0;

        foreach ($invoice->items as $item) {
            $invoice->items[$i]['taxname']          = get_invoice_item_taxes($item['id']);
            $invoice->items[$i]['long_description'] = clear_textarea_breaks($item['long_description']);
            $this->db->where('item_id', $item['id']);
            $rel              = $this->db->get(db_prefix() . 'related_items')->result_array();
            $item_related_val = '';
            $rel_type         = '';
            foreach ($rel as $item_related) {
                $rel_type = $item_related['rel_type'];
                $item_related_val .= $item_related['rel_id'] . ',';
            }
            if ($item_related_val != '') {
                $item_related_val = substr($item_related_val, 0, -1);
            }
            $invoice->items[$i]['item_related_formatted_for_input'] = $item_related_val;
            $invoice->items[$i]['rel_type']                         = $rel_type;

            $invoice->items[$i]['custom_fields'] = [];

            foreach ($cf as $custom_field) {
                $custom_field['value']                 = get_custom_field_value($item['id'], $custom_field['id'], 'items');
                $invoice->items[$i]['custom_fields'][] = $custom_field;
            }
            $i++;
        }
        echo json_encode($invoice);
    }

    public function get_bill_expense_data($id)
    {
        $this->load->model('expenses_model');
        $expense = $this->expenses_model->get($id);

        $expense->qty              = 1;
        $expense->long_description = clear_textarea_breaks($expense->description);
        $expense->description      = $expense->name;
        $expense->rate             = $expense->amount;
        if ($expense->tax != 0) {
            $expense->taxname = [];
            array_push($expense->taxname, $expense->tax_name . '|' . $expense->taxrate);
        }
        if ($expense->tax2 != 0) {
            array_push($expense->taxname, $expense->tax_name2 . '|' . $expense->taxrate2);
        }
        echo json_encode($expense);
    }

    /* Add new invoice or update existing */
    public function invoice($id = '')
    {
        if ($this->input->post()) {
            $invoice_data = $this->input->post();
            $invoice_data['is_churn_new_discount'] = isset($invoice_data['is_churn_new_discount']);
            unset($invoice_data['invoice_edit']);
            unset($invoice_data['discount_promotion_version']);
            unset($invoice_data['invoice_original_ad']);
            if ($id == '') {
                if (!has_permission('invoices', '', 'create')) {
                    access_denied('invoices');
                }

                if (isset($invoice_data['discount_percent']) && $invoice_data['discount_percent'] > 30) {
                    set_alert('danger', 'The Addition Discount entered is invalid');
                    redirect($_SERVER['HTTP_REFERER']);
                }

                $id = $this->invoices_model->add($invoice_data);
                if ($id) {
                    $message = _l('added_successfully', _l('invoice'));
                    $staffQuery = Staff::where('staffid', get_staff_user_id());
                    if (isset($invoice_data['discount_percent']) && 
                        $invoice_data['discount_percent'] > 0 && 
                        (
                            ($invoice_data['discount_percent'] > 10 & $staffQuery->clone()->isSaleader()->exists()) || 
                            $staffQuery->clone()->isSaleader()->doesntExist()
                        ) &&
                        !has_permission('invoices', '', 'po_approval_as_manager')
                    ) {
                        $message = _l('po_pending_approval_message');
                    }
                    set_alert('success', $message);
                    $redUrl = admin_url('invoices/list_invoices/' . $id);
                    ReportService::syncInvoiceItemsToReport($id);

                    if (isset($invoice_data['save_and_record_payment'])) {
                        $this->session->set_userdata('record_payment', true);
                    } elseif (isset($invoice_data['save_and_send_later'])) {
                        $this->session->set_userdata('send_later', true);
                    }

                    redirect($redUrl);
                }
            } else {
                if (!has_permission('invoices', '', 'edit', $id)) {
                    access_denied('invoices');
                }

                // If allow edit PO and then the number is greater than 30, then redirect with the error message
                if (Invoice::allowEditAD($id) && isset($invoice_data['discount_percent']) && $invoice_data['discount_percent'] > 30) {
                    set_alert('danger', 'The Addition Discount entered is invalid');
                    redirect($_SERVER['HTTP_REFERER']);
                }

                $originalAD = Invoice::where('id', $id)->value('discount_percent');
                $success = $this->invoices_model->update($invoice_data, $id);
                if ($success) {
                    ReportService::syncInvoiceItemsToReport($id);
                    ClientAmsSearchPackage::syncPaidSearchCv($id, true);
                    $invoice = Invoice::find($id);
                    $invoice->client->updateCustomerClassify2025();
                    $message = _l('updated_successfully', _l('invoice'));

                    $staffQuery = Staff::where('staffid', $invoice->addedfrom);
                    if (isset($invoice_data['discount_percent']) && 
                        $invoice_data['discount_percent'] > 0 && 
                        $invoice_data['discount_percent'] != $originalAD && 
                        (
                            ($invoice_data['discount_percent'] > 10 & $staffQuery->clone()->isSaleader()->exists()) || 
                            $staffQuery->clone()->isSaleader()->doesntExist()
                        ) &&
                        !has_permission('invoices', '', 'po_approval_as_manager')
                    ) {
                        $message = _l('po_pending_approval_message');
                    }

                    set_alert('success', $message);
                }
                redirect(admin_url('invoices/list_invoices/' . $id));
            }
        }
        if ($id == '') {
            $title                  = _l('create_new_invoice');
            $data['billable_tasks'] = [];
            $data['permission'] = true;
            $data['promotion_version'] = 'v3';
            $data['allow_edit_ad'] = has_permission('invoices', '', 'po_approval_as_manager') || has_permission('invoices', '', 'po_approval_as_leader');
        } else {
            $invoice = $this->invoices_model->get($id);

            if (!$invoice || !has_permission('invoices', '', 'view', $id)) {
                blank_page(_l('invoice_not_found'));
            }

            $data['permission'] = $this->invoices_model->check_permission_customer_source_invoice($invoice);

            $data['invoices_to_merge'] = $this->invoices_model->check_for_merge_invoice($invoice->clientid, $invoice->id);
            $data['expenses_to_bill']  = $this->invoices_model->get_expenses_to_bill($invoice->clientid);

            $data['invoice']        = $invoice;
            $data['edit']           = true;
            $data['billable_tasks'] = $this->tasks_model->get_billable_tasks($invoice->clientid, !empty($invoice->project_id) ? $invoice->project_id : '');
            $data['contact_data'] = $this->clients_model->get_contacts($invoice->clientid);
            $data['allow_edit_ad'] = Invoice::allowEditAD($id);

            $title = _l('edit', _l('invoice_lowercase')) . ' - ' . format_invoice_number($invoice->id);
        }

        if ($this->input->get('customer_id')) {
            $data['customer_id'] = $this->input->get('customer_id');
        }

        $this->load->model('payment_modes_model');
        $data['payment_modes'] = $this->payment_modes_model->get('', [
            'expenses_only !=' => 1,
        ]);

        $this->load->model('taxes_model');
        $data['taxes'] = $this->taxes_model->get();
        $this->load->model('invoice_items_model');

        $data['ajaxItems'] = false;
        if (total_rows(db_prefix() . 'items') <= ajax_on_total_items()) {
            $data['items'] = $this->invoice_items_model->get_grouped(true);
        } else {
            $data['items']     = [];
            $data['ajaxItems'] = true;
        }
        $data['items_groups'] = $this->invoice_items_model->get_groups();

        $this->load->model('currencies_model');
        $data['currencies'] = $this->currencies_model->get();

        $data['base_currency'] = $this->currencies_model->get_base_currency();

        $data['staff']     = $this->staff_model->get('');
        $data['title']     = $title;
        $data['bodyclass'] = 'invoice';
        $this->load->view('admin/invoices/invoice', $data);
    }

    /* Get all invoice data used when user click on invoiec number in a datatable left side*/
    public function get_invoice_data_ajax($id)
    {
        if (!has_permission('invoices', '', 'view', $id)) {
            echo _l('access_denied');
            die;
        }

        if (!$id) {
            die(_l('invoice_not_found'));
        }

        $invoice = $this->invoices_model->get($id);

        if (!$invoice) {
            echo _l('invoice_not_found');
            die;
        }

        $template_name = 'invoice_send_to_customer';

        $paymentModes = isset($invoice->allowed_payment_modes) ? unserialize($invoice->allowed_payment_modes) : [];
        $is_manual_payment = defined('PAYMENT_MODE_ID') && !in_array(PAYMENT_MODE_ID, $paymentModes);

        // if ($invoice->sent == 1) {
        //     $template_name = 'invoice_send_to_customer_already_sent';
        // }

        if ($is_manual_payment) {
            $template_name = 'invoice_send_manual_payment_to_customer';
        }

        $data = prepare_mail_preview_data($template_name, $invoice->clientid);

        // Check for recorded payments
        $this->load->model('payments_model');
        $data['invoices_to_merge']          = $this->invoices_model->check_for_merge_invoice($invoice->clientid, $id);
        $data['members']                    = $this->staff_model->get('', ['active' => 1]);
        $data['payments']                   = $this->payments_model->get_invoice_payments($id);
        $data['activity']                   = $this->invoices_model->get_invoice_activity($id);
        $data['totalNotes']                 = total_rows(db_prefix() . 'notes', ['rel_id' => $id, 'rel_type' => 'invoice']);
        $data['invoice_recurring_invoices'] = $this->invoices_model->get_invoice_recurring_invoices($id);

        $data['applied_credits'] = $this->credit_notes_model->get_applied_invoice_credits($id);
        // This data is used only when credit can be applied to invoice
        $this->load->model('currencies_model');
        if (credits_can_be_applied_to_invoice($invoice->status)) {
            $data['credits_available'] = $this->credit_notes_model->total_remaining_credits_by_customer($invoice->clientid);

            if ($data['credits_available'] > 0) {
                $data['open_credits'] = $this->credit_notes_model->get_open_credits($invoice->clientid);
            }

            $customer_currency = $this->clients_model->get_customer_default_currency($invoice->clientid);

            if ($customer_currency != 0) {
                $data['customer_currency'] = $this->currencies_model->get($customer_currency);
            } else {
                $data['customer_currency'] = $this->currencies_model->get_base_currency();
            }
        }

        // Lấy thông tin yêu cầu xuất hóa đơn của invoice
        $this->load->model('staff_model');
        $invoice->role = $this->staff_model->get(get_staff_user_id())->role;
        $invoice->invoice_request = $this->currencies_model->getInvoiceRequestId($id);

        $data['invoice'] = $invoice;

        $data['record_payment'] = false;
        $data['send_later']     = false;
        $data['is_manual_payment'] = $is_manual_payment;

        if ($this->session->has_userdata('record_payment')) {
            $data['record_payment'] = true;
            $this->session->unset_userdata('record_payment');
        } elseif ($this->session->has_userdata('send_later')) {
            $data['send_later'] = true;
            $this->session->unset_userdata('send_later');
        }

        $data['allow_send_po'] = Invoice::allowADPOAction($id);

        $this->load->view('admin/invoices/invoice_preview_template', $data);
    }

    public function apply_credits($invoice_id)
    {
        $total_credits_applied = 0;
        foreach ($this->input->post('amount') as $credit_id => $amount) {
            $success = $this->credit_notes_model->apply_credits($credit_id, [
                'invoice_id' => $invoice_id,
                'amount'     => $amount,
            ]);
            if ($success) {
                $total_credits_applied++;
            }
        }

        if ($total_credits_applied > 0) {
            update_invoice_status($invoice_id, true);
            set_alert('success', _l('invoice_credits_applied'));
        }
        redirect(admin_url('invoices/list_invoices/' . $invoice_id));
    }

    public function get_invoices_total()
    {
        if ($this->input->post()) {
            load_invoices_total_template();
        }
    }

    /* Record new inoice payment view */
    public function record_invoice_payment_ajax($id)
    {
        $this->load->model('payment_modes_model');
        $this->load->model('payments_model');
        $data['payment_modes'] = $this->payment_modes_model->get('', [
            'expenses_only !=' => 1,
        ]);
        $data['invoice']  = $this->invoices_model->get($id);
        $data['payments'] = $this->payments_model->get_invoice_payments($id);
        $this->load->view('admin/invoices/record_payment_template', $data);
    }

    /* invoice_request_ajax */
    public function invoice_request_ajax($id)
    {
        $this->load->model('payment_modes_model');
        $this->load->model('payments_model');
        $this->load->model('clients_model');
        $data['payment_modes'] = $this->payment_modes_model->get('', [
            'expenses_only !=' => 1,
        ]);
        $data['invoice']  = $this->invoices_model->get($id);
        $data['payments'] = $this->payments_model->get_invoice_payments($id);
        $data['client'] = $this->clients_model->get($data['invoice']->clientid);
        $this->load->view('admin/invoices/invoice_request_template', $data);
    }

    /* request_change_status */
    public function request_change_status()
    {
        if (!has_permission('account', '', 'change_status')) {
            ajax_error_response(_l('ticket_access_by_department_denied'));
        }

        $data = $this->input->post();
        $this->load->model('invoices_model');
        $result  = $this->invoices_model->request_change_status($data['id'], $data['value']);
        if ($result) {
            ajax_success_response(_l('request_status_update_ok'));
        } else {
            ajax_error_response(_l('request_status_update_no'));
        }
    }

    /* invoice_request submit */
    public function request_invoice_submit()
    {
        try {
            $data = $this->input->post();

            $this->load->model('invoices_model');

            // Prevent making duplicate request if it's already created
            if (InvoiceRequest::where('invoiceid', $data['invoiceid'])->exists()) {
                ajax_error_response(_l('error_exists_request_invoice'));
            }

            // Check permission
            $permission = $this->invoices_model->permission_request_invoice($data['invoiceid']);
            if (!$permission) {
                ajax_error_response(_l('error_add_request_invoice'));
            }

            // kiểm tra received email
            if (empty($data['walk_in_customer'])) {
                if (empty($data['received_email'])) {
                    ajax_error_response(_l('error_received_email_arr_null'));
                } else {
                    $arr_email = explode(',', $data['received_email'] ?? '');
                    foreach ($arr_email as $email) {
                        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                            ajax_error_response(_l('error_received_email_arr_format'));
                            break;
                        }
                    }
                }
            }

            $data['status'] = 2;
            DB::beginTransaction();
            $id_insert = $this->invoices_model->add_request_invoice($data);

            if ($id_insert) {
                $pdfContent = $this->invoices_model->createDraftInvoice($data['invoiceid'], $id_insert, $data['walk_in_customer'] ?? false);
                $isRequestDraft = $data['invoice_draft'] ?? false;

                // Notification
                $notifiedUsers = [];
                // Danh sách nhân viên kế toán được phân quyền xem yêu cầu xuất hóa đơn
                $staff = $this->db->where('active', 1)->get(db_prefix() . 'staff')->result_array();
                // Email người gửi yêu cầu xuất hóa đơn
                $email_invoice_request = $this->invoices_model->get_email_account_invoice_request($data['invoiceid']);
                if ($email_invoice_request) {
                    $staff = array_merge($staff, $email_invoice_request);
                }

                // Người yêu cầu
                $name_invoice_request = get_staff_full_name(get_staff_user_id());

                // Mã hóa đơn
                $invoiceid = $data['invoiceid'];
                $applied_prefix = 'INV-';
                $prefixPadding  = get_option('number_padding_prefixes');
                $invoice_code = $applied_prefix . str_pad($invoiceid, $prefixPadding, '0', STR_PAD_LEFT);

                foreach ($staff as $member) {
                    if (
                        (user_can_view_invoice_request($member['staffid']) || $member['staffid'] == get_staff_user_id())
                        && !in_array($member['staffid'], $notifiedUsers)
                    ) {
                        $notified = add_notification([
                            'fromcompany'     => true,
                            'touserid'        => $member['staffid'],
                            'description'     => 'invoice_request_notification',
                            'link'            => 'invoices/invoice_request',
                            'additional_data' => serialize([
                                '' . $invoice_code . '. ' . _l('name_invoice_reques') . ': ' . $name_invoice_request . ' - ' . _l('date_invoice_request') . ': ' . date('d/m/Y - H:i', time()),
                            ]),
                        ]);
                        if ($notified) {
                            array_push($notifiedUsers, $member['staffid']);
                        }
                    }
                }
                pusher_trigger_notification($notifiedUsers);

                // If it's request draft invoice, send this draft invoice to Customer
                $this->send_email_change_status_invoice_request($id_insert, $pdfContent, $isRequestDraft);

                DB::commit();
                ajax_success_response(_l('invoice_requested_success') . $invoice_code);
            } else {
                DB::rollback();
                ajax_error_response(_l('error_add_request_invoice'));
            }
        } catch (\Throwable $ex) {
            DB::rollback();
            sentry_exception_handler($ex);
            ajax_error_response($ex->getMessage());
        }
    }

    // Thông báo gửi email
    public function send_email_change_status_invoice_request($id, $pdfContent, $isRequestDraft = true)
    {
        // Lấy thông tin yêu cầu xuất hóa đơn
        $invoice_request = InvoiceRequest::find($id);
        if (!$invoice_request) {
            return false;
        }

        $invoice = Invoice::find($invoice_request->invoiceid);
        if (!$invoice) {
            return false;
        }

        // Danh sách nhân viên có quyền thay đổi trạng thái yêu cầu xuất hóa đơn
        $staff_emails  = StaffPermission::get_staff_permission_change_invoice_request();
        // Email người gửi yêu cầu xuất hóa đơn
        $email_invoice_request = $this->invoices_model->get_email_account_invoice_request($invoice_request->invoiceid);
        if ($email_invoice_request) {
            $staff_emails = array_merge($staff_emails, $email_invoice_request);
        }

        // If sale request send draft first then need input customer's email which is inputted in the form request
        if ($isRequestDraft && !$invoice_request->walk_in_customer) {
            $customerEmails = explode(',', $invoice_request->received_email);
            $staff_emails = array_merge($staff_emails, array_map(fn($email) => ['email' => $email], $customerEmails));
        }

        $notifiedUsers = [];
        foreach ($staff_emails as $email) {
            if (in_array($email['email'], $notifiedUsers)) {
                continue;
            }
            $template = mail_template('Invoice_request', $invoice, $invoice_request, $email['email'], $email['email']);
            $template->add_attachment([
                'attachment' => $pdfContent,
                'filename'   => 'PO' . date('dmy', strtotime($invoice->date))  . '.pdf',
                'type'       => 'application/pdf',
            ]);
            $template->send();
            array_push($notifiedUsers, $email['email']);
        }
        return true;
    }

    /* This is where invoice payment record $_POST data is send */
    public function record_payment()
    {
        if (!has_permission('payments', '', 'create')) {
            access_denied('Record Payment');
        }
        if ($this->input->post()) {
            $data_input = $this->input->post();
            if (!has_permission('payments', '', 'edit')) {
                unset($data_input['date']);
            }
            $this->load->model('payments_model');
            $id = $this->payments_model->process_payment($data_input, '');
            if ($id) {
                ClientAmsSearchPackage::syncPaidSearchCv($data_input['invoiceid']);
                ReportService::syncInvoiceItemsToReport($data_input['invoiceid']);
                set_alert('success', _l('invoice_payment_recorded'));
                redirect(admin_url('payments/payment/' . $id));
            } else {
                set_alert('danger', _l('invoice_payment_record_failed'));
            }
            redirect(admin_url('invoices/list_invoices/' . $data_input['invoiceid']));
        }
    }

    /* Send invoice to email */
    public function send_to_email($id)
    {
        $canView = user_can_view_invoice($id);
        if (!$canView) {
            access_denied('Invoices');
        } else {
            if (!has_permission('invoices', '', 'view') && !has_permission('invoices', '', 'view_own') && $canView == false) {
                access_denied('Invoices');
            }
        }

        // If po has AD is not approved yet, prevent send the email
        if (Invoice::where('id', $id)->hasAD()->haveNotADApproved()->exists()) {
            set_alert('danger', _l('po_pending_addition_discount_approval_message'));
            redirect(admin_url('invoices/list_invoices/' . $id));
        }

        try {
            $statementData = [];
            if ($this->input->post('attach_statement')) {
                $statementData['attach'] = true;
                $statementData['from']   = to_sql_date($this->input->post('statement_from'));
                $statementData['to']     = to_sql_date($this->input->post('statement_to'));
            }
            $tncPdf = null;
            if ($this->input->post('attach_tnc')) {
                $tncPdf = !empty($this->input->post('tnc_pdf_link')) ? $this->input->post('tnc_pdf_link') : DEFAULT_TNC_PDF_URL;
            }

            $additionData = [
                'tncPdf' => $tncPdf,
            ];

            $invoice = Invoice::findOrFail($id);
            $paymentModes = isset($invoice->allowed_payment_modes) ? unserialize($invoice->allowed_payment_modes) : [];
            $is_auto_payment = defined('PAYMENT_MODE_ID') && in_array(PAYMENT_MODE_ID, $paymentModes);

            if (defined('IS_PAYMENT_ENABLED') && IS_PAYMENT_ENABLED && $is_auto_payment) {
                // Create client's payer, e collection
                if (!$invoice->client->payer || !$invoice->client->payer->e_collection_code) {
                    $this->load->model('clients_model');
                    $result = $this->clients_model->createPayers([$invoice->client->userid], $this->input->post('sent_to')[0] ?? null);
                    if ($result === true) {
                        $invoice->refresh();
                    }
                }

                // Create e collection receivable
                // Generate QR code
                $ecRecv = $invoice->handleCreateEcRecv();
                if (empty($ecRecv)) {
                    set_alert('danger', 'Failed to create e collection receivable code');
                    redirect(admin_url('invoices/list_invoices/' . $id));
                }

                $ecollectionCd = $ecRecv['ecollectionCd'];
                $additionData['{qrImage}'] = $ecRecv['qr']['qrUrl'];
                $additionData['{ecollectionCd}'] = $ecollectionCd;
            }

            $success = $this->invoices_model->send_invoice_to_client(
                $id,
                $is_auto_payment ? 'invoice_send_to_customer' : 'invoice_send_manual_payment_to_customer',
                $this->input->post('attach_pdf'),
                $this->input->post('cc'),
                false,
                $statementData,
                $additionData,
            );
        } catch (Exception $e) {
            sentry_exception_handler($e);
            $message = $e->getMessage();

            if (strpos($message, 'Unable to get the size of the image') !== false) {
                echo $message;
                show_pdf_unable_to_get_image_size_error();
                die;
            }

            set_alert('danger', $message);
            redirect(admin_url('invoices/list_invoices/' . $id));
        }

        // In case client use another language
        load_admin_language();
        if ($success) {
            set_alert('success', _l('invoice_sent_to_client_success'));
        } else {
            set_alert('danger', _l('invoice_sent_to_client_fail'));
        }
        redirect(admin_url('invoices/list_invoices/' . $id));
    }

    /* Delete invoice payment*/
    public function delete_payment($id, $invoiceid)
    {
        if (!has_permission('payments', '', 'delete')) {
            access_denied('payments');
        }
        $this->load->model('payments_model');
        if (!$id) {
            redirect(admin_url('payments'));
        }
        $response = $this->payments_model->delete($id);
        if ($response == true) {
            set_alert('success', _l('deleted', _l('payment')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('payment_lowercase')));
        }
        redirect(admin_url('invoices/list_invoices/' . $invoiceid));
    }

    /* Delete invoice */
    public function delete($id)
    {
        if (!has_permission('invoices', '', 'delete', $id)) {
            access_denied('invoices');
        }
        if (!$id) {
            redirect(admin_url('invoices/list_invoices'));
        }
        $success = $this->invoices_model->delete($id);

        if ($success) {
            set_alert('success', _l('deleted', _l('invoice')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('invoice_lowercase')));
        }
        if (strpos($_SERVER['HTTP_REFERER'], 'list_invoices') !== false) {
            redirect(admin_url('invoices/list_invoices'));
        } else {
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

    public function delete_attachment($id)
    {
        $file = $this->misc_model->get_file($id);
        if ($file->staffid == get_staff_user_id() || is_admin()) {
            echo $this->invoices_model->delete_attachment($id);
        } else {
            header('HTTP/1.0 400 Bad error');
            echo _l('access_denied');
            die;
        }
    }

    /* Will send overdue notice to client */
    public function send_overdue_notice($id)
    {
        $canView = user_can_view_invoice($id);
        if (!$canView) {
            access_denied('Invoices');
        } else {
            if (!has_permission('invoices', '', 'view') && !has_permission('invoices', '', 'view_own') && $canView == false) {
                access_denied('Invoices');
            }
        }

        $send = $this->invoices_model->send_invoice_overdue_notice($id);
        if ($send) {
            set_alert('success', _l('invoice_overdue_reminder_sent'));
        } else {
            set_alert('warning', _l('invoice_reminder_send_problem'));
        }
        redirect(admin_url('invoices/list_invoices/' . $id));
    }

    /* Generates invoice PDF and senting to email of $send_to_email = true is passed */
    public function pdf($id)
    {
        if (!$id) {
            redirect(admin_url('invoices/list_invoices'));
        }

        $canView = user_can_view_invoice($id);
        if (!$canView) {
            access_denied('Invoices');
        } else {
            if (!has_permission('invoices', '', 'view') && !has_permission('invoices', '', 'view_own') && $canView == false) {
                access_denied('Invoices');
            }
        }
        //// Create custom relations to get contact based on selected contact in the invoice details
        $relations = [[
            'selects' => [
                db_prefix() . 'contacts.fullname as contact_name',
                db_prefix() . 'contacts.phonenumber as contact_phone',
                db_prefix() . 'contacts.email as contact_email'
            ],
            'table' => db_prefix() . 'contacts',
            'cond' => db_prefix() . 'contacts.id = ' . db_prefix() . 'invoices.contact_id'
        ]];
        $invoice        = $this->invoices_model->get($id, [], $relations);
        $invoice        = hooks()->apply_filters('before_admin_view_invoice_pdf', $invoice);
        $invoice_number = format_invoice_number($invoice->id);
        $custom_fields = get_custom_fields('invoice');
        $custom_field_values = [];
        foreach ($custom_fields as $field) {
            $custom_field_values[$field['slug']] = get_custom_field_value($id, $field['id'], 'invoice', false);
        }

        $invoice->custom_field = $custom_field_values;
        try {
            // $pdf = invoice_pdf($invoice);
            $pdf = invoice_pdf_v2($invoice);
        } catch (Exception $e) {
            $message = $e->getMessage();
            echo $message;
            if (strpos($message, 'Unable to get the size of the image') !== false) {
                show_pdf_unable_to_get_image_size_error();
            }
            die;
        }

        $type = 'D';

        if ($this->input->get('output_type')) {
            $type = $this->input->get('output_type');
        }

        if ($this->input->get('print')) {
            $type = 'I';
        }

        $pdf->Output(mb_strtoupper(slug_it($invoice_number)) . '.pdf', $type);
    }

    public function mark_as_sent($id)
    {
        if (!$id) {
            redirect(admin_url('invoices/list_invoices'));
        }
        if (!user_can_view_invoice($id)) {
            access_denied('Invoice Mark As Sent');
        }

        $success = $this->invoices_model->set_invoice_sent($id, true);

        if ($success) {
            set_alert('success', _l('invoice_marked_as_sent'));
        } else {
            set_alert('warning', _l('invoice_marked_as_sent_failed'));
        }

        redirect(admin_url('invoices/list_invoices/' . $id));
    }

    public function get_due_date()
    {
        if ($this->input->post()) {
            $date    = $this->input->post('date');
            $duedate = '';
            if (get_option('invoice_due_after') != 0) {
                $date    = to_sql_date($date);
                $d       = date('Y-m-d', strtotime('+' . get_option('invoice_due_after') . ' DAY', strtotime($date)));
                $duedate = _d($d);
                echo $duedate;
            }
        }
    }

    /**
     * Fetch all invoices reated to selected client id
     *
     * @param int $clientId
     * @param int $isFreePackage
     * @param int|null $invoiceId
     * @return string json string
     */
    public function get_invoices($clientId, $isFreePackage = 0, $invoiceId = 0, $selectedPackageId = 0)
    {
        $this->load->model('Invoices_model');
        $validInvoiceIds = $this->Invoices_model->getAvailableInvoices($clientId, null, $isFreePackage == 0 ? Itemable::PAID_ITEM : Itemable::FREE_ITEM, $selectedPackageId);
        $invoiceId && array_push($validInvoiceIds, $invoiceId);
        ajax_success_response(
            '',
            Invoice::select('id', 'date', 'number', 'prefix', 'number_format', 'status')
                ->where('clientid', $clientId)
                ->whereIn('id', $validInvoiceIds)
                ->whereHas(
                    'invoiceItems',
                    fn ($query) => $query->where('rate', ($isFreePackage ? '=' : '>'), 0)
                        ->when($selectedPackageId > 0, fn($query) => $query->whereOr('id', $selectedPackageId))
                )
                ->get()
                ->map(fn($invoice) => ['id' => $invoice->id, 'text' => format_invoice_number($invoice)])
                ->toArray(),
        );
    }

    /**
     * Fetch all credit invoices reated to selected client id
     *
     * @param int $clientId
     */
    public function get_credit_invoices($clientId)
    {
        ajax_success_response(
            '',
            array_merge(
                [[
                    'id' => '',
                    'text' => '',
                ]],
                Invoice::where('clientid', $clientId)
                    ->select('id', 'date', 'number', 'prefix', 'number_format', 'status')
                    ->whereHas('invoiceItems', fn($query) => $query->isCredit())
                    ->has('clientAmsSearchCvs')
                    ->get()
                    ->map(fn($invoice) => ['id' => $invoice->id, 'text' => format_invoice_number($invoice)])
                    ->toArray()
            ),
        );
    }

    /**
     * Fetch all invoice that have the same package name with the selected invoice
     *
     * @param int $clientId
     */
    public function get_same_credit_invoices($clientId, $clientAmsSearchPackageId)
    {
        $amsSearchpackage = ClientAmsSearchPackage::whereId($clientAmsSearchPackageId)->first();
        $relatedItemId = $amsSearchpackage->itemable->item_id ?? $amsSearchpackage->amsSearchPackage->crm_item_id;
        $relatedInvoiceId = $amsSearchpackage->invoice_id;

        $availableItems = ClientAmsSearchPackage::query()
            ->select(
                'crm_itemable_id',
                'i.qty',
                DB::raw('count(' . db_prefix() . 'client_ams_search_packages.id) as mapped_qty')
            )
            ->join('itemable as i', 'i.id', '=', 'crm_itemable_id')
            ->where('client_id', $clientId)
            ->when($relatedInvoiceId, fn($query) => $query->where('invoice_id', '!=', $relatedInvoiceId))
            ->groupBy('invoice_id', 'crm_itemable_id')
            ->havingRaw('mapped_qty < qty')
            ->pluck('crm_itemable_id');

        $unmappItems = Itemable::query()
            ->isCredit()
            ->invoiceType()
            ->whereHas('invoice', fn($query) => $query->where('clientid', $clientId))
            ->whereNotIn(
                'id',
                fn($query) =>
                $query->select('crm_itemable_id')
                    ->from('client_ams_search_packages')
                    ->where('client_id', $clientId)
                    ->whereNotNull('crm_itemable_id')
            )
            ->when($relatedInvoiceId, fn($query) => $query->where('rel_id', '!=', $relatedInvoiceId))
            ->where('item_id', $relatedItemId)
            ->pluck('id');

        ajax_success_response(
            '',
            Invoice::where('clientid', $clientId)
                ->select('id', 'date', 'number', 'prefix', 'number_format', 'status')
                ->whereHas(
                    'invoiceItems',
                    fn($query) => $query->isCredit()
                        ->useNotExpired()
                        ->where('item_id', $relatedItemId)
                        ->whereIn('id', $availableItems->merge($unmappItems)->toArray())
                )
                ->paid()
                ->when($relatedInvoiceId, fn($query) => $query->where('id', '!=', $relatedInvoiceId))
                ->get()
                ->map(fn($invoice) => ['id' => $invoice->id, 'text' => format_invoice_number($invoice)])
                ->toArray(),
        );
    }

    public function get_company_by_vat(string $vat)
    {
        $company = AccountingService::getCompanyByTax($vat);
        ajax_success_response('', $company);
    }

    public function view_minvoice_pdf(string $uuid)
    {
        $result = AccountingService::getPdf($uuid);
        if ($result['success']) {
            return $this->load->view('admin/invoices/view_minvoice_pdf', ['pdf' => $result['data']]);
        }
        return blank_page('PDF not found');
    }

    public function handle_fix_po(int $id)
    {
        try {

            $this->load->model('invoices_model');
            $invoiceRequest = InvoiceRequest::findOrFail($id);

            Invoice::findOrFail($invoiceRequest->invoiceid)->updateDraftInvoiceTT32($invoiceRequest);

            $requestData = [
                'id' => $id,
                'is_rejected' => false,
                'request_issue_status' => null,
            ];

            // Log reject activity
            $this->invoices_model->log_invoice_activity(
                $invoiceRequest->invoiceid,
                'handle_fix_po',
                false,
                serialize($requestData),
            );

            $invoiceRequest->fill($requestData)->save();


            ajax_success_response('Ok', []);
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            ajax_error_response($ex->getMessage());
        }
    }

    public function handle_request_issue_po(int $id)
    {
        try {
            $this->load->model('invoices_model');

            $invoiceRequest = InvoiceRequest::whereId($id)
                ->whereNull('request_issue_status')
                ->whereNotNull('requested_issue_at')
                ->where('invoice_draft', 1)
                ->first();

            if (!$invoiceRequest) {
                return ajax_error_response(_l('request_invoice_error_request_issue_invoice'));
            }

            $requestData = [
                'id' => $id,
                'invoice_draft' => false,
            ];

            // Log reject activity
            $this->invoices_model->log_invoice_activity(
                $invoiceRequest->invoiceid,
                'handle_issue_po',
                false,
                serialize($requestData),
            );

            $invoiceRequest->fill($requestData)->save();

            $invoice = $invoiceRequest->invoice;
            $invoiceCode = format_invoice_number($invoice);

            // Danh sách nhân viên có quyền thay đổi trạng thái yêu cầu xuất hóa đơn
            $members  = StaffPermission::get_staff_permission_change_invoice_request();
            // Người yêu cầu
            $name_invoice_request = get_staff_full_name(get_staff_user_id());

            $notifiedUsers = [];
            foreach ($members as $member) {
                $notification = add_notification([
                    'fromcompany'     => true,
                    'touserid'        => $member['staffid'],
                    'description'     => 'invoice_request_change_notification',
                    'link'            => 'invoices/invoice_request',
                    'additional_data' => serialize([
                        _l('invoice_request_issue_po', [
                            $invoiceCode,
                            $name_invoice_request,
                            $invoiceRequest->created_at->format('d-m-Y'),
                            $invoiceRequest->created_at->format('H:m'),
                        ]),
                    ]),
                ]);

                if ($notification) {
                    $notifiedUsers[] = $member['staffid'];
                }

                $template = mail_template('Invoice_request_issue_po', $invoice, $invoiceRequest, $member['email']);
                $template->send();
            }

            // Notify to the notification
            if (count($notifiedUsers)) {
                pusher_trigger_notification($notifiedUsers);
            }

            ajax_success_response('OK', []);
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            ajax_error_response($ex->getMessage());
        }
    }

    public function handle_issue_po(int $id)
    {
        try {
            // Danh sách nhân viên có quyền thay đổi trạng thái yêu cầu xuất hóa đơn
            $members  = collect(StaffPermission::get_staff_permission_change_invoice_request())->pluck('staffid')->toArray();
            if (!in_array(get_staff_user_id(), $members)) {
                access_denied('invoices');
            }

            $this->load->model('invoices_model');
            $invoiceRequest = InvoiceRequest::whereId($id)
                ->whereNull('request_issue_status')
                ->whereNotNull('requested_issue_at')
                ->where('invoice_draft', 0)
                ->first();

            if (!$invoiceRequest) {
                return ajax_error_response(_l('request_invoice_error_issue_invoice'));
            }

            $invoice = Invoice::findOrFail($invoiceRequest->invoiceid);
            $invoice->signInvoice();
            $invoice->client->updateTypeOfCustomer();

            $requestData = [
                'id' => $id,
                'request_issue_status' => InvoiceRequest::REQUEST_ISSUE_STATUS_APPROVED,
                'is_approved' => true,
                'approved_issue_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'status' => InvoiceRequest::INVOICE_REQUEST_STATUS_ISSUED
            ];

            // Log reject activity
            $this->invoices_model->log_invoice_activity(
                $invoiceRequest->invoiceid,
                'handle_issue_po',
                false,
                serialize($requestData),
            );

            $invoiceRequest->fill($requestData)->save();

            $notifiedUsers = [];

            // Danh sách nhân viên có quyền thay đổi trạng thái yêu cầu xuất hóa đơn
            $members  = StaffPermission::get_staff_permission_change_invoice_request();
            // Email người gửi yêu cầu xuất hóa đơn
            $email_invoice_request = $this->invoices_model->get_email_account_invoice_request($invoiceRequest->invoiceid);
            if ($email_invoice_request) {
                $members = array_merge($members, $email_invoice_request);
            }
            $invoiceCode = format_invoice_number($invoice);
            foreach ($members as $member) {
                $notification = add_notification([
                    'fromcompany'     => true,
                    'touserid'        => $member['staffid'],
                    'description'     => 'invoice_request_change_notification',
                    'link'            => 'invoices/invoice_request',
                    'additional_data' => serialize([
                        $invoice->client->company . ' ' . $invoiceCode . ' ' . INVOICE_DRAFT[$invoiceRequest->status]['text']
                    ]),
                ]);

                if ($notification) {
                    $notifiedUsers[] = $member['staffid'];
                }
            }

            // Notify to the notification
            if (count($notifiedUsers)) {
                pusher_trigger_notification($notifiedUsers);
            }

            // Send email using Minvoice
            // If sale request send draft first then need input customer's email which is inputted in the form request
            // Danh sách nhân viên có quyền thay đổi trạng thái yêu cầu xuất hóa đơn
            $staff_emails = collect(StaffPermission::get_staff_permission_change_invoice_request())->pluck('email')->toArray();

            // Email người gửi yêu cầu xuất hóa đơn
            if ($email_invoice_request && !$invoiceRequest->walk_in_customer) {
                $staff_emails = array_merge($staff_emails, collect($email_invoice_request)->pluck('email')->toArray());
            }

            $customerEmails = $invoiceRequest->walk_in_customer
                ? collect($email_invoice_request)->pluck('email')->toArray()
                : explode(',', $invoiceRequest->received_email);

            $minvoice = MInvoice::where([
                'invoice_id' => $invoiceRequest->invoiceid,
                'invoice_request_id' => $id,
                ])
                ->whereNotNull('invoice_sign_status')
                ->firstOrFail();

            $minvoice->sendSignedEmail($customerEmails, $staff_emails);

            // Set expires for all packages
            Invoice::findOrFail($invoiceRequest->invoiceid)->handleSetItemableUseExpires();
            ajax_success_response('OK', []);
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            ajax_error_response($ex->getMessage());
        }
    }

    public function handle_reject_po(int $id)
    {
        try {
            $this->load->model('invoices_model');
            $invoiceRequest = InvoiceRequest::whereId($id)
                ->whereNotNull('requested_issue_at')
                ->whereNull('request_issue_status')
                ->first();

            if (!$invoiceRequest) {
                return ajax_error_response(_l('request_invoice_error_reject_invoice'));
            }

            $requestData = [
                'id' => $id,
                'is_rejected' => true,
                'request_issue_status' => InvoiceRequest::REQUEST_ISSUE_STATUS_REJECTED,
                'rejected_issue_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'reject_reason' => array_merge($invoiceRequest->reject_reason ?? [], [
                    'reason_added_at' => Carbon::now()->format('Y-m-d H:i:s'),
                    'reason' => $this->input->post('reason')
                ]),
            ];

            // Log reject activity
            $this->invoices_model->log_invoice_activity(
                $invoiceRequest->invoiceid,
                'handle_reject_po',
                false,
                serialize($requestData),
            );

            $invoiceRequest->fill($requestData)->save();

            ajax_success_response('OK', []);
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            ajax_error_response($ex->getMessage());
        }
    }

    public function get_minvoice_status($invoiceId)
    {
        try {
            $status = [];
            $minvoiceStatus = get_minvoice_status($invoiceId);
            if (InvoiceRequest::INVOICE_REQUEST_STATUS_ISSUED == $minvoiceStatus && !empty($minvoice_status)) {
                $status = MInvoice::getSignedInvoiceStatus($invoiceId);;
            } elseif (isset($minvoiceStatus)) {
                $status = [
                    'text' => INVOICE_DRAFT[$minvoiceStatus]['text'],
                    'color' => 'info',
                ];
            }
            ajax_success_response('OK', $status);
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            ajax_error_response($ex->getMessage());
        }
    }

    public function update_invoice_request(int $id)
    {
        try {
            $invoiceRequest = InvoiceRequest::whereId($id)->firstOrFail();
            $invoiceRequest->fill(array_filter([
                'received_email' => $this->input->post('email') ? implode(',', $this->input->post('email')) : null,
                'company_name' => $this->input->post('company_name') ?? null,
                'export_address' => $this->input->post('export_address') ?? null,
                'walk_in_customer' => !empty($this->input->post('company_name'))
            ]))->save();

            ajax_success_response('OK', []);
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            ajax_error_response($ex->getMessage());
        }
    }

    public function mask_request_as_company(int $id)
    {
        try {
            $invoiceRequest = InvoiceRequest::whereId($id)->firstOrFail();
            $invoiceRequest->fill(['walk_in_customer' => false])->save();

            ajax_success_response('OK', []);
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            ajax_error_response($ex->getMessage());
        }
    }

    public function handle_send_draft_invoice(int $id)
    {
        try {
            $invoiceRequest = InvoiceRequest::whereId($id)->firstOrFail();
            if ($invoiceRequest->invoice_draft) {
                // Get PDF of the draft invoice
                $res = AccountingService::getPdf($invoiceRequest->invoice->minvoice->invoice_uuid);
                $pdfContent = base64_decode($res['data']);
                // If it's request draft invoice, send this draft invoice to Customer
                $this->send_email_change_status_invoice_request($id, $pdfContent, $invoiceRequest->invoice_draft);
            }
            ajax_success_response('OK', []);
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            ajax_error_response($ex->getMessage());
        }
    }
}

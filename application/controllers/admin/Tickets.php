<?php

use app\services\AmsService;
use Entities\ClientAmsCompany;
use Entities\Staff;

defined('BASEPATH') or exit('No direct script access allowed');
/**
 * @property Tickets_model $tickets_model
 * @property App_Input $input
 * @property App $app
 * @property Departments_model $departments_model
 * @property Clients_model $clients_model
 * @property Staff_model $staff_model
 * @property Knowledge_base_model $knowledge_base_model
 * @property Misc_model $misc_model
 * @property CI_DB_mysqli_driver $db
 * @property App_Session $session
 * @property Spam_filters_model $spam_filters_model
 */
class Tickets extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        if (get_option('access_tickets_to_none_staff_members') == 0 && !is_staff_member()) {
            redirect(admin_url());
        }
        $this->load->model('tickets_model');
    }

    public function index($status = '', $userid = '')
    {
        close_setup_menu();
        if (!is_numeric($status)) {
            $status = '';
        }

        if ($this->input->is_ajax_request()) {
            if (!$this->input->post('filters_ticket_id')) {
                $tableParams = [
                    'status' => $status,
                    'userid' => $userid,
                ];
            } else {
                // request for othes tickets when single ticket is opened
                $tableParams = [
                    'userid'              => $this->input->post('filters_userid'),
                    'where_not_ticket_id' => $this->input->post('filters_ticket_id'),
                ];
                if ($tableParams['userid'] == 0) {
                    unset($tableParams['userid']);
                    $tableParams['by_email'] = $this->input->post('filters_email');
                }
            }

            $this->app->get_table_data('tickets', $tableParams);
        }

        $data['chosen_ticket_status']              = $status;
        $data['weekly_tickets_opening_statistics'] = json_encode($this->tickets_model->get_weekly_tickets_opening_statistics());
        $data['title']                             = _l('support_tickets');
        $this->load->model('departments_model');
        $data['statuses']             = $this->tickets_model->get_ticket_status();
        $data['staff_deparments_ids'] = $this->departments_model->get_staff_departments(get_staff_user_id(), true);
        $data['departments']          = $this->departments_model->get();
        $data['priorities']           = $this->tickets_model->get_priority();
        $data['services']             = $this->tickets_model->get_service();
        $data['ticket_assignees']     = $this->tickets_model->get_tickets_assignes_disctinct();
        $data['bodyclass']            = 'tickets-page';
        add_admin_tickets_js_assets();
        $data['default_tickets_list_statuses'] = hooks()->apply_filters('default_tickets_list_statuses', [1, 2, 4]);
        $data['value_ticket'] = Tickets_model::VALUE_TICKET;
        $data['value_feedback'] = Tickets_model::VALUE_FEEDBACK;
        $this->load->view('admin/tickets/list', $data);
    }

    // Thống kê dashbard
    public function dashbard_ticket()
    {
        $type_search = $this->input->get('type_search');

        if ($type_search) {
            $data = $this->tickets_model->dashbard_ticket($type_search);
            print_r(json_encode(array(
                'success' => true,
                'data' => $data
            )));
            die;
        }
    }
    public function add($userid = false)
    {
        if ($this->input->post()) {
            $data            = $this->input->post();
            // Set type_id ticket = 1
            $data['type_id'] = 1;
            $data['message'] = html_purify($this->input->post('message', false));
            $id              = $this->tickets_model->add($data, get_staff_user_id());
            if ($id) {
                set_alert('success', _l('new_ticket_added_successfully', $id));
                redirect(admin_url('tickets/ticket/' . $id));
            }
        }
        if ($userid !== false) {
            $data['userid'] = $userid;
            $data['client'] = $this->clients_model->get($userid);
        }
        // Load necessary models
        $this->load->model('knowledge_base_model');
        $this->load->model('departments_model');

        $data['departments']        = $this->departments_model->get();
        $data['predefined_replies'] = $this->tickets_model->get_predefined_reply();
        $data['priorities']         = $this->tickets_model->get_priority();
        $data['services']           = $this->tickets_model->get_service();
        $whereStaff                 = [];
        if (get_option('access_tickets_to_none_staff_members') == 0) {
            $whereStaff['is_not_staff'] = 0;
        }
        $data['staff']     = $this->staff_model->get('', $whereStaff);
        $data['articles']  = $this->knowledge_base_model->get();
        $data['bodyclass'] = 'ticket';
        $data['title']     = _l('new_ticket');

        if ($this->input->get('project_id') && $this->input->get('project_id') > 0) {
            // request from project area to create new ticket
            $data['project_id'] = $this->input->get('project_id');
            $data['userid']     = get_client_id_by_project_id($data['project_id']);
            if (total_rows(db_prefix() . 'contacts', ['active' => 1, 'userid' => $data['userid']]) == 1) {
                $contact = $this->clients_model->get_contacts($data['userid']);
                if (isset($contact[0])) {
                    $data['contact'] = $contact[0];
                }
            }
        } elseif ($this->input->get('contact_id') && $this->input->get('contact_id') > 0 && $this->input->get('userid')) {
            $contact_id = $this->input->get('contact_id');
            if (total_rows(db_prefix() . 'contacts', ['active' => 1, 'id' => $contact_id]) == 1) {
                $contact = $this->clients_model->get_contact($contact_id);
                if ($contact) {
                    $data['contact'] = (array) $contact;
                }
            }
        }
        add_admin_tickets_js_assets();
        $data['cs_team_id'] = Staff::where('email', Staff::CS_TEAM_EMAIL)->value('staffid');
        $this->load->view('admin/tickets/add', $data);
    }

    public function delete($ticketid)
    {
        if (!$ticketid) {
            redirect(admin_url('tickets'));
        }

        $response = $this->tickets_model->delete($ticketid);

        if ($response == true) {
            set_alert('success', _l('deleted', _l('ticket')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('ticket_lowercase')));
        }

        if (strpos($_SERVER['HTTP_REFERER'], 'tickets/ticket') !== false) {
            redirect(admin_url('tickets'));
        } else {
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

    public function delete_attachment($id)
    {
        if (is_admin() || (!is_admin() && get_option('allow_non_admin_staff_to_delete_ticket_attachments') == '1')) {
            if (get_option('staff_access_only_assigned_departments') == 1 && !is_admin()) {
                $attachment = $this->tickets_model->get_ticket_attachment($id);
                $ticket     = $this->tickets_model->get_ticket_by_id($attachment->ticketid);

                $this->load->model('departments_model');
                $staff_departments = $this->departments_model->get_staff_departments(get_staff_user_id(), true);
                if (!in_array($ticket->department, $staff_departments)) {
                    set_alert('danger', _l('ticket_access_by_department_denied'));
                    redirect(admin_url('access_denied'));
                }
            }

            $this->tickets_model->delete_ticket_attachment($id);
        }

        redirect($_SERVER['HTTP_REFERER']);
    }

    public function update_staff_replying($ticketId, $userId = '')
    {
        if ($this->input->is_ajax_request()) {
            echo json_encode(['success' => $this->tickets_model->update_staff_replying($ticketId, $userId)]);
            die;
        }
    }

    public function check_staff_replying($ticketId)
    {
        if ($this->input->is_ajax_request()) {
            $ticket = $this->tickets_model->get_staff_replying($ticketId);
            $isAnotherReplying = $ticket->staff_id_replying !== null && $ticket->staff_id_replying !== get_staff_user_id();
            echo json_encode([
                'is_other_staff_replying' => $isAnotherReplying,
                'message' => $isAnotherReplying ? _l('staff_is_currently_replying', get_staff_full_name($ticket->staff_id_replying)) : ''
            ]);
            die;
        }
    }

    public function ticket($id)
    {
        if (!$id) {
            redirect(admin_url('tickets/add'));
        }

        $data['ticket'] = $this->tickets_model->get_ticket_by_id($id);

        // Danh sách cấp 2
        $data['level_second'] = $this->tickets_model->get_level_second($data['ticket']->first_level_id);

        $data['merged_tickets'] = $this->tickets_model->get_merged_tickets_by_primary_id($id);

        if (!$data['ticket']) {
            blank_page(_l('ticket_not_found'));
        }

        if (get_option('staff_access_only_assigned_departments') == 1) {
            if (!is_admin()) {
                $this->load->model('departments_model');
                $staff_departments = $this->departments_model->get_staff_departments(get_staff_user_id(), true);
                if (!in_array($data['ticket']->department, $staff_departments)) {
                    set_alert('danger', _l('ticket_access_by_department_denied'));
                    redirect(admin_url('access_denied'));
                }
            }
        }

        if ($this->input->post()) {
            $returnToTicketList = false;
            $data               = $this->input->post();

            if (isset($data['ticket_add_response_and_back_to_list'])) {
                $returnToTicketList = true;
                unset($data['ticket_add_response_and_back_to_list']);
            }

            $data['message'] = html_purify($this->input->post('message', false));
            $replyid         = $this->tickets_model->add_reply($data, $id, get_staff_user_id());

            if ($replyid) {
                set_alert('success', _l('replied_to_ticket_successfully', $id));
            }
            if (!$returnToTicketList) {
                redirect(admin_url('tickets/ticket/' . $id));
            } else {
                set_ticket_open(0, $id);
                redirect(admin_url('tickets'));
            }
        }
        // Load necessary models
        $this->load->model('knowledge_base_model');
        $this->load->model('departments_model');

        $data['statuses']                       = $this->tickets_model->get_ticket_status();
        $data['statuses']['callback_translate'] = 'ticket_status_translate';

        $data['departments']        = $this->departments_model->get();
        $data['predefined_replies'] = $this->tickets_model->get_predefined_reply();
        $data['priorities']         = $this->tickets_model->get_priority();
        $data['services']           = $this->tickets_model->get_service();
        $whereStaff                 = [];
        if (get_option('access_tickets_to_none_staff_members') == 0) {
            $whereStaff['is_not_staff'] = 0;
        }
        $data['staff']                = $this->staff_model->get('', $whereStaff);
        $data['articles']             = $this->knowledge_base_model->get();
        $data['ticket_replies']       = $this->tickets_model->get_ticket_replies($id);
        $data['bodyclass']            = 'top-tabs ticket single-ticket';
        $data['title']                = $data['ticket']->subject;
        $data['ticket']->ticket_notes = $this->misc_model->get_notes($id, 'ticket');
        add_admin_tickets_js_assets();
        $this->load->view('admin/tickets/single', $data);
    }

    public function edit_message()
    {
        if ($this->input->post()) {
            $data         = $this->input->post();
            $data['data'] = html_purify($this->input->post('data', false));

            if ($data['type'] == 'reply') {
                $this->db->where('id', $data['id']);
                $this->db->update(db_prefix() . 'ticket_replies', [
                    'message' => $data['data'],
                ]);
            } elseif ($data['type'] == 'ticket') {
                $this->db->where('ticketid', $data['id']);
                $this->db->update(db_prefix() . 'tickets', [
                    'message' => $data['data'],
                ]);
            }
            if ($this->db->affected_rows() > 0) {
                set_alert('success', _l('ticket_message_updated_successfully'));
            }
            redirect(admin_url('tickets/ticket/' . $data['main_ticket']));
        }
    }

    public function delete_ticket_reply($ticket_id, $reply_id)
    {
        if (!$reply_id) {
            redirect(admin_url('tickets'));
        }
        $response = $this->tickets_model->delete_ticket_reply($ticket_id, $reply_id);
        if ($response == true) {
            set_alert('success', _l('deleted', _l('ticket_reply')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('ticket_reply')));
        }
        redirect(admin_url('tickets/ticket/' . $ticket_id));
    }

    public function change_status_ajax($id, $status)
    {
        if ($this->input->is_ajax_request()) {
            echo json_encode($this->tickets_model->change_ticket_status($id, $status));
        }
    }

    public function update_single_ticket_settings()
    {
        if ($this->input->post()) {
            $this->session->mark_as_flash('active_tab');
            $this->session->mark_as_flash('active_tab_settings');

            if ($this->input->post('merge_ticket_ids') !== 0) {
                $ticketsToMerge = explode(',', $this->input->post('merge_ticket_ids'));

                $alreadyMergedTickets = $this->tickets_model->get_already_merged_tickets($ticketsToMerge);
                if (count($alreadyMergedTickets) > 0) {
                    echo json_encode([
                        'success' => false,
                        'message' => _l('cannot_merge_tickets_with_ids', implode(',', $alreadyMergedTickets)),
                    ]);

                    die();
                }
            }

            $success = $this->tickets_model->update_single_ticket_settings($this->input->post());
            if ($success) {
                $this->session->set_flashdata('active_tab', true);
                $this->session->set_flashdata('active_tab_settings', true);
                if (get_option('staff_access_only_assigned_departments') == 1) {
                    $ticket = $this->tickets_model->get_ticket_by_id($this->input->post('ticketid'));
                    $this->load->model('departments_model');
                    $staff_departments = $this->departments_model->get_staff_departments(get_staff_user_id(), true);
                    if (!in_array($ticket->department, $staff_departments) && !is_admin()) {
                        set_alert('success', _l('ticket_settings_updated_successfully_and_reassigned', $ticket->department_name));
                        echo json_encode([
                            'success'               => $success,
                            'department_reassigned' => true,
                        ]);
                        die();
                    }
                }
                set_alert('success', _l('ticket_settings_updated_successfully'));
            }
            echo json_encode([
                'success' => $success,
            ]);
            die();
        }
    }

    // Priorities
    /* Get all ticket priorities */
    public function priorities()
    {
        if (!is_admin()) {
            access_denied('Ticket Priorities');
        }
        $data['priorities'] = $this->tickets_model->get_priority();
        $data['title']      = _l('ticket_priorities');
        $this->load->view('admin/tickets/priorities/manage', $data);
    }

    /* Add new priority od update existing*/
    public function priority()
    {
        if (!is_admin()) {
            access_denied('Ticket Priorities');
        }
        if ($this->input->post()) {
            if (!$this->input->post('id')) {
                $id = $this->tickets_model->add_priority($this->input->post());
                if ($id) {
                    set_alert('success', _l('added_successfully', _l('ticket_priority')));
                }
            } else {
                $data = $this->input->post();
                $id   = $data['id'];
                unset($data['id']);
                $success = $this->tickets_model->update_priority($data, $id);
                if ($success) {
                    set_alert('success', _l('updated_successfully', _l('ticket_priority')));
                }
            }
            die;
        }
    }

    /* Delete ticket priority */
    public function delete_priority($id)
    {
        if (!is_admin()) {
            access_denied('Ticket Priorities');
        }
        if (!$id) {
            redirect(admin_url('tickets/priorities'));
        }
        $response = $this->tickets_model->delete_priority($id);
        if (is_array($response) && isset($response['referenced'])) {
            set_alert('warning', _l('is_referenced', _l('ticket_priority_lowercase')));
        } elseif ($response == true) {
            set_alert('success', _l('deleted', _l('ticket_priority')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('ticket_priority_lowercase')));
        }
        redirect(admin_url('tickets/priorities'));
    }

    /* List all ticket predefined replies */
    public function predefined_replies()
    {
        if (!is_admin()) {
            access_denied('Predefined Replies');
        }
        if ($this->input->is_ajax_request()) {
            $aColumns = [
                'name',
            ];
            $sIndexColumn = 'id';
            $sTable       = db_prefix() . 'tickets_predefined_replies';
            $result       = data_tables_init($aColumns, $sIndexColumn, $sTable, [], [], [
                'id',
            ]);
            $output  = $result['output'];
            $rResult = $result['rResult'];
            foreach ($rResult as $aRow) {
                $row = [];
                for ($i = 0; $i < count($aColumns); $i++) {
                    $_data = $aRow[$aColumns[$i]];
                    if ($aColumns[$i] == 'name') {
                        $_data = '<a href="' . admin_url('tickets/predefined_reply/' . $aRow['id']) . '">' . $_data . '</a>';
                    }
                    $row[] = $_data;
                }
                $options            = icon_btn('tickets/predefined_reply/' . $aRow['id'], 'pencil-square-o');
                $row[]              = $options .= icon_btn('tickets/delete_predefined_reply/' . $aRow['id'], 'remove', 'btn-danger _delete');
                $output['aaData'][] = $row;
            }
            echo json_encode($output);
            die();
        }
        $data['title'] = _l('predefined_replies');
        $this->load->view('admin/tickets/predefined_replies/manage', $data);
    }

    public function get_predefined_reply_ajax($id)
    {
        echo json_encode($this->tickets_model->get_predefined_reply($id));
    }

    public function ticket_change_data()
    {
        if ($this->input->is_ajax_request()) {
            $contact_id = $this->input->post('contact_id');
            echo json_encode([
                'contact_data'          => $this->clients_model->get_contact($contact_id),
                'customer_has_projects' => customer_has_projects(get_user_id_by_contact_id($contact_id)),
            ]);
        }
    }

    /* Add new reply or edit existing */
    public function predefined_reply($id = '')
    {
        if (!is_admin() && get_option('staff_members_save_tickets_predefined_replies') == '0') {
            access_denied('Predefined Reply');
        }
        if ($this->input->post()) {
            $data              = $this->input->post();
            $data['message']   = html_purify($this->input->post('message', false));
            $ticketAreaRequest = isset($data['ticket_area']);

            if (isset($data['ticket_area'])) {
                unset($data['ticket_area']);
            }

            if ($id == '') {
                $id = $this->tickets_model->add_predefined_reply($data);
                if (!$ticketAreaRequest) {
                    if ($id) {
                        set_alert('success', _l('added_successfully', _l('predefined_reply')));
                        redirect(admin_url('tickets/predefined_reply/' . $id));
                    }
                } else {
                    echo json_encode(['success' => $id ? true : false, 'id' => $id]);
                    die;
                }
            } else {
                $success = $this->tickets_model->update_predefined_reply($data, $id);
                if ($success) {
                    set_alert('success', _l('updated_successfully', _l('predefined_reply')));
                }
                redirect(admin_url('tickets/predefined_reply/' . $id));
            }
        }
        if ($id == '') {
            $title = _l('add_new', _l('predefined_reply_lowercase'));
        } else {
            $predefined_reply         = $this->tickets_model->get_predefined_reply($id);
            $data['predefined_reply'] = $predefined_reply;
            $title                    = _l('edit', _l('predefined_reply_lowercase')) . ' ' . $predefined_reply->name;
        }
        $data['title'] = $title;
        $this->load->view('admin/tickets/predefined_replies/reply', $data);
    }

    /* Delete ticket reply from database */
    public function delete_predefined_reply($id)
    {
        if (!is_admin()) {
            access_denied('Delete Predefined Reply');
        }
        if (!$id) {
            redirect(admin_url('tickets/predefined_replies'));
        }
        $response = $this->tickets_model->delete_predefined_reply($id);
        if ($response == true) {
            set_alert('success', _l('deleted', _l('predefined_reply')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('predefined_reply_lowercase')));
        }
        redirect(admin_url('tickets/predefined_replies'));
    }

    // Ticket statuses
    /* Get all ticket statuses */
    public function statuses()
    {
        if (!is_admin()) {
            access_denied('Ticket Statuses');
        }
        $data['statuses'] = $this->tickets_model->get_ticket_status();
        $data['title']    = 'Ticket statuses';
        $this->load->view('admin/tickets/tickets_statuses/manage', $data);
    }

    /* Add new or edit existing status */
    public function status()
    {
        if (!is_admin()) {
            access_denied('Ticket Statuses');
        }
        if ($this->input->post()) {
            if (!$this->input->post('id')) {
                $id = $this->tickets_model->add_ticket_status($this->input->post());
                if ($id) {
                    set_alert('success', _l('added_successfully', _l('ticket_status')));
                }
            } else {
                $data = $this->input->post();
                $id   = $data['id'];
                unset($data['id']);
                $success = $this->tickets_model->update_ticket_status($data, $id);
                if ($success) {
                    set_alert('success', _l('updated_successfully', _l('ticket_status')));
                }
            }
            die;
        }
    }

    /* Delete ticket status from database */
    public function delete_ticket_status($id)
    {
        if (!is_admin()) {
            access_denied('Ticket Statuses');
        }
        if (!$id) {
            redirect(admin_url('tickets/statuses'));
        }
        $response = $this->tickets_model->delete_ticket_status($id);
        if (is_array($response) && isset($response['default'])) {
            set_alert('warning', _l('cant_delete_default', _l('ticket_status_lowercase')));
        } elseif (is_array($response) && isset($response['referenced'])) {
            set_alert('danger', _l('is_referenced', _l('ticket_status_lowercase')));
        } elseif ($response == true) {
            set_alert('success', _l('deleted', _l('ticket_status')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('ticket_status_lowercase')));
        }
        redirect(admin_url('tickets/statuses'));
    }

    /* List all ticket services */
    public function services()
    {
        if (!has_permission('supports', '', 'view') && !has_permission('supports', '', 'create') && !has_permission('supports', '', 'edit') && !has_permission('supports', '', 'delete')) {
            access_denied('Ticket Services');
        }
        if ($this->input->is_ajax_request()) {
            if (!has_permission('supports', '', 'view')) {
                return;
            }

            $aColumns = [
                'serviceid',
                'name',
            ];
            $sIndexColumn = 'serviceid';
            $sTable       = db_prefix() . 'services';
            $result       = data_tables_init($aColumns, $sIndexColumn, $sTable, [], [], [
                'serviceid',
            ]);
            $output  = $result['output'];
            $rResult = $result['rResult'];

            foreach ($rResult as $aRow) {
                $row = [];
                for ($i = 0; $i < count($aColumns); $i++) {
                    $_data = $aRow[$aColumns[$i]];
                    if ($aColumns[$i] == 'name') {
                        $_data = '<a href="#" onclick="new_service(' . $aRow['serviceid'] . ');return false" data-name="' . $aRow['name'] . '">' . $_data . '</a>';
                    }
                    $row[] = $_data;
                }
                $options = icon_btn('#', 'pencil-square-o', 'btn-default', [
                    'data-name' => $aRow['name'],
                    'onclick'   => 'new_service(' . $aRow['serviceid'] . '); return false;',
                ]);

                $edit = (has_permission('supports', '', 'edit')) ? $options : '';
                $delete = (has_permission('supports', '', 'delete')) ? icon_btn('tickets/delete_service/' . $aRow['serviceid'], 'remove', 'btn-danger _delete') : '';
                $row[]              = $edit .= $delete;
                $output['aaData'][] = $row;
            }
            echo json_encode($output);
            die();
        }
        $data['title'] = _l('services');
        $this->load->view('admin/tickets/services/manage', $data);
    }

    /* Add new */
    public function service_add()
    {
        $data = $this->input->post();
        $this->load->helper('client_requests_helper');
        $this->load->helper('tickets_helper');
        //$data['service_id'] = 13;
        // Lấy thông tin serverce id
        if ($data['service_id']) {
            $data['service'] = $this->tickets_model->get_serice($data['service_id']);
        } else {
            $data['service'] = array(
                'serviceid' => 0,
                'name' => '',
                'level' => '',
            );
        }

        $data['array_pior'] = $this->tickets_model->get_priority();
        $data['arr_expected_completion'] = arr_expected_completion();

        // Select new pior
        $data['pior_select'] = '<div class="content_level1"><div class="row item_row1"><i onclick="dropdown_sub(this)" class="dropdown_sub fa fa-angle-down" aria-hidden="true"></i><div class="col-md-4"><div class="item_name_level1"><div class="item_name_level1_left"><i class="fa fa-folder-o" aria-hidden="true"></i></div><div class="item_name_level1_right"><input type="text" name="name_level1" class="form-control" placeholder="' . _l('level_1_holder') . '" autocomplete="off" value=""></div></div></div><div class="col-md-4"><div class="item_pior_level1">';

        $data['pior_select'] .= '<select name="pior1" class="form-control">';
        $data['pior_select'] .= '<option value="0">--' . _l('select_priority_level') . ' --</option>';
        foreach ($data['array_pior'] as $pior) {
            $data['pior_select'] .= '<option value="' . $pior['priorityid'] . '">' . $pior['name'] . '</option>';
        }
        $data['pior_select'] .= '</select>';

        $data['pior_select'] .= '</div></div><div class="col-md-4"><div class="item_time_level1">';

        $data['pior_select'] .= '<select name="time1" class="form-control">';
        $data['pior_select'] .= '<option value="0">--' . _l('select_expected_completion_time') . ' --</option>';
        foreach ($data['arr_expected_completion'] as $completion) {
            $data['pior_select'] .= '<option value="' . $completion['value'] . '">' . $completion['text'] . '</option>';
        }
        $data['pior_select'] .= '</select>';

        $data['pior_select'] .= '</div></div><i onclick="remove_level1(this)" class="fa fa-minus-circle" aria-hidden="true"></i></div><div class="content_level2"><div class="item_content_level2"><div class="result_level2"></div><div class="button_add_level2"><span onclick="button_add_level2(this)"><i class="fa fa-plus-circle" aria-hidden="true"></i>' . _l('button_add_level2') . '</span></div></div></div></div>';

        // Select new time
        $data['expected_completion_select'] = '<div class="row"><div class="col-md-4"><div class="item_name_level2"><div class="item_name_level1_left"><i class="fa fa-folder-open-o" aria-hidden="true"></i></div><div class="item_name_level1_right"><input type="text" name="name_level2" class="form-control" placeholder="' . _l('level_2_holder') . '" autocomplete="off" value=""></div></div></div><div class="col-md-4"><div class="item_pior_level2">';

        $data['expected_completion_select'] .= '<select name="pior2" class="form-control">';
        $data['expected_completion_select'] .= '<option value="0">--' . _l('select_priority_level') . ' --</option>';
        foreach ($data['array_pior'] as $pior) {
            $data['expected_completion_select'] .= '<option value="' . $pior['priorityid'] . '">' . $pior['name'] . '</option>';
        }
        $data['expected_completion_select'] .= '</select>';

        $data['expected_completion_select'] .= '</div></div><div class="col-md-4"><div class="item_time_level2">';

        $data['expected_completion_select'] .= '<select name="time2" class="form-control">';
        $data['expected_completion_select'] .= '<option value="0">--' . _l('select_expected_completion_time') . ' --</option>';
        foreach ($data['arr_expected_completion'] as $completion) {
            $data['expected_completion_select'] .= '<option value="' . $completion['value'] . '">' . $completion['text'] . '</option>';
        }
        $data['expected_completion_select'] .= '</select>';

        $data['expected_completion_select'] .= '</div></div><i onclick="remove_level2(this)" class="fa fa-minus-circle" aria-hidden="true"></i></div>';

        return $this->load->view('admin/tickets/services/add', $data);
    }

    /* Service submit */
    public function service_submit()
    {
        $data = $this->input->post();
        if (!isset($data['data']))
            $data['data'] = '';

        if (!$data['name']) {
            ajax_error_response(_l('name_service_null'));
        }

        // Permission add
        if (!isset($data['id']) && !has_permission('supports', '', 'create')) {
            access_denied('Ticket Services');
        }

        // Permission edit
        if (isset($data['id']) && $data['id'] && !has_permission('supports', '', 'edit')) {
            access_denied('Ticket Services');
        }

        $result = $this->tickets_model->service_submit($data);

        if ($result) {
            ajax_success_response(_l('success_json'));
        } else {
            ajax_error_response(_l('failed_json'));
        }

        print_r($result);
        die;
    }

    /* Get Job Company */
    public function get_job_company()
    {
        $data = $this->input->post();

        $page = 1;
        $queries = array_filter([
            'fields' => [
                'job' => join(',', [
                    'id',
                    'title',
                    'detail_url',
                    'addresses',
                    'crm_invoice_id',
                    'num_ready_candidates',
                    'created_by',
                    'status',
                    'status_display',
                    'service_list',
                    'package_list',
                    'candidates_count', // Logged in to view
                    'num_viewers', // Logged in to view
                    'published',
                    'closed',
                    'from_crm'
                ])
            ],
            'page' => $page,
            'page_size' => 20,
            'ordering' => 'crm_invoice_id'
        ]);

        if ($data['company_id']) {
            $amsIds = ClientAmsCompany::select('ams_company_id')->where('client_id', $data['company_id'])->get()->pluck('ams_company_id');
            $queries['companies'] = $amsIds->join(',');
        }

        $res = AmsService::search('jobs', [
            'query' => $queries
        ]);

        $jobs = $res['data'] ?? [];
        print_r(json_encode($jobs));
        die;
    }

    /* Get service level 1 */
    public function get_service_level1()
    {
        $data = $this->input->post();
        if (!$data['service_id']) {
            print_r(json_encode([]));
            die;
        }

        $data_arr = $this->tickets_model->get_service_level1($data['service_id']);
        print_r(json_encode($data_arr));
        die;
    }

    /* Get service level 2 */
    public function get_service_level2()
    {
        $data = $this->input->post();
        if (!$data['level1_id']) {
            print_r(json_encode([]));
            die;
        }

        $data_arr = $this->tickets_model->get_service_level2($data['level1_id']);
        $priority_level1 = $this->tickets_model->priority_level1($data['level1_id']);

        $this->load->helper('tickets_helper');
        $name_priority = ticket_priority_translate($priority_level1);

        $result['priority_level1_id'] = $priority_level1;
        $result['priority_level1'] = $name_priority;
        $arr = [];
        foreach ($data_arr as $item) {
            $arr[] = array(
                'id' => $item['id'],
                'name' => $item['name'],
            );
        }
        $result['data'] = $arr;
        print_r(json_encode($result));
        die;
    }

    /* Get service priority */
    public function get_service_priority()
    {
        $data = $this->input->post();
        if (!$data['level2_id']) {
            print_r(json_encode([]));
            die;
        }

        $data_arr = $this->tickets_model->get_service_priority($data['level2_id']);

        $this->load->helper('tickets_helper');
        $name_priority = ticket_priority_translate($data_arr[0]['priority']);

        $arr = array(
            'value' => $data_arr[0]['priority'],
            'text' => $name_priority,
        );
        print_r(json_encode($arr));
        die;
    }


    /* Add new service od delete existing one */
    public function service($id = '')
    {
        if (!is_admin() && get_option('staff_members_save_tickets_predefined_replies') == '0') {
            access_denied('Ticket Services');
        }

        if ($this->input->post()) {
            $post_data = $this->input->post();
            if (!$this->input->post('id')) {
                $requestFromTicketArea = isset($post_data['ticket_area']);
                if (isset($post_data['ticket_area'])) {
                    unset($post_data['ticket_area']);
                }
                $id = $this->tickets_model->add_service($post_data);
                if (!$requestFromTicketArea) {
                    if ($id) {
                        set_alert('success', _l('added_successfully', _l('service')));
                    }
                } else {
                    echo json_encode(['success' => $id ? true : false, 'id' => $id, 'name' => $post_data['name']]);
                }
            } else {
                $id = $post_data['id'];
                unset($post_data['id']);
                $success = $this->tickets_model->update_service($post_data, $id);
                if ($success) {
                    set_alert('success', _l('updated_successfully', _l('service')));
                }
            }
            die;
        }
    }

    /* Delete ticket service from database */
    public function delete_service($id)
    {
        if (!has_permission('supports', '', 'delete')) {
            access_denied('Ticket Services');
        }
        if (!$id) {
            redirect(admin_url('tickets/services'));
        }
        $response = $this->tickets_model->delete_service($id);
        if (is_array($response) && isset($response['referenced'])) {
            set_alert('warning', _l('is_referenced', _l('service_lowercase')));
        } elseif ($response == true) {
            set_alert('success', _l('deleted', _l('service')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('service_lowercase')));
        }
        redirect(admin_url('tickets/services'));
    }

    public function block_sender()
    {
        if ($this->input->post()) {
            $this->load->model('spam_filters_model');
            $sender  = $this->input->post('sender');
            $success = $this->spam_filters_model->add(['type' => 'sender', 'value' => $sender], 'tickets');
            if ($success) {
                set_alert('success', _l('sender_blocked_successfully'));
            }
        }
    }

    public function bulk_action()
    {
        hooks()->do_action('before_do_bulk_action_for_tickets');
        if ($this->input->post()) {
            $ids      = $this->input->post('ids');
            $is_admin = is_admin();

            if (!is_array($ids)) {
                return;
            }

            if ($this->input->post('merge_tickets')) {
                $primary_ticket = $this->input->post('primary_ticket');
                $status         = $this->input->post('primary_ticket_status');

                if ($this->tickets_model->is_merged($primary_ticket)) {
                    set_alert('warning', _l('cannot_merge_into_merged_ticket'));

                    return;
                }

                $total_merged = $this->tickets_model->merge($primary_ticket, $status, $ids);
            } elseif ($this->input->post('mass_delete')) {
                $total_deleted = 0;
                if ($is_admin) {
                    foreach ($ids as $id) {
                        if ($this->tickets_model->delete($id)) {
                            $total_deleted++;
                        }
                    }
                }
            } else {
                $status     = $this->input->post('status');
                $department = $this->input->post('department');
                $service    = $this->input->post('service');
                $priority   = $this->input->post('priority');
                $tags       = $this->input->post('tags');

                foreach ($ids as $id) {
                    if ($status) {
                        $this->db->where('ticketid', $id);
                        $this->db->update(db_prefix() . 'tickets', [
                            'status' => $status,
                        ]);
                    }
                    if ($department) {
                        $this->db->where('ticketid', $id);
                        $this->db->update(db_prefix() . 'tickets', [
                            'department' => $department,
                        ]);
                    }
                    if ($priority) {
                        $this->db->where('ticketid', $id);
                        $this->db->update(db_prefix() . 'tickets', [
                            'priority' => $priority,
                        ]);
                    }

                    if ($service) {
                        $this->db->where('ticketid', $id);
                        $this->db->update(db_prefix() . 'tickets', [
                            'service' => $service,
                        ]);
                    }
                    if ($tags) {
                        handle_tags_save($tags, $id, 'ticket');
                    }
                }
            }

            if ($this->input->post('mass_delete')) {
                set_alert('success', _l('total_tickets_deleted', $total_deleted));
            } elseif ($this->input->post('merge_tickets') && $total_merged > 0) {
                set_alert('success', _l('tickets_merged'));
            }
        }
    }
}

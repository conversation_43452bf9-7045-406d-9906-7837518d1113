<?php

use Carbon\Carbon;
use app\services\AmsService;
use Entities\AmsPackage;
use Entities\Client;
use Entities\ClientAmsCompany;
use Entities\ClientAmsJob;
use Entities\ClientAmsOpenJob;
use Entities\ClientAmsSearchPackage;
use Entities\Invoice;
use Entities\Item;
use Entities\Itemable;
use Entities\ReportClientAmsJob;
use GuzzleHttp\Exception\RequestException;

defined('BASEPATH') or exit('No direct script access allowed');

class Ams extends AdminController
{
    private const AUTOCOMPLETE_AMS_IDS = 'ams_ids';
    private const AUTOCOMPLETE_EMPLOYER = 'employer';

    public function __construct()
    {
        parent::__construct();
        $this->load->helper('ams');
    }

    public function companies($clientId)
    {
        $this->app->get_table_data('client_ams_companies', [
            'clientId' => $clientId,
        ]);
    }

    public function jobs($clientId)
    {
        $this->app->get_table_data('client_ams_jobs', [
            'clientId' => $clientId,
        ]);
    }

    public function jobsticket($ticketId)
    {
        $this->app->get_table_data('client_ams_jobs_ticket', [
            'ticketId' => $ticketId,
        ]);
    }

    public function search_cvs($clientId)
    {
        $this->app->get_table_data('client_ams_search_cvs', [
            'clientId' => $clientId,
        ]);
    }

    public function employers($clientId)
    {
        $this->app->get_table_data('client_ams_employees', [
            'clientId' => $clientId,
        ]);
    }

    public function job_invoices($clientId)
    {
        $this->app->get_table_data('client_ams_job_invoices', [
            'clientId' => $clientId,
        ]);
    }

    public function post_job($clientId, $clientAmsJobId = null)
    {
        $this->load->helper(array('form', 'form_rules'));
        $this->load->library('form_validation');
        $data = array_merge(
            $this->input->post(),
            [
                'client_id' => $clientId,
                'client_ams_job_id' => $clientAmsJobId,
                'benefitsIds' => array_filter($this->input->post('benefitsIds', [])),
                'recruitment_processesIds' => array_filter($this->input->post('recruitment_processesIds', [])),
                'requirementsIds' => array_filter($this->input->post('requirementsIds', [])),
                'responsibilitiesIds' => array_filter($this->input->post('responsibilitiesIds', [])),
            ]
        );
        $this->form_validation->set_data($data);
        $this->form_validation->set_rules(ams_post_job_form_rules($data));
        if ($this->input->post() && $this->input->is_ajax_request()) {
            if (!$this->form_validation->run()) {
                ajax_error_response('Validation error!', [
                    'validation_errors' => $this->form_validation->error_array(),
                ]);
            }
            $post = $data;
            $isUpdating = $clientAmsJobId || !empty($post['ams_job_id']);
            $packageId = $post['package_id'] ?? null;
            $responsibilitiesText = $post['responsibilitiesText'];
            $responsibilitiesDes = $post['responsibilitiesDes'];
            $responsibilities = [];
            collect($post['responsibilitiesIds'])->each(function ($id, $idx) use (&$responsibilities, $responsibilitiesText, $responsibilitiesDes) {
                $responsibilities[] = [
                    'id' => $id,
                    'name' => $responsibilitiesText[$idx],
                    'description' => $responsibilitiesDes[$idx],
                ];
            });
            $post['responsibilities'] = $responsibilities;

            $requirementsText = $post['requirementsText'];
            $requirementsDes = $post['requirementsDes'];
            $requirements = [];
            collect($post['requirementsIds'])->each(function ($id, $idx) use (&$requirements, $requirementsText, $requirementsDes) {
                $requirements[] = [
                    'id' => $id,
                    'name' => $requirementsText[$idx],
                    'description' => $requirementsDes[$idx],
                ];
            });
            $post['requirements'] = $requirements;

            $recruitment_processesText = $post['recruitment_processesText'];
            $recruitment_processesDes = $post['recruitment_processesDes'];
            $recruitment_processes = [];
            collect($post['recruitment_processesIds'])->each(function ($id, $idx) use (&$recruitment_processes, $recruitment_processesText, $recruitment_processesDes) {
                $recruitment_processes[] = [
                    'id' => $id,
                    'name' => $recruitment_processesText[$idx],
                    'description' => $recruitment_processesDes[$idx],
                ];
            });
            $post['recruitment_processes'] = $recruitment_processes;

            $benefitsText = $post['benefitsText'];
            $benefitsDes = $post['benefitsDes'];
            $benefits = [];
            collect($post['benefitsIds'])->each(function ($id, $idx) use (&$benefits, $benefitsText, $benefitsDes) {
                $benefits[] = [
                    'id' => $id,
                    'name' => $benefitsText[$idx],
                    'description' => $benefitsDes[$idx],
                ];
            });
            $post['benefits'] = $benefits;

            $paidPackage = $packageId ? Itemable::select('id', 'item_id')->whereId($packageId)->with('item:id,group_id')->first() : null;
            $isSmePackage = $packageId && $paidPackage->isSmePackage();
            $amsPaidPackageId = $packageId ? AmsPackage::whereCrmItemId($paidPackage->item_id)->value('ams_package_id') ?? null : null;
            $noteLabels = Itemable::getAmsLabelByPackage($paidPackage);
            $expiresIn = Itemable::getExpiresInByPackage($paidPackage);

            // If selecting free package, add label to free
            if (isset($post['free_package'])) {
                $noteLabels[] = Item::FREE_PACKAGE;
            }

            $jobData = [
                'is_content_image' => isset($post['is_content_image']) ? true : false,
                'company_id' => $post['company_id'] ?? null,
                'crm_invoice_id' => $post['crm_invoice_id'] ?? null,
                'title' => $post['title'],
                'job_category_id' => $post['job_category_id'],
                'content' => $post['content'] ?? null,
                'responsibilities' => $post['responsibilities'],
                'responsibilities_original' => $post['responsibilities_original'] ?? '',
                'requirements' => $post['requirements'],
                'requirements_original' => $post['requirements_original'] ?? '',
                'benefits' => $post['benefits'],
                'benefits_original' => $post['benefits_original'] ?? '',
                'recruiment_process' => $post['recruitment_processes'],
                'recruitment_processes_original' => array_map(fn ($process) => ['name' => $process], $post['recruitment_processes_original'] ?? []),
                'experiences_ids' => array_filter([
                    $post['yoe_from'],
                    $post['yoe_to'],
                ]),
                'salary' => [
                    'currency' => $post['currency'],
                    'currency_estimate' => $post['currency'],
                    'is_negotiable' => $post['salary'] == 'range' ? 0 : 1,
                    'max' => isset($post['range_to']) ? intval($post['range_to']) : 0,
                    'max_estimate' => intval($post['negotiable_to']) ?? 0,
                    'min' => isset($post['range_from']) ? intval($post['range_from']) : 0,
                    'min_estimate' => intval($post['negotiable_from']) ?? 0,
                    'unit' => "MONTH",
                    'value' => ucfirst($post['salary']),
                ],
                'employer_notes' => $post['note'] ?? null,
                'skills_ids' => $post['skills_ids'],
                'job_levels' => $post['job_levels'],
                'job_types' => $post['job_types'],
                'contract_type' => $post['contract_type'],
                'addresses_id' => $post['addresses_id'],
                'emails_cc' => explode(',', $post['emails_cc'] ?? ''),
                'notes_label' => $noteLabels,
                'level' => ClientAmsJob::JOB_LEVEL_PAID,
                'education_certificate' => $post['education_certificate'] ?? null,
                'education_major' => $post['education_major'] ?? [],
                'education_degree' => $post['education_degree'] ?? [],
                'packages' => $amsPaidPackageId ? [['name' => $amsPaidPackageId, 'expires_in' => $expiresIn, 'is_free_package' => isset($post['free_package'])]] : [],
                'job_template' => $post['job_template'],
                'job_banner' => $post['job_banner'],
                'job_template_color' => $post['job_template_color'],
                'company_tagline' => $post['company_tagline'],
                'company_job_logo' => (isset($_FILES['company_job_logo']['tmp_name']) && $_FILES['company_job_logo']['tmp_name'] != '') ? base64_encode(file_get_contents($_FILES['company_job_logo']['tmp_name'])) : null,
            ];
            $jobLogo = [];
            if (isset($_FILES['company_job_logo']['tmp_name']) && $_FILES['company_job_logo']['tmp_name'] != '') {
                $jobLogo = [
                    'company_job_logo' => base64_encode(file_get_contents($_FILES['company_job_logo']['tmp_name'])),
                    'company_job_logo_name' => $_FILES['company_job_logo']['name'],
                ];
            }
            $jobData = array_merge($jobData, $jobLogo);

            $multipartData = [];
            if (isset($_FILES['html_file_desktop']) && $_FILES['html_file_desktop']['tmp_name']) {
                $multipartData[] = [
                    'name' => 'html_content_desktop',
                    'contents' => fopen($_FILES['html_file_desktop']['tmp_name'], 'r'), // Open the file in read mode
                    'filename' => basename($_FILES['html_file_desktop']['name']), // Optional: Specify filename
                ];
            }

            if (isset($_FILES['html_file_mobile']) && $_FILES['html_file_mobile']['tmp_name']) {
                $multipartData[] = [
                    'name' => 'html_content_mobile',
                    'contents' => fopen($_FILES['html_file_mobile']['tmp_name'], 'r'), // Open the file in read mode
                    'filename' => basename($_FILES['html_file_mobile']['name']), // Optional: Specify filename
                ];
            }

            try {
                $clientAmsJobData = [
                    'ams_job_id' => $post['ams_job_id'],
                    'invoice_id' => $post['crm_invoice_id'],
                    'ams_company_id' => $post['ams_company_id'],
                    'package_id' => $packageId,
                    'free_package' => isset($post['free_package']),
                ];
                if ($isUpdating) {
                    $clientAmsJob = null;
                    $reportAmsJob = null;
                    if (!empty($post['ams_job_id'])) {
                        $clientAmsJob = ClientAmsJob::firstOrCreate(
                            [
                                'client_id' => $clientId,
                                'ams_company_id' => $post['ams_company_id'],
                                'ams_job_id' => $post['ams_job_id'],
                            ],
                            $clientAmsJobData
                        );
                        $reportAmsJob = ReportClientAmsJob::firstOrCreate(
                            [
                                'client_id' => $clientId,
                                'ams_company_id' => $post['ams_company_id'],
                                'ams_job_id' => $post['ams_job_id'],
                                'status_mapping' => 1,
                            ],
                            $clientAmsJobData
                        );
                    } else {
                        $clientAmsJob = ClientAmsJob::findOrFail($clientAmsJobId);
                        // Set removed flag to remove notes label "30 Ngày" in AMS if previously set sme package then change to non sme package
                        $jobData['remove_sme_note'] = !$isSmePackage && $clientAmsJob->itemable && $clientAmsJob->itemable->isSmePackage() ? Item::SMES_PACKAGE : null;
                        $reportAmsJob = ReportClientAmsJob::firstOrCreate(
                            [
                                'ams_job_id' => $clientAmsJob->ams_job_id,
                                'client_id' => $clientAmsJob->client_id,
                                'ams_company_id' => $clientAmsJob->ams_company_id,
                                'status_mapping' => 1,
                            ],
                            [
                                'invoice_id' => $post['crm_invoice_id'],
                                'package_id' => $packageId,
                                'free_package' => isset($post['free_package']),
                            ]
                        );
                    }
                    $this->updateJob($clientAmsJob, $jobData, $clientAmsJobData, $reportAmsJob);
                } else {
                    $clientAmsJobData['ams_company_id'] = $post['company_id'];
                    $this->createJob($clientId, $jobData, $clientAmsJobData, $post, $multipartData);
                }
            } catch (RequestException $ex) {
                if ($ex->getResponse()->getStatusCode() == 422) {
                    $response = json_decode($ex->getResponse()->getBody()->getContents(), true);
                    $errors = $response['errors'];
                    $arrayKeys = [
                        'addresses_id' => 'addresses_id[]',
                        'job_levels' => 'job_levels[]',
                        'job_types' => 'job_types[]',
                        'contract_type' => 'contract_type[]',
                        'skills_ids' => 'skills_ids[]',
                    ];
                    $mapErrors = [];
                    foreach ($errors as $key => $value) {
                        if (isset($arrayKeys[$key])) {
                            $mapErrors[$arrayKeys[$key]] = $value;
                        } else {
                            $mapErrors[$key] = $value;
                        }
                    }
                    ajax_error_response('Api validation error!', [
                        'validation_errors' => $mapErrors,
                    ]);
                } else {
                    $response = json_decode($ex->getResponse()->getBody()->getContents());
                    ajax_error_response($response->message ?? '');
                }
            } catch (\Exception $ex) {
                throw $ex;
                ajax_error_response($ex->getMessage());
            }
        }
    }

    /**
     * Call AMS API to update an existing job
     * @param ClientAmsJob $clientAmsJobId
     * @param array $jobData data will be update in AMS
     * @param array $clientAmsJobData selected package when updating
     * @return mixed json data contains message
     */
    private function updateJob($clientAmsJob, $jobData, $clientAmsJobData, $reportAmsJob = null)
    {
        $jobData['company_id'] = $clientAmsJob->ams_company_id;
        $res = AmsService::amsApi('crm/jobs/update/' . $clientAmsJob->ams_job_id, ['form_params' => $jobData], 'PUT');
        if (empty($res['error'])) {
            $amsJobId = $res['data']['id'] ?? null;
            $amsJobStatus = $res['data']['status'] ?? null;
            if ($amsJobId) {
                $clientAmsJob->invoice_id = $jobData['crm_invoice_id'];
                $clientAmsJob->package_id = $clientAmsJobData['package_id'];
                $clientAmsJob->free_package = $clientAmsJobData['free_package'];
                $clientAmsJob->save();
                if (!empty($reportAmsJob)) {
                    $reportAmsJob->invoice_id = $jobData['crm_invoice_id'];
                    $reportAmsJob->package_id = $clientAmsJobData['package_id'];
                    $reportAmsJob->free_package = $clientAmsJobData['free_package'];
                    $reportAmsJob->save();
                }

                // Update job open to count it correctly
                $clientAmsOpenJob = ClientAmsOpenJob::where('ams_job_id', $amsJobId)->first();
                if ($clientAmsOpenJob) {
                    $clientAmsOpenJob->fill([
                        'invoice_id' => $clientAmsJob->invoice_id,
                        'itemable_id' => $clientAmsJob->package_id,
                    ])
                    ->save();
                } elseif (in_array($amsJobStatus, [ClientAmsJob::JOB_STATUS_REVIEW, ClientAmsJob::JOB_STATUS_OPEN])) {
                    ClientAmsOpenJob::insert([
                        'invoice_id' => $clientAmsJob->invoice_id,
                        'itemable_id' => $clientAmsJob->package_id,
                        'client_id' => $clientAmsJob->client_id,
                        'ams_company_id' => $clientAmsJob->ams_company_id,
                        'ams_job_id' => $amsJobId,
                    ]);
                }

                ajax_success_response('Job updated successfully!', ['id' => $clientAmsJob->id]);
            } else {
                ajax_error_response('Not found AMS Job ID!');
            }
        } else {
            ajax_error_response($res['message']);
        }
    }

    /**
     * Call AMS API to post a new job
     * @param integer $clientId id of the client will be post
     * @param array $jobData data will be created in AMS
     * @param array $clientAmsJobData data will be stored to client_ams_Job table
     * @return mixed json data contains message
     * @throws \Exception
     */
    private function createJob($clientId, $jobData, $clientAmsJobData, $post = [], $multipartData = [])
    {
        $res = AmsService::amsApi('crm/jobs/create', ['form_params' => $jobData]);

        // Check if job created
        $amsJobId = $res['data']['id'] ?? null;

        // Check if we need to attach html
        $attachHtmlResponse = AmsService::amsApi('crm/jobs/' . $amsJobId . '/attach-html', ['multipart' => $multipartData], 'post');

        if (empty($res['error']) && empty($attachHtmlResponse['error'])) {
            if ($amsJobId) {
                $fill = array_merge(
                    $clientAmsJobData,
                    [
                        'client_id' => $clientId,
                        'ams_job_id' => $amsJobId,
                    ],
                );
                $amsJob = ClientAmsJob::updateOrCreate(
                    [
                        'client_id' => $clientId,
                        'ams_company_id' => $fill['ams_company_id'],
                        'ams_job_id' => $amsJobId,
                    ],
                    $fill
                );
                if (isset($post['package_id'])) {
                    $dataReport = ReportClientAmsJob::where(['client_id' => $clientId, 'invoice_id' => $post['crm_invoice_id'], 'package_id' => $post['package_id'], 'status_mapping' => 0])->first();
                    if ($dataReport) {
                        $dataReport->ams_job_id = $fill['ams_job_id'];
                        $dataReport->ams_company_id = $fill['ams_company_id'];
                        $dataReport->free_package = $fill['free_package'];
                        $dataReport->status_mapping = 1;
                        $dataReport->save();
                    }
                }
                ajax_success_response('Job created successfully!', ['id' => $amsJob->id, 'ams_job_id' => $amsJob->ams_job_id]);
            } else {
                ajax_error_response('Not found AMS Job ID!');
            }
        } else {
            ajax_error_response($res['message']);
        }
    }


    /**
     * Add free quota to a company's AMS account
     * 
     * @param  int  $clientId
     *
     * @return void
     */
    public function add_free_quota(int $clientId)
    {
        if (!has_permission('customers', '', 'edit')) {
            ajax_access_denied();
        }

        $this->load->library('form_validation');
        $this->form_validation->set_rules('free_quota_number', _l('free_quota'), 'required|numeric|greater_than[0]');

        if ($this->form_validation->run() === false) {
            ajax_error_response(validation_errors());
        }

        $amsCompanyId = ClientAmsCompany::where('client_id', $clientId)->first()->ams_company_id;

        if (empty($amsCompanyId)) {
            ajax_error_response('Company not found in AMS database');
        }

        $freeQuota = (int)$this->input->post('free_quota_number');

        try {
            // Get the current free quota from the AMS database
            $currentFreeQuota = $this->db->query("SELECT free_quota FROM " . AMS_DB_NAME . ".companies WHERE id = ?", [$amsCompanyId])->row();
            
            if (!$currentFreeQuota) {
                ajax_error_response('Company not found in AMS database');
            }

            // Update the free quota in the AMS database
            $newFreeQuota = ($currentFreeQuota->free_quota ?? 0) + $freeQuota;
            $this->db->query("UPDATE " . AMS_DB_NAME . ".companies SET free_quota = ? WHERE id = ?", [$newFreeQuota, $amsCompanyId]);

            // Log the action
            $this->db->insert(db_prefix() . 'activity_log', [
                'description' => 'Added ' . $freeQuota . ' free quota to AMS company ID: ' . $amsCompanyId,
                'date' => date('Y-m-d H:i:s'),
                'staffid' => get_staff_user_id(),
                'clientid' => $clientId,
                'data' => json_encode([
                    'free_quota' => $freeQuota,
                    'ams_company_id' => $amsCompanyId,
                ])
            ]);

            ajax_success_response('Free quota added successfully', [
                'free_quota' => $newFreeQuota
            ]);
        } catch (Exception $e) {
            ajax_error_response('Failed to add free quota: ' . $e->getMessage());
        }
    }

    /**
     * Get free quota summary for a company
     * 
     * @param  int  $clientId
     *
     * @return array
     */
    public function get_free_quota_summary(int $clientId): array
    {
        /**
         * @var Client $client
         */
        $client = Client::query()->where('userid', $clientId)->first();
        $amsCompanyId = ClientAmsCompany::query()->where('client_id', $clientId)->first()->ams_company_id;

        $result = [
            'free_quota' => 0,
            'free_quota_used' => 0,
            'free_quota_remaining' => 0,
        ];

        if (!$client) {
            return $result;
        }

        if ($amsCompanyId > 0) {
            $freeQuotaInfo = $this->db->query("SELECT free_quota, free_quota_used FROM " . AMS_DB_NAME . ".companies WHERE id = ?", [$amsCompanyId])->row();
            if ($freeQuotaInfo) {
                $result['free_quota'] = (int) ($freeQuotaInfo->free_quota + $freeQuotaInfo->free_quota_used);
                $result['free_quota_used'] = (int) $freeQuotaInfo->free_quota_used;
                $result['free_quota_remaining'] = (int) $freeQuotaInfo->free_quota;
            }
        }

        return $result;
    }

    /**
     * Get job summary including free quota information
     * 
     * @param int $clientId
     * @param int $amsId
     * @return void
     */
    public function job_summary($clientId, $amsId = -1)
    {
        $total = [
            'paid_invoices' => 0,
            'paid_packages' => 0,
            'used_packages' => 0,
            'remain_packages' => 0,
        ];

        $amsClient = $clientId != -1 ? ClientAmsCompany::select('ams_company_id')
            ->where('client_id', $clientId)
            ->get()
            ->pluck('ams_company_id')
            ->toArray() : [$amsId];

        $clientIds = $clientId != -1 ? [$clientId] : [];
        if ($amsId > -1) {
            $clientIds = ClientAmsCompany::select('client_id')
                ->where('ams_company_id', $amsId)
                ->get()
                ->pluck('client_id')
                ->toArray();
        }

        if (count($amsClient)) {
            // If have mapping record, then count totals
            $invoiceCount = 0;
            $packageCount = 0;
            if (count($clientIds)) {
                $clients = Client::select([
                        'package_count' => Itemable::select(DB::raw('CAST(SUM(qty) as UNSIGNED)'))
                            ->whereIn('rel_id', function ($builder) {
                                $builder
                                    ->select('id')
                                    ->from('invoices')
                                    ->whereColumn('clientid', 'clients.userid')
                                    ->where('status', Invoice::STATUS_PAID);
                            })
                            ->where('rel_type', Itemable::REL_TYPE_INVOICE)
                            ->whereHas('item', fn ($qr) => $qr->paidItems()),
                        'expired_package_count' => Itemable::select(DB::raw('CAST(SUM(qty) as UNSIGNED)'))
                            ->whereIn('rel_id', function ($builder) {
                                $builder
                                    ->select('id')
                                    ->from('invoices')
                                    ->whereColumn('clientid', 'clients.userid')
                                    ->where('status', Invoice::STATUS_PAID);
                            })
                            ->whereNotIn('id', fn($query) =>
                                $query->select('itemable_id')
                                    ->from('client_ams_open_jobs')
                                    ->whereColumn('client_id', 'clients.userid'))
                            ->useExpired()
                            ->where('rel_type', Itemable::REL_TYPE_INVOICE)
                            ->whereHas('item', fn ($qr) => $qr->paidItems()),
                        'not_used_but_expired_count' => Itemable::select(
                            DB::raw('SUM(qty - (select count(id) from '.db_prefix().'client_ams_open_jobs where itemable_id = '.db_prefix().'itemable.id))')
                            )
                            ->whereIn('rel_id', function ($builder) {
                                $builder
                                    ->select('id')
                                    ->from('invoices')
                                    ->whereColumn('clientid', 'clients.userid')
                                    ->where('status', Invoice::STATUS_PAID);
                            })
                            ->whereHas('amsOpenJobs')
                            ->useExpired()
                            ->where('rel_type', Itemable::REL_TYPE_INVOICE)
                            ->whereHas('item', fn ($qr) => $qr->paidItems()),
                    ])
                    ->whereIn('userid', $clientIds)
                    ->withCount(['invoices' => fn ($builder) => $builder->paid()])
                    ->get();

                if ($clients->count()) {
                    $invoiceCount = $clients->sum('invoices_count');
                    $packageCount = $clients->sum('package_count');
                    $packageExpiredCount = $clients->sum('expired_package_count');
                    $notUsedButExpired = $clients->sum('not_used_but_expired_count');
                }
            }

            $clientAmsJob = ClientAmsJob::select('id', 'invoice_id', 'used_packages')
                ->whereIn('client_id', $clientIds)
                ->whereNotNull('used_packages')
                ->get();

            $clientAmsOpenJobCount = ClientAmsOpenJob::query()
                ->whereIn('client_id', $clientIds)
                ->count();

            $totalUsedInputPackacges = $clientAmsJob->unique(fn ($job) => $job->invoice_id)->sum(fn ($job) => array_sum($job->used_packages ?? []));
            $totalUsed = $clientAmsOpenJobCount + $totalUsedInputPackacges;
            $totalUsedPackages = $totalUsed + $packageExpiredCount + $notUsedButExpired;

            $total = [
                'paid_invoices' => $invoiceCount,
                'paid_packages' => $packageCount,
                'used_packages' => $totalUsed,
                'remain_packages' => $packageCount > $totalUsedPackages ? $packageCount - $totalUsedPackages : 0,
            ];
        }

        ajax_success_response('', $total);
    }

    public function ams_autocomplete($clientId)
    {
        $type = $this->input->post('type');
        $search = $this->input->post('q');

        switch ($type) {
            case self::AUTOCOMPLETE_AMS_IDS:
                $this->search_ams_ids($clientId, $search);
                break;
            case self::AUTOCOMPLETE_EMPLOYER:
                $this->search_employer($clientId, $search);
                break;
            default:
                break;
        }
    }

    public function get_ams_locations($amsId)
    {
        $response = AmsService::search('companies/search', [
            'query' => array_filter([
                'ids' => $amsId,
                'page_size' => 1,
            ]),
        ]);
        $data = $response['data']['data'] ?? [];
        ajax_success_response('', array_map(fn ($dt) => ['id' => $dt['id'], 'text' => $dt['full_address']], $data[0]['addresses']['collection_addresses'] ?? []));
    }

    /**
     * Get all job's roles based on the selected job's category
     * @param int $jobCategoryId
     * @return void
     */
    public function get_ams_job_roles($jobCategoryId)
    {
        $response = AmsService::amsApi('crm/jobs/roles', [
            'query' => array_filter([
                'job_category_id' => $jobCategoryId,
            ]),
        ], 'get');
        $data = $response['data'] ?? [];
        ajax_success_response('', array_map(fn ($jobRole) => ['id' => $jobRole['id'], 'text' => $jobRole['name']], $data ?? []));
    }

    /**
     * Fetch ams search package usage based on crm id and/or ams id
     *
     * @param integer $clientId
     * @param integer $amsId
     * @param integer $invoiceId
     * @return string
     */
    public function search_cv_summary($clientId, $amsId = -1, $invoiceId = -1)
    {
        $summary = [
            'total_invoices' => 0,
            'total_unlocks' => 0,
            'total_unused' => 0,
            'total_unsed' => 0,
            'total_expired' => 0,
        ];

        $amsIds = ClientAmsSearchPackage::select('invoice_id', 'ams_company_search_package_id')
            ->when($clientId > 1, fn($query) => $query->where('client_id', $clientId))
            ->when($amsId > -1, fn($query) => $query->where('ams_company_id', $amsId))
            ->when($invoiceId > -1, fn($query) => $query->where('invoice_id', $invoiceId))
            ->get();

        if ($amsIds->count()) {
            $summary['total_invoices'] = $amsIds->pluck('invoice_id')->unique()->count();
            $response = AmsService::amsApi('crm/company-credits/summary', [
                'query' => [
                    'ids' => $amsIds->pluck('ams_company_search_package_id')->unique()->join(',')
                ]
            ], 'get');

            if (isset($response['data'])) {
                $data = $response['data'];
                $summary = array_merge($summary, [
                    'total_unlocks' => $data['total_unlocks'],
                    'total_unused' => $data['total_remain'],
                    'total_unsed' => $data['total_used'],
                    'total_expired' => $data['total_expired_unlocks'],
                ]);
            }
        }

        ajax_success_response('', $summary);
    }

    private function search_ams_ids($clientId, $search)
    {
        $amsIds = ClientAmsCompany::select('ams_company_id')
            ->where('client_id', $clientId)
            ->where('ams_company_id', 'like', $search . '%')
            ->get()
            ->pluck('ams_company_id')
            ->map(fn ($id) => ['id' => $id, 'name' => $id])
            ->toArray();

        echo json_encode($amsIds);
        exit;
    }

    private function search_employer($clientId, $search)
    {
        $amsIds = ClientAmsCompany::select('ams_company_id')->where('client_id', $clientId)->get()->pluck('ams_company_id');
        if (empty($search) || !count($amsIds)) {
            echo json_encode([]);
            exit;
        }
        $options = [
            'query' => array_filter([
                'crm_search' => !is_numeric($search) ? $search : null,
                'type' => 'employer',
                'companies' => is_numeric($search) ? intval($search) : $amsIds->join(','),
                'page_size' => 100,
            ]),
        ];
        $response = AmsService::search('employees', $options);
        $data = $response['data'] ?? [];

        echo json_encode(array_map(fn ($dt) => ['id' => $dt['id'], 'name' => $dt['email'] . '/' . $dt['lastname'], 'company_id' => $dt['company_id']], $data));
        exit;
    }

    /**
     * Get free quota for a specific AMS company *
     *
     * @param  int  $clientId
     *
     * @return void
     */
    public function get_free_quota($clientId)
    {
        if (!has_permission('customers', '', 'view')) {
            ajax_access_denied();
        }

        $summary = $this->get_free_quota_summary($clientId);
        ajax_success_response('', $summary);
    }
}

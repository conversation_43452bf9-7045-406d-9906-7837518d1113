<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Dashboard_model $dashboard_model
 * @property Dashboard_revenue_model $dashboard_revenue_model
 */
class Dashboard extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('dashboard_model');
    }

    /* This is admin dashboard view */
    public function index()
    {
        close_setup_menu();
        $this->load->model('departments_model');
        $this->load->model('todo_model');
        $data['departments'] = $this->departments_model->get();

        $data['todos'] = $this->todo_model->get_todo_items(0);
        // Only show last 5 finished todo items
        $this->todo_model->setTodosLimit(5);
        $data['todos_finished']            = $this->todo_model->get_todo_items(1);
        $data['upcoming_events_next_week'] = $this->dashboard_model->get_upcoming_events_next_week();
        $data['upcoming_events']           = $this->dashboard_model->get_upcoming_events();
        $data['title']                     = _l('dashboard_string');

        $this->load->model('contracts_model');
        $data['expiringContracts'] = $this->contracts_model->get_contracts_about_to_expire(get_staff_user_id());

        $this->load->model('currencies_model');
        $data['currencies']    = $this->currencies_model->get();
        $data['base_currency'] = $this->currencies_model->get_base_currency();
        $data['activity_log']  = $this->misc_model->get_activity_log();
        // Tickets charts
        $tickets_awaiting_reply_by_status     = $this->dashboard_model->tickets_awaiting_reply_by_status();
        $tickets_awaiting_reply_by_department = $this->dashboard_model->tickets_awaiting_reply_by_department();

        $data['tickets_reply_by_status']              = json_encode($tickets_awaiting_reply_by_status);
        $data['tickets_awaiting_reply_by_department'] = json_encode($tickets_awaiting_reply_by_department);

        $data['tickets_reply_by_status_no_json']              = $tickets_awaiting_reply_by_status;
        $data['tickets_awaiting_reply_by_department_no_json'] = $tickets_awaiting_reply_by_department;

        $data['projects_status_stats'] = json_encode($this->dashboard_model->projects_status_stats());
        $data['leads_status_stats']    = json_encode($this->dashboard_model->leads_status_stats());
        $data['google_ids_calendars']  = $this->misc_model->get_google_calendar_ids();
        $data['bodyclass']             = 'dashboard invoices-total-manual';
        $this->load->model('announcements_model');
        $data['staff_announcements']             = $this->announcements_model->get();
        $data['total_undismissed_announcements'] = $this->announcements_model->get_total_undismissed_announcements();

        $this->load->model('projects_model');
        $data['projects_activity'] = $this->projects_model->get_activity('', hooks()->apply_filters('projects_activity_dashboard_limit', 20));
        add_calendar_assets();
        $this->load->model('utilities_model');
        $this->load->model('estimates_model');
        $data['estimate_statuses'] = $this->estimates_model->get_statuses();

        $this->load->model('proposals_model');
        $data['proposal_statuses'] = $this->proposals_model->get_statuses();

        $wps_currency = 'undefined';
        if (is_using_multiple_currencies()) {
            $wps_currency = $data['base_currency']->id;
        }
        $data['weekly_payment_stats'] = json_encode($this->dashboard_model->get_weekly_payments_statistics($wps_currency));

        $data['dashboard'] = true;

        $data['user_dashboard_visibility'] = get_staff_meta(get_staff_user_id(), 'dashboard_widgets_visibility');

        if (!$data['user_dashboard_visibility']) {
            $data['user_dashboard_visibility'] = [];
        } else {
            $data['user_dashboard_visibility'] = unserialize($data['user_dashboard_visibility']);
        }
        $data['user_dashboard_visibility'] = json_encode($data['user_dashboard_visibility']);

        $data['tickets_report'] = [];
        if (is_admin()) {
            $data['tickets_report'] = (new \app\services\TicketsReportByStaff())->filterBy('this_month');

        }

        $data = hooks()->apply_filters('before_dashboard_render', $data);
        $this->load->view('admin/dashboard/dashboard', $data);
    }

    public function revenue_recognition_summary()
    {
        $this->load->model('dashboard_revenue_model');

        // Default to This Month if not specified
        list($from_date, $to_date) = $this->getSelectedDatesInput();

        $data['from_date'] = $from_date;
        $data['to_date'] = $to_date;

        // Get general dashboard metrics
        $data = $this->dashboard_model->get_dashboard_metrics($from_date, $to_date);

        // Get revenue recognition summary data
        $revenue_recognition_summary_data = $this->dashboard_revenue_model->get_revenue_recognition_summary_data(date('Y0101', strtotime($to_date)), $to_date);
        $data['revenue_recognition_data'] = $revenue_recognition_summary_data ?? [];

        $this->load->view('admin/dashboard/revenue_recognition_summary', $data);
    }

    public function revenue_recognition()
    {
        // Default to This Month if not specified
        list($from_date, $to_date) = $this->getSelectedDatesInput();

        // Get general dashboard metrics
        $data = $this->dashboard_model->get_dashboard_metrics($from_date, $to_date);

        $data['from_date'] = $from_date;
        $data['to_date'] = $to_date;

        // Get planning activities data
        $pa_from = min($from_date, date('Y0101', strtotime('last year')));
        $data['planning_activities_yearly'] = $this->dashboard_model->get_planning_activity_yearly_data($pa_from, $to_date);
        $data['planning_activities'] = $this->dashboard_model->get_planning_activity_data($pa_from, $to_date);
        //$data['planning_activities'] = $this->dashboard_model->get_planning_activity_year_week_data($pa_from, $to_date);

        $this->load->model('invoices_model');
        $data['invoices_statuses'] = $this->invoices_model->get_statuses();

        $this->load->view('admin/dashboard/revenue_recognition', $data);
    }

    /**
     * @return array
     */
    public function getSelectedDatesInput(): array
    {
        $from_date = $this->input->post_get('from_date') ?? date('Ym01'); // Default to first day of current month
        $to_date = $this->input->post_get('to_date') ?? date('Ymd');
        return array($from_date, $to_date);
    }

    /* Chart weekly payments statistics on home page / ajax */
    public function weekly_payments_statistics($currency)
    {
        if ($this->input->is_ajax_request()) {
            echo json_encode($this->dashboard_model->get_weekly_payments_statistics($currency));
            die();
        }
    }

    /* Chart monthly payments statistics on home page / ajax */
    public function monthly_payments_statistics($currency)
    {
        if ($this->input->is_ajax_request()) {
            echo json_encode($this->dashboard_model->get_monthly_payments_statistics($currency));
            die();
        }
    }

    public function ticket_widget($type)
    {
        $data['tickets_report'] = (new \app\services\TicketsReportByStaff())->filterBy($type);
        $this->load->view('admin/dashboard/widgets/tickets_report_table', $data);
    }
}

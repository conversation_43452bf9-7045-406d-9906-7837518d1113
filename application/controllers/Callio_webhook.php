<?php

use app\services\GoogleChatService;
use app\services\utilities\Arr;
use Carbon\Carbon;
use Entities\Contact;
use Entities\M3cCall;

defined('BASEPATH') or exit('No direct script access allowed');

class Callio_webhook extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->auth_check();
    }

    public function error($message, $code = null)
    {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $message,
            'code' => $code,
        ]);
    }

    public function _remap($method, $params = array())
    {
        $method = str_replace('-', '_', $method);
        if (method_exists($this, str_replace('-', '_', $method))) {
            return call_user_func_array(array($this, $method), $params);
        }
        return $this->error('Not found!');
    }

    /**
     * Check api auth before doing any action
     */
    private function auth_check()
    {
        try {
            $webhookToken = $this->input->get_request_header('X-Secret');
            // Verify api auth code before doing any action from API
            if ($webhookToken != CALLIO_WEBHOOK_TOKEN) {
                $this->error_un_authorized('UNAUTHORIZED');
                exit;
            }
        } catch (\Exception $ex) {
            _exception_handler($ex);
            $this->error_un_authorized('UNAUTHORIZED');
            exit;
        }
    }

    protected function error_un_authorized($message)
    {
        header('Content-Type: application/json');
        header('HTTP/1.0 401 UNAUTHORIZED');
        echo json_encode([
            'success' => false,
            'message' => $message
        ]);
    }

    public function missed_call()
    {
        $requestBody = json_decode($this->input->raw_input_stream, true);
        $call = Arr::pull($requestBody, 'call');
        if (!empty($call)) {
            $this->notify_google_space($call);
        }
    }

    public function call_end()
    {
        try {
            $requestBody = json_decode($this->input->raw_input_stream, true);
            $call = Arr::pull($requestBody, 'call');

            if (!empty($call)) {
                $this->store_call_log($call);
            }

            echo json_encode([
                'success' => true,
                'message' => ''
            ]);
        } catch (Exception $ex) {
            _exception_handler($ex);
        }
    }

    protected function notify_google_space($callLog)
    {
        try {
            $calledPhone = $callLog['fromNumber'];
            $contacts = Contact::selectRaw('userid, phonenumber, fullname as contact_name')
                ->where('phonenumber', $calledPhone)
                ->with([
                    'client:userid,business_name',
                    'client.clientAmsCompany:client_id,ams_company_id',
                ])
                ->get()
                ->keyBy('phonenumber');

            $startTime = Carbon::createFromTimestampMs($callLog['startTime']);

            $message = [
                'cards' => [
                    'header' => [
                        'title' => 'Cuộc gọi nhỡ lúc ' . $startTime->format('h:iA') . ', ngày ' . $startTime->format('d/m/Y')
                    ],
                    'sections' => $contacts->count() ? $contacts->flatMap(function ($contact) use ($callLog, $startTime) {
                        return [
                            'widgets' => [
                                [
                                    'textParagraph' => [
                                        'text' => '<b>SĐT gọi: </b>' . $callLog['fromNumber']
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>Thời gian: </b>' . $startTime->format('Y-m-d H:i:s'),
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>SĐT nhận: </b>' . $callLog['toNumber'],
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>Công ty: </b>' . ($contact && $contact->client ? $contact->client->business_name ?? '' : ''),
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>Khách hàng: </b>' . ($contact->contact_name ?? ''),
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>AMS ID: </b>' . ($contact && $contact->client && $contact->client->clientAmsCompany ? $contact->client->clientAmsCompany->pluck('ams_company_id')->join(', ') : ''),
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>CRM ID: </b>' . ($contact->userid ?? ''),
                                    ]
                                ],
                            ]
                        ];
                    })->toArray() : [
                        [
                            'widgets' => [
                                [
                                    'textParagraph' => [
                                        'text' => '<b>SĐT gọi: </b>' . $callLog['fromNumber']
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>Thời gian: </b>' . $startTime->format('Y-m-d H:i:s'),
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>SĐT nhận: </b>' . $callLog['toNumber'],
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>Công ty: </b>',
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>Khách hàng: </b>',
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>AMS ID: </b>',
                                    ]
                                ],
                                [
                                    'textParagraph' => [
                                        'text' => '<b>CRM ID: </b>',
                                    ]
                                ],
                            ]
                        ]
                    ]
                ]
            ];
            GoogleChatService::send(GOOGLE_CHAT_MISSED_CALL, $message);
        } catch (Exception $ex) {
            _exception_handler($ex);
        }
    }

    protected function store_call_log($callLog)
    {
        $isCallOut = $callLog['direction'] == 'outbound';
        $calls = [
            json_decode(
                json_encode(
                    [
                        'agent_id' => $isCallOut ? $callLog['fromExt'] : $callLog['toExt'],
                        'user_id' => $isCallOut ? $callLog['fromUser'] : $callLog['toUser'],
                        'id' => $callLog['id'],
                        'call_id' => $callLog['id'],
                        'path' => '',
                        'path_download' => '',
                        'customer_id' => 0,
                        'caller' => $callLog['fromNumber'],
                        'called' => $callLog['toNumber'],
                        'group_id' => $isCallOut ? $callLog['fromGroup'] : $callLog['toGroup'],
                        'call_type' => $isCallOut ? 1 : 2,
                        'wait_time' => gmdate("H:i:s", $callLog['duration'] - $callLog['billDuration']),
                        'hold_time' => '00:00:00',
                        'talk_time' => gmdate("H:i:s", $callLog['billDuration']),
                        'end_status' => $callLog['hangupCause'],
                        'ticket_id' => 0,
                        'last_user_id' => 0,
                        'last_agent_id' => 0,
                        'call_survey' => '',
                        'start_time' => Carbon::createFromTimestampMs($callLog['startTime'])->format('Y-m-d H:i:s'),
                        'end_time' => Carbon::createFromTimestampMs($callLog['endTime'])->format('Y-m-d H:i:s'),
                        'call_status' => $callLog['hangupCause'] != M3cCall::CALL_IN_NORMAL_CALL ? 'miss' : 'meetAgent',
                        'vendor' => M3cCall::CALL_LOG_VENDOR_CALLIO,
                        'transcripts' => $callLog['transcripts'],
                        'transcript_synced' => count($callLog['transcripts']) ? 1 : 0,
                    ]
                )
            )
        ];
        $this->load->model('Call_center_model');
        $this->Call_center_model->syncCallLogs($calls);
    }
}

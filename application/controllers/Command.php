<?php

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

class Command extends App_Controller
{
    public function __construct()
    {
        if (!is_cli()) {
            access_denied('command');
        }
        parent::__construct();
        if (!is_cli()) {
            access_denied();
        }
        $this->load->model('command_model');
    }

    public function map_client_countries()
    {
        if (is_cli()) {
            $this->command_model->mapClientCountries();
            $this->command_model->mapClientAffiliates();
            $this->command_model->mapClientGeneralNotes();
        }
    }

    public function create_entities()
    {
        $tables = $this->db->list_tables();
        foreach ($tables as $table) {
            $tbl = str_replace('tbl', '', $table);
            $tblName = str_replace('_', '', ucwords($tbl, '_'));
            $file = APPPATH . '/entities/' . $tblName . '.php';
            if (!file_exists($file)) {
                $content = <<<CODE
                <?php

                namespace Entities;

                defined('BASEPATH') or exit('No direct script access allowed');

                class $tblName extends BaseEntity
                {
                    protected \$primaryKey = 'id';
                    protected \$table = '$tbl';
                }
                CODE;

                if (file_put_contents($file, mb_convert_encoding($content, 'UTF-8'))) {
                    echo "Created " . $tblName . " Done!" . PHP_EOL;
                } else {
                    echo "Created " . $tblName . " Failed!" . PHP_EOL;
                }
            }
        }
    }

    /**
     * Resync call logs data of the month
     * @param string $monthStr list of month saparater by ":" since it not allow ",". Ex: 202310:202311
     */
    public function resync_3c($monthStr)
    {
        if (is_cli()) {
            $months = explode(":", $monthStr);
            $this->load->helper('call_center');
            foreach ($months as $month) {
                $startMonth = Carbon::createFromFormat('Ym', $month);
                $startOfMonth = $startMonth->startOfMonth()->format('Y-m-d 00:00:00');
                $endOfMonth = $startMonth->endOfMonth()->format('Y-m-d 23:59:59');
                echo "fetchMonthCallLogs: $startOfMonth - $endOfMonth" . PHP_EOL;
                fetchMonthCallLogs($startOfMonth, $endOfMonth);
            }
        }
    }

    public function syncCallAudios($from, $to)
    {
        if (is_cli()) {
            $startDay = new DateTime($from);
            $endDay = new DateTime($to);
            $this->load->helper('call_center');
            do {
                $startOfDay = $startDay->format('Y-m-d 00:00:00');
                $endOfDay = $startDay->format('Y-m-d 23:59:59');
                echo "Date: " . $startDay->format('Y-m-d') . PHP_EOL;
                syncMissingCallAudios($startOfDay, $endOfDay);
                $startDay = date_add($startDay, new DateInterval('P1D'));
            } while ($startDay->format('Ymd') <= $endDay->format('Ymd'));
        }
    }

    public function apply_client_request_permission()
    {
        $this->load->model('roles_model');
        // Sale role
        $saleRole = json_decode(json_encode($this->roles_model->get($this->roles_model->SALES_MEMBER)), true);
        $saleRolePermission = $saleRole['permissions'];
        $saleRolePermission['customers'] = array_unique(array_merge(
            $saleRolePermission['customers'] ?? [],
            [
                'view_client_creating_request_own',
            ]
        ));
        $saleRole['permissions'] = $saleRolePermission;
        $saleRole['update_staff_permissions'] = true;
        $this->roles_model->update($saleRole, $this->roles_model->SALES_MEMBER);

        // Sale Leader role
        $saleLeaderRole = json_decode(json_encode($this->roles_model->get($this->roles_model->SALES_LEADER)), true);
        $saleLeaderRolePermission = $saleLeaderRole['permissions'];
        $saleLeaderRolePermission['customers'] = array_unique(array_merge(
            $saleLeaderRolePermission['customers'] ?? [],
            [
                'view_client_creating_request_own',
                'approve_client_creating_request_own'
            ]
        ));
        $saleLeaderRole['permissions'] = $saleLeaderRolePermission;
        $saleLeaderRole['update_staff_permissions'] = true;
        $this->roles_model->update($saleLeaderRole, $this->roles_model->SALES_LEADER);

        // Sale Admin role
        $saleAdminRole = json_decode(json_encode($this->roles_model->get($this->roles_model->SALES_ADMIN)), true);
        $saleAdminRolePermission = $saleAdminRole['permissions'];
        $saleAdminRolePermission['customers'] = array_unique(array_merge(
            $saleAdminRolePermission['customers'] ?? [],
            [
                'view_client_creating_request_global',
                'approve_client_creating_request_global',
                'bulk_remove_customer_admin'
            ]
        ));
        $saleAdminRole['permissions'] = $saleAdminRolePermission;
        $saleAdminRole['update_staff_permissions'] = true;
        $this->roles_model->update($saleAdminRole, $this->roles_model->SALES_ADMIN);
    }

    public function update_permission_edit_ams_id()
    {
        $this->load->model('roles_model');
        $this->roles_model->update_role_permissions($this->roles_model->SALES_ADMIN, 'customers', ['edit_id_ams']);
    }

    public function update_permission_edit_jobs()
    {
        $this->load->model('roles_model');
        // Change use expired at
        $this->roles_model->update_role_permissions($this->roles_model->SALES_ADMIN, 'invoices', ['edit_use_expired_at']);
        $this->roles_model->update_role_permissions($this->roles_model->SALES_LEADER, 'invoices', ['edit_use_expired_at']);

        // Edit jobs
        $this->roles_model->update_role_permissions($this->roles_model->SALES_ADMIN, 'customers', ['edit_job_global']);
        $this->roles_model->update_role_permissions($this->roles_model->SALES_LEADER, 'customers', ['edit_job_owned']);
        $this->roles_model->update_role_permissions($this->roles_model->SALES_MEMBER, 'customers', ['edit_job_owned']);

        // Updated used packages numbers
        $this->roles_model->update_role_permissions($this->roles_model->SALES_ADMIN, 'customers', ['edit_used_packages']);
    }
}

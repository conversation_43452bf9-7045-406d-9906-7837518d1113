<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Migration extends CI_Controller
{
    // Do not show confirmation for some information methods
    public const SKIP_CONFIRM = [
        '', // same as index
        'index',
        'list'
    ];
    public function __construct()
    {
        parent::__construct();
        $this->load->dbforge();
        $this->load->database();
        $this->load->library('migration');

        if (!is_cli()) {
            echo "No Permission !!!";
            exit;
        }
        // Get command arguments
        $method = $this->uri->segment(2) ?? '';
        if (!in_array($method, self::SKIP_CONFIRM) && ENVIRONMENT == 'production') {
            echo "{$this->setColor('Are you sure you want to do this', 'red')} ({$this->setColor('yes', 'green')}/{$this->setColor('no', 'red')}): ";

            if (trim(fgets(STDIN)) !== 'yes') {
                exit;
            }
        }

        error_reporting(-1);
        ini_set('display_errors', 1);
    }

    public function index()
    {
        $this->displayMessage("
            {$this->setColor('Command                                         Description', 'green')}
            php index.php migration                         - Displays available commands for migrations
            php index.php migration {$this->setColor('make', 'green')}                    - Make file migration
            php index.php migration {$this->setColor('migrate', 'green')}                 - Runs all pending migrations
            php index.php migration {$this->setColor('run', 'green')} {$this->setColor('{version} {method}', 'yellow')}  - Runs a specific migration with the given version and method
            php index.php migration {$this->setColor('rollback', 'green')} {$this->setColor('{version}', 'yellow')}      - Undoes a specific migration with the given version
            php index.php migration {$this->setColor('reset', 'green')}                   - Undoes all migrations
            php index.php migration {$this->setColor('refresh', 'green')}                 - Undoes all migrations and runs them again
            php index.php migration {$this->setColor('rollback', 'green')}                - Undoes the last migration
            php index.php migration {$this->setColor('list', 'green')}                    - Lists all available migrations
            php index.php migration {$this->setColor('upgrade', 'green')}                 - Upgrades the migrations to the latest version.
        ");
    }

    public function migrate()
    {
        $migrations = $this->migration->find_migrations();

        $count = 0;
        foreach ($migrations as $version => $migration) {
            if (!$migration['run']) {
                $this->run($migration['version']);
                $count++;
            }
        }

        if (!$count) {
            $this->displayMessage($this->setColor('Nothing to migrate', 'yellow'));
        }

        return true;
    }


    /**
     * Rollback migration
     * @param string $version
     * @return bool
     */
    public function rollback($version = "")
    {
        $migrations = $this->migration->find_migrations();
        krsort($migrations);

        $count = 0;

        foreach ($migrations as $migration) {
            $migrationVersion = $migration['version'];

            if ((!$version && $migration['run'] == 1) || ($version && $migrationVersion === $version)) {
                $this->run($migrationVersion, "down");
                $count++;

                break;
            }
        }

        if ($count === 0) {
            $this->displayMessage($this->setColor('Nothing to rollback', 'yellow'));
        }

        return true;
    }



    /**
     * Reset migration
     */
    public function reset()
    {
        $migrations = $this->migration->find_migrations();
        krsort($migrations);

        foreach ($migrations as $version => $migration) {
            if ($migration['run'] == 1) {
                $version = $migration['version'];
                $this->run($version, "down");
            }
        }

        $this->displayMessage($this->setColor('Migration reset success', 'green'));
        return true;
    }

    /**
     * Refresh migration
     */
    public function refresh()
    {
        $this->benchmark->mark('start');

        $this->reset();

        if (!$this->migrate()) {
            $this->displayMessage("Migration refresh fail");
            return false;
        }
        $this->displayMessage("Migration refresh success");
        $this->benchmark->mark('finish');
        $this->displayMessage("Total: " .  $this->benchmark->elapsed_time('start', 'finish') . "second");
    }

    /**
     * List migrations
     */
    public function list()
    {
        $migrations = $this->migration->find_migrations();
        $i = $unfinished = 0;
        $this->displayMessage("     {$this->setColor('Version         Status  File', 'green')}");

        foreach ($migrations as $migration) {
            $i++;
            $version = $migration['version'];
            $name = $migrations[$version]['name'];
            $run = $migration['run'] ? $this->setColor('OK', 'green') : "--";
            echo sprintf("%3d. %s    %s    %s \n", $i, $version, $run, $name);

            // How many records are outstanding
            if (!$migration['run']) {
                $unfinished++;
            }
        }

        $this->displayMessage("---  --------------  ------  ------------------------------------");
        $this->displayMessage($this->setColor("     {$unfinished} Migration not execute", 'red'));
    }

    /**
     * Run migration
     */
    public function run($version = "", $method = "up")
    {
        if (!$version) {
            $this->displayMessage("Please specify a version");
            return false;
        }

        $migrations = $this->migration->find_migrations();

        if (isset($migrations[$version]) && isset($migrations[$version]['name'])) {
            $name = $migrations[$version]['name'];
            $file = APPPATH . 'migrations/' . $name . '.php';
            include_once($file);

            $class = 'Migration_' . ucfirst(strtolower($this->_get_migration_name(basename($file, '.php'))));

            // Validate the migration file structure
            if (!class_exists($class, FALSE)) {
                $this->_error_string = sprintf($this->lang->line('migration_class_doesnt_exist'), $class);
                return FALSE;
            } elseif (!is_callable(array($class, $method))) {
                $this->_error_string = sprintf($this->lang->line('migration_missing_' . $method . '_method'), $class);
                return FALSE;
            }

            $migration_obj = new $class();

            if (($method == "up" && !$migrations[$version]['run']) || ($method == "down" && $migrations[$version]['run'])) {
                $migration_obj->$method();

                $this->displayMessage("{$class}::{$method}() ............. {$this->setColor('success', 'green')}");

                $this->migration->_update_version([
                    'version' => $version,
                    'name' => $name,
                    'run' => 1
                ], $method);
            } else {
                $this->displayMessage("{$class}::{$method}() ............. {$this->setColor('failed', 'red')}");
            }
        } else {
            $this->displayMessage($this->setColor('Migration not found', 'yellow'));
        }
    }

    public function upgrade()
    {
        $this->load->config('migration');
        $table = $this->config->item('migration_table');

        if (in_array("run", $this->db->list_fields($table))) {
            $this->displayMessage($this->setColor('It is already a new version of Migration', 'yellow'));
            return false;
        }

        $this->displayMessage($this->setColor('Start updating the Migration data table...', 'green'));

        $this->dbforge->add_column($table, [
            'name' => [
                'type' => 'varchar',
                'constraint' => '255',
                'null' => false,
            ],
            'run' => [
                'type' => 'int',
                'constraint' => '',
                'null' => false,
            ]
        ]);

        // Check for success
        $this->db->data_cache = [];
        if (!in_array("run", $this->db->list_fields($table))) {
            $this->displayMessage($this->setColor('Migrations table update failed', 'red'));
            return false;
        }
        // Replace data
        $this->db->update($table, ['run' => 1]);

        $migrations = $this->migration->find_migrations();
        $this->db->truncate($table);

        foreach ($migrations as $migration) {

            if ($migration['run']) {
                $this->db->insert($table, [
                    'version' => $migration['version'],
                    'name' => $migration['name'],
                    'run' => 1,
                ]);
            }
        }

        $this->displayMessage($this->setColor('Migration data table conversion completed', 'green'));
    }

    protected function _get_migration_name($migration)
    {
        $parts = explode('_', $migration);
        array_shift($parts);
        return implode('_', $parts);
    }

    public function make($table_name = '')
    {
        if (empty($table_name)) {
            $this->displayMessage($this->setColor('Provide your migration name, e.g., php index.php migration make add_new_post_table', 'yellow'));
            exit;
        }

        $name = $this->convertCase($table_name);
        $version = date('YmdHis');
        $className = 'Migration_' . $name . '_' . $version;
        $fileName = $version . '_' . strtolower($name) . '_' . $version . '.php';
        $tableName = $this->guessTableName($name);
        $fullFileName = APPPATH . '/migrations/' . $fileName;
        $content = $this->createTableSnippet($className, $tableName);

        if (file_put_contents($fullFileName, mb_convert_encoding($content, 'UTF-8'))) {
            $this->displayMessage($this->setColor('Generated version: ' . $version, 'green'));
            $this->displayMessage($this->setColor('Generated file name: ' . $fileName, 'green'));

            return true;
        } else {
            $this->displayMessage($this->setColor('Generation failed', 'red'));
            return false;
        }
    }

    private function setColor($text, $color) {
        switch ($color) {
          case "black":
            $colorCode = "\033[0;30m";
            break;
          case "red":
            $colorCode = "\033[0;31m";
            break;
          case "green":
            $colorCode = "\033[0;32m";
            break;
          case "yellow":
            $colorCode = "\033[0;33m";
            break;
          case "blue":
            $colorCode = "\033[0;34m";
            break;
          case "purple":
            $colorCode = "\033[0;35m";
            break;
          case "cyan":
            $colorCode = "\033[0;36m";
            break;
          case "white":
            $colorCode = "\033[0;37m";
            break;
          default:
            $colorCode = "";
            break;
        }
        return $colorCode . $text . "\033[0m";
    }

    private function displayMessage($message)
    {
        echo $message . PHP_EOL;
    }

    private function convertCase($input)
    {
        if (strpos($input, '_') !== false) {
            $words  = explode('_', $input);
            $words[0] = ucfirst($words[0]);
            for ($i = 0; $i < count($words); $i++) {
                $words[$i] = ucfirst(strtolower($words[$i]));
            }
            $output = implode('_', $words);
        } else {
            $output = preg_replace('/[A-Z]/', '_$0', $input);
        }

        $output = ucfirst($output);
        return trim($output, '_');
    }

    private function guessTableName($name)
    {
        $cleanup = str_replace(array(
            "Create_",
            "Add_New_",
            "Modify_",
            "Update_",
            "Rename_",
            "Remove_",
            "Table_",
            "Column_",
            "To_",
            "_Table",
        ), "", $name);

        return strtolower($cleanup);
    }
    private function createTableSnippet($className, $tableName)
    {
        $content = <<<CODE
        <?php

        defined('BASEPATH') or exit('No direct script access allowed');
        class $className extends CI_Migration
        {
            public function up()
            {
                // Drop table 'table_name' if it exists
                \$this->dbforge->drop_table('$tableName', true);

                // Table structure for table 'table_name'
                \$this->dbforge->add_field([
                    'id' => [
                        'type' => 'INT',
                        'constraint' => '11',
                        'unsigned' => true,
                        'auto_increment' => true
                    ],
                    'created_at datetime default CURRENT_TIMESTAMP',
                    'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
                ]);
                \$this->dbforge->add_key('id', true);
                if(\$this->dbforge->create_table('$tableName')) {
                    echo "\\n\\rTable $tableName migrated.\\n\\r";
                }
            }
            public function down()
            {
                \$this->dbforge->drop_table('$tableName', true);
            }
        }
        CODE;

        return $content;
    }
}

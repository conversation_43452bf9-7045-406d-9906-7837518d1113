<?php

namespace Entities;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

defined('BASEPATH') or exit('No direct script access allowed');

class ClientAmsCompany extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'client_ams_companies';
    protected $fillable = [
        'client_id',
        'ams_company_id'
    ];
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    public function client():BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id', 'userid');
    }
}

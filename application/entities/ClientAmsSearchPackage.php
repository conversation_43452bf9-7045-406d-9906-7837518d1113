<?php

namespace Entities;

use app\services\AmsService;
use DB;

defined('BASEPATH') or exit('No direct script access allowed');

class ClientAmsSearchPackage extends BaseEntity
{
    protected $fillable = [
        'client_id',
        'ams_company_id',
        'crm_itemable_id',
        'ams_search_package_id',
        'invoice_id',
        'ams_company_search_package_id',
        'paid_at',
    ];

    protected $casts = [
        'paid_at' => 'datetime',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    public function itemable()
    {
        return $this->belongsTo(Itemable::class, 'crm_itemable_id');
    }

    public function amsSearchPackage()
    {
        return $this->belongsTo(AmsSearchPackage::class, 'ams_search_package_id', 'ams_search_package_id');
    }

    /**
     * When accountant make payment or sale add credit item this function will fetch all credit item is not synced to ams yet and sync it
     *
     * @param int $invoiceId
     * @return void
     */
    public static function syncPaidSearchCv($invoiceId, $isFreeItem = false)
    {
        // Fetch all credit item that related to invoice
        $itemables = Itemable::select('id', 'item_id', 'qty')
            ->with([
                'item:id',
                'item.amsSearchPackage:crm_item_id,ams_search_package_id',
            ])
            ->where('rel_id', $invoiceId)
            ->invoiceType()
            ->whereHas('invoice', fn($query) => $query->paid())
            ->isCredit()
            ->when($isFreeItem, fn($query) => $query->where('rate', 0))
            ->get()
            ->groupBy('item.amsSearchPackage.ams_search_package_id');

        $mappedItems = ClientAmsSearchPackage::query()
            ->where('invoice_id', $invoiceId)
            ->get()
            ->groupBy('ams_search_package_id');

        // Map to get item will be synced to AMS
        $syncItems = $itemables->map(function ($credits, $creditId) use ($mappedItems) {
            $mappedCredits = $mappedItems->get($creditId) ?? collect();
            if ($mappedCredits->count()) {
                $creditCount = $mappedCredits->countBy('crm_itemable_id');
                return $credits->map(fn($credit) => ['itemableid' => $credit['id'], 'qty' => intval($credit['qty']) - ($creditCount[$credit['id']] ?? 0)])->filter(fn($item) => $item['qty'] > 0);
            }

            return $credits->map(fn($credit) => ['itemableid' => $credit['id'], 'qty' => intval($credit['qty'])]);
        })->filter(fn($item) => $item->count() > 0);

        // If there is no credit package need to be sync, then return
        if (!$syncItems->count()) {
            return;
        }

        self::syncSearchCvToAms($invoiceId, $syncItems);
    }

    /**
     * Get related data then sync to AMS and create mapping in the ams_search_package table
     *
     * @param [type] $invoiceId
     * @param [type] $syncItems
     * @return void
     */
    protected static function syncSearchCvToAms($invoiceId, $syncItems)
    {
        // Call API to create package in AMS
        try {
            DB::beginTransaction();
            $invoice = Invoice::select('id', 'clientid')->where('id', $invoiceId)->firstOrFail();
            $clientAmsCompanies = ClientAmsCompany::where('client_id', $invoice->clientid)->where('ams_company_id', '>', 0)->get();

            $clientAmsCompanies->each(function ($amsCompany) use ($syncItems, $invoice, $invoiceId) {
                $options = [
                    'form_params' => [
                        'search_cvs' => $syncItems->map(fn($items, $packageId) => ['id' => $packageId, 'qty' => $items->sum('qty'), 'crm_itemable_id' => $items->first()['itemableid']])->values()->toArray(),
                    ]
                ];

                // Call AMS api to add unlock credit
                $response = AmsService::amsApi('crm/company-credits/' . $amsCompany->ams_company_id, $options);

                if (!$response['error'] && count($searchCvs = $response['data'])) {
                    $insertCredits = [];
                    $syncItems->each(function ($items, $packageId) use ($invoice, $invoiceId, $amsCompany, $searchCvs, &$insertCredits) {
                        $createdSearchCvs = collect($searchCvs[$packageId] ?? []);
                        $index = 0;
                        $items->each(function ($item) use ($packageId, $invoice, $invoiceId, $amsCompany, $createdSearchCvs, &$insertCredits, &$index) {
                            collect()->times($item['qty'])->each(function () use ($packageId, $invoice, $invoiceId, $amsCompany, $createdSearchCvs, $item, &$insertCredits, &$index) {
                                $insertCredits[] = [
                                    'client_id' => $invoice->clientid,
                                    'invoice_id' => $invoiceId,
                                    'ams_company_id' => $amsCompany->ams_company_id,
                                    'crm_itemable_id' => $item['itemableid'],
                                    'ams_search_package_id' => $packageId,
                                    'ams_company_search_package_id' => $createdSearchCvs->get($index++),
                                    'paid_at' => $invoice->paymentRecord->date->format('Y-m-d 00:00:00'),
                                ];
                            });
                        });
                    });
                    ClientAmsSearchPackage::insert($insertCredits);
                }
            });
            DB::commit();
        } catch (\Exception $ex) {
            DB::rollback();
            defined('SENTRY_DSN') && \Sentry\captureException($ex);
        }
    }
}

<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class ClientAmsOpenJob extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'client_ams_open_jobs';
    protected $fillable = [
        'client_id',
        'ams_company_id',
        'ams_job_id',
        'invoice_id',
        'itemable_id',
        'itemable_id',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    public function itemable()
    {
        return $this->belongsTo(Itemable::class, 'itemable_id');
    }
}

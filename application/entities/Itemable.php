<?php

namespace Entities;

use app\services\AmsService;
use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

class Itemable extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'itemable';

    public const TYPE_GIFT = JOB_TRACKING_REPORT_TYPE_GIFT_PACKAGE;
    public const TYPE_SERVICE = JOB_TRACKING_REPORT_TYPE_SERVICE_PACKAGE;

    public const REL_TYPE_INVOICE = "invoice";

    public const FREE_ITEM = 1;
    public const PAID_ITEM = -1;

    protected $fillable = [
        'use_expired_at'
    ];

    protected $casts = [
        'use_expired_at' => 'datetime'
    ];

    public function currency()
    {
        return $this->hasOne(Currency::class, 'id', 'currency');
    }

    /**
     * Model relationship
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function item()
    {
        return $this->belongsTo(Item::class, 'item_id');
    }

    public function tax()
    {
        return $this->hasOne(ItemTax::class, 'itemid');
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'rel_id');
    }

    public function amsOpenJobs()
    {
        return $this->hasMany(ClientAmsOpenJob::class, 'itemable_id');
    }

    public function amsJobs()
    {
        return $this->hasMany(ClientAmsJob::class, 'package_id');
    }

    public function scopeInvoiceType($query)
    {
        return $query->where('itemable.rel_type', self::REL_TYPE_INVOICE);
    }

    public function scopeIsCredit($query)
    {
        return $query->whereHas('item', fn($query) => $query->isCredit());
    }
    

    public function scopeIsService($query)
    {
        return $query->whereHas('item', fn($query) => $query->isService());
    }
    

    public function scopeIsJobPosting($query)
    {
        return $query->whereHas('item', fn($query) => $query->isJobPosting());
    }

    public function scopeIsNotCredit($query)
    {
        return $query->whereHas('item', fn($query) => $query->isNotCredit());
    }

    public function isSmePackage()
    {
        return $this->item && $this->item->isSmePackage();
    }

    public function isNew2024Package()
    {
        return $this->item && $this->item->isNew2024Package();
    }

    public function scopePaidItems($query)
    {
        return $query->whereHas('item', fn($builder) => $builder->paidItems());
    }

    public function scopeIsDiscountPackage($query)
    {
        return $query->where('rate', 0);
    }

    public function scopeIsNotDiscountPackage($query)
    {
        return $query->where('rate', '>', 0);
    }

    public function scopeHasOpenJobs($query)
    {
        $query->where(
            'qty',
            '>',
            fn($builder) => $builder->selectRaw('COUNT(id)')
                ->from('client_ams_open_jobs')
                ->whereColumn('client_ams_open_jobs.itemable_id', 'itemable.id')
        );
    }
    public function scopeHasUnusedPackage($query)
    {
        $query->where(
            'qty',
            '>',
            fn($builder) => $builder->selectRaw('COUNT(id)')
                ->from('client_ams_jobs')
                ->whereColumn('client_ams_jobs.package_id', 'itemable.id')
        );
    }
    public function scopeHasUnusedCombo($query)
    {
        $query->where(
            'qty',
            '>',
            fn($builder) => $builder->selectRaw('COUNT(id)')
                ->from('client_ams_search_packages')
                ->whereColumn('client_ams_search_packages.crm_itemable_id', 'itemable.id')
        );
    }
    
    public function scopeHasUnusedService($query)
    {
        $query->where(
            'qty',
            '>',
            fn($builder) => $builder->selectRaw('COUNT(id)')
                ->from('client_ams_services')
                ->whereColumn('client_ams_services.crm_itemable_id', 'itemable.id')
        );
    }

    public function scopeHasAmsPackage($query, $amsPackageId)
    {
        $query->whereHas(
            'item',
            fn($item)
            => $item->whereHas(
                'amsPackage',
                fn($amsPackage)
                => $amsPackage->where('ams_package_id', $amsPackageId)
            )
        );
    }

    public function scopeHasAmsSearchCv($query)
    {
        $query->whereHas(
            'item',
            fn($item) => $item->isCredit()
        );
    }

    public function scopeUseNotExpired($query)
    {
        $query->where('itemable.use_expired_at', '>=', date('Y-m-d'));
    }

    public function scopeUseExpired($query)
    {
        $query->where('itemable.use_expired_at', '<', date('Y-m-d'));
    }

    public function isUseNotExpired()
    {
        return $this->use_expired_at && $this->use_expired_at->greaterThanOrEqualTo(Carbon::now());
    }

    public function scopeUseExpiredAtBetween($query, $ranges)
    {
        $query->where('itemable.use_expired_at', '>=', Carbon::parse($ranges[0])->startOfDay()->format('Y-m-d H:i:s'))
            ->where('itemable.use_expired_at', '<=', Carbon::parse($ranges[1])->endOfDay()->format('Y-m-d H:i:s'));
    }

    /**
     * Get package by label from AMS to count correctly
     *
     * @param \Illuminate\Support\Collection $linkedJobs
     * @return array
     */
    public static function packageByLabel($linkedJobs)
    {
        $packages = collect();
        $smePackages = collect();
        $new2024Packages = collect();

        $linkedJobs->each(function ($job) use (&$packages, &$smePackages, &$new2024Packages) {
            // Detect SMES package by AMS label
            if (in_array(Item::SMES_PACKAGE, $job['notes_label'] ?? [])) {
                $smePackages = $smePackages->merge($job['package_list']);
            } else if (in_array(Item::NEW_2024_PACKAGE, $job['notes_label'] ?? [])) {
                $new2024Packages = $packages->merge($job['package_list']);
            } else {
                $packages = $packages->merge($job['package_list']);
            }
        });

        return [$packages->groupBy('id'), $smePackages->groupBy('id')];
    }

    /**
     * Get used package by ams package
     *
     * @param \Illuminate\Support\Collection $amsPackages
     * @param array $packageByLabel
     * @param  \Illuminate\Support\Collection $item
     * @param \Illuminate\Support\Carbon $expired
     * @param \Illuminate\Support\Collection|null $jobsHasPackages
     * @param integer|null $invoiceId
     * @return integer
     */
    public static function getUsedByPackage($amsPackages, $packageByLabel, $item, $expired, $jobsHasPackages = null, $invoiceId = null)
    {
        [$packages, $smePackages] = $packageByLabel;
        $amsPackage = $amsPackages->get($item->item->id);
        $used = 0;
        if ($amsPackage) {
            if ($item->item->isSmePackage()) {
                $package = $smePackages->get($amsPackage->first()->ams_package_id) ?? collect();
            } else {
                $package = $packages->get($amsPackage->first()->ams_package_id) ?? collect();
            }
            $used = ($expired && $expired->greaterThanOrEqualTo(Carbon::now())) ? $package->count() : 0;
        }
        if ($jobsHasPackages && $invoiceId) {
            $used += optional(optional($jobsHasPackages->get($invoiceId))->where('itemable_id', $item->id))->count() ?? 0;
        }

        return $used;
    }

    /**
     * Get expires in for package by package type
     *
     * @param Itemable $paidPackage
     * @return int
     */
    public static function getExpiresInByPackage($paidPackage = null)
    {
        $expiresIn = AmsPackage::PACKAGE_ENTERPRISE_EXPIRE_DAYS;
        if ($paidPackage) {
            if ($paidPackage->isSmePackage() || $paidPackage->isNew2024Package()) {
                $expiresIn = AmsPackage::PACKAGE_SME_EXPIRE_DAYS;
            }
        }

        return $expiresIn;
    }

    public static function getAmsLabelByPackage($paidPackage = null)
    {
        $noteLabels = [];
        if ($paidPackage) {
            if ($paidPackage->isSmePackage()) {
                $noteLabels = [Item::SMES_PACKAGE];
            }
        }

        return $noteLabels;
    }

    public static function getAvailableItems($invoiceId)
    {
        $clientAmsJobs = ClientAmsJob::select('ams_job_id', 'used_packages', 'package_id')->whereInvoiceId($invoiceId)->get();
        $clientAmsOpenJobCount = ClientAmsOpenJob::select('itemable_id')->whereInvoiceId($invoiceId)->pluck('itemable_id')->countBy();
        $invoice = Invoice::select('id', 'use_expired_at')
            ->whereId($invoiceId)
            ->with([
                'invoiceItems' => fn($builder)
                => $builder->select('id', 'qty', 'rel_id', 'rel_type', 'item_id')
                    ->paidItems()
                    ->isNotCredit()
            ])
            ->first();

        $usedPackages = [];
        if ($clientAmsJobs->count()) {
            // Merge duplicate item by add quantity
            collect($clientAmsJobs->first()->used_packages)->each(function ($qty, $key) use (&$usedPackages) {
                $usedPackages[$key] = $qty;
            });
        }

        // Count used packages by package that assigned to open jobs
        $clientAmsOpenJobCount->each(function ($qty, $key) use (&$usedPackages) {
            $usedPackages[$key] = ($usedPackages[$key] ?? 0) + $qty;
        });

        $itemables = $invoice->invoiceItems->pluck('qty', 'id');

        // If there are used package, should filter items that used all packages
        if (count($usedPackages)) {
            return $itemables->filter(fn($qty, $key) => empty($usedPackages[$key]) || $usedPackages[$key] < intval($qty))->keys();
        }

        // Return all itemIds to allow adding/editing
        return $itemables->keys();
    }

    /**
     * Set used expired at
     * @param null|string $date should be format Y-m-d
     */
    public function setUseExpiredAt($date = null)
    {
        $prevExpiredDate = $this->use_expired_at;
        $success = $this->update([
            'use_expired_at' => $date ? $date : date('Y-m-d', strtotime('+365 days'))
        ]);

        if ($success && $date) {
            log_activity(
                'Itemable Use Expired Updated [ID: ' . $this->id . ', From: ' . $prevExpiredDate . ', To: ' . $date . ']',
                get_staff_user_id(),
                $this->clientid,
                [],
                $this->id,
                'updated_itemable_use_expired'
            );
        }
    }

    public function getItemSaleRateAttribute()
    {
        return $this->rate * (1 - $this->discount_table);
    }

    public function getItemSaleRate($invoiceDiscountPercent = 0)
    {
        $itemRate = $this->item_sale_rate;
        return $itemRate - ($itemRate * ($invoiceDiscountPercent / 100));
    }

    public function getPackageDurationAttribute()
    {
        return in_array(
            $this->item_id,
            [23, 4]
        ) ? 60 : 30;
    }
}

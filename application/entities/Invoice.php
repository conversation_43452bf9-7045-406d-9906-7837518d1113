<?php

namespace Entities;

use app\services\AccountingService;
use Carbon\Carbon;
use Dto\Infoplus\CreateECollectionRecvDTO;
use Dto\Minvoice\CreateInvoiceDetailDTO;
use Dto\Minvoice\CreateInvoiceDetailTT32DTO;
use Dto\Minvoice\CreateInvoiceItemDTO;
use Dto\Infoplus\QrCodeDTO;
use Dto\Infoplus\UpdateECollectionRecvDTO;
use Dto\Minvoice\CreateInvoiceDTO;
use Dto\Minvoice\CreateInvoiceItemTT32DTO;
use Dto\Minvoice\CreateInvoiceTT32DTO;
use Dto\Minvoice\UpdateInvoiceDTO;
use Dto\Minvoice\UpdateInvoiceTT32DTO;
use Illuminate\Database\Eloquent\Builder;

defined('BASEPATH') or exit('No direct script access allowed');

class Invoice extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'invoices';

    protected $fillable = [
        'use_expired_at',
        'invoice_closing_date',
        'approved_by',
        'addition_discount_approved_at',
        'rejected_by',
        'addition_discount_rejected_at',
    ];

    protected $casts = [
        'date' => 'date',
        'use_expired_at' => 'datetime',
        'invoice_closing_date' => 'datetime',
        'addition_discount_approved_at' => 'datetime',
        'addition_discount_rejected_at' => 'datetime',
    ];

    public const STATUS_UNPAID = 1;
    public const STATUS_PAID = 2;
    public const STATUS_PARTIALLY = 3;
    public const STATUS_OVERDUE = 4;
    public const STATUS_CANCELLED = 5;
    public const STATUS_DRAFT = 6;

    public function scopePaid($query)
    {
        $query->where('invoices.status', self::STATUS_PAID);
    }

    public function scopeUnpaid($query)
    {
        $query->where('invoices.status', self::STATUS_UNPAID);
    }

    public function scopeNotPaidYet($query)
    {
        $query->where('invoices.status', '!=', self::STATUS_PAID);
    }

    public function scopeNotDraft($query)
    {
        $query->whereNotIn('invoices.status', [self::STATUS_DRAFT, self::STATUS_CANCELLED]);
    }

    public function scopeUseNotExpired($query)
    {
        $query->whereHas('invoiceItems', fn($query) => $query->useNotExpired());
    }

    public function scopeHasInvoiceIssueDate($query)
    {
        $query->whereNotNull('invoice_closing_date');
    }

    public function client()
    {
        return $this->belongsTo(Client::class, 'clientid');
    }

    public function isPaid()
    {
        return $this->status === self::STATUS_PAID;
    }

    public function customerAdmin()
    {
        return $this->hasOne(CustomerAdmin::class, 'customer_id', 'clientid');
    }

    public function creator()
    {
        return $this->belongsTo(Staff::class, 'addedfrom', 'staffid');
    }

    public function invoiceItems()
    {
        return $this->hasMany(Itemable::class, 'rel_id')->where('rel_type', Itemable::REL_TYPE_INVOICE);
    }

    public function clientAmsJobs()
    {
        return $this->hasMany(ClientAmsJob::class, 'invoice_id');
    }

    public function clientAmsOpenJobs()
    {
        return $this->hasMany(ClientAmsOpenJob::class, 'invoice_id');
    }

    public function clientAmsSearchCvs()
    {
        return $this->hasMany(ClientAmsSearchPackage::class, 'invoice_id');
    }

    public function clientAmsServices()
    {
        return $this->hasMany(ClientAmsService::class, 'invoice_id');
    }

    public function reportClientAmsJobs()
    {
        return $this->hasMany(ReportClientAmsJob::class, 'invoice_id');
    }

    public function invoiceNumber()
    {
        return $this->hasOne(CustomFieldValue::class, 'relid')
            ->where([
                'fieldto' => 'invoice',
                'fieldid' => 12,
            ])
            ->latestOfMany();
    }


    public function paymentRecord()
    {
        return $this->hasOne(InvoicePaymentRecord::class, 'invoiceid');
    }

    public function latestExtended()
    {
        return $this->hasOne(ActivityLog::class, 'rel_id')
            ->where('rel_type', 'updated_use_expired')
            ->latestOfMany();
    }

    public function getRangeLatestExtendedAttribute(): array
    {
        $latestExtended = $this->latestExtended;

        if ($latestExtended && $description = $latestExtended->description) {
            // Define the regular expression pattern
            $pattern = '/From:\s+(\d{4}-\d{2}-\d{2}),\s+To:\s+(\d{4}-\d{2}-\d{2})/';

            // Perform a regular expression match
            if (preg_match($pattern, $description, $matches)) {
                return [
                    'from' => $matches[1],
                    'to' => $matches[2]
                ];
            }
        }

        return [];
    }

    public function hasExtended(): bool
    {
        return !empty($this->rangeLatestExtended);
    }

    /**
     * Set used expired at
     * @param null|string $date should be format Y-m-d
     */
    public function setUseExpiredAt($date = null)
    {
        $prevExpiredDate = $this->use_expired_at;
        $newExpiredDate = $date ? $date : date('Y-m-d', strtotime('+365 days'));
        $success = $this->update(['use_expired_at' => $newExpiredDate]) && $this->invoiceItems()->update(['use_expired_at' => $newExpiredDate]);

        if ($success && $date) {
            log_activity(
                'Invoice Use Expired Updated [ID: ' . $this->id . ', From: ' . $prevExpiredDate . ', To: ' . $date . ']',
                get_staff_user_id(),
                $this->clientid,
                [],
                $this->id,
                'updated_use_expired'
            );
        }
    }

    /**
     * Set used expired at
     */
    public function handleSetItemableUseExpires()
    {
        if ($this->total > 0 && !$this->minvoice) {
            return;
        }

        $startDate = $this->minvoice->invoice_issued_date ?? Carbon::parse($this->datecreated);
        $expiresDate = $startDate->addMonths(12)->subDay();
        $result = $this->invoiceItems()->update(['use_expired_at' => $expiresDate->format('Y-m-d')]);
        if ($result) {
            log_activity(
                'Invoice Use Expired Updated [ID: ' . $this->id . ', From: null, To: ' . $expiresDate->format('Y-m-d') . ']',
                get_staff_user_id(),
                $this->clientid,
                [],
                $this->id,
                'updated_use_expired'
            );
        }
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function paymentRecords()
    {
        return $this->hasMany(InvoicePaymentRecord::class, 'invoiceid');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function lastPaymentRecord()
    {
        return $this->hasOne(InvoicePaymentRecord::class, 'invoiceid')->latestOfMany('date');
    }

    public function scopeUseExpiredAtBetween($query, $ranges)
    {
        $query->whereHas('invoiceItems', fn($query) => $query->useExpiredAtBetween($ranges));
    }

    public function scopeInAmsCompany($query, $amsCompanyId)
    {
        $query->whereIn(
            'clientid',
            fn($builder)
            => $builder->select('client_id')
                ->from('client_ams_companies')
                ->where('ams_company_id', $amsCompanyId)
        );
    }

    public function scopeHasAmsPackageId($query, $amsPackageId)
    {
        $query->whereHas('invoiceItems', fn($query) => $query->hasAmsPackage($amsPackageId));
    }

    public function scopeHasAmsSearchCvId($query)
    {
        $query->whereHas('invoiceItems', fn($query) => $query->hasAmsSearchCv());
    }

    public function invoiceRequest()
    {
        return $this->hasOne(InvoiceRequest::class, 'invoiceid');
    }

    public function minvoice()
    {
        return $this->hasOne(MInvoice::class, 'invoice_id');
    }

    /**
     * Create items of the invoice
     *
     * @param mixed $isTT32
     * @throws \Exception
     * @return CreateInvoiceDetailDTO|CreateInvoiceDetailTT32DTO
     */
    private function getDetailsItem($isTT32 = false)
    {
        if (!$this->invoiceRequest) {
            throw new \Exception('There is no invoice request, please create it first!');
        }

        $invoiceItems = $this->invoiceItems()
            ->with([
                'item:id,description,group_id',
                'tax:itemid,taxrate'
            ])
            ->isNotDiscountPackage()
            ->get()
            ->sortBy('item_order');

        if ($invoiceItems->isEmpty()) {
            throw new \Exception('There is no paid items');
        }

        $totalItems = $invoiceItems->sum('qty');
        $fixedDiscountPerItem = $this->discount_fixed / $totalItems;

        // Create InvoiceItemDTO by loop all PO items
        $index = 1;
        $items = [];
        $invoice = $this;

        $invoiceItems->each(function ($item) use (&$index, &$items, $invoice, $fixedDiscountPerItem, $isTT32) {
            $qty = intval($item->qty);
            $itemRate = $item->rate;
            $discountByItem = $itemRate * ($item->discount_table ?? 0);
            $itemRate -= $discountByItem;
            $totalItemPrice = $itemRate * $qty;
            $discountByPercent = ($invoice->discount_percent / 100) * $itemRate;
            $totalItemDiscount = round($discountByPercent + $fixedDiscountPerItem) * $qty;

            $totalPrice = $totalItemPrice - $totalItemDiscount;

            $taxRate = $item->tax ? intval($item->tax->taxrate) : 0;
            $taxTotal = $totalPrice * ($taxRate / 100);
            $unitCode = Item::getUnitCodeByGroup($item->item->group_id);
            $itemCode = Item::getItemCodeByGroup($item->item->group_id);

            array_push(
                $items,
                new CreateInvoiceItemTT32DTO(
                    $index,
                    $item->item ? $item->item->description : $item->description,
                    $qty,
                    $totalPrice / $qty,
                    0,
                    0,
                    $totalPrice,
                    $taxRate,
                    $taxTotal,
                    $totalPrice + $taxTotal,
                    MInvoice::PRODUCT,
                    $unitCode,
                    $itemCode,
                )
            );
            $index++;
        });

        return $isTT32 ? new CreateInvoiceDetailTT32DTO($items) : new CreateInvoiceDetailDTO($items);
    }

    /**
     * Update draft invoice based on the PO
     *
     * @throws \Exception
     * @return int
     */
    public function updateDraftInvoice()
    {
        if (!$this->minvoice) {
            throw new \Exception('There is no draft invoice. Please create it first!');
        }

        $invoiceDetails = $this->getDetailsItem();
        $client = $this->client;

        $createInvoice = new UpdateInvoiceDTO(
            $this->invoiceRequest->invoice_number,
            MINVOICE_SERIAL_VAT,
            MInvoice::CURRENCY_CODE_VND,
            $client->business_name,
            $client->vat,
            $client->address,
            $this->total,
            $this->discount_total,
            $this->total_tax,
            $this->subtotal - $this->discount_total,
            $invoiceDetails,
        );

        $response = AccountingService::updateDraftInvoice($this->invoiceRequest->invoice_uuid, $createInvoice);

        if (!$response['success']) {
            throw new \Exception($response['message'] ?? 'Failed to update draft invoice');
        }

        $isUpdated = MInvoice::where('id', $this->minvoice->id)
            ->update(array_filter(
                [
                    'invoice_series' => $response['data']['khieu'],
                    'invoice_number' => $response['data']['shdon'] ?? null,
                ]
            ));

        return $isUpdated;
    }

    /**
     * Update draft invoice based on the PO
     *
     * @throws \Exception
     * @return int
     */
    public function updateDraftInvoiceTT32(InvoiceRequest $invoiceRequest)
    {
        if (!$this->minvoice) {
            throw new \Exception('There is no draft invoice. Please create it first!');
        }

        $invoiceDetails = $this->getDetailsItem(true);
        $client = $this->client;

        $createInvoice = new UpdateInvoiceTT32DTO(
            MINVOICE_SERIAL_VAT,
            MInvoice::CURRENCY_CODE_VND,
            $client->business_name,
            $client->vat,
            $client->address,
            $this->total,
            0,
            $invoiceDetails,
            $this->total_tax,
            $this->subtotal - $this->discount_total,
        );
        $invoiceRequest->received_email && $createInvoice->setEmail($invoiceRequest->received_email);
        if ($this->minvoice->invoice_number) {
            $createInvoice->setInvNumber($this->minvoice->invoice_number);
        }

        // If request PO is person, need set some information
        if ($invoiceRequest->walk_in_customer) {
            $createInvoice->setPerson($invoiceRequest->company_name);
            $createInvoice->setPersonAddress($invoiceRequest->export_address);
        } else {
            $createInvoice->maskAsCompany();
        }

        $response = AccountingService::updateDraftInvoiceTT32($this->minvoice->invoice_uuid, $createInvoice);

        if (!$response['success']) {
            throw new \Exception($response['message'] ?? 'Failed to update draft invoice');
        }

        $isUpdated = MInvoice::where('id', $this->minvoice->id)
            ->update(array_filter(
                [
                    'invoice_series' => $response['data']['khieu'],
                    'invoice_number' => $response['data']['shdon'] ?? null,
                ]
            ));

        return $isUpdated;
    }

    /**
     * Create draft invoice based on the PO
     *
     * @throws \Exception
     * @return int
     */
    public function createDraftInvoice()
    {
        $invoiceDetails = $this->getDetailsItem();
        $client = $this->client;

        $createInvoice = new CreateInvoiceDTO(
            MINVOICE_SERIAL_VAT,
            MInvoice::CURRENCY_CODE_VND,
            $client->business_name,
            $client->vat,
            $client->address,
            $this->total,
            $this->discount_total,
            $this->total_tax,
            $this->subtotal - $this->discount_total,
            $invoiceDetails,
        );

        $response = AccountingService::createDraftInvoice($createInvoice);

        if (!$response['success']) {
            throw new \Exception($response['message'] ?? 'Failed to create draft invoice');
        }

        $createdId = MInvoice::upsert(
            array_filter(
                [
                    'invoice_id' => $this->id,
                    'invoice_request_id' => $this->invoiceRequest->id,
                    'invoice_uuid' => $response['data']['hoadon68_id'],
                    'invoice_series' => $response['data']['khieu'],
                    'invoice_number' => $response['data']['shdon'] ?? null,
                    'invoice_status' => $response['data']['is_tthdon'] ?? null,
                ]
            ),
            [
                'invoice_id'
            ],
            [
                'invoice_uuid',
                'invoice_series',
                'invoice_issued_date',
                'invoice_number'
            ]
        );

        return $createdId;
    }

    /**
     * Create draft invoice based on the PO
     *
     * @throws \Exception
     * @return int
     */
    public function createDraftInvoiceTT32(int $requestId, $isPerson = false)
    {
        /**
         * @var mixed
         */
        $invoiceDetails = $this->getDetailsItem(true);
        $client = $this->client;

        $invoiceRequest = InvoiceRequest::findOrFail($requestId);

        $createInvoice = new CreateInvoiceTT32DTO(
            MINVOICE_SERIAL_VAT,
            MInvoice::CURRENCY_CODE_VND,
            $client->business_name,
            $client->vat,
            $client->address,
            $this->total,
            0,
            $invoiceDetails,
            $this->total_tax,
            $this->subtotal - $this->discount_total,
        );

        if (!$isPerson) {
            $createInvoice->setEmail($invoiceRequest->received_email);
        }

        // If request PO is person, need set some information
        if ($isPerson) {
            $createInvoice->setPerson($invoiceRequest->company_name);
            $createInvoice->setPersonAddress($invoiceRequest->export_address);
        }

        $response = AccountingService::createDraftInvoiceTT32($createInvoice);

        if (empty($response['success'])) {
            throw new \Exception($response['message'] ?? 'Failed to create draft invoice');
        }

        $invoiceRequest->fill([
            'requested_issue_at' => Carbon::now(),
            'request_issue_status' => null,
        ])->save();

        MInvoice::upsert(
            array_filter(
                [
                    'invoice_id' => $this->id,
                    'invoice_request_id' => $this->invoiceRequest->id,
                    'invoice_uuid' => $response['data']['hoadon68_id'],
                    'invoice_series' => $response['data']['khieu'],
                    'invoice_number' => $response['data']['shdon'] ?? null,
                    'invoice_issued_date' => isset($response['data']['nky']) ?  Carbon::parse($response['data']['nky'])->format('Y-m-d') : null,
                    'invoice_status' => $response['data']['is_tthdon'] ?? null,
                ]
            ),
            [
                'invoice_id'
            ],
            [
                'invoice_uuid',
                'invoice_series',
                'invoice_issued_date',
                'invoice_number'
            ]
        );

        return true;
    }

    public function signInvoice()
    {
        if (!$this->minvoice) {
            throw new \Exception('There is no draft invoice. Please create it first!');
        }

        $result = AccountingService::signinvoice($this->minvoice->invoice_uuid);
        if (!$result['success']) {
            throw new \Exception($result['message'] ?? 'Failed to sign draft invoice');
        }

        $invoiceDetail = AccountingService::getInvoice($this->minvoice->invoice_uuid);
        if (!$invoiceDetail['success']) {
            throw new \Exception($result['message'] ?? 'Failed to fetch signed invoice');
        }
        $invoiceDetail = $invoiceDetail['data'];

        $this->minvoice->fill([
            'invoice_issued_date' => Carbon::parse($invoiceDetail['nky'])->format('Y-m-d H:i:s'),
            'invoice_sign_status' => $invoiceDetail['tthai'],
            'invoice_number' => $invoiceDetail['shdon'],
        ])->save();

        $this->invoice_closing_date = Carbon::parse($invoiceDetail['nky'])->format('Y-m-d H:i:s');
        $this->save();

        $customField = CustomField::where('slug', CustomField::SLUG_INVOICE_ID_HOA_DON_VAT)->firstOrFail();
        CustomFieldValue::updateOrCreate(
            [
                'relid' => $this->id,
                'fieldid' => $customField->id,
                'fieldto' => CustomField::REL_TYPE_INVOICE,
            ],
            ['value' => '#' . $invoiceDetail['shdon']]
        );
    }

    public function getPayEstimateDate()
    {
        return CustomFieldValue::query()
            ->whereHas('customField', fn($query) => $query->where('slug', CustomField::SLUG_INVOICE_NGAY_THU_TIEN_DU_KIEN))
            ->where('fieldto', CustomField::REL_TYPE_INVOICE)
            ->where('relid', $this->id)
            ->value('value');
    }

    public function updateEcRecv($ecRecv, $payer, $payableStart, $payableEnd)
    {
        $eRecvCode = new UpdateECollectionRecvDTO(
            $ecRecv->receivable_id,
            $payer->e_collection_code,
            floatval($this->total),
            $ecRecv->payable_start_at->format('Ymd'),
            $payableEnd->format('Ymd'),
            $ecRecv->payable_start_at->format('His'),
            $payableEnd->format('His'),
            ClientPayerEcRecv::NO_RESTRICTION
        );

        // Call API to create E Code Recv cod
        $response = AccountingService::updateECollectionRecvCode([$eRecvCode]);

        if (!$response['success']) {
            throw new \Exception($response['message'] ?? 'Failed to update e collection receivable');
        }

        $newERecvCode = collect($response['data']['succList'][0] ?? []);

        if ($newERecvCode->isEmpty()) {
            throw new \Exception('Failed to update e collection receivable');
        }

        // Store to db
        $ecRecv->fill([
            'client_payer_id' => $payer->id,
            'invoice_id' => $this->id,
            'receivable_id' => $newERecvCode['receivableId'],
            'deposit_amount' => $this->total,
            'callback_trans' => [],
            'payable_start_at' => $payableStart->format('Y-m-d H:i:s'),
            'payable_end_at' => $payableEnd->format('Y-m-d H:i:s'),
            'received_at' => null,
        ])->save();

        // Call API to create QR
        $qr = new QrCodeDTO(
            $newERecvCode['receivableId'],
            $newERecvCode['ecollectionCd'],
        );

        $qrRes = AccountingService::createQr($qr);

        if (!$qrRes['success']) {
            throw new \Exception($qrRes['message'] ?? 'Failed to create e collection receivable qr code');
        }

        $newQr = collect($qrRes['data'] ?? []);

        if ($newQr->isEmpty()) {
            throw new \Exception('Failed to create e collection receivable qr code');
        }

        // Store to db
        $ecRecv->fill([
            'qr_url' => $newQr['qrUrl'],
            'qr_data' => $newQr['qrData'],
        ])->save();

        return $ecRecv;
    }

    public function createEcRecv($payer, $payableStart, $payableEnd)
    {
        $eRecvCode = new CreateECollectionRecvDTO(
            $payer->mother_account_no,
            $payer->payer_no,
            $payer->e_collection_code,
            format_invoice_number($this),
            floatval($this->total),
            $payableStart->format('Ymd'),
            $payableEnd->format('Ymd'),
            $payableStart->format('His'),
            $payableEnd->format('His'),
            ClientPayerEcRecv::NO_RESTRICTION
        );

        // Call API to create E Code Recv cod
        $response = AccountingService::createECollectionRecvCode([$eRecvCode]);

        if (!$response['success']) {
            throw new \Exception($response['message'] ?? 'Failed to create e collection receivable');
        }

        $newERecvCode = collect($response['data']['succList'][0] ?? []);

        if ($newERecvCode->isEmpty()) {
            throw new \Exception('Failed to create e collection receivable');
        }

        // Store to db
        $ecRecv = ClientPayerEcRecv::create([
            'client_payer_id' => $payer->id,
            'invoice_id' => $this->id,
            'receivable_id' => $newERecvCode['receivableId'],
            'deposit_amount' => $this->total,
            'qr_url' => '',
            'qr_data' => '',
            'callback_trans' => json_encode([]),
            'payable_end_at' => $payableEnd->format('Y-m-d H:i:s')
        ]);

        // Call API to create QR
        $qr = new QrCodeDTO(
            $newERecvCode['receivableId'],
            $newERecvCode['ecollectionCd'],
        );

        $qrRes = AccountingService::createQr($qr);

        if (!$qrRes['success']) {
            throw new \Exception($qrRes['message'] ?? 'Failed to create e collection receivable qr code');
        }

        $newQr = collect($qrRes['data'] ?? []);

        if ($newQr->isEmpty()) {
            throw new \Exception('Failed to create e collection receivable qr code');
        }

        // Store to db
        $ecRecv->fill([
            'qr_url' => $newQr['qrUrl'],
            'qr_data' => $newQr['qrData']
        ])->save();

        return [$ecRecv, $newQr, $newERecvCode];
    }

    /**
     * Create a e collection receivable of the invoice
     *
     * @return array
     * @throws Exception
     */
    public function handleCreateEcRecv()
    {
        $payer = $this->client->payer;

        if (!$payer) {
            throw new \Exception('This client does not have payer yet');
        }

        $paidEstimateDate = $this->getPayEstimateDate();

        if (!$paidEstimateDate) {
            throw new \Exception('This invoice does not have Ngày thu tiền dự kiến');
        }

        $payableStart = Carbon::now();
        $payableEnd = Carbon::createFromFormat('Y-m-d', $paidEstimateDate)->endOfDay();

        // If paid estimate date is less than today
        if ($payableEnd->isBefore(Carbon::now())) {
            throw new \Exception('Please set the Ngày thu tiền dự kiến to the future day, current value is: ' . $paidEstimateDate);
        }

        $ecRecv = $payer->ecRecvs()->isNotExpired()->isNotPaid()->first();

        // If have ECRecv is not expired and not paid yet
        if ($ecRecv) {
            // This invoice is not the same with recv invoice id then request to update ECRecv
            if (
                $ecRecv->invoice_id !== $this->id
                || empty($ecRecv->qr_url)
                || !filter_var($ecRecv->qr_url, FILTER_VALIDATE_URL)
                || $payableEnd->isBefore($ecRecv->payable_end_at)
                || $ecRecv->deposit_amount != $this->total
            ) {
                // throw new \Exception('The ECRecv is not belongs to this invoice please merge invoice to use');
                $ecRecv = $this->updateEcRecv($ecRecv, $payer, $payableStart, $payableEnd);
            }

            return [
                'id' => $ecRecv->id,
                'qr' => [
                    'qrUrl' => $ecRecv->qr_url,
                    'qrData' => $ecRecv->qr_data,
                ],
                'ecollectionCd' => $payer->e_collection_code,
            ];
        }

        [$ecRecv, $newQr, $newERecvCode] = $this->createEcRecv($payer, $payableStart, $payableEnd);

        return [
            'id' => $ecRecv->id,
            'qr' => $newQr,
            'ecollectionCd' => $newERecvCode['ecollectionCd']
        ];
    }

    public function scopeIsNewDiscountProgram(Builder $query)
    {
        $query->where('is_churn_new_discount', true);
    }

    public function scopeHasNewDiscountProgram(Builder $query, int $invoiceId = null)
    {
        $query->isNewDiscountProgram()
            ->where('status', '!=', self::STATUS_CANCELLED)
            ->when(
                $invoiceId,
                fn(Builder $query)
                    => $query->where('id', '!=', $invoiceId)
            );
    }

    public function getFixedDiscountUnitAttribute()
    {
        return $this->invoice_items_sum_qty > 0 ? $this->discount_fixed / $this->invoice_items_sum_qty : 0;
    }

    public function getDiscountPercentFloatAttribute()
    {
        return $this->discount_percent / 100;
    }

    public function scopeIssuedBetween($query, $ranges)
    {
        $query->whereHas('minvoice', fn($query) => $query->issuedBetween($ranges));
    }

    public function getTotalWithoutTaxAttribute()
    {
        return $this->total - $this->total_tax;
    }

    public function scopeHasADApproved($query)
    {
        $query->whereNotNull('addition_discount_approved_at');
    }

    public function scopeHaveNotADApproved($query)
    {
        $query->whereNull('addition_discount_approved_at');
    }

    public function scopeHasAD($query)
    {
        $query->where('discount_percent', '>', 0);
    }

    public static function allowEditAD(int $invoiceId)
    {
        if (Invoice::query()
            ->where('id', $invoiceId)
            ->where(function ($query) {
                // ad = 0 for b2b, ad < 10 for lead
                $query->where(function ($qr) {
                    $qr->hasAD()->hasADApproved();
                })
                // Already paid
                ->orWhere(function ($qr) {
                    $qr->whereHas('minvoice', fn($qrr) => $qrr->issued());
                })
                // Already issued
                ->orWhere(function ($qr) {
                    $qr->paid();
                })
                ;
            })
            ->exists()) {
                return false;
        }

        return true;
    }

    /**
     * Check the PO able to do actions: send email, request issue by the following conditions
     *      0. Manager have all permission
     *      1. Has addition discount
     *          - If logged in is B2B sale:
     *              ad > 0 and approve is not null
     *          - If logged in is Sale Leader
     *              ad >= 10 and approve is not null OR
     *              ad < 10
     *      2. Does not have ad (ad = 0)
     *      3. Already paid
     *      4. Already issued
     * @param int $invoiceId
     * @return bool
     */
    public static function allowADPOAction(int $invoiceId)
    {
        // Manager always have permission to do any thing
        if (has_permission('invoices', '', 'po_approval_as_manager')) {
            return true;
        }

        if (Invoice::query()
            ->where('id', $invoiceId)
            ->where(function ($query) {
                // ad = 0 for b2b, ad < 10 for lead
                $query->when(
                    Staff::query()
                        ->where('staffid', get_staff_user_id())
                        ->isSaleader()
                        ->exists(),
                    fn($qr) => $qr->where('discount_percent', '<=', 10),
                    fn($qr) => $qr->where('discount_percent', 0)
                )
                // Already approved
                ->orWhere(function ($qr) {
                    $qr->when(
                        Staff::query()
                            ->where('staffid', get_staff_user_id())
                            ->isSaleader()
                            ->exists(),
                        fn($qr) => $qr->where('discount_percent', '>', 10),
                        fn($qr) => $qr->where('discount_percent', '>', 0)
                    )
                    ->hasADApproved();
                })
                // Already paid
                ->orWhere(function ($qr) {
                    $qr->whereHas('minvoice', fn($qrr) => $qrr->issued());
                })
                // Already issued
                ->orWhere(function ($qr) {
                    $qr->paid();
                });
            })
            ->exists()) {
            return true;
        }

        return false;
    }

    public function getTotalAmountNoTaxAttribute()
    {
        return $this->subtotal - $this->discount_total;
    }
}

<?php

namespace Entities;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

defined('BASEPATH') or exit('No direct script access allowed');

class InvoiceDailyRevenue extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'invoice_daily_revenues';
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    protected $fillable = [
        'invoice_id',
        'itemable_id',
        'ams_job_id',
        'revenue_date',
        'amount',
        'note',
        'package_type'
    ];

    public const PACKAGE_TYPE_JOB_POSTING = 'job_posting';
    public const PACKAGE_TYPE_COMBO = 'combo';
    public const PACKAGE_TYPE_SERVICE = 'service';

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    public function itemable(): BelongsTo
    {
        return $this->belongsTo(Itemable::class, 'itemable_id');
    }

    public function isCombo()
    {
        return $this->package_type == self::PACKAGE_TYPE_COMBO;
    }

    public function isService()
    {
        return $this->package_type == self::PACKAGE_TYPE_SERVICE;
    }

    public function isJobPosting()
    {
        return $this->package_type == self::PACKAGE_TYPE_JOB_POSTING;
    }
}
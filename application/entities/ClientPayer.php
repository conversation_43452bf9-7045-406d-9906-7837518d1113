<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class ClientPayer extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'client_payers';
    public $timestamps = true;

    protected $fillable = [
        'client_id',
        'payer_no',
        'payer_name',
        'mother_account_no',
        'e_collection_code',
    ];

    public function ecRecvs()
    {
        return $this->hasMany(ClientPayerEcRecv::class, 'client_payer_id');
    }

    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id', 'userid');
    }

    public static function getPayerNoFromClientId(int $clientId)
    {
        return 'CP' . str_pad($clientId, 10, 0, STR_PAD_LEFT);
    }

    public static function getPayerPhoneClientId(int $clientId)
    {
        return '0888' . str_pad($clientId, 6, 0, STR_PAD_LEFT);
    }

    public static function getClientIdFromPayerNo(string $payerNo)
    {
        if (preg_match('/^CP0+(.*)$/', $payerNo, $matches)) {
            return $matches[1];
        }

        return null;
    }
}

<?php

namespace Entities;

use Illuminate\Database\Eloquent\Builder;

defined('BASEPATH') or exit('No direct script access allowed');

class CustomField extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'customfields';

    public const SLUG_INVOICE_NGAY_THU_TIEN_DU_KIEN = 'invoice_ngay_thu_tien_du_kien';
    public const SLUG_INVOICE_ID_HOA_DON_VAT = 'invoice_id_hoa_don_vat';

    public const REL_TYPE_INVOICE = 'invoice';

    public function scopeShowOnTable(Builder $query)
    {
        return $query->where('show_on_table', 1);
    }
}

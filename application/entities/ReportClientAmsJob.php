<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class ReportClientAmsJob extends BaseEntity
{
    protected $table = 'report_client_ams_job';
    protected $fillable = [
        'client_id',
        'ams_company_id',
        'ams_job_id',
        'invoice_id',
        'used_packages',
        'paid_package_id',
        'package_id',
        'free_package',
        'status_mapping',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'used_packages' => '{}',
    ];

    protected $casts = [
        'used_packages' => 'json',
    ];
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    public function itemable()
    {
        return $this->belongsTo(Itemable::class, 'package_id');
    }

    public function usedPackage()
    {
        return $this->hasOne(ClientAmsOpenJob::class, 'itemable_id', 'package_id');
    }
}

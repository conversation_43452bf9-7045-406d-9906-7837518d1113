<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class InvoiceRequest extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'invoice_request';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    public const REQUEST_ISSUE_STATUS_APPROVED = 'approved';
    public const REQUEST_ISSUE_STATUS_REJECTED = 'rejected';

    public const INVOICE_REQUEST_STATUS_ISSUED = 3;

    protected $fillable = [
        'invoiceid',
        'received_email',
        'request_status',
        'contract_payment_date',
        'service_provided',
        'company_name',
        'export_address',
        'vat',
        'contract_status',
        'content',
        'note',
        'invoice_draft',
        'status',
        'staff_id',
        'account_id',
        'request_issue_status',
        'requested_issue_at',
        'is_approved',
        'approved_issue_at',
        'rejected_issue_at',
        'is_rejected',
        'reject_reason',
        'invoice_draft',
        'walk_in_customer',
    ];

    protected $casts = [
        'requested_issue_at' => 'datetime',
        'approved_issue_at' => 'datetime',
        'rejected_issue_at' => 'datetime',
        'reject_reason' => 'array'
    ];

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoiceid');
    }

    public function staff()
    {
        return $this->belongsTo(Staff::class, 'staff_id');
    }
}

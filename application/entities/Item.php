<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class Item extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'items';

    public const SME_GROUP_ID = 1;
    public const SMES_PACKAGE = '30 Ngày';
    public const NEW_2024_PACKAGE = 'New 2024';
    public const FREE_PACKAGE = 'Free';
    public const ENTERPRISE_GROUP_ID = 2;
    public const NEW_2024_GROUP_ID = 5;
    public const NEW_2025_GROUP_ID = 8;
    public const CREDIT_GROUP_ID = 7;
    public const EMPLOYER_BRANDING_GROUP_ID = 6;
    public const ITEM_CODE = [
        Item::NEW_2024_GROUP_ID => 'TD1',
        Item::ENTERPRISE_GROUP_ID => 'TD1',
        Item::CREDIT_GROUP_ID => 'TD2',
        Item::EMPLOYER_BRANDING_GROUP_ID => 'TD3',
    ];
    public const UNIT_CODE = [
        Item::NEW_2024_GROUP_ID => MInvoice::UNIT_CODE_PACKAGE_NAME,
        Item::ENTERPRISE_GROUP_ID => MInvoice::UNIT_CODE_PACKAGE_NAME,
        Item::CREDIT_GROUP_ID => MInvoice::UNIT_CODE_SEARCH_CV_NAME,
        Item::EMPLOYER_BRANDING_GROUP_ID => MInvoice::UNIT_CODE_SEARCH_CV_NAME,
    ];

    public function scopePaidItems($query)
    {
        return $query->whereIn('group_id', [self::SME_GROUP_ID, self::ENTERPRISE_GROUP_ID, self::NEW_2024_GROUP_ID, self::NEW_2025_GROUP_ID]);
    }

    public function scopeIsCredit($query)
    {
        return $query->whereIn('group_id', [self::CREDIT_GROUP_ID]);
    }

    public function scopeIsService($query)
    {
        return $query->whereIn('group_id', [self::EMPLOYER_BRANDING_GROUP_ID]);
    }

    public function scopeIsJobPosting($query)
    {
        return $query->whereIn('group_id', [self::SME_GROUP_ID, self::ENTERPRISE_GROUP_ID, self::NEW_2024_GROUP_ID]);
    }

    public function scopeIsNotCredit($query)
    {
        return $query->where('group_id', '!=', self::CREDIT_GROUP_ID);
    }

    /**
     * Model relationship
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function amsPackage()
    {
        return $this->hasOne(AmsPackage::class, 'crm_item_id');
    }

    public function isSmePackage()
    {
        return $this->group_id == Item::SME_GROUP_ID;
    }

    public function isNew2024Package()
    {
        return $this->group_id == Item::NEW_2024_GROUP_ID;
    }

    public function taxable()
    {
        return $this->belongsTo(Tax::class,'tax');
    }

    /**
     * Model relationship
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function amsSearchPackage()
    {
        return $this->hasOne(AmsSearchPackage::class, 'crm_item_id');
    }

    public function getPackageTypeByGroup()
    {
        if (in_array($this->group_id, [self::SME_GROUP_ID, self::ENTERPRISE_GROUP_ID, self::NEW_2024_GROUP_ID])) {
            return 'Job Posting';
        }

        if ($this->group_id == self::CREDIT_GROUP_ID) {
            return 'Search CV';
        }

        return 'Topdev Rewards';
    }

    public static function getItemCodeByGroup(int $groupId)
    {
        return self::ITEM_CODE[$groupId] ?? '';
    }

    public static function getUnitCodeByGroup(int $groupId)
    {
        return self::UNIT_CODE[$groupId] ?? '';
    }

    public static function getPackageTypeByGroupId(int $groupId)
    {
        if (in_array($groupId, [self::CREDIT_GROUP_ID])) {
            return 'combo';
        }

        if (in_array($groupId, [self::EMPLOYER_BRANDING_GROUP_ID])) {
            return 'service';
        }

        if (in_array($groupId, [self::SME_GROUP_ID, self::ENTERPRISE_GROUP_ID, self::NEW_2024_GROUP_ID])) {
            return 'job_posting';
        }

        return null;
    }
}

<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class SaleActivity extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'sales_activity';

    public const REL_TYPE_INVOICE = 'invoice';

    protected $fillable = [
        'rel_type',
        'rel_id',
        'description',
        'additional_data',
        'staffid',
        'full_name',
        'date',
        'customer_admin_id',
    ];

    public function scopeInvoice($query)
    {
        $query->where('rel_type', self::REL_TYPE_INVOICE);
    }
}

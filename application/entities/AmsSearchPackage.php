<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class AmsSearchPackage extends BaseEntity
{
    /**
     * Model relationship
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function item()
    {
        return $this->belongsTo(Item::class, 'crm_item_id');
    }

    public function scopeIsOnlinePayment($query)
    {
        $query->where('is_online_payment', 1);
    }

}

<?php

namespace Entities;

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

class ClientPayerEcRecv extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'client_payer_ec_recvs';

    public const NO_RESTRICTION = '0'; // : No restrictions,
    public const SPECIFIED_AMOUNT = '1'; // : Accept only specified amount,
    public const GREATER_THAN_AMOUNT = '2'; // : 지정 금액보다 클 때 (lớn hơn số tiền phải thu),
    public const LESS_THAN_AMOUNT = '3'; // : 지정 금액보다 작을때 (nhỏ hơn số tiền phải thu)

    public const NOT_USE = '0';
    public const PAYABLE_IN_RANGE = '1'; // Nhận tiền theo ngày nhận tiền và thời gian nhận tiền
    public const PAYABLE_IN_RANGE_TIME = '2'; // Nhận tiền riêng biệt giữa thời gian/gi<PERSON> nhận tiền

    protected $fillable = [
        'client_payer_id',
        'invoice_id',
        'receivable_id',
        'deposit_amount',
        'qr_url',
        'qr_data',
        'callback_trans',
        'received_at',
        'payable_start_at',
        'payable_end_at',
    ];

    protected $casts = [
        'callback_trans' => 'array',
        'received_at' => 'datetime',
        'payable_start_at' => 'datetime',
        'payable_end_at' => 'datetime',
    ];

    public function clientPayer()
    {
        return $this->belongsTo(ClientPayer::class, 'client_payer_id');
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    public function trans()
    {
        return $this->hasMany(ClientPayerEcRecvTrans::class, 'client_payer_ec_recv_id');
    }

    public function scopeIsExpired($query)
    {
        $query->where('payable_end_at', '<', Carbon::now()->format('Y-m-d H:i:s'));
    }

    public function scopeIsNotExpired($query)
    {
        $query->where('payable_end_at', '>', Carbon::now()->format('Y-m-d H:i:s'));
    }

    public function scopeIsPaid($query)
    {
        $query->whereNotNull('received_at');
    }

    public function scopeIsNotPaid($query)
    {
        $query->whereNull('received_at');
    }

    public function getPaidAttribute()
    {
        return $this->deposit_amount <= $this->trans->sum('deposit_amount');
    }
}

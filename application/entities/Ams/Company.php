<?php

namespace Entities\Ams;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;

defined('BASEPATH') or exit('No direct script access allowed');

class Company extends AmsBaseEntity
{
    use SoftDeletes;

    /**
     * {@inheritdoc}
     */
    protected $table = 'companies';

    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 2;
    public const STATUS_REVIEW = 3;

    /**
     * @return Builder
     */
    public function scopeActive(Builder $builder)
    {
        return $builder->where('status', self::STATUS_ACTIVE);
    }
}

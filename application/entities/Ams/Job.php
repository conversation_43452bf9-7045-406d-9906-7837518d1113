<?php

namespace Entities\Ams;

use Illuminate\Database\Eloquent\SoftDeletes;

defined('BASEPATH') or exit('No direct script access allowed');

class Job extends AmsBaseEntity
{
    use SoftDeletes;

    /**
     * {@inheritdoc}
     */
    protected $table = 'jobs';

    public const STATUS_DRAFT = 0;
    public const STATUS_CLOSED = 1;
    public const STATUS_REVIEW = 2;
    public const STATUS_OPEN = 3;
}

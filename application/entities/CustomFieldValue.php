<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class CustomFieldValue extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'customfieldsvalues';

    protected $fillable = [
        'relid',
        'fieldid',
        'fieldto',
        'value'
    ];

    public function customField()
    {
        return $this->belongsTo(CustomField::class, 'fieldid');
    }
}

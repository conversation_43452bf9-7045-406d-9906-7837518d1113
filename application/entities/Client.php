<?php

namespace Entities;

use app\services\AccountingService;
use Dto\Infoplus\CreateECollectionDTO;
use Entities\Ams\Company;
use DB;
use Illuminate\Database\Eloquent\Builder;

defined('BASEPATH') or exit('No direct script access allowed');

class Client extends BaseEntity
{
    protected $primaryKey = 'userid';
    protected $fillable = [
        'type_of_customer',
        'usage_behavior',
        'classify_2025',
    ];

    public const USAGE_BEHAVIOR_FOCUS = 'FOCUS';
    public const USAGE_BEHAVIOR_OTHER = 'OTHER';

    public const CUSTOMER_TYPE_NEW = 'NEW';
    public const CUSTOMER_TYPE_EXISTING = 'EXISTING';
    public const CUSTOMER_TYPE_CHURN = 'CHURN';

    public function contacts()
    {
        return $this->hasMany(Contact::class, 'userid', 'userid');
    }

    public function notes()
    {
        return $this->hasMany(Note::class, 'rel_id', 'userid')->where('rel_type', Note::REL_TYPE_CUSTOMER);
    }

    public function latest_note()
    {
        return $this->hasOne(Note::class, 'rel_id', 'userid')->ofMany([
            'id' => 'max'
        ])->where('rel_type', Note::REL_TYPE_CUSTOMER);
    }

    public function contact_requests()
    {
        return $this->hasManyThrough(
            ContactRequest::class,
            ClientContactPool::class,
            'client_id',
            'client_contact_pool_id',
            'userid',
            'id'
        );
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'clientid', 'userid');
    }

    public function estimates()
    {
        return $this->hasMany(Estimate::class, 'clientid', 'userid');
    }

    public function clientAmsCompanies()
    {
        return $this->hasMany(ClientAmsCompany::class, 'client_id', 'userid');
    }

    public function amsCompanies()
    {
        return $this->hasMany(Company::class, 'tax_number', 'vat');
    }

    public function scopeActive($query)
    {
        $query->where('active', 1);
    }

    public function scopeHasPaidOrder($query)
    {
        $query->whereHas('invoices', function ($builder) {
            $builder->paid();
        });
    }

    public function scopeFocus($query)
    {
        $query->where('usage_behavior', self::USAGE_BEHAVIOR_FOCUS);
    }

    public function isFocus()
    {
        return $this->usage_behavior == self::USAGE_BEHAVIOR_FOCUS;
    }

    public function isSaAssigned()
    {
        return $this->is_sa_assigned == 1;
    }

    public function customerAdmin()
    {
        return $this->hasOne(CustomerAdmin::class, 'customer_id', 'userid');
    }

    public function clientAmsCompany()
    {
        return $this->hasMany(ClientAmsCompany::class, 'client_id');
    }

    public function payer()
    {
        return $this->hasOne(ClientPayer::class, 'client_id', 'userid');
    }

    /**
     * Summary of updateTypeOfCustomer
     *
     * @return void
     */
    public function updateTypeOfCustomer()
    {
        // Default is fresh
        $finalCustomerType = self::CUSTOMER_TYPE_EXISTING;

        $preTypeOfCustomer = $this->type_of_customer;
        if ($preTypeOfCustomer != $finalCustomerType) {
            $this->update(['type_of_customer' => $finalCustomerType]);
            log_activity(
                'Updated Loại khách hàng TopDev from [' . $preTypeOfCustomer . '] to [' . $finalCustomerType . ']',
                null,
                $this->userid,
                [],
                null,
                'updateTypeOfCustomer'
            );
        }
    }

    public function updateUsageBehavior()
    {
        $paidInvoiceCount = $this->invoices()->where('total', '>', 0)->paid()->count();
        $prevUsageBehavior = $this->usage_behavior;
        $newUsageBehavior =  $this->num_of_usage_behavior > 0 || $paidInvoiceCount > 0 ? Client::USAGE_BEHAVIOR_FOCUS : Client::USAGE_BEHAVIOR_OTHER;
        if ($prevUsageBehavior != $newUsageBehavior) {
            $this->update(['usage_behavior' => $newUsageBehavior]);
            log_activity(
                'Updated Loại khách hàng Thị Trường from [' . $prevUsageBehavior . '] to [' . $newUsageBehavior . ']',
                null,
                $this->userid,
                [
                    'num_of_usage_behavior' => $this->num_of_usage_behavior,
                    'paid_invoice_count' => $this->paidInvoiceCount,
                ],
                $this->userid,
                'update_usage_behavior'
            );
        }
    }

    /**
     * Get billing of this client then update classification of 2025
     *
     * @return void
     */
    public function updateCustomerClassify2025()
    {
        if ($this->type_of_customer != self::CUSTOMER_TYPE_EXISTING) {
            return;
        }

        $total = $this->invoices()
            ->select(DB::raw('SUM(total-total_tax) as total'))
            ->where('status', '!=', Invoice::STATUS_CANCELLED)
            ->where('invoice_closing_date', '>=', '2025-01-01 00:00:00')
            ->where('invoice_closing_date', '<', '2025-12-31 23:59:59')
            ->value('total');

        $finalClassify2025 = null;
        $million = 1000000;

        if (0 < $total && $total < (15 * $million)) {
            $finalClassify2025 = 'POTENTIAL';
        } elseif ((15 * $million) <= $total && $total < (31 * $million)) {
            $finalClassify2025 = 'HIGH_POTENTIAL';
        } elseif ((31 * $million) <= $total && $total < (51 * $million)) {
            $finalClassify2025 = 'MEMBER';
        } elseif ((51 * $million) <= $total && $total < (101 * $million)) {
            $finalClassify2025 = 'BRONZE';
        } elseif ((101 * $million) <= $total && $total < (201 * $million)) {
            $finalClassify2025 = 'SILVER';
        } elseif ((201 * $million) <= $total && $total < (301 * $million)) {
            $finalClassify2025 = 'GOLD';
        } elseif ((301 * $million) <= $total) {
            $finalClassify2025 = 'DIAMOND';
        }

        $preClassify2025 = $this->classify_2025;
        if ($preClassify2025 != $finalClassify2025) {
            $this->update(['classify_2025' => $finalClassify2025]);
            log_activity(
                'Updated Classify 2025 from [' . $preClassify2025 . '] to [' . $finalClassify2025 . ']',
                null,
                $this->userid,
                [],
                null,
                'updateClassify2025'
            );
        }
    }

    /**
    * Use to create payer's ecolelction if want to have a new e collection
    *
    * @return array|bool
    * @throws Exception
    */
    public function createPayerECollection()
    {
        $payer = $this->payer;

        if (!$payer) {
            throw new \Exception('There is no Payer for this client');
        }

        $eCode = new CreateECollectionDTO(
            $payer->payer_no,
            PAYER_MOTHER_ACCOUNT_NUMBER,
        );

        // Call api to create e-collection code
        $eResult = AccountingService::createECollectionCode([$eCode]);
        if (!$eResult['success']) {
            throw new \Exception($eResult['message'] ?? 'Failed to create e collection');
        }

        $newEcodes = collect($eResult['data']['succList'] ?? []);
        $failedEcodes = collect($eResult['data']['errorList'] ?? []);

        if (!$newEcodes->count()) {
            throw new \Exception('Failed to create e collection');
        }

        // Store to the client_payers
        $clientPayers = $newEcodes->map(
            fn($eCode)  => [
                'client_id' => ClientPayer::getClientIdFromPayerNo($eCode['payerNo']),
                'payer_no' => $eCode['payerNo'],
                'payer_name' => $payer->payer_name,
                'mother_account_no' => $eCode['motherAccntNo'],
                'e_collection_code' => $eCode['ecollectionCd'],
            ]
        );

        ClientPayer::upsert($clientPayers, 'client_id', ['e_collection_code', 'payer_name', 'mother_account_no']);

        return $failedEcodes->count() ? [
            'failedEcodes' => $failedEcodes,
        ] : true;
    }

    public function scopeIsChurnNewType($query)
    {
        $query->whereIn('type_of_customer', [self::CUSTOMER_TYPE_CHURN, self::CUSTOMER_TYPE_NEW]);
    }

    public function scopeDoesntHaveEstimateNewDiscountProgram(Builder $query, $estimateId = 0)
    {
        $query->doesntHave('estimates', 'and', function ($query) use ($estimateId) {
            $query
                ->whereNotIn('status', [Estimate::STATUS_DECLINED, Estimate::STATUS_EXPIRED])
                ->hasNewDiscountProgram($estimateId);
        });
    }

    public function scopeDoesntHaveInvoiceNewDiscountProgram(Builder $query, $invoiceId = 0)
    {
        $query->doesntHave('invoices', 'and', fn($query) => $query->hasNewDiscountProgram($invoiceId));
    }
}

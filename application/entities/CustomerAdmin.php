<?php

namespace Entities;

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

class CustomerAdmin extends BaseEntity
{
    // This is not a primary key, should check when implementing
    protected $primaryKey = 'customer_id';
    protected $table = 'customer_admins';
    public const STATUS_WAITING = 0;
    public const STATUS_CONTACTED = 1;

    protected $casts = [
        'date_assigned' => 'datetime'
    ];

    protected $fillable = [
        'staff_id',
        'customer_id',
        'date_assigned',
        'contacted',
        'expired_at'
    ];

    public function staff()
    {
        return $this->belongsTo(Staff::class, 'staff_id', 'staffid');
    }

    public function client()
    {
        return $this->belongsTo(Client::class, 'customer_id', 'userid');
    }
}

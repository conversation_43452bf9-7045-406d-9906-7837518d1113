<?php

namespace Entities;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

defined('BASEPATH') or exit('No direct script access allowed');

class M3cCall extends BaseEntity
{
    public const MEET_AGENT = 'meetAgent';
    public const CALL_OUT = 'outbound';
    public const CALL_IN = 'inbound';
    public const CALL_IN_NORMAL_CALL = 'NORMAL_CLEARING';
    public const CALL_LOG_VENDOR_CALLIO = 'callio';

    protected $fillable = [
        'customer_id',
        'call_id',
        'call_code',
        'path',
        'path_download',
        'caller',
        'called',
        'user_id',
        'agent_id',
        'group_id',
        'call_type',
        'start_time',
        'end_time',
        'call_status',
        'wait_time',
        'hold_time',
        'talk_time',
        'end_status',
        'ticket_id',
        'last_user_id',
        'last_agent_id',
        'call_survey',
        'call_survey_result',
        'created_at',
        'vendor',
        'transcripts',
        'transcript_synced',
    ];

    public const CALL_IN_TYPE = 0;
    public const CALL_OUT_TYPE = 1;

    protected $primaryKey = 'id';
    protected $table = '3c_call';

    protected $casts = [
        'transcripts' => 'array'
    ];

    public function staff()
    {
        return $this->belongsTo(Staff::class, 'staff_id', 'staffid');
    }

    public function contact()
    {
        return $this->belongsTo(Contact::class, 'caller', 'phonenumber');
    }

    public function client()
    {
        return $this->belongsTo(Client::class, 'caller', 'phonenumber');
    }

    public function getCallTypeTextAttribute()
    {
        return $this->call_type == M3cCall::CALL_OUT_TYPE ? _l('call_log_call_out_type') : _l('call_log_call_in_type');
    }

    public function scopeDoesntHaveAudio(Builder $builder)
    {
        $builder->whereNull('path')->orWhere('path', '');
    }

    public function scopeMeetAgent(Builder $builder)
    {
        $builder->where('call_status', self::MEET_AGENT);
    }

    public function scopeNotSyncTranscriptYet($query)
    {
        $query->where('transcript_synced', 0)->where('talk_time', '!=', '00:00:00');
    }
}

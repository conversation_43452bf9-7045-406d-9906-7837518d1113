<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class MailQueueIssueInvoice extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'mail_queue_issues_invoices';

    public const STATUS_IS_READY = 'ready';
    public const STATUS_IS_SENT = 'sent';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    protected $fillable = [
        'minvoice_id',
        'status',
        'sent_at',
    ];

    public function scopeIsReady($query)
    {
        $query->where('status', self::STATUS_IS_READY);
    }

    public function minvoice()
    {
        return $this->belongsTo(MInvoice::class, 'minvoice_id');
    }
}

<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class CrawlerJob extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'crawler_jobs';

    protected $fillable = [
        'job_id',
        'title',
        'company',
        'type',
        'from_page',
        'posted_at',
        'working_model',
        'location',
        'skills',
        'salary',
        'description',
        'url',
    ];
}

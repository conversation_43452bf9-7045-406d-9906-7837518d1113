<?php

namespace Entities;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

defined('BASEPATH') or exit('No direct script access allowed');

class Estimate extends BaseEntity
{
    public const FIELD_TO = 'estimate';
    public const STATUS_DRAFT = 1;
    public const STATUS_SENT = 2;
    public const STATUS_DECLINED = 3;
    public const STATUS_ACCEPTED = 4;
    public const STATUS_EXPIRED = 5;


    public const REPRESENT_RECRUITMENT_CONSULTANT_LEADER_VALUE = 20000000;
    public const REPRESENT_RECRUITMENT_CONSULTANT_MANAGER_VALUE = 100000000;
    public const REPRESENT_COO_VALUE = 200000000;

    private const GENERAL_FIELDS = [
        'sent',
        'datesend',
        'clientid',
        'deleted_customer_name',
        'project_id',
        'number',
        'prefix',
        'number_format',
        'date',
        'expirydate',
        'currency',
        'status',
        'adminnote',
        'reference_no',
        'sale_agent',
        'billing_street',
        'billing_city',
        'billing_state',
        'billing_zip',
        'billing_country',
        'shipping_street',
        'shipping_city',
        'shipping_state',
        'shipping_zip',
        'shipping_country',
        'include_shipping',
        'show_shipping_on_estimate',
        'show_quantity_as',
        'pipeline_order',
        'is_expiry_notified',
        'acceptance_firstname',
        'acceptance_lastname',
        'acceptance_email',
        'acceptance_date',
        'acceptance_ip',
        'signature',
        'short_link',
        'customer_source',
        'contact_id',
        'reason_for_declined',
        'customer_actions',
        'status_updated_at',
        'is_round_number'
    ];
    const CREATED_AT = 'datecreated';
    const UPDATED_AT = 'updated_at';
    public $timestamps = true;

    protected $primaryKey = 'id';
    protected $table = 'estimates';

    protected $casts = [
        'number' => 'integer',
        'adjustment' => 'float'
    ];

    protected $fillable = [
        'sent',
        'datesend',
        'clientid',
        'deleted_customer_name',
        'project_id',
        'number',
        'prefix',
        'number_format',
        'hash',
        'date',
        'expirydate',
        'currency',
        'subtotal',
        'total_tax',
        'total',
        'adjustment',
        'addedfrom',
        'status',
        'clientnote',
        'adminnote',
        'discount_percent',
        'discount_total',
        'discount_type',
        'invoiceid',
        'invoiced_date',
        'terms',
        'reference_no',
        'sale_agent',
        'billing_street',
        'billing_city',
        'billing_state',
        'billing_zip',
        'billing_country',
        'shipping_street',
        'shipping_city',
        'shipping_state',
        'shipping_zip',
        'shipping_country',
        'include_shipping',
        'show_shipping_on_estimate',
        'show_quantity_as',
        'pipeline_order',
        'is_expiry_notified',
        'acceptance_firstname',
        'acceptance_lastname',
        'acceptance_email',
        'acceptance_date',
        'acceptance_ip',
        'signature',
        'short_link',
        'customer_source',
        'contact_id',
        'reason_for_declined',
        'customer_actions',
        'discount_fixed',
        'parent_id',
        'status_updated_at',
        'is_round_number',
        'is_churn_new_discount',
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function (Estimate $estimate) {
            $id = $estimate->id;
            $staffId = get_staff_user_id();
            $estimate->saleActivity()->create(
                [
                    'description' => 'estimate_activity_created',
                    'date' => Carbon::now(),
                    'rel_type' => self::FIELD_TO,
                    'staffid' => DEFINED('CRON') ? '[CRON]' : $staffId,
                    'full_name' => DEFINED('CRON') ? '[CRON]' : get_staff_full_name($staffId),
                    'additional_data' => '',
                    'customer_admin_id' => $estimate->client->customerAdmin->staff_id
                ]
            );
            $estimate->logs()->create(
                [
                    'description' => 'Estimates Created (#' . $id . ')',
                    'date' => Carbon::now(),
                    'staffid' => DEFINED('CRON') ? '[CRON]' : $staffId,
                    'clientid' => $estimate->clientid,
                    'rel_type' => self::FIELD_TO
                ]
            );
        });

        static::updating(function ($estimate) {
            if ($estimate->isDirty('status')) {
                $estimate->status_updated_at = Carbon::now();
            }
        });

        static::updated(function ($estimate) {
            if ($estimate->isDirty(self::GENERAL_FIELDS)) {
                $dataUpdate = collect($estimate->getDirty())->only(self::GENERAL_FIELDS)->toArray();
                Estimate::where('parent_id', $estimate->id)
                    ->when(!$estimate->isPrimary(), function ($query) use ($estimate) {
                        $query->orWhere('id', $estimate->parent_id)
                            ->orWhere('parent_id', $estimate->parent_id);
                    })
                    ->update($dataUpdate);
            }
        });
    }

    public function client()
    {
        return $this->belongsTo(Client::class, 'clientid', 'userid');
    }

    public function customFieldValues()
    {
        return $this->hasMany(CustomFieldValue::class, 'relid')
            ->where('fieldto', self::FIELD_TO);
    }

    public function customFields()
    {
        return $this->hasMany(CustomField::class)
            ->where('fieldto', self::FIELD_TO);
    }

    public function itemables()
    {
        return $this->hasMany(Itemable::class, 'rel_id', 'id')
            ->where('rel_type', self::FIELD_TO);
    }

    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }

    public function attachments()
    {
        return $this->hasMany(File::class, 'rel_id', 'id')
            ->where('rel_type', self::FIELD_TO);
    }

    public function project()
    {
        return $this->hasOne(Project::class, 'id', 'project_id');
    }

    public function currencyModel()
    {
        return $this->hasOne(Currency::class, 'id', 'currency');
    }

    public function scheduledEmail()
    {
        return $this->hasMany(ScheduledEmail::class, 'rel_id', 'id')
            ->where('rel_type', self::FIELD_TO);
    }

    public function tags()
    {
        return $this->morphToMany(Tag::class, 'rel', Taggable::class)
            ->withPivot([
                'tag_order'
            ]);
    }

    public function saleActivity()
    {
        return $this->hasMany(SaleActivity::class, 'rel_id', 'id')
            ->where('rel_type', self::FIELD_TO);
    }

    public function logs()
    {
        return $this->hasMany(ActivityLog::class, 'rel_id', 'id')
            ->where('rel_type', self::FIELD_TO);
    }

    public function options()
    {
        return $this->hasMany(Estimate::class, 'parent_id');
    }

    public function parent()
    {
        return $this->belongsTo(Estimate::class, 'parent_id');
    }

    public function invoice()
    {
        return $this->hasOne(Invoice::class, 'id', 'invoiceid');
    }

    public function scopeHasInvoice(Builder $query)
    {
        $query->whereNotNull('invoiceid');
    }

    public function scopePrimary(Builder $query)
    {
        $query->whereNull('parent_id');
    }

    public function isPrimary(): bool
    {
        return blank($this->parent_id);
    }

    public function createOption(array $data = [])
    {
        $data = collect($data);

        $this->fill([
            'date' => $data->get('date') ? Carbon::parse($data->get('date'))->format('Y-m-d') : $this->date,
            'expirydate' => $data->get('expirydate') ? Carbon::parse($data->get('expirydate'))->format('Y-m-d') : $this->expirydate,
            'adminnote' => $data->get('adminnote') ?? '',
            'status_updated_at' => Carbon::now()
        ]);

        if ($this->isDirty(['date', 'expirydate', 'adminnote', 'status_updated_at'])) {
            $this->save();
        }

        //copy attributes
        $clone = $this->replicate()->fill([
            'parent_id' => $this->id,
            'subtotal' => $data->get('subtotal') ?? 0,
            'discount_percent' => $data->get('discount_percent') ?? 0,
            'discount_total' => $data->get('discount_total') ?? 0,
            'discount_type' => $data->get('discount_type') ?? '',
            'discount_fixed' => $data->get('discount_fixed') ?? 0,
            'total_tax' => 0,
            'total' => $data->get('total') ?? 0,
            'adjustment' => $data->get('adjustment') ?? 0,
            'hash' => app_generate_hash(),
            'invoiceid' => null,
            'invoiced_date' => null,
            'addedfrom' => get_staff_user_id(),
            'clientnote' => $data->get('clientnote') ?? '',
            'terms' => $data->get('terms') ?? '',
            'is_churn_new_discount' => $data->get('is_churn_new_discount', false),
        ]);

        if ($clone->save()) {
            // Sync data tags
            $tags = $this->tags->keyBy('id')->map(fn ($tag) => [
                'tag_order' => $tag->pivot->tag_order
            ]);
            $clone->tags()->sync($tags);

            // Sync data customFieldValues
            $clone->customFieldValues()->createMany($this->customFieldValues->toArray());

            $items = $data->get('newitems') ?? [];
            foreach ($items as $item) {
                if ($itemid = add_new_sales_item_post($item, $clone->id, self::FIELD_TO)) {
                    _maybe_insert_post_item_tax($itemid, $item, $clone->id, self::FIELD_TO);
                }
            }

            update_sales_total_tax_column($clone->id, self::FIELD_TO, db_prefix() . 'estimates', $clone->is_round_number);

            return $clone->id;
        }

        return false;
    }

    public function setPrimary(): bool
    {
        if ($this->isPrimary()) {
            throw new \Exception(_l('estimate_make_primary_fail_message'));
        }

        $parent = $this->parent;

        $this->parent->options()->update(['parent_id' => $this->id]);
        $parent->update(['parent_id' => $this->id]);
        $this->update(['parent_id' => null]);

        return true;
    }

    public static function isLastOption($id): bool
    {
        return (new static)
            ->whereId($id)
            ->whereDoesntHave('options')
            ->whereDoesntHave('parent')
            ->exists();
    }

    public function smallestTotalOption()
    {
        return $this->options()->orderBy('total')->first();
    }

    public function getPDFData()
    {
        $relations = [
            'currencyModel:id,name',
            'contact:id,fullname,email,phonenumber',
            'options.currencyModel:id,name',
            'options.itemables',
            'itemables'
        ];

        if ($this->isPrimary()) {
            $estimate = $this->load($relations);
        } else {
            $estimate = $this::with($relations)
                ->find($this->parent->id);
        }

        $estimate->highest_total = collect($estimate->options)->max('total') > $estimate->total ? collect($estimate->options)->max('total') : $estimate->total;

        return $estimate;
    }

    public function customerAdmin()
    {
        return $this->hasOne(CustomerAdmin::class, 'customer_id', 'clientid');
    }

    public function scopeIsNewDiscountProgram(Builder $query)
    {
        $query->where('is_churn_new_discount', true);
    }

    public function scopeHasNewDiscountProgram(Builder $query, int $estimateId = 0)
    {
        $query->isNewDiscountProgram()
            ->when(
                $estimateId,
                fn(Builder $query)
                    => $query->where('id', '!=', $estimateId)
                            ->whereNull('parent_id')
                            ->where('id', '!=', self::where('id', $estimateId)->value('parent_id'))
            );
    }
}

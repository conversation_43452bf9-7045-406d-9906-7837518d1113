<?php

namespace Entities;

use app\services\AccountingService;

defined('BASEPATH') or exit('No direct script access allowed');

class MInvoice extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'minvoices';
    public $timestamps = true;

    public const PRODUCT = 1; // Hàng hóa dịch vụ ( giá trị tchat là 1)
    public const PROMOTION = 2; // <PERSON><PERSON><PERSON><PERSON>n mại (giá trị tchat là 2)
    public const DISCOUNT = 3; // Chiết khấu thương mại (giá trị tchat là 3)
    public const OTHER = 4; // <PERSON><PERSON> chú/ diễn gi<PERSON> (giá trị tchat là 4)

    public const TAX_10 = 10; //Thuế suất 10%
    public const TAX_8 = 8; //Thuế suất 8% (theo nghị định 44/2023/ND-CP)
    public const TAX_5 = 5; //Thuế suất 5%
    public const TAX_0 = 0; //Thuế suất 0%
    public const TAX_1 = -1; //<PERSON><PERSON>ông chịu thuế
    public const TAX_2 = -2; //<PERSON>hông kê khai nộp thuế

    public const CURRENCY_CODE_VND = 'VND';

    public const INVOICE_SIGN_STATUS_SENT = 'Đã gửi';
    public const INVOICE_MA_DT = 'VANGLAI';

    public const UNIT_CODE_PACKAGE_NAME = 'Tin';
    public const UNIT_CODE_SEARCH_CV_NAME = 'Gói';

    public const PAYMENT_METHOD_NAME_TMCK = 'TM/CK';

    protected $fillable = [
        'invoice_id',
        'invoice_request_id',
        'invoice_uuid',
        'invoice_series',
        'invoice_issued_date',
        'invoice_number',
        'invoice_status',
        'invoice_sign_status',
    ];

    protected $casts = [
        'invoice_issued_date' => 'date'
    ];

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    public function invoiceRequest()
    {
        return $this->belongsTo(InvoiceRequest::class, 'invoice_request_id');
    }

    public function scopeIssued($query)
    {
        $query->whereNotNull('invoice_issued_date');
    }

    public function scopeIssuedBetween($query, $ranges)
    {
        $query->whereBetween('invoice_issued_date', $ranges);
    }

    public static function getSignedInvoiceStatus($invoiceId)
    {
        try {
            $uuid = self::where('invoice_id', $invoiceId)->value('invoice_uuid');
            if (!$uuid) {
                return '';
            }
            $invoiceDetail = AccountingService::getInvoice($uuid);

            if (empty($invoiceDetail) || !$invoiceDetail['success']) {
                throw new \Exception($result['message'] ?? 'Failed to fetch signed invoice');
            }
            $invoiceStatus = isset($invoiceDetail['data']['is_tthdon']) ? $invoiceDetail['data']['is_tthdon'] : false;

            if (false === $invoiceStatus) {
                throw new \Exception($result['message'] ?? 'Failed to fetch signed invoice status');
            }

            return [
                'color' => INVOICE_SIGNED_STATUS_COLORS[$invoiceStatus],
                'text' => INVOICE_SIGNED_STATUSES[$invoiceStatus],
            ];
        } catch (\Throwable $ex) {
            sentry_exception_handler($ex);
            return '';
        }
    }

    public function sendSignedEmail(array $to, array $cc = [], $isAddToQueue = true)
    {
        $minvoiceRes = AccountingService::getInvoice($this->invoice_uuid);
        if (!$minvoiceRes['success']) {
            throw new \Exception($minvoiceRes['message'] ?? 'Failed to fetch signed invoice');
        }
        $invoiceDetail = $minvoiceRes['data'];
        // If need to be check before sending and email
        if (empty($invoiceDetail['macqt'])) {
            if ($isAddToQueue) {
                MailQueueIssueInvoice::create([
                    'minvoice_id' => $this->id
                ]);
            }
            return false;
        }
        $invoice = $this->invoice;

        // Get PDF of the draft invoice
        $res = AccountingService::getPdf($this->invoice_uuid);
        $pdfContent = base64_decode($res['data']);

        foreach ($to as $email) {
            $template = mail_template('Invoice_send_signed_invoice', $invoice, $invoiceDetail, $email, $cc);
            $template->add_attachment([
                'attachment' => $pdfContent,
                'filename'   => 'PO' . date('dmy', strtotime($invoice->date))  . '.pdf',
                'type'       => 'application/pdf',
            ]);
            $template->send();
        }

        return true;
    }
}

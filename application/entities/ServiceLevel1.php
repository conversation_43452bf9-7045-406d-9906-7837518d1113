<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class ServiceLevel1 extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'services_level1';
    protected $fillable = ['id','serviceid','name','priority','completion'];

    public function level2()
    {
        return $this->hasMany(ServiceLevel2::class,'level1_id');
    }

    public function service()
    {
        return $this->belongsTo(Service::class, 'serviceid', 'serviceid');
    }
}

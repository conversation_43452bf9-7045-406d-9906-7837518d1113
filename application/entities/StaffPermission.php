<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class StaffPermission extends BaseEntity
{
    protected $primaryKey = 'staff_id';
    protected $table = 'staff_permissions';

    // <PERSON>h sách nhân viên có quyền thay đổi trạng thái yêu cầu xuất hóa đơn
    public static function get_staff_permission_change_invoice_request(){
        return StaffPermission::join('staff', 'staff.staffid', '=', 'staff_permissions.staff_id')
            ->select('staff.staffid','staff.email')
            ->where('staff.active',1)
            ->where('staff_permissions.feature','account')
            ->where('staff_permissions.capability','change_status')
            ->distinct()
            ->get()
            ->toArray();
    }
}

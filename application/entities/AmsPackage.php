<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class AmsPackage extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'ams_packages';
    protected $fillable = [
        'crm_item_id',
        'ams_package_id'
    ];

    public const PACKAGE_SME_EXPIRE_DAYS = 30;
    public const PACKAGE_ENTERPRISE_EXPIRE_DAYS = 45;

    public const PACKAGE_TYPE_PACKAGE = 'package';
    public const PACKAGE_TYPE_CREDIT = 'credit';

    public const AMS_PACKAGE_BASIC_ID = 3908;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * Model relationship
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function item()
    {
        return $this->belongsTo(Item::class, 'crm_item_id');
    }

    public function scopeIsOnlinePayment($query)
    {
        $query->where('is_online_payment', 1);
    }
}

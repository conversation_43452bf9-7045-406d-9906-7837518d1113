<?php

namespace Entities;

use Illuminate\Database\Eloquent\Builder;

defined('BASEPATH') or exit('No direct script access allowed');

class M3cCallLogFailedJob extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'call_log_failed_jobs';

    public const STATUS_NEW = 'new';
    public const STATUS_IN_PROGRESS = 'in_progress';
    public const STATUS_DONE = 'done';
    public const STATUS_ERROR = 'error';

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    protected $fillable = [
        'start_time',
        'end_time',
        'status',
        'error',
    ];

    public function scopeNew(Builder $builder)
    {
        $builder->whereStatus(self::STATUS_NEW);
    }
}

<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class Department extends BaseEntity
{
    protected $primaryKey = 'departmentid';
    protected $table = 'departments';

    // HCM Sales Team 1 - id = 8
    // HCM Sales Team 2 - id = 9
    public const SALES_DEPARTMENT_IDS = [8, 9];

    public function scopeSalesOnly($query)
    {
        $query->whereIn('departments.departmentid', self::SALES_DEPARTMENT_IDS);
    }
}

<?php

namespace Entities;

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

class ClientPayerEcRecvTrans extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'client_payer_ec_recv_trans';

    protected $fillable = [
        'client_payer_ec_recv_id',
        'receivable_id',
        'trans_id',
        'remark',
        'recv_at',
        'bank_code',
        'deposit_amount',
        'deposit_at',
        'bank_trans_id',
    ];

    protected $casts = [
        'recv_at' => 'datetime',
        'deposit_at' => 'datetime',
    ];

    public function ecRecv()
    {
        return $this->belongsTo(ClientPayerEcRecv::class, 'client_payer_ec_recv_id');
    }
}

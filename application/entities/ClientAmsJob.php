<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class ClientAmsJob extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'client_ams_jobs';
    protected $fillable = [
        'client_id',
        'ams_company_id',
        'ams_job_id',
        'invoice_id',
        'package_id',
        'free_package',
    ];
    
    public const JOB_STATUS_CLOSED = 1;
    public const JOB_STATUS_REVIEW = 2;
    public const JOB_STATUS_OPEN = 3;
    public const JOB_LEVEL_PAID = 'paid';
    public const JOB_LEVEL_FREE = 'free';

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'used_packages' => '{}',
    ];

    protected $casts = [
        'used_packages' => 'json'
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    public function itemable()
    {
        return $this->belongsTo(Itemable::class, 'package_id');
    }
}

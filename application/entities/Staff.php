<?php

namespace Entities;

use Illuminate\Database\Eloquent\Relations\HasManyThrough;

defined('BASEPATH') or exit('No direct script access allowed');

class Staff extends BaseEntity
{
    protected $table = 'staff';
    protected $primaryKey = 'staffid';
    public const ROLE_SALES_LEADER = 7;
    public const ROLE_B2B_SALES = 1;
    public const CS_TEAM_EMAIL = '<EMAIL>';

    public function getFullnameAttribute(): string
    {
        return implode(' ', [$this->firstname, $this->lastname]);
    }

    public function scopeActive($builder)
    {
        $builder->where('staff.active', 1);
    }

    public function scopeIsSaleader($builder)
    {
        return $builder->where('role', self::ROLE_SALES_LEADER);
    }

    public function scopeIsB2BSale($query)
    {
        return $query->where('role', self::ROLE_B2B_SALES);
    }

    public function departments()
    {
        return $this->belongsToMany(Department::class, StaffDepartment::class, 'staffid', 'departmentid');
    }

    public static function isB2BSale()
    {
        return Staff::where('staffid', get_staff_user_id())->isB2BSale()->exists();
    }
}

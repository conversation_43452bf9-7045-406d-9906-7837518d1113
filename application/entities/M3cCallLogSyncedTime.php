<?php

namespace Entities;

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

class M3cCallLogSyncedTime extends BaseEntity
{
    protected $primaryKey = 'id';
    protected $table = 'call_log_synced_time';

    protected $fillable = [
        'synced_at'
    ];

    protected $casts = [
        'synced_at' => 'datetime',
    ];

    public static function store($syncedTime)
    {
        $synced = self::firstOrNew();
        if (!$synced->synced_at || $synced->synced_at->lt(Carbon::parse($syncedTime))) {
            $synced->synced_at = $syncedTime;
            $synced->save();
        }
    }
}

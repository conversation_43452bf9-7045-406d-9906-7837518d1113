<?php

namespace Entities;

defined('BASEPATH') or exit('No direct script access allowed');

class Note extends BaseEntity
{
    public const REL_TYPE_CUSTOMER = 'customer';
    public const TYPE_SUCCESS = 1;

    protected $casts = [
        'dateadded' => 'datetime'
    ];

    public function author()
    {
        return $this->belongsTo(Staff::class, 'addedfrom', 'staffid');
    }

    public function attachment()
    {
        return $this->hasOne(File::class, 'rel_id')->where('rel_type', 'note');
    }

    public function scopeCustomerNote($query)
    {
        $query->whereRelType(Note::REL_TYPE_CUSTOMER);
    }

    public function scopeSuccess($query)
    {
        $query->whereType(Note::TYPE_SUCCESS);
    }

    /**
     * Check deleted note is the latest record or not
     * @param int $clientId client id of that note
     * @return Note latest note
     */
    public static function latestNoteContactSuccess($clientId)
    {
        return self::select('id', 'dateadded')->whereRelId($clientId)->customerNote()->success()->latest('id')->first();
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Classify_2024_Column_To_Clients_Table_20250108144812 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'classify_2024' => [
                'type'       => 'ENUM',
                'constraint' => [
                    'DIAMOND',
                    'GOLD',
                    'SILVE<PERSON>',
                    'BRONZ<PERSON>',
                    'MEMBER',
                    'HIGH_POTENTIAL',
                    'POTENTIAL',
                ],
                'null' => true
            ],
            'classify_2025' => [
                'type'       => 'ENUM',
                'constraint' => [
                    'DIAMOND',
                    'GOLD',
                    'SILVER',
                    'BRONZE',
                    'MEMBER',
                    'HIGH_POTENTIAL',
                    'POTENTIAL',
                ],
                'null' => true
            ],
        ];

        if ($this->dbforge->add_column('clients', $fields)) {
            echo "\n\rAdd note_description to table tickets migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('clients', 'classify_2024');
        $this->dbforge->drop_column('clients', 'classify_2025');
    }
}

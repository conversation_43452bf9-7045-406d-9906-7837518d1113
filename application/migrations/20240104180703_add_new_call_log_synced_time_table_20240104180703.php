<?php

use Entities\M3cCallLogSyncedTime;

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_New_Call_Log_Synced_Time_Table_20240104180703 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'synced_at' => [
                'type' => 'DATETIME',
            ],
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('call_log_synced_time')) {
            M3cCallLogSyncedTime::create(['synced_at' => date('Y-m-d H:i:s')]);
            echo "\n\rTable call_log_synced_time migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('call_log_synced_time', true);
    }
}
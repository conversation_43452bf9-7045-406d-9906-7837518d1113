<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Migration_Add_Total_F2f_Meeting_Note_Per_Day_To_Kpi_Settings extends CI_Migration
{
    public function up()
    {
        if (!$this->db->field_exists('total_f2f_meeting_note_per_day', db_prefix() . 'kpi_settings')) {
            $this->db->query('ALTER TABLE ' . db_prefix() . 'kpi_settings 
                ADD COLUMN `total_f2f_meeting_note_per_day` INT(11) DEFAULT 0 COMMENT "Số F2F meeting/ngày" AFTER `total_note_per_day`');
            
            log_activity('Added total_f2f_meeting_note_per_day column to kpi_settings table');
        }
    }

    public function down()
    {
        if ($this->db->field_exists('total_f2f_meeting_note_per_day', db_prefix() . 'kpi_settings')) {
            $this->db->query('ALTER TABLE ' . db_prefix() . 'kpi_settings DROP COLUMN `total_f2f_meeting_note_per_day`');
        }
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Ext_Version_Table_20230822133908 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'version' => [
                'type' => 'INT',
                'constraint' => '10',
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('version');
        if ($this->dbforge->create_table('ext_version')) {
            $this->db->query('INSERT INTO ' . db_prefix() . 'ext_version(version) VALUES(101)');
            echo "\n\rTable ext_version migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('ext_version', true);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Unique_Key_For_Po_Master_Po_Details_Tables_20250311131307 extends CI_Migration
{
    public function up()
    {
        $this->db->query('ALTER TABLE `' . db_prefix() . 'po_master` ADD UNIQUE KEY `unique_po_id` (`po_id`)');
        $this->db->query('ALTER TABLE `' . db_prefix() . 'po_details` ADD UNIQUE KEY `unique_po_master_id` (`po_master_id`)');
    }
    public function down()
    {
        $this->db->query('ALTER TABLE `' . db_prefix() . 'po_master` DROP INDEX `unique_po_id`');
        $this->db->query('ALTER TABLE `' . db_prefix() . 'po_details` DROP INDEX `unique_po_master_id`');
    }
}
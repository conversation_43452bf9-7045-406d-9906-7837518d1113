<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Table_Kpi_Team_Revenue_20231030111749 extends CI_Migration
{
    public function up()
    {
        // Drop table 'table_name' if it exists
        $this->dbforge->drop_table('kpi_team_revenue', true);

        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'kpi_id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'comment' => 'KPI id'
            ],
            'team_id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'comment' => 'KPI id'
            ],
            'target_amount' => [
                'type' => 'FLOAT',
                'unsigned'       => true,
                'null' => true,
                'comment' => 'Chỉ tiêu'
            ],
        ]);
        $this->dbforge->add_key('kpi_id', true);
        $this->dbforge->add_key('team_id', true);
        if ($this->dbforge->create_table('kpi_team_revenue')) {
            echo "\n\rTable kpi_team_revenue migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('kpi_team_revenue', true);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Migration_Add_Field_Parent_Id_To_Estimate_Tables_20230911173334 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'estimates'
        $fields = [
            'parent_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'default' => null
            ],
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ];

        if ($this->dbforge->add_column('estimates', $fields)) {
            $this->db->query('CREATE INDEX idx_parent_id ON ' . db_prefix() . 'estimates(`parent_id`)');

            echo "\n\rTable tables_estimates migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('estimates', 'parent_id');
    }
}

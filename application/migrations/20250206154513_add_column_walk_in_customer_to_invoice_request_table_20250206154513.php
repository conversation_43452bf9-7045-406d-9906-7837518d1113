<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Column_Walk_In_Customer_To_Invoice_Request_Table_20250206154513 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'walk_in_customer' => [
                'type' => 'TINYINT',
                'default' => 0,
            ],
        ];

        if ($this->dbforge->add_column('invoice_request', $fields)) {
            echo "\n\rAdd note_description to table invoice_request migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('walk_in_customer', 'requested_issue_at');
    }
}
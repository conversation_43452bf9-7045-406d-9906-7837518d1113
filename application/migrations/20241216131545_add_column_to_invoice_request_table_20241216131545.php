<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Column_To_Invoice_Request_Table_20241216131545 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'request_issue_status' => [
                'type' => 'ENUM',
                'constraint' => ['fixed', 'approved', 'rejected'],
                'null' => true,
        ],
            'requested_issue_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'is_approved' => [
                'type' => 'TINYINT',
                'default' => 0,
            ],
            'approved_issue_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'rejected_issue_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'is_rejected' => [
                'type' => 'TINYINT',
                'default' => 0,
            ],
            'reject_reason' => [
                'type' => 'text',
                'null' => true,
            ],
        ];

        if ($this->dbforge->add_column('invoice_request', $fields)) {
            $this->db->query('CREATE INDEX idx_is_approved ON ' . db_prefix() . 'invoice_request(is_approved)');
            $this->db->query('CREATE INDEX idx_is_rejected ON ' . db_prefix() . 'invoice_request(is_rejected)');
            echo "\n\rAdd note_description to table invoice_request migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('invoice_request', 'requested_issue_at');
        $this->dbforge->drop_column('invoice_request', 'approved_issue_at');
        $this->dbforge->drop_column('invoice_request', 'rejected_issue_at');
        $this->dbforge->drop_column('invoice_request', 'reject_reason');
        $this->dbforge->drop_column('invoice_request', 'is_approved');
        $this->dbforge->drop_column('invoice_request', 'is_rejected');
        $this->dbforge->drop_column('invoice_request', 'request_issue_status');
    }
}

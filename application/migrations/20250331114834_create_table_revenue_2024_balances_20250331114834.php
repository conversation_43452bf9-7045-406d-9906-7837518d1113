<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Table_Revenue_2024_Balances_20250331114834 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'minvoice_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
                'null'  => true
            ],
            'minvoice_number' => [
                'type' => 'VARCHAR',
                'constraint' => '100',
                'null' => true,
            ],
            'minvoice_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
                'default' => 0,
            ],
            'po_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'po_no' => [
                'type' => 'VARCHAR',
                'constraint' => '100',
                'null' => true,
            ],
            'earned_revenue_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
                'default' => 0,
            ],
            'unearned_revenue_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
                'default' => 0,
            ],
            'company_name' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
                'null' => true,
            ],
            'issue_date datetime DEFAULT NULL',
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);

        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('revenue_2024_balances')) {
            $this->db->query('CREATE INDEX idx_po_id ON ' . db_prefix() . 'revenue_2024_balances(po_id)');
            $this->db->query('CREATE INDEX idx_minvoice_id ON ' . db_prefix() . 'revenue_2024_balances(minvoice_id)');
            $this->db->query('ALTER TABLE ' . db_prefix() . 'invoice_daily_revenues MODIFY COLUMN amount decimal(20,2) NULL');
            echo "\n\rTable revenue_2024_balances migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('revenue_2024_balances', true);
    }
}

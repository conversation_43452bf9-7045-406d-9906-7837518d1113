<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Free_Package_Column_To_Client_Ams_Jobs_Table_20240202142332 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'estimates'
        $fields = [
            'free_package' => [
                'type' => 'tinyint',
                'null' => true,
                'default' => 0
            ]
        ];

        if ($this->dbforge->add_column('client_ams_jobs', $fields)) {
            $this->db->query('CREATE INDEX idx_free_package ON ' . db_prefix() . 'client_ams_jobs(free_package)');
            echo "\n\rTable client_ams_jobs migrated.\n\r";
        }
    }

    public function down()
    {
        $this->dbforge->drop_column('client_ams_jobs', 'free_package');
    }
}
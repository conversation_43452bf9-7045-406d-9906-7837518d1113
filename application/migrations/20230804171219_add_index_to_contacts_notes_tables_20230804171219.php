<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Index_To_Contacts_Notes_Tables_20230804171219 extends CI_Migration
{
    public function up()
    {
        // Create index for the notes
        $this->db->query('CREATE INDEX idx_contact_id ON ' . db_prefix() . 'notes(`contact_id`)');
        $this->db->query('CREATE INDEX contact_phonenumber ON ' . db_prefix() . 'contacts(`phonenumber`)');
    }
    public function down()
    {
        $this->db->query('DROP INDEX idx_contact_id ON ' . db_prefix() . 'notes');
        $this->db->query('DROP INDEX contact_phonenumber ON ' . db_prefix() . 'contacts');
    }
}
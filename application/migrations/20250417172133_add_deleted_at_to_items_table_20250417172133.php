<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Deleted_At_To_Items_Table_20250417172133 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'deleted_at datetime DEFAULT NULL',
        ];

        if ($this->dbforge->add_column('items', $fields)) {
            echo "\n\rAdd deleted_at to table items migrated.\n\r";
        }
    }

    public function down()
    {
        $this->dbforge->drop_column('items', 'deleted_at');
    }
}
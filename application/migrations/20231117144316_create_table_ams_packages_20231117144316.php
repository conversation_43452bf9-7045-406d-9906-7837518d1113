<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Table_Ams_Packages_20231117144316 extends CI_Migration
{
    public function up()
    {
        // Drop table 'table_name' if it exists
        $this->dbforge->drop_table('ams_packages', true);

        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'crm_item_id' => [
                'type' => 'INT',
                'constraint' => '11',
            ],
            'ams_package_id' => [
                'type' => 'INT',
                'constraint' => '11',
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('ams_packages')) {
            $this->db->query('CREATE UNIQUE INDEX idx_crm_item_id_ams_package_id ON ' . db_prefix() . 'ams_packages(crm_item_id, ams_package_id)');
            $this->db->query('INSERT INTO '.db_prefix().'ams_packages(crm_item_id, ams_package_id) VALUES (3, 3908), (2, 3910), (4, 3912), (6, 3908), (7, 3910), (8, 3912)');
            echo "\n\rTable ams_packages migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('ams_packages', true);
    }
}
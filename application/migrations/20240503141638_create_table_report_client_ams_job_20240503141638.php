<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Table_Report_Client_Ams_Job_20240503141638 extends CI_Migration
{
    public function up()
    {
        // Drop table 'table_name' if it exists
        $this->dbforge->drop_table('report_client_ams_job', true);

        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_company_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_job_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'invoice_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'null' => true,
            ],
            'used_packages' => [
                'type' => 'text',
                'null' => true,
                'default' => null,
            ],
            'paid_package_id' => [
                'type' => 'int',
                'null' => true,
                'default' => null,
            ],
            'package_id' => [
                'type' => 'int',
                'null' => true,
                'default' => null,
            ],
            'free_package' => [
                'type' => 'tinyint',
                'null' => true,
                'default' => 0,
            ],
            'status_mapping' => [
                'type' => 'tinyint',
                'null' => true,
                'default' => 0,
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('report_client_ams_job')) {
            echo "\n\rTable report_client_ams_job migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('report_client_ams_job', true);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Client_Payers_Table_20241211153807 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'payer_no' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'payer_name' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
            ],
            'mother_account_no' => [
                'type' => 'VARCHAR',
                'constraint' => 30,
            ],
            'e_collection_code' => [
                'type' => 'VARCHAR',
                'constraint' => 30,
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('client_payers')) {
            $this->db->query('CREATE UNIQUE INDEX idx_client_id ON ' . db_prefix() . 'client_payers(client_id)');
            $this->db->query('CREATE INDEX idx_e_collection_code ON ' . db_prefix() . 'client_payers(e_collection_code)');
            echo "\n\rTable client_payers migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('client_payers', true);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Migration_Add_Field_Status_Updated_At_To_Estimates_Table_20231109162941 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'estimates'
        $fields = [
            'status_updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'default' => null
            ]
        ];

        if ($this->dbforge->add_column('estimates', $fields)) {

            // Mapping data from sales_activity into field status_updated_at of estimates table
            $this->db->query('
                UPDATE ' . db_prefix() . 'estimates A
                JOIN
                    (SELECT 
                        rel_id, MAX(date) AS dateChangeStatus
                    FROM
                        ' . db_prefix() . 'sales_activity
                    WHERE
                        description IN ("invoice_estimate_activity_sent_to_client" , "estimate_activity_marked", "estimate_activity_converted", "estimate_activity_client_accepted_and_converted", "estimate_activity_client_declined", "estimate_activity_created")
                    GROUP BY rel_id) B ON A.id = B.rel_id 
                SET 
                    A.status_updated_at = B.dateChangeStatus
            ');
        }

        $this->db->query('CREATE INDEX idx_invoiceid ON ' . db_prefix() . 'estimates(invoiceid)');
    }
    public function down()
    {
        $this->dbforge->drop_column('estimates', 'status_updated_at');

        $this->db->query('DROP INDEX idx_invoiceid ON ' . db_prefix() . 'estimates');
    }
}

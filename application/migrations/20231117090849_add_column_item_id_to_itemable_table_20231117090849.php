<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Column_Item_Id_To_Itemable_Table_20231117090849 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'estimates'
        $fields = [
            'item_id' => [
                'type' => 'int',
                'null' => true,
                'default' => null
            ]
        ];

        if ($this->dbforge->add_column('itemable', $fields)) {
            $this->db->query('CREATE INDEX idx_item_id ON ' . db_prefix() . 'itemable(item_id)');
            $this->db->query('UPDATE ' . db_prefix() . 'itemable it SET it.item_id = (select i.id FROM '.db_prefix().'items as i where TRIM(i.`description`) = TRIM(it.`description`) LIMIT 1)');
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('itemable', 'item_id');
    }
}
<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Columns_To_Clients_Table_20230727082740 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'business_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true
            ],
            'foreign_vat' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true
            ],
            'company_facebook' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true
            ],
            'company_linkedin' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true
            ],
            'company_notes' => [
                'type' => 'TEXT',
                'null' => true
            ],
            'company_types' => [
                'type'       => 'json',
                'null' => true
            ],
            'num_employees' => [
                'type'       => 'INT',
                'constraint'     => 11,
                'null' => true
            ],
            'company_email' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true
            ],
            'company_status' => [
                'type'       => 'ENUM',
                'constraint' => ['active', 'not_active_yet', 'has_no_legal_in_vn', 'inactive', 'insolvency', 'bankrupted'],
                'null' => true
            ],
            'established_date' => [
                'type' => 'DATE',
                'default' => null,
                'null' => true
            ],
            'approved_at' => [
                'type' => 'DATETIME',
                'default' => null,
                'null' => true
            ],
            'source' => [
                'type'       => 'ENUM',
                'constraint' => [
                    'hotline', 'recruit_socialite', 'it_recruit_platform', 'general_recruit_platform', 'linkedin', 'facebook', 'group',
                    'viet_nam_mobile_day', 'viet_nam_web_submit', 'networking'
                ],
                'null' => true
            ],
            'source_reason' => [
                'type' => 'TEXT',
                'null' => true
            ],
            'source_reference_link' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true
            ],
            'source_attachment' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true
            ],
        ];
        $this->dbforge->add_column('clients', $fields);

        // Set old data to approved for showing in the UI
        $this->db->query('CREATE INDEX idx_approved_at ON ' . db_prefix() . 'clients(`approved_at`)');
        $this->db->query('UPDATE ' . db_prefix() . 'clients SET approved_at = NOW()');
        $this->db->query('UPDATE ' . db_prefix() . 'clients SET business_name = company');

        // Create table client_requests
        $this->dbforge->add_field([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => 11
            ],
            'sale_id' => [
                'type' => 'INT',
                'constraint' => 11
            ],
            'leader_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true
            ],
            'admin_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true
            ],
            'request_status' => [
                'type'       => 'ENUM',
                'constraint' => ['waiting', 'sa_approved', 'sa_rejected', 'leader_approved', 'leader_rejected'],
                'default' => 'waiting'
            ],
            'created_at datetime default current_timestamp',
            'sa_note' => [
                'type' => 'TEXT',
                'null' => true
            ],
            'leader_note' => [
                'type' => 'TEXT',
                'null' => true
            ],
            'sa_approved_at' => [
                'type' => 'DATETIME',
                'default' => null,
                'null' => true
            ],
            'approved_at' => [
                'type' => 'DATETIME',
                'default' => null,
                'null' => true
            ],
            'rejected_at' => [
                'type' => 'DATETIME',
                'default' => null,
                'null' => true
            ],
        ]);
        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('client_id');
        $this->dbforge->add_key('sale_id');
        $this->dbforge->add_key('leader_id');
        $this->dbforge->add_key('admin_id');
        $this->dbforge->add_key('sa_approved_at');
        $this->dbforge->add_key('approved_at');
        $this->dbforge->add_key('rejected_at');
        $this->dbforge->create_table('client_requests');

        // Create table client_branches
        $this->dbforge->add_field([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => 11
            ],
            'branch_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true
            ],
            'branch_vat' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true
            ],
            'branch_business_address' => [
                'type' => 'TEXT',
                'null' => true
            ],
            'branch_office_address' => [
                'type' => 'TEXT',
                'null' => true
            ],
            'branch_status' => [
                'type'       => 'ENUM',
                'constraint' => ['active', 'not_active_yet', 'has_no_legal_in_vn', 'inactive', 'insolvency', 'bankrupted'],
                'null' => true
            ],
            'branch_note' => [
                'type' => 'TEXT',
                'null' => true
            ],
            'created_at datetime default current_timestamp'
        ]);
        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('client_id');
        $this->dbforge->create_table('client_branches');

        // Create table client_affiliates
        $this->dbforge->add_field([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => 11
            ],
            'relation_type' => [
                'type'       => 'ENUM',
                'constraint' => ['subsidiaries', 'holding_company', 'has_same_vat'],
                'null' => true
            ],
            'related_vat' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true
            ],
            'related_company_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true
            ],
            'created_at datetime default current_timestamp'
        ]);
        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('client_id');
        $this->dbforge->create_table('client_affiliates');

        // Create table client_nationalities
        $this->dbforge->add_field([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => 11
            ],
            'national_id' => [
                'type' => 'INT',
                'constraint' => 11
            ],
            'created_at datetime default current_timestamp'
        ]);
        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('client_id');
        $this->dbforge->add_key('national_id');
        $this->dbforge->create_table('client_nationalities');

        // Create table client_industries
        $this->dbforge->add_field([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => 11
            ],
            'industry_id' => [
                'type' => 'INT',
                'constraint' => 11
            ],
            'created_at datetime default current_timestamp'
        ]);
        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('client_id');
        $this->dbforge->add_key('industry_id');
        $this->dbforge->create_table('client_industries');

        // Create table client_offices
        $this->dbforge->add_field([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => 11
            ],
            'office_address' => [
                'type' => 'TEXT',
                'null' => true
            ],
            'created_at datetime default current_timestamp'
        ]);
        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('client_id');
        $this->dbforge->create_table('client_offices');
    }

    public function down()
    {
        $this->db->query('DROP INDEX idx_approved_at ON ' . db_prefix() . 'clients');
        $this->dbforge->drop_column('clients', 'business_name');
        $this->dbforge->drop_column('clients', 'foreign_vat');
        $this->dbforge->drop_column('clients', 'company_facebook');
        $this->dbforge->drop_column('clients', 'company_linkedin');
        $this->dbforge->drop_column('clients', 'company_notes');
        $this->dbforge->drop_column('clients', 'num_employees');
        $this->dbforge->drop_column('clients', 'company_types');
        $this->dbforge->drop_column('clients', 'company_email');
        $this->dbforge->drop_column('clients', 'company_status');
        $this->dbforge->drop_column('clients', 'established_date');
        $this->dbforge->drop_column('clients', 'approved_at');
        $this->dbforge->drop_column('clients', 'source');
        $this->dbforge->drop_column('clients', 'source_reason');
        $this->dbforge->drop_column('clients', 'source_reference_link');
        $this->dbforge->drop_column('clients', 'source_attachment');
        $this->dbforge->drop_table('client_requests');
        $this->dbforge->drop_table('client_branches');
        $this->dbforge->drop_table('client_affiliates');
        $this->dbforge->drop_table('client_nationalities');
        $this->dbforge->drop_table('client_industries');
        $this->dbforge->drop_table('client_offices');
    }
}

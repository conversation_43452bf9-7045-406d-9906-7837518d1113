        <?php

        defined('BASEPATH') or exit('No direct script access allowed');
        class Migration_Update_Column_Source_Table_Clients_20240222170248 extends CI_Migration
        {
            public function up()
            {
                $this->db->query("ALTER TABLE " . db_prefix() . "clients CHANGE `source` `source` enum('hotline','recruit_socialite','it_recruit_platform','general_recruit_platform','linkedin','facebook','group','viet_nam_mobile_day','viet_nam_web_submit','networking', 'signup') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL");
            }
            public function down()
            {
                $this->db->query("ALTER TABLE " . db_prefix() . "clients CHANGE `source` `source` enum('hotline','recruit_socialite','it_recruit_platform','general_recruit_platform','linkedin','facebook','group','viet_nam_mobile_day','viet_nam_web_submit','networking') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL");
            }
        }
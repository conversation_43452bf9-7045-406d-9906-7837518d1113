<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Invoice_Request_20231109164328 extends CI_Migration
{
    public function up()
    {
        // //// Drop table 'table_name' if it exists
        $this->dbforge->drop_table('invoice_request', true);

        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'invoiceid' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Invoice id'
            ],
            'received_email' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'default' => 0,
                'comment' => 'Email nhận hóa đơn tối đa 3'
            ],
            'request_status' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 0,
                'comment' => 'Trạng thái yêu cầu'
            ],
            'contract_payment_date' => [
                'type' => 'DATETIME',
                'default' => null,
                'null' => true,
                'comment' => 'Ngày thanh toán hợp đồng'
            ],
            'service_provided' => [
                'type' => 'ENUM("Tin đăng", "Truyền thông/ Sponsor","Vé sự kiện","Headhunt","Khác")',
                'comment' => 'Dịch vụ cung cấp'
            ],
            'company_name' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'comment' => 'Tên công ty xuất hóa đơn'
            ],
            'export_address' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'comment' => 'Địa chỉ xuất hóa đơn'
            ],
            'vat' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'comment' => 'Mã số thuê'
            ],
            'contract_status' => [
                'type' => 'ENUM("PO không ký", "PO có chữ ký của TopDev","PO có chữ ký của 2 bên","Hợp đồng chưa hoàn thành (Chưa đủ chữ ký của hai bên và sale chưa nhận được bản cứng","Hợp đồng đã hoàn tất (Đã ký 2 bên và Sale đã nhận được bản cứng)")',
                'comment' => 'Tình trạng hợp đồng'
            ],
            'content' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'comment' => 'Nội dung xuất'
            ],
            'note' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'comment' => 'Ghi chú'
            ],
            'invoice_draft' => [
                'type' => 'TINYINT',
                'unsigned' => true,
                'comment' => 'Yêu cầu xuất nháp trước khi ký'
            ],
            'status' => [
                'type' => 'int',
                'constraint' => 11,
                'unsigned' => true,
                'default' => 1,
                'comment' => 'Trạng thái'
            ],
            'staff_id' => [
                'type' => 'int',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'ID nhân viên'
            ],
            'account_id' => [
                'type' => 'int',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'ID kế toán'
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('invoice_request')) {
            echo "\n\rTable invoice_request migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('invoice_request', true);
    }
}

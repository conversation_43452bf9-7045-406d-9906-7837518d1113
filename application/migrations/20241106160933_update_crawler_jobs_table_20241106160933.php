<?php

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Update_Crawler_Jobs_Table_20241106160933 extends CI_Migration
{
    public function up()
    {
        $updateFields = [
            'posted_at' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'null' => true
            ],
        ];

        if ($this->dbforge->modify_column('crawler_jobs', $updateFields)) {
            $this->db->query('CREATE INDEX idx_posted_at ON ' . db_prefix() . 'crawler_jobs(posted_at)');
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('crawler_jobs', 'posted_at');
    }
}

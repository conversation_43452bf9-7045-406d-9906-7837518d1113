        <?php

        defined('BASEPATH') or exit('No direct script access allowed');
        class Migration_Update_Crm_Itemable_Id_In_Client_Ams_Search_Packages_Table_20240708092549 extends CI_Migration
        {
            public function up()
            {
                $this->db->query('ALTER TABLE `'.db_prefix().'client_ams_search_packages` CHANGE `crm_itemable_id` `crm_itemable_id` int unsigned UNSIGNED DEFAULT NULL;');
                $this->db->query('ALTER TABLE '.db_prefix().'client_ams_search_packages ADD CONSTRAINT unique_package_id UNIQUE (client_id, ams_company_id, ams_company_search_package_id);');
            }
            public function down()
            {
            }
        }
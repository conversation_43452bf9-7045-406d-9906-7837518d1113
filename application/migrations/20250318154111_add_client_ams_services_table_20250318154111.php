<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Client_Ams_Services_Table_20250318154111 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'invoice_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'null' => true
            ],
            'crm_itemable_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_company_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_job_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_service_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
                'comment' => 'This is the taxonomy_id from ams'
            ],
            'used_at datetime DEFAULT NULL',
            'note' => [
                'type' => 'TEXT',
                'null' => true
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('client_ams_services')) {
            echo "\n\rTable client_ams_services migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('client_ams_services', true);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Minvoice_Tables_20241212103747 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'invoice_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'invoice_request_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'invoice_uuid' => [
                'type' => 'VARCHAR',
                'constraint' => '50',
            ],
            'invoice_series' => [
                'type' => 'VARCHAR',
                'constraint' => '10',
            ],
            'invoice_issued_date' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'invoice_number' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => true,
            ],
            'invoice_status' => [
                'type' => 'TINYINT',
                'default' => 0,
            ],
            'invoice_sign_status' => [
                'type' => 'VARCHAR',
                'constraint' => '10',
                'null' => true
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('minvoices')) {
            echo "\n\rTable minvoices migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('minvoices', true);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Mail_Queue_Issue_Invoice_Table_20250326153237 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'minvoice_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'status' => [
                'type'       => 'ENUM',
                'constraint' => ['ready', 'sent'],
                'default' => 'ready'
            ],
            'sent_at datetime DEFAULT NULL',
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('mail_queue_issues_invoices')) {
            $this->db->query('CREATE INDEX idx_status ON ' . db_prefix() . 'mail_queue_issues_invoices(status)');
            echo "\n\rTable ams_services migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('mail_queue_issues_invoices', true);
    }
}
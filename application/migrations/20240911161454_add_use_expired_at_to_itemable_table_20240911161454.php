<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Use_Expired_At_To_Itemable_Table_20240911161454 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'use_expired_at date default null'
        ];

        if ($this->dbforge->add_column('itemable', $fields)) {
            $this->db->query('CREATE INDEX idx_use_expired_at ON ' . db_prefix() . 'itemable(`use_expired_at`)');
            $this->db->query('UPDATE ' . db_prefix() . 'itemable ii JOIN ' . db_prefix() . 'invoices i on ii.rel_id = i.id AND ii.rel_type = "invoice" AND i.status = 2 SET ii.use_expired_at = i.use_expired_at where i.use_expired_at IS NOT NULL');
            echo "\n\rTable tables_invoices migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('itemable', 'use_expired_at');
    }
}
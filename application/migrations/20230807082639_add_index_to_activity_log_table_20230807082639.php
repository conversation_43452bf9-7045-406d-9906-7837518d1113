<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Index_To_Activity_Log_Table_20230807082639 extends CI_Migration
{
    public function up()
    {
        // Create index for the notes
        $this->db->query('CREATE INDEX idx_rel_id ON ' . db_prefix() . 'activity_log(`rel_id`)');
        $this->db->query('CREATE INDEX idx_rel_type ON ' . db_prefix() . 'activity_log(`rel_type`)');
    }
    public function down()
    {
        $this->db->query('DROP INDEX idx_rel_id ON ' . db_prefix() . 'activity_log');
        $this->db->query('DROP INDEX idx_rel_type ON ' . db_prefix() . 'activity_log');
    }
}
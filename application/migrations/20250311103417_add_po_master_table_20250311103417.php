<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Po_Master_Table_20250311103417 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'po_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'po_no' => [
                'type' => 'VARCHAR',
                'constraint' => '100',
                'null' => true,
            ],
            'invoice_number' => [
                'type' => 'VARCHAR',
                'constraint' => '100',
                'null' => true,
            ],
            'issued_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'product_qty' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'default' => 0
            ],
            'po_total_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('po_master')) {
            echo "\n\rTable po_master migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('po_master', true);
    }
}

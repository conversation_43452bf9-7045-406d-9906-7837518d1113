        <?php

        defined('BASEPATH') or exit('No direct script access allowed');
        class Migration_Edit_Table_Service_Setting_20231127154504 extends CI_Migration
        {
            public function up()
            {
                // Drop table 'table_name' if it exists
                $this->dbforge->drop_table('services_level1', true);
                $this->dbforge->drop_table('services_level2', true);

                // Table level 1
                $this->dbforge->add_field([
                    'id' => [
                        'type'           => 'INT',
                        'constraint'     => 11,
                        'unsigned'       => true,
                        'auto_increment' => true
                    ],
                    'serviceid' => [
                        'type' => 'INT',
                        'constraint' => '11',
                        'unsigned' => true,
                        'comment' => 'ID dịch vụ'
                    ],
                    'name' => [
                        'type' => 'VARCHAR',
                        'constraint' => 300,
                        'null' => false,
                        'comment' => 'Tên cấp 1'
                    ],
                    'priority' => [
                        'type' => 'TINYINT',
                        'constraint' => 11,
                        'unsigned' => true,
                        'null' => true,
                        'comment' => 'Mức độ ưu tiên'
                    ],
                    'completion' => [
                        'type' => 'TINYINT',
                        'constraint' => 11,
                        'unsigned' => true,
                        'null' => true,
                        'comment' => 'Thời gian dự kiến hoàn thành'
                    ],
                ]);
                $this->dbforge->add_key('id', true);
                $this->dbforge->create_table('services_level1');

                // Table level 2
                $this->dbforge->add_field([
                    'id' => [
                        'type'           => 'INT',
                        'constraint'     => 11,
                        'unsigned'       => true,
                        'auto_increment' => true
                    ],
                    'level1_id' => [
                        'type' => 'INT',
                        'constraint' => '11',
                        'unsigned' => true,
                        'comment' => 'ID level 1'
                    ],
                    'name' => [
                        'type' => 'VARCHAR',
                        'constraint' => 300,
                        'null' => false,
                        'comment' => 'Tên cấp 2'
                    ],
                    'priority' => [
                        'type' => 'TINYINT',
                        'constraint' => 11,
                        'unsigned' => true,
                        'comment' => 'Mức độ ưu tiên'
                    ],
                    'completion' => [
                        'type' => 'TINYINT',
                        'constraint' => 11,
                        'unsigned' => true,
                        'comment' => 'Thời gian dự kiến hoàn thành'
                    ],
                ]);
                $this->dbforge->add_key('id', true);
                $this->dbforge->create_table('services_level2');
            }
            public function down()
            {
                $this->dbforge->drop_table('services_level1', true);
                $this->dbforge->drop_table('services_level2', true);
            }
        }
<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Update_Client_Payer_Ec_Recvs_Table_20241219134638 extends CI_Migration
{
    public function up()
    {
        $updateFields = [
            'payable_start_at datetime default CURRENT_TIMESTAMP',
            'payable_end_at' => [
                'type' => 'DATETIME',
            ],
            'received_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ];

        if ($this->dbforge->add_column('client_payer_ec_recvs', $updateFields)) {
            $this->db->query('DROP INDEX idx_invoice_id ON ' . db_prefix() . 'client_payer_ec_recvs');
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('client_payer_ec_recvs', 'received_at');
        $this->dbforge->drop_column('client_payer_ec_recvs', 'payable_start_at');
        $this->dbforge->drop_column('client_payer_ec_recvs', 'payable_end_at');
    }
}

        <?php

        defined('BASEPATH') or exit('No direct script access allowed');
        class Migration_Add_Staff_Id_To_3c_Call_Table_20240121110355 extends CI_Migration
        {
            public function up()
            {
                $fields = [
                    'staff_id' => [
                        'type' => 'int',
                        'constraint' => 11,
                        'null' => true
                    ],
                ];

                if($this->dbforge->add_column('3c_call', $fields)) {
                    $this->db->query('update tbl3c_call  c set staff_id = (select staffid from tblstaff s where s.ipphone = c.agent_id limit 1) where c.agent_id is not null and c.agent_id != ""');
                    $this->db->query('CREATE INDEX idx_staff_id ON ' . db_prefix() . '3c_call(staff_id)');
                    echo "\n\rTable add_staff_id_3c_call migrated.\n\r";
                }
            }
            public function down()
            {
                $this->dbforge->drop_column('3c_call', 'staff_id');
            }
        }
<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Columns_To_Invoice_20230913172909 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'invoice_closing_date' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'default' => null,
                'null' => true
            ],
        ];

        $this->dbforge->add_column('invoices', $fields);
    }
    public function down()
    {
        $this->dbforge->drop_column('invoices', 'invoice_closing_date');
    }
}

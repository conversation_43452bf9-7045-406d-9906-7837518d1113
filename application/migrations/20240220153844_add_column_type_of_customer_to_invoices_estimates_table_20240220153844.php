<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Column_Type_Of_Customer_To_Invoices_Estimates_Table_20240220153844 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'type_of_customer' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true
            ],
        ];

        if ($this->dbforge->add_column('estimates', $fields) && $this->dbforge->add_column('invoices', $fields)) {
            $this->db->query('UPDATE ' . db_prefix() . 'estimates ee set ee.type_of_customer = (select type_of_customer from ' . db_prefix() . 'clients where userid = ee.clientid)');
            $this->db->query('UPDATE ' . db_prefix() . 'invoices ee set ee.type_of_customer = (select type_of_customer from ' . db_prefix() . 'clients where userid = ee.clientid)');
            echo "\n\rAdd type_of_customer to table invoices, estimates migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('estimates', 'type_of_customer');
        $this->dbforge->drop_column('invoices', 'type_of_customer');
    }
}

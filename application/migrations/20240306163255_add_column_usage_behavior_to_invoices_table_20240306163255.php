<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Column_Usage_Behavior_To_Invoices_Table_20240306163255 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'usage_behavior' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true
            ],
        ];

        if ($this->dbforge->add_column('invoices', $fields)) {
            $this->db->query('UPDATE ' . db_prefix() . 'invoices ee set ee.usage_behavior = (select usage_behavior from ' . db_prefix() . 'clients where userid = ee.clientid)');
            $this->db->query("UPDATE " . db_prefix() . "clients cl set usage_behavior = ( CASE WHEN cl.num_of_usage_behavior > 0 OR ( select count(id) from " . db_prefix() . "invoices i where i.clientid = cl.userid and i.status = 2 and i.total ) > 0 THEN 'FOCUS' ELSE 'OTHER' END )");
            echo "\n\rAdd usage_behavior to table invoices migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('invoices', 'usage_behavior');
    }
}

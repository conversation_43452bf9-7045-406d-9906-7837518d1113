<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Approval_Columns_To_Invoices_Table_20250506153233 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'approval_version' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => true,
                'default'=> 'v2',
                'comment' => 'v2: prevent AD greter than 30%, v1: ignore checking AD'
            ],
            'approved_by' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
                'null' => true,
            ],
            'addition_discount_approved_at datetime DEFAULT NULL',
            'rejected_by' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
                'null' => true,
            ],
            'addition_discount_rejected_at datetime DEFAULT NULL',
        ];

        if ($this->dbforge->add_column('invoices', $fields)) {
            $this->db->query('update ' . db_prefix() . 'invoices set approval_version =  "v1"');
            echo "\n\rAdd columns to table invoices successfully.\n\r";
        }
    }

    public function down()
    {
        $this->dbforge->drop_column('invoices', 'addition_discount_approved_at');
        $this->dbforge->drop_column('invoices', 'approved_by');
        $this->dbforge->drop_column('invoices', 'addition_discount_rejected_at');
        $this->dbforge->drop_column('invoices', 'rejected_by');
        $this->dbforge->drop_column('invoices', 'approval_version');
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Client_Payer_Ec_Recvs_Table_20241211153827 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'client_payer_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'invoice_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'receivable_id' => [
                'type' => 'VARCHAR',
                'constraint' => '30',
            ],
            'deposit_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
            ],
            'qr_url' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
            ],
            'qr_data' => [
                'type' => 'TEXT',
            ],
            'callback_trans' => [
                'type' => 'json',
                'null' => true
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('client_payer_ec_recvs')) {
            $this->db->query('CREATE INDEX idx_receivable_id ON ' . db_prefix() . 'client_payer_ec_recvs(receivable_id)');
            $this->db->query('CREATE UNIQUE INDEX idx_invoice_id ON ' . db_prefix() . 'client_payer_ec_recvs(invoice_id)');
            echo "\n\rTable client_payer_ec_recvs migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('client_payer_ec_recvs', true);
    }
}

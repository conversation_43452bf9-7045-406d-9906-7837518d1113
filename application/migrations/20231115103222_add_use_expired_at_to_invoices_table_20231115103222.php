<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Use_Expired_At_To_Invoices_Table_20231115103222 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'use_expired_at date default null'
        ];

        if ($this->dbforge->add_column('invoices', $fields)) {
            $this->db->query('CREATE INDEX idx_use_expired_at ON ' . db_prefix() . 'invoices(`use_expired_at`)');
            $this->db->query('UPDATE ' . db_prefix() . 'invoices ii SET ii.use_expired_at = (SELECT DATE_ADD(MAX(`date`), INTERVAL 365 DAY) FROM ' . db_prefix() . 'invoicepaymentrecords ir WHERE ir.invoiceid = ii.id) WHERE ii.status = 2');
            echo "\n\rTable tables_invoices migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('invoices', 'use_expired_at');
    }
}

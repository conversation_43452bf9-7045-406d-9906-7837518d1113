<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Table_Client_Payer_Ec_Recv_Trans_20250117172556 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'client_payer_ec_recv_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'receivable_id' => [ // receivableId
                'type' => 'VARCHAR',
                'constraint' => '100',
            ],
            'trans_id' => [ // transId
                'type' => 'VARCHAR',
                'constraint' => '255',
                'null' => true
            ],
            'remark' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
                'null' => true
            ],
            'recv_at' => [ // recvDtm
                'type' => 'DATETIME',
                'null' => true,
            ],
            'bank_code' => [ // bankCode
                'type' => 'VARCHAR',
                'constraint' => '100',
            ],
            'deposit_amount' => [ // depositAmt
                'type' => 'DECIMAL',
                'constraint' => '15,2',
            ],
            'deposit_at' => [ // depositDtm
                'type' => 'DATETIME',
                'null' => true,
            ],
            'bank_trans_id' => [ // bankTransId
                'type' => 'VARCHAR',
                'constraint' => '100',
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('client_payer_ec_recv_trans')) {
            $this->db->query('CREATE INDEX idx_client_payer_ec_recv_id ON ' . db_prefix() . 'client_payer_ec_recv_trans(client_payer_ec_recv_id)');
            echo "\n\rTable client_payer_ec_recv_trans migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('client_payer_ec_recv_trans', true);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Is_50_Percent_Column_20250805150154 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'is_discount_50_percent' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => true,
                'default'=> 0,
                'comment' => '0: not checked, 1: checked'
            ],
        ];

        if ($this->dbforge->add_column('invoices', $fields)) {
            echo "\n\rAdd columns to table invoices successfully.\n\r";
        }
    }

    public function down()
    {
        $this->dbforge->drop_column('invoices', 'is_discount_50_percent');
    }
}
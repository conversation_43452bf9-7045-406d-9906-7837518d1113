<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Column_Promotion_Version_To_Invoice_Estimate_Table_20250402165709 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'promotion_version' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => true,
                'default'=> 'v2',
                'comment' => 'v2: churn/new program use new flow check DEV-2197'
            ],
        ];

        if ($this->dbforge->add_column('invoices', $fields)) {
            $this->db->query('update ' . db_prefix() . 'invoices set promotion_version =  "v1" where id > 4327');
            $this->db->query('update ' . db_prefix() . 'invoices set promotion_version = null where id <= 4327');
            echo "\n\rAdd promotion_version to table invoices migrated.\n\r";
        }

        if ($this->dbforge->add_column('estimates', $fields)) {
            $this->db->query('update ' . db_prefix() . 'estimates set promotion_version = "v1" where id > 5449');
            $this->db->query('update ' . db_prefix() . 'estimates set promotion_version = null where id <= 5449');
            echo "\n\rAdd promotion_version to table estimates migrated.\n\r";
        }
    }

    public function down()
    {
        $this->dbforge->drop_column('invoices', 'promotion_version');
        $this->dbforge->drop_column('estimates', 'promotion_version');
    }
}
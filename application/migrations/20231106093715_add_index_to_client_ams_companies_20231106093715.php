<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Index_To_Client_Ams_Companies_20231106093715 extends CI_Migration
{
    public function up()
    {
        $this->db->query('TRUNCATE table ' . db_prefix() . 'client_ams_companies');
        $this->db->query('CREATE INDEX ams_company_id ON ' . db_prefix() . 'client_ams_companies(ams_company_id)');
        $this->db->query('CREATE INDEX client_id ON ' . db_prefix() . 'client_ams_companies(client_id)');
        $this->db->query("INSERT INTO " . db_prefix() . "client_ams_companies(client_id, ams_company_id, created_at, updated_at) select relid, `value`, NOW(), NOW() from ".db_prefix()."customfieldsvalues where fieldid = 25 and fieldto = 'customers' and `value` != '' group by CONCAT(relid, `value`)");
    }

    public function down()
    {
        $this->db->query('DROP INDEX ams_company_id ON ' . db_prefix() . 'client_ams_companies');
        $this->db->query('DROP INDEX client_id ON ' . db_prefix() . 'client_ams_companies');
    }
}

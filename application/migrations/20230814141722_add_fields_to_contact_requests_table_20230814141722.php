<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Fields_To_Contact_Requests_Table_20230814141722 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'contact_requests'
        $fields = [
            'sa_note' => [
                'type' => 'text',
                'null' => true,
            ],
            'leader_note' => [
                'type' => 'text',
                'null' => true,
            ]
        ];

        if ($this->dbforge->add_column('contact_requests', $fields)) {
            echo "\n\rTable tables_contact_requests migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('contact_requests', 'sa_note');
        $this->dbforge->drop_column('contact_requests', 'leader_note');
    }
}

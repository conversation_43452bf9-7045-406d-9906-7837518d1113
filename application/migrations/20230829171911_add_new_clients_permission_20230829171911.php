<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_New_Clients_Permission_20230829171911 extends CI_Migration
{
    public function up()
    {
        $CI = &get_instance();
        $CI->load->model('Roles_model');
        // Sale Leader role
        $this->db->where('roleid', 7);
        $role = $this->db->get(db_prefix() . 'roles')->row();
        $role->permissions = !empty($role->permissions) ? unserialize($role->permissions) : [];
        $saleLeaderRole = json_decode(json_encode($role), true);
        $saleLeaderRolePermission = $saleLeaderRole['permissions'];
        if (isset($saleLeaderRolePermission['customers'])) {
            $saleLeaderRolePermission['customers'][] = 'active';
            $saleLeaderRole['permissions'] = $saleLeaderRolePermission;
            $saleLeaderRole['update_staff_permissions'] = true;
            $CI->Roles_model->update($saleLeaderRole, 7);
        }

        // Sale Admin role
        $this->db->where('roleid', 8);
        $role = $this->db->get(db_prefix() . 'roles')->row();
        $role->permissions = !empty($role->permissions) ? unserialize($role->permissions) : [];
        $saleAdminRole = json_decode(json_encode($role), true);
        $saleAdminRolePermission = $saleAdminRole['permissions'];
        if (isset($saleAdminRolePermission['customers'])) {
            $saleAdminRolePermission['customers'][] = 'active';
            $saleAdminRole['permissions'] = $saleAdminRolePermission;
            $saleAdminRole['update_staff_permissions'] = true;
            $CI->Roles_model->update($saleAdminRole, 8);
        }

        if (true) {
            echo "\n\rAdd clients's permission completed!.\n\r";
        }
    }
    public function down()
    {
    }
}

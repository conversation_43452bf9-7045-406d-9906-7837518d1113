<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Staff_Id_To_Tickets_Table_20250219113205 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'staff_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'null' => true,
            ],
            'closed_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ];

        if ($this->dbforge->add_column('tickets', $fields)) {
            echo "\n\rAdd staff_id to table tickets migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('tickets', 'staff_id');
    }
}
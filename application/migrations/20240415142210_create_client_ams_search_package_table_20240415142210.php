<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Client_Ams_Search_Package_Table_20240415142210 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'invoice_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'null' => true
            ],
            'ams_company_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'crm_itemable_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_search_package_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_company_search_package_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'paid_at datetime NOT NULL',
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if($this->dbforge->create_table('client_ams_search_packages')) {
            echo "\n\rTable client_ams_search_packages migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('client_ams_search_packages', true);
    }
}
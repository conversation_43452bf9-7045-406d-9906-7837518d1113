<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Setting_Kpi_20230911090537 extends CI_Migration
{
    public function up()
    {
       // Create table kpi
       $this->dbforge->add_field([
        'id' => [
            'type'           => 'INT',
            'constraint'     => 11,
            'unsigned'       => true,
            'auto_increment' => true,
        ],
        'month_kpi' => [
            'type' => 'DATETIME',
            'comment' => 'Tháng'
        ],
        'total_call_per_day' => [
            'type' => 'FLOAT',
            'comment' => 'Cuộc gọi/ngày	'
        ],
        'total_talk_per_day' => [
            'type' => 'FLOAT',
            'null' => true,
            'comment' => 'Phút/ngày'
        ],
        'total_note_per_day' => [
            'type' => 'INT',
            'null' => true,
            'comment' => 'Ghi chú/ngày'
        ],
        'total_new_client_month' => [
            'type' => 'INT',
            'null' => true,
            'comment' => '<PERSON>h<PERSON>ch hàng mới/tháng'
        ],
        'total_new_contact_month' => [
            'type' => 'INT',
            'null' => true,
            'comment' => 'Liên hệ mới/tháng'
        ],
        'created_by' => [
            'type' => 'INT',
            'default' => 0,
            'comment' => 'Tài khoản thêm'
        ],
        'updated_by' => [
            'type' => 'INT',
            'default' => 0,
            'comment' => 'Tài khoản cập nhật'
        ],
        'created_at datetime default current_timestamp',
        'updated_at datetime default current_timestamp',
        ]);

        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('month_kpi', true);
        $this->dbforge->create_table('kpi_settings');


        // Create table kpi detail
       $this->dbforge->add_field([
        'kpi_id' => [
            'type'           => 'INT',
            'constraint'     => 11,
            'unsigned'       => true,
            'comment' => 'KPI id'
        ],
        'staff_id' => [
            'type' => 'INT',
            'constraint' => 11,
            'default' => 0,
            'comment' => 'id nhân viên'
        ],
        'total_working_days' => [
            'type' => 'tinyint',
            'default' => 0,
            'comment' => 'Số ngày công'
        ],
        'target_amount' => [
            'type' => 'FLOAT',
            'unsigned'       => true,
            'null' => true,
            'comment' => 'Chỉ tiêu'
        ],
        ]);

        $this->dbforge->add_key('kpi_id',true);
        $this->dbforge->add_key('staff_id',true);
        $this->dbforge->create_table('kpi_staff_items');


        // Create table day off
       $this->dbforge->add_field([
        'id' => [
            'type'           => 'INT',
            'constraint'     => 11,
            'unsigned'       => true,
            'auto_increment' => true,
        ],
        'kpi_id' => [
            'type'           => 'INT',
            'constraint'     => 11,
            'unsigned'       => true,
            'comment' => 'KPI id'
        ],
        'staff_id' => [
            'type' => 'INT',
            'constraint' => 11,
            'default' => 0,
            'comment' => 'Id nhân viên'
        ],
        'date' => [
            'type' => 'DATETIME',
            'comment' => 'Ngày'
        ],
        'total_days' => [
            'type' => 'FLOAT',
            'default' => 0,
            'comment' => 'Thời gian'
        ],
        ]);

        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('staff_id', true);
        $this->dbforge->add_key('date', true);
        $this->dbforge->add_key('kpi_id', true);
        $this->dbforge->create_table('kpi_day_off');


    }

    public function down()
    {
        $this->dbforge->drop_table('kpi_settings');
        $this->dbforge->drop_table('kpi_staff_items');
        $this->dbforge->drop_table('kpi_day_off');
    }
}

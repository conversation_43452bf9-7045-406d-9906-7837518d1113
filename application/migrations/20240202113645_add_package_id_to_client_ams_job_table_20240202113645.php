<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Package_Id_To_Client_Ams_Job_Table_20240202113645 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'estimates'
        $fields = [
            'package_id' => [
                'type' => 'int',
                'null' => true,
                'default' => null
            ]
        ];

        if ($this->dbforge->add_column('client_ams_jobs', $fields)) {
            $this->db->query('CREATE INDEX idx_package_id ON ' . db_prefix() . 'client_ams_jobs(package_id)');
            echo "\n\rTable client_ams_jobs migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('add_package_id_client_ams_job', true);
    }
}
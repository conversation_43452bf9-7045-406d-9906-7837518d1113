<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Client_Ams_Jobs_Table_20231016102201 extends CI_Migration
{
    public function up()
    {
        // Drop table 'table_name' if it exists
        $this->dbforge->drop_table('client_ams_jobs', true);

        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_company_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_job_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'invoice_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'null' => true
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('client_ams_jobs')) {
            $this->db->query('CREATE UNIQUE INDEX idx_client_ams_company_job_id ON ' . db_prefix() . 'client_ams_jobs(client_id, ams_company_id, ams_job_id)');
            echo "\n\rTable client_ams_jobs migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('client_ams_jobs', true);
    }
}

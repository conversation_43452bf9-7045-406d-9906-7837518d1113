<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Colum_Ams_Order_Code_To_Invoices_Table_20240503094214 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'ams_order_code' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true
            ],
        ];

        if ($this->dbforge->add_column('invoices', $fields)) {
            echo "\n\rAdd ams_order_code to table invoices migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('invoices', 'ams_order_code');
    }
}
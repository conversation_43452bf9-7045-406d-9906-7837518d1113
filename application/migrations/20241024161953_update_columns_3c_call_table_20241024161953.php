<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Update_Columns_3c_Call_Table_20241024161953 extends CI_Migration
{
    public function up()
    {
        $updateFields = [
            'user_id' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true
            ],
            'group_id' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true
            ],
            'call_id' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true
            ],
            'end_status' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true
            ],
        ];
        $this->dbforge->modify_column('3c_call', $updateFields);
        $addFields = [
            'vendor' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true
            ],
            'transcripts' => [
                'type' => 'json',
                'null' => true
            ],
            'transcript_synced' => [
                'type' => 'tinyint',
                'null' => true,
                'default' => 0,
            ],
        ];
        $this->dbforge->add_key('transcript_synced');
        $this->dbforge->add_key('vendor');
        if ($this->dbforge->add_column('3c_call', $addFields)) {
            $this->db->query('CREATE INDEX idx_transcript_synced ON ' . db_prefix() . '3c_call(transcript_synced)');
            $this->db->query('CREATE INDEX idx_vendor ON ' . db_prefix() . '3c_call(vendor)');
        }
    }

    public function down()
    {
        $this->dbforge->drop_column('3c_call', 'vendor');
        $this->dbforge->drop_column('3c_call', 'transcripts');
        $this->dbforge->drop_column('3c_call', 'transcript_synced');
    }
}

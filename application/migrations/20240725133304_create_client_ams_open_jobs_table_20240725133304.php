<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Client_Ams_Open_Jobs_Table_20240725133304 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_company_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_job_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'invoice_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'itemable_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('ams_company_id');
        $this->dbforge->add_key('client_id');
        $this->dbforge->add_key('itemable_id');
        if ($this->dbforge->create_table('client_ams_open_jobs')) {
            $this->db->query('CREATE UNIQUE INDEX idx_ams_job_id ON ' . db_prefix() . 'client_ams_open_jobs(ams_job_id)');
            echo "\n\rTable client_ams_open_jobs migrated.\n\r";
        }
    }

    public function down()
    {
        $this->dbforge->drop_table('client_ams_open_jobs', true);
    }
}

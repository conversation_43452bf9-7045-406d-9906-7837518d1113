<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Crawler_Jobs_Table_20241101103154 extends CI_Migration
{
    public function up()
    {
        // Drop table 'table_name' if it exists
        $this->dbforge->drop_table('crawler_jobs', true);

        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'job_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
                'default' => null,
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'default' => null,

            ],
            'company' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'default' => null,
            ],
            'type' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'default' => null,
            ],
            'from_page' => [
                'type'       => 'ENUM',
                'constraint' => ['itviec', 'topcv', 'vietnamwork'],
                'default' => 'itviec',
                'null' => true
            ],
            'posted_at' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'default' => null,
            ],
            'working_model' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'default' => null,
            ],
            'location' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'default' => null,
            ],
            'skills' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'default' => null,
            ],
            'salary' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'default' => null,
            ],
            'description' => [
                'type' => 'text',
                'null' => true,
            ],
            'url' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'default' => null,
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('from_page');
        if ($this->dbforge->create_table('crawler_jobs')) {
            $this->db->query('CREATE UNIQUE INDEX idx_unique_from_page_job_id ON ' . db_prefix() . 'crawler_jobs(from_page, job_id)');
            echo "\n\rTable crawler_jobs migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('crawler_jobs', true);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Is_Round_Number_To_Invoices_Estimates_Tables_20240502095350 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'is_round_number' => [
                'type' => 'TINYINT',
                'default' => 1,
                'null' => true
            ],
        ];

        if ($this->dbforge->add_column('invoices', $fields) && $this->dbforge->add_column('estimates', $fields)) {
            $this->db->query('UPDATE ' . db_prefix() . 'invoices SET is_round_number = 0');
            $this->db->query('UPDATE ' . db_prefix() . 'estimates SET is_round_number = 0');
        }
    }

    public function down()
    {
        $this->dbforge->drop_column('invoices', 'is_round_number');
        $this->dbforge->drop_column('estimates', 'is_round_number');
    }
}
<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Index_For_Statistic_20230810113330 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'contact_requests'
        $fields = [
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true
            ],
            'sa_approved_at' => [
                'type' => 'datetime',
                'null' => true
            ],
        ];
        $this->dbforge->add_key('created_by');
        $this->dbforge->add_key('sa_approved_at');
        $this->dbforge->add_column('contacts', $fields);
        $this->db->query('CREATE INDEX idx_created_by ON ' . db_prefix() . 'contacts(`created_by`)');
        $this->db->query('CREATE INDEX idx_sa_approved_at ON ' . db_prefix() . 'contacts(`sa_approved_at`)');
        $this->db->query('update ' . db_prefix() . 'contacts c
                left join ' . db_prefix() . 'activity_log al ON c.id = al.rel_id AND al.description LIKE "Change Status Contact%"
                left join (
                    SELECT rel_id, staffid
                    FROM ' . db_prefix() . 'activity_log
                    WHERE
                        rel_type = "contact"
                        AND description LIKE "%created%"
                    GROUP BY
                        rel_id
                ) as call_log ON call_log.rel_id = c.id
            set
                c.sa_approved_at = al.`date`,
                c.created_by = call_log.staffid
            WHERE
                c.review_status = 1
                AND al.`date` is not null
                AND call_log.staffid is not null
        ');

        // Create index for the notes
        $this->db->query('CREATE INDEX idx_expired_at ON ' . db_prefix() . 'customer_admins(`expired_at`)');
        $this->db->query('CREATE INDEX idx_dateadded ON ' . db_prefix() . 'notes(`dateadded`)');
        $this->db->query('CREATE INDEX idx_datecreated ON ' . db_prefix() . 'contacts(`datecreated`)');
        $this->db->query('CREATE INDEX idx_date ON ' . db_prefix() . 'invoices(`date`)');
        $this->db->query('CREATE INDEX idx_status ON ' . db_prefix() . 'invoices(`status`)');
        $this->db->query('CREATE INDEX idx_date ON ' . db_prefix() . 'invoicepaymentrecords(`date`)');
        $this->db->query('CREATE INDEX idx_date ON ' . db_prefix() . 'estimates(`date`)');
        $this->db->query('CREATE INDEX idx_agent_id ON ' . db_prefix() . '3c_call(`agent_id`)');
        $this->db->query('CREATE INDEX idx_call_status ON ' . db_prefix() . '3c_call(`call_status`)');
        $this->db->query('CREATE INDEX idx_start_time ON ' . db_prefix() . '3c_call(`start_time`)');
        $this->db->query('CREATE INDEX idx_ipphone ON ' . db_prefix() . 'staff(`ipphone`)');
        
        $this->dbforge->add_field([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'staff_id' => [
                'type' => 'INT',
                'constraint' => 11
            ],
            'note_contacted' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0
            ],
            'note_contact_later' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0
            ],
            'created_at' => [
                'type' => 'date',
                'null' => true
            ]
        ]);
        
        $this->dbforge->add_key('id', true);
        $this->dbforge->add_key('staff_id');
        $this->dbforge->add_key('created_at');
        $this->dbforge->create_table('staff_statistics');
        $this->db->query('CREATE UNIQUE INDEX idx_unique_staff_created_at ON ' . db_prefix() . 'staff_statistics(`staff_id`, `created_at`)');
    }

    public function down()
    {
        $this->db->query('DROP INDEX idx_created_by ON ' . db_prefix() . 'contacts');
        $this->db->query('DROP INDEX idx_sa_approved_at ON ' . db_prefix() . 'contacts');
        $this->dbforge->drop_column('contacts', 'created_by');
        $this->dbforge->drop_column('contacts', 'sa_approved_at');
        $this->db->query('DROP INDEX idx_expired_at ON ' . db_prefix() . 'customer_admins');
        $this->db->query('DROP INDEX idx_dateadded ON ' . db_prefix() . 'notes');
        $this->db->query('DROP INDEX idx_datecreated ON ' . db_prefix() . 'contacts');
        $this->db->query('DROP INDEX idx_date ON ' . db_prefix() . 'invoices');
        $this->db->query('DROP INDEX idx_status ON ' . db_prefix() . 'invoices');
        $this->db->query('DROP INDEX idx_date ON ' . db_prefix() . 'invoicepaymentrecords');
        $this->db->query('DROP INDEX idx_date ON ' . db_prefix() . 'estimates');
        $this->db->query('DROP INDEX idx_agent_id ON ' . db_prefix() . '3c_call');
        $this->db->query('DROP INDEX idx_call_status ON ' . db_prefix() . '3c_call');
        $this->db->query('DROP INDEX idx_start_time ON ' . db_prefix() . '3c_call');
        $this->db->query('DROP INDEX idx_ipphone ON ' . db_prefix() . 'staff');
        $this->dbforge->drop_table('staff_statistics');
    }
}

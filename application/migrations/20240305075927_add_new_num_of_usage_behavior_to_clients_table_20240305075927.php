<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_New_Num_Of_Usage_Behavior_To_Clients_Table_20240305075927 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'num_of_usage_behavior' => [
                'type' => 'int',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true
            ],
        ];
        if ($this->dbforge->add_column('clients', $fields)) {
            echo "\n\rTable num_of_usage_behavior_clients migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('clients', 'usage_behavior');
    }
}

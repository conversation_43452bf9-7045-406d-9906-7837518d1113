        <?php

        defined('BASEPATH') or exit('No direct script access allowed');
        class Migration_Add_Column_Type_Table_Tickets_20231207135517 extends CI_Migration
        {
            public function up()
            {
                $fields = [
                    'type_id' => [
                        'type' => 'INT',
                        'constraint' => 11,
                        'unsigned' => true,
                        'default' => 2,
                        'comment' => '1 Ticket - 2 Feeback'
                    ],
                ];
                
                $this->dbforge->add_column('tickets', $fields);

                // Cập nhật data cũ về type_id = 2 cho Feedback
                $this->db->query('UPDATE tbltickets s SET type_id = 1 WHERE s.subject NOT LIKE "Feedback%"');
            }
            public function down()
            {
                $this->dbforge->drop_column('tickets', 'type_id');
            }
        }
<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Column_Is_Churn_New_Discount_To_Estimate_Table_20250225133623 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'is_churn_new_discount' => [
                'type' => 'TINYINT',
                'default' => 0,
            ],
        ];

        if ($this->dbforge->add_column('estimates', $fields)) {
            echo "\n\rAdd is_churn_new_discount to table estimates migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('estimates', 'is_churn_new_discount');
    }
}
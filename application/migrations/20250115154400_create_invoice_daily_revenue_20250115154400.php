<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_Invoice_Daily_Revenue_20250115154400 extends CI_Migration
{
    public function up()
    {
        $this->db->query('
            CREATE TABLE ' . db_prefix() . 'invoice_daily_revenues (
                id INT UNSIGNED NOT NULL AUTO_INCREMENT,
                invoice_id INT UNSIGNED NOT NULL,
                itemable_id INT UNSIGNED NOT NULL,
                ams_job_id INT UNSIGNED NOT NULL,
                revenue_date INT UNSIGNED NOT NULL,
                amount DECIMAL(10,2) DEFAULT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id, revenue_date),
                UNIQUE KEY idx_job_package_invoice_date (invoice_id, itemable_id, ams_job_id, revenue_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            PARTITION BY RANGE (revenue_date) (
                PARTITION p_202501 VALUES LESS THAN (20250201), -- January 2025
                PARTITION p_202502 VALUES LESS THAN (20250301), -- February 2025
                PARTITION p_202503 VALUES LESS THAN (20250401), -- March 2025
                PARTITION p_202504 VALUES LESS THAN (20250501), -- April 2025
                PARTITION p_202505 VALUES LESS THAN (20250601), -- May 2025
                PARTITION p_202506 VALUES LESS THAN (20250701), -- June 2025
                PARTITION p_202507 VALUES LESS THAN (20250801), -- July 2025
                PARTITION p_202508 VALUES LESS THAN (20250901), -- August 2025
                PARTITION p_202509 VALUES LESS THAN (20251001), -- September 2025
                PARTITION p_202510 VALUES LESS THAN (20251101), -- October 2025
                PARTITION p_202511 VALUES LESS THAN (20251201), -- November 2025
                PARTITION p_202512 VALUES LESS THAN (20260101), -- December 2025
                PARTITION p_202601 VALUES LESS THAN (20260201), -- January 2026
                PARTITION p_202602 VALUES LESS THAN (20260301), -- February 2026
                PARTITION p_202603 VALUES LESS THAN (20260401), -- March 2026
                PARTITION p_202604 VALUES LESS THAN (20260501), -- April 2026
                PARTITION p_202605 VALUES LESS THAN (20260601), -- May 2026
                PARTITION p_202606 VALUES LESS THAN (20260701), -- June 2026
                PARTITION p_202607 VALUES LESS THAN (20260801), -- July 2026
                PARTITION p_202608 VALUES LESS THAN (20260901), -- August 2026
                PARTITION p_202609 VALUES LESS THAN (20261001), -- September 2026
                PARTITION p_202610 VALUES LESS THAN (20261101), -- October 2026
                PARTITION p_202611 VALUES LESS THAN (20261201), -- November 2026
                PARTITION p_202612 VALUES LESS THAN (20270101), -- December 2026
                PARTITION p_202701 VALUES LESS THAN (20270201), -- January 2027
                PARTITION p_202702 VALUES LESS THAN (20270301), -- February 2027
                PARTITION p_202703 VALUES LESS THAN (20270401), -- March 2027
                PARTITION p_202704 VALUES LESS THAN (20270501), -- April 2027
                PARTITION p_202705 VALUES LESS THAN (20270601), -- May 2027
                PARTITION p_202706 VALUES LESS THAN (20270701), -- June 2027
                PARTITION p_202707 VALUES LESS THAN (20270801), -- July 2027
                PARTITION p_202708 VALUES LESS THAN (20270901), -- August 2027
                PARTITION p_202709 VALUES LESS THAN (20271001), -- September 2027
                PARTITION p_202710 VALUES LESS THAN (20271101), -- October 2027
                PARTITION p_202711 VALUES LESS THAN (20271201), -- November 2027
                PARTITION p_202712 VALUES LESS THAN (20280101), -- December 2027
                PARTITION p_future VALUES LESS THAN MAXVALUE -- Future dates
            );
        ');
    }
    public function down()
    {
        $this->dbforge->drop_table('invoice_daily_revenues', true);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Package_Type_To_Invoice_Daily_Revenues_Table_20250320141001 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'package_type' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
        ];

        if ($this->dbforge->add_column('invoice_daily_revenues', $fields)) {
            echo "\n\rAdd note to table invoice_daily_revenues migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('invoice_daily_revenues', 'package_type');
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_New_Call_Log_Failed_Jobs_Table_20240104141649 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'BIGINT',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'start_time' => [
                'type' => 'DATETIME',
            ],
            'end_time' => [
                'type' => 'DATETIME',
            ],
            'status' => [
                'type'       => 'ENUM',
                'constraint' => ['new', 'in_progress', 'done', 'error'],
                'null' => false
            ],
            'error' => [
                'type'       => 'TEXT',
                'null' => true
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('call_log_failed_jobs')) {
            echo "\n\rTable call_log_failed_jobs migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('call_log_failed_jobs', true);
    }
}
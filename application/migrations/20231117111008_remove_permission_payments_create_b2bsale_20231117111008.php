<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Remove_Permission_Payments_Create_B2bsale_20231117111008 extends CI_Migration
{
    public function up()
    {
        // Group ID - B2BSale
        $array_group = [1, 4, 7];

        // Array Delete
        $array_delete = array(
            [
                'position' => 'payments',
                'function' => 'create',
            ],
            [
                'position' => 'payments',
                'function' => 'edit',
            ],
            [
                'position' => 'payments',
                'function' => 'delete',
            ],
        );

        // Array Add
        $array_add = array();

        $CI = &get_instance();
        $CI->load->model('Roles_model');

        $CI->Roles_model->update_permission($array_group, $array_delete, $array_add);

        // Thêm quyền view_own cho sale admin
        $array_group = [1];
        $array_delete = array();
        $array_add = array(
            [
                'position' => 'account',
                'function' => 'view_own',
            ],
        );
        $CI->Roles_model->update_permission($array_group, $array_delete, $array_add);

        // Thêm quyền view cho sale admin
        $array_group = [8];
        $array_delete = array();
        $array_add = array(
            [
                'position' => 'account',
                'function' => 'view',
            ],
        );
        $CI->Roles_model->update_permission($array_group, $array_delete, $array_add);
    }
    public function down()
    {
        // Group ID - B2BSale
        $array_group = [1, 4, 7];

        // Array Delete
        $array_delete = array();

        // Array Add
        $array_add = array(
            [
                'position' => 'payments',
                'function' => 'create',
            ],
            [
                'position' => 'payments',
                'function' => 'edit',
            ],
            [
                'position' => 'payments',
                'function' => 'delete',
            ],
        );

        $CI = &get_instance();
        $CI->load->model('Roles_model');

        $CI->Roles_model->update_permission($array_group, $array_delete, $array_add);


        // Xóa đã Thêm quyền view_own cho sale admin
        $array_group = [1];
        $array_delete = array(
            [
                'position' => 'account',
                'function' => 'view_own',
            ],
        );
        $array_add = array(
        );
        $CI->Roles_model->update_permission($array_group, $array_delete, $array_add);

        // Thêm quyền view cho sale admin
        $array_group = [8];
        $array_delete = array(
            [
                'position' => 'account',
                'function' => 'view',
            ],
        );
        $array_add = array(
        );
        $CI->Roles_model->update_permission($array_group, $array_delete, $array_add);
    }
}

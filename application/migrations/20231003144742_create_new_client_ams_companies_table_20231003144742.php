<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Create_New_Client_Ams_Companies_Table_20231003144742 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'client_ams_companies'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'client_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'ams_company_id' => [
                'type' => 'BIGINT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('client_ams_companies')) {
            $this->db->query('CREATE UNIQUE INDEX idx_client_ams_company_id ON ' . db_prefix() . 'client_ams_companies(client_id, ams_company_id)');
            $this->db->query("INSERT INTO " . db_prefix() . "client_ams_companies(client_id, ams_company_id, created_at, updated_at) select relid, `value`, NOW(), NOW() from ".db_prefix()."customfieldsvalues where fieldid = 25 and fieldto = 'customers' and `value` != '' group by CONCAT(relid, `value`)");
            echo "\n\rTable client_ams_companies migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('client_ams_companies', true);
    }
}
        <?php

        defined('BASEPATH') or exit('No direct script access allowed');
        class Migration_Add_Column_Level_Table_Tickets_20231205101005 extends CI_Migration
        {
            public function up()
            {
                $fields = [
                    'first_level_id' => [
                        'type' => 'INT',
                        'constraint' => 11,
                        'unsigned' => true,
                        'default' => 0,
                        'comment' => 'Phân loại dịch vụ Cấp 1'
                    ],
                    'second_level_id' => [
                        'type' => 'INT',
                        'constraint' => 11,
                        'unsigned' => true,
                        'default' => 0,
                        'comment' => 'Phân loại dịch vụ Cấp 2'
                    ],
                ];
                
                $this->dbforge->add_column('tickets', $fields);
            }
            public function down()
            {
                $this->dbforge->drop_column('tickets', 'first_level_id');
                $this->dbforge->drop_column('tickets', 'second_level_id');
            }
        }
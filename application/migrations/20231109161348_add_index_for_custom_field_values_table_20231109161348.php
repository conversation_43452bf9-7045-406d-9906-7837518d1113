<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Index_For_Custom_Field_Values_Table_20231109161348 extends CI_Migration
{
    public function up()
    {
        $this->db->query('CREATE INDEX idx_relid ON ' . db_prefix() . 'customfieldsvalues(relid)');
        $this->db->query('CREATE INDEX idx_fieldid ON ' . db_prefix() . 'customfieldsvalues(fieldid)');
        $this->db->query('CREATE INDEX idx_fieldto ON ' . db_prefix() . 'customfieldsvalues(fieldto)');
    }

    public function down()
    {
        $this->db->query('DROP INDEX idx_relid ON ' . db_prefix() . 'customfieldsvalues');
        $this->db->query('DROP INDEX idx_fieldid ON ' . db_prefix() . 'customfieldsvalues');
        $this->db->query('DROP INDEX idx_fieldto ON ' . db_prefix() . 'customfieldsvalues');
    }
}

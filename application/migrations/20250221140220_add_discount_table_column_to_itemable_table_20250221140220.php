<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Discount_Table_Column_To_Itemable_Table_20250221140220 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'discount_table' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
                'default' => 0
            ],
        ];

        if ($this->dbforge->add_column('itemable', $fields)) {
            echo "\n\rAdd discount_table to table itemable migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('itemable', 'discount_table');
    }
}
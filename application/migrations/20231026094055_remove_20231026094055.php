<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Remove_20231026094055 extends CI_Migration
{
    public function up()
    {
        $CI = &get_instance();
        $CI->load->model('Roles_model');
        // B2B Sale Lead role
        $this->db->where('roleid', 7);
        $role = $this->db->get(db_prefix() . 'roles')->row();
        $role->permissions = !empty($role->permissions) ? unserialize($role->permissions) : [];
        $saleLeaderRole = json_decode(json_encode($role), true);
        $saleLeaderRolePermission = $saleLeaderRole['permissions'];

        foreach ($saleLeaderRolePermission['customers'] as $index => $role) {
            if ($role == 'active')
                unset($saleLeaderRolePermission['customers'][$index]);
        }


        $saleLeaderRole['permissions'] = $saleLeaderRolePermission;
        $saleLeaderRole['update_staff_permissions'] = true;

        $CI->Roles_model->update($saleLeaderRole, 7);
    }
    public function down()
    {
    }
}

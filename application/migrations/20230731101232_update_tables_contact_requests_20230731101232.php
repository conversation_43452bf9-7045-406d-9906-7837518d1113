<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Update_Tables_Contact_Requests_20230731101232 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'contact_requests'
        $fields = [
            'sa_approved_at' => [
                'type' => 'datetime',
                'after' => 'status'
            ],
            'rejected_at' => [
                'type' => 'datetime',
                'after' => 'sa_approved_at'
            ],
        ];

        if ($this->dbforge->add_column('contact_requests', $fields)) {
            echo "\n\rTable tables_contact_requests migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('contact_requests', 'sa_approved_at');
        $this->dbforge->drop_column('contact_requests', 'rejected_at');
    }
}

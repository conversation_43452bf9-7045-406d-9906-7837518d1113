<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Note_Column_For_Invoice_Daily_Revenues_Table_20250310122146 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'note' => [
                'type' => 'TEXT',
                'null' => true,
            ],
        ];

        if ($this->dbforge->add_column('invoice_daily_revenues', $fields)) {
            echo "\n\rAdd note to table invoice_daily_revenues migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('invoice_daily_revenues', 'note');
    }
}
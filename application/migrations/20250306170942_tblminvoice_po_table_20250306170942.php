<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Tblminvoice_Po_Table_20250306170942 extends CI_Migration
{
    public function up()
    {
        // Drop table 'table_name' if it exists
        $this->dbforge->drop_table('minvoice_purchase_order', true);

        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'minvoice_hoadon_number' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'null' => true,
            ],
           'po_number' => [
                'type' => 'VARCHAR',
                'constraint' => '100',
                'null' => true,
            ],
            'issued_at' => [
                'type' => 'DATE',
                'null' => true,
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if($this->dbforge->create_table('minvoice_purchase_order')) {
            echo "\n\rTable tblminvoice_purchase_order migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('minvoice_purchase_order', true);
    }
}

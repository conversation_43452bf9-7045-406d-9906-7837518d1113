<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Online_Payment_Package_Column_To_Mapping_20250424115931 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'is_online_payment' => [
                'type' => 'TINYINT',
                'default' => 0,
                'comment' => 'When set to 1, it will be used to mapping for the online payment feature'
            ],
        ];

        if ($this->dbforge->add_column('ams_packages', $fields)) {
            echo "\n\rAdd is_online_payment to table invoices migrated.\n\r";
        }

        if ($this->dbforge->add_column('ams_search_packages', $fields)) {
            echo "\n\rAdd is_online_payment to table estimates migrated.\n\r";
        }
    }

    public function down()
    {
        $this->dbforge->drop_column('ams_packages', 'is_online_payment');
        $this->dbforge->drop_column('ams_search_packages', 'is_online_payment');
    }
}

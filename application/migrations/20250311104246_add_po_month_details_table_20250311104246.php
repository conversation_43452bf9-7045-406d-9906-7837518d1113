<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Po_Month_Details_Table_20250311104246 extends CI_Migration
{
    public function up()
    {
        // Table structure for table 'table_name'
        $this->dbforge->add_field([
            'id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
                'auto_increment' => true
            ],
            'po_master_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'unsigned' => true,
            ],
            'revenue_date' => [
                'type' => 'DATE',
                'null' => true,
            ],
            'revenue_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
            ],
            'created_at datetime default CURRENT_TIMESTAMP',
            'updated_at datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]);
        $this->dbforge->add_key('id', true);
        if ($this->dbforge->create_table('po_details')) {
            echo "\n\rTable po_details migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_table('po_details', true);
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Index_Column_Last_Login_Table_Contacts_20231106101306 extends CI_Migration
{
    public function up()
    {
        // Create index for the contacts
        $this->db->query('CREATE INDEX last_login_is_primary ON ' . db_prefix() . 'contacts(last_login,is_primary)');
    }
    public function down()
    {
        $this->db->query('DROP INDEX last_login_is_primary ON ' . db_prefix() . 'contacts');
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');
class Migration_Add_Column_Note_Description_To_Tickets_20241030103630 extends CI_Migration
{
    public function up()
    {
        $fields = [
            'note_description' => [
                'type' => 'text',
                'null' => true,
                'default' => null,
            ],
        ];

        if ($this->dbforge->add_column('tickets', $fields)) {
            echo "\n\rAdd note_description to table tickets migrated.\n\r";
        }
    }
    public function down()
    {
        $this->dbforge->drop_column('tickets', 'note_description');
    }
}
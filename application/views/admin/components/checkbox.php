<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php
    // Required arguments
    $name = $name ?? '';
    $id = $name;
    $required = $required ?? false;
    $label = $label ?? '';
    $checkboxes = $checkboxes ?? [];
    $checkedIds = $checkedIds ?? [];
?>

<div class="dnd-select <?= $name ?>">
    <?php if ($required) : ?>
    <small class="req text-danger">* </small>
    <?php endif; ?>
    <label class="flex-none no-mbot"><b><?php echo _l($label) ?></b></label>

    <div class="chk mtop5 cb-error-<?= $name ?>">
        <?php foreach ($checkboxes as $idx => $checkbox) : ?>
            <div class="checkbox checkbox-inline">
                <input data-custom-error="cb-error-<?= $name ?>" type="checkbox" <?= in_array($checkbox['id'], $checkedIds) ? 'checked' : '' ?> id="chk_<?= $name ?>_<?= $idx ?>" value="<?= $checkbox['id'] ?>" name="<?= $name ?>[]">
                <label for="chk_<?= $name ?>_<?= $idx ?>"><?= $checkbox['text'] ?></label>
            </div>
        <?php endforeach ?>
    </div>
</div>
<script>
    
</script>
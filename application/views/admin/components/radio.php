<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php
    // Required arguments
    $name = $name ?? '';
    $id = $name;
    $required = $required ?? false;
    $label = $label ?? '';
    $radios = $radios ?? [];
    $checkedId = $checkedId ?? null;
?>

<div class="dnd-select <?= $name ?>">
    <?php if ($required) : ?>
    <small class="req text-danger">* </small>
    <?php endif; ?>
    <label class="flex-none no-mbot"><b><?php echo _l($label) ?></b></label>

    <div class="chk mtop5 radio-error-<?= $name ?>">
        <?php foreach ($radios as $idx => $radio) : ?>
            <div class="radio radio-inline">
                <input data-custom-error="radio-error-<?= $name ?>" type="radio" <?= $radio['id'] == $checkedId ? 'checked' : '' ?> id="chk_<?= $name ?>_<?= $idx ?>" value="<?= $radio['id'] ?>" name="<?= $name ?>">
                <label for="chk_<?= $name ?>_<?= $idx ?>"><?= $radio['text'] ?></label>
            </div>
        <?php endforeach ?>
    </div>
</div>
<script>
    
</script>
<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php
    // Required arguments
    $name = $name ?? '';
    $id = $name;
    $label = $label ?? '';
    $nonSelectedText = $nonSelectedText ?? '';
    $nonFilledText = $nonFilledText ?? '';
    $options = collect($options ?? [])->map(fn($option) => [
        'id' => $option['id'] ?? '',
        'text' => $option['text'] ?? '',
        'description' => $option['description'] ?? '',
    ])->toArray();
    $selectedOptions = $selectedOptions ?? [];
    $labelAsIndex = $labelAsIndex ?? null;
?>

<div class="dnd-select <?= $name ?>">
    <div class="col-12 col-lg-12 no-padding">
        <!-- <small class="req text-danger">*</small>&nbsp; -->
        <label><b><?php echo _l($label) ?></b></label>
    </div>
    <div class="col-12 col-lg-12 no-padding dnd-error-<?= $name ?>">
        <div class="list-dd col-12 col-lg-12 ui-sortable">
            <?php foreach ($selectedOptions as $option) : ?>
                <div class="dnd-items sortable d-flex justify-content-between align-items-center">
                    <div class="dnd-dragger" style="cursor: move;">
                        <svg fill="none" viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
                            <path xmlns="http://www.w3.org/2000/svg" d="M6 13H24M6 18H24" stroke="#393E46" stroke-width="2" stroke-linecap="round"></path>
                        </svg>
                    </div>
                    <div style="width: 258px;" class="flex-1 mleft10 mright10"><label id="item_label"><?= $option['name'] ?></label></div>
                    <div class="flex-grow-1 mleft5">
                        <input data-custom-error="dnd-error-<?= $name ?>" type="hidden" class="hidden-id" name="<?= $name ?>Ids[]" value="<?= $option['id'] ?>"/>
                        <input type="hidden" class="hidden-text" name="<?= $name ?>Text[]" value="<?= $option['name'] ?>"/>
                        <?= render_input($name . 'Des[]', '', $option['description'], 'text', ['placeholder' => _l($nonFilledText), 'data-custom-error' => 'dnd-des-error'], [], 'no-mbot', 'input-text'); ?>
                    </div>
                    <a href="javascript:;" class="remove-dnd mleft10 text-muted"><i class="fa fa-lg fa-2x fa-trash"></i></a>
                </div>
            <?php endforeach; ?>
        </div>
        <div class="col-12 col-lg-12 no-padding">
            <div class="d-flex justify-content-between align-items-center">
                <input class="hidden-id" data-custom-error="dnd-error-<?= $name ?>" type="hidden" name="<?= $name ?>Ids[]"/>
                <?= render_select($name . '-select', $options, ['id', 'text'], '', '', array_merge(['data-none-selected-text' => _l($nonSelectedText)]), [], 'flex-grow-1 no-mbot'); ?>
                <button type="button" id="add<?= $name ?>Btn" class="btn btn-info add"><i class="fa fa-plus"></i></button>
            </div>
        </div>
    </div>
</div>
<script>
    window.addEventListener("DOMContentLoaded", () => {
        const options = JSON.parse('<?= str_replace("'", "\\'", json_encode($options)); ?>');
        const dndItemTemplate = `
            <div class="dnd-items sortable d-flex justify-content-between align-items-center">
                <div class="dnd-dragger" style="cursor: move;">
                    <svg fill="none" viewBox="0 0 30 30" height="30" width="30" xmlns="http://www.w3.org/2000/svg">
                        <path xmlns="http://www.w3.org/2000/svg" d="M6 13H24M6 18H24" stroke="#393E46" stroke-width="2" stroke-linecap="round"></path>
                    </svg>
                </div>
                <div style="width: 258px;" class="flex-1 mleft10 mright10"><label id="item_label"></label></div>
                <div class="flex-grow-1 mleft5">
                    <input type="hidden" data-custom-error="dnd-error-<?= $name ?>"  class="hidden-id" name="<?= $name ?>Ids[]" value=""/>
                    <input type="hidden" class="hidden-text" name="<?= $name ?>Text[]" value=""/>
                    <?= render_input($name . 'Des[]', '', '', 'text', ['placeholder' => _l($nonFilledText), 'data-custom-error' => 'dnd-des-error'], [], 'no-mbot', 'input-text'); ?>
                </div>
                <a href="javascript:;" class="remove-dnd mleft10 text-muted"><i class="fa fa-lg fa-2x fa-trash"></i></a>
            </div>
        `
        $('.dnd-select div.list-dd').sortable({
            placeholder: 'ui-state-highlight',
            handle: '.dnd-dragger',
            cancel: '.disabled',
        });

        const pickerSettings = {
            noneSelectedText: '<?php echo _l($nonSelectedText) ?>',
            dropupAuto: false,
            liveSearch: true
        };

        $('div.dnd-select button#add<?= $name ?>Btn').on('click', function() {
            const select = $('div.dnd-select select#<?= $name ?>-select');
            if (select.val()) {
                const option = options.find((opt) => opt.id == select.val());
                if (option) {
                    $('div.dnd-select.<?= $name ?> div.list-dd').append(dndItemTemplate);
                    const lastItem = $('div.dnd-select.<?= $name ?> div.list-dd .dnd-items:last');
                    lastItem.find('label#item_label').text(option.text);
                    lastItem.find('input.input-text').val(option.description);
                    lastItem.find('input.hidden-id').val(option.id);
                    lastItem.find('input.hidden-text').val(option.text);
                    select.val(null).trigger('change');
                    lastItem.find('input.hidden-id').trigger('change');

                    <?php if (isset($labelAsIndex)) : ?>
                        reindexLabels();
                    <?php endif; ?>
                }
            }
        });

        $('div.dnd-select').on('click', 'a.remove-dnd', function () {
            $(this).parent().remove();
            <?php if (isset($labelAsIndex)) : ?>
                reindexLabels();
            <?php endif; ?>
        });

        function reindexLabels() {
            const items = $('div.dnd-select.<?= $name ?> div.list-dd .dnd-items');
            items.each((idx, e) => $(e).find('label#item_label').text(`<?= $labelAsIndex ?> ${idx+1}`));
        }
    });
</script>

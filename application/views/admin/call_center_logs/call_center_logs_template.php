<div id="call-center-log" class="tab-pane fade in active">
    <div class="row _filters">
        <?php if (!is_b2b_sale()) : ?>
            <div class="col-md-2">
                <?php echo render_select('customer_admins', $staff, ['staffid', ['firstname', 'lastname']], _l('customer_admins'), '', ['onchange' => "reloadDataLog()"]); ?>
            </div>
        <?php endif; ?>
        <div class="col-md-2">
            <?= render_select('call_type', get_call_types(), ['value', 'text'], _l('call_log_call_type'), '', ['onchange' => "reloadDataLog()"]); ?>
        </div>
        <div class="col-md-2">
            <?= render_select(
                'status',
                CALL_STATUS_OPTIONS,
                ['value', 'text'],
                _l('status'),
                '',
                ['onchange' => "reloadDataLog()"]
            ); ?>
        </div>
        <?php if (empty($client)): ?>
            <div class="col-md-2">
                <div class="form-group select-placeholder">
                    <label for="clientid" class="control-label"><?php echo _l('custom_field_company'); ?></label>
                    <select id="clientid" name="clientid" data-live-search="true" data-width="100%" class="ajax-search" onchange="reloadDataLog()"></select>
                </div>
            </div>
            <input type="hidden" id="mindate" name="mindate" value="<?= $min_date ?? '' ?>">
        <?php else : ?>
            <input type="hidden" id="clientid" name="clientid" value="<?= $client->userid ?>">
        <?php endif; ?>
        <div class="col-md-4">
            <div id="date-range" class="mbot15">
                <div class="row">
                    <div class="col-md-6">
                        <label for="dates" class="control-label">From - To</label>
                        <div class="input-group date">
                            <input type="text" class="form-control" id="dates" onchange="reloadDataLog()" name="dates" autocomplete="off">
                            <div class="input-group-addon">
                                <i class="fa fa-calendar calendar-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="clearfix mtop20"></div>
    <?php
    if (empty($client)) {
        $table_data = [
            _l('call_center_statistic_table_header_ca'),
            _l('call_center_statistic_table_header_no_call_out'),
            _l('call_center_statistic_table_header_no_call_out_success'),
            _l('call_center_statistic_table_header_no_call_out_missed'),
            _l('call_center_statistic_table_header_no_call_in'),
            _l('call_center_statistic_table_header_talktime'),
        ];

        render_datatable(
            $table_data,
            'call-center-statistics',
            [],
            [
                'data-export-filename' => 'exportCallStatisticFileName',
                'data-export-report-title' => 'exportCallStatisticReportTitle',
            ]
        );
    }
    ?>

    <div class="clearfix mtop20"></div>
    <?php
    $table_data = [
        'Call ID',
        [
            'name' => _l('record'),
            'th_attrs' => [
                'class' => (is_admin() || is_sales_leader()) ? '' : 'not_visible'
            ]
        ],
        [
            'name' => _l('custom_field_company'),
            'th_attrs' => [
                'class' => empty($client) ? 'not_visible' : ''
            ]
        ],
        _l('contact'),
        _l('clients_phone'),
        'IPPhone',
        _l('customer_admins'),
        _l('start_time'),
        _l('end_time'),
        _l('call_log_call_type'),
        _l('status'),
        _l('talk_time')
    ];

    render_datatable($table_data, 'call-center-logs');
    ?>
</div>
<script>
    const showStatistic = <?php echo empty($client) ? 1 : 0 ?>;
    let dataLog, dataStatistic = '';
    window.addEventListener("DOMContentLoaded", (event) => {
        let params = {}

        $.each($('#call-center-log ._filters select'), function() {
            params[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });
        $.each($('#call-center-log ._filters input'), function() {
            params[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });

        dataLog = initDataTable(
            '.table-call-center-logs',
            admin_url + 'call_center_logs/table',
            [1, 2, 3, 4, 6],
            [1, 2, 3, 4, 6],
            params,
            [7, 'desc']
        );
        if (showStatistic) {
            dataStatistic = initDataTable(
                '.table-call-center-statistics',
                admin_url + 'call_center_logs/statistic_table',
                [1, 2, 3, 4, 5],
                undefined,
                params,
                [5, 'desc'], {
                    searching: false,
                }
            );
        }

        $('#dates').daterangepicker({
            autoUpdateInput: false,
            alwaysShowCalendars: true,
            showCustomRangeLabel: true,
            autoApply: true,
            opens: 'center',
            ranges: {
                '<?= _l('call_center_statistic_date_today') ?>': [moment(), moment()],
                '<?= _l('call_center_statistic_date_yesterday') ?>': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                '<?= _l('call_center_statistic_date_last_7_days') ?>': [moment().subtract(6, 'days'), moment()],
                '<?= _l('call_center_statistic_date_last_30_days') ?>': [moment().subtract(29, 'days'), moment()],
                '<?= _l('call_center_statistic_date_this_week') ?>': [moment().startOf('week'), moment()],
                '<?= _l('call_center_statistic_date_last_week') ?>': [moment().subtract(1, 'week').startOf('week'), moment().subtract(1, 'week').endOf('week')],
                '<?= _l('call_center_statistic_date_this_month') ?>': [moment().startOf('month'), moment().endOf('month')],
                '<?= _l('call_center_statistic_date_last_month') ?>': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                '<?= _l('call_center_statistic_date_all') ?>': [moment($('#mindate').val()), moment()]
            }
        });
        $('#dates').on('apply.daterangepicker', function(ev, picker) {
            if (picker.startDate && picker.startDate.isValid() && picker.endDate && picker.endDate.isValid()) {
                $(this).val(`${picker.startDate.format('YYYY-MM-DD')} - ${picker.endDate.format('YYYY-MM-DD')}`);
            } else {
                $(this).val('');
            }
            reloadDataLog();
        });
    });

    function exportCallStatisticFileName() {
        return 'call-log-statistic-' + moment().format('YYYY-MM-DD');
    }

    function exportCallStatisticReportTitle() {
        let title = 'Call logs',
            dates = $('#dates').val() ? $('#dates').val().split(' - ') : [],
            startDate = moment(dates[0] ?? ''),
            endDate = moment(dates[1] ?? '');

        if (startDate.isValid() && endDate.isValid()) {
            if (startDate.format('YYYY-MM-DD') == endDate.format('YYYY-MM-DD')) {
                title = `${title} on ${startDate.format('YYYY-MM-DD')}`;
            } else {
                title = `${title} from ${startDate.format('YYYY-MM-DD')} to ${endDate.format('YYYY-MM-DD')}`;
            }
        } else {
            title = `${title} from ${moment($('#mindate').val()).format('YYYY-MM-DD')} to ${moment().format('YYYY-MM-DD')}`;
        }

        return title;
    }

    function reloadDataLog() {
        dataLog.ajax.reload();
        showStatistic && dataStatistic.ajax.reload();
    }

    function viewTranscript(transcript) {
        console.log('transcript', transcript);
        $('#view_transcript #modal-body').html(transcript);
    }
</script>
<?php include_once(APPPATH . 'views/admin/clients/modals/view_transcript_modal.php'); ?>

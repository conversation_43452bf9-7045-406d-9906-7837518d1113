<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<style>
    .modal-center {
        text-align: center;
        padding: 0 !important;
    }

    .modal-center:before {
        content: '';
        display: inline-block;
        height: 100%;
        vertical-align: middle;
        margin-right: -4px;
    }

    .modal-dialog-centered {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }

    #staticBackdropCallIn .company-list {
        max-height: 200px;
        overflow-x: hidden;
        overflow-y: auto;
    }
</style>
<input type="hidden" id="3c_call_staff_name" value="<?php echo get_staff_full_name() ?? ''; ?>">
<input type="hidden" id="3c_call_ipphone" value="<?php echo $this->session->userdata["ipphone"] ?? ''; ?>">
<input type="hidden" id="3c_call_token" value="<?php echo $this->session->userdata["3c_call_token"] ?? ''; ?>">
<input type="hidden" id="3c_call_domain" value="<?php echo MOBIFONE_3C_DOMAIN ?? ''; ?>">
<!-- Modal -->
<div class="modal modal-center fade in" id="staticBackdropCallIn" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"><?php echo _l('call_box_title') ?></h4>
            </div>
            <div class="modal-body row">
                <div class="col-md-12 text-center">
                    <h3 style="margin: 0;" class="text-danger"><i class="fa fa-phone">&nbsp;<span id="call_text"></span></i></h3>
                </div>
                <div class="hide row" id="company_info_template">
                    <div class="col-md-12">
                        <div class="col-md-4 bold"><?php echo _l('call_box_company'); ?></div>
                        <div class="col-md-8 company-title"></div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-4 bold"><?php echo _l('call_box_contact_name'); ?></div>
                        <div class="col-md-8 contact-name"></div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-4 bold"><?php echo _l('call_box_latest_note'); ?></div>
                        <div class="col-md-8 latest-note"></div>
                    </div>
                </div>
                <div class="col-md-12 mtop10">
                    <div class="company-list">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="extEndCall" type="button" class="btn btn-danger" data-bs-dismiss="modal"><?php echo _l('call_box_decline') ?></button>
                <button id="extAcceptCall" type="button" class="btn btn-primary"><?php echo _l('call_box_accept') ?></button>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="incomming-call-phone-number" value="">
<input type="hidden" id="incomming-call-chrome-ext-id" value="">
<button id="show-incomming-call-modal" type="button" class="hide" data-bs-toggle="modal" data-bs-target="#staticBackdropCallIn">
    Open incoming call modal
</button>
<button id="close-incomming-call-modal" class="hide" type="button">
    Open incoming call modal
</button>
<button id="ext-fetch-company" data-phonenumber="0968265555" class="hide" type="button">Fetch Company</button>
<script>
    (function() {
        const MESSAGE_TYPES = {
            REACT_INCOMMING_CALL: 'react_incomming_call',
            SEND_COMPANIES_INFO: 'send_companies_info',
            CONNECT_AUTH_CALLBACK: 'connect_auth_callback',
        };
        const MESSAGE_TARGET = 'service-worker';
        // Init instances
        var okBtn = document.getElementById('extAcceptCall'),
            cancelBtn = document.getElementById('extEndCall'),
            showModalBtn = document.getElementById('show-incomming-call-modal'),
            hideModalBtn = document.getElementById('close-incomming-call-modal'),
            fetchCompaniesBtn = document.getElementById('ext-fetch-company')
        authCallio = document.getElementById('auth-call-center-btn');

        // Add Event handler
        okBtn.addEventListener('click', reactIncomingCall.bind(okBtn, true));
        cancelBtn.addEventListener('click', reactIncomingCall.bind(cancelBtn, false));
        showModalBtn.addEventListener('click', showIncomingCall);
        hideModalBtn.addEventListener('click', function() {
            $('#staticBackdropCallIn').modal('hide');
        });
        fetchCompaniesBtn.addEventListener('click', fetchCompanies.bind(fetchCompaniesBtn))

        // Functions area
        function showIncomingCall() {
            var phoneNumber = $('#incomming-call-phone-number').val();
            $('#staticBackdropCallIn span#call_text').text(phoneNumber);
            $.getJSON(admin_url + 'clients/fetch_companies/' + phoneNumber, function(data) {
                const {
                    data: companies
                } = data,
                companyList = $('#staticBackdropCallIn div.company-list'),
                    template = $('#staticBackdropCallIn div#company_info_template'),
                    total = companies.length ?? 0;

                $('#staticBackdropCallIn').find('div.modal-dialog').removeClass('modal-sm');
                companyList.html('');
                companies && companies.forEach(function(company, idx) {
                    const companyEl = template.clone().removeAttr('hide');

                    companyEl.find('div.company-title').html(company.company);
                    companyEl.find('div.contact-name').html(company.contact_fullname);
                    companyEl.find('div.latest-note').html(company.latest_note);

                    companyList.append('<div class="row">' + companyEl.html() + '</div>');
                    // Append hr if not last
                    (idx + 1 < total) && companyList.append('<hr>');
                });

                $('#staticBackdropCallIn').modal({
                    show: true,
                    backdrop: 'static'
                });
            }).fail(function() {
                $('#staticBackdropCallIn div.company-list').html('');
                $('#staticBackdropCallIn').find('div.modal-dialog').addClass('modal-sm');
                $('#staticBackdropCallIn').modal({
                    show: true,
                    backdrop: 'static'
                });
            });
        }

        function reactIncomingCall(isAccept) {
            chrome.runtime && chrome.runtime.sendMessage($('#incomming-call-chrome-ext-id').val(), {
                type: MESSAGE_TYPES.REACT_INCOMMING_CALL,
                target: MESSAGE_TARGET,
                data: isAccept
            });
            $('#staticBackdropCallIn').modal('hide');
        }

        function fetchCompanies() {
            const extBtn = document.getElementById('ext-fetch-company');
            const phoneNumber = extBtn && extBtn.getAttribute('data-phonenumber');
            if (phoneNumber) {
                $.getJSON(admin_url + 'clients/fetch_companies/' + phoneNumber, function(data) {
                    const {
                        data: companies
                    } = data;

                    chrome.runtime.sendMessage($('#incomming-call-chrome-ext-id').val(), {
                        type: MESSAGE_TYPES.SEND_COMPANIES_INFO,
                        target: MESSAGE_TARGET,
                        data: companies
                    });
                });
            }
        }
    })();
</script>

<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
	<div class="content">
		<div class="row">
			<?php $this->load->view('admin/estimates/list_template'); ?>
		</div>
	</div>
</div>
<?php $this->load->view('admin/includes/modals/sales_attach_file'); ?>
<script>var hidden_columns = [];</script>
<?php init_tail(); ?>
<script>
	$(function(){
		init_estimate();
	});

    function toggleActionBtn(button, action) {
        if (action == 'show') {
            button.find('i.fa-spinner').removeClass('hide');
            button.addClass('disabled');
        } else {
            button.find('i.fa-spinner').addClass('hide');
            button.removeClass('disabled');
        }
    }

    function makeAsPrimary(estimateId) {
        if (confirm("<?= _l('estimate_make_primary_confirm_message'); ?>") && estimateId) {
            $.ajax({
                type: 'post',
                url: admin_url + 'estimates/make_primary/' + estimateId,
                data: {},
                success: function(response) {
                    alert_float('success', response.message);
                    reloadDataTable('.table-estimates')
                },
                error: function(response) {
                    alert_float('danger', response.responseJSON.message ?? 'Unknown Error!');
                }
            });
        }
    }

    function deleteOption(estimateId, estimateNumber) {
        var message = "<?= _l('estimate_delete_option_confirm_message', '_estimateNumber_'); ?>";

        if (confirm(message.replace('_estimateNumber_', estimateNumber)) && estimateId) {
            $.ajax({
                type: 'post',
                url: admin_url + 'estimates/delete/' + estimateId,
                data: {},
                success: function(response) {
                    alert_float('success', response.message);
                    reloadDataTable('.table-estimates')
                },
                error: function(response) {
                    alert_float('danger', response.responseJSON.message ?? 'Unknown Error!');
                }
            });
        }
    }
</script>
</body>
</html>

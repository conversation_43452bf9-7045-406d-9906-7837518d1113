<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php if (!isset($estimate) || $estimate->is_round_number): ?>
<script>
   app.options.is_round_number = true;
   app.options.new_decimal_places = 0;
</script>
<?php endif; ?>
<?php $type_of_customer = ''; ?>
<div class="panel_s accounting-template estimate">
   <div class="panel-body">
      <?php if(isset($estimate)){ ?>
      <?php echo format_estimate_status($estimate->status); ?>
      <hr class="hr-panel-heading" />
      <?php } ?>
      <div class="row">
        <?= form_hidden('discount_promotion_version', $estimate->promotion_version ?? ($promotion_version ?? '')) ?>
          <?php if (isset($estimate_request_id) && $estimate_request_id != '') {
              echo form_hidden('estimate_request_id',$estimate_request_id);
          }
          ?>
         <div class="col-md-6">
            <div class="f_client_id">
             <div class="form-group select-placeholder">
                <label for="clientid" class="control-label"><?php echo _l('estimate_select_customer'); ?></label>
                <select id="clientid" name="clientid" data-live-search="true" data-width="100%" class="ajax-search<?php if(isset($estimate) && empty($estimate->clientid)){echo ' customer-removed';} ?>" data-none-selected-text="<?php echo _l('dropdown_non_selected_tex'); ?>" <?= $is_create_option ? 'disabled' : ''?> >
               <?php $selected = (isset($estimate) ? $estimate->clientid : '');
                 if($selected == ''){
                   $selected = (isset($customer_id) ? $customer_id: '');
                 }
                 if($selected != ''){
                    $rel_data = get_relation_data('customer',$selected);
                    $type_of_customer = $rel_data->type_of_customer ?? '';
                    $rel_val = get_relation_values($rel_data,'customer');
                    echo '<option value="'.$rel_val['id'].'" selected>'.$rel_val['name'].'</option>';
                 } ?>
                </select>
              </div>
            </div>
            <?php echo render_select(
                'contact_id',
                $contact_data ?? [],
                ['id',
                  [
                    'fullname'
                  ]
                ],
                _l('contact'),
                $estimate->contact_id ?? '',
                $is_create_option ? [
                  'disabled' => 'disabled'
                ] : []
              );
              echo render_select(
                'customer_source',
                get_all_customer_sources(),
                ['value', 'text'],
                _l('customer_source'),
                $estimate->customer_source ?? '',
                $is_create_option ? [
                  'disabled' => 'disabled'
                ] : []
              );
            ?>
            <div class="form-group select-placeholder projects-wrapper<?php if((!isset($estimate)) || (isset($estimate) && !customer_has_projects($estimate->clientid))){echo $is_create_option || (isset($customer_id) && (!isset($project_id) || !$project_id)) ? ' hide' : '';} ?>">
             <label for="project_id"><?php echo _l('project'); ?></label>
             <div id="project_ajax_search_wrapper">
               <select name="project_id" id="project_id" class="projects ajax-search" data-live-search="true" data-width="100%" data-none-selected-text="<?php echo _l('dropdown_non_selected_tex'); ?>">
                <?php
                 if (!isset($project_id)) {
                    $project_id = '';
                 }
                  if(isset($estimate) && $estimate->project_id != 0){
                      $project_id = $estimate->project_id;
                  }
                  if ($project_id) {
                      echo '<option value="'.$project_id.'" selected>'.get_project_name_by_id($project_id).'</option>';
                  }
                ?>
              </select>
            </div>
           </div>
            <div class="row">
               <div class="col-md-12">
                  <?php if(!$is_create_option) { ?>
                    <a href="#" class="edit_shipping_billing_info" data-toggle="modal" data-target="#billing_and_shipping_details"><i class="fa fa-pencil-square-o"></i></a>
                  <?php } ?>
                  <?php include_once(APPPATH .'views/admin/estimates/billing_and_shipping_template.php'); ?>
               </div>
               <div class="col-md-6">
                  <p class="bold"><?php echo _l('invoice_bill_to'); ?></p>
                  <address>
                     <span class="billing_street">
                     <?php $billing_street = (isset($estimate) ? $estimate->billing_street : '--'); ?>
                     <?php $billing_street = ($billing_street == '' ? '--' :$billing_street); ?>
                     <?php echo $billing_street; ?></span><br>
                     <span class="billing_city">
                     <?php $billing_city = (isset($estimate) ? $estimate->billing_city : '--'); ?>
                     <?php $billing_city = ($billing_city == '' ? '--' :$billing_city); ?>
                     <?php echo $billing_city; ?></span>,
                     <span class="billing_state">
                     <?php $billing_state = (isset($estimate) ? $estimate->billing_state : '--'); ?>
                     <?php $billing_state = ($billing_state == '' ? '--' :$billing_state); ?>
                     <?php echo $billing_state; ?></span>
                     <br/>
                     <span class="billing_country">
                     <?php $billing_country = (isset($estimate) ? get_country_short_name($estimate->billing_country) : '--'); ?>
                     <?php $billing_country = ($billing_country == '' ? '--' :$billing_country); ?>
                     <?php echo $billing_country; ?></span>,
                     <span class="billing_zip">
                     <?php $billing_zip = (isset($estimate) ? $estimate->billing_zip : '--'); ?>
                     <?php $billing_zip = ($billing_zip == '' ? '--' :$billing_zip); ?>
                     <?php echo $billing_zip; ?></span>
                  </address>
               </div>
               <div class="col-md-6">
                  <p class="bold"><?php echo _l('ship_to'); ?></p>
                  <address>
                     <span class="shipping_street">
                     <?php $shipping_street = (isset($estimate) ? $estimate->shipping_street : '--'); ?>
                     <?php $shipping_street = ($shipping_street == '' ? '--' :$shipping_street); ?>
                     <?php echo $shipping_street; ?></span><br>
                     <span class="shipping_city">
                     <?php $shipping_city = (isset($estimate) ? $estimate->shipping_city : '--'); ?>
                     <?php $shipping_city = ($shipping_city == '' ? '--' :$shipping_city); ?>
                     <?php echo $shipping_city; ?></span>,
                     <span class="shipping_state">
                     <?php $shipping_state = (isset($estimate) ? $estimate->shipping_state : '--'); ?>
                     <?php $shipping_state = ($shipping_state == '' ? '--' :$shipping_state); ?>
                     <?php echo $shipping_state; ?></span>
                     <br/>
                     <span class="shipping_country">
                     <?php $shipping_country = (isset($estimate) ? get_country_short_name($estimate->shipping_country) : '--'); ?>
                     <?php $shipping_country = ($shipping_country == '' ? '--' :$shipping_country); ?>
                     <?php echo $shipping_country; ?></span>,
                     <span class="shipping_zip">
                     <?php $shipping_zip = (isset($estimate) ? $estimate->shipping_zip : '--'); ?>
                     <?php $shipping_zip = ($shipping_zip == '' ? '--' :$shipping_zip); ?>
                     <?php echo $shipping_zip; ?></span>
                  </address>
               </div>
            </div>
            <?php
               $next_estimate_number = get_option('next_estimate_number');
               $format = get_option('estimate_number_format');

                if(isset($estimate)){
                  $format = $estimate->number_format;
                }

               $prefix = get_option('estimate_prefix');

               if ($format == 1) {
                 $__number = $next_estimate_number;
                 if(isset($estimate)){
                   $__number = $estimate->number;
                   $prefix = '<span id="prefix">' . $estimate->prefix . '</span>';
                 }
               } else if($format == 2) {
                 if(isset($estimate)){
                   $__number = $estimate->number;
                   $prefix = $estimate->prefix;
                   $prefix = '<span id="prefix">'. $prefix . '</span><span id="prefix_year">' . date('Y',strtotime($estimate->date)).'</span>/';
                 } else {
                   $__number = $next_estimate_number;
                   $prefix = $prefix.'<span id="prefix_year">'.date('Y').'</span>/';
                 }
               } else if($format == 3) {
                  if(isset($estimate)){
                   $yy = date('y',strtotime($estimate->date));
                   $__number = $estimate->number;
                   $prefix = '<span id="prefix">'. $estimate->prefix . '</span>';
                 } else {
                  $yy = date('y');
                  $__number = $next_estimate_number;
                }
               } else if($format == 4) {
                  if(isset($estimate)){
                   $yyyy = date('Y',strtotime($estimate->date));
                   $mm = date('m',strtotime($estimate->date));
                   $__number = $estimate->number;
                   $prefix = '<span id="prefix">'. $estimate->prefix . '</span>';
                 } else {
                  $yyyy = date('Y');
                  $mm = date('m');
                  $__number = $next_estimate_number;
                }
               }

               $_estimate_number = str_pad($__number, get_option('number_padding_prefixes'), '0', STR_PAD_LEFT);
               $isedit = isset($estimate) ? 'true' : 'false';
               $data_original_number = isset($estimate) ? $estimate->number : 'false';
               ?>
            <div class="form-group">
               <label for="number"><?php echo _l('estimate_add_edit_number'); ?></label>
               <div class="input-group">
                  <span class="input-group-addon">
                  <!-- <?php if(isset($estimate)){ ?>
                  <a href="#" onclick="return false;" data-toggle="popover" data-container='._transaction_form' data-html="true" data-content="<label class='control-label'><?php echo _l('settings_sales_estimate_prefix'); ?></label><div class='input-group'><input name='s_prefix' type='text' class='form-control' value='<?php echo $estimate->prefix; ?>'></div><button type='button' onclick='save_sales_number_settings(this); return false;' data-url='<?php echo admin_url('estimates/update_number_settings/'.$estimate->id); ?>' class='btn btn-info btn-block mtop15'><?php echo _l('submit'); ?></button>"><i class="fa fa-cog"></i></a>
                   <?php } ?> -->
                  <?= $prefix ?>
                 </span>
                  <input type="text" name="number" readonly class="form-control" value="<?php echo $_estimate_number; ?>" data-isedit="<?php echo $isedit; ?>" data-original-number="<?php echo $data_original_number; ?>">
                  <?php if($format == 3) { ?>
                  <span class="input-group-addon">
                     <span id="prefix_year" class="format-n-yy"><?php echo $yy; ?></span>
                  </span>
                  <?php } else if($format == 4) { ?>
                   <span class="input-group-addon">
                     <span id="prefix_month" class="format-mm-yyyy"><?php echo $mm; ?></span>
                     /
                     <span id="prefix_year" class="format-mm-yyyy"><?php echo $yyyy; ?></span>
                  </span>
                  <?php } ?>
               </div>
            </div>

            <div class="row">
               <div class="col-md-6">
                  <?php $value = (isset($estimate) ? _d($estimate->date) : _d(date('Y-m-d'))); ?>
                  <?php echo render_date_input('date','estimate_add_edit_date',$value); ?>
               </div>
               <div class="col-md-6">
                  <?php
                  $value = '';
                  if(isset($estimate)){
                    $value = _d($estimate->expirydate);
                  } else {
                      if(get_option('estimate_due_after') != 0){
                          $value = _d(date('Y-m-d', strtotime('+' . get_option('estimate_due_after') . ' DAY', strtotime(date('Y-m-d')))));
                      }
                  }
                  echo render_date_input('expirydate','estimate_add_edit_expirydate',$value); ?>
               </div>
            </div>
            <div class="clearfix mbot15"></div>
            <?php $rel_id = (isset($estimate) ? $estimate->id : false); ?>
            <?php
                  if(isset($custom_fields_rel_transfer)) {
                      $rel_id = $custom_fields_rel_transfer;
                  }
             ?>
            <?php
              if (!$is_create_option) {
                echo render_custom_fields('estimate',$rel_id);
              }
            ?>
            <div class="row">
               <div class="col-md-6">
                  <div class="checkbox checkbox-primary checkbox-inline">
                    <?php $isNewChurn = is_po_estimate_new_churn($estimate->id ?? null, 'estimate'); ?>
                     <input
                        type="checkbox"
                        <?= $isNewChurn || does_not_have_discount_program($estimate->clientid ?? ($customer_id ?? null), 'estimate', $estimate->id ?? 0) ? '' : 'disabled' ?>
                        <?php echo ($estimate->is_churn_new_discount ?? false) && ($isNewChurn || is_churn_new_customer($type_of_customer)) ? 'checked' : '' ?>
                        name="is_churn_new_discount"
                        id="is_churn_new_discount"
                      >
                     <label for="is_churn_new_discount" class="no-mbot">
                        <b><?php echo _l('is_churn_new_discount_checkbox_label') ?></b>
                     </label>
                  </div>
               </div>
            </div>

            <div class="row">
               <div class="col-md-6">
                  <div class="checkbox checkbox-primary checkbox-inline">
                     <input
                        type="checkbox"
                        name="is_discount_50_percent"
                        id="is_discount_50_percent"
                        value="1"
                        <?php echo ($estimate->is_discount_50_percent ?? false) ? 'checked' : '' ?>
                      >
                     <label for="is_discount_50_percent" class="no-mbot">
                        <b><?php echo _l('is_discount_50_percent_checkbox_label') ?></b>
                     </label>
                  </div>
               </div>
            </div>

         </div>
         <div class="col-md-6">
            <div class="panel_s no-shadow">
              <?php if (!$is_create_option) { ?>
               <div class="form-group">
                  <label for="tags" class="control-label"><i class="fa fa-tag" aria-hidden="true"></i> <?php echo _l('tags'); ?></label>
                  <input type="text" class="tagsinput" id="tags" name="tags" value="<?php echo (isset($estimate) ? prep_tags_input(get_tags_in($estimate->id,'estimate')) : ''); ?>" data-role="tagsinput">
               </div>
              <?php } ?>
               <div class="row">
                  <div class="col-md-6">
                     <?php

                        $currency_attr = array('disabled'=>true,'data-show-subtext'=>true);
                        $currency_attr = apply_filters_deprecated('estimate_currency_disabled', [$currency_attr], '2.3.0', 'estimate_currency_attributes');
                        foreach($currencies as $currency){
                          if($currency['isdefault'] == 1){
                            $currency_attr['data-base'] = $currency['id'];
                          }
                          if(isset($estimate)){
                            if($currency['id'] == $estimate->currency){
                              $selected = $currency['id'];
                            }
                          } else{
                           if($currency['isdefault'] == 1){
                            $selected = $currency['id'];
                          }
                        }
                        }
                        $currency_attr = hooks()->apply_filters('estimate_currency_attributes',$currency_attr);
                        ?>
                     <?php echo render_select('currency', $currencies, array('id','name','symbol'), 'estimate_add_edit_currency', $selected, $currency_attr); ?>
                  </div>
                   <div class="col-md-6">
                     <div class="form-group select-placeholder">
                        <label class="control-label"><?php echo _l('estimate_status'); ?></label>
                        <select class="selectpicker display-block mbot15" name="status" data-width="100%" data-none-selected-text="<?php echo _l('dropdown_non_selected_tex'); ?>" <?= $is_create_option ? 'disabled' : ''?>>
                           <?php foreach($estimate_statuses as $status){ ?>
                           <option value="<?php echo $status; ?>" <?php if(isset($estimate) && $estimate->status == $status){echo 'selected';} ?>><?php echo format_estimate_status($status,'',false); ?></option>
                           <?php } ?>
                        </select>
                     </div>
                  </div>
                  <?php if (isset($estimate) && $estimate->status != \Entities\Estimate::STATUS_DECLINED) { ?>
                    <div class="col-md-12">
                        <?= render_textarea('reason_for_declined', 'estimate_declined_reason', '', ['rows' => 10], [], 'hidden'); ?>
                    </div>
                  <?php } ?>
                  <div class="col-md-12">
                    <?php echo render_input(
                      'reference_no',
                      'reference_no',
                      $estimate->reference_no ?? '',
                      'text',
                      $is_create_option ? [
                        'disabled' => 'disabled'
                      ] : []
                    );
                    ?>
                  </div>
                  <div class="col-md-6">
                         <?php
                        $selected = '';
                        foreach($staff as $member){
                         if(isset($estimate)){
                           if($estimate->sale_agent == $member['staffid']) {
                             $selected = $member['staffid'];
                           }
                         }
                        }
                          echo render_select(
                            'sale_agent',
                            $staff,
                            [
                              'staffid',
                              [
                                'firstname', 'lastname'
                              ]
                            ],
                            'sale_agent_string',
                            $selected,
                            $is_create_option ? [
                              'disabled' => 'disabled'
                            ] : []
                          );
                        ?>
                  </div>
                  <div class="col-md-6">
                       <div class="form-group select-placeholder">
                        <label for="discount_type" class="control-label"><?php echo _l('discount_type'); ?></label>
                        <select name="discount_type" class="selectpicker" data-width="100%" data-none-selected-text="<?php echo _l('dropdown_non_selected_tex'); ?>">
                           <option value="" selected><?php echo _l('no_discount'); ?></option>
                           <option value="before_tax" <?php
                              if(isset($estimate)){ if($estimate->discount_type == 'before_tax'){ echo 'selected'; }}?>><?php echo _l('discount_type_before_tax'); ?></option>
                           <option value="after_tax" <?php if(isset($estimate)){if($estimate->discount_type == 'after_tax'){echo 'selected';}} ?>><?php echo _l('discount_type_after_tax'); ?></option>
                        </select>
                     </div>
                  </div>
               </div>
               <?php $value = (isset($estimate) ? $estimate->adminnote : ''); ?>
               <?php echo render_textarea('adminnote','estimate_add_edit_admin_note',$value); ?>

            </div>
         </div>
      </div>
   </div>
   <?php $this->load->view('admin/estimates/_add_edit_items'); ?>
   <div class="row">
    <div class="col-md-12 mtop15">
      <div class="panel-body bottom-transaction">
        <?php
          $value = isset($estimate) && (!$is_create_option || $estimate->parent_id) ? $estimate->clientnote : get_option('predefined_clientnote_estimate')
        ?>
        <?php echo render_textarea('clientnote','estimate_add_edit_client_note',$value,array(),array(),'mtop15'); ?>
        <?php
          $value = isset($estimate) && (!$is_create_option || $estimate->parent_id) ? $estimate->terms : get_option('predefined_terms_estimate')
        ?>
        <?php echo render_textarea('terms','terms_and_conditions',$value,array(),array(),'mtop15'); ?>
        <div class="btn-bottom-toolbar text-right">
          <div class="btn-group dropup">
            <button type="button" class="btn-tr btn btn-info estimate-form-submit transaction-submit">
              <?php echo _l('submit'); ?>
            </button>
          <!-- <button type="button"
            class="btn btn-info dropdown-toggle"
            data-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false">
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu dropdown-menu-right width200">
            <li>
              <a href="#" class="estimate-form-submit save-and-send transaction-submit">
                <?php echo _l('save_and_send'); ?>
              </a>
            </li>
            <?php if(!isset($estimate)) { ?>
              <li>
                <a href="#" class="estimate-form-submit save-and-send-later transaction-submit">
                  <?php echo _l('save_and_send_later'); ?>
                </a>
              </li>
            <?php } ?>
          </ul> -->
        </div>
      </div>
    </div>
    <div class="btn-bottom-pusher"></div>
  </div>
</div>
</div>

<script>
  window.addEventListener('load', function() {
    $('select[name="status"]').on('change', function() {
      $('#reason_for_declined').parent().toggleClass('hidden', $(this).val() != <?= \Entities\Estimate::STATUS_DECLINED ?>);
    });

    $('#clientid').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
      var clientId = $(this).val();
      $.getJSON(`${admin_url}clients/is_churn_new_customer/${clientId}/estimate/<?= $estimate->id ?? 0 ?>`, function(data) {
          if (data.data) {
            $('#is_churn_new_discount').removeAttr('disabled');
          } else {
            $('#is_churn_new_discount').removeAttr('checked').attr('disabled', 'disabled');
          }
          calculate_total_v2();
      });
    });

    var discountProgram = $('#is_churn_new_discount');
    if (discountProgram && discountProgram.is(':checked')) {
      handle_discount_program();
    }
    toggle_promotion_program();
  });
</script>

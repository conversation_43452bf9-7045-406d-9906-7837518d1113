<div class="modal fade" id="mark_declined_estimate_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button group="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">
                    <span class="add-title"><?= _l('estimate_declined_reason'); ?></span>
                </h4>
            </div>
            <?= form_open('', ['id' => 'mark_declined_estimate_form']); ?>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <?= render_textarea('reason_for_declined', 'estimate_reason_for_declined', '', [
                            'rows' => 10
                        ]); ?>
                    </div>
                </div>
                <div class="col-md-2">
                </div>
                <div class="modal-footer">
                    <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                    <button group="submit" class="btn btn-info"><?= _l('submit'); ?></button>
                    <?= form_close(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function declinedEstimate(id) {
        $('#mark_declined_estimate_form').attr('action', `${admin_url}estimates/mark_action_status/${<?= \Entities\Estimate::STATUS_DECLINED ?>}/${id}/true`);
        appValidateForm($("#mark_declined_estimate_form"), {
            reason_for_declined: 'required',
        });
    }
</script>
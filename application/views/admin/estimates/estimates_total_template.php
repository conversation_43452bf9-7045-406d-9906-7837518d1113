<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="row">
  <div class="col-md-4">
    <div id="date-range" class="mbot15">
      <div class="row">
        <div class="col-md-6">
          <label for="estimates_total_from_date" class="control-label"><?= _l('from') ?></label>
          <div class="input-group date">
            <input type="text" class="form-control datepicker" value="<?= $this->input->post('from_date') ?? date('01.01.Y', strtotime('now')) ?>" id="estimates_total_from_date" onchange="init_estimates_total()" name="estimates_total_from_date" autocomplete="off">
            <div class="input-group-addon">
              <i class="fa fa-calendar calendar-icon"></i>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <label for="estimates_total_to_date" class="control-label"><?= _l('to') ?></label>
          <div class="input-group date">
            <input type="text" class="form-control datepicker" value="<?= $this->input->post('to_date') ?? date('31.12.Y', strtotime('now')) ?>" id="estimates_total_to_date" onchange="init_estimates_total()" name="estimates_total_to_date" autocomplete="off">
            <div class="input-group-addon">
              <i class="fa fa-calendar calendar-icon"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <?php if (count($estimates_years) > 1 || isset($currencies)) { ?>
    <div class="col-md-12 simple-bootstrap-select mbot5">
      <?php if (isset($currencies)) { ?>
        <select class="selectpicker" data-width="auto" name="total_currency" onchange="init_estimates_total();">
          <?php foreach ($currencies as $currency) {
            $selected = '';
            if (!$this->input->post('currency')) {
              if ($currency['isdefault'] == 1 || isset($_currency) && $_currency == $currency['id']) {
                $selected = 'selected';
              }
            } else {
              if ($this->input->post('currency') == $currency['id']) {
                $selected = 'selected';
              }
            }
          ?>
            <option value="<?php echo $currency['id']; ?>" <?php echo $selected; ?> data-subtext="<?php echo $currency['name']; ?>"><?php echo $currency['symbol']; ?></option>
          <?php } ?>
        </select>
      <?php } ?>
    </div>
</div>
<div class="row">
<?php }
  $total = array_sum(array_column($totals, 'total'));
  $total_tax = array_sum(array_column($totals, 'total_tax'));

  foreach ($totals as $key => $data) { ?>
  <div class="col-md-2 total-column">
    <div class="panel_s">
      <div class="panel-body">
        <h3 class="text-muted _total">
          <?= app_format_money($data['total'], $data['currency_name']); ?>
        </h3>
        <span class="text-<?= estimate_status_color_class($data['status']); ?>"><?= estimate_status_by_id($data['status']); ?></span>
      </div>
    </div>
  </div>
<?php } ?>
  <div class="col-md-2 total-column">
    <div class="panel_s">
      <div class="panel-body">
        <h3 class="text-muted _total">
          <?= app_format_money($total, $data['currency_name']); ?>
        </h3>
        <span class="text-info"><?= _l('all_estimates') ?></span>
      </div>
    </div>
  </div>
</div>

<label for="estimates_total_to_date" class="control-label"><?= _l('not_includes_vat') ?></label>
<div class="row">
  <?php
  $total_no_tax = 0;
  foreach ($totals as $key => $data) { ?>
    <div class="col-md-2 total-column">
      <div class="panel_s">
        <div class="panel-body">
          <h3 class="text-muted _total">
            <?= app_format_money($data['total'] - $data['total_tax'], $data['currency_name']); ?>
          </h3>
          <span class="text-<?= estimate_status_color_class($data['status']); ?>"><?= estimate_status_by_id($data['status']); ?></span>
        </div>
      </div>
    </div>
  <?php } ?>
  <div class="col-md-2 total-column">
    <div class="panel_s">
      <div class="panel-body">
        <h3 class="text-muted _total">
          <?= app_format_money($total - $total_tax, $data['currency_name']); ?>
        </h3>
        <span class="text-info"><?= _l('all_estimates') ?></span>
      </div>
    </div>
  </div>
</div>
<hr>
<div class="clearfix"></div>
<script>
  init_datepicker();
</script>
<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="panel-body mtop10">
   <div class="row">
      <div class="col-md-4">
          <?php $this->load->view('admin/invoice_items/item_select'); ?>
      </div>
      <div class="col-md-8 text-right show_quantity_as_wrapper">
         <div class="mtop10">
            <span><?php echo _l('show_quantity_as'); ?></span>
            <div class="radio radio-primary radio-inline">
               <input type="radio" value="1" id="1" name="show_quantity_as" data-text="<?php echo _l('estimate_table_quantity_heading'); ?>" <?php if(isset($estimate) && $estimate->show_quantity_as == 1){echo 'checked';}else{echo'checked';} ?>>
               <label for="1"><?php echo _l('quantity_as_qty'); ?></label>
            </div>
            <div class="radio radio-primary radio-inline">
               <input type="radio" value="2" id="2" name="show_quantity_as" data-text="<?php echo _l('estimate_table_hours_heading'); ?>" <?php if(isset($estimate) && $estimate->show_quantity_as == 2){echo 'checked';} ?>>
               <label for="2"><?php echo _l('quantity_as_hours'); ?></label>
            </div>
            <div class="radio radio-primary radio-inline">
               <input type="radio" id="3" value="3" name="show_quantity_as" data-text="<?php echo _l('estimate_table_quantity_heading'); ?>/<?php echo _l('estimate_table_hours_heading'); ?>" <?php if(isset($estimate) && $estimate->show_quantity_as == 3){echo 'checked';} ?>>
               <label for="3"><?php echo _l('estimate_table_quantity_heading'); ?>/<?php echo _l('estimate_table_hours_heading'); ?></label>
            </div>
         </div>
      </div>
   </div>
   <div class="table-responsive s_table">
      <table class="table estimate-items-table items table-main-estimate-edit has-calculations no-mtop">
         <thead>
            <tr>
               <th></th>
               <th width="20%" align="left"><i class="fa fa-exclamation-circle" aria-hidden="true" data-toggle="tooltip" data-title="<?php echo _l('item_description_new_lines_notice'); ?>"></i> <?php echo _l('estimate_table_item_heading'); ?></th>
               <th width="25%" align="left"><?php echo _l('estimate_table_item_description'); ?></th>
               <?php
                  $custom_fields = get_custom_fields('items');
                  foreach($custom_fields as $cf){
                   echo '<th width="15%" align="left" class="custom_field">' . $cf['name'] . '</th>';
                  }

                  $qty_heading = _l('estimate_table_quantity_heading');
                  if(isset($estimate) && $estimate->show_quantity_as == 2){
                  $qty_heading = _l('estimate_table_hours_heading');
                  } else if(isset($estimate) && $estimate->show_quantity_as == 3){
                  $qty_heading = _l('estimate_table_quantity_heading') . '/' . _l('estimate_table_hours_heading');
                  }
                  ?>
               <th width="10%" class="qty" align="right"><?php echo $qty_heading; ?></th>
               <th width="15%" align="right"><?php echo _l('estimate_table_rate_heading'); ?></th>
               <th width="10%" align="right" class="discount_table"><?php echo _l('estimate_table_discount_table_heading'); ?></th>
               <th width="10%" align="right"><?php echo _l('estimate_table_tax_heading'); ?></th>
               <th width="10%" align="right"><?php echo _l('estimate_table_amount_heading'); ?></th>
               <th align="center"><i class="fa fa-cog"></i></th>
            </tr>
         </thead>
         <tbody>
            <tr class="main">
               <td><?php echo form_hidden('item_id'); ?></td>
               <td>
                  <textarea readonly="true" name="description" rows="4" class="form-control" placeholder="<?php echo _l('item_description_placeholder'); ?>"></textarea>
               </td>
               <td>
                  <textarea name="long_description" rows="4" class="form-control" placeholder="<?php echo _l('item_long_description_placeholder'); ?>"></textarea>
               </td>
               <?php echo render_custom_fields_items_table_add_edit_preview(); ?>
               <td>
                  <input type="number" name="quantity" min="0" value="1" class="form-control" placeholder="<?php echo _l('item_quantity_placeholder'); ?>">
                  <input type="text" placeholder="<?php echo _l('unit'); ?>" name="unit" class="form-control input-transparent text-right">
               </td>
               <td>
                  <input type="number" readonly name="rate" class="form-control" placeholder="<?php echo _l('item_rate_placeholder'); ?>">
               </td>
               <td class="text-right discount_table">
                  <input type="hidden" value="0" name="discount_table"><span>0%</span>
               </td>
               <td>
                  <?php
                     $default_tax = unserialize(get_option('default_tax'));
                     $select = '<select disabled class="selectpicker display-block tax main-tax" data-width="100%" name="taxname" multiple data-none-selected-text="'._l('no_tax').'">';
                     $selectedValue = '';
                     foreach($taxes as $tax){
                       $selected = '';
                       $taxName = $tax['name'] . '|' . $tax['taxrate'];
                       if(is_array($default_tax)){
                         if(in_array($taxName,$default_tax)){
                           $selected = ' selected ';
                           $selectedValue = $taxName;
                         }
                       }
                       $select .= '<option value="' . $taxName . '"'.$selected.'data-taxrate="'.$tax['taxrate'].'" data-taxname="'.$tax['name'].'" data-subtext="'.$tax['name'].'">'.$tax['taxrate'].'%</option>';
                     }
                     $select .= '</select>';
                     $select .= '<input name="taxname" type="hidden" value="' . $selectedValue . '" />';
                     echo $select;
                     ?>
               </td>
               <td></td>
               <td>
                  <?php
                     $new_item = 'undefined';
                     if(isset($estimate)){
                       $new_item = true;
                     }
                     ?>
                  <button type="button" onclick="add_item_to_table('undefined','undefined',<?php echo $new_item; ?>); return false;" class="btn pull-right btn-info"><i class="fa fa-check"></i></button>
               </td>
            </tr>
            <?php if (isset($estimate) || isset($add_items)) {
               $i               = 1;
               $items_indicator = 'newitems';
               if (isset($estimate)) {
                 $add_items       = $estimate->items;
                 $items_indicator = 'items';
               }

               foreach ($add_items as $item) {
                 $manual    = false;
                 $table_row = '<tr class="sortable item">';
                 $table_row .= '<td class="dragger">';
                 if ($item['qty'] == '' || $item['qty'] == 0) {
                   $item['qty'] = 1;
                 }
                 if(!isset($is_proposal)){
                  $estimate_item_taxes = get_estimate_item_taxes($item['id']);
                } else {
                  $estimate_item_taxes = get_proposal_item_taxes($item['id']);
                }
                if ($item['id'] == 0) {
                 $estimate_item_taxes = $item['taxname'];
                 $manual              = true;
               }
               $table_row .= form_hidden('' . $items_indicator . '[' . $i . '][itemid]', $item['id']);
               $amount = $item['rate'] * $item['qty'];
               $amount = app_format_number($amount);
               // order input
               $table_row .= '<input type="hidden" class="item_id" name="' . $items_indicator . '[' . $i . '][item_id]" value="'.$item['item_id'].'">';
               $table_row .= '<input type="hidden" class="order" name="' . $items_indicator . '[' . $i . '][order]">';
               $table_row .= '</td>';
               $table_row .= '<td class="bold description"><textarea readonly name="' . $items_indicator . '[' . $i . '][description]" class="form-control" rows="5">' . clear_textarea_breaks($item['description']) . '</textarea></td>';
               $table_row .= '<td><textarea name="' . $items_indicator . '[' . $i . '][long_description]" class="form-control" rows="5">' . clear_textarea_breaks($item['long_description']) . '</textarea></td>';
               $table_row .= render_custom_fields_items_table_in($item,$items_indicator.'['.$i.']');
               $table_row .= '<td><input type="number" min="0" onchange="calculate_total_v2();" data-quantity name="' . $items_indicator . '[' . $i . '][qty]" value="' . $item['qty'] . '" class="form-control">';
               $unit_placeholder = '';
               if(!$item['unit']){
                 $unit_placeholder = _l('unit');
                 $item['unit'] = '';
               }
               $table_row .= '<input type="text" placeholder="'.$unit_placeholder.'" name="'.$items_indicator.'['.$i.'][unit]" class="form-control input-transparent text-right" value="'.$item['unit'].'">';
               $table_row .= '</td>';
               $table_row .= '<td class="rate"><input readonly type="number" data-rate data-toggle="tooltip" title="' . _l('numbers_not_formatted_while_editing') . '" onchange="calculate_total_v2();" name="' . $items_indicator . '[' . $i . '][rate]" value="' . $item['rate'] . '" class="form-control"></td>';
               $discount_table = ($item['discount_table'] ?? 0);
               $table_row .= '<td class="text-right discount_table"><input type="hidden" data-toggle="tooltip" name="' . $items_indicator . '[' . $i . '][discount_table]" value="' . $discount_table . '"><span>' . ($discount_table * 100) . '%</span></td>';
               $table_row .= '<td class="taxrate">' . $this->misc_model->get_taxes_dropdown_template('' . $items_indicator . '[' . $i . '][taxname][]', $estimate_item_taxes, (isset($is_proposal) ? 'proposal' : 'estimate'), $item['id'], false, $manual) . '</td>';
               $table_row .= '<td class="amount" align="right">' . $amount . '</td>';
               $table_row .= '<td><a href="#" class="btn btn-danger pull-left" onclick="delete_item(this,' . $item['id'] . '); return false;"><i class="fa fa-times"></i></a></td>';
               $table_row .= '</tr>';
               echo $table_row;
               $i++;
               }
               }
               ?>
         </tbody>
      </table>
   </div>
   <div class="col-md-8 col-md-offset-4">
      <table class="table text-right">
         <tbody>
            <tr id="subtotal">
               <td><span class="bold"><?php echo _l('estimate_subtotal'); ?> :</span>
               </td>
               <td class="subtotal">
               </td>
            </tr>
            <tr class="discount_percent_row">
               <td>
                  <div class="row">
                     <div class="col-md-7">
                        <span class="bold">
                        <?php echo _l('estimate_discount'); ?>
                     </span>
                     </div>
                     <div class="col-md-5">
                        <div class="input-group">
                           <input type="number" class="form-control" name="discount_percent" id="discount_percent" min="0" max="100" value="<?= isset($estimate) && (!$is_create_option || $estimate->parent_id) ? $estimate->discount_percent : 0 ?>">
                           <div class="input-group-addon">%</div>
                        </div>

                        <input type="number"
                           class="form-control hide" name="discount_total"
                           value="<?= isset($estimate) && (!$is_create_option || $estimate->parent_id) ? $estimate->discount_total : 0 ?>"
                        >
                     </div>
                  </div>
               </td>
               <td class="discount-percent-total"></td>
            </tr>
            <tr id="discount_area">
               <td>
                  <div class="row">
                     <div class="col-md-7">
                        <span class="bold">
                           <?php echo _l('estimate_discount_fixed'); ?>
                        </span>
                     </div>
                     <div class="col-md-5">
                        <div class="input-group">
                           <input type="number"
                              class="form-control"
                              name="discount_fixed"
                              id="discount_fixed"
                              value="<?= isset($estimate) && (!$is_create_option || $estimate->parent_id) ? $estimate->discount_fixed : 0 ?>"
                              min="0"
                           >
                           <div class="input-group-addon"><?= _l('discount_fixed_amount') ?></div>
                        </div>
                     </div>
                  </div>
               </td>
               <td class="discount-fixed-total"></td>
            </tr>
            <tr id="after_tax_revenue">
                <td><span class="bold"><?php echo _l('estimate_dt_table_heading_total_amount'); ?> :</span>
                </td>
                <td class="after_tax_revenue"></td>
            </tr>
            <?php if (isset($estimate->adjustment) && $estimate->adjustment != 0 && !$is_create_option) { ?>
               <tr>
                  <td>
                     <div class="row">
                        <div class="col-md-7">
                           <span class="bold"><?php echo _l('estimate_adjustment'); ?></span>
                        </div>
                        <div class="col-md-5">
                           <input type="number" data-toggle="tooltip" data-title="<?php echo _l('numbers_not_formatted_while_editing'); ?>" value="<?php if(isset($estimate)){echo $estimate->adjustment; } else { echo 0; } ?>" class="form-control pull-left" name="adjustment">
                        </div>
                     </div>
                  </td>
                  <td class="adjustment"></td>
               </tr>
            <?php } ?>
            <tr>
               <td><span class="bold"><?php echo _l('estimate_total'); ?> :</span>
               </td>
               <td class="total">
               </td>
            </tr>
         </tbody>
      </table>
   </div>
   <div id="removed-items"></div>
</div>

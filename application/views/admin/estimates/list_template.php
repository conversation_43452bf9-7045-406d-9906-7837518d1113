<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="col-md-12">
  <div class="panel_s mbot10">
   <div class="panel-body _buttons">
    <?php $this->load->view('admin/estimates/estimates_top_stats');
    ?>
    <?php if(has_permission('estimates','','create')){ ?>
     <a href="<?= admin_url('estimates/estimate'); ?>" class="btn btn-info pull-left new new-estimate-btn"><?= _l('create_new_estimate'); ?></a>
   <?php } ?>
   <a href="<?= admin_url('estimates/pipeline/'.$switch_pipeline); ?>" class="btn btn-default mleft5 pull-left switch-pipeline hidden-xs"><?= _l('switch_to_pipeline'); ?></a>
   <div class="display-block text-right">
     <div class="btn-group pull-right mleft4 btn-with-tooltip-group _filter_data" data-toggle="tooltip" data-title="<?= _l('filter_by'); ?>">
      <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="fa fa-filter" aria-hidden="true"></i>
      </button>
      <ul class="dropdown-menu width300">
       <li>
        <a href="#" data-cview="all" onclick="dt_custom_view('','.table-estimates',''); return false;">
          <?= _l('estimates_list_all'); ?>
        </a>
      </li>
      <li class="divider"></li>
      <li class="<?php if($this->input->get('filter') == 'not_sent'){echo 'active'; } ?>">
        <a href="#" data-cview="not_sent" onclick="dt_custom_view('not_sent','.table-estimates','not_sent'); return false;">
          <?= _l('not_sent_indicator'); ?>
        </a>
      </li>
      <li>
        <a href="#" data-cview="invoiced" onclick="dt_custom_view('invoiced','.table-estimates','invoiced'); return false;">
          <?= _l('estimate_invoiced'); ?>
        </a>
      </li>
      <li>
        <a href="#" data-cview="not_invoiced" onclick="dt_custom_view('not_invoiced','.table-estimates','not_invoiced'); return false;"><?= _l('estimates_not_invoiced'); ?></a>
      </li>
      <?php if(count($estimates_sale_agents) > 0){ ?>
        <div class="clearfix"></div>
        <li class="divider"></li>
        <li class="dropdown-submenu pull-left">
          <a href="#" tabindex="-1"><?= _l('sale_agent_string'); ?></a>
          <ul class="dropdown-menu dropdown-menu-left">
           <?php foreach($estimates_sale_agents as $agent){ ?>
             <li>
              <a href="#" data-cview="sale_agent_<?= $agent['sale_agent']; ?>" onclick="dt_custom_view(<?= $agent['sale_agent']; ?>,'.table-estimates','sale_agent_<?= $agent['sale_agent']; ?>'); return false;"><?= $agent['full_name']; ?>
            </a>
          </li>
        <?php } ?>
      </ul>
    </li>
  <?php } ?>
  <div class="clearfix"></div>
  <?php if(count($estimates_years) > 0){ ?>
    <li class="divider"></li>
    <?php foreach($estimates_years as $year){ ?>
      <li class="active">
        <a href="#" data-cview="year_<?= $year['year']; ?>" onclick="dt_custom_view(<?= $year['year']; ?>,'.table-estimates','year_<?= $year['year']; ?>'); return false;"><?= $year['year']; ?>
      </a>
    </li>
  <?php } ?>
<?php } ?>
</ul>
</div>
<a href="#" class="btn btn-default btn-with-tooltip toggle-small-view hidden-xs" onclick="toggle_small_view('.table-estimates','#estimate'); return false;" data-toggle="tooltip" title="<?= _l('estimates_toggle_table_tooltip'); ?>"><i class="fa fa-angle-double-left"></i></a>
<a href="#" class="btn btn-default btn-with-tooltip estimates-total" onclick="slideToggle('#stats-top'); init_estimates_total(true); return false;" data-toggle="tooltip" title="<?= _l('view_stats_tooltip'); ?>"><i class="fa fa-bar-chart"></i></a>
</div>
</div>
</div>
<div class="row">
  <div class="col-md-12" id="small-table">
    <div class="panel_s">
      <div class="panel-body">
        <div class="row _custom_filters">
          <div class="col-md-2">
              <?= render_select('customer_admins', $staff, ['staffid', ['firstname', 'lastname']], _l('customer_admins'), '', ['onchange' => "reloadDataTable('.table-estimates')"]); ?>
          </div>
          <div class="col-md-2">
              <?= render_select('estimate_creator', $staff, ['staffid', ['firstname', 'lastname']], _l('estimate_creator'), '', ['onchange' => "reloadDataTable('.table-estimates')"]); ?>
          </div>
          <div class="col-md-2">
              <?php
              $estimate_status_options = [];
              foreach ($estimate_statuses as $key => $status) {
                $estimate_status_options[] = [
                    'value' => $status,
                    'text' => format_estimate_status($status, '', false)
                ];
              }
              echo render_select('status', $estimate_status_options, ['value', 'text'], _l('estimate_dt_table_heading_status'), '', ['onchange' => "reloadDataTable('.table-estimates')"]); ?>
          </div>
          <div class="col-md-2">
              <?= render_select('customer_source', get_all_customer_sources(), ['value', 'text'], _l('customer_source'), '', ['onchange' => "reloadDataTable('.table-estimates')"]); ?>
          </div>
          <div class="col-md-2">
              <?= render_select('type_of_customer', get_all_type_of_customers(), ['value', 'value'], _l('type_of_customer'), '', ['onchange' => "reloadDataTable('.table-estimates')"]); ?>
          </div>
          <div class="col-md-2">
              <?= render_select('usage_behavior', get_all_usage_behavior(), ['value', 'value'], _l('usage_behavior'), '', ['onchange' => "reloadDataTable('.table-estimates')"]); ?>
          </div>
          <div class="col-md-2">
              <?= render_select('department', get_list_department(), ['departmentid', 'name'], _l('department') . " (Customer admin's Team)", '', ['onchange' => "reloadDataTable('.table-estimates')"]); ?>
          </div>
          <div class="col-md-4">
              <div id="date-range" class="mbot15">
                <div class="row">
                    <div class="col-md-6">
                      <label for="from_date" class="control-label"><?= _l('from') . ' (' . _l('estimate_dt_table_heading_date') . ')' ?></label>
                      <div class="input-group date">
                          <input type="text" class="form-control datepicker" id="from_date" onchange="reloadDataTable('.table-estimates')" name="from_date" autocomplete="off">
                          <div class="input-group-addon">
                            <i class="fa fa-calendar calendar-icon"></i>
                          </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <label for="to_date" class="control-label"><?= _l('to') . ' (' . _l('estimate_dt_table_heading_date') . ')' ?></label>
                      <div class="input-group date">
                          <input type="text" class="form-control datepicker" id="to_date" onchange="reloadDataTable('.table-estimates')" name="to_date" autocomplete="off">
                          <div class="input-group-addon">
                            <i class="fa fa-calendar calendar-icon"></i>
                          </div>
                      </div>
                    </div>
                </div>
              </div>
          </div>
        </div>
        <!-- if estimateid found in url -->
        <?= form_hidden('estimateid',$estimateid); ?>
        <?php $this->load->view('admin/estimates/table_html'); ?>
      </div>
    </div>
  </div>
  <div class="col-md-7 small-table-right-col">
    <div id="estimate" class="hide">
    </div>
  </div>
</div>
</div>

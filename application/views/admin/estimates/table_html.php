<?php defined('BASEPATH') or exit('No direct script access allowed');

$table_data = [
   [
      'name' => _l('estimate_dt_table_heading_number'),
      'th_attrs' => [
         'style' => 'min-width: 10rem'
      ]
   ],
   _l('estimate_dt_table_heading_total_amount'),
   _l('estimate_dt_table_heading_date'),
   _l('estimate_dt_table_heading_date_flow_up'),
   [
      'name' => _l('estimate_dt_table_heading_client'),
      'th_attrs' => [
         'class' => isset($client) ? 'not_visible' : ''
      ]
   ],
   _l('note'),
   _l('estimate_dt_table_heading_status'),
   _l('product'),
    [
        'name' => _l('discount'),
        'th_attrs' => [
            'style' => 'min-width: 10rem'
        ]
    ],
   _l('estimate_dt_table_heading_customer_admin'),
   _l('customer_source')
];

$hiddenColumn = json_encode(isset($client) ? [4] : [], true);

$custom_fields = get_custom_fields('estimate', ['show_on_table' => 1]);

foreach ($custom_fields as $field) {
   array_push($table_data, $field['name']);
}

$table_data = array_merge($table_data, [
   [
       'name' =>  _l('type_customer'),
       'th_attrs' => [
           'style' => 'min-width: 10rem'
       ]
   ],
   _l('actions'),
]);

$table_data = hooks()->apply_filters('estimates_table_columns', $table_data);

render_datatable($table_data, isset($class) ?  $class : 'estimates');

?>
<script>
   const hiddenColumn = JSON.parse("<?= $hiddenColumn ?>");
   // Formatting function for row details
   function format(data, format) {
      let dataFiltered = Object.entries(data).reduce((result, item) => {
         if (hiddenColumn.includes(parseInt(item[0]))) {
            return result
         }
         return [...result, item[1]]
      }, [])

      let listTd = [...format[0].querySelectorAll("* > td")]
      let newTr = listTd.map(function(td, index) {
         td.style.border = '';
         td.innerHTML = dataFiltered[index];
         return td.outerHTML;
      })
      return newTr.join('');
   }

   function toggle_child_items(aTag, estimateId) {
      const iTag = $(aTag).find('i.collapsed');
      iTag.toggleClass('expand');

      let table = $(aTag).closest('table.dataTable').DataTable()
      let tr = aTag.closest('tr');
      let trClone = $(tr).clone();
      let row = table.row(tr);

      const toggleRow = () => {
         // This row is already open - close it
         if (row.child.isShown()) {
            row.child.hide();
            row.child().fadeOut()
         } else {
            row.child.show();
            row.child().fadeIn();
         }
      };

      if (row.child()) {
         toggleRow();
      } else {
         $(aTag).find('i.fa-spin').removeClass('hide');
         iTag.addClass('hide');

         $.post(admin_url + 'estimates/get_data_related/' + estimateId, {
            draw: 1
         }, function(res) {
            const aaData = res.aaData;
            if (aaData && aaData.length) {
               const newTrList = aaData.map((child) => {
                  const newTr = document.createElement('tr');
                  newTr.className = tr.className;
                  newTr.innerHTML = format(child, trClone);
                  return newTr;
               });

               row.child(newTrList).show();
               row.child().hide().fadeIn();
            }
         }, 'json').always(() => {
            $(aTag).find('i.fa-spin').addClass('hide');
            iTag.removeClass('hide');
         });
      }
      toggleBorderColor(aTag);
   }
</script>
<?php

use app\services\AmsService;
use Entities\ClientAmsCompany;

defined('BASEPATH') or exit('No direct script access allowed');

$this->ci->load->helper('date');

$page = (intval($this->ci->input->post('start')) / 10) + 1;
$queries = array_filter([
    'fields' => [
        'company' => 'id,display_name,email,phone,detail_url,addresses,status,status_display,employer_list,person_in_charge,service_list,created_by'
    ],
    'page' => $page,
    'page_size' => 10,
    'ordering' => 'created_at'
]);

// Custom search value by company id
if ($company = $this->ci->input->post('company')) {
    $queries['ids'] = $company;
} else {
    $amsIds = ClientAmsCompany::select('ams_company_id')->where('client_id', $clientId)->get()->pluck('ams_company_id');
    $queries['ids'] = $amsIds->join(',');
}

$data = [];

if (!empty($queries['ids'])) {
    // Custom search value by status
    if ($status = $this->ci->input->post('status')) {
        $queries['status'] = $status;
    }

    // Custom search value by service
    if ($service = $this->ci->input->post('service')) {
        $queries['service_ids'] = $service;
    }

    $options = [
        'query' => $queries
    ];

    $response = AmsService::search('companies/search', $options);
    $data = $response['data']['data'] ?? [];
}

if (!empty($data)) {
    $rows = [];

    foreach ($data as $company) {
        $employees = $company['employer_list'] ?? [];
        $services = $company['service_list'] ?? [];
        usort($services, fn($s, $ss) => ($s['created_at'] == $ss['created_at'] ? 0 : ($s['created_at'] > $ss['created_at'] ? -1 : 1)));
        $validServices = [];
        foreach ($services as $service) {
            $now = date('Y-m-d H:i:s');
            if ($service['expires_at'] && $service['expires_at'] < $now) continue;
            $diffDays = $service['expires_at'] ? ($now < $service['expires_at'] ? (diff_datetime($now, $service['expires_at'])) : null) : -1;
            $validServices[] = [
                'id' => $service['id'],
                'name' => $service['name'],
                'date' => to_date_format($service['created_at'], 'Y-m-d H:i:s', 'd/m/Y'),
                'expired_in' => $diffDays !== null ? ($diffDays == -1 ? _l('ams_companies_row_service_unlimited') : ($diffDays > 1 ? ($diffDays . ' days') : ($diffDays . ' day'))) : '',
            ];
        }
        $rows[] = [
            // AMS ID
            has_permission('customers', '', 'view_ams_company') ?
                '<a href="'.AMS_URL.'/admin/companies/'.$company['id'].'/edit" target="_blank">'.$company['id'].'</a>' :
                $company['id'],
            // Company
            '<b><a href="'.$company['detail_url'].'" target="_blank">'.$company['display_name'] . '</a></b><br>'.
            '<b>'._l('ams_companies_row_phone').'</b>: ' . $company['phone'] . '<br>' .
            '<b>'._l('ams_companies_row_email').'</b>: ' . $company['email'] . '<br>' .
            '<b>'._l('ams_companies_row_address').'</b>: ' . (!empty($company['addresses']) ? implode('<br>', $company['addresses']['full_addresses'] ?? []) : '') . '<br>' .
            '<a onclick="toggleServiceDetails(this)" href="javascript:;">'._l('ams_companies_row_view_service_detail').' <i class="glyphicon glyphicon-chevron-down expand-btn"></i></a>',
            // Employer
            join('<br><br>', array_map(function ($employer) {
                return join('<br>', array_filter([
                    '<b>'.$employer['email'].'</b>',
                    $employer['lastname'] . ' - ' .$employer['position'],
                    $employer['phone'],
                ]));
            }, $employees)),
            // Additional Information
            '<b>'._l('ams_companies_row_pic').'</b>: ' . join(', ', ($company['person_in_charge'] ?? [])) . '<br>' .
            '<b>'._l('ams_companies_row_create_by').'</b>:<br>' . ($company['created_by'] ?? '') . '<br>',
            // Status
            '<span class="label label-' .($company['status'] == 1 ? 'success' : 'default'). '">' . ($company['status_display'] ?? '') . '</span>',
            // Services
            'services' => $validServices
        ];
    }

    $output = [
        'draw' => $this->ci->input->post('draw') ?? 1,
        'iTotalRecords' => $response['data']['meta']['total'],
        'iTotalDisplayRecords' => $response['data']['meta']['total'],
        'aaData' => $rows
    ];
} else {
    $output = [
        'draw' => $this->ci->input->post('draw') ?? 1,
        'iTotalRecords' => 0,
        'iTotalDisplayRecords' => 0,
        'aaData' => []
    ];
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');

$this->ci->db->query("SET sql_mode = ''");
$this->ci->load->helper(array('client_requests'));
$clienRequestTbl = db_prefix() . 'client_requests';
$clientTbl = db_prefix() . 'clients';

$aColumns = [
    $clienRequestTbl . '.id as id',
    $clientTbl . '.business_name as business_name',
    $clienRequestTbl . '.created_at as created_at'
];

// For sale admin/ leader, need to select created by
if (!is_sales_member()) {
    $aColumns = [
        $clienRequestTbl . '.id as id',
        $clientTbl . '.business_name as business_name',
        'CONCAT(' . db_prefix() . 'staff.firstname, " ", ' . db_prefix() . 'staff.lastname) as created_by',
        $clienRequestTbl . '.created_at as created_at',
        $clienRequestTbl . '.sa_approved_at as sa_approved_at',
        $clienRequestTbl . '.rejected_at as rejected_at',
        $clienRequestTbl . '.approved_at as leader_approved_at',
    ];
}

$additionalSelect = [
    $clienRequestTbl . '.id as id',
    $clienRequestTbl . '.client_id as client_id',
    $clienRequestTbl . '.sale_id as sale_id',
    $clienRequestTbl . '.leader_id as leader_id',
    $clienRequestTbl . '.admin_id as admin_id',
    $clienRequestTbl . '.request_status as request_status',
    $clienRequestTbl . '.sa_note as sa_note',
    $clienRequestTbl . '.leader_note as leader_note',
    $clientTbl . '.source_reason as reason',
    $clientTbl . '.source_reference_link as link',
    'CONCAT(' . db_prefix() . 'files.rel_type, "/",' . db_prefix() . 'files.rel_id, "/", ' . db_prefix() . 'files.file_name) as attachment_url',
];

$sIndexColumn = 'id';
$sTable       = $clienRequestTbl;
$where        = [];
// Add blank where all filter can be stored

$join = [
    'INNER JOIN ' . $clientTbl . ' ON ' . $clientTbl . '.userid =' . $clienRequestTbl . '.client_id',
    'LEFT JOIN ' . db_prefix() . 'files ON ' . $clientTbl . '.userid = ' . db_prefix() . 'files.rel_id AND ' . db_prefix() . 'files.rel_type = "clilent_request"',
];

// For sale admin/ leader, need to select created by
if (!is_sales_member()) {
    $join[] = 'INNER JOIN ' . db_prefix() . 'staff ON ' . db_prefix() . 'staff.staffid =' . $clienRequestTbl . '.sale_id';
}

// Custom search value by company id, name
if ($company = $this->ci->input->post('request_company')) {
    if (is_numeric($company)) {
        $where[] = 'AND ' . $clientTbl . '.userid = ' . $company;
    } else {
        $where[] = 'AND ' . $clientTbl . '.business_name LIKE "%' . $this->ci->db->escape_str($company) . '%"';
    }
}

// Custom search value by request status
if ($status = $this->ci->input->post('client_request_status')) {
    if ($status === 'rejected') {
        $where[] = 'AND ' . $clienRequestTbl . '.request_status IN ("' . CLIENT_REQUEST_STATUS_SA_REJECTED . '", "' . CLIENT_REQUEST_STATUS_LEADER_REJECTED . '")';
    } else if (is_sales_leader() && $status === CLIENT_REQUEST_STATUS_WAITING) { // Only see sa_approved if selected waiting
        $where[] = 'AND ' . $clienRequestTbl . '.request_status = "' . CLIENT_REQUEST_STATUS_SA_APPROVED . '"';
    } else {
        $where[] = 'AND ' . $clienRequestTbl . '.request_status = "' . $status . '"';
    }
}

// If Sale Leader, only see request of team members
if (is_sales_leader()) {
    $this->ci->load->model('roles_model');
    $saleMemeberIds = $this->ci->roles_model->get_sale_members(get_staff_user_id());
    // Include sale leader
    $saleMemeberIds[] = get_staff_user_id();
    $where[] = 'AND ' . $clienRequestTbl . '.sale_id IN (' . join(',', $saleMemeberIds) . ')';

    // Only fetch request that action by sale admin
    $where[] = 'AND ' . $clienRequestTbl . '.admin_id IS NOT NULL';
}

// Sale member can only see they request
if (is_sales_member()) {
    $where[] = 'AND ' . $clienRequestTbl . '.sale_id = ' . get_staff_user_id();
}

if (!is_sales_member()) {
    $created_at_from = $this->ci->input->post('created_at_from');
    $created_at_to = $this->ci->input->post('created_at_to');
    $sa_approved_at_from = $this->ci->input->post('sa_approved_at_from');
    $sa_approved_at_to = $this->ci->input->post('sa_approved_at_to');
    $leader_approved_at_from = $this->ci->input->post('leader_approved_at_from');
    $leader_approved_at_to = $this->ci->input->post('leader_approved_at_to');

    if ($created_at_from && $created_at_to) {
        $dbFormatFrom = date_format(date_create_from_format('d.m.Y', $created_at_from), 'Y-m-d 00:00:00');
        $dbFormatTo = date_format(date_create_from_format('d.m.Y', $created_at_to), 'Y-m-d 23:59:59');
        $where[] = 'AND ' . $clienRequestTbl . '.created_at BETWEEN "'.$dbFormatFrom. '" AND "'.$dbFormatTo.'"';
    }

    if ($sa_approved_at_from && $sa_approved_at_to) {
        $dbFormatFrom = date_format(date_create_from_format('d.m.Y', $sa_approved_at_from), 'Y-m-d 00:00:00');
        $dbFormatTo = date_format(date_create_from_format('d.m.Y', $sa_approved_at_to), 'Y-m-d 23:59:59');
        $where[] = 'AND (' . $clienRequestTbl . '.sa_approved_at BETWEEN "'.$dbFormatFrom. '" AND "' . $dbFormatTo . '")'
            . ' OR (' . $clienRequestTbl . '.rejected_at BETWEEN "'.$dbFormatFrom. '" AND "' . $dbFormatTo . '" AND '.$clienRequestTbl . '.request_status = "'.CLIENT_REQUEST_STATUS_SA_REJECTED.'")';
    }

    if ($leader_approved_at_from && $leader_approved_at_to) {
        $dbFormatFrom = date_format(date_create_from_format('d.m.Y', $leader_approved_at_from), 'Y-m-d 00:00:00');
        $dbFormatTo = date_format(date_create_from_format('d.m.Y', $leader_approved_at_to), 'Y-m-d 23:59:59');
        $where[] = 'AND (' . $clienRequestTbl . '.approved_at BETWEEN "'.$dbFormatFrom. '" AND "' . $dbFormatTo . '")'
            . ' OR (' . $clienRequestTbl . '.rejected_at BETWEEN "'.$dbFormatFrom. '" AND "' . $dbFormatTo . '" AND '.$clienRequestTbl . '.request_status = "'.CLIENT_REQUEST_STATUS_LEADER_REJECTED.'")';
    }
}

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, $additionalSelect);

$output  = $result['output'];
$rResult = $result['rResult'];
$lastCompanyId = '';
$rowColorCls = '';

$clientPhoneCalls = [];
$contactPhoneCalls = [];
$requestTotal = [];
$myRequests = [];

foreach ($rResult as $aRow) {
    $row = [];
    $row[] = $aRow['id'];

    // Company
    $row[] = '<a href="' . admin_url('clients/client/' . $aRow['client_id']) . '">' . $aRow['business_name'] . '</a>';

    // Created by
    if (!is_sales_member()) {
        $row[]  = $aRow['created_by'];
    }

    // Requested Date
    $row[] = _dt($aRow['created_at']);

    // Latest note contact
    $reason = str_replace("\r\n", "<br>", $aRow['reason']);
    if ($aRow['link']) {
        $reason .= '<br><a target="_blank" href="' . maybe_add_http($aRow['link']) . '">' . _l('client_request_row_view_link') . '</a>';
    }
    if ($aRow['attachment_url']) {
        $reason .= '<br><a target="_blank" href="' . site_url('uploads/company/' . $aRow['attachment_url']) . '">' . _l('client_request_row_view_attachment') . '</a>';
    }
    $row[] = $reason;

    
    $approveNote = (!empty($aRow['admin_id']) ? _l('client_request_col_sa_note', str_replace("\n", "<br>", $aRow['sa_note']) ?? '') : '')
        . (!empty($aRow['leader_id']) ? '<br>' .  _l('client_request_col_leader_note', str_replace("\n", "<br>", $aRow['leader_note']) ?? '') : '');

    if ((is_sales_admin() && $aRow['admin_id'] == get_staff_user_id() && in_array($aRow['request_status'], [CLIENT_REQUEST_STATUS_SA_APPROVED, CLIENT_REQUEST_STATUS_SA_REJECTED, CLIENT_REQUEST_STATUS_LEADER_APPROVED, CLIENT_REQUEST_STATUS_LEADER_REJECTED]))
        || (is_sales_leader() && $aRow['leader_id'] == get_staff_user_id() && in_array($aRow['request_status'], [CLIENT_REQUEST_STATUS_LEADER_APPROVED, CLIENT_REQUEST_STATUS_LEADER_REJECTED]))
        ) {
        $approveNote .= '<br><a href="javascript:" data-note="'.htmlspecialchars(is_sales_admin() ? $aRow['sa_note'] : $aRow['leader_note']).'" onclick="editNote('.$aRow['id'].', this)">'._l('client_request_col_edit_note_link').'</a>';
    }

    $row[] = $approveNote;

    // Request Status
    if (is_sales_member()) {
        $row[] = '<button class="btn btn-default">' . _l('client_request_' . $aRow['request_status'] . '_status') . '</button>';
    } else {
        $row[] = client_request_actions($aRow['id'], $aRow['created_by'], $aRow['request_status']);
    }

    if (!is_sales_member()) {
        // Last status action
        $row[] = _l('client_request_' . $aRow['request_status'] . '_status');

        // SA approved/rejected at
        $row[] = $aRow['request_status'] == CLIENT_REQUEST_STATUS_SA_REJECTED ? _dt($aRow['rejected_at']) : _dt($aRow['sa_approved_at']);

        // Leader approved/rejected at
        $row[] = $aRow['request_status'] == CLIENT_REQUEST_STATUS_LEADER_REJECTED ? _dt($aRow['rejected_at']) : _dt($aRow['leader_approved_at']);
    }

    $output['aaData'][] = $row;
}

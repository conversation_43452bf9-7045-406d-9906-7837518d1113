<?php

use Carbon\Carbon;
use Entities\M3cCall;
use Entities\Staff;

defined('BASEPATH') or exit('No direct script access allowed');

$sortFilterCols = [
    '3c_call.call_id',
    '',
    '',
    '', // Empty column for search/sort
    '',
    '3c_call.agent_id',
    '', // Empty column for search/sort
    '3c_call.start_time',
    '3c_call.end_time',
    '3c_call.call_type',
    '3c_call.call_status',
    '3c_call.talk_time',
];
$selects = [
    '3c_call.call_id',
    '3c_call.path',
    '3c_call.caller',
    '3c_call.called',
    '3c_call.agent_id',
    '3c_call.start_time',
    '3c_call.end_time',
    '3c_call.call_type',
    '3c_call.call_status',
    '3c_call.talk_time',
    '3c_call.staff_id',
    '3c_call.vendor',
    '3c_call.transcripts',
    '3c_call.transcript_synced',
];

$query = M3cCall::select($selects)
    ->whereHas(
        'staff.departments',
        fn($query) => $query->salesOnly()
    );

$staffId = get_staff_user_id();
if (is_b2b_sale()) {
    $query->where('staff_id', $staffId);
}

// Add blank where all filter can be stored
$custom_date_select = '';
$request = $this->ci->input->post();

$dates = explode(' - ', $request['dates']);
$from_date = count($dates) == 2 ? Carbon::createFromFormat('Y-m-d', $dates[0])->startOfDay()->format('Y-m-d H:i:s') : null;
$to_date = count($dates) == 2 ? Carbon::createFromFormat('Y-m-d', $dates[1])->endOfDay()->format('Y-m-d H:i:s') : null;
$customer_admins = $request['customer_admins'] ?? null;
$client_id = $request['clientid'];
$status = $request['status'];
$callType = $request['call_type'];

// Eager load
$query->with([
    'staff'  => function ($builder) use ($customer_admins) {
        $builder->select('staffid', 'firstname', 'lastname')
            ->when($customer_admins, fn($qr) => $qr->where('staff.staffid', $customer_admins)->active());
    },
    'contact' => function ($builder) use ($client_id) {
        $builder->select('id', 'userid', 'phonenumber', 'fullname')
            ->with('client:userid,phonenumber,business_name')
            ->when($client_id, function ($query) use ($client_id) {
                $query->where(function ($subQuery) use ($client_id) {
                    $subQuery->whereHas('client', fn($qr) => $qr->where('clients.userid', $client_id))
                        ->orWhereHas('client.contacts', fn($qr) => $qr->where('clients.userid', $client_id));
                });
            });
    },
    'client' => function ($builder) use ($client_id) {
        $builder->select('userid', 'phonenumber', 'business_name')
            ->when($client_id, fn($qr) => $qr->where('clients.userid', $client_id));
    }
])
    ->when($customer_admins, function ($query) use ($customer_admins) {
        $query->whereHas('staff', fn($qr) => $qr->where('staff.staffid', $customer_admins));
    })
    ->when($client_id, function ($query) use ($client_id) {
        $query->where(function ($subQuery) use ($client_id) {
            $subQuery->whereHas('client', fn($qr) => $qr->where('clients.userid', $client_id))
                ->orWhereHas('contact.client', fn($qr) => $qr->where('clients.userid', $client_id));
        });
    })
    ->when($status, fn($qr) => $qr->where('3c_call.call_status', $status))
    ->when($callType !== '', fn($qr) => $qr->where('3c_call.call_type', $callType == 1 ? M3cCall::CALL_OUT_TYPE : M3cCall::CALL_IN_TYPE))
    ->when($from_date, fn($qr) => $qr->whereBetween('3c_call.start_time', [
        $from_date,
        $to_date,
    ]));

$result = data_tables_eloquent_init($query, $sortFilterCols);

$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $key => $aRow) {
    $row = [];

    $row[] = $aRow['call_id'];
    if ($aRow['vendor'] === M3cCall::CALL_LOG_VENDOR_CALLIO && $aRow['talk_time'] !== '00:00:00') {
        $row[] = implode("<br>", array_merge([
            '<a href="' . admin_url('call_center_logs/voice_record/' . $aRow['call_id']) . '" target="_blank">Link</a>',

        ], $aRow['transcript_synced'] ? ['', '<a href="#" onclick="viewTranscript(\'' . implode(', ', $aRow->transcripts) . '\')" data-toggle="modal" data-target="#view_transcript">Voice AI</a>'] : []));
    } else {
        $row[] = $aRow['path'] ? '<a href="' . $aRow['path'] . '" target="_blank">Link</a>' : '';
    }

    $company = $aRow->client ? $aRow->client->business_name : ($aRow->contact && $aRow->contact->client ? $aRow->contact->client->business_name : '');
    $companyId = $aRow->client ? $aRow->client->userid : ($aRow->contact && $aRow->contact->client ? $aRow->contact->client->userid : '');

    $row[] = $companyId ? '<a href="' . admin_url('clients/client/' . $companyId) . '">' . $company . '</a><br>' : '';
    $row[] = $aRow->contact ? '<a href="' . admin_url('clients/client/' . $companyId . '?contactid=' . $aRow->contact->id) . '" target="_blank">' . $aRow->contact->fullname . '</a>' : '';
    $row[] = $aRow['call_type'] == 1 ? $aRow['called'] : $aRow['caller'];
    $row[] = $aRow['agent_id'];
    $row[] = $aRow->staff ? '<a href=" ' . admin_url('profile/' . $aRow->staff->staffid) . '">' . $aRow->staff->full_name . '</a>' : '';
    $row[] = $aRow['start_time'];
    $row[] = $aRow['end_time'];
    $row[] = $aRow['call_type_text'];
    $row[] = $aRow['call_status'];
    $row[] = $aRow['talk_time'];

    $output['aaData'][] = $row;
}

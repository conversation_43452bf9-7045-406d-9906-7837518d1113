<?php

use Carbon\Carbon;
use Entities\ClientPayer;

defined('BASEPATH') or exit('No direct script access allowed');

/*
* Output
*/
$CI          = &get_instance();
$post      = $CI->input->post();

$month = $post['trans_month'] ?? null;

if (empty($month) || !defined('IS_PAYMENT_ENABLED') || false === IS_PAYMENT_ENABLED) {
    $output = [
        'draw'                 => $post['draw'] ? intval($post['draw']) : 0,
        'iTotalRecords'        => 0,
        'iTotalDisplayRecords' => 0,
        'aaData'               => [],
    ];
} else {
    $records = get_infoplus_transactions($month);
    $ecollectionCds = $records->pluck('ecollectionCd')->unique();
    $payers = ClientPayer::whereIn('e_collection_code', $ecollectionCds)
        ->with([
            'client:userid,business_name'
        ])
        ->get()
        ->keyBy('e_collection_code');
    $total = count($records);
    $output = [
        'draw'                 => $post['draw'] ? intval($post['draw']) : 0,
        'iTotalRecords'        => $total,
        'iTotalDisplayRecords' => $total,
        'aaData'               => $records->sortByDesc(fn($trans) => $trans['depositDt'] . $trans['depositTm'])
            ->values()
            ->map(function ($tran) use ($payers) {
                $payer = $payers->get($tran['ecollectionCd']);
                return [
                    Carbon::createFromFormat('YmdHis', $tran['depositDt'] . $tran['depositTm'])->format('Y-m-d H:i:s'),
                    isset($payer) && isset($payer->client)
                        ? '<a href=" ' . admin_url('clients/client/' . $payer->client->userid) . '" target="_blank">' . $payer->client->business_name . '</a>'
                        : '',
                    $tran['ecollectionCd'] ?? '',
                    number_format($tran['depositAmt']),
                    $tran['depositorNm'],
                ];
            })
    ];
}

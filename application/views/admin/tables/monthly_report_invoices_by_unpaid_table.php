<?php

defined('BASEPATH') or exit('No direct script access allowed');

/*
* Output
*/
$CI          = &get_instance();
$post      = $CI->input->post();

$month = $post['po_created_month'] ?? null;
$output = [
    'draw'                 => $post['draw'] ? intval($post['draw']) : 0,
    'iTotalRecords'        => 0,
    'iTotalDisplayRecords' => 0,
    'aaData'               => [],
];
if (!empty($month)) {
    $sortFilterCols = [
        '',
        '',
        '',
        '',
        '',
    ];

    $selects = [
        'id',
        'number',
        'number_format',
        'prefix',
        'date',
        'invoice_closing_date',
        'clientid',
        'total',
        'status',
        'total_tax',
    ];

    $query = get_unpaid_po_query($month);

    $countQuery = $query;
    $countQuery = $countQuery->selectRaw('COUNT(id) as po_quantity, SUM(total - total_tax) as po_amount')->first();

    $query->select($selects)
        ->orderByDesc('invoice_closing_date');

    $result = data_tables_eloquent_init($query, $sortFilterCols);

    $output  = $result['output'];
    $rResult = $result['rResult'];

    foreach ($rResult as $key => $aRow) {
        $row = [];
        $row[] = optional($aRow['invoice_closing_date'])->format('Y-m-d H:i:s');
        $row[] = '<a href="' . admin_url('invoices#' . $aRow->id) . '" target="_blank">' . format_invoice_number($aRow) . '</a>';
        $row[] = '<a href="' . admin_url('clients/client/' . $aRow->client->userid) . '" target="_blank">' . $aRow->client->business_name . '</a>';
        $row[] = format_invoice_status($aRow->status);
        $row[] = number_format($aRow->total - $aRow->total_tax);
        $output['aaData'][] = $row;
    }
    $output['amount'] = $countQuery;
}

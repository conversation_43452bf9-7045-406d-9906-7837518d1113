<?php

defined('BASEPATH') or exit('No direct script access allowed');

$this->ci->load->model('gdpr_model');

$consentContacts = get_option('gdpr_enable_consent_for_contacts');
if (is_gdpr() && $consentContacts == '1') {
    array_push($aColumns, '1');
}
$aColumns = [
    db_prefix() . 'contacts.id as id',
    'clients.business_name as company',
    'fullname',
    db_prefix() . 'contacts.email as email',
    'title',
    db_prefix() . 'contacts.linkedin as linkedin',
    db_prefix() . 'contacts.zalo as zalo',
    db_prefix() . 'contacts.skype as skype',
    db_prefix() . 'contacts.landline as landline',
    db_prefix() . 'contacts.other as other',
    db_prefix() . 'contacts.phonenumber as phonenumber',
    db_prefix() . 'contacts.active as active',
    db_prefix() . 'contacts.last_login as last_login',
    'status',
    'review_status',
    db_prefix() . 'contacts.datecreated as datecreated',
    db_prefix() . 'contacts.updated_at as updated_at',
    'creator.staffid as creator_id',
    'CONCAT(creator.firstname, " ", creator.lastname) as creator_fullname',
];

$request = $this->ci->input->post();
$sIndexColumn = 'id';
$sGroupBy = 'GROUP BY id';
$sTable = db_prefix() . 'contacts';
$join = [];
$where = [];

$join[] = 'JOIN ' . db_prefix() . 'clients clients ON clients.userid = ' . db_prefix() . 'contacts.userid';
$join[] = 'LEFT JOIN (SELECT rel_id, staffid FROM ' . db_prefix() . 'activity_log WHERE rel_type = "contact" AND description LIKE "%created%" GROUP BY rel_id) creator_log
    ON ' . db_prefix() . 'contacts.id = creator_log.rel_id
    LEFT JOIN ' . db_prefix() . 'staff creator ON creator.staffid = creator_log.staffid';


$review_status = $request['review_status'];
if ($review_status !== '') {
    $where[] = 'AND ' . db_prefix() . 'contacts.review_status = ' . $review_status;
}
if (!has_permission('contacts', '', 'view')) {
    $where[] = 'AND creator.staffid = ' . get_staff_user_id();
}

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [db_prefix() . 'contacts.id as id', db_prefix() . 'contacts.userid', 'is_primary'], $sGroupBy);

$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];

    $row[] = '<a href="' . admin_url('clients/client/' . $aRow['userid'] . '?group=contacts') . '">' . $aRow['company'] . '</a>';

    $row[] = '<a href="' . admin_url('clients/client/' . $aRow['userid'] . '?contactid=' . $aRow['id']) . '" target="_blank">' . $aRow['fullname'] . '</a>';

    $row[] = $aRow['title'];

    $row[] = '<ul>
        <li>' . _l('client_phonenumber') . ': <a href="tel:' . $aRow['phonenumber'] . '">' . $aRow['phonenumber'] . '</a></li>
        <li>' . _l('client_email') . ': <a href="mailto:' . $aRow['email'] . '">' . $aRow['email'] . '</a></li>
        <li>' . _l('linkedin') . ': ' . $aRow['linkedin'] . '</li>
        <li>' . _l('zalo') . ': ' . $aRow['zalo'] . '</li>
        <li>' . _l('skype') . ': ' . $aRow['skype'] . '</li>
        <li>' . _l('landline') . ': ' . $aRow['landline'] . '</li>
        <li>' . _l('other') . ': ' . $aRow['other'] . '</li>
    </ul>';

    $row[] = '<a href="' . admin_url('profile/' . $aRow['creator_id']) . '">' . $aRow['creator_fullname'] . '</a>';
    $row[] = _dt($aRow['datecreated']);

    $options = '';
    foreach (CONTACT_REVIEW_STATUS_OPTIONS as $value) {
        $options .= '<option value="' . $value['value'] . '" ' . ($aRow['review_status'] == $value['value'] ? 'selected' : "") . '>' . $value['text'] . '</option>';
    }
    $row[] = '
    <div class="form-group">
        <select class="form-control" id="review_status_' . $aRow['id'] . '" onchange="updateStatusContact(' . $aRow['userid'] . ',' . $aRow['id'] . ');" ' . ($aRow['review_status'] != 0 || !has_permission('contacts', '', 'review') ? 'disabled' : '') . ' >' . $options  . '</select>
    </div>';

    $row['DT_RowClass'] = 'has-row-options';
    $output['aaData'][] = $row;
}

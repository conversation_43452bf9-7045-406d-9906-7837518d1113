<?php

defined('BASEPATH') or exit('No direct script access allowed');

$hasPermissionDelete = has_permission('payments', '', 'delete');

$aColumns = [
    db_prefix() . 'invoicepaymentrecords.id as id',
    'invoiceid',
    'paymentmode',
    'transactionid',
    get_sql_select_client_company(),
    'amount',
    db_prefix() . 'invoicepaymentrecords.date as date',
    db_prefix() . 'sales_activity.full_name as creator',
    'concat (customer_admin_of_payment.firstname, " ", customer_admin_of_payment.lastname) as customer_admin_of_payment',
    'concat (customer_admin_of_client.firstname, " ", customer_admin_of_client.lastname) as customer_admin_of_client_name',
    'customer_admin_of_client.staffid as customer_admin_of_client_id',
    db_prefix() . 'invoices.status as invoice_status',
    db_prefix() . 'invoices.total as invoice_total',
    db_prefix() . 'invoices.total_tax as invoice_total_tax',
];

$join = [
    'LEFT JOIN ' . db_prefix() . 'invoices ON ' . db_prefix() . 'invoices.id = ' . db_prefix() . 'invoicepaymentrecords.invoiceid',
    'LEFT JOIN ' . db_prefix() . 'clients ON ' . db_prefix() . 'clients.userid = ' . db_prefix() . 'invoices.clientid',
    'LEFT JOIN ' . db_prefix() . 'currencies ON ' . db_prefix() . 'currencies.id = ' . db_prefix() . 'invoices.currency',
    'LEFT JOIN ' . db_prefix() . 'payment_modes ON ' . db_prefix() . 'payment_modes.id = ' . db_prefix() . 'invoicepaymentrecords.paymentmode',
    'LEFT JOIN ' . db_prefix() . 'customer_admins ON ' . db_prefix() . 'customer_admins.customer_id = ' . db_prefix() . 'clients.userid',
    'LEFT JOIN ' . db_prefix() . 'sales_activity ON ' . db_prefix() . 'sales_activity.rel_id = ' . db_prefix() . 'invoicepaymentrecords.invoiceid and ' . db_prefix() . 'sales_activity.description like "invoice_activity_payment_made_by_staff"',
    'LEFT JOIN ' . db_prefix() . 'staff customer_admin_of_payment ON customer_admin_of_payment.staffid = ' . db_prefix() . 'sales_activity.customer_admin_id',
    'LEFT JOIN ' . db_prefix() . 'staff customer_admin_of_client ON customer_admin_of_client.staffid = ' . db_prefix() . 'customer_admins.staff_id'
];

$where = [];
$filter = [];

$custom_date_select = '';
$request = $this->ci->input->post();

$department = $request['department'] ?? '';
$from_date = $request['from_date'] ?? '';
$to_date = $request['to_date'] ?? '';

if ($clientid != '') {
    array_push($where, 'AND ' . db_prefix() . 'clients.userid=' . $this->ci->db->escape_str($clientid));
}

if (!has_permission('payments', '', 'view')) {
    array_push($where, 'AND ' . db_prefix() . 'invoices.clientid IN('. permission_get_clients('payments') .')');
}

if ($department) {
    array_push($join, 'JOIN ' . db_prefix() . 'staff_departments ON ' . db_prefix() . 'staff_departments.staffid=' . db_prefix() . 'customer_admins.staff_id 
    AND ' . db_prefix() . 'staff_departments.departmentid = ' . $department);
}

if ($from_date) {
    $custom_date_select = get_query_filter_date($from_date, $to_date, db_prefix() . 'invoicepaymentrecords.date');
}

if ($custom_date_select) {
    array_push($filter, $custom_date_select);
}

if (count($filter) > 0) {
    array_push($where, 'AND (' . prepare_dt_filter($filter) . ')');
}

$sIndexColumn = 'id';
$sTable       = db_prefix() . 'invoicepaymentrecords';

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [
    'clientid',
    db_prefix() . 'currencies.name as currency_name',
    db_prefix() . 'payment_modes.name as payment_mode_name',
    db_prefix() . 'payment_modes.id as paymentmodeid',
    'paymentmethod',
]);

$output  = $result['output'];
$rResult = $result['rResult'];

$this->ci->load->model('payment_modes_model');
$payment_gateways = $this->ci->payment_modes_model->get_payment_gateways(true);

foreach ($rResult as $aRow) {
    $row = [];

    $link = admin_url('payments/payment/' . $aRow['id']);


    $options = icon_btn('payments/payment/' . $aRow['id'], 'pencil-square-o');

    if ($hasPermissionDelete) {
        $options .= icon_btn('payments/delete/' . $aRow['id'], 'remove', 'btn-danger _delete');
    }

    $numberOutput = '<a href="' . $link . '">' . $aRow['id'] . '</a>';

    $numberOutput .= '<div class="row-options">';
    $numberOutput .= '<a href="' . $link . '">' . _l('view') . '</a>';
    if ($hasPermissionDelete) {
        $numberOutput .= ' | <a href="' . admin_url('payments/delete/' . $aRow['id']) . '" class="text-danger _delete">' . _l('delete') . '</a>';
    }
    $numberOutput .= '</div>';

    $row[] = $numberOutput;

    $row[] = '<a href="' . admin_url('invoices/list_invoices/' . $aRow['invoiceid']) . '">' . format_invoice_number($aRow['invoiceid']) . '</a>';

    $outputPaymentMode = $aRow['payment_mode_name'];

    // Since version 1.0.1
    if (is_null($aRow['paymentmodeid'])) {
        foreach ($payment_gateways as $gateway) {
            if ($aRow['paymentmode'] == $gateway['id']) {
                $outputPaymentMode = $gateway['name'];
            }
        }
    }

    if (!empty($aRow['paymentmethod'])) {
        $outputPaymentMode .= ' - ' . $aRow['paymentmethod'];
    }
    $row[] = $outputPaymentMode;

    $row[] = $aRow['transactionid'];

    $row[] = '<a href="' . admin_url('clients/client/' . $aRow['clientid']) . '">' . $aRow['company'] . '</a>';

    $row[] = $aRow['customer_admin_of_client_id'] ? '<a href=" ' . admin_url('profile/' . $aRow['customer_admin_of_client_id']) . '">' . $aRow['customer_admin_of_client_name'] . '</a>' : '';

    $row[] = $aRow['creator'] . ($aRow['customer_admin_of_payment'] ? ' (' . $aRow['customer_admin_of_payment'] . ')' : '');

    $row[] = format_invoice_status($aRow['invoice_status']);

    $row[] = $aRow['invoice_status'] == 2 ? app_format_money($aRow['invoice_total'] - $aRow['invoice_total_tax'], $aRow['currency_name']) : '';

    $row[] = app_format_money($aRow['amount'], $aRow['currency_name']);

    $row[] = _d($aRow['date']);

    $row['DT_RowClass'] = 'has-row-options';

    $output['aaData'][] = $row;
}

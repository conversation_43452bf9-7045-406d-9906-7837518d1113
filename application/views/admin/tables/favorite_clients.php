<?php

defined('BASEPATH') or exit('No direct script access allowed');

$this->ci->db->query("SET sql_mode = ''");

$aColumns = array_merge(
    [
        'tblclients.userid as company_id',
        'tblclients.business_name as company_name',
        'concat(tblstaff.firstname, " ", tblstaff.lastname) as customer_admin',
        'tblfavorite_clients.target as target',
        'tblfavorite_clients.created_at as created_at'
    ]
);

$sIndexColumn = 'id';
$sTable       = db_prefix() . 'favorite_clients';
$where        = [];
$sGroupBy     = '';

// Add blank where all filter can be stored
$filter = [];
$custom_date_select = '';
$request = $this->ci->input->post();

$from_date = $request['plan_from_date'];
$to_date = $request['plan_to_date'];
$customer_admins = $request['plan_customer_admins'];
$department = $request['plan_department'];

$join = [
    'JOIN ' . db_prefix() . 'clients ON ' . db_prefix() . 'favorite_clients.client_id =' . db_prefix() . 'clients.userid',
    'JOIN ' . db_prefix() . 'staff ON ' . db_prefix() . 'staff.staffid =' . db_prefix() . 'favorite_clients.staff_id',
];

if (!is_leader_member()) {
    array_push($where, 'AND ' . db_prefix() . 'favorite_clients.staff_id = ' . get_staff_user_id());
}

if ($customer_admins) {
    array_push($filter, 'AND ' . db_prefix() . 'favorite_clients.staff_id = ' .  $customer_admins);
}

if ($department) {
    array_push($join, 'JOIN ' . db_prefix() . 'staff_departments ON ' . db_prefix() . 'staff_departments.staffid=' . db_prefix() . 'favorite_clients.staff_id 
    AND ' . db_prefix() . 'staff_departments.departmentid = ' . $department);
}

if ($from_date) {
    $custom_date_select = get_query_filter_date($from_date, $to_date, db_prefix() . 'favorite_clients.created_at');
}

if ($custom_date_select) {
    array_push($filter, 'AND ' . $custom_date_select);
}

if (count($filter) > 0) {
    array_push($where, 'AND (' . prepare_dt_filter($filter) . ')');
}

array_push($where, 'AND (' . db_prefix() . 'favorite_clients.status = 1)');

$sGroupBy = '';

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [], $sGroupBy);

$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];

    $row[] = $aRow['company_id'];
    $row[] = $aRow['company_name'];
    $row[] = $aRow['customer_admin'];
    $row[] = $aRow['target'];
    $row[] = $aRow['created_at'];

    $output['aaData'][] = $row;
}

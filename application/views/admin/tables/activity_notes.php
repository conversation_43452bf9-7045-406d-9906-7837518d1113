<?php

defined('BASEPATH') or exit('No direct script access allowed');

$this->ci->db->query("SET sql_mode = ''");

$aColumns = array_merge(
    [
        db_prefix() . 'notes.dateadded as noted_at',
        db_prefix() . 'clients.userid as company_id',
        db_prefix() . 'clients.business_name as company_name',
        'type_of_customer',
        'usage_behavior',
        db_prefix() . 'contacts.email as contact_email',
        db_prefix() . 'contacts.phonenumber as contact_phone',
        'concat(' . db_prefix() . 'staff.firstname, " ", ' . db_prefix() .  'staff.lastname) as customer_admin',
        'concat(staff_note.firstname, " ", staff_note.lastname) as note_creator',
        db_prefix() . 'notes.description as note_detail',
        '1 as latest_feedback',
        '1 as call_duration',
        db_prefix() . 'customfieldsvalues.value as Hotline',
        db_prefix() . 'notes.potential_rate as potential_rate',
        '(select case when ' . db_prefix() . 'favorite_clients.client_id is not null then "Yes" else "No" end) as add_to_favorite'
    ]
);

$sIndexColumn = 'id';
$sTable = db_prefix() . 'notes';
$where = [];
$sGroupBy = '';

// Add blank where all filter can be stored
$filter = [];
$custom_date_select = '';
$request = $this->ci->input->post();

$from_date = $request['from_date'];
$to_date = $request['to_date'];
$customer_admins = $request['customer_admins'];
$department = $request['department'];
$note_creator = $request['note_creator'];
$potential_rate = $request['potential_rate'];
$hotline = $request['hotline'];
$type = $request['type'];
$contact_channel = $request['contact_channel'];

$join = [
    'LEFT JOIN ' . db_prefix() . 'clients ON ' . db_prefix() . 'clients.userid =' . db_prefix() . 'notes.rel_id AND ' . db_prefix() . 'notes.rel_type = "customer"',
    'LEFT JOIN ' . db_prefix() . 'customer_admins ON ' . db_prefix() . 'customer_admins.customer_id = ' . db_prefix() . 'clients.userid',
    'LEFT JOIN ' . db_prefix() . 'staff ON ' . db_prefix() . 'staff.staffid = ' . db_prefix() . 'customer_admins.staff_id',
    'LEFT JOIN ' . db_prefix() . 'contacts ON ' . db_prefix() . 'contacts.id =' . db_prefix() . 'notes.contact_id',
    'LEFT JOIN ' . db_prefix() . 'favorite_clients ON ' . db_prefix() . 'favorite_clients.client_id =' . db_prefix() . 'clients.userid AND DATE(' . db_prefix() . 'notes.dateadded) = DATE(' . db_prefix() . 'favorite_clients.created_at)',
    'LEFT JOIN ' . db_prefix() . 'staff as staff_note ON staff_note.staffid =' . db_prefix() . 'notes.addedfrom',
    'LEFT JOIN ' . db_prefix() . 'customfieldsvalues ON ' . db_prefix() . 'customfieldsvalues.relid =' . db_prefix() . 'clients.userid AND ' . db_prefix() . 'customfieldsvalues.fieldto = "customers" AND ' . db_prefix() . 'customfieldsvalues.fieldid = 24',
];

if (!is_leader_member()) {
    array_push($where, 'AND ' . db_prefix() . 'notes.addedfrom = ' . get_staff_user_id());
}

if ($customer_admins) {
    array_push($filter, 'AND ' . db_prefix() . 'customer_admins.staff_id = ' .  $customer_admins);
}

if ($department) {
    array_push($join, 'JOIN ' . db_prefix() . 'staff_departments ON ' . db_prefix() . 'staff_departments.staffid=' . db_prefix() . 'customer_admins.staff_id 
    AND ' . db_prefix() . 'staff_departments.departmentid = ' . $department);
}

if ($note_creator) {
    array_push($filter, 'AND ' . db_prefix() . 'notes.addedfrom = ' .  $note_creator);
}

if ($potential_rate) {
    array_push($filter, 'AND ' . db_prefix() . 'notes.potential_rate = "' .  $potential_rate . '"');
}

if ($hotline) {
    if ($hotline == 'yes') {
        array_push($filter, 'AND ' . db_prefix() . 'customfieldsvalues.value != ""');
    } else {
        array_push($filter, 'AND ' . db_prefix() . 'customfieldsvalues.value = "" OR ' . db_prefix() . 'customfieldsvalues.value is null');
    }
}

if ($type !== '') {
    array_push($filter, 'AND ' . db_prefix() . 'notes.type = ' .  $type);
}

if ($contact_channel !== '') {
    array_push($filter, 'AND ' . db_prefix() . 'notes.contact_channel = ' .  $contact_channel);
}

if ($from_date) {
    $custom_date_select = get_query_filter_date($from_date, $to_date, db_prefix() . 'notes.dateadded');
}

if ($custom_date_select) {
    array_push($filter, 'AND ' . $custom_date_select);
}

if (count($filter) > 0) {
    array_push($where, 'AND (' . prepare_dt_filter($filter) . ')');
}

$sGroupBy = '';

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [], $sGroupBy);

$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $key => $aRow) {
    $row = [];

    $row[] = $aRow['noted_at'];
    $row[] = $aRow['company_id'];
    $row[] = $aRow['company_name'];
    $row[] = $aRow['type_of_customer'];
    $row[] = $aRow['usage_behavior'];
    $row[] = ($aRow['contact_email'] ? "Email: {$aRow['contact_email']}" : "") . ($aRow['contact_phone'] ? "<br> Phone: {$aRow['contact_phone']}" : "");
    $row[] = $aRow['customer_admin'];
    $row[] = $aRow['note_creator'];
    $row[] = $aRow['note_detail'];
    // $row[] = $aRow['latest_feedback'];
    // $row[] = $aRow['call_duration'];
    $row[] = '';
    $row[] = '';
    $row[] = $aRow['Hotline'];
    $row[] = $aRow['potential_rate'];
    $row[] = $aRow['add_to_favorite'];

    $output['aaData'][] = $row;
}

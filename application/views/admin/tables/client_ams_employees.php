<?php

use app\services\AmsService;
use Entities\ClientAmsCompany;

defined('BASEPATH') or exit('No direct script access allowed');

$output = [
    'draw' => $this->ci->input->post('draw') ?? 1,
    'iTotalRecords' => 0,
    'iTotalDisplayRecords' => 0,
    'aaData' => [],
];

$page = (intval($this->ci->input->post('start')) / 10) + 1;
// Fetch ams companies first
$queries = array_filter([
    'fields' => [
        'company' => 'id,display_name,detail_url'
    ],
    'page' => $page,
    'page_size' => 10,
    'ordering' => 'created_at'
]);

// Custom search value by company id
if ($company = $this->ci->input->post('company')) {
    $queries['ids'] = $company;
} else {
    $amsIds = ClientAmsCompany::select('ams_company_id')->where('client_id', $clientId)->get()->pluck('ams_company_id');
    $queries['ids'] = $amsIds->join(',');
}
$companies = [];

if (!empty($queries['ids'])) {
    $options = [
        'query' => $queries
    ];
    $res = AmsService::search('companies/search', $options);
    $companies = $res['data']['data'] ?? [];
}

if (!empty($companies)) {
    $collect = collect($companies);
    $foundIds = $collect->pluck('id');
    
    // Fetch ams employees
    $queries = array_filter([
        'page' => 1,
        'companies' => $foundIds->join(','),
        'page_size' => count($foundIds) * 100,
        'ordering' => 'created_at'
    ]);

    // Add search value by employer id
    if ($employer = $this->ci->input->post('employer')) {
        $queries['ids'] = $employer;
    }
    $options = [
        'query' => $queries
    ];
    $response = AmsService::search('employees', $options);
    $employees = $response['data'] ?? [];

    if (!empty($companies)) {
        $empCollect = collect($employees)->groupBy('company_id');
        $rows = [];
        $collect->each(function ($company) use (&$rows, $empCollect) {
            $employees = $empCollect->get($company['id'], collect())->toArray();

            // Next are employees if have
            if (count($employees)) {
                foreach ($employees as $employer) {
                    $row = [];

                    // Company
                    $row[] = '<b><a href="'.$company['detail_url'].'" target="_blank">'.$company['display_name'] . '</a></b>';

                    // AMS ID
                    $row[] = has_permission('customers', '', 'view_ams_company') ?
                        '<a href="'.AMS_URL.'/admin/employers/'.$employer['id'].'/edit" target="_blank">'.$company['id'].'</a>' :
                        $company['id'];

                    // Account
                    $row[] = '<b>'._l('ams_employees_row_user_name').'</b>: ' . $employer['username'];

                    // Detail
                    $row[] = '<b>'._l('ams_employees_row_email').'</b>: ' . $employer['email'] . '<br>'.
                        '<b>'._l('ams_employees_row_phone').'</b>: ' . $employer['phone'] . '<br>'.
                        '<b>'._l('ams_employees_row_lastname').'</b>: ' . $employer['lastname'] . '<br>'.
                        '<b>'._l('ams_employees_row_position').'</b>: ' . $employer['position'];

                    // Approved at
                    $row[] = !empty($employer['approved_at']) ? date('Y-m-d H:i:s', strtotime($employer['approved_at'])) : '';

                    $rows[] = $row;
                }
            }
        });
        $output = [
            'draw' => $this->ci->input->post('draw') ?? 1,
            'iTotalRecords' => $res['data']['meta']['total'],
            'iTotalDisplayRecords' => $res['data']['meta']['total'],
            'aaData' => $rows,
        ];
    }
}

<?php

use Carbon\Carbon;
use Entities\Staff;

defined('BASEPATH') or exit('No direct script access allowed');

$project_id = $this->ci->input->post('project_id');

$aColumns = [
    db_prefix() . 'invoices.id',
    '(' .db_prefix() . 'invoices.total' . ' - ' . db_prefix() . 'invoices.total_tax) as invoices_total_amount',
    'YEAR('. db_prefix() . 'invoices.date) as year',
    db_prefix() . 'invoices.date as date',
    db_prefix() . 'invoices.clientid',
    db_prefix() . 'estimates.deleted_customer_name as deleted_customer_name',
    db_prefix() . 'contacts.fullname as contact_name',
    db_prefix() . 'clients.vat as vat',
    'duedate',
    db_prefix() . 'invoicepaymentrecords.date as date_of_payment',
    db_prefix() . 'invoices.status',
    db_prefix() . 'clients.userid as userid',
    db_prefix() . 'invoices.customer_source as customer_source',
    db_prefix() . 'invoices.type_of_customer as type_of_customer',
    db_prefix() . 'invoices.usage_behavior as usage_behavior',
    db_prefix() . 'invoices.addedfrom',
    db_prefix() . 'projects.name as project_name',
    '(SELECT GROUP_CONCAT(name SEPARATOR ",") FROM ' . db_prefix() . 'taggables JOIN ' . db_prefix() . 'tags ON ' . db_prefix() . 'taggables.tag_id = ' . db_prefix() . 'tags.id WHERE rel_id = ' . db_prefix() . 'invoices.id and rel_type="invoice" ORDER by tag_order ASC) as tags',
    get_sql_select_client_company(),
];

$sIndexColumn = 'id';
$sTable       = db_prefix() . 'invoices';

$join = [
    'LEFT JOIN ' . db_prefix() . 'clients ON ' . db_prefix() . 'clients.userid = ' . db_prefix() . 'invoices.clientid',
    'LEFT JOIN ' . db_prefix() . 'currencies ON ' . db_prefix() . 'currencies.id = ' . db_prefix() . 'invoices.currency',
    'LEFT JOIN ' . db_prefix() . 'projects ON ' . db_prefix() . 'projects.id = ' . db_prefix() . 'invoices.project_id',
    'LEFT JOIN ' . db_prefix() . 'invoicepaymentrecords ON ' . db_prefix() . 'invoicepaymentrecords.invoiceid = ' . db_prefix() . 'invoices.id',
    'LEFT JOIN ' . db_prefix() . 'estimates ON ' . db_prefix() . 'estimates.invoiceid = ' . db_prefix() . 'invoices.id',
    'LEFT JOIN ' . db_prefix() . 'contacts ON ' . db_prefix() . 'contacts.id = ' . db_prefix() . 'invoices.contact_id',
    'LEFT JOIN ' . db_prefix() . 'customer_admins ON ' . db_prefix() . 'customer_admins.customer_id = ' . db_prefix() . 'clients.userid',
];

$custom_fields = get_table_custom_fields('invoice');

foreach ($custom_fields as $key => $field) {
    $selectAs = (is_cf_date($field) ? 'date_picker_cvalue_' . $key : 'cvalue_' . $key);

    array_push($customFieldsColumns, $selectAs);
    array_push($aColumns, 'ctable_' . $key . '.value as ' . $selectAs);
    array_push($join, 'LEFT JOIN ' . db_prefix() . 'customfieldsvalues as ctable_' . $key . ' ON ' . db_prefix() . 'invoices.id = ctable_' . $key . '.relid AND ctable_' . $key . '.fieldto="' . $field['fieldto'] . '" AND ctable_' . $key . '.fieldid=' . $field['id']);
}

array_push($aColumns, 'paymentExpiresDate.value as payment_expires_date');
array_push($join, 'LEFT JOIN ' . db_prefix() . 'customfieldsvalues as paymentExpiresDate ON ' . db_prefix() . 'invoices.id = paymentExpiresDate.relid AND paymentExpiresDate.fieldto="invoice" AND paymentExpiresDate.fieldid=14');

$where  = [];

$custom_date_select = '';

$aColumns = hooks()->apply_filters('invoices_table_sql_columns', $aColumns);

// Fix for big queries. Some hosting have max_join_limit
if (count($custom_fields) > 4) {
    @$this->ci->db->query('SET SQL_BIG_SELECTS=1');
}

$leaderApprovePermission = false;
$managerApprovePermission = false;
$staffId = get_staff_user_id();

if ($managerApprovePermission = has_permission('invoices', '', 'po_approval_as_manager')) {
    $where[] = 'AND ' . db_prefix() . 'invoices.discount_percent > ' . DISCOUNT_PERCENT_MANAGER_APPROVAL;
} else if ($leaderApprovePermission = has_permission('invoices', '', 'po_approval_as_leader')) {
    $where[] = 'AND ((' . db_prefix() . 'invoices.discount_percent between 1 AND ' . DISCOUNT_PERCENT_MANAGER_APPROVAL . ' AND ' . db_prefix() . 'invoices.addedfrom != ' . $staffId . ') OR (' . db_prefix() . 'invoices.discount_percent > ' . DISCOUNT_PERCENT_MANAGER_APPROVAL . ' AND ' . db_prefix() . 'invoices.addedfrom = ' . $staffId . '))';
    // If Sale Leader, only see request of team members
    if (Staff::query()
        ->where('staffid', $staffId)
        ->isSaleader()
        ->exists()) 
    {
        $this->ci->load->model('roles_model');
        $saleMemeberIds = $this->ci->roles_model->get_sale_members($staffId);
        $saleMemeberIds[] = $staffId;
        $where[] = 'AND ' . db_prefix() . 'invoices.addedfrom IN (' . join(',', $saleMemeberIds) . ')';
    }
} else {
    $where[] = 'AND ' . db_prefix() . 'invoices.addedfrom = ' . $staffId;
    $where[] = 'AND ' . db_prefix() . 'invoices.discount_percent > 0';
}

$where[] = 'AND  ' . db_prefix() . 'invoices.addition_discount_approved_at IS NULL';
$where[] = 'AND  ' . db_prefix() . 'invoices.status NOT IN (2, 5)';
$where[] = 'AND  NOT EXISTS (SELECT * FROM ' . db_prefix() . 'minvoices WHERE ' . db_prefix() . 'invoices.id = ' . db_prefix() . 'minvoices.invoice_id AND invoice_issued_date IS NOT NULL)';
// Should not fetch PO that created by managers (admin or staff is checked on the "Approve POs as Manager")
$where[] = 'AND  ' . db_prefix() . 'invoices.addedfrom NOT IN (
    select ' . db_prefix() . 'staff.staffid FROM ' . db_prefix() . 'staff where ' . db_prefix() . 'staff.admin = 1 OR ' . db_prefix() . 'staff.staffid IN (select staff_id from ' . db_prefix() . 'staff_permissions WHERE ' . db_prefix() . 'staff_permissions.capability = "po_approval_as_manager" and ' . db_prefix() . 'staff_permissions.feature = "invoices")
)';
// Should not fetch PO that has approval version = v3
$where[] = 'AND  ' . db_prefix() . 'invoices.approval_version != "v3"';

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [
    db_prefix() . 'invoices.id',
    db_prefix() . 'invoices.clientid',
    db_prefix() . 'currencies.name as currency_name',
    db_prefix() . 'estimates.project_id as project_id',
    db_prefix() . 'estimates.hash as hash',
    'recurring',
    db_prefix() . 'invoices.addedfrom',
    db_prefix() . 'invoices.number',
    db_prefix() . 'invoices.status',
    db_prefix() . 'invoices.number_format',
    db_prefix() . 'invoices.prefix',
    db_prefix() . 'invoices.date',
    db_prefix() . 'invoices.addition_discount_approved_at',
    db_prefix() . 'invoices.addition_discount_rejected_at',
], 'group by ' . db_prefix() . 'invoices.id');
$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];

    $numberOutput = '';

    // If is from client area table
    $invObj = json_decode(json_encode([
        'id' => $aRow['id'],
        'number' => $aRow['number'],
        'status' => $aRow['status'],
        'number_format' => $aRow['number_format'],
        'prefix' => $aRow['prefix'],
        'date' => $aRow['date'],
    ]));

    $numberOutput = format_invoice_number($invObj);
    $numberOutput .= '<div class="row-options">';
    if (has_permission('invoices', '', 'edit')) {
        $numberOutput .= '<a target="_blank" href="' . admin_url('invoices/invoice/' . $aRow['id']) . '">' . _l('edit') . '</a>';
    }
    $numberOutput .= '</div>';
    $row[] = $numberOutput;

    $row[] = app_format_money($aRow['invoices_total_amount'], $aRow['currency_name']);

    $row[] = $aRow['year'];

    $row[] = _d($aRow['date']);

    $row[] = $aRow['clientid'];

    if (empty($aRow['deleted_customer_name'])) {
        $row[] = '<a href="' . admin_url('clients/client/' . $aRow['clientid']) . '">' . $aRow['company'] . '</a>';
    } else {
        $row[] = $aRow['deleted_customer_name'];
    }

    $row[] = $aRow['contact_name'];

    $row[] = $aRow['vat'];


    $row[] = isset($aRow['payment_expires_date']) ? _d($aRow['payment_expires_date']) : '';

    $row[] = _d($aRow['date_of_payment']);

    $row[] = format_invoice_status($aRow[db_prefix() . 'invoices.status']);

    $customer_admins = $this->ci->clients_model->get_admins($aRow['userid']);
    $dataAdmin = '';

    if (count($customer_admins) > 0) {
        foreach ($customer_admins as $admin) {
            $dataAdmin .= '<a href=" ' . admin_url('profile/' . $admin['staff_id']) . '">' . get_staff_full_name($admin['staff_id']) . '</a><br>';
        }
    }
    $row[] = $dataAdmin;

    $row[] = $aRow['customer_source'];

    // Custom fields add values
    foreach ($customFieldsColumns as $customFieldColumn) {
        $row[] = (strpos($customFieldColumn, 'date_picker_') !== false ? _d($aRow[$customFieldColumn]) : $aRow[$customFieldColumn]);
    }

    $row[] = $aRow['type_of_customer'];

    $row[] = $aRow['usage_behavior'];

    $dataCreators = $this->ci->invoices_model->get_creator($aRow['id']);
    $dataCreator = '';

    if (count($dataCreators) > 0) {
        foreach ($dataCreators as $creator) {
            $dataCreator .= $creator['full_name'] . ($creator['customer_admin_id'] ? ' (' . $creator['firstname'] . ' ' . $creator['lastname'] . ')' : '');
        }
    }

    $row[] = $dataCreator;

    $row[] = render_tags($aRow['tags']);

    $row['DT_RowClass'] = 'has-row-options';

    $row = hooks()->apply_filters('invoices_table_row_data', $row, $aRow);

        $approveAction = '';
        $ableToApprove = ($leaderApprovePermission && $aRow['addedfrom'] != $staffId) || $managerApprovePermission;
        if ($ableToApprove && empty($aRow['addition_discount_approved_at']) && empty($aRow['addition_discount_rejected_at'])) {
            $approveAction = '<button data-id="'.$aRow['id'].'" data-inv-number="' . format_invoice_number($invObj) . '" class="btn btn-info approve-btn">Approve<i class="fa fa-spinner fa-spin hide"></i></button></button>';
            $approveAction .= '<br>';
            $approveAction .= '<button data-id="'.$aRow['id'].'" data-inv-number="' . format_invoice_number($invObj) . '" class="btn btn-danger reject-btn mtop5">Reject<i class="fa fa-spinner fa-spin hide"></i></button></button>';
        } else {
            if (!empty($aRow['addition_discount_approved_at'])) {
                $approveAction = '<span class="label label-info s-status">Đã duyệt</span>';
            } else if (!empty($aRow['addition_discount_rejected_at'])) {
                $approveAction = '<span class="label label-danger s-status">Từ chối</span><br>' . '('.Carbon::parse($aRow['addition_discount_rejected_at'])->format('Y-m-d H:i:s') . ')';
            } else {
                $approveAction = '<span class="label label-default s-status">Đang chờ</span>';
            }
        }

        $row[] = $approveAction;

    $output['aaData'][] = $row;
}

<?php

use app\services\ReportService;
use Carbon\Carbon;
use Entities\ClientAmsSearchPackage;
use Entities\ClientAmsService;

defined('BASEPATH') or exit('No direct script access allowed');

/*
* Output
*/
$CI          = &get_instance();
$post      = $CI->input->post();

$month = $post['report_month'] ?? null;

if (empty($month)) {
    $output = [
        'draw'                 => $post['draw'] ? intval($post['draw']) : 0,
        'iTotalRecords'        => 0,
        'iTotalDisplayRecords' => 0,
        'aaData'               => [],
    ];
} else {
    $sortFilterCols = [
        'invoice_id',
        'invoice_id',
        'invoice_id',
        'invoice_id',
        'invoice_id',
        'invoice_id',
        'invoice_id',
        'invoice_id',
        'invoice_id',
        'invoice_id',
        'invoice_id',
        'invoice_id',
    ];
    $selects = [
        DB::raw('count(id) as duration'),
        'invoice_id',
        'itemable_id',
        'ams_job_id',
        DB::raw('sum(amount) as amount'),
        'package_type',
    ];

    $query = get_job_posting_monthly_revenue_query($month, $selects);
    $query = $query->groupBy(['invoice_id', 'itemable_id', 'ams_job_id']);

    $result = data_tables_eloquent_init($query, $sortFilterCols);

    $output  = $result['output'];
    $rResult = $result['rResult'];

    $jobIds = collect($rResult)->filter(fn($row) => $row->isJobPosting())->pluck('ams_job_id')->filter()->unique();
    $serviceIds = collect($rResult)->filter(fn($row) => $row->isService())->pluck('ams_job_id')->filter()->unique();
    $comboIds = collect($rResult)->filter(fn($row) => $row->isCombo())->pluck('ams_job_id')->filter()->unique();
    $jobs = $services = $combos = collect();
    if ($jobIds->count()) {
        [$hasNextPage, $jobs] = ReportService::fetchOpenJobs(1, $jobIds->toArray(), $jobIds->count(), true);
        $jobs = $jobs->keyBy('id');
    }

    if ($serviceIds->count()) {
        $services = ClientAmsService::select('id', 'used_at')->whereIn('id', $serviceIds)->get()->keyBy('id');
    }

    $requestComboIds = collect();
    if ($comboIds->isNotEmpty()) {
        $requestComboIds = ClientAmsSearchPackage::whereIn('id', $comboIds->toArray())->pluck('ams_company_search_package_id', 'id');
        $combos = collect(ReportService::fetchAmsCombo($requestComboIds->values()->toArray()));
    }

    $invoiceTotalByMonth = 0;
    $totals = get_total_amount_job_posting_monthly_revenue_query($month);
    $totals->each(function ($item) use (&$invoiceTotalByMonth) {
        $amount = Carbon::parse($item->invoice->date)->year < 2024 ? 0 : $item->amount;
        $invoiceTotalByMonth += $amount;
    });

    $currentMonth = Carbon::createFromFormat('Y.m.d', $month . '.01')->endOfMonth()->endOfDay();

    foreach ($rResult as $key => $aRow) {
        $invoice = $aRow->invoice;
        $itemable = $aRow->itemable;
        $qty = $itemable->qty;
        // Minus voucher
        $invoiceSubtotal = $invoice->total - $invoice->total_tax;
        $unitPrice = $invoiceSubtotal / intval($invoice->invoice_items_sum_qty);
        $floorSalePrice = floor($unitPrice / 1000) * 1000;
        $salePrice = $floorSalePrice * $qty;

        $start = $end = '';

        if ($aRow->isService()) {
            $service  = $services->get($aRow->ams_job_id);
            $start = $service ? Carbon::parse($service->used_at) : '';
        } else if ($aRow->isCombo()) {
            $serviceId = $requestComboIds->get($aRow->ams_job_id);
            $combo  = $serviceId ? $combos->get($serviceId) : null;
            $start = $combo ? Carbon::parse($combo['created_at']) : '';
        } else if ($aRow->isJobPosting()) {
            $job = $jobs->get($aRow->ams_job_id);
            $start = empty($job['published']['date']) ? '' : Carbon::createFromFormat('d-m-Y', $job['published']['date']);
            $end = empty($job['closed']['date']) ? '' : Carbon::createFromFormat('d-m-Y', $job['closed']['date']);
        }

        $row = [];

        $row[] = '<a href="' . admin_url('invoices/list_invoices/' . $aRow->invoice->id) . '" target="_blank">' . format_invoice_number($aRow->invoice) . '</a>';
        $row[] = isset($aRow->invoice->minvoice->invoice_number) ? intval($aRow->invoice->minvoice->invoice_number) : '';
        $row[] = isset($aRow->invoice->minvoice->invoice_issued_date) ? Carbon::parse($aRow->invoice->minvoice->invoice_issued_date)->format('Y.m.d') : '';
        $row[] = '<a href="' . admin_url('clients/client/' . $aRow->invoice->client->userid) . '" target="_blank">' . $aRow->invoice->client->business_name . '</a>';
        $row[] = $aRow->itemable->item->description ?? '';
        $row[] = !empty($start) ? $start->format('Y-m-d') : '';
        $row[] = !empty($end) ? $end->format('Y-m-d') : '';
        $packageDuration = in_array($aRow->itemable->item_id, [4, 23]) ? 30 : 30; // TODO: refactor this, it should be 30, but please check the in_array statement
        $row[] = $packageDuration;
        $row[] = number_format($salePrice);
        $row[] = !empty($start) ? $aRow->duration : 0;

        $amount = Carbon::parse($aRow->invoice->date)->year < 2024 ? 0 : $aRow->amount;
        $row[] = number_format($amount);

        $output['aaData'][] = $row;
    }
    $output['amounts'] = [
        'monthly_invoice_total_by_month' => round($invoiceTotalByMonth),
    ];
}

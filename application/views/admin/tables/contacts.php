<?php

defined('BASEPATH') or exit('No direct script access allowed');

$total_client_contacts = total_rows(db_prefix() . 'contacts', ['userid' => $client_id]);
$this->ci->load->model('gdpr_model');

$consentContacts = get_option('gdpr_enable_consent_for_contacts');
if (is_gdpr() && $consentContacts == '1') {
    array_push($aColumns, '1');
}
$aColumns = [
    db_prefix() . 'contacts.id as id',
    'fullname',
    db_prefix() . 'contacts.email as email',
    'title',
    db_prefix() . 'contacts.linkedin as linkedin',
    db_prefix() . 'contacts.zalo as zalo',
    db_prefix() . 'contacts.skype as skype',
    db_prefix() . 'contacts.landline as landline',
    db_prefix() . 'contacts.other as other',
    db_prefix() . 'contacts.phonenumber as phonenumber',
    db_prefix() . 'contacts.active as active',
    db_prefix() . 'contacts.last_login as last_login',
    'status',
    'review_status',
    db_prefix() . 'contacts.datecreated as datecreated',
    db_prefix() . 'contacts.updated_at as updated_at',
    'creator.staffid as creator_id',
    'CONCAT(creator.firstname, " ", creator.lastname) as creator_fullname',
    'modified_staff.staffid as modified_staff_id',
    'CONCAT(modified_staff.firstname, " ", modified_staff.lastname) as modified_staff_fullname',
    'update_status_log.date as change_status_at',
    'change_status_staff.staffid as change_status_staff_id',
    'CONCAT(change_status_staff.firstname, " ", change_status_staff.lastname) as change_status_staff_fullname',
];

$sIndexColumn = 'id';
$sGroupBy = 'GROUP BY id';
$sTable = db_prefix() . 'contacts';
$join = [];

$custom_fields = get_table_custom_fields('contacts');

foreach ($custom_fields as $key => $field) {
    $selectAs = (is_cf_date($field) ? 'date_picker_cvalue_' . $key : 'cvalue_' . $key);
    array_push($customFieldsColumns, $selectAs);
    array_push($aColumns, 'ctable_' . $key . '.value as ' . $selectAs);
    array_push($join, 'LEFT JOIN ' . db_prefix() . 'customfieldsvalues as ctable_' . $key . ' ON ' . db_prefix() . 'contacts.id = ctable_' . $key . '.relid AND ctable_' . $key . '.fieldto="' . $field['fieldto'] . '" AND ctable_' . $key . '.fieldid=' . $field['id']);
}

$join[] = 'LEFT JOIN (SELECT rel_id, staffid FROM ' . db_prefix() . 'activity_log WHERE rel_type = "contact" AND description LIKE "%created%" GROUP BY rel_id) creator_log
    ON ' . db_prefix() . 'contacts.id = creator_log.rel_id
    LEFT JOIN ' . db_prefix() . 'staff creator ON creator.staffid = creator_log.staffid';

$join[] = 'LEFT JOIN (SELECT rel_id, staffid, date FROM ' . db_prefix() . 'activity_log WHERE rel_type = "contact" AND description LIKE "%Change Status Contact%" GROUP BY rel_id) update_status_log
    ON ' . db_prefix() . 'contacts.id = update_status_log.rel_id
    LEFT JOIN ' . db_prefix() . 'staff change_status_staff ON change_status_staff.staffid = update_status_log.staffid';

$join[] = 'LEFT JOIN (SELECT rel_id, staffid FROM ' . db_prefix() . 'activity_log WHERE id IN (SELECT MAX(id) FROM ' . db_prefix() . 'activity_log WHERE rel_type = "contact" AND description LIKE "%updated%" GROUP BY rel_id)
    GROUP BY rel_id) last_modified_log
    ON ' . db_prefix() . 'contacts.id = last_modified_log.rel_id
    LEFT JOIN ' . db_prefix() . 'staff modified_staff ON modified_staff.staffid = last_modified_log.staffid';

$where = ['AND userid=' . $this->ci->db->escape_str($client_id)];

// Fix for big queries. Some hosting have max_join_limit
if (count($custom_fields) > 4) {
    @$this->ci->db->query('SET SQL_BIG_SELECTS=1');
}

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [db_prefix() . 'contacts.id as id', 'userid', 'is_primary'], $sGroupBy);

$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];

    $row[] = $aRow['id'];

    if ((has_permission('contacts', '', 'edit') && is_customer_admin($aRow['userid'])) || is_admin()) {
        $row[] = '<a href="#" onclick="contact(' . $aRow['userid'] . ',' . $aRow['id'] . ');return false;">' . $aRow['fullname'] . '</a>';
    } else {
        $row[] = $aRow['fullname'];
    }

    if (is_gdpr() && $consentContacts == '1') {
        $consentHTML = '<p class="bold"><a href="#" onclick="view_contact_consent(' . $aRow['id'] . '); return false;">' . _l('view_consent') . '</a></p>';
        $consents    = $this->ci->gdpr_model->get_consent_purposes($aRow['id'], 'contact');
        foreach ($consents as $consent) {
            $consentHTML .= '<p style="margin-bottom:0px;">' . $consent['name'] . (!empty($consent['consent_given']) ? '<i class="fa fa-check text-success pull-right"></i>' : '<i class="fa fa-remove text-danger pull-right"></i>') . '</p>';
        }
        $row[] = $consentHTML;
    }

    $row[] = $aRow['title'];
    $row[] = '<ul>
        <li>' . _l('contact_mobile_phone') . ': <a class="call-phone" href="javascript:void(0);">' . $aRow['phonenumber'] . '</a></li>
        <li>' . _l('client_email') . ': <a href="mailto:' . $aRow['email'] . '">' . $aRow['email'] . '</a></li>
        <li>' . _l('linkedin') . ': ' . $aRow['linkedin'] . '</li>
        <li>' . _l('zalo') . ': ' . $aRow['zalo'] . '</li>
        <li>' . _l('skype') . ': ' . $aRow['skype'] . '</li>
        <li>' . _l('landline') . ': <a class="call-phone" href="javascript:void(0);">' . $aRow['landline'] . '</a></li>
        <li>' . _l('other') . ': ' . $aRow['other'] . '</li>
    </ul>';

    $options = '<option></option>';
    foreach (CONTACT_STATUS_OPTIONS as $value) {
        $options .= '<option value="' . $value['value'] . '" ' . ($aRow['status'] == $value['value'] ? 'selected' : "") . ' >' . $value['text'] . '</option>';
    }
    $row[] = '
    <div class="form-group">
        <select class="form-control" id="status_' . $aRow['id'] . '" onchange="updateStatusContact(' . $aRow['userid'] . ',' . $aRow['id'] . ');" ' . (((has_permission('contacts', '', 'edit') && is_customer_admin($aRow['userid'])) || is_admin()) ? '' : 'disabled') . ' >' . $options  . '</select>
    </div>';

    $history = '<ul>
        <li>' . _l('created_by') . (!empty($aRow['creator_id']) ? (': <a href="' . admin_url('profile/' . $aRow['creator_id']) . '">' . $aRow['creator_fullname'] . '</a>') : ': System').'</li>
        <li>' . _l('created_at') . ': ' . _dt($aRow['datecreated']) . '</li>';

    if (isset($aRow['change_status_staff_id'])) {
        $history .= '
            <li>' . _l('approval_by') . ': <a href="' . admin_url('profile/' . $aRow['change_status_staff_id']) . '">' . $aRow['change_status_staff_fullname'] . '</a></li>
            <li>' . _l('approval_at') . ': ' . _dt($aRow['change_status_at']) . '</li>
        ';
    }
    if (isset($aRow['modified_staff_id'])) {
        $history .= '
            <li>' . _l('last_modified_by') . ': <a href="' . admin_url('profile/' . $aRow['modified_staff_id']) . '">' . $aRow['modified_staff_fullname'] . '</a></li>
            <li>' . _l('last_modified_at') . ': ' . _dt($aRow['updated_at']) . '</li>
            <a href="#" data-toggle="modal" onClick="showHistoryContact(' . $aRow['id'] . ')" data-target="#contact_history_modal"> ' . _l('contact_editing_history') . '</a>
        ';
    }
    $history .= '</ul>';
    $row[] = $history;

    $options = '';
    foreach (CONTACT_REVIEW_STATUS_OPTIONS as $value) {
        $options .= '<option value="' . $value['value'] . '" ' . ($aRow['review_status'] == $value['value'] ? 'selected' : "") . '>' . $value['text'] . '</option>';
    }
    $row[] = '
    <div class="form-group">
        <select class="form-control" id="review_status_' . $aRow['id'] . '" onchange="updateStatusContact(' . $aRow['userid'] . ',' . $aRow['id'] . ');" ' . ($aRow['review_status'] != 0 || !has_permission('contacts', '', 'review') ? 'disabled' : '') . ' >' . $options  . '</select>
    </div>';

    $actions = '<div>';
    if ((has_permission('contacts', '', 'edit') && is_customer_admin($aRow['userid'])) || is_admin()) {
        $actions .= '<a href="#" class="btn btn-default btn-icon mbot5" onclick="contact(' . $aRow['userid'] . ',' . $aRow['id'] . ');return false;"><i class="fa fa-pencil-square-o"></i></a>';
    }

    if (is_gdpr() && get_option('gdpr_data_portability_contacts') == '1' && is_admin()) {
        $actions .= ' | <a href="' . admin_url('clients/export/' . $aRow['id']) . '">
             ' . _l('dt_button_export') . ' (' . _l('gdpr_short') . ')
          </a>';
    }

    if (has_permission('contacts', '', 'delete')) {
        if ($aRow['is_primary'] == 0 || ($aRow['is_primary'] == 1 && $total_client_contacts == 1)) {
            $actions .= '<a href="' . admin_url('clients/delete_contact/' . $aRow['userid'] . '/' . $aRow['id']) . '" class="btn btn-danger btn-icon mbot5 _delete"><i class="fa fa-remove"></i></a>';
        }
    };
    $row[] = $actions;

    // Custom fields add values
    foreach ($customFieldsColumns as $customFieldColumn) {
        $row[] = (strpos($customFieldColumn, 'date_picker_') !== false ? _d($aRow[$customFieldColumn]) : $aRow[$customFieldColumn]);
    }

    $row['DT_RowClass'] = 'has-row-options';
    $output['aaData'][] = $row;
}

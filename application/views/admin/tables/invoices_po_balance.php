<?php

defined('BASEPATH') or exit('No direct script access allowed');

use Carbon\Carbon;
use Entities\AmsPackage;
use Entities\Invoice;
use Entities\Itemable;
use app\services\AmsService;
use Entities\Item;

function getInvoicePoBalanceQuery()
{
    $ci = &get_instance();

    // Request input
    $inputCustomerAdmins = $ci->input->post('customer_admins');
    $inputInvoiceCreator = $ci->input->post('invoice_creator');
    $inputStatus = $ci->input->post('status');
    $inputCustomerSource = $ci->input->post('customer_source');
    $inputTypeOfCustomer = $ci->input->post('type_of_customer');
    $inputUsageBehavior = $ci->input->post('usage_behavior');
    $inputDepartment = $ci->input->post('department');
    $inputFromDate = $ci->input->post('from_date');
    $inputToDate = $ci->input->post('to_date');
    $inputProjectTagReference = $ci->input->post('project_tag_reference');

    // Invoice query
    return Invoice::query()
        ->paid()
        ->with(
            'invoiceItems',
            'client',
            'customerAdmin',
            'creator',
            'clientAmsJobs',
            'clientAmsOpenJobs',
        )
        ->notDraft()
        ->hasInvoiceIssueDate()
        ->when(is_sales_member(), function ($query) {
            $query->whereHas('customerAdmin', function ($query) {
                $query->where('staff_id', get_staff_user_id());
            });
        })
        ->when(is_sales_leader(), function ($query) {
            $query->whereHas('customerAdmin', function ($query) {
                $query->whereIn('staff_id', get_list_staff_team(get_staff_user_id()));
            });
        })
        ->when($inputCustomerAdmins, function ($query) use ($inputCustomerAdmins) {
            $query->whereHas('customerAdmin', function ($query) use ($inputCustomerAdmins) {
                $query->where('staff_id', $inputCustomerAdmins);
            });
        })
        ->when($inputInvoiceCreator, function ($query) use ($inputInvoiceCreator) {
            $query->where('addedfrom', $inputInvoiceCreator);
        })
        ->when($inputStatus, function ($query) use ($inputStatus) {
            $query->where('status', $inputStatus);
        })
        ->when($inputCustomerSource, function ($query) use ($inputCustomerSource) {
            $query->where('customer_source', $inputCustomerSource);
        })
        ->when($inputTypeOfCustomer, function ($query) use ($inputTypeOfCustomer) {
            $query->where('type_of_customer', $inputTypeOfCustomer);
        })
        ->when($inputUsageBehavior, function ($query) use ($inputUsageBehavior) {
            $query->where('usage_behavior', $inputUsageBehavior);
        })
        ->when($inputDepartment, function ($query) use ($inputDepartment) {
            $query->whereHas('customerAdmin', function ($query) use ($inputDepartment) {
                $query->where('department_id', $inputDepartment);
            });
        })
        ->when($inputFromDate, function ($query) use ($inputFromDate) {
            $inputFromDate = Carbon::createFromFormat('d.m.Y', $inputFromDate);
            $query->where('date', '>=', $inputFromDate);
        })
        ->when($inputToDate, function ($query) use ($inputToDate) {
            $inputToDate = Carbon::createFromFormat('d.m.Y', $inputToDate);
            $query->where('date', '<=', $inputToDate);
        })
        ->when($inputProjectTagReference, function ($query) use ($inputProjectTagReference) {
            $query->whereHas('clientAmsJobs', function ($query) use ($inputProjectTagReference) {
                $query->where('notes_label', 'like', '%' . $inputProjectTagReference . '%');
            });
        });
}

function getAmsPackages()
{
    return AmsPackage::select('crm_item_id', 'ams_package_id')->get()->groupBy('crm_item_id');
}

function getAmsJobsFromTableResult($rResult)
{
    $amsJobIds = collect($rResult)
        ->pluck('clientAmsJobs')
        ->flatten()
        ->whereNull('package_id')
        ->pluck('ams_job_id')
        ->unique()
        ->values()
        ->implode(',');

    return empty($amsJobIds) ? collect() : AmsService::search('jobs', [
        'query' => [
            'fields' => [
                'job' => 'id,package_list,notes_label'
            ],
            'ids' => $amsJobIds,
            'all_jobs' => true,
            'page_size' => 100,
        ]
    ])['data'] ?? collect();
}


$amsPackages = getAmsPackages();

$result = data_tables_eloquent_init(getInvoicePoBalanceQuery(), ['id']);
$output = $result['output'];
$rResult = $result['rResult'];


$jobs = getAmsJobsFromTableResult($rResult);
$hasJob = $jobs->isNotEmpty();


$amsSearchPackagesIds = count($rResult) ? array_map(fn($aRow) => $aRow->clientAmsSearchCvs->pluck('ams_company_search_package_id')->join(','), $rResult) : [];
$amsSearchPackagesIds = join(',', array_filter($amsSearchPackagesIds));
$searchPackages = collect();
if (!empty($amsSearchPackagesIds)) {
    $res = AmsService::amsApi('crm/company-credits', [
        'query' => [
            'ids' => $amsSearchPackagesIds,
        ]
    ], 'get');
    $searchPackages = !empty($res['data']) ? collect($res['data']) : collect();
}


foreach ($rResult as $aRow) {
    $row = [];

    // Invoice number
    $row[0] = '<a href="' . admin_url('invoices/list_invoices/' . $aRow->id) . '" target="_blank">' . format_invoice_number($aRow->id) . '</a>';

    // Date created
    $row[1] = $aRow->datecreated;

    // ID Customer
    $row[2] = $aRow->clientid;

    // Customer
    $row[3] = $aRow->client->business_name;

    // Customer Admin
    $row[4] = $aRow->customerAdmin->staff->fullname;

    // PO Creator
    $row[5] = $aRow->creator->fullname;

    // Active On
    $row[6] = $aRow->date->format('Y-m-d');

    // Expired Date
    $maxExpired = $aRow->invoiceItems->max('use_expired_at');
    $row[7] = $maxExpired ? $maxExpired->format('Y-m-d') : '';

    // Item, all the column will base on this column
    $unitPrice = ($aRow->subtotal - $aRow->discount_total) / $aRow->invoiceItems->sum('qty');
    $aData = [];
    $row[8] = [];
    foreach($aRow->invoiceItems as $item) {
        $clientAmsJobs = $aRow->clientAmsJobs;
        $clientAmsSearchCvs = $aRow->clientAmsSearchCvs;
        $clientAmsServices = $aRow->clientAmsServices;

        $paidPackages = $item->qty;
        $usedPackages = 0;
        switch ($item->item->group_id) {
            case Item::SME_GROUP_ID:
            case Item::ENTERPRISE_GROUP_ID:
            case Item::NEW_2024_GROUP_ID:
                $usedPackages = $clientAmsJobs->where('package_id', $item->id)->count();
                break;
            case Item::CREDIT_GROUP_ID:
                $usedPackages = $clientAmsSearchCvs->where('crm_itemable_id', $item->id)->count();
                break;
            case Item::EMPLOYER_BRANDING_GROUP_ID:
                $usedPackages = $clientAmsServices->where('crm_itemable_id', $item->id)->count();
                break;
        }
        $remainPackages = $paidPackages - $usedPackages;
        $remainAmount = $remainPackages * $unitPrice;

        $aData[] = [
            'invoice_id' => $aRow->id,
            'itemable_id' => $item->itemable_id,
            'name' => $item->description,   
            'paid_packages' => number_format($paidPackages, 0),
            'used_packages' => number_format($usedPackages, 0),
            'remain_packages' => number_format($remainPackages, 0),
            'remain_amount' => app_format_money($remainAmount, 'vnd'),
            'use_expired_at' => $item->use_expired_at ? $item->use_expired_at->format('Y-m-d') : '',
        ];
    }

    $row[8] = $aData;

    $row[9] = null;
    $row[10] = null;
    $row[11] = null;
    $row[12] = null;

    $output['aaData'][] = $row;
}

return $output;

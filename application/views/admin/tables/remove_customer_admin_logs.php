<?php

defined('BASEPATH') or exit('No direct script access allowed');

$aColumns = [
    db_prefix().'activity_log.description as description',
    db_prefix().'activity_log.data as data',
    'concat(firstname, " ", lastname) staff',
    db_prefix().'activity_log.date as date',
];

$sIndexColumn = 'id';
$sTable       = db_prefix().'activity_log';
$where        = [
    'AND '.db_prefix().'activity_log.rel_type="rm_customer_admin"',
];
$join = [
    'JOIN '.db_prefix().'staff ON '.db_prefix().'staff.staffid = '.db_prefix().'activity_log.staffid',
];
$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where);
$output  = $result['output'];
$rResult = $result['rResult'];
foreach ($rResult as $aRow) {
    $data = json_decode($aRow['data'], true) ?? [];
    $output['aaData'][] = [
        $aRow['description'],
        $data['csv_file'] ?? '',
        $aRow['staff'],
        _dt($aRow['date']),
    ];
}

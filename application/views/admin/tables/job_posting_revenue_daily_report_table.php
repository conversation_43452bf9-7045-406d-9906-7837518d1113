<?php

use Carbon\Carbon;
use Entities\InvoiceDailyRevenue;

defined('BASEPATH') or exit('No direct script access allowed');

/*
* Output
*/
$CI          = &get_instance();
$post      = $CI->input->post();

$day = $post['report_date'] ?? null;

if (empty($day)) {
    $output = [
        'draw'                 => $post['draw'] ? intval($post['draw']) : 0,
        'iTotalRecords'        => 0,
        'iTotalDisplayRecords' => 0,
        'aaData'               => [],
    ];
} else {
    $sortFilterCols = [
        '',
        '',
        '',
        '',
        '',
    ];
    $selects = [
        'id',
        'invoice_id',
        'itemable_id',
        'ams_job_id',
        'revenue_date',
        'amount',
    ];

    $query = InvoiceDailyRevenue::select($selects)
        ->with([
            'invoice:id,number,number_format,prefix,date',
            'itemable:id,rel_id,rel_type,item_id',
            'itemable.item:id,description',
            'invoice.invoiceNumber'
        ])
        ->where('revenue_date', Carbon::createFromFormat('Y.m.d', $day)->format('Ymd'))
        ->groupBy(['invoice_id', 'itemable_id', 'ams_job_id'])
        ;

    $result = data_tables_eloquent_init($query, $sortFilterCols);

    $invoiceTotal = InvoiceDailyRevenue::query()
        ->where('revenue_date', Carbon::createFromFormat('Y.m.d', $day)->format('Ymd'))
        ->sum('amount');

    $output  = $result['output'];
    $rResult = $result['rResult'];

    foreach ($rResult as $key => $aRow) {
        $row = [];

        $row[] = '<a href="' . admin_url('invoices/list_invoices/' . $aRow->invoice->id) . '?output_type=I" target="_blank">' . format_invoice_number($aRow->invoice) . '</a>';
        $row[] = isset($aRow->invoice->minvoice->invoice_number) ? intval($aRow->invoice->minvoice->invoice_number) : '';
        $row[] = isset($aRow->invoice->minvoice->invoice_issued_date) ? Carbon::parse($aRow->invoice->minvoice->invoice_issued_date)->format('Y.m.d') : '';
        $row[] = $aRow->itemable->item->description ?? '';
        $row[] = Carbon::createFromFormat('Ymd', $aRow->revenue_date)->format('Y-m-d');
        //$row[] = number_format($aRow->amount);
        $amount = Carbon::parse($aRow->invoice->date)->year < 2024 ? 0 : $aRow->amount;
        $row[] = number_format($amount);

        $output['aaData'][] = $row;
    }

    $output['amounts'] = [
        'invoice_total' => round($invoiceTotal),
    ];
}

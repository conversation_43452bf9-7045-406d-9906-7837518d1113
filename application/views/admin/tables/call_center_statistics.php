<?php

use Carbon\Carbon;
use Entities\M3cCall;

defined('BASEPATH') or exit('No direct script access allowed');

$sortFilterCols = [
    '3c_call.staff_id',
    'total_callout',
    'total_callout_success',
    'total_callout_rejected',
    'total_callin',
    'total_talktime',
];
$selects = [
    '3c_call.staff_id',
    DB::raw('COUNT(CASE WHEN call_type = 1 THEN 1 ELSE NULL END) as total_callout'),
    DB::raw('COUNT(CASE WHEN call_type = 1 and call_status != "miss" and talk_time >= "00:00:05" THEN 1 ELSE NULL END) as total_callout_success'),
    DB::raw('COUNT(CASE WHEN call_type = 1 and call_status = "miss" THEN 1 ELSE NULL END) as total_callout_rejected'),
    DB::raw('COUNT(CASE WHEN call_type = 2 THEN 1 ELSE NULL END) as total_callin'),
    DB::raw('SUM( CASE WHEN talk_time >= "00:00:05" THEN TIME_TO_SEC(talk_time) ELSE NULL END) as total_talktime'),
];


$request = $this->ci->input->post();

$dates = explode(' - ', $request['dates']);
$from_date = count($dates) == 2 ? Carbon::createFromFormat('Y-m-d', $dates[0])->startOfDay()->format('Y-m-d H:i:s') : null;
$to_date = count($dates) == 2 ? Carbon::createFromFormat('Y-m-d', $dates[1])->endOfDay()->format('Y-m-d H:i:s') : null;
$customer_admins = $request['customer_admins'] ?? null;
$client_id = $request['clientid'];
$status = $request['status'];

$query = M3cCall::query()
    ->select($selects)
    ->whereHas('staff.departments', fn($query) => $query->salesOnly())
    ->with([
        'staff'  => function ($builder) use ($customer_admins) {
            $builder->select('staffid', 'firstname', 'lastname')
                ->when($customer_admins, fn($qr) => $qr->where('staff.staffid', $customer_admins)->active());
        },
    ])
    ->when($customer_admins, function ($query) use ($customer_admins) {
        $query->whereHas('staff', fn($qr) => $qr->where('staff.staffid', $customer_admins));
    })
    ->when($client_id, function ($query) use ($client_id) {
        $query->where(function ($subQuery) use ($client_id) {
            $subQuery->whereHas('client', fn($qr) => $qr->where('clients.userid', $client_id))
                ->orWhereHas('contact.client', fn($qr) => $qr->where('clients.userid', $client_id));
        });
    })
    ->when($status, fn($qr) => $qr->where('3c_call.call_status', $status))
    ->when($from_date, fn($qr) => $qr->whereBetween('3c_call.start_time', [
        $from_date,
        $to_date,
    ]))
    ->groupBy('3c_call.staff_id');

$result = data_tables_eloquent_init($query, $sortFilterCols);

$output  = $result['output'];
$rResult = $result['rResult'];


foreach ($rResult as $key => $aRow) {
    $row = [
        $aRow->staff ? '<a href=" ' . admin_url('profile/' . $aRow->staff->staffid) . '">' . $aRow->staff->full_name . '</a>' : '',
        $aRow['total_callout'] ?? 0,
        $aRow['total_callout_success'] ?? 0,
        $aRow['total_callout_rejected'] ?? 0,
        $aRow['total_callin'] ?? 0,
        $aRow['total_talktime'] ? talktime_to_time_format($aRow['total_talktime']) : '00:00'
    ];

    $output['aaData'][] = $row;
}

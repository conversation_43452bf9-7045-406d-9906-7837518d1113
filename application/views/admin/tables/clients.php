<?php

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

$hasPermissionDelete = has_permission('customers', '', 'delete');

$custom_fields = get_table_custom_fields('customers');
$this->ci->db->query("SET sql_mode = ''");


$aColumns = array_merge(
    [
        '1',
        db_prefix() . 'clients.userid as userid',
        // ID AMS
        '(select GROUP_CONCAT(ams_company_id SEPARATOR ", ") from ' . db_prefix() . 'client_ams_companies ams where ams.client_id=' . db_prefix() . 'clients.userid) as ams_company_id',
        db_prefix() . 'clients.business_name as business_name',
        db_prefix() . 'clients.phonenumber as phonenumber',
        db_prefix() . 'clients.company_status as company_status',
        db_prefix() . 'customer_admins.expired_at as cus_a_expired_at',
        db_prefix() . 'clients.type_of_customer as type_of_customer',
        db_prefix() . 'clients.usage_behavior as usage_behavior',
        '1',
        // <PERSON>ức độ tiềm năng gần nhất 
        '(select potential_rate from ' . db_prefix() . 'notes notes where notes.rel_id=' . db_prefix() . 'clients.userid AND notes.rel_type="customer" order by id desc limit 0,1) as potential_rate',
        // Note
        '(select CONCAT(notes.id,"|",notes.dateadded,"|",notes.addedfrom,"|",(CASE WHEN notes.action IS NULL THEN "0" ELSE notes.action END),"|",(CASE WHEN notes.potential_rate IS NULL THEN "0" ELSE notes.potential_rate END),"|",notes.description,"|",(CASE WHEN feedback.note_id IS NULL THEN "No" ELSE "Yes" END),"|",(CASE WHEN notes.reminder_calendar IS NULL THEN "0" ELSE notes.reminder_calendar END),"|",notes.type) from ' . db_prefix() . 'notes notes LEFT JOIN tblfeedback_notes feedback ON feedback.note_id = notes.id where notes.rel_id=' . db_prefix() . 'clients.userid AND notes.rel_type="customer" AND notes.type IN(1, 2) order by notes.id desc limit 0,1) as note',
        db_prefix() . 'customer_admins.staff_id as cus_a_staff_id',
        'cr.created_at as created_at',

        db_prefix() . 'clients.company as company',
        db_prefix() . 'clients.vat as vat',
        db_prefix() . 'clients.website as website',
        
        db_prefix() . 'clients.salesperson_id as salesperson_id',
        db_prefix() . 'clients.active as active',
        db_prefix() . 'clients.datecreated as datecreated',
        db_prefix() . 'favorite_clients.status as favorite_status',
        'cr.leader_id as cr_leader_id',
        'cr.sale_id as cr_sale_id',
        
        db_prefix() . 'contacts.fullname as fullname',
        db_prefix() . 'contacts.title as title_position',
        db_prefix() . 'contacts.email as email',
        db_prefix() . 'contacts.status as contact_status',
        db_prefix() . 'contacts.phonenumber as phonenumber_contact',
        db_prefix() . 'customer_admins.date_assigned as cus_a_date_assigned',
        db_prefix() . 'customer_admins.contacted as cus_a_contacted',
    ]
);

$additionalColumns = [
    db_prefix() . 'contacts.id as contact_id',
    db_prefix() . 'clients.zip as zip',
    'registration_confirmed',
    db_prefix() . 'invoices.id as invoice_id',
    // Note
    '(select DATEDIFF(NOW(), dateadded) from tblnotes notes where notes.rel_id = tblclients.userid AND notes.rel_type = "customer" order by notes.id desc limit 1) as note_day_counts'
];

$sIndexColumn = 'userid';
$sTable       = db_prefix() . 'clients';
$where        = [];
$sGroupBy     = '';
// Add blank where all filter can be stored
$filter = [];
$start_of_month = $this->ci->input->post('start_of_month');
$end_of_month = $this->ci->input->post('end_of_month');

$join = [
    'LEFT JOIN ' . db_prefix() . 'contacts ON ' . db_prefix() . 'contacts.userid=' . db_prefix() . 'clients.userid AND ' . db_prefix() . 'contacts.is_primary=1',
    'LEFT JOIN ' . db_prefix() . 'favorite_clients ON ' . db_prefix() . 'favorite_clients.client_id=' . db_prefix() . 'clients.userid AND ' . db_prefix() . 'favorite_clients.status=1',
    'LEFT JOIN ' . db_prefix() . 'staff ON ' . db_prefix() . 'staff.staffid=' . db_prefix() . 'clients.salesperson_id',
];

foreach ($custom_fields as $key => $field) {
    $selectAs = (is_cf_date($field) ? 'date_picker_cvalue_' . $key : 'cvalue_' . $key);
    array_push($customFieldsColumns, $selectAs);
    array_push($join, 'LEFT JOIN ' . db_prefix() . 'customfieldsvalues as ctable_' . $key . ' ON ' . db_prefix() . 'clients.userid = ctable_' . $key . '.relid AND ctable_' . $key . '.fieldto="' . $field['fieldto'] . '" AND ctable_' . $key . '.fieldid=' . $field['id']);

    if ($field['id'] == SALES_PERSON_CUSTOM_FIELD) {
        array_push($aColumns, 'concat(staff.firstname, " ", staff.lastname) as cvalue_' . $key);
        array_push($join, 'LEFT JOIN ' . db_prefix() . 'staff as staff ON staff.staffid = ctable_' . $key . '.value');
    } else {
        array_push($aColumns, 'ctable_' . $key . '.value as ' . $selectAs);
    }
}

array_push($join,
    'LEFT JOIN ' . db_prefix() . 'customer_admins ON ' .
        db_prefix() . 'customer_admins.customer_id = ' . db_prefix() . 'clients.userid'
);

array_push($join, 'LEFT JOIN ' . db_prefix() . 'client_requests cr ON ' . db_prefix() . 'clients.userid = cr.client_id');

$join = hooks()->apply_filters('customers_table_sql_join', $join);

// Filter by custom groups
$groups   = $this->ci->clients_model->get_groups();
$groupIds = [];
foreach ($groups as $group) {
    if ($this->ci->input->post('customer_group_' . $group['id'])) {
        array_push($groupIds, $group['id']);
    }
}
if (count($groupIds) > 0) {
    array_push($filter, 'AND ' . db_prefix() . 'clients.userid IN (SELECT customer_id FROM ' . db_prefix() . 'customer_groups WHERE groupid IN (' . implode(', ', $groupIds) . '))');
}

$countries  = $this->ci->clients_model->get_clients_distinct_countries();
$countryIds = [];
foreach ($countries as $country) {
    if ($this->ci->input->post('country_' . $country['country_id'])) {
        array_push($countryIds, $country['country_id']);
    }
}
if (count($countryIds) > 0) {
    array_push($filter, 'AND country IN (' . implode(',', $countryIds) . ')');
}


$this->ci->load->model('invoices_model');
// Filter by invoices
$invoiceStatusIds = [];
foreach ($this->ci->invoices_model->get_statuses() as $status) {
    if ($this->ci->input->post('invoices_' . $status)) {
        array_push($invoiceStatusIds, $status);
    }
}
if (count($invoiceStatusIds) > 0) {
    array_push($filter, 'AND ' . db_prefix() . 'clients.userid IN (SELECT clientid FROM ' . db_prefix() . 'invoices WHERE status IN (' . implode(', ', $invoiceStatusIds) . '))');
}

// Filter by estimates
$estimateStatusIds = [];
$this->ci->load->model('estimates_model');
foreach ($this->ci->estimates_model->get_statuses() as $status) {
    if ($this->ci->input->post('estimates_' . $status)) {
        array_push($estimateStatusIds, $status);
    }
}
if (count($estimateStatusIds) > 0) {
    array_push($filter, 'AND ' . db_prefix() . 'clients.userid IN (SELECT clientid FROM ' . db_prefix() . 'estimates WHERE status IN (' . implode(', ', $estimateStatusIds) . '))');
}

// Filter by projects
$projectStatusIds = [];
$this->ci->load->model('projects_model');
foreach ($this->ci->projects_model->get_project_statuses() as $status) {
    if ($this->ci->input->post('projects_' . $status['id'])) {
        array_push($projectStatusIds, $status['id']);
    }
}
if (count($projectStatusIds) > 0) {
    array_push($filter, 'AND ' . db_prefix() . 'clients.userid IN (SELECT clientid FROM ' . db_prefix() . 'projects WHERE status IN (' . implode(', ', $projectStatusIds) . '))');
}

// Filter by proposals
$proposalStatusIds = [];
$this->ci->load->model('proposals_model');
foreach ($this->ci->proposals_model->get_statuses() as $status) {
    if ($this->ci->input->post('proposals_' . $status)) {
        array_push($proposalStatusIds, $status);
    }
}
if (count($proposalStatusIds) > 0) {
    array_push($filter, 'AND ' . db_prefix() . 'clients.userid IN (SELECT rel_id FROM ' . db_prefix() . 'proposals WHERE status IN (' . implode(', ', $proposalStatusIds) . ') AND rel_type="customer")');
}

// Filter by having contracts by type
$this->ci->load->model('contracts_model');
$contractTypesIds = [];
$contract_types   = $this->ci->contracts_model->get_contract_types();

foreach ($contract_types as $type) {
    if ($this->ci->input->post('contract_type_' . $type['id'])) {
        array_push($contractTypesIds, $type['id']);
    }
}
if (count($contractTypesIds) > 0) {
    array_push($filter, 'AND ' . db_prefix() . 'clients.userid IN (SELECT client FROM ' . db_prefix() . 'contracts WHERE contract_type IN (' . implode(', ', $contractTypesIds) . '))');
}

// Filter by proposals
$customAdminIds = [];
foreach ($this->ci->clients_model->get_customers_admin_unique_ids() as $cadmin) {
    if ($this->ci->input->post('responsible_admin_' . $cadmin['staff_id'])) {
        array_push($customAdminIds, $cadmin['staff_id']);
    }
}

if (count($customAdminIds) > 0) {
    array_push($filter, 'AND ' . db_prefix() . 'clients.userid IN (SELECT customer_id FROM ' . db_prefix() . 'customer_admins WHERE staff_id IN (' . implode(', ', $customAdminIds) . '))');
}

if ($this->ci->input->post('requires_registration_confirmation')) {
    array_push($filter, 'AND ' . db_prefix() . 'clients.registration_confirmed=0');
}

if ($typeOfCus = $this->ci->input->post('type_of_customer')) {
    array_push($filter, 'AND ' . db_prefix() . 'clients.type_of_customer="' . $typeOfCus . '"');
}

if ($cusStaffId = $this->ci->input->post('customer_admin')) {
    array_push($filter, 'AND ' . db_prefix() . 'clients.userid IN (SELECT customer_id FROM ' . db_prefix() . 'customer_admins WHERE staff_id = ' . $cusStaffId . ')');
}

if (count($filter) > 0) {
    array_push($where, 'AND (' . prepare_dt_filter($filter) . ')');
}

if (!has_permission('customers', '', 'view')) {
    array_push($where, 'AND ' . db_prefix() . 'clients.userid IN ('. permission_get_clients('customers') .')');
}

if ($this->ci->input->post('exclude_inactive')) {
    array_push($where, 'AND (' . db_prefix() . 'clients.active = 1 OR ' . db_prefix() . 'clients.active=0 AND registration_confirmed = 0)');
}

if ($this->ci->input->post('has_salesperson')) {
    array_push($where, 'AND (' . db_prefix() . 'clients.salesperson_id)');
}

if ($this->ci->input->post('department')) {
    array_push($join, 'JOIN ' . db_prefix() . 'staff_departments ON ' . db_prefix() . 'staff_departments.staffid=' . db_prefix() . 'customer_admins.staff_id 
    AND ' . db_prefix() . 'staff_departments.departmentid = ' . $this->ci->input->post('department'));
}

if ($this->ci->input->post('my_customers')) {
    array_push($where, 'AND ' . db_prefix() . 'clients.userid IN (SELECT customer_id FROM ' . db_prefix() . 'customer_admins WHERE staff_id=' . get_staff_user_id() . ')');
}

if ($this->ci->input->post('contacted')) {
    array_push($join, 'JOIN ' . db_prefix() . 'notes ON ' . db_prefix() . 'notes.rel_id=' . db_prefix() . 'clients.userid AND ' . db_prefix() . 'notes.rel_type="customer"
    AND dateadded >= "' . $start_of_month . '" AND dateadded  <= "' . $end_of_month . '"');
}

if ($this->ci->input->post('not_contacted')) {
    array_push($where, 'AND NOT EXISTS (
        SELECT * FROM ' . db_prefix() . 'notes a 
        WHERE a.rel_id = ' . db_prefix() . 'clients.userid AND a.rel_type="customer" AND dateadded >= "' . $start_of_month . '" AND dateadded <= "' . $end_of_month . '"
    )');
}

if ($this->ci->input->post('expired_customer')) {
    $this->ci->load->model('Staff_statistics_model');
    $staffIds = $this->ci->Staff_statistics_model->whereStaffByRole();
    $today = date('Y-m-d H:i:s');
    $last20Days = date('Y-m-d H:i:s', strtotime('+20 days'));
    $where[] = 'AND ' . db_prefix() . 'clients.userid IN 
        (
            SELECT customer_id 
            FROM ' . db_prefix() . 'customer_admins 
            WHERE expired_at BETWEEN "' . $today . '" AND "' . $last20Days . '"
            ' . (count($staffIds) > 0 ? ' AND staff_id IN (' . implode(',', $staffIds) . ')' : '') . '
        )';
}

if ($day_of_note = $this->ci->input->post('day_of_note')) {
    if ($day_of_note == -1) {
        $where[] = 'AND ' . db_prefix() . 'clients.userid NOT IN (select rel_id from ' . db_prefix() . 'notes t where t.rel_type = "customer")';
    } else {
        [$start, $end] = explode('_', $day_of_note);
        $startDate = Carbon::createFromFormat('YmdHis', $start);
        $endDate = Carbon::createFromFormat('YmdHis', $end);
        $where[] = 'AND ' . db_prefix() . 'clients.userid IN 
        (
            SELECT rel_id 
            FROM ' . db_prefix() . 'notes 
            WHERE id in (
                select MAX(id) as latest_contact_id from ' . db_prefix() . 'notes t where t.rel_type = "customer" group by rel_id
            )
            and dateadded between "'.$startDate.'" and "'.$endDate.'"
        )';
    }
}

array_push(
    $join,
    ($this->ci->input->post('have_invoice') ? '' : 'LEFT ') . 'JOIN ' .
        db_prefix() . 'invoices ON ' . db_prefix() . 'invoices.clientid=' . db_prefix() . 'clients.userid ' .
        ($this->ci->input->post('have_invoice') ? 'AND date >= "' . $start_of_month . '" AND date <= "' . $end_of_month . '"' : '')
);
if ($this->ci->input->post('no_invoice')) {
    array_push($where, 'AND NOT EXISTS (
            SELECT * FROM ' . db_prefix() . 'invoices a 
            WHERE a.clientid = ' . db_prefix() . 'clients.userid AND date >= "' . $start_of_month . '" AND date <= "' . $end_of_month . '"
        )');
}
$sGroupBy = 'GROUP BY userid';

$aColumns = hooks()->apply_filters('customers_table_sql_columns', $aColumns);

// Fix for big queries. Some hosting have max_join_limit
if (count($custom_fields) > 4) {
    @$this->ci->db->query('SET SQL_BIG_SELECTS=1');
}
$where[] = 'AND ' . db_prefix() . 'clients.approved_at IS NOT NULL';

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, $additionalColumns, $sGroupBy);

$output  = $result['output'];
$rResult = $result['rResult'];
$this->ci->load->helper('date');

$array_company_status = get_all_company_statuses();

// Danh sách nhân viên
$list_staff = get_staff_all();

foreach ($rResult as $aRow) {

    $row = [];

    // Bulk actions
    $row[] = '<div class="checkbox"><input type="checkbox" value="' . $aRow['userid'] . '"><label></label></div>';

    // User id
    $dataUserId = $aRow['userid'];
    if ($aRow['cus_a_staff_id']) {
        $dataUserId .= '<br>';
        if ($aRow['favorite_status']) {
            $dataUserId .= '<a href="#" onClick="unsetFavorite(' . $aRow['userid'] . ',' . $aRow['cus_a_staff_id'] . ')"><i style="color: #FFBF4E;font-size: 20px;" class="fa fa-star"></i></a>';
        } else {
            $dataUserId .= '<a href="#" onClick="setFavorite(' . $aRow['userid'] . ',' . $aRow['cus_a_staff_id'] . ')" data-toggle="modal" data-target="#add_favorite_modal"><i style="color: #BFCBD9;font-size: 20px;" class="fa fa-star-o"></i></a>';
        }
    }

    $row[] = $dataUserId;

    // ID AMS
    $row[] = ($aRow['ams_company_id']) ? $aRow['ams_company_id'] : '';

    // Company
    $company  = $aRow['business_name'];
    $isPerson = false;

    if ($company == '') {
        $company  = _l('no_company_view_profile');
        $isPerson = true;
    }

    $url = admin_url('clients/client/' . $aRow['userid']);

    if ($isPerson && $aRow['contact_id']) {
        $url .= '?contactid=' . $aRow['contact_id'];
    }

    $company = '<a target="_blank" href="' . $url . '">' . $company . '</a><br>' .
        ($aRow['company'] != null ? '<b>' . _l('client_request_company_short_name') . ': </b>' . $aRow['company'] . '<br>'  : '') .
        ($aRow['vat'] != null ? '<b>MST: </b>' . $aRow['vat'] . '<br>'  : '') .
        ($aRow['website'] != null ? '<b>Website: </b>' . $aRow['website'] . '<br>'  : '');

    $company .= '<div class="row-options">';
    $company .= '<a target="_blank" href="' . admin_url('clients/client/' . $aRow['userid'] . ($isPerson && $aRow['contact_id'] ? '?group=contacts' : '')) . '">' . _l('view') . '</a>';

    if ($aRow['registration_confirmed'] == 0 && is_admin()) {
        $company .= ' | <a href="' . admin_url('clients/confirm_registration/' . $aRow['userid']) . '" class="text-success bold">' . _l('confirm_registration') . '</a>';
    }
    if (!$isPerson) {
        $company .= ' | <a target="_blank" href="' . admin_url('clients/client/' . $aRow['userid'] . '?group=contacts') . '">' . _l('customer_contacts') . '</a>';
    }
    if ($hasPermissionDelete) {
        $company .= ' | <a href="' . admin_url('clients/delete/' . $aRow['userid']) . '" class="text-danger _delete">' . _l('delete') . '</a>';
    }

    $company .= '</div>';

    $row[] = $company;

    // Contact
    $phonenumber = $aRow['phonenumber'] != null ? $aRow['phonenumber'] : $aRow['phonenumber_contact'];
    $contact = ($aRow['contact_id'] != null ? '<a href="' . admin_url('clients/client/' . $aRow['userid'] . '?contactid=' . $aRow['contact_id']) . '" target="_blank">' . $aRow['fullname'] . '</a><br>'  : '') .
        ($aRow['title_position'] != null ? '<b>' . _l('contact_position') . ': </b>' . $aRow['title_position'] . '<br>' : '') .
        ($phonenumber != null ? '<b>' . _l('sdt') . ': </b><a class="call-phone" href="javascript:void(0);">' . $phonenumber . '</a><br>' : '') .
        ($aRow['email'] != null ? '<b>Email: </b><a href="mailto:' . $aRow['email'] . '">' . $aRow['email'] . '</a><br>' : '') .
        ($aRow['contact_status'] ? '<b>' . _l('contact_status') . ': </b>' . get_text_of_select_options($aRow['contact_status'], 'CONTACT_STATUS') : '');

    $row[] = $contact;

    // Trạng thái công ty
    $row[] = (isset($array_company_status[$aRow['company_status']])) ? $array_company_status[$aRow['company_status']]['text'] : '';

    // Trạng thái liên hệ
    $dataAdmin = '';
    $dataContacted = '';

    // Loại khách hàng topdev
    $row[] = $aRow['type_of_customer'];

    // Loại khách hàng thị trường
    $row[] = $aRow['usage_behavior'];

    // Custom fields add values
    foreach ($customFieldsColumns as $customFieldColumn) {
        $row[] = (strpos($customFieldColumn, 'date_picker_') !== false ? _d($aRow[$customFieldColumn]) : $aRow[$customFieldColumn]);
    }

    //// Note
    $dataNote = '';
    $note = explode('|', $aRow['note']);
    if($note && isset($note[1]) && $note[1])
    {        
        // CONCAT(notes.id,"|",notes.dateadded,"|",notes.addedfrom,"|",notes.action,"|",notes.potential_rate,"|",notes.description,"|",(select CASE WHEN feedback.note_id IS NULL THEN "No" ELSE "Yes" END),"|",notes.reminder_calendar,notes.type)
        $full_name = $list_staff[$note[2]]['full_name'];
        $action_text = get_text_of_select_options($note[3], 'ACTION_NOTE');

        $dataNote .= ((is_leader_member() || $note[6] == 'Yes') ? '<a href="#" onClick="addFeedback(' . $note[0] . ')" data-toggle="modal" data-target="#add_feedback_modal"><i style="color: black;" class="fa fa-comments-o"></i> </a>' : '') .
        '<b>' . $full_name . '</b> (' . $note[1] . ') <br>';

        //$dataNote .= $aRow['note'] . '<br>';

        if ($note[8] ==  $this->ci->misc_model->CUSTOMER_TYPE_CONTACT_SUCCESS) {
            $dataNote .= '<ul>
                    <li>- ' . _l('action') . ': ' . $action_text . ' </li>
                    <li>- ' . _l('note') . ': ' . $note[5] . ' </li>
                </ul>';
        } elseif ($note[8] ==  $this->ci->misc_model->CUSTOMER_TYPE_CONTACT_AGAIN) {
            $dataNote .= '<ul>
                    <li>- ' . _l('reminder_calendar') . ': ' . $note[7] . ' </li>
                    <li>- ' . _l('note') . ': ' . $note[5] . ' </li>
                </ul>';
        }
        $dataNote .= '<b>' . _l('note_day_counts', $aRow['note_day_counts']) . '</b>';
    } else {
        if (isset($aRow['note_day_counts'])) {
            $dataNote = '<b>' . _l('note_day_counts', $aRow['note_day_counts']) . '</b>';
        } else {
            $dataNote = 'No Contact';
        }
    }

    $dataNote .= '
        <div class="row-options dropdown">
            <div>
                <a href="#" class="dropdown-toggle" id="note-dropdown" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                ' . _l('contact_note') . '
                    <span class="caret"></span>
                </a>
                <ul class="dropdown-menu" aria-labelledby="note-dropdown">
                    <li>
                        <a href="#" class="btn-add-note" onclick="showNoteForm(' . $aRow['userid'] . ', ' . $this->ci->misc_model->CUSTOMER_TYPE_CONTACT_SUCCESS . '); return false;">' . _l('contact_success_note') . '</a>
                    </li>
                    <li>
                        <a href="#" class="btn-add-note" onclick="showNoteForm(' . $aRow['userid'] . ', ' . $this->ci->misc_model->CUSTOMER_TYPE_CONTACT_AGAIN . '); return false;">' . _l('contact_again_note') . '</a>
                    </li>
                </ul>
            </div>
            <div>
                <a href="#" class="text-dark" onclick="showInternalNote(' . $aRow['userid'] . '); return false;">' . _l('internal_note') . '</a>
            </div>
        </div>
    ';
    $row[] = $dataNote;

    // Mức độ tiềm năng gần nhất 
    $row[] = ($aRow['potential_rate']) ? get_text_of_select_options($aRow['potential_rate'], 'POTENTIAL_RATE_NOTE') : '';

    // Customer admin
    $row[] = $dataAdmin;

    // Nguồn tạo
    $resource = ($aRow['created_at'] != null ? '<b>' . _l('project_datecreated') . ': </b>' . $aRow['created_at'] . '<br>' : '') .
        ($aRow['cr_sale_id'] != null ? '<b>' . _l('client_request_created_by') . ': </b>' . $list_staff[$aRow['cr_sale_id']]['full_name'] . '<br>' : '') .
        ($aRow['cr_leader_id'] != null ? '<b>' . _l('approve_by') . ': </b>' . $list_staff[$aRow['cr_leader_id']]['full_name'] . '<br>' : '');

    $row[] = $resource;

    $row['DT_RowClass'] = ($aRow['invoice_id'] ? 'has-row-options warning' : 'has-row-options');

    if ($aRow['registration_confirmed'] == 0) {
        $row['DT_RowClass'] .= ' alert-info requires-confirmation';
        $row['Data_Title']  = _l('customer_requires_registration_confirmation');
        $row['Data_Toggle'] = 'tooltip';
    }

    $row = hooks()->apply_filters('customers_table_row_data', $row, $aRow);

    $output['aaData'][] = $row;
}

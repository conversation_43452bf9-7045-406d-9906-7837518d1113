<?php

use Carbon\Carbon;
use Entities\CrawlerJob;

defined('BASEPATH') or exit('No direct script access allowed');

$sortFilterCols = [
    'posted_at',
    'from_page',
    'company',
    'title',
    'skills',
    'description',
    'salary',
    'location',
];

$selects = [
    'url',
];

$query = CrawlerJob::select(array_merge($selects, $sortFilterCols));

$request = $this->ci->input->post();

$from_date = $request['from_date'];
$to_date = $request['to_date'];
$crawl_page = $request['crawl_page'];

$query->when(
    $from_date,
    fn($qr) => $qr->where('posted_at', '>=', Carbon::parse($from_date)->startOfDay()->unix())
        ->where('posted_at', '<=', Carbon::parse($to_date ?: $from_date)->endOfDay()->unix())
)
    ->when($crawl_page, function ($query) use ($crawl_page) {
        $query->where('from_page', $crawl_page);
    });


$result = data_tables_eloquent_init($query, $sortFilterCols);

$output  = $result['output'];
$rResult = $result['rResult'];
$fromPages = collect(get_crawl_pages())->pluck('text', 'value');

try {
    foreach ($rResult as $key => $aRow) {
        $row = [];

        $shortDescription = $description = $aRow['description'];
        if (strlen(strip_html_tags($description)) > 500) {
            $shortDescription = strip_html_tags(substr($description, 0, 500));
            $shortDescription .= "...<br>";
            $shortDescription .= '<div class="full-description hide">' . $description . '</div>';
            $shortDescription .= '<a href="#" data-toggle="modal" data-target="#view_job_description" onclick="viewFullDescription(this, \'' . $aRow['title'] . '\')">View more</a>';
        }

        $row[] = Carbon::parse($aRow['posted_at'])->setTimezone('Asia/Ho_Chi_Minh')->format('Y-m-d');
        $row[] = $fromPages->get($aRow['from_page']);
        $row[] = $aRow['company'];
        $row[] = '<a target="_blank" href="' . $aRow['url'] . '">' . $aRow['title'] . '</a>';
        $row[] = $aRow['skills'];
        $row[] = $shortDescription;
        $row[] = $aRow['salary'];
        $row[] = $aRow['location'];

        $output['aaData'][] = $row;
    }
} catch (\Exception $ex) {
    dd($ex);
}

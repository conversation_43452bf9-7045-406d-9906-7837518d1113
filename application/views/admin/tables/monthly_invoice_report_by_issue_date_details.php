<?php

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

/*
* Output
*/
$CI          = &get_instance();
$post      = $CI->input->post();

$issueMonth = $post['issue_month_detail'] ?? null;
$reportMonth = $post['report_month_detail'] ?? null;
$advancedAmount = $post['advanced_amount'] ?? 0;
$invoiceAmount = $post['invoice_amount'] ?? 0;

if (empty($issueMonth) || empty($reportMonth)) {
    $output = [
        'draw'                 => $post['draw'] ? intval($post['draw']) : 0,
        'iTotalRecords'        => 0,
        'iTotalDisplayRecords' => 0,
        'aaData'               => [],
    ];
} else {
    [$invoices, $amsJobs, $credits, $invoicePaidItems] = fetch_monthly_report_by_month($issueMonth);
    $details = collect();
    if ($invoiceAmount) {
        $details = get_advanced_invoice_details_by_report_month(
            $invoices,
            $amsJobs,
            $credits,
            $invoicePaidItems
        );
        $firstOfIssueMonth = Carbon::createFromFormat('Y.m', $issueMonth)->startOfMonth()->startOfDay();
        $thisMonth = Carbon::now()->startOfMonth()->startOfDay();
        do {
            $details = $details->merge(get_invoice_details_by_report_month(
                $invoices,
                $amsJobs,
                $credits,
                $invoicePaidItems,
                $firstOfIssueMonth->format('Y.m')
            ));
            $firstOfIssueMonth = $firstOfIssueMonth->addMonth();
        } while ($firstOfIssueMonth->lte($thisMonth));
    } else {
        $details = $advancedAmount
            ? get_advanced_invoice_details_by_report_month(
                $invoices,
                $amsJobs,
                $credits,
                $invoicePaidItems
            )
            : get_invoice_details_by_report_month(
                $invoices,
                $amsJobs,
                $credits,
                $invoicePaidItems,
                $reportMonth
            );
    }

    $output = [
        'draw'                 => $post['draw'] ? intval($post['draw']) : 0,
        'iTotalRecords'        => $details->count(),
        'iTotalDisplayRecords' => $details->count(),
        'aaData'               => $details->sortBy([
                ['issued_date', 'desc'],
                ['posted_date', 'desc'],
                ['invoice_id', 'desc'],
            ])
            ->values()
            ->map(function ($invoice, $index) {
                return [
                    $index + 1,
                    $invoice['posted_date'],
                    '<a href="' . admin_url('invoices#' . $invoice['invoice_id']) . '" target="_blank">' . $invoice['invoice_no'] . '</a>',
                    isset($invoice['invoice_number']) ? intval($invoice['invoice_number']) : '',
                    format_invoice_status($invoice['invoice_status']),
                    $invoice['issued_date'],
                    '<a href=" ' . admin_url('profile/' . $invoice['staff_id']) . '" target="_blank">' . $invoice['sale_name'] . '</a>'
                ];
            }),
    ];
}

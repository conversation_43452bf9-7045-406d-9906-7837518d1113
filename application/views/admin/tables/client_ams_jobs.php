<?php

use app\services\AmsService;
use app\services\AmsPackageService;
use Entities\ClientAmsCompany;
use Entities\ClientAmsJob;
use Entities\Invoice;

defined('BASEPATH') or exit('No direct script access allowed');

$this->ci->load->helper('date');

const AMS_JOB_STATUS_DRAFT = 0;
const AMS_JOB_STATUS_CLOSED = 1;
const AMS_JOB_STATUS_REVIEW = 2;
const AMS_JOB_STATUS_OPEN = 3;

$output = [
    'draw' => $this->ci->input->post('draw') ?? 1,
    'iTotalRecords' => 0,
    'iTotalDisplayRecords' => 0,
    'aaData' => [],
];
$page = (intval($this->ci->input->post('start')) / 20) + 1;
// Fetch ams companies first
$queries = array_filter([
    'fields' => [
        'job' => join(',', [
            'id',
            'title',
            'detail_url',
            'addresses',
            'crm_invoice_id',
            'num_ready_candidates',
            'created_by',
            'status',
            'status_display',
            'service_list',
            'package_list',
            'candidates_count', // Logged in to view
            'num_viewers', // Logged in to view
            'published',
            'closed',
            'from_crm'
        ])
    ],
    'all_jobs' => true,
    'page' => $page,
    'page_size' => 20,
    'ordering' => 'crm_invoice_id'
]);

$searchById = false;
$amsIds = collect([]);
$clientIdForJob = $clientId;

// Custom search value by company id
if ($company = $this->ci->input->post('company')) {
    $queries['companies'] = $company;
    $amsIds = collect([$company]);
    $clientIdForJob = null;
} elseif ($crmCompany = $this->ci->input->post('crm_company')) {
    $clientIdForJob = $crmCompany;
    $searchById = true;
    // Need get ams companies that have invoice
    $amsIds = ClientAmsJob::select('ams_company_id')
        ->where('client_id', $crmCompany)
        ->whereNotNull('invoice_id')
        ->get()
        ->pluck('ams_company_id');

    $queries['companies'] = $amsIds->join(',');
} else {
    $searchById = true;
    $amsIds = ClientAmsCompany::select('ams_company_id')->where('client_id', $clientId)->get()->pluck('ams_company_id');
    $queries['companies'] = $amsIds->join(',');
}

if (!$searchById || ($searchById && $amsIds->isNotEmpty())) {
    $amsJobExcludeIds = $clientIdForJob ? ClientAmsJob::select('ams_job_id')
        ->whereIn('ams_company_id', $amsIds)
        ->where('client_id', '<>', $clientIdForJob)
        ->get()
        ->pluck('ams_job_id') : collect([]);

    if ($amsJobExcludeIds->count()) {
        $queries['except_ids'] = $amsJobExcludeIds->join(',');
    }

    // Custom search value by status
    if ($status = $this->ci->input->post('status')) {
        $queries['status'] = map_ams_job_status($status);
    }

    // Custom search value by service
    if ($package = $this->ci->input->post('package')) {
        $queries['package_ids'] = $package;
    }

    // Custom search value by packacge
    if ($service = $this->ci->input->post('service')) {
        $queries['service_ids'] = $service;
    }

    $res = AmsService::search('jobs', [
        'query' => $queries
    ]);

    $jobs = $res['data'] ?? [];

    if (!empty($jobs)) {
        $rows = [];
        $jobStatuses = collect(['default', 'warning', 'info', 'success']);
        $invoiceIds = (array) array_pluck($jobs, 'crm_invoice_id');
        $invoices = collect([]);
        $crmClientsInvoices = collect([]);
        if (count($invoiceIds)) {
            $invoices = Invoice::whereIn('id', $invoiceIds)
                ->with('client:userid,business_name')
                ->get()
                ->keyBy('id');

            $crmClientsInvoices = Invoice::select('id')
                ->whereIn('id', $invoiceIds)
                ->where('clientid', $clientId)
                ->get()
                ->keyBy('id');
        }
        $amsJobIds = (array) array_pluck($jobs, 'id');
        $ownedJobs = collect([]);
        if (count($amsJobIds)) {
            $ownedJobs = ClientAmsJob::select('id', 'ams_job_id', 'invoice_id', 'free_package','package_id')
                ->whereIn('ams_job_id', $amsJobIds)
                ->where('client_id', $clientId)
                ->with('itemable.item')
                ->get()
                ->keyBy('ams_job_id');
        }
        $availablePackages = AmsPackageService::get_available_packages($amsIds->first() ?? 0, null, true);
        foreach ($jobs as $job) {
            $availablePackage = optional($ownedJobs->get($job['id']))->package_id
                ? ""
                : (end($job['package_list']) ? $availablePackages->where('ams_package_id', end($job['package_list'])['id'])->first() : "");
            $row = [];
            $invoiceId = $job['crm_invoice_id'];
            $packages = optional($ownedJobs->get($job['id']))->itemable->item->description ?? optional($ownedJobs->get($job['id']))->itemable->description ?? $availablePackage['description'] ?? "";
            $services = $job['service_list'];
            $invoice = $invoices->get($invoiceId, null);
            $isFreePackage = optional($ownedJobs->get($job['id']))->free_package ?? false;

            // CRM INV
            $row[] = $invoice ? (
                '<a href="' . admin_url('invoices/pdf/' . $invoice->id) . '?output_type=I" target="_blank">' . format_invoice_number($invoice) . '</a>'
            ) : '';
            // CRM Status
            $row[] = ($invoice && $invoice->isPaid() ? ('<b>'._l('ams_jobs_row_paid_by').'</b>: ' . $invoice->client->business_name) : '');
            // ID AMS
            $row[] = has_permission('customers', '', 'view_ams_company') ?
                '<a href="'.AMS_URL.'/admin/jobs/'.$job['id'].'/edit" target="_blank">'.$job['id'].'</a>' :
                $job['id'];
            // Job
            $row[] = '<b><a href="'.$job['detail_url'].'" target="_blank">'.$job['title'] . '</a></b><br>'.
                '<b>'._l('ams_jobs_row_location').'</b>: ' . ($job['addresses']['address_region_list'] ?? '') . '<br>'.
                ($isFreePackage ? '<span class="label bg-success">'._l('ams_job_manage_invoice_free_tag').'</span>' : '')
                ;
            // Package
            $row[] = $packages;
            // Service
            $row[] = join('<br><br>', array_filter(array_map(function ($service) use ($job) {
                $now = date('Y-m-d H:i:s');
                if ($service['expires_at'] && $service['expires_at'] < $now) {
                    return null;
                }
                $diffDays = $service['expires_at'] ? ($now < $service['expires_at'] ? (diff_datetime($now, $service['expires_at'])) : null) : -1;
                return join('<br>', [
                    '<b>'.$service['name'].'</b>',
                    '<b>'._l('ams_jobs_row_service_active_at').'</b>: ' . (
                        (empty($job['published']['date']) || to_date_format($service['created_at'], 'Y-m-d H:i:s', 'Ymd') > to_date_format($job['published']['date'], 'd-m-Y', 'Ymd')) ? (
                            (!empty($service['created_at']) ? to_date_format($service['created_at'], 'Y-m-d H:i:s', 'd/m/Y') : '')
                        ) : (
                            (!empty($job['published']['date']) ? to_date_format($job['published']['date'], 'd-m-Y', 'd/m/Y') : '')
                        )
                    ),
                    '<b>'._l('ams_jobs_row_expires_at').'</b>: ' . ($diffDays !== null ? ($diffDays == -1 ? _l('ams_companies_row_service_unlimited') : ($diffDays > 1 ? ($diffDays . ' days') : ($diffDays . ' day'))) : ''),
                ]);
            }, $services)));
            // Views
            $row[] = $job['num_viewers'] ?? 0;
            // Apply
            $row[] = $job['candidates_count'] ?? 0;
            // Total Ready CV
            $row[] = $job['num_ready_candidates'] ?? 0;
            // Published date
            $now = date('Y-m-d');
            $closed = !empty($job['closed']['date']) ? to_date_format($job['closed']['date'], 'd-m-Y', 'Y-m-d') : null;
            $published = !empty($job['published']['date']) ? to_date_format($job['published']['date'], 'd-m-Y', 'd/m/Y') : null;
            $diffDays = ($closed && $now < $closed) ? diff_datetime($now, $closed) : null;
            $row[] = join("<br><br>", [
                '<b>'._l('ams_jobs_row_published_date').'</b>: ' . ($published ? '<br>'.$published : ''),
                '<b>'._l('ams_jobs_row_expired_date').'</b>: ' . ($closed ? '<br>'.(to_date_format($closed, 'Y-m-d', 'd/m/Y') . ' <br>(' . ($diffDays ? _l('ams_jobs_row_expired_in_days', $diffDays) : _l('ams_jobs_row_expired_now')).')') : ''),
                '<b>'._l('ams_jobs_row_created_by').'</b>: <br>' . $job['created_by']
            ]);
            $row[] = $published ?? '';
            $row[] = ($closed ? (to_date_format($closed, 'Y-m-d', 'd/m/Y') . ' (' . ($diffDays ? _l('ams_jobs_row_expired_in_days', $diffDays) : _l('ams_jobs_row_expired_now')).')') : '');
            $row[] = $job['created_by'] ?? '';

            $color = $jobStatuses->get($job['status'], 'default');
            // Status
            $row[] = '<span class="label label-' .$color. '">' . ($job['status_display'] ?? '') . '</span>';
            $ownedInvoice = $job['crm_invoice_id'] ? $crmClientsInvoices->get($job['crm_invoice_id']) : null;
            $row[] = has_permission('customers', '', 'edit_job_global') || (has_permission('customers', '', 'edit_job_owned') && empty($job['crm_invoice_id']) || !empty($ownedInvoice)) ? '<a href="'.admin_url('clients/client/'.$clientId.'?group=ams_post_job&ams_job_id='.$job['id']).'" class="btn btn-link"><i class="fa fa-pencil"></i></a>' : '';

            $rows[] = $row;
        }

        $output = [
            'draw' => $this->ci->input->post('draw') ?? 1,
            'iTotalRecords' => $res['meta']['total'],
            'iTotalDisplayRecords' => $res['meta']['total'],
            'aaData' => $rows,
        ];
    }
}

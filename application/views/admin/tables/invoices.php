<?php

use Entities\Staff;

defined('BASEPATH') or exit('No direct script access allowed');

$project_id = $this->ci->input->post('project_id');

$aColumns = [
    db_prefix() . 'invoices.id',
    '(' .db_prefix() . 'invoices.total' . ' - ' . db_prefix() . 'invoices.total_tax) as invoices_total_amount',
    'YEAR('. db_prefix() . 'invoices.date) as year',
    db_prefix() . 'invoices.date as date',
    db_prefix() . 'invoices.clientid',
    db_prefix() . 'estimates.deleted_customer_name as deleted_customer_name',
    db_prefix() . 'contacts.fullname as contact_name',
    db_prefix() . 'clients.vat as vat',
    'duedate',
    db_prefix() . 'invoicepaymentrecords.date as date_of_payment',
    db_prefix() . 'invoices.status',
    db_prefix() . 'clients.userid as userid',
    db_prefix() . 'invoices.customer_source as customer_source',
    db_prefix() . 'invoices.invoice_closing_date as invoice_closing_date',
    db_prefix() . 'invoices.type_of_customer as type_of_customer',
    db_prefix() . 'invoices.usage_behavior as usage_behavior',
    db_prefix() . 'invoices.addedfrom',
    db_prefix() . 'projects.name as project_name',
    '(SELECT GROUP_CONCAT(name SEPARATOR ",") FROM ' . db_prefix() . 'taggables JOIN ' . db_prefix() . 'tags ON ' . db_prefix() . 'taggables.tag_id = ' . db_prefix() . 'tags.id WHERE rel_id = ' . db_prefix() . 'invoices.id and rel_type="invoice" ORDER by tag_order ASC) as tags',
    db_prefix() . 'estimates.number as number',
    db_prefix() . 'estimates.total as total',
    db_prefix() . 'estimates.total_tax as total_tax',
    get_sql_select_client_company(),
];

$sIndexColumn = 'id';
$sTable       = db_prefix() . 'invoices';

$join = [
    'LEFT JOIN ' . db_prefix() . 'clients ON ' . db_prefix() . 'clients.userid = ' . db_prefix() . 'invoices.clientid',
    'LEFT JOIN ' . db_prefix() . 'currencies ON ' . db_prefix() . 'currencies.id = ' . db_prefix() . 'invoices.currency',
    'LEFT JOIN ' . db_prefix() . 'projects ON ' . db_prefix() . 'projects.id = ' . db_prefix() . 'invoices.project_id',
    'LEFT JOIN ' . db_prefix() . 'invoicepaymentrecords ON ' . db_prefix() . 'invoicepaymentrecords.invoiceid = ' . db_prefix() . 'invoices.id',
    'LEFT JOIN ' . db_prefix() . 'estimates ON ' . db_prefix() . 'estimates.invoiceid = ' . db_prefix() . 'invoices.id',
    'LEFT JOIN ' . db_prefix() . 'contacts ON ' . db_prefix() . 'contacts.id = ' . db_prefix() . 'invoices.contact_id',
    'LEFT JOIN ' . db_prefix() . 'customer_admins ON ' . db_prefix() . 'customer_admins.customer_id = ' . db_prefix() . 'clients.userid',
];

$custom_fields = get_table_custom_fields('invoice');

foreach ($custom_fields as $key => $field) {
    $selectAs = (is_cf_date($field) ? 'date_picker_cvalue_' . $key : 'cvalue_' . $key);

    array_push($customFieldsColumns, $selectAs);
    array_push($aColumns, 'ctable_' . $key . '.value as ' . $selectAs);
    array_push($join, 'LEFT JOIN ' . db_prefix() . 'customfieldsvalues as ctable_' . $key . ' ON ' . db_prefix() . 'invoices.id = ctable_' . $key . '.relid AND ctable_' . $key . '.fieldto="' . $field['fieldto'] . '" AND ctable_' . $key . '.fieldid=' . $field['id']);
}

array_push($aColumns, 'paymentExpiresDate.value as payment_expires_date');
array_push($join, 'LEFT JOIN ' . db_prefix() . 'customfieldsvalues as paymentExpiresDate ON ' . db_prefix() . 'invoices.id = paymentExpiresDate.relid AND paymentExpiresDate.fieldto="invoice" AND paymentExpiresDate.fieldid=14');

$where  = [];
$filter = [];

$custom_date_select = '';
$request = $this->ci->input->post();

$customer_admins = $request['customer_admins'] ?? '';
$department = $request['department'] ?? '';
$invoice_creator = $request['invoice_creator'] ?? '';
$status = $request['status'] ?? '';
$customer_source = $request['customer_source'] ?? '';
$type_of_customer = $request['type_of_customer'] ?? '';
$usage_behavior = $request['usage_behavior'] ?? '';
$from_date = $request['from_date'] ?? '';
$to_date = $request['to_date'] ?? '';
$project_tag_reference = $request['project_tag_reference'] ?? '';

if ($customer_admins) {
    array_push($filter, 'AND ' . db_prefix() . 'customer_admins.staff_id = ' .  $customer_admins);
}

if ($department) {
    array_push($join, 'JOIN ' . db_prefix() . 'staff_departments ON ' . db_prefix() . 'staff_departments.staffid=' . db_prefix() . 'customer_admins.staff_id 
    AND ' . db_prefix() . 'staff_departments.departmentid = ' . $department);
}

if ($invoice_creator) {
    array_push($filter, 'AND ' . db_prefix() . 'invoices.addedfrom = ' .  $invoice_creator);
}

if ($status) {
    array_push($filter, 'AND ' . db_prefix() . 'invoices.status = ' .  $status);
}

if ($customer_source) {
    array_push($filter, 'AND ' . db_prefix() . 'invoices.customer_source = "' .  $customer_source . '"');
}

if ($type_of_customer) {
    array_push($filter, 'AND ' . db_prefix() . 'invoices.type_of_customer = "' .  $type_of_customer . '"');
}

if ($usage_behavior) {
    array_push($filter, 'AND ' . db_prefix() . 'invoices.usage_behavior = "' .  $usage_behavior . '"');
}

if ($from_date) {
    $custom_date_select = get_query_filter_date($from_date, $to_date, db_prefix() . 'invoices.date');
}

if ($custom_date_select) {
    array_push($filter, 'AND ' . $custom_date_select);
}

if (isset($request['not_sent']) && $request['not_sent']) {
    array_push($filter, 'AND ' . db_prefix() . 'invoices.sent = 0 AND ' . db_prefix() . 'invoices.status NOT IN(' . Invoices_model::STATUS_PAID . ',' . Invoices_model::STATUS_CANCELLED . ')');
}
if (isset($request['not_have_payment']) && $request['not_have_payment']) {
    array_push($filter, 'AND ' . db_prefix() . 'invoices.id NOT IN(SELECT invoiceid FROM ' . db_prefix() . 'invoicepaymentrecords) AND ' . db_prefix() . 'invoices.status != ' . Invoices_model::STATUS_CANCELLED);
}
if (isset($request['recurring']) && $request['recurring']) {
    array_push($filter, 'AND recurring > 0');
}
if ($project_tag_reference) {
    $db_prefix = db_prefix();
    $queryFilterTag = "SELECT ${db_prefix}invoices.id FROM ${db_prefix}taggables JOIN ${db_prefix}tags ON ${db_prefix}taggables.tag_id = ${db_prefix}tags.id WHERE rel_id = ${db_prefix}invoices.id AND rel_type='invoice' AND ${db_prefix}tags.name LIKE '%${project_tag_reference}%'";
    array_push($filter, "AND ${db_prefix}invoices.id IN (${queryFilterTag})");
}

$agents    = $this->ci->invoices_model->get_sale_agents();
$agentsIds = [];
foreach ($agents as $agent) {
    if (isset($request['sale_agent_' . $agent['sale_agent']]) && $request['sale_agent_' . $agent['sale_agent']]) {
        array_push($agentsIds, $agent['sale_agent']);
    }
}
if (count($agentsIds) > 0) {
    array_push($filter, 'AND ' . db_prefix() . 'invoices.sale_agent IN (' . implode(', ', $agentsIds) . ')');
}

$modesIds = [];
foreach ($data['payment_modes'] as $mode) {
    if (isset($request['invoice_payments_by_' . $mode['id']]) && $request['invoice_payments_by_' . $mode['id']]) {
        array_push($modesIds, $mode['id']);
    }
}
if (count($modesIds) > 0) {
    array_push($where, 'AND ' . db_prefix() . 'invoices.id IN (SELECT invoiceid FROM ' . db_prefix() . 'invoicepaymentrecords WHERE paymentmode IN ("' . implode('", "', $modesIds) . '"))');
}

$years     = $this->ci->invoices_model->get_invoices_years();
$yearArray = [];
foreach ($years as $year) {
    if (isset($request['year_' . $year['year']]) && $request['year_' . $year['year']]) {
        array_push($yearArray, $year['year']);
    }
}
if (count($yearArray) > 0) {
    array_push($where, 'AND YEAR(' . db_prefix() . 'invoices.date) IN (' . implode(', ', $yearArray) . ')');
}

if (count($filter) > 0) {
    array_push($where, 'AND (' . prepare_dt_filter($filter) . ')');
}

if ($clientid != '') {
    array_push($where, 'AND ' . db_prefix() . 'invoices.clientid=' . $this->ci->db->escape_str($clientid));
}

if ($project_id) {
    array_push($where, 'AND project_id=' . $this->ci->db->escape_str($project_id));
}

if (!has_permission('invoices', '', 'view')) {
    array_push($where, 'AND ' . db_prefix() . 'invoices.clientid IN ('. permission_get_clients('invoices') .')');
}

// Addition discount approval condition
$dbPrefix = db_prefix();
$staffLeaderRole = Staff::ROLE_SALES_LEADER;

$sql = <<<EOD
    AND (
        {$dbPrefix}invoices.total = 0 OR
        {$dbPrefix}invoices.addition_discount_approved_at IS NOT NULL OR
        {$dbPrefix}invoices.discount_percent = 0 OR
        {$dbPrefix}invoices.status IN (2,5) OR
        ({$dbPrefix}invoices.addedfrom IN (
            SELECT staffid FROM {$dbPrefix}staff 
            WHERE (
                ({$dbPrefix}staff.role = {$staffLeaderRole}) AND {$dbPrefix}invoices.discount_percent <= 10) OR
                {$dbPrefix}staff.admin = 1 OR
                ({$dbPrefix}staff.staffid IN (select staff_id from {$dbPrefix}staff_permissions WHERE {$dbPrefix}staff_permissions.capability = "po_approval_as_manager" and {$dbPrefix}staff_permissions.feature = "invoices"))
            )
        ) OR
        (
            exists (select * from {$dbPrefix}minvoices where {$dbPrefix}invoices.id = {$dbPrefix}minvoices.invoice_id and invoice_issued_date is not null)
        )
    )
EOD;

$where[] = $sql;

$aColumns = hooks()->apply_filters('invoices_table_sql_columns', $aColumns);

// Fix for big queries. Some hosting have max_join_limit
if (count($custom_fields) > 4) {
    @$this->ci->db->query('SET SQL_BIG_SELECTS=1');
}

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [
    db_prefix() . 'invoices.id',
    db_prefix() . 'invoices.clientid',
    db_prefix() . 'currencies.name as currency_name',
    db_prefix() . 'estimates.project_id as project_id',
    db_prefix() . 'estimates.hash as hash',
    'recurring',
], 'group by ' . db_prefix() . 'invoices.id');
$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];

    $numberOutput = '';

    // If is from client area table
    if (is_numeric($clientid) || $project_id) {
        $numberOutput = '<a href="' . admin_url('invoices/list_invoices/' . $aRow['id']) . '" target="_blank">' . format_invoice_number($aRow['id']) . '</a>';
    } else {
        $numberOutput = '<a href="' . admin_url('invoices/list_invoices/' . $aRow['id']) . '" onclick="init_invoice(' . $aRow['id'] . '); return false;">' . format_invoice_number($aRow['id']) . '</a>';
    }

    if ($aRow['recurring'] > 0) {
        $numberOutput .= '<br /><span class="label label-primary inline-block mtop4"> ' . _l('invoice_recurring_indicator') . '</span>';
    }

    $numberOutput .= '<div class="row-options">';

    $numberOutput .= '<a href="' . site_url('invoice/' . $aRow['id'] . '/' . $aRow['hash']) . '" target="_blank">' . _l('view') . '</a>';
    if (has_permission('invoices', '', 'edit')) {
        $numberOutput .= ' | <a href="' . admin_url('invoices/invoice/' . $aRow['id']) . '">' . _l('edit') . '</a>';
    }
    $numberOutput .= '</div>';

    $row[] = $numberOutput;

    // $row[] = app_format_money($aRow['total'], $aRow['currency_name']);

    // $row[] = app_format_money($aRow['total_tax'], $aRow['currency_name']);
    
    // $row[] = app_format_money($aRow['total'] - $aRow['total_tax'], $aRow['currency_name']);
    
    $row[] = app_format_money($aRow['invoices_total_amount'], $aRow['currency_name']);

    $row[] = $aRow['year'];

    $row[] = _d($aRow['date']);

    $row[] = $aRow['clientid'];

    if (empty($aRow['deleted_customer_name'])) {
        $row[] = '<a href="' . admin_url('clients/client/' . $aRow['clientid']) . '">' . $aRow['company'] . '</a>';
    } else {
        $row[] = $aRow['deleted_customer_name'];
    }

    $row[] = $aRow['contact_name'];

    $row[] = $aRow['vat'];

    // $row[] = '<a href="' . admin_url('projects/view/' . $aRow['project_id']) . '">' . $aRow['project_name'] . '</a>';;

    $row[] = isset($aRow['payment_expires_date']) ? _d($aRow['payment_expires_date']) : '';

    $row[] = _d($aRow['date_of_payment']);

    $row[] = format_invoice_status($aRow[db_prefix() . 'invoices.status']);

    $customer_admins = $this->ci->clients_model->get_admins($aRow['userid']);
    $dataAdmin = '';

    if (count($customer_admins) > 0) {
        foreach ($customer_admins as $admin) {
            $dataAdmin .= '<a href=" ' . admin_url('profile/' . $admin['staff_id']) . '">' . get_staff_full_name($admin['staff_id']) . '</a><br>';
        }
    }
    $row[] = $dataAdmin;

    $row[] = $aRow['customer_source'];

    // Custom fields add values
    foreach ($customFieldsColumns as $customFieldColumn) {
        $row[] = (strpos($customFieldColumn, 'date_picker_') !== false ? _d($aRow[$customFieldColumn]) : $aRow[$customFieldColumn]);
    }

    $row[] = $aRow['invoice_closing_date'];

    $row[] = $aRow['type_of_customer'];

    $row[] = $aRow['usage_behavior'];

    $dataCreators = $this->ci->invoices_model->get_creator($aRow['id']);
    $dataCreator = '';

    if (count($dataCreators) > 0) {
        foreach ($dataCreators as $creator) {
            $dataCreator .= $creator['full_name'] . ($creator['customer_admin_id'] ? ' (' . $creator['firstname'] . ' ' . $creator['lastname'] . ')' : '');
        }
    }

    $row[] = $dataCreator;

    $row[] = render_tags($aRow['tags']);

    $row['DT_RowClass'] = 'has-row-options';

    $row = hooks()->apply_filters('invoices_table_row_data', $row, $aRow);

    $output['aaData'][] = $row;
}

<?php

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

/*
* Output
*/
$CI          = &get_instance();
$post      = $CI->input->post();

$month = $post['issue_month'] ?? null;

if (empty($month)) {
    $output = [
        'draw'                 => $post['draw'] ? intval($post['draw']) : 0,
        'iTotalRecords'        => 0,
        'iTotalDisplayRecords' => 0,
        'aaData'               => [],
    ];
} else {
    [$invoices, $amsJobs, $credits, $invoicePaidItems] = fetch_monthly_report_by_month($month);
    $invoicePaidItems = $invoicePaidItems->filter(fn($item) => $item->rate > 0)->values();
    [$reports, $invoiceQuantity, $invoiceAmount, $advancedAmount, $revenueAmount, $unpaidAmount] = get_total_amount_by_month($month, $invoices, $invoicePaidItems, $amsJobs, $credits);

    $reports = collect($reports);

    $output = [
        'draw'                 => $post['draw'] ? intval($post['draw']) : 0,
        'iTotalRecords'        => $reports->count(),
        'iTotalDisplayRecords' => $reports->count(),
        'aaData'               => $reports->sortKeys()
            ->map(function ($revenueAmount, $key) use ($month, $advancedAmount, $invoiceAmount) {
                $reportMonth = Carbon::createFromFormat('Ym', $key)->format('Y.m');
                return [
                    $month == $reportMonth ? $month : '',
                    $reportMonth,
                    $month == $reportMonth
                        ? ('<a href="javascript:;" class="revenue-amount" data-invoice-amount="' . $invoiceAmount . '" data-report-month="' . $reportMonth . '">' . number_format($invoiceAmount) . '<i class="fa fa-question-circle" style="margin-left: 5px;"></i><i class="fa fa-spinner fa-spin hide" style="margin-left: 5px;"></i></a>')
                        : '',
                    $month == $reportMonth
                        ? ('<a href="javascript:;" class="revenue-amount" data-advanced-amount="' . $advancedAmount . '" data-report-month="' . $reportMonth . '">' . number_format($advancedAmount) . '<i class="fa fa-question-circle" style="margin-left: 5px;"></i><i class="fa fa-spinner fa-spin hide" style="margin-left: 5px;"></i></a>')
                        : '',
                    '<a href="javascript:;" class="revenue-amount" data-report-month="' . $reportMonth . '">' . number_format($revenueAmount) . '<i class="fa fa-question-circle" style="margin-left: 5px;"></i><i class="fa fa-spinner fa-spin hide" style="margin-left: 5px;"></i></a>',
                ];
            })
            ->values(),
        'amounts' => [
            'invoice_quantity' => $invoiceQuantity,
            'invoice_amount' => round($invoiceAmount),
            'advanced_amount' => round($advancedAmount),
            'revenue_amount' => round($revenueAmount),
            'unpaid_amount' => round($unpaidAmount),
        ]
    ];
}

<?php

use app\services\AmsService;
use Carbon\Carbon;
use Entities\AmsPackage;
use Entities\ClientAmsCompany;
use Entities\Invoice;
use Entities\Item;
use Entities\Itemable;

defined('BASEPATH') or exit('No direct script access allowed');

$searchById = false;
$amsIds = collect([]);
$clientIdForJob = [$clientId];
$sortFilterCols = ['id'];
$selects = ['id','number','number_format','prefix','date','use_expired_at'];

// Custom search value by company id
$amsCompany = $this->ci->input->post('company');
$crmCompany = $this->ci->input->post('crm_company');
if ($amsCompany || $crmCompany) {
    $clientIdForJob = [];
    if ($amsCompany) {
        $clientIdForJob = ClientAmsCompany::select('client_id')
            ->where('ams_company_id', $amsCompany)
            ->get()
            ->pluck('client_id')
            ->toArray();
    }
    if ($crmCompany) {
        $clientIdForJob[] = $crmCompany;
    }
}

if (count($clientIdForJob)) {
    $query = Invoice::select($selects)
        ->whereHas('invoiceItems', fn($query) => $query->isNotCredit())
        ->whereIn('clientid', $clientIdForJob)
        ->with([
            'clientAmsJobs',
            'clientAmsOpenJobs',
            'invoiceItems:id,item_id,qty,rate,rel_id,rel_type,use_expired_at',
            'invoiceItems.item:id,description,group_id',
        ])
        ->notDraft()
        ->withCount('clientAmsOpenJobs');

    $paidItemIds = Item::select('id')->paidItems()->get()->pluck('id');

    // Fetch all map packages between CRM and AMS to show
    $amsPackages = AmsPackage::select('crm_item_id', 'ams_package_id')->get()->groupBy('crm_item_id');
    $showablePackageIds = $amsPackages->keys()->toArray();

    $result = data_tables_eloquent_init($query, $sortFilterCols);
    $output  = $result['output'];
    $rResult = $result['rResult'];

    $amsJobIds = count($rResult) ? array_map(fn($aRow) => $aRow->clientAmsJobs->whereNull('package_id')->pluck('ams_job_id')->join(','), $rResult) : [];
    $amsJobIds = join(',', array_filter($amsJobIds));
    // Having jobs, fetch ams jobs
    $jobs = collect();
    if (!empty($amsJobIds)) {
        $res = AmsService::search('jobs', [
            'query' => [
                'fields' => [
                    'job' => 'id,package_list,notes_label'
                ],
                'ids' => $amsJobIds,
                'all_jobs' => true,
                'page_size' => 100,
            ]
        ]);
        $jobs = !empty($res['data']) ? collect($res['data']) : collect();
    }

    $hasJob = $jobs->count() > 0;

    foreach ($rResult as $aRow) {
        // Only get paid item to show
        $items = $aRow->invoiceItems ? $aRow->invoiceItems->whereIn('item_id', $showablePackageIds) ?? collect() : collect();
        $row = [];
        // Invoice number
        $row[] = '<a href="' . admin_url('invoices#' . $aRow->id) . '" target="_blank">' . format_invoice_number($aRow) . '</a>';

        $paidPackages = $aRow->invoiceItems->whereIn('item_id', $paidItemIds)->sum('qty');
        $usedPackages = $aRow->client_ams_open_jobs_count;
        $usedPackageList = $usedPackages ? $aRow->clientAmsJobs->first()->used_packages ?? [] : [];
        $linkedJobs = $hasJob ? $jobs->whereIn('id', $aRow->clientAmsJobs->whereNull('package_id')->pluck('ams_job_id')) : collect();
        $jobsHasPackages = $aRow->clientAmsOpenJobs->groupBy('invoice_id') ?? collect();
        $packageByLabel = Itemable::packageByLabel($linkedJobs);
        $totalUsedByInputted = array_sum($usedPackageList);
        $totalItemExpired = 0;

        $aData = [];
        if ($items->count()) {
            $groupItems = $items->groupBy('item_id');
            $invoiceId = $aRow->id;
            $groupItems->each(function ($groupItem) use (&$aData, $amsPackages, $packageByLabel, $invoiceId, $usedPackageList, $jobsHasPackages, &$totalItemExpired) {
                $groupItemByFreePaid = $groupItem->groupBy(fn($groupItem) => $groupItem->rate > 0 ? 'paid' : 'free');
                $groupItemByFreePaid->each(function ($paidFreeGroupdItem, $key) use (&$aData, $amsPackages, $packageByLabel, $invoiceId, $usedPackageList, $jobsHasPackages, &$totalItemExpired) {
                    $itemable = $paidFreeGroupdItem->first();
                    if ($itemable->item) {
                        $expired = $itemable->use_expired_at ? Carbon::parse($itemable->use_expired_at) : null;
                        $used = Itemable::getUsedByPackage($amsPackages, $packageByLabel, $itemable, $expired, $jobsHasPackages, $invoiceId);
                        $totalQty = $paidFreeGroupdItem->sum(fn($item) => intval($item->qty));
                        $usedByInputted = $usedPackageList[$itemable->id] ?? 0;
                        $usedTotal = $used + $usedByInputted;
                        $giftText = $key == 'free' ? '<br>('._l('ams_job_manage_invoice_free_tag') . ')' : '';
                        $totalItemExpired += ($itemable->isUseNotExpired() ? 0 : $itemable->qty);
                        $aData[] = [
                            'invoice_id' => $invoiceId,
                            'itemable_id' => $itemable->id,
                            'name' => $itemable->item->description . ' ' . $giftText,
                            'paid_packages' => $totalQty,
                            'used_by_packages' => $used,
                            'used_input_packages' => $usedByInputted,
                            'used_packages' => $usedTotal,
                            'remain_packages' => max($totalQty - $usedTotal - ($itemable->isUseNotExpired() ? 0 : $itemable->qty), 0),
                            'use_expired_at' => $itemable->use_expired_at ? $itemable->use_expired_at->format('d/m/Y') : '',
                        ];
                    }
                });
            });
        }

        // Paid invoices
        $row[] = $paidPackages;

        // Used packages
        $row[] = $usedPackages + $totalUsedByInputted;

        // Remain packages
        $row[] = max(0, $paidPackages - ($usedPackages + $totalUsedByInputted + $totalItemExpired));

        // Invoice details
        $row[] = '';
        $row[] = '';
        $row[] = '';
        $row[] = '';
        $row[] = '';
        $row[] = '';
        $row['_aData'] = $aData;

        $output['aaData'][] = $row;
    }
} else {
    $output = [
        'draw' => $this->ci->input->post('draw') ?? 1,
        'iTotalRecords' => 0,
        'iTotalDisplayRecords' => 0,
        'aaData' => [],
    ];
}

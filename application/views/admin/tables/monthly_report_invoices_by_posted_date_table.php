<?php

use Carbon\Carbon;

defined('BASEPATH') or exit('No direct script access allowed');

/*
* Output
*/
$CI          = &get_instance();
$post      = $CI->input->post();

$month = $post['post_month'] ?? null;

if (empty($month)) {
    $output = [
        'draw'                 => $post['draw'] ? intval($post['draw']) : 0,
        'iTotalRecords'        => 0,
        'iTotalDisplayRecords' => 0,
        'aaData'               => [],
    ];
} else {
    [$details, $amounts] = fetch_monthly_report_by_job_posted_month($month);
    $total = $details->count();

    $output = [
        'draw'                 => $post['draw'] ? intval($post['draw']) : 0,
        'iTotalRecords'        => $total,
        'iTotalDisplayRecords' => $total,
        'aaData'               => $details->sortBy([
                ['issued_date', 'desc'],
                ['posted_date', 'desc']
            ])
            ->values()
            ->map(function ($invoice, $index) {
                return [
                    $index + 1,
                    $invoice['posted_date'],
                    '<a href="' . admin_url('invoices#' . $invoice['invoice_id']) . '" target="_blank">' . $invoice['invoice_no'] . '</a>',
                    format_invoice_status($invoice['invoice_status']),
                    $invoice['issued_date'],
                    '<a href=" ' . admin_url('profile/' . $invoice['staff_id']) . '" target="_blank">' . $invoice['sale_name'] . '</a>'
                ];
            }),
        'amounts' => $amounts,
    ];
}

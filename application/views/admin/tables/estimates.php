<?php

use Entities\Estimate;

defined('BASEPATH') or exit('No direct script access allowed');

$project_id = $this->ci->input->post('project_id');
$relatedId = $related_id ?? null;
$clientId = $clientid ?? null;
$sortFilterCols = [
    'estimates.number',
    'estimates.total',
    'estimates.date',
    'expirydate',
    DB::raw(get_sql_select_client_company()),
    DB::raw('(SELECT CONCAT(' . db_prefix() . 'contacts.fullname, ' . db_prefix() . 'contacts.phonenumber, ' . db_prefix() . 'contacts.email))'),
    'estimates.adminnote',
    'estimates.status',
    DB::raw('(SELECT GROUP_CONCAT(CONCAT("- ", ' . db_prefix() . 'itemable.description) SEPARATOR "<br>")) as products'),
    'estimates.discount_percent',
    'estimates.discount_fixed',
    'clients.userid',
    'estimates.customer_source',
    'estimates.type_of_customer',
    'clients.usage_behavior',
    'estimates.id',
    'estimates.total_tax',
    DB::raw('(SELECT GROUP_CONCAT(name SEPARATOR ",") FROM ' . db_prefix() . 'taggables JOIN ' . db_prefix() . 'tags ON ' . db_prefix() . 'taggables.tag_id = ' . db_prefix() . 'tags.id WHERE rel_id = ' . db_prefix() . 'estimates.id and rel_type="estimate" ORDER by tag_order ASC) as tags'),
    'status_updated_at',
    'estimates.number_format',
    'estimates.prefix',
];

$custom_fields = get_table_custom_fields('estimate');
$customFieldsColumns = [];

foreach ($custom_fields as $key => $field) {
    $selectAs = (is_cf_date($field) ? 'date_picker_cvalue_' . $key : 'cvalue_' . $key);
    $customFieldsColumns[] = $selectAs;
    $sortFilterCols[] = 'ctable_' . $key . '.value as ' . $selectAs;
}

$query = Estimate::select($sortFilterCols)
    ->with([
        'itemables:rel_id,description',
        'contact:id,fullname,email,phonenumber',
        'client.customerAdmin.staff:staffid,firstname,lastname',
        'invoice:id,status,prefix,number,number_format,prefix,date'
    ])
    ->withCount(['options', 'parent'])
    ->addSelect(
        'estimates.contact_id',
        'estimates.clientid',
        'estimates.invoiceid',
        'currencies.name as currency_name',
        'estimates.project_id',
        'estimates.deleted_customer_name',
        'estimates.hash',
        'estimates.parent_id',
    )
    ->leftJoin('clients', 'clients.userid', 'estimates.clientid')
    ->leftJoin('currencies', 'currencies.id', 'estimates.currency')
    ->leftJoin('projects', 'projects.id', 'estimates.project_id')
    ->leftJoin('contacts', 'contacts.id', 'estimates.contact_id')
    ->leftJoin('customer_admins', 'customer_admins.customer_id', 'clients.userid')
    ->leftJoin('itemable', function ($join) {
        $join->on('itemable.rel_id', '=', 'estimates.id')
            ->where('itemable.rel_type', 'estimate');
    })
    ->leftJoin('invoices', function ($join) {
        $join->on('invoices.clientid', '=', 'estimates.clientid')
            ->whereRaw(db_prefix() . 'invoices.id = (SELECT MAX(id) FROM  ' . db_prefix() . 'invoices WHERE clientid = ' . db_prefix() . 'estimates.clientid)');
    })
    ->groupBy('estimates.id');


foreach ($custom_fields as $key => $field) {
    $query->leftJoin('customfieldsvalues as ctable_' . $key, function ($join) use ($key, $field) {
        $join->on('estimates.id', '=', 'ctable_' . $key . '.relid')
            ->where('ctable_' . $key . '.fieldto', $field['fieldto'])
            ->where('ctable_' . $key . '.fieldid', $field['id']);
    });
}

$custom_date_select = '';
$request = $this->ci->input->post();

$customer_admins = $request['customer_admins'] ?? '';
$department = $request['department'] ?? '';
$estimate_creator = $request['estimate_creator'] ?? '';
$status = $request['status'] ?? '';
$customer_source = $request['customer_source'] ?? '';
$type_of_customer = $request['type_of_customer'] ?? '';
$usage_behavior = $request['usage_behavior'] ?? '';
$from_date = $request['from_date'] ?? '';
$to_date = $request['to_date'] ?? '';

if ($from_date) {
    $custom_date_select = get_query_filter_date($from_date, $to_date, db_prefix() . 'estimates.date');
}

$query
    ->when($customer_admins, function ($query, $customer_admins) {
        $query->where('customer_admins.staff_id', $customer_admins);
    })
    ->when($department, function ($query, $department) {
        $query->join('staff_departments', 'staff_departments.staffid', 'customer_admins.staff_id')
            ->where('staff_departments.departmentid', $department);
    })
    ->when($estimate_creator, function ($query, $estimate_creator) {
        $query->where('estimates.addedfrom', $estimate_creator);
    })
    ->when($status, function ($query, $status) {
        $query->where('estimates.status', $status);
    })
    ->when($customer_source, function ($query, $customer_source) {
        $query->where('estimates.customer_source', $customer_source);
    })
    ->when($type_of_customer, function ($query, $type_of_customer) {
        $query->where('clients.type_of_customer', $type_of_customer);
    })
    ->when($usage_behavior, function ($query, $usage_behavior) {
        $query->where('clients.usage_behavior', $usage_behavior);
    })
    ->when($custom_date_select, function ($query, $custom_date_select) {
        $query->whereRaw($custom_date_select);
    })
    ->when(isset($request['not_sent']) && $request['not_sent'], function ($query) {
        $query->orWhere('estimates.sent', 0)
            ->orWhereNotIn('estimates.status', [2, 3, 4]);
    })
    ->when(isset($request['invoiced']) && $request['invoiced'], function ($query) {
        $query->orWhereNotNull('invoiceid');
    })
    ->when(isset($request['not_invoiced']) && $request['not_invoiced'], function ($query) {
        $query->orWhereNull('invoiceid');
    });

$agents = $this->ci->estimates_model->get_sale_agents();
$agentsIds = [];
foreach ($agents as $agent) {
    if (isset($request['sale_agent_' . $agent['sale_agent']]) && $request['sale_agent_' . $agent['sale_agent']]) {
        array_push($agentsIds, $agent['sale_agent']);
    }
}

$query->when(count($agentsIds), function ($query) use ($agentsIds) {
    $query->whereIn('estimates.sale_agent', $agentsIds);
});

$years = $this->ci->estimates_model->get_estimates_years();
$yearsArray = [];
foreach ($years as $year) {
    if (isset($request['year_' . $year['year']]) && $request['year_' . $year['year']]) {
        array_push($yearsArray, $year['year']);
    }
}

$query->when(count($yearsArray), function ($query) use ($yearsArray) {
    $query->whereRaw('YEAR(' . db_prefix() . 'estimates.date) IN (' . implode(', ', $yearsArray) . ')');
});

$query->when($clientId, function ($query, $clientId) {
    $query->where('estimates.clientid', $clientId);
});

$query->when($project_id, function ($query, $project_id) {
    $query->where('estimates.project_id', $project_id);
});

$query->when(!has_permission('estimates', '', 'view'), function ($query) {
    $query->whereIn('estimates.clientid', permission_get_clients('estimates', true));
});

if ($relatedId) {
    $ids = $this->ci->estimates_model->getRelatedIds($relatedId);
    $query->whereIn('estimates.id', $ids);
} else {
    $query->where(function ($query) {
        $query->where(function ($query1) {
            $query1->primary()
                ->whereNotExists(function ($query2) {
                    $query2->select(DB::raw(1))
                        ->from('estimates as zzz')
                        ->whereColumn('estimates.id', 'zzz.parent_id')
                        ->whereNotNull('invoiceid');
                });
        })->orWhereNotNull('invoiceid');
    });
}
// Fix for big queries. Some hosting have max_join_limit
if (count($custom_fields) > 4) {
    @$this->ci->db->query('SET SQL_BIG_SELECTS=1');
}

$result = data_tables_eloquent_init($query, $sortFilterCols);

$output = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $index => $aRow) {
    $row = [];

    $numberOutput = '';
    // If is from client area table or projects area request
    if (is_numeric($clientId) || $project_id) {
        $numberOutput = '<a href="' . admin_url('estimates/list_estimates/' . $aRow['id']) . '" target="_blank">' . format_estimate_number($aRow) . '</a>';
    } else {
        $numberOutput = '<a href="' . admin_url('estimates/list_estimates/' . $aRow['id']) . '" onmousedown="toggleBorderColor(this);" onclick="init_estimate(' . $aRow['id'] . '); return false;">' . format_estimate_number($aRow) . '</a>';
    }

    if (($aRow['options_count'] || $aRow['parent_count']) && !$relatedId) {
        $numberOutput .= '<a href="#" class="btn" onclick="toggle_child_items(this, ' . $aRow['id'] . '); return false;"><i class="fa fa-spinner fa-spin hide"></i><i class="fa fa-angle-left collapsed"></i></a>';
    }

    if ($invoice = $aRow->invoice) {
        $numberOutput .= '<br><a href="' . admin_url('invoices/list_invoices/' . $invoice->id) . '" target="_blank"><h4><small><u>' . format_invoice_number($invoice) . '</u></h4></small></a>';
    }

    if (!$aRow['parent_id']) {
        $numberOutput .= '<span class="label label-warning s-status">' . _l('estimate_primary') . '</span>';
    }

    $numberOutput .= '<div class="row-options">';

    $numberOutput .= '<a href="' . site_url('estimate/' . $aRow['id'] . '/' . $aRow['hash']) . '" target="_blank">' . _l('view') . '</a>';
    if (has_permission('estimates', '', 'edit')) {
        $numberOutput .= ' | <a href="' . admin_url('estimates/estimate/' . $aRow['id']) . '">' . _l('edit') . '</a>';
    }
    $numberOutput .= '</div>';

    $row[] = $numberOutput;

    $row[] = app_format_money($aRow['total'] - $aRow['total_tax'], $aRow['currency_name']);

    $row[] = _d($aRow['date']);

    $row[] = _d($aRow['expirydate']);

    $contact = join(' - ', array_filter(
        [
            $aRow->contact->fullname ?? null,
            $aRow->contact->phonenumber ?? null,
        ]
    ));
    if (empty($aRow['deleted_customer_name'])) {
        $row[] = '<a href="' . admin_url('clients/client/' . $aRow['clientid']) . '">' . $aRow['company'] . '</a>' . "</br>". $contact . "</br>". ($aRow->contact->email ?? "");
    } else {
        $row[] = $aRow['deleted_customer_name'] . "</br>" . $contact . "</br>". ($aRow->contact->email ?? "");
    }


    // Note
    $row[] = $aRow['adminnote'];

    // Status
    $dataStatus = '';
    if (!empty($aRow['status_updated_at'])) {
        $date_diff = (new DateTime(date('Y-m-d', strtotime($aRow['status_updated_at']))))
            ->diff(new DateTime(date('Y-m-d', strtotime($aRow['date']))))
            ->format('%a') + 1;

        $dataStatus = '<p>' . _d(date('Y-m-d', strtotime($aRow['status_updated_at']))) . '<br>(' . $date_diff . ($date_diff > 1 ? ' days' : ' day') . ')</p>';
    }

    $row[] = format_estimate_status($aRow['status']) . $dataStatus;

    $products = '';
    foreach ($aRow['itemables']->pluck('description') as $item) {
        $products .= "- " . ucfirst($item) . '<br>';
    }
    $row[] = $products;

    $row[] = "<b>Rate: </b>".number_format($aRow['discount_percent']) . "% </br></br><b>Fixed Amount:</b> " . app_format_money($aRow['discount_fixed'], $aRow['currency_name']);


    $dataAdmin = '';
    if (isset($aRow->client->customerAdmin->staff) && $customerAdmin = $aRow->client->customerAdmin->staff) {
        $dataAdmin = '<a href=" ' . admin_url('profile/' . $customerAdmin->staffid) . '">' . $customerAdmin->fullname . '</a><br>';
    }

    $row[] = $dataAdmin;

    $row[] = $aRow['customer_source'];

    // Custom fields add values
    foreach ($customFieldsColumns as $customFieldColumn) {
        $row[] = (strpos($customFieldColumn, 'date_picker_') !== false ? _d($aRow[$customFieldColumn]) : $aRow[$customFieldColumn]);
    }

    $row[] = "<b>TopDev: </b>". $aRow['type_of_customer'] . "</br>" . "<b>Thị trường</b>: ". $aRow['usage_behavior'] ;

    $row[] = ($aRow['parent_id'] ?
        '<a onclick="makeAsPrimary(' . $aRow['id'] . ')" href="javascript:;">' . _l('estimate_make_primary') . '</a>' :
        '<a href="' . admin_url('estimates/estimate/' . $aRow['id']) . '/option">' . _l('estimate_create_option') . '</a>') .
        (staff_can('delete', 'estimates') ?
        '<br><a onclick="deleteOption(' . $aRow['id'] . ',\'' . format_estimate_number($aRow) . '\')" href="javascript:;">' . _l('estimate_delete_option') . '</a>' : '');

    $row['DT_RowClass'] = 'has-row-options ' . ($index % 2 ? 'info' : '');

    $row = hooks()->apply_filters('estimates_table_row_data', $row, $aRow);

    $output['aaData'][] = $row;
}

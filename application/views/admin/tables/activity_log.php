<?php

defined('BASEPATH') or exit('No direct script access allowed');

$aColumns = [
    'description',
    'date',
    'clientid',
    db_prefix() . 'activity_log.staffid as staffid',
    'company',
    'concat(firstname, " ", lastname) staff_name'
];
////
$sWhere = [];
$join = [
    'LEFT JOIN ' . db_prefix() . 'staff ON ' . db_prefix() . 'staff.staffid = ' . db_prefix() . 'activity_log.staffid',
    'LEFT JOIN ' . db_prefix() . 'clients ON ' . db_prefix() . 'clients.userid = ' . db_prefix() . 'activity_log.clientid',
];

if ($this->ci->input->post('activity_log_date')) {
    array_push($sWhere, 'AND date LIKE "' . $this->ci->db->escape_like_str(to_sql_date($this->ci->input->post('activity_log_date'))) . '%" ESCAPE \'!\'');
}
$sIndexColumn = 'id';
$sTable       = db_prefix() . 'activity_log';
$result       = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $sWhere);
$output       = $result['output'];
$rResult      = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];

    $row[] = $aRow['description'];

    $row[] = $aRow['clientid'] ? '<a href="' . admin_url('clients/client/' . $aRow['clientid']) . '">' . $aRow['company'] . '</a>' : '';

    $row[] = $aRow['staffid'] ? '<a href="' . admin_url('profile/' . $aRow['staffid']) . '">' . $aRow['staff_name'] . '</a>' : '';

    $row[] = _dt($aRow['date']);

    $output['aaData'][] = $row;
}

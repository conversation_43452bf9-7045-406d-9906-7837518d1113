<?php

use app\services\AmsService;
use Entities\ClientAmsSearchPackage;

defined('BASEPATH') or exit('No direct script access allowed');

$sortFilterCols = [
    '', // Empty column for search/sort
    '', // Empty column for search/sort
    '', // Empty column for search/sort
    '', // Empty column for search/sort
    '', // Empty column for search/sort
    '', // Empty column for search/sort
    '', // Empty column for search/sort
];

// Custom search value by company id
$amsCompany = $this->ci->input->post('company');
$crmCompany = $this->ci->input->post('crm_company');
$invoiceId = $this->ci->input->post('invoice_id');

$query = ClientAmsSearchPackage::query()
    ->when($crmCompany, fn ($query) => $query->where('client_id', $crmCompany), fn ($query) => $query->where('client_id', $clientId))
    ->when($invoiceId, fn ($query) => $query->where('invoice_id', $invoiceId))
    ->when($amsCompany, fn ($query) => $query->where('ams_company_id', $amsCompany))
    ->with([
        'invoice:id,date,number,prefix,number_format,status',
        'itemable:id,description',
        'amsSearchPackage.item:id,description'
    ])
    ->orderByDesc('invoice_id')
    ->orderByDesc('ams_company_search_package_id');

$result = data_tables_eloquent_init($query, $sortFilterCols);

$output  = $result['output'];
$rResult = $result['rResult'];

$searchPackageIds = collect($rResult)->pluck('ams_company_search_package_id')->toArray();
$credits = collect();
if (count($searchPackageIds)) {
    $response = AmsService::amsApi('crm/company-credits', [
        'query' => [
            'ids' => implode(',', $searchPackageIds)
        ]
    ], 'get');

    if (isset($response['data'])) {
        $credits = collect($response['data'])->keyBy('id');
    }
}

$colors = [
    'inactive' => 'default',
    'active' => 'success',
    'expired' => 'warning',
];

foreach ($rResult as $key => $aRow) {
    $row = [];
    $credit = $credits->get($aRow->ams_company_search_package_id);

    // Invoice number
    $invoiceId = $aRow->invoice->id ?? 0;
    $invoiceNumber = $aRow->invoice ? format_invoice_number($aRow->invoice) : '';
    $row[] = (
        '<span id="row_invoice" data-id="'.$aRow->id.'" data-selected-invoice-id="'.$invoiceId.'" data-selected-invoice-text="'.$invoiceNumber.'">'.join('', [
            '<a class="view-comp" href="' . admin_url('invoices#' . $invoiceId) . '" target="_blank">' . $invoiceNumber . '</a>',
            has_permission('customers', '', 'change_search_cv_invoice')
                ? render_select('invoice_id', [], ['id', 'name'], null, $invoiceId, ['data-none-selected-text' => 'Invoice'], [], 'hide edit-comp')
                : ''
        ]) . '</span>'
    );
    // ID AMS
    $row[] = has_permission('customers', '', 'view_ams_company') ?
        '<a href="' . AMS_URL . '/admin/companies/' . $aRow->ams_company_id . '/edit" target="_blank">' . $aRow->ams_company_id . '</a>' :
        $aRow->ams_company_id;
    // Product name
    $row[] = $aRow->itemable->description ?? ($aRow->amsSearchPackage->item->description ?? '');
    // Purchased date
    $row[] = $aRow->paid_at->format('d/m/Y H:i:s');
    // Used/Total unlocks
    $row[] = $credit ? $credit['used_credit'] . '/' . $credit['total_credit'] : '';
    // Status
    $row[] = $credit ? ('<span class="label label-' . $colors[strtolower($credit['status'])] . '">' . $credit['status'] . '</span>') : '';
    // Time
    $row[] = $credit ? join('<br>', [
        _l('ams_search_column_time_active_date_row') . ': ' . '<b>' . ($credit['valid_at'] ?? '---') . '</b>',
        _l('ams_search_column_time_expired_date_row') . ': ' . '<b>' . ($credit['expired_at'] ?? '---') . '</b>',
    ]) : '';

    if (has_permission('customers', '', 'change_search_cv_invoice')) {
        $row[] = '<span id="expired_action" data-id="'.$aRow->id.'">'.join('', [
            '<button class="btn btn-info btn-xs update-btn view-comp">' . _l('ams_search_column_action_edit_btn') . '&nbsp;<i class="fa fa-spinner fa-spin hide"></i></button>',
            '<button class="btn btn-info btn-xs hide save-btn edit-comp">' . _l('ams_search_column_action_save_btn') . '<i class="fa fa-spinner fa-spin hide"></i></button>',
            '<br><button class="mtop5 btn btn-danger btn-xs hide cancel-btn edit-comp">' . _l('ams_search_column_action_cancel_btn') . '</button>',
        ]) . '</span>';
    } else {
        $row[] = '';
    }

    $output['aaData'][] = $row;
}

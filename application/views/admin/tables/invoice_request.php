<?php

use Entities\InvoiceRequest;

defined('BASEPATH') or exit('No direct script access allowed');

$aColumns = [];

$aColumns = array_merge($aColumns, [
    'iv.id',
    'iv.total',
    db_prefix() . 'invoice_request.created_at as created_at',
    db_prefix() . 'invoice_request.request_status as request_status',
    'cl.business_name as company_name',
    'cl.address as export_address',
    db_prefix() . 'invoice_request.content as content',
    db_prefix() . 'invoice_request.contract_status as contract_status',
    'iv.status',
    'iv.invoice_closing_date',
    db_prefix() . 'invoice_request.staff_id as staff_id',
    db_prefix() . 'invoice_request.note as note',
    db_prefix() . 'invoice_request.invoice_draft as invoice_draft',
]);

$sIndexColumn = 'id';
$sTable       = db_prefix() . 'invoice_request';

$join = [
    'JOIN ' . db_prefix() . 'invoices iv ON iv.id = ' . db_prefix() . 'invoice_request.invoiceid',
    'JOIN ' . db_prefix() . 'clients as cl ON cl.userid = iv.clientid',
    'LEFT JOIN ' . db_prefix() . 'staff s ON s.staffid = ' . db_prefix() . 'invoice_request.staff_id',
    'LEFT JOIN ' . db_prefix() . 'minvoices min ON min.invoice_request_id = ' . db_prefix() . 'invoice_request.id',
];
$additionalSelect = [
    'concat(s.firstname, " ", s.lastname) staff_name',
    db_prefix() . 'invoice_request.contract_payment_date as contract_payment_date',
    db_prefix() . 'invoice_request.received_email as received_email',
    'cl.vat as vat',
    db_prefix() . 'invoice_request.id as id_request',
    db_prefix() . 'invoice_request.status as status_request',
    db_prefix() . 'invoice_request.requested_issue_at',
    db_prefix() . 'invoice_request.is_approved',
    db_prefix() . 'invoice_request.rejected_issue_at',
    db_prefix() . 'invoice_request.is_rejected',
    db_prefix() . 'invoice_request.rejected_issue_at',
    db_prefix() . 'invoice_request.request_issue_status',
    db_prefix() . 'invoice_request.walk_in_customer',
    db_prefix() . 'invoice_request.company_name as customer_name',
    db_prefix() . 'invoice_request.export_address as customer_export_address',
    'min.invoice_uuid',
    'iv.date',
    'iv.number',
    'iv.prefix',
    'iv.number_format',
    'iv.number_format',
];

$request = $this->ci->input->post();
$where  = [];
$filter = [];

// Phân quyền được xem
$staff_id = get_staff_user_id();
if (has_permission('account', '', 'view')) {
} else if (has_permission('account', '', 'view_own')) {
    array_push($filter, 'AND ' . db_prefix() . 'invoice_request.staff_id = ' .  $staff_id);
} else {
    array_push($filter, 'AND 1=0');
}

if ($request['request_status']) {
    array_push($filter, 'AND ' . db_prefix() . 'invoice_request.status = ' .  $request['request_status']);
}

if ($request['company_name']) {
    array_push($filter, 'AND ( (cl.business_name LIKE "%' .  $request['company_name'] . '%") OR (' . db_prefix() . 'invoice_request.walk_in_customer = 1 AND ' . db_prefix() . 'invoice_request.company_name LIKE "%' .  $request['company_name'] . '%") )');
}

if ($request['contract_status']) {
    array_push($filter, 'AND ' . db_prefix() . 'invoice_request.contract_status = "' .  $request['contract_status'] . '"');
}

if ($request['date_invoice_request_from'] || $request['date_invoice_request_to']) {
    $custom_date_select = get_query_filter_date(($request['date_invoice_request_from']) ?? '', ($request['date_invoice_request_to']) ?? '', db_prefix() . 'invoice_request.created_at');
}

if (isset($custom_date_select)) {
    array_push($filter, 'AND ' . $custom_date_select);
}

if ($request['status']) {
    array_push($filter, 'AND iv.status = ' .  $request['status']);
}

if ($request['name_invoice_reques']) {
    array_push($filter, 'AND s.staffid = ' .  $request['name_invoice_reques']);
}

if (count($filter) > 0) {
    array_push($where, 'AND (' . prepare_dt_filter($filter) . ')');
}

$result  = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, $additionalSelect);
$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];

    $inv = json_decode(json_encode([
        'id' => $aRow['id'],
        'status' => $aRow['status'],
        'date' => $aRow['date'],
        'number' => $aRow['number'],
        'prefix' => $aRow['prefix'],
        'number_format' => $aRow['number_format'],
    ]));

    $numberOutput = join('<br><br>', array_filter([
        '<a href="' . admin_url('invoices/list_invoices/' . $aRow['id']) . '" target="_blank">' . format_invoice_number($inv) . '</a>',
        isset($aRow['invoice_uuid']) ? '<a href="' . admin_url('invoices/view_minvoice_pdf/' . $aRow['invoice_uuid']) . '" class="mtop5" target="_blank">' . _l('request_invoice_view_invoice_pdf_link') . '</a>' : null,
    ]));
    $row[] = $numberOutput;

    $row[] = app_format_money($aRow['total'], 'VND');

    $row[] = _dt($aRow['created_at']);

    // Trạng thái yêu cầu
    $request_status = '<div class="request_status_table">' . isset(REQUEST_STATUS[$aRow['request_status']]) ? REQUEST_STATUS[$aRow['request_status']]['text'] : '' . '</div>';
    if ($aRow['request_status'] == Invoices_model::REQUEST_STATUS_2 && $aRow['contract_payment_date']) {
        $request_status .= '<div class="contract_payment_date_table item_trang_thai_yc"><b>Ngày thanh toán theo PO:</b> ' . date('d.m.Y', strtotime($aRow['contract_payment_date'])) . '</div>';
    }
    $request_status .= ($aRow['invoice_draft']) ? '<div class="item_trang_thai_yc"><b>' . _l('request_invoice_draft_title') . '</b>: ' . _l('request_invoice_draft') . '</div>' : '';
    $row[] = $request_status;

    // Thông tin công ty
    $company_info = '<div class="company_name_table">' . ($aRow['walk_in_customer'] ? ('<b>Customer Name:</b> ' . $aRow['customer_name']) : $aRow['company_name']) . '</div>';
    $company_info .= '<div class="vat_table"><b>MST:</b> ' . $aRow['vat'] . '</div>';
    $company_info .= '<div class="export_address_table"><b>Địa chỉ:</b> ' . ($aRow['walk_in_customer'] ? $aRow['customer_export_address'] : $aRow['export_address']) . '</div>';
    $company_info .= $aRow['walk_in_customer'] ? join('', [
        '<span class="label label-info s-status mtop5">Walk-in customer</span>',
        ($aRow['is_rejected'] ? '<button onclick="maskAsCompany(this, ' . $aRow['id_request'] . ')" class="btn btn-info btn-icon update-btn view-comp mtop5">Mask as Company <i class="fa fa-spinner fa-spin hide"></i></button></button>' : '')
    ]) : ($aRow['is_rejected'] ? '<button onclick="showEditRequestAddressModal(' . $aRow['id_request'] . ', \'' . $aRow['company_name'] . '\', \'' . $aRow['export_address'] . '\')" class="btn btn-info btn-icon update-btn view-comp">Mask as Walk-in customer</button>' : '');

    $row[] = $company_info;

    $arr_email = explode(',', $aRow['received_email']);
    $email_htmt = '';
    foreach ($arr_email as $email) {
        $email_htmt .= '<div>' . $email . '</div>';
    }
    $row[] = join('', [
        $email_htmt,
        $aRow['is_rejected'] ? '<button onclick="showEditRequestModal(' . $aRow['id_request'] . ', \'' . $aRow['received_email'] . '\')" class="btn btn-info btn-icon update-btn view-comp">Edit</button>' : ''
    ]);
    $row[] = $aRow['content'];
    $row[] = $aRow['contract_status'];
    $row[] = format_invoice_status($aRow['status']);
    $row[] = _dt($aRow['invoice_closing_date']);
    $row[] = $aRow['staff_name'];
    $row[] = $aRow['note'];

    $ableRejectIssue = $aRow['requested_issue_at'] && !$aRow['request_issue_status'];

    if (has_permission('account', '', 'change_status')) {
        $request_status = join('<br>', array_filter([
            '<button class="btn request_status' . $aRow['status_request'] . '">' . INVOICE_DRAFT[$aRow['status_request']]['text'] . '</button>',
            $ableRejectIssue && !$aRow['invoice_draft']
                ? '<button data-id="' . $aRow['id_request'] . '" data-inv-number="' . format_invoice_number($inv) . '"  class="btn btn-info mtop5 btn-issue-po">' . _l('request_invoice_issue_invoice_btn') . ' <i class="fa fa-spinner fa-spin hide"></i></button>'
                : null,
            // Just create draft, show button request to issue invoice
            $ableRejectIssue && !$aRow['invoice_draft'] ? '<button data-id=' . $aRow['id_request'] . ' data-title=' . format_invoice_number($inv) . ' class="btn btn-danger mtop5 btn-reject-po">' . _l('request_invoice_reject_invoice_btn') . ' <i class="fa fa-spinner fa-spin hide"></i></button>' : null,
        ]));
    } else {
        $request_status = join('<br>', array_filter([
            '<button class="btn request_status' . $aRow['status_request'] . '">' . INVOICE_DRAFT[$aRow['status_request']]['text'] . '</button>',
            $aRow['is_rejected'] ? '<button data-id=' . $aRow['id_request'] . ' class="btn btn-info mtop5 btn-fix-po">' . _l('request_invoice_fix_invoice_btn') . ' <i class="fa fa-spinner fa-spin hide"></i></button>' : null,
            $ableRejectIssue ? '<button data-id=' . $aRow['id_request'] . ' data-title=' . format_invoice_number($inv) . ' class="btn btn-danger mtop5 btn-reject-po">' . _l('request_invoice_reject_invoice_btn') . ' <i class="fa fa-spinner fa-spin hide"></i></button>' : null,
            $ableRejectIssue && $aRow['invoice_draft'] && $aRow['status_request'] != 3 ? '<button data-id="' . $aRow['id_request'] . '" class="btn btn-success mtop5 btn-request-issue-po">' . _l('request_invoice_request_issue_invoice_btn') . ' <i class="fa fa-spinner fa-spin hide"></i></button>' : null,
            $ableRejectIssue && $aRow['invoice_draft'] && $aRow['status_request'] != 3 ? '<button data-id="' . $aRow['id_request'] . '" data-inv-number="' . format_invoice_number($inv) . '" class="btn btn-info mtop5 btn-request-send-draft-invoice">Send Draft Invoice <i class="fa fa-spinner fa-spin hide"></i></button>' : null,
        ]));
    }

    $row[] = $request_status;

    $row['DT_RowClass'] = 'has-row-options';

    $output['aaData'][] = $row;
}

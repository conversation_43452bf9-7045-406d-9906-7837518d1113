<?php

use app\services\AmsService;
use Carbon\Carbon;
use Entities\ReportClientAmsJob;
use Illuminate\Support\Arr;

defined('BASEPATH') or exit('No direct script access allowed');

$pageSize = $this->ci->input->post('length');
$page = (intval($this->ci->input->post('start')) / $pageSize) + 1;
$type = $this->ci->input->post('job_tracking_report_type');

$sortFilterCols = [
    'client_id',
    'invoice_id',
];

$fromDate = $this->ci->input->post('job_tracking_report_invoice_expiration_from_date');
$fromDate = !empty($fromDate) ? Carbon::createFromFormat('d.m.Y', $fromDate)->format('Y-m-d') : null;
$toDate = $this->ci->input->post('job_tracking_report_invoice_expiration_to_date');
$toDate = !empty($toDate) ? Carbon::createFromFormat('d.m.Y', $toDate)->format('Y-m-d') : null;

$query = ReportClientAmsJob::query()
    ->select(
        array_merge(
            $sortFilterCols,
            [
                'ams_company_id',
                'ams_job_id',
                'package_id',
                'free_package',
                'status_mapping',
            ]
        )
    )
    ->with([
        'usedPackage',
        'itemable',
        'invoice:id,number,number_format,prefix,date,status,clientid',
        'invoice.paymentRecord',
        'invoice.latestExtended',
        'invoice.client.customerAdmin.staff:staffid,firstname,lastname',
    ])
    ->whereHas('itemable', fn($query) => $query->isNotCredit())
    ->when(
        $type,
        fn($query, $type) => $query->whereHas(
            'itemable',
            fn($query) => $query->where('rate', $type == JOB_TRACKING_REPORT_TYPE_GIFT_PACKAGE ? '=' : '!=', 0)
        )
    )
    ->when(
        $this->ci->input->post('job_tracking_ams_company_id'),
        fn($query, $amsCompanyIds) => $query->whereIn('ams_company_id', $amsCompanyIds)
    )
    ->when(
        $this->ci->input->post('job_tracking_sale'),
        fn($query, $customerAdmins) => $query->whereHas(
            'client.customerAdmin',
            fn($query) => $query->whereIn('staff_id', array_filter($customerAdmins))
        )
    )
    ->when(
        $fromDate && $toDate,
        fn($query) => $query->whereHas('itemable', fn($query) => $query->whereBetween('use_expired_at', [$fromDate, $toDate])),
        fn($query) => $query->when(
            $fromDate,
            fn($query, $fromDate) => $query->whereHas(
                'itemable',
                fn($query) => $query->where('use_expired_at', '>=', $fromDate)
            ),
            fn($query) => $query->when(
                $toDate,
                fn($query, $toDate) => $query->whereHas(
                    'itemable',
                    fn($query) => $query->where('use_expired_at', '<=', $toDate)
                )
            )
        )
    )
    ->when(
        $this->ci->input->post('job_tracking_id_crm'),
        fn($query, $crmIds) => $query->whereHas('invoice', fn($query) => $query->whereIn('client_id', array_filter($crmIds)))
    )
    ->when(
        $this->ci->input->post('job_tracking_invoice'),
        fn($query, $invoiceIds) => $query->whereIn('invoice_id', array_filter($invoiceIds))
    )
    ->orderByDesc('package_id');

$result = data_tables_eloquent_init($query, $sortFilterCols);
$output = $result['output'];

$rResult = collect($result['rResult']);

$amsCompanyIds = $rResult->pluck('usedPackage.ams_company_id')
    ->filter()
    ->unique();

$amsJobIds = $rResult->pluck('usedPackage.ams_job_id')
    ->filter()
    ->unique();

if ($amsJobIds) {
    // Fetch ams companies first
    $queriesJob = array_filter([
        'fields' => [
            'job' => join(',', [
                'id',
                'package_list',
                'published',
                'company'
            ])
        ],
        'all_jobs' => true,
        'page' => 1,
        'page_size' => $pageSize,
        'ordering' => 'crm_invoice_id'
    ]);

    $queriesJob['ids'] = $amsJobIds->join(',');
    $queriesJob['companies'] = $amsCompanyIds->join(',');

    $response = AmsService::search('jobs', [
        'query' => $queriesJob
    ]);

    $amsJob = $response['data'] ?? [];

    if ($amsJob) {
        $amsJob = collect($amsJob)->keyBy('id');

        $rResult->map(function ($value) use ($amsJob) {
            $amsJobId = $value->usedPackage->ams_job_id ?? null;
            $value->job = $amsJobId ? collect($amsJob[$amsJobId] ?? []) : collect([]);
            return $value;
        });
    }
}

// Mapping data
$rResult = $rResult->map(function ($data) {

    $job = $data->job;
    $activeDate = null;
    $expiredDate = $data->itemable->use_expired_at ? Carbon::parse($data->itemable->use_expired_at) : null;

    if (Arr::first(Arr::get($job, 'package_list'))) {

        $activeDate = empty($job['published']['date']) ? null : Carbon::parse($job['published']['date']);

        if ($expiredDate && $expiredDate->gte($activeDate)) {
            if ($data->invoice && $data->invoice->hasExtended() && Carbon::parse($data->invoice->rangeLatestExtended['from'])->lt($activeDate)) {
                // Ngày hết hạn bảo lưu (đã gia hạn) >= Ngày kích hoạt: Gia hạn
                $status = JOB_TRACKING_REPORT_STATUS_EXTEND;
            } else {
                // Ngày hết hạn bảo lưu (chưa gia hạn) >= Ngày kích hoạt: Đã dùng
                $status = JOB_TRACKING_REPORT_STATUS_USED;
            }
        }
    }

    if (Carbon::now()->startOfDay()->gt($expiredDate) && !$activeDate) {
        // Nếu now > Ngày hết hạn bảo lưu
        $status = JOB_TRACKING_REPORT_STATUS_EXPIRED;
    }

    $data->status = $status ?? JOB_TRACKING_REPORT_STATUS_UNUSED;
    $data->active_date = $activeDate;
    $data->expired_date = $expiredDate;

    return $data;
});

$statusArray = [
    JOB_TRACKING_REPORT_STATUS_UNUSED => [
        'text' => _l('job_tracking_report_status_unused'),
        'class' => ''
    ],
    JOB_TRACKING_REPORT_STATUS_EXTEND => [
        'text' => _l('job_tracking_report_status_extend'),
        'class' => 'text-warning'
    ],
    JOB_TRACKING_REPORT_STATUS_USED => [
        'text' => _l('job_tracking_report_status_used'),
        'class' => 'text-success'
    ],
    JOB_TRACKING_REPORT_STATUS_EXPIRED => [
        'text' => _l('job_tracking_report_status_expired'),
        'class' => 'text-danger'
    ],
];

$packageTypeArray = [
    JOB_TRACKING_REPORT_TYPE_SERVICE_PACKAGE => [
        'text' => get_text_of_select_options(JOB_TRACKING_REPORT_TYPE_SERVICE_PACKAGE, 'JOB_TRACKING_REPORT_TYPE'),
        'class' => 'text-success'
    ],
    JOB_TRACKING_REPORT_TYPE_GIFT_PACKAGE => [
        'text' => get_text_of_select_options(JOB_TRACKING_REPORT_TYPE_GIFT_PACKAGE, 'JOB_TRACKING_REPORT_TYPE'),
        'class' => 'text-warning'
    ]
];

foreach ($rResult as $index => $aRow) {
    $job = $aRow->job;
    $status = Arr::get($statusArray, $aRow->status);
    $type = Arr::get($packageTypeArray, $aRow->itemable->rate > 0 ? JOB_TRACKING_REPORT_TYPE_SERVICE_PACKAGE : JOB_TRACKING_REPORT_TYPE_GIFT_PACKAGE);
    $activeDate = optional($aRow->active_date)->format('d/m/Y');
    $expiredDate = optional($aRow->expired_date)->format('d/m/Y');
    $textJobId = empty($job['id']) ? '' : $job['id'];
    $row = [
        '<a href="' . admin_url('clients/client/' . $aRow->invoice->clientid) . '" target="_blank">' . $aRow->invoice->clientid . '</a>',
        '<a href="' . admin_url('invoices#' . $aRow->invoice_id) . '" target="_blank">' . format_invoice_number($aRow->invoice) . '</a>',
        $aRow->invoice->client->customerAdmin->staff->fullname ?? null,
        empty($aRow->usedPackage->ams_company_id) ? '' : $aRow->usedPackage->ams_company_id,
        $job['company']['display_name'] ?? '',
        $aRow->itemable->description ?? '',
        '<p class="' . $type['class'] . '"> ' . $type['text'] . '</p>',
        $aRow->invoice->paymentRecord ? $aRow->invoice->paymentRecord->date->format('d/m/Y') : null,
        $expiredDate,
        '<p class="' . $status['class'] . '"> ' . $status['text'] . '</p>',
        $activeDate,
        $textJobId,
        $aRow->invoice && $aRow->invoice->hasExtended() && $expiredDate ? $expiredDate : null,
        $aRow->invoice && $aRow->invoice->hasExtended() && $activeDate ? $activeDate : null,
    ];

    $output['aaData'][] = $row;
}

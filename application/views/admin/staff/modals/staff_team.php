<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="modal fade" id="staff_team_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button group="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">
                    <span class="edit-title"><?= _l('staff_team_edit_heading'); ?></span>
                    <span class="add-title"><?= _l('staff_team_add_heading'); ?></span>
                </h4>
            </div>
            <?= form_open('admin/staff/team', ['id' => 'staff-team-modal']); ?>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <?= render_input('name', 'staff_team_name'); ?>
                        <?= form_hidden('id'); ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                <button group="submit" class="btn btn-info"><?= _l('submit'); ?></button>
                <?= form_close(); ?>
            </div>
        </div>
    </div>
</div>
<script>
    window.addEventListener('load', function() {
        appValidateForm($('#staff-team-modal'), {
            name: 'required'
        }, manage_staff_teams);

        $('#staff_team_modal').on('show.bs.modal', function(e) {
            var invoker = $(e.relatedTarget);
            var group_id = $(invoker).data('id');
            $('#staff_team_modal .add-title').removeClass('hide');
            $('#staff_team_modal .edit-title').addClass('hide');
            $('#staff_team_modal input[name="id"]').val('');
            $('#staff_team_modal input[name="name"]').val('');
            // is from the edit button
            if (typeof(group_id) !== 'undefined') {
                $('#staff_team_modal input[name="id"]').val(group_id);
                $('#staff_team_modal .add-title').addClass('hide');
                $('#staff_team_modal .edit-title').removeClass('hide');
                $('#staff_team_modal input[name="name"]').val($(invoker).parents('tr').find('td').eq(0).text());
            }
        });
    });

    function manage_staff_teams(form) {
        $.post(form.action, $(form).serialize()).done(function(response) {
            response = JSON.parse(response);
            if (response.success) {
                if ($.fn.DataTable.isDataTable('.table-staff-teams')) {
                    $('.table-staff-teams').DataTable().ajax.reload();
                }

                if ($('body').hasClass('dynamic-create-teams')) {
                    const data = response.data
                    const teams = $('select[name="teams_in[]"]');
                    teams.prepend('<option value="' + data.id + '">' + data.name + '</option>');
                    teams.selectpicker('refresh');
                }
                alert_float('success', response.message);
            }
            $('#staff_team_modal').modal('hide');
        });
        return false;
    }
</script>
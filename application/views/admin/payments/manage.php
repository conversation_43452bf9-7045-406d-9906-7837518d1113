<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
	<div class="content">
		<div class="panel_s">
			<div class="panel-body">
				<div class="row _custom_filters">
					<div class="col-md-2">
						<?= render_select('department', get_list_department(), ['departmentid', 'name'], _l('department') . " (Customer admin's Team)", '', ['onchange' => "reloadDataPayment()"]); ?>
					</div>
					<div class="col-md-4">
						<div id="date-range" class="mbot15">
							<div class="row">
								<div class="col-md-6">
									<label for="from_date" class="control-label"><?= _l('from') . ' (' . _l('invoice_dt_table_heading_date') . ')' ?></label>
									<div class="input-group date">
										<input type="text" class="form-control datepicker" id="from_date" onchange="reloadDataPayment()" name="from_date" autocomplete="off">
										<div class="input-group-addon">
											<i class="fa fa-calendar calendar-icon"></i>
										</div>
									</div>
								</div>
								<div class="col-md-6">
									<label for="to_date" class="control-label"><?= _l('to') . ' (' . _l('invoice_dt_table_heading_date') . ')' ?></label>
									<div class="input-group date">
										<input type="text" class="form-control datepicker" id="to_date" onchange="reloadDataPayment()" name="to_date" autocomplete="off">
										<div class="input-group-addon">
											<i class="fa fa-calendar calendar-icon"></i>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<?php $this->load->view('admin/payments/table_html'); ?>
			</div>
		</div>
	</div>
</div>
<?php init_tail(); ?>
<script>
	let paymentParams = {};

   $.each($('.payments ._custom_filters input'), function() {
      paymentParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
   });

   $.each($('.payments ._custom_filters select'), function() {
      paymentParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
   });

   const dataPayment = initDataTable(
      '.table-payments',
      admin_url + 'payments/table',
      undefined,
      undefined,
      paymentParams,
      <?= hooks()->apply_filters('payments_table_default_order', json_encode(array(0, 'desc'))); ?>
   );

   function reloadDataPayment() {
      dataPayment.ajax.reload();
   }
</script>
</body>
</html>

<?php defined('BASEPATH') or exit('No direct script access allowed');

$table_data = array(
  _l('invoice_dt_table_heading_number'),
  // _l('invoice_dt_table_heading_amount'),
  // _l('invoice_total_tax'),
  _l('invoice_dt_table_heading_total_amount'),
  array(
    'name'=>_l('invoice_estimate_year'),
    'th_attrs'=>array('class'=>'not_visible')
  ),
  _l('proposal_date_created'),
  _l('invoice_dt_table_heading_id_customer'),
  array(
    'name'=>_l('invoice_dt_table_heading_client'),
    'th_attrs'=>array('class'=>(isset($client) ? 'not_visible' : ''))
  ),
  _l('contact'),
  _l('invoice_dt_table_heading_vat_number'),
  // _l('project'),
  '<PERSON><PERSON><PERSON> <PERSON><PERSON> tiền dự kiến',
  _l('invoice_dt_table_heading_payment_date'),
  _l('invoice_dt_table_heading_status'),
  _l('invoice_dt_table_heading_customer_admin'),
  _l('customer_source'),
);
$custom_fields = get_custom_fields('invoice',array('show_on_table'=>1));
foreach($custom_fields as $field){
  array_push($table_data,$field['name']);
}

$table_data = array_merge($table_data, [
  _l('invoice_closing_date'),
  _l('type_of_customer'),
  _l('usage_behavior'),
  _l('invoice_creator'),
  _l('project_tag_reference'),
]);


$table_data = hooks()->apply_filters('invoices_table_columns', $table_data);
render_datatable($table_data, (isset($class) ? $class : 'invoices'));
?>

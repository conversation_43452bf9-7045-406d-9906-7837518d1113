<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="modal fade" id="reject_issue_po_modal" tabindex="-1" role="dialog" aria-labelledby="addNoteModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button group="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="rejectIssuePoTitle"></h4>
                </div>
                <?= form_open('', ['id' => 'reject_issue_po_form']); ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <?= render_textarea('reason', 'Reject Draft Invoice Reason', '', ['rows' => 20]); ?>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                    <button group="submit" class="btn btn-info btn-reject-submit"><?= _l('submit'); ?> <i class="fa fa-spinner fa-spin hide"></i></button></button>
                </div>
                <?= form_close(); ?>
            </div>
        </div>
    </div>
    <?php $this->load->view('admin/invoices/modal/edit_invoice_request_modal'); ?>
    <?php $this->load->view('admin/invoices/modal/edit_invoice_request_address_modal'); ?>

    <div class="content">
        <div class="row">
            <div class="col-md-12" id="small-table">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row _custom_filters">

                            <div class="col-md-4">
                                <?php
                                echo render_select('request_status',  INVOICE_DRAFT, ['value', 'text'], _l('request_status'), '', ['onchange' => "reloadDataTable()"]); ?>
                            </div>
                            <div class="col-md-4">
                                <?php
                                $company_name = [];
                                foreach ($list_invoice as $key => $status) {
                                    $company_name[$status['company_name']] = [
                                        'value' => $status['staff_id'],
                                        'text' => $status['company_name']
                                    ];
                                }
                                echo render_select('company_name', $company_name, ['text', 'text'], _l('clients_company'), '', ['onchange' => "reloadDataTable()"]); ?>
                            </div>
                            <div class="col-md-4">
                                <?= render_select('contract_status', CONTRACT_STATUS, ['text', 'text'], _l('request_invoice_contract_status'), '', ['onchange' => "reloadDataTable()"]); ?>
                            </div>
                            <div class="col-md-2">
                                <label for="from_date" class="control-label"><?= _l('date_invoice_request_from') ?></label>
                                <div class="input-group date">
                                    <input type="text" class="form-control datepicker" id="date_invoice_request_from" onchange="reloadDataTable()" name="date_invoice_request_from" autocomplete="off">
                                    <div class="input-group-addon">
                                        <i class="fa fa-calendar calendar-icon"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label for="from_date" class="control-label"><?= _l('date_invoice_request_to') ?></label>
                                <div class="input-group date">
                                    <input type="text" class="form-control datepicker" id="date_invoice_request_to" onchange="reloadDataTable()" name="date_invoice_request_to" autocomplete="off">
                                    <div class="input-group-addon">
                                        <i class="fa fa-calendar calendar-icon"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <?php
                                $invoices_status_options = [];
                                foreach ($invoices_statuses as $key => $status) {
                                    $invoices_status_options[] = [
                                        'value' => $status,
                                        'text' => format_invoice_status($status, '', false)
                                    ];
                                }
                                echo render_select('status', $invoices_status_options, ['value', 'text'], _l('request_invoice_status'), '', ['onchange' => "reloadDataTable()"]); ?>
                            </div>
                            <div class="col-md-4">
                                <?php
                                $name_invoice_reques = [];
                                foreach ($list_invoice as $key => $status) {
                                    $name_invoice_reques[$status['staff_id']] = [
                                        'value' => $status['staff_id'],
                                        'text' => $status['staff_name']
                                    ];
                                }
                                echo render_select('name_invoice_reques', $name_invoice_reques, ['value', 'text'], _l('name_invoice_reques'), '', ['onchange' => "reloadDataTable()"]); ?>
                            </div>

                        </div>
                        <!-- if invoiceid found in url -->

                        <?php $this->load->view('admin/invoices/table_html_invoice_request'); ?>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<?php init_tail(); ?>

<script>
    var tAPI;

    var InvoiceRequestServerParams = {};
    InvoiceRequestServerParams['request_status'] = '[name="request_status"]';
    InvoiceRequestServerParams['company_name'] = '[name="company_name"]';
    InvoiceRequestServerParams['contract_status'] = '[name="contract_status"]';
    InvoiceRequestServerParams['date_invoice_request_from'] = '[name="date_invoice_request_from"]';
    InvoiceRequestServerParams['date_invoice_request_to'] = '[name="date_invoice_request_to"]';
    InvoiceRequestServerParams['status'] = '[name="status"]';
    InvoiceRequestServerParams['name_invoice_reques'] = '[name="name_invoice_reques"]';

    tAPI = initDataTable(
        '.table-invoices_request',
        admin_url + 'invoices/table_request',
        'undefined',
        'undefined',
        InvoiceRequestServerParams,
        [2, 'desc']
    );

    $(document).ready(function() {
        $('.dataTables_wrapper .row:nth-child(2)').append('<div class="total_price_invoice"><?php echo _l('total_invoice_value') ?>: <span></span></div></div>');

        $(document).on('click', '.table-invoices_request button.btn-issue-po', handleIssuePo);
        $(document).on('click', '.table-invoices_request button.btn-request-send-draft-invoice', handleSendDraftInvoice);
        $(document).on('click', '.table-invoices_request button.btn-fix-po', handleFixPo);
        $(document).on('click', '.table-invoices_request button.btn-reject-po', showRejectPoModal);
        $(document).on('click', '.table-invoices_request button.btn-request-issue-po', handleRequestIssuePo);
    });

    function handleRequestIssuePo() {
        const btn = $(this);
        const id = $(this).data('id');

        btn.attr('disabled', true);
        btn.find('i.fa-spinner').removeClass('hide');
        $.ajax({
            type: 'post',
            dataType: 'json',
            url: admin_url + 'invoices/handle_request_issue_po/' + id,
            success: function(response) {
                if (response.success) {
                    alert_float('info', response.message);
                } else {
                    alert_float('danger', response.message);
                }
            },
            error: function(response) {
                alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
            },
            complete: function() {
                btn.attr('disabled', false);
                btn.find('i.fa-spinner').addClass('hide');
                tAPI.ajax.reload();
            }
        });
    }

    function handleIssuePo() {
        const btn = $(this);
        const id = $(this).data('id');

        var r = confirm('<?= _l('invoice_request_issue_po_confirm') ?>'.replace('{inv_number}', $(this).data('inv-number')));
        if (r == false) {
            return false;
        }

        btn.attr('disabled', true);
        btn.find('i.fa-spinner').removeClass('hide');
        $.ajax({
            type: 'post',
            dataType: 'json',
            url: admin_url + 'invoices/handle_issue_po/' + id,
            success: function(response) {
                if (response.success) {
                    alert_float('info', response.message);
                } else {
                    alert_float('danger', response.message);
                }
            },
            error: function(response) {
                alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
            },
            complete: function() {
                btn.attr('disabled', false);
                btn.find('i.fa-spinner').addClass('hide');
                tAPI.ajax.reload();
            }
        });
    }

    function handleSendDraftInvoice() {
        const btn = $(this);
        const id = $(this).data('id');

        var r = confirm('Are you sure you want to send the invoice ' + $(this).data('inv-number') + ' to customer?');
        if (r == false) {
            return false;
        }

        btn.attr('disabled', true);
        btn.find('i.fa-spinner').removeClass('hide');
        $.ajax({
            type: 'post',
            dataType: 'json',
            url: admin_url + 'invoices/handle_send_draft_invoice/' + id,
            success: function(response) {
                if (response.success) {
                    alert_float('info', response.message);
                } else {
                    alert_float('danger', response.message);
                }
            },
            error: function(response) {
                alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
            },
            complete: function() {
                btn.attr('disabled', false);
                btn.find('i.fa-spinner').addClass('hide');
                tAPI.ajax.reload();
            }
        });
    }

    function handleFixPo() {
        const btn = $(this);
        const id = $(this).data('id');

        btn.attr('disabled', true);
        btn.find('i.fa-spinner').removeClass('hide');
        $.ajax({
            type: 'post',
            dataType: 'json',
            url: admin_url + 'invoices/handle_fix_po/' + id,
            success: function(response) {
                if (response.success) {
                    alert_float('info', response.message);
                } else {
                    alert_float('danger', response.message);
                }
            },
            error: function(response) {
                alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
            },
            complete: function() {
                btn.attr('disabled', false);
                btn.find('i.fa-spinner').addClass('hide');
                tAPI.ajax.reload();
            }
        });

    }

    var data = {
        requestId: null
    }
    const modal = $('#reject_issue_po_modal');

    function showRejectPoModal(requestId) {
        data.requestId = $(this).data('id');
        $('#reject_issue_po_modal #rejectIssuePoTitle').html('Reject PO ' + $(this).data('title'));
        modal.modal('show');
    }

    modal.on('submit', function (event)  {
        event.preventDefault();
        handleRejectPo();
    });

    function handleRejectPo() {
        const btn = $('#reject_issue_po_modal .btn-reject-submit');
        const id = data.requestId;
        const reason = $('#reject_issue_po_modal #reason').val();

        btn.attr('disabled', true);
        btn.find('i.fa-spinner').removeClass('hide');
        $.ajax({
            type: 'post',
            dataType: 'json',
            data: {
                reason
            },
            url: admin_url + 'invoices/handle_reject_po/' + id,
            success: function(response) {
                if (response.success) {
                    alert_float('info', response.message);
                } else {
                    alert_float('danger', response.message);
                }
            },
            error: function(response) {
                alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
            },
            complete: function() {
                btn.attr('disabled', false);
                btn.find('i.fa-spinner').addClass('hide');
                tAPI.ajax.reload();
                modal.modal('hide');
                $('#reject_issue_po_modal #reason').val('')
            }
        });
    }

    get_total_price_invoice();

    function get_total_price_invoice() {
        $.ajax({
            type: 'post',
            dataType: "json",
            url: admin_url + 'invoices/get_total_price_invoice',
            data: {
                request_status: $('select[name=request_status]').val(),
                company_name: $('select[name=company_name]').val(),
                contract_status: $('select[name=contract_status]').val(),
                date_invoice_request_from: $('input[name=date_invoice_request_from]').val(),
                date_invoice_request_to: $('input[name=date_invoice_request_to]').val(),
                status: $('select[name=status]').val(),
                name_invoice_reques: $('select[name=name_invoice_reques]').val(),
            },
            success: function(res) {
                if (res.success) {
                    $('.total_price_invoice span').html(res.data);
                } else {
                    $('.total_price_invoice span').html('Error');
                }
            }
        });
    }

    function reloadDataTable() {
        get_total_price_invoice();
        tAPI.ajax.reload();
    }

    function maskAsCompany(thisBtn, requestId) {
        const btn = $(thisBtn);

        btn.attr('disabled', true);
        btn.find('i.fa-spinner').removeClass('hide');
        $.ajax({
            type: 'post',
            dataType: 'json',
            url: admin_url + 'invoices/mask_request_as_company/' + requestId,
            success: function(response) {
                if (response.success) {
                    alert_float('info', response.message);
                } else {
                    alert_float('danger', response.message);
                }
            },
            error: function(response) {
                alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
            },
            complete: function() {
                btn.attr('disabled', false);
                btn.find('i.fa-spinner').addClass('hide');
                tAPI.ajax.reload();
            }
        });
    }
</script>

</body>

</html>

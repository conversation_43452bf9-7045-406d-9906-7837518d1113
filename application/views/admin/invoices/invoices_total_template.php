<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="row">
  <div class="col-md-4">
    <div id="date-range" class="mbot15">
      <div class="row">
        <div class="col-md-6">
          <label for="invoices_total_from_date" class="control-label"><?= _l('from') ?></label>
          <div class="input-group date">
            <input type="text" class="form-control datepicker" value="<?= $this->input->post('from_date') ?? date('01.m.Y', strtotime('now')) ?>" id="invoices_total_from_date" onchange="init_invoices_total()" name="invoices_total_from_date" autocomplete="off">
            <div class="input-group-addon">
              <i class="fa fa-calendar calendar-icon"></i>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <label for="invoices_total_to_date" class="control-label"><?= _l('to') ?></label>
          <div class="input-group date">
            <input type="text" class="form-control datepicker" value="<?= $this->input->post('to_date') ?? date('d.m.Y', strtotime('now')) ?>" id="invoices_total_to_date" onchange="init_invoices_total()" name="invoices_total_to_date" autocomplete="off">
            <div class="input-group-addon">
              <i class="fa fa-calendar calendar-icon"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
    <?php
    foreach ($stats as $key => $data) {?>
        <div class="col-md-3 total-column">
            <div class="panel_s mbot25">
                <div class="panel-body">
                    <h3 class="text-muted _total relative">
                        <span class="" style="position: absolute; left: 0; top: 0; font-size: 12px; border: solid 1px; border-radius: 4px; padding: 2px 5px">T: <?= $data['total'] ?></span>
                        <?= $data['qty'] ?>
                    </h3>
                    <span class="text-info">
                      <?= _l('counts_job_postings_by_payment_type') ?>: <?= $data['the_real_paymenttype'] ?>
                    </span>
                </div>
            </div>
        </div>
    <?php } ?>
</div>
<div class="row">
  <div class="col-md-3 total-column">
    <div class="panel_s mbot25">
      <div class="panel-body">
        <h3 class="text-muted _total">
          <?= app_format_money($total_amount ?? 0, 'vnd'); ?>
        </h3>
        <span class="text-info">
          <?= _l('monthly_invoice_report_detail_amount_title') ?>
          <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('dashboard_invoice_amount_tooltip') ?>"></i></a>
        </span>
      </div>
    </div>
  </div>
  <?php
  $total = array_sum(array_column($total_result, 'total'));
  $total_tax = array_sum(array_column($total_result, 'total_tax'));

  foreach ($total_result as $key => $data) { ?>
    <div class="col-md-3 total-column">
      <div class="panel_s mbot25">
        <div class="panel-body">
          <h3 class="text-muted _total">
            <?= app_format_money($data['total'], $data['currency_name']); ?>
          </h3>
          <span class="text-info">
            <?= _l($data['title'] ?? '') ?>
            <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l($data['tooltip'] ?? '') ?>"></i></a>
          </span>
        </div>
      </div>
    </div>
  <?php } ?>
</div>
<div class="clearfix"></div>
<script>
  init_datepicker();
</script>

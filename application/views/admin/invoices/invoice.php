<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
	<div class="content">
		<div class="row">
			<?php
			echo form_open($this->uri->uri_string(),array('id'=>'invoice-form','class'=>'_transaction_form invoice-form'));
			if(isset($invoice)){
				echo form_hidden('isedit');
				echo form_hidden('invoice_edit', $invoice->id);
				echo form_hidden('invoice_original_ad', $invoice->discount_percent);
			}
			?>
			<div class="col-md-12">
				<?php $this->load->view('admin/invoices/invoice_template'); ?>
			</div>
			<?php echo form_close(); ?>
			<?php $this->load->view('admin/invoice_items/item'); ?>
		</div>
	</div>
</div>
<?php init_tail(); ?>
<script>
	$(function(){
		validate_invoice_form();
	    // Init accountacy currency symbol
	    init_currency();
	    // Project ajax search
	    init_ajax_project_search_by_customer_id();
	    // Maybe items ajax search
	    init_ajax_search('items','#item_select.ajax-search',undefined,admin_url+'items/search');

		$("body").find('input[name="discount_percent"]').rules('add', {
			min: 0,
			max: 100,
			messages: {
				min: jQuery.validator.format("The Addition Discount entered is invalid"),
				max: jQuery.validator.format("The Addition Discount entered is invalid"),
			}
		});
	});
</script>
</body>
</html>

<?php defined('BASEPATH') or exit('No direct script access allowed');

$table_data = array(
  _l('invoice'),
  _l('invoice_value'),
  _l('date_invoice_request'),
  _l('request_invoice_request_status'),
  _l('request_invoice_company_name'),
  _l('request_invoice_email_table'),
  _l('request_invoice_output_content'),
  _l('request_invoice_contract_status'),
  _l('request_invoice_status'),
  _l('invoice_data_date_title'),
  _l('name_invoice_reques'),
  _l('request_invoice_note'),
  _l('request_status'),
);

render_datatable($table_data, (isset($class) ? $class : 'invoices_request'));

?>

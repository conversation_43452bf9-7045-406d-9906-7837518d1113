<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="col-md-12">
   <div class="panel_s mbot10">
      <div class="panel-body _buttons">
         <?php $this->load->view('admin/invoices/invoices_top_stats'); ?>
         <?php if(has_permission('invoices','','create')){ ?>
            <a href="<?= admin_url('invoices/invoice'); ?>" class="btn btn-info pull-left new new-invoice-list mright5"><?= _l('create_new_invoice'); ?></a>
         <?php } ?>
         <?php if(!isset($project)){ ?>
               <a href="<?= admin_url('invoices/recurring'); ?>" class="btn btn-info pull-left mright5">
                  <?= _l('invoices_list_recurring'); ?>
               </a>
         <?php } ?>
          <?php if (!isset($project) && !isset($customer) && staff_can('create', 'payments')) { ?>
              <button id="add-batch-payment" onclick="add_batch_payment()" class="btn btn-info pull-left">
                  <?= _l('batch_payments'); ?>
              </button>
          <?php } ?>
         <div class="display-block text-right">
            <div class="btn-group pull-right mleft4 invoice-view-buttons btn-with-tooltip-group _filter_data" data-toggle="tooltip" data-title="<?= _l('filter_by'); ?>">
               <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
               <i class="fa fa-filter" aria-hidden="true"></i>
               </button>
               <ul class="dropdown-menu width300">
                  <li>
                     <a href="#" data-cview="all" onclick="dt_custom_view('','.table-invoices',''); return false;">
                     <?= _l('invoices_list_all'); ?>
                     </a>
                  </li>
                  <li class="divider"></li>
                  <li class="<?php if($this->input->get('filter') == 'not_sent'){echo 'active';} ?>">
                     <a href="#" data-cview="not_sent" onclick="dt_custom_view('not_sent','.table-invoices','not_sent'); return false;">
                     <?= _l('not_sent_indicator'); ?>
                     </a>
                  </li>
                  <li>
                     <a href="#" data-cview="not_have_payment" onclick="dt_custom_view('not_have_payment','.table-invoices','not_have_payment'); return false;">
                     <?= _l('invoices_list_not_have_payment'); ?>
                     </a>
                  </li>
                  <li>
                     <a href="#" data-cview="recurring" onclick="dt_custom_view('recurring','.table-invoices','recurring'); return false;">
                     <?= _l('invoices_list_recurring'); ?>
                     </a>
                  </li>
                  <?php if(count($invoices_years) > 0){ ?>
                  <li class="divider"></li>
                  <?php foreach($invoices_years as $year){ ?>
                  <li class="active">
                     <a href="#" data-cview="year_<?php echo $year['year']; ?>" onclick="dt_custom_view(<?php echo $year['year']; ?>,'.table-invoices','year_<?php echo $year['year']; ?>'); return false;"><?php echo $year['year']; ?>
                     </a>
                  </li>
                  <?php } ?>
                  <?php } ?>
                  <?php if(count($invoices_sale_agents) > 0){ ?>
                  <div class="clearfix"></div>
                  <li class="divider"></li>
                  <li class="dropdown-submenu pull-left">
                     <a href="#" tabindex="-1"><?php echo _l('sale_agent_string'); ?></a>
                     <ul class="dropdown-menu dropdown-menu-left">
                        <?php foreach($invoices_sale_agents as $agent){ ?>
                        <li>
                           <a href="#" data-cview="sale_agent_<?php echo $agent['sale_agent']; ?>" onclick="dt_custom_view(<?php echo $agent['sale_agent']; ?>,'.table-invoices','sale_agent_<?php echo $agent['sale_agent']; ?>'); return false;"><?php echo $agent['full_name']; ?>
                           </a>
                        </li>
                        <?php } ?>
                     </ul>
                  </li>
                  <?php } ?>
                  <div class="clearfix"></div>
                  <?php if(count($payment_modes) > 0){ ?>
                  <li class="divider"></li>
                  <?php } ?>
                  <?php foreach($payment_modes as $mode){
                     if(total_rows(db_prefix().'invoicepaymentrecords',array('paymentmode'=>$mode['id'])) == 0){continue;}
                     ?>
                  <li>
                     <a href="#" data-cview="invoice_payments_by_<?php echo $mode['id']; ?>" onclick="dt_custom_view('<?php echo $mode['id']; ?>','.table-invoices','invoice_payments_by_<?php echo $mode['id']; ?>'); return false;">
                     <?php echo _l('invoices_list_made_payment_by',$mode['name']); ?>
                     </a>
                  </li>
                  <?php } ?>
               </ul>
            </div>
            <a href="#" class="btn btn-default btn-with-tooltip toggle-small-view hidden-xs" onclick="toggle_small_view('.table-invoices','#invoice'); return false;" data-toggle="tooltip" title="<?php echo _l('invoices_toggle_table_tooltip'); ?>"><i class="fa fa-angle-double-left"></i></a>
            <a href="#" class="btn btn-default btn-with-tooltip invoices-total" onclick="slideToggle('#stats-top'); init_invoices_total(true); return false;" data-toggle="tooltip" title="<?php echo _l('view_stats_tooltip'); ?>"><i class="fa fa-bar-chart"></i></a>
         </div>
      </div>
   </div>
   <div class="row">
      <div class="col-md-12" id="small-table">
         <div class="panel_s">
            <div class="panel-body">
            <div class="row _custom_filters">
                  <div class="col-md-2">
                     <?= render_select('customer_admins', $staff, ['staffid', ['firstname', 'lastname']], _l('customer_admins'), '', ['onchange' => "reloadDataTable('.table-invoices')"]); ?>
                  </div>
                  <div class="col-md-2">
                     <?= render_select('invoice_creator', $staff, ['staffid', ['firstname', 'lastname']], _l('invoice_creator'), '', ['onchange' => "reloadDataTable('.table-invoices')"]); ?>
                  </div>
                  <div class="col-md-2">
                     <?php
                     $invoices_status_options = [];
                     foreach ($invoices_statuses as $key => $status) {
                        $invoices_status_options[] = [
                           'value' => $status,
                           'text' => format_invoice_status($status, '', false)
                        ];
                     }
                     echo render_select('status', $invoices_status_options, ['value', 'text'], _l('estimate_dt_table_heading_status'), '', ['onchange' => "reloadDataTable('.table-invoices')"]); ?>
                  </div>
                  <div class="col-md-2">
                     <?= render_select('customer_source', get_all_customer_sources(), ['value', 'text'], _l('customer_source'), '', ['onchange' => "reloadDataTable('.table-invoices')"]); ?>
                  </div>
                  <div class="col-md-2">
                     <?= render_select('type_of_customer', get_all_type_of_customers(), ['value', 'value'], _l('type_of_customer'), '', ['onchange' => "reloadDataTable('.table-invoices')"]); ?>
                  </div>
                  <div class="col-md-2">
                     <?= render_select('usage_behavior', get_all_usage_behavior(), ['value', 'value'], _l('usage_behavior'), '', ['onchange' => "reloadDataTable('.table-invoices')"]); ?>
                  </div>
                  <div class="col-md-2">
                     <?= render_select('department', get_list_department(), ['departmentid', 'name'], _l('department') . " (Customer admin's Team)", '', ['onchange' => "reloadDataTable('.table-invoices')"]); ?>
                  </div>
                  <div class="col-md-4">
                     <div id="date-range" class="mbot15">
                        <div class="row">
                           <div class="col-md-6">
                              <label for="from_date" class="control-label"><?= _l('from') . ' (' . _l('invoice_dt_table_heading_date') . ')' ?></label>
                              <div class="input-group date">
                                 <input type="text" class="form-control datepicker" id="from_date" onchange="reloadDataTable('.table-invoices')" name="from_date" autocomplete="off">
                                 <div class="input-group-addon">
                                    <i class="fa fa-calendar calendar-icon"></i>
                                 </div>
                              </div>
                           </div>
                           <div class="col-md-6">
                              <label for="to_date" class="control-label"><?= _l('to') . ' (' . _l('invoice_dt_table_heading_date') . ')' ?></label>
                              <div class="input-group date">
                                 <input type="text" class="form-control datepicker" id="to_date" onchange="reloadDataTable('.table-invoices')" name="to_date" autocomplete="off">
                                 <div class="input-group-addon">
                                    <i class="fa fa-calendar calendar-icon"></i>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                <div class="col-md-2">
                    <label for="project_tag_reference" class="control-label"><?= _l('project_tag_reference') ?></label>
                    <input type="text" class="form-control" id="project_tag_reference" name="project_tag_reference" onchange="reloadDataTable('.table-invoices')" autocomplete="off">
                </div>
               </div>
               <!-- if invoiceid found in url -->
               <?= form_hidden('invoiceid',$invoiceid); ?>
               <?php $this->load->view('admin/invoices/table_html'); ?>
            </div>
         </div>
      </div>
      <div class="col-md-7 small-table-right-col">
         <div id="invoice" class="hide">
         </div>
      </div>
   </div>
</div>

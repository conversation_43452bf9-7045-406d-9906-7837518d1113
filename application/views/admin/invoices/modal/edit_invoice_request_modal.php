<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="modal fade" id="edit_customer_email_modal" tabindex="-1" role="dialog" aria-labelledby="addNoteModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button group="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="rejectIssuePoTitle">Edit Customer Email</h4>
            </div>
            <?= form_open('', ['id' => 'edit_customer_email_form']); ?>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <input type="text" class="form-control" id="received_email" name="received_email">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <input type="hidden" id="invoice_request_id" />
                <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                <button group="submit" class="btn btn-info btn-submit"><?= _l('submit'); ?> <i class="fa fa-spinner fa-spin hide"></i></button></button>
            </div>
            <?= form_close(); ?>
        </div>
    </div>
</div>
<script>
    var editRequestModal = null;
    window.addEventListener("DOMContentLoaded", () => {
        $('#edit_customer_email_modal #received_email').tagit({
            animate: false,
            tagLimit: 3,
            placeholderText: 'Add cc',
            caseSensitive: false,
            allowSpaces: false,
            showAutocompleteOnFocus: true,
            autocomplete: {
                appendTo: '#inputTagsWrapper',
            },
            beforeTagAdded: function(event, ui) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (ui.tagLabel && !emailRegex.test(ui.tagLabel)) {
                    return false;
                }
            }
        });

        editRequestModal = $('#edit_customer_email_modal');
        editRequestModal.on('submit', function (event)  {
            event.preventDefault();
            handleUpdateCustomerEmail();
        });
    });

    function handleUpdateCustomerEmail() {
        const btn = $('#edit_customer_email_modal .btn-submit');
        const requestId = $('#edit_customer_email_modal').find('#invoice_request_id').val();
        const email = $('#edit_customer_email_modal').find('#received_email').tagit('assignedTags');

        btn.attr('disabled', true);
        btn.find('i.fa-spinner').removeClass('hide');
        $.ajax({
            type: 'post',
            dataType: 'json',
            data: {
                email
            },
            url: admin_url + 'invoices/update_invoice_request/' + requestId,
            success: function(response) {
                if (response.success) {
                    alert_float('info', response.message);
                } else {
                    alert_float('danger', response.message);
                }
            },
            error: function(response) {
                alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
            },
            complete: function() {
                btn.attr('disabled', false);
                btn.find('i.fa-spinner').addClass('hide');
                editRequestModal.modal('hide');
                tAPI && tAPI.ajax.reload();
            }
        });
    }

    function showEditRequestModal(requestId, emailStr) {
        editRequestModal.find('#invoice_request_id').val(requestId);
        var emailInput = editRequestModal.find('#received_email');
        emailInput.tagit('removeAll');
        var emailArr = emailStr.split(',');
        emailArr.forEach(function (email) {
            emailInput.tagit('createTag', email);
        });
        editRequestModal.modal('show');
    }
</script>
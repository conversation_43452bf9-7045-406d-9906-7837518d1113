<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="modal fade" id="edit_customer_address_modal" tabindex="-1" role="dialog" aria-labelledby="addNoteModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button group="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="rejectIssuePoTitle">Edit Customer Address</h4>
            </div>
            <?= form_open('', ['id' => 'edit_customer_address_form']); ?>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <?php echo render_input('edit_company_name', 'Customer Name', '', 'text', ['placeholder' => 'Input Customer name']); ?>
                    </div>
                    <div class="col-md-12">
                        <?php echo render_textarea('edit_export_address', 'Customer Address', '', ['placeholder' => _l('Input Customer address'), 'rows' => 5]); ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <input type="hidden" id="invoice_request_id" />
                <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                <button group="submit" class="btn btn-info btn-submit"><?= _l('submit'); ?> <i class="fa fa-spinner fa-spin hide"></i></button></button>
            </div>
            <?= form_close(); ?>
        </div>
    </div>
</div>
<script>
    var editRequestAddressModal = null;
    window.addEventListener("DOMContentLoaded", () => {
        $($('#edit_customer_address_modal #edit_customer_address_form')).appFormValidator({
            rules: {
                edit_company_name: 'required',
                edit_export_address: 'required',
            },
        });
        editRequestAddressModal = $('#edit_customer_address_modal');
        editRequestAddressModal.on('submit', function(event) {
            event.preventDefault();
            var form = $('#edit_customer_address_modal #edit_customer_address_form');

            if (form.valid()) {
                handleUpdateCustomerAddress();
            }
        });
    });

    function handleUpdateCustomerAddress() {
        const btn = $('#edit_customer_address_modal .btn-submit');
        const requestId = $('#edit_customer_address_modal').find('#invoice_request_id').val();
        const company_name = $('#edit_customer_address_modal').find('#edit_company_name').val();
        const export_address = $('#edit_customer_address_modal').find('#edit_export_address').val();

        btn.attr('disabled', true);
        btn.find('i.fa-spinner').removeClass('hide');
        $.ajax({
            type: 'post',
            dataType: 'json',
            data: {
                company_name,
                export_address,
            },
            url: admin_url + 'invoices/update_invoice_request/' + requestId,
            success: function(response) {
                if (response.success) {
                    alert_float('info', response.message);
                } else {
                    alert_float('danger', response.message);
                }
            },
            error: function(response) {
                alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
            },
            complete: function() {
                btn.attr('disabled', false);
                btn.find('i.fa-spinner').addClass('hide');
                editRequestAddressModal.modal('hide');
                tAPI && tAPI.ajax.reload();
            }
        });
    }

    function showEditRequestAddressModal(requestId, company_name, export_address) {
        editRequestAddressModal.find('#invoice_request_id').val(requestId);
        editRequestAddressModal.find('#edit_company_name').val(company_name);
        editRequestAddressModal.find('#edit_export_address').val(export_address);
        editRequestAddressModal.modal('show');
    }
</script>
<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="col-md-12 no-padding animated fadeIn">
    <div class="panel_s">
        <?php echo form_open(admin_url('invoices/request_invoice_submit'), array('id' => 'request_invoice_form')); ?>
        <?php echo form_hidden('invoiceid', $invoice->id); ?>
        <div class="panel-body">
            <h4 class="no-margin"><?php echo _l('export_invoice'); ?> <?php echo format_invoice_number($invoice->id); ?></h4>
            <hr class="hr-panel-heading" />
            <div class="row received_email">
                <div class="col-md-12">
                    <div class="form-group" app-field-wrapper="received_email">
                        <label for="received_email" class="control-label"> <small class="req text-danger">* </small><?php echo _l('request_invoice_email'); ?></label>
                        <div class="request_invoice_email">
                            <div class="all-mail"></div>
                            <div class="item_input_email col-md-3">
                                <input type="text" id="received_email" name="received_email" class="form-control" placeholder="<?php echo _l('request_invoice_email_placeholder'); ?>" autocomplete="off" value="">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <?php
                $field_status = array(

                    'type' => 'radio-group',
                    'name' => 'request_status',
                    'label' => _l('request_invoice_request_status'),
                    'values' => array(
                        [
                            'value' => 1,
                            'selected' => true,
                            'name' => 'request_invoice_request_status1',
                            'label' => _l('request_invoice_request_status1'),
                            'inline' => true,
                        ],
                        [
                            'value' => 2,
                            'name' => 'request_invoice_request_status2',
                            'label' => _l('request_invoice_request_status2'),
                            'inline' => true,
                        ]
                    )
                );
                $json = json_encode($field_status);
                $field = json_decode($json, false);
                echo render_form_builder_field($field) ?>

                <div class="col-md-4 item_contract_payment_date">
                    <div class="form-group">
                        <?php echo render_date_input(
                            'contract_payment_date',
                            _l('contract_payment_date'),
                            '',
                            ['placeholder' => _l('contract_payment_date_holder'), 'autocomplete' => 'off']
                        ); ?>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <?php echo render_select('service_provided', SERVICE_APPLANCER, ['value', 'text'], _l('request_invoice_service')); ?>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <?php echo render_input('company_name', 'Company issues invoices', $client->business_name ?? '', 'text', ['placeholder' => _l('request_invoice_company_name_holder'), 'autocomplete' => 'off', 'readonly' => true], [], 'company-group', 'input'); ?>
                </div>
                <div class="col-md-6">
                    <?php echo render_textarea('export_address', 'request_invoice_export_address', $client->address ?? '', ['placeholder' => _l('request_invoice_export_address_holder'), 'readonly' => true], [], 'company-group', 'input'); ?>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <?php echo render_input('vat', 'request_invoice_vat_holder', $client->vat ?? '', 'text', ['placeholder' => _l('request_invoice_vat_holder'), 'autocomplete' => 'off', 'readonly' => true], []); ?>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <?php echo render_select('contract_status', CONTRACT_STATUS, ['value', 'text'], _l('request_invoice_contract_status')); ?>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <?php echo render_textarea('content', _l('request_invoice_output_content'), '', ['placeholder' => _l('request_invoice_output_content_holder'), 'autocomplete' => 'off']); ?>
                </div>
                <div class="col-md-6">
                    <?php echo render_textarea('note', _l('request_invoice_note'), '', ['placeholder' => _l('request_invoice_note_holder')]); ?>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <input type="checkbox" id="invoice_draft" name="invoice_draft" class="top2_relative" checked value="1"> <label style="cursor: pointer;" for="invoice_draft"><?php echo _l('invoice_draft_placeholder'); ?></label>
                </div>
                <div class="col-md-12">
                    <input type="checkbox" id="walk_in_customer" name="walk_in_customer" class="top2_relative" value="1"> <label style="cursor: pointer;" for="walk_in_customer">Walk-in customer</label>
                </div>
            </div>

            <div class="pull-right mtop15">
                <a href="#" class="btn btn-danger" onclick="init_invoice(<?php echo $invoice->id; ?>); return false;"><?php echo _l('cancel'); ?></a>
                <button type="button" id="requestInvBtn" autocomplete="off" data-loading-text="<?php echo _l('wait_text'); ?>" data-form="#record_payment_form" class="btn btn-success"><?php echo _l('submit'); ?> <i class="fa fa-spinner fa-spin hide"></i></button>
            </div>
            <?php
            if ($payments) { ?>
                <div class="mtop25 inline-block full-width">
                    <h5 class="bold"><?php echo _l('invoice_payments_received'); ?></h5>
                    <?php include_once(APPPATH . 'views/admin/invoices/invoice_payments_table.php'); ?>
                </div>
            <?php } ?>
        </div>
        <?php echo form_close(); ?>
    </div>
</div>
<script>
    $(function() {
        init_selectpicker();
        init_datepicker();

        // Custom validate VAT number
        jQuery.validator.addMethod("validateVAT", function(value, element) {
            let pattern = "";

            // Check for 10-digit VAT numbers
            if (value.length === 10) {
                pattern = /\d{10}$/;
            }
            // Check for 14-digit VAT numbers
            else if (value.length === 14) {
                pattern = /\d{10}-\d{3}$/;
            }
            // Invalid VAT number length
            else {
                return false;
            }

            // Check if the VAT number matches the pattern
            if (!value.match(pattern)) {
                return false;
            }

            return this.optional(element) || true;
        }, 'Please enter a valid VAT number.');

        jQuery.validator.addMethod("validateEmail", function(value, element) {
            if ($('.all-mail .item_email').length == 0)
                return false;

            return true;
        }, 'Enter email');

        const validationRules = {
            received_email: {
                validateEmail: true,
            },
            service_provided: 'required',
            company_name: 'required',
            export_address: 'required',
            vat: {
                validateVAT: true,
                required: true
            },
            contract_status: 'required',
            content: 'required',
        };
        // appValidateForm($('#request_invoice_form'), validationRules, submitRequestInvoiceForm);
        $('#request_invoice_form').appFormValidator({
            rules: validationRules
        });

        $('input[name=request_status]').change(function() {
            var value = $(this).val();
            if (value == 2) {
                validationRules['contract_payment_date'] = 'required';
                $('.item_contract_payment_date').show();
            } else {
                $('.item_contract_payment_date').hide();
                delete validationRules.contract_payment_date;
            }
            // appValidateForm($('#request_invoice_form'), validationRules, submitRequestInvoiceForm);
            $('#request_invoice_form').appFormValidator({
                rules: validationRules
            });
        });

        $('#request_invoice_form input[name=walk_in_customer]').change(function() {
            var checked = $(this).is(':checked');
            var labelText = $('#request_invoice_form .company-group label[for=company_name]').text();
            if (checked) {
                $('#request_invoice_form .company-group .input').removeAttr('readonly');
                $('#request_invoice_form .company-group label[for=company_name]').html('<small class="req text-danger">* </small> Customer Name');
                $('#request_invoice_form .received_email').addClass('hide');
                delete validationRules.received_email;
            } else {
                $('#request_invoice_form .company-group .input').attr('readonly', 1);
                $('#request_invoice_form .company-group label[for=company_name]').html('<small class="req text-danger">* </small> Company issues invoices');
                $('#request_invoice_form .received_email').removeClass('hide');
                validationRules['received_email'] = { validateEmail: true };
            }
            $('#request_invoice_form').appFormValidator({
                rules: validationRules
            });
        });

        $(document).on('click', '#requestInvBtn', submitRequestInvoiceForm)

        function submitRequestInvoiceForm() {
            const form = $($('#request_invoice_form')[0]);

            var formURL = form.attr("action");
            var formData = new FormData(form[0]);

            // Get received_email
            var received_email = [];
            $('.all-mail .item_email').each(function(index, value) {
                received_email.push($(value).text());
            });

            formData.append('received_email', received_email);

            const validator = form.validate();

            if (!form.valid()) {
                alert_float('warning', 'Please check validation errors!');
                return;
            }

            $('#requestInvBtn').attr('disabled', true).find('i.fa-spinner').removeClass('hide');

            $.ajax({
                type: 'POST',
                data: formData,
                mimeType: "multipart/form-data",
                contentType: false,
                dataType: 'json',
                cache: false,
                processData: false,
                url: formURL,
                success: function(response) {
                    if (response.success) {
                        alert_float('success', response.message);
                        var invoiceid = formData.get("invoiceid");
                        init_invoice(invoiceid);
                    } else {
                        alert_float('danger', response.message);
                    }
                },
                error: function(error) {
                    alert_float('danger', JSON.parse(error.responseText));
                },
                complete: function() {
                    $('#requestInvBtn').removeAttr('disabled').find('i.fa-spinner').addClass('hide');
                }
            });
        }
    });
</script>
<script>
    // validateEmail
    function isValidEmail(email) {
        // Biểu thức chính quy để kiểm tra định dạng email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        // Kiểm tra xem email có khớp với biểu thức chính quy không
        return emailRegex.test(email);
    }

    $("#received_email").keydown(function(e) {
        if (e.keyCode == 13 || e.keyCode == 32 || e.keyCode == 9 || e.keyCode == 188) {
            //alert('You Press enter');
            e.preventDefault();
            var getValue = $(this).val();
            // check email
            if (isValidEmail(getValue) && $('.all-mail .item_email').length < 3) {
                $('.all-mail').append('<span class="email-ids"><span class="item_email">' + getValue + '</span> <span class="cancel-email">x</span></span>');
                $('#received_email').val('');
                $('#received_email').focus();
            }
        }
    });

    /// Cancel
    $(document).on('click', '.cancel-email', function() {
        $(this).parent().remove();
    });
</script>

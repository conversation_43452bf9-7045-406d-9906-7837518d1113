<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">

    <div class="content">
        <div class="row">
            <div class="col-md-12" id="small-table">
                <div class="panel_s">
                    <div class="panel-body">
                        <?php
                            $table_data = array(
                                _l('invoice_dt_table_heading_number'),
                                _l('invoice_dt_table_heading_total_amount'),
                                array(
                                  'name'=>_l('invoice_estimate_year'),
                                  'th_attrs'=>array('class'=>'not_visible')
                                ),
                                _l('proposal_date_created'),
                                _l('invoice_dt_table_heading_id_customer'),
                                array(
                                  'name'=>_l('invoice_dt_table_heading_client'),
                                  'th_attrs'=>array('class'=>(isset($client) ? 'not_visible' : ''))
                                ),
                                _l('contact'),
                                _l('invoice_dt_table_heading_vat_number'),
                                '<PERSON><PERSON><PERSON> <PERSON>hu tiền dự kiến',
                                _l('invoice_dt_table_heading_payment_date'),
                                _l('invoice_dt_table_heading_status'),
                                _l('invoice_dt_table_heading_customer_admin'),
                                _l('customer_source'),
                            );
                            $custom_fields = get_custom_fields('invoice',array('show_on_table'=>1));
                            foreach($custom_fields as $field){
                                array_push($table_data,$field['name']);
                            }

                            $table_data = array_merge($table_data, [
                                _l('type_of_customer'),
                                _l('usage_behavior'),
                                _l('invoice_creator'),
                                _l('project_tag_reference'),
                                _l('clients_invoice_dt_status'),
                            ]);

                            render_datatable($table_data, (isset($class) ? $class : 'invoice-addition-discount-approval'));
                        ?>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<?php init_tail(); ?>

<script>
    var tAPI,
        InvoiceRequestServerParams = {};

    tAPI = initDataTable(
        '.table-invoice-addition-discount-approval',
        admin_url + 'invoices/addition_discount_approval_table',
        'undefined',
        'undefined',
        InvoiceRequestServerParams,
        [0, 'desc']
    );

    $(document).ready(function() {
        $('.dataTables_wrapper .row:nth-child(2)').append('<div class="total_price_invoice"><?php echo _l('total_invoice_value') ?>: <span></span></div></div>');
        $(document).on('click', '.table-invoice-addition-discount-approval button.approve-btn', handleApproveRequest);
        $(document).on('click', '.table-invoice-addition-discount-approval button.reject-btn', handleRejectRequest);
    });

    function handleApproveRequest() {
        const btn = $(this);
        const id = $(this).data('id');

        var r = confirm('<?= _l('approve_addition_discount_confirmation') ?>'.replace('{inv_number}', $(this).data('inv-number')));
        if (r == false) {
            return false;
        }

        btn.attr('disabled', true);
        btn.find('i.fa-spinner').removeClass('hide');
        $.ajax({
            type: 'post',
            dataType: 'json',
            url: admin_url + 'invoices/approve_addition_discount/' + id,
            success: function(response) {
                if (response.success) {
                    alert_float('info', response.message);
                } else {
                    alert_float('danger', response.message);
                }
            },
            error: function(response) {
                alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
            },
            complete: function() {
                btn.attr('disabled', false);
                btn.find('i.fa-spinner').addClass('hide');
                tAPI.ajax.reload();
            }
        });
    }

    function handleRejectRequest() {
        const btn = $(this);
        const id = $(this).data('id');

        var r = confirm('<?= _l('reject_addition_discount_confirmation') ?>'.replace('{inv_number}', $(this).data('inv-number')));
        if (r == false) {
            return false;
        }

        btn.attr('disabled', true);
        btn.find('i.fa-spinner').removeClass('hide');
        $.ajax({
            type: 'post',
            dataType: 'json',
            url: admin_url + 'invoices/reject_addition_discount/' + id,
            success: function(response) {
                if (response.success) {
                    alert_float('info', response.message);
                } else {
                    alert_float('danger', response.message);
                }
            },
            error: function(response) {
                alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
            },
            complete: function() {
                btn.attr('disabled', false);
                btn.find('i.fa-spinner').addClass('hide');
                tAPI.ajax.reload();
            }
        });
    }
</script>

</body>

</html>

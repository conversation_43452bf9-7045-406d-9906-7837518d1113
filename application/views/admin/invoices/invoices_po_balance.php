<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<?php init_head(); ?>

<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12" id="small-table">
                <div class="panel_s">
                    <div class="panel-body">
                        <?php include_once(APPPATH.'views/admin/invoices/filter_params.php'); ?>
                        <div class="row _custom_filters">
                            <div class="col-md-2">
                                <?= render_select('customer_admins', $staff, ['staffid', ['firstname', 'lastname']], _l('customer_admins'), '', ['onchange' => "reloadDataTable('.table-table-invoice-po-balance')"]); ?>
                            </div>
                            <div class="col-md-2">
                                <?= render_select('invoice_creator', $staff, ['staffid', ['firstname', 'lastname']], _l('invoice_creator'), '', ['onchange' => "reloadDataTable('.table-table-invoice-po-balance')"]); ?>
                            </div>
                            <div class="col-md-2">
                                <?php
                                $invoices_status_options = [];
                                foreach ($invoices_statuses as $key => $status) {
                                    $invoices_status_options[] = [
                                        'value' => $status,
                                        'text' => format_invoice_status($status, '', false)
                                    ];
                                }
                                echo render_select('status', $invoices_status_options, ['value', 'text'], _l('estimate_dt_table_heading_status'), '', ['onchange' => "reloadDataTable('.table-table-invoice-po-balance')"]); ?>
                            </div>
                            <div class="col-md-2">
                                <?= render_select('customer_source', get_all_customer_sources(), ['value', 'text'], _l('customer_source'), '', ['onchange' => "reloadDataTable('.table-table-invoice-po-balance')"]); ?>
                            </div>
                            <div class="col-md-2">
                                <?= render_select('type_of_customer', get_all_type_of_customers(), ['value', 'value'], _l('type_of_customer'), '', ['onchange' => "reloadDataTable('.table-table-invoice-po-balance')"]); ?>
                            </div>
                            <div class="col-md-2">
                                <?= render_select('usage_behavior', get_all_usage_behavior(), ['value', 'value'], _l('usage_behavior'), '', ['onchange' => "reloadDataTable('.table-table-invoice-po-balance')"]); ?>
                            </div>
                            <div class="col-md-2">
                                <?= render_select('department', get_list_department(), ['departmentid', 'name'], _l('department') . " (Customer admin's Team)", '', ['onchange' => "reloadDataTable('.table-table-invoice-po-balance')"]); ?>
                            </div>
                            <div class="col-md-4">
                                <div id="date-range" class="mbot15">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="from_date" class="control-label"><?= _l('from') . ' (' . _l('invoice_dt_table_heading_date') . ')' ?></label>
                                            <div class="input-group date">
                                                <input type="text" class="form-control datepicker" id="from_date" onchange="reloadDataTable('.table-table-invoice-po-balance')" name="from_date" autocomplete="off">
                                                <div class="input-group-addon">
                                                    <i class="fa fa-calendar calendar-icon"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="to_date" class="control-label"><?= _l('to') . ' (' . _l('invoice_dt_table_heading_date') . ')' ?></label>
                                            <div class="input-group date">
                                                <input type="text" class="form-control datepicker" id="to_date" onchange="reloadDataTable('.table-table-invoice-po-balance')" name="to_date" autocomplete="off">
                                                <div class="input-group-addon">
                                                    <i class="fa fa-calendar calendar-icon"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label for="project_tag_reference" class="control-label"><?= _l('project_tag_reference') ?></label>
                                <input type="text" class="form-control" id="project_tag_reference" name="project_tag_reference" onchange="reloadDataTable('.table-table-invoice-po-balance')" autocomplete="off">
                            </div>
                        </div>

                        <?php
                        $table_data = [
                            _l('PO #'),
                            _l('Date Created'),
                            _l('ID Customer'),
                            _l('Customer'),
                            _l('Customer Admin'),
                            _l('PO Creator'),
                            _l('Active On'),
                            _l('Expired Date'),
                            ['name' => _l('Item'), 'th_attrs' => ['class' => 'cel-item']],
                            ['name' => _l('Quantity'), 'th_attrs' => ['class' => 'cel-qty']],
                            ['name' => _l('Used Quantity'), 'th_attrs' => ['class' => 'cel-used-qty']],
                            ['name' => _l('Remaining Quantity'), 'th_attrs' => ['class' => 'cel-remaining-qty']],
                            ['name' => _l('Remaining Amount'), 'th_attrs' => ['class' => 'cel-remaining-amount']],
                        ];

                        render_datatable($table_data, 'table-invoice-po-balance');
                        ?>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<?php init_tail(); ?>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    var tAPI,
        InvoiceRequestServerParams = {};

    // Invoices additional server params
    var Sales_table_Filter = $('._hidden_inputs._filters input');

    $.each(Sales_table_Filter, function() {
        InvoiceRequestServerParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
    });
    $.each($('._custom_filters select'), function() {
        InvoiceRequestServerParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
    });
    $.each($('._custom_filters input'), function() {
        InvoiceRequestServerParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
    });

    // Render detail child table
    function renderDetailTable(items) {
        const table = $("<table>").addClass('table dataTable no-footer');
        const tbody = $("<tbody>");

        table.append(tbody);

        items.forEach(item => {
            const tr = $("<tr>");
            tr.append($("<td class='cel-child-item'>" + item.name + "</td>"));
            tr.append($("<td class='cel-child-qty'>" + item.paid_packages + "</td>"));
            tr.append($("<td class='cel-child-used-qty'>" + item.used_packages + "</td>"));
            tr.append($("<td class='cel-child-remaining-qty'>" + item.remain_packages + "</td>"));
            tr.append($("<td class='cel-child-remaining-amount'>" + item.remain_amount + "</td>"));

            tbody.append(tr);
        });

        return table;
    }

    function buildChildTable(row, data, index, cols) {
        const detailColumnIndex = 8;
        const detailColumn = $(cols[detailColumnIndex]);

        detailColumn.attr('colspan', 5);
        detailColumn.css('padding', 0);
        detailColumn.html(renderDetailTable(data[detailColumnIndex]));

        // Hide the rest colum
        [9, 10, 11, 12].forEach(item => $(cols[item]).addClass('hide'));
    }

    // Init table
    tAPI = initDataTable(
        '.table-table-invoice-po-balance',
        admin_url + 'invoices/po_balance_table',
        'undefined',
        'undefined',
        InvoiceRequestServerParams,
        [0, 'desc'], {
            createdRow: function(row, data, index, cols) {
                buildChildTable(row, data, index, cols);
            }
        }
    );

    // Auto resize child table
    tAPI.on('draw', function() {
        const childElements = ['.cel-child-item', '.cel-child-qty', '.cel-child-used-qty', '.cel-child-remaining-qty', '.cel-child-remaining-amount'];
        const parentElements = ['.cel-item', '.cel-qty', '.cel-used-qty', '.cel-remaining-qty', '.cel-remaining-amount'];

        childElements.forEach((childElement, index) => {
            const width = $(parentElements[index])[0].clientWidth;
            $(childElement).each((index, item) => $(item).css('width', width + 'px'));
        });
    });
</script>

</body>

</html>
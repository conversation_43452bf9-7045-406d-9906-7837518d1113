<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="col-md-12 no-padding">
    <div id="report_transactions">

        <div class="col-md-12 no-padding">
            <button id="exportReport" type="button" disabled class="btn btn-info mright5 test pull-left display-block">
                <i class="fa fa-spinner fa-spin hide" style="margin-right: 5px;"></i>
                <?= _l('monthly_invoice_report_export_btn'); ?>
            </button>
        </div>

        <div class="col-md-2 no-padding mtop10 _filters">
            <input type="text" class="form-control" placeholder="Select transaction month" id="trans_month" name="trans_month">
        </div>

        <div class="col-md-12 no-padding mtop10">
            <h4 class="bold"><?php echo _l('monthly_invoice_report_tab_transactions_title'); ?></h4>
            <?php render_datatable([
                'Datetime',
                'Company Name',
                'Bank account',
                'Amount',
                'Note',
            ], 'statistic-transactions'); ?>
        </div>
    </div>
</div>
<script>
    window.addEventListener("DOMContentLoaded", (event) => {
        let params = {};

        $.each($('#report_transactions ._filters input'), function() {
            params[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });

        var invoiceJobPostingSummaryTable = initDataTable(
            '.table-statistic-transactions',
            admin_url + 'reports/invoice_transactions',
            [0, 1, 2, 3, 4],
            [0, 1, 2, 3, 4],
            params,
            [], {
                searching: false,
                paging: false,
                buttons: [{
                    text: '<i class="fa fa-refresh"></i>',
                    className: 'btn btn-default-dt-options btn-dt-reload',
                    action: function(e, dt, node, config) {
                        dt.ajax.reload();
                    }
                }]
            }
        );

        invoiceJobPostingSummaryTable.on('preXhr.dt', function() {
            $('#report_transactions div.loading-mask').removeClass('hide');
        });

        invoiceJobPostingSummaryTable.on('draw.dt', function() {
            if (invoiceJobPostingSummaryTable.rows().count()) {
                $('#report_transactions #exportReport').removeAttr('disabled');
            } else {
                $('#report_transactions #exportReport').attr('disabled', true);
            }
        });

        let selectedMonth = null;
        jSuites.calendar(document.getElementById('trans_month'), {
            readonly: false,
            placeholder: 'Select month',
            type: 'year-month-picker',
            format: 'YYYY.mm',
            today: true,
            onupdate: function() {
                if (selectedMonth != $('#trans_month').val()) {
                    selectedMonth = $('#trans_month').val();
                    invoiceJobPostingSummaryTable.ajax.reload();
                }
            },
        });

        invoiceJobPostingSummaryTable.on('xhr', function(e, settings, json) {
            const amounts = json?.amounts ?? {};
            for (const key in amounts) {
                let amount = amounts[key];
                let obj = document.getElementById(key);
                if (obj && amount > 0) {
                    animateValue(obj, 0, amount, 1000, true);
                } else {
                    $('#' + key).text(amount);
                }
            }
            $('#report_transactions div.loading-mask').addClass('hide');
        });

        $('#report_transactions #exportReport').click(function() {
            const btn = $(this);
            btn.attr('disabled', true);
            btn.find('i.fa-spinner').removeClass('hide');
            $.ajax({
                url: admin_url + 'reports/export_invoice_transactions/' + $('#report_transactions input#trans_month').val(),
                type: 'GET',
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data, status, xhr) {
                    let blob = data,
                        link = document.createElement('a'),
                        url = window.URL.createObjectURL(blob);

                    // Set the download filename
                    link.href = url;
                    link.download = `infoplus-trans-report-${$('#month').val()}.xlsx`;

                    // Trigger the download by simulating a click
                    link.click();

                    // Clean up the object URL after download
                    window.URL.revokeObjectURL(url);
                },
                error: function(xhr, status, error) {
                    alert_float('danger', status ?? 'Unknown error!');
                },
                complete: function() {
                    btn.attr('disabled', false);
                    btn.find('i.fa-spinner').addClass('hide');
                }
            });
        });
    });
</script>

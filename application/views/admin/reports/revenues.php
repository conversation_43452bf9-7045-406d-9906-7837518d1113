<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<style>
    .loading-mask {
        width: 100%;
        height: 100%;
        position: absolute;
        background-color: rgba(45, 62, 80, .4);
        color: white;
        z-index: 1;
        cursor: wait;
    }

    .loading-mask div {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        height: 20px;
    }
</style>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="panel_s">
                <div class="panel-body">
                    <div class="panel-title">
                        <h4><?= _l('job_revenue_report_title') ?></h4>
                    </div>
                    <div class="col-md-12 no-padding">
                        <ul class="nav nav-tabs">
                            <li>
                                <a aria-controls="daily_revenue" role="tab" data-toggle="tab" id="tab_daily_revenue" href="#daily_revenue"><?php echo _l('job_revenue_report_daily_tab_title'); ?></a>
                            </li>
                            <li>
                                <a aria-controls="monthly_revenue" role="tab" data-toggle="tab" id="tab_monthly_revenue" href="#monthly_revenue"><?php echo _l('job_revenue_report_monthly_tab_title'); ?></a>
                            </li>
                        </ul>
                        <div class="tab-content">
                            <div id="daily_revenue" class="tab-pane fade">
                                <?php include_once(APPPATH . 'views/admin/reports/job_revenue_report_daily.php'); ?>
                            </div>
                            <div id="monthly_revenue" class="tab-pane fade">
                                <?php include_once(APPPATH . 'views/admin/reports/job_revenue_report_monthly.php'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php init_tail(); ?>
<script src="/assets/plugins/jsuites/jsuites.js"></script>
<link rel="stylesheet" href="/assets/plugins/jsuites/jsuites.css" type="text/css" />

<script>
    window.addEventListener("DOMContentLoaded", (event) => {
        var validTabs = ['#tab_daily_revenue', '#tab_monthly_revenue'],
            tabId = (location.hash ?? '#daily_revenue').replace('#', '#tab_');

        tabId = validTabs.includes(tabId) ? tabId : '#tab_daily_revenue';
        $(tabId).tab('show');

        $('ul.nav-tabs li a').on('click', function () {
            location.href = this.href;
        });
    });
</script>

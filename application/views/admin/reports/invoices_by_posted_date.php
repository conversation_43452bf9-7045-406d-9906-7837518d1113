<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="col-md-12 no-padding">
        <div id="report_job_posting">
            <div class="col-md-12 no-padding">
                <button id="exportReport" type="button" disabled class="btn btn-info mright5 test pull-left display-block">
                    <i class="fa fa-spinner fa-spin hide" style="margin-right: 5px;"></i>
                    <?= _l('monthly_invoice_report_export_btn'); ?>
                </button>
            </div>
            <div class="col-md-2 no-padding mtop10 _filters">
                <input type="text" class="form-control jsuites" placeholder="Select posted month" id="post_month" name="post_month" autocomplete="off">
            </div>

            <div class="col-md-12 mtop10 no-padding relative">
                <div class="loading-mask text-center hide">
                    <div><?php echo _l('customer_statistic_please_wailt') ?></div>
                </div>
                <div class="col-md-12 mtop10 d-flex no-padding">
                    <div class="flex-grow-1 d-flex padding-5 mright5" style="background-color: #ECF2FF; border-radius: 10px;">
                        <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                            <div class="panel-body no-border no-border-color">
                                <h3 class="text-default _total">
                                    <?= _l('monthly_invoice_report_detail_quantity_title') ?>
                                    <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_quantity_tooltip') ?>"></i></a>
                                </h3>
                            </div>
                            <div class="panel-body no-border no-border-color">
                                <h3 id="posted_total_quantity" class="_total">0</h3>
                            </div>
                        </div>
                        <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                        <div class="panel-body no-border no-border-color">
                                <h3 class="text-default _total">
                                    <?= _l('monthly_invoice_report_detail_amount_title') ?>
                                    <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_amount_tooltip') ?>"></i></a>
                                </h3>
                            </div>
                            <div class="panel-body no-border no-border-color">
                                <h3 id="posted_total_amount" class="_total">0</h3>
                            </div>
                        </div>
                        <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                        <div class="panel-body no-border no-border-color">
                                <h3 class="text-default _total">
                                    <?= _l('monthly_invoice_report_detail_revenue_title') ?>
                                    <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_used_amount_tooltip') ?>"></i></a>
                                </h3>
                            </div>
                            <div class="panel-body no-border no-border-color">
                                <h3 id="posted_total_revenue" class="_total">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 no-padding mtop10">
                <h4 class="bold"><?php echo _l('monthly_invoice_report_detail_heading_title'); ?></h4>
                <?php render_datatable([
                    _l('monthly_invoice_report_detail_status_invoice_title'),
                    _l('monthly_invoice_report_job_posting_job_title'),
                    _l('monthly_invoice_report_detail_invoice_no_title'),
                    _l('monthly_invoice_report_detail_invoice_status_title'),
                    _l('monthly_invoice_report_detail_issued_date_title'),
                    _l('monthly_invoice_report_job_posting_start_title'),
                ], 'statistic-by-posting-date-detail'); ?>
            </div>
        </div>
</div>
<script>
    window.addEventListener("DOMContentLoaded", (event) => {
        let params = {};

        $.each($('#report_job_posting ._filters input'), function() {
            params[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });

        var invoiceJobPostingSummaryTable = initDataTable(
            '.table-statistic-by-posting-date-detail',
            admin_url + 'reports/invoice_report_by_posted_date',
            [0, 1, 2, 3, 4],
            [0, 1, 2, 3, 4],
            params,
            [], {
                searching: false,
                paging: false,
                buttons: [{
                    text: '<i class="fa fa-refresh"></i>',
                    className: 'btn btn-default-dt-options btn-dt-reload',
                    action: function(e, dt, node, config) {
                        dt.ajax.reload();
                    }
                }]
            }
        );

        invoiceJobPostingSummaryTable.on('preXhr.dt', function () {
            $('#report_job_posting div.loading-mask').removeClass('hide');
        });

        invoiceJobPostingSummaryTable.on('draw.dt', function() {
            if (invoiceJobPostingSummaryTable.rows().count()) {
                $('#report_job_posting #exportReport').removeAttr('disabled');
            } else {
                $('#report_job_posting #exportReport').attr('disabled', true);
            }
        });

        let selectedMonth = null;
        jSuites.calendar(document.getElementById('post_month'), {
            readonly: false,
            placeholder: 'Select posted month',
            type: 'year-month-picker',
            format: 'YYYY.mm',
            today: true,
            onupdate: function() {
                if (selectedMonth != $('#post_month').val()) {
                    selectedMonth = $('#post_month').val();
                    invoiceJobPostingSummaryTable.ajax.reload();
                }
            }
        });

        invoiceJobPostingSummaryTable.on('xhr', function(e, settings, json) {
            const amounts = json?.amounts ?? {};
            for (const key in amounts) {
                let amount = amounts[key];
                let obj = document.getElementById(key);
                if (obj && amount > 0) {
                    animateValue(obj, 0, amount, 1000, true);
                } else {
                    $('#' + key).text(amount);
                }
            }
            $('#report_job_posting div.loading-mask').addClass('hide');
        });

        $('#report_job_posting #exportReport').click(function() {
            const btn = $(this);
            btn.attr('disabled', true);
            btn.find('i.fa-spinner').removeClass('hide');
            $.ajax({
                url: admin_url + 'reports/export_invoice_by_job_posting_date/' + $('#report_job_posting input#post_month').val(),
                type: 'GET',
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data, status, xhr) {
                    let blob = data,
                        link = document.createElement('a'),
                        url = window.URL.createObjectURL(blob);

                    // Set the download filename
                    link.href = url;
                    link.download = `purchase-order-job-posting-report-${$('#month').val()}.xlsx`;

                    // Trigger the download by simulating a click
                    link.click();

                    // Clean up the object URL after download
                    window.URL.revokeObjectURL(url);
                },
                error: function(xhr, status, error) {
                    alert_float('danger', status ?? 'Unknown error!');
                },
                complete: function() {
                    btn.attr('disabled', false);
                    btn.find('i.fa-spinner').addClass('hide');
                }
            });
        });
    });
</script>

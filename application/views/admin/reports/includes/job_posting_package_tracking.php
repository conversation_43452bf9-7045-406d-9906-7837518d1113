<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div id="job-posting-package-tracking-report" class="hide">
    <div class="_custom_filters">
        <div class="row">
            <div class="col-md-3">
                <div class="form-group select-placeholder">
                    <label for="job_tracking_id_crm" class="control-label"><?= _l('filter_job_tracking_report_id_crm'); ?></label>
                    <select id="crm_id" name="job_tracking_id_crm" data-live-search="true" data-width="100%" class="ajax-search" multiple data-none-selected-text="<?= _l('dropdown_non_selected_tex'); ?>"></select>
                </div>
            </div>
            <div class="col-md-3">
                <?= render_select(
                    'job_tracking_ams_company_id',
                    [],
                    ['id', 'text'],
                    _l('filter_job_tracking_report_ams_company_id'),
                    '',
                    ['multiple' => true],
                    [],
                    '',
                    '',
                    false
                ); ?>
            </div>
            <div class="col-md-3">
                <?= render_select(
                    'job_tracking_sale',
                    get_list_staff(),
                    [
                        'staffid',
                        ['firstname', 'lastname']
                    ],
                    _l('filter_job_tracking_report_sale'),
                    '',
                    ['multiple' => true],
                    [],
                    '',
                    '',
                    false
                ); ?>
            </div>
            <div class="col-md-3">
                <?= render_select(
                    'job_tracking_invoice',
                    [],
                    ['id', 'text'],
                    _l('filter_job_tracking_report_invoice'),
                    '',
                    ['multiple' => true],
                    [],
                    '',
                    '',
                    false
                ); ?>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3">
                <?= render_date_input(
                    'job_tracking_report_invoice_expiration_from_date',
                    _l('filter_job_tracking_report_invoice_expiration_from_date')
                ) ?>
            </div>
            <div class="col-md-3">
                <?= render_date_input(
                    'job_tracking_report_invoice_expiration_to_date',
                    _l('filter_job_tracking_report_invoice_expiration_to_date')
                ) ?>
            </div>
            <div class="col-md-3">
                <?= render_select(
                    'job_tracking_report_type',
                    JOB_TRACKING_REPORT_TYPE_OPTIONS,
                    ['value', 'text'],
                    _l('filter_job_tracking_report_type')
                ); ?>
            </div>
        </div>
    </div>
    <div class="table-responsive">
        <?= render_datatable(
            [
                _l('job_tracking_report_id_crm'),
                _l('job_tracking_report_invoice_crm'),
                _l('job_tracking_report_sales'),
                _l('job_tracking_report_ams_company_id'),
                _l('job_tracking_report_ams_company_name'),
                _l('job_tracking_report_package_service_name'),
                _l('job_tracking_report_type'),
                _l('job_tracking_report_payment_date'),
                _l('job_tracking_report_invoice_expiration_date'),
                _l('job_tracking_report_status'),
                _l('job_tracking_report_activation_date'),
                _l('job_tracking_report_id_job'),
                _l('job_tracking_report_extended_retention_expiry_date'),
                _l('job_tracking_report_reactivation_date_for_retention'),
            ],
            'job-posting-package-tracking-report'
        ); ?>
    </div>
</div>

<script>
    window.addEventListener("DOMContentLoaded", () => {
        init_ajax_search('customer_has_invoice', '#crm_id.ajax-search');

        $('#crm_id').on('changed.bs.select', function(evt) {
            var clientId = $(this).val().join('-');

            const targetCompany = '#job_tracking_ams_company_id';
            const targetInvoice = '#job_tracking_invoice';

            if (clientId) {
                fetchRelationSelect(this, targetCompany, null, [], 'clients/get_ams_company/' + clientId);
                fetchRelationSelect(this, targetInvoice, null, [], 'clients/get_invoice/' + clientId);
            } else {
                fetchRelationSelect('', targetCompany, null, []);
                fetchRelationSelect('', targetInvoice, null, []);
            }
        });

        var timer;
        $('select[name="job_tracking_id_crm"],select[name="job_tracking_ams_company_id"],select[name="job_tracking_sale"],select[name="job_tracking_invoice"],input[name="job_tracking_report_invoice_expiration_from_date"],input[name="job_tracking_report_invoice_expiration_to_date"],select[name="job_tracking_report_type"],select[name="job_tracking_report_status"]').on('change', function() {
            clearTimeout(timer)
            timer = setTimeout(function() {
                gen_reports();
            }, 500);
        });
    })
</script>
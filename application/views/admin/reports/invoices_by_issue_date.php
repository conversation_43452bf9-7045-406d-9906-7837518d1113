<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="col-md-12 no-padding">
    <div id="invoice_by_issue_date">
        <div class="col-md-12 no-padding">
            <button id="exportReport" type="button" disabled class="btn btn-info mright5 test pull-left display-block">
                <i class="fa fa-spinner fa-spin hide" style="margin-right: 5px;"></i>
                <?= _l('monthly_invoice_report_export_btn'); ?>
            </button>
        </div>
        <div class="col-md-2 no-padding mtop10 _filters">
            <input type="text" class="form-control jsuites" placeholder="Select issue month" id="issue_month" name="issue_month" autocomplete="off">
        </div>
        <div class="col-md-12 mtop10 no-padding relative">
            <div class="loading-mask text-center hide">
                <div><?php echo _l('customer_statistic_please_wailt') ?></div>
            </div>
            <div class="col-md-12 mtop10 d-flex no-padding">
                <div class="flex-grow-1 d-flex padding-5 mright5" style="background-color: #ECF2FF; border-radius: 10px;">
                    <div class="col-xs-2 mleft5 no-padding-right panel no-mbot">
                        <div class="panel-body no-border no-border-color">
                            <h3 class="text-default _total">
                                <?= _l('monthly_invoice_report_detail_quantity_title') ?>
                                <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_quantity_tooltip') ?>"></i></a>
                            </h3>
                        </div>
                        <div class="panel-body no-border no-border-color">
                            <h3 id="invoice_quantity" class="_total">0</h3>
                        </div>
                    </div>
                    <div class="col-xs-3 mleft5 no-padding-right panel no-mbot">
                        <div class="panel-body no-border no-border-color">
                            <h3 class="text-default _total">
                                <?= _l('monthly_invoice_report_detail_amount_title') ?>
                                <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_amount_tooltip') ?>"></i></a>
                            </h3>
                        </div>
                        <div class="panel-body no-border no-border-color">
                            <h3 id="invoice_amount" class="_total">0</h3>
                        </div>
                    </div>
                    <div class="col-xs-3 mleft5 no-padding-right panel no-mbot">
                        <div class="panel-body no-border no-border-color">
                            <h3 class="text-default _total">
                                <?= _l('monthly_invoice_report_detail_advanced_amount_title') ?>
                                <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_unused_amount_tooltip') ?>"></i></a>
                            </h3>
                        </div>
                        <div class="panel-body no-border no-border-color">
                            <h3 id="advanced_amount" class="_total">0</h3>
                        </div>
                    </div>
                    <div class="col-xs-3 mleft5 no-padding-right panel no-mbot">
                        <div class="panel-body no-border no-border-color">
                            <h3 class="text-default _total">
                                <?= _l('monthly_invoice_report_detail_revenue_title') ?>
                                <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_used_amount_tooltip') ?>"></i></a>
                            </h3>
                        </div>
                        <div class="panel-body no-border no-border-color">
                            <h3 id="revenue_amount" class="_total">0</h3>
                        </div>
                    </div>
                    <div class="col-xs-3 mleft5 no-padding-right panel no-mbot">
                        <div class="panel-body no-border no-border-color">
                            <h3 class="text-default _total">
                                <?= _l('monthly_invoice_report_detail_unpaid_title') ?>
                                <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_unpaid_tooltip') ?>"></i></a>
                            </h3>
                        </div>
                        <div class="panel-body no-border no-border-color">
                            <h3 id="unpaid_amount" class="_total">0</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 no-padding mtop10">
            <h4 class="bold"><?php echo _l('monthly_invoice_report_detail_summary_heading_title'); ?></h4>
            <?php render_datatable([
                _l('monthly_invoice_report_detail_invoice_month_title'),
                _l('monthly_invoice_report_detail_month_title'),
                _l('monthly_invoice_report_detail_amount_title'),
                _l('monthly_invoice_report_detail_advanced_amount_title'),
                _l('monthly_invoice_report_detail_revenue_title'),
            ], 'statistic-by-issue-date'); ?>
        </div>
        <div class="col-md-12 no-padding mtop10">
            <input class="filter-details" type="hidden" id="issue_month_detail" name="issue_month_detail">
            <input class="filter-details" type="hidden" id="report_month_detail" name="report_month_detail">
            <input class="filter-details" type="hidden" id="advanced_amount" name="advanced_amount" value="0">
            <input class="filter-details" type="hidden" id="invoice_amount" name="invoice_amount" value="0">
            <h4 class="bold"><?php echo _l('monthly_invoice_report_detail_heading_title'); ?></h4>
            <?php render_datatable([
                _l('monthly_invoice_report_detail_status_invoice_title'),
                _l('monthly_invoice_report_job_posting_job_title'),
                _l('monthly_invoice_report_detail_invoice_no_title'),
                _l('monthly_invoice_report_detail_minvoice_no_title'),
                _l('monthly_invoice_report_detail_invoice_status_title'),
                _l('monthly_invoice_report_detail_issued_date_title'),
                _l('monthly_invoice_report_job_posting_start_title'),
            ], 'statistic-by-issue-date-detail'); ?>
        </div>
    </div>
</div>
<script>
    window.addEventListener("DOMContentLoaded", (event) => {
        let params = {},
            detailParams = {};

        $.each($('#invoice_by_issue_date ._filters input'), function() {
            params[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });

        $.each($('#invoice_by_issue_date input.filter-details'), function() {
            detailParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });

        var invoiceSummaryTable = initDataTable(
            '.table-statistic-by-issue-date',
            admin_url + 'reports/invoice_report_by_issue_date',
            [0, 1, 2, 3],
            [0, 1, 2, 3],
            params,
            [], {
                searching: false,
                paging: false,
                buttons: [{
                    text: '<i class="fa fa-refresh"></i>',
                    className: 'btn btn-default-dt-options btn-dt-reload',
                    action: function(e, dt, node, config) {
                        dt.ajax.reload();
                    }
                }]
            }
        );

        var invoiceDetailTable = initDataTable(
            '.table-statistic-by-issue-date-detail',
            admin_url + 'reports/invoice_report_by_issue_date_details',
            [0, 1, 2, 3, 4, 5],
            [0, 1, 2, 3, 4, 5],
            detailParams,
            [], {
                searching: false,
                paging: false,
                buttons: [{
                    text: '<i class="fa fa-refresh"></i>',
                    className: 'btn btn-default-dt-options btn-dt-reload',
                    action: function(e, dt, node, config) {
                        dt.ajax.reload();
                    }
                }]
            }
        );

        invoiceDetailTable.on('draw.dt', function() {
            $('.table-statistic-by-issue-date a i.fa-spinner:not(.hide)').addClass('hide');
        });

        invoiceSummaryTable.on('draw.dt', function() {
            if (invoiceSummaryTable.rows().count()) {
                $('#invoice_by_issue_date #exportReport').removeAttr('disabled');
            } else {
                $('#invoice_by_issue_date #exportReport').attr('disabled', true);
            }
            $('#invoice_by_issue_date input#issue_month_detail').val($('#issue_month').val());
            $('#invoice_by_issue_date input#report_month_detail').val($('#issue_month').val());
            $('#invoice_by_issue_date input#advanced_amount').val(0);
            $('#invoice_by_issue_date input#invoice_amount').val(1);
            invoiceDetailTable.clear().draw();
        });

        invoiceSummaryTable.on('xhr', function(e, settings, json) {
            const amounts = json?.amounts ?? {};
            for (const key in amounts) {
                let amount = amounts[key];
                let obj = document.getElementById(key);
                if (obj && amount > 0) {
                    animateValue(obj, 0, amount, 1000, true);
                } else {
                    $('#' + key).text(amount);
                }
            }
            $('#invoice_by_issue_date div.loading-mask').addClass('hide');
        });

        invoiceSummaryTable.on('preXhr.dt', function() {
            $('#invoice_by_issue_date div.loading-mask').removeClass('hide');
        });

        function showInvoiceDetail(event) {
            const aTag = event.target.tagName == 'I' ? $(event.target).closest('a') : $(event.target);
            const reportMonth = aTag.data('report-month');
            const advancedAmount = aTag.data('advanced-amount') ? 1 : 0;
            const invoiceAmount = aTag.data('invoice-amount') ? 1 : 0;

            aTag.find('i.fa-spinner').removeClass('hide');
            $('#invoice_by_issue_date input#report_month_detail').val(reportMonth);
            $('#invoice_by_issue_date input#advanced_amount').val(advancedAmount);
            $('#invoice_by_issue_date input#invoice_amount').val(invoiceAmount);
            invoiceDetailTable.ajax.reload();
        }

        $(document).on('click', '.table-statistic-by-issue-date a.revenue-amount', showInvoiceDetail);

        let selectedMonth = null;
        const jIssueMonth = jSuites.calendar(document.getElementById('issue_month'), {
            readonly: false,
            placeholder: 'Select issue month',
            type: 'year-month-picker',
            format: 'YYYY.mm',
            today: true,
            onupdate: function() {
                if (selectedMonth != $('#issue_month').val()) {
                    selectedMonth = $('#issue_month').val();
                    invoiceSummaryTable.ajax.reload();
                }
            },
        });

        // jQuery AJAX request to fetch the Excel file
        $('#invoice_by_issue_date #exportReport').click(function() {
            const btn = $(this);
            const issueMonth = $('#issue_month_detail').val();
            const reportMonth = $('#report_month_detail').val();
            const advancedAmount = $('#invoice_by_issue_date input#advanced_amount').val();
            const invoiceAmount = $('#invoice_by_issue_date input#invoice_amount').val();
            btn.attr('disabled', true);
            btn.find('i.fa-spinner').removeClass('hide');
            $.ajax({
                url: `${admin_url}reports/export_invoice_by_issue_date/${issueMonth}/${reportMonth}/${advancedAmount}/${invoiceAmount}`,
                type: 'GET',
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data, status, xhr) {
                    let blob = data,
                        link = document.createElement('a'),
                        url = window.URL.createObjectURL(blob);

                    // Set the download filename
                    link.href = url;
                    link.download = `purchase-order-job-posting-report-${issueMonth}${reportMonth ? ' - ' + reportMonth : ''}.xlsx`;

                    // Trigger the download by simulating a click
                    link.click();

                    // Clean up the object URL after download
                    window.URL.revokeObjectURL(url);
                },
                error: function(xhr, status, error) {
                    alert_float('danger', status ?? 'Unknown error!');
                },
                complete: function() {
                    btn.attr('disabled', false);
                    btn.find('i.fa-spinner').addClass('hide');
                }
            });
        });
    });
</script>

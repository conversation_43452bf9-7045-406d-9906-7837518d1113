<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="col-md-12 no-padding">
    <div id="revenue_report_monthly">
        <div class="d-flex align-items-center justify-content-between">
            <div class="no-padding _filters d-flex" style="gap: 5px">
                <div class="no-padding">
                    <input type="text" class="form-control" placeholder="Select issue month" id="report_month" name="report_month" autocomplete="off">
                </div>
                <div class="refresh-icon"></div>
            </div>
            <div class="no-paddingn d-flex">
                <button id="exportReport" type="button" disabled class="btn btn-info mright5 test pull-left display-block" style="height: 36px">
                    <i class="fa fa-spinner fa-spin hide" style="margin-right: 5px;"></i>
                    <?= _l('monthly_invoice_report_export_btn'); ?>
                </button>
                <div class="pagination-section"></div>
            </div>
        </div>
        <div class="col-md-12 mtop10 no-padding relative">
            <div class="loading-mask text-center hide">
                <div><?php echo _l('customer_statistic_please_wailt') ?></div>
            </div>
            <div class="col-md-12 mtop10 d-flex no-padding">
                <div class="flex-grow-1 d-flex padding-5 mright5" style="background-color: #ECF2FF; border-radius: 10px;">
                    <div class="col-xs-12 mleft5 no-padding-right panel no-mbot">
                        <div class="panel-body no-border no-border-color">
                            <h3 class="text-default _total">
                                Revenue Amount (No tax) (<span id="invoice_amt_month"></span>)
                                <!-- <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_amount_tooltip') ?>"></i></a> -->
                            </h3>
                        </div>
                        <div class="panel-body no-border no-border-color">
                            <h3 id="monthly_invoice_total_by_month" class="_total">0</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 no-padding mtop10">
            
            <div class="">
                <table class="dt-table-loading table table-monthly-revenue">
                    <thead>
                        <tr>
                            <th class="text-center" colspan="4"><?= _l('job_revenue_report_monthly_inv_info_title') ?></th>
                            <th class="text-center" colspan="5"><?= _l('job_revenue_report_monthly_jd_info_title') ?></th>
                            <th class="text-center" colspan="2"><?= _l('job_revenue_report_monthly_report_month_title') ?> (<span class="month-title"></span>)</th>
                        </tr>
                        <tr>
                            <th class="text-center"><?= _l('job_revenue_report_monthly_po_no_title') ?></th>
                            <th class="text-center"><?= _l('job_revenue_report_monthly_invoice_no_title') ?></th>
                            <th class="text-center">Issue Date</th>
                            <th class="text-center"><?= _l('job_revenue_report_monthly_customer_title') ?></th>
                            <th class="text-center"><?= _l('job_revenue_report_daily_package_title') ?></th>
                            <th class="text-center"><?= _l('job_revenue_report_monthly_start_title') ?></th>
                            <th class="text-center" ><?= _l('job_revenue_report_monthly_end_title') ?></th>
                            <th class="text-center"><?= _l('job_revenue_report_monthly_package_duration_title') ?></th>
                            <th class="text-center"><?= _l('job_revenue_report_monthly_total_amount_title') ?></th>
                            <!-- <th class="text-center"><?= _l('job_revenue_report_monthly_contract_duration_title') ?> (<span class="month-title"></span>)</th> -->
                            <th class="text-center"><?= _l('job_revenue_report_monthly_duration_title') ?> (<span class="month-title"></span>)</th>
                            <th class="text-center"><?= _l('job_revenue_report_monthly_amount_title') ?> (<span class="month-title"></span>)</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<script>
    window.addEventListener("DOMContentLoaded", (event) => {
        let params = {},
            detailParams = {};

        $.each($('#revenue_report_monthly ._filters input'), function() {
            params[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });

        var monthlyReportTable = initDataTable(
            '.table-monthly-revenue',
            admin_url + 'reports_revenue/monthly_report_table',
            [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            params,
            [], {
                searching: false,
                buttons: [{
                    text: '<i class="hide fa fa-refresh"></i>',
                    className: 'btn hide btn-default-dt-options btn-dt-reload',
                    action: function(e, dt, node, config) {
                        dt.ajax.reload();
                    }
                }],
                columnDefs: [
                    {
                        targets: [1, 6, 7, 8, 9, 10],
                        className: 'text-right',
                    },
                ],
            }
        );

        monthlyReportTable.on('draw.dt', function() {
            if (monthlyReportTable.rows().count()) {
                $('#revenue_report_monthly #exportReport').removeAttr('disabled');
            } else {
                $('#revenue_report_monthly #exportReport').attr('disabled', true);
            }
        });

        monthlyReportTable.on('xhr', function(e, settings, json) {
            const amounts = json?.amounts ?? {};
            for (const key in amounts) {
                let amount = amounts[key];
                let obj = document.getElementById(key);
                if (obj && amount > 0) {
                    animateValue(obj, 0, amount, 1000, true);
                } else {
                    $('#' + key).text(amount);
                }
            }
            $('#revenue_report_monthly div.loading-mask').addClass('hide');
        });

        monthlyReportTable.on('preXhr.dt', function() {
            $('#revenue_report_monthly div.loading-mask').removeClass('hide');
        });

        var selectedMonth = null;
        jSuites.calendar(document.getElementById('report_month'), {
            readonly: false,
            placeholder: 'Select issue month',
            type: 'year-month-picker',
            format: 'YYYY.mm',
            today: true,
            onupdate: function() {
                if (selectedMonth != $('#report_month').val()) {
                    selectedMonth = $('#report_month').val();
                    monthlyReportTable.ajax.reload();
                    $('#revenue_report_monthly .month-title').text(moment(selectedMonth, 'YYYY.MM').format('YYYY.MM'));
                    $('#revenue_report_monthly #invoice_amt_month').text(moment(selectedMonth, 'YYYY.MM').format('YYYY.MM'));
                    $('#revenue_report_monthly #revenue_amt_month').text(moment(selectedMonth, 'YYYY.MM').format('YYYY.MM'));
                }
            },
        });

        // jQuery AJAX request to fetch the Excel file
        $('#revenue_report_monthly #exportReport').click(function() {
            const btn = $(this);
            const reportMonth = $('#report_month').val();
            btn.attr('disabled', true);
            btn.find('i.fa-spinner').removeClass('hide');
            $.ajax({
                url: `${admin_url}reports_revenue/export_revenue_report_monthly/${reportMonth}`,
                type: 'GET',
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data, status, xhr) {
                    let blob = data,
                        link = document.createElement('a'),
                        url = window.URL.createObjectURL(blob);

                    // Set the download filename
                    link.href = url;
                    link.download = `job-posting-monthly-revenue-${reportMonth}.xlsx`;

                    // Trigger the download by simulating a click
                    link.click();

                    // Clean up the object URL after download
                    window.URL.revokeObjectURL(url);
                },
                error: function(xhr, status, error) {
                    alert_float('danger', status ?? 'Unknown error!');
                },
                complete: function() {
                    btn.attr('disabled', false);
                    btn.find('i.fa-spinner').addClass('hide');
                }
            });
        });

        $('.pagination-section').append($('#DataTables_Table_1_length').html());
        $('.refresh-icon').append($('#DataTables_Table_1_wrapper .dt-buttons.btn-group').html());
        $('.refresh-icon .btn.btn-dt-reload i').removeClass('hide');
        $('.refresh-icon .btn.btn-dt-reload')
            .removeClass('hide')
            .css('height', '36px')
            .on('click', function() {
            monthlyReportTable.ajax.reload();
        });
        $('#DataTables_Table_1_length').remove();
        $('.pagination-section select').on('change', function() {
            monthlyReportTable.page.len($(this).val()).draw();
        }).val(monthlyReportTable.page.len());
    });
</script>

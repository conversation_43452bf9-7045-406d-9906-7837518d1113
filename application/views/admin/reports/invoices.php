<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<style>
    .loading-mask {
        width: 100%;
        height: 100%;
        position: absolute;
        background-color: rgba(45, 62, 80, .4);
        color: white;
        z-index: 1;
        cursor: wait;
    }

    .loading-mask div {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        height: 20px;
    }
</style>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="panel_s">
                <div class="panel-body">
                    <div class="panel-title">
                        <h4><?= _l('als_reports_monthly_invoice_submenu') ?></h4>
                    </div>
                    <div class="col-md-12 no-padding">
                        <ul class="nav nav-tabs">
                            <li>
                                <a aria-controls="issue_date" role="tab" data-toggle="tab" id="tab_issue_date" href="#issue_date"><?php echo _l('monthly_invoice_report_tab_issue_date_title'); ?></a>
                            </li>
                            <li>
                                <a aria-controls="posted_date" role="tab" data-toggle="tab" id="tab_posted_date" href="#posted_date"><?php echo _l('monthly_invoice_report_tab_posted_date_title'); ?></a>
                            </li>
                            <li>
                                <a aria-controls="unpaid_po" role="tab" data-toggle="tab" id="tab_unpaid_po" href="#unpaid_po">Unpaid Invoice Report</a>
                            </li>
                            <?php if (defined('IS_PAYMENT_ENABLED') && true === IS_PAYMENT_ENABLED) : ?>
                            <li>
                                <a aria-controls="transactions" role="tab" data-toggle="tab" id="tab_transactions" href="#transactions"><?php echo _l('monthly_invoice_report_tab_transactions_title'); ?></a>
                            </li>
                            <?php endif; ?>
                        </ul>
                        <div class="tab-content">
                            <div id="issue_date" class="tab-pane fade">
                                <?php include_once(APPPATH . 'views/admin/reports/invoices_by_issue_date.php'); ?>
                            </div>
                            <div id="posted_date" class="tab-pane fade">
                                <?php include_once(APPPATH . 'views/admin/reports/invoices_by_posted_date.php'); ?>
                            </div>
                            <div id="unpaid_po" class="tab-pane fade">
                                <?php include_once(APPPATH . 'views/admin/reports/po_unpaid_report.php'); ?>
                            </div>
                            <?php if (defined('IS_PAYMENT_ENABLED') && true === IS_PAYMENT_ENABLED) : ?>
                            <div id="transactions" class="tab-pane fade">
                                <?php include_once(APPPATH . 'views/admin/reports/invoice_transactions.php'); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php init_tail(); ?>
<script src="/assets/plugins/jsuites/jsuites.js"></script>
<link rel="stylesheet" href="/assets/plugins/jsuites/jsuites.css" type="text/css" />

<script>
    window.addEventListener("DOMContentLoaded", (event) => {
        var validTabs = ['#tab_issue_date', '#tab_posted_date', '#tab_transactions', '#tab_unpaid_po'],
            tabId = (location.hash ?? '#issue_date').replace('#', '#tab_');

        tabId = validTabs.includes(tabId) ? tabId : '#tab_issue_date';
        $(tabId).tab('show');

        $('ul.nav-tabs li a').on('click', function () {
            location.href = this.href;
        });
    });
</script>

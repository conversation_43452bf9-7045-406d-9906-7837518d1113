<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="col-md-12 no-padding">
    <div id="revenue_report_daily">
        <div class="d-flex align-items-center justify-content-between">
            <div class="no-padding _filters d-flex" style="gap: 5px">
                <div class="no-padding">
                    <input type="text" class="form-control" placeholder="Select issue month" id="report_date" name="report_date" autocomplete="off">
                </div>
                <div class="refresh-icon-daily"></div>
            </div>
            <div class="no-paddingn d-flex">
                <button id="exportReport" type="button" disabled class="btn btn-info mright5 test pull-left display-block" style="height: 36px">
                    <i class="fa fa-spinner fa-spin hide" style="margin-right: 5px;"></i>
                    <?= _l('monthly_invoice_report_export_btn'); ?>
                </button>
                <div class="pagination-section-daily"></div>
            </div>
        </div>
        <div class="col-md-12 mtop10 no-padding relative">
            <div class="loading-mask text-center hide">
                <div><?php echo _l('customer_statistic_please_wailt') ?></div>
            </div>
            <div class="flex-grow-1 mtop10 d-flex no-padding">
                <div class="flex-grow-1 d-flex padding-5 mright5" style="background-color: #ECF2FF; border-radius: 10px;">
                    <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                        <div class="panel-body no-border no-border-color">
                            <h3 class="text-default _total">
                                Revennue Total Amount (No tax) 
                                <!-- <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_quantity_tooltip') ?>"></i></a> -->
                            </h3>
                        </div>
                        <div class="panel-body no-border no-border-color">
                            <h3 id="invoice_total" class="_total">0</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 no-padding mtop10">
            <?php render_datatable([
                _l('job_revenue_report_daily_po_no_title'),
                _l('job_revenue_report_daily_invoice_no_title'),
                'Issue Date',
                _l('job_revenue_report_daily_package_title'),
                _l('job_revenue_report_daily_date_title'),
                _l('job_revenue_report_daily_revenue_title'),
            ], 'daily-revenue'); ?>
        </div>
    </div>
</div>
<script>
    window.addEventListener("DOMContentLoaded", (event) => {
        let params = {},
            detailParams = {};

        $.each($('#revenue_report_daily ._filters input'), function() {
            params[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });

        var dailyReportTable = initDataTable(
            '.table-daily-revenue',
            admin_url + 'reports_revenue/daily_report_table',
            [0, 1, 2, 3, 4, 5],
            [0, 1, 2, 3, 4, 5],
            params,
            [], {
                searching: false,
                buttons: [{
                    text: '<i class="hide fa fa-refresh"></i>',
                    className: 'hide btn btn-default-dt-options btn-dt-reload',
                    action: function(e, dt, node, config) {
                        dt.ajax.reload();
                    }
                }]
            }
        );

        dailyReportTable.on('draw.dt', function() {
            if (dailyReportTable.rows().count()) {
                $('#revenue_report_daily #exportReport').removeAttr('disabled');
            } else {
                $('#revenue_report_daily #exportReport').attr('disabled', true);
            }
        });

        dailyReportTable.on('xhr', function(e, settings, json) {
            const amounts = json?.amounts ?? {};
            for (const key in amounts) {
                let amount = amounts[key];
                let obj = document.getElementById(key);
                if (obj && amount > 0) {
                    animateValue(obj, 0, amount, 1000, true);
                } else {
                    $('#' + key).text(amount);
                }
            }
            $('#revenue_report_daily div.loading-mask').addClass('hide');
        });

        dailyReportTable.on('preXhr.dt', function() {
            $('#revenue_report_daily div.loading-mask').removeClass('hide');
        });

        var selectReportDay = null;
        jSuites.calendar(document.getElementById('report_date'), {
            readonly: false,
            placeholder: 'Select job\'s posting date',
            format: 'YYYY.mm.dd',
            today: true,
            onupdate: function() {
                if (selectReportDay != $('#report_date').val()) {
                    selectReportDay = $('#report_date').val();
                    dailyReportTable.ajax.reload();
                }
            },
        });

        // jQuery AJAX request to fetch the Excel file
        $('#revenue_report_daily #exportReport').click(function() {
            const btn = $(this);
            const reportDate = $('#report_date').val();
            btn.attr('disabled', true);
            btn.find('i.fa-spinner').removeClass('hide');
            $.ajax({
                url: `${admin_url}reports_revenue/export_revenue_report_daily/${reportDate}`,
                type: 'GET',
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data, status, xhr) {
                    let blob = data,
                        link = document.createElement('a'),
                        url = window.URL.createObjectURL(blob);

                    // Set the download filename
                    link.href = url;
                    link.download = `job-posting-daily-revenue-${reportDate}.xlsx`;

                    // Trigger the download by simulating a click
                    link.click();

                    // Clean up the object URL after download
                    window.URL.revokeObjectURL(url);
                },
                error: function(xhr, status, error) {
                    alert_float('danger', status ?? 'Unknown error!');
                },
                complete: function() {
                    btn.attr('disabled', false);
                    btn.find('i.fa-spinner').addClass('hide');
                }
            });
        });

        $('.pagination-section-daily').append($('#DataTables_Table_0_length').html());
        $('.refresh-icon-daily').append($('#DataTables_Table_0_wrapper .dt-buttons.btn-group').html());
        $('.refresh-icon-daily .btn.btn-dt-reload i').removeClass('hide');
        $('.refresh-icon-daily .btn.btn-dt-reload')
        .removeClass('hide')
        .css('height', '36px')
        .on('click', function() {
            dailyReportTable.ajax.reload();
        });
        $('#DataTables_Table_0_length').remove();
        $('.pagination-section-daily select').on('change', function() {
            dailyReportTable.page.len($(this).val()).draw();
        }).val(dailyReportTable.page.len());
    });
</script>

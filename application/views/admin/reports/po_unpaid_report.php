<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="col-md-12 no-padding">
    <div id="po_unpaid_report">
        <div class="col-md-12 no-padding">
            <button id="exportReport" type="button" disabled class="btn btn-info mright5 test pull-left display-block">
                <i class="fa fa-spinner fa-spin hide" style="margin-right: 5px;"></i>
                <?= _l('monthly_invoice_report_export_btn'); ?>
            </button>
        </div>
        <div class="col-md-2 no-padding mtop10 _filters">
            <input type="text" class="form-control" placeholder="Select created month" id="po_created_month" name="po_created_month" autocomplete="off">
        </div>
        <div class="col-md-12 mtop10 no-padding relative">
            <div class="loading-mask text-center hide">
                <div><?php echo _l('customer_statistic_please_wailt') ?></div>
            </div>
            <div class="col-md-12 mtop10 d-flex no-padding">
                <div class="flex-grow-1 d-flex padding-5 mright5" style="background-color: #ECF2FF; border-radius: 10px;">
                    <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                        <div class="panel-body no-border no-border-color">
                            <h3 class="text-default _total">
                                <?= _l('monthly_invoice_report_detail_quantity_title') ?>
                                <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_quantity_tooltip') ?>"></i></a>
                            </h3>
                        </div>
                        <div class="panel-body no-border no-border-color">
                            <h3 id="po_quantity" class="_total">0</h3>
                        </div>
                    </div>
                    <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                        <div class="panel-body no-border no-border-color">
                            <h3 class="text-default _total">
                                <?= _l('monthly_invoice_report_detail_amount_title') ?>
                                <a href="javascript:;"><i class="fa fa-question-circle" data-toggle="tooltip" data-title="<?= _l('po_monthly_report_invoice_amount_tooltip') ?>"></i></a>
                            </h3>
                        </div>
                        <div class="panel-body no-border no-border-color">
                            <h3 id="po_amount" class="_total">0</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 no-padding mtop10">
            <h4 class="bold">Unpaid PO Report</h4>
            <?php render_datatable([
                'Issue Date',
                'Invoice #',
                'Client',
                'Status',
                'Amount',
            ], 'statistic-by-unpaid-po'); ?>
        </div>
    </div>
</div>
<script>
    window.addEventListener("DOMContentLoaded", (event) => {
        let params = {},
            detailParams = {};

        $.each($('#po_unpaid_report ._filters input'), function() {
            params[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });

        var invoiceSummaryTable = initDataTable(
            '.table-statistic-by-unpaid-po',
            admin_url + 'reports/invoice_report_by_unpaid',
            [0, 1, 2, 3, 4],
            [0, 1, 2, 3, 4],
            params,
            [], {
                searching: false,
                buttons: [{
                    text: '<i class="fa fa-refresh"></i>',
                    className: 'btn btn-default-dt-options btn-dt-reload',
                    action: function(e, dt, node, config) {
                        dt.ajax.reload();
                    }
                }]
            }
        );

        invoiceSummaryTable.on('draw.dt', function() {
            if (invoiceSummaryTable.rows().count()) {
                $('#po_unpaid_report #exportReport').removeAttr('disabled');
            } else {
                $('#po_unpaid_report #exportReport').attr('disabled', true);
            }
            $('#po_unpaid_report input#issue_month_detail').val($('#issue_month').val());
            $('#po_unpaid_report input#report_month_detail').val('');
            $('#po_unpaid_report input#advanced_amount').val(0);
        });

        invoiceSummaryTable.on('xhr', function(e, settings, json) {
            const amounts = json?.amount ?? {};
            for (const key in amounts) {
                let amount = parseFloat(amounts[key] ?? 0);
                let obj = document.getElementById(key);
                if (obj && amount > 0) {
                    animateValue(obj, 0, amount, 1000, true);
                } else {
                    $('#' + key).text(amount);
                }
            }
            $('#po_unpaid_report div.loading-mask').addClass('hide');
        });

        invoiceSummaryTable.on('preXhr.dt', function() {
            $('#po_unpaid_report div.loading-mask').removeClass('hide');
        });

        let selectedMonth = null;
        jSuites.calendar(document.getElementById('po_created_month'), {
            readonly: false,
            placeholder: 'Select issue month',
            type: 'year-month-picker',
            format: 'YYYY.mm',
            today: true,
            onupdate: function() {
                if (selectedMonth != $('#po_created_month').val()) {
                    selectedMonth = $('#po_created_month').val();
                    invoiceSummaryTable.ajax.reload();
                }
            }
        });

        // jQuery AJAX request to fetch the Excel file
        $('#po_unpaid_report #exportReport').click(function() {
            const btn = $(this);
            const createdMonth = $('#po_created_month').val();
            btn.attr('disabled', true);
            btn.find('i.fa-spinner').removeClass('hide');
            $.ajax({
                url: `${admin_url}reports/export_invoice_report_by_unpaid/${createdMonth}`,
                type: 'GET',
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data, status, xhr) {
                    let blob = data,
                        link = document.createElement('a'),
                        url = window.URL.createObjectURL(blob);

                    // Set the download filename
                    link.href = url;
                    link.download = `purchase-order-unpaid-${createdMonth}.xlsx`;

                    // Trigger the download by simulating a click
                    link.click();

                    // Clean up the object URL after download
                    window.URL.revokeObjectURL(url);
                },
                error: function(xhr, status, error) {
                    alert_float('danger', status ?? 'Unknown error!');
                },
                complete: function() {
                    btn.attr('disabled', false);
                    btn.find('i.fa-spinner').addClass('hide');
                }
            });
        });
    });
</script>

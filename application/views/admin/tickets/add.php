<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
	<div class="content">
		<?php echo form_open_multipart($this->uri->uri_string(), array('id' => 'new_ticket_form')); ?>
		<div class="row">
			<div class="col-md-12">
				<div class="panel_s">
					<div class="panel-body">
						<div class="row">
							<div class="col-md-6">
								<?php if (!isset($project_id) && !isset($contact)) { ?>
									<a href="#" id="ticket_no_contact"><span class="label label-default">
											<i class="fa fa-envelope"></i> <?php echo _l('ticket_create_no_contact'); ?>
										</span>
									</a>
									<a href="#" class="hide" id="ticket_to_contact"><span class="label label-default">
											<i class="fa fa-user-o"></i> <?php echo _l('ticket_create_to_contact'); ?>
										</span>
									</a>
									<div class="mbot15"></div>
								<?php } ?>
								<?php echo render_input('subject', 'ticket_settings_subject', '', 'text', array('required' => 'true')); ?>

								<label for="note_description" class="control-label"><?php echo _l('note_description'); ?></label>
								<textarea name="note_description" class="form-control mbot10" rows="4"></textarea>

								<div class="form-group select-placeholder" id="ticket_contact_w">
									<label for="clientid" class="control-label"><?php echo _l('ticket_select_customer'); ?></label>
									<select id="clientid" name="userid" data-live-search="true" data-width="100%" class="ajax-search" data-width="100%" data-live-search="true" data-none-selected-text="<?php echo _l('dropdown_non_selected_tex'); ?>">
										<?php if (isset($contact)) { ?>
											<option value="<?php echo $contact['id']; ?>" selected><?php echo $contact['firstname'] . ' ' . $contact['lastname']; ?></option>
										<?php } ?>
										<option value=""></option>
									</select>
								</div>
								<div class="row">
									<div class="col-md-6">
										<?php echo render_select('department', $departments, array('departmentid', 'name'), 'ticket_settings_departments', (count($departments) == 1) ? $departments[0]['departmentid'] : '', array('required' => 'true')); ?>
									</div>
									<div class="col-md-6">
										<?php echo render_input('cc', 'CC'); ?>
									</div>
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label for="tags" class="control-label"><i class="fa fa-tag" aria-hidden="true"></i> <?php echo _l('tags'); ?></label>
									<input type="text" class="tagsinput" id="tags" name="tags" data-role="tagsinput">
								</div>

								<div class="form-group select-placeholder">
									<label for="assigned" class="control-label">
										<?php echo _l('ticket_settings_assign_to'); ?>
									</label>
									<select name="assigned" id="assigned" class="form-control selectpicker" data-live-search="true" data-none-selected-text="<?php echo _l('dropdown_non_selected_tex'); ?>" data-width="100%">
										<option value=""><?php echo _l('ticket_settings_none_assigned'); ?></option>
										<?php foreach ($staff as $member) { ?>
											<option value="<?php echo $member['staffid']; ?>" <?php if ($member['staffid'] == $cs_team_id ?? null) {
																									echo 'selected';
																								} ?>>
												<?php echo $member['firstname'] . ' ' . $member['lastname']; ?>
											</option>
										<?php } ?>
									</select>
								</div>

								<div class="row">
									<?php if (get_option('services') == 1) { ?>
										<div class="col-md-4">
											<?php if (is_admin() || get_option('staff_members_create_inline_ticket_services') == '1') {
												echo render_select_with_input_group('service', $services, array('serviceid', 'name'), 'ticket_settings_service', '', '<a href="#" onclick="new_service();return false;"><i class="fa fa-plus"></i></a>', ['onchange' => "reloadLevel1(this)"]);
											} else {
												echo render_select('service', $services, array('serviceid', 'name'), 'ticket_settings_service', '', ['onchange' => "reloadLevel1(this)"]);
											}
											?>
										</div>
									<?php } ?>
									<div class="col-md-4">
										<?= render_select('first_level_id', [], [], 'level_1', '', ['onchange' => "reloadLevel2(this)"]); ?>
									</div>
									<div class="col-md-4">
										<?= render_select('second_level_id', [], [], 'level_2', '', ['onchange' => "get_priority(this)"]); ?>

									</div>
								</div>

								<div class="row">
									<div class="col-md-12">
										<?= render_select('priority', [], [], 'priority', '', []); ?>
									</div>
								</div>



								<div class="form-group projects-wrapper hide">
									<label for="project_id"><?php echo _l('project'); ?></label>
									<div id="project_ajax_search_wrapper">
										<select name="project_id" id="project_id" class="projects ajax-search" data-live-search="true" data-width="100%" data-none-selected-text="<?php echo _l('dropdown_non_selected_tex'); ?>" <?php if (isset($project_id)) { ?> data-auto-project="true" data-project-userid="<?php echo $userid; ?>" <?php } ?>>
											<?php if (isset($project_id)) { ?>
												<option value="<?php echo $project_id; ?>" selected><?php echo '#' . $project_id . ' - ' . get_project_name_by_id($project_id); ?></option>
											<?php } ?>
										</select>
									</div>
								</div>
							</div>

							<div class="col-md-12 job_id">
								<?= render_select('job_id[]', [], [], 'job_id', '', ['multiple' => true, 'data-tick-icon' => 'glyphicon-check']); ?>
							</div>

							<div class="col-md-12">
								<?php echo render_custom_fields('tickets'); ?>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<div class="panel_s">
							<div class="panel-heading">
								<?php echo _l('ticket_add_body'); ?>
							</div>
							<div class="panel-body">
								<div class="btn-bottom-toolbar text-right">
									<button type="submit" data-form="#new_ticket_form" autocomplete="off" data-loading-text="<?php echo _l('wait_text'); ?>" class="btn btn-info"><?php echo _l('open_ticket'); ?></button>
								</div>
								<div class="clearfix"></div>
								<?php echo render_textarea('message', '', '', array(), array(), '', 'tinymce'); ?>
							</div>
							<div class="panel-footer attachments_area">
								<div class="row attachments">
									<div class="attachment">
										<div class="col-md-4 col-md-offset-4 mbot15">
											<div class="form-group">
												<label for="attachment" class="control-label"><?php echo _l('ticket_add_attachments'); ?></label>
												<div class="input-group">
													<input type="file" extension="<?php echo str_replace(['.', ' '], '', get_option('ticket_attachments_file_extensions')); ?>" filesize="<?php echo file_upload_max_size(); ?>" class="form-control" name="attachments[0]" accept="<?php echo get_ticket_form_accepted_mimes(); ?>">
													<span class="input-group-btn">
														<button class="btn btn-success add_more_attachments p8-half" data-max="<?php echo get_option('maximum_allowed_ticket_attachments'); ?>" type="button"><i class="fa fa-plus"></i></button>
													</span>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<?php echo form_close(); ?>
		</div>
	</div>
	<style>
		

		.job_id .bootstrap-select.show-tick .dropdown-menu a:not(a.option-all) span.check-mark {
			position: relative;
			display: inline-block;
			top: 2px;
			right: 15px;
			margin-left: 15px;
		}

		.job_id .bootstrap-select.show-tick .dropdown-menu a:not(a.option-all) span.check-mark:before {
			content: "\e157";
		}
		.job_id .bootstrap-select.show-tick .dropdown-menu .selected a:not(a.option-all) span.check-mark:before {
			content: "\e067";
		}

		.job_id .bootstrap-select.show-tick .dropdown-menu a:not(a.option-all) span.text {
			margin-left: -10px;
		}
	</style>
	<?php $this->load->view('admin/tickets/services/service'); ?>
	<?php init_tail(); ?>
	<?php hooks()->do_action('new_ticket_admin_page_loaded'); ?>
	<script>
		$(function() {

			get_job_company(0);

			$('#clientid').change(function() {
				var company_id = $(this).val();
				get_job_company(company_id);
			});

			function get_job_company(company_id) {
				$.ajax({
					type: 'post',
					dataType: "json",
					url: admin_url + 'tickets/get_job_company',
					data: {
						company_id: company_id,
					},
					success: function(res) {
						if (res) {
							var job_id = $('.job_id select');
							job_id.find('option').remove().end();
							job_id.append('<option class="option-all" value="0">-- <?php echo _l('select_job_company') ?> --</option>');
							res.forEach(function(job) {
								job_id.append('<option value="' + job.id + '">' + `${job.id} - ${job.title}` + '</option>');
							});
							job_id.selectpicker('refresh');
						}
					}
				});
			}

			init_ajax_search('contact', '#contactid.ajax-search', {
				tickets_contacts: true,
				contact_userid: function() {
					// when ticket is directly linked to project only search project client id contacts
					var uid = $('select[data-auto-project="true"]').attr('data-project-userid');
					if (uid) {
						return uid;
					} else {
						return '';
					}
				}
			});

			validate_new_ticket_form();

			<?php if (isset($project_id) || isset($contact)) { ?>
				$('body.ticket select[name="contactid"]').change();
			<?php } ?>

			<?php if (isset($project_id)) { ?>
				$('body').on('selected.cleared.ajax.bootstrap.select', 'select[data-auto-project="true"]', function(e) {
					$('input[name="userid"]').val('');
					$(this).parents('.projects-wrapper').addClass('hide');
					$(this).prop('disabled', false);
					$(this).removeAttr('data-auto-project');
					$('body.ticket select[name="contactid"]').change();
				});
			<?php } ?>
		});

		function reloadLevel1(a) {
			var value = $(a).val();

			var level2 = $('#second_level_id');
			// Clear all options before fetching new once
			level2.find('option').remove().end();
			level2.selectpicker('refresh');

			var priority = $('#priority');
			// Clear all options before fetching new once
			priority.find('option').remove().end();
			priority.selectpicker('refresh');

			var locationSelect = $('#first_level_id');
			// Clear all options before fetching new once
			locationSelect.find('option').remove().end();
			$.ajax({
				type: 'post',
				dataType: "json",
				url: admin_url + 'tickets/get_service_level1',
				data: {
					service_id: value,
				},
				success: function(res) {
					if (res) {
						locationSelect.append('<option value="0">-- <?php echo _l('select_c1') ?> --</option>');
						res.forEach(function(location) {
							locationSelect.append('<option value="' + location.id + '">' + location.name + '</option>');
						});
						locationSelect.selectpicker('refresh');
					}

				}
			});
		}

		function reloadLevel2(a) {
			var value = $(a).val();

			var priority = $('#priority');
			// Clear all options before fetching new once
			priority.find('option').remove().end();

			var locationSelect = $('#second_level_id');
			// Clear all options before fetching new once
			locationSelect.find('option').remove().end();
			$.ajax({
				type: 'post',
				dataType: "json",
				url: admin_url + 'tickets/get_service_level2',
				data: {
					level1_id: value,
				},
				success: function(res) {
					if (res.data) {
						locationSelect.append('<option value="0">-- <?php echo _l('select_c2') ?> --</option>')
						res.data.forEach(function(location) {
							locationSelect.append('<option value="' + location.id + '">' + location.name + '</option>')
						});
						locationSelect.selectpicker('refresh');
					} else {
						locationSelect.append('<option value="0">-- <?php echo _l('select_c2') ?> --</option>')
						locationSelect.selectpicker('refresh');
					}
					if (res.priority_level1_id) {
						priority.append('<option value="' + res.priority_level1_id + '">' + res.priority_level1 + '</option>');
						priority.selectpicker('refresh');
					} else {
						priority.append('<option value="0">-- <?php echo _l('select_ut') ?> --</option>')
						priority.selectpicker('refresh');
					}

				}
			});
		}

		function get_priority(a) {
			var value = $(a).val();
			var locationSelect = $('#priority');
			// Clear all options before fetching new once
			locationSelect.find('option').remove().end();
			$.ajax({
				type: 'post',
				dataType: "json",
				url: admin_url + 'tickets/get_service_priority',
				data: {
					level2_id: value,
				},
				success: function(res) {
					if (res.value) {
						locationSelect.append('<option value="' + res.value + '">' + res.text + '</option>')
						locationSelect.selectpicker('refresh');
					} else {
						locationSelect.append('<option value="0">-- <?php echo _l('select_ut') ?> --</option>')
						locationSelect.selectpicker('refresh');
					}

				}
			});
		}
	</script>
	</body>

	</html>
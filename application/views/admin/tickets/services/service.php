<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="modal fade" id="ticket-service-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">
                    <span class="service-title"></span>
                </h4>
            </div>
            <div class="modal-body">
               
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                <button type="button" class="btn btn-info level_submit"><?php echo _l('submit'); ?></button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<style>
    .modal-dialog {
        width: 1000px;
    }
</style>
<script>
    
    function new_service(service_id = 0){
        if(service_id){
            $('.service-title').html('<?php echo  _l('ticket_service_edit');?>');
        }else{
            $('.service-title').html('<?php echo  _l('new_service');?>');
        }
        
        // Load thông tin modal-body
        $.ajax({
				type: 'post',
				dataType: "html",
				url: site_url + 'admin/tickets/service_add',
				data: {
					service_id: service_id,
				},
				success: function(res) {
					$('#ticket-service-modal .modal-body').html(res);
                    $('#ticket-service-modal').modal('show');
				}
			});
    }
    
</script>

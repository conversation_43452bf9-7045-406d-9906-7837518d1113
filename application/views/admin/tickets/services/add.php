		<div id="service_submit">
			<input type="hidden" name="service_id" value="<?php echo $service['serviceid'] ?>">
			<div class="row">
				<div class="col-md-12 group_bold_lable">
					<div class="form-group" app-field-wrapper="name"><small class="req text-danger">* </small><label for="name" class="control-label"><?php echo _l('services_dt_name') ?></label><input type="text" id="name" name="name" class="form-control" placeholder="<?php echo _l('services_dt_name_holder') ?>" autocomplete="off" value="<?php echo $service['name'] ?>"></div>
				</div>
			</div>

	<div class="row">
		<div class="col-md-12">
			<div class="item_title_level1"><b class="level_service1"><?php echo _l('level_service1'); ?></b><b class="result_name_service"></b></div>
			<div class="des_item_title_level1"><?php echo _l('des_item_title_level1'); ?></div>

			<div class="result_level1">
				<?php
				if (isset($service['data'])) {
					foreach ($service['data'] as $level1) {
				?>
						<div class="content_level1">
							<div class="row item_row1">
								<i onclick="dropdown_sub(this)" class="dropdown_sub fa fa-angle-right" aria-hidden="true"></i>
								<div class="col-md-4">
									<div class="item_name_level1">
										<div class="item_name_level1_left">
											<i class="fa fa-folder-o" aria-hidden="true"></i>
										</div>
										<div class="item_name_level1_right">
											<input type="text" name="name_level1" class="form-control" placeholder="<?php echo _l('level_1_holder') ?>" autocomplete="off" value="<?php echo $level1['name'] ?>">
										</div>
									</div>
								</div>
								<div class="col-md-4">
									<div class="item_pior_level1">
										<select name="pior1" class="form-control">
											<option value="0">-- <?php echo _l('select_priority_level') ?> --</option>
											<?php
											foreach ($array_pior as $pior) {
												$selected = ($pior['priorityid'] == $level1['priority']) ? 'selected=selected' : '';
												echo '<option ' . $selected . ' value="' . $pior['priorityid'] . '">' . $pior['name'] . '</option>';
											}
											?>
										</select>
									</div>
								</div>
								<div class="col-md-4">
									<div class="item_time_level1">
										<select name="time1" class="form-control">
											<option value="0">-- <?php echo _l('select_expected_completion_time') ?> --</option>
											<?php
											foreach ($arr_expected_completion as $completion) {
												$selected = ($completion['value'] == $level1['completion']) ? 'selected=selected' : '';
												echo '<option ' . $selected . ' value="' . $completion['value'] . '">' . $completion['text'] . '</option>';
											}
											?>
										</select>
									</div>
								</div>
								<input type="hidden" name="level1_id" value="<?php echo $level1['id'] ?>">
								<i onclick="remove_level1(this, <?php echo $level1['id'] ?>)" class="fa fa-minus-circle" aria-hidden="true"></i>
							</div>
							<div class="content_level2">
								<div class="item_content_level2" style="display: none;">
									<div class="result_level2">
										<?php
										if (isset($level1['level2']) && $level1['level2']) {
											foreach ($level1['level2'] as $level2) {
										?>
												<div class="row">
													<div class="col-md-4">
														<div class="item_name_level2">
															<div class="item_name_level1_left">
																<i class="fa fa-folder-open-o" aria-hidden="true"></i>
															</div>
															<div class="item_name_level1_right">
																<input type="text" name="name_level2" class="form-control" placeholder="<?php echo _l('level_2_holder') ?>" autocomplete="off" value="<?php echo $level2['name'] ?>">
															</div>
														</div>
													</div>
													<div class="col-md-4">
														<div class="item_pior_level2">
															<select name="pior2" class="form-control">
																<option value="0">-- <?php echo _l('select_priority_level') ?> --</option>
																<?php
																foreach ($array_pior as $pior) {
																	$selected = ($pior['priorityid'] == $level2['priority']) ? 'selected=selected' : '';
																	echo '<option ' . $selected . ' value="' . $pior['priorityid'] . '">' . $pior['name'] . '</option>';
																}
																?>
															</select>
														</div>
													</div>
													<div class="col-md-4">
														<div class="item_time_level2">
															<select name="time2" class="form-control">
																<option value="0">-- <?php echo _l('select_expected_completion_time') ?> --</option>
																<?php
																foreach ($arr_expected_completion as $completion) {
																	$selected = ($completion['value'] == $level2['completion']) ? 'selected=selected' : '';
																	echo '<option ' . $selected . ' value="' . $completion['value'] . '">' . $completion['text'] . '</option>';
																}
																?>
															</select>
														</div>
													</div>
													<input type="hidden" name="level2_id" value="<?php echo $level2['id'] ?>">
													<i onclick="remove_level2(this, <?php echo $level2['id'] ?>)" class="fa fa-minus-circle" aria-hidden="true"></i>
												</div>
										<?php
											}
										}
										?>
									</div>
									<div class="button_add_level2">
										<span onclick="button_add_level2(this)">
											<i class="fa fa-plus-circle" aria-hidden="true"></i> <?php echo _l('button_add_level2'); ?> </span>
									</div>
								</div>
							</div>
						</div>
				<?php

					}
				}
				?>
			</div>

			<div class="button_add_level1"><span><i class="fa fa-plus-circle" aria-hidden="true"></i> <?php echo _l('button_add_level1'); ?></span></div>

		</div>
	</div>
</div>

<script>
	// Button add level 1
	var pior_select = '<?php echo $pior_select ?>';
	$('.button_add_level1 span').click(function() {
		$('.result_level1').append(pior_select);
	});

	// Button add level 2
	var expected_completion_select = '<?php echo $expected_completion_select ?>';

	function button_add_level2(a) {
		$(a).parent().parent().find('.result_level2').append(expected_completion_select);
	}

	// Dropdown icon hide, show sub menu
	function dropdown_sub(a) {
		var parent = $(a).parent().parent();
		var htmlString = parent.html();
		var classExists = htmlString.includes('fa-angle-down');

		if (classExists) {
			$(a).removeClass('fa-angle-down');
			$(a).addClass('fa-angle-right');
			parent.find('.item_content_level2').hide();
		} else {
			$(a).removeClass('fa-angle-right');
			$(a).addClass('fa-angle-down');
			parent.find('.item_content_level2').show();
		}
	}

	// Remove level 1
	function remove_level1(a, id) {
		// Kiểm tra được quyền xóa level 1 không
		var permission = count_children_level1(a);
		if (!permission || id > 0) {
			// Hỏi có muốn xóa hay không?
			var r = confirm(app.lang.confirm_action_prompt);
			if (r == true) {
				action_remove_level1(a, id);
			}
		} else {
			action_remove_level1(a, id);
		}
	}

	// Remove level 1 on database
	var arr_remove_level1 = [];

	// Remove level 2 on database
	var arr_remove_level2 = [];

	// Kiểm tra quyền xóa level 1
	function count_children_level1(a) {
		// Đếm danh sách con
		var count_child = $(a).parent().parent().find('.content_level2 .row').length;
		if (count_child > 0) {
			return false;
		}
		return true;
	}

	// Xóa level 1
	function action_remove_level1(a, id) {
		// Đưa id xóa vào mảng arr_remove_level1
		if (id > 0)
			arr_remove_level1.push(id);

		$(a).parent().parent().remove();
	}

	// Remove level 2
	function remove_level2(a, id) {
		// Kiểm tra được quyền xóa level 2 không
		if (id > 0) {
			// Hỏi có muốn xóa hay không?
			var r = confirm(app.lang.confirm_action_prompt);
			if (r == true) {
				action_remove_level2(a, id);
			}
		} else {
			action_remove_level2(a, id);
		}
	}

	// Xóa level 2
	function action_remove_level2(a, id) {
		// Đưa id xóa vào mảng arr_remove_level2
		if (id > 0)
			arr_remove_level2.push(id);

		$(a).parent().remove();
	}

	// flag prevent double click
	var onclick_flag = false;

	$('.level_submit').click(function(event) {

		if (onclick_flag)
			return false;


		$('#service_submit').find('.error_input_kpi').removeClass('error_input_kpi');
		var flag = true;

		var service_id = $('input[name=service_id]').val();
		// Tên dịch vụ
		var name = $('input[name=name]').val();
		if (name == '') {
			alert_float('danger', '<?php echo _l('name_service_null'); ?>');
			$('input[name=name]').addClass('error_input_kpi');
			return false;
		}

		// Level all
		var data = [];
		var data_check = [];
		$('.result_level1 .content_level1').map(function() {

			var level2 = [];
			var level2_check = [];

			$(this).find('.result_level2 .row').map(function() {
				var name_level2 = $(this).find('input[name=name_level2]').val()
				var pior2 = $(this).find('select[name=pior2]').val()
				var time2 = $(this).find('select[name=time2]').val()
				var level2_id = $(this).find('input[name=level2_id]').val()

				level2.push({
					'id': level2_id,
					'name_level2': name_level2,
					'pior2': pior2,
					'time2': time2,
				});

				level2_check.push({
					'name_level2': name_level2,
					'pior2': pior2,
					'time2': time2,
					'this': $(this)
				});
			});

			var name_level1 = $(this).find('input[name=name_level1]').val()
			var pior1 = $(this).find('select[name=pior1]').val()
			var time1 = $(this).find('select[name=time1]').val()
			var level1_id = $(this).find('input[name=level1_id]').val()

			var level1 = {
				'id': level1_id,
				'name_level1': name_level1,
				'pior1': pior1,
				'time1': time1,
				'level2': level2,
			}

			var level1_check = {
				'name_level1': name_level1,
				'pior1': pior1,
				'time1': time1,
				'level2': level2_check,
				'this': $(this)
			}

			data.push(level1);
			data_check.push(level1_check);

		});

		// Kiểm tra thời gian, thời lượng day off
		data_check.forEach(function(value, index) {
			// Check name service level 1
			if (value.name_level1 == '') {
				value.this.find('input[name=name_level1]').addClass(
					'error_input_kpi');
				alert_float('danger', '<?php echo _l('error_name_level1'); ?>');
				flag = false;
			}

			value.level2.forEach(function(level2, index) {
				// Kiểm tra thời gian không được để trống
				if (level2.name_level2 == '') {
					level2.this.find('input[name=name_level2]').addClass(
						'error_input_kpi');
					alert_float('danger', '<?php echo _l('error_name_level2'); ?>');
					flag = false;
				}

			})

		})

		if (flag) {
			onclick_flag = true;
			$.ajax({
				type: 'post',
				dataType: "json",
				url: admin_url + 'tickets/service_submit',
				data: {
					service_id: service_id,
					name: name,
					data: data,
					arr_remove_level1: arr_remove_level1,
					arr_remove_level2: arr_remove_level2,
				},
				success: function(res) {
					if (res.error == 1) {
						alert_float('danger', res.message);
					} else {
						alert_float('success', res.message);
						const myTimeout = setTimeout(function() {
							location.reload();
						}, 3000);
					}
				}
			});
		}

	})
</script>
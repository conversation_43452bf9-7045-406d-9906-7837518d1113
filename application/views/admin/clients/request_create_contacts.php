<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
   <div class="content">
      <div class="row">
         <div class="col-md-12">
            <div class="panel_s">
               <div class="panel-body">
                  <div class="row _filters">
                     <div class="col-md-2">
                        <div class="form-group">
                           <select class="form-control selectpicker" name="review_status" id="review_status" onchange="reloadData()">
                              <option></option>
                              <?php foreach (CONTACT_REVIEW_STATUS_OPTIONS as $value) { ?>
                                 <option value=<?= $value['value'] ?>><?= $value['text'] ?></option>
                              <?php } ?>
                           </select>
                        </div>
                     </div>
                  </div>
                  <div class="clearfix"></div>
                  <?php
                  $table_data = [
                     [
                        'name' => _l('clients'),
                        'th_attrs' =>  [
                           'style' => 'width: 20%'
                        ]
                     ],
                     _l('clients_list_full_name'),
                     _l('contact_position'),
                     _l('contact_information'),
                     _l('created_by'),
                     _l('created_at'),
                     _l('review_status'),
                  ];

                  render_datatable($table_data, 'request-create-contacts');
                  ?>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
<?php init_tail(); ?>
<?php $this->load->view('admin/clients/client_js'); ?>
<script>
   let params = {};

   $.each($('._filters select'), function() {
      params[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
   });

   const data = initDataTable(
      '.table-request-create-contacts',
      window.location.href,
      [],
      [],
      params,
      [0, 'desc']
   );

   function reloadData() {
      data.ajax.reload();
   }
</script>
</body>

</html>
<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
   <div class="content">
      <div class="row">
         <div class="col-md-12">
            <div class="panel_s">
               <div class="panel-body">
                  <?php echo $this->import->downloadSampleFormHtml(); ?>
                  <?php echo $this->import->maxInputVarsWarningHtml(); ?>
                  <h3><?= _l('import_num_of_usage_behavior_title') ?></h3>

                  <?php if (!$this->import->isSimulation()) { ?>
                     <?php echo $this->import->importGuidelinesInfoHtml(); ?>
                     <?php echo $this->import->createSampleTableHtml(); ?>
                  <?php } else { ?>
                     <?php echo $this->import->simulationDataInfo(); ?>
                     <?php echo $this->import->createSampleTableHtml(true); ?>
                  <?php } ?>
                  <div class="row">
                     <div class="col-md-4 mtop15">
                        <?php echo form_open_multipart($this->uri->uri_string(), array('id' => 'import_form')); ?>
                        <?php echo form_hidden('clients_import', 'true'); ?>
                        <?php echo render_input('file_csv', 'choose_csv_file', '', 'file'); ?>
                        <div class="form-group">
                           <button type="button" class="btn btn-info import btn-import-submit"><?php echo _l('import'); ?></button>
                           <button type="button" class="btn btn-info simulate btn-import-submit"><?php echo _l('simulate_import'); ?></button>
                        </div>
                        <?php echo form_close(); ?>
                     </div>
                  </div>
                  <div class="mtop15">
                     <table class="table dt-table" data-order-col="2" data-order-type="desc">
                        <thead>
                           <tr>
                              <th width="50%">
                                 <?php echo _l('utility_activity_log_import_history'); ?>
                              </th>
                              <th width="50%">
                                 <?php echo _l('utility_activity_log_import_file'); ?>
                              </th>
                              <th>
                                 <?php echo _l('utility_activity_log_dt_staff'); ?>
                              </th>
                              <th>
                                 <?php echo _l('utility_activity_log_dt_date'); ?>
                              </th>
                           </tr>
                        </thead>
                        <tbody>
                           <?php foreach ($history as $log) { ?>
                              <tr>
                                 <td width="70%">
                                    <?php echo check_for_links($log['description']); ?>
                                 </td>
                                 <td width="70%">
                                    <?php echo '<a href="' . admin_url('clients/download_file_csv/' . $log['id']) . '">' . (json_decode($log['data'])->file_name ?? '') . '</a>' ?>
                                 </td>
                                 <td>
                                    <?php echo '<a href="' . admin_url('profile/' . $log['staffid']) . '">' . $log['staff_name'] . '</a>' ?>
                                 </td>
                                 <td>
                                    <?php echo _dt($log['date']); ?>
                                 </td>
                              </tr>
                           <?php } ?>
                        </tbody>
                     </table>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
<?php init_tail(); ?>
<script src="<?php echo base_url('assets/plugins/jquery-validation/additional-methods.min.js'); ?>"></script>
<script>
   appValidateForm($('#import_form'), {
      file_csv: {
         required: true,
         extension: "csv"
      }
   });
</script>
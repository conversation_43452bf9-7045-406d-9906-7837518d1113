<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
   <div class="content">
      <div class="row">
         <div class="col-md-12">
            <div class="panel_s">   
               <div class="panel-body">
                  <h4 class="no-margin"><?php echo _l('client_request_title'); ?></h4>
                  <hr class="hr-panel-heading" />
                  <?php // echo validation_errors('<div class="alert alert-danger">', '</div>'); ?>
                  <?php echo form_open_multipart($this->uri->uri_string(), ['class' => 'disable-on-submit']); ?>
                  <div class="col-md-12 no-padding-left no-padding-right">
                     <div class="col-md-6 no-padding-left">
                        <?php echo render_input('business_name', 'client_company', $data['business_name'] ?? '', 'text', ['placeholder' => _l('client_request_company_placeholder')]); ?>
                        <?php echo form_error('business_name', '<p id="business_name-error" class="text-danger">', '</p>'); ?>
                        
                        <?php echo render_input('vat', 'client_vat_number', $data['vat'] ?? '', 'text', ['placeholder' => _l('client_request_vat_placeholder')]); ?>
                        <?php echo form_error('vat', '<p id="vat-error" class="text-danger">', '</p>'); ?>
                        
                        <?php echo render_input('website', 'client_website', $data['website'] ?? '', 'text', ['placeholder' => _l('client_request_website_placeholder')]); ?>
                        <?php echo form_error('website', '<p id="website-error" class="text-danger">', '</p>'); ?>
                        
                        <?php echo render_input('source_reason', 'client_request_note', $data['source_reason'] ?? '', 'text', ['placeholder' => _l('client_request_note_placeholder')]); ?>
                        <?php echo form_error('source_reason', '<p id="source_reason-error" class="text-danger">', '</p>'); ?>

                        <?php echo render_select('source', get_all_company_creation_sources(), ['value', 'text'], _l('client_request_create_source'), $data['source'] ?? null); ?>
                        <?php echo form_error('source', '<p id="source-error" class="text-danger">', '</p>'); ?>
                     </div>
                     <div class="col-md-6 no-padding-left  no-padding-right">
                        <?php echo render_input('address', 'client_address', $data['address'] ?? '', 'text', ['placeholder' => _l('client_request_company_addr_placeholder')]); ?>
                        <?php echo form_error('address', '<p id="address-error" class="text-danger">', '</p>'); ?>
                        
                        <?php echo render_input('phonenumber', 'client_phonenumber', $data['phonenumber'] ?? '', 'text', ['placeholder' => _l('phone_placeholder')]); ?>
                        <?php echo form_error('phonenumber', '<p id="phonenumber-error" class="text-danger">', '</p>'); ?>
                        
                        <?php echo render_input('company_email', 'client_request_company_email', $data['company_email'] ?? '', 'text', ['placeholder' => _l('email_placeholder')]); ?>
                        <?php echo form_error('company_email', '<p id="company_email-error" class="text-danger">', '</p>'); ?>
                        
                        <?php echo render_input('source_reference_link', 'client_request_reference_link', $data['source_reference_link'] ?? '', 'text', ['placeholder' => _l('client_request_reference_link_placeholder')]); ?>
                        <?php echo form_error('source_reference_link', '<p id="source_reference_link-error" class="text-danger">', '</p>'); ?>
                        
                        <?php echo render_input('attachment', 'client_request_reference_file', '', 'file', ['accept' => '.png, .jpeg, .pdf']); ?>
                        <?php echo form_error('attachment', '<p id="attachment-error" class="text-danger">', '</p>'); ?>
                     </div>
                  </div>
                  <button type="submit" class="btn btn-info pull-right"><?php echo _l('save_customer_and_add_contact'); ?></button>
                  <a href="<?php echo admin_url('clients/list_created_requests') ?>" class="btn btn-default pull-right mright10"><?php echo _l('close'); ?></button>
                  <?php echo form_close(); ?>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
<?php init_tail(); ?>
<script>
   $(function() {
      jQuery.validator.addMethod("validatePhoneNumber", 
        function(value, element) {
            if (value) {
               return value.match(/^(0\d{9,})$/gi);
            }
            return true;
        }, 
        '<?php echo _l('request_client_valid_phonenumer') ?>'
    );
      appValidateForm($('form'), {
         business_name: 'required',
         vat: 'required',
         website: 'required',
         source_reason: 'required',
         source: 'required',
         address: 'required',
         reference_link_info: 'url',
         company_email: 'email',
         phonenumber: {
            validatePhoneNumber: true
         },
         attachment: {
            extension: 'png|jpg|pdf'
         }
      }, null, {
         business_name: {
            required: '<?php echo str_replace('{field}', '<b>' . _l('client_company') . '</b>', _l('form_validation_required')) ?>'
         },
         vat: {
            required: '<?php echo str_replace('{field}', '<b>' . _l('client_vat_number') . '</b>', _l('form_validation_required')) ?>'
         },
         website: {
            required: '<?php echo str_replace('{field}', '<b>' . _l('client_website') . '</b>', _l('form_validation_required')) ?>'
         },
         reason: {
            required: '<?php echo str_replace('{field}', '<b>' . _l('client_request_note') . '</b>', _l('form_validation_required')) ?>'
         },
         source: {
            required: '<?php echo str_replace('{field}', '<b>' . _l('client_request_create_source') . '</b>', _l('form_validation_required')) ?>'
         },
         address: {
            required: '<?php echo str_replace('{field}', '<b>' . _l('client_address') . '</b>', _l('form_validation_required')) ?>'
         },
         link: {
            url: '<?php echo str_replace('{field}', '<b>' . _l('client_request_reference_link') . '</b>', _l('request_client_valid_url')) ?>'
         },
         company_email: {
            email: '<?php echo str_replace('{field}', '<b>' . _l('client_request_company_email') . '</b>', _l('form_validation_valid_email')) ?>'
         },
      });
   });
</script>
</body>

</html>
<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<?php $this->load->view('admin/clients/modals/note.php'); ?>
<div class="modal fade" id="note_internal_modal" tabindex="-1" role="dialog" aria-labelledby="internalNoteModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <a href="#" class="pull-right" data-dismiss="modal"> <?=_l('new_note') ?></a>
                <h4 class="modal-title" id="internalNoteModalLabel">
                    <span class="add-title"><?= _l('internal_note'); ?></span>
                </h4>
            </div>
            <div class="modal-body">
               <div class="row">
                  <div class="col-md-12">
                        <div id="internal_note"></div><br>
                  </div>
               </div>
            </div>
            <div class="modal-footer">
                <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="add_favorite_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button group="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">
                    <span class="add-title"><?= _l('new_favorite'); ?></span>
                </h4>
            </div>
            <?= form_open('', ['id' => 'add_favorite_form']); ?>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <?php 
                           echo render_textarea('target', _l('your_target'), '', ['required' => true]);
                        ?>
                        <input type="hidden" name="staff_id" id="favorite_staff_id"/>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                <button group="submit" class="btn btn-info"><?= _l('submit'); ?></button>
                <?= form_close(); ?>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="add_feedback_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button group="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">
                    <span class="add-title"><?= _l('add_feedback'); ?></span>
                </h4>
            </div>
            <?= form_open('', ['id' => 'add_feedback_form']); ?>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div id="feedback_data_note"></div>
                        <input type="hidden" name="feedback_user_id" id="feedback_user_id"/>
                        <input type="hidden" name="feedback_assigned" id="feedback_assigned"/>
                        <div id="feedback"></div><br>
                        <?php
                           if (is_leader_member()) {
                              echo render_textarea('description', _l('feedback_description'), '', ['required' => true]);
                           }
                        ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                <?php if (is_leader_member()) { ?>
                <button group="submit" class="btn btn-info"><?= _l('submit'); ?></button>
                <?php } ?>
                <?= form_close(); ?>
            </div>
        </div>
    </div>
</div>
<div id="wrapper">
   <div class="content">
      <div class="row">
         <div class="col-md-12">
            <div class="_filters _hidden_inputs hidden">
               <?php
                  $startOfMonth = date('Y-m-01');
                  $endOfMonth = date('Y-m-t 23:59:59');

                  echo form_hidden('my_customers');
                  echo form_hidden('requires_registration_confirmation');

                  echo form_hidden('start_of_month', $startOfMonth);
                  echo form_hidden('end_of_month', $endOfMonth);

                  foreach($groups as $group){
                     echo form_hidden('customer_group_'.$group['id']);
                  }
                  foreach($contract_types as $type){
                     echo form_hidden('contract_type_'.$type['id']);
                  }
                  foreach($invoice_statuses as $status){
                     echo form_hidden('invoices_'.$status);
                  }
                  foreach($estimate_statuses as $status){
                     echo form_hidden('estimates_'.$status);
                  }
                  foreach($project_statuses as $status){
                  echo form_hidden('projects_'.$status['id']);
                  }
                  foreach($proposal_statuses as $status){
                  echo form_hidden('proposals_'.$status);
                  }
                  foreach($customer_admins as $cadmin){
                  echo form_hidden('responsible_admin_'.$cadmin['staff_id']);
                  }
                  foreach($countries as $country){
                  echo form_hidden('country_'.$country['country_id']);
                  }
                  ?>
                  <div class="filter-summary">
                     <?php
                        echo form_hidden('contacted');
                        echo form_hidden('not_contacted');
                        echo form_hidden('have_invoice');
                        echo form_hidden('no_invoice');
                     ?>
                  </div>
            </div>
            <div class="panel_s">
               <div class="panel-body">
                  <div class="_buttons">
                     <?php if (has_permission('customers','','create')) { ?>
                     <a href="<?= admin_url('clients/client'); ?>" class="btn btn-info mright5 test pull-left display-block"><?= _l('new_client'); ?></a>
                     <a href="<?= admin_url('clients/import'); ?>" class="btn btn-info pull-left display-block mright5 hidden-xs">
                     <?= _l('import_customers'); ?></a>
                     <a href="<?= admin_url('clients/import_customer_admins'); ?>" class="btn btn-info pull-left display-block mright5 hidden-xs">
                     <?= _l('import_customer_admins'); ?></a>
                     <?php if (has_permission('customers', '', 'bulk_remove_customer_admin')) : ?>
                        <a href="<?= admin_url('clients/remove_customer_admins'); ?>" class="btn btn-info pull-left display-block mright5 hidden-xs">
                     <?= _l('bulk_remove_customer_admins'); ?></a>
                     <?php endif; ?>
                     <a href="<?= admin_url('clients/import_salesperson'); ?>" class="btn btn-info pull-left display-block mright5 hidden-xs">
                     <?= _l('import_salesperson'); ?></a>
                     <?php } ?>
                     <?php if (has_permission('customers', '', 'update_num_of_usage_behavior')) : ?>
                        <a href="<?= admin_url('clients/import_num_of_usage_behavior'); ?>" class="btn btn-info pull-left display-block mright5 hidden-xs">
                     <?= _l('import_num_of_usage_behavior_btn'); ?></a>
                     <?php endif; ?>
                     <a href="<?= admin_url('clients/all_contacts'); ?>" class="btn btn-info pull-left display-block mright5">
                     <?= _l('customer_contacts'); ?></a>
                     <div class="visible-xs">
                        <div class="clearfix"></div>
                     </div>
                     <div class="btn-group pull-right btn-with-tooltip-group _filter_data" data-toggle="tooltip" data-title="<?= _l('filter_by'); ?>">
                        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fa fa-filter" aria-hidden="true"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-left" style="width:300px;">
                           <li class="active"><a href="#" data-cview="all" onclick="dt_custom_view('','.table-clients',''); return false;"><?= _l('customers_sort_all'); ?></a>
                           </li>
                           <?php if(get_option('customer_requires_registration_confirmation') == '1' || total_rows(db_prefix().'clients','registration_confirmed=0') > 0) { ?>
                           <li class="divider"></li>
                           <li>
                              <a href="#" data-cview="requires_registration_confirmation" onclick="dt_custom_view('requires_registration_confirmation','.table-clients','requires_registration_confirmation'); return false;">
                              <?= _l('customer_requires_registration_confirmation'); ?>
                              </a>
                           </li>
                           <?php } ?>
                           <li class="divider"></li>
                           <li>
                              <a href="#" data-cview="my_customers" onclick="dt_custom_view('my_customers','.table-clients','my_customers'); return false;">
                              <?= _l('customers_assigned_to_me'); ?>
                              </a>
                           </li>
                           <li class="divider"></li>
                           <?php if(count($groups) > 0){ ?>
                           <li class="dropdown-submenu pull-left groups">
                              <a href="#" tabindex="-1"><?= _l('customer_groups'); ?></a>
                              <ul class="dropdown-menu dropdown-menu-left">
                                 <?php foreach($groups as $group){ ?>
                                 <li><a href="#" data-cview="customer_group_<?= $group['id']; ?>" onclick="dt_custom_view('customer_group_<?= $group['id']; ?>','.table-clients','customer_group_<?= $group['id']; ?>'); return false;"><?= $group['name']; ?></a></li>
                                 <?php } ?>
                              </ul>
                           </li>
                           <div class="clearfix"></div>
                           <li class="divider"></li>
                           <?php } ?>
                           <?php if(count($countries) > 1){ ?>
                           <li class="dropdown-submenu pull-left countries">
                              <a href="#" tabindex="-1"><?= _l('clients_country'); ?></a>
                              <ul class="dropdown-menu dropdown-menu-left">
                                 <?php foreach($countries as $country){ ?>
                                 <li><a href="#" data-cview="country_<?= $country['country_id']; ?>" onclick="dt_custom_view('country_<?= $country['country_id']; ?>','.table-clients','country_<?= $country['country_id']; ?>'); return false;"><?= $country['short_name']; ?></a></li>
                                 <?php } ?>
                              </ul>
                           </li>
                           <div class="clearfix"></div>
                           <li class="divider"></li>
                           <?php } ?>
                           <li class="dropdown-submenu pull-left invoice">
                              <a href="#" tabindex="-1"><?= _l('invoices'); ?></a>
                              <ul class="dropdown-menu dropdown-menu-left">
                                 <?php foreach($invoice_statuses as $status){ ?>
                                 <li>
                                    <a href="#" data-cview="invoices_<?= $status; ?>" onclick="dt_custom_view('invoices_<?= $status; ?>','.table-clients','invoices_<?= $status; ?>'); return false;"><?= _l('customer_have_invoices_by',format_invoice_status($status,'',false)); ?></a>
                                 </li>
                                 <?php } ?>
                              </ul>
                           </li>
                           <div class="clearfix"></div>
                           <li class="divider"></li>
                           <li class="dropdown-submenu pull-left estimate">
                              <a href="#" tabindex="-1"><?= _l('estimates'); ?></a>
                              <ul class="dropdown-menu dropdown-menu-left">
                                 <?php foreach($estimate_statuses as $status){ ?>
                                 <li>
                                    <a href="#" data-cview="estimates_<?= $status; ?>" onclick="dt_custom_view('estimates_<?= $status; ?>','.table-clients','estimates_<?= $status; ?>'); return false;">
                                    <?= _l('customer_have_estimates_by',format_estimate_status($status,'',false)); ?>
                                    </a>
                                 </li>
                                 <?php } ?>
                              </ul>
                           </li>
                           <div class="clearfix"></div>
                           <li class="divider"></li>
                           <li class="dropdown-submenu pull-left project">
                              <a href="#" tabindex="-1"><?= _l('projects'); ?></a>
                              <ul class="dropdown-menu dropdown-menu-left">
                                 <?php foreach($project_statuses as $status){ ?>
                                 <li>
                                    <a href="#" data-cview="projects_<?= $status['id']; ?>" onclick="dt_custom_view('projects_<?= $status['id']; ?>','.table-clients','projects_<?= $status['id']; ?>'); return false;">
                                    <?= _l('customer_have_projects_by',$status['name']); ?>
                                    </a>
                                 </li>
                                 <?php } ?>
                              </ul>
                           </li>
                           <div class="clearfix"></div>
                           <li class="divider"></li>
                           <li class="dropdown-submenu pull-left proposal">
                              <a href="#" tabindex="-1"><?= _l('proposals'); ?></a>
                              <ul class="dropdown-menu dropdown-menu-left">
                                 <?php foreach($proposal_statuses as $status){ ?>
                                 <li>
                                    <a href="#" data-cview="proposals_<?= $status; ?>" onclick="dt_custom_view('proposals_<?= $status; ?>','.table-clients','proposals_<?= $status; ?>'); return false;">
                                    <?= _l('customer_have_proposals_by',format_proposal_status($status,'',false)); ?>
                                    </a>
                                 </li>
                                 <?php } ?>
                              </ul>
                           </li>
                           <div class="clearfix"></div>
                           <?php if(count($contract_types) > 0) { ?>
                           <li class="divider"></li>
                           <li class="dropdown-submenu pull-left contract_types">
                              <a href="#" tabindex="-1"><?= _l('contract_types'); ?></a>
                              <ul class="dropdown-menu dropdown-menu-left">
                                 <?php foreach($contract_types as $type){ ?>
                                 <li>
                                    <a href="#" data-cview="contract_type_<?= $type['id']; ?>" onclick="dt_custom_view('contract_type_<?= $type['id']; ?>','.table-clients','contract_type_<?= $type['id']; ?>'); return false;">
                                    <?= _l('customer_have_contracts_by_type',$type['name']); ?>
                                    </a>
                                 </li>
                                 <?php } ?>
                              </ul>
                           </li>
                           <?php } ?>
                           <?php if(count($customer_admins) > 0 && (has_permission('customers','','create') || has_permission('customers','','edit'))){ ?>
                           <div class="clearfix"></div>
                           <li class="divider"></li>
                           <li class="dropdown-submenu pull-left responsible_admin">
                              <a href="#" tabindex="-1"><?= _l('responsible_admin'); ?></a>
                              <ul class="dropdown-menu dropdown-menu-left">
                                 <?php foreach($customer_admins as $cadmin){ ?>
                                 <li>
                                    <a href="#" data-cview="responsible_admin_<?= $cadmin['staff_id']; ?>" onclick="dt_custom_view('responsible_admin_<?= $cadmin['staff_id']; ?>','.table-clients','responsible_admin_<?= $cadmin['staff_id']; ?>'); return false;">
                                    <?= get_staff_full_name($cadmin['staff_id']); ?>
                                    </a>
                                 </li>
                                 <?php } ?>
                              </ul>
                           </li>
                           <?php } ?>
                        </ul>
                     </div>
                  </div>
                  <div class="clearfix"></div>
                  <hr class="hr-panel-heading" />
                  <?php $this->load->view('admin/clients/statistics.php'); ?>
                  <hr class="hr-panel-heading" />
                  <a href="#" data-toggle="modal" data-target="#customers_bulk_action" class="bulk-actions-btn table-btn hide" data-table=".table-clients"><?= _l('bulk_actions'); ?></a>
                  <div class="modal fade bulk_actions" id="customers_bulk_action" tabindex="-1" role="dialog">
                     <div class="modal-dialog" role="document">
                        <div class="modal-content">
                           <div class="modal-header">
                              <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                              <h4 class="modal-title"><?= _l('bulk_actions'); ?></h4>
                           </div>
                           <div class="modal-body">
                              <?php if(has_permission('customers','','delete')){ ?>
                              <div class="checkbox checkbox-danger">
                                 <input type="checkbox" name="mass_delete" id="mass_delete">
                                 <label for="mass_delete"><?= _l('mass_delete'); ?></label>
                              </div>
                              <hr class="mass_delete_separator" />
                              <?php } ?>
                              <div id="bulk_change">
                                 <?= render_select('move_to_groups_customers_bulk[]',$groups,array('id','name'),'customer_groups','', array('multiple'=>true),array(),'','',false); ?>
                                 <p class="text-danger"><?= _l('bulk_action_customers_groups_warning'); ?></p>
                              </div>
                           </div>
                           <div class="modal-footer">
                              <button type="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                              <a href="#" class="btn btn-info" onclick="customers_bulk_action(this); return false;"><?= _l('confirm'); ?></a>
                           </div>
                        </div>
                        <!-- /.modal-content -->
                     </div>
                     <!-- /.modal-dialog -->
                  </div>

                  <?php
                  if (in_array($this->staff_model->get(get_staff_user_id())->role, [4, 8])) {
                  ?>
                     <a href="#" data-toggle="modal" data-target="#customers_transfer_action" class="bulk-actions-btn table-btn hide" data-table=".table-clients"><?= _l('transfer_actions'); ?></a>
                  <?php   
                  }
                  ?>
                  <div class="modal fade bulk_actions" id="customers_transfer_action" tabindex="-1" role="dialog">
                     <div class="modal-dialog" role="document">
                        <div class="modal-content">
                           <div class="modal-header">
                              <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                              <h4 class="modal-title"><?= _l('transfer_actions'); ?></h4>
                           </div>
                           <?= form_open('/admin/clients/transfer_customer', ['class' => 'form-transfer-validate']); ?>
                              <div class="modal-body">
                                 <?php if(has_permission('customers','','delete')) ?>
                                 <label class="radio-inline">
                                    <input style="margin-top: 2px" type="radio" name="type_transfer" value="common" checked> Bể chung
                                 </label>
                                 <label class="radio-inline">
                                    <input style="margin-top: 2px" type="radio" name="type_transfer" value="private"> Bể riêng
                                 </label>
                                 <hr class="mass_delete_separator" />
                                 <div class="form-transfer" id="form-common">
                                    <?php
                                    $option_attr = ['staffid', array('firstname','lastname')];
                                       echo render_input('number_transfer','Number transfer', '', 'number');
                                       echo render_select('customer_admin', $staff_active, $option_attr, _l('customer_admins'));
                                    ?>
                                 </div>
                                 <div class="form-transfer" id="form-private" style="display: none;">
                                    <?php
                                       echo render_select('customer_admin_from', $staff, $option_attr, 'From');
                                       echo render_select('customer_admin_to', $staff_active, $option_attr, 'To');
                                    ?>
                                 </div>
                              </div>
                              <div class="modal-footer">
                                 <button type="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                                 <button type="submit" class="btn btn-info"><?= _l('confirm'); ?></button>
                              </div>
                           <?= form_close(); ?>
                        </div>
                     </div>
                  </div>
                  <!-- /.modal -->

                   <?php if (in_array($this->staff_model->get(get_staff_user_id())->role, [4, 7, 8])): ?>
                   <a href="#" id="btnExportGoogleSheet" class="bulk-actions-btn table-btn hide" data-table=".table-clients"><?= _l('export_google_sheet'); ?></a>
                   <?php endif; ?>

                  <div class="row">
                     <div class="col-md-2">
                        <?= render_select('department', get_list_department(), ['departmentid', 'name'], _l('department') . " (Customer admin's Team)", '', ['onchange' => "reloadDataTable('.table-invoices')"]); ?>
                     </div>
                     <div class="col-md-2">
                        <?= render_select('day_of_note', get_day_of_note(), ['value', 'text'], _l('day_of_note'), '', ['onchange' => "reloadDataTable('.table-clients')"]); ?>
                     </div>
                     <div class="col-md-2">
                        <?= render_select('filter_type_of_customer', get_all_type_of_customers(), ['value', 'value'], _l('type_of_customer'), '', ['onchange' => "reloadDataTable('.table-clients')"]); ?>
                     </div>
                     <?php if(!is_sales_member()): ?>
                        <div class="col-md-2">
                           <?= render_select('filter_customer_admin', get_active_customer_admins(), ['id', 'name'], _l('customer_admins'), '', ['onchange' => "reloadDataTable('.table-clients')"]); ?>
                        </div>
                     <?php endif; ?>
                  </div>
                  <div class="checkbox d-flex">
                     <div class="mright35">
                        <input type="checkbox" checked id="exclude_inactive" name="exclude_inactive">
                        <label for="exclude_inactive"><?= _l('exclude_inactive'); ?> <?= _l('clients'); ?></label>
                     </div>

                     <div>
                        <input type="checkbox" id="has_salesperson" name="has_salesperson">
                        <label for="has_salesperson">Has <?= _l('salesperson_f1'); ?></label>
                     </div>
                  </div>
                  <div class="clearfix mtop20"></div>
                  <?php
                     $table_data = [];
                     $custom_fields = get_custom_fields('customers', ['show_on_table' => 1]);
                     $table_data = [
                        '<span class="hide"> - </span><div class="checkbox mass_select_all_wrap"><input type="checkbox" id="mass_select_all" data-to-table="clients"><label></label></div>',
                        [
                           'name' => _l('the_number_sign'),
                           'th_attrs' => ['class' => 'toggleable', 'id' => 'th-number']
                        ],
                        [
                           'name' => _l('id_ams'),
                           'th_attrs' => ['class' => 'toggleable', 'id' => 'th-id_ams']
                        ],
                        [
                           'name' => _l('clients_list_company'),
                           'th_attrs' => ['class' => 'toggleable', 'id' => 'th-company']
                        ],
                        [
                           'name' => _l('contact'),
                           'th_attrs' => ['class' => 'toggleable', 'id' => 'th-contact']
                        ],
                        [
                           'name' => _l('client_request_company_status'),
                           'th_attrs' => ['class' => 'toggleable', 'id' => 'th-client_request_company_status']
                        ],
                        [
                           'name' => _l('type_of_customer'),
                           'th_attrs' => ['class' => 'toggleable', 'id' => 'th-type-of-customer']
                        ],
                        [
                           'name' => _l('usage_behavior'),
                           'th_attrs' => ['class' => 'toggleable', 'id' => 'th-usage-behavior']
                        ],
                        [
                           'name' => _l('note'),
                           'th_attrs' => [
                              'class' => 'toggleable',
                              'id' => 'th-note',
                              'style' => 'min-width: 300px'
                           ]
                        ],
                        [
                           'name' => _l('latest_potential_rate'),
                           'th_attrs' => ['class' => 'toggleable', 'id' => 'th-potential-rate']
                        ],
                        [
                           'name' => _l('customer_admins'),
                           'th_attrs' => ['class' => 'toggleable', 'id' => 'th-customer-admins']
                        ],
                        [
                           'name' => _l('client_request_create_source'),
                           'th_attrs' => ['class' => 'toggleable', 'id' => 'th-client_request_create_source']
                        ]                     
                     ];
                     array_splice($table_data, 9, 0, array_column($custom_fields, 'name'));
                     $table_data = hooks()->apply_filters('customers_table_columns', $table_data);
                     render_datatable($table_data, 'clients', [], [
                        'data-last-order-identifier' => 'customers',
                        'data-default-order' => get_table_last_order('customers'),
                        'id' => 'clients'
                     ]);
                  ?>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
<?php init_tail(); ?>
<script>
   function setFavorite(clientId, staffId) {
      $('#add_favorite_form').attr('action', `/admin/clients/favorite/${clientId}`);
      $('#favorite_staff_id').val(staffId);

      appValidateForm($("#add_favorite_form"), {
         target: 'required',
      });
   }

   function addFeedback(noteId) {
      requestGetJSON('misc/get_note/' + noteId).done(function (response) {
         const data = response.data;
         $('#feedback_data_note').html(`<b>${data['staff']}</b>: ${data['description']} (${data['dateadded']})<br><br>`);
         $('#feedback_user_id').val(data['rel_id'])
         $('#feedback_assigned').val(data['addedfrom'])

         let feedback = '<b>Feedback</b><br>';
         data.feedback.forEach(element => {
            feedback += `
               <b>${element['staff']}</b>: ${element['description']} (${element['created_at']})
               <a href="/admin/tickets/ticket/${element['ticket_id']}" class="mleft5"><i style="color: black;" class="fa fa-reply"></i></a>
               <?php if (is_leader_member()) { ?>
                  <a href="/admin/misc/delete_feedback/${element['id']}" class="mleft5 _delete"><i style="color: red;" class="fa fa-remove"></i></a>
               <?php } ?>
               <br>
            `
         });

         $('#feedback').html(feedback)
      })

      const form = $('#add_feedback_form');
      form.attr('action', `/admin/misc/feedback/${noteId}`);
      appValidateForm(form, {
         description: 'required',
      });
   }

   function showInternalNote(clientId) {
      $('#note_internal_modal').modal('show');
      $('#note_internal_modal .modal-header > a').attr('onclick', 'showNoteForm(' + clientId + ')')

      requestGetJSON('misc/get_internal_note/' + clientId).done(function (response) {
         const data = response.data;

         let content = '';
         data.forEach(element => {
            content += `
               <b>${element['firstname'] + ' ' + element['lastname']}</b>: (${element['dateadded']})
               <br>
               <div class="description">
                  ${element['description']}
               </div>
               <br>
            `
         });

         $('#internal_note').html(content)
      })
   }

   var tAPI;

   $(function(){
       var CustomersServerParams = {};
       $.each($('._hidden_inputs._filters input'),function(){
          CustomersServerParams[$(this).attr('name')] = '[name="'+$(this).attr('name')+'"]';
      });
       CustomersServerParams['exclude_inactive'] = '[name="exclude_inactive"]:checked';
       CustomersServerParams['has_salesperson'] = '[name="has_salesperson"]:checked';
       CustomersServerParams['department'] = '[name="department"]';
       CustomersServerParams['day_of_note'] = '[name="day_of_note"]';
       CustomersServerParams['type_of_customer'] = '[name="filter_type_of_customer"]';
       CustomersServerParams['customer_admin'] = '[name="filter_customer_admin"]';

       tAPI = initDataTable(
         '.table-clients',
         admin_url + 'clients/table',
         [0],
         [0],
         CustomersServerParams,
         <?= hooks()->apply_filters('customers_table_default_order', json_encode(array(3, 'desc'))); ?>
      );

       $('input[name="exclude_inactive"]').on('change',function() {
           tAPI.ajax.reload();
       });

       $('input[name="has_salesperson"]').on('change',function() {
           tAPI.ajax.reload();
       });

       $('select[name="department"]').on('change',function() {
           tAPI.ajax.reload();
       });

       $('a#filterExpiredCustomer').on('click', function () {
         document.getElementById('clients').scrollIntoView({behavior: "smooth"});
         CustomersServerParams['expired_customer'] = 'input#expiredCustomer';
         tAPI.ajax.reload();
       });

      var vRules = {};
      var data = {};
      vRules = {
         number_transfer: 'required',
         customer_admin: 'required',
      }
      appValidateForm($(".form-transfer-validate"), vRules);

      $("input[name$='type_transfer']").click(function() {
         var type = $(this).val();

         if(type == 'private') {
            vRules = {
               customer_admin_from: 'required',
            }
         } else {
            vRules = {
               number_transfer: 'required',
               customer_admin: 'required',
            }
         }

         appValidateForm($(".form-transfer-validate"), vRules);
         $("div.form-transfer").hide();
         $("#form-" + type).show();
      });
   });

   function unsetFavorite(clientId, staffId) {
      var r = confirm(app.lang.confirm_action_prompt);
       if (r == false) {
           return false;
       } else {
         $.ajax({
                type:"GET",
                dataType: 'json',
                async: false,
                url: admin_url + 'clients/not_favorite',
                data: {
                  clientId: clientId,
                  staffId: staffId
                },
                success: function(response) {
                  if (response.success) {
                     alert_float('success', response.message);
                     tAPI.ajax.reload();
                  } else {
                     alert_float('danger', _l(response.message));
                     
                  }
                }
            });
       }
   }

   function customers_bulk_action(event) {
       var r = confirm(app.lang.confirm_action_prompt);
       if (r == false) {
           return false;
       } else {
           var mass_delete = $('#mass_delete').prop('checked');
           var ids = [];
           var data = {};
           if(mass_delete == false || typeof(mass_delete) == 'undefined'){
               data.groups = $('select[name="move_to_groups_customers_bulk[]"]').selectpicker('val');
               if (data.groups.length == 0) {
                   data.groups = 'remove_all';
               }
           } else {
               data.mass_delete = true;
           }
           var rows = $('.table-clients').find('tbody tr');
           $.each(rows, function() {
               var checkbox = $($(this).find('td').eq(0)).find('input');
               if (checkbox.prop('checked') == true) {
                   ids.push(checkbox.val());
               }
           });
           data.ids = ids;
           $(event).addClass('disabled');
           setTimeout(function(){
             $.post(admin_url + 'clients/bulk_action', data).done(function() {
              window.location.reload();
          });
         },50);
       }
   }

   /*
    * Export to google sheet function
    */
   $(function () {
       $('#btnExportGoogleSheet').on('click', function () {
           if (! confirm('Are you sure to export to Google sheet?')) return;

           $.when(
               $.post('https://reports.topdev.asia/api/crm/export-customer-admin'),
               $.post('https://reports.topdev.asia/api/crm/export-all-team-customer-admin'),
               $.post('https://reports.topdev.asia/api/crm/export-all-clients'),
               $.post('https://reports.topdev.asia/api/crm/export-estimates'),
               $.post('https://reports.topdev.asia/api/crm/export-invoices')
           )
               .done(function () {
                   alert('Push message to Reports system success! Please wait for notify in CRM Notify Space!')
               })
               .fail(function () {
                   alert('Failed! Try again please')
               });
       });
   });
</script>
</body>
</html>

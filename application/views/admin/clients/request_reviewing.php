<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<?php $this->load->view('admin/clients/modals/request_note.php'); ?>
<div id="wrapper">
   <div class="content">
      <div class="row">
         <div class="col-md-12">
            <div class="panel_s">
               <div class="panel-body">
                  <div class="row request-search">
                     <div class="col-md-12 no-padding-left no-padding-right">
                        <div class="col-md-10 no-padding-left">
                           <div class="col-md-6 form-group">
                              <div class="input-group">
                                 <span class="input-group-addon"><span class="fa fa-search"></span></span>
                                 <input type="search" id="request_company" name="request_company" class="form-control input-sm" placeholder="<?= _l('client_request_search_input_placeholder') ?>">
                              </div>
                           </div>
                           <div class="col-md-12 no-padding-left">
                              <div class="col-md-3">
                                 <?= render_select('client_request_status', get_client_request_statuses(), ['value', 'text'], _l('client_request_statuses'), CLIENT_REQUEST_STATUS_WAITING); ?>
                              </div>
                           </div>
                           <div class="col-md-12 no-padding-left">
                              <div class="col-md-2">
                                 <?= render_date_input('created_at_from', 'client_request_created_at_from'); ?>
                              </div>
                              <div class="col-md-2">
                                 <?= render_date_input('created_at_to', 'client_request_created_at_to'); ?>
                              </div>
                              <div class="col-md-2">
                                 <?= render_date_input('sa_approved_at_from', 'client_request_sa_approved_at_from'); ?>
                              </div>
                              <div class="col-md-2">
                                 <?= render_date_input('sa_approved_at_to', 'client_request_sa_approved_at_to'); ?>
                              </div>
                              <div class="col-md-2">
                                 <?= render_date_input('leader_approved_at_from', 'client_request_leader_approved_at_from'); ?>
                              </div>
                              <div class="col-md-2">
                                 <?= render_date_input('leader_approved_at_to', 'client_request_leader_approved_at_to'); ?>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <?php render_datatable(client_approve_requests_grid_headers(), 'all-requests'); ?>
               </div>
            </div>
         </div>
      </div>
   </div>
   <?php init_tail(); ?>
   <script>
      var tAPI;
      $(function() {
         var customSearchParams = {};
         $.each($('._hidden_inputs._filters input'), function() {
            customSearchParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
         });
         customSearchParams['client_request_status'] = '[name="client_request_status"]';
         customSearchParams['request_company'] = '[name="request_company"]';
         // Dates filters
         customSearchParams['created_at_from'] = 'input[name="created_at_from"]';
         customSearchParams['created_at_to'] = 'input[name="created_at_to"]';
         customSearchParams['sa_approved_at_from'] = 'input[name="sa_approved_at_from"]';
         customSearchParams['sa_approved_at_to'] = 'input[name="sa_approved_at_to"]';
         customSearchParams['leader_approved_at_from'] = 'input[name="leader_approved_at_from"]';
         customSearchParams['leader_approved_at_to'] = 'input[name="leader_approved_at_to"]';

         tAPI = initDataTable('.table-all-requests', '<?= admin_url('clients/client_request_table'); ?>', [2, 3, 4, 5,6, 7], [2, 3, 4, 5, 6, 7], customSearchParams, [0, 'asc']);
         $('input[name="request_company"], select[name="client_request_status"], input.datepicker').on('change', function() {
            tAPI.ajax.reload();
         });
      });
   </script>
   </body>
   </html>
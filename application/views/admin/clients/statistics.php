<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<style>
    .customer-statistic .panel-body {
        min-height: 30px;
    }

    .customer-statistic .panel.title,
    .customer-statistic .panel.title .panel-body {
        background: transparent;
        border: none;
        border-color: transparent;
        box-shadow: none;
    }

    .customer-statistic .panel-body {
        padding: 5px;
    }

    .customer-statistic .performance .panel-body div {
        max-width: 130px;
    }

    .customer-statistic {
        position: relative;
    }

    .customer-statistic .loading-mask {
        width: 100%;
        height: 100%;
        position: absolute;
        background-color: rgba(45, 62, 80, .7);
        color: white;
        z-index: 1;
        cursor: not-allowed;
    }

    .customer-statistic .loading-mask div {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        height: 20px;
    }
</style>

<div class="customer-statistic row mbot15 no-mleft no-mright no-padding">
    <div class="loading-mask text-center">
        <div><?php echo _l('customer_statistic_please_wailt') ?></div>
    </div>
    <div class="col-md-12 d-flex no-padding">
        <div class="flex-grow-1 d-flex padding-5 mright5" style="background-color: #ECF2FF; border-radius: 10px;">
            <div class="flex-column text-left mtop10 mright10 mleft10">
                <h4 class="no-margin"><?php echo _l('customer_statistic_customer_overall'); ?></h4>
            </div>
            <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <a href="<?php echo admin_url('clients') ?>"><h3 id="total_customers" class="_total">0</h3></a>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_total_customers'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <a href="javascript:;" id="filterExpiredCustomer"><h3 id="total_expired_customers_next_10_days" class="_total">0</h3></a>
                    <input id="expiredCustomer" type="hidden" value="true" />
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_total_expired_customers_next_x_days'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <a href="<?php echo admin_url('free_data') ?>"><h3 id="total_free_customers" class="_total">0</h3></a>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_total_free_customers'); ?></div>
                </div>
            </div>
        </div>
        <div class="flex-grow-1 d-flex padding-5 mleft5" style="background-color: #ECF2FF; border-radius: 10px;">
            <div class="flex-column text-left mtop10 mright10 mleft10">
                <h4 class="no-margin"><?php echo _l('customer_statistic_business_overall'); ?></h4>
            </div>
            <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="total_paid_revenue" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_total_paid_reve_orders'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="total_paid_orders" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_total_paid_orders'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="total_delivered_orders" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_total_delivered_orders'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mleft5 no-padding-right panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="total_orders" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_total_orders'); ?></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12 padding-5 mtop10 performance" style="background-color: #B7E8EB; border-radius: 10px;">
        <div class="col-md-1 no-padding panel no-mbot no-border no-border-color title">
            <div class="panel-body no-border no-border-color mtop5">
                <?php echo _l('customer_statistic_working_performance'); ?>
            </div>
            <div class="panel-body no-border no-border-color mtop15">
                <h4 class="no-margin"><?php echo _l('customer_statistic_this_month'); ?></h4>
            </div>
        </div>
        <div class="col-md-11 d-flex no-padding-right">
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="this_month_company_contacted_success" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_company_contacted_success'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="this_month_company_contacted_later" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_company_contacted_later'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="this_month_company_contacted" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_company_contacted'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="this_month_company_not_contacted_yet" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_company_not_contacted_yet'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="this_month_valid_created_company" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_valid_created_company'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="this_month_valid_requested_take_care" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_valid_requested_take_care'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="this_month_calls" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_calls'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="this_month_call_durations" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_call_durations'); ?></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12 padding-5 mtop10 performance" style="background-color: #B6EDFF; border-radius: 10px;">
        <div class="col-md-1 no-padding panel no-mbot no-border no-border-color title">
            <div class="panel-body no-border no-border-color mtop5">
                <?php echo _l('customer_statistic_working_performance'); ?>
            </div>
            <div class="panel-body no-border no-border-color mtop15">
                <h4 class="no-margin"><?php echo _l('customer_statistic_today'); ?></h4>
            </div>
        </div>
        <div class="col-md-11 d-flex no-padding-right">
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="today_company_contacted_success" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_company_contacted_success'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="today_company_contacted_later" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_company_contacted_later'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="today_company_contacted" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_company_contacted'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="today_valid_created_contact" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_valid_created_contact'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="today_valid_created_company" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_valid_created_company'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="today_valid_requested_take_care" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_valid_requested_take_care'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 mright5 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="today_calls" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_calls'); ?></div>
                </div>
            </div>
            <div class="flex-grow-1 panel no-mbot">
                <div class="panel-body no-border no-border-color">
                    <h3 id="today_call_durations" class="_total">0</h3>
                </div>
                <div class="panel-body no-border no-border-color">
                    <div class="text-default"><?php echo _l('customer_statistic_call_durations'); ?></div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    window.addEventListener('DOMContentLoaded', function() {
        $.getJSON(admin_url + '/misc/customer_statiscal', function(response) {
            var data = response.data ?? {},
                floatFields = ['today_call_durations', 'this_month_call_durations'];
            for (const [key, value] of Object.entries(data)) {
                if (value > 0) {
                    const obj = document.getElementById(key);
                    animateValue(obj, 0, value, 1000, floatFields.indexOf(key) > -1);
                } else {
                    $('#' + key).text(value);
                }
            }
            $('.customer-statistic .loading-mask').addClass('hide');
        });
    });
</script>

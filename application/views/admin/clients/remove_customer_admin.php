<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
  <div class="content">
    <div class="row">
      <div class="col-md-12">
        <div class="panel_s">
          <div class="panel-body">
            <?php echo form_open($this->uri->uri_string()); ?>
            <?php echo form_hidden('download_sample', 'true'); ?>
            <button type="submit" class="btn btn-success">Download Sample</button>
            <hr />
            <?php echo form_close(); ?>
            <h3><?= _l('bulk_remove_customer_admins') ?></h3>
            <ul>
              <li><?php echo _l('bulk_remove_customer_admin_note') ?></li>
            </ul>
            <div class="table-responsive no-dt">
              <table class="table table-hover table-bordered" style="max-width: 100px; margin: 1px;">
                <thead>
                  <tr><th class="bold"><b><?php echo _l('bulk_remove_customer_admin_company_id'); ?></th></b></tr>
                </thead>
                <tbody>
                  <tr><td>123456</td></tr>
                </tbody>
              </table>
            </div>
            <div class="row">
              <div class="col-md-4 mtop15">
                <?php echo form_open_multipart($this->uri->uri_string(), array('id' => 'remove_form')); ?>
                <?php echo form_hidden('remove_customer_admins', 'true'); ?>
                <?php echo render_input('file_csv', 'choose_csv_file', '', 'file'); ?>
                <div class="form-group">
                  <button type="submit" class="btn btn-info import btn-import-submit"><?php echo _l('import'); ?></button>
                </div>
                <?php echo form_close(); ?>
              </div>
            </div>
            <div class="mtop15">
              <?php echo render_datatable(
                [
                  _l('utility_activity_log_import_history'),
                  _l('utility_activity_log_import_file'),
                  _l('utility_activity_log_dt_staff'),
                  _l('utility_activity_log_dt_date')
                ],
                'remove-customer-admin-histories'
              ); ?>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<?php init_tail(); ?>
<script src="<?php echo base_url('assets/plugins/jquery-validation/additional-methods.min.js'); ?>"></script>
<script>
  initDataTable('.table-remove-customer-admin-histories', '<?= admin_url('clients/remove_customer_admin_table'); ?>', null, [0, 1], null, [3, 'desc']);
  appValidateForm($('#remove_form'), {
    file_csv: {
      required: true,
      extension: "csv"
    }
  });
</script>
<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
   <div class="content">
      <div class="row">
         <div class="col-md-12">
            <div class="panel_s">
               <div class="panel-body">
                  <div class="row request-search">
                     <div class="col-md-12 no-padding-left no-padding-right">
                        <div class="col-md-10 no-padding-left">
                           <div class="col-md-6 form-group">
                              <div class="input-group">
                                 <span class="input-group-addon"><span class="fa fa-search"></span></span>
                                 <input type="search" id="request_company" name="request_company" class="form-control input-sm" placeholder="<?= _l('client_request_search_input_placeholder') ?>">
                              </div>
                           </div>
                           <div class="col-md-12 no-padding-left">
                              <div class="col-md-3">
                                 <?= render_select('client_request_status', get_client_request_statuses(), ['value', 'text'], _l('client_request_statuses')); ?>
                              </div>
                           </div>
                        </div>
                        <div class="col-md-2 text-right">
                           <a href="<?= admin_url('clients/create_client_request'); ?>" class="btn btn-info mbot15"><?= _l('client_request_create_request_btn') ?></a>
                        </div>
                     </div>
                  </div>
                  <?php render_datatable(client_requests_grid_headers(), 'all-requests'); ?>
               </div>
            </div>
         </div>
      </div>
   </div>
   <?php init_tail(); ?>
   <script>
      var tAPI;
      $(function() {
         var customSearchParams = {};
         $.each($('._hidden_inputs._filters input'), function() {
            customSearchParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
         });
         customSearchParams['client_request_status'] = '[name="client_request_status"]';
         customSearchParams['request_company'] = '[name="request_company"]';
         tAPI = initDataTable('.table-all-requests', '<?= admin_url('clients/client_request_table'); ?>', [3, 4], [3, 4], customSearchParams, [0, 'asc']);
         $('input[name="request_company"], select[name="client_request_status"]').on('change', function() {
            tAPI.ajax.reload();
         });
      });
   </script>
   </body>

   </html>
<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<style>
.process-list-container {
  padding: 5px 0;
  margin: 0 0 0 10px;
  border-left: 2px solid #d34127;
}
.process-list-container li {
  list-style-type: none;
  padding-left: 25px;
  margin-bottom: 5px;
  margin-left: -11px;
  background-image: url("<?php echo base_url('assets/images/red-circle.png') ?>");
  background-repeat: no-repeat;
  background-size: 16px;
  background-position-x: 2px;
  background-position-y: 2px;
}
.process-list-container li p {
  margin: 0;
}
.ui-state-highlight {
    height: 3em;
    line-height: 2em;
}
button.add-btn {
    text-decoration: unset !important;
}
#ams_post_job ul.tagit {
    display: -webkit-box!important;
    display: -ms-flexbox!important;
    display: flex!important;
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
    border: 1px solid #ccc !important;
}

#ams_post_job ul.tagit li.tagit-new {
    -webkit-box-flex: 1!important;
    -ms-flex-positive: 1!important;
    flex-grow: 1!important;
}

#ams_post_job ul.tagit input[type=text] {
    border-left: none !important;
}

#ams_post_job ul.tagit li.tagit-new input {
    width: 100%;
}
.has-error {
    border-color: #fc2d42 !important;
}
.benefit-error {
    position: absolute;
    padding: 0;
    margin: 0;
}
</style>
<?php if (isset($client)) { ?>
    <h4 class="customer-profile-group-heading"><?php echo _l('ams_post_job_title'); ?></h4>
    <?php echo form_open(admin_url('ams/post_job/' . $client_id . ($client_ams_job_id ? ('/' . $client_ams_job_id) : '')), ['id' => 'ams_post_job', 'class'=>'ams_post_job','autocomplete'=>'off']); ?>
    <input type="hidden" id="client_ams_job_id" name="client_ams_job_id" value="<?php echo $client_ams_job_id ?>" />
    <input type="hidden" id="ams_job_id" name="ams_job_id" value="<?php echo !$client_ams_job_id && $ams_job_id ? $ams_job_id : '' ?>" />
    <input type="hidden" id="ams_company_id" name="ams_company_id" value="<?php echo $job['company_id'] ?? '' ?>" />
    <div class="row">
        <div class="col-lg-12 mbot10">
            <label class="no-mbot">
                <input type="checkbox" <?php echo $free_package ?? '' ? 'checked' : '' ?> name="free_package" id="free_package">
                <b><?php echo _l('ams_post_job_free_checkbox_title') ?></b>
            </label>
            <div><?php echo _l('ams_post_job_free_checkbox_description') ?></div>
        </div>
        <div class="col-lg-12 mbot10">
            <label class="no-mbot">
                <input type="checkbox" <?php echo $job['is_content_image'] ?? '' ? 'checked' : '' ?> name="is_content_image" id="is_content_image">
                <b><?php echo _l('ams_post_job_content_image_title') ?></b>
            </label>
        </div>
        <div class="col-md-3">
            <?= render_select('company_id', $ams_companies ?? [], ['id', 'name'], 'ams_post_job_company_list', $job['company_id'] ?? '', array_merge(['data-none-selected-text' => _l('ams_post_job_company_list')], !empty($job['company_id']) ? ['disabled' => true] : [])); ?>
        </div>
        <div class="col-md-3 ">
            <?= render_select('crm_invoice_id', $invoices ?? [], ['id', 'name'], 'ams_post_job_invoice_list', $job['crm_invoice_id'] ?? '', ['data-none-selected-text' => _l('ams_post_job_invoice_placeholder')]); ?>
        </div>
        <div class="col-md-3 ">
            <?= render_select('package_id', $paid_packages ?? [], ['id', 'text'], 'ams_post_paid_package_id', $package_id ?? '', ['data-none-selected-text' => _l('ams_post_paid_package_id')]); ?>
        </div>
        <div class="col-md-3">
            <?= render_select('job_status', $job_statuses ?? [], ['value', 'label'], 'ams_post_paid_status', $package_id ?? '', ['data-none-selected-text' => _l('ams_post_paid_status')]); ?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
        <?php echo render_input('title', 'ams_post_job_title_label', $job['title'] ?? '', 'text', ['placeholder' => _l('ams_post_job_title_placeholder')]); ?>
        </div>
        <div class="col-md-6">
        <?= render_select('job_category_job', $job_categories ?? [], ['id', 'name'], 'ams_post_job_category_label', $job['job_category_job'] ?? '', ['id' => 'job_category_job', 'data-none-selected-text' => _l('ams_post_job_category_placeholder')]); ?>
        </div>
        <div class="col-md-6">
        <?= render_select('job_category_id[]', [], ['id', 'name'], 'ams_post_job_role_label', $job['job_category_id'] ?? [], ['id' => 'job_category_id', 'multiple' => true , 'data-none-selected-text' => _l('ams_post_job_role_placeholder')], [], '', '', false); ?>
        </div>
        <div class="col-lg-12">
        <?= render_select(
            'skills_ids[]',
            $taxonomies['skills'] ?? [],
            ['id', 'text'],
            'ams_post_job_skill_label',
            $job['skills_ids'] ?? [],
            [
                'id' => 'skills_ids',
                'multiple' => true,
                'data-max-options' => 5,
                'data-max-options-text' => _l('ams_post_job_skill_max_options'),
                'data-none-selected-text' => _l('ams_post_job_skill_placeholder')
            ],
            [],
            '',
            '',
            false,
        ); ?>
        </div>
        <div class="col-lg-12 mtop10">
            <?php $this->load->view('admin/components/checkbox', [
                'name' => 'job_levels',
                'required' => true,
                'label' => 'ams_post_job_level_label',
                'checkboxes' => $taxonomies['job_levels'] ?? [],
                'checkedIds' => $job['job_levels'] ?? [],
            ]); ?>
        </div>
        <div class="col-lg-12 mtop10">
            <div class="col-lg-12 no-padding">
                <label class="no-mbot"><span class="text-danger">*</span> <b><?php echo _l('ams_post_job_yoe_label') ?></b></label>
            </div>
            <div class="col-lg-6 mtop10 no-padding yoe-from-error">
                <div class="justify-content-between align-items-center">
                    <label for="yoe_from" class="control-label no-mbot mright5"><?php echo _l('ams_post_job_from_label') ?></label>
                    <select id="yoe_from" data-custom-error="yoe-from-error" name="yoe_from" class="form-control">
                        <option value=""></option>
                        <?php foreach ($taxonomies['experiences'] as $exp) : ?>
                            <option <?php echo (isset($job['yoe_from']) && $exp['id'] == $job['yoe_from']) ? 'selected' : '' ?> value="<?php echo $exp['id'] ?>"><?php echo $exp['text'] ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <div class="col-lg-6 mtop10 pleft10 yoe-to-error">
                <div class="justify-content-between align-items-center">
                    <label for="yoe_to" class="control-label no-mbot mright5"><?php echo _l('ams_post_job_to_label') ?></label>
                    <select id="yoe_to" name="yoe_to" data-custom-error="yoe-to-error" class="form-control">
                        <option value=""></option>
                        <?php foreach ($taxonomies['experiences'] as $exp) : ?>
                            <option <?php echo (isset($job['yoe_to']) && $exp['id'] == $job['yoe_to']) ? 'selected' : '' ?> value="<?php echo $exp['id'] ?>"><?php echo $exp['text'] ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-lg-12 mtop10">
            <?php $this->load->view('admin/components/checkbox', [
                'name' => 'job_types',
                'required' => true,
                'label' => 'ams_post_job_type_label',
                'checkboxes' => $taxonomies['job_types'] ?? [],
                'checkedIds' => $job['job_types'] ?? [],
            ]); ?>
        </div>
        <div class="col-lg-12 mtop10">
            <?php $this->load->view('admin/components/checkbox', [
                'name' => 'contract_type',
                'required' => true,
                'label' => 'ams_post_contract_type_label',
                'checkboxes' => $taxonomies['contract_types'] ?? [],
                'checkedIds' => $job['contract_type'] ?? [],
            ]); ?>
        </div>

        <div class="col-lg-12 mtop10">
        <?= render_select('addresses_id[]', [], ['id', 'text'], 'ams_post_job_localtion_label', $job['addresses_id'] ?? [], ['id' => 'addresses_id', 'multiple'=>true, 'data-none-selected-text' => _l('ams_post_job_localtion_placeholder')], [], '', '', false); ?>
        </div>

        <!-- Job Detail -->
        <div class="col-lg-12 mtop10">
            <?php echo render_textarea('content', 'ams_post_job_description_label', $job['content'] ?? '', ['placeholder' => _l('ams_post_job_description_placeholder'), 'rows' => 10], [], '', 'tinymce tinymce-manual'); ?>
        </div>


        <div class="col-lg-12 mtop10">
            <?php echo render_textarea('responsibilities_original', 'ams_post_job_responsibilities_label', $job['responsibilities_original'] ?? '', ['placeholder' => _l('ams_post_job_responsibilities_placeholder'), 'rows' => 15], [], '', 'tinymce tinymce-manual'); ?>
        </div>

        <!-- Responsibilities -->
        <?php $responsibilities = $taxonomies['responsibilities'] ?? []; ?>
        <div class="col-lg-12 mtop10">
            <?php $this->load->view('admin/components/select_dnd', [
                'name' => 'responsibilities',
                'label' => 'ams_post_job_responsibilities_label',
                'nonSelectedText' => 'ams_post_job_responsibilities_select',
                'nonFilledText' => 'ams_post_job_responsibilities_placeholder',
                'options' => $taxonomies['responsibilities'] ?? [],
                'selectedOptions' => $job['responsibilities'] ?? [],
            ]); ?>
        </div>

        <!-- Requirements -->
        <div class="col-lg-12 mtop10">
        <?php echo render_textarea('requirements_original', 'ams_post_job_requirements_label', $job['requirements_original'] ?? '', ['placeholder' => _l('ams_post_job_requirements_select'), 'rows' => 15], [], '', 'tinymce tinymce-manual'); ?>
        </div>

        <div class="col-lg-12 mtop10">
            <?php $this->load->view('admin/components/select_dnd', [
                'name' => 'requirements',
                'label' => 'ams_post_job_requirements_label',
                'nonSelectedText' => 'ams_post_job_requirements_select',
                'nonFilledText' => 'ams_post_job_requirements_placeholder',
                'options' => $taxonomies['requirements'] ?? [],
                'selectedOptions' => $job['requirements'] ?? [],
            ]); ?>
        </div>

        <!-- Recruitment process -->

        <div class="col-lg-12 mtop10">
            <div class="col-lg-12 no-padding">
                <label class="no-mbot"><b><?php echo _l('ams_post_job_process_label') ?></b></label>
                <div><?php echo _l('ams_post_job_process_description') ?></div>
            </div>
            <div class="col-lg-12 mtop10 no-padding">
                <div class="input-with-icon">
                    <input id="recruitment_process" style="padding-right: 5px;" class="form-control" type="text" placeholder="<?php echo _l('ams_post_job_process_placeholder') ?>">
                    <span class="right-icon">
                        <button type="button" disabled id="add-process" class="btn text-primary btn-link disabled add-btn"><?php echo _l('ams_post_job_btn_add') ?></button>
                        <button type="button" disabled data-edit-id="" id="edit-process" class="btn text-primary btn-link add-btn hide"><?php echo _l('ams_post_job_btn_save') ?></button>
                    </span>
                </div>
            </div>
            <div class="col-lg-12 mtop10 no-padding">
                <ul class="process-list-container <?php echo empty($job['recruitment_processes_original']) ? 'hide' : '' ?>">
                    <?php if (!empty($processes = $job['recruitment_processes_original'] ?? [])) : ?>
                        <?php foreach ($processes as $idx => $process) : ?>
                            <li class="process-items" id="item-<?php echo ($idx + 1); ?>">
                                <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <p class="bold"><?php echo _l('ams_post_job_process_round'); ?>: <span id="round"><?php echo ($idx + 1); ?></span></p>
                                    <p id="message"><?php echo $process['name'] ?></p>
                                    <input type="hidden" name="recruitment_processes_original[]" value="<?php echo $process['name'] ?>">
                                </div>
                                <div class="d-flex">
                                    <a href="javascript:;" id="edit-<?php echo ($idx + 1); ?>" class="text-muted process-edit"><i class="fa fa-lg fa-2x fa-pencil"></i></a>
                                    <a href="javascript:;" id="remove-<?php echo ($idx + 1); ?>" class="mleft10 text-muted process-remove"><i class="fa fa-lg fa-2x fa-trash"></i></a>
                                </div>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
        <div class="col-lg-12 mtop10">
            <?php $this->load->view('admin/components/select_dnd', [
                'name' => 'recruitment_processes',
                'label' => 'ams_post_job_process_label',
                'nonSelectedText' => 'ams_post_job_process_select',
                'nonFilledText' => 'ams_post_job_process_placeholder',
                'options' => $taxonomies['recruitment_processes'] ?? [],
                'selectedOptions' => $job['recruiment_process'] ?? [],
                'labelAsIndex' => 'Round',
            ]); ?>
        </div>

        <!-- Education -->
        <div class="col-lg-12 mtop10 no-padding">
            <div class="col-lg-12 mtop10">
                <?php $this->load->view('admin/components/checkbox', [
                    'name' => 'education_degree',
                    'required' => false,
                    'label' => 'ams_post_job_education_degree_label',
                    'checkboxes' => $taxonomies['education'] ?? [],
                    'checkedIds' => $job['education_degree'] ?? [],
                ]); ?>
            </div>
            <div class="col-lg-12 mtop10">
            <?= render_select('education_major[]', $taxonomies['education_major'], ['id', 'text'], 'ams_post_job_education_major_label', $job['education_major'] ?? [], ['id' => 'education_major', 'data-none-selected-text' => _l('ams_post_job_education_major_placeholder'), 'multiple' => true]); ?>
            </div>
            <div class="col-lg-12">
            <?php echo render_input('education_certificate', 'ams_post_job_education_certification_label', $job['education_certificate'] ?? '', 'text', ['placeholder' => _l('ams_post_job_education_certification_placeholder')]); ?>
            </div>
        </div>

        <!-- Benefit -->
        <div class="col-lg-12 mtop10 no-padding">
            <div class="col-lg-12">
                <div class="col-lg-12 no-padding d-flex">
                    <div class="flex-grow-1 bold"><?php echo _l('ams_post_job_salary_label') ?></div>
                    <?php
                        $currency = isset($job['currency']) ? strtolower($job['currency']) : 'vnd';
                    ?>
                    <div class="btn-group salary-types" data-toggle="buttons">
                        <label for="usd" class="btn usd <?php echo $currency == 'usd' ? 'btn-info active' : 'btn-default' ?>">
                            <input type="radio" value="USD" name="currency" <?php echo $currency == 'usd' ? 'checked' : '' ?> id="usd"> <?php echo _l('ams_post_job_salary_btn_usd'); ?>
                        </label>
                        <label for="vnd" class="btn vnd <?php echo $currency == 'vnd' ? 'btn-info active' : 'btn-default' ?>">
                            <input type="radio" value="VND" name="currency" <?php echo $currency == 'vnd' ? 'checked' : '' ?> id="vnd"> <?php echo _l('ams_post_job_salary_btn_vnd'); ?>
                        </label>
                    </div>
                </div>
                <div class="col-lg-12 mtop10 no-padding range-error">
                    <?php $isRange = (empty($job['salary']) || ($job['salary'] ?? '') == 'range'); ?>
                    <div class="d-flex align-items-center">
                        <input type="radio" class="no-mtop" <?php echo $isRange ? 'checked' : '' ?> value="range" name="salary" id="range">
                        <div class="d-flex mleft15 justify-content-between range-salary">
                            <div class="d-flex align-items-center">
                                <label for="range_from" class="control-label no-mbot"><?php echo _l('ams_post_job_from_label') ?></label>
                                <input <?php echo $isRange ? '' : 'disabled' ?> type="text" data-custom-error="range-error" id="range_from" name="range_from" class="form-control mleft5 currency-input" value="<?php echo $job['range_from'] ?? '' ?>">
                            </div>
                            <div class="d-flex mleft5 align-items-center">
                                <label for="range_to" class="control-label no-mbot"><?php echo _l('ams_post_job_to_label') ?></label>
                                <input <?php echo $isRange ? '' : 'disabled' ?> type="text" data-reffer-field="#range_from" data-custom-error="range-error" id="range_to" name="range_to" class="form-control mleft5 currency-input reffer-fields" value="<?php echo $job['range_to'] ?? '' ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-12 mtop5 no-padding">
                    <div class="d-flex align-items-center">
                        <input type="radio" class="no-mtop" <?php echo !$isRange ? 'checked' : '' ?> value="negotiable" name="salary" id="negotiable">
                        <label class="no-mbot mleft5" for="negotiable"><b><?php echo _l('ams_post_job_salary_negotiable_radio') ?></b></label>
                    </div>
                </div>
                <div class="col-lg-12 mtop5 no-padding negotiable-salary mleft30 hide"><?php echo _l('ams_post_job_salary_negotiable_desciption') ?></div>
                <div class="col-lg-12 mtop10 no-padding negotiable-salary <?php echo $isRange ? 'hide' : '' ?> negotiable-error">
                    <div class="d-flex mleft5 align-items-center">
                        <div class="d-flex mleft25 justify-content-between">
                            <div class="d-flex align-items-center">
                                <label for="negotiable_from" class="control-label no-mbot"><?php echo _l('ams_post_job_from_label') ?></label>
                                <input type="text" data-custom-error="negotiable-error" id="negotiable_from" name="negotiable_from" class="form-control mleft5 currency-input" value="<?php echo $job['negotiable_from'] ?? '' ?>">
                            </div>
                            <div class="d-flex mleft5 align-items-center">
                                <label for="negotiable_to" class="control-label no-mbot"><?php echo _l('ams_post_job_to_label') ?></label>
                                <input type="text" data-custom-error="negotiable-error" data-reffer-field="#negotiable_from" id="negotiable_to" name="negotiable_to" class="form-control mleft5 currency-input reffer-fields" value="<?php echo $job['negotiable_to'] ?? '' ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Benefit -->
            <div class="col-lg-12 mtop10">
                <?php $benefits_original = collect($job['benefits_original'] ?? [])->pluck('value')->implode('<br>') ?>
                <?php echo render_textarea('benefits_original', _l('ams_post_job_benefit_title'), $benefits_original, ['placeholder' => _l('ams_post_job_benefit_title'), 'rows' => 15], [], '', 'tinymce tinymce-manual'); ?>
            </div>
            <div class="col-lg-12 mtop10">
                <?php $this->load->view('admin/components/select_dnd', [
                    'name' => 'benefits',
                    'label' => 'ams_post_job_benefit_title',
                    'nonSelectedText' => 'ams_post_job_benefit_select',
                    'nonFilledText' => 'ams_post_job_benefit_placeholder',
                    'options' => $taxonomies['benefits'] ?? [],
                    'selectedOptions' => $job['benefits'] ?? [],
                    'labelAsIndex' => null
                ]); ?>
            </div>
        </div>

        <!-- Design -->
        <div class="col-lg-12 mtop10 no-padding">
            <!-- Template -->
            <div class="col-lg-12 mtop10">
                <?php $this->load->view('admin/components/radio', [
                    'name' => 'job_template',
                    'label' => 'ams_post_job_design_template_title',
                    'radios' => $taxonomies['job_template'] ?? [],
                    'checkedId' => $job['job_template'] ?? null,
                ]); ?>
            </div>

            <!-- Major -->
            <div class="col-lg-12 mtop10">
                <?php $this->load->view('admin/components/radio', [
                    'name' => 'job_banner',
                    'label' => 'ams_post_job_design_top_banner_title',
                    'radios' => $taxonomies['job_banner'] ?? [],
                    'checkedId' => $job['job_banner'] ?? null,
                ]); ?>
            </div>

            <!-- Color -->
            <div class="col-lg-12 mtop10">
                <?php $this->load->view('admin/components/radio', [
                    'name' => 'job_template_color',
                    'label' => 'ams_post_job_design_color_title',
                    'radios' => $taxonomies['job_template_color'] ?? [],
                    'checkedId' => $job['job_template_color'] ?? null,
                ]); ?>
            </div>

            <!-- Company tagline -->
            <div class="col-lg-12 mtop10">
                <?php echo render_input('company_tagline', 'ams_post_job_company_tagline_title', $job['company_tagline'] ?? '', 'text', ['placeholder' => _l('ams_post_job_company_tagline_placeholder')]); ?>
            </div>

            <!-- Upload company logo -->
            <div class="col-lg-12 mtop10">
                <?php if (isset($job['company_logo_url'])) : ?>
                    <img src="<?= $job['company_logo_url'] ?>" class="staff-profile-image-thumb">
                <?php endif; ?>
                <?php echo render_input('company_job_logo', 'ams_post_job_company_job_logo_title', '', 'file', ['accept' => '.png, .jpeg']); ?>
            </div>
        </div>

    </div>
    <!-- Upload file html content -->
    <div class="col-12 col-lg-6 no-padding mtop10">
        <?php echo render_input('html_file_desktop', 'ams_post_html_desktop_label', '', 'file'); ?>
        <a href="<?php echo $job['job_posting_html_desktop_url'] ?? '' ?>" target="_blank">View HTML file</a>
    </div>
    <div class="col-12 col-lg-6 no-padding-right mtop10">
        <?php echo render_input('html_file_mobile', 'ams_post_html_mobile_label', '', 'file'); ?>
        <a href="<?php echo $job['job_posting_html_mobile_url'] ?? '' ?>" target="_blank">View HTML file</a>
    </div>
    <!-- End upload file html content -->

    <div class="col-12 col-lg-12 no-padding mtop10">
        <div class="form-group">
            <label for="tags" class="control-label"><b><?php echo _l('ams_post_job_email_label'); ?></b></label>
            <input type="text" class="form-control" id="emails_cc" name="emails_cc" value="<?php echo $job['emails_cc'] ?? '' ?>">
        </div>
    </div>
    <div class="col-12 col-lg-12 no-padding">
        <?php echo render_textarea('note', 'ams_post_job_note_label', $job['employer_notes'] ?? '', ['placeholder' => _l('ams_post_job_note_placeholder'), 'rows' => 5]); ?>
    </div>
    <div class="row mtop10">
        <div class="col-lg-6 text-left">
            <?php echo _l('ams_post_job_required_notice') ?>
            <?php if (isset($job)): ?>
            <br />Preview URL: https://topdev.vn/preview-jobs/<?php echo $job['slug'] ?>-<?php echo $job['id'] ?>
            <?php endif; ?>
        </div>
        <div class="col-lg-6">
            <button type="button" id="postJobBtn" class="btn btn-info pull-right">
                <?php echo _l('ams_post_job_btn_submit'); ?>
                <i class="fa fa-spinner fa-spin hide"></i>
            </button>
            <button type="button" id="cancelJobBtn" class="btn btn-default pull-right mright5"><?php echo _l('ams_post_job_btn_cancel'); ?></button>
        </div>
    </div>
    <?php echo form_close(); ?>
<?php } ?>
<script>
    window.addEventListener("DOMContentLoaded", () => {
        var expOrders = JSON.parse('<?php echo json_encode(array_map(fn($exp) => $exp['id'], $taxonomies['experiences'])) ?>'),
            defaultPaidPackages = JSON.parse('<?php echo json_encode($paid_packages ?? []) ?>'),
            defaultInvoices = JSON.parse('<?php echo json_encode($invoices ?? []) ?>'),
            recruitmentProcessTemplate = `
                <li class="process-items" id="item-{idx}">
                    <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <p class="bold"><?php echo _l('ams_post_job_process_round'); ?>: <span id="round">{idx}</span></p>
                        <p id="message">{message}</p>
                        <input type="hidden" name="recruitment_processes_original[]" value="{message}">
                    </div>
                    <div class="d-flex">
                        <a href="javascript:;" id="edit-{idx}" class="text-muted process-edit"><i class="fa fa-lg fa-2x fa-pencil"></i></a>
                        <a href="javascript:;" id="remove-{idx}" class="mleft10 text-muted process-remove"><i class="fa fa-lg fa-2x fa-trash"></i></a>
                    </div>
                    </div>
                </li>
            `,
            selectedLocations = JSON.parse('<?php echo json_encode(!empty($job['addresses_id']) ? $job['addresses_id'] : []) ?>'),
            selectedJobCategory = '<?php echo !empty($job['job_category_job']) ? $job['job_category_job'] : '' ?>',
            selectedJobRoles = JSON.parse('<?php echo json_encode(!empty($job['job_category_id']) ? $job['job_category_id'] : []) ?>'),
            clientId = <?php echo $client_id ?? null ?>,
            crmInvoiceId = $('#crm_invoice_id').val(),
            packageId = '<?php echo $package_id ?? '' ?>',
            freePackage = <?php echo empty($free_package) ? 0 : 1 ?>;

        jQuery.validator.addMethod("validateYoeValue",
            function(yoeFrom, element) {
                var yoeTo = $('#yoe_to').val();

                if (yoeFrom && yoeTo) {
                    return expOrders.indexOf(parseInt(yoeFrom)) <= expOrders.indexOf(parseInt(yoeTo));
                }

                return true;
            },
            '<?php echo _l('ams_post_job_validate_yoe_value_invalid') ?>'
        );

        jQuery.validator.addMethod("tinyMceRequired",
            function(value, element) {
                return !!value;
            },
            'This field is required'
        );

        jQuery.validator.addMethod("requiredSalaryRange",
            function(value, element) {
                var salaryValue = $('input[name=salary]:checked').val(),
                    rangeFromValue = $('#range_from').val(),
                    rangeToValue = $('#range_to').val();

                return (salaryValue != 'range') || rangeFromValue || rangeToValue;
            },
            '<?php echo _l('ams_post_job_validate_salary_required') ?>'
        );

        jQuery.validator.addMethod("lessThanOrEqualSalaryRange",
            function(value, element) {
                var salaryValue = $('input[name=salary]:checked').val(),
                    rangeFromValue = $('#range_from').val().replaceAll(/[^\d+]/gi, ''),
                    rangeToValue = $('#range_to').val().replaceAll(/[^\d+]/gi, '');

                if (salaryValue == 'range') {
                    if (rangeFromValue && !rangeToValue) {
                        return false;
                    }

                    if (rangeFromValue && rangeToValue && parseInt(rangeFromValue) > parseInt(rangeToValue)) {
                        return false;
                    }
                }

                return true;
            },
            '<?php echo _l('ams_post_job_validate_salary_value_invalid') ?>'
        );

        jQuery.validator.addMethod("requiredSalaryNegotiable",
            function(value, element) {
                var salaryValue = $('input[name=salary]:checked').val(),
                    fromValue = $('#negotiable_from').val(),
                    toValue = $('#negotiable_to').val();

                return (salaryValue != 'negotiable') || fromValue || toValue;
            },
            '<?php echo _l('ams_post_job_validate_salary_required') ?>'
        );

        jQuery.validator.addMethod("lessThanOrEqualSalaryNegotiable",
            function(value, element) {
                var salaryValue = $('input[name=salary]:checked').val(),
                    fromValue = $('#negotiable_from').val().replaceAll(/[^\d+]/gi, ''),
                    toValue = $('#negotiable_to').val().replaceAll(/[^\d+]/gi, '');

                if (salaryValue == 'negotiable') {
                    if (fromValue && !toValue) {
                        return false;
                    }

                    if (fromValue && toValue && parseInt(fromValue) > parseInt(toValue)) {
                        return false;
                    }
                }

                return true;
            },
            '<?php echo _l('ams_post_job_validate_salary_value_invalid') ?>'
        );


        $($('#ams_post_job')).appFormValidator({
            rules: {
                company_id: 'required',
                crm_invoice_id: 'required',
                package_id: 'required',
                title: 'required',
                job_category_job: 'required',
                'job_category_id[]': 'required',
                'job_category_id': 'required',
                // requirements: 'required',
                // 'responsibilitiesIds[]': 'required',
                // 'responsibilitiesIds': 'required',
                // 'requirementsIds[]': 'required',
                // 'requirementsIds': 'required',
                // 'recruitment_processesIds[]': 'required',
                // 'recruitment_processesIds': 'required',
                // 'benefitsIds[]': 'required',
                // 'benefitsIds': 'required',
                "addresses_id[]": 'required',
                "addresses_id": 'required',
                "job_levels[]": 'required',
                "job_levels": 'required',
                "job_types[]": 'required',
                "job_types": 'required',
                "contract_type[]": 'required',
                "contract_type": 'required',
                "skills_ids[]": 'required',
                "skills_ids": 'required',
                // 'education_degree[]': 'required',
                // 'education_degree': 'required',
                // 'education_major[]': '',
                // 'education_major': '',
                salary: 'required',
                currency: 'required',
                yoe_from: {
                    required: true,
                    validateYoeValue: true,
                },
                range_from: {
                    requiredSalaryRange: true,
                    lessThanOrEqualSalaryRange: true
                },
                negotiable_from: {
                    requiredSalaryNegotiable: true,
                    lessThanOrEqualSalaryNegotiable: true
                },
                job_template: {
                    // required: true,
                },
                job_banner: {
                    // required: true,
                },
                job_template_color: {
                    // required: true,
                },
            },
            messages: {
                company_id: {
                    required: '<?= _l('ams_post_job_validate_company_required') ?>',
                },
                crm_invoice_id: {
                    required: '<?= _l('ams_post_job_validate_invoice_required') ?>',
                },
                package_id: {
                    required: '<?= _l('ams_post_job_validate_package_required') ?>',
                },
                title: {
                    required: '<?= _l('ams_post_job_validate_title_required') ?>',
                },
                job_category_job: {
                    required: '<?= _l('ams_post_job_validate_category_required') ?>',
                },
                'job_category_id[]': {
                    required: '<?= _l('ams_post_job_validate_role_required') ?>',
                },
                'responsibilitiesIds[]': {
                    required: '<?= _l('ams_post_job_validate_skill_qualify_required') ?>',
                },
                'requirementsIds[]': {
                    required: '<?= _l('ams_post_job_validate_responsibilities_required') ?>',
                },
                'recruitment_processesIds[]': {
                    required: '<?= _l('ams_post_job_validate_recruitment_processes_required') ?>',
                },
                'benefitsIds[]': {
                    required: '<?= _l('ams_post_job_validate_benefits_required') ?>',
                },
                "addresses_id[]": {
                    required: '<?= _l('ams_post_job_validate_location_required') ?>',
                },
                "job_levels[]": {
                    required: '<?= _l('ams_post_job_validate_level_required') ?>',
                },
                "job_types[]": {
                    required: '<?= _l('ams_post_job_validate_job_type_required') ?>',
                },
                "contract_type[]": {
                    required: '<?= _l('ams_post_job_validate_contract_type_required') ?>',
                },
                "skills_ids[]": {
                    required: '<?= _l('ams_post_job_validate_skill_required') ?>',
                },
                currency: {
                    required: '<?= _l('ams_post_job_validate_salary_type_required') ?>',
                },
                salary: {
                    required: '<?= _l('ams_post_job_validate_salary_required') ?>',
                },
                yoe_from: {
                    required: '<?= _l('ams_post_job_validate_yoe_required') ?>',
                },
                job_template: {
                    required: '<?= _l('ams_post_job_validate_job_template_required') ?>',
                },
                job_banner: {
                    required: '<?= _l('ams_post_job_validate_job_banner_required') ?>',
                },
                job_template_color: {
                    required: '<?= _l('ams_post_job_validate_job_template_color_required') ?>',
                }
            },
            errorPlacement: function(error, element) {
                if (element.data("custom-error")) {
                    element.closest('.' + element.data("custom-error")).append(error);
                } else {
                    element.closest('.form-group').append(error);
                }
            },
            highlight: function(element, errorClass) {
                var element = $(element);
                if (element.data("custom-error")) {
                    element.closest('.' + element.data("custom-error")).addClass(errorClass + ' has-error');
                } else {
                    element.closest('.form-group').addClass(errorClass + ' has-error');
                }
            },
            unhighlight: function(element, errorClass) {
                var element = $(element);
                if (element.data("custom-error")) {
                    element.closest('.' + element.data("custom-error")).removeClass(errorClass + ' has-error');
                } else {
                    element.closest('.form-group').removeClass(errorClass + ' has-error');
                }
            }
        });

        var settings = {
            valid_elements: '+*[*]',
            table_default_styles: {
                width: '100%'
            },
            height: 200,
            menubar: false,
            plugins: ['lists', 'paste'],
            toolbar1: 'forecolor backcolor | bold italic | alignleft alignjustify | bullist numlist',
            branding: false,
            elementpath: false,
            content_style: 'body { font-family: Roboto, Poppins, Helvetica, "sans-serif"; }',
            content_css: false,
            paste_as_text: true,
        };

        init_editor('#content', settings);
        init_editor('#responsibilities_original', settings);
        init_editor('#requirements_original', settings);
        init_editor('#benefits_original', settings);

        var pickerSettings = {
            noneSelectedText: '<?php echo _l('ams_post_job_icon_placeholder') ?>',
            dropupAuto: false,
            liveSearch: true
        };

        $('#yoe_from').selectpicker({...pickerSettings, noneSelectedText: '<?php echo _l('ams_post_job_yoe_min_placeholder') ?>'});
        $('#yoe_to').selectpicker({...pickerSettings, noneSelectedText: '<?php echo _l('ams_post_job_yoe_max_placeholder') ?>'});

        $('#emails_cc').tagit({
            allowSpaces: true,
            animate: false,
            placeholderText: '<?php echo _l('ams_post_job_email_placeholder') ?>',
            caseSensitive: false,
            autocomplete: {
                appendTo: '#inputTagsWrapper',
            },
            beforeTagAdded: function(event, ui) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (ui.tagLabel && !emailRegex.test(ui.tagLabel)) {
                    return false;
                }
            }
        });

        $('input[name=currency]').on('change', function () {
            $('div.salary-types').find('label.btn').removeClass('btn-info').addClass('btn-default');
            $(this).closest('label.btn').removeClass('btn-default').addClass('btn-info');
            $('input.currency-input').trigger('blur');
        });

        $('input[name=salary]').on('change', function () {
            var val = $(this).val();
            if (val == 'range') {
                $('.range-salary').find('input').removeAttr('disabled');
                $('.negotiable-salary').addClass('hide');
            } else {
                $('.range-salary').find('input').attr('disabled', true);
                $('.negotiable-salary').removeClass('hide');
            }
        });



        $('ul.process-list-container').on('click', 'a.process-edit', function () {
            var index = $(this).attr('id').replace('edit-', '');
            var text = $('.process-list-container').find('li#item-' + index + ' p#message').html();
            $('#recruitment_process').val(text);
            $('#recruitment_process').parent().find('button#add-process').addClass('hide');
            $('#recruitment_process').parent().find('button#edit-process')
                .data('edit-id', index)
                .removeClass('hide')
                .removeAttr('disabled');
        });

        $('ul.process-list-container').on('click', 'a.process-remove', function () {
            var index = $(this).attr('id').replace('remove-', '');
            $('.process-list-container').find('li#item-' + index).remove();
            // Re-index round
            var list = $('.process-list-container li');
            if (list.length) {
                list.each(function (idx, li) {
                    $(li).attr('id', 'item-' + (idx + 1));
                    $(li).find('span#round').text((idx + 1));
                    $(li).find('a.process-edit').attr('id', 'edit-' + (idx + 1));
                    $(li).find('a.process-remove').attr('id', 'remove-' + (idx + 1));
                });
            } else {
                $('.process-list-container').addClass('hide');
            }
        });

        $('button#edit-process').on('click', function () {
            var processList = $('.process-list-container'),
                editIndex = $(this).data('edit-id');

            processList.find('li#item-'+editIndex+' p#message').html($('#recruitment_process').val());
            processList.find('li#item-'+editIndex+' input').val($('#recruitment_process').val());
            $('#recruitment_process').val('');
            $('button#add-process, button#edit-process').attr('disabled', true).addClass('disabled');
            $('#recruitment_process').parent().find('button#edit-process').addClass('hide');
            $('#recruitment_process').parent().find('button#add-process').removeClass('hide');
        });

        $('button#add-process').on('click', function () {
            var processList = $('.process-list-container'),
                itemIndex = processList.find('li').length + 1;

            if (itemIndex > 8) {
                return false;
            }

            if (processList.hasClass('hide')) {
                processList.removeClass('hide');
            }

            // add items
            var template = recruitmentProcessTemplate
                .replaceAll('{round}', itemIndex)
                .replaceAll('{idx}', itemIndex)
                .replaceAll('{message}', $('#recruitment_process').val());

            processList.append(template);
            $('#recruitment_process').val('');
            $('button#add-process').attr('disabled', true).addClass('disabled');
        });

        $('#recruitment_process').on('keyup', function () {
            var val = $(this).val();
            if (val && val.trim()) {
                $('button#add-process, button#edit-process').removeAttr('disabled').removeClass('disabled');
            } else {
                $('button#add-process, button#edit-process').attr('disabled', true).addClass('disabled');
            }
        });

        $('#company_id').on('changed.bs.select', function (evt) {
            fetchRelationSelect(this, '#addresses_id', null, [], 'ams/get_ams_locations/' + $(this).val());
        });

        $('#job_category_job').on('changed.bs.select', function (evt) {
            fetchRelationSelect(this, '#job_category_id', null, [], 'ams/get_ams_job_roles/' + $(this).val());
        });

        var ignoreSelectInvoice = false,
            ignoreSelectPackage = false;

        $('#crm_invoice_id').on('changed.bs.select', function (evt) {

            if (ignoreSelectInvoice) {
                ignoreSelectInvoice = false;
                return;
            }

            ignoreSelectPackage = true;
            var selectedValue = $(this).val();
            fetchRelationSelect(this, '#package_id', $('#package_id').val(), defaultPaidPackages, 'invoice_items/get_packages/' + selectedValue + '/' + (($('#free_package').is(':checked') ? 1 : 0) + '/' + (selectedValue == crmInvoiceId ? packageId : null)));
        });

        $('#package_id').on('changed.bs.select', function (evt) {

            if (ignoreSelectPackage) {
                ignoreSelectPackage = false;
                return;
            }

            var selectedValue = $(this).val(),
            freePackage = $('#free_package').is(':checked') ? 1 : 0;
            ignoreSelectInvoice = true;
            fetchRelationSelect(this, '#crm_invoice_id', $('#crm_invoice_id').val(), [], 'invoices/get_invoices/' + clientId + '/' + freePackage + '/0/' + selectedValue);
        });

        $('#free_package').on('change', function (evt) {
            fetchRelationSelect('#free_package', '#crm_invoice_id', crmInvoiceId, [], 'invoices/get_invoices/' + clientId + '/' + ($(this).is(':checked') ? 1 : 0));
        });

        $('input.currency-input').on('keydown', function (evt) {
            if ([0,8,9,35,36,37,38,39,40,46].indexOf(evt.which) > -1 ||
                (evt.which >= 96 && evt.which <= 105) ||
                /[0-9]/.test(String.fromCharCode(evt.which))) { // numbers
                return;
            }
            evt.preventDefault();
        });

        $('input.currency-input').on('focus', function () {
            var currentValue = $(this).val();
            if (currentValue) {
                $(this).val(currentValue.replaceAll(/[^\d+]/gi, ''));
            }
        });

        $('input.currency-input').on('blur', function () {
            var currentValue = $(this).val(),
                currencyValue = $('input[name=currency]:checked').val();

            if (currentValue) {
                currentValue = currentValue.replaceAll(/[^\d+]/gi, '');
                var formattedValue = new Intl.NumberFormat().format(currentValue);
                if (currencyValue == 'USD') {
                    formattedValue = '$' + formattedValue;
                } else {
                    formattedValue = formattedValue + ' đ';
                }
                $(this).val(formattedValue);
            }
        });

        $('#ams_post_job').on('change', 'input, select, textarea', function () {
            $('#ams_post_job').validate().element(this);
        });

        $('#ams_post_job .reffer-fields').on('keyup', function () {
            var validateEl = $('#ams_post_job').find($(this).data('reffer-field')) ?? null;
            validateEl && $('#ams_post_job').validate().element(validateEl);
        });

        $('#postJobBtn').on('click', function () {
            // Trigger to sync text editor to textarea
            tinymce.triggerSave();

            var postJobForm = $('#ams_post_job'),
                validator = postJobForm.validate(),
                isEdit = $('input#client_ams_job_id').val(),
                isAmsJob = $('input#ams_job_id').val();

            if (postJobForm.valid()) {
                $('#postJobBtn').attr('disabled', true).find('i.fa-spinner').removeClass('hide');
                $('input.currency-input').each((idx, el) => $(el).val($(el).val().replaceAll(/[^\d+]/gi, '')));
                var data = new FormData(postJobForm[0]);
                $('input.currency-input').trigger('blur');
                $.ajax({
                    url: postJobForm.attr('action'),
                    method: 'POST',
                    data,
                    processData: false, // Prevent jQuery from automatically transforming the data into a query string
                    contentType: false,
                    success: function (response) {
                        validator.resetForm();
                        if (response.success) {
                            $('#ams_post_job').removeClass('dirty');
                            if (!isEdit) {
                                // Change url, form action, hidden fields
                                $('#ams_post_job').attr('action', postJobForm.attr('action') + '/' + response.data.id);
                                $('input#client_ams_job_id').val(response.data.id);
                                if (isAmsJob) {
                                    alert_float('info', '<?php echo _l('ams_post_job_submit_updated_success') ?>');
                                } else {
                                    alert_float('info', '<?php echo _l('ams_post_job_submit_review_success') ?>');
                                }
                            } else {
                                alert_float('info', '<?php echo _l('ams_post_job_submit_updated_success') ?>');
                            }
                            setTimeout(() => window.location.href = window.location.origin + window.location.pathname + '?group=ams_jobs', 200);
                        } else {
                            if (response.data && response.data.validation_errors) {
                                var fields = $('#ams_post_job').find('input,select,textarea').map((idx, el) => el.name).toArray(),
                                    errors = response.data.validation_errors,
                                    newErrors = {};

                                for (var key in errors) {
                                    if (fields.indexOf(key)) {
                                        newErrors[key] = errors[key];
                                    }
                                }
                                // To make sure none fields are set in the error
                                validator.showErrors(newErrors);
                            } else {
                                alert_float('danger', response.message ?? 'Unknowned error!');
                            }
                        }
                    },
                    error: function(xhr, resp, text) {
                        alert_float('danger', 'Unknowned error!');
                    },
                    complete: function () {
                        $('#postJobBtn').removeAttr('disabled').find('i.fa-spinner').addClass('hide');
                    }
                });
            } else {
                alert_float('warning', 'Please check validation errors!');
            }
        });

        $('#cancelJobBtn').on('click', function () {
            if ($('#ams_post_job').hasClass('dirty')) {
                var action = confirm('<?= _l('ams_post_job_cancel_confirmation') ?>')
                if (action) {
                    $('#ams_post_job').removeClass('dirty');
                    window.location.href = window.location.origin + window.location.pathname + '?group=ams_jobs';
                }
            } else {
                window.location.href = window.location.origin + window.location.pathname + '?group=ams_jobs';
            }
        });

        // Auto select previous locations when editing.
        selectedLocations && fetchRelationSelect('#company_id', '#addresses_id', selectedLocations, [], 'ams/get_ams_locations/' + $('#company_id').val());
        crmInvoiceId && fetchRelationSelect('#crm_invoice_id', '#package_id', packageId, [], 'invoice_items/get_packages/' + crmInvoiceId + '/' + freePackage + '/' + packageId);
        selectedJobCategory && fetchRelationSelect('#job_category_job', '#job_category_id', selectedJobRoles, [], 'ams/get_ams_job_roles/' + $('#job_category_job').val());
        // Apply number format for fields
        $('input.currency-input').trigger('blur');
    });
</script>

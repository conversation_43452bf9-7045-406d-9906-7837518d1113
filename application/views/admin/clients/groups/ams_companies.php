<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php if (isset($client)) { ?>
    <h4 class="customer-profile-group-heading"><?php echo _l('ams_companies_title'); ?></h4>
    <div class="col-md-12 no-padding">
        <!-- TMP disable this feature until have final decision
            <button <?php echo allow_sync_ams_ids($client->userid) ? '' : 'disabled title="' . _l('ams_not_valid_sync_rules') . '"'; ?> id="sync_ams_company_btn" class="btn btn-info mbot10">
            <?php echo _l('ams_companies_sync_btn'); ?>
            <i class="fa fa-spinner fa-spin hide"></i>
            </button>
        -->
        <div class="col-md-12 d-flex no-padding">
            <div class="flex-column text-left mtop10 bold"><?php echo _l('ams_filter_title') ?>:</div>
            <div class="col-md-12 flex-grow-1 mleft5 no-padding-right no-mbot">
                <div class="col-md-4" style="padding-left: 0">
                    <?= render_select('company', $ams_companies ?? [], ['id', 'name'], null, '', ['data-none-selected-text' => _l('ams_companies_placeholder_company_name')], [], '', 'table-filters'); ?>
                </div>
                <div class="col-md-4" style="padding-left: 0">
                    <?= render_select('status', ams_get_statuses(), ['id', 'text'], null, '', ['data-none-selected-text' => _l('ams_companies_placeholder_status')], [], '', 'table-filters'); ?>
                </div>
                <div class="col-md-4" style="padding-left: 0">
                    <?= render_select('service', $taxonomies['services'] ?? [], ['id', 'text'], null, '', ['data-none-selected-text' => _l('ams_companies_placeholder_features')], [], '', 'table-filters'); ?>
                </div>
            </div>
        </div>
        <div class="col-md-12 no-padding">
            <?php render_datatable([
                _l('ams_companies_column_id_ams'),
                _l('ams_companies_column_company'),
                _l('ams_companies_column_employer'),
                _l('ams_companies_column_additional_info'),
                _l('ams_companies_column_status'),
            ], 'ams-companies'); ?>
        </div>
    </div>
<?php } ?>

<div class="modal modal-center fade" id="sync_ams_company_modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title text-uppercase"><?php echo _l('ams_companies_sync_btn'); ?></h4>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('settings_no'); ?></button>
                <button type="button" data-ids="" id="sync_ids" class="btn btn-info">
                    <?php echo _l('settings_yes'); ?>
                    <i class="fa fa-spinner fa-spin hide"></i>
                </button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>

<style>
    table.toggle {
        height: 0 !important;
        transition: height 0.3s;
    }

    table.toggle.expand {
        height: unset !important
    }

    i.expand-btn {
        transform: rotate(90deg);
        transition: transform 0.3s;
    }

    i.expanded {
        transform: rotate(0);
    }
</style>

<script>
    var tAPI;
    window.addEventListener("DOMContentLoaded", () => {
        // Init datatables
        var customSearchParams = {};
        $.each($('._hidden_inputs._filters input'), function() {
            customSearchParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });
        $('.table-filters').each((idx, el) => customSearchParams[$(el).attr('name')] = '[name="' + $(el).attr('name') + '"]');

        tAPI = initDataTable('.table-ams-companies', admin_url + 'ams/companies/' + <?php echo $client->userid ?>, [0, 1, 2, 3, 4], [0, 1, 2, 3, 4], customSearchParams, [0, 'desc'], {
            tables_pagination_limit: 10,
            displayLengthMenu: false,
            searching: false,
        });
        var timer;
        $('.table-filters').on('changed.bs.select', function () {
            clearTimeout(timer);
            timer = setTimeout(() => tAPI.ajax.reload(), 100);
        });

        /* TMP disable this feature until have final decision
        $('#sync_ams_company_btn').on('click', function() {
            var btn = $(this);
            btn.attr('disabled', true);
            btn.find('i.fa-spinner').removeClass('hide');
            $.ajax({
                type: 'get',
                dataType: 'json',
                url: admin_url + 'clients/check_sync_company/<?php echo $client->userid ?? 0 ?>',
                success: function(response) {
                    if (response.success) {
                        $('#sync_ams_company_modal').find('.modal-body').html(response.message);
                        $('#sync_ams_company_modal').find('#sync_ids').data('ids', response.data.ids)
                        $('#sync_ams_company_modal').modal({
                            show: true,
                            backdrop: 'static'
                        });
                    } else {
                        alert_float('danger', response.message);
                    }
                },
                error: function(response) {
                    alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
                },
                complete: function() {
                    btn.attr('disabled', false);
                    btn.find('i.fa-spinner').addClass('hide');
                }
            });
        });
        */

        $('#sync_ids').on('click', function() {
            var btn = $(this);
            btn.attr('disabled', true);
            btn.find('i.fa-spinner').removeClass('hide');
            $.ajax({
                type: 'post',
                dataType: 'json',
                data: {
                    ids: btn.data('ids')
                },
                url: admin_url + 'clients/sync_ams_ids/<?php echo $client->userid ?? 0 ?>',
                success: function(response) {
                    alert_float(response.success ? 'info' : 'danger', response.message);
                    $('#sync_ams_company_modal').modal('hide');
                    toggleSyncBtn();
                },
                error: function(response) {
                    alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
                },
                complete: function() {
                    btn.attr('disabled', false);
                    btn.find('i.fa-spinner').addClass('hide');
                }
            })
        });

        function toggleSyncBtn() {
            $.ajax({
                type: 'get',
                dataType: 'json',
                url: admin_url + 'clients/check_sync_company/<?php echo $client->userid ?? 0 ?>/1',
                success: function(response) {
                    $('#sync_ams_company_btn').attr('disabled', response.success ? false : true);
                    tAPI.ajax.reload();
                },
                error: function(response) {
                    $('#sync_ams_company_btn').attr('disabled', true);
                },
            });
        }
    });

    function amsRowFormat(services) {
        return (
            `<table class="table no-mtop no-mbot toggle">` +
            `<thead>` +
            `<tr>` +
            `<th><?php echo _l('ams_companies_row_service_no') ?></th>` +
            `<th><?php echo _l('ams_companies_row_service_name') ?></th>` +
            `<th><?php echo _l('ams_companies_row_service_activated_at') ?></th>` +
            `<th><?php echo _l('ams_companies_row_service_expired_at') ?></th>` +
            `</tr>` +
            `<thead>` +
            `<tbody>` +
            services.map((service, index) => {
                return `<tr>` +
                    `<td>` + (index + 1) + '</td>' +
                    `<td>` + service.name + '</td>' +
                    `<td>` + service.date + '</td>' +
                    `<td>` + service.expired_in + '</td>' +
                    `</tr>`;
            }).join('') +
            `</tbody>` +
            `</table>`
        );
    }

    function toggleServiceDetails(aTag) {
        var tr = aTag.closest('tr'),
            row = tAPI.row(tr);

        if (row.child && row.child.isShown()) {
            row.child.hide();
        } else {
            row.child(amsRowFormat(row.data().services ?? [])).show();
        }
        $(aTag).find('i').toggleClass('expanded');
    }
</script>
<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<style>
    .summary-section {
        background-color: #ECF2FF;
        border-radius: 15px;
    }

    .summary-section .title {
        margin-top: 2.5rem;
    }

    .summary-section .number {
        background-color: #FFF;
        border-radius: 15px;
        margin-right: 5px;
    }

    .summary-section .number .panel-body {
        background-color: #FFF;
        border-radius: 15px;
    }

    .table-ams-jobs-invoices tbody tr td:not(:first-child) {
        text-align: center !important;
    }
    .table-ams-jobs-invoices tbody tr td {
        vertical-align: middle;
    }
    .used-editing input.form-control {
        height: 30px !important;
    }
    .invoice-packages tr:not(:last-child) {
        border-bottom: 1px solid #ebf5ff !important;
    }
</style>
<?php if (isset($client)) { ?>
    <h4 class="customer-profile-group-heading"><?php echo _l('ams_job_manage_title'); ?></h4>
    <div class="col-md-12 no-padding">
        <div class="col-md-12 no-padding">
            <a href="<?php echo admin_url('clients/client/' . $client->userid . '?group=ams_post_job') ?>" id="sync_ams_company_btn" class="btn btn-info mbot10 <?php echo !$synced_already ? 'disabled' : '' ?>">
                <?php echo _l('ams_post_job_title'); ?>
            </a>
            <button type="button" class="btn btn-success mbot10" id="add-free-quota-btn">
                <i class="fa fa-plus"></i> <?php echo _l('add_free_quota'); ?>
            </button>
            <div class="col-md-12 d-flex no-padding">
                <div class="flex-column text-left bold"><?php echo _l('ams_filter_title') ?>:</div>
                <div class="col-md-12 flex-grow-1 mleft5 no-padding-right no-mbot">
                    <div class="col-md-4" style="padding-left: 0">
                        <?= render_select('company', $ams_companies ?? [], ['id', 'name'], null, '', ['data-none-selected-text' => _l('ams_companies_placeholder_ams_company_name')], [], '', 'table-filters'); ?>
                    </div>
                    <div class="col-md-4" style="padding-left: 0">
                        <?= render_select('crm_company', $crm_companies ?? [], ['userid', 'company'], null, '', ['data-none-selected-text' => _l('ams_companies_placeholder_crm_company_name')], [], '', 'table-filters'); ?>
                    </div>
                    <div class="col-md-4" style="padding-left: 0">
                        <?= render_select('status', ams_get_job_statuses(), ['id', 'text'], null, '', ['data-none-selected-text' => _l('ams_companies_placeholder_status')], [], '', 'table-filters'); ?>
                    </div>
                    <div class="col-md-4" style="padding-left: 0">
                        <?= render_select('package', $taxonomies['packages'] ?? [], ['id', 'text'], null, '', ['data-none-selected-text' => _l('ams_jobs_placeholder_packages')], [], '', 'table-filters'); ?>
                    </div>
                    <div class="col-md-4" style="padding-left: 0">
                        <?= render_select('service', $taxonomies['services'] ?? [], ['id', 'text'], null, '', ['data-none-selected-text' => _l('ams_companies_placeholder_features')], [], '', 'table-filters'); ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 no-padding">
            <div class="panel_s">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h4 class="bold"><?php echo _l('free_quota_summary'); ?></h4>
                            <div class="row">
                                <div class="col-md-12">
                                    <p><?php echo _l('total_free_quota'); ?>: <span id="total-free-quota">0</span></p>
                                    <p><?php echo _l('used_free_quota'); ?>: <span id="used-free-quota">0</span></p>
                                    <p><?php echo _l('remaining_free_quota'); ?>: <span id="remaining-free-quota">0</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-12 no-padding job-invoices">
            <h4 class="bold"><?php echo _l('ams_job_manage_invoice_table_title'); ?></h4>
            <div class="">
                <table class="dt-table-loading table table-ams-jobs-invoices">
                    <thead>
                        <tr>
                            <th class="text-center" width="13%" rowspan="2"><?= _l('ams_job_manage_invoice_th_invoice_id') ?></th>
                            <th class="text-center" width="8%" rowspan="2"><?= _l('ams_job_manage_invoice_th_paid_packages') ?></th>
                            <th class="text-center" width="8%" rowspan="2"><?= _l('ams_job_manage_invoice_th_used_packages') ?></th>
                            <th class="text-center" width="8%" rowspan="2"><?= _l('ams_job_manage_invoice_th_remain_packages') ?></th>
                            <th class="text-center" width="63%" colspan="5"><?= _l('ams_job_manage_invoice_th_invoice_detail') ?></th>
                        </tr>
                        <tr>
                            <th width="12.6%" class="text-center"><?= _l('ams_job_manage_invoice_th_invoice_packacge_name') ?></th>
                            <th width="9.45%" class="text-center"><?= _l('ams_job_manage_invoice_th_invoice_paid_packages') ?></th>
                            <th width="9.45%" class="text-center"><?= _l('ams_job_manage_invoice_th_invoice_used_packages') ?></th>
                            <th width="9.45%" class="text-center"><?= _l('ams_job_manage_invoice_th_invoice_remain_packages') ?></th>
                            <th width="12.6%" class="text-center" ><?= _l('ams_job_manage_invoice_th_expired_at') ?></th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="col-md-12 no-padding">
                <div class="col-md-12 no-padding">
                    <div class="col-md-2 no-padding"><?php echo _l('ams_job_manage_invoice_total_paid_invoices') ?></div>
                    <div class="col-md-10 no-padding" id="paid-invoices">_</div>
                </div>
                <div class="col-md-12 no-padding">
                    <div class="col-md-2 no-padding"><?php echo _l('ams_job_manage_invoice_total_paid_packages') ?></div>
                    <div class="col-md-10 no-padding" id="paid-packages">_</div>
                </div>
                <div class="col-md-12 no-padding">
                    <div class="col-md-2 no-padding"><?php echo _l('ams_job_manage_invoice_total_used_packages') ?></div>
                    <div class="col-md-10 no-padding" id="used-packages">_</div>
                </div>
                <div class="col-md-12 no-padding">
                    <div class="col-md-2 no-padding"><?php echo _l('ams_job_manage_invoice_total_remain_packages') ?></div>
                    <div class="col-md-10 no-padding" id="remain-packages">_</div>
                </div>
            </div>
        </div>
        <hr style="width: 100%">
        <div class="col-md-12 no-padding">
            <h4 class="bold"><?php echo _l('ams_job_manage_job_table_title'); ?></h4>
            <?php render_datatable([
                _l('ams_jobs_column_crm_invoice'),
                _l('ams_jobs_column_crm_status'),
                _l('ams_companies_column_id_ams'),
                _l('ams_jobs_column_job'),
                _l('ams_jobs_column_package'),
                _l('ams_jobs_column_service'),
                _l('ams_jobs_column_view_avg'),
                _l('ams_jobs_column_click_apply'),
                _l('ams_jobs_column_total_ready_cv'),
                [
                    'name' => _l('ams_jobs_column_published_date'),
                    'th_attrs' => array('class' => 'not-export')
                ],
                [
                    'name' => _l('ams_jobs_row_published_date'),
                    'th_attrs' => array('class' => 'not_visible')
                ],
                [
                    'name' => _l('ams_jobs_row_expired_date'),
                    'th_attrs' => array('class' => 'not_visible')
                ],
                [
                    'name' => _l('ams_jobs_row_created_by'),
                    'th_attrs' => array('class' => 'not_visible')
                ],
                _l('ams_jobs_column_status'),
                [
                    'name' => _l('ams_jobs_column_action'),
                    'th_attrs' => array('class' => 'not-export')
                ],
            ], 'ams-jobs'); ?>
        </div>
    </div>
<?php } ?>

<!-- Add Free Quota Modal -->
<div class="modal fade" id="addFreeQuotaModal" tabindex="-1" role="dialog" aria-labelledby="addFreeQuotaModalLabel">
    <div class="modal-dialog" role="document">
        <?php echo form_open(admin_url('ams/add_free_quota/' . $client->userid), ['id' => 'add-free-quota-form']); ?>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="addFreeQuotaModalLabel"><?php echo _l('add_free_quota'); ?></h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="free_quota_number" class="control-label"><?php echo _l('item_quantity_placeholder'); ?></label>
                    <input type="number" class="form-control" id="free_quota_number" name="free_quota_number" min="1" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                <button type="submit" class="btn btn-primary"><?php echo _l('submit'); ?></button>
            </div>
        </div>
        <?php echo form_close(); ?>
    </div>
</div>

<script>
    window.addEventListener("DOMContentLoaded", () => {
        var clientId = <?php echo $client->userid ?>;
        init_ajax_search('crm_company', '#crm_company', undefined, admin_url + 'ams/ams_autocomplete/' + clientId);

        // Load free quota summary
        function loadFreeQuotaSummary() {
            $.get(admin_url + 'ams/get_free_quota/' + clientId, function(res) {
                    $('#total-free-quota').text(res.data.free_quota || 0);
                    $('#used-free-quota').text(res.data.free_quota_used || 0);
                    $('#remaining-free-quota').text(res.data.free_quota_remaining || 0);

            }, 'json');
        }
        
        // Load initial data
        loadFreeQuotaSummary();
        
        // Handle add free quota button click
        $('#add-free-quota-btn').on('click', function() {
            $('#addFreeQuotaModal').modal('show');
        });
        
        // Handle add free quota form submission
        $('#add-free-quota-form').on('submit', function(e) {
            e.preventDefault();
            var form = $(this);
            var submitButton = form.find('button[type="submit"]');
            var originalButtonText = submitButton.html();
            
            submitButton.html('<i class="fa fa-spinner fa-spin"></i> <?php echo _l('processing'); ?>');
            
            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert_float('success', response.message);
                        $('#addFreeQuotaModal').modal('hide');
                        form[0].reset();
                        loadFreeQuotaSummary();
                    } else {
                        alert_float('danger', response.message || '<?php echo _l('error_occurred'); ?>');
                    }
                },
                error: function() {
                    alert_float('danger', '<?php echo _l('error_occurred'); ?>');
                },
                complete: function() {
                    submitButton.html(originalButtonText);
                }
            });
        });
        
        // Init datatables
        var customSearchParams = {};
        $.each($('._hidden_inputs._filters input'), function() {
            customSearchParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });
        $('.table-filters').each((idx, el) => customSearchParams[$(el).attr('name')] = '[name="' + $(el).attr('name') + '"]');
        var tAPI = initDataTable('.table-ams-jobs', admin_url + 'ams/jobs/<?php echo $client->userid ?>', [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], customSearchParams, [0, 'desc'], {
            displayLengthMenu: true,
            pageLength: 20,
            lengthMenu: [10, 20],
            searching: false,
        });
        var tInvoiceAPI = initDataTable('.table-ams-jobs-invoices', admin_url + 'ams/job_invoices/<?php echo $client->userid ?>', [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], customSearchParams, [], {
            tables_pagination_limit: 10,
            displayLengthMenu: false,
            buttons: [{
                text: '<i class="fa fa-refresh"></i>',
                className: 'btn btn-default-dt-options btn-dt-reload',
                action: function(e, dt, node, config) {
                    dt.ajax.reload();
                    doFetchJobSummary();
                }
            }],
            searching: false,
            createdRow: makeRowable
        });
        var timer;
        $('.table-filters').on('changed.bs.select', function(evt) {
            // To avoid execute multiple times
            clearTimeout(timer);
            timer = setTimeout(() => {
                tAPI.ajax.reload();
                tInvoiceAPI.ajax.reload();
                if (['company', 'crm_company'].indexOf(evt.target.id) > -1) {
                    doFetchJobSummary();
                }
            }, 100);
        });

        <?php if (has_permission('invoices', '', 'edit_use_expired_at')) : ?>
            $('.table-ams-jobs-invoices').on('click', 'button.update-btn', onUpdateExpiredBtnClick);
            $('.table-ams-jobs-invoices').on('click', 'button.save-btn', onSaveExpiredBtnClick);
            $('.table-ams-jobs-invoices').on('click', 'button.cancel-btn', onCancelExpiredBtnClick);
        <?php endif; ?>

        function doFetchJobSummary() {
            var selectedClientId = $('#crm_company').val(),
                selectedAmsId = $('#company').val();

            if (!selectedClientId && !selectedAmsId) {
                selectedClientId = clientId;
            }

            fetchSummary(selectedClientId ? selectedClientId : -1, selectedAmsId ? selectedAmsId : -1);
        }

        function onUpdateExpiredBtnClick() {
            var tr = $(this).closest('tr');
            // Set original expired
            tr.find('span#expired_at_text').data('expired-at', tr.find('input#expired_at').val());
            toggleEditing(tr, true);
        }

        function onSaveExpiredBtnClick() {
            var tr = $(this).closest('tr'),
                itemableId = tr.find('span#expired_at').data('id'),
                datepicker = tr.find('input#expired_at'),
                expired_at = datepicker.val(),
                btn = tr.find('button.edit-comp');

            if (!expired_at) {
                var group = datepicker.closest('.form-group');
                group.addClass('has-error');
                group.append('<p class="text-danger"><?= _l('ams_job_manage_invoice_expired_at_required') ?></p>');
                return;
            }

            btn.attr('disabled', true);
            btn.find('i.fa-spinner').removeClass('hide');
            $.ajax({
                type: 'post',
                dataType: 'json',
                data: {
                    expired_at
                },
                url: admin_url + 'invoice_items/update_use_expired_at/' + itemableId,
                success: function(response) {
                    if (response.success) {
                        if (response.data && response.data.updated) {
                            alert_float('info', response.message);
                            tr.find('span#expired_at_text').data('expired-at', tr.find('input#expired_at').val());
                        } else {
                            alert_float('warning', response.message);
                        }
                        // Set latest value when saved
                        toggleEditing(tr, false);
                        doFetchJobSummary();
                    } else {
                        alert_float('danger', response.message);
                    }
                },
                error: function(response) {
                    alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
                },
                complete: function() {
                    btn.attr('disabled', false);
                    btn.find('i.fa-spinner').addClass('hide');
                }
            });
        }

        function onCancelExpiredBtnClick() {
            var tr = $(this).closest('tr');
            toggleEditing(tr, false);
        }

        function toggleEditing(tr, isEditing) {
            var datepicker = tr.find('input#expired_at'),
                span = tr.find('span#expired_at_text'),
                expiredAt = span.data('expired-at');

            if (isEditing) {
                var opt = {
                    timepicker: false,
                    scrollInput: false,
                    lazyInit: true,
                    format: 'd/m/Y',
                    formatDate: 'd/m/Y',
                    onSelectDate: function(ct, picker) {
                        picker.closest('.form-group').removeClass('has-error');
                        picker.closest('.form-group').find('p.text-danger').remove();
                    }
                };
                // Init the picker
                datepicker.datetimepicker(opt);
                datepicker.parents('.form-group').find('.calendar-icon').on('click', function() {
                    datepicker.focus();
                    datepicker.trigger('open.xdsoft');
                });
                tr.find('.view-comp').addClass('hide');
                tr.find('.edit-comp').removeClass('hide');
            } else {
                span.text(expiredAt);
                datepicker.val(expiredAt);
                tr.find('.view-comp').removeClass('hide');
                tr.find('.edit-comp').addClass('hide');
            }
        }


        function fetchSummary(clientId, amsId) {
            $.get(admin_url + 'ams/job_summary/' + clientId + '/' + amsId, function(res) {
                if (res.success) {
                    var data = res.data;
                    $('#paid-invoices').html(data.paid_invoices + (data.paid_invoices > 1 ? ' <?= _l('ams_job_manage_invoice_total_invoices') ?>' : ' <?= _l('ams_job_manage_invoice_total_invoice') ?>'));
                    $('#paid-packages').html(data.paid_packages + (data.paid_packages > 1 ? ' <?= _l('ams_job_manage_invoice_total_posts') ?>' : ' <?= _l('ams_job_manage_invoice_total_post') ?>'));
                    $('#used-packages').html(data.used_packages + (data.used_packages > 1 ? ' <?= _l('ams_job_manage_invoice_total_posts') ?>' : ' <?= _l('ams_job_manage_invoice_total_post') ?>'));
                    $('#remain-packages').html(data.remain_packages + (data.remain_packages > 1 ? ' <?= _l('ams_job_manage_invoice_total_posts') ?>' : ' <?= _l('ams_job_manage_invoice_total_post') ?>'));
                }
            }, 'json');
        }

        function makeRowable(row, data, index, cols) {
            var invoiceCol = $(cols[5]),
                items = data._aData ?? [],
                hasPermission = <?php echo has_permission('invoices', '', 'edit_use_expired_at') ? 1 : 0 ?>;

            invoiceCol.attr('colspan', 6);
            $(cols[4]).addClass('hide');
            $(cols[6]).addClass('hide');
            $(cols[7]).addClass('hide');
            $(cols[8]).addClass('hide');
            $(cols[9]).addClass('hide');

            invoiceCol.html(`
                <table class="invoice-packages" width="100%" height="100%">
                    ${items.map(function (item) {
                        return `
                            <tr style="width:100%;">
                                <td style="width: 20%; text-align: left">${item.name}</td>
                                <td style="width: 15%">${item.paid_packages}</td>
                                <td style="width: 15%">
                                    <span class="used-display">
                                        <a href="javascript:;" title="<?= _l('total_used_packages') ?>: ${item.used_packages}\n<?= _l('total_used_input_packages') ?>: ${item.used_input_packages ?? 0}">
                                            ${item.used_packages}${item.used_input_packages ? '(' + item.used_input_packages + ')' : ''}
                                        </a>
                                    </span>
                                    <span class="used-editing hide">
                                        <div class="form-group">
                                            <div class="input-group mbot5">
                                                <input type="text"
                                                    data-value="${item.used_packages}"
                                                    data-invoice-id="${item.invoice_id}"
                                                    data-itemable-id="${item.itemable_id}"
                                                    data-used-by-packages="${item.used_by_packages}"
                                                    id="used_manual"
                                                    name="used_manual"
                                                    value="${item.used_packages}"
                                                    class="form-control">
                                                <div class="input-group-btn ">
                                                    <button type="button" class="btn btn-xs btn-info save-used">
                                                        <i class="fa fa-check"></i>
                                                        <i class="fa fa-spinner fa-spin hide"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-xs btn-danger cancel-used"><i class="fa fa-close"></i></button>
                                                </div>
                                            </div>
                                        </div>
                                    </span>
                                </td>
                                <td style="width: 10%">${item.remain_packages}</td>
                                <td style="width: 20%" class="text-center">
                                    <span id="expired_at" data-id="${item.itemable_id}">
                                        <span class="view-comp" id="expired_at_text">${item.use_expired_at}</span>
                                    </span>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </table>
            `)
        }

        $('.table-ams-jobs-invoices').on('focus', 'input[name=used_manual]', function () {$(this).select();});

        fetchSummary(<?php echo $client->userid ?>, -1);
    });
</script>

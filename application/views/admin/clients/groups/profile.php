<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php $this->load->view('admin/clients/modals/request_note.php'); ?>
<div class="customer-profile-group-heading d-flex">
    <h4 class="flex-column"><?php echo _l('client_add_edit_profile'); ?></h4>
    <div class="flex-grow-1 text-right">
        <?php if (
            isset($client_request)
            && empty($client_request['approved_at'])
            && ((has_permission('customers', '', 'approve_client_creating_request_global') && empty($client_request['admin_approved_id'])) ||
                (is_sales_leader() && has_permission('customers', '', 'approve_client_creating_request_own') && isset($client_request['admin_approved_id']) && empty($client_request['leader_approved_id'])))
        ) { ?>
            <div class="btn-group request-approve-btn">
                <button type="button" class="btn btn-default dropdown-toggle request-action" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fa fa-spinner fa-spin hide" style="margin-right: 2px;"></i><?php echo _l('client_request_waiting_status'); ?>&nbsp;<span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a onclick="changeRequestStatus(<?php echo $client_request['request_id'] ?>,'<?php echo $client_request['sale_name']; ?>', 'reject', true)" href="javascript:;"><?php echo _l('client_request_rejected_status') ?></a></li>
                    <li><a onclick="changeRequestStatus(<?php echo $client_request['request_id'] ?>,'<?php echo $client_request['sale_name']; ?>', 'approve', true)" href="javascript:;"><?php echo _l('client_request_approved_status') ?></a></li>
                </ul>
            </div>
        <?php } elseif (isset($client_request)) { ?>
            <button class="btn btn-default"><?php echo _l('client_request_' . $client_request['request_status'] . '_status') ?></button>
        <?php } ?>
    </div>
</div>
<div class="row">
    <?php echo form_open_multipart($this->uri->uri_string(), array('class' => 'client-form', 'autocomplete' => 'off')); ?>
    <div class="additional"></div>
    <div class="col-md-12">
        <div class="horizontal-scrollable-tabs">
            <div class="scroller arrow-left"><i class="fa fa-angle-left"></i></div>
            <div class="scroller arrow-right"><i class="fa fa-angle-right"></i></div>
            <div class="horizontal-tabs">
                <ul class="nav nav-tabs profile-tabs row customer-profile-tabs nav-tabs-horizontal" role="tablist">
                    <li role="presentation" class="<?php if (!$this->input->get('tab')) {
                                                        echo 'active';
                                                    }; ?>">
                        <a href="#contact_info" aria-controls="contact_info" role="tab" data-toggle="tab">
                            <?php echo _l('customer_profile_details'); ?>
                        </a>
                    </li>
                    <?php
                    $customer_custom_fields = false;
                    if (total_rows(db_prefix() . 'customfields', array('fieldto' => 'customers', 'active' => 1)) > 0) {
                        $customer_custom_fields = true;
                    ?>
                        <li role="presentation" class="<?php if ($this->input->get('tab') == 'custom_fields') {
                                                            echo 'active';
                                                        }; ?>">
                            <a href="#custom_fields" aria-controls="custom_fields" role="tab" data-toggle="tab">
                                <?php echo hooks()->apply_filters('customer_profile_tab_custom_fields_text', _l('custom_fields')); ?>
                            </a>
                        </li>
                    <?php } ?>
                    <li role="presentation">
                        <a href="#client_source" aria-controls="client_source" role="tab" data-toggle="tab">
                            <?php echo _l('client_request_create_source'); ?>
                        </a>
                    </li>
                    <?php if (isset($client)) { ?>
                        <li role="presentation">
                            <a href="#customer_admins" aria-controls="customer_admins" role="tab" data-toggle="tab">
                                <?php echo _l('customer_admins'); ?>
                                <?php if (count($customer_admins) > 0) { ?>
                                    <span class="badge bg-default"><?php echo count($customer_admins) ?></span>
                                <?php } ?>
                            </a>
                        </li>
                        <?php hooks()->do_action('after_customer_admins_tab', $client); ?>
                    <?php } ?>
                    <?php if (isset($client) && isset($payer)) : ?>
                        <li role="presentation">
                            <a href="#infoplus-payer-info" aria-controls="infoplus-payer-info" role="tab" data-toggle="tab">
                                <?php echo _l('infoplus_payer_info'); ?>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
        <div class="tab-content mtop15">
            <?php hooks()->do_action('after_custom_profile_tab_content', isset($client) ? $client : false); ?>
            <?php if ($customer_custom_fields) { ?>
                <div role="tabpanel" class="tab-pane <?php if ($this->input->get('tab') == 'custom_fields') {
                                                            echo ' active';
                                                        }; ?>" id="custom_fields">
                    <?php $rel_id = (isset($client) ? $client->userid : false); ?>
                    <?php echo render_custom_fields('customers', $rel_id); ?>
                </div>
            <?php } ?>
            <div role="tabpanel" class="tab-pane<?php if (!$this->input->get('tab')) {
                                                    echo ' active';
                                                }; ?>" id="contact_info">
                <div class="row">
                    <div class="col-md-12 mtop15 <?php if (isset($client) && (!is_empty_customer_company($client->userid) && total_rows(db_prefix() . 'contacts', array('userid' => $client->userid, 'is_primary' => 1)) > 0)) {
                                                        echo '';
                                                    } else {
                                                        echo ' hide';
                                                    } ?>" id="client-show-primary-contact-wrapper">
                        <div class="checkbox checkbox-info mbot20 no-mtop">
                            <input type="checkbox" name="show_primary_contact" <?php if (isset($client) && $client->show_primary_contact == 1) {
                                                                                    echo ' checked';
                                                                                } ?> value="1" id="show_primary_contact">
                            <label for="show_primary_contact"><?php echo _l('show_primary_contact', _l('invoices') . ', ' . _l('estimates') . ', ' . _l('payments') . ', ' . _l('credit_notes')); ?></label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <?php echo isset($client) ? render_input('id', 'client_request_idm_crm',  $client->userid, 'text', ['disabled' => true]) : ''; ?>
                        <?php $value = (isset($client) ? $client->business_name : ''); ?>
                        <?php
                        $attrs = [
                            'placeholder' => _l('client_request_company_placeholder')
                        ];
                        if (!isset($client)) {
                            $attrs['autofocus'] = true;
                        }

                        if ($value && !is_leader_member()) {
                            $attrs['disabled'] = true;
                        }
                        ?>
                        <?php echo render_input('business_name', 'client_company', $value, 'text', $attrs); ?>
                        <div id="company_exists_info" class="hide"></div>
                        <?php $value = (isset($client) ? $client->company : ''); ?>
                        <?php echo render_input('company', 'client_request_company_short_name', $value, 'text'); ?>

                        <?php if (get_option('company_requires_vat_number_field') == 1) : ?>
                            <?php $value = (isset($client) ? $client->vat : ''); ?>
                            <div class="form-group">
                                <label for="vat"><?php echo _l('client_vat_number'); ?></label>
                                <div class="input-group">
                                    <input type="text" <?= $value && !is_leader_member() ? 'disabled' : '' ?> placeholder="<?php echo _l('client_request_vat_placeholder') ?>" value="<?= $value ?>" name="vat" id="vat" autocomplete="off" class="form-control">
                                    <div class="input-group-btn pleft5">
                                        <button <?= $value && !is_leader_member() ? 'disabled' : '' ?> id="sync_vat" type="button" class="btn btn-info sync">
                                            <i class="fa fa-search"></i>
                                            <i class="fa fa-spinner fa-spin hide" style="margin-right: 5px;"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endif ?>

                        <?php echo render_input('foreign_vat', 'client_request_foreign_vat', $client->foreign_vat ?? '', 'text'); ?>
                        <?php $value = (isset($client) ? $client->address : ''); ?>
                        <?php echo render_textarea('address', 'client_address', $value, $value && !is_leader_member() ? ['disabled' => true] : []); ?>
                        <?php
                        $type_of_customers = get_all_type_of_customers();
                        $type_tooltip_data = '';

                        foreach ($type_of_customers as $type) {
                            $type_tooltip_data .= $type['value'] . ': ' . $type['text'] . '&#13;&#10;';
                        }
                        ?>
                        <p title="<?php echo $type_tooltip_data ?>"><i class="fa fa-question-circle pull-left"></i></p>
                        <?php
                        $value = $client->type_of_customer ?? '';
                        echo render_select(
                            'type_of_customer',
                            $type_of_customers,
                            ['value', 'value'],
                            _l('type_of_customer'),
                            $value,
                            array_merge(['required' => true], $value ? ['disabled' => true] : [])
                        );
                        ?>
                        <?php
                        $usage_behavior = get_all_usage_behavior();
                        $usage_behavior_tooltip_data = '';

                        foreach ($usage_behavior as $type) {
                            $usage_behavior_tooltip_data .= $type['value'] . ': ' . $type['text'] . '&#13;&#10;';
                        }
                        ?>
                        <p title="<?php echo $usage_behavior_tooltip_data ?>"><i class="fa fa-question-circle pull-left"></i></p>
                        <?php
                        $value = $client->usage_behavior ?? '';
                        echo render_select(
                            'usage_behavior',
                            $usage_behavior,
                            ['value', 'value'],
                            _l('usage_behavior'),
                            $value,
                            array_merge(['disabled' => true])
                        );
                        ?>
                        <?php echo render_input('num_of_usage_behavior', 'num_of_usage_behavior', $client->num_of_usage_behavior ?? null, 'number', array_merge([], !has_permission('customers', '', 'update_num_of_usage_behavior') ? ['disabled' => true] : [])); ?>
                        <div class="form-group">
                            <label for="website"><?php echo _l('client_website'); ?></label>
                            <div class="input-group">
                                <input type="text" placeholder="<?php echo _l('client_request_website_placeholder') ?>" name="website" id="website" value="<?php echo $client->website ?? ''; ?>" class="form-control">
                                <div class="input-group-addon">
                                    <span><a href="<?php echo maybe_add_http($client->website ?? ''); ?>" target="_blank" tabindex="-1"><i class="fa fa-globe"></i></a></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="company_facebook"><?php echo _l('client_request_company_facebook'); ?></label>
                            <div class="input-group">
                                <input type="text" name="company_facebook" id="company_facebook" value="<?php echo $client->company_facebook ?? ''; ?>" class="form-control">
                                <div class="input-group-addon">
                                    <span><a href="<?php echo maybe_add_http($client->company_facebook ?? ''); ?>" target="_blank" tabindex="-1"><i class="fa fa-globe"></i></a></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="company_linkedin"><?php echo _l('client_request_company_linkedin'); ?></label>
                            <div class="input-group">
                                <input type="text" name="company_linkedin" id="company_linkedin" value="<?php echo $client->company_linkedin ?? ''; ?>" class="form-control">
                                <div class="input-group-addon">
                                    <span><a href="<?php echo maybe_add_http($client->company_linkedin ?? ''); ?>" target="_blank" tabindex="-1"><i class="fa fa-globe"></i></a></span>
                                </div>
                            </div>
                        </div>
                        <?php $value = (isset($client) ? $client->company_notes : ''); ?>
                        <?php echo render_textarea('company_notes', 'client_request_another_notes', $value); ?>
                    </div>
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-6"><?= render_input( 'id', 'Classify 2024', $classify_2024 ?? '', 'text', ['disabled' => true]); ?></div>
                            <div class="col-md-6"><?= render_input( 'id', 'Classify 2025', $classify_2025 ?? '', 'text', ['disabled' => true]); ?></div>
                        </div>
                        <div class="form-group">
                            <label for="tags" class="control-label"><i class="fa fa-tag" aria-hidden="true"></i> <?php echo _l('tags'); ?></label>
                            <input type="text" class="tagsinput" id="tags" name="tags" value="<?php echo (isset($client) ? prep_tags_input(get_tags_in($client->userid, 'client')) : ''); ?>" data-role="tagsinput">
                        </div>
                        <?php
                        $value = $client->num_employees ?? '';
                        $num_employees_list = $taxonomies['num_employees'];
                        echo render_select('num_employees', $num_employees_list, ['id', 'text'], _l('client_request_num_employees'), $value);
                        ?>
                        <?php
                        $value = isset($client) && $client->company_types ? json_decode($client->company_types, true) : [];
                        echo render_select('company_types[]', get_all_company_types(), ['value', 'text'], _l('client_request_company_types'), $value, ['multiple' => true], [], '', '', $include_blank = false);
                        ?>
                        <?php $value = (isset($client) ? $client->phonenumber : ''); ?>
                        <?php echo render_input('phonenumber', 'client_phonenumber', $value, 'text', ['placeholder' => _l('client_request_phone_placeholder')]); ?>
                        <?php $value = (isset($client) ? $client->company_email : ''); ?>
                        <?php echo render_input('company_email', 'client_request_company_email', $value, 'text', ['placeholder' => _l('client_request_email_placeholder')]); ?>
                        <?php
                        $value = $client->company_status ?? '';
                        echo render_select('company_status', get_all_company_statuses(), ['value', 'text'], _l('client_request_company_status'), $value);
                        ?>
                        <div class="form-group">
                            <label for="established_date" class="control-label"><?= _l('client_request_established_date') ?></label>
                            <div class="input-group date">
                                <input type="text" value="<?php echo isset($client) && $client->established_date ? date_format(new DateTime($client->established_date), 'd/m/Y') : '' ?>" placeholder="dd/mm/yyyy" class="form-control" id="established_date" name="established_date" autocomplete="off">
                                <div class="input-group-addon">
                                    <i class="fa fa-calendar calendar-icon"></i>
                                </div>
                            </div>
                        </div>
                        <?php
                        $value = !empty($industries) ? array_pluck($industries, 'industry_id') : [];
                        $company_industries = $taxonomies['industries'];
                        echo render_select('company_industries[]', $company_industries, ['id', 'text'], _l('client_request_company_industry'), $value, ['multiple' => true], [], '', '', $include_blank = false);
                        ?>
                        <?php
                        $value = !empty($nationalities) ? array_pluck($nationalities, 'national_id') : [];
                        $company_nationalities = $taxonomies['nationalities'];
                        echo render_select('company_nationalities[]', $company_nationalities, ['id', 'text'], _l('client_request_company_nationality'), $value, ['multiple' => true], [], '', '', $include_blank = false);
                        ?>
                        <div class="form-group ams_company_ids">
                            <label for="ams_company_id"><?php echo _l('client_ams_company_id'); ?></label>
                            <?php if (!empty($ams_company_ids) && count($ams_company_ids)) : ?>
                                <?php $amsIdLength = count($ams_company_ids); ?>
                                <?php foreach ($ams_company_ids as $index => $id) : ?>
                                    <div class="input-group mbot5 ams_company_id">
                                        <input <?php echo !has_permission('customers', '', 'edit_id_ams') ? 'disabled' : '' ?> type="text" placeholder="<?php echo _l('client_ams_company_id') ?>" name="ams_company_ids[]" value="<?php echo  $id ?>" class="form-control">
                                        <div class="input-group-btn <?php echo !has_permission('customers', '', 'edit_id_ams') ? 'hide' : '' ?>">
                                            <button type="button" class="btn btn-info add <?php echo ($amsIdLength == ($index + 1) ? '' : 'hide'); ?>"><i class="fa fa-plus"></i></button>
                                            <button type="button" class="btn btn-danger remove <?php echo ($amsIdLength === 1 ? 'hide' : ''); ?>"><i class="fa fa-minus"></i></button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else : ?>
                                <div class="input-group mbot5 ams_company_id">
                                    <input <?php echo !has_permission('customers', '', 'edit_id_ams') ? 'disabled' : '' ?> type="text" placeholder="<?php echo _l('client_ams_company_id') ?>" name="ams_company_ids[]" class="form-control">
                                    <div class="input-group-btn <?php echo !has_permission('customers', '', 'edit_id_ams') ? 'hide' : '' ?>">
                                        <button type="button" class="btn btn-info add"><i class="fa fa-plus"></i></button>
                                        <button type="button" class="btn btn-danger remove hide"><i class="fa fa-minus"></i></button>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="hide">
                            <?php
                            $s_attrs = array('data-none-selected-text' => _l('system_default_string'));
                            $selected = '';
                            if (isset($client) && client_have_transactions($client->userid)) {
                                $s_attrs['disabled'] = true;
                            }
                            foreach ($currencies as $currency) {
                                if (isset($client)) {
                                    if ($currency['id'] == $client->default_currency) {
                                        $selected = $currency['id'];
                                    }
                                }
                            }
                            // Do not remove the currency field from the customer profile!
                            echo render_select('default_currency', $currencies, array('id', 'name', 'symbol'), 'invoice_add_edit_currency', $selected, $s_attrs); ?>
                        </div>
                        <div class="form-group">
                            <div id="company_offices">
                                <?php $offices = [...get_office_template_items(), ...$offices ?? []]; ?>
                                <?php foreach ($offices as $index => $office) : ?>
                                    <div class="office-item text-right <?php echo $index == 0 ? 'hide' : '' ?>" data-index="<?php echo $index ?>">
                                        <input type="hidden" name="company_office_ids[]" value="<?php echo $office['id'] ?>" />
                                        <?php echo render_input('company_office_addresses[]', 'client_request_office_address', $office['office_address'], 'text', [], [], 'text-left'); ?>
                                        <button onclick="remove_company_item(this)" title="<?php echo _l('delete') ?>" class="btn btn-danger" data-max="4" type="button"><i class="fa fa-minus"></i></button>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="input-group" style="margin-top: 10px;">
                                <button type="button" onclick="add_company_office()" class="btn btn-info"><?php echo _l('client_request_add_office_btn') ?></button>
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <div id="company_branches">
                                <?php
                                $branches = [...get_branch_template_items(), ...($branches ?? [])];
                                $statuses = get_all_company_statuses();
                                ?>
                                <?php foreach ($branches as $index => $branch) : ?>
                                    <div class="branch-item text-right <?php echo $index == 0 ? 'hide' : '' ?>">
                                        <div class="text-left">
                                            <input type="hidden" name="company_branch_ids[]" value="<?php echo $branch['id'] ?>" />
                                            <?php echo render_input('company_branch_name[]', 'client_request_company_branch_name', $branch['branch_name']); ?>
                                            <?php echo render_input('company_branch_vat[]', 'client_request_company_branch_vat', $branch['branch_vat']); ?>
                                            <?php echo render_input('company_branch_business_address[]', 'client_request_company_branch_business_address', $branch['branch_business_address']); ?>
                                            <?php echo render_input('company_branch_office_address[]', 'client_request_company_branch_office_address', $branch['branch_office_address']); ?>
                                            <?php echo render_select($index == 0 ? 'company_branch_status_temp' : 'company_branch_status[' . $index . ']', $statuses, ['value', 'text'], 'client_request_company_branch_status', $branch['branch_status'], $index == 0 ? ['disabled' => true] : []); ?>
                                            <?php echo render_textarea('company_branch_note[]', 'client_request_company_branch_note', $branch['branch_note']); ?>
                                        </div>
                                        <button onclick="remove_company_item(this)" title="<?php echo _l('delete') ?>" class="btn btn-danger" data-max="4" type="button"><i class="fa fa-minus"></i></button>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="input-group" style="margin-top: 10px;">
                                <button type="button" onclick="add_company_branch()" class="btn btn-info"><?php echo _l('client_request_add_branch_btn') ?></button>
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <div id="company_affiliates">
                                <?php
                                $affiliates = [...get_affiliate_template_items(), ...$affiliates ?? []];
                                $statuses = get_all_company_affiliate_relations();
                                ?>
                                <?php foreach ($affiliates as $index => $affiliate) : ?>
                                    <div class="affiliate-item text-right <?php echo $index == 0 ? 'hide' : '' ?>">
                                        <div class="text-left">
                                            <input type="hidden" name="company_affiliate_ids[]" value="<?php echo $affiliate['id'] ?>" />
                                            <?php echo render_select($index == 0 ? 'company_affiliate_relation_temp' : 'company_affiliate_relation_type[' . $index . ']', $statuses, ['value', 'text'], 'client_request_relation_type', $affiliate['relation_type'], $index == 0 ? ['disabled' => true] : []); ?>
                                            <?php echo render_input('company_affiliate_related_vat[]', 'client_request_related_vat', $affiliate['related_vat']); ?>
                                            <?php echo render_input('company_affiliate_related_company_name[]', 'client_request_related_company_name', $affiliate['related_company_name']); ?>
                                        </div>
                                        <button onclick="remove_company_item(this)" title="<?php echo _l('delete') ?>" class="btn btn-danger" data-max="4" type="button"><i class="fa fa-minus"></i></button>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="input-group" style="margin-top: 10px;">
                                <button type="button" onclick="add_company_affiliate()" class="btn btn-info"><?php echo _l('client_request_add_affiliate_btn') ?></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php if (isset($client)) { ?>
                <div role="tabpanel" class="tab-pane" id="customer_admins">
                    <?php if (has_permission('customers', '', 'create') || has_permission('customers', '', 'edit')) { ?>
                        <div class="row mbot30">
                            <div class="col-md-6">
                                <a href="#" data-toggle="modal" data-target="#customer_admins_assign" class="btn btn-info"><?php echo _l('assign_admin'); ?></a>
                            </div>
                            <div class="col-md-6 text-right">
                                <span>
                                    Last Assigned: <b><?php echo $client->last_assigned_name ?></b>
                                </span>
                            </div>
                        </div>
                    <?php } ?>
                    <table class="table dt-table">
                        <thead>
                            <tr>
                                <th><?php echo _l('staff_member'); ?></th>
                                <th><?php echo _l('customer_admin_date_assigned'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($customer_admins as $c_admin) { ?>
                                <tr>
                                    <td><a href="<?php echo admin_url('profile/' . $c_admin['staff_id']); ?>">
                                            <?php echo staff_profile_image($c_admin['staff_id'], array(
                                                'staff-profile-image-small',
                                                'mright5'
                                            ));
                                            echo get_staff_full_name($c_admin['staff_id']); ?></a>
                                    </td>
                                    <td data-order="<?php echo $c_admin['date_assigned']; ?>"><?php echo _dt($c_admin['date_assigned']); ?></td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            <?php } ?>
            <?php if (isset($client) && isset($payer)) : ?>
                <?php $this->load->view('admin/clients/groups/profile_infoplus_payer.php'); ?>
            <?php endif; ?>
            <div role="tabpanel" class="tab-pane" id="client_source">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-6">
                                <?php
                                $clientRequest = $client_request ?? [];
                                echo render_select('source', get_all_company_creation_sources(), ['value', 'text'], _l('client_request_create_source'), $client->source ?? null, (!is_sales_admin() && !is_sales_leader()) ? ['disabled' => true] : []);
                                echo render_input('source_reason', 'client_request_create_reason', $client->source_reason ?? null, 'text', (!is_sales_admin() && !is_sales_leader()) ? ['disabled' => true] : []);
                                echo render_input('source_reference_link', 'client_request_reference_link', $client->source_reference_link ?? null, 'text', (!is_sales_admin() && !is_sales_leader()) ? ['disabled' => true] : []);
                                echo render_input('client_request_attachment', 'client_request_reference_file', '', 'file', array_merge(['accept' => '.png, .jpeg, .pdf'], (!is_sales_admin() && !is_sales_leader()) ? ['disabled' => true] : []), [], 'mbot5');
                                echo isset($request_file['attachment_url']) ?  '<a target="_blank" href="' . site_url('uploads/company/' . $request_file['attachment_url']) . '">' . _l('client_request_row_view_attachment') . '</a>' : '';
                                echo form_error('client_request_attachment', '<p id="client_request_attachment-error" class="text-danger">', '</p>');
                                echo render_input('requested_by', 'client_request_created_by', $clientRequest['sale_name'] ?? '', 'text', ['disabled' => true], [], 'mtop5');
                                ?>
                            </div>
                            <div class="col-md-6">
                                <?php
                                echo render_input('requested_at', 'client_request_created_at', isset($clientRequest['created_at']) ? _d($clientRequest['created_at']) :  '', 'text', ['disabled' => true]);
                                echo render_input('sale_admin_approved', 'client_request_approve_by_1', $clientRequest['sale_admin_name'] ?? '', 'text', ['disabled' => true]);
                                echo render_input('sale_leader_approved', 'client_request_approve_by_2', $clientRequest['sale_leader_name'] ?? '', 'text', ['disabled' => true]);
                                echo render_input('approved_at', 'client_request_approve_time', isset($clientRequest['approved_at']) ? _d($clientRequest['approved_at']) : '', 'text', ['disabled' => true]);
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php echo form_close(); ?>
</div>
<?php if (isset($client)) { ?>
    <?php if (has_permission('customers', '', 'create') || has_permission('customers', '', 'edit')) { ?>
        <div class="modal fade" id="customer_admins_assign" tabindex="-1" role="dialog">
            <div class="modal-dialog">
                <?php echo form_open(admin_url('clients/assign_admins/' . $client->userid)); ?>
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo _l('assign_admin'); ?></h4>
                    </div>
                    <div class="modal-body">
                        <?php
                        $selected = array();
                        foreach ($customer_admins as $c_admin) {
                            array_push($selected, $c_admin['staff_id']);
                        }
                        echo render_select(
                            'customer_admins[]',
                            $staff,
                            ['staffid', ['firstname', 'lastname']],
                            _l('customer_admins'),
                            $selected
                        );
                        ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                        <button type="submit" class="btn btn-info"><?php echo _l('submit'); ?></button>
                    </div>
                </div>
                <!-- /.modal-content -->
                <?php echo form_close(); ?>
            </div>
            <!-- /.modal-dialog -->
        </div>
        <!-- /.modal -->
    <?php } ?>
<?php } ?>
<?php $this->load->view('admin/clients/client_group'); ?>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        $('.client-form button#sync_vat').on('click', function() {
            const btn = $(this);
            const vat = $('.client-form input#vat').val();

            if (!vat) {
                alert_float('danger', 'Input field <?= _l('request_invoice_vat_holder') ?>');
                return;
            }

            btn.attr('disabled', true);
            btn.find('i.fa-spinner').removeClass('hide');
            $.ajax({
                type: 'get',
                dataType: 'json',
                url: admin_url + 'invoices/get_company_by_vat/' + vat,
                success: function(response) {
                    if (!response.success) {
                        alert_float('warning', response.message);
                        return;
                    }
                    const company = response.data?.data;
                    $('.client-form input#business_name').val(company.ten_cty);
                    $('.client-form textarea#address').val(company.dia_chi);
                },
                error: function(response) {
                    alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
                },
                complete: function() {
                    btn.attr('disabled', false);
                    btn.find('i.fa-spinner').addClass('hide');
                    $('.client-form input.autofill').removeAttr('disabled');
                }
            });
        });
    });
</script>

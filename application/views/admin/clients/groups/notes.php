<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php if (isset($client)) { ?>
    <h4 class="customer-profile-group-heading"><?= _l('contracts_notes_tab'); ?></h4>
    <div class="col-md-12">
        <?php if (is_leader_member() || (is_customer_service() && $type_note == $this->misc_model->CUSTOMER_TYPE_INTERNAL) || in_array(get_staff_user_id(), array_column($customer_admins, 'staff_id'))) { ?>
            <div class="d-flex">
                <button class="btn btn-success" id="newContactNoteBtn" type="button" onclick="showNoteForm(<?= $client->userid ?>,<?= $type_note ?>)">
                    <?= _l('new_note') ?>
                </button>
            </div>
            <div class="clearfix"></div>
            <div class="row">
                <hr class="hr-panel-heading" />
            </div>
            <div class="clearfix"></div>
        <?php } ?>
        <div class="clearfix"></div>
        <div class="mtop15">
            <table class="table dt-table" data-order-col=<?= $type_note != $this->misc_model->CUSTOMER_TYPE_INTERNAL ? 4 : 1 ?> data-order-type="desc">
                <thead>
                    <tr>
                        <?php if ($type_note != $this->misc_model->CUSTOMER_TYPE_INTERNAL) { ?>
                            <th>
                                <?= _l('contact'); ?>
                            </th>
                            <th>
                                <?= _l('contact_channel'); ?>
                            </th>
                        <?php } ?>
                        <th width="40%">
                            <?= _l('detail'); ?>
                        </th>
                        <?php if ($type_note == $this->misc_model->CUSTOMER_TYPE_CONTACT_SUCCESS) { ?>
                            <th>
                                <?= _l('follow_up'); ?>
                            </th>
                        <?php } ?>
                        <?php if ($type_note == $this->misc_model->CUSTOMER_TYPE_CONTACT_AGAIN) { ?>
                            <th>
                                <?= _l('reminder_calendar'); ?>
                            </th>
                        <?php } ?>
                        <th>
                            <?= _l('history'); ?>
                        </th>
                        <th>
                            <?= _l('options'); ?>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($user_notes as $note) { ?>
                        <tr>
                            <?php if ($type_note != $this->misc_model->CUSTOMER_TYPE_INTERNAL) { ?>
                                <td>
                                    <div data-note-contact="<?= $note['id']; ?>">
                                        <?= join(' - ', array_filter([$note['contact_name'], $note['contact_phone'], $note['contact_email']])) ?>
                                    </div>
                                </td>
                                <td>
                                    <?= $note['contact_channel_text']; ?>
                                </td>
                            <?php } ?>
                            <td width="40%">
                                <div>
                                    <ul>
                                    <?php if ($type_note == $this->misc_model->CUSTOMER_TYPE_CONTACT_SUCCESS) { ?>
                                        <li>- <?= _l('action') ?>: <?= $note['action_text'] ?></li>
                                        <li>- <?= _l('potential_rate') ?>: <?= $note['potential_rate_text'] ?></li>
                                    <?php } ?>
                                        <li>- <?= _l('note') ?>: <?= check_for_links($note['description']) ?></li>
                                    </ul>
                                </div>
                                <br>
                                <?php if (count($note['feedback'])) { ?>
                                    <b>Feedback</b>
                                    <div>
                                        <?php foreach ($note['feedback'] as $key => $value) {
                                            echo '<b>' . $value['staff'] . '</b>: ' . $value['description'] . ' (' . $value['created_at'] . ')'
                                        ?>
                                            <a href="<?= admin_url('tickets/ticket/' . $value['ticket_id']); ?>" class="mleft5"><i style="color: black;" class="fa fa-reply"></i></a>
                                            <?php if (is_leader_member()) { ?>
                                                <a href="<?= admin_url('misc/delete_feedback/' . $value['id']); ?>" class="mleft5 _delete"><i style="color: red;" class="fa fa-remove"></i></a>
                                            <?php } ?>
                                            <br>
                                        <?php
                                        } ?>
                                    </div>
                                <?php } ?>
                                <?php echo form_open(admin_url('misc/feedback/' . $note['id']), ['id' => 'add_feedback_form']); ?>
                                <div data_feedback_note="<?php echo $note['id']; ?>" class="hide">
                                    <div id="feedback_data_note"></div>
                                    <input type="hidden" name="feedback_user_id" value="<?php echo $note['rel_id'] ?>" />
                                    <input type="hidden" name="feedback_assigned" value="<?php echo $note['addedfrom'] ?>" />
                                    <textarea name="description" class="form-control mbot10" required rows="4"></textarea>
                                    <div class="text-right mtop15">
                                        <button type="button" class="btn btn-default" onclick="toggle_feedback_note(<?php echo $note['id']; ?>);return false;"><?php echo _l('cancel'); ?></button>
                                        <button type="submit" class="btn btn-info"><?php echo _l('send_feedback'); ?></button>
                                    </div>
                                </div>
                                <?php echo form_close(); ?>
                            </td>
                            <?php if ($type_note == $this->misc_model->CUSTOMER_TYPE_CONTACT_SUCCESS) { ?>
                                <td>
                                    <?= $note['follow_up_text'] ?>
                                </td>
                            <?php } ?>
                            <?php if ($type_note == $this->misc_model->CUSTOMER_TYPE_CONTACT_AGAIN) { ?>
                                <td>
                                    <?= _dt($note['reminder_calendar']) ?>
                                </td>
                            <?php } ?>
                            <td data-order="<?= $note['dateadded']; ?>">
                                <ul>
                                    <li>- <?= _l('created_by') ?>: <?= '<a href="' . admin_url('profile/' . $note['addedfrom']) . '">' . $note['firstname'] . ' ' . $note['lastname'] . '</a>' ?></li>
                                    <li>- <?= _l('created_at') ?>: <?= _dt($note['dateadded']) ?></li>
                                    <?php
                                    if (isset($note['last_modified']['staffid'])) { ?>
                                        <li>- <?= _l('last_modified_by') ?>: <?= '<a href="' . admin_url('profile/' . $note['last_modified']['staffid']) . '">' . $note['last_modified']['fullname'] . '</a>' ?></li>
                                        <li>- <?= _l('last_modified_at') ?>: <?= _dt($note['updated_at']) ?></li>
                                        <?php if ($note['type'] != $this->misc_model->CUSTOMER_TYPE_INTERNAL) { ?>
                                            <a href="#" data-toggle="modal" onClick="showHistoryNote(<?= $note['id'] ?>)" data-target="#note_history_modal"><?= _l('note_editing_history') ?></a>
                                        <?php }
                                    } ?>
                                </ul>
                            </td>
                            <td>
                                <?php if ($note['addedfrom'] == get_staff_user_id() || is_admin() || is_sales_admin()) { ?>
                                    <?php if (isset($note['attachments']) && $note['type'] == $this->misc_model->CUSTOMER_TYPE_CONTACT_SUCCESS) { ?>
                                        <a href="<?= site_url('download/file/note/' . $note['attachments']->attachment_key) ?>" class="btn btn-default btn-icon mbot5"><i style="color: black;" class="fa fa-file-text"></i></a>
                                    <?php } ?>
                                    <a href="#" class="btn btn-default btn-icon mbot5" onclick='showNoteForm(
                                            <?= $client->userid ?>,
                                            <?= $note["type"] ?>,
                                            <?= $note["id"] ?>
                                        );return false;'><i class="fa fa-pencil-square-o"></i></a>
                                    <a href="#" class="btn btn-default btn-icon mbot5" onclick="toggle_feedback_note(<?= $note['id']; ?>);return false;"><i style="color: black;" class="fa fa-comments-o"></i></a>
                                    <?php if (is_admin() || is_sales_admin()) { ?>
                                        <a href="<?= admin_url('misc/delete_note/' . $note['id']); ?>" class="btn btn-danger btn-icon mbot5 _delete"><i class="fa fa-remove"></i></a>
                                    <?php } ?>
                                <?php } ?>
                            </td>
                        </tr>
                    <?php } ?>
                </tbody>
            </table>
        </div>
    <?php } ?>
    <?php init_tail(); ?>

    <script>
    </script>
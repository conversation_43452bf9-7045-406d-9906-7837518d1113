<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- Client send file modal -->
<div class="modal fade" id="view_qr_code" tabindex="-1" role="dialog" aria-labelledby="view_qr_code">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="view_qr_code">QR</h4>
            </div>
            <div id="modal-body" class="modal-body">
                <img width="100%" height="100%" />
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
            </div>
        </div>
    </div>
</div>

<div role="tabpanel" class="tab-pane" id="infoplus-payer-info">
    <div class="row mleft5 mbot30">
        <div class="d-flex">
            <div><b><?= _l('infoplus_payer_info_payer_no') ?>:</b></div>
            <div class="mleft5"><?= $payer->payer_no ?>,</div>
            <div class="mleft10"><b><?= _l('infoplus_payer_info_payer_name') ?>:</b></div>
            <div class="mleft5"><?= $payer->payer_name ?>,</div>
            <div class="mleft10"><b><?= _l('infoplus_payer_info_ec') ?>:</b></div>
            <div class="mleft5"><?= $payer->e_collection_code ?></div>
        </div>
    </div>
    <h4 class="bold">Payer Transactions</h4>
    <table class="table dt-table" data-order='[]' data-searching="false" data-paging="false">
        <thead>
            <tr>
                <th><?php echo _l('infoplus_payer_info_table_index'); ?></th>
                <th><?php echo _l('infoplus_payer_info_table_invoice'); ?></th>
                <th><?php echo _l('infoplus_payer_info_table_transaction_id'); ?></th>
                <th><?php echo _l('infoplus_payer_info_table_qr'); ?></th>
                <th><?php echo _l('infoplus_payer_info_table_amount'); ?></th>
                <th><?php echo _l('infoplus_payer_info_table_start_at'); ?></th>
                <th><?php echo _l('infoplus_payer_info_table_expires_at'); ?></th>
                <th><?php echo _l('infoplus_payer_info_table_status'); ?></th>
                <th><?php echo _l('infoplus_payer_info_table_paid_at'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $payer->ecRecvs->each(function ($ecRecv, $idx) { ?>
                <tr>
                    <td><?= ($idx + 1) ?></td>
                    <td><?= '<a href="' . admin_url('invoices#' . $ecRecv->invoice->id) . '" target="_blank">' . format_invoice_number($ecRecv->invoice) . '</a>' ?></td>
                    <td><?= $ecRecv->receivable_id ?></td>
                    <td><a href="#" onclick="viewQrData(this)" data-qr="<?= $ecRecv->qr_data ?>" data-toggle="modal" data-target="#view_qr_code">View QR</a></td>
                    <td><?= number_format($ecRecv->deposit_amount) ?></td>
                    <td><?= $ecRecv->payable_start_at->format('Y-m-d H:i:s') ?></td>
                    <td><?= $ecRecv->payable_end_at->format('Y-m-d H:i:s') ?></td>
                    <td><?= $ecRecv->paid ? 'Paid' : 'Unpaid' ?></td>
                    <td><?= isset($ecRecv->received_at) ? $ecRecv->received_at->format('Y-m-d H:i:s') : '' ?></td>
                </tr>
                <?php $ecRecv->trans->each(function ($tran) { ?>
                    <tr>
                        <td></td>
                        <td></td>
                        <td><?= $tran->trans_id ?></td>
                        <td></td>
                        <td><?= number_format($tran->deposit_amount) ?></td>
                        <td><?= isset($tran->deposit_at) ? $tran->deposit_at->format('Y-m-d H:i:s') : '' ?></td>
                        <td></td>
                        <td></td>
                        <td><?= isset($tran->recv_at) ? $tran->recv_at->format('Y-m-d H:i:s') : '' ?></td>
                    </tr>
                <?php }); ?>
            <?php }); ?>
        </tbody>
    </table>
</div>
<script>
    // window.addEventListener("DOMContentLoaded", (event) => {
        function viewQrData(aTag) {
            const qrData = $(aTag).data('qr');
            $('#view_qr_code #modal-body img').attr('src', qrData);
        }
    // })
</script>

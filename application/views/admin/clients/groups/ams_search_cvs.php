<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<style>
    div.summary-section {
        background-color: rgb(165 162 162 / 50%);;
        padding-top: 5px;
        padding-bottom: 5px;
    }
    .summary-section-title {
        background-color: gray;
        color: white;
        font-weight: bold;
        padding: 5px;
        margin: 0;
        text-align: center;
    }

    .summary-section-number {
        padding: 5px 0 5px 5px;
        margin: 0;
        text-align: center;
    }
</style>
<?php if (isset($client)) { ?>
    <h4 class="customer-profile-group-heading"><?php echo _l('ams_search_cv_title'); ?></h4>
    <div class="col-md-12 no-padding">
        <div class="col-md-12 no-padding">
            <div class="col-md-12 d-flex no-padding">
                <div class="flex-column text-left bold"><?php echo _l('ams_filter_title') ?>:</div>
                <div class="col-md-12 flex-grow-1 mleft5 no-padding-right no-mbot">
                    <div class="col-md-4" style="padding-left: 0">
                        <?= render_select('company', $ams_companies ?? [], ['id', 'name'], null, '', ['data-none-selected-text' => _l('ams_companies_placeholder_ams_company_name')], [], '', 'table-filters'); ?>
                    </div>
                    <div class="col-md-4" style="padding-left: 0">
                        <?= render_select('crm_company', $crm_companies ?? [], ['userid', 'company'], null, '', ['data-none-selected-text' => _l('ams_companies_placeholder_crm_company_name')], [], '', 'table-filters'); ?>
                    </div>
                    <div class="col-md-4" style="padding-left: 0">
                        <?= render_select('invoice_id', $invoice_search_cvs, ['id', 'text'], null, '', ['data-none-selected-text' => 'Invoice'], [], '', 'table-filters'); ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 no-padding search-cvs">
            <div class="col-md-3 summary-section">
                <div class="col-md-5 no-padding-left summary-section-title"><strong><?php echo _l('ams_search_summary_invoice_title'); ?></strong></div>
                <div class="col-md-6 summary-section-number"><strong><?php echo _l('ams_search_summary_invoice_purchased'); ?></strong>: <span id="total_invoices">0</span></div>
            </div>
            <div class="col-md-8 summary-section mleft5">
                <div class="col-md-2 no-padding-left summary-section-title"><strong><?php echo _l('ams_search_summary_unlock_title'); ?></strong></div>
                <div class="col-md-2 summary-section-number"><strong><?php echo _l('ams_search_summary_unlock_total'); ?></strong>: <span id="total_unlocks">0</span></div>
                <div class="col-md-2 summary-section-number"><strong><?php echo _l('ams_search_summary_unlock_unused'); ?></strong>: <span id="total_unused">0</span></div>
                <div class="col-md-2 summary-section-number"><strong><?php echo _l('ams_search_summary_unlock_used'); ?></strong>: <span id="total_unsed">0</span></div>
                <div class="col-md-2 summary-section-number"><strong><?php echo _l('ams_search_summary_unlock_expired'); ?></strong>: <span id="total_expired">0</span></div>
            </div>
        </div>
        <div class="col-md-12 no-padding mtop10">
            <?php render_datatable(array_filter([
                _l('ams_search_column_invoice'),
                _l('ams_search_column_ams_id'),
                _l('ams_search_column_purchased_date'),
                _l('ams_search_column_product_name'),
                _l('ams_search_column_used_total_unlocks'),
                _l('ams_search_column_status'),
                _l('ams_search_column_time'),
                has_permission('customers', '', 'change_search_cv_invoice') ? _l('ams_search_column_action') : null,
            ]), 'ams-search-cvs'); ?>
        </div>
    </div>
<?php } ?>

<script>
    window.addEventListener("DOMContentLoaded", () => {
        var clientId = <?php echo $client->userid ?>,
            selectedInvoices = JSON.parse('<?php echo json_encode(!empty($invoice_search_cvs) ? array_merge([['id' => '', 'text' => '',]], $invoice_search_cvs) : []) ?>');

        $('#crm_company').on('changed.bs.select', function (evt) {
            const selectedId = $(this).val();
            fetchRelationSelect(this, '#invoice_id', null, selectedInvoices, 'invoices/get_credit_invoices/' + (selectedId ? selectedId : clientId));
        });

        // Init datatables
        var customSearchParams = {};
        $.each($('._hidden_inputs._filters input'), function() {
            customSearchParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });
        $('.table-filters').each((idx, el) => customSearchParams[$(el).attr('name')] = '[name="' + $(el).attr('name') + '"]');
        var tAPI = initDataTable('.table-ams-search-cvs', admin_url + 'ams/search_cvs/<?php echo $client->userid ?>', [0, 1, 2, 3, 4, 5, 6, 7], [0, 1, 2, 3, 4, 5, 6, 7], customSearchParams, [], {
            displayLengthMenu: true,
            pageLength: 20,
            lengthMenu: [10, 20],
            searching: false,
        });
        var timer;
        $('.table-filters').on('changed.bs.select', function(evt) {
            // To avoid execute multiple times
            clearTimeout(timer);
            timer = setTimeout(() => {
                tAPI.ajax.reload();
                doFetchJobSummary();
            }, 200);
        });

        function doFetchJobSummary() {
            var selectedClientId = $('#crm_company').val(),
                selectedAmsId = $('#company').val(),
                invoiceId = $('#invoice_id').val();

            if (!selectedClientId && !selectedAmsId) {
                selectedClientId = clientId;
            }

            fetchSummary(selectedClientId ? selectedClientId : -1, selectedAmsId ? selectedAmsId : -1, invoiceId ? invoiceId : -1);
        }

        function fetchSummary(clientId, amsId, invoiceId) {
            $.get(admin_url + 'ams/search_cv_summary/' + clientId + '/' + amsId + '/' + invoiceId, function(res) {
                if (res.success) {
                    var data = res.data;
                    $('#total_invoices').html(data.total_invoices);
                    $('#total_unlocks').html(data.total_unlocks);
                    $('#total_unused').html(data.total_unused);
                    $('#total_unsed').html(data.total_unsed);
                    $('#total_expired').html(data.total_expired);
                }
            }, 'json');
        }

        fetchSummary(<?php echo $client->userid ?>, -1, -1);

        <?php if (has_permission('customers', '', 'change_search_cv_invoice')) : ?>
            $('.table-ams-search-cvs').on('click', 'button.update-btn', onUpdateExpiredBtnClick);
            $('.table-ams-search-cvs').on('click', 'button.save-btn', onSaveExpiredBtnClick);
            $('.table-ams-search-cvs').on('click', 'button.cancel-btn', onCancelExpiredBtnClick);
        <?php endif; ?>

        function onUpdateExpiredBtnClick() {
            var tr = $(this).closest('tr');
            toggleEditing(tr, true);
        }

        function onSaveExpiredBtnClick() {
            var tr = $(this).closest('tr'),
                invoiceSelect = tr.find('select#invoice_id'),
                span = tr.find('span#row_invoice'),
                selectedInvoiceId = invoiceSelect.val(),
                selectedInvoiceText = invoiceSelect.find('option:selected').text(),
                searchPackageId = span.data('id'),
                btn = tr.find('button.edit-comp');

            btn.attr('disabled', true);
            btn.find('i.fa-spinner').removeClass('hide');
            $.ajax({
                type: 'post',
                dataType: 'json',
                data: {
                    invoice_id: selectedInvoiceId
                },
                url: admin_url + 'clients/change_search_cv_invoice/' + searchPackageId,
                success: function(response) {
                    if (response.success) {
                        if (response.data && response.data.updated) {
                            alert_float('info', response.message);
                            span.data('selected-invoice-id', selectedInvoiceId);
                            span.data('selected-invoice-text', selectedInvoiceText);
                        } else {
                            alert_float('warning', response.message);
                        }
                        // Set latest value when saved
                        toggleEditing(tr, false);
                        tAPI.ajax.reload();
                    } else {
                        alert_float('danger', response.message);
                    }
                },
                error: function(response) {
                    alert_float('danger', response.responseJSON?.message ?? 'Unknown error!');
                },
                complete: function() {
                    btn.attr('disabled', false);
                    btn.find('i.fa-spinner').addClass('hide');
                }
            });
        }

        function onCancelExpiredBtnClick() {
            var tr = $(this).closest('tr');
            toggleEditing(tr, false);
        }

        function toggleEditing(tr, isEditing) {
            var invoiceSelect = tr.find('select#invoice_id'),
                span = tr.find('span#row_invoice'),
                aTag = span.find('a'),
                selectedInvoiceId = span.data('selected-invoice-id'),
                selectedInvoiceText = span.data('selected-invoice-text'),
                searchPackageId = span.data('id'),
                selectedClientId = $('#crm_company').val();

            if (!selectedClientId) {
                selectedClientId = clientId;
            }

            if (isEditing) {
                var btn = tr.find('button.update-btn');
                btn.attr('disabled', true);
                btn.find('i.fa-spinner').removeClass('hide');
                // Clear all options before fetching new once
                invoiceSelect.find('option').remove().end();
                $.getJSON(admin_url + 'invoices/get_same_credit_invoices/' + selectedClientId + '/' + searchPackageId, function (res) {
                    if (res.success) {
                        setSelectOptions(invoiceSelect, selectedInvoiceId, res.data);
                        tr.find('.view-comp').addClass('hide');
                        tr.find('.edit-comp').removeClass('hide');
                        btn.attr('disabled', false);
                        btn.find('i.fa-spinner').addClass('hide');
                    }
                });
            } else {
                aTag.href = admin_url + 'invoices#' + selectedInvoiceId;
                aTag.text(selectedInvoiceText);
                tr.find('.view-comp').removeClass('hide');
                tr.find('.edit-comp').addClass('hide');
            }
        }
    });
</script>
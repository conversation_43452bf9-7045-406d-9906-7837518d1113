<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php if (isset($client)) { ?>
    <h4 class="customer-profile-group-heading"><?php echo _l('ams_employees_title'); ?></h4>
    <div class="col-md-12 no-padding">
        <div class="col-md-12 d-flex no-padding">
            <div class="flex-column text-left mtop10 bold"><?php echo _l('ams_filter_title') ?>:</div>
            <div class="col-md-12 flex-grow-1 mleft5 no-padding-right no-mbot">
                <div class="col-md-4" style="padding-left: 0">
                    <?= render_select('company', $ams_companies ?? [], ['id', 'name'], null, '', ['data-none-selected-text' => _l('ams_companies_placeholder_company_name')], [], '', 'table-filters'); ?>
                </div>
                <div class="col-md-4" style="padding-left: 0">
                    <div class="form-group select-placeholder">
                        <select id="employer" name="employer" data-live-search="true" data-width="100%" class="ajax-search table-filters ignore-filter" data-empty-title="<?php echo _l('ams_companies_placeholder_employer_account'); ?>"></select>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 no-padding">
            <?php render_datatable([
                array(
                    'name' => 'Company',
                    'th_attrs' => array('class'=>'not_visible')
                ),
                _l('ams_companies_column_id_ams'),
                _l('ams_employees_column_account'),
                _l('ams_employees_column_detail'),
                _l('ams_employees_column_approved_at')
            ], 'ams-companies'); ?>
        </div>
    </div>
<?php } ?>

<div class="modal modal-center fade" id="sync_ams_company_modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title text-uppercase"><?php echo _l('ams_companies_sync_btn'); ?></h4>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('settings_no'); ?></button>
                <button type="button" data-ids="" id="sync_ids" class="btn btn-info">
                    <?php echo _l('settings_yes'); ?>
                    <i class="fa fa-spinner fa-spin hide"></i>
                </button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>

<script>
    window.addEventListener("DOMContentLoaded", () => {
        init_ajax_search('employer', '#employer', undefined, admin_url + 'ams/ams_autocomplete/' + <?php echo $client->userid ?>, true);
        init_trigger_select_event('#employer', '#company', 'company_id');

        // Init datatables
        var customSearchParams = {};
        $.each($('._hidden_inputs._filters input'), function() {
            customSearchParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
        });
        $('.table-filters').each((idx, el) => customSearchParams[$(el).attr('name')] = '[name="' + $(el).attr('name') + '"]');
        var tAPI = initDataTable('.table-ams-companies', admin_url + 'ams/employers/<?php echo $client->userid ?>', [0,1,2,3,4], [0,1,2,3,4], customSearchParams, [0, 'desc'], {
            tables_pagination_limit: 10,
            displayLengthMenu: false,
            rowGroup: {
                dataSrc: 0
            },
            searching: false,
        });
        var timer;
        $('.table-filters:not(.ignore-filter)').on('changed.bs.select', function () {
            // To avoid execute multiple times
            clearTimeout(timer);
            timer = setTimeout(() => tAPI.ajax.reload(), 100);
        });
        $('#employer').on('selected.cleared.ajax.bootstrap.select', function () {
            tAPI.ajax.reload();
        });
    });
</script>
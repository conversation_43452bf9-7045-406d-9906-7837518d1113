<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php if(isset($client)){ ?>
<h4 class="customer-profile-group-heading"><?php echo is_empty_customer_company($client->userid) ? _l('contact') : _l('customer_contacts'); ?></h4>
<?php if($this->session->flashdata('gdpr_delete_warning')){ ?>
    <div class="alert alert-warning">
     [GDPR] The contact you removed has associated proposals using the email address of the contact and other personal information. You may want to re-check all proposals related to this customer and remove any personal data from proposals linked to this contact.
   </div>
<?php } ?>
<?php if((has_permission('contacts','','create') || is_customer_admin($client->userid)) && $client->registration_confirmed == '1'){
   $disable_new_contacts = false;
   if(is_empty_customer_company($client->userid) && total_rows(db_prefix().'contacts',array('userid'=>$client->userid)) == 1){
      $disable_new_contacts = true;
   }
   ?>
<div class="inline-block new-contact-wrapper" data-title="<?php echo _l('customer_contact_person_only_one_allowed'); ?>"<?php if($disable_new_contacts){ ?> data-toggle="tooltip"<?php } ?>>
   <a href="#" onclick="contact(<?php echo $client->userid; ?>); return false;" class="btn btn-info new-contact mbot25<?php if($disable_new_contacts){echo ' disabled';} ?>"><?php echo _l('new_contact'); ?></a>
</div>
<?php } ?>
<?php
   $table_data = [
      _l('clients_contact_id'),
      _l('clients_list_full_name'),
      _l('contact_position'),
      _l('contact_information'),
      _l('contact_status'),
      _l('history'),
      _l('review_status'),
      _l('action'),
   ];

   if(is_gdpr() && get_option('gdpr_enable_consent_for_contacts') == '1'){
      array_splice($table_data, 2, 0, [
            [
               'name'=>_l('gdpr_consent') .' ('._l('gdpr_short').')',
               'th_attrs'=>array('id'=>'th-consent', 'class'=>'not-export')
            ]
         ]
      );
   }

   echo render_datatable($table_data, 'contacts');
} ?>
<div id="contact_data"></div>
<div id="consent_data"></div>

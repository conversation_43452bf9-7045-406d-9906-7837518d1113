<div class="modal fade" id="note_approve_modal" tabindex="-1" role="dialog" aria-labelledby="approveNoteModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="approveNoteModalLabel">
                    <span class="add-title"><?= _l('client_request_note_header'); ?></span>
                </h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <input id="approve_note_status" type="hidden" />
                        <input id="approve_note_request_id" type="hidden" />
                        <input id="approve_form" type="hidden" />
                        <?php echo render_textarea('note_approve', 'client_request_note_text_label', '', ['rows' => 30]) ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                <button group="button" onclick="submitApproveNote(this)" class="btn btn-info"><?= _l('submit'); ?>
                    <i class="fa fa-spinner fa-spin hide" style="margin-right: 2px;"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    function toggleActionBtn(button, action) {
        if (action == 'show') {
            button.find('i.fa-spinner').removeClass('hide');
            button.addClass('disabled');
        } else {
            button.find('i.fa-spinner').addClass('hide');
            button.removeClass('disabled');
        }
    }

    function changeRequestStatus(requestId, createdBy, status, editForm) {
        var confirmMsg = status == 'approve' ? "<?php echo _l('client_request_approve_confirm_message'); ?>" : "<?php echo _l('client_request_reject_confirm_message'); ?>";
        cf = confirm(confirmMsg.replace('__created_by__', createdBy));

        if (cf == true) {
            var row = editForm ? $('.request-approve-btn') : $('.table-all-requests tr.request-' + requestId),
                button = row.find('button.request-action');

            toggleActionBtn(button, 'show');
            editNote(requestId, null, status, editForm);
        }
    }

    function editNote(requestId, btn, status, editForm) {
        var modal = $('#note_approve_modal');
        modal.find('#approve_note_request_id').val(requestId);
        modal.find('#note_approve').val(btn ? $(btn).data('note') : '');
        modal.find('#approve_note_status').val(status);
        modal.find('#approve_form').val(editForm ? 1 : 0);
        modal.modal({
            show: true,
            backdrop: 'static'
        });

        // Come from selection button
        if (status) {
            modal.on('hide.bs.modal', function() {
                var button = (editForm ? $('.request-approve-btn') : $('.table-all-requests tr.request-' + requestId)).find('button.request-action');
                toggleActionBtn(button, 'hide');
            });
        }
    }

    function submitApproveNote(btn) {
        var requestId = $('#approve_note_request_id').val(),
            status = $('#approve_note_status').val(),
            approveForm = parseInt($('#approve_form').val());

        toggleActionBtn($(btn), 'show');
        $.ajax({
            type: 'post',
            url: admin_url + (status ?
                'clients/client_' + status + '_request/' :
                'clients/client_edit_approve_note/'
            ) + requestId + '/' + (approveForm ? 0 : 1),
            data: {
                note: $('#note_approve').val()
            },
            success: function(response) {
                !approveForm && alert_float('success', response.message);
            },
            error: function(response) {
                !approveForm && alert_float('danger', response.responseJSON?.message ?? response.responseText);
            },
            complete: function() {
                if (approveForm) {
                    window.location.reload();
                    return;
                }
                toggleActionBtn($(btn), 'hide');
                toggleActionBtn($('.table-all-requests tr.request-' + requestId).find('button.request-action'), 'hide');
                $('#note_approve_modal').modal('hide');
                tAPI.ajax.reload();
                
            }
        });
    }
</script>
<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<!-- Modal Contact -->
<div class="modal fade" id="contact" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <?php echo form_open(admin_url('clients/form_contact/' . $customer_id . ($contactid ? '/' . $contactid : '')), array('id' => 'contact-form', 'autocomplete' => 'off')); ?>
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo $title; ?><br /><small id=""><?php echo get_company_name($customer_id, true); ?></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <?= form_hidden('contactid', $contactid) ?>
                        <?= render_input(
                            'fullname',
                             'clients_list_full_name',
                              $contact->fullname ?? '',
                              'text',
                              [
                                'placeholder' => 'Nguyen A A'
                              ]
                        ) ?>
                        <?= render_input(
                            'title',
                            'contact_position',
                            $contact->title ?? '',
                            'text',
                            [
                                'placeholder' => _l('job_position_placeholder')
                            ]
                        ) ?>
                        <?= render_input(
                            'email',
                            'client_email',
                            $contact->email ?? '',
                            'email',
                            [
                                'placeholder' => _l('email_placeholder')
                            ]
                        ) ?>
                        <?= render_input(
                            'phonenumber',
                            'contact_mobile_phone',
                            $contact->phonenumber ?? '',
                            'number',
                            [
                                'autocomplete' => 'off',
                                'maxlength' => 10,
                                'minlength' => 10,
                                'placeholder' => _l('phone_placeholder')
                            ]
                        ); ?>
                        <?= render_input(
                            'landline',
                            'landline',
                            $contact->landline ?? '',
                            'number'
                        ); ?>
                        <?= render_input(
                            'ext',
                            'ext',
                            $contact->ext ?? '',
                            'number'
                        ); ?>
                        <?= render_input('facebook', 'facebook', $contact->facebook ?? '') ?>

                        <div class="checkbox checkbox-primary">
                            <input type="checkbox" name="is_primary" id="contact_primary"
                            <?php if ((!isset($contact) && total_rows(db_prefix() . 'contacts', array('is_primary' => 1, 'userid' => $customer_id)) == 0) || (isset($contact) && $contact->is_primary == 1)) {
                                echo 'checked';
                            } ?>
                            <?php if ((isset($contact) && total_rows(db_prefix() . 'contacts', array('is_primary' => 1, 'userid' => $customer_id)) == 1 && $contact->is_primary == 1)) {
                                echo 'disabled';
                            } ?>>
                            <label for="contact_primary">
                                <?php echo _l('contact_primary'); ?>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <?= render_select('sex', SEX_OPTIONS, ['value', 'text'], _l('sex'), $contact->sex ?? '', [], [], '', '', false); ?>
                        <?= render_date_input(
                            'birthday',
                            'birthday',
                            isset($contact->birthday) ? _d($contact->birthday) : '',
                            [
                                'placeholder' => 'dd.mm.yyyy'
                            ]
                        ) ?>
                        <?= render_input('zalo', 'zalo', $contact->zalo ?? '') ?>
                        <?= render_input('linkedin', 'linkedin', $contact->linkedin ?? '') ?>
                        <?= render_input('skype', 'skype', $contact->skype ?? '') ?>
                        <?= render_input('other', 'other', $contact->other ?? '') ?>
                    </div>
                </div>
                <?php hooks()->do_action('after_contact_modal_content_loaded'); ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                <button type="submit" class="btn btn-info" data-loading-text="<?php echo _l('wait_text'); ?>" autocomplete="off" data-form="#contact-form"><?php echo _l('submit'); ?></button>
            </div>
            <?php echo form_close(); ?>
        </div>
    </div>
</div>
<?php if (!isset($contact)) { ?>
    <script>
        $(function() {
            var permInputs = $('input[name="permissions[]"]');
            $.each(permInputs, function(i, input) {
                input = $(input);
                if (input.prop('checked') === true) {
                    $('#contact_email_notifications [data-perm-id="' + input.val() + '"]').prop('checked', true);
                }
            });
        });
    </script>
<?php } ?>
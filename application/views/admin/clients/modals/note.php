<div class="modal fade" id="add_note_modal" tabindex="-1" role="dialog" aria-labelledby="addNoteModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
         <div class="modal-header">
            <button group="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="addNoteModalLabel"></h4>
         </div>
         <?= form_open('', ['id' => 'add_note_form', "enctype" => "multipart/form-data"]); ?>
         <div class="modal-body">
            <div class="row">
               <div class="col-md-12">
                  <?php
                  echo render_textarea('description', _l('note_description'), '', ['rows' => 20]);
                  echo render_input('type', '', '', 'hidden');
                  ?>
               </div>
            </div>
            <div id="form-general">
               <div class="row">
                  <div class="col-md-12">
                     <?= render_select('contact_id', [], ['id', ['firstname', 'lastname']], _l('contact'), ''); ?>
                  </div>
               </div>
               <div class="row">
                  <div class="col-md-6">
                     <?= render_select('contact_channel', CONTACT_CHANNEL_NOTE_OPTIONS, ['value', 'text'], _l('contact_channel'), ''); ?>
                  </div>
                  <div class="col-md-6">
                     <label for="reminder_calendar" class="control-label"><?= _l('reminder_calendar') ?></label>
                     <div class="input-group date">
                        <input type="text" class="form-control datetimepicker" id="reminder_calendar" name="reminder_calendar" autocomplete="off">
                        <div class="input-group-addon">
                           <i class="fa fa-calendar calendar-icon"></i>
                        </div>
                     </div>
                  </div>
               </div>
            </div>

            <div id="form-failed">
               <div class="row">
                  <div class="col-md-6">
                     <?= render_select('switch_reminders_to', get_list_staff(), ['staffid', ['firstname', 'lastname']], _l('switch_reminders_to'), get_staff_user_id()); ?>
                  </div>
                  <div class="col-md-6 mtop25">
                     <div class="checkbox">
                        <input type="checkbox" id="send_mail_this_reminder" value=1 name="send_mail_this_reminder">
                        <label for="send_mail_this_reminder"><?= _l('send_mail_this_reminder'); ?></label>
                     </div>
                  </div>
               </div>
            </div>

            <div id="form-success">
               <div class="row">
                  <div class="col-md-6">
                     <?= render_select('action', ACTION_NOTE_OPTIONS, ['value', 'text'], _l('action'), ''); ?>
                  </div>
                  <div class="col-md-6">
                     <?= render_select('follow_up', ACTION_NOTE_OPTIONS, ['value', 'text'], _l('follow_up'), ''); ?>
                  </div>
               </div>
               <div class="row">
                  <div class="col-md-6">
                     <?= render_select('detailed_signal[]', DETAILED_SIGNAL_NOTE_OPTIONS, ['value', 'text'], _l('detailed_signal'), [], ['multiple' => true], [], '', '', false); ?>
                  </div>
                  <div class="col-md-6">
                     <?= render_select('potential_rate', POTENTIAL_RATE_NOTE_OPTIONS, ['value', 'text'], _l('potential_rate')); ?>
                  </div>
               </div>
               <div class="row">
                  <div class="col-md-6">
                     <div class="form-group">
                        <label for="attachments" class="profile-image"><?php echo _l('attachments'); ?></label>
                        <input type="file" name="attachments" class="form-control" id="attachments">
                        <div class="text-danger"><?php echo form_error('attachments'); ?></div>
                     </div>
                  </div>
                  <div class="col-md-6">
                     <?= render_input('link', 'link'); ?>
                  </div>
               </div>
            </div>
         </div>
         <div class="modal-footer">
            <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
            <button group="submit" class="btn btn-info btn-submit"><?= _l('submit'); ?></button>
            <?= form_close(); ?>
         </div>
      </div>
   </div>
</div>
<div class="modal fade" id="note_history_modal" tabindex="-1" role="dialog" aria-labelledby="noteHistoryModalLabel">
   <div class="modal-dialog" role="document">
      <div class="modal-content">
         <div class="modal-header">
            <button group="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="noteHistoryModalLabel">
               <span class="add-title"><?= _l('note_editing_history'); ?></span>
            </h4>
         </div>
         <div class="modal-body">
            <div class="row">
               <div class="col-md-12">
                  <div id="note_history"></div><br>
               </div>
            </div>
         </div>
         <div class="modal-footer">
            <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
         </div>
      </div>
   </div>
</div>

<script>
   async function showNoteForm(customerId, type = null, noteId = null) {
      const modal = $('#add_note_modal');
      let title = modal.find('.modal-title');

      modal.modal('show');
      if (type) {
         await requestGetJSON('clients/client_change_data/' + customerId).done(function(response) {
            var selectContact = $('select[name="contact_id"]');
            selectContact.html('');
            if (!empty(response['contact_data'])) {
               selectContact.append('<option id="" value=""></option>');
               $.each(response['contact_data'], function(index, value) {
                  let textSelect = value.fullname + ' - ' + value.phonenumber + ' - ' + value.email
                  selectContact.append('<option id="' + value.id + '" value="' + value.id + '">' + textSelect + '</option>');
               });

               $('.selectpicker').selectpicker('refresh');
            }
         })
      }

      if (noteId) {
         editNote(noteId)
         const isPageClient = window.location.pathname.includes('/clients/client');
         const buttonSubmit = modal.find('.modal-footer > .btn-submit');

         if (isPageClient) {
            buttonSubmit.prop('disabled', false);
            $('#add_note_form').attr('action', `/admin/misc/edit_note/${noteId}`);
         } else {
            buttonSubmit.prop('disabled', true);
         }
      } else {
         $('#add_note_form').attr('action', `/admin/misc/add_note/${customerId}/customer`);
      }

      $('#type').val(type);

      var vRules = {
         description: 'required'
      };

      let formSuccess = $('#form-success');
      let formFailed = $('#form-failed');
      let formGeneral = $('#form-general');

      if (type == <?= $this->misc_model->CUSTOMER_TYPE_CONTACT_SUCCESS ?>) {
         title.text('<?= _l('contact_success_note') ?>');

         formSuccess.show();
         formGeneral.show();
         formFailed.hide();

         vRules = {
            ...vRules,
            contact_id: 'required',
            reminder_calendar: 'required',
            contact_channel: 'required',
            action: 'required',
            follow_up: 'required'
         }
      } else if (type == <?= $this->misc_model->CUSTOMER_TYPE_CONTACT_AGAIN ?>) {
         title.text('<?= _l('contact_again_note') ?>');

         formFailed.show();
         formGeneral.show();
         formSuccess.hide();

         vRules = {
            ...vRules,
            contact_id: 'required',
            reminder_calendar: 'required',
            contact_channel: 'required',
            switch_reminders_to: 'required'
         }
      } else {
         title.text('<?= _l('internal_note') ?>');

         formFailed.hide();
         formSuccess.hide();
         formGeneral.hide();
      }

      // Add custom validation method for F2F attachments
      $.validator.addMethod('requireIfF2F', function(value, element) {
         // Skip validation if it's a Contact Again Note (type = 2)
         if (type == <?= $this->misc_model->CUSTOMER_TYPE_CONTACT_AGAIN ?>) {
            return true;
         }
         
         if ($('select[name="contact_channel"]').val() !== '7') {
            return true; // Not F2F, so not required
         }
         // For F2F, check if there's a file selected
         return $(element).val() !== '';
      }, '<?php echo _l('f2f_meeting_attachment_required'); ?>');

      // Add validation rules only if not a Contact Again Note
      if (type != <?= $this->misc_model->CUSTOMER_TYPE_CONTACT_AGAIN ?>) {
         vRules.attachments = {
            requireIfF2F: true
         };
      }

      // Initialize form validation
      appValidateForm($("#add_note_form"), vRules);

      // Add event listener for contact channel change
      $('select[name="contact_channel"]').on('change', function() {
         const isF2F = $(this).val() === '7';
         const attachmentLabel = $('#attachments').closest('.form-group').find('label');
         
         // Toggle required indicator
         if (isF2F) {
            attachmentLabel.find('.text-danger').remove();
            attachmentLabel.append(' <span class="text-danger">*</span>');
         } else {
            attachmentLabel.find('.text-danger').remove();
         }
         
         // Re-validate the form to update the validation state
         $("#add_note_form").valid();
      });

      // Initial check in case F2F is pre-selected (editing existing note)
      if ($('select[name="contact_channel"]').val() === '7') {
         $('#attachments').closest('.form-group').find('label').append(' <span class="text-danger">*</span>');
      }
   }

   function editNote(noteId) {
      requestGetJSON('misc/get_note/' + noteId).done(function(response) {
         const {
            rel_type,
            type,
            description,
            contact_id,
            contact_channel,
            reminder_calendar,
            action,
            follow_up,
            detailed_signal,
            potential_rate,
            attachments,
            link,
            switch_reminders_to,
            send_mail_this_reminder,
            client
         } = response.data

         $('#add_note_modal').find('.modal-title').append(`<br><small><a href="/admin/clients/client/${client.userid}">${client.company}</a></small>`);

         $('#add_note_modal #description').val(description.replace(/(<|<)br\s*\/*(>|>)/g, " "))
         $('#add_note_modal select[id="contact_id"]').val(contact_id);
         $('#add_note_modal select[id="contact_channel"]').val(contact_channel);
         $('#add_note_modal #reminder_calendar').val(moment(reminder_calendar).format('DD.MM.YYYY H:mm'));
         $('#add_note_modal #action').val(action);
         $('#add_note_modal #follow_up').val(follow_up);
         $('#add_note_modal [name="detailed_signal[]"]').selectpicker('val', detailed_signal);
         $('#add_note_modal #potential_rate').val(potential_rate);
         $('#add_note_modal #link').val(link);
         $('#add_note_modal #switch_reminders_to').val(switch_reminders_to);
         $('#add_note_modal #send_mail_this_reminder').prop('checked', Number(send_mail_this_reminder));
         $('#add_note_modal .selectpicker').selectpicker('refresh');
      })
   }

   function showHistoryNote(noteId) {
      requestGetJSON('misc/get_history_note/' + noteId).done(function(response) {
         var content = '';
         response.data.forEach(element => {
            const titles = {
               description: '<?php echo _l('note_description') ?>',
               action: '<?php echo _l('action') ?>',
               attachments: '<?php echo _l('attachments') ?>',
               contact_channel: '<?php echo _l('contact_channel') ?>',
               contact_id: '<?php echo _l('contact') ?>',
               detailed_signal: '<?php echo _l('detailed_signal') ?>',
               follow_up: '<?php echo _l('follow_up') ?>',
               link: '<?php echo _l('link') ?>',
               potential_rate: '<?php echo _l('potential_rate') ?>',
               reminder_calendar: '<?php echo _l('reminder_calendar') ?>',
               send_mail_this_reminder: '<?php echo _l('send_mail_this_reminder') ?>',
               switch_reminders_to: '<?php echo _l('switch_reminders_to') ?>',
            }

            const getChanges = (oldValue, newValue) => {
               const changes = [];
               Object.keys(oldValue).forEach(key => {
                  if (oldValue[key] !== newValue[key]) {
                     if (['attachments', 'detailed_signal'].includes(key)) {
                        if (JSON.stringify(oldValue[key]) === JSON.stringify(newValue[key])) {
                           return
                        }
                        switch (key) {
                           case 'attachments':
                              oldValue[key] = oldValue[key] ? `<a href="/download/file/note/${oldValue[key].attachment_key}">${oldValue[key].file_name}</a>` : '';
                              newValue[key] = newValue[key] ? `<a href="/download/file/note/${newValue[key].attachment_key}">${newValue[key].file_name}</a>` : '';
                              break;
                           case 'detailed_signal':
                              oldValue[key] = `<div>${Object(oldValue[key]).map(value => `${value}<br>`).join('')}</div>`;
                              newValue[key] = `<div>${Object(newValue[key]).map(value => `${value}<br>`).join('')}</div>`;
                              break;
                        }
                     }
                     changes.push(`<b>${titles[key]}</b>: ${oldValue[key]}` +
                        '<i class="fa fa-long-arrow-right mleft10 mright10"></i>' +
                        `${newValue[key]}<br>`);
                  }
               });

               if (changes.length) {
                  content += `<div>
                        <b>${element.staff}</b> (${element.date})
                        <div class="description">${changes.join('')}</div>
                    </div><br>`;
               }
            };

            const {
               old_value,
               new_value
            } = element.data;

            if (old_value && new_value) {
               delete old_value.updated_at
               delete new_value.updated_at
               getChanges(old_value, new_value);
            }
         });
         $('#note_history').html(content)
      })
   }

   window.addEventListener('DOMContentLoaded', function () {
      if (window.location.search && window.location.search.indexOf('open_note_form=1') > -1) {
         const addNoteBtn = document.getElementById('newContactNoteBtn');
         addNoteBtn && addNoteBtn.click();
      }
   });
</script>

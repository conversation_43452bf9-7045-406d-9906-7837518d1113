<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="tab-content">
                            <div id="jobs-crawler" class="tab-pane fade in active">
                                <div class="row _filters">
                                    <div class="col-md-4">
                                        <div id="date-range" class="mbot15">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label for="from_date" class="control-label">From</label>
                                                    <div class="input-group date">
                                                        <input type="text" class="form-control" id="from_date" onchange="reloadCrawlerTable()" value="<?= $today ?>" name="from_date" autocomplete="off">
                                                        <div class="input-group-addon">
                                                            <i class="fa fa-calendar calendar-icon"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="to_date" class="control-label">To</label>
                                                    <div class="input-group date">
                                                        <input type="text" class="form-control" id="to_date" onchange="reloadCrawlerTable()" value="<?= $today ?>" name="to_date" autocomplete="off">
                                                        <div class="input-group-addon">
                                                            <i class="fa fa-calendar calendar-icon"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <?= render_select('crawl_page', get_crawl_pages(), ['value', 'text'], _l('crawler_from_company'), '', ['onchange' => "reloadCrawlerTable()"]); ?>
                                    </div>
                                </div>
                                <div class="clearfix mtop20"></div>
                                <?php
                                $table_data = [
                                    _l('crawler_table_header_posted_at'),
                                    _l('crawler_table_header_from_page'),
                                    _l('crawler_table_header_company'),
                                    _l('crawler_table_header_title'),
                                    _l('crawler_table_header_skills'),
                                    [
                                        'name' => _l('crawler_table_header_description'),
                                        'th_attrs' => [
                                            'width' => '30%',
                                            'class' => 'truncate'
                                        ]
                                    ],
                                    _l('crawler_table_header_salary'),
                                    _l('crawler_table_header_location'),
                                ];

                                render_datatable($table_data, 'jobs-crawler');
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php init_tail(); ?>
<script>
    let params = {};

    $.each($('#jobs-crawler ._filters select'), function() {
        params[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
    });
    $.each($('#jobs-crawler ._filters input'), function() {
        params[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
    });

    const crawlerTable = initDataTable(
        '.table-jobs-crawler',
        admin_url + 'crawler/table',
        undefined,
        [3, 5, 6],
        params,
        [0, 'desc']
    );

    function reloadCrawlerTable() {
        crawlerTable.ajax.reload();
    }

    function viewFullDescription(aTag, jobTitle) {
        $('#view_job_description h4.modal-title').html(jobTitle);
        $('#view_job_description #modal-body').html($(aTag).closest('td').find('div.full-description').html());
    }

    function getDate(element) {
        var date;
        try {
            date = $.datepicker.parseDate('yy-mm-dd', element.value);
            console.log('element.value', element.value, date);
        } catch (error) {
            date = null;
        }

        return date;
    }

    const from = $('#from_date').datetimepicker({
        timepicker: false,
        format: 'Y-m-d'
    });
    const to = $('#to_date').datetimepicker({
        timepicker: false,
        format: 'Y-m-d'
    });

    from.on('change', function() {
        to.datetimepicker({
            minDate: getDate(this)
        });
    });

    to.on('change', function() {
        from.datetimepicker({
            maxDate: getDate(this)
        });
    });
</script>
<?php include_once(APPPATH . 'views/admin/clients/modals/view_crawl_job_description_modal.php'); ?>
</body>

</html>

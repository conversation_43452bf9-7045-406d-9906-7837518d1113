<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
   <div class="content">
      <div class="row">
         <div class="col-md-12">
            <div class="panel_s">
               <div class="panel-body">

                  <ul class="nav nav-tabs">
                     <li class="active activity-tab">
                        <a data-toggle="tab" href="#activity">Note Activity</a>
                     </li>
                     <li class="plan-tab">
                        <a data-toggle="tab" href="#plan">Plan</a>
                     </li>
                  </ul>

                  <div class="tab-content">
                     <div id="activity" class="tab-pane fade in active">
                        <div class="row _filters">
                           <div class="col-md-2">
                              <?php echo render_select('customer_admins', $staff, ['staffid', ['firstname', 'lastname']], _l('customer_admins'), '', ['onchange' => "reloadDataActivity()"]); ?>
                           </div>
                           <div class="col-md-2">
                              <?php echo render_select('note_creator', $staff, ['staffid', ['firstname', 'lastname']], _l('note_creator'), '', ['onchange' => "reloadDataActivity()"]); ?>
                           </div>
                           <div class="col-md-2">
                              <?php echo render_select('potential_rate', POTENTIAL_RATE_NOTE_OPTIONS, ['value', 'text'], _l('potential_rate'), '', ['onchange' => "reloadDataActivity()"]); ?>
                           </div>
                           <div class="col-md-2">
                              <?php echo render_select('hotline', [
                                 [
                                    'value' => 'yes',
                                    'text' => 'Yes',
                                 ], [
                                    'value' => 'no',
                                    'text' => 'No',
                                 ],
                              ], ['value', 'text'], 'Hotline', '', ['onchange' => "reloadDataActivity()"]); ?>
                           </div>
                           <div class="col-md-4">
                              <div id="date-range" class="mbot15">
                                 <div class="row">
                                    <div class="col-md-6">
                                       <label for="from_date" class="control-label">From</label>
                                       <div class="input-group date">
                                          <input type="text" class="form-control datepicker" id="from_date" onchange="reloadDataActivity()" name="from_date" autocomplete="off">
                                          <div class="input-group-addon">
                                             <i class="fa fa-calendar calendar-icon"></i>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-md-6">
                                       <label for="to_date" class="control-label">To</label>
                                       <div class="input-group date">
                                          <input type="text" class="form-control datepicker" id="to_date" onchange="reloadDataActivity()" name="to_date" autocomplete="off">
                                          <div class="input-group-addon">
                                             <i class="fa fa-calendar calendar-icon"></i>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="col-md-2">
                              <?= render_select('department', get_list_department(), ['departmentid', 'name'], _l('department') . " (Customer admin's Team)", '', ['onchange' => "reloadDataActivity()"]); ?>
                           </div>
                           <div class="col-md-2">
                              <label for="type" class="control-label">Type Note</label>
                              <div class="form-group">
                                 <select class="form-control selectpicker" name="type" id="type" onchange="reloadDataActivity()">
                                    <option></option>
                                    <option value=<?= $this->misc_model->CUSTOMER_TYPE_INTERNAL ?>><?= _l('internal_note') ?></option>
                                    <option value=<?= $this->misc_model->CUSTOMER_TYPE_CONTACT_SUCCESS ?>><?= _l('contact_success_note') ?></option>
                                    <option value=<?= $this->misc_model->CUSTOMER_TYPE_CONTACT_AGAIN ?>><?= _l('contact_again_note') ?></option>
                                 </select>
                              </div>
                           </div>
                           <div class="col-md-2">
                              <?= render_select('contact_channel', CONTACT_CHANNEL_NOTE_OPTIONS, ['value', 'text'], _l('contact_channel'), '', ['onchange' => "reloadDataActivity()"]); ?>
                           </div>
                        </div>

                        <div class="clearfix mtop20"></div>
                        <?php
                        $table_data = array();

                        $table_data = [
                           'Ghi chú',
                           'Company ID',
                           'Company Name',
                           _l('type_of_customer'),
                           _l('usage_behavior'),
                           'Company Contact',
                           'Customer Admin',
                           'Note Creator',
                           'Note Detail',
                           'Last Feedback',
                           'Call Duration',
                           'Hotline gần nhất',
                           'Phân loại độ tiềm năng',
                           'Add to favorite',
                        ];

                        render_datatable($table_data, 'activity-notes');
                        ?>
                     </div>
                     <div id="plan" class="tab-pane fade">
                        <div class="row _filters">
                           <div class="col-md-2">
                              <?php echo render_select('plan_customer_admins', $staff, ['staffid', ['firstname', 'lastname']], _l('customer_admins'), '', ['onchange' => "reloadDataPlan()"]); ?>
                           </div>
                           <div class="col-md-2">
                              <?= render_select('plan_department', get_list_department(), ['departmentid', 'name'], _l('department') . " (Customer admin's Team)", '', ['onchange' => "reloadDataPlan()"]); ?>
                           </div>
                           <div class="col-md-4">
                              <div id="date-range" class="mbot15">
                                 <div class="row">
                                    <div class="col-md-6">
                                       <label for="from_date" class="control-label">From</label>
                                       <div class="input-group date">
                                          <input type="text" class="form-control datepicker" id="from_date" onchange="reloadDataPlan()" name="plan_from_date" autocomplete="off">
                                          <div class="input-group-addon">
                                             <i class="fa fa-calendar calendar-icon"></i>
                                          </div>
                                       </div>
                                    </div>
                                    <div class="col-md-6">
                                       <label for="to_date" class="control-label">To</label>
                                       <div class="input-group date">
                                          <input type="text" class="form-control datepicker" id="to_date" onchange="reloadDataPlan()" name="plan_to_date" autocomplete="off">
                                          <div class="input-group-addon">
                                             <i class="fa fa-calendar calendar-icon"></i>
                                          </div>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>

                        <div class="clearfix mtop20"></div>
                        <?php
                        $table_data = array();

                        $table_data = [
                           'Company ID',
                           'Company Name',
                           'Customer Admin',
                           'Target của company đó',
                           'Created At',
                        ];

                        render_datatable($table_data, 'plans');
                        ?>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
<?php init_tail(); ?>
<script>
   let activityParams = {};
   let planParams = {};

   $.each($('#activity ._filters select'), function() {
      activityParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
   });
   $.each($('#activity ._filters input'), function() {
      activityParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
   });

   $.each($('#plan ._filters select'), function() {
      planParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
   });
   $.each($('#plan ._filters input'), function() {
      planParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
   });

   const dataActivity = initDataTable(
      '.table-activity-notes',
      admin_url + 'activity_notes/table',
      'undefined',
      'undefined',
      activityParams,
      [0, 'desc']
   );

   const dataPlan = initDataTable(
      '.table-plans',
      admin_url + 'favorite_clients/table',
      'undefined',
      'undefined',
      planParams,
      [4, 'desc']
   );

   function reloadDataActivity() {
      dataActivity.ajax.reload();
   }

   function reloadDataPlan() {
      dataPlan.ajax.reload();
   }
</script>
</body>

</html>
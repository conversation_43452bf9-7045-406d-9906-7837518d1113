<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="_buttons">
                            <a href="#" onclick="new_status(); return false;"
                               class="btn btn-info pull-left display-block">
                                <?php echo _l('estimate_request_new_status'); ?>
                            </a>
                        </div>
                        <div class="clearfix"></div>
                        <hr class="hr-panel-heading"/>
                        <?php if (count($statuses) > 0) { ?>
                            <table class="table dt-table" data-order-col="1" data-order-type="asc">
                                <thead>
                                <th><?php echo _l('id'); ?></th>
                                <th><?php echo _l('estimate_request_status_table_name'); ?></th>
                                <th><?php echo _l('options'); ?></th>
                                </thead>
                                <tbody>
                                <?php foreach ($statuses as $status) { ?>
                                    <tr>
                                        <td>
                                            <?php echo $status['id']; ?>
                                        </td>
                                        <td><a href="#"
                                               onclick="edit_status(this,<?php echo $status['id']; ?>);return false;"
                                               data-color="<?php echo $status['color']; ?>"
                                               data-name="<?php echo $status['name']; ?>"
                                               data-order="<?php echo $status['statusorder']; ?>"><?php echo $status['name']; ?></a><br/>
                                            <span class="text-muted">
											<?php echo _l('estimate_request_table_total', total_rows(db_prefix() . 'estimate_requests', array('status' => $status['id']))); ?></span>
                                        </td>
                                        <td>
                                            <a href="#"
                                               onclick="edit_status(this,<?php echo $status['id']; ?>);return false;"
                                               data-color="<?php echo $status['color']; ?>"
                                               data-name="<?php echo $status['name']; ?>"
                                               data-order="<?php echo $status['statusorder']; ?>"
                                               class="btn btn-default btn-icon"><i
                                                        class="fa fa-pencil-square-o"></i></a>
                                            <?php
                                                if (empty($status['flag'])) { ?>
                                                    <a href="<?php echo admin_url('estimate_request/delete_status/' . $status['id']); ?>"
                                                       class="btn btn-danger btn-icon _delete"><i
                                                                class="fa fa-remove"></i></a>
                                                <?php } ?>
                                        </td>
                                    </tr>
                                <?php } ?>
                                </tbody>
                            </table>
                        <?php } else { ?>
                            <p class="no-margin"><?php echo _l('estimate_request_statuses_not_found'); ?></p>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php include_once(APPPATH . 'views/admin/estimate_request/status.php'); ?>
<?php init_tail(); ?>
</body>
</html>

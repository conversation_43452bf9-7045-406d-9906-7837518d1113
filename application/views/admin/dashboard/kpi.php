<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <form method="post" id="setting_kpi">
        <div class="content content_kpi">

            <div class="row">

                <div class="col-md-3">
                    <div class="form-group">
                        <label class="control-label"><?php echo _l('month') ?></label>
                        <div class="input-group date"><input type="text" id="month_kpi" name="month_kpi"
                                class="form-control datepicker_my" value="<?php echo date('m.Y',time()) ?>" autocomplete="off">
                            <input type="hidden" id="kpi_id" name="kpi_id" value="">
                            <div class="input-group-addon">
                                <i class="fa fa-calendar calendar-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="control-label"><?php echo _l('team') ?></label>
                        <div>
                            <?= render_select('team_sales', get_group_team_sale(), ['value', 'text'], '', [0], [], [], '', '', false); ?>
                        </div>
                    </div>
                </div>

            </div>

            <div class="kpi_process">
                <div class="title_kpi"><?php echo _l('title_kpi_process') ?></div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="control-label"><?php echo _l('total_working_days_holder') ?></label>
                            <div>
                                <input onkeyup="this.value=FormatNumber(this.value);" autocomplete="off" type="text" name="total_working_days_team" class="form-control"
                                    value="" placeholder="<?php echo _l('total_working_days_note') ?>">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="control-label"><?php echo _l('call') ?></label>
                            <div>
                                <input onkeyup="this.value=FormatNumber(this.value);" autocomplete="off" type="text"
                                    name="total_call_per_day" class="form-control number_format" value=""
                                    placeholder="<?php echo _l('call_holder') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="control-label"><?php echo _l('total_talk_per_day') ?></label>
                            <div>
                                <input onkeyup="this.value=FormatNumber(this.value);" autocomplete="off" type="text"
                                    name="total_talk_per_day" class="form-control number_format" value=""
                                    placeholder="<?php echo _l('talk_holder') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="control-label"><?php echo _l('connect') ?></label>
                            <div>
                                <input onkeyup="this.value=FormatNumber(this.value);" autocomplete="off" type="text"
                                    name="total_note_per_day" class="form-control number_format" value=""
                                    placeholder="<?php echo _l('connect_holder') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="control-label">F2F Meeting</label>
                            <div>
                                <input onkeyup="this.value=FormatNumber(this.value);" autocomplete="off" type="text"
                                    name="total_f2f_meeting_note_per_day" class="form-control number_format" value=""
                                    placeholder="Số F2F meeting/ngày">
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="kpi_process">
                <div class="title_kpi"><?php echo _l('kpi_sales') ?></div>
                    <div class="row">                
                        <div class="col-md-4">
                        <div class="form-group">
                            <label class="control-label kpi_revenue_team"><?php echo _l('kpi_revenue_team') ?> <span></span></label>
                            <div>
                                <input onkeyup="this.value=FormatNumber(this.value);" autocomplete="off" type="text"
                                    name="kpi_revenue_team" class="form-control number_format" value=""
                                    placeholder="<?php echo _l('vnd_month') ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="kpi_revenue">
                    <div class="kpi_revenue_title">
                        <div class="row">
                        <div class="col-md-1">
                                <?php echo _l('wieght') ?>
                            </div>
                            <div class="col-md-2">
                                <?php echo _l('salespersion') ?>
                            </div>
                            <div class="col-md-2">
                                <?php echo _l('total_working_days_note') ?>
                            </div>
                            <div class="col-md-2">
                                <?php echo _l('kpi_sales') ; echo _l('vnd_month') ?>
                            </div>
                            <div class="col-md-5">
                                <?php echo _l('days_off') ?>
                            </div>
                        </div>
                    </div>

                    <div class="kpi_revenue_content result_search_kpi">

                    </div>


                </div>
            </div>

            <div class="row">
                <div class="btn-bottom-toolbar text-right btn-toolbar-container-out">
                    <span class="btn btn-info submit_kpi_setting"><?php echo _l('submit'); ?></span>
                </div>
            </div>

            <div class="btn-bottom-pusher"></div>

        </div>



    </form>

</div>


<?php init_tail(); ?>

<script>
$(document).ready(function() {

    get_kpi();

    // Genarate datapicker month and year
    $('.datepicker_my').map(function() {
        var render_datapicker_my = '<div id="calendar_my" class="datepicker-months"><table class="table-condensed"><thead><tr><th colspan="7" class="datepicker-title" style="display: none;"></th></tr><tr><th class="prev">«</th><th colspan="5" class="datepicker-switch">2023</th><th class="next">»</th></tr></thead><tbody><tr><td colspan="7"><span month="01" class="month m01">Jan</span><span month="02" class="month m02">Feb</span><span month="03" class="month m03">Mar</span><span month="04" class="month m04">Apr</span><span month="05" class="month m05">May</span><span month="06" class="month m06">Jun</span><span month="07" class="month m07">Jul</span><span month="08" class="month m08">Aug</span><span month="09" class="month m09">Sep</span><span month="10" class="month m10">Oct</span><span month="11" class="month m11">Nov</span><span month="12" class="month m12">Dec</span></td></tr></tbody><tfoot><tr><th colspan="7" class="today" style="display: none;">Today</th></tr><tr><th colspan="7" class="clear" style="display: none;">Clear</th></tr></tfoot></table></div>';
        $(this).parent().append(render_datapicker_my);

        // Gán giá trị
        var value = $(this).val();
        selected_month_year(value)
    });


    function selected_month_year(string)
    {
        var arr = string.split('.');
        $('.datepicker-months .datepicker-switch').html(arr[1]);
        $('.datepicker-months .month').removeClass('active');
        $('.datepicker-months .m'+ arr[0]).addClass('active');
    }

    // Button next
    $(document).on("click", ".datepicker-months .next", function() {
        var year_current = parseInt($('.datepicker-months .datepicker-switch').html());
        $('.datepicker-months .datepicker-switch').html(year_current + 1);
        $('.datepicker-months .month').removeClass('active');
    });

    // Button prev
    $(document).on("click", ".datepicker-months .prev", function() {
        var year_current = parseInt($('.datepicker-months .datepicker-switch').html());
        $('.datepicker-months .datepicker-switch').html(year_current - 1);
        $('.datepicker-months .month').removeClass('active');
    });

    // Chọn tháng
    $(document).on("click", ".datepicker-months .month", function(event) {
        event.preventDefault()
        var month_current = $(this).attr('month');
        $('.datepicker-months .month').removeClass('active');
        $(this).addClass('active');

        var year_current = parseInt($('.datepicker-months .datepicker-switch').html());

        var day_current = month_current + '.' + year_current;
        $('.datepicker_my').val(day_current);

        $('.datepicker-months').toggle();

        get_kpi();
        
    });

    $(document).on("click", ".datepicker_my", function() {
        $('.datepicker-months').toggle();
    });

    const calendar = document.getElementById('calendar_my');
    const showCalendarButton = document.getElementById('month_kpi');
    document.addEventListener('click', (event) => {
      if (!calendar.contains(event.target) && event.target !== showCalendarButton) {
        calendar.style.display = 'none';
      }
    });

    $('select[name=team_sales]').change(function() {
        get_kpi();
    });

    $('input[name=total_working_days_team]').change(function() {
        var value = $(this).val();
        $('input[name=total_working_days]').map(function() {
            if($(this).val() == '')
                $(this).val(value);
        });
    });

    $('.submit_kpi_setting').click(function() {
        $('#setting_kpi').find('.error_input_kpi').removeClass('error_input_kpi');
        var flag = true;

        var month_kpi = $('input[name=month_kpi]').val();
        var team_sales = $('select[name=team_sales]').val();

        if (month_kpi == '') {
            alert_float('danger', '<?php echo _l('month_null'); ?>');
            $('input[name=month_kpi]').addClass('error_input_kpi');
            return false;
        }

        var arr_month_kpi = month_kpi.split('.');
        var month = arr_month_kpi[0]; // Lấy phần tháng
        var year = arr_month_kpi[1]
        var data = [];
        var data_check = [];
        $('.result_search_kpi .kpi_item').map(function() {

            var day_off = [];
            var day_off_check = [];
           
            $(this).find('.item_day_off').map(function() {
                var date_day_off = $(this).find('input[name=date_day_off]').val()
                var time_day_off = $(this).find('select[name=time_day_off]').val()

                day_off.push({
                    'date_day_off': date_day_off,
                    'time_day_off': time_day_off,
                });

                day_off_check.push({
                    'date_day_off': date_day_off,
                    'time_day_off': time_day_off,
                    'this' : $(this)
                });

            });

            var staff_id = $(this).find('input[name=staff_id]').val();
            var staff_name = $(this).find('input[name=staff_name]').val();
            var total_working_days = $(this).find('input[name=total_working_days]').val();
            var target_amount = $(this).find('input[name=target_amount]').val();

            var staff = {
                'staff_id': staff_id,
                'staff_name': staff_name,
                'total_working_days': total_working_days,
                'target_amount': target_amount,
                'day_off': day_off
            }

            var staff_check = {
                'staff_id': staff_id,
                'staff_name': staff_name,
                'total_working_days': total_working_days,
                'target_amount': target_amount,
                'day_off': day_off_check
            }

            data.push(staff);
            data_check.push(staff_check);

        });
        
        // Kiểm tra thời gian, thời lượng day off
        data_check.forEach(function(value, index){
            if(!flag)
                return;
            
            var array_day_off = [];
            value.day_off.forEach(function(staff, index_staff){
                if(!flag)
                    return;

                 // Kiểm tra thời gian không được để trống
                 if (staff.date_day_off == '') {
                    staff.this.find('input[name=date_day_off]').addClass(
                        'error_input_kpi');
                    alert_float('danger', '<?php echo _l('error_day_off'); ?>');
                    flag = false;
                    return;
                }

                // Kiểm tra day off đã tồn tại trong mảng chưa
                if (array_day_off.includes(staff.date_day_off) || staff.date_day_off == '') {
                    staff.this.find('input[name=date_day_off]').addClass(
                        'error_input_kpi');
                    alert_float('danger', '<?php echo _l('error_day_off_exits'); ?>');
                    flag = false;
                    return;
                }

                // Kiểm tra tháng của day off bằng với month_kpi
                if (staff.date_day_off != '') {
                    var arr_date_day_off = staff.date_day_off.split('.');
                    if (arr_date_day_off[1] != month || arr_date_day_off[2] != year) {
                        staff.this.find('input[name=date_day_off]').addClass(
                            'error_input_kpi');
                        alert_float('danger', '<?php echo _l('error_day_off_month_exits'); ?>');
                        flag = false;
                        return;
                    }
                }

                // Kiểm tra thời lượng không được để trống
                if (staff.time_day_off == 0) {
                    staff.this.find('select[name=time_day_off]').addClass(
                        'error_input_kpi');
                    alert_float('danger', '<?php echo _l('error_day_off_time'); ?>');
                    flag = false;
                    return;
                }

                array_day_off.push(staff.date_day_off);
            })

        })


        if (flag) {
            var kpi_id = $('input[name=kpi_id]').val();
            var total_call_per_day = $('input[name=total_call_per_day]').val();
            var total_talk_per_day = $('input[name=total_talk_per_day]').val();
            var total_note_per_day = $('input[name=total_note_per_day]').val();
            var total_f2f_meeting_note_per_day = $('input[name=total_f2f_meeting_note_per_day]').val();
            var kpi_revenue_team  = $('input[name=kpi_revenue_team]').val();
            $.ajax({
                type: 'post',
                dataType: "json",
                url: site_url + 'admin/ranking/confirm_kpi',
                data: {
                    kpi_id: kpi_id,
                    month_kpi: month_kpi,
                    team_sales: team_sales,
                    total_call_per_day: total_call_per_day,
                    total_talk_per_day: total_talk_per_day,
                    total_note_per_day: total_note_per_day,
                    total_f2f_meeting_note_per_day: total_f2f_meeting_note_per_day,
                    kpi_revenue_team: kpi_revenue_team,
                    kpi: data,
                },
                success: function(res) {
                    //console.log(res);
                    if (res.error == 1) {
                        alert_float('danger', res.mess);
                        $('#' + res.field).addClass('error_input_kpi');
                    }else{
                        alert_float('success', res.mess);
                    }
                }
            });
        }

    });



    function get_kpi() {
        var team_id = $('select[name=team_sales]').val();
        var month_kpi = $('#month_kpi').val();
        var team_name = $('select[name=team_sales] option:selected').text();

        if (!month_kpi)
        {
            alert_float('danger', '<?php echo _l('month_null'); ?>');
            return '';
        }
            
        $.ajax({
            type: 'get',
            dataType: "json",
            url: site_url + 'admin/ranking/get_kpi_team',
            data: {
                month_kpi: month_kpi,
                team_id: team_id,
            },
            success: function(res) {
                if(team_id == 0){
                    $('input[name=kpi_revenue_team]').prop('disabled',true);
                    $('input[name=kpi_revenue_team]').val('');
                }else{
                    $('input[name=kpi_revenue_team]').prop('disabled',false);
                    $('input[name=kpi_revenue_team]').val(res.kpi['kpi_revenue_team']);
                }
                $('.kpi_revenue_team span').html(team_name);

                $('.result_search_kpi').html(res.html);
                $('input[name=kpi_id]').val(res.kpi['id']);
                $('input[name=total_working_days_team]').val('');
                $('input[name=total_call_per_day]').val(res.kpi['total_call_per_day']);
                $('input[name=total_talk_per_day]').val(res.kpi['total_talk_per_day']);
                $('input[name=total_note_per_day]').val(res.kpi['total_note_per_day']);
                $('input[name=total_f2f_meeting_note_per_day]').val(res.kpi['total_f2f_meeting_note_per_day']);
                appDatepicker();
                number_format();
                if (res.error == 0) {
                    console.log(res.mess);
                    //alert(res.mess);
                }
            }
        });

    }


    $(document).on("click", "i.fa.fa-trash.calendar-icon", function() {
        $(this).parent().parent().parent().remove();
    });


    $(document).on("click", ".day_off_titile span", function() {
        var html =
            ' <div class="item_day_off"><div class="row"><div class="col-md-5"><div class="input-group date "><input autocomplete="off" placeholder="<?php echo _l('select_day') ?>" type="text" autocomplete="off" class="form-control datepicker" name="date_day_off"/><div class="input-group-addon"><i class="fa fa-calendar calendar-icon"></i></div></div></div><div class="col-md-5"><select class="form-control" name="time_day_off"><option value="0"><?php echo _l('select_time') ?></option><option value="0.5">0.5</option><option value="1">1</option></select></div><div class="col-md-2"><i class="fa fa-trash calendar-icon"></i></div></div></div>';

        $(this).parent().parent().prepend(html);
        appDatepicker();
    });


});
</script>

<script type="text/javascript">
function number_format() {
    $('.number_format').map(function() {
        $(this).val(FormatNumber($(this).val()));
    });
}

function FormatNumber(str) {
    str = str.toString();
    var strTemp = GetNumber(str);
    if (strTemp.length <= 3)
        return strTemp;
    strResult = "";
    for (var i = 0; i < strTemp.length; i++)
        strTemp = strTemp.replace(",", "");
    var m = strTemp.lastIndexOf(".");
    if (m == -1) {
        for (var i = strTemp.length; i >= 0; i--) {
            if (strResult.length > 0 && (strTemp.length - i - 1) % 3 == 0)
                strResult = "," + strResult;
            strResult = strTemp.substring(i, i + 1) + strResult;
        }
    } else {
        var strphannguyen = strTemp.substring(0, strTemp.lastIndexOf("."));
        var strphanthapphan = strTemp.substring(strTemp.lastIndexOf("."), strTemp.length);
        var tam = 0;
        for (var i = strphannguyen.length; i >= 0; i--) {

            if (strResult.length > 0 && tam == 4) {
                strResult = "," + strResult;
                tam = 1;
            }

            strResult = strphannguyen.substring(i, i + 1) + strResult;
            tam = tam + 1;
        }
        strResult = strResult + strphanthapphan;
    }
    strResult = strResult.replace(/\.00$/, '');
    return strResult;
}

function GetNumber(str) {
    var count = 0;
    for (var i = 0; i < str.length; i++) {
        var temp = str.substring(i, i + 1);
        if (!(temp == "," || temp == "." || (temp >= 0 && temp <= 9))) {
            alert_float('danger', "Vui lòng nhập số");
            return str.substring(0, i);
        }
        if (temp == " ")
            return str.substring(0, i);
        if (temp == ".") {
            if (count > 0)
                return str.substring(0, ipubl_date);
            count++;
        }
    }
    return str;
}

function IsNumberInt(str) {
    for (var i = 0; i < str.length; i++) {
        var temp = str.substring(i, i + 1);
        if (!(temp == "." || (temp >= 0 && temp <= 9))) {
            alert_float('danger', "Vui lòng nhập số");
            return str.substring(0, i);
        }
        if (temp == ",") {
            alert_float('danger', "Vui lòng nhập số");
            return str.substring(0, i);
        }
    }
    return str;
}
</script>

</body>

</html>

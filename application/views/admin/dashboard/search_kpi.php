<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php
    $weight = 0;
    foreach($result as $item){
        $weight++;
    ?>
<div class="kpi_item">
    <div class="row">
         <div class="col-md-1 kpi_item_weight kpi_item_name">
         <?php echo $weight?>
        </div>
        <div class="col-md-2 kpi_item_name">
            <?php echo $item['firstname'] . ' ' . $item['lastname'] ?>
            <input type="hidden" name="staff_id" class="form-control" value="<?php echo $item['staffid'] ?>" />
            <input type="hidden" name="staff_name" class="form-control"
                value="<?php echo $item['firstname'] . ' ' . $item['lastname'] ?>" />
        </div>
        <div class="col-md-2">
            <input onkeyup="this.value=FormatNumber(this.value);" autocomplete="off" type="text" name="total_working_days" class="form-control" value="<?php echo $item['total_working_days'] ?>"
                placeholder="<?php echo _l('total_working_days_holder') ?>">
        </div>
        <div class="col-md-2">
            <input onkeyup="this.value=FormatNumber(this.value);" autocomplete="off" type="text" name="target_amount" class="form-control number_format" value="<?php echo $item['target_amount'] ?>"
                placeholder="<?php echo _l('sales_target') ?>">
        </div>
        <div class="col-md-5 add_day_off">
        <?php
        if(isset($item['day_off']))
        {
            foreach($item['day_off'] as $day_off){
            ?>
                <div class="item_day_off">
                    <div class="row">
                        <div class="col-md-5">
                            <div class="input-group date ">
                                <input autocomplete="off" value="<?php echo $day_off['date'] ?>" placeholder="<?php echo _l('select_day') ?>"
                                    type="text" autocomplete="off" class="form-control datepicker" name="date_day_off" />
                                <div class="input-group-addon">
                                    <i class="fa fa-calendar calendar-icon"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <select class="form-control" name="time_day_off">
                            <?php foreach($array_time_kpi as $time){
                            ?>
                                <option <?php if($time['value'] == $day_off['total_days']) echo 'selected=selected' ?> value="<?php echo $time['value'] ?>"><?php echo $time['text'] ?></option>
                            <?php 
                            } 
                            ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <i class="fa fa-trash calendar-icon"></i>
                        </div>
                    </div>
                </div>
                <?php
            }
        }
        ?>
            <div class="day_off_titile"><span>+ Thêm ngày nghỉ</span></div>
        </div>
    </div>
</div>
<?php
}
?>

<?php defined('BASEPATH') or exit('No direct script access allowed');

// Define chart items and types for reuse throughout the file
$chart_items = [
    'estimates' => ['title' => 'Quotations',],
    'purchase_orders' => ['title' => _l('invoices'),],
    'invoices' => ['title' => 'Invoice',]
];

$chart_types = [
    'amount' => ['title' => 'Amount (VND) - tax excluded',],
    'quantity' => ['title' => 'Quantity',]
];
?>
<div class="widget" id="widget-sales-activities" data-name="Sales Activities">
    <div class="panel_s">
        <div class="panel-body">
            <div class="widget-dragger"></div>
            <h4 class="pull-left">Sales Activities</h4>
            <div class="clearfix"></div>
            <hr class="hr-panel-heading-dashboard">
            <div class="row">
                <?php foreach ($chart_items as $item_key => $item_data) : ?>
                    <div class="col-md-4">
                        <h4 class="text-center"><?= $item_data['title'] ?></h4>
                    </div>
                <?php endforeach; ?>
            </div>
            <?php foreach ($chart_types as $chart_type => $chart_type_data) : ?>
                <h5 class="pull-left"><?= $chart_type_data['title'] ?></h5>
                <div class="clearfix"></div>
                <hr class="hr-panel-heading-dashboard">
                <div class="row">
                    <?php foreach ($chart_items as $item_key => $item_data) : ?>
                        <div class="col-md-4">
                            <div class="panel_s">
                                <div class="panel-body" style="height: 250px">
                                    <canvas id="<?= $item_key ?>-<?= $chart_type ?>-chart"></canvas>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php
/** @see \Dashboard_model::get_dashboard_metrics */
// Set default values for data if not provided
$estimates = array_merge([
    'accepted' => ['count' => 0, 'amount' => 0],
    'sent' => ['count' => 0, 'amount' => 0],
], $estimates ?? []);

/** @see \Dashboard_model::get_invoices_data */
$purchase_orders = array_merge([
    'paid' => ['count' => 0, 'amount' => 0],
    'unpaid' => ['count' => 0, 'amount' => 0],
    'overdue' => ['count' => 0, 'amount' => 0],
    'partially_paid' => ['count' => 0, 'amount' => 0]
], $purchase_orders ?? []);

/** @see \Dashboard_model::get_invoice_request_data */
$invoices = array_merge([
    'paid' => ['count' => 0, 'amount' => 0],
    'unpaid' => ['count' => 0, 'amount' => 0],
    'overdue' => ['count' => 0, 'amount' => 0],
    'partially_paid' => ['count' => 0, 'amount' => 0]
], $invoices ?? []);

// Chart items and types are defined at the top of the file

hooks()->add_action('app_admin_footer', function () use ($estimates, $purchase_orders, $invoices, $chart_items, $chart_types) {
    ?>
    <script id="sales_activities_charts" type="text/javascript">
        $(function () {
            var config = {
                type: 'doughnut',
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        display: true,
                        position: 'right',
                        labels: {
                            boxWidth: 10, // Smaller box for legend items
                            padding: 8, // Add padding between legend items
                            fontSize: 11,
                            fontStyle: 'normal',
                            generateLabels: function(chart) {
                                var data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                    return data.labels.map(function(label, i) {
                                        var meta = chart.getDatasetMeta(0);
                                        var ds = data.datasets[0];
                                        var arc = meta.data[i];
                                        var value = ds.data[i];
                                        var formattedValue = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");

                                        return {
                                            text: label + ': ' + formattedValue,
                                            fillStyle: ds.backgroundColor[i],
                                            strokeStyle: ds.backgroundColor[i],
                                            lineWidth: 0,
                                            hidden: isNaN(ds.data[i]) || meta.data[i].hidden,
                                            index: i
                                        };
                                    });
                                }
                                return [];
                            }
                        }
                    },
                    tooltips: {
                        callbacks: {
                            title: function (tooltipItem, data) {
                                // Return empty string as we'll include chart title in label
                                return '';
                            },
                            label: function (tooltipItem, data) {
                                // Get the dataset label (chart title)
                                var label = data.labels[tooltipItem.index];
                                var value = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];

                                // Format the value with thousands separator if it's an amount
                                var formattedValue = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");

                                // Format the label with chart title to distinguish between charts
                                return label + ': ' + formattedValue;
                            }
                        }
                    }
                }
            };

            var estimateStatusLabels = ['<?= _l('estimate_status_sent'); ?>', '<?= _l('estimate_status_accepted'); ?>'];
            var invoiceStatusLabels = ['<?= _l('invoice_status_paid'); ?>', '<?= _l('invoice_status_unpaid'); ?>', '<?= _l('invoice_status_overdue'); ?>', '<?= _l('invoice_status_not_paid_completely'); ?>'];

            // Chart configuration data
            var chartData = {
                'estimates': {
                    'amount': {
                        labels: estimateStatusLabels,
                        data: [<?= $estimates['sent']['amount']; ?>, <?= $estimates['accepted']['amount']; ?>],
                    },
                    'quantity': {
                        labels: estimateStatusLabels,
                        data: [<?= $estimates['sent']['count']; ?>, <?= $estimates['accepted']['count']; ?>],
                    }
                },
                'purchase_orders': {
                    'amount': {
                        labels: invoiceStatusLabels,
                        data: [<?= $purchase_orders['paid']['amount']; ?>, <?= $purchase_orders['unpaid']['amount']; ?>, <?= $purchase_orders['overdue']['amount']; ?>, <?= $purchase_orders['partially_paid']['amount']; ?>],
                    },
                    'quantity': {
                        labels: invoiceStatusLabels,
                        data: [<?= $purchase_orders['paid']['count']; ?>, <?= $purchase_orders['unpaid']['count']; ?>, <?= $purchase_orders['overdue']['count']; ?>, <?= $purchase_orders['partially_paid']['count']; ?>],
                    }
                },
                'invoices': {
                    'amount': {
                        labels: invoiceStatusLabels,
                        data: [<?= $invoices['paid']['amount']; ?>, <?= $invoices['unpaid']['amount']; ?>, <?= $invoices['overdue']['amount']; ?>, <?= $invoices['partially_paid']['amount']; ?>],
                    },
                    'quantity': {
                        labels: invoiceStatusLabels,
                        data: [<?= $invoices['paid']['count']; ?>, <?= $invoices['unpaid']['count']; ?>, <?= $invoices['overdue']['count']; ?>, <?= $invoices['partially_paid']['count']; ?>]
                    }
                }
            };

            // Initialize all charts using foreach
            <?php foreach ($chart_items as $item_key => $item_data) : ?>
            <?php foreach ($chart_types as $chart_type => $chart_type_data) : ?>
            var <?= $item_key . ucfirst($chart_type) ?>Chart = new Chart('<?= $item_key ?>-<?= $chart_type ?>-chart', Object.assign({
                data: {
                    labels: chartData['<?= $item_key ?>']['<?= $chart_type ?>'].labels,
                    datasets: [{
                        label: '<?= $item_data['title'] ?> - <?= $chart_type_data['title'] ?>',
                        data: chartData['<?= $item_key ?>']['<?= $chart_type ?>'].data,
                        backgroundColor: ['#84c529', '#03a9f4', '#ff6f00', '#9c27b0']
                    }]
                }
            }, config));
            <?php endforeach; ?>
            <?php endforeach; ?>
        });
    </script>
<?php }) ?>

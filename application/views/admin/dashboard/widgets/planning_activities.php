<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="widget" id="widget-planning-activities" data-name="Planning Activities">
    <div class="panel_s">
        <div class="panel-body">
            <div class="widget-dragger"></div>
            <h4>Planning Activities</h4>
            <div class="clearfix"></div>
            <hr class="hr-panel-heading-sm no-margin"/>
            <div class="widget-content">
                <div class="row">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                            <tr>
                                <th class="min-tablet">Year(Month)</th>
                                <th class="min-tablet">Status</th>
                                <th class="min-tablet">Sales Amount (tax excluded)</th>
                                <th class="min-tablet">Invoices</th>
                                <th class="min-desktop">
                                    <span>Same fiscal year</span>
                                    <i class="fa fa-question-circle" data-toggle="tooltip" data-placement="top"
                                       title="Revenue recognized in the year it was generated"></i>
                                </th>
                                <th class="min-tablet">Deferred earned revenue</th>
                                <th class="min-desktop">
                                    <span>Break-age</span>
                                    <i class="fa fa-question-circle" data-toggle="tooltip" data-placement="top"
                                       title="Expired unused"></i>
                                </th>
                                <th class="min-desktop">
                                    <span>Unused</span>
                                    <i class="fa fa-question-circle" data-toggle="tooltip" data-placement="top"
                                       title="Sales Amount - Recognized revenue (Same fiscal year + deferred earned revenue)"></i>
                                </th>
                                <th class="min-desktop">
                                    <span>Unused Rate</span>
                                    <i class="fa fa-question-circle" data-toggle="tooltip" data-placement="top"
                                       title="Unused / Sales Amount"></i>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php foreach ($planning_activities_yearly ?? [] as $row): ?>
                                <tr class="info">
                                    <td><strong><?php echo $row['year']; ?></strong></td>
                                    <td><?php echo format_invoice_status($row['status']) ?>
                                        <?php if ($row['unpaid_amount'] > 0): ?>
                                            <i class="fa fa-question-circle" data-toggle="tooltip" data-placement="top"
                                               title="Unpaid <?php echo number_format($row['unpaid_amount'], 0) ?>"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-right">
                                        <strong><?php echo number_format($row['total_amount'], 0); ?></strong></td>
                                    <td class="text-right">
                                        <strong><?php echo number_format($row['invoice_count'], 0); ?></strong></td>
                                    <td class="text-right">
                                        <strong><?php echo number_format($row['fiscal_year_earned_revenue'], 0); ?></strong>
                                    </td>
                                    <td class="text-right">
                                        <strong><?php echo number_format($row['deferred_earned_revenue'], 0); ?></strong>
                                    </td>
                                    <td class="text-right"><?php echo number_format($row['expired_unused'], 0); ?></td>
                                    <td class="text-right">
                                        <strong><?php echo number_format($row['unused_amount'], 0); ?></strong>
                                    </td>
                                    <td class="text-right">
                                        <strong><?php echo number_format(($row['total_amount'] > 0) ? $row['unused_amount'] / $row['total_amount'] * 100 : 0, 2); ?>
                                            %</strong></td>
                                </tr>
                            <?php endforeach; ?>
                            <?php if (empty($planning_activities)): ?>
                                <tr>
                                    <td colspan="8" class="text-center">No data available</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($planning_activities as $row): ?>
                                    <tr>
                                        <td><span data-toggle="tooltip" data-placement="top"
                                                  title="<?= $row['period_date'] ?>">
                                                <?php echo $row['year']; ?>
                                            </span></td>
                                        <td><?php echo format_invoice_status($row['status']) ?>
                                            <?php if ($row['unpaid_amount'] > 0): ?>
                                                <i class="fa fa-question-circle" data-toggle="tooltip"
                                                   data-placement="top"
                                                   title="Unpaid <?php echo number_format($row['unpaid_amount'], 0) ?>"></i>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-right">
                                            <?php echo number_format($row['total_amount'], 0); ?></td>
                                        <td class="text-right"><span
                                                data-toggle="tooltip" data-placement="top"
                                                title="invoice: <?= $row['invoice_ids'] ?>"><?php echo number_format($row['invoice_count']); ?></span>
                                        </td>
                                        <td class="text-right"><?php echo number_format($row['fiscal_year_earned_revenue'], 0); ?></td>
                                        <td class="text-right"><?php echo number_format($row['deferred_earned_revenue'], 0); ?></td>
                                        <td class="text-right"><span
                                                data-toggle="tooltip" data-placement="top"
                                                title="<?= $row['period_expired'] ?>">
                                                <?php echo number_format($row['expired_unused'], 0); ?></span></td>
                                        <td class="text-right"><?php echo number_format($row['unused_amount'], 0); ?></td>
                                        <td class="text-right">
                                            <?php echo number_format(($row['total_amount'] > 0) ? $row['unused_amount'] / $row['total_amount'] * 100 : 0, 2); ?>
                                            %
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php hooks()->add_action('app_admin_footer', function () { ?>
    <script>
        $(function () {

        });
    </script>
<?php }) ?>

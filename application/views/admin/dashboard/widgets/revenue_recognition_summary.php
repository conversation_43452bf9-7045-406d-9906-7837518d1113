<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="widget" id="widget-revenue-recognition-summary" data-name="Revenue Recognition Summary">
    <div class="panel_s">
        <div class="panel-body">
            <div class="widget-dragger"></div>
            <div class="row">
                <div class="col-md-8">
                    <h4>ALC, Sales Revenue and Unearned Revenue</h4>
                </div>
                <div class="col-md-4 text-right">
                    <!--<a href="#" id="export-revenue-recognition" class="btn btn-success btn-sm">-->
                    <!--    <i class="fa fa-file-excel-o"></i> --><?php //echo _l('export_to_csv'); ?>
                    <!--</a>-->
                </div>
            </div>
            <div class="clearfix"></div>
            <hr class="hr-panel-heading-sm no-margin"/>

            <script>
                $(function () {
                    $('#export-revenue-recognition').on('click', function (e) {
                        e.preventDefault();
                        var from_date = $('#from_date').length ? $.datepicker.formatDate('yymmdd', $('#from_date').datepicker("getDate")) : '<?php echo date('Ym01'); ?>';
                        var to_date = $('#to_date').length ? $.datepicker.formatDate('yymmdd', $('#to_date').datepicker("getDate")) : '<?php echo date('Ymd'); ?>';

                        window.location.href = admin_url + 'dashboard/export_revenue_recognition?from_date=' + from_date + '&to_date=' + to_date;
                    });
                });
            </script>
            <div class="widget-content">
                <div class="row">
                    <div class="table-responsive">
                        <table class="table table-bordered table-condensed table-hover">
                            <thead>
                            <tr class="warning">
                                <th class="min-tablet text-center">Category</th>
                                <?php $filtered_periods = []; foreach ($revenue_recognition_data = $revenue_recognition_data ?? [] as $period => $period_data): ?>
                                    <?php if ($period !== 'monthly' && $period !== 'previous_unearned_revenue' && $period !== 'previous_year'): ?>
                                        <?php if (!empty($revenue_recognition_data[$period]['invoice_amount']) || !empty($revenue_recognition_data[$period]['earned_revenue'])):
                                            $filtered_periods[] = $period;
                                            ?>
                                            <th class="min-tablet text-center"><?php echo $period; ?></th>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                                <?php foreach ($revenue_recognition_data['monthly'] ?? [] as $month_name => $month_data): ?>
                                    <th class="min-desktop text-center"><?php echo $month_name; ?></th>
                                <?php endforeach; ?>
                            </tr>
                            </thead>
                            <tbody>
                            <?php if (empty($revenue_recognition_data)): ?>
                                <tr>
                                    <td colspan="15" class="text-center">No data available</td>
                                </tr>
                            <?php else: ?>
                                <!-- Invoice issued (Amount) row -->
                                <tr>
                                    <td class="text-overflow-ellipsis">
                                        <span>Invoice issued</span>
                                        <i class="fa fa-question-circle" data-toggle="tooltip" data-placement="top"
                                           title="based on invoice issued date (excluding VAT)"></i>
                                    </td>
                                    <?php foreach ($filtered_periods as $period): ?>
                                        <td class="text-right"><?php echo number_format($revenue_recognition_data[$period]['invoice_amount']); ?></td>
                                    <?php endforeach; ?>
                                    <?php foreach ($revenue_recognition_data['monthly'] ?? [] as $month_data): ?>
                                        <td class="text-right"><?php echo number_format($month_data['invoice_amount']); ?></td>
                                    <?php endforeach; ?>
                                </tr>

                                <!-- Revenue total (recognized) row -->
                                <tr>
                                    <td class="text-overflow-ellipsis">
                                        <span>Revenue total</span>
                                        <i class="fa fa-question-circle" data-toggle="tooltip" data-placement="top"
                                           title="Earned Revenue"></i>
                                    </td>
                                    <?php foreach ($filtered_periods as $period): ?>
                                        <td class="text-right"><?php echo number_format($revenue_recognition_data[$period]['earned_revenue']); ?></td>
                                    <?php endforeach; ?>
                                    <?php foreach ($revenue_recognition_data['monthly'] ?? [] as $month_data): ?>
                                        <td class="text-right"><?php echo number_format($month_data['earned_revenue']); ?></td>
                                    <?php endforeach; ?>
                                </tr>

                                <!-- Active (Earned Rev) row -->
                                <tr>
                                    <td class="text-overflow-ellipsis text-right">
                                        <span>Active</span>
                                        <i class="fa fa-question-circle" data-toggle="tooltip" data-placement="top"
                                           title="recognized"></i>
                                    </td>
                                    <?php foreach ($filtered_periods as $period): ?>
                                        <td class="text-right"><?php echo number_format($revenue_recognition_data[$period]['recognized_revenue']); ?></td>
                                    <?php endforeach; ?>
                                    <?php foreach ($revenue_recognition_data['monthly'] ?? [] as $month_data): ?>
                                        <td class="text-right"><?php echo number_format($month_data['recognized_revenue']); ?></td>
                                    <?php endforeach; ?>
                                </tr>

                                <!-- Breakage (Expired Unused) row -->
                                <tr>
                                    <td class="text-overflow-ellipsis text-right">
                                        <span>Breakage</span>
                                        <i class="fa fa-question-circle" data-toggle="tooltip" data-placement="top"
                                           title="Expired Unused Amount"></i>
                                    </td>
                                    <?php foreach ($filtered_periods as $period): ?>
                                        <td class="text-right"><?php echo number_format($revenue_recognition_data[$period]['breakage_revenue']); ?></td>
                                    <?php endforeach; ?>
                                    <?php foreach ($revenue_recognition_data['monthly'] ?? [] as $month_data): ?>
                                        <td class="text-right"><?php echo number_format($month_data['breakage_revenue']); ?></td>
                                    <?php endforeach; ?>
                                </tr>

                                <!-- Unearned Rev (Unused) row -->
                                <tr>
                                    <td class="text-overflow-ellipsis">
                                        <span>Unearned Rev</span>
                                        <i class="fa fa-question-circle" data-toggle="tooltip" data-placement="top"
                                           title="(Unused) 선수 수익, Deferred Revenue(이연 수익)"></i>
                                    </td>
                                    <td class="text-right"
                                        colspan="<?= count($filtered_periods) ?>">
                                        <span
                                            class="text-muted">Year <?= $revenue_recognition_data['previous_year'] ?? date('Y', strtotime('last year')) ?>:
                                        <?php echo number_format($revenue_recognition_data['previous_unearned_revenue']); ?></span>
                                    </td>
                                    <?php foreach ($revenue_recognition_data['monthly'] ?? [] as $month_data): ?>
                                        <td class="text-right"><?php echo number_format($month_data['unearned_revenue']); ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

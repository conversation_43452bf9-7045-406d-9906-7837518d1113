<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="widget" id="widget-job-statistics" data-name="Job Sales Statistics">
    <div class="panel_s">
        <div class="panel-body">
            <div class="widget-dragger"></div>
            <h4 class="pull-left mtop5">Job Sales Statistics</h4>
            <div class="clearfix"></div>
            <hr class="hr-panel-heading-dashboard">

            <div class="row">
                <?php foreach ($job_sales_statistics ?? [] as $level => $row) { ?>
                    <div class="col-md-3 text-center">
                        <h4 class="bold" id="<?= $level; ?>-jobs-count"><?= $row['count']; ?></h4>
                        <p class="text-muted"><?= $level; ?> job postings</p>
                    </div>
                <?php } ?>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="progress progress-bar-mini progress-striped no-margin">
                        <?php
                        $class = [
                            'paid' => 'success',
                            'free' => 'info',
                            'gift' => 'warning',
                        ];
                        $sum = array_sum(array_column($job_sales_statistics ?? [], 'count'));
                        foreach ($job_sales_statistics ?? [] as $level => $row) {
                            $percentage = round($row['count'] / $sum * 100, 1)
                            ?>
                            <div class="progress-bar no-percent-text progress-bar-<?= $class[$level] ?? 'danger' ?>"
                                 role="progressbar" aria-valuenow="<?= $row['count'] ?>" aria-valuemin="0"
                                 aria-valuemax="<?= $sum ?>"
                                 style="width: <?= $percentage ?>%">
                                <span class="sr-only"><?= $level ?>: <?= $row['count'] ?> (<?= $percentage ?>%)</span>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

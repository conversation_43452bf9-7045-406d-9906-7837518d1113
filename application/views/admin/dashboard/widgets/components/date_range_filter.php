<?php use Illuminate\Support\Carbon;

defined('BASEPATH') or exit('No direct script access allowed'); ?>
<style>
    .date-group {
        display: flex;
        align-items: center;
        margin-bottom: 0;
    }

    .date-group label {
        margin-right: 10px;
        min-width: 80px;
        margin-bottom: 0;
        white-space: nowrap;
    }

    .date-group .input-group {
        flex: 1;
    }

    .date-presets-dropdown {
        width: 100%;
    }

    .date-presets-dropdown .dropdown-menu {
        width: 100%;
    }

    .widget-content.loading {
        position: relative;
        min-height: 100px;
    }

    .widget-content.loading:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1;
    }

    .widget-content.loading:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 30px;
        height: 30px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        z-index: 2;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: translate(-50%, -50%) rotate(0deg);
        }
        100% {
            transform: translate(-50%, -50%) rotate(360deg);
        }
    }
</style>
<form method="get" class="filter-container" id="date-range-filter-form">
    <div class="row">
        <div class="col-md-2">
            <div class="dropdown date-presets-dropdown">
                <button class="btn btn-default dropdown-toggle" type="button"
                        id="periodDropdown" data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="true">
                    <span class="selected-period">presets</span>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" aria-labelledby="periodDropdown">
                    <li><a href="#" data-period="month" class="active">This Month</a></li>
                    <li><a href="#" data-period="last_month">Last Month</a></li>
                    <li><a href="#" data-period="quarter">This Quarter</a></li>
                    <li><a href="#" data-period="year">This Year</a></li>
                    <li><a href="#" data-period="last_year">Last Year</a></li>
                    <li><a href="#" data-period="week">This Week</a></li>
                    <li><a href="#" data-period="today">Today</a></li>
                </ul>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group date-group">
                <label for="from_date" class="control-label">From:</label>
                <div class="input-group date">
                    <input type="text" id="from_date" name="from_date"
                           class="form-control"
                           value="<?= Carbon::createFromFormat('Ymd', $from_date ?? date('Ym01'))->format('m/d/Y'); ?>"
                           autocomplete="off">
                    <div class="input-group-addon">
                        <i class="fa fa-calendar calendar-icon"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group date-group">
                <label for="to_date" class="control-label">To:</label>
                <div class="input-group date">
                    <input type="text" id="to_date" name="to_date" class="form-control"
                           value="<?= Carbon::createFromFormat('Ymd', $to_date ?? date('Ymd'))->format('m/d/Y'); ?>"
                           autocomplete="off">
                    <div class="input-group-addon">
                        <i class="fa fa-calendar calendar-icon"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <button type="submit" id="apply-filter" class="btn btn-info">
                <?php echo _l('apply'); ?>
            </button>
        </div>
    </div>
</form>

<?php hooks()->add_action('app_admin_footer', function () { ?>
    <script>
        $(function () {
            // Initialize datepickers
            $('#from_date').datepicker();
            $('#to_date').datepicker();


            var setDateRange = function (period) {
                var fromDate = new Date();
                var toDate = new Date();

                switch (period) {
                    case 'year':
                        fromDate = new Date(toDate.getFullYear(), 0, 1);
                        break;
                    case 'month':
                        fromDate = new Date(toDate.getFullYear(), toDate.getMonth(), 1);
                        break;
                    case 'quarter':
                        var quarter = Math.floor(toDate.getMonth() / 3);
                        fromDate = new Date(toDate.getFullYear(), quarter * 3, 1);
                        break;
                    case 'week':
                        var day = toDate.getDay();
                        var diff = toDate.getDate() - day + (day === 0 ? -6 : 1); // monday
                        fromDate = new Date(toDate.getFullYear(), toDate.getMonth(), diff);
                        break;
                    case 'today':
                        fromDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate());
                        break;
                    case 'last_month':
                        // First day of previous month
                        var lastMonth = new Date(toDate.getFullYear(), toDate.getMonth() - 1, 1);
                        fromDate = new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1);
                        // Last day of previous month
                        toDate = new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 0);
                        break;
                    case 'last_year':
                        // First day of previous year
                        var lastYear = toDate.getFullYear() - 1;
                        fromDate = new Date(lastYear, 0, 1);
                        // Last day of previous year
                        toDate = new Date(lastYear, 11, 31);
                        break;
                }

                $('#from_date').datepicker('setDate', fromDate);
                $('#to_date').datepicker('setDate', toDate);
            };

            $('.date-presets-dropdown .dropdown-menu a').on('click', function (e) {
                e.preventDefault();
                var period = $(this).data('period');
                var periodText = $(this).text();

                setDateRange(period);

                $('.selected-period').text(periodText);

                $('.date-presets-dropdown .dropdown-menu a').removeClass('active');
                $(this).addClass('active');
            });
        });
    </script>
<?php }) ?>

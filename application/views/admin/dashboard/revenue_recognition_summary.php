<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">

            <!-- Revenue Recognition Summary Widget -->
            <div class="col-md-12">
                <?php $this->load->view('admin/dashboard/widgets/revenue_recognition_summary'); ?>
            </div>

            <div class="col-md-12" data-container="top-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <?php $this->load->view('admin/dashboard/widgets/components/date_range_filter'); ?>
                    </div>
                </div>
            </div>

            <!-- Sales Activities Widget -->
            <div class="col-md-12">
                <?php //$this->load->view('admin/dashboard/widgets/sales_activities'); ?>
                <div class="widget" id="widget-sales-activities" data-name="Sales Activities">
                    <div class="panel_s">
                        <div class="panel-body">
                            <div class="widget-dragger"></div>
                            <h4 class="pull-left">Sales Activities</h4>
                            <div class="clearfix"></div>
                            <hr class="hr-panel-heading-dashboard">
                            <?php
                            /** @see \Dashboard_model::get_dashboard_metrics 건수 및 금액 */
                            $chart_items = [
                                'estimates' => [
                                    'title' => 'Quotations',
                                    'data' => array_merge([
                                        'accepted' => ['count' => 0, 'amount' => 0],
                                        'sent' => ['count' => 0, 'amount' => 0],
                                    ], $estimates ?? [])
                                    ],
                                'purchase_orders' => ['title' => _l('invoices'),
                                    'data' => array_merge([
                                        'paid' => ['count' => 0, 'amount' => 0],
                                        'unpaid' => ['count' => 0, 'amount' => 0],
                                        'overdue' => ['count' => 0, 'amount' => 0],
                                        'partially_paid' => ['count' => 0, 'amount' => 0]
                                    ], $purchase_orders ?? [])],
                                'invoices' => ['title' => 'Invoice', 'data' => array_merge([
                                    'paid' => ['count' => 0, 'amount' => 0],
                                    'unpaid' => ['count' => 0, 'amount' => 0],
                                    'overdue' => ['count' => 0, 'amount' => 0],
                                    'partially_paid' => ['count' => 0, 'amount' => 0]
                                ], $invoices ?? [])]
                            ];
                            ?>
                            <style>
                                .sales-activity-card {
                                    background: #fff;
                                    border-radius: 8px;
                                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                                    margin-bottom: 20px;
                                    overflow: hidden;
                                }
                                .sales-activity-header {
                                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                    color: white;
                                    padding: 15px;
                                    text-align: center;
                                    font-weight: bold;
                                    font-size: 16px;
                                }
                                .sales-activity-item {
                                    display: flex;
                                    align-items: center;
                                    padding: 12px 15px;
                                    border-bottom: 1px solid #f0f0f0;
                                    transition: background-color 0.2s;
                                }
                                .sales-activity-item:hover {
                                    background-color: #f8f9fa;
                                }
                                .sales-activity-item:last-child {
                                    border-bottom: none;
                                }
                                .status-icon {
                                    width: 12px;
                                    height: 12px;
                                    border-radius: 50%;
                                    margin-right: 10px;
                                    flex-shrink: 0;
                                }
                                .status-paid { background-color: #28a745; }
                                .status-unpaid { background-color: #dc3545; }
                                .status-overdue { background-color: #fd7e14; }
                                .status-partially_paid { background-color: #ffc107; }
                                .status-accepted { background-color: #28a745; }
                                .status-sent { background-color: #17a2b8; }
                                .status-label {
                                    flex: 1;
                                    font-weight: 500;
                                    text-transform: capitalize;
                                }
                                .status-count {
                                    background: #e9ecef;
                                    padding: 4px 8px;
                                    border-radius: 12px;
                                    font-size: 12px;
                                    font-weight: bold;
                                    margin-right: 10px;
                                    min-width: 40px;
                                    text-align: center;
                                }
                                .status-amount {
                                    font-weight: bold;
                                    color: #495057;
                                }
                            </style>
                            <div class="row">
                                <?php foreach ($chart_items as $item_key => $item_data) : ?>
                                    <div class="col-md-4">
                                        <div class="sales-activity-card">
                                            <div class="sales-activity-header">
                                                <?= $item_data['title'] ?>
                                            </div>
                                            <div class="sales-activity-body">
                                                <?php foreach ($item_data['data'] as $status => $data) : ?>
                                                <div class="sales-activity-item">
                                                    <div class="status-icon status-<?= $status ?>"></div>
                                                    <div class="status-label"><?= str_replace('_', ' ', $status) ?></div>
                                                    <div class="status-count"><?= number_format($data['count']) ?></div>
                                                    <div class="status-amount"><?= number_format($data['amount']) ?></div>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                                <div class="row">
                                    <?php foreach ($chart_items as $item_key => $item_data) : ?>
                                        <div class="col-md-4">

                                        </div>
                                    <?php endforeach; ?>
                                </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Job Sales Statistics Widget -->
            <div class="col-md-12">
                <?php $this->load->view('admin/dashboard/widgets/job_sales_statistics'); ?>
            </div>
        </div>
    </div>
</div>
<?php hooks()->add_action('app_admin_footer', function () { ?>
    <script>
        $(function () {
            $('#date-range-filter-form').on('submit', function (e) {
                e.preventDefault(); // Prevent default form submission
                var from_date = $.datepicker.formatDate('yymmdd', $('#from_date').datepicker("getDate"));
                var to_date = $.datepicker.formatDate('yymmdd', $('#to_date').datepicker("getDate"));

                // Validate date range
                if (from_date > to_date) {
                    alert('Invalid date range: From date cannot be greater than To date');
                    return;
                }

                location.href = admin_url + 'dashboard/revenue_recognition_summary?from_date=' + from_date
                    + '&to_date=' + to_date;
            });
        })
    </script>
<?php }) ?>

<?php init_tail(); ?>
<script>
    $(function() {
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
</body>
</html>


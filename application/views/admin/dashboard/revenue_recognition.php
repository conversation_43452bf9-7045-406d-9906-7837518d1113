<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12" data-container="top-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <?php $this->load->view('admin/dashboard/widgets/components/date_range_filter'); ?>
                    </div>
                </div>
                <?php $this->load->view('admin/dashboard/widgets/job_sales_statistics'); ?>
            </div>

            <!-- Sales Activities Widget -->
            <div class="col-md-12">
                <?php $this->load->view('admin/dashboard/widgets/sales_activities'); ?>
            </div>

            <!-- Planning Activities Widget -->
            <div class="col-md-12">
                <?php $this->load->view('admin/dashboard/widgets/planning_activities'); ?>
            </div>
        </div>
    </div>
</div>
<?php hooks()->add_action('app_admin_footer', function () { ?>
    <script>
        $(function () {
            $('#date-range-filter-form').on('submit', function (e) {
                e.preventDefault(); // Prevent default form submission
                var from_date = $.datepicker.formatDate('yymmdd', $('#from_date').datepicker("getDate"));
                var to_date = $.datepicker.formatDate('yymmdd', $('#to_date').datepicker("getDate"));

                // Validate date range
                if (from_date > to_date) {
                    alert('Invalid date range: From date cannot be greater than To date');
                    return;
                }

                location.href = admin_url + 'dashboard/revenue_recognition?from_date=' + from_date
                    + '&to_date=' + to_date;
            });
        }
    </script>
<?php }) ?>

<?php init_tail(); ?>
</body>
</html>


<?php

defined('BASEPATH') or exit('No direct script access allowed');

$dimensions = $pdf->getPageDimensions();

$pdf->setHtmlVSpace([
    'ol' => [
        [
            'h' => 0,
            'n' => 0
        ],
        [
            'h' => 0,
            'n' => 0
        ]
    ]
]);
$pdf->setListIndentWidth(3);
$pdf->setCellPaddings(0, 0, 0, 0);

$font_size_big_word = $font_size + 6;

$pdf->ln(4);

// Get Y position for the separation
$y = $pdf->getY();

//add header left
$estimate_info = '<div style="font-size:' . ($font_size + 4) . 'px;">' . show_info_header_of_estimate($estimate) . '</div>';

$pdf->writeHTMLCell(
    ($dimensions['wk'] * 2 / 3) - $dimensions['lm'],
    '',
    '',
    $y,
    $estimate_info,
    0,
    0,
    false,
    true,
    'L',
    true
);

//add header right
$pdf_logo_url = pdf_logo_url_v2();
$rowcount = max([$pdf->getNumLines($estimate_info, 80)]);
$pdf->writeHTMLCell(
    ($dimensions['wk'] / 3) - $dimensions['lm'],
    $rowcount * 3.5,
    '',
    '',
    $pdf_logo_url,
    0,
    1,
    false,
    true,
    'C',
    true
);

$pdf->ln(4);

//add content header table
$header_table = '<table width="100%" cellpadding="7" style="font-size:' . ($font_size + 4) . 'px;background-color: #FFE3DD;">' .
    get_header_content_estimate_info($estimate, $font_size + 4) .
    get_row_header_content_estimate_info($estimate) .
    '</table>';

$pdf->writeHTML($header_table, true, false, true, false, '');

// The items table
$options = mappingEstimatePDFData($estimate);

foreach ($options as $index => $option) {
    $items = get_items_estimate_table_data(
        [
            'ordinal_number' => $index + 1,
            'transaction' => $option
        ],
        'estimate',
        'pdf'
    );

    if (is_null($option->parent_id)) {
        $primaryTaxes = $items->taxes();
    }

    $pdf->writeHTML($items->table(), true, false, false, false, '');

    $pdf->Ln(-6);

    if (!empty($option->clientnote)) {
        $pdf->writeHTMLCell('', '', $pdf->getX() + 2, '',  '<b>' . _l('estimate_note') . '</b>', 0, 1, false, true, 'L', true);
        $pdf->Ln(2);
        $pdf->writeHTMLCell('', '', $pdf->getX() + 2, '', $option->clientnote, 0, 1, false, true, 'L', true);
        $pdf->Ln(4);
    }

    $features = '<table cellpadding="6" style="font-size:' . ($font_size + 4) . 'px">' . $items->get_row_features_des_table() . '</table>';

    $pdf->writeHTML($features, true, false, false, false, '');
}
$item_html = '<br pagebreak="true"/>';
$item_html .= '<table width="100%" cellpadding="6" style="font-size:' . ($font_size + 4) . 'px;">' .
    getEstimateTermsOfService($primaryTaxes) .
    getRowContractSignatureEstimate($estimate) .
    '</table>';

$pdf->writeHTML($item_html, true, false, true, false, '');

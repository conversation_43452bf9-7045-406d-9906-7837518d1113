<?php

defined('BASEPATH') or exit('No direct script access allowed');
$dimensions = $pdf->getPageDimensions();

$pdf->setHtmlVSpace(array('ol' => array(0 => array('h' => 0, 'n' => 0), 1 => array('h' => 0, 'n' => 0))));
$pdf->setListIndentWidth(3);
$pdf->setCellPaddings(0, 0, 0, 0);

$font_size_big_word = $font_size + 6;

$pdf->ln(4);

// Get Y position for the separation
$y = $pdf->getY();

//add header left
$invoice_info = '<div style="font-size:' . ($font_size + 4) . 'px;">';
$invoice_info .= show_info_header_of_invoice($invoice);
$invoice_info .= '</div>';

$pdf->writeHTMLCell(
    ($dimensions['wk'] * 2 / 3) - $dimensions['lm'],
    '',
    '',
    $y,
    $invoice_info,
    0,
    0,
    false,
    true,
    'L',
    true
);

//add header right
$pdf_logo_url = pdf_logo_url_v2();
$rowcount = max([$pdf->getNumLines($invoice_info, 80)]);
$pdf->writeHTMLCell(
    ($dimensions['wk'] / 3) - $dimensions['lm'],
    $rowcount * 3.5,
    '',
    '',
    $pdf_logo_url,
    0,
    1,
    false,
    true,
    'C',
    true
);

$pdf->ln(4);

//add content header table
$header_table = '<table width="100%" cellpadding="6" style="font-size:' . ($font_size + 4) . 'px;">';

$header_table .= get_header_content_invoice_info($invoice, $font_size + 4);

$header_table .= get_row_header_content_invoice_info($invoice);

$header_table .= '</table>';

$pdf->writeHTML($header_table, true, false, true, false, '');

//add content table
$items = get_items_invoice_table_data($invoice, 'invoice', 'pdf');

$items_html = $items->table();

$pdf->writeHTML($items_html, true, false, true, false, '');

$item_html = '<table width="100%" cellpadding="6" style="font-size:' . ($font_size + 4) . 'px;">';
$item_html .= get_row_gift($invoice);
// $item_html .= get_row_general_features_of_Job();
$item_html .= '<tr>
<td align="left" width="100%">' . '' . '</td>
</tr>';

$item_html .= $items->get_row_features_des_table();
$item_html .= get_row_terms_of_service($items->taxes());

$item_html .= '<tr>
<td align="left" width="100%">' . '' . '</td>
</tr>';

$item_html .= '<br pagebreak="true"/>';
$item_html .= get_row_contract_signature($invoice);

$item_html .= '</table>';
$pdf->writeHTML($item_html, true, false, true, false, '');

// Check to remove blank page
if (strlen($pdf->getPageBuffer($pageDelete = $pdf->getNumPages() - 1)) == 814) {
    $pdf->deletePage($pageDelete);
}

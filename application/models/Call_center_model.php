<?php

use Entities\M3cCall;
use Entities\Staff;

defined('BASEPATH') or exit('No direct script access allowed');

class Call_center_model extends App_Model
{
    public function syncCallLogs($calls)
    {
        $staffIPs = Staff::select('staffid', 'ipphone')->active()->whereNotNull('ipphone')->where('ipphone', '!=', '')->pluck('staffid', 'ipphone');
        $values = array_map(function ($call) use ($staffIPs) {
            $agentIds = $call->agent_id ? explode(',', $call->agent_id) : [];
            $agentId = count($agentIds) ? end($agentIds) : '';
            $staffId = null;
            if ($agentId) {
                $staffId = $staffIPs->get($agentId);
            }
            $userIds = $call->user_id ? explode(',', $call->user_id) : [];
            return array_merge((array) $call, [
                'agent_id' => $agentId,
                'staff_id' => $staffId,
                'call_code' => $call->call_id,
                'call_id' => $call->id,
                'path' => $call->path ?? '',
                'path_download' => $call->path_download ?? '',
                'id' => null,
                'customer_id' => $call->customer_id ?? 0,
                'caller' => $call->caller ?? '',
                'called' => $call->called ?? '',
                'user_id' => count($userIds) ? end($userIds) : '',
                'group_id' => $call->group_id ?? 0,
                'call_type' => $call->call_type ?? 0,
                'wait_time' => $call->wait_time ?? '',
                'hold_time' => $call->hold_time ?? '',
                'talk_time' => $call->talk_time ?? '',
                'end_status' => $call->end_status ?? '',
                'ticket_id' => $call->ticket_id ?? 0,
                'last_user_id' => $call->last_user_id ?? 0,
                'last_agent_id' => $call->last_agent_id ?? 0,
                'call_survey' => $call->call_survey ?? '',
                'vendor' => $call->vendor ?? '',
                'transcripts' => json_encode($call->transcripts ?? []),
            ]);
        }, $calls);

        M3cCall::upsert(
            // Insert/Update records
            $values,
            // Unique field checking
            ['call_id'],
            // Update value
            ['path', 'path_download']
        );
    }
}

<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Client_requests_model extends App_Model
{
    protected $requestTbl;

    public function __construct()
    {
        parent::__construct();

        $this->requestTbl = db_prefix() . 'client_requests';
    }

    /**
     * Create request
     * @param integer $clientId client id
     * @param array $requestData form data
     * @return integer request id
     */
    public function createRequest($clientId)
    {
        // Cleanup other data
        $this->db->insert($this->requestTbl, [
            'created_at' => date('Y-m-d H:i:s'),
            'client_id'   => $clientId,
            'sale_id'   => get_staff_user_id(),
        ]);

        return $this->db->insert_id();
    }

    /**
     * Check sale permission to approve request
     * @param $requestId request will be checked permission
     */
    public function admin_can_approve_request($requestId)
    {
        $requestTbl = db_prefix().'client_requests';
        $request = $this->db
            ->select(
                join(',', [
                    'id',
                    'sale_id as staff_id',
                    'client_id',
                    'approved_at'
                ])
            )
            ->where([
                $requestTbl.'.id' => $requestId,
                $requestTbl.'.request_status' => CLIENT_REQUEST_STATUS_WAITING,
            ])
            ->get($requestTbl)
            ->row();

        // Request is not approved by Sale Admin
        if (!$request) {
            throw new Exception(_l('client_request_exception_request_admin_not_found'));
        }

        // Request already approved by other
        if ($request->approved_at) {
            throw new Exception(_l('client_request_exception_request_aproved_already'));
        }

        $this->load->model('clients_model');
        // Check duplicate VAT for approved clients
        if ($this->clients_model->isDuplicateVat($request->client_id)) {
            throw new Exception(_l('client_request_exception_duplicate_vat'));
        }

        return $request;
    }

    /**
     * Check sale permission to approve request
     * @param $requestId request will be checked permission
     */
    public function leader_can_approve_request($requestId)
    {
        $request = $this->db
            ->select(
                join(',', [
                    'id',
                    'sale_id as staff_id',
                    'client_id',
                    'approved_at'
                ])
            )
            ->where([
                $this->requestTbl.'.id' => $requestId,
                $this->requestTbl.'.request_status' => CLIENT_REQUEST_STATUS_SA_APPROVED,
            ])
            ->get($this->requestTbl)
            ->row();

        // Request is not approved by Sale Admin
        if (!$request) {
            throw new Exception(_l('client_request_exception_request_not_found'));
        }

        // Request already approved by other
        if ($request->approved_at) {
            throw new Exception(_l('client_request_exception_request_aproved_already'));
        }
        $this->load->model('roles_model');
        $memberIds = $this->roles_model->get_sale_members(get_staff_user_id());
        // Check leader can approve to members only
        if (!in_array($request->staff_id, $memberIds)) {
            throw new Exception(_l('client_request_exception_request_approve_member_only'));
        }

        // Check duplicate VAT for approved clients
        if ($this->clients_model->isDuplicateVat($request->client_id)) {
            throw new Exception(_l('client_request_exception_duplicate_vat'));
        }

        return $request;
    }

    /**
     * Process approve status
     * @param int $requestId
     * @param mixed $request
     * @param string $note
     */
    public function process_leader_approve($requestId, $request, $note)
    {
        // Change request status to approved
        $this->db
            ->where('id', $requestId)
            ->update($this->requestTbl, [
                'request_status' => CLIENT_REQUEST_STATUS_LEADER_APPROVED,
                'leader_id' => get_staff_user_id(),
                'approved_at' => date('Y-m-d H:i:s'),
                'leader_note' => $note
            ]);
        
        // Mask status is active and set approved_at to be not null
        $this->db
            ->where('userid', $request->client_id)
            ->update(db_prefix().'clients', [
                'approved_at' => date('Y-m-d H:i:s'),
                'datecreated' => date('Y-m-d H:i:s'),
                'active' => 1,
                'registration_confirmed' => 1,
            ]);
        
        // Assign customer admin to the client
        $this->load->model('clients_model');
        $this->clients_model->assign_admins([
            'customer_admins' => [$request->staff_id]
        ], $request->client_id);

        return _l('client_request_approve_success_message');
    }

    /**
     * Process approve status
     * @param int $requestId
     * @param mixed $request
     */
    public function process_admin_approve($requestId, $note)
    {
        $this->db
            ->where('id', $requestId)
            ->update($this->requestTbl, [
                'request_status' => CLIENT_REQUEST_STATUS_SA_APPROVED,
                'admin_id' => get_staff_user_id(),
                'sa_approved_at' => date('Y-m-d H:i:s'),
                'sa_note' => $note
            ]);
        
        return _l('client_request_approve_success_message');
    }

    /**
     * Check sale permission to reject request
     * @param $requestId request will be checked permission
     */
    public function admin_can_reject_request($requestId)
    {
        $request = $this->db
            ->select(
                join(',', [
                    'id',
                    'sale_id as staff_id',
                    'client_id',
                    'sa_approved_at'
                ])
            )
            ->where([
                $this->requestTbl.'.id' => $requestId,
                $this->requestTbl.'.request_status' => CLIENT_REQUEST_STATUS_WAITING,
            ])
            ->get($this->requestTbl)
            ->row();

        // Request is not approved by Sale Admin
        if (!$request) {
            throw new Exception(_l('client_request_exception_request_admin_not_found'));
        }

        // Request already approved by other
        if ($request->sa_approved_at) {
            throw new Exception(_l('client_request_exception_request_aproved_already'));
        }
    }

    /**
     * Check sale permission to reject request
     * @param $requestId request will be checked permission
     */
    public function leader_can_reject_request($requestId)
    {
        $request = $this->db
            ->select(
                join(',', [
                    'id',
                    'sale_id as staff_id',
                    'client_id',
                    'approved_at'
                ])
            )
            ->where([
                $this->requestTbl.'.id' => $requestId,
                $this->requestTbl.'.request_status' => CLIENT_REQUEST_STATUS_SA_APPROVED,
            ])
            ->get($this->requestTbl)
            ->row();

        // Request is not approved by Sale Admin
        if (!$request) {
            throw new Exception(_l('client_request_exception_request_not_found'));
        }

        // Request already approved by other
        if ($request->approved_at) {
            throw new Exception(_l('client_request_exception_request_aproved_already'));
        }

        $this->load->model('roles_model');
        $memberIds = $this->roles_model->get_sale_members(get_staff_user_id());
        // Check leader can reject to members only
        if (!in_array($request->staff_id, $memberIds)) {
            throw new Exception(_l('client_request_exception_request_reject_member_only'));
        }
    }

    /**
     * Check sale permission to reject request
     * @param interger $requestId request will be checked permission
     * @param string $note
     */
    public function process_reject_request($requestId, $note, $status = CLIENT_REQUEST_STATUS_SA_REJECTED)
    {
        $this->db
            ->where('id', $requestId)
            ->update($this->requestTbl, [
                'request_status' => $status,
                ($status == CLIENT_REQUEST_STATUS_SA_REJECTED ? 'admin_id' : 'leader_id') => get_staff_user_id(),
                ($status == CLIENT_REQUEST_STATUS_SA_REJECTED ? 'sa_note' : 'leader_note') => $note,
                'rejected_at' => date('Y-m-d H:i:s')
            ]);
        
        return _l('client_request_reject_success_message');
    }

    /**
     * Check request is already approved/rejected before or not before allow edit note
     * @param integer $requestId
     * @param boolean $isLeader
     */
    public function can_edit_request_note($requestId, $isLeader = false)
    {
        $hasRecord = total_rows($this->requestTbl, [
            'id' => $requestId,
            ($isLeader ? 'leader_id' : 'admin_id') => get_staff_user_id(),
        ]) > 0;

        if (!$hasRecord) {
            throw new Exception(_l('client_exception_request_cant_edit_note'));
        }
    }

    /**
     * Perform update request's note
     * @param integer $requestId
     * @param string $note
     * @param boolean $isLeader
     */
    public function edit_request_note($requestId, $note, $isLeader = false)
    {
        $this->db
            ->where('id', $requestId)
            ->update($this->requestTbl, [
                ($isLeader ? 'leader_note' : 'sa_note') => $note,
            ]);

        return _l('client_request_update_note_success_message');
    }
}

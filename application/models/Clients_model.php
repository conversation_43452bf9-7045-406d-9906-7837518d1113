<?php

use app\services\AccountingService;
use app\services\utilities\Arr;
use Carbon\Carbon;
use Dto\Infoplus\CreateECollectionDTO;
use Dto\Infoplus\CreatePayerDTO;
use Dto\Infoplus\InquiryEcCodeDTO;
use Dto\Infoplus\MapECollectionDTO;
use Entities\Client;
use Entities\ClientAmsCompany;
use Entities\ClientPayer;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Client_groups_model $client_groups_model
 */
class Clients_model extends App_Model
{
    private $contact_columns;
    private $restrict_update_columns = [
        'userid',
        'vat',
        'business_name',
        'address',
        'type_of_customer',
        'usage_behavior',
    ];

    public function __construct()
    {
        parent::__construct();

        $this->contact_columns = hooks()->apply_filters('contact_columns', [
            'firstname',
            'lastname',
            'email',
            'phonenumber',
            'title',
            'password',
            'send_set_password_email',
            'donotsendwelcomeemail',
            'permissions',
            'direction',
            'invoice_emails',
            'estimate_emails',
            'credit_note_emails',
            'contract_emails',
            'task_emails',
            'project_emails',
            'ticket_emails',
            'is_primary',
            'fullname',
            'landline',
            'sex',
            'birthday',
            'facebook',
            'zalo',
            'linkedin',
            'skype',
            'other',
            'status',
            'review_status'
        ]);

        $this->load->model(['client_vault_entries_model', 'client_groups_model', 'statement_model', 'misc_model']);
    }

    /**
     * Get client object based on passed clientid if not passed clientid return array of all clients
     * @param  mixed $id    client id
     * @param  array  $where
     * @return mixed
     */
    public function get($id = '', $where = [], $limit = '', $getBusinessName = true)
    {
        $this->db->select(
            implode(
                ',',
                prefixed_table_fields_array(db_prefix() . 'clients')
            ) . ',' .
                get_sql_select_client_company('company', $getBusinessName) . ', concat (last_assigned.firstname, " ", last_assigned.lastname) as last_assigned_name,' .
                db_prefix() . 'contacts.fullname as contact_name,' .
                db_prefix() . 'contacts.phonenumber as contact_phone,' .
                db_prefix() . 'contacts.email as contact_email'
        );

        $this->db->join(db_prefix() . 'countries', '' . db_prefix() . 'countries.country_id = ' . db_prefix() . 'clients.country', 'left');
        $this->db->join(db_prefix() . 'contacts', '' . db_prefix() . 'contacts.userid = ' . db_prefix() . 'clients.userid AND is_primary = 1', 'left');
        $this->db->join(db_prefix() . 'staff last_assigned', 'last_assigned.staffid = ' . db_prefix() . 'clients.last_assigned', 'left');

        if ((is_array($where) && count($where) > 0) || (is_string($where) && $where != '')) {
            $this->db->where($where);
        }

        if (is_numeric($id)) {
            $this->db->where(db_prefix() . 'clients.userid', $id);
            $client = $this->db->get(db_prefix() . 'clients')->row();

            if ($client && get_option('company_requires_vat_number_field') == 0) {
                $client->vat = null;
            }

            $GLOBALS['client'] = $client;

            return $client;
        }

        $this->db->order_by('userid', 'desc');
        $limit ? $this->db->limit($limit) : '';

        return $this->db->get(db_prefix() . 'clients')->result_array();
    }

    /**
     * Get customers contacts
     * @param  mixed $customer_id
     * @param  array  $where       perform where in query
     * @return array
     */
    public function get_contacts($customer_id = '', $where = [])
    {
        $this->db->where($where);
        if ($customer_id != '') {
            $this->db->where('userid', $customer_id);
        }

        $this->db->order_by('is_primary', 'DESC');

        return $this->db->get(db_prefix() . 'contacts')->result_array();
    }

    public function get_activity_logs($client_id, $select = '*')
    {
        $this->db->select($select)
            ->where('clientid', $client_id)
            ->join(db_prefix() . 'staff', '' . db_prefix() . 'staff.staffid = ' . db_prefix() . 'activity_log.staffid', 'left')
            ->join(db_prefix() . 'clients', '' . db_prefix() . 'clients.userid = ' . db_prefix() . 'activity_log.clientid', 'left')
            ->order_by('date', 'DESC');

        return $this->db->get(db_prefix() . 'activity_log')->result_array();
    }

    public function getHistoryImport($type)
    {
        $data = [];

        if ($type === 'salesperson') {
            $value = 'Import Salesperson';
        } else if ($type === 'customer_admin') {
            $value = 'Import Customer Admin';
        } else if ($type === 'num_of_usage_behavior') {
            $value = 'Import num of usage behavior';
        }

        if ($value) {
            $data = $this->db
                ->select(db_prefix() . 'activity_log.*, concat(firstname, " ", lastname) staff_name')
                ->from(db_prefix() . 'activity_log')
                ->join(db_prefix() . 'staff', '' . db_prefix() . 'staff.staffid = ' . db_prefix() . 'activity_log.staffid', 'left')
                ->like('description', $value, 'both')
                ->order_by('date', 'DESC')
                ->get()
                ->result_array();
        }

        return $data;
    }

    /**
     * Get single contacts
     * @param  mixed $id contact id
     * @return object
     */
    public function get_contact($id)
    {
        $this->db->where('id', $id);

        return $this->db->get(db_prefix() . 'contacts')->row();
    }

    /**
     * Get contact by given email
     *
     * @since 2.8.0
     *
     * @param  string $email
     *
     * @return \strClass|null
     */
    public function get_contact_by_email($email)
    {
        $this->db->where('email', $email);
        $this->db->limit(1);

        return $this->db->get('contacts')->row();
    }

    /**
     * @param array $_POST data
     * @param withContact
     *
     * @return integer Insert ID
     *
     * Add new client to database
     */
    public function add($data, $withContact = false)
    {
        $data = $this->check_zero_columns($data);

        $data = hooks()->apply_filters('before_client_added', $data);

        $contact_data = [];
        $dataKeys = array_keys($data);
        foreach ($this->contact_columns as $field) {
            if (!in_array($field, $dataKeys)) {
                continue;
            }

            $contact_data[$field] = $data[$field];

            // Phonenumber is also used for the company profile
            if ($field != 'phonenumber') {
                unset($data[$field]);
            }
        }

        $customer_admin_data = [];
        if (isset($data['customer_admin'])) {
            $customer_admin_data['customer_admin'] = $data['customer_admin'];
            unset($data['customer_admin']);
        }
        $groups_in     = Arr::pull($data, 'groups_in') ?? [];
        $custom_fields = Arr::pull($data, 'custom_fields') ?? [];

        // From customer profile register
        if (isset($data['contact_phonenumber'])) {
            $contact_data['phonenumber'] = $data['contact_phonenumber'];
            unset($data['contact_phonenumber']);
        }

        $tags = Arr::pull($data, 'tags');

        $this->db->insert(db_prefix() . 'clients', array_merge($data, [
            'datecreated' => date('Y-m-d H:i:s'),
            // Auto approve if lead creating client
            'approved_at' => is_staff_logged_in() && is_leader_member() ? date('Y-m-d H:i:s') : (isset($data['approved_at']) ? $data['approved_at'] : null),
            'addedfrom'   => is_staff_logged_in() ? get_staff_user_id() : 0,
            // Default is NEW when creating
            'type_of_customer' => Client::CUSTOMER_TYPE_NEW,
        ]));

        $client_id = $this->db->insert_id();

        if ($client_id) {
            if (count($custom_fields) > 0) {
                $_custom_fields = $custom_fields;
                // Possible request from the register area with 2 types of custom fields for contact and for comapny/customer
                if (count($custom_fields) == 2) {
                    unset($custom_fields);
                    $custom_fields['customers']                = $_custom_fields['customers'];
                    $contact_data['custom_fields']['contacts'] = $_custom_fields['contacts'];
                } elseif (count($custom_fields) == 1) {
                    if (isset($_custom_fields['contacts'])) {
                        $contact_data['custom_fields']['contacts'] = $_custom_fields['contacts'];
                        unset($custom_fields);
                    }
                }

                handle_custom_fields_post($client_id, $custom_fields);
            }

            handle_tags_save($tags, $client_id, 'client');

            $members = isset($customer_admin_data['customer_admin']) ? explode(', ', $customer_admin_data['customer_admin']) : [];
            $this->insertCustomerAdmins($members, $client_id);

            // Update usage behavior after creating
            Client::select('userid', 'usage_behavior', 'num_of_usage_behavior')
                ->where('userid', $client_id)
                ->firstOrFail()
                ->updateUsageBehavior();

            /**
             * Used in Import, Lead Convert, Register
             */
            if ($withContact == true) {
                $contact_id = $this->add_contact($contact_data, $client_id, $withContact);
            }

            foreach ($groups_in as $group) {
                $this->db->insert('customer_groups', [
                    'customer_id' => $client_id,
                    'groupid'     => $group,
                ]);
            }

            $isStaff = null;

            if (!is_client_logged_in() && is_staff_logged_in()) {
                $isStaff = get_staff_user_id();
            }

            do_action_deprecated('after_client_added', $client_id, '2.9.4', 'after_client_created');

            hooks()->do_action('after_client_created', [
                'id'            => $client_id,
                'data'          => $data,
                'contact_data'  => $contact_data,
                'custom_fields' => $custom_fields,
                'groups_in'     => $groups_in,
                'with_contact'  => $withContact,
            ]);

            log_activity('New Customer Created', $isStaff, $client_id);
        }

        return $client_id;
    }

    /**
     * @param  array $_POST data
     * @param  integer ID
     * @return boolean
     * Update client informations
     */
    public function update($data, $id, $client_request = false)
    {
        $updated = false;
        $data    = $this->check_zero_columns($data);

        $data = hooks()->apply_filters('before_client_updated', $data, $id);

        $update_all_other_transactions = (bool) Arr::pull($data, 'update_all_other_transactions');
        $update_credit_notes           = (bool) Arr::pull($data, 'update_credit_notes');
        $custom_fields                 = Arr::pull($data, 'custom_fields') ?? [];
        $groups_in                     = Arr::pull($data, 'groups_in') ?? false;
        $tags = Arr::pull($data, 'tags') ?? '';

        if (handle_custom_fields_post($id, $custom_fields)) {
            $updated = true;
        }

        $oldClientData = $this->clients_model->get($id, [], '', false);
        if (!is_leader_member()) {
            // The filter field is updatable if not the leader
            $fieldsCanUpdate = array_keys(json_decode(json_encode($oldClientData), true));
            $fieldsCanUpdate = array_diff($fieldsCanUpdate, $this->restrict_update_columns);
            $fieldsCanUpdate[] = 'custom_fields';
            $data = array_intersect_key($data, array_flip($fieldsCanUpdate));
            // Map old data if no data is uploaded
            $data['billing_street'] = $data['billing_street'] ?? $oldClientData->billing_street;
            $data['billing_city'] = $data['billing_city'] ?? $oldClientData->billing_city;
            $data['billing_state'] = $data['billing_state'] ?? $oldClientData->billing_state;
            $data['billing_zip'] = $data['billing_zip'] ?? $oldClientData->billing_zip;
            $data['billing_country'] = $data['billing_country'] ?? $oldClientData->billing_country;
            $data['shipping_street'] = $data['shipping_street'] ?? $oldClientData->shipping_street;
            $data['shipping_city'] = $data['shipping_city'] ?? $oldClientData->shipping_city;
            $data['shipping_state'] = $data['shipping_state'] ?? $oldClientData->shipping_state;
            $data['shipping_zip'] = $data['shipping_zip'] ?? $oldClientData->shipping_zip;
            $data['shipping_country'] = $data['shipping_country'] ?? $oldClientData->shipping_country;
        }

        // If sale lead, not allow to remove these items when editing
        if (is_sales_leader()) {
            revert_empty_value(['source', 'source_reason', 'source_reference_link'], $data, $oldClientData);
        }

        $oldSalesperson = $oldClientData->salesperson_id ?? '';
        $newSalesperson = $data['salesperson_id'] ?? '';

        if ($newSalesperson) {
            if ($oldSalesperson && $oldSalesperson != $newSalesperson) {
                log_activity('Change Salesperson (F1_Cá nhân) From #' . $oldSalesperson . ' To #' . $newSalesperson, '', $id);
            } else if (!$oldSalesperson) {
                log_activity('Add Salesperson (F1_Cá nhân) (#' . $newSalesperson . ')', '', $id);
            }
        } else {
            if ($oldSalesperson) {
                log_activity('Remove Salesperson (F1_Cá nhân) (#' . $oldSalesperson . ')', '', $id);
            }
        }

        $this->db->where('userid', $id);
        $this->db->update(db_prefix() . 'clients', $data);

        if (handle_tags_save($tags, $id, 'client')) {
            $updated = true;
        }

        if ($this->db->affected_rows() > 0) {
            $updated = true;
            Client::select('userid', 'usage_behavior', 'num_of_usage_behavior')->where('userid', $id)->firstOrFail()->updateUsageBehavior();
        }

        if ($update_all_other_transactions || $update_credit_notes) {
            $transactions_update = [
                'billing_street'   => $data['billing_street'],
                'billing_city'     => $data['billing_city'],
                'billing_state'    => $data['billing_state'],
                'billing_zip'      => $data['billing_zip'],
                'billing_country'  => $data['billing_country'],
                'shipping_street'  => $data['shipping_street'],
                'shipping_city'    => $data['shipping_city'],
                'shipping_state'   => $data['shipping_state'],
                'shipping_zip'     => $data['shipping_zip'],
                'shipping_country' => $data['shipping_country'],
            ];

            if ($update_all_other_transactions) {
                // Update all invoices except paid ones.
                $this->db->where('clientid', $id)
                    ->where('status !=', 2)
                    ->update('invoices', $transactions_update);

                if ($this->db->affected_rows() > 0) {
                    $updated = true;
                }

                // Update all estimates
                $this->db->where('clientid', $id)
                    ->update('estimates', $transactions_update);
                if ($this->db->affected_rows() > 0) {
                    $updated = true;
                }
            }

            if ($update_credit_notes) {
                $this->db->where('clientid', $id)
                    ->where('status !=', 2)
                    ->update('creditnotes', $transactions_update);

                if ($this->db->affected_rows() > 0) {
                    $updated = true;
                }
            }
        }

        if ($this->client_groups_model->sync_customer_groups($id, $groups_in)) {
            $updated = true;
        }

        do_action_deprecated('after_client_updated', $id, '2.9.4', 'client_updated');

        hooks()->do_action('client_updated', [
            'id'                            => $id,
            'data'                          => $data,
            'update_all_other_transactions' => $update_all_other_transactions,
            'update_credit_notes'           => $update_credit_notes,
            'custom_fields'                 => $custom_fields,
            'groups_in'                     => $groups_in,
            'updated'                       => &$updated,
        ]);

        if ($updated) {
            log_activity('Customer Info Updated', '', $id);
        }

        return $updated;
    }

    /**
     * Update contact data
     * @param  array  $data           $_POST data
     * @param  mixed  $id             contact id
     * @param  boolean $client_request is request from customers area
     * @return mixed
     */
    public function update_contact($data, $id, $client_request = false)
    {
        $old_value = $this->db
            ->from(db_prefix() . 'contacts')
            ->where('id', $id)
            ->get()
            ->first_row();

        $affectedRows = 0;
        $contact      = $this->get_contact($id);
        if (empty($data['password'])) {
            unset($data['password']);
        } else {
            $data['password']             = app_hash_password($data['password']);
            $data['last_password_change'] = date('Y-m-d H:i:s');
        }

        $send_set_password_email = isset($data['send_set_password_email']) ? true : false;
        $set_password_email_sent = false;

        $permissions        = isset($data['permissions']) ? $data['permissions'] : [];

        if (isset($data['is_primary']))
            $data['is_primary'] = $data['is_primary'] ? 1 : 0;

        // Contact cant change if is primary or not
        if ($client_request == true) {
            unset($data['is_primary']);
        }

        if (isset($data['custom_fields'])) {
            $custom_fields = $data['custom_fields'];
            if (handle_custom_fields_post($id, $custom_fields)) {
                $affectedRows++;
            }
            unset($data['custom_fields']);
        }

        if ($client_request == false) {
            $data['invoice_emails']     = isset($data['invoice_emails']) ? 1 : 0;
            $data['estimate_emails']    = isset($data['estimate_emails']) ? 1 : 0;
            $data['credit_note_emails'] = isset($data['credit_note_emails']) ? 1 : 0;
            $data['contract_emails']    = isset($data['contract_emails']) ? 1 : 0;
            $data['task_emails']        = isset($data['task_emails']) ? 1 : 0;
            $data['project_emails']     = isset($data['project_emails']) ? 1 : 0;
            $data['ticket_emails']      = isset($data['ticket_emails']) ? 1 : 0;
        }

        $data['updated_at'] = date('Y-m-d H:i:s');
        $data['birthday'] = to_sql_date($data['birthday'] ?? '', true);

        $data = hooks()->apply_filters('before_update_contact', $data, $id);
        $changeReviewStatus = isset($data['review_status']) && $data['review_status'] != 0;
        // Set approved date if not set yet
        if ($changeReviewStatus && $data['review_status'] == 1 && empty($old_value->sa_approved_at)) {
            $data['sa_approved_at'] = date('Y-m-d H:i:s');
        }

        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'contacts', $data);

        if ($changeReviewStatus) {
            log_activity('Change Status Contact(#' . $id . ')', '', $contact->userid, [], $id, 'contact');
        }

        $new_value = $this->db
            ->from(db_prefix() . 'contacts')
            ->where('id', $id)
            ->get()
            ->first_row();

        if ($this->db->affected_rows() > 0) {
            $affectedRows++;
            if (isset($data['is_primary']) && $data['is_primary'] == 1) {
                $this->db->where('userid', $contact->userid);
                $this->db->where('id !=', $id);
                $this->db->update(db_prefix() . 'contacts', [
                    'is_primary' => 0,
                ]);
            }
        }

        if ($client_request == false) {
            $customer_permissions = $this->roles_model->get_contact_permissions($id);
            if (sizeof($customer_permissions) > 0) {
                foreach ($customer_permissions as $customer_permission) {
                    if (!in_array($customer_permission['permission_id'], $permissions)) {
                        $this->db->where('userid', $id);
                        $this->db->where('permission_id', $customer_permission['permission_id']);
                        $this->db->delete(db_prefix() . 'contact_permissions');
                        if ($this->db->affected_rows() > 0) {
                            $affectedRows++;
                        }
                    }
                }
                foreach ($permissions as $permission) {
                    $this->db->where('userid', $id);
                    $this->db->where('permission_id', $permission);
                    $_exists = $this->db->get(db_prefix() . 'contact_permissions')->row();
                    if (!$_exists) {
                        $this->db->insert(db_prefix() . 'contact_permissions', [
                            'userid'        => $id,
                            'permission_id' => $permission,
                        ]);
                        if ($this->db->affected_rows() > 0) {
                            $affectedRows++;
                        }
                    }
                }
            } else {
                foreach ($permissions as $permission) {
                    $this->db->insert(db_prefix() . 'contact_permissions', [
                        'userid'        => $id,
                        'permission_id' => $permission,
                    ]);
                    if ($this->db->affected_rows() > 0) {
                        $affectedRows++;
                    }
                }
            }
            if ($send_set_password_email) {
                $set_password_email_sent = $this->authentication_model->set_password_email($data['email'], 0);
            }
        }

        if (($client_request == true) && $send_set_password_email) {
            $set_password_email_sent = $this->authentication_model->set_password_email($data['email'], 0);
        }

        if ($affectedRows > 0) {
            hooks()->do_action('contact_updated', $id, $data);
        }

        if ($affectedRows > 0 && !$set_password_email_sent) {
            log_activity('Contact Updated (#' . $id . ')', '', $contact->userid, [
                'old_value' => $old_value,
                'new_value' => $new_value
            ], $id, 'contact');

            return true;
        } elseif ($affectedRows > 0 && $set_password_email_sent) {
            return [
                'set_password_email_sent_and_profile_updated' => true,
            ];
        } elseif ($affectedRows == 0 && $set_password_email_sent) {
            return [
                'set_password_email_sent' => true,
            ];
        }

        return false;
    }

    public function insertCustomerAdmins($members, $customer_id, $isSaAssign = false)
    {
        $client = Client::where('userid', $customer_id)->first();
        $expiredAt = $client->isFocus()
            ? (new Carbon('last day of next month'))->endOfDay()->format('Y-m-d H:i:s')
            : (new Carbon('last day of next month'))->addMonthNoOverflow()->endOfDay()->format('Y-m-d H:i:s');

        foreach ($members as $member) {
            $this->db->insert(db_prefix() . 'customer_admins', [
                'customer_id' => $customer_id,
                'staff_id'     => $member,
                'date_assigned' => date('Y-m-d H:i:s'),
                'expired_at' => $expiredAt,
            ]);

            log_activity('Add Customer Admin (#' . $member . ')', '', $customer_id);
        }
    }

    public function deleteCustomerAdmin($staff_id, $customer_id)
    {
        $success = $this->db->where('staff_id', $staff_id)
            ->where('customer_id', $customer_id)
            ->delete(db_prefix() . 'customer_admins');

        if ($success) {
            log_activity('Remove Customer Admin (#' . $staff_id . ')', '', $customer_id);
            return true;
        }
        return false;
    }

    /**
     * Add new contact
     * @param array  $data               $_POST data
     * @param mixed  $customer_id        customer id
     * @param boolean $not_manual_request is manual from admin area customer profile or register, convert to lead
     */
    public function add_contact($data, $customer_id, $not_manual_request = false)
    {
        $send_set_password_email = isset($data['send_set_password_email']) ? true : false;

        if (isset($data['custom_fields'])) {
            $custom_fields = $data['custom_fields'];
            unset($data['custom_fields']);
        }

        if (isset($data['permissions'])) {
            $permissions = $data['permissions'];
            unset($data['permissions']);
        }

        $data['email_verified_at'] = date('Y-m-d H:i:s');

        $send_welcome_email = true;

        if (isset($data['donotsendwelcomeemail'])) {
            $send_welcome_email = false;
        }

        if (defined('CONTACT_REGISTERING')) {
            $send_welcome_email = true;

            // Do not send welcome email if confirmation for registration is enabled
            if (get_option('customers_register_require_confirmation') == '1') {
                $send_welcome_email = false;
            }

            // If client register set this contact as primary
            $data['is_primary'] = 1;

            if (is_email_verification_enabled() && !empty($data['email'])) {
                // Verification is required on register
                $data['email_verified_at']      = null;
                $data['email_verification_key'] = app_generate_hash();
            }
        }

        if (isset($data['is_primary'])) {
            $data['is_primary'] = 1;
            $this->db->where('userid', $customer_id);
            $this->db->update(db_prefix() . 'contacts', [
                'is_primary' => 0,
            ]);
        } else {
            $data['is_primary'] = 0;
        }

        $password_before_hash = '';
        $data['userid']       = $customer_id;
        if (isset($data['password'])) {
            $password_before_hash = $data['password'];
            $data['password']     = app_hash_password($data['password']);
        }

        $data['datecreated'] = date('Y-m-d H:i:s');

        if (!$not_manual_request) {
            $data['invoice_emails']     = isset($data['invoice_emails']) ? 1 : 0;
            $data['estimate_emails']    = isset($data['estimate_emails']) ? 1 : 0;
            $data['credit_note_emails'] = isset($data['credit_note_emails']) ? 1 : 0;
            $data['contract_emails']    = isset($data['contract_emails']) ? 1 : 0;
            $data['task_emails']        = isset($data['task_emails']) ? 1 : 0;
            $data['project_emails']     = isset($data['project_emails']) ? 1 : 0;
            $data['ticket_emails']      = isset($data['ticket_emails']) ? 1 : 0;
        }

        $data['email'] = trim($data['email']);
        $data['updated_at'] = date('Y-m-d H:i:s');
        $data['created_by'] = is_logged_in() ? get_staff_user_id() : null;
        $data['birthday'] = to_sql_date($data['birthday'], true);

        $data = hooks()->apply_filters('before_create_contact', $data);

        $this->db->insert(db_prefix() . 'contacts', $data);
        $contact_id = $this->db->insert_id();

        if ($contact_id) {
            if (isset($custom_fields)) {
                handle_custom_fields_post($contact_id, $custom_fields);
            }
            // request from admin area
            if (!isset($permissions) && $not_manual_request == false) {
                $permissions = [];
            } elseif ($not_manual_request == true) {
                $permissions         = [];
                $_permissions        = get_contact_permissions();
                $default_permissions = @unserialize(get_option('default_contact_permissions'));
                if (is_array($default_permissions)) {
                    foreach ($_permissions as $permission) {
                        if (in_array($permission['id'], $default_permissions)) {
                            array_push($permissions, $permission['id']);
                        }
                    }
                }
            }

            if ($not_manual_request == true) {
                // update all email notifications to 0
                $this->db->where('id', $contact_id);
                $this->db->update(db_prefix() . 'contacts', [
                    'invoice_emails'     => 0,
                    'estimate_emails'    => 0,
                    'credit_note_emails' => 0,
                    'contract_emails'    => 0,
                    'task_emails'        => 0,
                    'project_emails'     => 0,
                    'ticket_emails'      => 0,
                ]);
            }
            foreach ($permissions as $permission) {
                $this->db->insert(db_prefix() . 'contact_permissions', [
                    'userid'        => $contact_id,
                    'permission_id' => $permission,
                ]);

                // Auto set email notifications based on permissions
                if ($not_manual_request == true) {
                    if ($permission == 6) {
                        $this->db->where('id', $contact_id);
                        $this->db->update(db_prefix() . 'contacts', ['project_emails' => 1, 'task_emails' => 1]);
                    } elseif ($permission == 3) {
                        $this->db->where('id', $contact_id);
                        $this->db->update(db_prefix() . 'contacts', ['contract_emails' => 1]);
                    } elseif ($permission == 2) {
                        $this->db->where('id', $contact_id);
                        $this->db->update(db_prefix() . 'contacts', ['estimate_emails' => 1]);
                    } elseif ($permission == 1) {
                        $this->db->where('id', $contact_id);
                        $this->db->update(db_prefix() . 'contacts', ['invoice_emails' => 1, 'credit_note_emails' => 1]);
                    } elseif ($permission == 5) {
                        $this->db->where('id', $contact_id);
                        $this->db->update(db_prefix() . 'contacts', ['ticket_emails' => 1]);
                    }
                }
            }

            if ($send_welcome_email == true && !empty($data['email'])) {
                send_mail_template(
                    'customer_created_welcome_mail',
                    $data['email'],
                    $data['userid'],
                    $contact_id,
                    $password_before_hash
                );
            }

            if ($send_set_password_email) {
                $this->authentication_model->set_password_email($data['email'], 0);
            }

            if (defined('CONTACT_REGISTERING')) {
                $this->send_verification_email($contact_id);
            } else {
                // User already verified because is added from admin area, try to transfer any tickets
                $this->load->model('tickets_model');
                $this->tickets_model->transfer_email_tickets_to_contact($data['email'], $contact_id);
            }

            log_activity('Contact Created (#' . $contact_id . ')', '', $data['userid'], [], $contact_id, 'contact');

            hooks()->do_action('contact_created', $contact_id);

            return $contact_id;
        }

        return false;
    }

    /**
     * Add new contact via customers area
     *
     * @param array  $data
     * @param mixed  $customer_id
     */
    public function add_contact_via_customers_area($data, $customer_id)
    {
        $send_welcome_email      = isset($data['donotsendwelcomeemail']) && $data['donotsendwelcomeemail'] ? false : true;
        $send_set_password_email = isset($data['send_set_password_email']) && $data['send_set_password_email'] ? true : false;
        $custom_fields           = $data['custom_fields'];
        unset($data['custom_fields']);

        if (!is_email_verification_enabled()) {
            $data['email_verified_at'] = date('Y-m-d H:i:s');
        } elseif (is_email_verification_enabled() && !empty($data['email'])) {
            // Verification is required on register
            $data['email_verified_at']      = null;
            $data['email_verification_key'] = app_generate_hash();
        }

        $password_before_hash = $data['password'];

        $data = array_merge($data, [
            'datecreated' => date('Y-m-d H:i:s'),
            'userid'      => $customer_id,
            'password'    => app_hash_password(isset($data['password']) ? $data['password'] : time()),
            'created_by' => is_logged_in() ? get_staff_user_id() : null,
        ]);

        $data = hooks()->apply_filters('before_create_contact', $data);
        $this->db->insert(db_prefix() . 'contacts', $data);

        $contact_id = $this->db->insert_id();

        if ($contact_id) {
            handle_custom_fields_post($contact_id, $custom_fields);

            // Apply default permissions
            $default_permissions = @unserialize(get_option('default_contact_permissions'));

            if (is_array($default_permissions)) {
                foreach (get_contact_permissions() as $permission) {
                    if (in_array($permission['id'], $default_permissions)) {
                        $this->db->insert(db_prefix() . 'contact_permissions', [
                            'userid'        => $contact_id,
                            'permission_id' => $permission['id'],
                        ]);
                    }
                }
            }

            if ($send_welcome_email === true) {
                send_mail_template(
                    'customer_created_welcome_mail',
                    $data['email'],
                    $customer_id,
                    $contact_id,
                    $password_before_hash
                );
            }

            if ($send_set_password_email === true) {
                $this->authentication_model->set_password_email($data['email'], 0);
            }

            log_activity('Contact Created (#' . $contact_id . ')', '', $customer_id);
            hooks()->do_action('contact_created', $contact_id);

            return $contact_id;
        }

        return false;
    }

    /**
     * Used to update company details from customers area
     * @param  array $data $_POST data
     * @param  mixed $id
     * @return boolean
     */
    public function update_company_details($data, $id)
    {
        $affectedRows = 0;
        if (isset($data['custom_fields'])) {
            $custom_fields = $data['custom_fields'];
            if (handle_custom_fields_post($id, $custom_fields)) {
                $affectedRows++;
            }
            unset($data['custom_fields']);
        }
        if (isset($data['country']) && $data['country'] == '' || !isset($data['country'])) {
            $data['country'] = 0;
        }
        if (isset($data['billing_country']) && $data['billing_country'] == '') {
            $data['billing_country'] = 0;
        }
        if (isset($data['shipping_country']) && $data['shipping_country'] == '') {
            $data['shipping_country'] = 0;
        }

        // From v.1.9.4 these fields are textareas
        $data['address'] = trim($data['address']);
        $data['address'] = nl2br($data['address']);
        if (isset($data['billing_street'])) {
            $data['billing_street'] = trim($data['billing_street']);
            $data['billing_street'] = nl2br($data['billing_street']);
        }
        if (isset($data['shipping_street'])) {
            $data['shipping_street'] = trim($data['shipping_street']);
            $data['shipping_street'] = nl2br($data['shipping_street']);
        }

        $data = hooks()->apply_filters('customer_update_company_info', $data, $id);

        $this->db->where('userid', $id);
        $this->db->update(db_prefix() . 'clients', $data);
        if ($this->db->affected_rows() > 0) {
            $affectedRows++;
        }
        if ($affectedRows > 0) {
            hooks()->do_action('customer_updated_company_info', $id);
            log_activity('Customer Info Updated From Clients Area', '', $id);

            return true;
        }

        return false;
    }

    /**
     * Get customer staff members that are added as customer admins
     * @param  mixed $id customer id
     * @return array
     */
    public function get_admins($id)
    {
        $this->db->where('customer_id', $id);

        return $this->db->get(db_prefix() . 'customer_admins')->result_array();
    }

    public function get_customer_of_admins($id)
    {
        $this->db->where('staff_id', $id);

        return $this->db->get(db_prefix() . 'customer_admins')->result_array();
    }

    /**
     * Get unique staff id's of customer admins
     * @return array
     */
    public function get_customers_admin_unique_ids()
    {
        return $this->db->query('SELECT DISTINCT(staff_id) FROM ' . db_prefix() . 'customer_admins')->result_array();
    }

    /**
     * Assign staff members as admin to customers
     * @param  array $data $_POST data
     * @param  mixed $id   customer id
     * @return boolean
     */
    public function assign_admins($data, $id)
    {
        $affectedRows = 0;
        $members = [];

        if (count($data) == 0 || count($data['customer_admins']) == 0) {
            $this->log_contact_terminated('customer_id = ' . $id, true);
            $this->db->where('customer_id', $id);
            $this->db->delete(db_prefix() . 'customer_admins');
            if ($this->db->affected_rows() > 0) {
                $affectedRows++;
            }
        } else {
            $current_admins     = $this->get_admins($id);
            $current_admins_ids = [];
            foreach ($current_admins as $c_admin) {
                array_push($current_admins_ids, $c_admin['staff_id']);
            }
            foreach ($current_admins_ids as $c_admin_id) {
                if (!in_array($c_admin_id, $data['customer_admins'])) {
                    if ($this->deleteCustomerAdmin($c_admin_id, $id)) {
                        $affectedRows++;
                    }
                }
            }

            foreach ($data['customer_admins'] as $n_admin_id) {
                if (total_rows(db_prefix() . 'customer_admins', [
                    'customer_id' => $id,
                    'staff_id' => $n_admin_id,
                ]) == 0) {
                    /**
                     * Bển mới request là bỏ rule này @@
                     * Customer admin nào dc assign mà không liên hệ sau 30 ngày bị đá ra sẽ không được assign lại
                     */
                    // $check = $this->db->from(db_prefix() . 'contact_terminated')->where([
                    //     'customer_id' => $id,
                    //     'staff_id' => $n_admin_id
                    // ])->get()->result_array();
                    // if (count($check) === 0) {
                    //     $members[] = $n_admin_id;
                    // } else {
                    //     return false;
                    // }
                    $members[] = $n_admin_id;
                }
            }
        }
        if (count($members)) {
            $affectedRows += count($members);
            $this->insertCustomerAdmins($members, $id, isset($data['is_sa_assigned']));
            $this->db->where('userid', $id)->update(
                db_prefix() . 'clients',
                [
                    'last_assigned' => end($members),
                    'is_sa_assigned' => $data['is_sa_assigned'] ?? 0,
                    'approved_at' => Carbon::now()
                ]
            );
        }

        if ($affectedRows > 0) {
            return true;
        }

        return false;
    }

    /**
     * @param  integer ID
     * @return boolean
     * Delete client, also deleting rows from, dismissed client announcements, ticket replies, tickets, autologin, user notes
     */
    public function delete($id)
    {
        $affectedRows = 0;

        if (!is_gdpr() && is_reference_in_table('clientid', db_prefix() . 'invoices', $id)) {
            return [
                'referenced' => true,
            ];
        }

        if (!is_gdpr() && is_reference_in_table('clientid', db_prefix() . 'estimates', $id)) {
            return [
                'referenced' => true,
            ];
        }

        if (!is_gdpr() && is_reference_in_table('clientid', db_prefix() . 'creditnotes', $id)) {
            return [
                'referenced' => true,
            ];
        }

        hooks()->do_action('before_client_deleted', $id);

        $last_activity = get_last_system_activity_id();
        $company       = get_company_name($id);

        $this->db->where('userid', $id);
        $this->db->delete(db_prefix() . 'clients');
        if ($this->db->affected_rows() > 0) {
            $affectedRows++;
            // Delete all user contacts
            $this->db->where('userid', $id);
            $contacts = $this->db->get(db_prefix() . 'contacts')->result_array();
            foreach ($contacts as $contact) {
                $this->delete_contact($contact['id']);
            }

            // Delete all tickets start here
            $this->db->where('userid', $id);
            $tickets = $this->db->get(db_prefix() . 'tickets')->result_array();
            $this->load->model('tickets_model');
            foreach ($tickets as $ticket) {
                $this->tickets_model->delete($ticket['ticketid']);
            }

            $this->db->where('rel_id', $id);
            $this->db->where('rel_type', 'customer');
            $this->db->delete(db_prefix() . 'notes');

            if (is_gdpr() && get_option('gdpr_on_forgotten_remove_invoices_credit_notes') == '1') {
                $this->load->model('invoices_model');
                $this->db->where('clientid', $id);
                $invoices = $this->db->get(db_prefix() . 'invoices')->result_array();
                foreach ($invoices as $invoice) {
                    $this->invoices_model->delete($invoice['id'], true);
                }

                $this->load->model('credit_notes_model');
                $this->db->where('clientid', $id);
                $credit_notes = $this->db->get(db_prefix() . 'creditnotes')->result_array();
                foreach ($credit_notes as $credit_note) {
                    $this->credit_notes_model->delete($credit_note['id'], true);
                }
            } elseif (is_gdpr()) {
                $this->db->where('clientid', $id);
                $this->db->update(db_prefix() . 'invoices', ['deleted_customer_name' => $company]);

                $this->db->where('clientid', $id);
                $this->db->update(db_prefix() . 'creditnotes', ['deleted_customer_name' => $company]);
            }

            $this->db->where('clientid', $id);
            $this->db->update(db_prefix() . 'creditnotes', [
                'clientid'   => 0,
                'project_id' => 0,
            ]);

            $this->db->where('clientid', $id);
            $this->db->update(db_prefix() . 'invoices', [
                'clientid'                 => 0,
                'recurring'                => 0,
                'recurring_type'           => null,
                'custom_recurring'         => 0,
                'cycles'                   => 0,
                'last_recurring_date'      => null,
                'project_id'               => 0,
                'subscription_id'          => 0,
                'cancel_overdue_reminders' => 1,
                'last_overdue_reminder'    => null,
                'last_due_reminder'        => null,
            ]);

            if (is_gdpr() && get_option('gdpr_on_forgotten_remove_estimates') == '1') {
                $this->load->model('estimates_model');
                $this->db->where('clientid', $id);
                $estimates = $this->db->get(db_prefix() . 'estimates')->result_array();
                foreach ($estimates as $estimate) {
                    $this->estimates_model->delete($estimate['id'], true);
                }
            } elseif (is_gdpr()) {
                $this->db->where('clientid', $id);
                $this->db->update(db_prefix() . 'estimates', ['deleted_customer_name' => $company]);
            }

            $this->db->where('clientid', $id);
            $this->db->update(db_prefix() . 'estimates', [
                'clientid'           => 0,
                'project_id'         => 0,
                'is_expiry_notified' => 1,
            ]);

            $this->load->model('subscriptions_model');
            $this->db->where('clientid', $id);
            $subscriptions = $this->db->get(db_prefix() . 'subscriptions')->result_array();
            foreach ($subscriptions as $subscription) {
                $this->subscriptions_model->delete($subscription['id'], true);
            }
            // Get all client contracts
            $this->load->model('contracts_model');
            $this->db->where('client', $id);
            $contracts = $this->db->get(db_prefix() . 'contracts')->result_array();
            foreach ($contracts as $contract) {
                $this->contracts_model->delete($contract['id']);
            }
            // Delete the custom field values
            $this->db->where('relid', $id);
            $this->db->where('fieldto', 'customers');
            $this->db->delete(db_prefix() . 'customfieldsvalues');

            // Get customer related tasks
            $this->db->where('rel_type', 'customer');
            $this->db->where('rel_id', $id);
            $tasks = $this->db->get(db_prefix() . 'tasks')->result_array();

            foreach ($tasks as $task) {
                $this->tasks_model->delete_task($task['id'], false);
            }

            $this->db->where('rel_type', 'customer');
            $this->db->where('rel_id', $id);
            $this->db->delete(db_prefix() . 'reminders');

            $this->db->where('customer_id', $id);
            $this->db->delete(db_prefix() . 'customer_admins');

            $this->db->where('customer_id', $id);
            $this->db->delete(db_prefix() . 'vault');

            $this->db->where('customer_id', $id);
            $this->db->delete(db_prefix() . 'customer_groups');

            $this->load->model('proposals_model');
            $this->db->where('rel_id', $id);
            $this->db->where('rel_type', 'customer');
            $proposals = $this->db->get(db_prefix() . 'proposals')->result_array();
            foreach ($proposals as $proposal) {
                $this->proposals_model->delete($proposal['id']);
            }
            $this->db->where('rel_id', $id);
            $this->db->where('rel_type', 'customer');
            $attachments = $this->db->get(db_prefix() . 'files')->result_array();
            foreach ($attachments as $attachment) {
                $this->delete_attachment($attachment['id']);
            }

            $this->db->where('clientid', $id);
            $expenses = $this->db->get(db_prefix() . 'expenses')->result_array();

            $this->load->model('expenses_model');
            foreach ($expenses as $expense) {
                $this->expenses_model->delete($expense['id'], true);
            }

            $this->db->where('client_id', $id);
            $this->db->delete(db_prefix() . 'user_meta');

            $this->db->where('client_id', $id);
            $this->db->update(db_prefix() . 'leads', ['client_id' => 0]);

            // Delete all projects
            $this->load->model('projects_model');
            $this->db->where('clientid', $id);
            $projects = $this->db->get(db_prefix() . 'projects')->result_array();
            foreach ($projects as $project) {
                $this->projects_model->delete($project['id']);
            }
        }
        if ($affectedRows > 0) {
            hooks()->do_action('after_client_deleted', $id);

            // Delete activity log caused by delete customer function
            if ($last_activity) {
                $this->db->where('id >', $last_activity->id);
                $this->db->delete(db_prefix() . 'activity_log');
            }

            log_activity('Customer Deleted', '', $id);

            return true;
        }

        return false;
    }

    /**
     * Delete customer contact
     * @param  mixed $id contact id
     * @return boolean
     */
    public function delete_contact($id)
    {
        hooks()->do_action('before_delete_contact', $id);

        $this->db->where('id', $id);
        $result      = $this->db->get(db_prefix() . 'contacts')->row();
        $customer_id = $result->userid;

        $last_activity = get_last_system_activity_id();

        $this->db->where('id', $id);
        $this->db->delete(db_prefix() . 'contacts');

        if ($this->db->affected_rows() > 0) {
            if (is_dir(get_upload_path_by_type('contact_profile_images') . $id)) {
                delete_dir(get_upload_path_by_type('contact_profile_images') . $id);
            }

            $this->db->where('contact_id', $id);
            $this->db->delete(db_prefix() . 'consents');

            $this->db->where('contact_id', $id);
            $this->db->delete(db_prefix() . 'shared_customer_files');

            $this->db->where('userid', $id);
            $this->db->where('staff', 0);
            $this->db->delete(db_prefix() . 'dismissed_announcements');

            $this->db->where('relid', $id);
            $this->db->where('fieldto', 'contacts');
            $this->db->delete(db_prefix() . 'customfieldsvalues');

            $this->db->where('userid', $id);
            $this->db->delete(db_prefix() . 'contact_permissions');

            $this->db->where('user_id', $id);
            $this->db->where('staff', 0);
            $this->db->delete(db_prefix() . 'user_auto_login');

            $this->db->select('ticketid');
            $this->db->where('contactid', $id);
            $this->db->where('userid', $customer_id);
            $tickets = $this->db->get(db_prefix() . 'tickets')->result_array();

            $this->load->model('tickets_model');
            foreach ($tickets as $ticket) {
                $this->tickets_model->delete($ticket['ticketid']);
            }

            $this->load->model('tasks_model');

            $this->db->where('addedfrom', $id);
            $this->db->where('is_added_from_contact', 1);
            $tasks = $this->db->get(db_prefix() . 'tasks')->result_array();

            foreach ($tasks as $task) {
                $this->tasks_model->delete_task($task['id'], false);
            }

            // Added from contact in customer profile
            $this->db->where('contact_id', $id);
            $this->db->where('rel_type', 'customer');
            $attachments = $this->db->get(db_prefix() . 'files')->result_array();

            foreach ($attachments as $attachment) {
                $this->delete_attachment($attachment['id']);
            }

            // Remove contact files uploaded to tasks
            $this->db->where('rel_type', 'task');
            $this->db->where('contact_id', $id);
            $filesUploadedFromContactToTasks = $this->db->get(db_prefix() . 'files')->result_array();

            foreach ($filesUploadedFromContactToTasks as $file) {
                $this->tasks_model->remove_task_attachment($file['id']);
            }

            $this->db->where('contact_id', $id);
            $tasksComments = $this->db->get(db_prefix() . 'task_comments')->result_array();
            foreach ($tasksComments as $comment) {
                $this->tasks_model->remove_comment($comment['id'], true);
            }

            $this->load->model('projects_model');

            $this->db->where('contact_id', $id);
            $files = $this->db->get(db_prefix() . 'project_files')->result_array();
            foreach ($files as $file) {
                $this->projects_model->remove_file($file['id'], false);
            }

            $this->db->where('contact_id', $id);
            $discussions = $this->db->get(db_prefix() . 'projectdiscussions')->result_array();
            foreach ($discussions as $discussion) {
                $this->projects_model->delete_discussion($discussion['id'], false);
            }

            $this->db->where('contact_id', $id);
            $discussionsComments = $this->db->get(db_prefix() . 'projectdiscussioncomments')->result_array();
            foreach ($discussionsComments as $comment) {
                $this->projects_model->delete_discussion_comment($comment['id'], false);
            }

            $this->db->where('contact_id', $id);
            $this->db->delete(db_prefix() . 'user_meta');

            $this->db->where('(email="' . $result->email . '" OR bcc LIKE "%' . $result->email . '%" OR cc LIKE "%' . $result->email . '%")');
            $this->db->delete(db_prefix() . 'mail_queue');

            if (is_gdpr()) {
                $this->db->where('email', $result->email);
                $this->db->delete(db_prefix() . 'listemails');

                if (!empty($result->last_ip)) {
                    $this->db->where('ip', $result->last_ip);
                    $this->db->delete(db_prefix() . 'knowedge_base_article_feedback');
                }

                $this->db->where('email', $result->email);
                $this->db->delete(db_prefix() . 'tickets_pipe_log');

                $this->db->where('email', $result->email);
                $this->db->delete(db_prefix() . 'tracked_mails');

                $this->db->where('contact_id', $id);
                $this->db->delete(db_prefix() . 'project_activity');

                $this->db->where('(additional_data LIKE "%' . $result->email . '%" OR full_name LIKE "%' . $result->firstname . ' ' . $result->lastname . '%")');
                $this->db->where('additional_data != "" AND additional_data IS NOT NULL');
                $this->db->delete(db_prefix() . 'sales_activity');

                $contactActivityQuery = false;
                if (!empty($result->email)) {
                    $this->db->or_like('description', $result->email);
                    $contactActivityQuery = true;
                }
                if (!empty($result->firstname)) {
                    $this->db->or_like('description', $result->firstname);
                    $contactActivityQuery = true;
                }
                if (!empty($result->lastname)) {
                    $this->db->or_like('description', $result->lastname);
                    $contactActivityQuery = true;
                }

                if (!empty($result->phonenumber)) {
                    $this->db->or_like('description', $result->phonenumber);
                    $contactActivityQuery = true;
                }

                if (!empty($result->last_ip)) {
                    $this->db->or_like('description', $result->last_ip);
                    $contactActivityQuery = true;
                }

                if ($contactActivityQuery) {
                    $this->db->delete(db_prefix() . 'activity_log');
                }
            }

            // Delete activity log caused by delete contact function
            if ($last_activity) {
                $this->db->where('id >', $last_activity->id);
                $this->db->delete(db_prefix() . 'activity_log');
            }

            hooks()->do_action('contact_deleted', $id, $result);
            log_activity('Contact Deleted (#' . $id . ')', '', $customer_id);

            return true;
        }

        return false;
    }

    /**
     * Get customer default currency
     * @param  mixed $id customer id
     * @return mixed
     */
    public function get_customer_default_currency($id)
    {
        $this->db->select('default_currency');
        $this->db->where('userid', $id);
        $result = $this->db->get(db_prefix() . 'clients')->row();
        if ($result) {
            return $result->default_currency;
        }

        return false;
    }

    /**
     *  Get customer billing details
     * @param   mixed $id   customer id
     * @return  array
     */
    public function get_customer_billing_and_shipping_details($id)
    {
        $this->db->select('billing_street,billing_city,billing_state,billing_zip,billing_country,shipping_street,shipping_city,shipping_state,shipping_zip,shipping_country');
        $this->db->from(db_prefix() . 'clients');
        $this->db->where('userid', $id);

        $result = $this->db->get()->result_array();
        if (count($result) > 0) {
            $result[0]['billing_street']  = clear_textarea_breaks($result[0]['billing_street']);
            $result[0]['shipping_street'] = clear_textarea_breaks($result[0]['shipping_street']);
        }

        return $result;
    }

    /**
     * Get customer files uploaded in the customer profile
     * @param  mixed $id    customer id
     * @param  array  $where perform where
     * @return array
     */
    public function get_customer_files($id, $where = [])
    {
        $this->db->where($where);
        $this->db->where('rel_id', $id);
        $this->db->where('rel_type', 'customer');
        $this->db->order_by('dateadded', 'desc');

        return $this->db->get(db_prefix() . 'files')->result_array();
    }

    /**
     * Delete customer attachment uploaded from the customer profile
     * @param  mixed $id attachment id
     * @return boolean
     */
    public function delete_attachment($id)
    {
        $this->db->where('id', $id);
        $attachment = $this->db->get(db_prefix() . 'files')->row();
        $deleted    = false;
        if ($attachment) {
            if (empty($attachment->external)) {
                $relPath  = get_upload_path_by_type('customer') . $attachment->rel_id . '/';
                $fullPath = $relPath . $attachment->file_name;
                unlink($fullPath);
                $fname     = pathinfo($fullPath, PATHINFO_FILENAME);
                $fext      = pathinfo($fullPath, PATHINFO_EXTENSION);
                $thumbPath = $relPath . $fname . '_thumb.' . $fext;
                if (file_exists($thumbPath)) {
                    unlink($thumbPath);
                }
            }

            $this->db->where('id', $id);
            $this->db->delete(db_prefix() . 'files');
            if ($this->db->affected_rows() > 0) {
                $deleted = true;
                $this->db->where('file_id', $id);
                $this->db->delete(db_prefix() . 'shared_customer_files');
                log_activity('Customer Attachment Deleted [ID: ' . $attachment->rel_id . ']');
            }

            if (is_dir(get_upload_path_by_type('customer') . $attachment->rel_id)) {
                // Check if no attachments left, so we can delete the folder also
                $other_attachments = list_files(get_upload_path_by_type('customer') . $attachment->rel_id);
                if (count($other_attachments) == 0) {
                    delete_dir(get_upload_path_by_type('customer') . $attachment->rel_id);
                }
            }
        }

        return $deleted;
    }

    /**
     * @param  integer ID
     * @param  integer Status ID
     * @return boolean
     * Update contact status Active/Inactive
     */
    public function change_contact_status($id, $status)
    {
        $status = hooks()->apply_filters('change_contact_status', $status, $id);

        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'contacts', [
            'active' => $status,
        ]);
        if ($this->db->affected_rows() > 0) {
            hooks()->do_action('contact_status_changed', [
                'id'     => $id,
                'status' => $status,
            ]);

            $contact = $this->get_contact($id);
            log_activity('Contact Status Changed [ContactID: ' . $id . ' Status(Active/Inactive): ' . $status . ']', '', $contact->userid);

            return true;
        }

        return false;
    }

    /**
     * @param  integer ID
     * @param  integer Status ID
     * @return boolean
     * Update client status Active/Inactive
     */
    public function change_client_status($id, $status)
    {
        $this->db->where('userid', $id);
        $this->db->update(db_prefix() . 'clients', [
            'active' => $status,
        ]);

        if ($this->db->affected_rows() > 0) {
            hooks()->do_action('client_status_changed', [
                'id'     => $id,
                'status' => $status,
            ]);

            log_activity('Customer Status Changed [ID: ' . $id . ' Status(Active/Inactive): ' . $status . ']');

            return true;
        }

        return false;
    }

    /**
     * Change contact password, used from client area
     * @param  mixed $id          contact id to change password
     * @param  string $oldPassword old password to verify
     * @param  string $newPassword new password
     * @return boolean
     */
    public function change_contact_password($id, $oldPassword, $newPassword)
    {
        // Get current password
        $this->db->where('id', $id);
        $client = $this->db->get(db_prefix() . 'contacts')->row();

        if (!app_hasher()->CheckPassword($oldPassword, $client->password)) {
            return [
                'old_password_not_match' => true,
            ];
        }

        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'contacts', [
            'last_password_change' => date('Y-m-d H:i:s'),
            'password'             => app_hash_password($newPassword),
        ]);

        if ($this->db->affected_rows() > 0) {
            log_activity('Contact Password Changed [ContactID: ' . $id . ']');

            return true;
        }

        return false;
    }

    /**
     * Get customer groups where customer belongs
     * @param  mixed $id customer id
     * @return array
     */
    public function get_customer_groups($id)
    {
        return $this->client_groups_model->get_customer_groups($id);
    }

    /**
     * Get all customer groups
     * @param  string $id
     * @return mixed
     */
    public function get_groups($id = '')
    {
        return $this->client_groups_model->get_groups($id);
    }

    public function get_customer_have_note($where = [])
    {
        if (!is_leader_member()) {
            $this->db->join(
                db_prefix() . 'customer_admins',
                db_prefix() . 'customer_admins.customer_id=' . db_prefix() . 'clients.userid
                AND ' . db_prefix() . 'customer_admins.staff_id = ' . get_staff_user_id()
            );
        }

        $this->db
            ->select(db_prefix() . 'clients.*')
            ->from(db_prefix() . 'clients')
            ->where(array_merge($where, ['rel_type' => 'customer']))
            ->join(db_prefix() . 'notes', db_prefix() . 'notes.rel_id=' . db_prefix() . 'clients.userid')
            ->group_by(db_prefix() . 'clients.userid');

        return $this->db->get()->result_array();
    }

    public function get_customer_have_contract($where = [])
    {
        if (!is_leader_member()) {
            $this->db->join(
                db_prefix() . 'customer_admins',
                db_prefix() . 'customer_admins.customer_id=' . db_prefix() . 'clients.userid
                AND ' . db_prefix() . 'customer_admins.staff_id = ' . get_staff_user_id()
            );
        }

        $this->db
            ->select(db_prefix() . 'clients.*')
            ->from(db_prefix() . 'clients')
            ->where($where)
            ->join(db_prefix() . 'invoices', db_prefix() . 'invoices.clientid=' . db_prefix() . 'clients.userid');

        return $this->db->get()->result_array();
    }

    /**
     * Delete customer groups
     * @param  mixed $id group id
     * @return boolean
     */
    public function delete_group($id)
    {
        return $this->client_groups_model->delete($id);
    }

    /**
     * Add new customer groups
     * @param array $data $_POST data
     */
    public function add_group($data)
    {
        return $this->client_groups_model->add($data);
    }

    /**
     * Edit customer group
     * @param  array $data $_POST data
     * @return boolean
     */
    public function edit_group($data)
    {
        return $this->client_groups_model->edit($data);
    }

    /**
     * Create new vault entry
     * @param  array $data        $_POST data
     * @param  mixed $customer_id customer id
     * @return boolean
     */
    public function vault_entry_create($data, $customer_id)
    {
        return $this->client_vault_entries_model->create($data, $customer_id);
    }

    /**
     * Update vault entry
     * @param  mixed $id   vault entry id
     * @param  array $data $_POST data
     * @return boolean
     */
    public function vault_entry_update($id, $data)
    {
        return $this->client_vault_entries_model->update($id, $data);
    }

    /**
     * Delete vault entry
     * @param  mixed $id entry id
     * @return boolean
     */
    public function vault_entry_delete($id)
    {
        return $this->client_vault_entries_model->delete($id);
    }

    /**
     * Get customer vault entries
     * @param  mixed $customer_id
     * @param  array  $where       additional wher
     * @return array
     */
    public function get_vault_entries($customer_id, $where = [])
    {
        return $this->client_vault_entries_model->get_by_customer_id($customer_id, $where);
    }

    /**
     * Get single vault entry
     * @param  mixed $id vault entry id
     * @return object
     */
    public function get_vault_entry($id)
    {
        return $this->client_vault_entries_model->get($id);
    }

    /**
     * Get customer statement formatted
     * @param  mixed $customer_id customer id
     * @param  string $from        date from
     * @param  string $to          date to
     * @return array
     */
    public function get_statement($customer_id, $from, $to)
    {
        return $this->statement_model->get_statement($customer_id, $from, $to);
    }

    /**
     * Send customer statement to email
     * @param  mixed $customer_id customer id
     * @param  array $send_to     array of contact emails to send
     * @param  string $from        date from
     * @param  string $to          date to
     * @param  string $cc          email CC
     * @return boolean
     */
    public function send_statement_to_email($customer_id, $send_to, $from, $to, $cc = '')
    {
        return $this->statement_model->send_statement_to_email($customer_id, $send_to, $from, $to, $cc);
    }

    /**
     * When customer register, mark the contact and the customer as inactive and set the registration_confirmed field to 0
     * @param  mixed $client_id  the customer id
     * @return boolean
     */
    public function require_confirmation($client_id)
    {
        $contact_id = get_primary_contact_user_id($client_id);
        $this->db->where('userid', $client_id);
        $this->db->update(db_prefix() . 'clients', ['active' => 0, 'registration_confirmed' => 0]);

        $this->db->where('id', $contact_id);
        $this->db->update(db_prefix() . 'contacts', ['active' => 0]);

        return true;
    }

    public function confirm_registration($client_id)
    {
        $contact_id = get_primary_contact_user_id($client_id);
        $this->db->where('userid', $client_id);
        $this->db->update(db_prefix() . 'clients', ['active' => 1, 'registration_confirmed' => 1]);

        $this->db->where('id', $contact_id);
        $this->db->update(db_prefix() . 'contacts', ['active' => 1]);

        $contact = $this->get_contact($contact_id);

        if ($contact) {
            send_mail_template('customer_registration_confirmed', $contact);

            return true;
        }

        return false;
    }

    public function send_verification_email($id)
    {
        $contact = $this->get_contact($id);

        if (empty($contact->email)) {
            return false;
        }

        $success = send_mail_template('customer_contact_verification', $contact);

        if ($success) {
            $this->db->where('id', $id);
            $this->db->update(db_prefix() . 'contacts', ['email_verification_sent_at' => date('Y-m-d H:i:s')]);
        }

        return $success;
    }

    public function mark_email_as_verified($id)
    {
        $contact = $this->get_contact($id);

        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'contacts', [
            'email_verified_at'          => date('Y-m-d H:i:s'),
            'email_verification_key'     => null,
            'email_verification_sent_at' => null,
        ]);

        if ($this->db->affected_rows() > 0) {

            // Check for previous tickets opened by this email/contact and link to the contact
            $this->load->model('tickets_model');
            $this->tickets_model->transfer_email_tickets_to_contact($contact->email, $contact->id);

            return true;
        }

        return false;
    }

    public function get_clients_distinct_countries()
    {
        return $this->db->query('SELECT DISTINCT(country_id), short_name FROM ' . db_prefix() . 'clients JOIN ' . db_prefix() . 'countries ON ' . db_prefix() . 'countries.country_id=' . db_prefix() . 'clients.country')->result_array();
    }

    public function send_notification_customer_profile_file_uploaded_to_responsible_staff($contact_id, $customer_id)
    {
        $staff         = $this->get_staff_members_that_can_access_customer($customer_id);
        $merge_fields  = $this->app_merge_fields->format_feature('client_merge_fields', $customer_id, $contact_id);
        $notifiedUsers = [];


        foreach ($staff as $member) {
            mail_template('customer_profile_uploaded_file_to_staff', $member['email'], $member['staffid'])
                ->set_merge_fields($merge_fields)
                ->send();

            if (add_notification([
                'touserid' => $member['staffid'],
                'description' => 'not_customer_uploaded_file',
                'link' => 'clients/client/' . $customer_id . '?group=attachments',
            ])) {
                array_push($notifiedUsers, $member['staffid']);
            }
        }
        pusher_trigger_notification($notifiedUsers);
    }

    public function get_staff_members_that_can_access_customer($id)
    {
        $id = $this->db->escape_str($id);

        return $this->db->query('SELECT * FROM ' . db_prefix() . 'staff
            WHERE (
                    admin=1
                    OR staffid IN (SELECT staff_id FROM ' . db_prefix() . "customer_admins WHERE customer_id='.$id.')
                    OR staffid IN(SELECT staff_id FROM " . db_prefix() . 'staff_permissions WHERE feature = "customers" AND capability="view")
                )
            AND active=1')->result_array();
    }

    private function check_zero_columns($data)
    {
        if (!isset($data['show_primary_contact'])) {
            $data['show_primary_contact'] = 0;
        }

        if (isset($data['default_currency']) && $data['default_currency'] == '' || !isset($data['default_currency'])) {
            $data['default_currency'] = 0;
        }

        if (isset($data['country']) && $data['country'] == '' || !isset($data['country'])) {
            $data['country'] = 0;
        }

        if (isset($data['billing_country']) && $data['billing_country'] == '' || !isset($data['billing_country'])) {
            $data['billing_country'] = 0;
        }

        if (isset($data['shipping_country']) && $data['shipping_country'] == '' || !isset($data['shipping_country'])) {
            $data['shipping_country'] = 0;
        }

        return $data;
    }

    public function delete_contact_profile_image($id)
    {
        hooks()->do_action('before_remove_contact_profile_image');
        if (file_exists(get_upload_path_by_type('contact_profile_images') . $id)) {
            delete_dir(get_upload_path_by_type('contact_profile_images') . $id);
        }
        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'contacts', [
            'profile_image' => null,
        ]);
    }

    /**
     * @param $projectId
     * @param  string  $tasks_email
     *
     * @return array[]
     */
    public function get_contacts_for_project_notifications($projectId, $type)
    {
        $this->db->select('clientid,contact_notification,notify_contacts');
        $this->db->from(db_prefix() . 'projects');
        $this->db->where('id', $projectId);
        $project = $this->db->get()->row();

        if (!in_array($project->contact_notification, [1, 2])) {
            return [];
        }

        $this->db
            ->where('userid', $project->clientid)
            ->where('active', 1)
            ->where($type, 1);

        if ($project->contact_notification == 2) {
            $projectContacts = unserialize($project->notify_contacts);
            $this->db->where_in('id', $projectContacts);
        }

        return $this->db->get(db_prefix() . 'contacts')->result_array();
    }

    public function log_contact_terminated($data, $query = false)
    {
        if ($query) {
            $this->db->query('INSERT INTO ' . db_prefix() . 'contact_terminated(customer_id, staff_id, created_at)
            SELECT customer_id, staff_id, "' . date('Y-m-d H:i:s') . '"
                FROM ' . db_prefix() . 'customer_admins WHERE ' . $data . '
                GROUP BY customer_id');
        } else {
            $this->db->insert_batch(db_prefix() . 'contact_terminated', $data);
        }

        return true;
    }

    public function get_client_has_no_admin($id = '', $where = [], $limit = '')
    {
        $this->db->select(implode(',', prefixed_table_fields_array(db_prefix() . 'clients')) . ',' . get_sql_select_client_company());

        if ((is_array($where) && count($where) > 0) || (is_string($where) && $where != '')) {
            $this->db->where($where);
        }

        $this->db->where(
            'NOT EXISTS (
            SELECT * FROM ' . db_prefix() . 'customer_admins WHERE ' . db_prefix() . 'clients.userid = ' . db_prefix()  . 'customer_admins.customer_id
            )'
        );

        if (is_numeric($id)) {
            $this->db->where(db_prefix() . 'clients.userid', $id);
            $client = $this->db->get(db_prefix() . 'clients')->row();

            if ($client && get_option('company_requires_vat_number_field') == 0) {
                $client->vat = null;
            }

            $GLOBALS['client'] = $client;

            return $client;
        }

        $this->db->order_by('userid', 'desc');
        $limit ? $this->db->limit($limit) : '';

        return $this->db->get(db_prefix() . 'clients')->result_array();
    }

    public function fetchCompanies($phoneNumber)
    {
        $companies = $this->db
            ->select(join(', ', [
                db_prefix() . 'clients.userid as client_id',
                get_sql_select_client_company(),
                'contact.fullname as contact_fullname',
                '(select `description` from ' . db_prefix() . 'notes where ' . db_prefix() . 'notes.rel_type = "customer" AND ' . db_prefix() . 'notes.rel_id = ' . db_prefix() . 'clients.userid order by id desc limit 1) as latest_note'
            ]))
            ->from(db_prefix() . 'clients')
            ->join(db_prefix() . 'contacts as contact', db_prefix() . 'clients.userid = contact.userid', 'left')
            ->where('REPLACE(contact.phonenumber, " ", "") = ', $phoneNumber)
            ->or_where('REPLACE(' . db_prefix() . 'clients.phonenumber, " ", "") = ', $phoneNumber)
            ->get()
            ->result_array();

        return $companies ?? [];
    }

    /**
     * Get all offcies based on client
     * @param integer $clentId client id used to fetch offices
     * @return array
     */
    public function get_offices($clientId)
    {
        return $this->db
            ->select(join(',', [
                'id',
                'office_address'
            ]))
            ->where('client_id', $clientId)
            ->get(db_prefix() . 'client_offices')
            ->result_array();
    }

    /**
     * Get all branches based on client id
     * @param integer $clientId client id used to get branches
     * @return array
     */
    public function get_branches($clientId)
    {
        return $this->db
            ->where('client_id', $clientId)
            ->get(db_prefix() . 'client_branches')
            ->result_array();
    }

    /**
     * Get all affiliates based on client id
     * @param integer $clientId client id used to get affiliates
     * @return array
     */
    public function get_affiliates($clientId)
    {
        return $this->db
            ->where('client_id', $clientId)
            ->get(db_prefix() . 'client_affiliates')
            ->result_array();
    }

    /**
     * Get all affiliates based on client id
     * @param integer $clientId client id used to get affiliates
     * @return array
     */
    public function get_client_industries($clientId)
    {
        return $this->db
            ->where('client_id', $clientId)
            ->get(db_prefix() . 'client_industries')
            ->result_array();
    }

    /**
     * Get all affiliates based on client id
     * @param integer $clientId client id used to get affiliates
     * @return array
     */
    public function get_client_nationalities($clientId)
    {
        return $this->db
            ->where('client_id', $clientId)
            ->get(db_prefix() . 'client_nationalities')
            ->result_array();
    }

    /**
     * Get request related to client
     * @param integer $clientId client id used to get request
     * @return array
     */
    public function get_client_request($clientId)
    {
        return $this->db
            ->select(join(',', [
                db_prefix() . 'client_requests.created_at as created_at',
                db_prefix() . 'client_requests.approved_at as approved_at',
                'CONCAT(sale.firstname, " ", sale.lastname) AS sale_name',
                'CONCAT(sale_leader.firstname, " ", sale_leader.lastname) AS sale_leader_name',
                'CONCAT(sale_admin.firstname, " ", sale_admin.lastname) AS sale_admin_name',
                db_prefix() . 'client_requests.admin_id AS admin_approved_id',
                db_prefix() . 'client_requests.leader_id AS leader_approved_id',
                db_prefix() . 'client_requests.id AS request_id',
                db_prefix() . 'client_requests.request_status AS request_status'
            ]))
            ->where(db_prefix() . 'client_requests.client_id', $clientId)
            ->from(db_prefix() . 'client_requests')
            ->join(db_prefix() . 'staff as sale', '' . db_prefix() . 'client_requests.sale_id = sale.staffid')
            ->join(db_prefix() . 'staff as sale_leader', '' . db_prefix() . 'client_requests.leader_id = sale_leader.staffid', 'left')
            ->join(db_prefix() . 'staff as sale_admin', '' . db_prefix() . 'client_requests.admin_id = sale_admin.staffid', 'left')
            ->get()
            ->row_array();
    }

    public function get_source_attachment($clientId)
    {
        return $this->db
            ->select('CONCAT(' . db_prefix() . 'files.rel_type, "/",' . db_prefix() . 'files.rel_id, "/", ' . db_prefix() . 'files.file_name) as attachment_url')
            ->from(db_prefix() . 'files')
            ->where(db_prefix() . 'files.rel_id', $clientId)
            ->where(db_prefix() . 'files.rel_type = "clilent_request"')
            ->get()
            ->row_array();
    }

    public function update_offices($clientId, $offices)
    {
        $updateList = [];
        // Add 0 to avoid exception when deleting without keep ids
        $keepIds = [0];
        $insertList = [];
        // If not have any record, then remove all existing values
        if (empty($offices['ids']) || count($offices['ids']) === 1) {
            $this->db->where('client_id', $clientId)->delete(db_prefix() . 'client_offices');
            return;
        }
        $ids = $offices['ids'];
        $addresses = $offices['addresses'];
        foreach ($ids as $index => $id) {
            // Skip template row
            if ($index == 0) {
                continue;
            }
            // Update
            if (!empty($id)) {
                $updateList[] = [
                    'id' => $id,
                    'office_address' => $addresses[$index]
                ];
                $keepIds[] = $id;
            } else {
                $data = array_filter([
                    'office_address' => $addresses[$index]
                ]);
                if (!empty($data)) {
                    $insertList[] = array_merge($data, ['client_id' => $clientId]);
                }
            }
        }

        // Delete all items are deleted
        $this->db
            ->where('client_id', $clientId)
            ->where_not_in('id', $keepIds)
            ->delete(db_prefix() . 'client_offices');

        // Update exsiting
        if (count($updateList)) {
            $this->db->update_batch(db_prefix() . 'client_offices', $updateList, 'id');
        }
        // Insert new one
        if (count($insertList)) {
            $this->db->insert_batch(db_prefix() . 'client_offices', $insertList);
        }
    }

    public function update_branches($clientId, $branches)
    {
        $updateList = [];
        // Add 0 to avoid exception when deleting without keep ids
        $keepIds = [0];
        $insertList = [];
        // If not have any record, then remove all existing values
        if (empty($branches['ids']) || count($branches['ids']) === 1) {
            $this->db->where('client_id', $clientId)->delete(db_prefix() . 'client_branches');
            return;
        }
        $ids = $branches['ids'];
        $name = $branches['name'];
        $vat = $branches['vat'];
        $business_address = $branches['business_address'];
        $office_address = $branches['office_address'];
        $note = $branches['note'];
        $status = $branches['status'];
        foreach ($ids as $index => $id) {
            // Skip template row
            if ($index == 0) {
                continue;
            }
            // Update
            if (!empty($id)) {
                $updateList[] = [
                    'id' => $id,
                    'branch_name' => $name[$index],
                    'branch_vat' => $vat[$index],
                    'branch_business_address' => $business_address[$index],
                    'branch_office_address' => $office_address[$index],
                    'branch_status' => $status[$index],
                    'branch_note' => $note[$index],
                ];
                $keepIds[] = $id;
            } else {
                $data = array_filter([
                    'branch_name' => $name[$index],
                    'branch_vat' => $vat[$index],
                    'branch_business_address' => $business_address[$index],
                    'branch_office_address' => $office_address[$index],
                    'branch_status' => $status[$index],
                    'branch_note' => $note[$index],
                ]);
                if (!empty($data)) {
                    $insertList[] = array_merge($data, ['client_id' => $clientId]);
                }
            }
        }

        // Delete all items are deleted
        $this->db
            ->where('client_id', $clientId)
            ->where_not_in('id', $keepIds)
            ->delete(db_prefix() . 'client_branches');

        // Update exsiting
        if (count($updateList)) {
            $this->db->update_batch(db_prefix() . 'client_branches', $updateList, 'id');
        }
        // Insert new one
        if (count($insertList)) {
            $this->db->insert_batch(db_prefix() . 'client_branches', $insertList);
        }
    }

    public function update_affiliates($clientId, $affiliates)
    {
        $updateList = [];
        // Add 0 to avoid exception when deleting without keep ids
        $keepIds = [0];
        $insertList = [];
        // If not have any record, then remove all existing values
        if (empty($affiliates['ids']) || count($affiliates['ids']) === 1) {
            $this->db->where('client_id', $clientId)->delete(db_prefix() . 'client_affiliates');
            return;
        }
        $ids = $affiliates['ids'];
        $relation_type = $affiliates['relation_type'];
        $related_vat = $affiliates['related_vat'];
        $related_company_name = $affiliates['related_company_name'];
        foreach ($ids as $index => $id) {
            // Skip template row
            if ($index == 0) {
                continue;
            }
            // Update
            if (!empty($id)) {
                $updateList[] = [
                    'id' => $id,
                    'relation_type' => $relation_type[$index],
                    'related_vat' => $related_vat[$index],
                    'related_company_name' => $related_company_name[$index]
                ];
                $keepIds[] = $id;
            } else {
                $data = array_filter([
                    'relation_type' => $relation_type[$index],
                    'related_vat' => $related_vat[$index],
                    'related_company_name' => $related_company_name[$index],
                ]);
                if (!empty($data)) {
                    $insertList[] = array_merge($data, ['client_id' => $clientId]);
                }
            }
        }

        // Delete all items are deleted
        $this->db
            ->where('client_id', $clientId)
            ->where_not_in('id', $keepIds)
            ->delete(db_prefix() . 'client_affiliates');

        // Update exsiting
        if (count($updateList)) {
            $this->db->update_batch(db_prefix() . 'client_affiliates', $updateList, 'id');
        }
        // Insert new one
        if (count($insertList)) {
            $this->db->insert_batch(db_prefix() . 'client_affiliates', $insertList);
        }
    }

    public function update_industries($clientId, $industries)
    {
        // Delete all items are deleted
        $this->db
            ->where('client_id', $clientId)
            ->delete(db_prefix() . 'client_industries');

        if (isset($industries) && count($industries)) {
            $this->db->insert_batch(db_prefix() . 'client_industries', array_map(function ($id) use ($clientId) {
                return [
                    'client_id' => $clientId,
                    'industry_id' => $id
                ];
            }, $industries));
        }
    }

    public function update_nationalities($clientId, $nationalities)
    {
        $this->db
            ->where('client_id', $clientId)
            ->delete(db_prefix() . 'client_nationalities');

        if (isset($nationalities) && count($nationalities)) {
            $this->db->insert_batch(db_prefix() . 'client_nationalities', array_map(function ($id) use ($clientId) {
                return [
                    'client_id' => $clientId,
                    'national_id' => $id
                ];
            }, $nationalities));
        }
    }

    public function isDuplicateVat($clientId)
    {
        return total_rows(db_prefix() . 'clients', [
            'userid <>' => intval($clientId),
            'approved_at IS NOT NULL' => null,
            'vat = (select vat from ' . db_prefix() . 'clients where userid = ' . $clientId . ')' => null
        ]) > 0;
    }

    public function updateRelationsData($userId, $relationsData)
    {
        // Will get offices, branches, affiliates, industries, nationalities
        extract($relationsData);
        $this->update_offices($userId, $offices);
        $this->update_branches($userId, $branches);
        $this->update_affiliates($userId, $affiliates);
        $this->update_industries($userId, $industries);
        $this->update_nationalities($userId, $nationalities);
        $this->updateAmsCompanyIds($userId, $ams_company_ids);
        $this->load->model('client_requests_model');
        if (isset($_FILES['client_request_attachment'])) {
            $this->load->helper(['upload']);
            $attachmentUuid = handle_client_request_attachment($userId, 'client_request_attachment');
            $this->updateRequestAttachment($userId, $attachmentUuid);
        }

        return true;
    }

    public function updateAmsCompanyIds($userId, $amsCompanyIds)
    {
        if (has_permission('customers', '', 'edit_id_ams')) {
            ClientAmsCompany::where('client_id', $userId)->whereNotIn('ams_company_id', $amsCompanyIds)->delete();
            if (count($amsCompanyIds)) {
                ClientAmsCompany::upsert(
                    array_map(fn($id) => ['client_id' => intval($userId), 'ams_company_id' => intval($id)], $amsCompanyIds),
                    ['client_id', 'ams_company_id']
                );
            }
        }
    }

    /**
     * Update attachment
     * @param integer $clientId created request id
     * @param string $attachmentUuid attachment uuid that is uploaded recently
     * @return bool
     */
    public function updateRequestAttachment($clientId, $attachmentUuid)
    {

        // Find to remove old file if exists in case update
        $oldAttachment = $this->db
            ->select(db_prefix() . 'files.id', db_prefix() . 'files.file_name')
            ->from(db_prefix() . 'files')
            ->where(db_prefix() . 'files.rel_id', $clientId)
            ->where(db_prefix() . 'files.rel_type = "clilent_request"')
            ->where(db_prefix() . 'files.attachment_key !=', $attachmentUuid)
            ->get()
            ->row_array();

        if (isset($oldAttachment)) {
            @unlink(get_upload_path_by_type('client_request') . $clientId . '/' . $oldAttachment['file_name']);
            $this->db->where(db_prefix() . 'files.id', $oldAttachment['id'])->delete(db_prefix() . 'files');
        }

        $this->db
            ->where('userid', $clientId)
            ->update(db_prefix() . 'clients', [
                'source_attachment' => $attachmentUuid
            ]);

        return $this->db->affected_rows() > 0;
    }

    public function getAmsCompanyIds($userId)
    {
        return ClientAmsCompany::where('client_id', $userId)->select('ams_company_id')->get()->pluck('ams_company_id')->toArray();
    }

    public function createPayers(array $userIds, $contactId)
    {
        // Fetch client informations
        $clients = Client::query()
            ->select('userid')
            ->whereIn('userid', $userIds)
            ->wherehas('contacts', fn($query) => $query->where('id', $contactId))
            ->with([
                'contacts' => fn($query) => $query->select(['id','userid','fullname','email','phonenumber'])->where('id', $contactId)
            ])
            ->get();

        if ($clients->count()) {
            $existingPayers = ClientPayer::whereIn('client_id', $userIds)
                ->get()
                ->map(fn($payer) => ['payerNo' => $payer->payer_no, 'client_id' => $payer->client_id]);

            $existClients = $existingPayers->pluck('client_id')->toArray();
            $newClients = $clients->filter(fn($client) => !in_array($client->userid, $existClients))->values();

            $newPayers = collect();

            if ($newClients->isNotEmpty()) {
                // Mapping data to create payer
                $payers = $newClients->map(
                    fn($client) =>
                    new CreatePayerDTO(
                        ClientPayer::getPayerNoFromClientId($client->userid),
                        $client->contacts->first()->fullname,
                        ClientPayer::getPayerPhoneClientId($client->userid),
                    )
                )->toArray();

                $result = AccountingService::createPayer($payers);

                if (!$result['success']) {
                    throw new \Exception($result['message'] ?? 'Failed to create payer');
                }

                $newPayers = collect($result['data']['succList'] ?? []);
                $failedPayers = collect($result['data']['errorList'] ?? []);

                if (!$newPayers->count()) {
                    throw new \Exception('Failed to create payer');
                }

                // Store to the client_payers
                $newClientPayers = $newPayers->map(
                    function ($payer) {
                        return [
                            'client_id' => ClientPayer::getClientIdFromPayerNo($payer['payerNo']),
                            'payer_no' => $payer['payerNo'],
                            'payer_name' => $payer['payerName'],
                            'mother_account_no' => '',
                            'e_collection_code' => '',
                        ];
                    }
                )->toArray();

                ClientPayer::insert($newClientPayers);

            }
            $newPayers = $newPayers->merge($existingPayers);

            $newPayers = $newPayers->keyBy('payerNo');

            $availableECodes = AccountingService::inquiryEcCodes(new InquiryEcCodeDTO(1,10, InquiryEcCodeDTO::STATUS_UNUSED));
            if (!$availableECodes['success']) {
                throw new \Exception($availableECodes['message'] ?? 'Failed to get available EC Codes');
            }
            $ecCodes = collect($availableECodes['data']['records'] ?? []);
            if ($ecCodes->isEmpty()) {
                throw new \Exception('There is no avaiable EC codes');
            }

            $index = 0;
            $eCollections = $newPayers->map(function ($payer) use (&$ecCodes, &$index) {
                $eCode = $ecCodes->get($index++);
                return new MapECollectionDTO(
                    $payer['payerNo'],
                    $eCode['ecollectionCd'],
                    PAYER_COMPANY_NAME,
                );
            })
            ->values()
            ->toArray();

            // Call api to create e-collection code
            $eResult = AccountingService::mapECollectionCode($eCollections);
            if (!$eResult['success']) {
                throw new \Exception($eResult['message'] ?? 'Failed to create e collection');
            }

            $mapedEcodes = collect($eResult['data']['succList'] ?? []);
            $failedEcodes = collect($eResult['data']['errorList'] ?? []);

            if (!$mapedEcodes->count()) {
                throw new \Exception('Failed to create e collection');
            }

            // Store to the client_payers
            $clientPayers = $mapedEcodes->map(
                function ($eCode)  use ($newPayers) {
                    $foundPayer = $newPayers->get($eCode['payerNo']);
                    return [
                        'client_id' => ClientPayer::getClientIdFromPayerNo($eCode['payerNo']),
                        'payer_no' => $eCode['payerNo'],
                        'payer_name' => $foundPayer['payerName'] ?? '',
                        'mother_account_no' => $eCode['motherAccntNo'],
                        'e_collection_code' => $eCode['ecollectionCd'],
                    ];
                }
            )->toArray();

            ClientPayer::upsert($clientPayers, 'client_id', ['e_collection_code', 'payer_name', 'mother_account_no']);

            return $failedEcodes->count() ? [
                'failedEcodes' => $failedEcodes,
            ] : true;
        }

        return false;
    }
}

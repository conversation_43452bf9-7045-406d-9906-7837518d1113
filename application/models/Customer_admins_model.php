<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Customer_admins_model extends App_Model
{
    public $STATUS_WAITING = 0;
    public $STATUS_CONTACTED = 1;

    public function __construct()
    {
        parent::__construct();
    }

    public function updateStatusContact($where, $data = [])
    {
        $this->db
            ->where($where, null, false)
            ->update(db_prefix() . 'customer_admins', $data);

        return $this->db->affected_rows() > 0;
    }

    /**
     * Remove customer admin based on the where condition
     * @param string $where query string
     * @return integer Number records are deleted
     */
    public function removeCustomerAdmin($where)
    {
        $this->db
            ->where($where, null, false)
            ->delete(db_prefix().'customer_admins');

        return $this->db->affected_rows();
    }

    public function bulk_remove_cutomer_admins($ids = [])
    {
        $this->db->query("
            INSERT INTO
                ".db_prefix()."client_contact_pools(client_id, created_at)
            SELECT customer_id, NOW()
            FROM ".db_prefix()."customer_admins
            WHERE
                customer_id IN (".implode(',', $ids).")
                AND customer_id NOT IN (
                    SELECT client_id
                    from ".db_prefix()."client_contact_pools
                    WHERE
                        client_id IN (".implode(',', $ids).")
                        AND approved_at IS NULL
                )
            GROUP BY customer_id;
        ");
        
        // Remove from customer_admins
        $this->db->query("DELETE FROM ".db_prefix()."customer_admins WHERE customer_id IN (".implode(',', $ids).")");
    }
}

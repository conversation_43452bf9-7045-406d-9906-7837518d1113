<?php

use app\services\utilities\Arr;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\Joining\NestedQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\WildcardQuery;

defined('BASEPATH') or exit('No direct script access allowed');

class Misc_model extends App_Model
{
    public $notifications_limit;

    public $CUSTOMER_TYPE_INTERNAL = 0;
    public $CUSTOMER_TYPE_CONTACT_SUCCESS = 1;
    public $CUSTOMER_TYPE_CONTACT_AGAIN = 2;
    

    public function __construct()
    {
        parent::__construct();
        $this->notifications_limit = 15;
    }

    public function get_notifications_limit()
    {
        return hooks()->apply_filters('notifications_limit', $this->notifications_limit);
    }

    public function get_taxes_dropdown_template($name, $taxname, $type = '', $item_id = '', $is_edit = false, $manual = false)
    {
        // if passed manually - like in proposal convert items or project
        if ($manual == true) {
            // + is no longer used and is here for backward compatibilities
            if (is_array($taxname) || strpos($taxname, '+') !== false) {
                if (!is_array($taxname)) {
                    $__tax = explode('+', $taxname);
                } else {
                    $__tax = $taxname;
                }
                // Multiple taxes found // possible option from default settings when invoicing project
                $taxname = [];
                foreach ($__tax as $t) {
                    $tax_array = explode('|', $t);
                    if (isset($tax_array[0]) && isset($tax_array[1])) {
                        array_push($taxname, $tax_array[0] . '|' . $tax_array[1]);
                    }
                }
            } else {
                $tax_array = explode('|', $taxname);
                // isset tax rate
                if (isset($tax_array[0]) && isset($tax_array[1])) {
                    $tax = get_tax_by_name($tax_array[0]);
                    if ($tax) {
                        $taxname = $tax->name . '|' . $tax->taxrate;
                    }
                }
            }
        }
        // First get all system taxes
        $this->load->model('taxes_model');
        $taxes = $this->taxes_model->get();
        $i     = 0;
        foreach ($taxes as $tax) {
            unset($taxes[$i]['id']);
            $taxes[$i]['name'] = $tax['name'] . '|' . $tax['taxrate'];
            $i++;
        }
        if ($is_edit == true) {

            // Lets check the items taxes in case of changes.
            // Separate functions exists to get item taxes for Invoice, Estimate, Proposal, Credit Note
            $func_taxes = 'get_' . $type . '_item_taxes';
            if (function_exists($func_taxes)) {
                $item_taxes = call_user_func($func_taxes, $item_id);
            }

            foreach ($item_taxes as $item_tax) {
                $new_tax            = [];
                $new_tax['name']    = $item_tax['taxname'];
                $new_tax['taxrate'] = $item_tax['taxrate'];
                $taxes[]            = $new_tax;
            }
        }

        // In case tax is changed and the old tax is still linked to estimate/proposal when converting
        // This will allow the tax that don't exists to be shown on the dropdowns too.
        if (is_array($taxname)) {
            foreach ($taxname as $tax) {
                // Check if tax empty
                if ((!is_array($tax) && $tax == '') || is_array($tax) && $tax['taxname'] == '') {
                    continue;
                };
                // Check if really the taxname NAME|RATE don't exists in all taxes
                if (!value_exists_in_array_by_key($taxes, 'name', $tax)) {
                    if (!is_array($tax)) {
                        $tmp_taxname = $tax;
                        $tax_array   = explode('|', $tax);
                    } else {
                        $tax_array   = explode('|', $tax['taxname']);
                        $tmp_taxname = $tax['taxname'];
                        if ($tmp_taxname == '') {
                            continue;
                        }
                    }
                    $taxes[] = ['name' => $tmp_taxname, 'taxrate' => $tax_array[1]];
                }
            }
        }

        // Clear the duplicates
        $taxes = Arr::uniqueByKey($taxes, 'name');

        $select = '<select disabled class="selectpicker display-block tax" data-width="100%" name="' . $name . '" multiple data-none-selected-text="' . _l('no_tax') . '">';

        $selectedValue = "";
        foreach ($taxes as $tax) {
            $selected = '';
            if (is_array($taxname)) {
                foreach ($taxname as $_tax) {
                    if (is_array($_tax)) {
                        if ($_tax['taxname'] == $tax['name']) {
                            $selected = 'selected';
                            $selectedValue = $tax['name'];
                        }
                    } else {
                        if ($_tax == $tax['name']) {
                            $selected = 'selected';
                            $selectedValue = $tax['name'];
                        }
                    }
                }
            } else {
                if ($taxname == $tax['name']) {
                    $selected = 'selected';
                    $selectedValue = $tax['name'];
                }
            }

            $select .= '<option value="' . $tax['name'] . '" ' . $selected . ' data-taxrate="' . $tax['taxrate'] . '" data-taxname="' . $tax['name'] . '" data-subtext="' . $tax['name'] . '">' . $tax['taxrate'] . '%</option>';
        }
        $select .= '</select>';
        $select .= '<input name="' . $name . '" type="hidden" value="' . $selectedValue . '" />';

        return $select;
    }

    public function add_attachment_to_database($rel_id, $rel_type, $attachment, $external = false)
    {
        $data['dateadded'] = date('Y-m-d H:i:s');
        $data['rel_id']    = $rel_id;
        if (!isset($attachment[0]['staffid'])) {
            $data['staffid'] = get_staff_user_id();
        } else {
            $data['staffid'] = $attachment[0]['staffid'];
        }

        if (isset($attachment[0]['task_comment_id'])) {
            $data['task_comment_id'] = $attachment[0]['task_comment_id'];
        }

        $data['rel_type'] = $rel_type;

        if (isset($attachment[0]['contact_id'])) {
            $data['contact_id']          = $attachment[0]['contact_id'];
            $data['visible_to_customer'] = 1;
            if (isset($data['staffid'])) {
                unset($data['staffid']);
            }
        }

        $data['attachment_key'] = app_generate_hash();

        if ($external == false) {
            $data['file_name'] = $attachment[0]['file_name'];
            $data['filetype']  = $attachment[0]['filetype'];
        } else {
            $path_parts            = pathinfo($attachment[0]['name']);
            $data['file_name']     = $attachment[0]['name'];
            $data['external_link'] = $attachment[0]['link'];
            $data['filetype']      = !isset($attachment[0]['mime']) ? get_mime_by_extension('.' . $path_parts['extension']) : $attachment[0]['mime'];
            $data['external']      = $external;
            if (isset($attachment[0]['thumbnailLink'])) {
                $data['thumbnail_link'] = $attachment[0]['thumbnailLink'];
            }
        }

        $this->db->insert(db_prefix() . 'files', $data);
        $insert_id = $this->db->insert_id();

        if ($data['rel_type'] == 'customer' && isset($data['contact_id'])) {
            if (get_option('only_own_files_contacts') == 1) {
                $this->db->insert(db_prefix() . 'shared_customer_files', [
                    'file_id'    => $insert_id,
                    'contact_id' => $data['contact_id'],
                ]);
            } else {
                $this->db->select('id');
                $this->db->where('userid', $data['rel_id']);
                $contacts = $this->db->get(db_prefix() . 'contacts')->result_array();
                foreach ($contacts as $contact) {
                    $this->db->insert(db_prefix() . 'shared_customer_files', [
                        'file_id'    => $insert_id,
                        'contact_id' => $contact['id'],
                    ]);
                }
            }
        }

        return $insert_id;
    }

    public function get_file($id)
    {
        $this->db->where('id', $id);

        return $this->db->get(db_prefix() . 'files')->row();
    }

    public function get_note_attachments($id)
    {
        $this->db->select(implode(', ', prefixed_table_fields_array(db_prefix() . 'files')))
            ->where([
                db_prefix() . 'files.rel_id' => $id,
                db_prefix() . 'files.rel_type' => 'note'
            ])
            ->order_by(db_prefix() . 'files.id', 'desc')
            ->join(db_prefix() . 'notes', db_prefix() . 'notes.id = ' . db_prefix() . 'files.rel_id');

        return $this->db->get(db_prefix() . 'files')->result_array();
    }

    public function get_current_note_attachments($id)
    {
        return $this->db->select('files.*')
            ->from(db_prefix() . 'files files')
            ->where([
                'files.rel_id' => $id,
                'files.rel_type' => 'note'
            ])
            ->order_by('files.id', 'desc')
            ->join(db_prefix() . 'notes notes', 'notes.id = files.rel_id')
            ->get()
            ->first_row();
    }

    public function get_staff_started_timers()
    {
        $this->db->select(db_prefix() . 'taskstimers.*,' . db_prefix() . 'tasks.name as task_subject');
        $this->db->join(db_prefix() . 'staff', db_prefix() . 'staff.staffid=' . db_prefix() . 'taskstimers.staff_id');
        $this->db->join(db_prefix() . 'tasks', db_prefix() . 'tasks.id=' . db_prefix() . 'taskstimers.task_id', 'left');
        $this->db->where('staff_id', get_staff_user_id());
        $this->db->where('end_time IS NULL');

        return $this->db->get(db_prefix() . 'taskstimers')->result_array();
    }

    /**
     * Add reminder
     * @since  Version 1.0.2
     * @param mixed $data All $_POST data for the reminder
     * @param mixed $id   relid id
     * @return boolean
     */
    public function add_reminder($data, $id)
    {
        if (isset($data['notify_by_email'])) {
            $data['notify_by_email'] = 1;
        } //isset($data['notify_by_email'])
        else {
            $data['notify_by_email'] = 0;
        }
        $data['date']        = to_sql_date($data['date'], true);
        $data['description'] = nl2br($data['description']);
        $data['creator']     = get_staff_user_id();
        $this->db->insert(db_prefix() . 'reminders', $data);
        $insert_id = $this->db->insert_id();
        if ($insert_id) {
            if ($data['rel_type'] == 'lead') {
                $this->load->model('leads_model');
                $this->leads_model->log_lead_activity($data['rel_id'], 'not_activity_new_reminder_created', false, serialize([
                    get_staff_full_name($data['staff']),
                    _dt($data['date']),
                ]));
            }
            log_activity('New Reminder Added [' . ucfirst($data['rel_type']) . 'ID: ' . $data['rel_id'] . ' Description: ' . $data['description'] . ']');

            return true;
        } //$insert_id
        return false;
    }

    public function edit_reminder($data, $id)
    {
        if (isset($data['notify_by_email'])) {
            $data['notify_by_email'] = 1;
        } else {
            $data['notify_by_email'] = 0;
        }

        $data['date']        = to_sql_date($data['date'], true);
        $data['description'] = nl2br($data['description']);

        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'reminders', $data);

        if ($this->db->affected_rows() > 0) {
            return true;
        }

        return false;
    }

    public function get_notes($rel_id, $rel_type, $limit = '')
    {
        $this->db->from(db_prefix() . 'notes notes');
        $this->db->join(db_prefix() . 'staff staff', 'staff.staffid = notes.addedfrom');
        if ($rel_type == 'customer') {
            $this->db->select('notes.*, staff.firstname, staff.lastname, contacts.fullname as contact_name');
            $this->db->join(db_prefix() . 'contacts contacts', 'contacts.id = notes.contact_id', 'left');
        }
        $this->db->where('rel_id', $rel_id);
        $this->db->where('rel_type', $rel_type);
        $this->db->order_by('dateadded', 'desc');
        $limit ? $this->db->limit($limit) : '';

        $notes = $this->db->get()->result_array();

        return hooks()->apply_filters('get_notes', $notes, ['rel_id' => $rel_id, 'rel_type' => $rel_type]);
    }

    public function get_client_notes_by_types($client_id, array $types, $limit = '', $relations = [])
    {
        $this->db
            ->select('
                notes.*,
                staff.firstname,
                staff.lastname,
                contacts.phonenumber as contact_phone,
                contacts.email as contact_email,
                contacts.fullname as contact_name,
                (select CASE WHEN feedback.note_id IS NULL THEN "No" ELSE "Yes" END) as has_feedback
            ')
            ->from(db_prefix() . 'notes notes')
            ->join(db_prefix() . 'contacts contacts', 'contacts.id = notes.contact_id', 'left')
            ->join(db_prefix() . 'staff staff', 'staff.staffid = notes.addedfrom')
            ->join(db_prefix() . 'feedback_notes feedback', 'feedback.note_id = notes.id', 'left')
            ->where(
                [
                    'rel_id' => $client_id,
                    'rel_type' => 'customer'
                ]
            )
            ->group_by('notes.id')
            ->where_in('type', $types)
            ->order_by('dateadded', 'desc');

        $limit ? $this->db->limit($limit) : '';

        $notes = $this->db->get()->result_array();

        $ids = array_column($notes, 'id');
        $dataLastModified = [];
        $dataFeedback = [];
        if ($ids) {
            if (in_array('last_modified', $relations)) {
                foreach ($this->get_last_modified($ids) as $value) {
                    $dataLastModified[$value['id']] = $value;
                }
            }
            if (in_array('feedback', $relations)) {
                foreach ($this->get_feedback_notes($ids) as $value) {
                    $dataFeedback[$value['note_id']][] = $value;
                }
            }
        }

        $notes = array_map(function ($note) use ($dataLastModified, $dataFeedback) {
            $note['attachments'] = $this->get_current_note_attachments($note['id']);
            $note['action_text'] = get_text_of_select_options($note['action'], 'ACTION_NOTE');
            $note['follow_up_text'] = get_text_of_select_options($note['follow_up'], 'ACTION_NOTE');
            $note['potential_rate_text'] = get_text_of_select_options($note['potential_rate'], 'POTENTIAL_RATE_NOTE');
            $note['contact_channel_text'] = get_text_of_select_options($note['contact_channel'], 'CONTACT_CHANNEL_NOTE');
            $note['detailed_signal'] = explode(',', $note['detailed_signal']);
            $note['detailed_signal_text'] = array_map(function ($item) {
                return get_text_of_select_options($item, 'DETAILED_SIGNAL_NOTE');
            }, $note['detailed_signal']);

            $note['last_modified'] = $dataLastModified[$note['id']] ?? [];
            $note['feedback'] = $dataFeedback[$note['id']] ?? [];

            return $note;
        }, $notes);

        return $notes;
    }

    public function get_client_notes_by_types_success($client_id, array $types, $limit = '', $relations = [])
    {
        $this->db
            ->select('potential_rate')
            ->from(db_prefix() . 'notes notes')
            ->where(
                [
                    'rel_id' => $client_id,
                    'rel_type' => 'customer'
                ]
            )
            ->where_in('type', $types)
            ->order_by('dateadded', 'desc');

        $limit ? $this->db->limit($limit) : '';

        $notes = $this->db->get()->row();

        return $notes;
    }

    public function get_feedback_notes(array $ids)
    {
        $this->db
            ->select('feedback.*, CONCAT(staff.firstname, " ", staff.lastname) staff')
            ->from(db_prefix() . 'feedback_notes feedback')
            ->join(db_prefix() . 'staff staff', 'staff.staffid = feedback.added_by')
            ->where_in('note_id', $ids);

        return $this->db->get()->result_array();
    }

    public function get_history_notes($id)
    {
        $data = $this->db
            ->select('log.*, CONCAT(staff.firstname, " ", staff.lastname) staff')
            ->from(db_prefix() . 'activity_log log')
            ->join(db_prefix() . 'staff staff', 'staff.staffid = log.staffid')
            ->like('description', 'Update Customer Note')
            ->like('description', $id)
            ->where('data is NOT NULL', NULL, FALSE)
            ->order_by('id', 'desc')
            ->get()
            ->result_array();

        $data = array_map(function ($item) {
            $item['data'] = array_map(function ($item) {
                $item['action'] = get_text_of_select_options($item['action'], 'ACTION_NOTE');
                $item['follow_up'] = get_text_of_select_options($item['follow_up'], 'ACTION_NOTE');
                $item['potential_rate'] = get_text_of_select_options($item['potential_rate'], 'POTENTIAL_RATE_NOTE');
                $item['contact_channel'] = get_text_of_select_options($item['contact_channel'], 'CONTACT_CHANNEL_NOTE');
                $item['detailed_signal'] = array_map(function ($item) {
                    return get_text_of_select_options($item, 'DETAILED_SIGNAL_NOTE');
                }, explode(',', $item['detailed_signal']));
                $item['send_mail_this_reminder'] = $item['send_mail_this_reminder'] == 1 ? 'Yes' : 'No';
                return $item;
            }, json_decode($item['data'], true));
            return $item;
        }, $data);

        return $data;
    }
    
    public function get_history_contacts($id)
    {
        $data = $this->db
            ->select('log.*, CONCAT(staff.firstname, " ", staff.lastname) staff')
            ->from(db_prefix() . 'activity_log log')
            ->join(db_prefix() . 'staff staff', 'staff.staffid = log.staffid')
            ->where([
                'rel_id' => $id,
                'rel_type' => 'contact'
            ])
            ->where('data is NOT NULL', NULL, FALSE)
            ->order_by('id', 'desc')
            ->get()
            ->result_array();

        $data = array_map(function ($item) {
            $item['data'] = array_map(function ($item) {
                $item['sex'] = get_text_of_select_options($item['sex'], 'SEX');
                $item['status'] = get_text_of_select_options($item['status'], 'CONTACT_STATUS');
                $item['review_status'] = get_text_of_select_options($item['review_status'], 'CONTACT_REVIEW_STATUS');
                return $item;
            }, json_decode($item['data'], true));
            return $item;
        }, $data);

        return $data;
    }

    public function get_last_modified($ids)
    {
        $query = $this->db->select('notes.id, last_log.staffid, CONCAT(staff.firstname, " ", staff.lastname) fullname')
            ->from(db_prefix() . 'notes notes')
            ->join('(
                SELECT MAX(id) maxid, description, rel_id, staffid
                FROM ' . db_prefix() . 'activity_log log
                WHERE id IN (SELECT MAX(id) FROM ' . db_prefix() . 'activity_log WHERE rel_type = "note" AND description LIKE "%update%"
                GROUP BY rel_id) GROUP BY rel_id
            ) last_log', 'last_log.rel_id = notes.id')
            ->join(db_prefix() . 'staff staff', 'last_log.staffid = staff.staffid')
            ->where('rel_type', 'customer')
            ->where_in('last_log.rel_id', $ids)
            ->get();

        return $query->result_array();
    }

    public function get_note($id, $select = 'note.*')
    {
        $data = $this->db
            ->select($select)
            ->from(db_prefix() . 'notes note')
            ->join(db_prefix() . 'staff staff', 'staff.staffid = note.addedfrom')
            ->where('id', $id)
            ->get()
            ->first_row();

        if ($data) {
            if ($data->rel_type == 'customer') {
                $data->client = $this->clients_model->get($data->rel_id);
            }
            $data->detailed_signal = explode(',', $data->detailed_signal);
        }

        return $data;
    }

    public function add_feedback($data, $id)
    {
        $data_insert['note_id'] = $id;
        $data_insert['ticket_id'] = $data['ticket_id'];
        $data_insert['added_by'] = get_staff_user_id();
        $data_insert['description'] = $data['description'];
        $data_insert['created_at'] = date('Y-m-d H:i:s');
        $this->db->insert(db_prefix() . 'feedback_notes', $data_insert);

        log_activity('Add Feedback For Note (# ' . $id . ')', $data_insert['added_by'], $data['userid']);

        return true;
    }

    public function get_list_notes($ids = [], $where = [])
    {
        return $this->db
            ->select('notes.*, staff.*, (select CASE WHEN feedback.note_id IS NULL THEN "No" ELSE "Yes" END) as has_feedback')
            ->from(db_prefix() . 'notes notes')
            ->join(db_prefix() . 'staff staff', 'staff.staffid = notes.addedfrom')
            ->join(db_prefix() . 'feedback_notes feedback', 'feedback.note_id = notes.id', 'left')
            ->where_in($ids)
            ->where($where)
            ->group_by('notes.id')
            ->order_by('dateadded', 'desc')
            ->get()
            ->result_array();
    }

    public function add_note($data, $rel_type, $rel_id)
    {
        $data = $this->dataProcessingBeforeExecution($data, $rel_type);

        $data['dateadded']   = date('Y-m-d H:i:s');
        $data['addedfrom']   = get_staff_user_id();
        $data['rel_type']    = $rel_type;
        $data['rel_id']      = $rel_id;

        $data = hooks()->apply_filters('create_note_data', $data, $rel_type, $rel_id);

        $this->db->insert(db_prefix() . 'notes', $data);
        $insert_id = $this->db->insert_id();

        if ($insert_id) {
            handle_note_attachments($insert_id);
            log_activity('Create Customer Note (#' . $insert_id . ')', '', $rel_id, [], $insert_id, 'note');
            hooks()->do_action('note_created', $insert_id, $data);

            return $insert_id;
        }

        return false;
    }

    public function dataProcessingBeforeExecution2(array $data = [], $rel_type = '')
    {
        if ($rel_type == 'customer') {
            $data['reminder_calendar'] = to_sql_date($data['reminder_calendar'], true);
            $type = $this->input->post('type');
            if ($type == $this->CUSTOMER_TYPE_CONTACT_AGAIN) {
                unset(
                    $data['action'],
                    $data['follow_up'],
                    $data['detailed_signal'],
                    $data['potential_rate'],
                    $data['attachments'],
                    $data['link'],
                );
            } elseif ($type == $this->CUSTOMER_TYPE_CONTACT_SUCCESS) {
                unset(
                    $data['switch_reminders_to'],
                    $data['send_mail_this_reminder'],
                );
            } else {
                unset(
                    $data['contact_channel'],
                    $data['reminder_calendar'],
                    $data['switch_reminders_to'],
                    $data['send_mail_this_reminder'],
                    $data['action'],
                    $data['follow_up'],
                    $data['detailed_signal'],
                    $data['potential_rate'],
                    $data['attachments'],
                    $data['link'],
                );
            }
        } else {
            $data = [
                'description' => nl2br($data['description']),
            ];
        }

        unset(
            $data['id'],
            $data['rel_id'],
            $data['rel_type'],
            $data['addedfrom'],
            $data['dateadded'],
        );

        $data['updated_at'] = date('Y-m-d H:i:s');

        return $data;
    }
    public function dataProcessingBeforeExecution(array $data = [], $rel_type = '')
    {
        $new_data = [];

        if ($rel_type == 'customer') {
            $type = $data['type'];

            $new_data['type'] = $type;
            $new_data['contact_id'] = $data['contact_id'];
            $new_data['contact_channel'] = $data['contact_channel'];
            $new_data['reminder_calendar'] = to_sql_date($data['reminder_calendar'], true);
            if ($type == $this->CUSTOMER_TYPE_CONTACT_AGAIN) {
                $new_data = array_merge($new_data, [
                    'switch_reminders_to' => $data['switch_reminders_to'],
                    'send_mail_this_reminder' => $data['send_mail_this_reminder'],
                ]);
            } elseif ($type == $this->CUSTOMER_TYPE_CONTACT_SUCCESS) {
                $new_data = array_merge($new_data, [
                    'action' => $data['action'],
                    'follow_up' => $data['follow_up'],
                    'detailed_signal' => implode(',', $data['detailed_signal'] ?? []),
                    'potential_rate' => $data['potential_rate'],
                    'link' => $data['link']
                ]);
            } else {
                unset(
                    $new_data['contact_channel'],
                    $new_data['reminder_calendar'],
                );
            }
        }

        $new_data['description'] = nl2br($data['description']);
        $new_data['updated_at'] = date('Y-m-d H:i:s');

        return $new_data;
    }

    public function edit_note($data, $id)
    {
        $old_value = $this->db
            ->from(db_prefix() . 'notes')
            ->where('id', $id)
            ->get()
            ->first_row();
        $old_value->attachments = $this->get_current_note_attachments($old_value->id);

        $data = $this->dataProcessingBeforeExecution($data, $old_value->rel_type);

        hooks()->do_action('before_update_note', [
            'data' => $data,
            'id'   => $id,
        ]);

        $this->db
            ->where('id', $id)
            ->update(db_prefix() . 'notes', $data);

        $new_value = $this->db
            ->from(db_prefix() . 'notes')
            ->where('id', $id)
            ->get()
            ->first_row();

        if ($this->db->affected_rows() > 0) {
            handle_note_attachments($id);
            $new_value->attachments = $this->get_current_note_attachments($new_value->id);
            hooks()->do_action('note_updated', $id, $data);

            $clientid = $this->db->where('id', $id)->get(db_prefix() . 'notes')->first_row()->rel_id ?? '';
            log_activity(
                'Update Customer Note (#' . $id . ')',
                '',
                $clientid,
                [
                    'old_value' => $old_value,
                    'new_value' => $new_value,
                ],
                $id,
                'note'
            );

            return true;
        }

        return false;
    }

    public function get_activity_log($limit = 30)
    {
        $this->db->limit($limit);
        $this->db->order_by('date', 'desc');

        return $this->db->get(db_prefix() . 'activity_log')->result_array();
    }

    public function delete_note($note_id)
    {
        hooks()->do_action('before_delete_note', $note_id);

        $this->db->where('id', $note_id);
        $note = $this->db->get(db_prefix() . 'notes')->row();

        if (is_admin() || is_sales_admin()) {
            $this->db->where('id', $note_id);
            $this->db->delete(db_prefix() . 'notes');
            if ($this->db->affected_rows() > 0) {
                $this->delete_feedback($note_id);
                hooks()->do_action('note_deleted', $note_id, $note);

                return true;
            }
        }

        return false;
    }

    public function delete_feedback($note_id)
    {
        $data_ticket = $this->db->from(db_prefix() . 'feedback_notes')
            ->where('note_id', $note_id)
            ->get()
            ->result_array();

        // Delete ticket
        if (count($data_ticket)) {
            $ticket_ids = array_pluck($data_ticket, 'ticket_id');

            $this->db->where_in('ticketid', $ticket_ids);
            $this->db->delete(db_prefix() . 'tickets');
        }

        // Delete feedback
        $this->db->where('note_id', $note_id);
        $this->db->delete(db_prefix() . 'feedback_notes');

        if ($this->db->affected_rows() > 0) {
            return true;
        }

        return false;
    }

    public function delete_feedback_by_id($id)
    {
        $data_ticket = $this->db->from(db_prefix() . 'feedback_notes')
            ->where('id', $id)
            ->get()
            ->first_row();

        // Delete ticket
        if ($data_ticket) {
            $this->db->where('ticketid', $data_ticket->ticket_id)->delete(db_prefix() . 'tickets');
        }

        // Delete feedback
        $this->db->where('id', $id)->delete(db_prefix() . 'feedback_notes');

        if ($this->db->affected_rows() > 0) {
            return true;
        }

        return false;
    }

    /**
     * Get all reminders or 1 reminder if id is passed
     * @since Version 1.0.2
     * @param  mixed $id reminder id OPTIONAL
     * @return array or object
     */
    public function get_reminders($id = '')
    {
        $this->db->join(db_prefix() . 'staff', '' . db_prefix() . 'staff.staffid = ' . db_prefix() . 'reminders.staff', 'left');
        if (is_numeric($id)) {
            $this->db->where(db_prefix() . 'reminders.id', $id);

            return $this->db->get(db_prefix() . 'reminders')->row();
        } //is_numeric($id)
        $this->db->order_by('date', 'desc');

        return $this->db->get(db_prefix() . 'reminders')->result_array();
    }

    /**
     * Remove client reminder from database
     * @since Version 1.0.2
     * @param  mixed $id reminder id
     * @return boolean
     */
    public function delete_reminder($id)
    {
        $reminder = $this->get_reminders($id);
        if ($reminder->creator == get_staff_user_id() || is_admin()) {
            $this->db->where('id', $id);
            $this->db->delete(db_prefix() . 'reminders');
            if ($this->db->affected_rows() > 0) {
                log_activity('Reminder Deleted [' . ucfirst($reminder->rel_type) . 'ID: ' . $reminder->id . ' Description: ' . $reminder->description . ']');

                return true;
            } //$this->db->affected_rows() > 0
            return false;
        } //$reminder->creator == get_staff_user_id() || is_admin()
        return false;
    }

    public function get_tasks_distinct_assignees()
    {
        return $this->db->query('SELECT DISTINCT(' . db_prefix() . "task_assigned.staffid) as assigneeid, CONCAT(firstname,' ',lastname) as full_name FROM " . db_prefix() . 'task_assigned JOIN ' . db_prefix() . 'staff ON ' . db_prefix() . 'staff.staffid=' . db_prefix() . 'task_assigned.staffid')->result_array();
    }

    public function get_google_calendar_ids()
    {
        $is_admin = is_admin();
        $this->load->model('departments_model');
        $departments       = $this->departments_model->get();
        $staff_departments = $this->departments_model->get_staff_departments(false, true);
        $ids               = [];

        // Check departments google calendar ids
        foreach ($departments as $department) {
            if ($department['calendar_id'] == '') {
                continue;
            }
            if ($is_admin) {
                $ids[] = $department['calendar_id'];
            } else {
                if (in_array($department['departmentid'], $staff_departments)) {
                    $ids[] = $department['calendar_id'];
                }
            }
        }

        // Ok now check if main calendar is setup
        $main_id_calendar = get_option('google_calendar_main_calendar');
        if ($main_id_calendar != '') {
            $ids[] = $main_id_calendar;
        }

        return array_unique($ids);
    }

    /**
     * Get current user notifications
     * @param  boolean $read include and readed notifications
     * @return array
     */
    public function get_user_notifications($read = false)
    {
        $read     = $read == false ? 0 : 1;
        $total    = $this->notifications_limit;
        $staff_id = get_staff_user_id();

        $sql = 'SELECT COUNT(*) as total FROM ' . db_prefix() . 'notifications WHERE isread=' . $read . ' AND touserid=' . $staff_id;
        $sql .= ' UNION ALL ';
        $sql .= 'SELECT COUNT(*) as total FROM ' . db_prefix() . 'notifications WHERE isread_inline=' . $read . ' AND touserid=' . $staff_id;

        $res = $this->db->query($sql)->result();

        $total_unread        = $res[0]->total;
        $total_unread_inline = $res[1]->total;

        if ($total_unread > $total) {
            $total = ($total_unread - $total) + $total;
        } elseif ($total_unread_inline > $total) {
            $total = ($total_unread_inline - $total) + $total;
        }

        // In case user is not marking the notifications are read this process may be long because the script will always fetch the total from the not read notifications.
        // In this case we are limiting to 30
        $total = $total > 30 ? 30 : $total;

        $this->db->where('touserid', $staff_id);
        $this->db->limit($total);
        $this->db->order_by('date', 'desc');

        return $this->db->get(db_prefix() . 'notifications')->result_array();
    }

    /**
     * Set notification read when user open notification dropdown
     * @return boolean
     */
    public function set_notifications_read()
    {
        $this->db->where('touserid', get_staff_user_id());
        $this->db->update(db_prefix() . 'notifications', [
            'isread' => 1,
        ]);
        if ($this->db->affected_rows() > 0) {
            return true;
        }

        return false;
    }

    public function set_notification_read_inline($id)
    {
        $this->db->where('touserid', get_staff_user_id());
        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'notifications', [
            'isread_inline' => 1,
        ]);
    }

    public function set_desktop_notification_read($id)
    {
        $this->db->where('touserid', get_staff_user_id());
        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'notifications', [
            'isread'        => 1,
            'isread_inline' => 1,
        ]);
    }

    public function mark_all_notifications_as_read_inline()
    {
        $this->db->where('touserid', get_staff_user_id());
        $this->db->update(db_prefix() . 'notifications', [
            'isread_inline' => 1,
            'isread'        => 1,
        ]);
    }

    /**
     * Dismiss announcement
     * @param  array  $data  announcement data
     * @param  boolean $staff is staff or client
     * @return boolean
     */
    public function dismiss_announcement($id, $staff = true)
    {
        if ($staff == false) {
            $userid = get_contact_user_id();
        } //$staff == false
        else {
            $userid = get_staff_user_id();
        }
        $data['announcementid'] = $id;
        $data['userid']         = $userid;
        $data['staff']          = $staff;
        $this->db->insert(db_prefix() . 'dismissed_announcements', $data);

        return true;
    }

    /**
     * Perform search on top header
     * @since  Version 1.0.1
     * @param  string $q search
     * @return array    search results
     */
    public function perform_search($q)
    {
        $q = trim($q);
        $result                         = [];
        $limit                          = get_option('limit_top_search_bar_results_to');

        // Clients search
        $clients_search = $this->_search_clients($q, $limit);
        if (count($clients_search['result']) > 0) {
            $result[] = $clients_search;
        }

        $staff_search = $this->_search_staff($q, $limit);
        if (count($staff_search['result']) > 0) {
            $result[] = $staff_search;
        }

        $contacts_search = $this->_search_contacts($q, $limit);
        if (count($contacts_search['result']) > 0) {
            $result[] = $contacts_search;
        }

        $tickets_search = $this->_search_tickets($q, $limit);
        if (count($tickets_search['result']) > 0) {
            $result[] = $tickets_search;
        }

        $leads_search = $this->_search_leads($q, $limit);
        if (count($leads_search['result']) > 0) {
            $result[] = $leads_search;
        }

        $proposals_search = $this->_search_proposals($q, $limit);
        if (count($proposals_search['result']) > 0) {
            $result[] = $proposals_search;
        }

        $invoices_search = $this->_search_invoices($q, $limit);
        if (count($invoices_search['result']) > 0) {
            $result[] = $invoices_search;
        }

        $credit_notes_search = $this->_search_credit_notes($q, $limit);
        if (count($credit_notes_search['result']) > 0) {
            $result[] = $credit_notes_search;
        }

        $estimates_search = $this->_search_estimates($q, $limit);
        if (count($estimates_search['result']) > 0) {
            $result[] = $estimates_search;
        }

        $expenses_search = $this->_search_expenses($q, $limit);
        if (count($expenses_search['result']) > 0) {
            $result[] = $expenses_search;
        }

        $projects_search = $this->_search_projects($q, $limit);
        if (count($projects_search['result']) > 0) {
            $result[] = $projects_search;
        }

        $contracts_search = $this->_search_contracts($q, $limit);
        if (count($contracts_search['result']) > 0) {
            $result[] = $contracts_search;
        }

        $knowledge_base_search = $this->_search_knowledge_base($q, $limit);
        if (count($knowledge_base_search['result']) > 0) {
            $result[] = $knowledge_base_search;
        }

        // Tasks Search
        $tasks_search = $this->_search_tasks($q, $limit);
        if (count($tasks_search['result']) > 0) {
            $result[] = $tasks_search;
        }

        // Payments search
        $payments_search = $this->_search_payments($q, $limit);
        if (count($payments_search['result']) > 0) {
            $result[] = $payments_search;
        }

        // Custom fields only admins
        $custom_fields_search = $this->_search_custom_fields($q, $limit);
        if (count($custom_fields_search['result']) > 0) {
            $result[] = $custom_fields_search;
        }

        // Invoice Items Search
        $invoice_items_search = $this->_search_invoice_items($q, $limit);
        if (count($invoice_items_search['result']) > 0) {
            $result[] = $invoice_items_search;
        }

        // Estimate Items Search
        $estimate_items_search = $this->_search_estimate_items($q, $limit);
        if (count($estimate_items_search['result']) > 0) {
            $result[] = $estimate_items_search;
        }

        $result = hooks()->apply_filters('global_search_result_query', $result, $q, $limit);

        return $result;
    }

    public function _search_clients($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'clients',
            'search_heading' => _l('clients'),
        ];

        $baseQuery = new BoolQuery();
        $fields = [
            'company', 'business_name', 'phonenumber', 'short_name', 'vat', 'address'
        ];
        if (is_numeric($q) && es_is_integer($q)) {
            $fields = array_merge($fields, ['userid']);
        }

        $baseQuery->add(new MultiMatchQuery(
            $fields,
            $q,
            ['operator' => 'and']
        ), BoolQuery::SHOULD);

        $contactBoolQuery = new BoolQuery();
        $contactBoolQuery->add(new MultiMatchQuery(
            ['contacts.phonenumber', 'contacts.email', 'contacts.fullname'],
            $q,
            [
                'operator' => 'and'
            ]
        ));
        $contactBoolQuery->add(new MatchQuery('contacts.is_primary', 1));

        $baseQuery->add(new NestedQuery('contacts', $contactBoolQuery), BoolQuery::SHOULD);

        $clientIds = es_search(ES_INDEXES['clients'], $baseQuery);

        if (!count($clientIds)) {
            return $result;
        }
       
        // Clients
        $this->db->select(implode(',', prefixed_table_fields_array(db_prefix() . 'clients')) . ',' . get_sql_select_client_company() . ', ' . db_prefix() . 'clients.company as short_name');

        $this->db->join(db_prefix() . 'countries', db_prefix() . 'countries.country_id = ' . db_prefix() . 'clients.country', 'left');
        $this->db->join(db_prefix() . 'contacts', db_prefix() . 'contacts.userid = ' . db_prefix() . 'clients.userid AND is_primary = 1', 'left');
        $this->db->from(db_prefix() . 'clients');
        $this->db->where_in(db_prefix() . 'clients.userid', $clientIds);
        $this->db->limit($limit);
        $order = sprintf('FIELD(' . db_prefix() . 'clients.userid, %s)', implode(', ', $clientIds));
        $this->db->order_by($order);
        $result['result'] = $this->db->get()->result_array();
        

        return $result;
    }

    public function _search_proposals($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'proposals',
            'search_heading' => _l('proposals'),
        ];

        $has_permission_view_proposals     = has_permission('proposals', '', 'view');
        $has_permission_view_proposals_own = has_permission('proposals', '', 'view_own');

        if ($has_permission_view_proposals || $has_permission_view_proposals_own || get_option('allow_staff_view_proposals_assigned') == '1') {
            if (is_numeric($q)) {
                $q = trim($q);
                $q = ltrim($q, '0');
            } elseif (startsWith($q, get_option('proposal_number_prefix'))) {
                $q = strafter($q, get_option('proposal_number_prefix'));
                $q = trim($q);
                $q = ltrim($q, '0');
            }

            $noPermissionQuery = get_proposals_sql_where_staff(get_staff_user_id());

            // ES search
            $baseQuery = new BoolQuery();
            $fields = [
                'subject', 'content', 'proposal_to', 'zip', 'state', 'city', 'address', 'email', 'phone'
            ];
            if (is_numeric($q) && es_is_integer($q)) {
                $fields[] = 'id';
            }
            $baseQuery->add(new MultiMatchQuery(
                $fields,
                $q,
                ['operator' => 'and']
            ));
            $recordIds = es_search(ES_INDEXES['proposals'], $baseQuery);

            if (!count($recordIds)) {
                return $result;
            }

            // Proposals
            $this->db->select('*,' . db_prefix() . 'proposals.id as id');
            $this->db->from(db_prefix() . 'proposals');
            $this->db->join(db_prefix() . 'currencies', db_prefix() . 'currencies.id = ' . db_prefix() . 'proposals.currency');

            if (!$has_permission_view_proposals) {
                $this->db->where($noPermissionQuery);
            }


            $this->db->where_in(db_prefix() . 'proposals.id', $recordIds);
            $order = sprintf('FIELD(' . db_prefix() . 'proposals.id, %s)', implode(', ', $recordIds));
            $this->db->order_by($order);
            $this->db->order_by(db_prefix() . 'proposals.id', 'desc');
            if ($limit != 0) {
                $this->db->limit($limit);
            }
            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_leads($q, $limit = 0, $where = [])
    {
        $result = [
            'result'         => [],
            'type'           => 'leads',
            'search_heading' => _l('leads'),
        ];

        $has_permission_view = has_permission('leads', '', 'view');

        if (is_staff_member()) {
            $recordIds = [];
            // ES search
            $baseQuery = new BoolQuery();
            if (!startsWith($q, '#')) {
                $baseQuery->add(new MultiMatchQuery(
                    [
                        'name', 'title', 'company', 'zip', 'city', 'state', 'address', 'email', 'phonenumber'
                    ],
                    $q,
                    ['operator' => 'and']
                ));
                $recordIds = es_search(ES_INDEXES['leads'], $baseQuery);
            } else {
                $baseQuery->add(new MatchQuery('name', strafter($q, '#')));
                $baseQuery->add(new NestedQuery('taggables', new MatchQuery('taggables.rel_type', 'lead')));
                $searchRs = es_search(ES_INDEXES['tags'], $baseQuery, true);
                $taggables = !empty($searchRs[0]) ? ($searchRs[0]['taggables'] ?? []) : [];
                $recordIds = es_get_valid_taggables($taggables, 'lead');
            }

            if (!count($recordIds)) {
                return $result;
            }
            // Leads
            $this->db->select();
            $this->db->from(db_prefix() . 'leads');

            if (!$has_permission_view) {
                $this->db->where('(assigned = ' . get_staff_user_id() . ' OR addedfrom = ' . get_staff_user_id() . ' OR is_public=1)');
            }

            $this->db->where_in(db_prefix() . 'leads.id', $recordIds);


            $this->db->where($where);

            if ($limit != 0) {
                $this->db->limit($limit);
            }
            $order = sprintf('FIELD(' . db_prefix() . 'leads.id, %s)', implode(', ', $recordIds));
            $this->db->order_by($order);
            $this->db->order_by('name', 'ASC');
            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_tickets($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'tickets',
            'search_heading' => _l('support_tickets'),
        ];

        if (is_staff_member() || (!is_staff_member() && get_option('access_tickets_to_none_staff_members') == 1)) {
            $is_admin = is_admin();

            $where = '';
            if (!$is_admin && get_option('staff_access_only_assigned_departments') == 1) {
                $this->load->model('departments_model');
                $staff_deparments_ids = $this->departments_model->get_staff_departments(get_staff_user_id(), true);
                $departments_ids      = [];
                if (count($staff_deparments_ids) == 0) {
                    $departments = $this->departments_model->get();
                    foreach ($departments as $department) {
                        array_push($departments_ids, $department['departmentid']);
                    }
                } else {
                    $departments_ids = $staff_deparments_ids;
                }
                if (count($departments_ids) > 0) {
                    $where = 'department IN (SELECT departmentid FROM ' . db_prefix() . 'staff_departments WHERE departmentid IN (' . implode(',', $departments_ids) . ') AND staffid="' . get_staff_user_id() . '")';
                }
            }
            $recordIds = [];
            // ES search
            $baseQuery = new BoolQuery();
            if (!startsWith($q, '#')) {

                $fields = [
                    'subject', 'message',
                ];
                if (is_numeric($q) && es_is_integer($q)) {
                    $fields = array_merge($fields, ['ticketid']);
                }
                $baseQuery->add(new MultiMatchQuery(
                    $fields,
                    $q,
                    ['operator' => 'and']
                ), BoolQuery::SHOULD);
                $clientFields = [
                    'client.company', 'client.vat', 'client.phonenumber', 'client.city', 'client.state', 'client.address',
                ];
                $nestedClientQuery = new NestedQuery('client', new MultiMatchQuery(
                    $clientFields,
                    $q,
                    ['operator' => 'and']
                ));
                $baseQuery->add($nestedClientQuery, BoolQuery::SHOULD);
                $departmentFields = [
                    'department.name'
                ];
                $nestedDepartmentQuery = new NestedQuery('department', new MultiMatchQuery(
                    $departmentFields,
                    $q,
                    ['operator' => 'and']
                ));
                $baseQuery->add($nestedDepartmentQuery, BoolQuery::SHOULD);
                $contactBoolQuery = new BoolQuery();
                $contactBoolQuery->add(new MultiMatchQuery(
                    ['contact.phonenumber', 'contact.email', 'contact.fullname'],
                    $q,
                    [
                        'operator' => 'and'
                    ]
                ));
                $contactBoolQuery->add(new MatchQuery('contact.is_primary', 1));

                $baseQuery->add(new NestedQuery('contact', $contactBoolQuery), BoolQuery::SHOULD);
                $recordIds = es_search(ES_INDEXES['tickets'], $baseQuery);
            } else {
                $baseQuery->add(new MatchQuery('name', strafter($q, '#')));
                $baseQuery->add(new NestedQuery('taggables', new MatchQuery('taggables.rel_type', 'ticket')));
                $searchRs = es_search(ES_INDEXES['tags'], $baseQuery, true);
                $taggables = !empty($searchRs[0]) ? ($searchRs[0]['taggables'] ?? []) : [];
                $recordIds = es_get_valid_taggables($taggables, 'ticket');
            }

            if (!count($recordIds)) {
                return $result;
            }

            $this->db->select();
            $this->db->from(db_prefix() . 'tickets');
            $this->db->join(db_prefix() . 'departments', db_prefix() . 'departments.departmentid = ' . db_prefix() . 'tickets.department');
            $this->db->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . db_prefix() . 'tickets.userid', 'left');
            $this->db->join(db_prefix() . 'contacts', db_prefix() . 'contacts.id = ' . db_prefix() . 'tickets.contactid', 'left');

            $this->db->where_in(db_prefix() . 'tickets.ticketid', $recordIds);
            if ($where != '') {
                $this->db->where($where);
            }

            if ($limit != 0) {
                $this->db->limit($limit);
            }

            $order = sprintf('FIELD(' . db_prefix() . 'tickets.ticketid, %s)', implode(', ', $recordIds));
            $this->db->order_by($order);
            $this->db->order_by('ticketid', 'DESC');
            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_contacts($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'contacts',
            'search_heading' => _l('customer_contacts'),
        ];

        // ES search
        $baseQuery = new BoolQuery();
        $baseQuery->add(new MultiMatchQuery(
            [
                'fullname', 'email', 'phonenumber', 'title',
            ],
            $q,
            ['operator' => 'and']
        ), BoolQuery::SHOULD);
        $clientFields = [
            'client.company'
        ];
        $nestedClientQuery = new NestedQuery('client', new MultiMatchQuery(
            $clientFields,
            $q,
            ['operator' => 'and']
        ));
        $baseQuery->add($nestedClientQuery, BoolQuery::SHOULD);
        $recordIds = es_search(ES_INDEXES['contacts'], $baseQuery);

        if (!count($recordIds)) {
            return $result;
        }


        // Contacts
        $this->db->select(implode(',', prefixed_table_fields_array(db_prefix() . 'contacts')) . ', business_name');
        $this->db->from(db_prefix() . 'contacts');

        $this->db->join(db_prefix() . 'clients', '' . db_prefix() . 'clients.userid=' . db_prefix() . 'contacts.userid', 'left');
        $this->db->where_in(db_prefix() . 'contacts.id', $recordIds);

        if ($limit != 0) {
            $this->db->limit($limit);
        }

        $order = sprintf('FIELD(' . db_prefix() . 'contacts.id, %s)', implode(', ', $recordIds));
        $this->db->order_by($order);
        $this->db->order_by('firstname', 'ASC');
        $result['result'] = $this->db->get()->result_array();
        

        return $result;
    }

    public function _search_staff($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'staff',
            'search_heading' => _l('staff_members'),
        ];

        if (has_permission('staff', '', 'view')) {

            // ES search
            $baseQuery = new BoolQuery();
            $baseQuery->add(new MultiMatchQuery(
                [
                    'firstname', 'lastname', 'facebook', 'linkedin', 'phonenumber', 'email', 'skype'
                ],
                $q,
                ['operator' => 'and']
            ));
            $staffIds = es_search(ES_INDEXES['staff'], $baseQuery);

            if (!count($staffIds)) {
                return $result;
            }

            // Staff
            $this->db->select();
            $this->db->from(db_prefix() . 'staff');
            $this->db->where_in(db_prefix() . 'staff.staffid', $staffIds);

            if ($limit != 0) {
                $this->db->limit($limit);
            }
            $order = sprintf('FIELD(' . db_prefix() . 'staff.staffid, %s)', implode(', ', $staffIds));
            $this->db->order_by($order);
            $this->db->order_by('firstname', 'ASC');
            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_contracts($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'contracts',
            'search_heading' => _l('contracts'),
        ];

        $has_permission_view_contracts = has_permission('contracts', '', 'view');
        if ($has_permission_view_contracts || has_permission('contracts', '', 'view_own')) {

            // ES search
            $baseQuery = new BoolQuery();
            $baseQuery->add(new MultiMatchQuery(
                [
                    'description', 'subject',
                ],
                $q,
                ['operator' => 'and']
            ));
            $recordIds = es_search(ES_INDEXES['contracts'], $baseQuery);

            if (!count($recordIds)) {
                return $result;
            }

            // Contracts
            $this->db->select();
            $this->db->from(db_prefix() . 'contracts');
            if (!$has_permission_view_contracts) {
                $this->db->where(db_prefix() . 'contracts.addedfrom', get_staff_user_id());
            }
            $this->db->where_in(db_prefix() . 'contracts.id', $recordIds);

            if ($limit != 0) {
                $this->db->limit($limit);
            }
            $order = sprintf('FIELD(' . db_prefix() . 'contracts.id, %s)', implode(', ', $recordIds));
            $this->db->order_by($order);
            $this->db->order_by('subject', 'ASC');
            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_knowledge_base($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'knowledge_base_articles',
            'search_heading' => _l('kb_string'),
        ];

        if (has_permission('knowledge_base', '', 'view')) {
            // ES search
            $baseQuery = new BoolQuery();
            $baseQuery->add(new MultiMatchQuery(
                [
                    'subject', 'description', 'slug'
                ],
                $q,
                ['operator' => 'and']
            ));
            $recordIds = es_search(ES_INDEXES['knowledge_base'], $baseQuery);
            if (!count($recordIds)) {
                return $result;
            }
            $order = sprintf('FIELD(' . db_prefix() . 'knowledge_base.articleid, %s)', implode(', ', $recordIds));
            // Knowledge base articles
            $this->db->select()->from(db_prefix() . 'knowledge_base')
                ->where_in(db_prefix() . 'knowledge_base.articleid', $recordIds)
                ->limit($limit)
                ->order_by($order)
                ->order_by('subject', 'ASC');
            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_tasks($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'tasks',
            'search_heading' => _l('tasks'),
        ];

        $recordIds = [];
        // ES search
        $baseQuery = new BoolQuery();
        if (!startsWith($q, '#')) {
            $baseQuery->add(new MultiMatchQuery(
                [
                    'name', 'description',
                ],
                $q,
                ['operator' => 'and']
            ));
            $recordIds = es_search(ES_INDEXES['tasks'], $baseQuery);
        } else {
            $baseQuery->add(new MatchQuery('name', strafter($q, '#')));
            $baseQuery->add(new NestedQuery('taggables', new MatchQuery('taggables.rel_type', 'task')));
            $searchRs = es_search(ES_INDEXES['tags'], $baseQuery, true);
            $taggables = !empty($searchRs[0]) ? ($searchRs[0]['taggables'] ?? []) : [];
            $recordIds = es_get_valid_taggables($taggables, 'task');
        }

        if (!count($recordIds)) {
            return $result;
        }

        // Tasks Search
        $tasks = has_permission('tasks', '', 'view');
        // Staff tasks
        $this->db->select();
        $this->db->from(db_prefix() . 'tasks');
        if (!is_admin()) {
            if (!$tasks) {
                $where = '(id IN (SELECT taskid FROM ' . db_prefix() . 'task_assigned WHERE staffid = ' . get_staff_user_id() . ') OR id IN (SELECT taskid FROM ' . db_prefix() . 'task_followers WHERE staffid = ' . get_staff_user_id() . ') OR (addedfrom=' . get_staff_user_id() . ' AND is_added_from_contact=0) ';
                if (get_option('show_all_tasks_for_project_member') == 1) {
                    $where .= ' OR (rel_type="project" AND rel_id IN (SELECT project_id FROM ' . db_prefix() . 'project_members WHERE staff_id=' . get_staff_user_id() . '))';
                }
                $where .= ' OR is_public = 1)';
                $this->db->where($where);
            } //!$tasks
        } //!$is_admin
        $this->db->where_in(db_prefix() . 'tasks.id', $recordIds);

        $this->db->limit($limit);
        $order = sprintf('FIELD(' . db_prefix() . 'tasks.id, %s)', implode(', ', $recordIds));
        $this->db->order_by($order);
        $this->db->order_by('name', 'ASC');

        $result['result'] = $this->db->get()->result_array();

        return $result;
    }

    public function _search_payments($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'invoice_payment_records',
            'search_heading' => _l('payments'),
        ];

        // Payments search
        $has_permission_view_payments     = has_permission('payments', '', 'view');
        $has_permission_view_invoices_own = has_permission('invoices', '', 'view_own');

        if (has_permission('payments', '', 'view') || $has_permission_view_invoices_own || get_option('allow_staff_view_invoices_assigned') == '1') {
            // ES search
            $baseQuery = new BoolQuery();
            if (!startsWith($q, get_option('invoice_prefix'))) {
                $fields = [
                    'paymentmode', 'note',
                ];
                if (is_numeric($q) && es_is_integer($q)) {
                    $fields = array_merge($fields, ['id']);
                }
                $baseQuery->add(new MultiMatchQuery(
                    $fields,
                    $q,
                    ['operator' => 'and']
                ), BoolQuery::SHOULD);
                $paymentModeFields = [
                    'payment_mode.name',
                ];
                $nestedPaymentModeQuery = new NestedQuery('payment_mode', new MultiMatchQuery(
                    $paymentModeFields,
                    $q,
                    ['operator' => 'and']
                ));
                $baseQuery->add($nestedPaymentModeQuery, BoolQuery::SHOULD);
                if (is_numeric($q) && es_is_integer($q)) {
                    $invoiceFields = [
                        'invoice.number'
                    ];
                    $nestedInvoiceQuery = new NestedQuery('invoice', new MultiMatchQuery(
                        $invoiceFields,
                        $q,
                        ['operator' => 'and']
                    ));
                    $baseQuery->add($nestedInvoiceQuery, BoolQuery::SHOULD);
                }
            } else {
                $baseQuery->add(new WildcardQuery('invoice_number', $q . '*'));
            }
            $recordIds = es_search(ES_INDEXES['invoicepaymentrecords'], $baseQuery);

            if (!count($recordIds)) {
                return $result;
            }
            $noPermissionQuery = get_invoices_where_sql_for_staff(get_staff_user_id());

            // Invoice payment records
            $this->db->select('*,' . db_prefix() . 'invoicepaymentrecords.id as paymentid');
            $this->db->from(db_prefix() . 'invoicepaymentrecords');
            $this->db->join(db_prefix() . 'payment_modes', '' . db_prefix() . 'invoicepaymentrecords.paymentmode = ' . db_prefix() . 'payment_modes.id', 'LEFT');
            $this->db->join(db_prefix() . 'invoices', '' . db_prefix() . 'invoices.id = ' . db_prefix() . 'invoicepaymentrecords.invoiceid');

            if (!$has_permission_view_payments) {
                $this->db->where('invoiceid IN (select id from ' . db_prefix() . 'invoices where ' . $noPermissionQuery . ')');
            }
            $this->db->where_in(db_prefix() . 'invoicepaymentrecords.id', $recordIds);

            $order = sprintf('FIELD(' . db_prefix() . 'invoicepaymentrecords.id, %s)', implode(', ', $recordIds));
            $this->db->order_by($order);
            $this->db->order_by(db_prefix() . 'invoicepaymentrecords.date', 'ASC');
            $this->db->limit($limit);
            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_custom_fields($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'custom_fields',
            'search_heading' => _l('custom_fields'),
        ];

        if (is_admin()) {
            // ES search
            $baseQuery = new BoolQuery();
            $baseQuery->add(new MultiMatchQuery(
                ['value'],
                $q,
                ['operator' => 'and']
            ));

            $recordIds = es_search(ES_INDEXES['customfieldsvalues'], $baseQuery);

            if (!count($recordIds)) {
                return $result;
            }

            $order = sprintf('FIELD(' . db_prefix() . 'customfieldsvalues.id, %s)', implode(', ', $recordIds));
            $this->db->select()
                ->from(db_prefix() . 'customfieldsvalues')
                ->where_in(db_prefix() . 'customfieldsvalues.id', $recordIds)
                ->order_by($order)
                ->limit($limit);

            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_invoice_items($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'invoice_items',
            'search_heading' => _l('invoice_items'),
        ];

        // Invoice Items Search
        $has_permission_view_invoices       = has_permission('invoices', '', 'view');
        $has_permission_view_invoices_own   = has_permission('invoices', '', 'view_own');
        $allow_staff_view_invoices_assigned = get_option('allow_staff_view_invoices_assigned');

        if ($has_permission_view_invoices || $has_permission_view_invoices_own || $allow_staff_view_invoices_assigned == '1') {
            $noPermissionQuery = get_invoices_where_sql_for_staff(get_staff_user_id());

            // ES search
            $baseQuery = new BoolQuery();
            $baseQuery->add(new MultiMatchQuery(
                ['description', 'long_description'],
                $q,
                ['operator' => 'and']
            ));

            $recordIds = es_search(ES_INDEXES['itemable'], $baseQuery);

            if (!count($recordIds)) {
                return $result;
            }

            $this->db->select()->from(db_prefix() . 'itemable');
            $this->db->where('rel_type', 'invoice');

            if (!$has_permission_view_invoices) {
                $this->db->where('rel_id IN (select id from ' . db_prefix() . 'invoices where ' . $noPermissionQuery . ')');
            }
            $this->db->where_in(db_prefix() . 'itemable.id', $recordIds);
            $order = sprintf('FIELD(' . db_prefix() . 'itemable.id, %s)', implode(', ', $recordIds));
            $this->db->order_by($order);
            $this->db->order_by('description', 'ASC');
            $this->db->limit($limit);
            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_estimate_items($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'estimate_items',
            'search_heading' => _l('estimate_items'),
        ];

        // Estimate Items Search
        $has_permission_view_estimates       = has_permission('estimates', '', 'view');
        $has_permission_view_estimates_own   = has_permission('estimates', '', 'view_own');
        $allow_staff_view_estimates_assigned = get_option('allow_staff_view_estimates_assigned');
        if ($has_permission_view_estimates || $has_permission_view_estimates_own || $allow_staff_view_estimates_assigned) {
            $noPermissionQuery = get_estimates_where_sql_for_staff(get_staff_user_id());

            // ES search
            $baseQuery = new BoolQuery();
            $baseQuery->add(new MultiMatchQuery(
                ['description', 'long_description'],
                $q,
                ['operator' => 'and']
            ));

            $recordIds = es_search(ES_INDEXES['itemable'], $baseQuery);

            if (!count($recordIds)) {
                return $result;
            }

            $this->db->select()->from(db_prefix() . 'itemable');
            $this->db->where('rel_type', 'estimate');

            if (!$has_permission_view_estimates) {
                $this->db->where('rel_id IN (select id from ' . db_prefix() . 'estimates where ' . $noPermissionQuery . ')');
            }

            $this->db->where_in(db_prefix() . 'itemable.id', $recordIds);
            $order = sprintf('FIELD(' . db_prefix() . 'itemable.id, %s)', implode(', ', $recordIds));
            $this->db->order_by($order);
            $this->db->order_by('description', 'ASC');
            $this->db->limit($limit);
            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_projects($q, $limit = 0, $where = false)
    {
        $result = [
            'result'         => [],
            'type'           => 'projects',
            'search_heading' => _l('projects'),
        ];

        $recordIds = [];
        // ES search
        $baseQuery = new BoolQuery();
        if (!startsWith($q, '#')) {
            $baseQuery->add(new MultiMatchQuery(
                [
                    'description', 'name',
                ],
                $q,
                ['operator' => 'and']
            ), BoolQuery::SHOULD);
            $clientFields = [
                'client.company', 'client.vat', 'client.phonenumber', 'client.city', 'client.zip', 'client.address', 'client.state'
            ];
            $nestedClientQuery = new NestedQuery('client', new MultiMatchQuery(
                $clientFields,
                $q,
                ['operator' => 'and']
            ));
            $baseQuery->add($nestedClientQuery, BoolQuery::SHOULD);
            $recordIds = es_search(ES_INDEXES['projects'], $baseQuery);
        } else {
            $baseQuery->add(new MatchQuery('name', strafter($q, '#')));
            $baseQuery->add(new NestedQuery('taggables', new MatchQuery('taggables.rel_type', 'project')));
            $searchRs = es_search(ES_INDEXES['tags'], $baseQuery, true);
            $taggables = !empty($searchRs[0]) ? ($searchRs[0]['taggables'] ?? []) : [];
            $recordIds = es_get_valid_taggables($taggables, 'project');
        }

        if (!count($recordIds)) {
            return $result;
        }

        $projects = has_permission('projects', '', 'view');
        // Projects
        $this->db->select();
        $this->db->from(db_prefix() . 'projects');
        $this->db->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . db_prefix() . 'projects.clientid');
        if (!$projects) {
            $this->db->where(db_prefix() . 'projects.id IN (SELECT project_id FROM ' . db_prefix() . 'project_members WHERE staff_id=' . get_staff_user_id() . ')');
        }
        if ($where != false) {
            $this->db->where($where);
        }
        $this->db->where_in(db_prefix() . 'projects.id', $recordIds);

        if ($limit != 0) {
            $this->db->limit($limit);
        }

        $order = sprintf('FIELD(' . db_prefix() . 'projects.id, %s)', implode(', ', $recordIds));
        $this->db->order_by($order);
        $this->db->order_by('name', 'ASC');
        $result['result'] = $this->db->get()->result_array();

        return $result;
    }

    public function _search_invoices($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'invoices',
            'search_heading' => _l('invoices'),
        ];
        $has_permission_view_invoices     = has_permission('invoices', '', 'view');
        $has_permission_view_invoices_own = has_permission('invoices', '', 'view_own');

        if ($has_permission_view_invoices || $has_permission_view_invoices_own || get_option('allow_staff_view_invoices_assigned') == '1') {

            $recordIds = [];
            // ES search
            $baseQuery = new BoolQuery();
            if (!startsWith($q, '#')) {
                if (!startsWith($q, get_option('invoice_prefix'))) {
                    $fields = [
                        'clientnote', 'adminnote', 'billing_street', 'billing_city', 'billing_state', 'billing_zip', 'shipping_street', 'shipping_city', 'shipping_state', 'shipping_zip', 'subject', 'content', 'proposal_to', 'zip', 'state', 'city', 'address', 'email', 'phone',
                    ];
                    if (is_numeric($q) && es_is_integer($q)) {
                        $fields = array_merge($fields, ['id', 'number']);
                    }
                    $baseQuery->add(new MultiMatchQuery(
                        $fields,
                        $q,
                        ['operator' => 'and']
                    ), BoolQuery::SHOULD);

                    $clientFields = [
                        'client.company', 'client.vat', 'client.phonenumber', 'client.city', 'client.state', 'client.zip', 'client.address', 'client.billing_street', 'client.billing_city', 'client.billing_state', 'client.billing_zip', 'client.shipping_street', 'client.shipping_city', 'client.shipping_state', 'client.shipping_zip',
                    ];
                    $nestedClientQuery = new NestedQuery('client', new MultiMatchQuery(
                        $clientFields,
                        $q,
                        ['operator' => 'and']
                    ));
                    $baseQuery->add($nestedClientQuery, BoolQuery::SHOULD);

                    $contactBoolQuery = new BoolQuery();
                    $contactBoolQuery->add(new MultiMatchQuery(
                        ['client.contacts.phonenumber', 'client.contacts.email', 'client.contacts.fullname'],
                        $q,
                        [
                            'operator' => 'and'
                        ]
                    ));
                    $contactBoolQuery->add(new MatchQuery('client.contacts.is_primary', 1));
                    $baseQuery->add(new NestedQuery('client.contacts', $contactBoolQuery), BoolQuery::SHOULD);
                } else {
                    $baseQuery->add(new WildcardQuery('invoice_number', $q . '*'));
                }
                $recordIds = es_search(ES_INDEXES['invoices'], $baseQuery);
            } else {
                $baseQuery->add(new MatchQuery('name', strafter($q, '#')));
                $baseQuery->add(new NestedQuery('taggables', new MatchQuery('taggables.rel_type', 'invoice')));
                $searchRs = es_search(ES_INDEXES['tags'], $baseQuery, true);
                $taggables = !empty($searchRs[0]) ? ($searchRs[0]['taggables'] ?? []) : [];
                $recordIds = es_get_valid_taggables($taggables, 'invoice');
            }

            if (!count($recordIds)) {
                return $result;
            }


            $invoice_fields    = prefixed_table_fields_array(db_prefix() . 'invoices');
            $clients_fields    = prefixed_table_fields_array(db_prefix() . 'clients');
            $noPermissionQuery = get_invoices_where_sql_for_staff(get_staff_user_id());
            // Invoices
            $this->db->select(implode(',', $invoice_fields) . ',' . implode(',', $clients_fields) . ',' . db_prefix() . 'invoices.id as invoiceid,' . get_sql_select_client_company());
            $this->db->from(db_prefix() . 'invoices');
            $this->db->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . db_prefix() . 'invoices.clientid', 'left');
            $this->db->join(db_prefix() . 'currencies', db_prefix() . 'currencies.id = ' . db_prefix() . 'invoices.currency');
            $this->db->join(db_prefix() . 'contacts', db_prefix() . 'contacts.userid = ' . db_prefix() . 'clients.userid AND is_primary = 1', 'left');

            if (!$has_permission_view_invoices) {
                $this->db->where($noPermissionQuery);
            }
            $this->db->where_in(db_prefix() . 'invoices.id', $recordIds);

            $order = sprintf('FIELD(' . db_prefix() . 'invoices.id, %s)', implode(', ', $recordIds));
            $this->db->order_by($order);
            $this->db->order_by('number,YEAR(date)', 'desc');
            if ($limit != 0) {
                $this->db->limit($limit);
            }

            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_credit_notes($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'credit_note',
            'search_heading' => _l('credit_notes'),
        ];

        $has_permission_view_credit_notes     = has_permission('credit_notes', '', 'view');
        $has_permission_view_credit_notes_own = has_permission('credit_notes', '', 'view_own');

        if ($has_permission_view_credit_notes || $has_permission_view_credit_notes_own) {
            if (is_numeric($q)) {
                $q = trim($q);
                $q = ltrim($q, '0');
            } elseif (startsWith($q, get_option('credit_note_prefix'))) {
                $q = strafter($q, get_option('credit_note_prefix'));
                $q = trim($q);
                $q = ltrim($q, '0');
            }

            // ES search
            $baseQuery = new BoolQuery();
            $fields = [
                'clientnote', 'adminnote', 'billing_street', 'billing_city', 'billing_state', 'billing_zip', 'shipping_street', 'shipping_city', 'shipping_state', 'shipping_zip',
            ];
            if (is_numeric($q) && es_is_integer($q)) {
                $fields = array_merge($fields, ['number']);
            }
            $baseQuery->add(new MultiMatchQuery(
                $fields,
                $q,
                ['operator' => 'and']
            ), BoolQuery::SHOULD);
            $clientFields = [
                'client.company', 'client.vat', 'client.phonenumber', 'client.city', 'client.state', 'client.zip', 'client.address', 'client.billing_street', 'client.billing_city', 'client.billing_state', 'client.billing_zip', 'client.shipping_street', 'client.shipping_city', 'client.shipping_state', 'client.shipping_zip',
            ];
            $nestedClientQuery = new NestedQuery('client', new MultiMatchQuery(
                $clientFields,
                $q,
                ['operator' => 'and']
            ));
            $baseQuery->add($nestedClientQuery, BoolQuery::SHOULD);
            $subBoolQuery = new BoolQuery();
            $subBoolQuery->add(new MatchQuery('client.contacts.fullname', $q));
            $subBoolQuery->add(new MatchQuery('client.contacts.is_primary', 1));
            $baseQuery->add(new NestedQuery('client.contacts', $subBoolQuery), BoolQuery::SHOULD);
            $recordIds = es_search(ES_INDEXES['creditnotes'], $baseQuery);

            if (!count($recordIds)) {
                return $result;
            }

            $credit_note_fields = prefixed_table_fields_array(db_prefix() . 'creditnotes');
            $clients_fields     = prefixed_table_fields_array(db_prefix() . 'clients');
            // Invoices
            $this->db->select(implode(',', $credit_note_fields) . ',' . implode(',', $clients_fields) . ',' . db_prefix() . 'creditnotes.id as credit_note_id,' . get_sql_select_client_company());
            $this->db->from(db_prefix() . 'creditnotes');
            $this->db->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . db_prefix() . 'creditnotes.clientid', 'left');
            $this->db->join(db_prefix() . 'currencies', db_prefix() . 'currencies.id = ' . db_prefix() . 'creditnotes.currency');
            $this->db->join(db_prefix() . 'contacts', db_prefix() . 'contacts.userid = ' . db_prefix() . 'clients.userid AND is_primary = 1', 'left');

            if (!$has_permission_view_credit_notes) {
                $this->db->where(db_prefix() . 'creditnotes.addedfrom', get_staff_user_id());
            }
            $this->db->where_in(db_prefix() . 'creditnotes.id', $recordIds);
            $order = sprintf('FIELD(' . db_prefix() . 'creditnotes.id, %s)', implode(', ', $recordIds));
            $this->db->order_by($order);
            $this->db->order_by('number', 'desc');
            if ($limit != 0) {
                $this->db->limit($limit);
            }

            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_estimates($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'estimates',
            'search_heading' => _l('estimates'),
        ];

        $has_permission_view_estimates     = has_permission('estimates', '', 'view');
        $has_permission_view_estimates_own = has_permission('estimates', '', 'view_own');

        if ($has_permission_view_estimates || $has_permission_view_estimates_own || get_option('allow_staff_view_estimates_assigned') == '1') {
            // Estimates
            $estimates_fields  = prefixed_table_fields_array(db_prefix() . 'estimates');
            $clients_fields    = prefixed_table_fields_array(db_prefix() . 'clients');
            $noPermissionQuery = get_estimates_where_sql_for_staff(get_staff_user_id());

            // ES search
            $baseQuery = new BoolQuery();
            if (!startsWith($q, get_option('estimate_prefix'))) {
                $fields = [
                    'clientnote', 'adminnote', 'billing_street', 'billing_city', 'billing_state', 'billing_zip', 'shipping_street', 'shipping_city', 'shipping_state', 'shipping_zip',
                ];
                if (is_numeric($q) && es_is_integer($q)) {
                    $fields = array_merge($fields, ['number']);
                }
                $baseQuery->add(new MultiMatchQuery(
                    $fields,
                    $q,
                    ['operator' => 'and']
                ), BoolQuery::SHOULD);
                $clientFields = [
                    'client.company', 'client.address', 'client.vat', 'client.phonenumber', 'client.city', 'client.state', 'client.zip', 'client.billing_street', 'client.billing_city', 'client.billing_state', 'client.billing_zip', 'client.shipping_street', 'client.shipping_city', 'client.shipping_state', 'client.shipping_zip',
                ];
                $nestedClientQuery = new NestedQuery('client', new MultiMatchQuery(
                    $clientFields,
                    $q,
                    ['operator' => 'and']
                ));
                $baseQuery->add($nestedClientQuery, BoolQuery::SHOULD);
            } else {
                $baseQuery->add(new WildcardQuery('estimate_number', $q . '*'));
            }
            
            $recordIds = es_search(ES_INDEXES['estimates'], $baseQuery);

            if (!count($recordIds)) {
                return $result;
            }

            $this->db->select(implode(',', $estimates_fields) . ',' . implode(',', $clients_fields) . ',' . db_prefix() . 'estimates.id as estimateid,' . get_sql_select_client_company());
            $this->db->from(db_prefix() . 'estimates');
            $this->db->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . db_prefix() . 'estimates.clientid', 'left');
            $this->db->join(db_prefix() . 'currencies', db_prefix() . 'currencies.id = ' . db_prefix() . 'estimates.currency');
            $this->db->join(db_prefix() . 'contacts', db_prefix() . 'contacts.userid = ' . db_prefix() . 'clients.userid AND is_primary = 1', 'left');

            if (!$has_permission_view_estimates) {
                $this->db->where($noPermissionQuery);
            }
            $this->db->where_in(db_prefix() . 'estimates.id', $recordIds);
            $order = sprintf('FIELD(' . db_prefix() . 'estimates.id, %s)', implode(', ', $recordIds));
            $this->db->order_by($order);
            $this->db->order_by('number,YEAR(date)', 'desc');
            if ($limit != 0) {
                $this->db->limit($limit);
            }
            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }

    public function _search_expenses($q, $limit = 0)
    {
        $result = [
            'result'         => [],
            'type'           => 'expenses',
            'search_heading' => _l('expenses'),
        ];

        $has_permission_expenses_view     = has_permission('expenses', '', 'view');
        $has_permission_expenses_view_own = has_permission('expenses', '', 'view_own');

        if ($has_permission_expenses_view || $has_permission_expenses_view_own) {
            // ES search
            $baseQuery = new BoolQuery();
            $baseQuery->add(new MultiMatchQuery(
                [
                    'paymentmode', 'note', 'expense_name',
                ],
                $q,
                [
                    'type' => 'best_fields',
                    'operator' => 'and'
                ]
            ), BoolQuery::SHOULD);
            $clientFields = [
                'client.company', 'client.vat', 'client.phonenumber', 'client.city', 'client.zip', 'client.address', 'client.state'
            ];
            $nestedClientQuery = new NestedQuery('client', new MultiMatchQuery(
                $clientFields,
                $q,
                [
                    'type' => 'best_fields',
                    'operator' => 'and'
                ]
            ));
            $baseQuery->add($nestedClientQuery, BoolQuery::SHOULD);
            $expensesCategoryFields = [
                'expenses_category.name',
            ];
            $nestedExpensesCategoryQuery = new NestedQuery('expenses_category', new MultiMatchQuery(
                $expensesCategoryFields,
                $q,
                [
                    'type' => 'best_fields',
                    'operator' => 'and'
                ]
            ));
            $baseQuery->add($nestedExpensesCategoryQuery, BoolQuery::SHOULD);
            $paymentModeFields = [
                'payment_mode.name',
            ];
            $nestedPaymentModeQuery = new NestedQuery('payment_mode', new MultiMatchQuery(
                $paymentModeFields,
                $q,
                [
                    'type' => 'best_fields',
                    'operator' => 'and'
                ]
            ));
            $baseQuery->add($nestedPaymentModeQuery, BoolQuery::SHOULD);
            $recordIds = es_search(ES_INDEXES['expenses'], $baseQuery);

            if (!count($recordIds)) {
                return $result;
            }

            // Expenses
            $this->db->select('*,' . db_prefix() . 'expenses.amount as amount,' . db_prefix() . 'expenses_categories.name as category_name,' . db_prefix() . 'payment_modes.name as payment_mode_name,' . db_prefix() . 'taxes.name as tax_name, ' . db_prefix() . 'expenses.id as expenseid,' . db_prefix() . 'currencies.name as currency_name');
            $this->db->from(db_prefix() . 'expenses');
            $this->db->join(db_prefix() . 'clients', db_prefix() . 'clients.userid = ' . db_prefix() . 'expenses.clientid', 'left');
            $this->db->join(db_prefix() . 'payment_modes', db_prefix() . 'payment_modes.id = ' . db_prefix() . 'expenses.paymentmode', 'left');
            $this->db->join(db_prefix() . 'taxes', db_prefix() . 'taxes.id = ' . db_prefix() . 'expenses.tax', 'left');
            $this->db->join(db_prefix() . 'expenses_categories', db_prefix() . 'expenses_categories.id = ' . db_prefix() . 'expenses.category');
            $this->db->join(db_prefix() . 'currencies', '' . db_prefix() . 'currencies.id = ' . db_prefix() . 'expenses.currency', 'left');
            if (!$has_permission_expenses_view) {
                $this->db->where(db_prefix() . 'expenses.addedfrom', get_staff_user_id());
            }

            $this->db->where_in(db_prefix() . 'expenses.id', $recordIds);

            if ($limit != 0) {
                $this->db->limit($limit);
            }
            $order = sprintf('FIELD(' . db_prefix() . 'expenses.id, %s)', implode(', ', $recordIds));
            $this->db->order_by($order);
            $this->db->order_by('date', 'DESC');
            $result['result'] = $this->db->get()->result_array();
        }

        return $result;
    }
}

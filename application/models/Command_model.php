<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Command_model extends App_Model
{
    public function mapClientCountries()
    {
        $nationalities = get_ams_taxonomies(['nationalities'])['nationalities'] ?? [];
        $cols = array_column($nationalities, 'text');
        $records = [];
        $hasNextRecords = false;
        $nextId = 0;
        do {
            $records = $this->db
                ->select('cl.userid, cl.country, co.short_name')
                ->from(db_prefix() . 'clients cl')
                ->join(db_prefix() . 'countries as co', 'cl.country = co.country_id')
                ->where('cl.userid > ', $nextId)
                ->where('cl.country > ', 0)
                ->order_by('cl.userid', 'ASC')
                ->limit(50)
                ->get()
                ->result_array();

            if (isset($records)) {
                $insertRecords = [];
                foreach ($records as $record) {
                    $idx = array_search($record['short_name'], $cols);
                    if ($idx >= 0) {
                        $insertRecords[] = [
                            'client_id' => intval($record['userid']),
                            'national_id' => intval($nationalities[$idx]['id'])
                        ];
                    }
                    $nextId = $record['userid'];
                }
                if (count($insertRecords)) {
                    $this->db->insert_batch(db_prefix() . 'client_nationalities', $insertRecords);
                }
            }

            $hasNextRecords = (count($records) >= 50);
        } while ($hasNextRecords);
    }

    public function mapClientAffiliates()
    {
        $customFieldKey = 'company_affliate';
        $fieldto = 'customers';
        $records = [];
        $hasNextRecords = false;
        $nextId = 0;
        do {
            $records = $this->db
                ->select('cfv.relid as relid, cfv.`value` as `value`')
                ->from(db_prefix() . 'customfieldsvalues cfv')
                ->join(db_prefix() . 'customfields cf', 'cfv.fieldid = cf.id')
                ->where('cfv.id > ', $nextId)
                ->where('cf.`slug`', $customFieldKey)
                ->where('cf.`fieldto`', $fieldto)
                ->order_by('cfv.id', 'ASC')
                ->limit(50)
                ->get()
                ->result_array();

            if (isset($records)) {
                $insertRecords = [];
                foreach ($records as $record) {
                    $insertRecords[] = [
                        'client_id' => $record['relid'],
                        'related_company_name' => $record['value']
                    ];
                }
                if (count($insertRecords)) {
                    $this->db->insert_batch(db_prefix() . 'client_affiliates', $insertRecords);
                }
            }

            $hasNextRecords = (count($records) >= 50);
        } while ($hasNextRecords);
    }

    public function mapClientGeneralNotes()
    {
        $customFieldKey = 'customers_ghi_chu_tong_quan_ve_khach';
        $fieldto = 'customers';
        $records = [];
        $hasNextRecords = false;
        $nextId = 0;
        do {
            $records = $this->db
                ->select('cfv.relid as relid, cfv.`value` as `value`')
                ->from(db_prefix() . 'customfieldsvalues as cfv')
                ->join(db_prefix() . 'customfields as cf', 'cfv.fieldid = cf.id')
                ->where('cfv.id > ', $nextId)
                ->where('cf.`slug`', $customFieldKey)
                ->where('cf.`fieldto`', $fieldto)
                ->order_by('cfv.id', 'ASC')
                ->limit(50)
                ->get()
                ->result_array();

            if (isset($records)) {
                $updateRecords = [];
                foreach ($records as $record) {
                    $updateRecords[] = [
                        'userid' => $record['relid'],
                        'company_notes' => $record['value']
                    ];
                }
                if (count($updateRecords)) {
                    $this->db->update_batch(db_prefix() . 'clients', $updateRecords, 'userid');
                }
            }

            $hasNextRecords = (count($records) >= 50);
        } while ($hasNextRecords);
    }
}

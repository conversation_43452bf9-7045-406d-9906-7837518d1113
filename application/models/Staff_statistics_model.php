<?php

use Entities\M3cCall;

defined('BASEPATH') or exit('No direct script access allowed');

class Staff_statistics_model extends App_Model
{
    private $viewStaffIds = [];

    public function whereStaffByRole()
    {
        $staffRole = get_staff()->role;
        $staffIds = [];
        $this->load->model('roles_model');
        // View all
        if (in_array($staffRole, [$this->roles_model->SALES_ADMIN, $this->roles_model->ADMIN])) {
            $staffIds = [];
        } else if ($staffRole == $this->roles_model->SALES_LEADER) { // View members only
            $staffIds = $this->roles_model->get_sale_members(get_staff_user_id());
        } else { // View Own only
            $staffIds = [get_staff_user_id()];
        }

        return $staffIds;
    }

    private function getCompaniesOverall()
    {
        $this->db
            ->select(join(',', [
                'SUM((SELECT
                        COUNT(distinct customer_id)
                    FROM
                        ' . db_prefix() . 'customer_admins
                    WHERE
                        ' . db_prefix() . 'customer_admins.customer_id = parentclients.userid
                        ' . (count($this->viewStaffIds) > 0 ? ' AND ' . db_prefix() . 'customer_admins.staff_id IN (' . implode(',', $this->viewStaffIds) . ')' : '') . '
                )) AS total_customers',
                '0 AS total_expired_customers_next_10_days',
                'SUM((SELECT
                    COUNT(distinct ' . db_prefix() . 'clients.userid)
                FROM
                    ' . db_prefix() . 'clients
                        JOIN
                    ' . db_prefix() . 'client_contact_pools ON ' . db_prefix() . 'client_contact_pools.client_id = ' . db_prefix() . 'clients.userid
                WHERE
                    ' . db_prefix() . 'client_contact_pools.approved_at IS NULL
                    AND ' . db_prefix() . 'clients.userid = parentclients.userid
                )) AS total_free_customers'
            ]))
            ->from(db_prefix() . 'clients as parentclients')
            ->where('parentclients.`active`', 1);

        return $this->db->get()->row_array();
    }

    private function getPerformances($from, $to, $prefix = 'this_month')
    {
        $selects = [
            // Số công ty đề xuất chăm sóc hợp lệ
            'SUM((SELECT
                    COUNT(DISTINCT ' . db_prefix() . 'clients.userid)
                FROM
                    ' . db_prefix() . 'clients
                        JOIN
                    ' . db_prefix() . 'client_contact_pools on ' . db_prefix() . 'clients.userid = ' . db_prefix() . 'client_contact_pools.client_id
                WHERE
                    ' . db_prefix() . 'clients.userid = parentclients.userid
                    AND ' . db_prefix() . 'client_contact_pools.approved_at BETWEEN "' . $from . '" AND "' . $to . '"
                    AND ' . db_prefix() . 'client_contact_pools.id IN (SELECT client_contact_pool_id from ' . db_prefix() . 'contact_requests where ' . (count($this->viewStaffIds) > 0 ? ' staff_id IN (' . implode(',', $this->viewStaffIds) . ') AND ' : '') . 'status = "leader_approved" )
            )) AS ' . $prefix . '_valid_requested_take_care',
            // Số công ty tạo mới hợp lệ
            '(
                SELECT COUNT(DISTINCT ' . db_prefix() . 'clients.userid)
                FROM ' . db_prefix() . 'clients
                JOIN
                    ' . db_prefix() . 'client_requests on ' . db_prefix() . 'clients.userid = ' . db_prefix() . 'client_requests.client_id
                WHERE
                    ' . db_prefix() . 'clients.approved_at BETWEEN "' . $from . '" AND "' . $to . '"
                    ' . (count($this->viewStaffIds) > 0 ? ' AND ' . db_prefix() . 'client_requests.sale_id IN (' . implode(',', $this->viewStaffIds) . ')' : '') . '
            ) AS ' . $prefix . '_valid_created_company'
        ];

        if ($prefix == 'today') {
            $selects = array_merge($selects, [
                // Số công ty liên hệ thành công
                'SUM((SELECT
                    CASE
                        WHEN ' . db_prefix() . 'notes.`type` = 1
                            THEN 1 ELSE 0
                        END
                    FROM
                        ' . db_prefix() . 'notes
                    WHERE
                    ' . db_prefix() . 'notes.id = (
                        SELECT max(id) FROM ' . db_prefix() . 'notes
                        WHERE
                            ' . db_prefix() . 'notes.rel_type = "customer"
                            AND ' . db_prefix() . 'notes.rel_id = parentclients.userid
                            AND ' . db_prefix() . 'notes.`type` IN (1, 2)
                            AND ' . db_prefix() . 'notes.`dateadded` BETWEEN "' . $from . '" AND "' . $to . '"
                            ' . (count($this->viewStaffIds) > 0 ? ' AND ' . db_prefix() . 'notes.addedfrom IN (' . implode(',', $this->viewStaffIds) . ')' : '') . '
                    )
                )) AS ' . $prefix . '_company_contacted_success',
                // Số công ty liên hệ lại
                'SUM((SELECT
                    CASE
                        WHEN ' . db_prefix() . 'notes.`type` = 2 THEN 1
                            ELSE 0
                        END
                    FROM
                        ' . db_prefix() . 'notes
                    WHERE
                    ' . db_prefix() . 'notes.id = (
                        SELECT max(id) FROM ' . db_prefix() . 'notes
                        WHERE
                            ' . db_prefix() . 'notes.rel_type = "customer"
                            AND ' . db_prefix() . 'notes.rel_id = parentclients.userid
                            AND ' . db_prefix() . 'notes.`type` IN (1, 2)
                            AND ' . db_prefix() . 'notes.`dateadded` BETWEEN "' . $from . '" AND "' . $to . '"
                            ' . (count($this->viewStaffIds) > 0 ? ' AND ' . db_prefix() . 'notes.addedfrom IN (' . implode(',', $this->viewStaffIds) . ')' : '') . '
                    )
                )) AS ' . $prefix . '_company_contacted_later',
                // Số liên hệ tạo mới hợp lệ only for today
                '(
                    SELECT COUNT(*)
                    FROM ' . db_prefix() . 'contacts
                    WHERE  datecreated BETWEEN "' . $from . '" AND "' . $to . '"
                    ' . (count($this->viewStaffIds) > 0 ? (' AND created_by IN (' . implode(',', $this->viewStaffIds) . ')') : '') . '
                ) as ' . $prefix . '_valid_created_contact',
            ]);
        }

        $this->db->select(join(',', $selects));
        $this->db->from(db_prefix() . 'clients as parentclients');
        $this->db->where('parentclients.`active`', 1);
        if (count($this->viewStaffIds)) {
            $this->db->where('parentclients.`userid` IN (SELECT customer_id from ' . db_prefix() . 'customer_admins where staff_id IN(' . implode(',', $this->viewStaffIds) . '))');
        }
        $result = $this->db->get()->row_array();

        if ($prefix == 'today') {
            // Tổng số công ty đã liên hệ
            $result[$prefix . '_company_contacted'] = ($result[$prefix . '_company_contacted_success'] ?? 0) + ($result[$prefix . '_company_contacted_later'] ?? 0);
        }

        if ($prefix == 'this_month') {
            // Số công ty đã liên hệ từ đầu tháng
            $this->db->select('COUNT(DISTINCT rel_id) as this_month_company_contacted')
                ->from(db_prefix().'notes')
                ->where(db_prefix().'notes.rel_type', 'customer')
                ->where_in(db_prefix().'notes.type', [1, 2])
                ->where(db_prefix().'notes.dateadded BETWEEN "'.$from.'" AND "'.$to.'"', null);

            if (count($this->viewStaffIds)) {
                $this->db->where_in(db_prefix() . 'notes.addedfrom', $this->viewStaffIds);
            }
            $contacted = $this->db->get()->row_array();
            $result = array_merge($result, $contacted);
        }

        // Tổng số cuộc gọi
        // Thời gian đàm thoại
        $this->db->select(join(',', [
            'COUNT(*) as ' . $prefix . '_calls',
            'SUM(TIME_TO_SEC(talk_time)) as ' . $prefix . '_call_durations'
        ]))
            ->from(db_prefix() . '3c_call')
            ->where(db_prefix() . '3c_call.call_status', 'meetAgent')
            ->where(db_prefix() . '3c_call.call_type', M3cCall::CALL_OUT_TYPE)
            ->where(db_prefix() . '3c_call.talk_time >= ', '00:00:05')
            ->where(db_prefix() . '3c_call.staff_id in (
                select '.db_prefix().'staff.staffid from '.db_prefix().'staff ' . (count($this->viewStaffIds) > 0
                    ? (' where '.db_prefix().'staff.staffid IN (' . implode(',', $this->viewStaffIds) . ')')
                    : '') . '
            )')
            ->where(db_prefix() . '3c_call.start_time BETWEEN "' . $from . '" AND "' . $to . '"');

        $call = $this->db->get()->row_array();
        $call[$prefix . '_call_durations'] = $call[$prefix . '_call_durations'] > 0 ? round($call[$prefix . '_call_durations'] / 60, 2) : 0;
        $result = array_merge($result, $call);

        return $result;
    }

    private function getNoteContactedMonthly($from, $to)
    {
        $this->db->select('SUM(note_contacted) as this_month_company_contacted_success, SUM(note_contact_later) as this_month_company_contacted_later')
            ->from(db_prefix() . 'staff_statistics')
            ->where(db_prefix() . 'staff_statistics.`created_at` BETWEEN "' . $from . '" AND "' . $to . '" ');

        if (count($this->viewStaffIds)) {
            $this->db->where_in(db_prefix() . 'staff_statistics.staff_id', $this->viewStaffIds);
        }

        return $this->db->get()->row_array();
    }

    private function getOrders()
    {
        $firstDoM = date('Y-m-01');
        $now = date('Y-m-d');
        $this->db
            ->select(join(',', [
                'SUM(subtotal - discount_total) as total_delivered_orders',
                'COUNT(*) AS total_orders'
            ]));

        $this->db->from(db_prefix() . 'invoices');
        $this->db->where(db_prefix() . 'invoices.`date` BETWEEN "' . $firstDoM . '" AND "' . $now . '" ');

        if (count($this->viewStaffIds)) {
            $this->db->where(db_prefix() . 'invoices.clientid IN (SELECT customer_id FROM ' . db_prefix() . 'customer_admins WHERE staff_id IN (' . implode(',', $this->viewStaffIds) . '))');
        }

        return $this->db->get()->row_array();
    }

    private function getPaidOrders()
    {
        $firstDoM = date('Y-m-01');
        $now = date('Y-m-d');
        $this->db
            ->select(join(',', [
                'SUM(' . db_prefix() . 'invoices.subtotal - ' . db_prefix() . 'invoices.discount_total) as total_paid_revenue',
                'COUNT(*) AS total_paid_orders'
            ]));
        $this->load->model('Invoices_model');
        $this->db->from(db_prefix() . 'invoices')
            ->join(db_prefix() . 'invoicepaymentrecords', db_prefix() . 'invoicepaymentrecords.invoiceid = ' . db_prefix() . 'invoices.id')
            ->where(db_prefix() . 'invoices.`status`', $this->Invoices_model::STATUS_PAID)
            ->where(db_prefix() . 'invoicepaymentrecords.`date` BETWEEN "' . $firstDoM . '" AND "' . $now . '" ');

        if (count($this->viewStaffIds)) {
            $this->db->where(db_prefix() . 'invoices.clientid IN (SELECT customer_id FROM ' . db_prefix() . 'customer_admins WHERE staff_id IN (' . implode(',', $this->viewStaffIds) . '))');
        }

        return $this->db->get()->row_array();
    }

    public function getCustomerStatiscal()
    {
        $this->viewStaffIds = $this->whereStaffByRole();
        $response = array_merge([], $this->getCompaniesOverall());
        $response = array_merge($response, $this->getOrders());
        $response = array_merge($response, $this->getPaidOrders());
        $response = array_merge($response, $this->getNoteContactedMonthly(date('Y-m-01'), date('Y-m-d')));
        $response = array_merge($response, $this->getPerformances(date('Y-m-01 00:00:00'), date('Y-m-d H:i:s')));
        $response = array_merge($response, $this->getPerformances(date('Y-m-d 00:00:00'), date('Y-m-d H:i:s'), 'today'));

        // Since data of the month will be got daily at 0h, so need plus today to get correct data
        $response['this_month_company_contacted_success'] = ($response['this_month_company_contacted_success'] ?? 0) + ($response['today_company_contacted_success'] ?? 0);
        $response['this_month_company_contacted_later'] = ($response['this_month_company_contacted_later'] ?? 0) + ($response['today_company_contacted_later'] ?? 0);

        // Số công ty chưa liên hệ only for monthly
        $response['this_month_company_not_contacted_yet'] = max(0, ($response['total_customers'] ?? 0) - ($response['this_month_company_contacted'] ?? 0));
        return $response;
    }

    /**
     * Get note contacted of all staff by day based on inputted range
     * @param string $from start datetime
     * @param string $to end datetime
     * @return void
     */
    public function getNoteContacted($from, $to)
    {
        $startDay = new DateTime($from);
        $endDay = new DateTime($to);

        do {
            $startOfDay = $startDay->format('Y-m-d 00:00:00');
            $endOfDay = $startDay->format('Y-m-d 23:59:59');
            echo "Date: " . $startOfDay . PHP_EOL;
            $this->db->query('
                INSERT INTO ' . db_prefix() . 'staff_statistics(staff_id, note_contacted, note_contact_later, created_at)
                SELECT
                    notetbl.addedfrom,
                    notetbl.contacted,
                    notetbl.contact_later,
                    notetbl.created_at
                FROM
                (SELECT
                    `addedfrom`,
                    SUM(
                        CASE
                            WHEN `type` = 1 THEN 1
                            ELSE 0
                        END
                    ) as contacted,
                    SUM(
                        CASE
                            WHEN `type` = 2 THEN 1
                            ELSE 0
                        END
                    ) as contact_later,
                    "' . $startDay->format('Y-m-d') . '" as created_at
                FROM ' . db_prefix() . 'notes
                WHERE id IN (
                        SELECT MAX(id)
                        FROM ' . db_prefix() . 'notes
                        WHERE
                            ' . db_prefix() . 'notes.rel_type = "customer"
                            AND ' . db_prefix() . 'notes.`type` IN (1, 2)
                            AND ' . db_prefix() . 'notes.`dateadded` BETWEEN "' . $startOfDay . '" AND "' . $endOfDay . '"
                        GROUP BY
                            rel_id
                    )
                    AND `dateadded` BETWEEN "' . $startOfDay . '" AND "' . $endOfDay . '"
                GROUP BY addedfrom) as notetbl
                ON DUPLICATE KEY UPDATE note_contacted = notetbl.contacted, note_contact_later = notetbl.contact_later
            ');
            echo 'Effected rows: ' . $this->db->affected_rows() . PHP_EOL;
            $startDay = date_add($startDay, new DateInterval('P1D'));
        } while ($startDay->format('Ymd') <= $endDay->format('Ymd'));
    }
}

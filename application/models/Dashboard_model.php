<?php

use Entities\Ams\Job;
use Entities\InvoiceRequest;

defined('BASEPATH') or exit('No direct script access allowed');

class Dashboard_model extends App_Model
{
    protected $CI;
    protected $role_sale = 1;

    public function __construct()
    {
        $this->CI =& get_instance();
        $this->CI->load->helper('date');
        parent::__construct();
    }

    /**
     * @return array
     * Used in home dashboard page
     * Get staff in setting kpi month
     */
    public function get_staff_kpi($data, $arr_team_sale)
    {
        $arr = explode('.',$data['month_kpi']);

        $this->db->select('d.staff_id');
        $this->db->from(db_prefix() . 'kpi_settings as k');
        $this->db->join(db_prefix() . 'kpi_staff_items as d', 'k.id = d.kpi_id');
        $this->db->join(db_prefix() . 'staff_departments as p', 'd.staff_id  = p.staffid');
        if($data['team_id']){
            $this->db->where('p.departmentid', $data['team_id']);
        }else{
            $this->db->where_in('p.departmentid', $arr_team_sale);
        }

        $month_kpi = $arr[1] . '-' . $arr[0];
        $this->db->where('DATE_FORMAT(month_kpi, "%Y-%m") = "'. $month_kpi .'"');

        $data = $this->db->get()->result_array();

        // Xử lý lại mảng
        $arr = [];
        foreach($data as $item)
        {
            array_push($arr, $item['staff_id']);
        }

        return $arr;

    }



    /**
     * @return array
     * Get staff active > month_kpi, nhỏ hơn, nhưng cùng trong tháng, năm là vẫn lấy ra
     * Chỉ lấy nhân viên sale ((Sale admin, Sale Leader, Sale Manager không hiển thị)
     */
    public function get_staff_active($data, $arr_team_sale)
    {
        // Danh sách nhân viên trong tháng đó trở về trước
        $day_end_month = date('Y-m-t', strtotime('01.' . $data['month_kpi']));

        $this->db->select('s.staffid');
        $this->db->from(db_prefix() . 'staff as s');
        $this->db->where('active', 1);
        $this->db->where('datecreated <=', $day_end_month);
        $this->db->where('s.role', $this->role_sale);

        if($data['team_id']){
            $this->db->where('departmentid', $data['team_id']);
        }else{
            $this->db->where_in('departmentid', $arr_team_sale);
        }
        $this->db->join(db_prefix() . 'staff_departments as d', 's.staffid = d.staffid');
        $this->db->order_by('s.staffid', 'ASC');

        $result = $this->db->get()->result_array();

        // Xử lý lại mảng
        $arr = [];
        foreach($result as $item)
        {
            array_push($arr, $item['staffid']);
        }

        return $arr;
    }


    /**
     * @return array
     * Get setting kpi staff
     * Get setting kpi
     */
    public function list_setting_kpi($month, $list_agent)
    {
        $arr = explode('.',$month['month_kpi']);

        // Lấy kpi_id của thang $month['month_kpi']
        $month_kpi = $arr[1] . '-' . $arr[0];

        $query = $this->db->select('id')
                ->where('DATE_FORMAT(k.month_kpi, "%Y-%m") = "'. $month_kpi .'"')
                ->get(db_prefix() . 'kpi_settings as k');

        if ($query->num_rows() > 0) {
        $kpi_id = $query->row()->id; // Lấy trực tiếp giá trị cột "number"
        }else{
            $kpi_id = 0;
        }

        $this->db->select('s.staffid, s.firstname, s.lastname, d.total_working_days, d.target_amount, o.date, o.total_days');
        $this->db->from(db_prefix() . 'staff as s');
        $this->db->join(db_prefix() . 'staff_departments as p', 's.staffid = p.staffid');
        $this->db->join(db_prefix() . 'kpi_staff_items as d', 's.staffid = d.staff_id AND d.kpi_id ='. $kpi_id,'LEFT');
        $this->db->join(db_prefix() . 'kpi_day_off as o', 'o.kpi_id = '. $kpi_id .' AND d.staff_id = o.staff_id','LEFT');
        $this->db->where_in('s.staffid',$list_agent);
        $this->db->order_by('p.departmentid','ASC');
        $this->db->order_by('s.staffid','ASC');
        $this->db->order_by('o.date','ASC');

        $data = $this->db->get()->result_array();

        // Lọc lại kết quả
        $result = [];
        foreach($data as $item)
        {
            if(!isset($result[$item['staffid']]))
            {
                $result[$item['staffid']] = $item;
            }

            if(isset($item['date']) and isset($item['total_days']))
            {
                $result[$item['staffid']]['day_off'][] = array(
                    'date' => get_date($item['date']),
                    'total_days' => $item['total_days'],
                );
            }

        }

        return $result;
    }

    /**
     * @return array
     * Get setting kpi staff
     * Get setting kpi
     */
    public function get_setting_kpi($data)
    {
        $arr = explode('.',$data['month_kpi']);
        $month_kpi = $arr[1] . '-' . $arr[0];

        $this->db->select('*');
        $this->db->from(db_prefix() . 'kpi_settings as k');
        $this->db->where('DATE_FORMAT(k.month_kpi, "%Y-%m") = "'. $month_kpi .'"');

        $result = $this->db->get();
        if($result->num_rows() > 0)
        {
            return $result->row_array();
        }
        else{
            $result = [];
            $result['id'] = 0;
            $result['total_call_per_day'] = '';
            $result['total_talk_per_day'] = '';
            $result['total_note_per_day'] = '';
            $result['total_f2f_meeting_note_per_day'] = '';
            return $result;
        }
    }

    /**
     * @return array
     * Get target_amount kpi revenue team
     */
    public function get_kpi_revenue_team($kpi_id, $team_id)
    {
        if(!$kpi_id || !$team_id)
        return '';

        $this->db->select('*');
        $this->db->from(db_prefix() . 'kpi_team_revenue');
        $this->db->where('kpi_id', $kpi_id);
        $this->db->where('team_id', $team_id);

        $result = $this->db->get()->row();
        if(!isset($result))
        {
            return '';
        }

        return $result->target_amount;
    }


     /**
     * @return bool
     * Insert or Update KPI
     */
    public function insert_update_kpi($data)
    {
        $data['month_kpi'] = '01.' . $data['month_kpi'];
        $data['month_kpi'] = convert_date_format($data['month_kpi']);

        $data_kpi = array(
            'month_kpi' => $data['month_kpi'],
            'total_call_per_day' => str_replace(',', '', $data['total_call_per_day']),
            'total_talk_per_day' => str_replace(',', '', $data['total_talk_per_day']),
            'total_note_per_day' => str_replace(',', '', $data['total_note_per_day']),
            'total_f2f_meeting_note_per_day' => str_replace(',', '', $data['total_f2f_meeting_note_per_day']),
        );

        // Dựa vào $data['month_kpi'] lấy $data['kpi_id']
        $arr = explode('-',$data['month_kpi']);
        $month_kpi = $arr[0] . '-' . $arr[1];
        $this->db->select('id');
        $this->db->from(db_prefix() . 'kpi_settings as k');
        $this->db->where('DATE_FORMAT(k.month_kpi, "%Y-%m") = "'. $month_kpi .'"');

        $result = $this->db->get()->row();

        // Table KPI
        if(isset($result->id))
        {
            $data['kpi_id'] = $result->id;
            // Update
            $this->db->where('id', $data['kpi_id']);
            $this->db->update(db_prefix() . 'kpi_settings', $data_kpi);
        }else{
            // Insert
            $this->db->insert(db_prefix() . 'kpi_settings', $data_kpi);
            $data['kpi_id'] = $this->db->insert_id();
        }

        // kpi_revenue_team kpi doanh thu của từng team
        if($data['kpi_revenue_team'] && $data['team_sales'])
        {
            $data['kpi_revenue_team'] = str_replace(',', '', $data['kpi_revenue_team']);
            $sql = 'INSERT INTO '. db_prefix() . 'kpi_team_revenue (kpi_id, team_id, target_amount) VALUES ('. $data['kpi_id'] .', '. $data['team_sales'] .', '. $data['kpi_revenue_team'] .') ON DUPLICATE KEY UPDATE target_amount = ' . $data['kpi_revenue_team'];
            $this->db->query($sql);
        }

        // Table kpi_staff_items
        foreach($data['kpi'] as $staff)
        {
            // Insert or Update
            $staff['total_working_days'] = str_replace(',', '', $staff['total_working_days']);
            if(!$staff['total_working_days'])
                $staff['total_working_days'] = 0;

            $staff['target_amount'] = str_replace(',', '', $staff['target_amount']);
            if(!$staff['target_amount'])
                $staff['target_amount'] = 0;

            $sql = 'INSERT INTO '. db_prefix() . 'kpi_staff_items (kpi_id, staff_id, total_working_days, target_amount) VALUES ('. $data['kpi_id'] .', '. $staff['staff_id'] .', '. $staff['total_working_days'] .', '. $staff['target_amount'] .') ON DUPLICATE KEY UPDATE total_working_days = ' . $staff['total_working_days'] .', target_amount ='. $staff['target_amount'];
            $this->db->query($sql);

            // Table kpi day offf
            // Xóa dữ liệu cũ day off của $data['kpi_id'] của nhân viên $staff['staff_id']
            $this->db->where('kpi_id', $data['kpi_id']);
            $this->db->where('staff_id', $staff['staff_id']);
            $this->db->delete(db_prefix() . 'kpi_day_off');

            if(isset($staff['day_off']))
            {
                $data_day_off = [];
                foreach($staff['day_off'] as $day_off)
                {
                    $day_off['date_day_off'] = convert_date_format($day_off['date_day_off']);
                    $data_day_off[] = array(
                        'kpi_id' => $data['kpi_id'],
                        'staff_id' => $staff['staff_id'] ,
                        'date' => $day_off['date_day_off'],
                        'total_days' => $day_off['time_day_off'],
                    );
                }

                $this->db->insert_batch(db_prefix() . 'kpi_day_off', $data_day_off);
            }


        }

        return true;

    }


    /**
     * @return array
     * Used in home dashboard page
     * Return all upcoming events this week
     */
    public function get_upcoming_events()
    {
        $monday_this_week = date('Y-m-d', strtotime('monday this week'));
        $sunday_this_week = date('Y-m-d', strtotime('sunday this week'));

        $this->db->where("(start BETWEEN '$monday_this_week' and '$sunday_this_week')");
        $this->db->where('(userid = ' . get_staff_user_id() . ' OR public = 1)');
        $this->db->order_by('start', 'desc');
        $this->db->limit(6);

        return $this->db->get(db_prefix() . 'events')->result_array();
    }


    /**
     * @param  integer (optional) Limit upcoming events
     * @return integer
     * Used in home dashboard page
     * Return total upcoming events next week
     */
    public function get_upcoming_events_next_week()
    {
        $monday_this_week = date('Y-m-d', strtotime('monday next week'));
        $sunday_this_week = date('Y-m-d', strtotime('sunday next week'));
        $this->db->where("(start BETWEEN '$monday_this_week' and '$sunday_this_week')");
        $this->db->where('(userid = ' . get_staff_user_id() . ' OR public = 1)');

        return $this->db->count_all_results(db_prefix() . 'events');
    }

    /**
     * @param  mixed
     * @return array
     * Used in home dashboard page, currency passed from javascript (undefined or integer)
     * Displays weekly payment statistics (chart)
     */
    public function get_weekly_payments_statistics($currency)
    {
        $all_payments                 = [];
        $has_permission_payments_view = has_permission('payments', '', 'view');
        $this->db->select(db_prefix() . 'invoicepaymentrecords.id, amount,' . db_prefix() . 'invoicepaymentrecords.date');
        $this->db->from(db_prefix() . 'invoicepaymentrecords');
        $this->db->join(db_prefix() . 'invoices', '' . db_prefix() . 'invoices.id = ' . db_prefix() . 'invoicepaymentrecords.invoiceid');
        $this->db->where('YEARWEEK(' . db_prefix() . 'invoicepaymentrecords.date) = YEARWEEK(CURRENT_DATE)');
        $this->db->where('' . db_prefix() . 'invoices.status !=', 5);
        if ($currency != 'undefined') {
            $this->db->where('currency', $currency);
        }

        if (!$has_permission_payments_view) {
            $this->db->where('invoiceid IN (SELECT id FROM ' . db_prefix() . 'invoices WHERE addedfrom=' . get_staff_user_id() . ' and addedfrom IN (SELECT staff_id FROM ' . db_prefix() . 'staff_permissions WHERE feature="invoices" AND capability="view_own"))');
        }

        // Current week
        $all_payments[] = $this->db->get()->result_array();
        $this->db->select(db_prefix() . 'invoicepaymentrecords.id, amount,' . db_prefix() . 'invoicepaymentrecords.date');
        $this->db->from(db_prefix() . 'invoicepaymentrecords');
        $this->db->join(db_prefix() . 'invoices', '' . db_prefix() . 'invoices.id = ' . db_prefix() . 'invoicepaymentrecords.invoiceid');
        $this->db->where('YEARWEEK(' . db_prefix() . 'invoicepaymentrecords.date) = YEARWEEK(CURRENT_DATE - INTERVAL 7 DAY) ');

        $this->db->where('' . db_prefix() . 'invoices.status !=', 5);
        if ($currency != 'undefined') {
            $this->db->where('currency', $currency);
        }

        if (!$has_permission_payments_view) {
            $this->db->where('invoiceid IN (SELECT id FROM ' . db_prefix() . 'invoices WHERE addedfrom=' . get_staff_user_id() . ' and addedfrom IN (SELECT staff_id FROM ' . db_prefix() . 'staff_permissions WHERE feature="invoices" AND capability="view_own"))');
        }

        // Last Week
        $all_payments[] = $this->db->get()->result_array();

        $chart = [
            'labels'   => get_weekdays(),
            'datasets' => [
                [
                    'label'           => _l('this_week_payments'),
                    'backgroundColor' => 'rgba(37,155,35,0.2)',
                    'borderColor'     => '#84c529',
                    'borderWidth'     => 1,
                    'tension'         => false,
                    'data'            => [
                        0,
                        0,
                        0,
                        0,
                        0,
                        0,
                        0,
                    ],
                ],
                [
                    'label'           => _l('last_week_payments'),
                    'backgroundColor' => 'rgba(197, 61, 169, 0.5)',
                    'borderColor'     => '#c53da9',
                    'borderWidth'     => 1,
                    'tension'         => false,
                    'data'            => [
                        0,
                        0,
                        0,
                        0,
                        0,
                        0,
                        0,
                    ],
                ],
            ],
        ];


        for ($i = 0; $i < count($all_payments); $i++) {
            foreach ($all_payments[$i] as $payment) {
                $payment_day = date('l', strtotime($payment['date']));
                $x           = 0;
                foreach (get_weekdays_original() as $day) {
                    if ($payment_day == $day) {
                        $chart['datasets'][$i]['data'][$x] += $payment['amount'];
                    }
                    $x++;
                }
            }
        }

        return $chart;
    }


    /**
     * @param  mixed
     * @return array
     * Used in home dashboard page, currency passed from javascript (undefined or integer)
     * Displays monthly payment statistics (chart)
     */
    public function get_monthly_payments_statistics($currency)
    {
        $all_payments                 = [];
        $has_permission_payments_view = has_permission('payments', '', 'view');
        $this->db->select('SUM(amount) as total, MONTH(' . db_prefix() . 'invoicepaymentrecords.date) as month');
        $this->db->from(db_prefix() . 'invoicepaymentrecords');
        $this->db->join(db_prefix() . 'invoices', '' . db_prefix() . 'invoices.id = ' . db_prefix() . 'invoicepaymentrecords.invoiceid');
        $this->db->where('YEAR(' . db_prefix() . 'invoicepaymentrecords.date) = YEAR(CURRENT_DATE)');
        $this->db->where('' . db_prefix() . 'invoices.status !=', 5);
        $this->db->group_by('month');

        if ($currency != 'undefined') {
            $this->db->where('currency', $currency);
        }

        if (!$has_permission_payments_view) {
            $this->db->where('invoiceid IN (SELECT id FROM ' . db_prefix() . 'invoices WHERE addedfrom=' . get_staff_user_id() . ' and addedfrom IN (SELECT staff_id FROM ' . db_prefix() . 'staff_permissions WHERE feature="invoices" AND capability="view_own"))');
        }

        $all_payments = $this->db->get()->result_array();

        for ($i = 1; $i <= 12; $i++) {
            if (!isset($all_payments[$i])) {
                $all_payments[$i]['total'] = 0;
                $all_payments[$i]['month'] = $i;
            }
            $all_payments[$i]['label'] = _l(date("F", mktime(0, 0, 0, $i, 1)));
        }
        usort($all_payments, function($a, $b) {
            return (int) $a['month'] <=> (int) $b['month'];
        });

        $chart = [
            'labels'   => array_column($all_payments, 'label'),
            'datasets' => [
                [
                    'label'           => _l('report_sales_type_income'),
                    'backgroundColor' => 'rgba(37,155,35,0.2)',
                    'borderColor'     => '#84c529',
                    'borderWidth'     => 1,
                    'tension'         => false,
                    'data'            => array_column($all_payments, 'total'),
                ],
            ],
        ];
        return $chart;
    }

    public function projects_status_stats()
    {
        $this->load->model('projects_model');
        $statuses = $this->projects_model->get_project_statuses();
        $colors   = get_system_favourite_colors();

        $chart = [
            'labels'   => [],
            'datasets' => [],
        ];

        $_data                         = [];
        $_data['data']                 = [];
        $_data['backgroundColor']      = [];
        $_data['hoverBackgroundColor'] = [];
        $_data['statusLink']           = [];


        $has_permission = has_permission('projects', '', 'view');
        $sql            = '';
        foreach ($statuses as $status) {
            $sql .= ' SELECT COUNT(*) as total';
            $sql .= ' FROM ' . db_prefix() . 'projects';
            $sql .= ' WHERE status=' . $status['id'];
            if (!$has_permission) {
                $sql .= ' AND id IN (SELECT project_id FROM ' . db_prefix() . 'project_members WHERE staff_id=' . get_staff_user_id() . ')';
            }
            $sql .= ' UNION ALL ';
            $sql = trim($sql);
        }

        $result = [];
        if ($sql != '') {
            // Remove the last UNION ALL
            $sql    = substr($sql, 0, -10);
            $result = $this->db->query($sql)->result();
        }

        foreach ($statuses as $key => $status) {
            array_push($_data['statusLink'], admin_url('projects?status=' . $status['id']));
            array_push($chart['labels'], $status['name']);
            array_push($_data['backgroundColor'], $status['color']);
            array_push($_data['hoverBackgroundColor'], adjust_color_brightness($status['color'], -20));
            array_push($_data['data'], $result[$key]->total);
        }

        $chart['datasets'][]           = $_data;
        $chart['datasets'][0]['label'] = _l('home_stats_by_project_status');

        return $chart;
    }

    public function leads_status_stats()
    {
        $chart = [
            'labels'   => [],
            'datasets' => [],
        ];

        $_data                         = [];
        $_data['data']                 = [];
        $_data['backgroundColor']      = [];
        $_data['hoverBackgroundColor'] = [];
        $_data['statusLink']           = [];

        $result = get_leads_summary();

        foreach ($result as $status) {
            if ($status['color'] == '') {
                $status['color'] = '#737373';
            }
            array_push($chart['labels'], $status['name']);
            array_push($_data['backgroundColor'], $status['color']);
            if (!isset($status['junk']) && !isset($status['lost'])) {
                array_push($_data['statusLink'], admin_url('leads?status=' . $status['id']));
            }
            array_push($_data['hoverBackgroundColor'], adjust_color_brightness($status['color'], -20));
            array_push($_data['data'], $status['total']);
        }

        $chart['datasets'][] = $_data;

        return $chart;
    }

    /**
     * Display total tickets awaiting reply by department (chart)
     * @return array
     */
    public function tickets_awaiting_reply_by_department()
    {
        $this->load->model('departments_model');
        $departments = $this->departments_model->get();
        $colors      = get_system_favourite_colors();
        $chart       = [
            'labels'   => [],
            'datasets' => [],
        ];

        $_data                         = [];
        $_data['data']                 = [];
        $_data['backgroundColor']      = [];
        $_data['hoverBackgroundColor'] = [];

        $i = 0;
        foreach ($departments as $department) {
            if (!is_admin()) {
                if (get_option('staff_access_only_assigned_departments') == 1) {
                    $staff_deparments_ids = $this->departments_model->get_staff_departments(get_staff_user_id(), true);
                    $departments_ids      = [];
                    if (count($staff_deparments_ids) == 0) {
                        $departments = $this->departments_model->get();
                        foreach ($departments as $department) {
                            array_push($departments_ids, $department['departmentid']);
                        }
                    } else {
                        $departments_ids = $staff_deparments_ids;
                    }
                    if (count($departments_ids) > 0) {
                        $this->db->where('department IN (SELECT departmentid FROM ' . db_prefix() . 'staff_departments WHERE departmentid IN (' . implode(',', $departments_ids) . ') AND staffid="' . get_staff_user_id() . '")');
                    }
                }
            }
            $this->db->where_in('status', [
                1,
                2,
                4,
            ]);

            $this->db->where('department', $department['departmentid']);
            $this->db->where(db_prefix() . 'tickets.merged_ticket_id IS NULL', null, false);
            $total = $this->db->count_all_results(db_prefix() . 'tickets');

            if ($total > 0) {
                $color = '#333';
                if (isset($colors[$i])) {
                    $color = $colors[$i];
                }
                array_push($chart['labels'], $department['name']);
                array_push($_data['backgroundColor'], $color);
                array_push($_data['hoverBackgroundColor'], adjust_color_brightness($color, -20));
                array_push($_data['data'], $total);
            }
            $i++;
        }

        $chart['datasets'][] = $_data;

        return $chart;
    }

    /**
     * Display total tickets awaiting reply by status (chart)
     * @return array
     */
    public function tickets_awaiting_reply_by_status()
    {
        $this->load->model('tickets_model');
        $statuses             = $this->tickets_model->get_ticket_status();
        $_statuses_with_reply = [
            1,
            2,
            4,
        ];

        $chart = [
            'labels'   => [],
            'datasets' => [],
        ];

        $_data                         = [];
        $_data['data']                 = [];
        $_data['backgroundColor']      = [];
        $_data['hoverBackgroundColor'] = [];
        $_data['statusLink']           = [];

        foreach ($statuses as $status) {
            if (in_array($status['ticketstatusid'], $_statuses_with_reply)) {
                if (!is_admin()) {
                    if (get_option('staff_access_only_assigned_departments') == 1) {
                        $staff_deparments_ids = $this->departments_model->get_staff_departments(get_staff_user_id(), true);
                        $departments_ids      = [];
                        if (count($staff_deparments_ids) == 0) {
                            $departments = $this->departments_model->get();
                            foreach ($departments as $department) {
                                array_push($departments_ids, $department['departmentid']);
                            }
                        } else {
                            $departments_ids = $staff_deparments_ids;
                        }
                        if (count($departments_ids) > 0) {
                            $this->db->where('department IN (SELECT departmentid FROM ' . db_prefix() . 'staff_departments WHERE departmentid IN (' . implode(',', $departments_ids) . ') AND staffid="' . get_staff_user_id() . '")');
                        }
                    }
                }

                $this->db->where('status', $status['ticketstatusid']);
                $this->db->where(db_prefix() . 'tickets.merged_ticket_id IS NULL', null, false);
                $total = $this->db->count_all_results(db_prefix() . 'tickets');
                if ($total > 0) {
                    array_push($chart['labels'], ticket_status_translate($status['ticketstatusid']));
                    array_push($_data['statusLink'], admin_url('tickets/index/' . $status['ticketstatusid']));
                    array_push($_data['backgroundColor'], $status['statuscolor']);
                    array_push($_data['hoverBackgroundColor'], adjust_color_brightness($status['statuscolor'], -20));
                    array_push($_data['data'], $total);
                }
            }
        }

        $chart['datasets'][] = $_data;

        return $chart;
    }

    /**
     * Get all dashboard metrics in a single cache entry (todo)
     * This reduces the number of cache lookups and improves performance
     *
     * @param string $from_date From date
     * @param string $to_date To date
     * @return array All dashboard metrics
     */
    public function get_dashboard_metrics($from_date, $to_date)
    {
        // Use query caching if available
        if (method_exists($this->db, 'cache_on')) {
            $this->db->cache_on();
        }

        // Get all metrics
        return [
            'job_sales_statistics' => $this->get_job_sales_statistics($from_date, $to_date),
            'estimates' => $this->get_estimates_data($from_date, $to_date),
            'purchase_orders' => $this->get_invoices_data($from_date, $to_date),
            'invoices' => $this->get_invoice_request_data($from_date, $to_date),
        ];
    }

    /**
     * Get job postings sales statistics
     *
     * @param string $from_date From date
     * @param string $to_date To date
     * @see getJobPostingTypeCount() Referenced
     *
     */
    public function get_job_sales_statistics($from_date, $to_date)
    {
        $query = Job::query();
        $query->select('level')
            ->selectRaw('COUNT(id) as count')
            ->selectRaw('COUNT(IF(crm_invoice_id IS NULL, NULL, 1)) as invoice_count')
            ->whereIn('status', [1,3]) /* Close, Open */
            ->where(function ($query) use ($from_date, $to_date) {
                $query->whereBetween('published_at', [$from_date . '000000', $to_date . '235959'])
                    ->orWhere(function ($query) use ($from_date) {
                        $query->where('published_at', '<', $from_date)
                            ->where('expires_at', '>', $from_date);
                    })
                    ->orWhere(function ($query) {
                        $query->whereNull('expires_at')
                            ->where('status', 3);
                    })
                    ->orWhere(function ($query) use ($to_date) {
                        $query->where('expires_at', '=', $to_date);
                    });
            })->groupBy('level');

        return $query->get()->mapWithKeys(function ($item) {
            return [$item->level => [
                'level' => $item->level,
                'count' => $item->count,
                'invoice_count' => $item->invoice_count,
            ]];
        })->all();
    }

    /**
     * Get estimates data
     *
     * @param string $from_date From date
     * @param string $to_date To date
     * @return array Estimates data
     */
    public function get_estimates_data($from_date, $to_date)
    {
        // Initialize result array
        $result = [
            'accepted' => ['count' => 0, 'amount' => 0],
            'sent' => ['count' => 0, 'amount' => 0],
        ];

        $builder = \Entities\Estimate::query()
            ->selectRaw('status, SUM(total - total_tax) as amount, count(id) as count')
            ->whereNull('parent_id')
            ->where('currency', 3)
            ->where('date', '>=', $from_date)
            ->where('date', '<=', $to_date)
            ->whereIn('status', [2, 4])
            ->groupBy('status');

        $items = $builder->get()->mapWithKeys(function ($item) {
            $code = ['2' => 'sent', '4' => 'accepted'];
            return [$code[$item->status] => [
                'count' => $item->count,
                'amount' => $item->amount,
            ]];
        });

        return array_merge($result, $items->all());
    }

    /**
     * Get purchase orders data
     *
     * @param string $from_date From date
     * @param string $to_date To date
     * @return array Purchase orders data
     */
    public function get_invoices_data($from_date, $to_date)
    {
        // Initialize result array
        $result = [
            'paid' => ['count' => 0, 'amount' => 0],
            'unpaid' => ['count' => 0, 'amount' => 0],
            'overdue' => ['count' => 0, 'amount' => 0],
            'partially_paid' => ['count' => 0, 'amount' => 0]
        ];

        $query = \Entities\Invoice::query()
            ->select(DB::raw('status, count(id) as `count`, sum(total - total_tax) as amount'))
            ->whereBetween('date', [$from_date . '000000', $to_date . '235959'])
            ->whereIn('status', [1, 2, 3, 4])
            ->groupBy('status');

        $items = $query->get()->mapWithKeys(function ($item) {
            $codes = [
                '1' => 'unpaid',
                '2' => 'paid',
                '3' => 'partially_paid',
                '4' => 'overdue',
            ];
            return [$codes[$item->status] => [
                'count' => $item->count,
                'amount' => $item->amount,
            ]];
        });

        return array_merge($result, $items->all());
    }

    /**
     * Get invoice Request data
     *
     * @param string $from_date From date
     * @param string $to_date To date
     * @return array Invoice Request data
     */
    public function get_invoice_request_data($from_date, $to_date)
    {
        $result = [
            'paid' => ['count' => 0, 'amount' => 0],
            'unpaid' => ['count' => 0, 'amount' => 0],
            'overdue' => ['count' => 0, 'amount' => 0],
            'partially_paid' => ['count' => 0, 'amount' => 0]
        ];
        $tblinvoices = db_prefix() . 'invoices';
        $query = InvoiceRequest::query()
            ->select(DB::raw("$tblinvoices.status, count(tblinvoice_request.id) as `count`, sum($tblinvoices.total - $tblinvoices.total_tax) as amount"))
            ->join('invoices', 'invoices.id', '=', 'invoice_request.invoiceid')
            ->whereBetween('invoice_request.created_at', [$from_date . '000000', $to_date . '235959'])
            ->whereIn('invoices.status', [1, 2, 3, 4])
            ->groupBy('invoices.status');

        $items = $query->get()->mapWithKeys(function ($item) {
            $codes = [
                '1' => 'unpaid',
                '2' => 'paid',
                '3' => 'partially_paid',
                '4' => 'overdue',
            ];
            return [$codes[$item->status] => [
                'count' => $item->count,
                'amount' => $item->amount,
            ]];
        });

        return array_merge($result, $items->all());
    }

    public function get_planning_activity_yearly_data($from_date, $to_date, $status = null)
    {
        $builder = \Entities\Invoice::query()
            ->selectRaw('year(date) as year, `status`');

        $builder = $this->buildSelectRawExpr($builder)
            ->whereBetween('invoice_issued_date', [$from_date, $to_date])
            //->whereIn('status', $status ?? [2,3])
            ->groupBy('year', 'status')
            ->orderBy('year');

        return $builder->get()->all();
    }

    public function get_planning_activity_year_week_data($from_date, $to_date, $status = null)
    {
        $builder = \Entities\Invoice::query()
            ->selectRaw('yearweek(invoice_issued_date) as year, `status`');
        $builder = $this->buildSelectRawExpr($builder)
            ->whereBetween('invoice_issued_date', [$from_date, $to_date])
            //->whereIn('status', $status ?? [2,3])
            ->groupBy('year', 'status')
            ->orderBy('year');

        return $builder->get()->all();
    }

    /**
     * @param $from_date
     * @param $to_date
     * @param null $group_by
     * @return array
     */
    public function get_planning_activity_data($from_date, $to_date, $status = [])
    {
        if (empty($status)) {
            $status = [2,3];
        }

        $builder = \Entities\Invoice::query()
            ->selectRaw("DATE_FORMAT(invoice_issued_date, '%Y-%m') as year, `status`");

        $builder = $this->buildSelectRawExpr($builder)
            ->whereBetween('invoice_issued_date', [$from_date, $to_date])
            //->whereIn('status', $status)
            ->groupBy('year', 'status')
            ->orderBy('year');

        return $builder->get()->all();
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $builder
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function buildSelectRawExpr($builder)
    {
        $db_prefix = db_prefix();
        return $builder
            ->join('minvoices', 'minvoices.invoice_id', '=', 'invoices.id')
            ->selectRaw("
       concat(min(invoice_issued_date), ' ~ ', max(invoice_issued_date)) as period_date,
       sum(`total` - total_tax)                      as total_amount,
       sum((select sum(amount)
            from {$db_prefix}invoice_daily_revenues
            where invoice_id = {$db_prefix}invoices.id
              and revenue_date <= year(invoice_issued_date) * 10000 + 1231))
                                                     as fiscal_year_earned_revenue,
       sum((select sum(amount) from {$db_prefix}invoice_daily_revenues
            where invoice_id = {$db_prefix}invoices.id
            and revenue_date >= (year(invoice_issued_date) + 1) * 10000 + 101))
                                                     as deferred_earned_revenue,
       concat(min(use_expired_at), '~', max(use_expired_at)) as period_expired_at,
       sum(`total` - total_tax - IFNULL((select sum(amount)
                                         from {$db_prefix}invoice_daily_revenues
                                         where invoice_id = {$db_prefix}invoices.id
                                         and datediff(revenue_date, invoice_issued_date) >= 335 and note is not null
                                         ), 0)
       )                                             as expired_unused,
       sum(`total` - total_tax - IFNULL((select sum(amount)
                                        from {$db_prefix}invoice_daily_revenues
                                        where invoice_id = {$db_prefix}invoices.id
                                        and (datediff(revenue_date, invoice_issued_date) < 335
                                             or (datediff(revenue_date, invoice_issued_date) < 365 and note is null))
                                        ), 0)
       )                                             as unused_amount,
       count(distinct {$db_prefix}invoices.id)                as invoice_count,
       group_concat(distinct {$db_prefix}invoices.id)         as invoice_ids,
       sum(total - IFNULL((select sum(`{$db_prefix}invoicepaymentrecords`.`amount`)
            from `{$db_prefix}invoicepaymentrecords`
            where `{$db_prefix}invoices`.`id` = `{$db_prefix}invoicepaymentrecords`.`invoiceid`), 0)
       )                                             as `unpaid_amount`");
    }


}

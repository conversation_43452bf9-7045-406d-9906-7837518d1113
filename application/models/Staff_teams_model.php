<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Staff_teams_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get staff teams where customer belongs
     * @param  mixed $id customer id
     * @return array
     */
    public function get_staff_teams($id)
    {
        return $this->db->where('staff_id', $id)
            ->get(db_prefix() . 'staff_teams')
            ->result_array();
    }

    /**
     * Get all staff teams
     * @param  string $id
     * @return mixed
     */
    public function get_teams($id = '')
    {
        if ($id) {
            return $this->db->where('id', $id)
                ->get(db_prefix() . 'teams')
                ->row();
        }

        return $this->db
            ->order_by('name', 'asc')
            ->get(db_prefix() . 'teams')
            ->result_array();
    }

    /**
     * Add new staff team
     * @param array $data $_POST data
     */
    public function add($data)
    {
        $this->db->insert(db_prefix() . 'teams', $data);

        $insert_id = $this->db->insert_id();

        if ($insert_id) {
            log_activity('New Staff Team Created (#' . $insert_id . ', Name: ' . $data['name'] . ')');

            return $insert_id;
        }

        return false;
    }

    /**
     * Edit staff team
     * @param  array $data $_POST data
     * @return boolean
     */
    public function edit($data)
    {
        $this->db->where('id', $data['id'])
            ->update(db_prefix() . 'teams', [
                'name' => $data['name'],
            ]);

        if ($this->db->affected_rows()) {
            log_activity('Staff Team Updated (#' . $data['id'] . ')');

            return true;
        }

        return false;
    }

    /**
     * Delete staff team
     * @param  mixed $id group id
     * @return boolean
     */
    public function delete($id)
    {
        $this->db->where('id', $id)
            ->delete(db_prefix() . 'teams');

        if ($this->db->affected_rows()) {
            $this->db->where('team_id', $id)
                ->delete(db_prefix() . 'staff_teams');

            log_activity('Staff Team Deleted (#' . $id . ')');

            return true;
        }

        return false;
    }

    public function sync_staff_teams($id, $teamIds)
    {
        $affectedRows = 0;
        $staffTeams = $this->get_staff_teams($id);

        if (count($staffTeams)) {
            foreach ($staffTeams as $staffTeam) {
                if (count($teamIds)) {
                    if (!in_array($staffTeam['team_id'], $teamIds)) {
                        $this->db
                            ->where('staff_id', $id)
                            ->where('id', $staffTeam['id'])
                            ->delete(db_prefix() . 'staff_teams');

                        if ($this->db->affected_rows()) {
                            $affectedRows++;
                        }
                    }
                } else {
                    $this->db
                        ->where('staff_id', $id)
                        ->delete(db_prefix() . 'staff_teams');

                    if ($this->db->affected_rows()) {
                        $affectedRows++;
                    }
                }
            }
            if (count($teamIds)) {
                foreach ($teamIds as $teamId) {
                    $_exists = $this->db
                        ->where('staff_id', $id)
                        ->where('team_id', $teamId)
                        ->get(db_prefix() . 'staff_teams')
                        ->row();

                    if (!$_exists) {
                        $this->db->insert(db_prefix() . 'staff_teams', [
                            'staff_id' => $id,
                            'team_id' => $teamId,
                        ]);

                        if ($this->db->affected_rows()) {
                            $affectedRows++;
                        }
                    }
                }
            }
        } else {
            if (count($teamIds)) {
                foreach ($teamIds as $teamId) {
                    $this->db->insert(db_prefix() . 'staff_teams', [
                        'staff_id' => $id,
                        'team_id' => $teamId,
                    ]);

                    if ($this->db->affected_rows()) {
                        $affectedRows++;
                    }
                }
            }
        }

        return $affectedRows ? true : false;
    }
}

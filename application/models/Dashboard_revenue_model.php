<?php

use Entities\Ams\Job;
use Entities\InvoiceRequest;

defined('BASEPATH') or exit('No direct script access allowed');

class Dashboard_revenue_model extends App_Model
{

    /**
     * Get revenue recognition summary data grouped by accounting periods
     *
     * This method calculates revenue recognition data for different accounting periods:
     * - Fiscal Year (FY)
     * - Half-years (1H, 2H)
     * - Quarters (1Q, 2Q, 3Q, 4Q)
     * - Monthly (Jan-25 ~ Jul-25)
     *
     * @param string $from_date Start date in format YYYYMMDD
     * @param string $to_date End date in format YYYYMMDD
     * @return array Revenue recognition data grouped by periods
     */
    public function get_revenue_recognition_summary_data($from_date, $to_date)
    {
        try {
            // Validate input parameters
            if (empty($from_date) || empty($to_date)) {
                log_message('error', 'Invalid date parameters provided to get_revenue_recognition_summary_data');
                throw new InvalidArgumentException('From date and to date are required parameters');
            }

            // Validate date format
            if (!preg_match('/^\d{8}$/', $from_date) || !preg_match('/^\d{8}$/', $to_date)) {
                log_message('error', 'Invalid date format provided: from_date=' . $from_date . ', to_date=' . $to_date);
                throw new InvalidArgumentException('Date parameters must be in YYYYMMDD format');
            }

            // Generate a cache key based on the date parameters
            $cache_key = 'revenue_recognition_' . md5($from_date . '_' . $to_date);
            clock('debug', 'Revenue recognition summary data cache key: ' . $cache_key);

            // Check if we have cached data for these parameters
            $cached_data = false;
            $this->get_cached_data($cache_key);
            if ($cached_data !== false) {
                clock('info', 'Revenue recognition data retrieved from cache for period: ' . $from_date . ' to ' . $to_date);
                return $cached_data;
            }
        } catch (Exception $e) {
            log_message('error', 'Error in get_revenue_recognition_summary_data initialization: ' . $e->getMessage());
            // Return empty structure to prevent application crashes
            return $this->get_empty_revenue_structure();
        }

        // Use the three separate methods to get data as per DEV-2361 requirements
        $invoice_issued_data = $this->get_invoice_issued_revenue($from_date, $to_date);
        $earned_revenue_data = $this->get_earned_revenue_by_period($from_date, $to_date);

        // Combine data from the three methods
        $monthly_data = [];

        // Process invoice issued data
        foreach ($invoice_issued_data as $period => $amount) {
            if (!isset($monthly_data[$period])) {
                $monthly_data[$period] = $this->initialize_metrics();
            }
            $monthly_data[$period]['invoice_amount'] = $amount;
        }


        // Process recognized revenue data
        foreach ($earned_revenue_data as $period => $data) {
            if (!isset($monthly_data[$period])) {
                $monthly_data[$period] = $this->initialize_metrics();
            }
            $monthly_data[$period]['earned_revenue'] = $data['earned_revenue'] ?? 0;
            $monthly_data[$period]['recognized_revenue'] = $data['recognized_revenue'] ?? 0;
            $monthly_data[$period]['breakage_revenue'] = $data['breakage_revenue'] ?? 0;
        }

        // Sort the monthly data by period key (YYYYMM)
        ksort($monthly_data);

        // Current year for fiscal periods
        $current_year = date('Y', strtotime($to_date));

        // Get 2024 unused amount
        $previous_unearned_revenue = $this->get_previous_unearned_revenue_amount($current_year);
        $unearned_revenue = $previous_unearned_revenue;
        $invoice_amount = $previous_unearned_revenue;

        // Calculate unearned amounts and recognition rates
        foreach ($monthly_data as $period => $data) {
            // Calculate unearned amount (invoice amount - earned revenue - breakage)
            $unearned_revenue = max(0, $data['invoice_amount'] - $data['earned_revenue'] + $unearned_revenue);
            $monthly_data[$period]['unearned_revenue'] = $unearned_revenue;
            $invoice_amount =+ $data['invoice_amount'];

            // Calculate Unearned rate
            $monthly_data[$period]['unearned_rate'] = ($data['invoice_amount'] > 0)
                ? ($unearned_revenue / $invoice_amount * 100)
                : 0;
        }



        // Initialize result array with fiscal periods
        $result = [
            'FY' => $this->initialize_metrics(),
            '1H' => $this->initialize_metrics(),
            '2H' => $this->initialize_metrics(),
            '1Q' => $this->initialize_metrics(),
            '2Q' => $this->initialize_metrics(),
            '3Q' => $this->initialize_metrics(),
            '4Q' => $this->initialize_metrics(),
            'monthly' => [],
            'previous_unearned_revenue' => $previous_unearned_revenue
        ];


        // Aggregate monthly data into fiscal periods
        foreach ($monthly_data as $yearMonth => $data) {
            $year = substr($yearMonth, 0, 4);
            $month = (int)substr($yearMonth, 4, 2);

            // Add to monthly data with formatted month name
            $month_name = date('M-y', strtotime($yearMonth . '01'));
            $result['monthly'][$month_name] = $data;

            // Only aggregate into fiscal periods if it's the current year
            if ($year == $current_year) {
                // Add to Fiscal Year
                $this->add_to_period($result['FY'], $data);

                // Add to Half-Year
                if ($month <= 6) {
                    $this->add_to_period($result['1H'], $data);
                } else {
                    $this->add_to_period($result['2H'], $data);
                }

                // Add to Quarter
                if ($month <= 3) {
                    $this->add_to_period($result['1Q'], $data);
                } elseif ($month <= 6) {
                    $this->add_to_period($result['2Q'], $data);
                } elseif ($month <= 9) {
                    $this->add_to_period($result['3Q'], $data);
                } else {
                    $this->add_to_period($result['4Q'], $data);
                }
            }
        }

        // Calculate recognition rates for aggregated periods
        foreach (['FY', '1H', '2H', '1Q', '2Q', '3Q', '4Q'] as $period) {
            if ($result[$period]['invoice_amount'] > 0) {
                $result[$period]['unearned_rate'] = ($result[$period]['unearned_revenue'] / ($previous_unearned_revenue + $result[$period]['invoice_amount'])) * 100;
            } else {
                $result[$period]['unearned_rate'] = 0;
            }
        }

        // Store the result in cache for future requests (cache for 1 hour)
        $this->store_cached_data($cache_key, $result, 3600);

        return $result;
    }

    /**
     * Initialize metrics array with zero values
     *
     * @return array
     */
    private function initialize_metrics()
    {
        return [
            'invoice_amount' => 0,
            'earned_revenue' => 0,
            'recognized_revenue' => 0,
            'breakage_revenue' => 0,
            'unearned_revenue' => 0,
            'unearned_rate' => 0
        ];
    }

    /**
     * Get empty revenue recognition structure for error handling
     *
     * This method provides a safe fallback structure when errors occur
     * in revenue recognition data processing, preventing application crashes.
     *
     * @return array Empty revenue recognition structure with all required fields
     */
    private function get_empty_revenue_structure()
    {
        return [
            'FY' => $this->initialize_metrics(),
            '1H' => $this->initialize_metrics(),
            '2H' => $this->initialize_metrics(),
            '1Q' => $this->initialize_metrics(),
            '2Q' => $this->initialize_metrics(),
            '3Q' => $this->initialize_metrics(),
            '4Q' => $this->initialize_metrics(),
            'monthly' => [],
            'previous_unearned_revenue' => 2997965765,
            'error' => true,
            'error_message' => 'Unable to retrieve revenue recognition data due to system error'
        ];
    }

    /**
     * Get cached data by key
     *
     * @param string $key Cache key
     * @return mixed Cached data or false if not found
     */
    private function get_cached_data($key)
    {
        $this->load->driver('cache', ['adapter' => 'file']);
        return $this->cache->get($key);
    }

    /**
     * Store data in cache
     *
     * @param string $key Cache key
     * @param mixed $data Data to cache
     * @param int $ttl Time to live in seconds (default: 3600 = 1 hour)
     * @return bool True on success, false on failure
     */
    private function store_cached_data($key, $data, $ttl = 3600)
    {
        $this->load->driver('cache', ['adapter' => 'file']);
        return $this->cache->save($key, $data, $ttl);
    }

    /**
     * Add data from one period to another
     *
     * @param array &$target Target period data
     * @param array $source Source period data
     */
    private function add_to_period(&$target, $source)
    {
        $target['invoice_amount'] += $source['invoice_amount'];
        $target['earned_revenue'] += $source['earned_revenue'];
        $target['recognized_revenue'] += $source['recognized_revenue'];
        $target['breakage_revenue'] += $source['breakage_revenue'];
    }

    /**
     * Get Unearned Revenue from previous year
     *
     * @return int
     */
    private function get_previous_unearned_revenue_amount($year = 2025)
    {
        // 2024년 Unused Amount는 2,997,965,765로 하드코딩 처리 (tblrevenue_2024_balances 테이블의 sum(unearned_revenue_amount))
        $unearned_amount = 2997965765;
        if ($year >  2025) {
            // todo 이전 년도 이연(Deferred)/선수(Unearned) Unearned 리턴
            $unearned_amount = 0;
        }

        return $unearned_amount;
    }

    /**
     * Get invoice issued revenue based on tax invoice issuance date (excluding tax)
     *
     * This method calculates revenue based on the invoice issuance date from minvoices table,
     * excluding tax amounts. Data is grouped by year-month format.
     *
     * @param string $from_date Start date in format YYYYMMDD
     * @param string $to_date End date in format YYYYMMDD
     * @return array Array of revenue amounts grouped by period ['YYYYMM' => amount, ...]
     */
    public function get_invoice_issued_revenue($from_date, $to_date)
    {
        // Query to get invoice issued revenue grouped by month
        $result = \Entities\Invoice::query()
            ->selectRaw("
                DATE_FORMAT(invoice_issued_date, '%Y%m') as period,
                SUM(total - total_tax) as invoice_amount
            ")
            ->join('minvoices', 'invoices.id', '=', 'minvoices.invoice_id')
            ->whereNotNull('minvoices.invoice_issued_date')
            ->whereBetween('minvoices.invoice_issued_date', [$from_date, $to_date])
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        // Convert to associative array
        $revenue_data = [];
        foreach ($result as $row) {
            $revenue_data[$row->period] = (float)$row->invoice_amount;
        }

        return $revenue_data;
    }

    /**
     * Get recognized revenue grouped by revenue recognition date
     *
     * This method calculates recognized revenue based on the revenue_date from
     * tblinvoice_daily_revenues table, grouped by year-month format.
     *
     * The method implements DEV-2361 requirements for revenue recognition
     * based on daily revenue recognition dates rather than invoice issue dates.
     *
     * @param string $from_date Start date in format YYYYMMDD
     * @param string $to_date End date in format YYYYMMDD
     * @return array Array of recognized revenue amounts grouped by period ['YYYYMM' => amount, ...]
     *
     */
    public function get_earned_revenue_by_period($from_date, $to_date)
    {
        // Build optimized query to get recognized revenue grouped by month
        // Uses proper joins to ensure data integrity and performance
        $earned_revenue_query = \Entities\InvoiceDailyRevenue::query()
            ->selectRaw("
                DATE_FORMAT(revenue_date, '%Y%m') as period,
                SUM(amount)                       as earned_revenue,
                sum(if(datediff(revenue_date, invoice_issued_date) < 335 or (datediff(revenue_date, invoice_issued_date) < 365 and note is null), amount, 0))   as recognized_revenue,
                sum(if(datediff(revenue_date, invoice_issued_date) >= 335 and note is not null, amount, 0))                                                     as breakage_revenue,
                group_concat(distinct if(datediff(revenue_date, invoice_issued_date) < 335 or (datediff(revenue_date, invoice_issued_date) < 365 and note is null), note, null) separator '\n' ) as recognized_notes,
                group_concat(distinct if(datediff(revenue_date, invoice_issued_date) >= 335 and note is not null, note, null) separator '\n')                                                    as breakage_notes
            ")
            ->join('invoices', 'invoices.id', '=', 'invoice_daily_revenues.invoice_id')
            ->join('minvoices', 'invoices.id', '=', 'minvoices.invoice_id')
            //->whereNotNull('minvoices.invoice_issued_date')
            ->whereBetween('invoice_daily_revenues.revenue_date', [$from_date, $to_date])
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        // Convert query results to associative array for easier processing
        return $earned_revenue_query->mapWithKeys(function ($row) {
            return [$row->period => [
                'earned_revenue' => $row->earned_revenue,
                'recognized_revenue' => $row->recognized_revenue,
                'breakage_revenue' => $row->breakage_revenue,
                'recognized_notes' => $row->recognized_notes,
                'breakage_notes' => $row->breakage_notes,
            ]];
        })->toArray();
    }

}

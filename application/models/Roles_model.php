<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Roles_model extends App_Model
{
    public $CUSTOMER_SERVICE = 5;
    public $SALES_ADMIN = 8;
    public $SALES_LEADER = 7;
    public $SALES_MEMBER = 1;
    public $ADMIN = 4;
    public $ACCOUNT = 3;

    /**
     * update permission roleid
     * input array delete function, array add function
     * return bool
     */
    public function update_permission($array_group, $array_delete = [], $array_add = [])
    {
        if(!$array_group || (!$array_delete && !$array_add)){
            return false;
        }
        
        foreach($array_group as $group_id)
        {
            $this->db->where('roleid', $group_id);
            $role = $this->db->get(db_prefix() . 'roles')->row();
            $role->permissions = !empty($role->permissions) ? unserialize($role->permissions) : [];
            $saleLeaderRole = json_decode(json_encode($role), true);
            $saleLeaderRolePermission = $saleLeaderRole['permissions'];
            
            // Delete permisstion
            foreach($array_delete as $item)
            {
                if(isset($saleLeaderRolePermission[$item['position']]))
                {
                    foreach($saleLeaderRolePermission[$item['position']] as $index => $value)
                    {
                        if($item['function'] == $value)
                        { 
                            // Delete
                            unset($saleLeaderRolePermission[$item['position']][$index]);
                        }
                    }
                }
            }

            foreach($array_add as $item)
            {
                $saleLeaderRolePermission[$item['position']][] = $item['function'];
            }

            // Update database
            $saleLeaderRole['permissions'] = $saleLeaderRolePermission;
            $saleLeaderRole['update_staff_permissions'] = true;
            $this->update($saleLeaderRole, $group_id);
        }
       
        return true;
    }

    /**
     * Add new employee role
     * @param mixed $data
     */
    public function add($data)
    {
        $permissions = [];
        if (isset($data['permissions'])) {
            $permissions = $data['permissions'];
        }

        $data['permissions'] = serialize($permissions);

        $this->db->insert(db_prefix() . 'roles', $data);
        $insert_id = $this->db->insert_id();

        if ($insert_id) {
            log_activity('New Role Added [ID: ' . $insert_id . '.' . $data['name'] . ']');

            return $insert_id;
        }

        return false;
    }

    /**
     * Update employee role
     * @param  array $data role data
     * @param  mixed $id   role id
     * @return boolean
     */
    public function update($data, $id)
    {
        $affectedRows = 0;
        $permissions  = [];
        if (isset($data['permissions'])) {
            $permissions = $data['permissions'];
        }

        $data['permissions'] = serialize($permissions);

        $update_staff_permissions = false;
        if (isset($data['update_staff_permissions'])) {
            $update_staff_permissions = true;
            unset($data['update_staff_permissions']);
        }

        $this->db->where('roleid', $id);
        $this->db->update(db_prefix() . 'roles', $data);

        if ($this->db->affected_rows() > 0) {
            $affectedRows++;
        }

        if ($update_staff_permissions == true) {
            $this->load->model('staff_model');

            $staff = $this->staff_model->get('', [
                'role' => $id,
            ]);

            foreach ($staff as $member) {
                if ($this->staff_model->update_permissions($permissions, $member['staffid'])) {
                    $affectedRows++;
                }
            }
        }

        if ($affectedRows > 0) {
            log_activity('Role Updated [ID: ' . $id . ', Name: ' . $data['name'] . ']');

            return true;
        }

        return false;
    }

    /**
     * Get employee role by id
     * @param  mixed $id Optional role id
     * @return mixed     array if not id passed else object
     */
    public function get($id = '')
    {
        if (is_numeric($id)) {

            $role = $this->app_object_cache->get('role-' . $id);

            if ($role) {
                return $role;
            }

            $this->db->where('roleid', $id);

            $role              = $this->db->get(db_prefix() . 'roles')->row();
            $role->permissions = !empty($role->permissions) ? unserialize($role->permissions) : [];

            $this->app_object_cache->add('role-' . $id, $role);

            return $role;
        }

        return $this->db->get(db_prefix() . 'roles')->result_array();
    }

    /**
     * Delete employee role
     * @param  mixed $id role id
     * @return mixed
     */
    public function delete($id)
    {
        $current = $this->get($id);

        // Check first if role is used in table
        if (is_reference_in_table('role', db_prefix() . 'staff', $id)) {
            return [
                'referenced' => true,
            ];
        }

        $affectedRows = 0;
        $this->db->where('roleid', $id);
        $this->db->delete(db_prefix() . 'roles');

        if ($this->db->affected_rows() > 0) {
            $affectedRows++;
        }

        if ($affectedRows > 0) {
            log_activity('Role Deleted [ID: ' . $id);

            return true;
        }

        return false;
    }

    public function get_contact_permissions($id)
    {
        $this->db->where('userid', $id);

        return $this->db->get(db_prefix() . 'contact_permissions')->result_array();
    }

    public function get_role_staff($role_id)
    {
        $this->db->where('role', $role_id);

        return $this->db->get(db_prefix() . 'staff')->result_array();
    }

    /**
     * Get all members of the leader by it's departments
     * @param int $leaderId
     * @return array staff ids
     */
    public function get_sale_members($leaderId)
    {
        $deptTable = db_prefix().'staff_departments';
        $staffIds = $this->db->select($deptTable . '.staffid as member_id')
            ->where($deptTable . '.departmentid IN (SELECT '.$deptTable . '.departmentid FROM '.$deptTable . ' WHERE staffid = ' . $leaderId . ')')
            ->where([
                db_prefix().'staff.role' => $this->SALES_MEMBER,
                db_prefix().'staff.active' => 1
            ])
            ->join($deptTable, $deptTable.'.staffid = '.db_prefix().'staff.staffid')
            ->get(db_prefix().'staff')
            ->result_array();

        return array_merge(array_pluck($staffIds, 'member_id'), [$leaderId]);
    }

    public function update_role_permissions($roleId, $feature, $permissions)
    {
        // Sale role
        $staffRole = json_decode(json_encode($this->get($roleId)), true);
        $staffRolePermission = $staffRole['permissions'];
        $staffRolePermission[$feature] = array_unique(array_merge(
            $staffRolePermission[$feature] ?? [],
            $permissions
        ));
        $staffRole['permissions'] = $staffRolePermission;
        $staffRole['update_staff_permissions'] = true;
        $this->update($staffRole, $roleId);
    }

}

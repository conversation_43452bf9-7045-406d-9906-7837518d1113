<?php

defined('BASEPATH') or exit('No direct script access allowed');

/*
|--------------------------------------------------------------------------
| Display Debug backtrace
|--------------------------------------------------------------------------
|
| If setss to TRUE, a backtrace will be displayed along with php errors. If
| error_reporting is disabled, the backtrace will not display, regardless
| of this setting
|
*/
defined('SHOW_DEBUG_BACKTRACE') or define('SHOW_DEBUG_BACKTRACE', true);

/*
|--------------------------------------------------------------------------
| File and Directory Modes
|--------------------------------------------------------------------------
|
| These prefs are used when checking and setting modes when working
| with the file system.  The defaults are fine on servers with proper
| security, but you may wish (or even need) to change the values in
| certain environments (Apache running a separate process for each
| user, PHP under CGI with Apache suEXEC, etc.).  Octal values should
| always be used to set the mode correctly.
|
*/
defined('FILE_READ_MODE') or define('FILE_READ_MODE', 0644);
defined('FILE_WRITE_MODE') or define('FILE_WRITE_MODE', 0666);
defined('DIR_READ_MODE') or define('DIR_READ_MODE', 0755);
defined('DIR_WRITE_MODE') or define('DIR_WRITE_MODE', 0755);

defined('APP_CHMOD_DIR') or define('APP_CHMOD_DIR', (fileperms(FCPATH) & 0777 | 0755));
defined('APP_CHMOD_FILE') or define('APP_CHMOD_FILE', (fileperms(FCPATH . 'index.php') & 0777 | 0644));
/*
|--------------------------------------------------------------------------
| File Stream Modes
|--------------------------------------------------------------------------
|
| These modes are used when working with fopen()/popen()
|
*/
defined('FOPEN_READ') or define('FOPEN_READ', 'rb');
defined('FOPEN_READ_WRITE') or define('FOPEN_READ_WRITE', 'r+b');
defined('FOPEN_WRITE_CREATE_DESTRUCTIVE') or define('FOPEN_WRITE_CREATE_DESTRUCTIVE', 'wb'); // truncates existing file data, use with care
defined('FOPEN_READ_WRITE_CREATE_DESCTRUCTIVE') or define('FOPEN_READ_WRITE_CREATE_DESTRUCTIVE', 'w+b'); // truncates existing file data, use with care
defined('FOPEN_WRITE_CREATE') or define('FOPEN_WRITE_CREATE', 'ab');
defined('FOPEN_READ_WRITE_CREATE') or define('FOPEN_READ_WRITE_CREATE', 'a+b');
defined('FOPEN_WRITE_CREATE_STRICT') or define('FOPEN_WRITE_CREATE_STRICT', 'xb');
defined('FOPEN_READ_WRITE_CREATE_STRICT') or define('FOPEN_READ_WRITE_CREATE_STRICT', 'x+b');

/*
|--------------------------------------------------------------------------
| Exit Status Codes
|--------------------------------------------------------------------------
|
| Used to indicate the conditions under which the script is exit()ing.
| While there is no universal standard for error codes, there are some
| broad conventions.  Three such conventions are mentioned below, for
| those who wish to make use of them.  The CodeIgniter defaults were
| chosen for the least overlap with these conventions, while still
| leaving room for others to be defined in future versions and user
| applications.
|
| The three main conventions used for determining exit status codes
| are as follows:
|
|    Standard C/C++ Library (stdlibc):
|       http://www.gnu.org/software/libc/manual/html_node/Exit-Status.html
|       (This link also contains other GNU-specific conventions)
|    BSD sysexits.h:
|       http://www.gsp.com/cgi-bin/man.cgi?section=3&topic=sysexits
|    Bash scripting:
|       http://tldp.org/LDP/abs/html/exitcodes.html
|
*/
defined('EXIT_SUCCESS') or define('EXIT_SUCCESS', 0); // no errors
defined('EXIT_ERROR') or define('EXIT_ERROR', 1); // generic error
defined('EXIT_CONFIG') or define('EXIT_CONFIG', 3); // configuration error
defined('EXIT_UNKNOWN_FILE') or define('EXIT_UNKNOWN_FILE', 4); // file not found
defined('EXIT_UNKNOWN_CLASS') or define('EXIT_UNKNOWN_CLASS', 5); // unknown class
defined('EXIT_UNKNOWN_METHOD') or define('EXIT_UNKNOWN_METHOD', 6); // unknown class member
defined('EXIT_USER_INPUT') or define('EXIT_USER_INPUT', 7); // invalid user input
defined('EXIT_DATABASE') or define('EXIT_DATABASE', 8); // database error
defined('EXIT__AUTO_MIN') or define('EXIT__AUTO_MIN', 9); // lowest automatically-assigned error code
defined('EXIT__AUTO_MAX') or define('EXIT__AUTO_MAX', 125); // highest automatically-assigned error code

/**
 * Used for phpass
 */
define('PHPASS_HASH_STRENGTH', 8);
define('PHPASS_HASH_PORTABLE', false);

/**
 * Admin URL
 */
define('ADMIN_URL', 'admin');
/**
 * Admin URI
 * CUSTOM_ADMIN_URL is not yet tested well, don't define it
 */
define('ADMIN_URI', DEFINED('CUSTOM_ADMIN_URL') ? CUSTOM_ADMIN_URL : ADMIN_URL);

/**
 * CRM server update url
 */
define('UPDATE_URL', 'https://www.perfexcrm.com/perfex_updates/index.php');

/**
 * Get latest version info
 */
define('UPDATE_INFO_URL', 'https://www.perfexcrm.com/perfex_updates/update_info.php');

/**
 * Do not send sms to data eq. invoices, estimates older then X days.
 */
if (!defined('DO_NOT_SEND_SMS_ON_DATA_OLDER_THEN')) {
    define('DO_NOT_SEND_SMS_ON_DATA_OLDER_THEN', 45);
}

if (!defined('CUSTOM_FIELD_TRANSFER_SIMILARITY')) {
    define('CUSTOM_FIELD_TRANSFER_SIMILARITY', 85);
}

/**
 * CRM temporary path
 */
define('TEMP_FOLDER', FCPATH . 'temp' . '/');

/**
 * Customer attachments folder from profile
 */
define('CLIENT_ATTACHMENTS_FOLDER', FCPATH . 'uploads/clients' . '/');
/**
 * All tickets attachments
 */
define('TICKET_ATTACHMENTS_FOLDER', FCPATH . 'uploads/ticket_attachments' . '/');
/**
 * Company attachments, favicon, logo etc..
 */
define('COMPANY_FILES_FOLDER', FCPATH . 'uploads/company' . '/');
/**
 * Staff profile images
 */
define('STAFF_PROFILE_IMAGES_FOLDER', FCPATH . 'uploads/staff_profile_images' . '/');
/**
 * Contact profile images
 */
define('CONTACT_PROFILE_IMAGES_FOLDER', FCPATH . 'uploads/client_profile_images' . '/');
/**
 * Newsfeed attachments
 */
define('NEWSFEED_FOLDER', FCPATH . 'uploads/newsfeed' . '/');
/**
 * Csv import salesperson attachments
 */
define('IMPORT_SALESPERSON_FOLDER', FCPATH . 'uploads/csv/salesperson/');
/**
 * Csv import customer admin attachments
 */
define('IMPORT_CUSTOMER_ADMIN_FOLDER', FCPATH . 'uploads/csv/customer_admin/');
/**
 * Csv import customer admin attachments
 */
define('IMPORT_NUM_OF_USAGE_BEHAVIOR', FCPATH . 'uploads/csv/import_num_of_usage_behavior/');
/**
 * Note attachments
 */
define('NOTE_ATTACHMENTS_FOLDER', FCPATH . 'uploads/notes/');
/**
 * Contracts attachments
 */
define('CONTRACTS_UPLOADS_FOLDER', FCPATH . 'uploads/contracts' . '/');
/**
 * Tasks attachments
 */
define('TASKS_ATTACHMENTS_FOLDER', FCPATH . 'uploads/tasks' . '/');
/**
 * Invoice attachments
 */
define('INVOICE_ATTACHMENTS_FOLDER', FCPATH . 'uploads/invoices' . '/');
/**
 * Estimate attachments
 */
define('ESTIMATE_ATTACHMENTS_FOLDER', FCPATH . 'uploads/estimates' . '/');
/**
 * Proposal attachments
 */
define('PROPOSAL_ATTACHMENTS_FOLDER', FCPATH . 'uploads/proposals' . '/');
/**
 * Expenses receipts
 */
define('EXPENSE_ATTACHMENTS_FOLDER', FCPATH . 'uploads/expenses' . '/');
/**
 * Lead attachments
 */
define('LEAD_ATTACHMENTS_FOLDER', FCPATH . 'uploads/leads' . '/');
/**
 * Project files attachments
 */
define('PROJECT_ATTACHMENTS_FOLDER', FCPATH . 'uploads/projects' . '/');
/**
 * Project discussions attachments
 */
define('PROJECT_DISCUSSION_ATTACHMENT_FOLDER', FCPATH . 'uploads/discussions' . '/');
/**
 * Credit notes attachment folder
 */
define('CREDIT_NOTES_ATTACHMENTS_FOLDER', FCPATH . 'uploads/credit_notes' . '/');
/**
 * Modules Path
 */
define('APP_MODULES_PATH', FCPATH . 'modules/');
/**
 * Helper libraries path
 */
define('LIBSPATH', APPPATH . 'libraries/');
/**
 * Id of custom field salesperson
 */
define('SALES_PERSON_CUSTOM_FIELD', 1);

define('LEAD_MEMBERS', [
    2, 3, 4, 7, 8
]);

define('MOBIFONE_3C_HOST', "https://3c-capi.mobifone.vn");
define('MOBIFONE_3C_DOMAIN', "**********");
define('MOBIFONE_3C_ACCESS_KEY', "FRSEpNZfiig6F5cv");

/**
 * Define infomation for proposalpdf version 2
 */
define('TOPDEV_WEBSITE', "https://topdev.vn");
define('BANK_ACCOUNT_TOPDEV', "191 287 9411 9012");
define('SWIFT_CODE', "VTCB VN VX");
define('USER_PROPOSAL_SIGNATURE_IF_PRICE_LARGEST_THAN_20_MILLION', "TRƯƠNG QUỲNH NHƯ");
define('USER_PROPOSAL_SIGNATURE_IF_PRICE_LESS_THAN_20_MILLION', "NGUYỄN KHÁNH DUY");

define('POTENTIAL_RATE_NOTE_OPTIONS', [
    [
        'value' => 1,
        'text' => 'Tiếp cận lần đầu'
    ], [
        'value' => 2,
        'text' => '20% (1 tín hiệu)'
    ], [
        'value' => 3,
        'text' => '50% (Từ 3 tín hiệu)'
    ], [
        'value' => 4,
        'text' => '80% (Từ 4 tín hiệu)'
    ], [
        'value' => 5,
        'text' => '100%'
    ]
]);


define('SERVICE_APPLANCER', [
    [
        'value' => 'Tin đăng',
        'text' => 'Tin đăng'
    ],
    [
        'value' => 'Truyền thông/ Sponsor',
        'text' => 'Truyền thông/ Sponsor'
    ],
    [
        'value' => 'Vé sự kiện',
        'text' => 'Vé sự kiện'
    ],
    [
        'value' => 'Headhunt',
        'text' => 'Headhunt'
    ],
    [
        'value' => 'Khác',
        'text' => 'Khác'
    ]
]);

define('CONTRACT_STATUS', [
    [
        'value' => 'PO không ký',
        'text' => 'PO không ký'
    ],
    [
        'value' => 'PO có chữ ký của TopDev',
        'text' => 'PO có chữ ký của TopDev'
    ],
    [
        'value' => 'PO có chữ ký của 2 bên',
        'text' => 'PO có chữ ký của 2 bên'
    ],
    [
        'value' => 'Hợp đồng chưa hoàn thành (Chưa đủ chữ ký của hai bên và sale chưa nhận được bản cứng)',
        'text' => 'Hợp đồng chưa hoàn thành (Chưa đủ chữ ký của hai bên và sale chưa nhận được bản cứng)'
    ],
    [
        'value' => 'Hợp đồng đã hoàn tất (Đã ký 2 bên và Sale đã nhận được bản cứng)',
        'text' => 'Hợp đồng đã hoàn tất (Đã ký 2 bên và Sale đã nhận được bản cứng)'
    ]
]);

define('REQUEST_STATUS', [
    1 => [
        'value' => 1,
        'text' => 'Đã thu tiền cần xuất hóa đơn'
    ],
    2 => [
        'value' => 2,
        'text' => 'Xuất hóa đơn trước thu tiền sau'
    ],
]);

define('INVOICE_DRAFT', [
    '1' => [
        'value' => 1,
        'text' => 'Đang chờ'
    ],
    '2' => [
        'value' => 2,
        'text' => 'Đã xuất nháp'
    ],
    '3' => [
        'value' => 3,
        'text' => 'Đã xuất chính'
    ],
]);

define('ACTION_NOTE_OPTIONS', [
    [
        'value' => 1,
        'text' => 'Bán hàng - Tiếp cận'
    ], [
        'value' => 2,
        'text' => 'Bán hàng - Tư vấn'
    ], [
        'value' => 3,
        'text' => 'Bán hàng - Đề xuất/ Báo giá'
    ], [
        'value' => 4,
        'text' => 'Bán hàng - Thanh toán'
    ], [
        'value' => 5,
        'text' => 'Sau bán hàng - Hóa đơn, chứng từ'
    ], [
        'value' => 6,
        'text' => 'Sau bán hàng - Kích hoạt gói dịch vụ'
    ], [
        'value' => 7,
        'text' => 'Sau bán hàng - Feedback/ Hỗ trợ'
    ]
]);

define('CONTACT_CHANNEL_NOTE_OPTIONS', [
    [
        'value' => 1,
        'text' => 'Call In'
    ], [
        'value' => 2,
        'text' => 'Call Out'
    ], [
        'value' => 3,
        'text' => 'Email'
    ], [
        'value' => 4,
        'text' => 'Zalo'
    ], [
        'value' => 5,
        'text' => 'Skype'
    ], [
        'value' => 6,
        'text' => 'Telegram'
    ], [
        'value' => 7,
        'text' => 'F2F meeting'
    ], [
        'value' => 8,
        'text' => 'Online meeting'
    ], [
        'value' => 9,
        'text' => 'Linkedin'
    ]
]);

define('DETAILED_SIGNAL_NOTE_OPTIONS', [
    [
        'value' => 1,
        'text' => 'Có nhu cầu tuyển IT'
    ], [
        'value' => 2,
        'text' => 'Có ngân sách'
    ], [
        'value' => 3,
        'text' => 'Cần tuyển gấp/ Có kế hoạch tuyển IT'
    ], [
        'value' => 4,
        'text' => 'Hỏi về giá, ưu đãi, chiết khấu, quà tặng'
    ], [
        'value' => 5,
        'text' => 'Hỏi về tính năng, gói dịch vụ, điểm khác biệt so với kênh khác'
    ], [
        'value' => 6,
        'text' => 'Hỏi về chính sách, CV/ cam kết số lượng, chất lượng, quy trình thanh toán/ kích hoạt'
    ]
]);

define('CONTACT_STATUS_OPTIONS', [
    [
        'value' => 1,
        'text' => 'Đang phụ trách'
    ],
    [
        'value' => 2,
        'text' => 'Không phụ trách tuyển dụng'
    ],
    [
        'value' => 3,
        'text' => 'Sắp nghỉ việc/ Bàn giao'
    ],
    [
        'value' => 4,
        'text' => 'Đã nghỉ việc'
    ],
    [
        'value' => 5,
        'text' => 'Chưa xác định'
    ]
]);

define('CONTACT_REVIEW_STATUS_OPTIONS', [
    [
        'value' => 0,
        'text' => 'Hệ thống duyệt'
    ], [
        'value' => 1,
        'text' => 'SA duyệt'
    ], [
        'value' => 2,
        'text' => 'Từ chối'
    ]
]);

define('SEX_OPTIONS', [
    [
        'value' => 0,
        'text' => 'Khác'
    ], [
        'value' => 1,
        'text' => 'Nam'
    ], [
        'value' => 2,
        'text' => 'Nữ'
    ]
]);

define('CALL_STATUS_OPTIONS', [
    [
        'value' => 'meetAgent',
        'text' => 'meetAgent'
    ],
    [
        'value' => 'miss',
        'text' => 'miss'
    ]
]);

define('JOB_TRACKING_REPORT_STATUS_UNUSED', 1);
define('JOB_TRACKING_REPORT_STATUS_USED', 2);
define('JOB_TRACKING_REPORT_STATUS_EXTEND', 3);
define('JOB_TRACKING_REPORT_STATUS_EXPIRED', 4);

define('JOB_TRACKING_REPORT_STATUS_OPTIONS', [
    [
        'value' => JOB_TRACKING_REPORT_STATUS_UNUSED,
        'text' => 'Chưa sử dụng/ Unused'
    ],
    [
        'value' => JOB_TRACKING_REPORT_STATUS_USED,
        'text' => 'Đã dùng/ Used'
    ],
    [
        'value' => JOB_TRACKING_REPORT_STATUS_EXTEND,
        'text' => 'Gia hạn/ Extend'
    ],
    [
        'value' => JOB_TRACKING_REPORT_STATUS_EXPIRED,
        'text' => 'Hết hạn/ expired'
    ]
]);

define('JOB_TRACKING_REPORT_TYPE_SERVICE_PACKAGE', 1);
define('JOB_TRACKING_REPORT_TYPE_GIFT_PACKAGE', 2);

define('JOB_TRACKING_REPORT_TYPE_OPTIONS', [
    [
        'value' => JOB_TRACKING_REPORT_TYPE_SERVICE_PACKAGE,
        'text' => 'Gói dịch vụ'
    ],
    [
        'value' => JOB_TRACKING_REPORT_TYPE_GIFT_PACKAGE,
        'text' => 'Quà tặng'
    ]
]);

// Client request status
define('CLIENT_REQUEST_STATUS_WAITING', 'waiting');
define('CLIENT_REQUEST_STATUS_SA_APPROVED', 'sa_approved');
define('CLIENT_REQUEST_STATUS_SA_REJECTED', 'sa_rejected');
define('CLIENT_REQUEST_STATUS_LEADER_APPROVED', 'leader_approved');
define('CLIENT_REQUEST_STATUS_LEADER_REJECTED', 'leader_rejected');
define('CLIENT_REQUEST_STATUS_REJECTED', 'rejected');

// COMPANY REQUEST FILES FOLDER
define('COMPANY_REQUEST_FILES_FOLDER', COMPANY_FILES_FOLDER . 'clilent_request/');

define('ES_INDEXES', [
    'clients' => 'crm_clients',
    'staff' => 'crm_staff',
    'contacts' => 'crm_contacts',
    'tickets' => 'crm_tickets',
    'leads' => 'crm_leads',
    'proposals' => 'crm_proposals',
    'invoices' => 'crm_invoices',
    'creditnotes' => 'crm_creditnotes',
    'estimates' => 'crm_estimates',
    'expenses' => 'crm_expenses',
    'projects' => 'crm_projects',
    'contracts' => 'crm_contracts',
    'knowledge_base' => 'crm_knowledge_base',
    'tasks' => 'crm_tasks',
    'invoicepaymentrecords' => 'crm_invoicepaymentrecords',
    'customfieldsvalues' => 'crm_customfieldsvalues',
    'itemable' => 'crm_itemable',
    'goals' => 'crm_goals',
    'survey' => 'crm_survey',
    'tags' => 'crm_tags',
]);

define('INVOICE_SIGNED_STATUSES', [
    0 => 'Đã xuất chính',
    1 => 'Hủy',
    2 => 'Điều chỉnh',
    3 => 'Thay thế',
    4 => 'Giải trình',
    5 => 'Sai sót do tổng hợp',
    6 => 'Bị thay thế',
    7 => 'Bị điều chỉnh',
]);

define('INVOICE_SIGNED_STATUS_COLORS', [
    0 => 'info',
    1 => 'danger',
    2 => 'warning',
    3 => 'warning',
    4 => 'warning',
    5 => 'danger',
    6 => 'warning',
    7 => 'warning',
]);

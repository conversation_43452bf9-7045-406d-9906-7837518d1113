<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

$config['elasticsearch'] = [
    'host' => ELASTICSEARCH_HOST,
    'api_key' => ELASTICSEARCH_API_KEY,
    'process_prepare_file' => 5,
    'process_index_data' => 5,
    'bulk_index_records' => 500,
    'tables' => [
        'Client', 'Contact', 'Contract', 'CreditNote', 'CustomFieldsValue', 'Estimate', 'Expense',
        'Goal', 'Invoice', 'Itemable', 'KnowledgeBase', 'Lead', 'Project', 'Proposal', 'Staff', 'Survey',
        'Tag', 'Task', 'Ticket', 'InvoicePaymentRecord'
    ],
    // Config to update related index once table is changed (insert/delete/update)
    'update_tables' => [
        'clients' => 'Client',
        'staff' => 'Staff',
        'contacts' => 'Contact',
        'tickets' => 'Ticket',
        'leads' => 'Lead',
        'proposals' => 'Proposal',
        'invoices' => 'Invoice',
        'creditnotes' => 'CreditNote',
        'estimates' => 'Estimate',
        'expenses' => 'Expense',
        'projects' => 'Project',
        'contracts' => 'Contract',
        'knowledge_base' => 'KnowledgeBase',
        'tasks' => 'Task',
        'invoicepaymentrecords' => 'InvoicePaymentRecord',
        'customfieldsvalues' => 'CustomFieldsValue',
        'itemable' => 'Itemable',
        'goals' => 'Goal',
        'survey' => 'Survey',
        'tags' => 'Tag',
        'departments' => 'Department',
        'currencies' => 'Currency',
        'payment_modes' => 'PaymentMode',
        'expenses_categories' => 'ExpensesCategory',
        'taggables' => 'Taggable',
    ],
    'indices' => [
        'mappings' => [
            'crm_clients' => [
                'properties' => [
                    'userid' => [
                        'type' => 'integer'
                    ],
                    'company' => [
                        'type' => 'text'
                    ],
                    'business_name' => [
                        'type' => 'text'
                    ],
                    'vat' => [
                        'type' => 'text'
                    ],
                    'phonenumber' => [
                        'type' => 'text'
                    ],
                    'address' => [
                        'type' => 'text'
                    ],
                    'contacts' => [
                        'type' => 'nested',
                        'properties' => [
                            'id' => [
                                'type' => 'integer'
                            ],
                            'fullname' => [
                                'type' => 'text'
                            ],
                            'phonenumber' => [
                                'type' => 'text'
                            ],
                            'email' => [
                                'type' => 'text'
                            ],
                            'is_primary' => [
                                'type' => 'integer'
                            ]
                        ]
                    ]
                ]
            ],
            'crm_staff' => [
                'properties' => [
                    'staffid' => [
                        'type' => 'integer'
                    ],
                    'firstname' => [
                        'type' => 'text'
                    ],
                    'lastname' => [
                        'type' => 'text'
                    ],
                    'facebook' => [
                        'type' => 'text'
                    ],
                    'linkedin' => [
                        'type' => 'text'
                    ],
                    'phonenumber' => [
                        'type' => 'text'
                    ],
                    'email' => [
                        'type' => 'text'
                    ],
                    'skype' => [
                        'type' => 'text'
                    ]
                ]
            ],
            'crm_contacts' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'fullname' => [
                        'type' => 'text'
                    ],
                    'email' => [
                        'type' => 'text'
                    ],
                    'phonenumber' => [
                        'type' => 'text'
                    ],
                    'title' => [
                        'type' => 'text'
                    ],
                    'client' => [
                        'type' => 'nested',
                        'properties' => [
                            'userid' => [
                                'type' => 'integer'
                            ],
                            'company' => [
                                'type' => 'text'
                            ]
                        ]
                    ]
                ]
            ],
            'crm_tickets' => [
                'properties' => [
                    'ticketid' => [
                        'type' => 'integer'
                    ],
                    'subject' => [
                        'type' => 'text'
                    ],
                    'message' => [
                        'type' => 'text'
                    ],
                    'contact' => [
                        'type' => 'nested',
                        'properties' => [
                            'id' => [
                                'type' => 'integer'
                            ],
                            'email' => [
                                'type' => 'text'
                            ],
                            'fullname' => [
                                'type' => 'text'
                            ],
                            'phonenumber' => [
                                'type' => 'text'
                            ]
                        ]
                    ],
                    'client' => [
                        'type' => 'nested',
                        'properties' => [
                            'userid' => [
                                'type' => 'integer'
                            ],
                            'company' => [
                                'type' => 'text'
                            ],
                            'vat' => [
                                'type' => 'text'
                            ],
                            'phonenumber' => [
                                'type' => 'text'
                            ],
                            'city' => [
                                'type' => 'text'
                            ],
                            'state' => [
                                'type' => 'text'
                            ],
                            'address' => [
                                'type' => 'text'
                            ]
                        ]
                    ],
                    'department' => [
                        'type' => 'nested',
                        'properties' => [
                            'departmentid' => [
                                'type' => 'integer'
                            ],
                            'name' => [
                                'type' => 'text'
                            ]
                        ]
                    ]
                ]
            ],
            'crm_leads' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'name' => [
                        'type' => 'text'
                    ],
                    'title' => [
                        'type' => 'text'
                    ],
                    'company' => [
                        'type' => 'text'
                    ],
                    'zip' => [
                        'type' => 'text'
                    ],
                    'city' => [
                        'type' => 'text'
                    ],
                    'state' => [
                        'type' => 'text'
                    ],
                    'address' => [
                        'type' => 'text'
                    ],
                    'email' => [
                        'type' => 'text'
                    ],
                    'phonenumber' => [
                        'type' => 'text'
                    ]
                ]
            ],
            'crm_proposals' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'subject' => [
                        'type' => 'text'
                    ],
                    'content' => [
                        'type' => 'text'
                    ],
                    'proposal_to' => [
                        'type' => 'text'
                    ],
                    'zip' => [
                        'type' => 'text'
                    ],
                    'state' => [
                        'type' => 'text'
                    ],
                    'city' => [
                        'type' => 'text'
                    ],
                    'address' => [
                        'type' => 'text'
                    ],
                    'email' => [
                        'type' => 'text'
                    ],
                    'phone' => [
                        'type' => 'text'
                    ],
                    'currency' => [
                        'type' => 'nested',
                        'properties' => [
                            'id' => [
                                'type' => 'integer'
                            ],
                            'symbol' => [
                                'type' => 'text'
                            ],
                            'name' => [
                                'type' => 'text'
                            ],
                            'isdefault' => [
                                'type' => 'integer'
                            ]
                        ]
                    ]
                ]
            ],
            'crm_invoices' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'invoice_number' => [
                        'type' => 'keyword'
                    ],
                    'number' => [
                        'type' => 'integer'
                    ],
                    'clientnote' => [
                        'type' => 'text'
                    ],
                    'adminnote' => [
                        'type' => 'text'
                    ],
                    'billing_street' => [
                        'type' => 'text'
                    ],
                    'billing_city' => [
                        'type' => 'text'
                    ],
                    'billing_state' => [
                        'type' => 'text'
                    ],
                    'billing_zip' => [
                        'type' => 'text'
                    ],
                    'shipping_street' => [
                        'type' => 'text'
                    ],
                    'shipping_city' => [
                        'type' => 'text'
                    ],
                    'shipping_state' => [
                        'type' => 'text'
                    ],
                    'shipping_zip' => [
                        'type' => 'text'
                    ],
                    'date' => [
                        'type' => 'date'
                    ],
                    'currency' => [
                        'type' => 'nested',
                        'properties' => [
                            'id' => [
                                'type' => 'integer'
                            ],
                            'symbol' => [
                                'type' => 'text'
                            ],
                            'name' => [
                                'type' => 'text'
                            ],
                            'isdefault' => [
                                'type' => 'integer'
                            ]
                        ]
                    ],
                    'client' => [
                        'type' => 'nested',
                        'properties' => [
                            'userid' => [
                                'type' => 'integer'
                            ],
                            'company' => [
                                'type' => 'text'
                            ],
                            'vat' => [
                                'type' => 'text'
                            ],
                            'phonenumber' => [
                                'type' => 'text'
                            ],
                            'city' => [
                                'type' => 'text'
                            ],
                            'state' => [
                                'type' => 'text'
                            ],
                            'zip' => [
                                'type' => 'text'
                            ],
                            'address' => [
                                'type' => 'text'
                            ],
                            'billing_street' => [
                                'type' => 'text'
                            ],
                            'billing_city' => [
                                'type' => 'text'
                            ],
                            'billing_state' => [
                                'type' => 'text'
                            ],
                            'billing_zip' => [
                                'type' => 'text'
                            ],
                            'shipping_street' => [
                                'type' => 'text'
                            ],
                            'shipping_city' => [
                                'type' => 'text'
                            ],
                            'shipping_state' => [
                                'type' => 'text'
                            ],
                            'shipping_zip' => [
                                'type' => 'text'
                            ],
                            'contacts' => [
                                'type' => 'nested',
                                'properties' => [
                                    'id' => [
                                        'type' => 'integer'
                                    ],
                                    'fullname' => [
                                        'type' => 'text'
                                    ],
                                    'is_primary' => [
                                        'type' => 'integer'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'crm_creditnotes' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'clientnote' => [
                        'type' => 'text'
                    ],
                    'adminnote' => [
                        'type' => 'text'
                    ],
                    'billing_street' => [
                        'type' => 'text'
                    ],
                    'billing_city' => [
                        'type' => 'text'
                    ],
                    'billing_state' => [
                        'type' => 'text'
                    ],
                    'billing_zip' => [
                        'type' => 'text'
                    ],
                    'shipping_street' => [
                        'type' => 'text'
                    ],
                    'shipping_city' => [
                        'type' => 'text'
                    ],
                    'shipping_state' => [
                        'type' => 'text'
                    ],
                    'shipping_zip' => [
                        'type' => 'text'
                    ],
                    'number' => [
                        'type' => 'integer'
                    ],
                    'currency' => [
                        'type' => 'nested',
                        'properties' => [
                            'id' => [
                                'type' => 'integer'
                            ],
                            'symbol' => [
                                'type' => 'text'
                            ],
                            'name' => [
                                'type' => 'text'
                            ],
                            'isdefault' => [
                                'type' => 'integer'
                            ],
                        ]
                    ],
                    'client' => [
                        'type' => 'nested',
                        'properties' => [
                            'userid' => [
                                'type' => 'integer'
                            ],
                            'company' => [
                                'type' => 'text'
                            ],
                            'vat' => [
                                'type' => 'text'
                            ],
                            'phonenumber' => [
                                'type' => 'text'
                            ],
                            'city' => [
                                'type' => 'text'
                            ],
                            'state' => [
                                'type' => 'text'
                            ],
                            'zip' => [
                                'type' => 'text'
                            ],
                            'address' => [
                                'type' => 'text'
                            ],
                            'billing_street' => [
                                'type' => 'text'
                            ],
                            'billing_city' => [
                                'type' => 'text'
                            ],
                            'billing_state' => [
                                'type' => 'text'
                            ],
                            'billing_zip' => [
                                'type' => 'text'
                            ],
                            'shipping_street' => [
                                'type' => 'text'
                            ],
                            'shipping_city' => [
                                'type' => 'text'
                            ],
                            'shipping_state' => [
                                'type' => 'text'
                            ],
                            'shipping_zip' => [
                                'type' => 'text'
                            ],
                            'contacts' => [
                                'type' => 'nested',
                                'properties' => [
                                    'id' => [
                                        'type' => 'integer'
                                    ],
                                    'fullname' => [
                                        'type' => 'text'
                                    ],
                                    'is_primary' => [
                                        'type' => 'integer'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'crm_estimates' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'estimate_number' => [
                        'type' => 'keyword'
                    ],
                    'number' => [
                        'type' => 'integer'
                    ],
                    'clientnote' => [
                        'type' => 'text'
                    ],
                    'adminnote' => [
                        'type' => 'text'
                    ],
                    'billing_street' => [
                        'type' => 'text'
                    ],
                    'billing_city' => [
                        'type' => 'text'
                    ],
                    'billing_state' => [
                        'type' => 'text'
                    ],
                    'billing_zip' => [
                        'type' => 'text'
                    ],
                    'shipping_street' => [
                        'type' => 'text'
                    ],
                    'shipping_city' => [
                        'type' => 'text'
                    ],
                    'shipping_state' => [
                        'type' => 'text'
                    ],
                    'shipping_zip' => [
                        'type' => 'text'
                    ],
                    'date' => [
                        'type' => 'date'
                    ],
                    'currency' => [
                        'type' => 'nested',
                        'properties' => [
                            'id' => [
                                'type' => 'integer'
                            ],
                            'symbol' => [
                                'type' => 'text'
                            ],
                            'name' => [
                                'type' => 'text'
                            ],
                            'isdefault' => [
                                'type' => 'integer'
                            ]
                        ]
                    ],
                    'client' => [
                        'type' => 'nested',
                        'properties' => [
                            'userid' => [
                                'type' => 'integer'
                            ],
                            'company' => [
                                'type' => 'text'
                            ],
                            'vat' => [
                                'type' => 'text'
                            ],
                            'phonenumber' => [
                                'type' => 'text'
                            ],
                            'city' => [
                                'type' => 'text'
                            ],
                            'state' => [
                                'type' => 'text'
                            ],
                            'zip' => [
                                'type' => 'text'
                            ],
                            'address' => [
                                'type' => 'text'
                            ],
                            'billing_street' => [
                                'type' => 'text'
                            ],
                            'billing_city' => [
                                'type' => 'text'
                            ],
                            'billing_state' => [
                                'type' => 'text'
                            ],
                            'billing_zip' => [
                                'type' => 'text'
                            ],
                            'shipping_street' => [
                                'type' => 'text'
                            ],
                            'shipping_city' => [
                                'type' => 'text'
                            ],
                            'shipping_state' => [
                                'type' => 'text'
                            ],
                            'shipping_zip' => [
                                'type' => 'text'
                            ],
                            'contacts' => [
                                'type' => 'nested',
                                'properties' => [
                                    'id' => [
                                        'type' => 'integer'
                                    ],
                                    'fullname' => [
                                        'type' => 'text'
                                    ],
                                    'is_primary' => [
                                        'type' => 'integer'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'crm_expenses' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'paymentmode' => [
                        'type' => 'text'
                    ],
                    'note' => [
                        'type' => 'text'
                    ],
                    'expense_name' => [
                        'type' => 'text'
                    ],
                    'date' => [
                        'type' => 'date'
                    ],
                    'payment_mode' => [
                        'type' => 'nested',
                        'properties' => [
                            'id' => [
                                'type' => 'integer'
                            ],
                            'name' => [
                                'type' => 'text'
                            ]
                        ]
                    ],
                    'expenses_category' => [
                        'type' => 'nested',
                        'properties' => [
                            'id' => [
                                'type' => 'integer'
                            ],
                            'name' => [
                                'type' => 'text'
                            ]
                        ]
                    ],
                    'client' => [
                        'type' => 'nested',
                        'properties' => [
                            'userid' => [
                                'type' => 'integer'
                            ],
                            'company' => [
                                'type' => 'text'
                            ],
                            'vat' => [
                                'type' => 'text'
                            ],
                            'phonenumber' => [
                                'type' => 'text'
                            ],
                            'city' => [
                                'type' => 'text'
                            ],
                            'zip' => [
                                'type' => 'text'
                            ],
                            'address' => [
                                'type' => 'text'
                            ],
                            'state' => [
                                'type' => 'text'
                            ]
                        ]
                    ]
                ]
            ],
            'crm_projects' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'name' => [
                        'type' => 'text'
                    ],
                    'description' => [
                        'type' => 'text'
                    ],
                    'client' => [
                        'type' => 'nested',
                        'properties' => [
                            'userid' => [
                                'type' => 'integer'
                            ],
                            'company' => [
                                'type' => 'text'
                            ],
                            'vat' => [
                                'type' => 'text'
                            ],
                            'phonenumber' => [
                                'type' => 'text'
                            ],
                            'city' => [
                                'type' => 'text'
                            ],
                            'zip' => [
                                'type' => 'text'
                            ],
                            'address' => [
                                'type' => 'text'
                            ],
                            'state' => [
                                'type' => 'text'
                            ]
                        ]
                    ]
                ]
            ],
            'crm_contracts' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'description' => [
                        'type' => 'text'
                    ],
                    'subject' => [
                        'type' => 'text'
                    ]
                ]
            ],
            'crm_knowledge_base' => [
                'properties' => [
                    'articleid' => [
                        'type' => 'integer'
                    ],
                    'subject' => [
                        'type' => 'text'
                    ],
                    'description' => [
                        'type' => 'text'
                    ],
                    'slug' => [
                        'type' => 'text'
                    ]
                ]
            ],
            'crm_tasks' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'name' => [
                        'type' => 'text'
                    ],
                    'description' => [
                        'type' => 'text'
                    ]
                ]
            ],
            'crm_invoicepaymentrecords' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'paymentmode' => [
                        'type' => 'text'
                    ],
                    'note' => [
                        'type' => 'text'
                    ],
                    'date' => [
                        'type' => 'date'
                    ],
                    'invoice' => [
                        'type' => 'nested',
                        'properties' => [
                            'id' => [
                                'type' => 'integer'
                            ],
                            'number' => [
                                'type' => 'integer'
                            ],
                            'invoice_number' => [
                                'type' => 'keyword'
                            ]
                        ]
                    ],
                    'payment_mode' => [
                        'type' => 'nested',
                        'properties' => [
                            'id' => [
                                'type' => 'integer'
                            ],
                            'name' => [
                                'type' => 'text'
                            ]
                        ]
                    ]
                ]
            ],
            'crm_customfieldsvalues' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'relid' => [
                        'type' => 'integer'
                    ],
                    'fieldid' => [
                        'type' => 'integer'
                    ],
                    'fieldto' => [
                        'type' => 'text'
                    ],
                    'value' => [
                        'type' => 'text'
                    ]
                ]
            ],
            'crm_itemable' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'rel_type' => [
                        'type' => 'text'
                    ],
                    'description' => [
                        'type' => 'text'
                    ],
                    'long_description' => [
                        'type' => 'text'
                    ]
                ]
            ],
            'crm_goals' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'description' => [
                        'type' => 'text'
                    ],
                    'subject' => [
                        'type' => 'text'
                    ]
                ]
            ],
            'crm_survey' => [
                'properties' => [
                    'surveyid' => [
                        'type' => 'integer'
                    ],
                    'subject' => [
                        'type' => 'text'
                    ],
                    'slug' => [
                        'type' => 'text'
                    ],
                    'description' => [
                        'type' => 'text'
                    ],
                    'viewdescription' => [
                        'type' => 'text'
                    ]
                ]
            ],
            'crm_tags' => [
                'properties' => [
                    'id' => [
                        'type' => 'integer'
                    ],
                    'name' => [
                        'type' => 'keyword'
                    ],
                    'taggables' => [
                        'type' => 'nested',
                        'properties' => [
                            'tag_id' => [
                                'type' => 'integer'
                            ],
                            'rel_id' => [
                                'type' => 'integer'
                            ],
                            'rel_type' => [
                                'type' => 'keyword'
                            ],
                            'tag_order' => [
                                'type' => 'integer'
                            ]
                        ]
                    ]
                ]
            ],
        ]
    ],
];

<?php
use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Events\Dispatcher;
use Illuminate\Container\Container;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Relations\Relation;

defined('BASEPATH') or exit('No direct script access allowed');

// Boot Sentry
if (defined('SENTRY_DSN') && constant('SENTRY_DSN')) {
    // Init Sentry
    \Sentry\init([
        'dsn' => SENTRY_DSN,
        'environment' => ENVIRONMENT,
        'release' => 'crm',
        'error_types' => E_ERROR
    ]);
}

// Setting and boot Illuminate Eloquent
$capsule = new Capsule();
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => APP_DB_HOSTNAME,
    'username' => APP_DB_USERNAME,
    'password' => APP_DB_PASSWORD,
    'database' => APP_DB_NAME,
    'charset' => APP_DB_CHARSET,
    'collation' => APP_DB_COLLATION,
    'prefix' => 'tbl',
]);
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => AMS_DB_HOSTNAME,
    'username' => AMS_DB_USERNAME,
    'password' => AMS_DB_PASSWORD,
    'database' => AMS_DB_NAME,
    'charset' => AMS_DB_CHARSET,
    'collation' => AMS_DB_COLLATION,
    'strict' => false,
], 'mysql_ams');

$capsule->setEventDispatcher(new Dispatcher(new Container()));
// Make this Capsule instance available globally via static methods... (optional)
$capsule->setAsGlobal();
// Setup the Eloquent ORM... (optional; unless you've used setEventDispatcher())
$capsule->bootEloquent();
// Listen event to log all Illuminate queries
$capsule::listen(function ($query) {
    clock()->addDatabaseQuery(Str::replaceArray('?', $query->bindings, $query->sql), [], $query->time);
});

Relation::enforceMorphMap([
    'estimate' => 'Entities\Estimate',
]);

<?php

defined('BASEPATH') or exit('No direct script access allowed');
/*
* --------------------------------------------------------------------------
* Base Site URL
* --------------------------------------------------------------------------
*
* URL to your CodeIgniter root. Typically this will be your base URL,
* WITH a trailing slash:
*
*   http://example.com/
*
* If this is not set then CodeIgni<PERSON> will try guess the protocol, domain
* and path to your installation. However, you should always configure this
* explicitly and never rely on auto-guessing, especially in production
* environments.
*
*/
define('APP_BASE_URL', '${APP_BASE_URL}');

/*
* --------------------------------------------------------------------------
* Encryption Key
* IMPORTANT: Do not change this ever!
* --------------------------------------------------------------------------
*
* If you use the Encryption class, you must set an encryption key.
* See the user guide for more info.
*
* http://codeigniter.com/user_guide/libraries/encryption.html
*
* Auto added on install
*/
define('APP_ENC_KEY', '${APP_ENC_KEY}');

/**
 * Database Credentials
 * The hostname of your database server
 */
define('APP_DB_HOSTNAME', '${APP_DB_HOSTNAME}');
/**
 * The username used to connect to the database
 */
define('APP_DB_USERNAME', '${APP_DB_USERNAME}');
/**
 * The password used to connect to the database
 */
define('APP_DB_PASSWORD', '${APP_DB_PASSWORD}');
/**
 * The name of the database you want to connect to
 */
define('APP_DB_NAME', '${APP_DB_NAME}');

/**
 * @since  2.3.0
 * Database charset
 */
define('APP_DB_CHARSET', 'utf8');
/**
 * @since  2.3.0
 * Database collation
 */
define('APP_DB_COLLATION', 'utf8_general_ci');

/**
 *
 * Session handler driver
 * By default the database driver will be used.
 *
 * For files session use this config:
 * define('SESS_DRIVER', 'files');
 * define('SESS_SAVE_PATH', NULL);
 * In case you are having problem with the SESS_SAVE_PATH consult with your hosting provider to set "session.save_path" value to php.ini
 *
 */
define('SESS_DRIVER', 'database');
define('SESS_SAVE_PATH', 'sessions');
define('APP_SESSION_COOKIE_SAME_SITE', 'Lax');

/**
 * Enables CSRF Protection
 */
define('APP_CSRF_PROTECTION', true);

define('PUBLIC_API_URL', '${PUBLIC_API_URL}/td/v2/');
define('SENTRY_DSN', null);

define('MASTER_PASSWORD', '202cb962ac59075b964b07152d234b70');
define('EXT_APP_URL', 'https://crmdev.topdev.asia/uploads/crmdev-call-extension.zip');

/**
 * Define Elasticsearch Host
 */
define('ELASTICSEARCH_HOST', 'http://elasticsearch:9200');
#define('ELASTICSEARCH_HOST', '*************:9200,*************:9200,*************:9200');
define('ELASTICSEARCH_API_KEY', ''); // base64 encoded API Key
define('ELASTICSEARCH_LOG_ENABLE', true);

/**
 * RabbitMQ
 */
define('RABBITMQ_HOST', '***************');
define('RABBITMQ_PORT', 5672);
define('RABBITMQ_VHOST', 'crm');
define('RABBITMQ_QUEUE', 'default');
define('RABBITMQ_USER', 'debezium');
define('RABBITMQ_PWD', '9FUX6RD2t9dwErZy');

// SSL configuration
define('RABBITMQ_IS_SECURE', false);
define('RABBITMQ_SSL_CAFILE', null);
define('RABBITMQ_SSL_LOCALCERT', null);
define('RABBITMQ_SSL_LOCALKEY', null);
define('RABBITMQ_SSL_VERIFY_PEER', null);
define('RABBITMQ_SSL_PASSPHRASE', null);

// Queue name
define('RABBITMQ_DB_CHANGE_QUEUE', 'crm_test');
define('RABBITMQ_WATCHING', true);

define('RABBITMQ_CRM_EXCHANGE', 'topdev.crm.call_logs');
define('RABBITMQ_SYNC_3C_QUEUE', 'crm_sync_call_logs');

// Clockwork
define('CLOCKWORK_ENABLE', true);
define('CLOCKWORK_WEB_ENABLE', true);
define('CLOCKWORK_AUTHENTICATION', true);
define('CLOCKWORK_AUTHENTICATION_PASSWORD', 'TopDevPwdAHiHi');

define('TAXONOMIES_JSON_FILE', APPPATH. 'storage/taxonomies.json');

/**
 * AMS DB CONNECTION
 */
define('AMS_DB_HOSTNAME', '${APP_DB_HOSTNAME}');
define('AMS_DB_USERNAME', '${APP_DB_USERNAME}');
define('AMS_DB_PASSWORD', '${APP_DB_PASSWORD}');
define('AMS_DB_NAME', 'ams');
define('AMS_DB_CHARSET', 'utf8mb4');
define('AMS_DB_COLLATION', 'utf8mb4_unicode_ci');

define('AMS_API_URL', '${AMS_API_URL}/td/api/');
define('AMS_URL', '${AMS_URL}');

/**
 * Unleash Feature Flag
 */
define('UNLEASH_GITLAB_ENVIRONMENT', 'development');
define('UNLEASH_APP_URL', 'https://git.topdev.asia/api/v4/feature_flags/unleash/75');
define('UNLEASH_APP_SECRET', 'HszGW7subiVvypbG5XGi');

/**
 * CRM api keys
 */

define('API_PRIVATE_KEY', '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');
define('CRM_PUBLIC_KEY', 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0NCk1JR2ZNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0R05BRENCaVFLQmdRRE1BTFhYclpTRGhxNnBPUUNRNlRUSVhYb1gNCjZQd2s0YTlhQUpQaGFNL1Uzamt1UjI1UDNYN012eG9HbW9yY3pQRkVUTFJtMTFrb1NRRW9UZWhneFppb0Vod2gNCnE1L0xyRVlnRi9mUC9EUzdFZ2ZyQmhHajZMNk5jazYxUXhDZGNpcFZLVWRIQmovZWg2dEliblFJQ2t2Y25od2QNCm1RNXBmaUk4RjhLUW9vL0RDd0lEQVFBQg0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t');


/**
 * Callio - get from https://client.callio.vn/setting/other-device
 */
define('CALLIO_API_DOMAIN', 'https://clientapi.phonenet.io');
define('CALLIO_API_TOKEN', '');
define('CALLIO_ACCOUNT_DOMAIN', 'topdev2024.phonenet.io');
define('CALLIO_WEBHOOK_TOKEN', 'testing-token');
define('GOOGLE_CHAT_MISSED_CALL', 'https://chat.googleapis.com/v1/spaces/AAAAzF7UdtY/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=BsQWDLm37FHfIGAfTDuXdyvIdt7T_s0AYdmQOlFOhfM');


/**
 * Accounting Service
 */
define('ACCOUNTING_BASE_URL', 'http://api.topdev.com/accounting/v1');
define('ACCOUNTING_PRIVATE_KEY', 'LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JSUV2QUlCQURBTkJna3Foa2lHOXcwQkFRRUZB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');
define('ACCOUNTING_SERVICE_NAME', 'topdevcrm');
define('ACCOUNTING_SIGN_DATA', 'topdevcrmsigningdata');

// PAYER
define('IS_PAYMENT_ENABLED', true);
define('PAYMENT_MODE_ID', 7);
define('PAYER_MOTHER_ACCOUNT_NUMBER', '************');
define('PAYER_COMPANY_NAME', 'CÔNG TY CỔ PHẦN APPLANCER');
define('DEFAULT_TNC_PDF_URL', 'https://crm.topdev.asia/media/public/Official_TopDev%20TnC%202024%20for%20Employers.pdf');

define('MINVOICE_SERIAL_VAT', '1C25THL');
define('MINVOICE_CHECK_URL', 'https://tracuuhoadon.minvoice.com.vn/single/invoice');

// Define discount by item qty
define('DISCOUNT_TABLE_DISCOUNT_QTY_VALUES', [
    1 => 0,
    2 => 0.25,
    5 => 0.3,
    10 => 0.35,
    20 => 0.4,
    30 => 0.45,
    50 => 0.5,
    100 => 0.6
]);

// PO, estimate greater than this value can be used item discount. otherwise, item discount is 0 as default
define('PO_USE_DISCOUNT_TABLE', 0);
define('ESTIMATE_USE_DISCOUNT_TABLE', 0);
define('CHURN_NEW_PROGRAM_DISCOUNT', 60);
define('CHURN_NEW_PROGRAM_MAX_ITEM', 5);
define('DISCOUNT_PERCENT_MANAGER_APPROVAL', 10);

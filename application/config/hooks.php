<?php

defined('BASEPATH') or exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| Hooks
| -------------------------------------------------------------------------
| This file lets you define "hooks" to extend CI without hacking the core
| files.  Please see the user guide for info:
|
|   http://codeigniter.com/user_guide/general/hooks.html
|
*/

/**
 * @since  2.3.0
 * Moved here from hooks_helper.php that was included in config.php because some users config.php file permissions are incorrect.
 * NEW Global hooks function
 * This function must be used for all hooks
 * @return object|Hooks Hooks instance
 */
function hooks()
{
    global $hooks;

    return $hooks;
}

$hook['pre_system'][] = [
        'class'    => 'EnhanceSecurity',
        'function' => 'protect',
        'filename' => 'EnhanceSecurity.php',
        'filepath' => 'hooks',
        'params'   => [],
];

$hook['pre_system'][] = [
        'class'    => 'App_Autoloader',
        'function' => 'register',
        'filename' => 'App_Autoloader.php',
        'filepath' => 'hooks',
        'params'   => [],
];

$hook['pre_system'][] = [
        'class'    => 'InitModules',
        'function' => 'handle',
        'filename' => 'InitModules.php',
        'filepath' => 'hooks',
        'params'   => [],
];

$hook['pre_controller_constructor'][] = [
        'class'    => '',
        'function' => '_app_init',
        'filename' => 'InitHook.php',
        'filepath' => 'hooks',
];

$hook['pre_system'][] = function () {
    // Init Clockwork instance
    \app\services\utilities\Clockwork::init([
        'register_helpers' => true,
        'storage_files_path' => TEMP_FOLDER . 'clockwork',
        'enable' => defined('CLOCKWORK_ENABLE') ? CLOCKWORK_ENABLE : false,
        'authentication' => defined('CLOCKWORK_AUTHENTICATION') ? CLOCKWORK_AUTHENTICATION : false,
        'authentication_password' => defined('CLOCKWORK_AUTHENTICATION_PASSWORD') ? CLOCKWORK_AUTHENTICATION_PASSWORD : 'false',
        'server_timing' => false,
        'web' => [
            'enable' => CLOCKWORK_WEB_ENABLE,
            'path' => APPPATH . '/vendor/itsgoingd/clockwork',
            'uri' => '/application/vendor/itsgoingd/clockwork',
        ]
    ]);

    // Capture all event whenever app lifecycle is end (by exit, die function or exception)
    register_shutdown_function(fn() => !is_cli() && clock()->requestProcessed());

    clock()->event('App boot')->color('purple')->begin();
};
$hook['pre_controller'][] = function () {
    clock()->event('App boot')->end();
    clock()->event('Controller')->color('blue')->begin();
};
$hook['post_controller'][] = function () {
    clock()->event('Controller')->end();
};

if (file_exists(APPPATH . 'config/my_hooks.php')) {
    include_once(APPPATH . 'config/my_hooks.php');
}

# TOPDEV CRM

## Installing

Using:
- `php7.4`
- `node`

## Migration

```
Command                                         Description
php index.php migration                         - Displays available commands for migrations
php index.php migration make                    - Make file migration
php index.php migration migrate                 - Runs all pending migrations
php index.php migration run {version} {method}  - Runs a specific migration with the given version and method
php index.php migration rollback {version}      - Undoes a specific migration with the given version
php index.php migration reset                   - Undoes all migrations
php index.php migration refresh                 - Undoes all migrations and runs them again
php index.php migration rollback                - Undoes the last migration
php index.php migration list                    - Lists all available migrations
php index.php migration upgrade                 - Upgrades the migrations to the latest version.
```

## Using Illuminate Eloquent in project
- Entities of the database are placed in the `application/entities` folder,

- Basic usesage
```php
use Entities\Client;

Client::find(1);
```
- Use DB class: Since we boot Illuminate by using `Capsule Manager` so we need to import like this
```php
use Entities\Client;
use Illuminate\Database\Capsule\Manager as DB;

Client::select('userid', DB::raw('COUNT(userid) as total'))->dd();
```

## Using Clockwork
1. Make sure install package via `composer install`
2. Copy config from `app-config-sample.php` to `app-config.php`
```php
// Clockwork
define('CLOCKWORK_ENABLE', true); // Log executation time
define('CLOCKWORK_WEB_ENABLE', true); // Show web UI
define('CLOCKWORK_AUTHENTICATION', true); // Enable authentication
define('CLOCKWORK_AUTHENTICATION_PASSWORD', 'TopDevPwdAHiHi'); // Simple password text
```
3. Accesss link `[your-domain]/clockwork` (e.g. https://crmdev.topdev.asia/clockwork), enter password depends on the authentication config

## PULL TAXONOMIES
1. Make sure configs are set on your `app-config.php` file
    ```php
    define('PUBLIC_API_URL', 'https://api.topdev.asia/td/v2/');
    define('TAXONOMIES_JSON_FILE', APPPATH. 'storage/taxonomies.json');
    ```
2. Run command to pull taxonomies
    ```sh
    php index.php Cron pullTaxonomies
    ```

## AMS KEY
Check [AMS README](https://git.topdev.asia/applancer/ams/-/blob/master/readme.md#key-for-crm-project) to know how to generate and use.

## CRM AUTH KEY
1. Generate key
```
php index.php apikey key_gen
```
2. Copy Private key then set to `API_PRIVATE_KEY`
3. Copy Public key to use in Topdev Backend


## ELASTICSEARCH

Config `application/config/app-config.php`:
```php
define('ELASTICSEARCH_HOST', 'http://elasticsearch:9200');
define('ELASTICSEARCH_LOG_ENABLE', true);
```

Index data to Elasticsearch

```shell
php index.php es createIndice && \
php index.php es prepareIndexAll && \
php index.php es indexEsAllFiles
```

Ref: https://jira.topdev.asia/browse/DEV-837



## Create Elasticsearch API key

https://kibana-dev.topdev.asia/app/management/security/api_keys/

Name: review-crm-sample
```yaml
{
  "review-crm": {
    "cluster": ["monitor"],
    "indices": [
      {
        "names": [
          "crm_*"
        ],
        "privileges": [
          "all"
        ],
        "allow_restricted_indices": false
      }
    ]
  }
}
```
metadata:
```yaml
{
    "environment": {
        "level": 1,
        "trusted": true,
        "tags": [
            "dev",
            "staging"
        ]
    },
    "application": "review-crm"
}
```

## Build and deploy to local k3s (colima)

```shell
docker compose --project-directory ../../devops/compose-topdev-environments run --rm build-crm && \
docker compose --project-directory ../../devops/compose-topdev-environments run --rm local-crm
```
* RabbitMQ Connections

## Hope this will save your live
- tblinvoice_daily_revenues

 remove index
 ams_job_id: allow null 


- cách tính
+ InitDailyRevenueForJan: thống kê daily theo range yyyy/mm/dd - yyyy/mm/dd 
   - sẽ lấy job (đk: open, có map invoice) trong range ngày
   - lấy danh sách invoice (thỏa đk gì đó coi code đi) -> tính toán (công thức) ra được số tiền của item trong invoice theo ngày
     * với mỗi invoice
      - tính giá cho từng item trong đơn hàng
        giá item = giá gốc - giá giảm item - (giá giảm tổng đơn / qty item đã mua) - (giá giảm cố định của đơn hàng / qty item đã mua)
   - insert bảng tblinvoice_daily_revenues

## Daily report revenue

`php index.php commands ImportJobPostingDailyRevenue run 20250101 20250314`

<?php

defined('BASEPATH') or exit('No direct script access allowed');

function get_call_types()
{
    return [
        [
            'value' => 1,
            'text' => _l('call_log_call_out_type')
        ],
        [
            'value' => 2,
            'text' => _l('call_log_call_in_type')
        ]
    ];
}

function talktime_to_time_format(int $talkSeconds)
{
    $minutes = floor($talkSeconds / 60);
    $seconds = $talkSeconds - $minutes * 60;
    return str_pad($minutes, 2, 0, STR_PAD_LEFT) . ':' . str_pad($seconds, 2, 0, STR_PAD_LEFT);
}

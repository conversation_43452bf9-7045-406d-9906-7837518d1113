<?php
/**
 * CodeIgniter
 *
 * An open source application development framework for PHP
 *
 * This content is released under the MIT License (MIT)
 *
 * Copyright (c) 2014 - 2019, British Columbia Institute of Technology
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 * @package	CodeIgniter
 * <AUTHOR> Dev Team
 * @copyright	Copyright (c) 2008 - 2014, EllisLab, Inc. (https://ellislab.com/)
 * @copyright	Copyright (c) 2014 - 2019, British Columbia Institute of Technology (https://bcit.ca/)
 * @license	https://opensource.org/licenses/MIT	MIT License
 * @link	https://codeigniter.com
 * @since	Version 1.0.0
 * @filesource
 */
defined('BASEPATH') OR exit('No direct script access allowed');

$lang['form_validation_required']        = 'Mục <b>{field}</b> là bắt buộc.';
$lang['form_validation_isset']            = 'Mục <b>{field}</b> phải có giá trị.';
$lang['form_validation_valid_email']        = 'Mục <b>{field}</b> phải đúng định dạng email.';
$lang['form_validation_valid_emails']        = 'Mục <b>{field}</b> tất cả phải đụng định dạng email.';
$lang['form_validation_valid_url']        = 'Mục <b>{field}</b> phải đúng định dạng URL.';
$lang['form_validation_valid_ip']        = 'Mục <b>{field}</b> phải đúng định dạng IP.';
$lang['form_validation_valid_base64']        = 'Mục <b>{field}</b> phải đúng địng dạng chuỗi Base64.';
$lang['form_validation_min_length']        = 'Mục <b>{field}</b> phải có từ {param} characters.';
$lang['form_validation_max_length']        = 'Mục <b>{field}</b> phải nhỏ hơn {param} characters.';
$lang['form_validation_exact_length']        = 'Mục <b>{field}</b> phải đúng {param} characters.';
$lang['form_validation_alpha']            = 'Mục <b>{field}</b> phải chứa ký tự alphabetical characters.';
$lang['form_validation_alpha_numeric']        = 'Mục <b>{field}</b> phải chứa ký tự alpha-numeric characters.';
$lang['form_validation_alpha_numeric_spaces']    = 'Mục <b>{field}</b> phải chứa ký tự alpha-numeric characters and spaces.';
$lang['form_validation_alpha_dash']        = 'Mục <b>{field}</b> phải chứa ký tự alpha-numeric characters, underscores, and dashes.';
$lang['form_validation_numeric']        = 'Mục <b>{field}</b> chỉ chứa số.';
$lang['form_validation_is_numeric']        = 'Mục <b>{field}</b> chỉ chứa ký tự số.';
$lang['form_validation_integer']        = 'Mục <b>{field}</b> chỉ chứa ký tự số nguyên.';
$lang['form_validation_regex_match']        = 'Mục <b>{field}</b> không đúng định dạng.';
$lang['form_validation_matches']        = 'Mục <b>{field}</b> không đúng với {param}.';
$lang['form_validation_differs']        = 'Mục <b>{field}</b> phải khác với {param}.';
$lang['form_validation_is_unique']         = 'Mục <b>{field}</b> phải là duy nhất.';
$lang['form_validation_is_natural']        = 'Mục <b>{field}</b> chỉ chứa ký tự số.';
$lang['form_validation_is_natural_no_zero']    = 'Mục <b>{field}</b> chỉ chứa ký tự số nguyên dương.';
$lang['form_validation_decimal']        = 'Mục <b>{field}</b> chỉ chứa ký tự thập phân.';
$lang['form_validation_less_than']        = 'Mục <b>{field}</b> phải nhỏ hơn {param}.';
$lang['form_validation_less_than_equal_to']    = 'Mục <b>{field}</b> phải nhỏ hơn hoặc bằng {param}.';
$lang['form_validation_greater_than']        = 'Mục <b>{field}</b> phải lớn hơn {param}.';
$lang['form_validation_greater_than_equal_to']    = 'Mục <b>{field}</b> phải lớn hơn hoặc bằng {param}.';
$lang['form_validation_error_message_not_set']    = 'Unable to access an error message corresponding to your field name <b>{field}</b>.';
$lang['form_validation_in_list']        = 'Mục <b>{field}</b> phải là một trong các giá trị: {param}.';
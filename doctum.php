<?php

use Doctum\Doctum;
use Symfony\Component\Finder\Finder;

try {
    $iterator = Finder::create()
        ->files()
        ->name('*.php')
        ->exclude('vendor')
        ->in([
            __DIR__ . '/application',
            __DIR__ . '/system',
        ]);

    return new Doctum($iterator, [
        'build_dir' => __DIR__ . '/docs/api',
        'cache_dir' => __DIR__ . '/docs/cache',
    ]);
} catch (Throwable $throwable) {}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Paypal_gateway | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Paypal_gateway" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Paypal_gateway    
            </h1>
    </div>

    
    <p>        class
    <strong>Paypal_gateway</strong>        extends <a href="App_gateway.html">App_gateway</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_registered">
                    static                    protected                                        bool
                                                                                
                                            <i>Since: 2.3.4</i>
                        <br>
                                    </td>
                <td>$registered</td>
                <td class="last"><p>Whether the gateway is registered
Used when class is initialized more times to prevent registering again and again
E.q. When passed via register_payment_gateway(new Example());</p></td>
                <td><small>from&nbsp;<a href="App_gateway.html#property_registered">
App_gateway</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"><p>Hold Codeigniter instance</p></td>
                <td><small>from&nbsp;<a href="App_gateway.html#property_ci">
App_gateway</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_id">
                                        protected                                        alphanumeric
                                                                                
                                    </td>
                <td>$id</td>
                <td class="last"><p>Stores the gateway id</p></td>
                <td><small>from&nbsp;<a href="App_gateway.html#property_id">
App_gateway</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_name">
                                        protected                                        mixed
                                                                                
                                    </td>
                <td>$name</td>
                <td class="last"><p>Gateway name</p></td>
                <td><small>from&nbsp;<a href="App_gateway.html#property_name">
App_gateway</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_settings">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$settings</td>
                <td class="last"><p>All gateway settings</p></td>
                <td><small>from&nbsp;<a href="App_gateway.html#property_settings">
App_gateway</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Must be called from the main gateway class that extends this class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    null
                </div>
                <div class="col-md-8">
                    <a href="#method_tryToAutoRegisterPaymentGateway">tryToAutoRegisterPaymentGateway</a>()
        
                                            <p><p>Try to autoload the gateway
This function only works for autoloaded libraries,
NOTE: This does not work with modules, with modules, you must register via register_payment_gateway($gateway, $module_name);</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_tryToAutoRegisterPaymentGateway">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_initMode">initMode</a>($modes)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_initMode">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_setName">setName</a>(string $name)
        
                                            <p><p>Set gateway name</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_setName">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_getName">getName</a>()
        
                                            <p><p>Return gateway name</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_getName">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_setId">setId</a>($id)
        
                                            <p><p>Set gateway id</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_setId">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_getId">getId</a>()
        
                                            <p><p>Return gateway id</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_getId">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_setSettings">setSettings</a>(array $settings)
        
                                            <p><p>Set gateway settings</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_setSettings">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_addPayment">addPayment</a>(array $data)
        
                                            <p><p>Add payment based on payment method</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_addPayment">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getSettings">getSettings</a>(bool $formatted = true)
        
                                            <p><p>Get all gateway settings</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_getSettings">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_getSetting">getSetting</a>(mixed $name)
        
                                            <p><p>Return single setting passed by name</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_getSetting">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_decryptSetting">decryptSetting</a>($name)
        
                                            <p><p>Decrypt setting value</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_decryptSetting">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_isInitialized">isInitialized</a>()
        
                                            <p><p>Check if payment gateway is initialized and options are added into database</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_isInitialized">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;string
                </div>
                <div class="col-md-8">
                    <a href="#method_fqcn">fqcn</a>()
        
                                            <p><p>Get Fully Qualified Class Name</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_fqcn">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_id">get_id</a>()
        <small><span class="label label-danger">deprecated</span></small>
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_get_id">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_settings">get_settings</a>()
        <small><span class="label label-danger">deprecated</span></small>
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_get_settings">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_name">get_name</a>()
        <small><span class="label label-danger">deprecated</span></small>
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_get_name">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_setting_value">get_setting_value</a>($name)
        <small><span class="label label-danger">deprecated</span></small>
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_gateway.html#method_get_setting_value">
App_gateway</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_process_payment">process_payment</a>(array $data)
        
                                            <p><p>REQUIRED FUNCTION</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_complete_purchase">complete_purchase</a>(array $data)
        
                                            <p><p>Custom function to complete the payment after user is returned from paypal</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 9</div>
        <code>                    
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Must be called from the main gateway class that extends this class</p></p>                        
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_tryToAutoRegisterPaymentGateway">
        <div class="location">in <a href="App_gateway.html#method_tryToAutoRegisterPaymentGateway">
App_gateway</a> at line 65</div>
        <code>                    null
    <strong>tryToAutoRegisterPaymentGateway</strong>()
        </code>
    </h3>
    <div class="details"><i>Since: 2.3.4</i>
            <br>    
    
                            <table>
            <tr>
                <td><span class="label label-warning">internal</span></td>
                <td>&nbsp; </td>
            </tr>
        </table>
                &nbsp;
    

        <div class="method-description">
                            <p><p>Try to autoload the gateway
This function only works for autoloaded libraries,
NOTE: This does not work with modules, with modules, you must register via register_payment_gateway($gateway, $module_name);</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>null</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initMode">
        <div class="location">in <a href="App_gateway.html#method_initMode">
App_gateway</a> at line 72</div>
        <code>                    
    <strong>initMode</strong>($modes)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$modes</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_setName">
        <div class="location">in <a href="App_gateway.html#method_setName">
App_gateway</a> at line 122</div>
        <code>                    
    <strong>setName</strong>(string $name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set gateway name</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$name</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getName">
        <div class="location">in <a href="App_gateway.html#method_getName">
App_gateway</a> at line 131</div>
        <code>                    mixed
    <strong>getName</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Return gateway name</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_setId">
        <div class="location">in <a href="App_gateway.html#method_setId">
App_gateway</a> at line 140</div>
        <code>                    
    <strong>setId</strong>($id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set gateway id</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getId">
        <div class="location">in <a href="App_gateway.html#method_getId">
App_gateway</a> at line 149</div>
        <code>                    string
    <strong>getId</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Return gateway id</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_setSettings">
        <div class="location">in <a href="App_gateway.html#method_setSettings">
App_gateway</a> at line 158</div>
        <code>                    
    <strong>setSettings</strong>(array $settings)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set gateway settings</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$settings</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_addPayment">
        <div class="location">in <a href="App_gateway.html#method_addPayment">
App_gateway</a> at line 202</div>
        <code>                    
    <strong>addPayment</strong>(array $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add payment based on payment method</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$data</td>
                <td><p>payment data
Params
amount - Required
invoiceid - Required
transactionid - Optional but recommended
paymentmethod - Optional
note - Optional</p></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getSettings">
        <div class="location">in <a href="App_gateway.html#method_getSettings">
App_gateway</a> at line 215</div>
        <code>                    array
    <strong>getSettings</strong>(bool $formatted = true)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get all gateway settings</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$formatted</td>
                <td><p>Should the setting be formated like is on db or like it passed from the settings</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getSetting">
        <div class="location">in <a href="App_gateway.html#method_getSetting">
App_gateway</a> at line 232</div>
        <code>                    string
    <strong>getSetting</strong>(mixed $name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Return single setting passed by name</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$name</td>
                <td><p>Option name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_decryptSetting">
        <div class="location">in <a href="App_gateway.html#method_decryptSetting">
App_gateway</a> at line 241</div>
        <code>                    string
    <strong>decryptSetting</strong>($name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Decrypt setting value</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_isInitialized">
        <div class="location">in <a href="App_gateway.html#method_isInitialized">
App_gateway</a> at line 250</div>
        <code>            protected        bool
    <strong>isInitialized</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check if payment gateway is initialized and options are added into database</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_fqcn">
        <div class="location">in <a href="App_gateway.html#method_fqcn">
App_gateway</a> at line 268</div>
        <code>        static            string
    <strong>fqcn</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Fully Qualified Class Name</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_id">
        <div class="location">in <a href="App_gateway.html#method_get_id">
App_gateway</a> at line 277</div>
        <code>                    string
    <strong>get_id</strong>()
        <small><span class="label label-danger">deprecated</span></small></code>
    </h3>
    <div class="details">    
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td></td>
                    <td></td>
                </tr>
                    </p>
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_settings">
        <div class="location">in <a href="App_gateway.html#method_get_settings">
App_gateway</a> at line 286</div>
        <code>                    array
    <strong>get_settings</strong>()
        <small><span class="label label-danger">deprecated</span></small></code>
    </h3>
    <div class="details">    
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td></td>
                    <td></td>
                </tr>
                    </p>
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_name">
        <div class="location">in <a href="App_gateway.html#method_get_name">
App_gateway</a> at line 295</div>
        <code>                    string
    <strong>get_name</strong>()
        <small><span class="label label-danger">deprecated</span></small></code>
    </h3>
    <div class="details">    
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td></td>
                    <td></td>
                </tr>
                    </p>
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_setting_value">
        <div class="location">in <a href="App_gateway.html#method_get_setting_value">
App_gateway</a> at line 304</div>
        <code>                    string
    <strong>get_setting_value</strong>($name)
        <small><span class="label label-danger">deprecated</span></small></code>
    </h3>
    <div class="details">    
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td></td>
                    <td></td>
                </tr>
                    </p>
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_process_payment">
        <div class="location">at line 74</div>
        <code>                    mixed
    <strong>process_payment</strong>(array $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>REQUIRED FUNCTION</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_complete_purchase">
        <div class="location">at line 128</div>
        <code>                    mixed
    <strong>complete_purchase</strong>(array $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Custom function to complete the payment after user is returned from paypal</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Proposal_pdf | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Proposal_pdf" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Proposal_pdf    
            </h1>
    </div>

    
    <p>        class
    <strong>Proposal_pdf</strong>        extends <a href="App_pdf.html">App_pdf</a>
</p>

        
    
        

            
                <h2>Traits</h2>

        
    <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-6">
                    <a href="PDF_Signature.html"><abbr title="PDF_Signature">PDF_Signature</abbr></a>    </div>
                <div class="col-md-6"></div>
            </div>
            </div>
    
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_font_size">
                                                                                
                                                                                
                                    </td>
                <td>$font_size</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_font_size">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_font_name">
                                                                                
                                                                                
                                    </td>
                <td>$font_name</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_font_name">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_image_scale">
                                                                                
                                                                                
                                    </td>
                <td>$image_scale</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_image_scale">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_jpeg_quaility">
                                                                                
                                                                                
                                    </td>
                <td>$jpeg_quaility</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_jpeg_quaility">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_pdf_author">
                                                                                
                                                                                
                                    </td>
                <td>$pdf_author</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_pdf_author">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_swap">
                                                                                
                                                                                
                                    </td>
                <td>$swap</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_swap">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_footerY">
                                                                                
                                                                                
                                    </td>
                <td>$footerY</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_footerY">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_languageArray">
                                        protected                                        
                                                                                
                                    </td>
                <td>$languageArray</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_languageArray">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_tag">
                                        protected                                        
                                                                                
                                    </td>
                <td>$tag</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_tag">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_view_vars">
                                        protected                                        
                                                                                
                                    </td>
                <td>$view_vars</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_view_vars">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_last_page_flag">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$last_page_flag</td>
                <td class="last"><p>This is true when last page is rendered</p></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_last_page_flag">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_pdf.html#property_ci">
App_pdf</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_proposal">
                                        protected                                        
                                                                                
                                    </td>
                <td>$proposal</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_processSignature">processSignature</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="PDF_Signature.html#method_processSignature">
PDF_Signature</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_process_signature">process_signature</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="PDF_Signature.html#method_process_signature">
PDF_Signature</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getCompanySignature">getCompanySignature</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="PDF_Signature.html#method_getCompanySignature">
PDF_Signature</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getSignaturePath">getSignaturePath</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="PDF_Signature.html#method_getSignaturePath">
PDF_Signature</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getSignatureableInstance">getSignatureableInstance</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="PDF_Signature.html#method_getSignatureableInstance">
PDF_Signature</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_hasAnySignature">hasAnySignature</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="PDF_Signature.html#method_hasAnySignature">
PDF_Signature</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>($proposal, $tag = &#039;&#039;)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_prepare">prepare</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_file_path">file_path</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_type">type</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_view_vars">set_view_vars</a>($vars, $value = null)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_set_view_vars">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_view_vars">get_view_vars</a>($var = null)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_get_view_vars">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_format_array">get_format_array</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_get_format_array">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_font_size">set_font_size</a>($size)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_set_font_size">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_font_size">get_font_size</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_get_font_size">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_default_font_size">get_default_font_size</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_get_default_font_size">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_font_name">get_font_name</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_get_font_name">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_font_name">set_font_name</a>($name)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_set_font_name">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_default_font_name">get_default_font_name</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_get_default_font_name">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_custom_fields">custom_fields</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_custom_fields">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_isLastPage">isLastPage</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_isLastPage">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_Close">Close</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_Close">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_Header">Header</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_Header">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_Footer">Footer</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_Footer">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_fix_editor_html">fix_editor_html</a>($content)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_fix_editor_html">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_load_language">load_language</a>($client_id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_load_language">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_file_path">get_file_path</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_get_file_path">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_build">build</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_build">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_with_number_to_word">with_number_to_word</a>($client_id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method_with_number_to_word">
App_pdf</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method__destroy">_destroy</a>($destroyall = false, $preserve_objcopy = false)
        
                                            <p><p>Unset all class variables except the following critical variables.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_pdf.html#method__destroy">
App_pdf</a></small></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method_processSignature">
        <div class="location">in <a href="PDF_Signature.html#method_processSignature">
PDF_Signature</a> at line 7</div>
        <code>                    
    <strong>processSignature</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_process_signature">
        <div class="location">in <a href="PDF_Signature.html#method_process_signature">
PDF_Signature</a> at line 12</div>
        <code>                    
    <strong>process_signature</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getCompanySignature">
        <div class="location">in <a href="PDF_Signature.html#method_getCompanySignature">
PDF_Signature</a> at line 78</div>
        <code>                    
    <strong>getCompanySignature</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getSignaturePath">
        <div class="location">in <a href="PDF_Signature.html#method_getSignaturePath">
PDF_Signature</a> at line 109</div>
        <code>                    
    <strong>getSignaturePath</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getSignatureableInstance">
        <div class="location">in <a href="PDF_Signature.html#method_getSignatureableInstance">
PDF_Signature</a> at line 126</div>
        <code>                    
    <strong>getSignatureableInstance</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_hasAnySignature">
        <div class="location">in <a href="PDF_Signature.html#method_hasAnySignature">
PDF_Signature</a> at line 137</div>
        <code>                    
    <strong>hasAnySignature</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 13</div>
        <code>                    
    <strong>__construct</strong>($proposal, $tag = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$proposal</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$tag</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_prepare">
        <div class="location">at line 44</div>
        <code>                    
    <strong>prepare</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_file_path">
        <div class="location">at line 75</div>
        <code>            protected        
    <strong>file_path</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_type">
        <div class="location">at line 70</div>
        <code>            protected        
    <strong>type</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_view_vars">
        <div class="location">in <a href="App_pdf.html#method_set_view_vars">
App_pdf</a> at line 95</div>
        <code>                    
    <strong>set_view_vars</strong>($vars, $value = null)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$vars</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$value</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_view_vars">
        <div class="location">in <a href="App_pdf.html#method_get_view_vars">
App_pdf</a> at line 106</div>
        <code>                    
    <strong>get_view_vars</strong>($var = null)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$var</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_format_array">
        <div class="location">in <a href="App_pdf.html#method_get_format_array">
App_pdf</a> at line 115</div>
        <code>                    
    <strong>get_format_array</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_font_size">
        <div class="location">in <a href="App_pdf.html#method_set_font_size">
App_pdf</a> at line 120</div>
        <code>                    
    <strong>set_font_size</strong>($size)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$size</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_font_size">
        <div class="location">in <a href="App_pdf.html#method_get_font_size">
App_pdf</a> at line 127</div>
        <code>                    
    <strong>get_font_size</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_default_font_size">
        <div class="location">in <a href="App_pdf.html#method_get_default_font_size">
App_pdf</a> at line 132</div>
        <code>                    
    <strong>get_default_font_size</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_font_name">
        <div class="location">in <a href="App_pdf.html#method_get_font_name">
App_pdf</a> at line 143</div>
        <code>                    
    <strong>get_font_name</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_font_name">
        <div class="location">in <a href="App_pdf.html#method_set_font_name">
App_pdf</a> at line 148</div>
        <code>                    
    <strong>set_font_name</strong>($name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$name</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_default_font_name">
        <div class="location">in <a href="App_pdf.html#method_get_default_font_name">
App_pdf</a> at line 155</div>
        <code>                    
    <strong>get_default_font_name</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_custom_fields">
        <div class="location">in <a href="App_pdf.html#method_custom_fields">
App_pdf</a> at line 165</div>
        <code>                    
    <strong>custom_fields</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_isLastPage">
        <div class="location">in <a href="App_pdf.html#method_isLastPage">
App_pdf</a> at line 175</div>
        <code>                    
    <strong>isLastPage</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_Close">
        <div class="location">in <a href="App_pdf.html#method_Close">
App_pdf</a> at line 180</div>
        <code>                    
    <strong>Close</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_Header">
        <div class="location">in <a href="App_pdf.html#method_Header">
App_pdf</a> at line 193</div>
        <code>                    
    <strong>Header</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_Footer">
        <div class="location">in <a href="App_pdf.html#method_Footer">
App_pdf</a> at line 198</div>
        <code>                    
    <strong>Footer</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_fix_editor_html">
        <div class="location">in <a href="App_pdf.html#method_fix_editor_html">
App_pdf</a> at line 214</div>
        <code>                    
    <strong>fix_editor_html</strong>($content)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$content</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_load_language">
        <div class="location">in <a href="App_pdf.html#method_load_language">
App_pdf</a> at line 250</div>
        <code>            protected        
    <strong>load_language</strong>($client_id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$client_id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_file_path">
        <div class="location">in <a href="App_pdf.html#method_get_file_path">
App_pdf</a> at line 257</div>
        <code>            protected        
    <strong>get_file_path</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_build">
        <div class="location">in <a href="App_pdf.html#method_build">
App_pdf</a> at line 262</div>
        <code>            protected        
    <strong>build</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_with_number_to_word">
        <div class="location">in <a href="App_pdf.html#method_with_number_to_word">
App_pdf</a> at line 294</div>
        <code>                    
    <strong>with_number_to_word</strong>($client_id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$client_id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__destroy">
        <div class="location">in <a href="App_pdf.html#method__destroy">
App_pdf</a> at line 309</div>
        <code>                    
    <strong>_destroy</strong>($destroyall = false, $preserve_objcopy = false)
        </code>
    </h3>
    <div class="details"><i>Since: 4.5.016 (2009-02-24)</i>
            <br>    
    
            

        <div class="method-description">
                            <p><p>Unset all class variables except the following critical variables.</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$destroyall</td>
                <td><p>(boolean) if true destroys all class variables, otherwise preserves critical variables.</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$preserve_objcopy</td>
                <td><p>(boolean) if true preserves the objcopy variable</p></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Proposals_merge_fields | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Proposals_merge_fields" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Proposals_merge_fields    
            </h1>
    </div>

    
    <p>        class
    <strong>Proposals_merge_fields</strong>        extends <a href="App_merge_fields.html">App_merge_fields</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"><p>Codeigniter instance</p></td>
                <td><small>from&nbsp;<a href="App_merge_fields.html#property_ci">
App_merge_fields</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_fields">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$fields</td>
                <td class="last"><p>The actual registered fields from the classes that extends</p></td>
                <td><small>from&nbsp;<a href="App_merge_fields.html#property_fields">
App_merge_fields</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_registered">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$registered</td>
                <td class="last"><p>Paths to load the classes
e.q. merge_fields/client</p></td>
                <td><small>from&nbsp;<a href="App_merge_fields.html#property_registered">
App_merge_fields</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_for">
                                        protected                                        mixed
                                                                                
                                    </td>
                <td>$for</td>
                <td class="last"><p>Merge fields relation</p></td>
                <td><small>from&nbsp;<a href="App_merge_fields.html#property_for">
App_merge_fields</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_all_merge_fields">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$all_merge_fields</td>
                <td class="last"><p>All merge fields are stored here</p></td>
                <td><small>from&nbsp;<a href="App_merge_fields.html#property_all_merge_fields">
App_merge_fields</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_merge_fields.html#method___construct">
App_merge_fields</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_by_name">get_by_name</a>($name)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_merge_fields.html#method_get_by_name">
App_merge_fields</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_format_feature">format_feature</a>($name, ...$params)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_merge_fields.html#method_format_feature">
App_merge_fields</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get">get</a>($name = null)
        
                                            <p><p>Get the registered class fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_merge_fields.html#method_get">
App_merge_fields</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set">set</a>(array $fields)
        
                                            <p><p>Set merge fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_merge_fields.html#method_set">
App_merge_fields</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    $this
                </div>
                <div class="col-md-8">
                    <a href="#method_register">register</a>(mixed $loadPath)
        
                                            <p><p>Register merge field path class</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_merge_fields.html#method_register">
App_merge_fields</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_registered">get_registered</a>()
        
                                            <p><p>Get all registered paths</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_merge_fields.html#method_get_registered">
App_merge_fields</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_all">all</a>($reBuild = false)
        
                                            <p><p>Get all merge fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_merge_fields.html#method_all">
App_merge_fields</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_name">name</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_merge_fields.html#method_name">
App_merge_fields</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_load">load</a>($merge_field)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_merge_fields.html#method_load">
App_merge_fields</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_flat">get_flat</a>($primary, $additional = [], $exclude_keys = [])
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_merge_fields.html#method_get_flat">
App_merge_fields</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_build">build</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_format">format</a>(mixed $proposal_id)
        
                                            <p><p>Merge fields for proposals</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">in <a href="App_merge_fields.html#method___construct">
App_merge_fields</a> at line 44</div>
        <code>                    
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_by_name">
        <div class="location">in <a href="App_merge_fields.html#method_get_by_name">
App_merge_fields</a> at line 57</div>
        <code>                    
    <strong>get_by_name</strong>($name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$name</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_format_feature">
        <div class="location">in <a href="App_merge_fields.html#method_format_feature">
App_merge_fields</a> at line 68</div>
        <code>                    
    <strong>format_feature</strong>($name, ...$params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$name</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>...$params</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get">
        <div class="location">in <a href="App_merge_fields.html#method_get">
App_merge_fields</a> at line 115</div>
        <code>                    array
    <strong>get</strong>($name = null)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the registered class fields</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set">
        <div class="location">in <a href="App_merge_fields.html#method_set">
App_merge_fields</a> at line 126</div>
        <code>                    
    <strong>set</strong>(array $fields)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set merge fields</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$fields</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_register">
        <div class="location">in <a href="App_merge_fields.html#method_register">
App_merge_fields</a> at line 144</div>
        <code>                    $this
    <strong>register</strong>(mixed $loadPath)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Register merge field path class</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$loadPath</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>$this</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_registered">
        <div class="location">in <a href="App_merge_fields.html#method_get_registered">
App_merge_fields</a> at line 163</div>
        <code>                    array
    <strong>get_registered</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get all registered paths</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_all">
        <div class="location">in <a href="App_merge_fields.html#method_all">
App_merge_fields</a> at line 172</div>
        <code>                    array
    <strong>all</strong>($reBuild = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get all merge fields</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$reBuild</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_name">
        <div class="location">in <a href="App_merge_fields.html#method_name">
App_merge_fields</a> at line 213</div>
        <code>                    
    <strong>name</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_load">
        <div class="location">in <a href="App_merge_fields.html#method_load">
App_merge_fields</a> at line 222</div>
        <code>                    
    <strong>load</strong>($merge_field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$merge_field</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_flat">
        <div class="location">in <a href="App_merge_fields.html#method_get_flat">
App_merge_fields</a> at line 233</div>
        <code>                    
    <strong>get_flat</strong>($primary, $additional = [], $exclude_keys = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$primary</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$additional</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$exclude_keys</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_build">
        <div class="location">at line 7</div>
        <code>                    
    <strong>build</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_format">
        <div class="location">at line 144</div>
        <code>                    array
    <strong>format</strong>(mixed $proposal_id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Merge fields for proposals</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$proposal_id</td>
                <td><p>proposal id</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

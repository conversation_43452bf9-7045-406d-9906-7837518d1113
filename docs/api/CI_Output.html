<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Output | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Output" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Output    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Output</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Output Class</p></p>            <p><p>Responsible for sending final output to the browser.</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_final_output">
                                                                                string
                                                                                
                                    </td>
                <td>$final_output</td>
                <td class="last"><p>Final output string</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_cache_expiration">
                                                                                int
                                                                                
                                    </td>
                <td>$cache_expiration</td>
                <td class="last"><p>Cache expiration time</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_headers">
                                                                                array
                                                                                
                                    </td>
                <td>$headers</td>
                <td class="last"><p>List of server headers</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_mimes">
                                                                                array
                                                                                
                                    </td>
                <td>$mimes</td>
                <td class="last"><p>List of mime types</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_mime_type">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$mime_type</td>
                <td class="last"><p>Mime-type for the current page</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_enable_profiler">
                                                                                bool
                                                                                
                                    </td>
                <td>$enable_profiler</td>
                <td class="last"><p>Enable Profiler flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__zlib_oc">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_zlib_oc</td>
                <td class="last"><p>php.ini zlib.output_compression flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__compress_output">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_compress_output</td>
                <td class="last"><p>CI output compression flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__profiler_sections">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_profiler_sections</td>
                <td class="last"><p>List of profiler sections</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_parse_exec_vars">
                                                                                bool
                                                                                
                                    </td>
                <td>$parse_exec_vars</td>
                <td class="last"><p>Parse markers flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_func_overload">
                    static                    protected                                        bool
                                                                                
                                    </td>
                <td>$func_overload</td>
                <td class="last"><p>mbstring.func_overload flag</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_output">get_output</a>()
        
                                            <p><p>Get Output</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Output.html">CI_Output</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_output">set_output</a>(string $output)
        
                                            <p><p>Set Output</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Output.html">CI_Output</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_append_output">append_output</a>(string $output)
        
                                            <p><p>Append Output</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Output.html">CI_Output</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_header">set_header</a>(string $header, bool $replace = TRUE)
        
                                            <p><p>Set Header</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Output.html">CI_Output</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_content_type">set_content_type</a>(string $mime_type, string $charset = NULL)
        
                                            <p><p>Set Content-Type Header</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_content_type">get_content_type</a>()
        
                                            <p><p>Get Current Content-Type Header</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_header">get_header</a>(string $header)
        
                                            <p><p>Get Header</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Output.html">CI_Output</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_status_header">set_status_header</a>(int $code = 200, string $text = &#039;&#039;)
        
                                            <p><p>Set HTTP Status Header</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Output.html">CI_Output</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_enable_profiler">enable_profiler</a>(bool $val = TRUE)
        
                                            <p><p>Enable/disable Profiler</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Output.html">CI_Output</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_profiler_sections">set_profiler_sections</a>(array $sections)
        
                                            <p><p>Set Profiler Sections</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Output.html">CI_Output</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_cache">cache</a>(int $time)
        
                                            <p><p>Set Cache</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__display">_display</a>(string $output = &#039;&#039;)
        
                                            <p><p>Display Output</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__write_cache">_write_cache</a>(string $output)
        
                                            <p><p>Write Cache</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__display_cache">_display_cache</a>(object $CFG, object $URI)
        
                                            <p><p>Update/serve cached output</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_delete_cache">delete_cache</a>(string $uri = &#039;&#039;)
        
                                            <p><p>Delete cache</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_cache_header">set_cache_header</a>(int $last_modified, int $expiration)
        
                                            <p><p>Set Cache Header</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;int
                </div>
                <div class="col-md-8">
                    <a href="#method_strlen">strlen</a>(string $str)
        
                                            <p><p>Byte-safe strlen()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;string
                </div>
                <div class="col-md-8">
                    <a href="#method_substr">substr</a>(string $str, int $start, int $length = NULL)
        
                                            <p><p>Byte-safe substr()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 139</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                <p><p>Determines whether zLib output compression will be used.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_output">
        <div class="location">at line 165</div>
        <code>                    string
    <strong>get_output</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Output</p></p>                <p><p>Returns the current output string.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_output">
        <div class="location">at line 180</div>
        <code>                    <a href="CI_Output.html">CI_Output</a>
    <strong>set_output</strong>(string $output)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Output</p></p>                <p><p>Sets the output string.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$output</td>
                <td><p>Output data</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Output.html">CI_Output</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_append_output">
        <div class="location">at line 196</div>
        <code>                    <a href="CI_Output.html">CI_Output</a>
    <strong>append_output</strong>(string $output)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Append Output</p></p>                <p><p>Appends data onto the output string.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$output</td>
                <td><p>Data to append</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Output.html">CI_Output</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_header">
        <div class="location">at line 216</div>
        <code>                    <a href="CI_Output.html">CI_Output</a>
    <strong>set_header</strong>(string $header, bool $replace = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Header</p></p>                <p><p>Lets you set a server header which will be sent with the final output.</p>
<p>Note: If a file is cached, headers will not be sent.</p></p>        
                    <p>
                        
                                    <tr>
                        <td>We</td>
                        <td>need to figure out how to permit headers to be cached.</td>
                        </tr>
                            </p>
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$header</td>
                <td>Header</td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$replace</td>
                <td><p>Whether to replace the old header value, if already set</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Output.html">CI_Output</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_content_type">
        <div class="location">at line 240</div>
        <code>                    <a href="CI_Output.html">CI_Output</a>
    <strong>set_content_type</strong>(string $mime_type, string $charset = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Content-Type Header</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$mime_type</td>
                <td><p>Extension of the file we're outputting</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$charset</td>
                <td><p>Character set (default: NULL)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Output.html">CI_Output</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_content_type">
        <div class="location">at line 279</div>
        <code>                    string
    <strong>get_content_type</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Current Content-Type Header</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>'text/html', if not already set</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_header">
        <div class="location">at line 300</div>
        <code>                    string
    <strong>get_header</strong>(string $header)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Header</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$header</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_status_header">
        <div class="location">at line 338</div>
        <code>                    <a href="CI_Output.html">CI_Output</a>
    <strong>set_status_header</strong>(int $code = 200, string $text = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set HTTP Status Header</p></p>                <p><p>As of version 1.7.2, this is an alias for common function
set_status_header().</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$code</td>
                <td><p>Status code (default: 200)</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$text</td>
                <td><p>Optional message</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Output.html">CI_Output</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_enable_profiler">
        <div class="location">at line 352</div>
        <code>                    <a href="CI_Output.html">CI_Output</a>
    <strong>enable_profiler</strong>(bool $val = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Enable/disable Profiler</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$val</td>
                <td><p>TRUE to enable or FALSE to disable</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Output.html">CI_Output</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_profiler_sections">
        <div class="location">at line 369</div>
        <code>                    <a href="CI_Output.html">CI_Output</a>
    <strong>set_profiler_sections</strong>(array $sections)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Profiler Sections</p></p>                <p><p>Allows override of default/config settings for
Profiler section display.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$sections</td>
                <td><p>Profiler sections</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Output.html">CI_Output</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_cache">
        <div class="location">at line 393</div>
        <code>                    <a href="CI_Output.html">CI_Output</a>
    <strong>cache</strong>(int $time)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Cache</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$time</td>
                <td><p>Cache expiration time in minutes</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Output.html">CI_Output</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__display">
        <div class="location">at line 415</div>
        <code>                    void
    <strong>_display</strong>(string $output = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Display Output</p></p>                <p><p>Processes and sends finalized output data to the browser along
with any server headers and profile data. It also stops benchmark
timers so the page rendering speed and memory usage can be shown.</p>
<p>Note: All &quot;view&quot; data is automatically put into $this-&gt;final_output
by controller class.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$output</td>
                <td><p>Output data override</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__write_cache">
        <div class="location">at line 554</div>
        <code>                    void
    <strong>_write_cache</strong>(string $output)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Write Cache</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$output</td>
                <td><p>Output data to cache</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__display_cache">
        <div class="location">at line 657</div>
        <code>                    bool
    <strong>_display_cache</strong>(object $CFG, object $URI)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Update/serve cached output</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>object</td>
                <td>$CFG</td>
                <td><p>CI_Config class instance</p></td>
            </tr>
                    <tr>
                <td>object</td>
                <td>$URI</td>
                <td><p>CI_URI class instance</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td><p>TRUE on success or FALSE on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_delete_cache">
        <div class="location">at line 733</div>
        <code>                    bool
    <strong>delete_cache</strong>(string $uri = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Delete cache</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$uri</td>
                <td><p>URI string</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_cache_header">
        <div class="location">at line 788</div>
        <code>                    void
    <strong>set_cache_header</strong>(int $last_modified, int $expiration)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Cache Header</p></p>                <p><p>Set the HTTP headers to match the server-side file cache settings
in order to reduce bandwidth.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$last_modified</td>
                <td><p>Timestamp of when the page was last modified</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$expiration</td>
                <td><p>Timestamp of when should the requested page expire from cache</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_strlen">
        <div class="location">at line 812</div>
        <code>        static    protected        int
    <strong>strlen</strong>(string $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe strlen()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_substr">
        <div class="location">at line 829</div>
        <code>        static    protected        string
    <strong>substr</strong>(string $str, int $start, int $length = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe substr()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$start</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$length</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

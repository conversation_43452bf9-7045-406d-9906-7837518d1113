<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Encryption | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Encryption" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Encryption    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Encryption</strong>
</p>

        
    
        

            <div class="description">
            <p><p>CodeIgniter Encryption Class</p></p>            <p><p>Provides two-way keyed encryption via PHP's MCrypt and/or OpenSSL extensions.</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property__cipher">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_cipher</td>
                <td class="last"><p>Encryption cipher</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__mode">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_mode</td>
                <td class="last"><p>Cipher mode</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__handle">
                                        protected                                        mixed
                                                                                
                                    </td>
                <td>$_handle</td>
                <td class="last"><p>Cipher handle</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__key">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_key</td>
                <td class="last"><p>Encryption key</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__driver">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_driver</td>
                <td class="last"><p>PHP extension to be used</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__drivers">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_drivers</td>
                <td class="last"><p>List of usable drivers (PHP extensions)</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__modes">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_modes</td>
                <td class="last"><p>List of available modes</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__digests">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_digests</td>
                <td class="last"><p>List of supported HMAC algorithms</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_func_overload">
                    static                    protected                                        bool
                                                                                
                                    </td>
                <td>$func_overload</td>
                <td class="last"><p>mbstring.func_overload flag</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $params = array())
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Encryption.html">CI_Encryption</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_initialize">initialize</a>(array $params)
        
                                            <p>Initialize</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__mcrypt_initialize">_mcrypt_initialize</a>(array $params)
        
                                            <p><p>Initialize MCrypt</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__openssl_initialize">_openssl_initialize</a>(array $params)
        
                                            <p><p>Initialize OpenSSL</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_create_key">create_key</a>(int $length)
        
                                            <p><p>Create a random key</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_encrypt">encrypt</a>(string $data, array $params = NULL)
        
                                            <p>Encrypt</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__mcrypt_encrypt">_mcrypt_encrypt</a>(string $data, array $params)
        
                                            <p><p>Encrypt via MCrypt</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__openssl_encrypt">_openssl_encrypt</a>(string $data, array $params)
        
                                            <p><p>Encrypt via OpenSSL</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_decrypt">decrypt</a>(string $data, array $params = NULL)
        
                                            <p>Decrypt</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__mcrypt_decrypt">_mcrypt_decrypt</a>(string $data, array $params)
        
                                            <p><p>Decrypt via MCrypt</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__openssl_decrypt">_openssl_decrypt</a>(string $data, array $params)
        
                                            <p><p>Decrypt via OpenSSL</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method__get_params">_get_params</a>(array $params)
        
                                            <p><p>Get params</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    resource
                </div>
                <div class="col-md-8">
                    <a href="#method__mcrypt_get_handle">_mcrypt_get_handle</a>(string $cipher, string $mode)
        
                                            <p><p>Get MCrypt handle</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__openssl_get_handle">_openssl_get_handle</a>(string $cipher, string $mode)
        
                                            <p><p>Get OpenSSL handle</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__cipher_alias">_cipher_alias</a>(string $cipher)
        
                                            <p><p>Cipher alias</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_hkdf">hkdf</a>($key, $digest = &#039;sha512&#039;, $salt = NULL, $length = NULL, $info = &#039;&#039;)
        
                                            <p>HKDF</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method___get">__get</a>(string $key)
        
                                            <p><p>__get() magic</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;int
                </div>
                <div class="col-md-8">
                    <a href="#method_strlen">strlen</a>(string $str)
        
                                            <p><p>Byte-safe strlen()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;string
                </div>
                <div class="col-md-8">
                    <a href="#method_substr">substr</a>(string $str, int $start, int $length = NULL)
        
                                            <p><p>Byte-safe substr()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 152</div>
        <code>                    void
    <strong>__construct</strong>(array $params = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Configuration parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initialize">
        <div class="location">at line 183</div>
        <code>                    <a href="CI_Encryption.html">CI_Encryption</a>
    <strong>initialize</strong>(array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Initialize</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Configuration parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Encryption.html">CI_Encryption</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__mcrypt_initialize">
        <div class="location">at line 227</div>
        <code>            protected        void
    <strong>_mcrypt_initialize</strong>(array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize MCrypt</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Configuration parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__openssl_initialize">
        <div class="location">at line 286</div>
        <code>            protected        void
    <strong>_openssl_initialize</strong>(array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize OpenSSL</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Configuration parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_create_key">
        <div class="location">at line 336</div>
        <code>                    string
    <strong>create_key</strong>(int $length)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create a random key</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$length</td>
                <td><p>Output length</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_encrypt">
        <div class="location">at line 371</div>
        <code>                    string
    <strong>encrypt</strong>(string $data, array $params = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Encrypt</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$data</td>
                <td><p>Input data</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Input parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__mcrypt_encrypt">
        <div class="location">at line 405</div>
        <code>            protected        string
    <strong>_mcrypt_encrypt</strong>(string $data, array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Encrypt via MCrypt</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$data</td>
                <td><p>Input data</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Input parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__openssl_encrypt">
        <div class="location">at line 470</div>
        <code>            protected        string
    <strong>_openssl_encrypt</strong>(string $data, array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Encrypt via OpenSSL</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$data</td>
                <td><p>Input data</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Input parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_decrypt">
        <div class="location">at line 506</div>
        <code>                    string
    <strong>decrypt</strong>(string $data, array $params = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Decrypt</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$data</td>
                <td><p>Encrypted data</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Input parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__mcrypt_decrypt">
        <div class="location">at line 564</div>
        <code>            protected        string
    <strong>_mcrypt_decrypt</strong>(string $data, array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Decrypt via MCrypt</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$data</td>
                <td><p>Encrypted data</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Input parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__openssl_decrypt">
        <div class="location">at line 626</div>
        <code>            protected        string
    <strong>_openssl_decrypt</strong>(string $data, array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Decrypt via OpenSSL</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$data</td>
                <td><p>Encrypted data</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Input parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_params">
        <div class="location">at line 657</div>
        <code>            protected        array
    <strong>_get_params</strong>(array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get params</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Input parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__mcrypt_get_handle">
        <div class="location">at line 740</div>
        <code>            protected        resource
    <strong>_mcrypt_get_handle</strong>(string $cipher, string $mode)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get MCrypt handle</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$cipher</td>
                <td><p>Cipher name</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$mode</td>
                <td><p>Encryption mode</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>resource</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__openssl_get_handle">
        <div class="location">at line 754</div>
        <code>            protected        string
    <strong>_openssl_get_handle</strong>(string $cipher, string $mode)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get OpenSSL handle</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$cipher</td>
                <td><p>Cipher name</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$mode</td>
                <td><p>Encryption mode</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__cipher_alias">
        <div class="location">at line 772</div>
        <code>            protected        void
    <strong>_cipher_alias</strong>(string $cipher)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Cipher alias</p></p>                <p><p>Tries to translate cipher names between MCrypt and OpenSSL's &quot;dialects&quot;.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$cipher</td>
                <td><p>Cipher name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_hkdf">
        <div class="location">at line 850</div>
        <code>                    string
    <strong>hkdf</strong>($key, $digest = &#039;sha512&#039;, $salt = NULL, $length = NULL, $info = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>HKDF</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$key</td>
                <td><p>Input key</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$digest</td>
                <td><p>A SHA-2 hashing algorithm</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$salt</td>
                <td><p>Optional salt</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$length</td>
                <td><p>Output length (defaults to the selected digest size)</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$info</td>
                <td><p>Optional context/application-specific info</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>A pseudo-random key</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___get">
        <div class="location">at line 887</div>
        <code>                    mixed
    <strong>__get</strong>(string $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>__get() magic</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td><p>Property name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_strlen">
        <div class="location">at line 910</div>
        <code>        static    protected        int
    <strong>strlen</strong>(string $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe strlen()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_substr">
        <div class="location">at line 931</div>
        <code>        static    protected        string
    <strong>substr</strong>(string $str, int $start, int $length = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe substr()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$start</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$length</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

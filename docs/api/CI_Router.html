<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Router | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Router" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Router    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Router</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Router Class</p></p>            <p><p>Parses URIs and determines routing</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_config">
                                                                                object
                                                                                
                                    </td>
                <td>$config</td>
                <td class="last"><p>CI_Config class object</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_routes">
                                                                                array
                                                                                
                                    </td>
                <td>$routes</td>
                <td class="last"><p>List of routes</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_class">
                                                                                string
                                                                                
                                    </td>
                <td>$class</td>
                <td class="last"><p>Current class name</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_method">
                                                                                string
                                                                                
                                    </td>
                <td>$method</td>
                <td class="last"><p>Current method name</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_directory">
                                                                                string
                                                                                
                                    </td>
                <td>$directory</td>
                <td class="last"><p>Sub-directory that contains the requested controller class</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_default_controller">
                                                                                string
                                                                                
                                    </td>
                <td>$default_controller</td>
                <td class="last"><p>Default controller (and method if specific)</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_translate_uri_dashes">
                                                                                bool
                                                                                
                                    </td>
                <td>$translate_uri_dashes</td>
                <td class="last"><p>Translate URI dashes</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_enable_query_strings">
                                                                                bool
                                                                                
                                    </td>
                <td>$enable_query_strings</td>
                <td class="last"><p>Enable query strings flag</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $routing = NULL)
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__set_routing">_set_routing</a>()
        
                                            <p><p>Set route mapping</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__set_request">_set_request</a>(array $segments = array())
        
                                            <p><p>Set request route</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__set_default_controller">_set_default_controller</a>()
        
                                            <p><p>Set default controller</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method__validate_request">_validate_request</a>(array $segments)
        
                                            <p><p>Validate request</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__parse_routes">_parse_routes</a>()
        
                                            <p><p>Parse Routes</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_class">set_class</a>(string $class)
        
                                            <p><p>Set class name</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_fetch_class">fetch_class</a>()
        <small><span class="label label-danger">deprecated</span></small>
                                            <p><p>Fetch the current class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_method">set_method</a>(string $method)
        
                                            <p><p>Set method name</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_fetch_method">fetch_method</a>()
        <small><span class="label label-danger">deprecated</span></small>
                                            <p><p>Fetch the current method</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_directory">set_directory</a>(string $dir, bool $append = FALSE)
        
                                            <p><p>Set directory name</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_fetch_directory">fetch_directory</a>()
        <small><span class="label label-danger">deprecated</span></small>
                                            <p><p>Fetch directory</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 124</div>
        <code>                    void
    <strong>__construct</strong>(array $routing = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                <p><p>Runs the route mapping function.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$routing</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_routing">
        <div class="location">at line 155</div>
        <code>            protected        void
    <strong>_set_routing</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set route mapping</p></p>                <p><p>Determines what should be served based on the URI request,
as well as any &quot;routes&quot; that have been set in the routing config file.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_request">
        <div class="location">at line 248</div>
        <code>            protected        void
    <strong>_set_request</strong>(array $segments = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set request route</p></p>                <p><p>Takes an array of URI segments as input and sets the class/method
to be called.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$segments</td>
                <td><p>URI segments</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_default_controller">
        <div class="location">at line 290</div>
        <code>            protected        void
    <strong>_set_default_controller</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set default controller</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__validate_request">
        <div class="location">at line 332</div>
        <code>            protected        mixed
    <strong>_validate_request</strong>(array $segments)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validate request</p></p>                <p><p>Attempts validate the URI request and determine the controller path.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$segments</td>
                <td><p>URI segments</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>URI segments</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__parse_routes">
        <div class="location">at line 370</div>
        <code>            protected        void
    <strong>_parse_routes</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Parse Routes</p></p>                <p><p>Matches any routes that may exist in the config/routes.php file
against the URI to determine if the class/method need to be remapped.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_class">
        <div class="location">at line 434</div>
        <code>                    void
    <strong>set_class</strong>(string $class)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set class name</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$class</td>
                <td><p>Class name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_fetch_class">
        <div class="location">at line 447</div>
        <code>                    string
    <strong>fetch_class</strong>()
        <small><span class="label label-danger">deprecated</span></small></code>
    </h3>
    <div class="details">    
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td>3.0.0</td>
                    <td>Read the 'class' property instead</td>
                </tr>
                    </p>
    
            

        <div class="method-description">
                            <p><p>Fetch the current class</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_method">
        <div class="location">at line 460</div>
        <code>                    void
    <strong>set_method</strong>(string $method)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set method name</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$method</td>
                <td><p>Method name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_fetch_method">
        <div class="location">at line 473</div>
        <code>                    string
    <strong>fetch_method</strong>()
        <small><span class="label label-danger">deprecated</span></small></code>
    </h3>
    <div class="details">    
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td>3.0.0</td>
                    <td>Read the 'method' property instead</td>
                </tr>
                    </p>
    
            

        <div class="method-description">
                            <p><p>Fetch the current method</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_directory">
        <div class="location">at line 487</div>
        <code>                    void
    <strong>set_directory</strong>(string $dir, bool $append = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set directory name</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$dir</td>
                <td><p>Directory name</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$append</td>
                <td><p>Whether we're appending rather than setting the full value</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_fetch_directory">
        <div class="location">at line 510</div>
        <code>                    string
    <strong>fetch_directory</strong>()
        <small><span class="label label-danger">deprecated</span></small></code>
    </h3>
    <div class="details">    
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td>3.0.0</td>
                    <td>Read the 'directory' property instead</td>
                </tr>
                    </p>
    
            

        <div class="method-description">
                            <p><p>Fetch directory</p></p>                <p><p>Feches the sub-directory (if any) that contains the requested
controller class.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

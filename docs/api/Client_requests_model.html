<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Client_requests_model | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Client_requests_model" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Client_requests_model    
            </h1>
    </div>

    
    <p>        class
    <strong>Client_requests_model</strong>        extends <a href="App_Model.html">App_Model</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_requestTbl">
                                        protected                                        
                                                                                
                                    </td>
                <td>$requestTbl</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___get">__get</a>(string $key)
        
                                            <p><p>__get magic</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Model.html#method___get">
CI_Model</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_createRequest">createRequest</a>(int $clientId)
        
                                            <p><p>Create request</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_admin_can_approve_request">admin_can_approve_request</a>($requestId)
        
                                            <p><p>Check sale permission to approve request</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_leader_can_approve_request">leader_can_approve_request</a>($requestId)
        
                                            <p><p>Check sale permission to approve request</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_process_leader_approve">process_leader_approve</a>(int $requestId, mixed $request, string $note)
        
                                            <p><p>Process approve status</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_process_admin_approve">process_admin_approve</a>(int $requestId, $note)
        
                                            <p><p>Process approve status</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_admin_can_reject_request">admin_can_reject_request</a>($requestId)
        
                                            <p><p>Check sale permission to reject request</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_leader_can_reject_request">leader_can_reject_request</a>($requestId)
        
                                            <p><p>Check sale permission to reject request</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_process_reject_request">process_reject_request</a>(interger $requestId, string $note, $status = CLIENT_REQUEST_STATUS_SA_REJECTED)
        
                                            <p><p>Check sale permission to reject request</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_can_edit_request_note">can_edit_request_note</a>(int $requestId, bool $isLeader = false)
        
                                            <p><p>Check request is already approved/rejected before or not before allow edit note</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_edit_request_note">edit_request_note</a>(int $requestId, string $note, bool $isLeader = false)
        
                                            <p><p>Perform update request's note</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 9</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___get">
        <div class="location">in <a href="CI_Model.html#method___get">
CI_Model</a> at line 67</div>
        <code>                    
    <strong>__get</strong>(string $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>__get magic</p></p>                <p><p>Allows models to access CI's loaded classes using the same
syntax as controllers.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_createRequest">
        <div class="location">at line 22</div>
        <code>                    int
    <strong>createRequest</strong>(int $clientId)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create request</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$clientId</td>
                <td><p>client id</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td><p>request id</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_admin_can_approve_request">
        <div class="location">at line 38</div>
        <code>                    
    <strong>admin_can_approve_request</strong>($requestId)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check sale permission to approve request</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$requestId</td>
                <td><p>request will be checked permission</p></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_leader_can_approve_request">
        <div class="location">at line 80</div>
        <code>                    
    <strong>leader_can_approve_request</strong>($requestId)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check sale permission to approve request</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$requestId</td>
                <td><p>request will be checked permission</p></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_process_leader_approve">
        <div class="location">at line 128</div>
        <code>                    
    <strong>process_leader_approve</strong>(int $requestId, mixed $request, string $note)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Process approve status</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$requestId</td>
                <td></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$request</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$note</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_process_admin_approve">
        <div class="location">at line 164</div>
        <code>                    
    <strong>process_admin_approve</strong>(int $requestId, $note)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Process approve status</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$requestId</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$note</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_admin_can_reject_request">
        <div class="location">at line 182</div>
        <code>                    
    <strong>admin_can_reject_request</strong>($requestId)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check sale permission to reject request</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$requestId</td>
                <td><p>request will be checked permission</p></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_leader_can_reject_request">
        <div class="location">at line 215</div>
        <code>                    
    <strong>leader_can_reject_request</strong>($requestId)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check sale permission to reject request</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$requestId</td>
                <td><p>request will be checked permission</p></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_process_reject_request">
        <div class="location">at line 256</div>
        <code>                    
    <strong>process_reject_request</strong>(interger $requestId, string $note, $status = CLIENT_REQUEST_STATUS_SA_REJECTED)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check sale permission to reject request</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>interger</td>
                <td>$requestId</td>
                <td><p>request will be checked permission</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$note</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$status</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_can_edit_request_note">
        <div class="location">at line 275</div>
        <code>                    
    <strong>can_edit_request_note</strong>(int $requestId, bool $isLeader = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check request is already approved/rejected before or not before allow edit note</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$requestId</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$isLeader</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_edit_request_note">
        <div class="location">at line 293</div>
        <code>                    
    <strong>edit_request_note</strong>(int $requestId, string $note, bool $isLeader = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Perform update request's note</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$requestId</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$note</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$isLeader</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

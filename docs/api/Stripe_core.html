<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Stripe_core | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Stripe_core" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Stripe_core    
            </h1>
    </div>

    
    <p>        class
    <strong>Stripe_core</strong>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_secretKey">
                                        protected                                        
                                                                                
                                    </td>
                <td>$secretKey</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_publishableKey">
                                        protected                                        
                                                                                
                                    </td>
                <td>$publishableKey</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_apiVersion">
                                        protected                                        
                                                                                
                                    </td>
                <td>$apiVersion</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Initialize Stripe_core class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <abbr title="Stripe\Customer">Customer</abbr>
                </div>
                <div class="col-md-8">
                    <a href="#method_create_customer">create_customer</a>(array $data)
        
                                            <p><p>Create new customer in strip</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <abbr title="Stripe\Customer">Customer</abbr>
                </div>
                <div class="col-md-8">
                    <a href="#method_get_customer">get_customer</a>(array|string $id)
        
                                            <p><p>Retrieve customer</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <abbr title="Stripe\Customer">Customer</abbr>
                </div>
                <div class="col-md-8">
                    <a href="#method_update_customer">update_customer</a>(string $id, array $payload)
        
                                            <p><p>Update customer</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string|null
                </div>
                <div class="col-md-8">
                    <a href="#method_get_publishable_key">get_publishable_key</a>()
        
                                            <p><p>Get Stripe publishable key</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_list_webhook_endpoints">list_webhook_endpoints</a>()
        
                                            <p><p>List the created webhook endpoint for the current environment</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_webhook_events">get_webhook_events</a>()
        
                                            <p><p>Get the necessary Stripe integration webhook events</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_tax_rates">get_tax_rates</a>()
        
                                            <p><p>Get available Stripe tax rates</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <abbr title="Stripe\TaxRate">TaxRate</abbr>
                </div>
                <div class="col-md-8">
                    <a href="#method_retrieve_tax_rate">retrieve_tax_rate</a>(array|string $id)
        
                                            <p><p>Retrieve tax rate by given id</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <abbr title="Stripe\WebhookEndpoint">WebhookEndpoint</abbr>
                </div>
                <div class="col-md-8">
                    <a href="#method_create_webhook">create_webhook</a>()
        
                                            <p><p>Create webhook in Stripe for the integration</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_enable_webhook">enable_webhook</a>(string $id)
        
                                            <p><p>Enable webhook by given id</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_delete_webhook">delete_webhook</a>(string $id)
        
                                            <p><p>Delete the given webhook</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <abbr title="Stripe\Checkout\Session">Session</abbr>
                </div>
                <div class="col-md-8">
                    <a href="#method_create_session">create_session</a>(array $data)
        
                                            <p><p>Create new checkout session</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <abbr title="Stripe\Checkout\Session">Session</abbr>
                </div>
                <div class="col-md-8">
                    <a href="#method_retrieve_session">retrieve_session</a>(array|string $data)
        
                                            <p><p>Retrieve checkout session</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <abbr title="Stripe\PaymentIntent">PaymentIntent</abbr>
                </div>
                <div class="col-md-8">
                    <a href="#method_retrieve_payment_intent">retrieve_payment_intent</a>(array|string $data)
        
                                            <p><p>Retrieve payment intent</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <abbr title="Stripe\PaymentMethod">PaymentMethod</abbr>
                </div>
                <div class="col-md-8">
                    <a href="#method_retrieve_payment_method">retrieve_payment_method</a>(array|string $data)
        
                                            <p><p>Retrieve payment method</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_construct_event">construct_event</a>(array $payload, string $secret)
        
                                            <p><p>Create constturct event</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_has_api_key">has_api_key</a>()
        
                                            <p><p>Check whether there is api key added for the integration</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 18</div>
        <code>                    
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize Stripe_core class</p></p>                        
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_create_customer">
        <div class="location">at line 35</div>
        <code>                    <abbr title="Stripe\Customer">Customer</abbr>
    <strong>create_customer</strong>(array $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create new customer in strip</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><abbr title="Stripe\Customer">Customer</abbr></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_customer">
        <div class="location">at line 47</div>
        <code>                    <abbr title="Stripe\Customer">Customer</abbr>
    <strong>get_customer</strong>(array|string $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Retrieve customer</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array|string</td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><abbr title="Stripe\Customer">Customer</abbr></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_update_customer">
        <div class="location">at line 60</div>
        <code>                    <abbr title="Stripe\Customer">Customer</abbr>
    <strong>update_customer</strong>(string $id, array $payload)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Update customer</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$id</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$payload</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><abbr title="Stripe\Customer">Customer</abbr></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_publishable_key">
        <div class="location">at line 70</div>
        <code>                    string|null
    <strong>get_publishable_key</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Stripe publishable key</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string|null</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_list_webhook_endpoints">
        <div class="location">at line 80</div>
        <code>                    array
    <strong>list_webhook_endpoints</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>List the created webhook endpoint for the current environment</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_webhook_events">
        <div class="location">at line 90</div>
        <code>                    array
    <strong>get_webhook_events</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the necessary Stripe integration webhook events</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_tax_rates">
        <div class="location">at line 111</div>
        <code>                    array
    <strong>get_tax_rates</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get available Stripe tax rates</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_retrieve_tax_rate">
        <div class="location">at line 123</div>
        <code>                    <abbr title="Stripe\TaxRate">TaxRate</abbr>
    <strong>retrieve_tax_rate</strong>(array|string $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Retrieve tax rate by given id</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array|string</td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><abbr title="Stripe\TaxRate">TaxRate</abbr></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_create_webhook">
        <div class="location">at line 133</div>
        <code>                    <abbr title="Stripe\WebhookEndpoint">WebhookEndpoint</abbr>
    <strong>create_webhook</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create webhook in Stripe for the integration</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><abbr title="Stripe\WebhookEndpoint">WebhookEndpoint</abbr></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_enable_webhook">
        <div class="location">at line 155</div>
        <code>                    void
    <strong>enable_webhook</strong>(string $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Enable webhook by given id</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_delete_webhook">
        <div class="location">at line 169</div>
        <code>                    void
    <strong>delete_webhook</strong>(string $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Delete the given webhook</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_create_session">
        <div class="location">at line 182</div>
        <code>                    <abbr title="Stripe\Checkout\Session">Session</abbr>
    <strong>create_session</strong>(array $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create new checkout session</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><abbr title="Stripe\Checkout\Session">Session</abbr></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_retrieve_session">
        <div class="location">at line 194</div>
        <code>                    <abbr title="Stripe\Checkout\Session">Session</abbr>
    <strong>retrieve_session</strong>(array|string $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Retrieve checkout session</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array|string</td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><abbr title="Stripe\Checkout\Session">Session</abbr></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_retrieve_payment_intent">
        <div class="location">at line 206</div>
        <code>                    <abbr title="Stripe\PaymentIntent">PaymentIntent</abbr>
    <strong>retrieve_payment_intent</strong>(array|string $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Retrieve payment intent</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array|string</td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><abbr title="Stripe\PaymentIntent">PaymentIntent</abbr></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_retrieve_payment_method">
        <div class="location">at line 218</div>
        <code>                    <abbr title="Stripe\PaymentMethod">PaymentMethod</abbr>
    <strong>retrieve_payment_method</strong>(array|string $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Retrieve payment method</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array|string</td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><abbr title="Stripe\PaymentMethod">PaymentMethod</abbr></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_construct_event">
        <div class="location">at line 231</div>
        <code>                    mixed
    <strong>construct_event</strong>(array $payload, string $secret)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create constturct event</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$payload</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$secret</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_has_api_key">
        <div class="location">at line 247</div>
        <code>                    bool
    <strong>has_api_key</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check whether there is api key added for the integration</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

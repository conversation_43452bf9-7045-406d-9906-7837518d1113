<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Stripe | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Stripe" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Stripe    
            </h1>
    </div>

    
    <p>        class
    <strong>Stripe</strong>        extends <a href="App_Controller.html">App_Controller</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_load">
                                                                                <a href="CI_Loader.html">CI_Loader</a>
                                                                                
                                    </td>
                <td>$load</td>
                <td class="last">CI_Loader</td>
                <td><small>from&nbsp;<a href="CI_Controller.html#property_load">
CI_Controller</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_current_db_version">
                                        protected                                        
                                                                                
                                    </td>
                <td>$current_db_version</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_Controller.html#property_current_db_version">
App_Controller</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_subscriptionMetaKey">
                                        protected                                        
                                                                                
                                    </td>
                <td>$subscriptionMetaKey</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_Controller.html#method___construct">
App_Controller</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;object
                </div>
                <div class="col-md-8">
                    <a href="#method_get_instance">get_instance</a>()
        
                                            <p><p>Get the CI singleton</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Controller.html#method_get_instance">
CI_Controller</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_create_webhook">create_webhook</a>()
        
                                            <p><p>Create the application Stripe webhook endpoint</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_enable_webhook">enable_webhook</a>()
        
                                            <p><p>Enable the application Stripe webhook endpoint</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_webhook_endpoint">webhook_endpoint</a>()
        
                                            <p><p>The application Stripe webhook endpoint</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_success">success</a>(string $invoice_id, strgin $invoice_hash)
        
                                            <p><p>After stripe checkout succcess
Used only to display success message to the customer</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_customerSubscriptionCreatedEvent">customerSubscriptionCreatedEvent</a>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        
                                            <p><p>Handle subscription created event</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_invoicePaymentSucceededEvent">invoicePaymentSucceededEvent</a>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        
                                            <p><p>Handle subscription invoice payment succeded event</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_invoicePaymentFailedEevent">invoicePaymentFailedEevent</a>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        
                                            <p><p>Handle subscription invoice payment failed event</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_invoicePaymentActionRequiredEevent">invoicePaymentActionRequiredEevent</a>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        
                                            <p><p>Handle subscription invoice payment require action event event</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_customerSubscriptionUpdatedEvent">customerSubscriptionUpdatedEvent</a>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        
                                            <p><p>Handle subscription updated event</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_customerSubscriptionDeletedEvent">customerSubscriptionDeletedEvent</a>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        
                                            <p><p>Handle subscription deleted event</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_customerDeletedEvent">customerDeletedEvent</a>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        
                                            <p><p>Handle customer deleted</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_getStaffCCForMailTemplate">getStaffCCForMailTemplate</a>(int $staff_id)
        
                                            <p><p>Get CC for the subscription mail template</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">in <a href="App_Controller.html#method___construct">
App_Controller</a> at line 9</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_instance">
        <div class="location">in <a href="CI_Controller.html#method_get_instance">
CI_Controller</a> at line 98</div>
        <code>        static            object
    <strong>get_instance</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the CI singleton</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_create_webhook">
        <div class="location">at line 14</div>
        <code>                    mixed
    <strong>create_webhook</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create the application Stripe webhook endpoint</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_enable_webhook">
        <div class="location">at line 53</div>
        <code>                    mixed
    <strong>enable_webhook</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Enable the application Stripe webhook endpoint</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_webhook_endpoint">
        <div class="location">at line 69</div>
        <code>                    mixed
    <strong>webhook_endpoint</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>The application Stripe webhook endpoint</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_success">
        <div class="location">at line 161</div>
        <code>                    mixed
    <strong>success</strong>(string $invoice_id, strgin $invoice_hash)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>After stripe checkout succcess
Used only to display success message to the customer</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$invoice_id</td>
                <td><p>The invoice id the payment is made to</p></td>
            </tr>
                    <tr>
                <td>strgin</td>
                <td>$invoice_hash</td>
                <td><p>invoice hash</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_customerSubscriptionCreatedEvent">
        <div class="location">at line 175</div>
        <code>            protected        void
    <strong>customerSubscriptionCreatedEvent</strong>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Handle subscription created event</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a></td>
                <td>$event</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_invoicePaymentSucceededEvent">
        <div class="location">at line 223</div>
        <code>            protected        void
    <strong>invoicePaymentSucceededEvent</strong>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Handle subscription invoice payment succeded event</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a></td>
                <td>$event</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_invoicePaymentFailedEevent">
        <div class="location">at line 303</div>
        <code>            protected        void
    <strong>invoicePaymentFailedEevent</strong>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Handle subscription invoice payment failed event</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a></td>
                <td>$event</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_invoicePaymentActionRequiredEevent">
        <div class="location">at line 331</div>
        <code>            protected        void
    <strong>invoicePaymentActionRequiredEevent</strong>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Handle subscription invoice payment require action event event</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a></td>
                <td>$event</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_customerSubscriptionUpdatedEvent">
        <div class="location">at line 372</div>
        <code>            protected        void
    <strong>customerSubscriptionUpdatedEvent</strong>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Handle subscription updated event</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a></td>
                <td>$event</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_customerSubscriptionDeletedEvent">
        <div class="location">at line 403</div>
        <code>            protected        void
    <strong>customerSubscriptionDeletedEvent</strong>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Handle subscription deleted event</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a></td>
                <td>$event</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_customerDeletedEvent">
        <div class="location">at line 431</div>
        <code>            protected        void
    <strong>customerDeletedEvent</strong>(<a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a> $event)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Handle customer deleted</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><a target="_blank" rel="noopener" href="https://www.php.net/stdClass">stdClass</a></td>
                <td>$event</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getStaffCCForMailTemplate">
        <div class="location">at line 449</div>
        <code>            protected        string
    <strong>getStaffCCForMailTemplate</strong>(int $staff_id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get CC for the subscription mail template</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$staff_id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_DB_sqlsrv_utility | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_DB_sqlsrv_utility" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_DB_sqlsrv_utility    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_DB_sqlsrv_utility</strong>        extends <a href="CI_DB_utility.html">CI_DB_utility</a>
</p>

        
    
        

            <div class="description">
            <p><p>SQLSRV Utility Class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_db">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$db</td>
                <td class="last"><p>Database object</p></td>
                <td><small>from&nbsp;<a href="CI_DB_utility.html#property_db">
CI_DB_utility</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__list_databases">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_list_databases</td>
                <td class="last"><p>List databases statement</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__optimize_table">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_optimize_table</td>
                <td class="last"><p>OPTIMIZE TABLE statement</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__repair_table">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_repair_table</td>
                <td class="last"><p>REPAIR TABLE statement</p></td>
                <td><small>from&nbsp;<a href="CI_DB_utility.html#property__repair_table">
CI_DB_utility</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(object $db)
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_utility.html#method___construct">
CI_DB_utility</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_list_databases">list_databases</a>()
        
                                            <p><p>List databases</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_utility.html#method_list_databases">
CI_DB_utility</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_database_exists">database_exists</a>(string $database_name)
        
                                            <p><p>Determine if a particular database exists</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_utility.html#method_database_exists">
CI_DB_utility</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_optimize_table">optimize_table</a>(string $table_name)
        
                                            <p><p>Optimize Table</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_utility.html#method_optimize_table">
CI_DB_utility</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_optimize_database">optimize_database</a>()
        
                                            <p><p>Optimize Database</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_utility.html#method_optimize_database">
CI_DB_utility</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_repair_table">repair_table</a>(string $table_name)
        
                                            <p><p>Repair Table</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_utility.html#method_repair_table">
CI_DB_utility</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_csv_from_result">csv_from_result</a>(object $query, string $delim = &#039;,&#039;, string $newline = &quot;\n&quot;, string $enclosure = &#039;&quot;&#039;)
        
                                            <p><p>Generate CSV from a query result object</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_utility.html#method_csv_from_result">
CI_DB_utility</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_xml_from_result">xml_from_result</a>(object $query, array $params = array())
        
                                            <p><p>Generate XML data from a query result object</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_utility.html#method_xml_from_result">
CI_DB_utility</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_backup">backup</a>(array $params = array())
        
                                            <p><p>Database Backup</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_utility.html#method_backup">
CI_DB_utility</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__backup">_backup</a>(array $params = array())
        
                                            <p>Export</p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">in <a href="CI_DB_utility.html#method___construct">
CI_DB_utility</a> at line 87</div>
        <code>                    void
    <strong>__construct</strong>(object $db)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>object</td>
                <td>$db</td>
                <td><p>Database object</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_list_databases">
        <div class="location">in <a href="CI_DB_utility.html#method_list_databases">
CI_DB_utility</a> at line 100</div>
        <code>                    array
    <strong>list_databases</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>List databases</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_database_exists">
        <div class="location">in <a href="CI_DB_utility.html#method_database_exists">
CI_DB_utility</a> at line 136</div>
        <code>                    bool
    <strong>database_exists</strong>(string $database_name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Determine if a particular database exists</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$database_name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_optimize_table">
        <div class="location">in <a href="CI_DB_utility.html#method_optimize_table">
CI_DB_utility</a> at line 149</div>
        <code>                    mixed
    <strong>optimize_table</strong>(string $table_name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Optimize Table</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table_name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_optimize_database">
        <div class="location">in <a href="CI_DB_utility.html#method_optimize_database">
CI_DB_utility</a> at line 173</div>
        <code>                    mixed
    <strong>optimize_database</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Optimize Database</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_repair_table">
        <div class="location">in <a href="CI_DB_utility.html#method_repair_table">
CI_DB_utility</a> at line 210</div>
        <code>                    mixed
    <strong>repair_table</strong>(string $table_name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Repair Table</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table_name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_csv_from_result">
        <div class="location">in <a href="CI_DB_utility.html#method_csv_from_result">
CI_DB_utility</a> at line 238</div>
        <code>                    string
    <strong>csv_from_result</strong>(object $query, string $delim = &#039;,&#039;, string $newline = &quot;\n&quot;, string $enclosure = &#039;&quot;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Generate CSV from a query result object</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>object</td>
                <td>$query</td>
                <td><p>Query result object</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$delim</td>
                <td><p>Delimiter (default: ,)</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$newline</td>
                <td><p>Newline character (default: \n)</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$enclosure</td>
                <td><p>Enclosure (default: &quot;)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_xml_from_result">
        <div class="location">in <a href="CI_DB_utility.html#method_xml_from_result">
CI_DB_utility</a> at line 277</div>
        <code>                    string
    <strong>xml_from_result</strong>(object $query, array $params = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Generate XML data from a query result object</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>object</td>
                <td>$query</td>
                <td><p>Query result object</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Any preferences</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_backup">
        <div class="location">in <a href="CI_DB_utility.html#method_backup">
CI_DB_utility</a> at line 322</div>
        <code>                    string
    <strong>backup</strong>(array $params = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Database Backup</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__backup">
        <div class="location">at line 71</div>
        <code>            protected        bool
    <strong>_backup</strong>(array $params = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Export</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td>Preferences</td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

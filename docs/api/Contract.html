<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Contract | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Contract" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Contract    
            </h1>
    </div>

    
    <p>        class
    <strong>Contract</strong>        extends <a href="ClientsController.html">ClientsController</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_load">
                                                                                <a href="CI_Loader.html">CI_Loader</a>
                                                                                
                                    </td>
                <td>$load</td>
                <td class="last">CI_Loader</td>
                <td><small>from&nbsp;<a href="CI_Controller.html#property_load">
CI_Controller</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_current_db_version">
                                        protected                                        
                                                                                
                                    </td>
                <td>$current_db_version</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_Controller.html#property_current_db_version">
App_Controller</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_template">
                                                                                
                                                                                
                                    </td>
                <td>$template</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="ClientsController.html#property_template">
ClientsController</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_data">
                                                                                
                                                                                
                                    </td>
                <td>$data</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="ClientsController.html#property_data">
ClientsController</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_use_footer">
                                                                                
                                                                                
                                    </td>
                <td>$use_footer</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="ClientsController.html#property_use_footer">
ClientsController</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_use_submenu">
                                                                                
                                                                                
                                    </td>
                <td>$use_submenu</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="ClientsController.html#property_use_submenu">
ClientsController</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_use_navigation">
                                                                                
                                                                                
                                    </td>
                <td>$use_navigation</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="ClientsController.html#property_use_navigation">
ClientsController</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="ClientsController.html#method___construct">
ClientsController</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;object
                </div>
                <div class="col-md-8">
                    <a href="#method_get_instance">get_instance</a>()
        
                                            <p><p>Get the CI singleton</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Controller.html#method_get_instance">
CI_Controller</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_layout">layout</a>($notInThemeViewFiles = false)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="ClientsController.html#method_layout">
ClientsController</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_data">data</a>(array $data)
        
                                            <p><p>Sets view data</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="ClientsController.html#method_data">
ClientsController</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_view">view</a>(string $view)
        
                                            <p><p>Set view to load</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="ClientsController.html#method_view">
ClientsController</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_title">title</a>(string $title)
        
                                            <p><p>Sets view title</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="ClientsController.html#method_title">
ClientsController</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_disableNavigation">disableNavigation</a>()
        
                                            <p><p>Disables theme navigation</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="ClientsController.html#method_disableNavigation">
ClientsController</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_disableSubMenu">disableSubMenu</a>()
        
                                            <p><p>Disables theme navigation</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="ClientsController.html#method_disableSubMenu">
ClientsController</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_disableFooter">disableFooter</a>()
        
                                            <p><p>Disables theme footer</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="ClientsController.html#method_disableFooter">
ClientsController</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_createThemeViewPath">createThemeViewPath</a>(string $view)
        
                                            <p><p>Create theme view path</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="ClientsController.html#method_createThemeViewPath">
ClientsController</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_index">index</a>($id, $hash)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">in <a href="ClientsController.html#method___construct">
ClientsController</a> at line 19</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_instance">
        <div class="location">in <a href="CI_Controller.html#method_get_instance">
CI_Controller</a> at line 98</div>
        <code>        static            object
    <strong>get_instance</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the CI singleton</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_layout">
        <div class="location">in <a href="ClientsController.html#method_layout">
ClientsController</a> at line 39</div>
        <code>                    
    <strong>layout</strong>($notInThemeViewFiles = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$notInThemeViewFiles</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_data">
        <div class="location">in <a href="ClientsController.html#method_data">
ClientsController</a> at line 112</div>
        <code>                    
    <strong>data</strong>(array $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sets view data</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_view">
        <div class="location">in <a href="ClientsController.html#method_view">
ClientsController</a> at line 128</div>
        <code>                    
    <strong>view</strong>(string $view)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set view to load</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$view</td>
                <td><p>view file</p></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_title">
        <div class="location">in <a href="ClientsController.html#method_title">
ClientsController</a> at line 140</div>
        <code>                    
    <strong>title</strong>(string $title)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sets view title</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$title</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_disableNavigation">
        <div class="location">in <a href="ClientsController.html#method_disableNavigation">
ClientsController</a> at line 151</div>
        <code>                    
    <strong>disableNavigation</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Disables theme navigation</p></p>                        
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_disableSubMenu">
        <div class="location">in <a href="ClientsController.html#method_disableSubMenu">
ClientsController</a> at line 162</div>
        <code>                    
    <strong>disableSubMenu</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Disables theme navigation</p></p>                        
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_disableFooter">
        <div class="location">in <a href="ClientsController.html#method_disableFooter">
ClientsController</a> at line 173</div>
        <code>                    
    <strong>disableFooter</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Disables theme footer</p></p>                        
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_createThemeViewPath">
        <div class="location">in <a href="ClientsController.html#method_createThemeViewPath">
ClientsController</a> at line 187</div>
        <code>            protected        string
    <strong>createThemeViewPath</strong>(string $view)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create theme view path</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$view</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_index">
        <div class="location">at line 7</div>
        <code>                    
    <strong>index</strong>($id, $hash)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$hash</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

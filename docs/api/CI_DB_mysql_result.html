<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_DB_mysql_result | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_DB_mysql_result" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_DB_mysql_result    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_DB_mysql_result</strong>        extends <a href="CI_DB_result.html">CI_DB_result</a>
</p>

        
    
        

            <div class="description">
            <p><p>MySQL Result Class</p></p>            <p><p>This class extends the parent result class: CI_DB_result</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_conn_id">
                                                                                resource|object
                                                                                
                                    </td>
                <td>$conn_id</td>
                <td class="last"><p>Connection ID</p></td>
                <td><small>from&nbsp;<a href="CI_DB_result.html#property_conn_id">
CI_DB_result</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_result_id">
                                                                                resource|object
                                                                                
                                    </td>
                <td>$result_id</td>
                <td class="last"><p>Result ID</p></td>
                <td><small>from&nbsp;<a href="CI_DB_result.html#property_result_id">
CI_DB_result</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_result_array">
                                                                                array[]
                                                                                
                                    </td>
                <td>$result_array</td>
                <td class="last"><p>Result Array</p></td>
                <td><small>from&nbsp;<a href="CI_DB_result.html#property_result_array">
CI_DB_result</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_result_object">
                                                                                object[]
                                                                                
                                    </td>
                <td>$result_object</td>
                <td class="last"><p>Result Object</p></td>
                <td><small>from&nbsp;<a href="CI_DB_result.html#property_result_object">
CI_DB_result</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_custom_result_object">
                                                                                object[]
                                                                                
                                    </td>
                <td>$custom_result_object</td>
                <td class="last"><p>Custom Result Object</p></td>
                <td><small>from&nbsp;<a href="CI_DB_result.html#property_custom_result_object">
CI_DB_result</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_current_row">
                                                                                int
                                                                                
                                    </td>
                <td>$current_row</td>
                <td class="last"><p>Current Row index</p></td>
                <td><small>from&nbsp;<a href="CI_DB_result.html#property_current_row">
CI_DB_result</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_num_rows">
                                                                                int
                                                                                
                                    </td>
                <td>$num_rows</td>
                <td class="last"><p>Number of rows</p></td>
                <td><small>from&nbsp;<a href="CI_DB_result.html#property_num_rows">
CI_DB_result</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_row_data">
                                                                                array
                                                                                
                                    </td>
                <td>$row_data</td>
                <td class="last"><p>Row data</p></td>
                <td><small>from&nbsp;<a href="CI_DB_result.html#property_row_data">
CI_DB_result</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(object $driver_object)
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_num_rows">num_rows</a>()
        
                                            <p><p>Number of rows in the result set</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_result">result</a>(string $type = &#039;object&#039;)
        
                                            <p><p>Query result. Acts as a wrapper function for the following functions.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_result">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_custom_result_object">custom_result_object</a>(string $class_name)
        
                                            <p><p>Custom query result.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_custom_result_object">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_result_object">result_object</a>()
        
                                            <p><p>Query result. &quot;object&quot; version.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_result_object">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_result_array">result_array</a>()
        
                                            <p><p>Query result. &quot;array&quot; version.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_result_array">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_row">row</a>(mixed $n = 0, string $type = &#039;object&#039;)
        
                                            <p>Row</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_row">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_row">set_row</a>(mixed $key, mixed $value = NULL)
        
                                            <p><p>Assigns an item into a particular column slot</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_set_row">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_custom_row_object">custom_row_object</a>(int $n, string $type)
        
                                            <p><p>Returns a single result row - custom object version</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_custom_row_object">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_row_object">row_object</a>(int $n = 0)
        
                                            <p><p>Returns a single result row - object version</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_row_object">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_row_array">row_array</a>(int $n = 0)
        
                                            <p><p>Returns a single result row - array version</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_row_array">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_first_row">first_row</a>(string $type = &#039;object&#039;)
        
                                            <p><p>Returns the &quot;first&quot; row</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_first_row">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_last_row">last_row</a>(string $type = &#039;object&#039;)
        
                                            <p><p>Returns the &quot;last&quot; row</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_last_row">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_next_row">next_row</a>(string $type = &#039;object&#039;)
        
                                            <p><p>Returns the &quot;next&quot; row</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_next_row">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_previous_row">previous_row</a>(string $type = &#039;object&#039;)
        
                                            <p><p>Returns the &quot;previous&quot; row</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_previous_row">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_unbuffered_row">unbuffered_row</a>(string $type = &#039;object&#039;)
        
                                            <p><p>Returns an unbuffered row and move pointer to next row</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_result.html#method_unbuffered_row">
CI_DB_result</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_num_fields">num_fields</a>()
        
                                            <p><p>Number of fields in the result set</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_list_fields">list_fields</a>()
        
                                            <p><p>Fetch Field Names</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_field_data">field_data</a>()
        
                                            <p><p>Field data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_free_result">free_result</a>()
        
                                            <p><p>Free the result</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_data_seek">data_seek</a>(int $n = 0)
        
                                            <p><p>Data Seek</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method__fetch_assoc">_fetch_assoc</a>()
        
                                            <p><p>Result - associative array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method__fetch_object">_fetch_object</a>(string $class_name = &#039;stdClass&#039;)
        
                                            <p><p>Result - object</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 57</div>
        <code>                    void
    <strong>__construct</strong>(object $driver_object)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>object</td>
                <td>$driver_object</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_num_rows">
        <div class="location">at line 73</div>
        <code>                    int
    <strong>num_rows</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Number of rows in the result set</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_result">
        <div class="location">in <a href="CI_DB_result.html#method_result">
CI_DB_result</a> at line 156</div>
        <code>                    array
    <strong>result</strong>(string $type = &#039;object&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Query result. Acts as a wrapper function for the following functions.</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$type</td>
                <td><p>'object', 'array' or a custom class name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_custom_result_object">
        <div class="location">in <a href="CI_DB_result.html#method_custom_result_object">
CI_DB_result</a> at line 178</div>
        <code>                    array
    <strong>custom_result_object</strong>(string $class_name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Custom query result.</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$class_name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_result_object">
        <div class="location">in <a href="CI_DB_result.html#method_result_object">
CI_DB_result</a> at line 233</div>
        <code>                    array
    <strong>result_object</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Query result. &quot;object&quot; version.</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_result_array">
        <div class="location">in <a href="CI_DB_result.html#method_result_array">
CI_DB_result</a> at line 274</div>
        <code>                    array
    <strong>result_array</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Query result. &quot;array&quot; version.</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_row">
        <div class="location">in <a href="CI_DB_result.html#method_row">
CI_DB_result</a> at line 319</div>
        <code>                    mixed
    <strong>row</strong>(mixed $n = 0, string $type = &#039;object&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Row</p>                <p><p>A wrapper method.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$n</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$type</td>
                <td><p>'object' or 'array'</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_row">
        <div class="location">in <a href="CI_DB_result.html#method_set_row">
CI_DB_result</a> at line 350</div>
        <code>                    void
    <strong>set_row</strong>(mixed $key, mixed $value = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Assigns an item into a particular column slot</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$key</td>
                <td></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$value</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_custom_row_object">
        <div class="location">in <a href="CI_DB_result.html#method_custom_row_object">
CI_DB_result</a> at line 382</div>
        <code>                    object
    <strong>custom_row_object</strong>(int $n, string $type)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns a single result row - custom object version</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_row_object">
        <div class="location">in <a href="CI_DB_result.html#method_row_object">
CI_DB_result</a> at line 407</div>
        <code>                    object
    <strong>row_object</strong>(int $n = 0)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns a single result row - object version</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_row_array">
        <div class="location">in <a href="CI_DB_result.html#method_row_array">
CI_DB_result</a> at line 431</div>
        <code>                    array
    <strong>row_array</strong>(int $n = 0)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns a single result row - array version</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_first_row">
        <div class="location">in <a href="CI_DB_result.html#method_first_row">
CI_DB_result</a> at line 455</div>
        <code>                    mixed
    <strong>first_row</strong>(string $type = &#039;object&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns the &quot;first&quot; row</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_last_row">
        <div class="location">in <a href="CI_DB_result.html#method_last_row">
CI_DB_result</a> at line 469</div>
        <code>                    mixed
    <strong>last_row</strong>(string $type = &#039;object&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns the &quot;last&quot; row</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_next_row">
        <div class="location">in <a href="CI_DB_result.html#method_next_row">
CI_DB_result</a> at line 483</div>
        <code>                    mixed
    <strong>next_row</strong>(string $type = &#039;object&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns the &quot;next&quot; row</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_previous_row">
        <div class="location">in <a href="CI_DB_result.html#method_previous_row">
CI_DB_result</a> at line 504</div>
        <code>                    mixed
    <strong>previous_row</strong>(string $type = &#039;object&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns the &quot;previous&quot; row</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_unbuffered_row">
        <div class="location">in <a href="CI_DB_result.html#method_unbuffered_row">
CI_DB_result</a> at line 527</div>
        <code>                    mixed
    <strong>unbuffered_row</strong>(string $type = &#039;object&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns an unbuffered row and move pointer to next row</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$type</td>
                <td><p>'array', 'object' or a custom class name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_num_fields">
        <div class="location">at line 85</div>
        <code>                    int
    <strong>num_fields</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Number of fields in the result set</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_list_fields">
        <div class="location">at line 99</div>
        <code>                    array
    <strong>list_fields</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch Field Names</p></p>                <p><p>Generates an array of column names</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_field_data">
        <div class="location">at line 120</div>
        <code>                    array
    <strong>field_data</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Field data</p></p>                <p><p>Generates an array of objects containing field meta-data</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_free_result">
        <div class="location">at line 142</div>
        <code>                    void
    <strong>free_result</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Free the result</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_data_seek">
        <div class="location">at line 163</div>
        <code>                    bool
    <strong>data_seek</strong>(int $n = 0)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Data Seek</p></p>                <p><p>Moves the internal pointer to the desired offset. We call
this internally before fetching results to make sure the
result set starts at zero.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__fetch_assoc">
        <div class="location">at line 179</div>
        <code>            protected        array
    <strong>_fetch_assoc</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Result - associative array</p></p>                <p><p>Returns the result set as an array</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__fetch_object">
        <div class="location">at line 194</div>
        <code>            protected        object
    <strong>_fetch_object</strong>(string $class_name = &#039;stdClass&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Result - object</p></p>                <p><p>Returns the result set as an object</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$class_name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Zip | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Zip" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Zip    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Zip</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Zip Compression Class</p></p>            <p><p>This class is based on a library I found at Zend:
<a href="http://www.zend.com/codex.php?id=696&amp;single=1">http://www.zend.com/codex.php?id=696&amp;single=1</a></p>
<p>The original library is a little rough around the edges so I
refactored it and added several additional methods -- Rick Ellis</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_zipdata">
                                                                                string
                                                                                
                                    </td>
                <td>$zipdata</td>
                <td class="last"><p>Zip data in string form</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_directory">
                                                                                string
                                                                                
                                    </td>
                <td>$directory</td>
                <td class="last"><p>Zip data for a directory in string form</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_entries">
                                                                                int
                                                                                
                                    </td>
                <td>$entries</td>
                <td class="last"><p>Number of files/folder in zip file</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_file_num">
                                                                                int
                                                                                
                                    </td>
                <td>$file_num</td>
                <td class="last"><p>Number of files in zip</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_offset">
                                                                                int
                                                                                
                                    </td>
                <td>$offset</td>
                <td class="last"><p>relative offset of local header</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_now">
                                                                                int
                                                                                
                                    </td>
                <td>$now</td>
                <td class="last"><p>Reference to time at init</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_compression_level">
                                                                                int
                                                                                
                                    </td>
                <td>$compression_level</td>
                <td class="last"><p>The level of compression</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_func_overload">
                    static                    protected                                        bool
                                                                                
                                    </td>
                <td>$func_overload</td>
                <td class="last"><p>mbstring.func_overload flag</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Initialize zip compression class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_add_dir">add_dir</a>(mixed $directory)
        
                                            <p><p>Add Directory</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method__get_mod_time">_get_mod_time</a>(string $dir)
        
                                            <p><p>Get file/directory modification time</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__add_dir">_add_dir</a>(string $dir, int $file_mtime, int $file_mdate)
        
                                            <p><p>Add Directory</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_add_data">add_data</a>(mixed $filepath, string $data = NULL)
        
                                            <p><p>Add Data to Zip</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__add_data">_add_data</a>(string $filepath, string $data, int $file_mtime, int $file_mdate)
        
                                            <p><p>Add Data to Zip</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_read_file">read_file</a>(string $path, bool $archive_filepath = FALSE)
        
                                            <p><p>Read the contents of a file and add it to the zip</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_read_dir">read_dir</a>(string $path, bool $preserve_filepath = TRUE, string $root_path = NULL)
        
                                            <p><p>Read a directory and add it to the zip.</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_zip">get_zip</a>()
        
                                            <p><p>Get the Zip file</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_archive">archive</a>(string $filepath)
        
                                            <p><p>Write File to the specified directory</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_download">download</a>(string $filename = &#039;backup.zip&#039;)
        
                                            <p>Download</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Zip.html">CI_Zip</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_clear_data">clear_data</a>()
        
                                            <p><p>Initialize Data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;int
                </div>
                <div class="col-md-8">
                    <a href="#method_strlen">strlen</a>(string $str)
        
                                            <p><p>Byte-safe strlen()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;string
                </div>
                <div class="col-md-8">
                    <a href="#method_substr">substr</a>(string $str, int $start, int $length = NULL)
        
                                            <p><p>Byte-safe substr()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 120</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize zip compression class</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_dir">
        <div class="location">at line 138</div>
        <code>                    void
    <strong>add_dir</strong>(mixed $directory)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Directory</p></p>                <p><p>Lets you add a virtual directory into which you can place files.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$directory</td>
                <td><p>the directory name. Can be string or array</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_mod_time">
        <div class="location">at line 162</div>
        <code>            protected        array
    <strong>_get_mod_time</strong>(string $dir)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get file/directory modification time</p></p>                <p><p>If this is a newly created file/dir, we will set the time to 'now'</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$dir</td>
                <td><p>path to file</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td>filemtime/filemdate</td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__add_dir">
        <div class="location">at line 183</div>
        <code>            protected        void
    <strong>_add_dir</strong>(string $dir, int $file_mtime, int $file_mdate)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Directory</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$dir</td>
                <td><p>the directory name</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$file_mtime</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$file_mdate</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_data">
        <div class="location">at line 235</div>
        <code>                    void
    <strong>add_data</strong>(mixed $filepath, string $data = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Data to Zip</p></p>                <p><p>Lets you add files to the archive. If the path is included
in the filename it will be placed within a directory. Make
sure you use add_dir() first to create the folder.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$filepath</td>
                <td><p>A single filepath or an array of file =&gt; data pairs</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$data</td>
                <td><p>Single file contents</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__add_data">
        <div class="location">at line 263</div>
        <code>            protected        void
    <strong>_add_data</strong>(string $filepath, string $data, int $file_mtime, int $file_mdate)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Data to Zip</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$filepath</td>
                <td><p>the file name/path</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$data</td>
                <td><p>the data to be encoded</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$file_mtime</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$file_mdate</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_read_file">
        <div class="location">at line 314</div>
        <code>                    bool
    <strong>read_file</strong>(string $path, bool $archive_filepath = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Read the contents of a file and add it to the zip</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$archive_filepath</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_read_dir">
        <div class="location">at line 353</div>
        <code>                    bool
    <strong>read_dir</strong>(string $path, bool $preserve_filepath = TRUE, string $root_path = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Read a directory and add it to the zip.</p></p>                <p><p>This function recursively reads a folder and everything it contains (including
sub-folders) and creates a zip based on it. Whatever directory structure
is in the original file path will be recreated in the zip file.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td><p>path to source directory</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$preserve_filepath</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$root_path</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_zip">
        <div class="location">at line 401</div>
        <code>                    string
    <strong>get_zip</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the Zip file</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>(binary encoded)</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_archive">
        <div class="location">at line 428</div>
        <code>                    bool
    <strong>archive</strong>(string $filepath)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Write File to the specified directory</p></p>                <p><p>Lets you write a file</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$filepath</td>
                <td><p>the file name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_download">
        <div class="location">at line 459</div>
        <code>                    void
    <strong>download</strong>(string $filename = &#039;backup.zip&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Download</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$filename</td>
                <td><p>the file name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_clear_data">
        <div class="location">at line 483</div>
        <code>                    <a href="CI_Zip.html">CI_Zip</a>
    <strong>clear_data</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize Data</p></p>                <p><p>Lets you clear current zip data. Useful if you need to create
multiple zips with different data.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Zip.html">CI_Zip</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_strlen">
        <div class="location">at line 501</div>
        <code>        static    protected        int
    <strong>strlen</strong>(string $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe strlen()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_substr">
        <div class="location">at line 518</div>
        <code>        static    protected        string
    <strong>substr</strong>(string $str, int $start, int $length = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe substr()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$start</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$length</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

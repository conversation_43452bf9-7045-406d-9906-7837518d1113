<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Security | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Security" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Security    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Security</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Security Class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_filename_bad_chars">
                                                                                array
                                                                                
                                    </td>
                <td>$filename_bad_chars</td>
                <td class="last"><p>List of sanitize filename strings</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_charset">
                                                                                string
                                                                                
                                    </td>
                <td>$charset</td>
                <td class="last"><p>Character set</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__xss_hash">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_xss_hash</td>
                <td class="last"><p>XSS Hash</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__csrf_hash">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_csrf_hash</td>
                <td class="last"><p>CSRF Hash</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__csrf_expire">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$_csrf_expire</td>
                <td class="last"><p>CSRF Expire time</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__csrf_token_name">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_csrf_token_name</td>
                <td class="last"><p>CSRF Token name</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__csrf_cookie_name">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_csrf_cookie_name</td>
                <td class="last"><p>CSRF Cookie name</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__never_allowed_str">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_never_allowed_str</td>
                <td class="last"><p>List of never allowed strings</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__never_allowed_regex">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_never_allowed_regex</td>
                <td class="last"><p>List of never allowed regex replacements</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Security.html">CI_Security</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_csrf_verify">csrf_verify</a>()
        
                                            <p><p>CSRF Verify</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Security.html">CI_Security</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_csrf_set_cookie">csrf_set_cookie</a>()
        
                                            <p><p>CSRF Set Cookie</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_csrf_show_error">csrf_show_error</a>()
        
                                            <p><p>Show CSRF Error</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_csrf_hash">get_csrf_hash</a>()
        
                                            <p><p>Get CSRF Hash</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_csrf_token_name">get_csrf_token_name</a>()
        
                                            <p><p>Get CSRF Token Name</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_xss_clean">xss_clean</a>(string|string[] $str, bool $is_image = FALSE)
        
                                            <p><p>XSS Clean</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_xss_hash">xss_hash</a>()
        
                                            <p><p>XSS Hash</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_random_bytes">get_random_bytes</a>(int $length)
        
                                            <p><p>Get random bytes</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_entity_decode">entity_decode</a>(string $str, string $charset = NULL)
        
                                            <p><p>HTML Entities Decode</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_sanitize_filename">sanitize_filename</a>(string $str, bool $relative_path = FALSE)
        
                                            <p><p>Sanitize Filename</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_strip_image_tags">strip_image_tags</a>(string $str)
        
                                            <p><p>Strip Image Tags</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__urldecodespaces">_urldecodespaces</a>(array $matches)
        
                                            <p><p>URL-decode taking spaces into account</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__compact_exploded_words">_compact_exploded_words</a>(array $matches)
        
                                            <p><p>Compact Exploded Words</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__sanitize_naughty_html">_sanitize_naughty_html</a>(array $matches)
        
                                            <p><p>Sanitize Naughty HTML</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__js_link_removal">_js_link_removal</a>(array $match)
        
                                            <p><p>JS Link Removal</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__js_img_removal">_js_img_removal</a>(array $match)
        
                                            <p><p>JS Image Removal</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__convert_attribute">_convert_attribute</a>(array $match)
        
                                            <p><p>Attribute Conversion</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__filter_attributes">_filter_attributes</a>(string $str)
        
                                            <p><p>Filter Attributes</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__decode_entity">_decode_entity</a>(array $match)
        
                                            <p><p>HTML Entity Decode Callback</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__do_never_allowed">_do_never_allowed</a>($str)
        
                                            <p><p>Do Never Allowed</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__csrf_set_hash">_csrf_set_hash</a>()
        
                                            <p><p>Set CSRF Hash and Cookie</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 172</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_csrf_verify">
        <div class="location">at line 208</div>
        <code>                    <a href="CI_Security.html">CI_Security</a>
    <strong>csrf_verify</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>CSRF Verify</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Security.html">CI_Security</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_csrf_set_cookie">
        <div class="location">at line 265</div>
        <code>                    <a href="CI_Security.html">CI_Security</a>
    <strong>csrf_set_cookie</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>CSRF Set Cookie</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Security.html">CI_Security</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_csrf_show_error">
        <div class="location">at line 296</div>
        <code>                    void
    <strong>csrf_show_error</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Show CSRF Error</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_csrf_hash">
        <div class="location">at line 309</div>
        <code>                    string
    <strong>get_csrf_hash</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get CSRF Hash</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>CSRF hash</p></td>
        </tr>
    </table>

            
            
                            <h4>See also</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>
                                            CI_Security::$_csrf_hash
                                    </td>
                <td></td>
            </tr>
            </table>

            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_csrf_token_name">
        <div class="location">at line 322</div>
        <code>                    string
    <strong>get_csrf_token_name</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get CSRF Token Name</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>CSRF token name</p></td>
        </tr>
    </table>

            
            
                            <h4>See also</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>
                                            CI_Security::$_csrf_token_name
                                    </td>
                <td></td>
            </tr>
            </table>

            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_xss_clean">
        <div class="location">at line 355</div>
        <code>                    string
    <strong>xss_clean</strong>(string|string[] $str, bool $is_image = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>XSS Clean</p></p>                <p><p>Sanitizes data so that Cross Site Scripting Hacks can be
prevented.  This method does a fair amount of work but
it is extremely thorough, designed to prevent even the
most obscure XSS attempts.  Nothing is ever 100% foolproof,
of course, but I haven't been able to get anything passed
the filter.</p>
<p>Note: Should only be used to deal with data upon submission.
It's not something that should be used for general
runtime processing.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string|string[]</td>
                <td>$str</td>
                <td><p>Input data</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$is_image</td>
                <td><p>Whether the input is an image</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_xss_hash">
        <div class="location">at line 588</div>
        <code>                    string
    <strong>xss_hash</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>XSS Hash</p></p>                <p><p>Generates the XSS hash if needed and returns it.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>XSS hash</p></td>
        </tr>
    </table>

            
            
                            <h4>See also</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>
                                            CI_Security::$_xss_hash
                                    </td>
                <td></td>
            </tr>
            </table>

            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_random_bytes">
        <div class="location">at line 609</div>
        <code>                    string
    <strong>get_random_bytes</strong>(int $length)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get random bytes</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$length</td>
                <td><p>Output length</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_entity_decode">
        <div class="location">at line 678</div>
        <code>                    string
    <strong>entity_decode</strong>(string $str, string $charset = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>HTML Entities Decode</p></p>                <p><p>A replacement for html_entity_decode()</p>
<p>The reason we are not using html_entity_decode() by itself is because
while it is not technically correct to leave out the semicolon
at the end of an entity most browsers will still interpret the entity
correctly. html_entity_decode() does not convert entities without
semicolons, so we are left with our own little solution here. Bummer.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td>Input</td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$charset</td>
                <td><p>Character set</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_sanitize_filename">
        <div class="location">at line 753</div>
        <code>                    string
    <strong>sanitize_filename</strong>(string $str, bool $relative_path = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sanitize Filename</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td><p>Input file name</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$relative_path</td>
                <td><p>Whether to preserve paths</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_strip_image_tags">
        <div class="location">at line 783</div>
        <code>                    string
    <strong>strip_image_tags</strong>(string $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Strip Image Tags</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__urldecodespaces">
        <div class="location">at line 804</div>
        <code>            protected        string
    <strong>_urldecodespaces</strong>(array $matches)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>URL-decode taking spaces into account</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$matches</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
                            <h4>See also</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>
                                            <a href="https://github.com/bcit-ci/CodeIgniter/issues/4877">https://github.com/bcit-ci/CodeIgniter/issues/4877</a>
                                    </td>
                <td></td>
            </tr>
            </table>

            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__compact_exploded_words">
        <div class="location">at line 825</div>
        <code>            protected        string
    <strong>_compact_exploded_words</strong>(array $matches)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Compact Exploded Words</p></p>                <p><p>Callback method for xss_clean() to remove whitespace from
things like 'j a v a s c r i p t'.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$matches</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__sanitize_naughty_html">
        <div class="location">at line 841</div>
        <code>            protected        string
    <strong>_sanitize_naughty_html</strong>(array $matches)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sanitize Naughty HTML</p></p>                <p><p>Callback method for xss_clean() to remove naughty HTML elements.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$matches</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__js_link_removal">
        <div class="location">at line 936</div>
        <code>            protected        string
    <strong>_js_link_removal</strong>(array $match)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>JS Link Removal</p></p>                <p><p>Callback method for xss_clean() to sanitize links.</p>
<p>This limits the PCRE backtracks, making it more performance friendly
and prevents PREG_BACKTRACK_LIMIT_ERROR from being triggered in
PHP 5.2+ on link-heavy strings.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$match</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__js_img_removal">
        <div class="location">at line 964</div>
        <code>            protected        string
    <strong>_js_img_removal</strong>(array $match)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>JS Image Removal</p></p>                <p><p>Callback method for xss_clean() to sanitize image tags.</p>
<p>This limits the PCRE backtracks, making it more performance friendly
and prevents PREG_BACKTRACK_LIMIT_ERROR from being triggered in
PHP 5.2+ on image tag heavy strings.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$match</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__convert_attribute">
        <div class="location">at line 986</div>
        <code>            protected        string
    <strong>_convert_attribute</strong>(array $match)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Attribute Conversion</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$match</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__filter_attributes">
        <div class="location">at line 1003</div>
        <code>            protected        string
    <strong>_filter_attributes</strong>(string $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Filter Attributes</p></p>                <p><p>Filters tag attributes for consistency and safety.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__decode_entity">
        <div class="location">at line 1026</div>
        <code>            protected        string
    <strong>_decode_entity</strong>(array $match)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>HTML Entity Decode Callback</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$match</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__do_never_allowed">
        <div class="location">at line 1049</div>
        <code>            protected        string
    <strong>_do_never_allowed</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Do Never Allowed</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__csrf_set_hash">
        <div class="location">at line 1068</div>
        <code>            protected        string
    <strong>_csrf_set_hash</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set CSRF Hash and Cookie</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

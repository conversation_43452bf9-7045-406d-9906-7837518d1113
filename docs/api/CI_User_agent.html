<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_User_agent | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_User_agent" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_User_agent    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_User_agent</strong>
</p>

        
    
        

            <div class="description">
            <p><p>User Agent Class</p></p>            <p><p>Identifies the platform, browser, robot, or mobile device of the browsing agent</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_agent">
                                                                                string
                                                                                
                                    </td>
                <td>$agent</td>
                <td class="last"><p>Current user-agent</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_is_browser">
                                                                                bool
                                                                                
                                    </td>
                <td>$is_browser</td>
                <td class="last"><p>Flag for if the user-agent belongs to a browser</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_is_robot">
                                                                                bool
                                                                                
                                    </td>
                <td>$is_robot</td>
                <td class="last"><p>Flag for if the user-agent is a robot</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_is_mobile">
                                                                                bool
                                                                                
                                    </td>
                <td>$is_mobile</td>
                <td class="last"><p>Flag for if the user-agent is a mobile browser</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_languages">
                                                                                array
                                                                                
                                    </td>
                <td>$languages</td>
                <td class="last"><p>Languages accepted by the current user agent</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_charsets">
                                                                                array
                                                                                
                                    </td>
                <td>$charsets</td>
                <td class="last"><p>Character sets accepted by the current user agent</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_platforms">
                                                                                array
                                                                                
                                    </td>
                <td>$platforms</td>
                <td class="last"><p>List of platforms to compare against current user agent</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_browsers">
                                                                                array
                                                                                
                                    </td>
                <td>$browsers</td>
                <td class="last"><p>List of browsers to compare against current user agent</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_mobiles">
                                                                                array
                                                                                
                                    </td>
                <td>$mobiles</td>
                <td class="last"><p>List of mobile browsers to compare against current user agent</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_robots">
                                                                                array
                                                                                
                                    </td>
                <td>$robots</td>
                <td class="last"><p>List of robots to compare against current user agent</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_platform">
                                                                                string
                                                                                
                                    </td>
                <td>$platform</td>
                <td class="last"><p>Current user-agent platform</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_browser">
                                                                                string
                                                                                
                                    </td>
                <td>$browser</td>
                <td class="last"><p>Current user-agent browser</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_version">
                                                                                string
                                                                                
                                    </td>
                <td>$version</td>
                <td class="last"><p>Current user-agent version</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_mobile">
                                                                                string
                                                                                
                                    </td>
                <td>$mobile</td>
                <td class="last"><p>Current user-agent mobile name</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_robot">
                                                                                string
                                                                                
                                    </td>
                <td>$robot</td>
                <td class="last"><p>Current user-agent robot name</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_referer">
                                                                                mixed
                                                                                
                                    </td>
                <td>$referer</td>
                <td class="last"><p>HTTP Referer</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p>Constructor</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__load_agent_file">_load_agent_file</a>()
        
                                            <p><p>Compile the User Agent Data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__compile_data">_compile_data</a>()
        
                                            <p><p>Compile the User Agent Data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__set_platform">_set_platform</a>()
        
                                            <p><p>Set the Platform</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__set_browser">_set_browser</a>()
        
                                            <p><p>Set the Browser</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__set_robot">_set_robot</a>()
        
                                            <p><p>Set the Robot</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__set_mobile">_set_mobile</a>()
        
                                            <p><p>Set the Mobile Device</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__set_languages">_set_languages</a>()
        
                                            <p><p>Set the accepted languages</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__set_charsets">_set_charsets</a>()
        
                                            <p><p>Set the accepted character sets</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_browser">is_browser</a>(string $key = NULL)
        
                                            <p><p>Is Browser</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_robot">is_robot</a>(string $key = NULL)
        
                                            <p><p>Is Robot</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_mobile">is_mobile</a>(string $key = NULL)
        
                                            <p><p>Is Mobile</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_referral">is_referral</a>()
        
                                            <p><p>Is this a referral from another site?</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_agent_string">agent_string</a>()
        
                                            <p><p>Agent String</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_platform">platform</a>()
        
                                            <p><p>Get Platform</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_browser">browser</a>()
        
                                            <p><p>Get Browser Name</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_version">version</a>()
        
                                            <p><p>Get the Browser Version</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_robot">robot</a>()
        
                                            <p><p>Get The Robot Name</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_mobile">mobile</a>()
        
                                            <p><p>Get the Mobile Device</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_referrer">referrer</a>()
        
                                            <p><p>Get the referrer</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_languages">languages</a>()
        
                                            <p><p>Get the accepted languages</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_charsets">charsets</a>()
        
                                            <p><p>Get the accepted Character Sets</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_accept_lang">accept_lang</a>(string $lang = &#039;en&#039;)
        
                                            <p><p>Test for a particular language</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_accept_charset">accept_charset</a>(string $charset = &#039;utf-8&#039;)
        
                                            <p><p>Test for a particular character set</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_parse">parse</a>(string $string)
        
                                            <p><p>Parse a custom user-agent string</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 174</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Constructor</p>                <p><p>Sets the User Agent and runs the compilation routine</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__load_agent_file">
        <div class="location">at line 194</div>
        <code>            protected        bool
    <strong>_load_agent_file</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Compile the User Agent Data</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__compile_data">
        <div class="location">at line 252</div>
        <code>            protected        bool
    <strong>_compile_data</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Compile the User Agent Data</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_platform">
        <div class="location">at line 272</div>
        <code>            protected        bool
    <strong>_set_platform</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the Platform</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_browser">
        <div class="location">at line 297</div>
        <code>            protected        bool
    <strong>_set_browser</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the Browser</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_robot">
        <div class="location">at line 324</div>
        <code>            protected        bool
    <strong>_set_robot</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the Robot</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_mobile">
        <div class="location">at line 350</div>
        <code>            protected        bool
    <strong>_set_mobile</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the Mobile Device</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_languages">
        <div class="location">at line 375</div>
        <code>            protected        void
    <strong>_set_languages</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the accepted languages</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_charsets">
        <div class="location">at line 395</div>
        <code>            protected        void
    <strong>_set_charsets</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the accepted character sets</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_browser">
        <div class="location">at line 416</div>
        <code>                    bool
    <strong>is_browser</strong>(string $key = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is Browser</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_robot">
        <div class="location">at line 441</div>
        <code>                    bool
    <strong>is_robot</strong>(string $key = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is Robot</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_mobile">
        <div class="location">at line 466</div>
        <code>                    bool
    <strong>is_mobile</strong>(string $key = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is Mobile</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_referral">
        <div class="location">at line 490</div>
        <code>                    bool
    <strong>is_referral</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is this a referral from another site?</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_agent_string">
        <div class="location">at line 517</div>
        <code>                    string
    <strong>agent_string</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Agent String</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_platform">
        <div class="location">at line 529</div>
        <code>                    string
    <strong>platform</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Platform</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_browser">
        <div class="location">at line 541</div>
        <code>                    string
    <strong>browser</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Browser Name</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_version">
        <div class="location">at line 553</div>
        <code>                    string
    <strong>version</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the Browser Version</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_robot">
        <div class="location">at line 565</div>
        <code>                    string
    <strong>robot</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get The Robot Name</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mobile">
        <div class="location">at line 576</div>
        <code>                    string
    <strong>mobile</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the Mobile Device</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_referrer">
        <div class="location">at line 588</div>
        <code>                    bool
    <strong>referrer</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the referrer</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_languages">
        <div class="location">at line 600</div>
        <code>                    array
    <strong>languages</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the accepted languages</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_charsets">
        <div class="location">at line 617</div>
        <code>                    array
    <strong>charsets</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the accepted Character Sets</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_accept_lang">
        <div class="location">at line 635</div>
        <code>                    bool
    <strong>accept_lang</strong>(string $lang = &#039;en&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Test for a particular language</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$lang</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_accept_charset">
        <div class="location">at line 648</div>
        <code>                    bool
    <strong>accept_charset</strong>(string $charset = &#039;utf-8&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Test for a particular character set</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$charset</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_parse">
        <div class="location">at line 661</div>
        <code>                    void
    <strong>parse</strong>(string $string)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Parse a custom user-agent string</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$string</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

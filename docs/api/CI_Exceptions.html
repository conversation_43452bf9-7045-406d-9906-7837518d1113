<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Exceptions | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Exceptions" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Exceptions    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Exceptions</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Exceptions Class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_ob_level">
                                                                                int
                                                                                
                                    </td>
                <td>$ob_level</td>
                <td class="last"><p>Nesting level of the output buffering mechanism</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_levels">
                                                                                array
                                                                                
                                    </td>
                <td>$levels</td>
                <td class="last"><p>List of available error levels</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_log_exception">log_exception</a>(int $severity, string $message, string $filepath, int $line)
        
                                            <p><p>Exception Logger</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_show_404">show_404</a>(string $page = &#039;&#039;, bool $log_error = TRUE)
        
                                            <p><p>404 Error Handler</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_show_error">show_error</a>(string $heading, string|string[] $message, string $template = &#039;error_general&#039;, int $status_code = 500)
        
                                            <p><p>General Error Page</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_show_exception">show_exception</a>($exception)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_show_php_error">show_php_error</a>(int $severity, string $message, string $filepath, int $line)
        
                                            <p><p>Native PHP error handler</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 83</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_log_exception">
        <div class="location">at line 102</div>
        <code>                    void
    <strong>log_exception</strong>(int $severity, string $message, string $filepath, int $line)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Exception Logger</p></p>                <p><p>Logs PHP generated error messages</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$severity</td>
                <td><p>Log level</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$message</td>
                <td><p>Error message</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$filepath</td>
                <td><p>File path</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$line</td>
                <td><p>Line number</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_show_404">
        <div class="location">at line 119</div>
        <code>                    void
    <strong>show_404</strong>(string $page = &#039;&#039;, bool $log_error = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>404 Error Handler</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$page</td>
                <td><p>Page URI</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$log_error</td>
                <td><p>Whether to log the error</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_show_error">
        <div class="location">at line 157</div>
        <code>                    string
    <strong>show_error</strong>(string $heading, string|string[] $message, string $template = &#039;error_general&#039;, int $status_code = 500)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>General Error Page</p></p>                <p><p>Takes an error message as input (either as a string or an array)
and displays it using the specified template.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$heading</td>
                <td><p>Page heading</p></td>
            </tr>
                    <tr>
                <td>string|string[]</td>
                <td>$message</td>
                <td><p>Error message</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$template</td>
                <td><p>Template name</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$status_code</td>
                <td><p>(default: 500)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>Error page output</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_show_exception">
        <div class="location">at line 190</div>
        <code>                    
    <strong>show_exception</strong>($exception)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$exception</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_show_php_error">
        <div class="location">at line 236</div>
        <code>                    void
    <strong>show_php_error</strong>(int $severity, string $message, string $filepath, int $line)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Native PHP error handler</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$severity</td>
                <td><p>Error level</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$message</td>
                <td><p>Error message</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$filepath</td>
                <td><p>File path</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$line</td>
                <td><p>Line number</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Typography | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Typography" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Typography    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Typography</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Typography Class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_block_elements">
                                                                                string
                                                                                
                                    </td>
                <td>$block_elements</td>
                <td class="last"><p>Block level elements that should not be wrapped inside <p> tags</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_skip_elements">
                                                                                string
                                                                                
                                    </td>
                <td>$skip_elements</td>
                <td class="last"><p>Elements that should not have <p> and <br /> tags within them.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_inline_elements">
                                                                                string
                                                                                
                                    </td>
                <td>$inline_elements</td>
                <td class="last"><p>Tags we want the parser to completely ignore when splitting the string.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_inner_block_required">
                                                                                array
                                                                                
                                    </td>
                <td>$inner_block_required</td>
                <td class="last"><p>array of block level elements that require inner content to be within another block level element</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_last_block_element">
                                                                                string
                                                                                
                                    </td>
                <td>$last_block_element</td>
                <td class="last"><p>the last block element parsed</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_protect_braced_quotes">
                                                                                bool
                                                                                
                                    </td>
                <td>$protect_braced_quotes</td>
                <td class="last"><p>whether or not to protect quotes within { curly braces }</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_auto_typography">auto_typography</a>($str, $reduce_linebreaks = FALSE)
        
                                            <p><p>Auto Typography</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_format_characters">format_characters</a>($str)
        
                                            <p><p>Format Characters</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__format_newlines">_format_newlines</a>($str)
        
                                            <p><p>Format Newlines</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__protect_characters">_protect_characters</a>($match)
        
                                            <p><p>Protect Characters</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_nl2br_except_pre">nl2br_except_pre</a>($str)
        
                                            <p><p>Convert newlines to HTML line breaks except within PRE tags</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method_auto_typography">
        <div class="location">at line 108</div>
        <code>                    string
    <strong>auto_typography</strong>($str, $reduce_linebreaks = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Auto Typography</p></p>                <p><p>This function converts text, making it typographically correct:</p>
<ul>
<li>Converts double spaces into paragraphs.</li>
<li>Converts single line breaks into <br /> tags</li>
<li>Converts single and double quotes into correctly facing curly quote entities.</li>
<li>Converts three dots into ellipsis.</li>
<li>Converts double dashes into em-dashes.
<ul>
<li>Converts two spaces into entities</li>
</ul></li>
</ul></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$reduce_linebreaks</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_format_characters">
        <div class="location">at line 293</div>
        <code>                    string
    <strong>format_characters</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Format Characters</p></p>                <p><p>This function mainly converts double and single quotes
to curly entities, but it also converts em-dashes,
double spaces, and ampersands</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__format_newlines">
        <div class="location">at line 356</div>
        <code>            protected        string
    <strong>_format_newlines</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Format Newlines</p></p>                <p><p>Converts newline characters into either <p> tags or <br /></p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__protect_characters">
        <div class="location">at line 396</div>
        <code>            protected        string
    <strong>_protect_characters</strong>($match)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Protect Characters</p></p>                <p><p>Protects special characters from being formatted later
We don't want quotes converted within tags so we'll temporarily convert them to {@DQ} and {@SQ}
and we don't want double dashes converted to emdash entities, so they are marked with {@DD}
likewise double spaces are converted to {@NBS} to prevent entity conversion</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$match</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_nl2br_except_pre">
        <div class="location">at line 409</div>
        <code>                    string
    <strong>nl2br_except_pre</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Convert newlines to HTML line breaks except within PRE tags</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

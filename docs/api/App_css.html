<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>App_css | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:App_css" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>App_css    
            </h1>
    </div>

    
    <p>        class
    <strong>App_css</strong>        extends <a href="App_assets.html">App_assets</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_registered">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$registered</td>
                <td class="last"><p>All registered assets</p></td>
                <td><small>from&nbsp;<a href="App_assets.html#property_registered">
App_assets</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_to_do">
                                        protected                                        array
                                                                                
                                            <i>Since: 2.3.0</i>
                        <br>
                                    </td>
                <td>$to_do</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_assets.html#property_to_do">
App_assets</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_done">
                                                                                array
                                                                                
                                            <i>Since: 2.3.0</i>
                        <br>
                                    </td>
                <td>$done</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_assets.html#property_done">
App_assets</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_handleGroups">
                                        protected                                        array
                                                                                
                                            <i>Since: 2.3.0</i>
                        <br>
                                    </td>
                <td>$handleGroups</td>
                <td class="last"><p>An array of handle handleGroups to enqueue.</p></td>
                <td><small>from&nbsp;<a href="App_assets.html#property_handleGroups">
App_assets</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_themeGroup">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$themeGroup</td>
                <td class="last"><p>Default group for assets in the customers area theme</p></td>
                <td><small>from&nbsp;<a href="App_assets.html#property_themeGroup">
App_assets</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_theme">theme</a>($name, $data, $deps = [])
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_theme">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_default_theme_group">default_theme_group</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_default_theme_group">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_remove">remove</a>($name, $group = &#039;admin&#039;)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_remove">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_core_version">core_version</a>()
        
                                            <p><p>Used for core js/css version</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_core_version">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_core_file">core_file</a>($path, $fileName)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_core_file">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getMinifiedFileName">getMinifiedFileName</a>($nonMinifiedFileName, $path)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_getMinifiedFileName">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_initializeEmptyGroup">initializeEmptyGroup</a>($group)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_initializeEmptyGroup">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_compileUrl">compileUrl</a>($path, $version = true)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_compileUrl">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_attributesToString">attributesToString</a>($id, $defaults, $asset)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_attributesToString">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_removeEmptyAttributes">removeEmptyAttributes</a>($attributes)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_removeEmptyAttributes">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_removeEmptyStringAttributes">removeEmptyStringAttributes</a>($parsedAttributes, $id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_removeEmptyStringAttributes">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_strStartsWith">strStartsWith</a>($haystack, $needle)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_strStartsWith">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_do_items">do_items</a>($handles, $assetsGroup = false, $group = false)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_do_items">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_all_deps">all_deps</a>($handles, $assetsGroup = false, $recursion = false, $group = false)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_all_deps">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_group">set_group</a>($handle, $recursion, $group)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_assets.html#method_set_group">
App_assets</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_add">add</a>($name, $data, $group = &#039;admin&#039;, $deps = [])
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get">get</a>($group = &#039;admin&#039;)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_compile">compile</a>($group = &#039;admin&#039;)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_coreStylesheet">coreStylesheet</a>($path, $fileName)
        <small><span class="label label-danger">deprecated</span></small>
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method_theme">
        <div class="location">in <a href="App_assets.html#method_theme">
App_assets</a> at line 41</div>
        <code>                    
    <strong>theme</strong>($name, $data, $deps = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$name</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$deps</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_default_theme_group">
        <div class="location">in <a href="App_assets.html#method_default_theme_group">
App_assets</a> at line 46</div>
        <code>                    
    <strong>default_theme_group</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_remove">
        <div class="location">in <a href="App_assets.html#method_remove">
App_assets</a> at line 51</div>
        <code>                    
    <strong>remove</strong>($name, $group = &#039;admin&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$name</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$group</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_core_version">
        <div class="location">in <a href="App_assets.html#method_core_version">
App_assets</a> at line 66</div>
        <code>                    mixed
    <strong>core_version</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Used for core js/css version</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_core_file">
        <div class="location">in <a href="App_assets.html#method_core_file">
App_assets</a> at line 71</div>
        <code>                    
    <strong>core_file</strong>($path, $fileName)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$path</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$fileName</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getMinifiedFileName">
        <div class="location">in <a href="App_assets.html#method_getMinifiedFileName">
App_assets</a> at line 80</div>
        <code>                    
    <strong>getMinifiedFileName</strong>($nonMinifiedFileName, $path)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$nonMinifiedFileName</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$path</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initializeEmptyGroup">
        <div class="location">in <a href="App_assets.html#method_initializeEmptyGroup">
App_assets</a> at line 101</div>
        <code>            protected        
    <strong>initializeEmptyGroup</strong>($group)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$group</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_compileUrl">
        <div class="location">in <a href="App_assets.html#method_compileUrl">
App_assets</a> at line 110</div>
        <code>            protected        
    <strong>compileUrl</strong>($path, $version = true)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$path</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$version</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_attributesToString">
        <div class="location">in <a href="App_assets.html#method_attributesToString">
App_assets</a> at line 126</div>
        <code>            protected        
    <strong>attributesToString</strong>($id, $defaults, $asset)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$defaults</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$asset</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_removeEmptyAttributes">
        <div class="location">in <a href="App_assets.html#method_removeEmptyAttributes">
App_assets</a> at line 137</div>
        <code>            protected        
    <strong>removeEmptyAttributes</strong>($attributes)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$attributes</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_removeEmptyStringAttributes">
        <div class="location">in <a href="App_assets.html#method_removeEmptyStringAttributes">
App_assets</a> at line 148</div>
        <code>            protected        
    <strong>removeEmptyStringAttributes</strong>($parsedAttributes, $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$parsedAttributes</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_strStartsWith">
        <div class="location">in <a href="App_assets.html#method_strStartsWith">
App_assets</a> at line 156</div>
        <code>            protected        
    <strong>strStartsWith</strong>($haystack, $needle)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$haystack</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$needle</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_do_items">
        <div class="location">in <a href="App_assets.html#method_do_items">
App_assets</a> at line 164</div>
        <code>            protected        
    <strong>do_items</strong>($handles, $assetsGroup = false, $group = false)
        </code>
    </h3>
    <div class="details"><i>Since: 2.3.0</i>
            <br>    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$handles</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$assetsGroup</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$group</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_all_deps">
        <div class="location">in <a href="App_assets.html#method_all_deps">
App_assets</a> at line 191</div>
        <code>            protected        
    <strong>all_deps</strong>($handles, $assetsGroup = false, $recursion = false, $group = false)
        </code>
    </h3>
    <div class="details"><i>Since: 2.3.0</i>
            <br>    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$handles</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$assetsGroup</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$recursion</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$group</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_group">
        <div class="location">in <a href="App_assets.html#method_set_group">
App_assets</a> at line 245</div>
        <code>            protected        
    <strong>set_group</strong>($handle, $recursion, $group)
        </code>
    </h3>
    <div class="details"><i>Since: 2.3.0</i>
            <br>    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$handle</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$recursion</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$group</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add">
        <div class="location">at line 9</div>
        <code>                    
    <strong>add</strong>($name, $data, $group = &#039;admin&#039;, $deps = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$name</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$group</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$deps</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get">
        <div class="location">at line 30</div>
        <code>                    
    <strong>get</strong>($group = &#039;admin&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$group</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_compile">
        <div class="location">at line 35</div>
        <code>                    
    <strong>compile</strong>($group = &#039;admin&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$group</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_coreStylesheet">
        <div class="location">at line 79</div>
        <code>                    
    <strong>coreStylesheet</strong>($path, $fileName)
        <small><span class="label label-danger">deprecated</span></small></code>
    </h3>
    <div class="details">    
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td>2.3.0</td>
                    <td></td>
                </tr>
                    </p>
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$path</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$fileName</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

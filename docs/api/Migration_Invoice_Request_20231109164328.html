<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Migration_Invoice_Request_20231109164328 | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Migration_Invoice_Request_20231109164328" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Migration_Invoice_Request_20231109164328    
            </h1>
    </div>

    
    <p>        class
    <strong>Migration_Invoice_Request_20231109164328</strong>        extends <a href="CI_Migration.html">CI_Migration</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property__migration_enabled">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_migration_enabled</td>
                <td class="last"><p>Whether the library is enabled</p></td>
                <td><small>from&nbsp;<a href="CI_Migration.html#property__migration_enabled">
CI_Migration</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__migration_type">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_migration_type</td>
                <td class="last"><p>Migration numbering type</p></td>
                <td><small>from&nbsp;<a href="CI_Migration.html#property__migration_type">
CI_Migration</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__migration_path">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_migration_path</td>
                <td class="last"><p>Path to migration classes</p></td>
                <td><small>from&nbsp;<a href="CI_Migration.html#property__migration_path">
CI_Migration</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__migration_version">
                                        protected                                        mixed
                                                                                
                                    </td>
                <td>$_migration_version</td>
                <td class="last"><p>Current migration version</p></td>
                <td><small>from&nbsp;<a href="CI_Migration.html#property__migration_version">
CI_Migration</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__migration_table">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_migration_table</td>
                <td class="last"><p>Database table with migration info</p></td>
                <td><small>from&nbsp;<a href="CI_Migration.html#property__migration_table">
CI_Migration</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__migration_auto_latest">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_migration_auto_latest</td>
                <td class="last"><p>Whether to automatically run migrations</p></td>
                <td><small>from&nbsp;<a href="CI_Migration.html#property__migration_auto_latest">
CI_Migration</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__migration_regex">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_migration_regex</td>
                <td class="last"><p>Migration basename regex</p></td>
                <td><small>from&nbsp;<a href="CI_Migration.html#property__migration_regex">
CI_Migration</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__error_string">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_error_string</td>
                <td class="last"><p>Error message</p></td>
                <td><small>from&nbsp;<a href="CI_Migration.html#property__error_string">
CI_Migration</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $config = array())
        
                                            <p><p>Initialize Migration Class</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Migration.html#method___construct">
CI_Migration</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_version">version</a>(string $target_version)
        
                                            <p><p>Migrate to a schema version</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Migration.html#method_version">
CI_Migration</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_latest">latest</a>()
        
                                            <p><p>Sets the schema to the latest migration</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Migration.html#method_latest">
CI_Migration</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_current">current</a>()
        
                                            <p><p>Sets the schema to the migration version set in config</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Migration.html#method_current">
CI_Migration</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_error_string">error_string</a>()
        
                                            <p><p>Error string</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Migration.html#method_error_string">
CI_Migration</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_find_migrations">find_migrations</a>()
        
                                            <p><p>Retrieves list of available migration scripts</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Migration.html#method_find_migrations">
CI_Migration</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_migration_number">_get_migration_number</a>(string $migration)
        
                                            <p><p>Extracts the migration number from a filename</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Migration.html#method__get_migration_number">
CI_Migration</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_migration_name">_get_migration_name</a>(string $migration)
        
                                            <p><p>Extracts the migration class name from a filename</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Migration.html#method__get_migration_name">
CI_Migration</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_version">_get_version</a>()
        
                                            <p><p>Retrieves current schema version</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Migration.html#method__get_version">
CI_Migration</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__update_version">_update_version</a>(string $migration)
        
                                            <p><p>Stores the current schema version</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Migration.html#method__update_version">
CI_Migration</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method___get">__get</a>(string $var)
        
                                            <p><p>Enable the use of CI super-global</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Migration.html#method___get">
CI_Migration</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_up">up</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_down">down</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">in <a href="CI_Migration.html#method___construct">
CI_Migration</a> at line 116</div>
        <code>                    void
    <strong>__construct</strong>(array $config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize Migration Class</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_version">
        <div class="location">in <a href="CI_Migration.html#method_version">
CI_Migration</a> at line 196</div>
        <code>                    mixed
    <strong>version</strong>(string $target_version)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Migrate to a schema version</p></p>                <p><p>Calls each migration step required to get to the schema version of
choice</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$target_version</td>
                <td><p>Target schema version</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>TRUE if no migrations are found, current version string on success, FALSE on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_latest">
        <div class="location">in <a href="CI_Migration.html#method_latest">
CI_Migration</a> at line 330</div>
        <code>                    mixed
    <strong>latest</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sets the schema to the latest migration</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>Current version string on success, FALSE on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_current">
        <div class="location">in <a href="CI_Migration.html#method_current">
CI_Migration</a> at line 354</div>
        <code>                    mixed
    <strong>current</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sets the schema to the migration version set in config</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>TRUE if no migrations are found, current version string on success, FALSE on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_error_string">
        <div class="location">in <a href="CI_Migration.html#method_error_string">
CI_Migration</a> at line 366</div>
        <code>                    string
    <strong>error_string</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Error string</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>Error message returned as a string</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_find_migrations">
        <div class="location">in <a href="CI_Migration.html#method_find_migrations">
CI_Migration</a> at line 378</div>
        <code>                    array
    <strong>find_migrations</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Retrieves list of available migration scripts</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td><p>list of migration file paths sorted by version</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_migration_number">
        <div class="location">in <a href="CI_Migration.html#method__get_migration_number">
CI_Migration</a> at line 415</div>
        <code>            protected        string
    <strong>_get_migration_number</strong>(string $migration)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Extracts the migration number from a filename</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$migration</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>Numeric portion of a migration filename</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_migration_name">
        <div class="location">in <a href="CI_Migration.html#method__get_migration_name">
CI_Migration</a> at line 429</div>
        <code>            protected        string
    <strong>_get_migration_name</strong>(string $migration)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Extracts the migration class name from a filename</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$migration</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>text portion of a migration filename</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_version">
        <div class="location">in <a href="CI_Migration.html#method__get_version">
CI_Migration</a> at line 443</div>
        <code>            protected        string
    <strong>_get_version</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Retrieves current schema version</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>Current migration version</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__update_version">
        <div class="location">in <a href="CI_Migration.html#method__update_version">
CI_Migration</a> at line 457</div>
        <code>            protected        void
    <strong>_update_version</strong>(string $migration)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Stores the current schema version</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$migration</td>
                <td><p>Migration reached</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___get">
        <div class="location">in <a href="CI_Migration.html#method___get">
CI_Migration</a> at line 472</div>
        <code>                    mixed
    <strong>__get</strong>(string $var)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Enable the use of CI super-global</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$var</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_up">
        <div class="location">at line 6</div>
        <code>                    
    <strong>up</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_down">
        <div class="location">at line 110</div>
        <code>                    
    <strong>down</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

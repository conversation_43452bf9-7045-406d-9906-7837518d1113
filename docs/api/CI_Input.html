<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Input | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Input" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Input    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Input</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Input Class</p></p>            <p><p>Pre-processes global input data for security</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_ip_address">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$ip_address</td>
                <td class="last"><p>IP address of the current user</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__allow_get_array">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_allow_get_array</td>
                <td class="last"><p>Allow GET array flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__standardize_newlines">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_standardize_newlines</td>
                <td class="last"><p>Standardize new lines flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__enable_xss">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_enable_xss</td>
                <td class="last"><p>Enable XSS flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__enable_csrf">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_enable_csrf</td>
                <td class="last"><p>Enable CSRF flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_headers">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$headers</td>
                <td class="last"><p>List of all HTTP request headers</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__raw_input_stream">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_raw_input_stream</td>
                <td class="last"><p>Raw input stream data</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__input_stream">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_input_stream</td>
                <td class="last"><p>Parsed input stream data</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_security">
                                        protected                                        
                                                                                
                                    </td>
                <td>$security</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_uni">
                                        protected                                        
                                                                                
                                    </td>
                <td>$uni</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method__fetch_from_array">_fetch_from_array</a>(array $array, mixed $index = NULL, bool $xss_clean = NULL)
        
                                            <p><p>Fetch from array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_get">get</a>(mixed $index = NULL, bool $xss_clean = NULL)
        
                                            <p><p>Fetch an item from the GET array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_post">post</a>(mixed $index = NULL, bool $xss_clean = NULL)
        
                                            <p><p>Fetch an item from the POST array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_post_get">post_get</a>(string $index, bool $xss_clean = NULL)
        
                                            <p><p>Fetch an item from POST data with fallback to GET</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_get_post">get_post</a>(string $index, bool $xss_clean = NULL)
        
                                            <p><p>Fetch an item from GET data with fallback to POST</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_cookie">cookie</a>(mixed $index = NULL, bool $xss_clean = NULL)
        
                                            <p><p>Fetch an item from the COOKIE array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_server">server</a>(mixed $index, bool $xss_clean = NULL)
        
                                            <p><p>Fetch an item from the SERVER array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_input_stream">input_stream</a>(string $index = NULL, bool $xss_clean = NULL)
        
                                            <p><p>Fetch an item from the php://input stream</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_cookie">set_cookie</a>(string|array $name, string $value = &#039;&#039;, int $expire = &#039;&#039;, string $domain = &#039;&#039;, string $path = &#039;/&#039;, string $prefix = &#039;&#039;, bool $secure = NULL, bool $httponly = NULL)
        
                                            <p><p>Set cookie</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_ip_address">ip_address</a>()
        
                                            <p><p>Fetch the IP Address</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_valid_ip">valid_ip</a>(string $ip, string $which = &#039;&#039;)
        
                                            <p><p>Validate IP Address</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string|null
                </div>
                <div class="col-md-8">
                    <a href="#method_user_agent">user_agent</a>($xss_clean = NULL)
        
                                            <p><p>Fetch User Agent string</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__sanitize_globals">_sanitize_globals</a>()
        
                                            <p><p>Sanitize Globals</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__clean_input_data">_clean_input_data</a>(string|string[] $str)
        
                                            <p><p>Clean Input Data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string|bool
                </div>
                <div class="col-md-8">
                    <a href="#method__clean_input_keys">_clean_input_keys</a>(string $str, bool $fatal = TRUE)
        
                                            <p><p>Clean Keys</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_request_headers">request_headers</a>(bool $xss_clean = FALSE)
        
                                            <p><p>Request Headers</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string|null
                </div>
                <div class="col-md-8">
                    <a href="#method_get_request_header">get_request_header</a>(string $index, bool $xss_clean = FALSE)
        
                                            <p><p>Get Request Header</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_ajax_request">is_ajax_request</a>()
        
                                            <p><p>Is AJAX request?</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_cli_request">is_cli_request</a>()
        <small><span class="label label-danger">deprecated</span></small>
                                            <p><p>Is CLI request?</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_method">method</a>(bool $upper = FALSE)
        
                                            <p><p>Get Request Method</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method___get">__get</a>(string $name)
        
                                            <p><p>Magic __get()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 138</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                <p><p>Determines whether to globally enable the XSS processing
and whether to allow the $_GET array.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__fetch_from_array">
        <div class="location">at line 177</div>
        <code>            protected        mixed
    <strong>_fetch_from_array</strong>(array $array, mixed $index = NULL, bool $xss_clean = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch from array</p></p>                <p><p>Internal method used to retrieve values from global arrays.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$array</td>
                <td><p>$_GET, $_POST, $_COOKIE, $_SERVER, etc.</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$index</td>
                <td><p>Index for item to be fetched from $array</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$xss_clean</td>
                <td><p>Whether to apply XSS filtering</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get">
        <div class="location">at line 240</div>
        <code>                    mixed
    <strong>get</strong>(mixed $index = NULL, bool $xss_clean = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch an item from the GET array</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$index</td>
                <td><p>Index for item to be fetched from $_GET</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$xss_clean</td>
                <td><p>Whether to apply XSS filtering</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_post">
        <div class="location">at line 254</div>
        <code>                    mixed
    <strong>post</strong>(mixed $index = NULL, bool $xss_clean = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch an item from the POST array</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$index</td>
                <td><p>Index for item to be fetched from $_POST</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$xss_clean</td>
                <td><p>Whether to apply XSS filtering</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_post_get">
        <div class="location">at line 268</div>
        <code>                    mixed
    <strong>post_get</strong>(string $index, bool $xss_clean = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch an item from POST data with fallback to GET</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$index</td>
                <td><p>Index for item to be fetched from $_POST or $_GET</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$xss_clean</td>
                <td><p>Whether to apply XSS filtering</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_post">
        <div class="location">at line 284</div>
        <code>                    mixed
    <strong>get_post</strong>(string $index, bool $xss_clean = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch an item from GET data with fallback to POST</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$index</td>
                <td><p>Index for item to be fetched from $_GET or $_POST</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$xss_clean</td>
                <td><p>Whether to apply XSS filtering</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_cookie">
        <div class="location">at line 300</div>
        <code>                    mixed
    <strong>cookie</strong>(mixed $index = NULL, bool $xss_clean = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch an item from the COOKIE array</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$index</td>
                <td><p>Index for item to be fetched from $_COOKIE</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$xss_clean</td>
                <td><p>Whether to apply XSS filtering</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_server">
        <div class="location">at line 314</div>
        <code>                    mixed
    <strong>server</strong>(mixed $index, bool $xss_clean = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch an item from the SERVER array</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$index</td>
                <td><p>Index for item to be fetched from $_SERVER</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$xss_clean</td>
                <td><p>Whether to apply XSS filtering</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_input_stream">
        <div class="location">at line 330</div>
        <code>                    mixed
    <strong>input_stream</strong>(string $index = NULL, bool $xss_clean = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch an item from the php://input stream</p></p>                <p><p>Useful when you need to access PUT, DELETE or PATCH request data.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$index</td>
                <td><p>Index for item to be fetched</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$xss_clean</td>
                <td><p>Whether to apply XSS filtering</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_cookie">
        <div class="location">at line 362</div>
        <code>                    void
    <strong>set_cookie</strong>(string|array $name, string $value = &#039;&#039;, int $expire = &#039;&#039;, string $domain = &#039;&#039;, string $path = &#039;/&#039;, string $prefix = &#039;&#039;, bool $secure = NULL, bool $httponly = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set cookie</p></p>                <p><p>Accepts an arbitrary number of parameters (up to 7) or an associative
array in the first parameter containing all the values.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string|array</td>
                <td>$name</td>
                <td><p>Cookie name or an array containing parameters</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$value</td>
                <td><p>Cookie value</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$expire</td>
                <td><p>Cookie expiration time in seconds</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$domain</td>
                <td><p>Cookie domain (e.g.: '.yourdomain.com')</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td><p>Cookie path (default: '/')</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$prefix</td>
                <td><p>Cookie name prefix</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$secure</td>
                <td><p>Whether to only transfer cookies via SSL</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$httponly</td>
                <td><p>Whether to only makes the cookie accessible via HTTP (no javascript)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_ip_address">
        <div class="location">at line 420</div>
        <code>                    string
    <strong>ip_address</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch the IP Address</p></p>                <p><p>Determines and validates the visitor's IP address.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>IP address</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_valid_ip">
        <div class="location">at line 557</div>
        <code>                    bool
    <strong>valid_ip</strong>(string $ip, string $which = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validate IP Address</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$ip</td>
                <td><p>IP address</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$which</td>
                <td><p>IP protocol: 'ipv4' or 'ipv6'</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_user_agent">
        <div class="location">at line 582</div>
        <code>                    string|null
    <strong>user_agent</strong>($xss_clean = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch User Agent string</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$xss_clean</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string|null</td>
            <td><p>User Agent string or NULL if it doesn't exist</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__sanitize_globals">
        <div class="location">at line 600</div>
        <code>            protected        void
    <strong>_sanitize_globals</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sanitize Globals</p></p>                <p><p>Internal method serving for the following purposes:</p>
<ul>
<li>Unsets $_GET data, if query strings are not enabled</li>
<li>Cleans POST, COOKIE and SERVER data
<ul>
<li>Standardizes newline characters to PHP_EOL</li>
</ul></li>
</ul></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__clean_input_data">
        <div class="location">at line 668</div>
        <code>            protected        string
    <strong>_clean_input_data</strong>(string|string[] $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Clean Input Data</p></p>                <p><p>Internal method that aids in escaping data and
standardizing newline characters to PHP_EOL.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string|string[]</td>
                <td>$str</td>
                <td><p>Input string(s)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__clean_input_keys">
        <div class="location">at line 723</div>
        <code>            protected        string|bool
    <strong>_clean_input_keys</strong>(string $str, bool $fatal = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Clean Keys</p></p>                <p><p>Internal method that helps to prevent malicious users
from trying to exploit keys we make sure that keys are
only named with alpha-numeric text and a few other items.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td><p>Input string</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$fatal</td>
                <td><p>Whether to terminate script exection
or to return FALSE if an invalid
key is encountered</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string|bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_request_headers">
        <div class="location">at line 756</div>
        <code>                    array
    <strong>request_headers</strong>(bool $xss_clean = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Request Headers</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$xss_clean</td>
                <td><p>Whether to apply XSS filtering</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_request_header">
        <div class="location">at line 800</div>
        <code>                    string|null
    <strong>get_request_header</strong>(string $index, bool $xss_clean = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Request Header</p></p>                <p><p>Returns the value of a single member of the headers class member</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$index</td>
                <td><p>Header name</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$xss_clean</td>
                <td><p>Whether to apply XSS filtering</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string|null</td>
            <td><p>The requested header on success or NULL on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_ajax_request">
        <div class="location">at line 834</div>
        <code>                    bool
    <strong>is_ajax_request</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is AJAX request?</p></p>                <p><p>Test to see if a request contains the HTTP_X_REQUESTED_WITH header.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_cli_request">
        <div class="location">at line 849</div>
        <code>                    bool
    <strong>is_cli_request</strong>()
        <small><span class="label label-danger">deprecated</span></small></code>
    </h3>
    <div class="details">    
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td>3.0.0</td>
                    <td>Use is_cli() instead</td>
                </tr>
                    </p>
    
            

        <div class="method-description">
                            <p><p>Is CLI request?</p></p>                <p><p>Test to see if a request was made from the command line.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_method">
        <div class="location">at line 865</div>
        <code>                    string
    <strong>method</strong>(bool $upper = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Request Method</p></p>                <p><p>Return the request method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$upper</td>
                <td><p>Whether to return in upper or lower case
(default: FALSE)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___get">
        <div class="location">at line 882</div>
        <code>                    mixed
    <strong>__get</strong>(string $name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Magic __get()</p></p>                <p><p>Allows read access to protected properties</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

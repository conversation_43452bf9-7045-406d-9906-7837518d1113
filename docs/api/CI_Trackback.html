<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Trackback | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Trackback" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Trackback    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Trackback</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Trackback Class</p></p>            <p><p>Trackback Sending/Receiving Class</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_charset">
                                                                                string
                                                                                
                                    </td>
                <td>$charset</td>
                <td class="last"><p>Character set</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_data">
                                                                                array
                                                                                
                                    </td>
                <td>$data</td>
                <td class="last"><p>Trackback data</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_convert_ascii">
                                                                                bool
                                                                                
                                    </td>
                <td>$convert_ascii</td>
                <td class="last"><p>Convert ASCII flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_response">
                                                                                string
                                                                                
                                    </td>
                <td>$response</td>
                <td class="last">Response</td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_error_msg">
                                                                                string[]
                                                                                
                                    </td>
                <td>$error_msg</td>
                <td class="last"><p>Error messages list</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p>Constructor</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_send">send</a>($tb_data)
        
                                            <p><p>Send Trackback</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_receive">receive</a>()
        
                                            <p><p>Receive Trackback  Data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_send_error">send_error</a>($message = &#039;Incomplete Information&#039;)
        
                                            <p><p>Send Trackback Error Message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_send_success">send_success</a>()
        
                                            <p><p>Send Trackback Success Message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_data">data</a>($item)
        
                                            <p><p>Fetch a particular item</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_process">process</a>($url, $data)
        
                                            <p><p>Process Trackback</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_extract_urls">extract_urls</a>($urls)
        
                                            <p><p>Extract Trackback URLs</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_validate_url">validate_url</a>($url)
        
                                            <p><p>Validate URL</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_id">get_id</a>($url)
        
                                            <p><p>Find the Trackback URL's ID</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_convert_xml">convert_xml</a>($str)
        
                                            <p><p>Convert Reserved XML characters to Entities</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_limit_characters">limit_characters</a>($str, $n = 500, $end_char = &#039;&amp;#8230;&#039;)
        
                                            <p><p>Character limiter</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_convert_ascii">convert_ascii</a>($str)
        
                                            <p><p>High ASCII to Entities</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_error">set_error</a>($msg)
        
                                            <p><p>Set error message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_display_errors">display_errors</a>($open = &#039;&lt;p&gt;&#039;, $close = &#039;&lt;/p&gt;&#039;)
        
                                            <p><p>Show error messages</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 104</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Constructor</p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send">
        <div class="location">at line 117</div>
        <code>                    bool
    <strong>send</strong>($tb_data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send Trackback</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$tb_data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_receive">
        <div class="location">at line 191</div>
        <code>                    bool
    <strong>receive</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Receive Trackback  Data</p></p>                <p><p>This function simply validates the incoming TB data.
It returns FALSE on failure and TRUE on success.
If the data is valid it is set to the $this-&gt;data array
so that it can be inserted into a database.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send_error">
        <div class="location">at line 240</div>
        <code>                    void
    <strong>send_error</strong>($message = &#039;Incomplete Information&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send Trackback Error Message</p></p>                <p><p>Allows custom errors to be set. By default it
sends the &quot;incomplete information&quot; error, as that's
the most common one.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$message</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send_success">
        <div class="location">at line 255</div>
        <code>                    void
    <strong>send_success</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send Trackback Success Message</p></p>                <p><p>This should be called when a trackback has been
successfully received and inserted.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_data">
        <div class="location">at line 268</div>
        <code>                    string
    <strong>data</strong>($item)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch a particular item</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$item</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_process">
        <div class="location">at line 285</div>
        <code>                    bool
    <strong>process</strong>($url, $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Process Trackback</p></p>                <p><p>Opens a socket connection and passes the data to
the server. Returns TRUE on success, FALSE on failure</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$url</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_extract_urls">
        <div class="location">at line 347</div>
        <code>                    string
    <strong>extract_urls</strong>($urls)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Extract Trackback URLs</p></p>                <p><p>This function lets multiple trackbacks be sent.
It takes a string of URLs (separated by comma or
space) and puts each URL into an array</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$urls</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_validate_url">
        <div class="location">at line 369</div>
        <code>                    void
    <strong>validate_url</strong>($url)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validate URL</p></p>                <p><p>Simply adds &quot;http://&quot; if missing</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$url</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_id">
        <div class="location">at line 387</div>
        <code>                    string
    <strong>get_id</strong>($url)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Find the Trackback URL's ID</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$url</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_convert_xml">
        <div class="location">at line 428</div>
        <code>                    string
    <strong>convert_xml</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Convert Reserved XML characters to Entities</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_limit_characters">
        <div class="location">at line 453</div>
        <code>                    string
    <strong>limit_characters</strong>($str, $n = 500, $end_char = &#039;&amp;#8230;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Character limiter</p></p>                <p><p>Limits the string based on the character count. Will preserve complete words.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$n</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$end_char</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_convert_ascii">
        <div class="location">at line 489</div>
        <code>                    string
    <strong>convert_ascii</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>High ASCII to Entities</p></p>                <p><p>Converts Hight ascii text and MS Word special chars
to character entities</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_error">
        <div class="location">at line 536</div>
        <code>                    void
    <strong>set_error</strong>($msg)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set error message</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$msg</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_display_errors">
        <div class="location">at line 551</div>
        <code>                    string
    <strong>display_errors</strong>($open = &#039;&lt;p&gt;&#039;, $close = &#039;&lt;/p&gt;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Show error messages</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$open</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$close</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Proposal_expiration_reminder | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Proposal_expiration_reminder" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Proposal_expiration_reminder    
            </h1>
    </div>

    
    <p>        class
    <strong>Proposal_expiration_reminder</strong>        extends <a href="App_mail_template.html">App_mail_template</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_slug">
                                                                                
                                                                                
                                    </td>
                <td>$slug</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_send_to">
                                                                                string
                                                                                
                                    </td>
                <td>$send_to</td>
                <td class="last"><p>Email to send to</p></td>
                <td><small>from&nbsp;<a href="App_mail_template.html#property_send_to">
App_mail_template</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_cc">
                                                                                string
                                                                                
                                    </td>
                <td>$cc</td>
                <td class="last"><p>Email CC</p></td>
                <td><small>from&nbsp;<a href="App_mail_template.html#property_cc">
App_mail_template</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_merge_fields">
                                                                                array
                                                                                
                                    </td>
                <td>$merge_fields</td>
                <td class="last"><p>The merge fields for the email template</p></td>
                <td><small>from&nbsp;<a href="App_mail_template.html#property_merge_fields">
App_mail_template</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_attachments">
                                                                                array
                                                                                
                                    </td>
                <td>$attachments</td>
                <td class="last">Attachments</td>
                <td><small>from&nbsp;<a href="App_mail_template.html#property_attachments">
App_mail_template</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_rel_id">
                                                                                mixed
                                                                                
                                    </td>
                <td>$rel_id</td>
                <td class="last"><p>Relation ID, e.q. invoice id</p></td>
                <td><small>from&nbsp;<a href="App_mail_template.html#property_rel_id">
App_mail_template</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_rel_type">
                                                                                
                                                                                
                                    </td>
                <td>$rel_type</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_staff_id">
                                                                                mixed
                                                                                
                                    </td>
                <td>$staff_id</td>
                <td class="last"><p>If mail is sent to staff member, set staff id</p></td>
                <td><small>from&nbsp;<a href="App_mail_template.html#property_staff_id">
App_mail_template</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"><p>Codeigniter instance</p></td>
                <td><small>from&nbsp;<a href="App_mail_template.html#property_ci">
App_mail_template</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_template">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$template</td>
                <td class="last"><p>The actual template object from database</p></td>
                <td><small>from&nbsp;<a href="App_mail_template.html#property_template">
App_mail_template</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_skipQueue">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$skipQueue</td>
                <td class="last"><p>Indicates whether this template should be not be added in queue when enabled</p></td>
                <td><small>from&nbsp;<a href="App_mail_template.html#property_skipQueue">
App_mail_template</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_for">
                                        protected                                        
                                                                                
                                    </td>
                <td>$for</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_proposal">
                                        protected                                        
                                                                                
                                    </td>
                <td>$proposal</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>($proposal)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_send">send</a>()
        
                                            <p><p>Send mail template</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_send">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_is_for">is_for</a>($for)
        
                                            <p><p>Return for who this email is intended</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_is_for">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    null
                </div>
                <div class="col-md-8">
                    <a href="#method__alt_message">_alt_message</a>()
        
                                            <p><p>Sets mail alt message</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method__alt_message">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    null
                </div>
                <div class="col-md-8">
                    <a href="#method__attachments">_attachments</a>()
        
                                            <p><p>Set the mail attachments</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method__attachments">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__subject">_subject</a>()
        
                                            <p><p>Get template subject</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method__subject">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method__reply_to">_reply_to</a>()
        
                                            <p><p>Get template reply to header</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method__reply_to">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method__from">_from</a>()
        
                                            <p><p>Get template from header</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method__from">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_get_default_property_value">get_default_property_value</a>(string $property, string $className, array $params = [])
        
                                            <p><p>Get reflection class default property</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_get_default_property_value">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_prepare">prepare</a>(string $email = null, $template = null, $params = [])
        
                                            <p><p>Based on the template slug and email the function will fetch a template from database
The template will be fetched on the language that should be sent</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_prepare">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_merge_fields">set_merge_fields</a>(array $fields, ...$params)
        
                                            <p><p>Set template merge fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_set_merge_fields">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_merge_fields">get_merge_fields</a>()
        
                                            <p><p>Get template merge fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_get_merge_fields">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_cc">cc</a>(mixed $cc)
        
                                            <p><p>Set template CC header</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_cc">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_to">to</a>(string $email)
        
                                            <p><p>Set template TO email header</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_to">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_add_attachment">add_attachment</a>($attachment)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_add_attachment">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_rel_id">set_rel_id</a>(mixed $rel_id)
        
                                            <p><p>Set template relation id</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_set_rel_id">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_get_rel_id">get_rel_id</a>()
        
                                            <p><p>Get template relation id</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_get_rel_id">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_rel_type">set_rel_type</a>(string $rel_type)
        
                                            <p><p>Set template relation type</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_set_rel_type">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_rel_type">get_rel_type</a>()
        
                                            <p><p>Get template relation typ</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_get_rel_type">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_staff_id">set_staff_id</a>(mixed $id)
        
                                            <p><p>Set template staff id</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_set_staff_id">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_get_staff_id">get_staff_id</a>()
        
                                            <p><p>Get template staff id</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_mail_template.html#method_get_staff_id">
App_mail_template</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_build">build</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 15</div>
        <code>                    
    <strong>__construct</strong>($proposal)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$proposal</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send">
        <div class="location">in <a href="App_mail_template.html#method_send">
App_mail_template</a> at line 90</div>
        <code>                    bool
    <strong>send</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send mail template</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_for">
        <div class="location">in <a href="App_mail_template.html#method_is_for">
App_mail_template</a> at line 229</div>
        <code>                    mixed
    <strong>is_for</strong>($for)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Return for who this email is intended</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$for</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__alt_message">
        <div class="location">in <a href="App_mail_template.html#method__alt_message">
App_mail_template</a> at line 238</div>
        <code>            protected        null
    <strong>_alt_message</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sets mail alt message</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>null</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__attachments">
        <div class="location">in <a href="App_mail_template.html#method__attachments">
App_mail_template</a> at line 252</div>
        <code>            protected        null
    <strong>_attachments</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the mail attachments</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>null</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__subject">
        <div class="location">in <a href="App_mail_template.html#method__subject">
App_mail_template</a> at line 267</div>
        <code>            protected        string
    <strong>_subject</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get template subject</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__reply_to">
        <div class="location">in <a href="App_mail_template.html#method__reply_to">
App_mail_template</a> at line 276</div>
        <code>            protected        mixed
    <strong>_reply_to</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get template reply to header</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__from">
        <div class="location">in <a href="App_mail_template.html#method__from">
App_mail_template</a> at line 285</div>
        <code>            protected        array
    <strong>_from</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get template from header</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_default_property_value">
        <div class="location">in <a href="App_mail_template.html#method_get_default_property_value">
App_mail_template</a> at line 399</div>
        <code>                    mixed
    <strong>get_default_property_value</strong>(string $property, string $className, array $params = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get reflection class default property</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$property</td>
                <td><p>property name</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$className</td>
                <td>className</td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>option mail class params</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_prepare">
        <div class="location">in <a href="App_mail_template.html#method_prepare">
App_mail_template</a> at line 413</div>
        <code>                    object
    <strong>prepare</strong>(string $email = null, $template = null, $params = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Based on the template slug and email the function will fetch a template from database
The template will be fetched on the language that should be sent</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$email</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$template</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$params</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_merge_fields">
        <div class="location">in <a href="App_mail_template.html#method_set_merge_fields">
App_mail_template</a> at line 521</div>
        <code>                    
    <strong>set_merge_fields</strong>(array $fields, ...$params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set template merge fields</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$fields</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>...$params</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_merge_fields">
        <div class="location">in <a href="App_mail_template.html#method_get_merge_fields">
App_mail_template</a> at line 536</div>
        <code>                    array
    <strong>get_merge_fields</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get template merge fields</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_cc">
        <div class="location">in <a href="App_mail_template.html#method_cc">
App_mail_template</a> at line 546</div>
        <code>                    object
    <strong>cc</strong>(mixed $cc)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set template CC header</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$cc</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_to">
        <div class="location">in <a href="App_mail_template.html#method_to">
App_mail_template</a> at line 558</div>
        <code>                    object
    <strong>to</strong>(string $email)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set template TO email header</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$email</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_attachment">
        <div class="location">in <a href="App_mail_template.html#method_add_attachment">
App_mail_template</a> at line 570</div>
        <code>                    object
    <strong>add_attachment</strong>($attachment)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$attachment</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td><p>App_send_mail
Add attachment to property to check before an email is send</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_rel_id">
        <div class="location">in <a href="App_mail_template.html#method_set_rel_id">
App_mail_template</a> at line 592</div>
        <code>                    
    <strong>set_rel_id</strong>(mixed $rel_id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set template relation id</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$rel_id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_rel_id">
        <div class="location">in <a href="App_mail_template.html#method_get_rel_id">
App_mail_template</a> at line 603</div>
        <code>                    mixed
    <strong>get_rel_id</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get template relation id</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td>x</td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_rel_type">
        <div class="location">in <a href="App_mail_template.html#method_set_rel_type">
App_mail_template</a> at line 612</div>
        <code>                    
    <strong>set_rel_type</strong>(string $rel_type)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set template relation type</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$rel_type</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_rel_type">
        <div class="location">in <a href="App_mail_template.html#method_get_rel_type">
App_mail_template</a> at line 623</div>
        <code>                    string
    <strong>get_rel_type</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get template relation typ</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_staff_id">
        <div class="location">in <a href="App_mail_template.html#method_set_staff_id">
App_mail_template</a> at line 632</div>
        <code>                    
    <strong>set_staff_id</strong>(mixed $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set template staff id</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_staff_id">
        <div class="location">in <a href="App_mail_template.html#method_get_staff_id">
App_mail_template</a> at line 643</div>
        <code>                    mixed
    <strong>get_staff_id</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get template staff id</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_build">
        <div class="location">at line 25</div>
        <code>                    
    <strong>build</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

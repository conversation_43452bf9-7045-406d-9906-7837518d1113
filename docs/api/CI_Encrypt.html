<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Encrypt | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Encrypt" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Encrypt    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Encrypt</strong>
</p>

        
    
        

            <div class="description">
            <p><p>CodeIgniter Encryption Class</p></p>            <p><p>Provides two-way keyed encoding using Mcrypt</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_encryption_key">
                                                                                string
                                                                                
                                    </td>
                <td>$encryption_key</td>
                <td class="last"><p>Reference to the user's encryption key</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__hash_type">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_hash_type</td>
                <td class="last"><p>Type of hash operation</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__mcrypt_exists">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_mcrypt_exists</td>
                <td class="last"><p>Flag for the existence of mcrypt</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__mcrypt_cipher">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_mcrypt_cipher</td>
                <td class="last"><p>Current cipher to be used with mcrypt</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__mcrypt_mode">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$_mcrypt_mode</td>
                <td class="last"><p>Method for encrypting/decrypting data</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Initialize Encryption class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_key">get_key</a>($key = &#039;&#039;)
        
                                            <p><p>Fetch the encryption key</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Encrypt.html">CI_Encrypt</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_key">set_key</a>($key = &#039;&#039;)
        
                                            <p><p>Set the encryption key</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_encode">encode</a>($string, $key = &#039;&#039;)
        
                                            <p>Encode</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_decode">decode</a>($string, $key = &#039;&#039;)
        
                                            <p>Decode</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_encode_from_legacy">encode_from_legacy</a>($string, $legacy_mode = MCRYPT_MODE_ECB, $key = &#039;&#039;)
        
                                            <p><p>Encode from Legacy</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__xor_decode">_xor_decode</a>($string, $key)
        
                                            <p><p>XOR Decode</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__xor_merge">_xor_merge</a>($string, $key)
        
                                            <p><p>XOR key + string Combiner</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_mcrypt_encode">mcrypt_encode</a>($data, $key)
        
                                            <p><p>Encrypt using Mcrypt</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_mcrypt_decode">mcrypt_decode</a>($data, $key)
        
                                            <p><p>Decrypt using Mcrypt</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__add_cipher_noise">_add_cipher_noise</a>($data, $key)
        
                                            <p><p>Adds permuted noise to the IV + encrypted data to protect
against Man-in-the-middle attacks on CBC mode ciphers
<a href="http://www.ciphersbyritter.com/GLOSSARY.HTM#IV">http://www.ciphersbyritter.com/GLOSSARY.HTM#IV</a></p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__remove_cipher_noise">_remove_cipher_noise</a>(string $data, string $key)
        
                                            <p><p>Removes permuted noise from the IV + encrypted data, reversing
_add_cipher_noise()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Encrypt.html">CI_Encrypt</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_cipher">set_cipher</a>($cipher)
        
                                            <p><p>Set the Mcrypt Cipher</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Encrypt.html">CI_Encrypt</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_mode">set_mode</a>($mode)
        
                                            <p><p>Set the Mcrypt Mode</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method__get_cipher">_get_cipher</a>()
        
                                            <p><p>Get Mcrypt cipher Value</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method__get_mode">_get_mode</a>()
        
                                            <p><p>Get Mcrypt Mode Value</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_hash">set_hash</a>($type = &#039;sha1&#039;)
        
                                            <p><p>Set the Hash type</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_hash">hash</a>($str)
        
                                            <p><p>Hash encode a string</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;int
                </div>
                <div class="col-md-8">
                    <a href="#method_strlen">strlen</a>(string $str)
        
                                            <p><p>Byte-safe strlen()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;string
                </div>
                <div class="col-md-8">
                    <a href="#method_substr">substr</a>(string $str, int $start, int $length = NULL)
        
                                            <p><p>Byte-safe substr()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 93</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize Encryption class</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_key">
        <div class="location">at line 114</div>
        <code>                    string
    <strong>get_key</strong>($key = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch the encryption key</p></p>                <p><p>Returns it as MD5 in order to have an exact-length 128 bit key.
Mcrypt is sensitive to keys that are not the correct length</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_key">
        <div class="location">at line 142</div>
        <code>                    <a href="CI_Encrypt.html">CI_Encrypt</a>
    <strong>set_key</strong>($key = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the encryption key</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Encrypt.html">CI_Encrypt</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_encode">
        <div class="location">at line 165</div>
        <code>                    string
    <strong>encode</strong>($string, $key = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Encode</p>                <p><p>Encodes the message string using bitwise XOR encoding.
The key is combined with a random hash, and then it
too gets converted using XOR. The whole thing is then run
through mcrypt using the randomized key. The end result
is a double-encrypted message string that is randomized
with each call to this function, even if the supplied
message and key are the same.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$string</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_decode">
        <div class="location">at line 181</div>
        <code>                    string
    <strong>decode</strong>($string, $key = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Decode</p>                <p><p>Reverses the above process</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$string</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_encode_from_legacy">
        <div class="location">at line 208</div>
        <code>                    string
    <strong>encode_from_legacy</strong>($string, $legacy_mode = MCRYPT_MODE_ECB, $key = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Encode from Legacy</p></p>                <p><p>Takes an encoded string from the original Encryption class algorithms and
returns a newly encoded string using the improved method added in 2.0.0
This allows for backwards compatibility and a method to transition to the
new encryption algorithms.</p>
<p>For more details, see <a href="https://codeigniter.com/user_guide/installation/upgrade_200.html#encryption">https://codeigniter.com/user_guide/installation/upgrade_200.html#encryption</a></p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$string</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$legacy_mode</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__xor_decode">
        <div class="location">at line 250</div>
        <code>            protected        string
    <strong>_xor_decode</strong>($string, $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>XOR Decode</p></p>                <p><p>Takes an encoded string and key as input and generates the
plain-text original message</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$string</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__xor_merge">
        <div class="location">at line 274</div>
        <code>            protected        string
    <strong>_xor_merge</strong>($string, $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>XOR key + string Combiner</p></p>                <p><p>Takes a string and key as input and computes the difference using XOR</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$string</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mcrypt_encode">
        <div class="location">at line 296</div>
        <code>                    string
    <strong>mcrypt_encode</strong>($data, $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Encrypt using Mcrypt</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mcrypt_decode">
        <div class="location">at line 312</div>
        <code>                    string
    <strong>mcrypt_decode</strong>($data, $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Decrypt using Mcrypt</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__add_cipher_noise">
        <div class="location">at line 339</div>
        <code>            protected        string
    <strong>_add_cipher_noise</strong>($data, $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Adds permuted noise to the IV + encrypted data to protect
against Man-in-the-middle attacks on CBC mode ciphers
<a href="http://www.ciphersbyritter.com/GLOSSARY.HTM#IV">http://www.ciphersbyritter.com/GLOSSARY.HTM#IV</a></p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__remove_cipher_noise">
        <div class="location">at line 369</div>
        <code>            protected        string
    <strong>_remove_cipher_noise</strong>(string $data, string $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Removes permuted noise from the IV + encrypted data, reversing
_add_cipher_noise()</p></p>                <p><p>Function description</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$data</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_cipher">
        <div class="location">at line 402</div>
        <code>                    <a href="CI_Encrypt.html">CI_Encrypt</a>
    <strong>set_cipher</strong>($cipher)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the Mcrypt Cipher</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$cipher</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Encrypt.html">CI_Encrypt</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_mode">
        <div class="location">at line 416</div>
        <code>                    <a href="CI_Encrypt.html">CI_Encrypt</a>
    <strong>set_mode</strong>($mode)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the Mcrypt Mode</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$mode</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Encrypt.html">CI_Encrypt</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_cipher">
        <div class="location">at line 429</div>
        <code>            protected        int
    <strong>_get_cipher</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Mcrypt cipher Value</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_mode">
        <div class="location">at line 446</div>
        <code>            protected        int
    <strong>_get_mode</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Mcrypt Mode Value</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_hash">
        <div class="location">at line 464</div>
        <code>                    void
    <strong>set_hash</strong>($type = &#039;sha1&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the Hash type</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_hash">
        <div class="location">at line 477</div>
        <code>                    string
    <strong>hash</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Hash encode a string</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_strlen">
        <div class="location">at line 490</div>
        <code>        static    protected        int
    <strong>strlen</strong>(string $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe strlen()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_substr">
        <div class="location">at line 507</div>
        <code>        static    protected        string
    <strong>substr</strong>(string $str, int $start, int $length = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe substr()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$start</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$length</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Session_redis_driver | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Session_redis_driver" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Session_redis_driver    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Session_redis_driver</strong>        extends <a href="CI_Session_driver.html">CI_Session_driver</a>        implements        <a href="SessionHandlerInterface.html">SessionHandlerInterface</a>
</p>

        
    
        

            <div class="description">
            <p><p>CodeIgniter Session Redis Driver</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property__config">
                                        protected                                        
                                                                                
                                    </td>
                <td>$_config</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="CI_Session_driver.html#property__config">
CI_Session_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__fingerprint">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_fingerprint</td>
                <td class="last"><p>Data fingerprint</p></td>
                <td><small>from&nbsp;<a href="CI_Session_driver.html#property__fingerprint">
CI_Session_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__lock">
                                        protected                                        mixed
                                                                                
                                    </td>
                <td>$_lock</td>
                <td class="last"><p>Lock placeholder</p></td>
                <td><small>from&nbsp;<a href="CI_Session_driver.html#property__lock">
CI_Session_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__session_id">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_session_id</td>
                <td class="last"><p>Read session ID</p></td>
                <td><small>from&nbsp;<a href="CI_Session_driver.html#property__session_id">
CI_Session_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__success">
                                        protected                                        mixed
                                                                                
                                    </td>
                <td>$_success</td>
                <td class="last"><p>Success and failure return values</p></td>
                <td><small>from&nbsp;<a href="CI_Session_driver.html#property__success">
CI_Session_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__failure">
                                        protected                                        mixed
                                                                                
                                    </td>
                <td>$_failure</td>
                <td class="last"><p>Success and failure return values</p></td>
                <td><small>from&nbsp;<a href="CI_Session_driver.html#property__failure">
CI_Session_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__redis">
                                        protected                                        Redis
                                                                                
                                    </td>
                <td>$_redis</td>
                <td class="last"><p>phpRedis instance</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__key_prefix">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_key_prefix</td>
                <td class="last"><p>Key prefix</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__lock_key">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_lock_key</td>
                <td class="last"><p>Lock key</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__key_exists">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_key_exists</td>
                <td class="last"><p>Key exists flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__setTimeout_name">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_setTimeout_name</td>
                <td class="last"><p>Name of setTimeout() method in phpRedis</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__delete_name">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_delete_name</td>
                <td class="last"><p>Name of delete() method in phpRedis</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__ping_success">
                                        protected                                        mixed
                                                                                
                                    </td>
                <td>$_ping_success</td>
                <td class="last"><p>Success return value of ping() method in phpRedis</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $params)
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_php5_validate_id">php5_validate_id</a>()
        
                                            <p><p>PHP 5.x validate ID</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session_driver.html#method_php5_validate_id">
CI_Session_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__cookie_destroy">_cookie_destroy</a>()
        
                                            <p><p>Cookie destroy</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session_driver.html#method__cookie_destroy">
CI_Session_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__get_lock">_get_lock</a>(string $session_id)
        
                                            <p><p>Get lock</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__release_lock">_release_lock</a>()
        
                                            <p><p>Release lock</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_open">open</a>($save_path, $name)
        
                                            <p>Open</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_read">read</a>($session_id)
        
                                            <p>Read</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_write">write</a>($session_id, $session_data)
        
                                            <p>Write</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_close">close</a>()
        
                                            <p>Close</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_destroy">destroy</a>($session_id)
        
                                            <p>Destroy</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_gc">gc</a>($maxlifetime)
        
                                            <p><p>Garbage Collector</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_validateSessionId">validateSessionId</a>(string $id)
        
                                            <p><p>Validate ID</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 114</div>
        <code>                    void
    <strong>__construct</strong>(array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Configuration parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_php5_validate_id">
        <div class="location">in <a href="CI_Session_driver.html#method_php5_validate_id">
CI_Session_driver</a> at line 122</div>
        <code>                    void
    <strong>php5_validate_id</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>PHP 5.x validate ID</p></p>                <p><p>Enforces session.use_strict_mode</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__cookie_destroy">
        <div class="location">in <a href="CI_Session_driver.html#method__cookie_destroy">
CI_Session_driver</a> at line 140</div>
        <code>            protected        bool
    <strong>_cookie_destroy</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Cookie destroy</p></p>                <p><p>Internal method to force removal of a cookie by the client
when session_destroy() is called.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_lock">
        <div class="location">at line 381</div>
        <code>            protected        bool
    <strong>_get_lock</strong>(string $session_id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get lock</p></p>                <p><p>Acquires an (emulated) lock.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$session_id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__release_lock">
        <div class="location">at line 442</div>
        <code>            protected        bool
    <strong>_release_lock</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Release lock</p></p>                <p><p>Releases a previously acquired lock</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_open">
        <div class="location">at line 171</div>
        <code>                    
    <strong>open</strong>($save_path, $name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Open</p>                <p><p>Sanitizes save_path and initializes connection.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$save_path</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$name</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_read">
        <div class="location">at line 211</div>
        <code>                    
    <strong>read</strong>($session_id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Read</p>                <p><p>Reads session data and acquires a lock</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$session_id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_write">
        <div class="location">at line 242</div>
        <code>                    
    <strong>write</strong>($session_id, $session_data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Write</p>                <p><p>Writes (create / update) session data</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$session_id</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$session_data</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_close">
        <div class="location">at line 287</div>
        <code>                    
    <strong>close</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Close</p>                <p><p>Releases locks and closes connection.</p></p>        
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_destroy">
        <div class="location">at line 323</div>
        <code>                    
    <strong>destroy</strong>($session_id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Destroy</p>                <p><p>Destroys the current session.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$session_id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_gc">
        <div class="location">at line 349</div>
        <code>                    
    <strong>gc</strong>($maxlifetime)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Garbage Collector</p></p>                <p><p>Deletes expired sessions</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$maxlifetime</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_validateSessionId">
        <div class="location">at line 366</div>
        <code>                    bool
    <strong>validateSessionId</strong>(string $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validate ID</p></p>                <p><p>Checks whether a session ID record exists server-side,
to enforce session.use_strict_mode.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

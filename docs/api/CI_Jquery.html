<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Jquery | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Jquery" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Jquery    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Jquery</strong>        extends <a href="CI_Javascript.html">CI_Javascript</a>
</p>

        
    
        

            <div class="description">
            <p><p>Jquery Class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property__javascript_location">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_javascript_location</td>
                <td class="last"><p>JavaScript location</p></td>
                <td><small>from&nbsp;<a href="CI_Javascript.html#property__javascript_location">
CI_Javascript</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__javascript_folder">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_javascript_folder</td>
                <td class="last"><p>JavaScript directory location</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_jquery_code_for_load">
                                                                                array
                                                                                
                                    </td>
                <td>$jquery_code_for_load</td>
                <td class="last"><p>JQuery code for load</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_jquery_code_for_compile">
                                                                                array
                                                                                
                                    </td>
                <td>$jquery_code_for_compile</td>
                <td class="last"><p>JQuery code for compile</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_jquery_corner_active">
                                                                                bool
                                                                                
                                    </td>
                <td>$jquery_corner_active</td>
                <td class="last"><p>JQuery corner active flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_jquery_table_sorter_active">
                                                                                bool
                                                                                
                                    </td>
                <td>$jquery_table_sorter_active</td>
                <td class="last"><p>JQuery table sorter active flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_jquery_table_sorter_pager_active">
                                                                                bool
                                                                                
                                    </td>
                <td>$jquery_table_sorter_pager_active</td>
                <td class="last"><p>JQuery table sorter pager active</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_jquery_ajax_img">
                                                                                string
                                                                                
                                    </td>
                <td>$jquery_ajax_img</td>
                <td class="last"><p>JQuery AJAX image</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $params)
        
                                            <p>Constructor</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_blur">blur</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Blur</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_blur">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_change">change</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Change</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_change">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_click">click</a>($element = &#039;this&#039;, $js = &#039;&#039;, $ret_false = TRUE)
        
                                            <p>Click</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_click">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_dblclick">dblclick</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p><p>Double Click</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_dblclick">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_error">error</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Error</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_error">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_focus">focus</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Focus</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_focus">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_hover">hover</a>($element = &#039;this&#039;, $over = &#039;&#039;, $out = &#039;&#039;)
        
                                            <p>Hover</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_hover">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_keydown">keydown</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Keydown</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_keydown">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_keyup">keyup</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Keyup</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_keyup">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_load">load</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Load</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_load">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_mousedown">mousedown</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Mousedown</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_mousedown">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_mouseout">mouseout</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p><p>Mouse Out</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_mouseout">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_mouseover">mouseover</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p><p>Mouse Over</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_mouseover">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_mouseup">mouseup</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Mouseup</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_mouseup">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_output">output</a>($js)
        
                                            <p>Output</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_output">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_ready">ready</a>(string $js)
        
                                            <p>Ready</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_ready">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_resize">resize</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Resize</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_resize">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_scroll">scroll</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Scroll</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_scroll">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_unload">unload</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Unload</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_unload">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_addClass">addClass</a>($element = &#039;this&#039;, $class = &#039;&#039;)
        
                                            <p><p>Add Class</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_addClass">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_animate">animate</a>(string $element = &#039;this&#039;, array $params = array(), mixed $speed = &#039;&#039;, string $extra = &#039;&#039;)
        
                                            <p>Animate</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_animate">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_fadeIn">fadeIn</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Fade In</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_fadeIn">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_fadeOut">fadeOut</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Fade Out</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_fadeOut">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_slideUp">slideUp</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Slide Up</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_slideUp">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_removeClass">removeClass</a>($element = &#039;this&#039;, $class = &#039;&#039;)
        
                                            <p><p>Remove Class</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_removeClass">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_slideDown">slideDown</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Slide Down</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_slideDown">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_slideToggle">slideToggle</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Slide Toggle</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_slideToggle">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_hide">hide</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p>Hide</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_hide">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_toggle">toggle</a>($element = &#039;this&#039;)
        
                                            <p>Toggle</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_toggle">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_toggleClass">toggleClass</a>(string $element = &#039;this&#039;, string $class = &#039;&#039;)
        
                                            <p><p>Toggle Class</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_toggleClass">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_show">show</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p>Show</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_show">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_compile">compile</a>(string $view_var = &#039;script_foot&#039;, bool $script_tags = TRUE)
        
                                            <p>Compile</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_compile">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_clear_compile">clear_compile</a>()
        
                                            <p><p>Clear Compile</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_clear_compile">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_external">external</a>(string $external_file = &#039;&#039;, bool $relative = FALSE)
        
                                            <p>External</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_external">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_inline">inline</a>($script, $cdata = TRUE)
        
                                            <p>Inline</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_inline">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__open_script">_open_script</a>($src = &#039;&#039;)
        
                                            <p><p>Open Script</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method__open_script">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__close_script">_close_script</a>($extra = &quot;\n&quot;)
        
                                            <p><p>Close Script</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method__close_script">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_update">update</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p>Update</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_update">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_generate_json">generate_json</a>($result = NULL, $match_array_type = FALSE)
        
                                            <p><p>Generate JSON</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method_generate_json">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__is_associative_array">_is_associative_array</a>($arr)
        
                                            <p><p>Is associative array</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method__is_associative_array">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__prep_args">_prep_args</a>(mixed $result, bool $is_key = FALSE)
        
                                            <p><p>Prep Args</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Javascript.html#method__prep_args">
CI_Javascript</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__blur">_blur</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Blur</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__change">_change</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Change</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__click">_click</a>($element = &#039;this&#039;, $js = &#039;&#039;, $ret_false = TRUE)
        
                                            <p>Click</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__dblclick">_dblclick</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p><p>Double Click</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__error">_error</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Error</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__focus">_focus</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Focus</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__hover">_hover</a>($element = &#039;this&#039;, $over = &#039;&#039;, $out = &#039;&#039;)
        
                                            <p>Hover</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__keydown">_keydown</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Keydown</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__keyup">_keyup</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Keyup</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__load">_load</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Load</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__mousedown">_mousedown</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Mousedown</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__mouseout">_mouseout</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p><p>Mouse Out</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__mouseover">_mouseover</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p><p>Mouse Over</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__mouseup">_mouseup</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Mouseup</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__output">_output</a>(array $array_js = array())
        
                                            <p>Output</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__resize">_resize</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Resize</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__scroll">_scroll</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Scroll</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__unload">_unload</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Unload</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__addClass">_addClass</a>(string $element = &#039;this&#039;, string $class = &#039;&#039;)
        
                                            <p><p>Add Class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__animate">_animate</a>(string $element = &#039;this&#039;, array $params = array(), string $speed = &#039;&#039;, string $extra = &#039;&#039;)
        
                                            <p>Animate</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__fadeIn">_fadeIn</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Fade In</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__fadeOut">_fadeOut</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Fade Out</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__hide">_hide</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p>Hide</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__removeClass">_removeClass</a>(string $element = &#039;this&#039;, string $class = &#039;&#039;)
        
                                            <p><p>Remove Class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__slideUp">_slideUp</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Slide Up</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__slideDown">_slideDown</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Slide Down</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__slideToggle">_slideToggle</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Slide Toggle</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__toggle">_toggle</a>($element = &#039;this&#039;)
        
                                            <p>Toggle</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__toggleClass">_toggleClass</a>(string $element = &#039;this&#039;, string $class = &#039;&#039;)
        
                                            <p><p>Toggle Class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__show">_show</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p>Show</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__updater">_updater</a>($container = &#039;this&#039;, $controller = &#039;&#039;, $options = &#039;&#039;)
        
                                            <p>Updater</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__zebraTables">_zebraTables</a>(string $class = &#039;&#039;, string $odd = &#039;odd&#039;, string $hover = &#039;&#039;)
        
                                            <p><p>Zebra tables</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_corner">corner</a>(string $element = &#039;&#039;, string $corner_style = &#039;&#039;)
        
                                            <p><p>Corner Plugin</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_modal">modal</a>(string $src, bool $relative = FALSE)
        
                                            <p><p>Modal window</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_effect">effect</a>(string $src, bool $relative = FALSE)
        
                                            <p>Effect</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_plugin">plugin</a>(string $src, bool $relative = FALSE)
        
                                            <p>Plugin</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_ui">ui</a>(string $src, bool $relative = FALSE)
        
                                            <p>UI</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_sortable">sortable</a>(string $element, array $options = array())
        
                                            <p>Sortable</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_tablesorter">tablesorter</a>($table = &#039;&#039;, $options = &#039;&#039;)
        
                                            <p><p>Table Sorter Plugin</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__add_event">_add_event</a>($element, $js, $event)
        
                                            <p><p>Add Event</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__compile">_compile</a>(string $view_var = &#039;script_foot&#039;, bool $script_tags = TRUE)
        
                                            <p>Compile</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__clear_compile">_clear_compile</a>()
        
                                            <p><p>Clear Compile</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__document_ready">_document_ready</a>(array $js)
        
                                            <p><p>Document Ready</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_script">script</a>(string $library_src = &#039;&#039;, bool $relative = FALSE)
        
                                            <p><p>Script Tag</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__prep_element">_prep_element</a>($element)
        
                                            <p><p>Prep Element</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__validate_speed">_validate_speed</a>($speed)
        
                                            <p><p>Validate Speed</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 108</div>
        <code>                    void
    <strong>__construct</strong>(array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Constructor</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_blur">
        <div class="location">in <a href="CI_Javascript.html#method_blur">
CI_Javascript</a> at line 104</div>
        <code>                    string
    <strong>blur</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Blur</p>                <p><p>Outputs a javascript library blur event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_change">
        <div class="location">in <a href="CI_Javascript.html#method_change">
CI_Javascript</a> at line 120</div>
        <code>                    string
    <strong>change</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Change</p>                <p><p>Outputs a javascript library change event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_click">
        <div class="location">in <a href="CI_Javascript.html#method_click">
CI_Javascript</a> at line 137</div>
        <code>                    string
    <strong>click</strong>($element = &#039;this&#039;, $js = &#039;&#039;, $ret_false = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Click</p>                <p><p>Outputs a javascript library click event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$ret_false</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_dblclick">
        <div class="location">in <a href="CI_Javascript.html#method_dblclick">
CI_Javascript</a> at line 153</div>
        <code>                    string
    <strong>dblclick</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Double Click</p></p>                <p><p>Outputs a javascript library dblclick event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_error">
        <div class="location">in <a href="CI_Javascript.html#method_error">
CI_Javascript</a> at line 169</div>
        <code>                    string
    <strong>error</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Error</p>                <p><p>Outputs a javascript library error event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_focus">
        <div class="location">in <a href="CI_Javascript.html#method_focus">
CI_Javascript</a> at line 185</div>
        <code>                    string
    <strong>focus</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Focus</p>                <p><p>Outputs a javascript library focus event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_hover">
        <div class="location">in <a href="CI_Javascript.html#method_hover">
CI_Javascript</a> at line 202</div>
        <code>                    string
    <strong>hover</strong>($element = &#039;this&#039;, $over = &#039;&#039;, $out = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Hover</p>                <p><p>Outputs a javascript library hover event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$over</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$out</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_keydown">
        <div class="location">in <a href="CI_Javascript.html#method_keydown">
CI_Javascript</a> at line 218</div>
        <code>                    string
    <strong>keydown</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Keydown</p>                <p><p>Outputs a javascript library keydown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_keyup">
        <div class="location">in <a href="CI_Javascript.html#method_keyup">
CI_Javascript</a> at line 234</div>
        <code>                    string
    <strong>keyup</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Keyup</p>                <p><p>Outputs a javascript library keydown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_load">
        <div class="location">in <a href="CI_Javascript.html#method_load">
CI_Javascript</a> at line 250</div>
        <code>                    string
    <strong>load</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Load</p>                <p><p>Outputs a javascript library load event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mousedown">
        <div class="location">in <a href="CI_Javascript.html#method_mousedown">
CI_Javascript</a> at line 266</div>
        <code>                    string
    <strong>mousedown</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Mousedown</p>                <p><p>Outputs a javascript library mousedown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mouseout">
        <div class="location">in <a href="CI_Javascript.html#method_mouseout">
CI_Javascript</a> at line 282</div>
        <code>                    string
    <strong>mouseout</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Mouse Out</p></p>                <p><p>Outputs a javascript library mouseout event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mouseover">
        <div class="location">in <a href="CI_Javascript.html#method_mouseover">
CI_Javascript</a> at line 298</div>
        <code>                    string
    <strong>mouseover</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Mouse Over</p></p>                <p><p>Outputs a javascript library mouseover event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mouseup">
        <div class="location">in <a href="CI_Javascript.html#method_mouseup">
CI_Javascript</a> at line 314</div>
        <code>                    string
    <strong>mouseup</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Mouseup</p>                <p><p>Outputs a javascript library mouseup event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_output">
        <div class="location">in <a href="CI_Javascript.html#method_output">
CI_Javascript</a> at line 329</div>
        <code>                    string
    <strong>output</strong>($js)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Output</p>                <p><p>Outputs the called javascript to the screen</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_ready">
        <div class="location">in <a href="CI_Javascript.html#method_ready">
CI_Javascript</a> at line 344</div>
        <code>                    string
    <strong>ready</strong>(string $js)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Ready</p>                <p><p>Outputs a javascript library mouseup event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$js</td>
                <td><p>Code to execute</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_resize">
        <div class="location">in <a href="CI_Javascript.html#method_resize">
CI_Javascript</a> at line 360</div>
        <code>                    string
    <strong>resize</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Resize</p>                <p><p>Outputs a javascript library resize event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_scroll">
        <div class="location">in <a href="CI_Javascript.html#method_scroll">
CI_Javascript</a> at line 376</div>
        <code>                    string
    <strong>scroll</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Scroll</p>                <p><p>Outputs a javascript library scroll event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_unload">
        <div class="location">in <a href="CI_Javascript.html#method_unload">
CI_Javascript</a> at line 392</div>
        <code>                    string
    <strong>unload</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Unload</p>                <p><p>Outputs a javascript library unload event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_addClass">
        <div class="location">in <a href="CI_Javascript.html#method_addClass">
CI_Javascript</a> at line 410</div>
        <code>                    string
    <strong>addClass</strong>($element = &#039;this&#039;, $class = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Class</p></p>                <p><p>Outputs a javascript library addClass event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$class</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_animate">
        <div class="location">in <a href="CI_Javascript.html#method_animate">
CI_Javascript</a> at line 428</div>
        <code>                    string
    <strong>animate</strong>(string $element = &#039;this&#039;, array $params = array(), mixed $speed = &#039;&#039;, string $extra = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Animate</p>                <p><p>Outputs a javascript library animate event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$element</td>
                <td><p>= 'this'</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>= array()</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$speed</td>
                <td><p>'slow', 'normal', 'fast', or time in milliseconds</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$extra</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_fadeIn">
        <div class="location">in <a href="CI_Javascript.html#method_fadeIn">
CI_Javascript</a> at line 445</div>
        <code>                    string
    <strong>fadeIn</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fade In</p></p>                <p><p>Outputs a javascript library hide event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_fadeOut">
        <div class="location">in <a href="CI_Javascript.html#method_fadeOut">
CI_Javascript</a> at line 462</div>
        <code>                    string
    <strong>fadeOut</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fade Out</p></p>                <p><p>Outputs a javascript library hide event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_slideUp">
        <div class="location">in <a href="CI_Javascript.html#method_slideUp">
CI_Javascript</a> at line 478</div>
        <code>                    string
    <strong>slideUp</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Slide Up</p></p>                <p><p>Outputs a javascript library slideUp event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_removeClass">
        <div class="location">in <a href="CI_Javascript.html#method_removeClass">
CI_Javascript</a> at line 495</div>
        <code>                    string
    <strong>removeClass</strong>($element = &#039;this&#039;, $class = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Remove Class</p></p>                <p><p>Outputs a javascript library removeClass event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$class</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_slideDown">
        <div class="location">in <a href="CI_Javascript.html#method_slideDown">
CI_Javascript</a> at line 512</div>
        <code>                    string
    <strong>slideDown</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Slide Down</p></p>                <p><p>Outputs a javascript library slideDown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_slideToggle">
        <div class="location">in <a href="CI_Javascript.html#method_slideToggle">
CI_Javascript</a> at line 529</div>
        <code>                    string
    <strong>slideToggle</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Slide Toggle</p></p>                <p><p>Outputs a javascript library slideToggle event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_hide">
        <div class="location">in <a href="CI_Javascript.html#method_hide">
CI_Javascript</a> at line 547</div>
        <code>                    string
    <strong>hide</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Hide</p>                <p><p>Outputs a javascript library hide action</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_toggle">
        <div class="location">in <a href="CI_Javascript.html#method_toggle">
CI_Javascript</a> at line 562</div>
        <code>                    string
    <strong>toggle</strong>($element = &#039;this&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Toggle</p>                <p><p>Outputs a javascript library toggle event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_toggleClass">
        <div class="location">in <a href="CI_Javascript.html#method_toggleClass">
CI_Javascript</a> at line 579</div>
        <code>                    string
    <strong>toggleClass</strong>(string $element = &#039;this&#039;, string $class = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Toggle Class</p></p>                <p><p>Outputs a javascript library toggle class event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$element</td>
                <td><p>= 'this'</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$class</td>
                <td><p>= ''</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_show">
        <div class="location">in <a href="CI_Javascript.html#method_show">
CI_Javascript</a> at line 596</div>
        <code>                    string
    <strong>show</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Show</p>                <p><p>Outputs a javascript library show event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_compile">
        <div class="location">in <a href="CI_Javascript.html#method_compile">
CI_Javascript</a> at line 612</div>
        <code>                    string
    <strong>compile</strong>(string $view_var = &#039;script_foot&#039;, bool $script_tags = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Compile</p>                <p><p>gather together all script needing to be output</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$view_var</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$script_tags</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_clear_compile">
        <div class="location">in <a href="CI_Javascript.html#method_clear_compile">
CI_Javascript</a> at line 626</div>
        <code>                    void
    <strong>clear_compile</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Clear Compile</p></p>                <p><p>Clears any previous javascript collected for output</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_external">
        <div class="location">in <a href="CI_Javascript.html#method_external">
CI_Javascript</a> at line 642</div>
        <code>                    string
    <strong>external</strong>(string $external_file = &#039;&#039;, bool $relative = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>External</p>                <p><p>Outputs a <script> tag with the source as an external js file</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$external_file</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$relative</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_inline">
        <div class="location">in <a href="CI_Javascript.html#method_inline">
CI_Javascript</a> at line 680</div>
        <code>                    string
    <strong>inline</strong>($script, $cdata = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Inline</p>                <p><p>Outputs a <script> tag</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$script</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$cdata</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__open_script">
        <div class="location">in <a href="CI_Javascript.html#method__open_script">
CI_Javascript</a> at line 697</div>
        <code>            protected        string
    <strong>_open_script</strong>($src = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Open Script</p></p>                <p><p>Outputs an opening <script></p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$src</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__close_script">
        <div class="location">in <a href="CI_Javascript.html#method__close_script">
CI_Javascript</a> at line 713</div>
        <code>            protected        string
    <strong>_close_script</strong>($extra = &quot;\n&quot;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Close Script</p></p>                <p><p>Outputs an closing </script></p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$extra</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_update">
        <div class="location">in <a href="CI_Javascript.html#method_update">
CI_Javascript</a> at line 732</div>
        <code>                    string
    <strong>update</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Update</p>                <p><p>Outputs a javascript library slideDown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_generate_json">
        <div class="location">in <a href="CI_Javascript.html#method_generate_json">
CI_Javascript</a> at line 748</div>
        <code>                    string
    <strong>generate_json</strong>($result = NULL, $match_array_type = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Generate JSON</p></p>                <p><p>Can be passed a database result or associative array and returns a JSON formatted string</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$result</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$match_array_type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>a json formatted string</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__is_associative_array">
        <div class="location">in <a href="CI_Javascript.html#method__is_associative_array">
CI_Javascript</a> at line 812</div>
        <code>            protected        bool
    <strong>_is_associative_array</strong>($arr)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is associative array</p></p>                <p><p>Checks for an associative array</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$arr</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__prep_args">
        <div class="location">in <a href="CI_Javascript.html#method__prep_args">
CI_Javascript</a> at line 836</div>
        <code>            protected        string
    <strong>_prep_args</strong>(mixed $result, bool $is_key = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prep Args</p></p>                <p><p>Ensures a standard json value and escapes values</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$result</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$is_key</td>
                <td><p>= FALSE</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__blur">
        <div class="location">at line 134</div>
        <code>            protected        string
    <strong>_blur</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Blur</p>                <p><p>Outputs a jQuery blur event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__change">
        <div class="location">at line 150</div>
        <code>            protected        string
    <strong>_change</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Change</p>                <p><p>Outputs a jQuery change event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__click">
        <div class="location">at line 167</div>
        <code>            protected        string
    <strong>_click</strong>($element = &#039;this&#039;, $js = &#039;&#039;, $ret_false = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Click</p>                <p><p>Outputs a jQuery click event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$ret_false</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__dblclick">
        <div class="location">at line 190</div>
        <code>            protected        string
    <strong>_dblclick</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Double Click</p></p>                <p><p>Outputs a jQuery dblclick event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__error">
        <div class="location">at line 206</div>
        <code>            protected        string
    <strong>_error</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Error</p>                <p><p>Outputs a jQuery error event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__focus">
        <div class="location">at line 222</div>
        <code>            protected        string
    <strong>_focus</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Focus</p>                <p><p>Outputs a jQuery focus event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__hover">
        <div class="location">at line 239</div>
        <code>            protected        string
    <strong>_hover</strong>($element = &#039;this&#039;, $over = &#039;&#039;, $out = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Hover</p>                <p><p>Outputs a jQuery hover event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$over</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$out</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__keydown">
        <div class="location">at line 259</div>
        <code>            protected        string
    <strong>_keydown</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Keydown</p>                <p><p>Outputs a jQuery keydown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__keyup">
        <div class="location">at line 275</div>
        <code>            protected        string
    <strong>_keyup</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Keyup</p>                <p><p>Outputs a jQuery keydown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__load">
        <div class="location">at line 291</div>
        <code>            protected        string
    <strong>_load</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Load</p>                <p><p>Outputs a jQuery load event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__mousedown">
        <div class="location">at line 307</div>
        <code>            protected        string
    <strong>_mousedown</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Mousedown</p>                <p><p>Outputs a jQuery mousedown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__mouseout">
        <div class="location">at line 323</div>
        <code>            protected        string
    <strong>_mouseout</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Mouse Out</p></p>                <p><p>Outputs a jQuery mouseout event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__mouseover">
        <div class="location">at line 339</div>
        <code>            protected        string
    <strong>_mouseover</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Mouse Over</p></p>                <p><p>Outputs a jQuery mouseover event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__mouseup">
        <div class="location">at line 355</div>
        <code>            protected        string
    <strong>_mouseup</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Mouseup</p>                <p><p>Outputs a jQuery mouseup event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__output">
        <div class="location">at line 370</div>
        <code>            protected        void
    <strong>_output</strong>(array $array_js = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Output</p>                <p><p>Outputs script directly</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$array_js</td>
                <td><p>= array()</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__resize">
        <div class="location">at line 394</div>
        <code>            protected        string
    <strong>_resize</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Resize</p>                <p><p>Outputs a jQuery resize event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__scroll">
        <div class="location">at line 410</div>
        <code>            protected        string
    <strong>_scroll</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Scroll</p>                <p><p>Outputs a jQuery scroll event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__unload">
        <div class="location">at line 426</div>
        <code>            protected        string
    <strong>_unload</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Unload</p>                <p><p>Outputs a jQuery unload event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__addClass">
        <div class="location">at line 444</div>
        <code>            protected        string
    <strong>_addClass</strong>(string $element = &#039;this&#039;, string $class = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Class</p></p>                <p><p>Outputs a jQuery addClass event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$class</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__animate">
        <div class="location">at line 463</div>
        <code>            protected        string
    <strong>_animate</strong>(string $element = &#039;this&#039;, array $params = array(), string $speed = &#039;&#039;, string $extra = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Animate</p>                <p><p>Outputs a jQuery animate event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$speed</td>
                <td><p>'slow', 'normal', 'fast', or time in milliseconds</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$extra</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__fadeIn">
        <div class="location">at line 502</div>
        <code>            protected        string
    <strong>_fadeIn</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fade In</p></p>                <p><p>Outputs a jQuery hide event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__fadeOut">
        <div class="location">at line 527</div>
        <code>            protected        string
    <strong>_fadeOut</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fade Out</p></p>                <p><p>Outputs a jQuery hide event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__hide">
        <div class="location">at line 552</div>
        <code>            protected        string
    <strong>_hide</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Hide</p>                <p><p>Outputs a jQuery hide action</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__removeClass">
        <div class="location">at line 576</div>
        <code>            protected        string
    <strong>_removeClass</strong>(string $element = &#039;this&#039;, string $class = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Remove Class</p></p>                <p><p>Outputs a jQuery remove class event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$class</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__slideUp">
        <div class="location">at line 594</div>
        <code>            protected        string
    <strong>_slideUp</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Slide Up</p></p>                <p><p>Outputs a jQuery slideUp event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__slideDown">
        <div class="location">at line 619</div>
        <code>            protected        string
    <strong>_slideDown</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Slide Down</p></p>                <p><p>Outputs a jQuery slideDown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__slideToggle">
        <div class="location">at line 644</div>
        <code>            protected        string
    <strong>_slideToggle</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Slide Toggle</p></p>                <p><p>Outputs a jQuery slideToggle event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__toggle">
        <div class="location">at line 667</div>
        <code>            protected        string
    <strong>_toggle</strong>($element = &#039;this&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Toggle</p>                <p><p>Outputs a jQuery toggle event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__toggleClass">
        <div class="location">at line 684</div>
        <code>            protected        string
    <strong>_toggleClass</strong>(string $element = &#039;this&#039;, string $class = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Toggle Class</p></p>                <p><p>Outputs a jQuery toggle class event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$class</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__show">
        <div class="location">at line 702</div>
        <code>            protected        string
    <strong>_show</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Show</p>                <p><p>Outputs a jQuery show event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__updater">
        <div class="location">at line 729</div>
        <code>            protected        string
    <strong>_updater</strong>($container = &#039;this&#039;, $controller = &#039;&#039;, $options = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Updater</p>                <p><p>An Ajax call that populates the designated DOM node with
returned content</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$container</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$controller</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$options</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__zebraTables">
        <div class="location">at line 770</div>
        <code>            protected        string
    <strong>_zebraTables</strong>(string $class = &#039;&#039;, string $odd = &#039;odd&#039;, string $hover = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Zebra tables</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$class</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$odd</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$hover</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_corner">
        <div class="location">at line 797</div>
        <code>                    string
    <strong>corner</strong>(string $element = &#039;&#039;, string $corner_style = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Corner Plugin</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$corner_style</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_modal">
        <div class="location">at line 821</div>
        <code>                    void
    <strong>modal</strong>(string $src, bool $relative = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Modal window</p></p>                <p><p>Load a thickbox modal window</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$src</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$relative</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_effect">
        <div class="location">at line 837</div>
        <code>                    void
    <strong>effect</strong>(string $src, bool $relative = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Effect</p>                <p><p>Load an Effect library</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$src</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$relative</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_plugin">
        <div class="location">at line 853</div>
        <code>                    void
    <strong>plugin</strong>(string $src, bool $relative = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Plugin</p>                <p><p>Load a plugin library</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$src</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$relative</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_ui">
        <div class="location">at line 869</div>
        <code>                    void
    <strong>ui</strong>(string $src, bool $relative = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>UI</p>                <p><p>Load a user interface library</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$src</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$relative</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_sortable">
        <div class="location">at line 885</div>
        <code>                    string
    <strong>sortable</strong>(string $element, array $options = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Sortable</p>                <p><p>Creates a jQuery sortable</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$options</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_tablesorter">
        <div class="location">at line 913</div>
        <code>                    string
    <strong>tablesorter</strong>($table = &#039;&#039;, $options = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Table Sorter Plugin</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$table</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$options</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__add_event">
        <div class="location">at line 932</div>
        <code>            protected        string
    <strong>_add_event</strong>($element, $js, $event)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Event</p></p>                <p><p>Constructs the syntax for an event, and adds to into the array for compilation</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$event</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__compile">
        <div class="location">at line 956</div>
        <code>            protected        void
    <strong>_compile</strong>(string $view_var = &#039;script_foot&#039;, bool $script_tags = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Compile</p>                <p><p>As events are specified, they are stored in an array
This function compiles them all for output on a page</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$view_var</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$script_tags</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__clear_compile">
        <div class="location">at line 987</div>
        <code>            protected        void
    <strong>_clear_compile</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Clear Compile</p></p>                <p><p>Clears the array of script events collected for output</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__document_ready">
        <div class="location">at line 1002</div>
        <code>            protected        void
    <strong>_document_ready</strong>(array $js)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Document Ready</p></p>                <p><p>A wrapper for writing document.ready()</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_script">
        <div class="location">at line 1023</div>
        <code>                    string
    <strong>script</strong>(string $library_src = &#039;&#039;, bool $relative = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Script Tag</p></p>                <p><p>Outputs the script tag that loads the jquery.js file into an HTML document</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$library_src</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$relative</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__prep_element">
        <div class="location">at line 1042</div>
        <code>            protected        string
    <strong>_prep_element</strong>($element)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prep Element</p></p>                <p><p>Puts HTML element in quotes for use in jQuery code
unless the supplied element is the Javascript 'this'
object, in which case no quotes are added</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__validate_speed">
        <div class="location">at line 1062</div>
        <code>            protected        string
    <strong>_validate_speed</strong>($speed)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validate Speed</p></p>                <p><p>Ensures the speed parameter is valid for jQuery</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

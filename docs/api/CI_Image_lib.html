<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Image_lib | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Image_lib" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Image_lib    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Image_lib</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Image Manipulation class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_image_library">
                                                                                string
                                                                                
                                    </td>
                <td>$image_library</td>
                <td class="last"><p>PHP extension/library to use for image manipulation
Can be: imagemagick, netpbm, gd, gd2</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_library_path">
                                                                                string
                                                                                
                                    </td>
                <td>$library_path</td>
                <td class="last"><p>Path to the graphic library (if applicable)</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_dynamic_output">
                                                                                bool
                                                                                
                                    </td>
                <td>$dynamic_output</td>
                <td class="last"><p>Whether to send to browser or write to disk</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_source_image">
                                                                                string
                                                                                
                                    </td>
                <td>$source_image</td>
                <td class="last"><p>Path to original image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_new_image">
                                                                                string
                                                                                
                                    </td>
                <td>$new_image</td>
                <td class="last"><p>Path to the modified image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_width">
                                                                                int
                                                                                
                                    </td>
                <td>$width</td>
                <td class="last"><p>Image width</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_height">
                                                                                int
                                                                                
                                    </td>
                <td>$height</td>
                <td class="last"><p>Image height</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_quality">
                                                                                int
                                                                                
                                    </td>
                <td>$quality</td>
                <td class="last"><p>Quality percentage of new image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_create_thumb">
                                                                                bool
                                                                                
                                    </td>
                <td>$create_thumb</td>
                <td class="last"><p>Whether to create a thumbnail</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_thumb_marker">
                                                                                string
                                                                                
                                    </td>
                <td>$thumb_marker</td>
                <td class="last"><p>String to add to thumbnail version of image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_maintain_ratio">
                                                                                bool
                                                                                
                                    </td>
                <td>$maintain_ratio</td>
                <td class="last"><p>Whether to maintain aspect ratio when resizing or use hard values</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_master_dim">
                                                                                string
                                                                                
                                    </td>
                <td>$master_dim</td>
                <td class="last"><p>auto, height, or width.  Determines what to use as the master dimension</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_rotation_angle">
                                                                                string
                                                                                
                                    </td>
                <td>$rotation_angle</td>
                <td class="last"><p>Angle at to rotate image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_x_axis">
                                                                                int
                                                                                
                                    </td>
                <td>$x_axis</td>
                <td class="last"><p>X Coordinate for manipulation of the current image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_y_axis">
                                                                                int
                                                                                
                                    </td>
                <td>$y_axis</td>
                <td class="last"><p>Y Coordinate for manipulation of the current image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_text">
                                                                                string
                                                                                
                                    </td>
                <td>$wm_text</td>
                <td class="last"><p>Watermark text if graphic is not used</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_type">
                                                                                string
                                                                                
                                    </td>
                <td>$wm_type</td>
                <td class="last"><p>Type of watermarking.  Options:  text/overlay</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_x_transp">
                                                                                int
                                                                                
                                    </td>
                <td>$wm_x_transp</td>
                <td class="last"><p>Default transparency for watermark</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_y_transp">
                                                                                int
                                                                                
                                    </td>
                <td>$wm_y_transp</td>
                <td class="last"><p>Default transparency for watermark</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_overlay_path">
                                                                                string
                                                                                
                                    </td>
                <td>$wm_overlay_path</td>
                <td class="last"><p>Watermark image path</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_font_path">
                                                                                string
                                                                                
                                    </td>
                <td>$wm_font_path</td>
                <td class="last"><p>TT font</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_font_size">
                                                                                int
                                                                                
                                    </td>
                <td>$wm_font_size</td>
                <td class="last"><p>Font size (different versions of GD will either use points or pixels)</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_vrt_alignment">
                                                                                string
                                                                                
                                    </td>
                <td>$wm_vrt_alignment</td>
                <td class="last"><p>Vertical alignment:   T M B</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_hor_alignment">
                                                                                string
                                                                                
                                    </td>
                <td>$wm_hor_alignment</td>
                <td class="last"><p>Horizontal alignment: L R C</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_padding">
                                                                                int
                                                                                
                                    </td>
                <td>$wm_padding</td>
                <td class="last"><p>Padding around text</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_hor_offset">
                                                                                int
                                                                                
                                    </td>
                <td>$wm_hor_offset</td>
                <td class="last"><p>Lets you push text to the right</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_vrt_offset">
                                                                                int
                                                                                
                                    </td>
                <td>$wm_vrt_offset</td>
                <td class="last"><p>Lets you push text down</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_font_color">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$wm_font_color</td>
                <td class="last"><p>Text color</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_shadow_color">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$wm_shadow_color</td>
                <td class="last"><p>Dropshadow color</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_shadow_distance">
                                                                                int
                                                                                
                                    </td>
                <td>$wm_shadow_distance</td>
                <td class="last"><p>Dropshadow distance</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_opacity">
                                                                                int
                                                                                
                                    </td>
                <td>$wm_opacity</td>
                <td class="last"><p>Image opacity: 1 - 100  Only works with image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_source_folder">
                                                                                string
                                                                                
                                    </td>
                <td>$source_folder</td>
                <td class="last"><p>Source image folder</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_dest_folder">
                                                                                string
                                                                                
                                    </td>
                <td>$dest_folder</td>
                <td class="last"><p>Destination image folder</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_mime_type">
                                                                                string
                                                                                
                                    </td>
                <td>$mime_type</td>
                <td class="last"><p>Image mime-type</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_orig_width">
                                                                                int
                                                                                
                                    </td>
                <td>$orig_width</td>
                <td class="last"><p>Original image width</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_orig_height">
                                                                                int
                                                                                
                                    </td>
                <td>$orig_height</td>
                <td class="last"><p>Original image height</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_image_type">
                                                                                string
                                                                                
                                    </td>
                <td>$image_type</td>
                <td class="last"><p>Image format</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_size_str">
                                                                                string
                                                                                
                                    </td>
                <td>$size_str</td>
                <td class="last"><p>Size of current image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_full_src_path">
                                                                                string
                                                                                
                                    </td>
                <td>$full_src_path</td>
                <td class="last"><p>Full path to source image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_full_dst_path">
                                                                                string
                                                                                
                                    </td>
                <td>$full_dst_path</td>
                <td class="last"><p>Full path to destination image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_file_permissions">
                                                                                int
                                                                                
                                    </td>
                <td>$file_permissions</td>
                <td class="last"><p>File permissions</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_create_fnc">
                                                                                string
                                                                                
                                    </td>
                <td>$create_fnc</td>
                <td class="last"><p>Name of function to create image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_copy_fnc">
                                                                                string
                                                                                
                                    </td>
                <td>$copy_fnc</td>
                <td class="last"><p>Name of function to copy image</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_error_msg">
                                                                                array
                                                                                
                                    </td>
                <td>$error_msg</td>
                <td class="last"><p>Error messages</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_use_drop_shadow">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$wm_use_drop_shadow</td>
                <td class="last"><p>Whether to have a drop shadow on watermark</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wm_use_truetype">
                                                                                bool
                                                                                
                                    </td>
                <td>$wm_use_truetype</td>
                <td class="last"><p>Whether to use truetype fonts</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $props = array())
        
                                            <p><p>Initialize Image Library</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_clear">clear</a>()
        
                                            <p><p>Initialize image properties</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_initialize">initialize</a>($props = array())
        
                                            <p><p>initialize image preferences</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_resize">resize</a>()
        
                                            <p><p>Image Resize</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_crop">crop</a>()
        
                                            <p><p>Image Crop</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_rotate">rotate</a>()
        
                                            <p><p>Image Rotate</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_image_process_gd">image_process_gd</a>($action = &#039;resize&#039;)
        
                                            <p><p>Image Process Using GD/GD2</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_image_process_imagemagick">image_process_imagemagick</a>($action = &#039;resize&#039;)
        
                                            <p><p>Image Process Using ImageMagick</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_image_process_netpbm">image_process_netpbm</a>($action = &#039;resize&#039;)
        
                                            <p><p>Image Process Using NetPBM</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_image_rotate_gd">image_rotate_gd</a>()
        
                                            <p><p>Image Rotate Using GD</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_image_mirror_gd">image_mirror_gd</a>()
        
                                            <p><p>Create Mirror Image using GD</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_watermark">watermark</a>()
        
                                            <p><p>Image Watermark</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_overlay_watermark">overlay_watermark</a>()
        
                                            <p><p>Watermark - Graphic Version</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_text_watermark">text_watermark</a>()
        
                                            <p><p>Watermark - Text Version</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    resource
                </div>
                <div class="col-md-8">
                    <a href="#method_image_create_gd">image_create_gd</a>($path = &#039;&#039;, $image_type = &#039;&#039;)
        
                                            <p><p>Create Image - GD</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_image_save_gd">image_save_gd</a>($resource)
        
                                            <p><p>Write image file to disk - GD</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_image_display_gd">image_display_gd</a>($resource)
        
                                            <p><p>Dynamically outputs an image</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_image_reproportion">image_reproportion</a>()
        
                                            <p><p>Re-proportion Image Width/Height</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_get_image_properties">get_image_properties</a>($path = &#039;&#039;, $return = FALSE)
        
                                            <p><p>Get image properties</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_size_calculator">size_calculator</a>($vals)
        
                                            <p><p>Size calculator</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_explode_name">explode_name</a>($source_image)
        
                                            <p><p>Explode source_image</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_gd_loaded">gd_loaded</a>()
        
                                            <p><p>Is GD Installed?</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_gd_version">gd_version</a>()
        
                                            <p><p>Get GD version</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_error">set_error</a>($msg)
        
                                            <p><p>Set error message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_display_errors">display_errors</a>($open = &#039;&lt;p&gt;&#039;, $close = &#039;&lt;/p&gt;&#039;)
        
                                            <p><p>Show error messages</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 388</div>
        <code>                    void
    <strong>__construct</strong>(array $props = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize Image Library</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$props</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_clear">
        <div class="location">at line 417</div>
        <code>                    void
    <strong>clear</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize image properties</p></p>                <p><p>Resets values in case this class is used in a loop</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initialize">
        <div class="location">at line 460</div>
        <code>                    bool
    <strong>initialize</strong>($props = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>initialize image preferences</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$props</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_resize">
        <div class="location">at line 675</div>
        <code>                    bool
    <strong>resize</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Image Resize</p></p>                <p><p>This is a wrapper function that chooses the proper
resize function based on the protocol specified</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_crop">
        <div class="location">at line 691</div>
        <code>                    bool
    <strong>crop</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Image Crop</p></p>                <p><p>This is a wrapper function that chooses the proper
cropping function based on the protocol specified</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_rotate">
        <div class="location">at line 707</div>
        <code>                    bool
    <strong>rotate</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Image Rotate</p></p>                <p><p>This is a wrapper function that chooses the proper
rotation function based on the protocol specified</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_image_process_gd">
        <div class="location">at line 752</div>
        <code>                    bool
    <strong>image_process_gd</strong>($action = &#039;resize&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Image Process Using GD/GD2</p></p>                <p><p>This function will resize or crop</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$action</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_image_process_imagemagick">
        <div class="location">at line 856</div>
        <code>                    bool
    <strong>image_process_imagemagick</strong>($action = &#039;resize&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Image Process Using ImageMagick</p></p>                <p><p>This function will resize, crop or rotate</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$action</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_image_process_netpbm">
        <div class="location">at line 926</div>
        <code>                    bool
    <strong>image_process_netpbm</strong>($action = &#039;resize&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Image Process Using NetPBM</p></p>                <p><p>This function will resize, crop or rotate</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$action</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_image_rotate_gd">
        <div class="location">at line 1011</div>
        <code>                    bool
    <strong>image_rotate_gd</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Image Rotate Using GD</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_image_mirror_gd">
        <div class="location">at line 1057</div>
        <code>                    bool
    <strong>image_mirror_gd</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create Mirror Image using GD</p></p>                <p><p>This function will flip horizontal or vertical</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_watermark">
        <div class="location">at line 1136</div>
        <code>                    bool
    <strong>watermark</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Image Watermark</p></p>                <p><p>This is a wrapper function that chooses the type
of watermarking based on the specified preference.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_overlay_watermark">
        <div class="location">at line 1148</div>
        <code>                    bool
    <strong>overlay_watermark</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Watermark - Graphic Version</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_text_watermark">
        <div class="location">at line 1262</div>
        <code>                    bool
    <strong>text_watermark</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Watermark - Text Version</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_image_create_gd">
        <div class="location">at line 1437</div>
        <code>                    resource
    <strong>image_create_gd</strong>($path = &#039;&#039;, $image_type = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create Image - GD</p></p>                <p><p>This simply creates an image resource handle
based on the type of image being processed</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$path</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$image_type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>resource</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_image_save_gd">
        <div class="location">at line 1492</div>
        <code>                    bool
    <strong>image_save_gd</strong>($resource)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Write image file to disk - GD</p></p>                <p><p>Takes an image resource as input and writes the file
to the specified destination</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$resource</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_image_display_gd">
        <div class="location">at line 1552</div>
        <code>                    void
    <strong>image_display_gd</strong>($resource)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Dynamically outputs an image</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$resource</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_image_reproportion">
        <div class="location">at line 1586</div>
        <code>                    void
    <strong>image_reproportion</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Re-proportion Image Width/Height</p></p>                <p><p>When creating thumbs, the desired width/height
can end up warping the image due to an incorrect
ratio between the full-sized image and the thumb.</p>
<p>This function lets us re-proportion the width/height
if users choose to maintain the aspect ratio when resizing.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_image_properties">
        <div class="location">at line 1638</div>
        <code>                    mixed
    <strong>get_image_properties</strong>($path = &#039;&#039;, $return = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get image properties</p></p>                <p><p>A helper function that gets info about the file</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$path</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$return</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_size_calculator">
        <div class="location">at line 1703</div>
        <code>                    array
    <strong>size_calculator</strong>($vals)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Size calculator</p></p>                <p><p>This function takes a known width x height and
recalculates it to a new size. Only one
new variable needs to be known</p>
<p>$props = array(
'width'     =&gt; $width,
'height'    =&gt; $height,
'new_width' =&gt; 40,
'new_height'    =&gt; ''
);</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$vals</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_explode_name">
        <div class="location">at line 1752</div>
        <code>                    array
    <strong>explode_name</strong>($source_image)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Explode source_image</p></p>                <p><p>This is a helper function that extracts the extension
from the source_image.  This function lets us deal with
source_images with multiple periods, like: my.cool.jpg
It returns an associative array with two elements:
$array['ext']  = '.jpg';
$array['name'] = 'my.cool';</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$source_image</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_gd_loaded">
        <div class="location">at line 1767</div>
        <code>                    bool
    <strong>gd_loaded</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is GD Installed?</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_gd_version">
        <div class="location">at line 1787</div>
        <code>                    mixed
    <strong>gd_version</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get GD version</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_error">
        <div class="location">at line 1806</div>
        <code>                    void
    <strong>set_error</strong>($msg)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set error message</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$msg</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_display_errors">
        <div class="location">at line 1837</div>
        <code>                    string
    <strong>display_errors</strong>($open = &#039;&lt;p&gt;&#039;, $close = &#039;&lt;/p&gt;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Show error messages</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$open</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$close</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Email | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Email" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Email    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Email</strong>
</p>

        
    
        

            <div class="description">
            <p><p>CodeIgniter Email Class</p></p>            <p><p>Permits email to be sent using Mail, Sendmail, or SMTP.</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_useragent">
                                                                                string
                                                                                
                                    </td>
                <td>$useragent</td>
                <td class="last"><p>Used as the User-Agent and X-Mailer headers' value.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_mailpath">
                                                                                string
                                                                                
                                    </td>
                <td>$mailpath</td>
                <td class="last"><p>Path to the Sendmail binary.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_protocol">
                                                                                string
                                                                                
                                    </td>
                <td>$protocol</td>
                <td class="last"><p>Which method to use for sending e-mails.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_smtp_host">
                                                                                string
                                                                                
                                    </td>
                <td>$smtp_host</td>
                <td class="last"><p>STMP Server host</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_smtp_user">
                                                                                string
                                                                                
                                    </td>
                <td>$smtp_user</td>
                <td class="last"><p>SMTP Username</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_smtp_pass">
                                                                                string
                                                                                
                                    </td>
                <td>$smtp_pass</td>
                <td class="last"><p>SMTP Password</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_smtp_port">
                                                                                int
                                                                                
                                    </td>
                <td>$smtp_port</td>
                <td class="last"><p>SMTP Server port</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_smtp_timeout">
                                                                                int
                                                                                
                                    </td>
                <td>$smtp_timeout</td>
                <td class="last"><p>SMTP connection timeout in seconds</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_smtp_keepalive">
                                                                                bool
                                                                                
                                    </td>
                <td>$smtp_keepalive</td>
                <td class="last"><p>SMTP persistent connection</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_smtp_crypto">
                                                                                string
                                                                                
                                    </td>
                <td>$smtp_crypto</td>
                <td class="last"><p>SMTP Encryption</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wordwrap">
                                                                                bool
                                                                                
                                    </td>
                <td>$wordwrap</td>
                <td class="last"><p>Whether to apply word-wrapping to the message body.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_wrapchars">
                                                                                int
                                                                                
                                    </td>
                <td>$wrapchars</td>
                <td class="last"><p>Number of characters to wrap at.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_mailtype">
                                                                                string
                                                                                
                                    </td>
                <td>$mailtype</td>
                <td class="last"><p>Message format.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_charset">
                                                                                string
                                                                                
                                    </td>
                <td>$charset</td>
                <td class="last"><p>Character set (default: utf-8)</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_alt_message">
                                                                                string
                                                                                
                                    </td>
                <td>$alt_message</td>
                <td class="last"><p>Alternative message (for HTML messages only)</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_validate">
                                                                                bool
                                                                                
                                    </td>
                <td>$validate</td>
                <td class="last"><p>Whether to validate e-mail addresses.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_priority">
                                                                                int
                                                                                
                                    </td>
                <td>$priority</td>
                <td class="last"><p>X-Priority header value.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_newline">
                                                                                string
                                                                                
                                    </td>
                <td>$newline</td>
                <td class="last"><p>Newline character sequence.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_crlf">
                                                                                string
                                                                                
                                    </td>
                <td>$crlf</td>
                <td class="last"><p>CRLF character sequence</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_dsn">
                                                                                bool
                                                                                
                                    </td>
                <td>$dsn</td>
                <td class="last"><p>Whether to use Delivery Status Notification.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_send_multipart">
                                                                                bool
                                                                                
                                    </td>
                <td>$send_multipart</td>
                <td class="last"><p>Whether to send multipart alternatives.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_bcc_batch_mode">
                                                                                bool
                                                                                
                                    </td>
                <td>$bcc_batch_mode</td>
                <td class="last"><p>Whether to send messages to BCC recipients in batches.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_bcc_batch_size">
                                                                                int
                                                                                
                                    </td>
                <td>$bcc_batch_size</td>
                <td class="last"><p>BCC Batch max number size.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__safe_mode">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_safe_mode</td>
                <td class="last"><p>Whether PHP is running in safe mode. Initialized by the class constructor.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__subject">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_subject</td>
                <td class="last"><p>Subject header</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__body">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_body</td>
                <td class="last"><p>Message body</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__finalbody">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_finalbody</td>
                <td class="last"><p>Final message body to be sent.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__header_str">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_header_str</td>
                <td class="last"><p>Final headers to send</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__smtp_connect">
                                        protected                                        resource
                                                                                
                                    </td>
                <td>$_smtp_connect</td>
                <td class="last"><p>SMTP Connection socket placeholder</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__encoding">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_encoding</td>
                <td class="last"><p>Mail encoding</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__smtp_auth">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_smtp_auth</td>
                <td class="last"><p>Whether to perform SMTP authentication</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__replyto_flag">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_replyto_flag</td>
                <td class="last"><p>Whether to send a Reply-To header</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__debug_msg">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_debug_msg</td>
                <td class="last"><p>Debug messages</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__recipients">
                                        protected                                        string[]
                                                                                
                                    </td>
                <td>$_recipients</td>
                <td class="last">Recipients</td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__cc_array">
                                        protected                                        string[]
                                                                                
                                    </td>
                <td>$_cc_array</td>
                <td class="last"><p>CC Recipients</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__bcc_array">
                                        protected                                        string[]
                                                                                
                                    </td>
                <td>$_bcc_array</td>
                <td class="last"><p>BCC Recipients</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__headers">
                                        protected                                        string[]
                                                                                
                                    </td>
                <td>$_headers</td>
                <td class="last"><p>Message headers</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__attachments">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_attachments</td>
                <td class="last"><p>Attachment data</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__protocols">
                                        protected                                        string[]
                                                                                
                                    </td>
                <td>$_protocols</td>
                <td class="last"><p>Valid $protocol values</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__base_charsets">
                                        protected                                        string[]
                                                                                
                                    </td>
                <td>$_base_charsets</td>
                <td class="last"><p>Base charsets</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__bit_depths">
                                        protected                                        string[]
                                                                                
                                    </td>
                <td>$_bit_depths</td>
                <td class="last"><p>Bit depths</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__priorities">
                                        protected                                        string[]
                                                                                
                                    </td>
                <td>$_priorities</td>
                <td class="last"><p>$priority translations</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_func_overload">
                    static                    protected                                        bool
                                                                                
                                    </td>
                <td>$func_overload</td>
                <td class="last"><p>mbstring.func_overload flag</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $config = array())
        
                                            <p><p>Constructor - Sets Email Preferences</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_initialize">initialize</a>(array $config = array())
        
                                            <p><p>Initialize preferences</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_clear">clear</a>($clear_attachments = FALSE)
        
                                            <p><p>Initialize the Email Data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_from">from</a>(string $from, string $name = &#039;&#039;, string $return_path = NULL)
        
                                            <p><p>Set FROM</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_reply_to">reply_to</a>($replyto, $name = &#039;&#039;)
        
                                            <p><p>Set Reply-to</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_to">to</a>($to)
        
                                            <p><p>Set Recipients</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_cc">cc</a>($cc)
        
                                            <p><p>Set CC</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_bcc">bcc</a>($bcc, $limit = &#039;&#039;)
        
                                            <p><p>Set BCC</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_subject">subject</a>($subject)
        
                                            <p><p>Set Email Subject</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_message">message</a>($body)
        
                                            <p><p>Set Body</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_attach">attach</a>(string $file, string $disposition = &#039;&#039;, string $newname = NULL, string $mime = &#039;&#039;)
        
                                            <p><p>Assign file attachments</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_attachment_cid">attachment_cid</a>(string $filename)
        
                                            <p><p>Set and return attachment Content-ID</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_header">set_header</a>($header, $value)
        
                                            <p><p>Add a Header Item</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method__str_to_array">_str_to_array</a>($email)
        
                                            <p><p>Convert a String to an Array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_alt_message">set_alt_message</a>($str)
        
                                            <p><p>Set Multipart Value</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_mailtype">set_mailtype</a>($type = &#039;text&#039;)
        
                                            <p><p>Set Mailtype</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_wordwrap">set_wordwrap</a>($wordwrap = TRUE)
        
                                            <p><p>Set Wordwrap</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_protocol">set_protocol</a>($protocol = &#039;mail&#039;)
        
                                            <p><p>Set Protocol</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_priority">set_priority</a>($n = 3)
        
                                            <p><p>Set Priority</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_newline">set_newline</a>($newline = &quot;\n&quot;)
        
                                            <p><p>Set Newline Character</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Email.html">CI_Email</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_crlf">set_crlf</a>($crlf = &quot;\n&quot;)
        
                                            <p><p>Set CRLF</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_message_id">_get_message_id</a>()
        
                                            <p><p>Get the Message ID</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method__get_protocol">_get_protocol</a>()
        
                                            <p><p>Get Mail Protocol</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_encoding">_get_encoding</a>()
        
                                            <p><p>Get Mail Encoding</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_content_type">_get_content_type</a>()
        
                                            <p><p>Get content type (text/html/attachment)</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__set_date">_set_date</a>()
        
                                            <p><p>Set RFC 822 Date</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_mime_message">_get_mime_message</a>()
        
                                            <p><p>Mime message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_validate_email">validate_email</a>($email)
        
                                            <p><p>Validate Email Address</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_valid_email">valid_email</a>($email)
        
                                            <p><p>Email Validation</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_clean_email">clean_email</a>($email)
        
                                            <p><p>Clean Extended Email Address: Joe Smith <a href="mailto:<EMAIL>"><EMAIL></a></p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_alt_message">_get_alt_message</a>()
        
                                            <p><p>Build alternative plain text message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_word_wrap">word_wrap</a>($str, $charlim = NULL)
        
                                            <p><p>Word Wrap</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__build_headers">_build_headers</a>()
        
                                            <p><p>Build final headers</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__write_headers">_write_headers</a>()
        
                                            <p><p>Write Headers as a string</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__build_message">_build_message</a>()
        
                                            <p><p>Build Final Body and attachments</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method__attachments_have_multipart">_attachments_have_multipart</a>($type)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__append_attachments">_append_attachments</a>(string $body, string $boundary, string $multipart = null)
        
                                            <p><p>Prepares attachment string</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__prep_quoted_printable">_prep_quoted_printable</a>($str)
        
                                            <p><p>Prep Quoted Printable</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__prep_q_encoding">_prep_q_encoding</a>($str)
        
                                            <p><p>Prep Q Encoding</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_send">send</a>(bool $auto_clear = TRUE)
        
                                            <p><p>Send Email</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_batch_bcc_send">batch_bcc_send</a>()
        
                                            <p><p>Batch Bcc Send. Sends groups of BCCs in batches</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__unwrap_specials">_unwrap_specials</a>()
        
                                            <p><p>Unwrap special elements</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__remove_nl_callback">_remove_nl_callback</a>(string $matches)
        
                                            <p><p>Strip line-breaks via callback</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__spool_email">_spool_email</a>()
        
                                            <p><p>Spool mail to the mail server</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__validate_email_for_shell">_validate_email_for_shell</a>(string $email)
        
                                            <p><p>Validate email for shell</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__send_with_mail">_send_with_mail</a>()
        
                                            <p><p>Send using mail()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__send_with_sendmail">_send_with_sendmail</a>()
        
                                            <p><p>Send using Sendmail</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__send_with_smtp">_send_with_smtp</a>()
        
                                            <p><p>Send using SMTP</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__smtp_end">_smtp_end</a>()
        
                                            <p><p>SMTP End</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__smtp_connect">_smtp_connect</a>()
        
                                            <p><p>SMTP Connect</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__send_command">_send_command</a>($cmd, $data = &#039;&#039;)
        
                                            <p><p>Send SMTP command</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__smtp_authenticate">_smtp_authenticate</a>()
        
                                            <p><p>SMTP Authenticate</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__send_data">_send_data</a>(string $data)
        
                                            <p><p>Send SMTP data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_smtp_data">_get_smtp_data</a>()
        
                                            <p><p>Get SMTP data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_hostname">_get_hostname</a>()
        
                                            <p><p>Get Hostname</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_print_debugger">print_debugger</a>(array $include = array(&#039;headers&#039;, &#039;subject&#039;, &#039;body&#039;))
        
                                            <p><p>Get Debug Message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__set_error_message">_set_error_message</a>(string $msg, string $val = &#039;&#039;)
        
                                            <p><p>Set Message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__mime_types">_mime_types</a>($ext = &#039;&#039;)
        
                                            <p><p>Mime Types</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___destruct">__destruct</a>()
        
                                            <p>Destructor</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;int
                </div>
                <div class="col-md-8">
                    <a href="#method_strlen">strlen</a>(string $str)
        
                                            <p><p>Byte-safe strlen()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;string
                </div>
                <div class="col-md-8">
                    <a href="#method_substr">substr</a>(string $str, int $start, int $length = NULL)
        
                                            <p><p>Byte-safe substr()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 394</div>
        <code>                    void
    <strong>__construct</strong>(array $config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Constructor - Sets Email Preferences</p></p>                <p><p>The constructor can be passed an array of config values</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td><p>= array()</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initialize">
        <div class="location">at line 413</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>initialize</strong>(array $config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize preferences</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_clear">
        <div class="location">at line 448</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>clear</strong>($clear_attachments = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize the Email Data</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$clear_attachments</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_from">
        <div class="location">at line 481</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>from</strong>(string $from, string $name = &#039;&#039;, string $return_path = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set FROM</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$from</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$name</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$return_path</td>
                <td><p>= NULL  Return-Path</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_reply_to">
        <div class="location">at line 529</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>reply_to</strong>($replyto, $name = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Reply-to</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$replyto</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_to">
        <div class="location">at line 569</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>to</strong>($to)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Recipients</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$to</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_cc">
        <div class="location">at line 597</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>cc</strong>($cc)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set CC</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$cc</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_bcc">
        <div class="location">at line 625</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>bcc</strong>($bcc, $limit = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set BCC</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$bcc</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$limit</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_subject">
        <div class="location">at line 660</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>subject</strong>($subject)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Email Subject</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$subject</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_message">
        <div class="location">at line 675</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>message</strong>($body)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Body</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$body</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_attach">
        <div class="location">at line 704</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>attach</strong>(string $file, string $disposition = &#039;&#039;, string $newname = NULL, string $mime = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Assign file attachments</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$file</td>
                <td><p>Can be local path, URL or buffered content</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$disposition</td>
                <td><p>= 'attachment'</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$newname</td>
                <td><p>= NULL</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$mime</td>
                <td><p>= ''</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_attachment_cid">
        <div class="location">at line 750</div>
        <code>                    string
    <strong>attachment_cid</strong>(string $filename)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set and return attachment Content-ID</p></p>                <p><p>Useful for attached inline pictures</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$filename</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_header">
        <div class="location">at line 774</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>set_header</strong>($header, $value)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add a Header Item</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$header</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$value</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__str_to_array">
        <div class="location">at line 788</div>
        <code>            protected        array
    <strong>_str_to_array</strong>($email)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Convert a String to an Array</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$email</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_alt_message">
        <div class="location">at line 808</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>set_alt_message</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Multipart Value</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_mailtype">
        <div class="location">at line 822</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>set_mailtype</strong>($type = &#039;text&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Mailtype</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_wordwrap">
        <div class="location">at line 836</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>set_wordwrap</strong>($wordwrap = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Wordwrap</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$wordwrap</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_protocol">
        <div class="location">at line 850</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>set_protocol</strong>($protocol = &#039;mail&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Protocol</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$protocol</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_priority">
        <div class="location">at line 864</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>set_priority</strong>($n = 3)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Priority</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$n</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_newline">
        <div class="location">at line 878</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>set_newline</strong>($newline = &quot;\n&quot;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Newline Character</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$newline</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_crlf">
        <div class="location">at line 892</div>
        <code>                    <a href="CI_Email.html">CI_Email</a>
    <strong>set_crlf</strong>($crlf = &quot;\n&quot;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set CRLF</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$crlf</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Email.html">CI_Email</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_message_id">
        <div class="location">at line 905</div>
        <code>            protected        string
    <strong>_get_message_id</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the Message ID</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_protocol">
        <div class="location">at line 918</div>
        <code>            protected        mixed
    <strong>_get_protocol</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Mail Protocol</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_encoding">
        <div class="location">at line 932</div>
        <code>            protected        string
    <strong>_get_encoding</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Mail Encoding</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_content_type">
        <div class="location">at line 954</div>
        <code>            protected        string
    <strong>_get_content_type</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get content type (text/html/attachment)</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_date">
        <div class="location">at line 975</div>
        <code>            protected        string
    <strong>_set_date</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set RFC 822 Date</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_mime_message">
        <div class="location">at line 992</div>
        <code>            protected        string
    <strong>_get_mime_message</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Mime message</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_validate_email">
        <div class="location">at line 1005</div>
        <code>                    bool
    <strong>validate_email</strong>($email)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validate Email Address</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$email</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_valid_email">
        <div class="location">at line 1033</div>
        <code>                    bool
    <strong>valid_email</strong>($email)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Email Validation</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$email</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_clean_email">
        <div class="location">at line 1059</div>
        <code>                    string
    <strong>clean_email</strong>($email)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Clean Extended Email Address: Joe Smith <a href="mailto:<EMAIL>"><EMAIL></a></p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$email</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_alt_message">
        <div class="location">at line 1088</div>
        <code>            protected        string
    <strong>_get_alt_message</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Build alternative plain text message</p></p>                <p><p>Provides the raw message for use in plain-text headers of
HTML-formatted emails.
If the user hasn't specified his own alternative message
it creates one by stripping the HTML</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_word_wrap">
        <div class="location">at line 1122</div>
        <code>                    string
    <strong>word_wrap</strong>($str, $charlim = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Word Wrap</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$charlim</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__build_headers">
        <div class="location">at line 1212</div>
        <code>            protected        void
    <strong>_build_headers</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Build final headers</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__write_headers">
        <div class="location">at line 1229</div>
        <code>            protected        void
    <strong>_write_headers</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Write Headers as a string</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__build_message">
        <div class="location">at line 1266</div>
        <code>            protected        bool
    <strong>_build_message</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Build Final Body and attachments</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__attachments_have_multipart">
        <div class="location">at line 1437</div>
        <code>            protected        
    <strong>_attachments_have_multipart</strong>($type)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$type</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__append_attachments">
        <div class="location">at line 1460</div>
        <code>            protected        string
    <strong>_append_attachments</strong>(string $body, string $boundary, string $multipart = null)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prepares attachment string</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$body</td>
                <td><p>Message body to append to</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$boundary</td>
                <td><p>Multipart boundary</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$multipart</td>
                <td><p>When provided, only attachments of this type will be processed</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__prep_quoted_printable">
        <div class="location">at line 1498</div>
        <code>            protected        string
    <strong>_prep_quoted_printable</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prep Quoted Printable</p></p>                <p><p>Prepares string for Quoted-Printable Content-Transfer-Encoding
Refer to RFC 2045 <a href="http://www.ietf.org/rfc/rfc2045.txt">http://www.ietf.org/rfc/rfc2045.txt</a></p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__prep_q_encoding">
        <div class="location">at line 1606</div>
        <code>            protected        string
    <strong>_prep_q_encoding</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prep Q Encoding</p></p>                <p><p>Performs &quot;Q Encoding&quot; on a string for use in email headers.
It's related but not identical to quoted-printable, so it has its
own method.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send">
        <div class="location">at line 1681</div>
        <code>                    bool
    <strong>send</strong>(bool $auto_clear = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send Email</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$auto_clear</td>
                <td><p>= TRUE</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_batch_bcc_send">
        <div class="location">at line 1738</div>
        <code>                    void
    <strong>batch_bcc_send</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Batch Bcc Send. Sends groups of BCCs in batches</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__unwrap_specials">
        <div class="location">at line 1795</div>
        <code>            protected        void
    <strong>_unwrap_specials</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Unwrap special elements</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__remove_nl_callback">
        <div class="location">at line 1808</div>
        <code>            protected        string
    <strong>_remove_nl_callback</strong>(string $matches)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Strip line-breaks via callback</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$matches</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__spool_email">
        <div class="location">at line 1825</div>
        <code>            protected        bool
    <strong>_spool_email</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Spool mail to the mail server</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__validate_email_for_shell">
        <div class="location">at line 1858</div>
        <code>            protected        bool
    <strong>_validate_email_for_shell</strong>(string $email)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validate email for shell</p></p>                <p><p>Applies stricter, shell-safe validation to email addresses.
Introduced to prevent RCE via sendmail's -f option.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$email</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
                            <h4>See also</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>
                                            <a href="https://github.com/bcit-ci/CodeIgniter/issues/4963">https://github.com/bcit-ci/CodeIgniter/issues/4963</a>
                                    </td>
                <td></td>
            </tr>
                    <tr>
                <td>
                                            <a href="https://gist.github.com/Zenexer/40d02da5e07f151adeaeeaa11af9ab36">https://gist.github.com/Zenexer/40d02da5e07f151adeaeeaa11af9ab36</a>
                                    </td>
                <td></td>
            </tr>
            </table>

            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__send_with_mail">
        <div class="location">at line 1883</div>
        <code>            protected        bool
    <strong>_send_with_mail</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send using mail()</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__send_with_sendmail">
        <div class="location">at line 1913</div>
        <code>            protected        bool
    <strong>_send_with_sendmail</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send using Sendmail</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__send_with_smtp">
        <div class="location">at line 1956</div>
        <code>            protected        bool
    <strong>_send_with_smtp</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send using SMTP</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__smtp_end">
        <div class="location">at line 2042</div>
        <code>            protected        void
    <strong>_smtp_end</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>SMTP End</p></p>                <p><p>Shortcut to send RSET or QUIT depending on keep-alive</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__smtp_connect">
        <div class="location">at line 2056</div>
        <code>            protected        string
    <strong>_smtp_connect</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>SMTP Connect</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__send_command">
        <div class="location">at line 2118</div>
        <code>            protected        bool
    <strong>_send_command</strong>($cmd, $data = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send SMTP command</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$cmd</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__smtp_authenticate">
        <div class="location">at line 2200</div>
        <code>            protected        bool
    <strong>_smtp_authenticate</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>SMTP Authenticate</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__send_data">
        <div class="location">at line 2263</div>
        <code>            protected        bool
    <strong>_send_data</strong>(string $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send SMTP data</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_smtp_data">
        <div class="location">at line 2308</div>
        <code>            protected        string
    <strong>_get_smtp_data</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get SMTP data</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_hostname">
        <div class="location">at line 2338</div>
        <code>            protected        string
    <strong>_get_hostname</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Hostname</p></p>                <p><p>There are only two legal types of hostname - either a fully
qualified domain name (eg: &quot;mail.example.com&quot;) or an IP literal
(eg: &quot;[*******]&quot;).</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_print_debugger">
        <div class="location">at line 2357</div>
        <code>                    string
    <strong>print_debugger</strong>(array $include = array(&#039;headers&#039;, &#039;subject&#039;, &#039;body&#039;))
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Debug Message</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$include</td>
                <td><p>List of raw data chunks to include in the output
Valid options are: 'headers', 'subject', 'body'</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_error_message">
        <div class="location">at line 2400</div>
        <code>            protected        void
    <strong>_set_error_message</strong>(string $msg, string $val = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Message</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$msg</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$val</td>
                <td><p>= ''</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__mime_types">
        <div class="location">at line 2423</div>
        <code>            protected        string
    <strong>_mime_types</strong>($ext = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Mime Types</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$ext</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___destruct">
        <div class="location">at line 2446</div>
        <code>                    void
    <strong>__destruct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Destructor</p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_strlen">
        <div class="location">at line 2459</div>
        <code>        static    protected        int
    <strong>strlen</strong>(string $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe strlen()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_substr">
        <div class="location">at line 2476</div>
        <code>        static    protected        string
    <strong>substr</strong>(string $str, int $start, int $length = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe substr()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$start</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$length</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

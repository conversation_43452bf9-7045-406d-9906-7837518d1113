<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>app\services\imap\Imap | API</title>

            <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="../../../css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="../../../css/doctum.css">
        <link rel="stylesheet" type="text/css" href="../../../fonts/doctum-font.css">
        <script src="../../../js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="../../../doctum.js"></script>
        <script async defer src="../../../js/bootstrap.min.js"></script>
        <script async defer src="../../../js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:app_services_imap_Imap" data-root-path="../../../" data-search-index-url="../../../doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="../../../search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="../../../index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="../../../classes.html">Classes</a></li>
                    <li><a href="../../../namespaces.html">Namespaces</a></li>
                    <li><a href="../../../interfaces.html">Interfaces</a></li>
                    <li><a href="../../../traits.html">Traits</a></li>
                    <li><a href="../../../doc-index.html">Index</a></li>
                    <li><a href="../../../search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                        <div class="namespace-breadcrumbs">
            <ol class="breadcrumb">
                <li><span class="label label-default">class</span></li>
                        <li><a href="../../../app.html">app</a></li><li class="backslash">\</li><li><a href="../../../app/services.html">services</a></li><li class="backslash">\</li><li><a href="../../../app/services/imap.html">imap</a></li><li class="backslash">\</li><li>Imap</li>
            </ol>
        </div>
                <div id="page-content">
    <div class="page-header">
        <h1>Imap    
            </h1>
    </div>

    
    <p>        class
    <strong>Imap</strong>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_host">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$host</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_port">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$port</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_encryption">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$encryption</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_validateCertificate">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$validateCertificate</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_username">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$username</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_connection">
                                        protected                                        <abbr title="Ddeboer\Imap\Connection">Connection</abbr>
                                                                                
                                    </td>
                <td>$connection</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>($username, $password, $host, $encryption, $port = &#039;&#039;, $validateCertificate = false)
        
                                            <p><p>Create new IMAP instance</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getSelectableFolders">getSelectableFolders</a>()
        
                                            <p><p>Get the selectable folder names</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <abbr title="Ddeboer\Imap\Connection">Connection</abbr>
                </div>
                <div class="col-md-8">
                    <a href="#method_testConnection">testConnection</a>()
        
                                            <p><p>Test the IMAP connection</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <abbr title="Ddeboer\Imap\Connection">Connection</abbr>
                </div>
                <div class="col-md-8">
                    <a href="#method_createConnection">createConnection</a>()
        
                                            <p><p>Create IMAP connection</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_getConnectionFlags">getConnectionFlags</a>()
        
                                            <p><p>Get full address of mailbox.</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 43</div>
        <code>                    
    <strong>__construct</strong>($username, $password, $host, $encryption, $port = &#039;&#039;, $validateCertificate = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create new IMAP instance</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$username</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$password</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$host</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$encryption</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$port</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$validateCertificate</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getSelectableFolders">
        <div class="location">at line 58</div>
        <code>                    array
    <strong>getSelectableFolders</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the selectable folder names</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_testConnection">
        <div class="location">at line 79</div>
        <code>                    <abbr title="Ddeboer\Imap\Connection">Connection</abbr>
    <strong>testConnection</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Test the IMAP connection</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><abbr title="Ddeboer\Imap\Connection">Connection</abbr></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_createConnection">
        <div class="location">at line 93</div>
        <code>                    <abbr title="Ddeboer\Imap\Connection">Connection</abbr>
    <strong>createConnection</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create IMAP connection</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><abbr title="Ddeboer\Imap\Connection">Connection</abbr></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getConnectionFlags">
        <div class="location">at line 113</div>
        <code>            protected        string
    <strong>getConnectionFlags</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get full address of mailbox.</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

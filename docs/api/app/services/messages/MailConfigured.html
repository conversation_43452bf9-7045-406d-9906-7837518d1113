<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>app\services\messages\MailConfigured | API</title>

            <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="../../../css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="../../../css/doctum.css">
        <link rel="stylesheet" type="text/css" href="../../../fonts/doctum-font.css">
        <script src="../../../js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="../../../doctum.js"></script>
        <script async defer src="../../../js/bootstrap.min.js"></script>
        <script async defer src="../../../js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:app_services_messages_MailConfigured" data-root-path="../../../" data-search-index-url="../../../doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="../../../search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="../../../index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="../../../classes.html">Classes</a></li>
                    <li><a href="../../../namespaces.html">Namespaces</a></li>
                    <li><a href="../../../interfaces.html">Interfaces</a></li>
                    <li><a href="../../../traits.html">Traits</a></li>
                    <li><a href="../../../doc-index.html">Index</a></li>
                    <li><a href="../../../search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                        <div class="namespace-breadcrumbs">
            <ol class="breadcrumb">
                <li><span class="label label-default">class</span></li>
                        <li><a href="../../../app.html">app</a></li><li class="backslash">\</li><li><a href="../../../app/services.html">services</a></li><li class="backslash">\</li><li><a href="../../../app/services/messages.html">messages</a></li><li class="backslash">\</li><li>MailConfigured</li>
            </ol>
        </div>
                <div id="page-content">
    <div class="page-header">
        <h1>MailConfigured    
            </h1>
    </div>

    
    <p>        class
    <strong>MailConfigured</strong>        extends <a href="../../../app/services/messages/AbstractPopupMessage.html"><abbr title="app\services\messages\AbstractPopupMessage">AbstractPopupMessage</abbr></a>
</p>

        
    
        

            
        
    
    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_isVisible">isVisible</a>(...$params)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getMessage">getMessage</a>(...$params)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_fqcn">fqcn</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/messages/AbstractPopupMessage.html#method_fqcn">
<abbr title="app\services\messages\AbstractPopupMessage">AbstractPopupMessage</abbr></a></small></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method_isVisible">
        <div class="location">at line 11</div>
        <code>                    
    <strong>isVisible</strong>(...$params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>...$params</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getMessage">
        <div class="location">at line 22</div>
        <code>                    
    <strong>getMessage</strong>(...$params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>...$params</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_fqcn">
        <div class="location">in <a href="../../../app/services/messages/AbstractPopupMessage.html#method_fqcn">
<abbr title="app\services\messages\AbstractPopupMessage">AbstractPopupMessage</abbr></a> at line 13</div>
        <code>        static            
    <strong>fqcn</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

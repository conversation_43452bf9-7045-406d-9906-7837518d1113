<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>app\services\estimates\EstimatesPipeline | API</title>

            <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="../../../css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="../../../css/doctum.css">
        <link rel="stylesheet" type="text/css" href="../../../fonts/doctum-font.css">
        <script src="../../../js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="../../../doctum.js"></script>
        <script async defer src="../../../js/bootstrap.min.js"></script>
        <script async defer src="../../../js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:app_services_estimates_EstimatesPipeline" data-root-path="../../../" data-search-index-url="../../../doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="../../../search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="../../../index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="../../../classes.html">Classes</a></li>
                    <li><a href="../../../namespaces.html">Namespaces</a></li>
                    <li><a href="../../../interfaces.html">Interfaces</a></li>
                    <li><a href="../../../traits.html">Traits</a></li>
                    <li><a href="../../../doc-index.html">Index</a></li>
                    <li><a href="../../../search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                        <div class="namespace-breadcrumbs">
            <ol class="breadcrumb">
                <li><span class="label label-default">class</span></li>
                        <li><a href="../../../app.html">app</a></li><li class="backslash">\</li><li><a href="../../../app/services.html">services</a></li><li class="backslash">\</li><li><a href="../../../app/services/estimates.html">estimates</a></li><li class="backslash">\</li><li>EstimatesPipeline</li>
            </ol>
        </div>
                <div id="page-content">
    <div class="page-header">
        <h1>EstimatesPipeline    
            </h1>
    </div>

    
    <p>        class
    <strong>EstimatesPipeline</strong>        extends <a href="../../../app/services/AbstractKanban.html"><abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_limit">
                                        protected                                        
                                                                                
                                    </td>
                <td>$limit</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#property_limit">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_default_sort">
                                        protected                                        
                                                                                
                                    </td>
                <td>$default_sort</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#property_default_sort">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_default_sort_direction">
                                        protected                                        
                                                                                
                                    </td>
                <td>$default_sort_direction</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#property_default_sort_direction">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_status">
                                        protected                                        
                                                                                
                                    </td>
                <td>$status</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#property_status">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_page">
                                        protected                                        
                                                                                
                                    </td>
                <td>$page</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#property_page">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_refreshAtTotal">
                                        protected                                        
                                                                                
                                    </td>
                <td>$refreshAtTotal</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#property_refreshAtTotal">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#property_ci">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_q">
                                        protected                                        
                                                                                
                                    </td>
                <td>$q</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#property_q">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_queryTapCallback">
                                        protected                                        
                                                                                
                                    </td>
                <td>$queryTapCallback</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#property_queryTapCallback">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>($status)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method___construct">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_tapQuery">tapQuery</a>(callable $callback)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_tapQuery">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_totalPages">totalPages</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_totalPages">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get">get</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_get">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_countAll">countAll</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_countAll">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_refresh">refresh</a>($atTotal)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_refresh">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_page">page</a>($page)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_page">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getPage">getPage</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_getPage">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_sortBy">sortBy</a>($column, $direction)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_sortBy">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_search">search</a>($q)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_search">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_applySortQuery">applySortQuery</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_applySortQuery">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_tapQueryIfNeeded">tapQueryIfNeeded</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_tapQueryIfNeeded">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_qualifyColumn">qualifyColumn</a>($column)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_qualifyColumn">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_updateOrder">updateOrder</a>($data, $column, $table, $status, $statusColumnName = &#039;status&#039;, $primaryKey = &#039;id&#039;)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/AbstractKanban.html#method_updateOrder">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_table">table</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_initiateQuery">initiateQuery</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_applySearchQuery">applySearchQuery</a>($q)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_defaultSortDirection">defaultSortDirection</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_defaultSortColumn">defaultSortColumn</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_limit">limit</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method___construct">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 25</div>
        <code>                    
    <strong>__construct</strong>($status)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$status</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_tapQuery">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_tapQuery">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 34</div>
        <code>                    
    <strong>tapQuery</strong>(callable $callback)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>callable</td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_totalPages">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_totalPages">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 41</div>
        <code>                    
    <strong>totalPages</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_get">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 48</div>
        <code>                    
    <strong>get</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_countAll">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_countAll">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 81</div>
        <code>                    
    <strong>countAll</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_refresh">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_refresh">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 94</div>
        <code>                    
    <strong>refresh</strong>($atTotal)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$atTotal</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_page">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_page">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 101</div>
        <code>                    
    <strong>page</strong>($page)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$page</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getPage">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_getPage">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 108</div>
        <code>                    
    <strong>getPage</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_sortBy">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_sortBy">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 113</div>
        <code>                    
    <strong>sortBy</strong>($column, $direction)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$column</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$direction</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_search">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_search">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 123</div>
        <code>                    
    <strong>search</strong>($q)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$q</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_applySortQuery">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_applySortQuery">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 130</div>
        <code>            protected        
    <strong>applySortQuery</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_tapQueryIfNeeded">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_tapQueryIfNeeded">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 144</div>
        <code>            protected        
    <strong>tapQueryIfNeeded</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_qualifyColumn">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_qualifyColumn">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 151</div>
        <code>            protected        
    <strong>qualifyColumn</strong>($column)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$column</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_updateOrder">
        <div class="location">in <a href="../../../app/services/AbstractKanban.html#method_updateOrder">
<abbr title="app\services\AbstractKanban">AbstractKanban</abbr></a> at line 156</div>
        <code>        static            
    <strong>updateOrder</strong>($data, $column, $table, $status, $statusColumnName = &#039;status&#039;, $primaryKey = &#039;id&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$column</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$table</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$status</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$statusColumnName</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$primaryKey</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_table">
        <div class="location">at line 9</div>
        <code>            protected        
    <strong>table</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initiateQuery">
        <div class="location">at line 65</div>
        <code>            protected        
    <strong>initiateQuery</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_applySearchQuery">
        <div class="location">at line 29</div>
        <code>            protected        
    <strong>applySearchQuery</strong>($q)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$q</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_defaultSortDirection">
        <div class="location">at line 14</div>
        <code>                    
    <strong>defaultSortDirection</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_defaultSortColumn">
        <div class="location">at line 19</div>
        <code>                    
    <strong>defaultSortColumn</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_limit">
        <div class="location">at line 24</div>
        <code>                    
    <strong>limit</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

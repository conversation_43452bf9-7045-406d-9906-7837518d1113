<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>app\services\upgrade\GuzzleCoreUpgradeAdapter | API</title>

            <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="../../../css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="../../../css/doctum.css">
        <link rel="stylesheet" type="text/css" href="../../../fonts/doctum-font.css">
        <script src="../../../js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="../../../doctum.js"></script>
        <script async defer src="../../../js/bootstrap.min.js"></script>
        <script async defer src="../../../js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:app_services_upgrade_GuzzleCoreUpgradeAdapter" data-root-path="../../../" data-search-index-url="../../../doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="../../../search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="../../../index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="../../../classes.html">Classes</a></li>
                    <li><a href="../../../namespaces.html">Namespaces</a></li>
                    <li><a href="../../../interfaces.html">Interfaces</a></li>
                    <li><a href="../../../traits.html">Traits</a></li>
                    <li><a href="../../../doc-index.html">Index</a></li>
                    <li><a href="../../../search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                        <div class="namespace-breadcrumbs">
            <ol class="breadcrumb">
                <li><span class="label label-default">class</span></li>
                        <li><a href="../../../app.html">app</a></li><li class="backslash">\</li><li><a href="../../../app/services.html">services</a></li><li class="backslash">\</li><li><a href="../../../app/services/upgrade.html">upgrade</a></li><li class="backslash">\</li><li>GuzzleCoreUpgradeAdapter</li>
            </ol>
        </div>
                <div id="page-content">
    <div class="page-header">
        <h1>GuzzleCoreUpgradeAdapter    
            </h1>
    </div>

    
    <p>        class
    <strong>GuzzleCoreUpgradeAdapter</strong>        extends <a href="../../../app/services/upgrade/AbstractCurlAdapter.html"><abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a>
</p>

        
    
        

            
                <h2>Traits</h2>

        
    <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-6">
                    <a href="../../../app/services/upgrade/Response.html"><abbr title="app\services\upgrade\Response">Response</abbr></a>    </div>
                <div class="col-md-6"></div>
            </div>
                    <div class="row">
                <div class="col-md-6">
                    <a href="../../../app/services/upgrade/Response.html"><abbr title="app\services\upgrade\Response">Response</abbr></a>    </div>
                <div class="col-md-6"></div>
            </div>
            </div>
    
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_config">
                                        protected                                        
                                                                                
                                    </td>
                <td>$config</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="../../../app/services/upgrade/AbstractCurlAdapter.html#property_config">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_failedExtractException">failedExtractException</a>($zipFile, $upgradeCopyLocation)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/upgrade/Response.html#method_failedExtractException">
<abbr title="app\services\upgrade\Response">Response</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getErrorByStatusCode">getErrorByStatusCode</a>($statusCode)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/upgrade/Response.html#method_getErrorByStatusCode">
<abbr title="app\services\upgrade\Response">Response</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getFileOwnersMessage">getFileOwnersMessage</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/upgrade/Response.html#method_getFileOwnersMessage">
<abbr title="app\services\upgrade\Response">Response</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_setConfig">setConfig</a>(<a href="../../../app/services/upgrade/Config.html"><abbr title="app\services\upgrade\Config">Config</abbr></a> $config)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_setConfig">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getConfig">getConfig</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_getConfig">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_extract">extract</a>($zipFile)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_extract">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_maybeCreateUpgradeDirectory">maybeCreateUpgradeDirectory</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_maybeCreateUpgradeDirectory">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_copyUpgrade">copyUpgrade</a>($zipFile)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_copyUpgrade">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_cleanTmpFiles">cleanTmpFiles</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_cleanTmpFiles">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___destruct">__destruct</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method___destruct">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_perform">perform</a>($zipFile)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method_failedExtractException">
        <div class="location">in <a href="../../../app/services/upgrade/Response.html#method_failedExtractException">
<abbr title="app\services\upgrade\Response">Response</abbr></a> at line 9</div>
        <code>            protected        
    <strong>failedExtractException</strong>($zipFile, $upgradeCopyLocation)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$zipFile</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$upgradeCopyLocation</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getErrorByStatusCode">
        <div class="location">in <a href="../../../app/services/upgrade/Response.html#method_getErrorByStatusCode">
<abbr title="app\services\upgrade\Response">Response</abbr></a> at line 23</div>
        <code>            protected        
    <strong>getErrorByStatusCode</strong>($statusCode)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$statusCode</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getFileOwnersMessage">
        <div class="location">in <a href="../../../app/services/upgrade/Response.html#method_getFileOwnersMessage">
<abbr title="app\services\upgrade\Response">Response</abbr></a> at line 41</div>
        <code>            protected        
    <strong>getFileOwnersMessage</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_setConfig">
        <div class="location">in <a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_setConfig">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a> at line 18</div>
        <code>                    
    <strong>setConfig</strong>(<a href="../../../app/services/upgrade/Config.html"><abbr title="app\services\upgrade\Config">Config</abbr></a> $config)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><a href="../../../app/services/upgrade/Config.html"><abbr title="app\services\upgrade\Config">Config</abbr></a></td>
                <td>$config</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getConfig">
        <div class="location">in <a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_getConfig">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a> at line 26</div>
        <code>                    
    <strong>getConfig</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_extract">
        <div class="location">in <a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_extract">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a> at line 31</div>
        <code>                    
    <strong>extract</strong>($zipFile)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$zipFile</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_maybeCreateUpgradeDirectory">
        <div class="location">in <a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_maybeCreateUpgradeDirectory">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a> at line 47</div>
        <code>            protected        
    <strong>maybeCreateUpgradeDirectory</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_copyUpgrade">
        <div class="location">in <a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_copyUpgrade">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a> at line 58</div>
        <code>            protected        
    <strong>copyUpgrade</strong>($zipFile)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$zipFile</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_cleanTmpFiles">
        <div class="location">in <a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method_cleanTmpFiles">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a> at line 83</div>
        <code>            protected        
    <strong>cleanTmpFiles</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___destruct">
        <div class="location">in <a href="../../../app/services/upgrade/AbstractCurlAdapter.html#method___destruct">
<abbr title="app\services\upgrade\AbstractCurlAdapter">AbstractCurlAdapter</abbr></a> at line 93</div>
        <code>                    
    <strong>__destruct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_perform">
        <div class="location">at line 15</div>
        <code>                    
    <strong>perform</strong>($zipFile)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$zipFile</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

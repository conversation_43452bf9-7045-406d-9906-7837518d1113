<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>app\services\MergeTickets | API</title>

            <link rel="stylesheet" type="text/css" href="../../css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="../../css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="../../css/doctum.css">
        <link rel="stylesheet" type="text/css" href="../../fonts/doctum-font.css">
        <script src="../../js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="../../doctum.js"></script>
        <script async defer src="../../js/bootstrap.min.js"></script>
        <script async defer src="../../js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:app_services_MergeTickets" data-root-path="../../" data-search-index-url="../../doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="../../search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="../../index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="../../classes.html">Classes</a></li>
                    <li><a href="../../namespaces.html">Namespaces</a></li>
                    <li><a href="../../interfaces.html">Interfaces</a></li>
                    <li><a href="../../traits.html">Traits</a></li>
                    <li><a href="../../doc-index.html">Index</a></li>
                    <li><a href="../../search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                        <div class="namespace-breadcrumbs">
            <ol class="breadcrumb">
                <li><span class="label label-default">class</span></li>
                        <li><a href="../../app.html">app</a></li><li class="backslash">\</li><li><a href="../../app/services.html">services</a></li><li class="backslash">\</li><li>MergeTickets</li>
            </ol>
        </div>
                <div id="page-content">
    <div class="page-header">
        <h1>MergeTickets    
            </h1>
    </div>

    
    <p>        class
    <strong>MergeTickets</strong>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_primaryTicketId">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$primaryTicketId</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_ids">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$ids</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_status">
                                        protected                                        int|null
                                                                                
                                    </td>
                <td>$status</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"><p>CI Instance</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(int $primaryTicketId, array $ids)
        
                                            <p><p>Initiate new MergeTickets class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_merge">merge</a>()
        
                                            <p><p>Merge the tickets into the primary ticket</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    $this
                </div>
                <div class="col-md-8">
                    <a href="#method_markPrimaryTicketAs">markPrimaryTicketAs</a>(int $status)
        
                                            <p><p>After merge, change the primary ticket status to the given status</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_mergeInPrimaryTicket">mergeInPrimaryTicket</a>(array $reply)
        
                                            <p><p>Merge the given reply into the primary ticket</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getTicketsToMerge">getTicketsToMerge</a>()
        
                                            <p><p>Get the tickets to be merged into the primary ticket</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getAttachments">getAttachments</a>(int $id, int|null $replyId = null)
        
                                            <p><p>Get attachments for the merge</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_removeAlreadyMergedTickets">removeAlreadyMergedTickets</a>(array $tickets)
        
                                            <p><p>Remove the already merged tickets from the given tickets list</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_markTicketAsMerged">markTicketAsMerged</a>(array $ticket)
        
                                            <p><p>Mark the ticket as merged</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getReplies">getReplies</a>(int $id)
        
                                            <p><p>Get the replies for merging for the given ticket</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_convertToMergeReplies">convertToMergeReplies</a>(array $tickets)
        
                                            <p><p>Convert the given tickets with replies to replies for ready for merging</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_moveAttachments">moveAttachments</a>($attachments, int $replyId)
        
                                            <p><p>Move the given attachment from merged ticket/reply to the new reply</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 33</div>
        <code>                    
    <strong>__construct</strong>(int $primaryTicketId, array $ids)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initiate new MergeTickets class</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$primaryTicketId</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$ids</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_merge">
        <div class="location">at line 45</div>
        <code>                    bool
    <strong>merge</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Merge the tickets into the primary ticket</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_markPrimaryTicketAs">
        <div class="location">at line 86</div>
        <code>                    $this
    <strong>markPrimaryTicketAs</strong>(int $status)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>After merge, change the primary ticket status to the given status</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$status</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>$this</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mergeInPrimaryTicket">
        <div class="location">at line 100</div>
        <code>            protected        bool
    <strong>mergeInPrimaryTicket</strong>(array $reply)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Merge the given reply into the primary ticket</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$reply</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getTicketsToMerge">
        <div class="location">at line 127</div>
        <code>            protected        array
    <strong>getTicketsToMerge</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the tickets to be merged into the primary ticket</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getAttachments">
        <div class="location">at line 151</div>
        <code>            protected        array
    <strong>getAttachments</strong>(int $id, int|null $replyId = null)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get attachments for the merge</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$id</td>
                <td></td>
            </tr>
                    <tr>
                <td>int|null</td>
                <td>$replyId</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_removeAlreadyMergedTickets">
        <div class="location">at line 163</div>
        <code>            protected        array
    <strong>removeAlreadyMergedTickets</strong>(array $tickets)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Remove the already merged tickets from the given tickets list</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$tickets</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_markTicketAsMerged">
        <div class="location">at line 179</div>
        <code>            protected        void
    <strong>markTicketAsMerged</strong>(array $ticket)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Mark the ticket as merged</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$ticket</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getReplies">
        <div class="location">at line 199</div>
        <code>            protected        array
    <strong>getReplies</strong>(int $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the replies for merging for the given ticket</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_convertToMergeReplies">
        <div class="location">at line 221</div>
        <code>            protected        array
    <strong>convertToMergeReplies</strong>(array $tickets)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Convert the given tickets with replies to replies for ready for merging</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$tickets</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_moveAttachments">
        <div class="location">at line 242</div>
        <code>            protected        void
    <strong>moveAttachments</strong>($attachments, int $replyId)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Move the given attachment from merged ticket/reply to the new reply</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$attachments</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$replyId</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

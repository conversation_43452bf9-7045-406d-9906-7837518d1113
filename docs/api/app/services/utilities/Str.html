<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>app\services\utilities\Str | API</title>

            <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="../../../css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="../../../css/doctum.css">
        <link rel="stylesheet" type="text/css" href="../../../fonts/doctum-font.css">
        <script src="../../../js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="../../../doctum.js"></script>
        <script async defer src="../../../js/bootstrap.min.js"></script>
        <script async defer src="../../../js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:app_services_utilities_Str" data-root-path="../../../" data-search-index-url="../../../doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="../../../search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="../../../index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="../../../classes.html">Classes</a></li>
                    <li><a href="../../../namespaces.html">Namespaces</a></li>
                    <li><a href="../../../interfaces.html">Interfaces</a></li>
                    <li><a href="../../../traits.html">Traits</a></li>
                    <li><a href="../../../doc-index.html">Index</a></li>
                    <li><a href="../../../search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                        <div class="namespace-breadcrumbs">
            <ol class="breadcrumb">
                <li><span class="label label-default">class</span></li>
                        <li><a href="../../../app.html">app</a></li><li class="backslash">\</li><li><a href="../../../app/services.html">services</a></li><li class="backslash">\</li><li><a href="../../../app/services/utilities.html">utilities</a></li><li class="backslash">\</li><li>Str</li>
            </ol>
        </div>
                <div id="page-content">
    <div class="page-header">
        <h1>Str    
            </h1>
    </div>

    
    <p>        class
    <strong>Str</strong>
</p>

        
    
        

            
                <h2>Traits</h2>

        
    <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-6">
                    <a href="../../../app/services/utilities/StrClickable.html"><abbr title="app\services\utilities\StrClickable">StrClickable</abbr></a>    </div>
                <div class="col-md-6"></div>
            </div>
            </div>
    
    
    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_make_url_clickable_cb">make_url_clickable_cb</a>($matches)
        
                                            <p><p>Callback for clickable</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/utilities/StrClickable.html#method_make_url_clickable_cb">
<abbr title="app\services\utilities\StrClickable">StrClickable</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_make_web_ftp_clickable_cb">make_web_ftp_clickable_cb</a>($matches)
        
                                            <p><p>Callback for clickable</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/utilities/StrClickable.html#method_make_web_ftp_clickable_cb">
<abbr title="app\services\utilities\StrClickable">StrClickable</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_make_email_clickable_cb">make_email_clickable_cb</a>($matches)
        
                                            <p><p>Callback for clickable</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/utilities/StrClickable.html#method_make_email_clickable_cb">
<abbr title="app\services\utilities\StrClickable">StrClickable</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;string
                </div>
                <div class="col-md-8">
                    <a href="#method_clickable">clickable</a>(string $ret)
        
                                            <p><p>Check for links/emails/ftp in string to wrap in href</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="../../../app/services/utilities/StrClickable.html#method_clickable">
<abbr title="app\services\utilities\StrClickable">StrClickable</abbr></a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_startsWith">startsWith</a>($haystack, $needle)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_endsWith">endsWith</a>($haystack, $needle)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_isHtml">isHtml</a>($string)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_after">after</a>($string, $substring)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_before">before</a>($string, $substring)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_replaceLast">replaceLast</a>($search, $replace, $subject)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_between">between</a>($string, $start, $end)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_similarity">similarity</a>($str1, $str2)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_slug">slug</a>($str, $options = [])
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method_make_url_clickable_cb">
        <div class="location">in <a href="../../../app/services/utilities/StrClickable.html#method_make_url_clickable_cb">
<abbr title="app\services\utilities\StrClickable">StrClickable</abbr></a> at line 12</div>
        <code>        static    protected        
    <strong>make_url_clickable_cb</strong>($matches)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Callback for clickable</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$matches</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_make_web_ftp_clickable_cb">
        <div class="location">in <a href="../../../app/services/utilities/StrClickable.html#method_make_web_ftp_clickable_cb">
<abbr title="app\services\utilities\StrClickable">StrClickable</abbr></a> at line 36</div>
        <code>        static    protected        
    <strong>make_web_ftp_clickable_cb</strong>($matches)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Callback for clickable</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$matches</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_make_email_clickable_cb">
        <div class="location">in <a href="../../../app/services/utilities/StrClickable.html#method_make_email_clickable_cb">
<abbr title="app\services\utilities\StrClickable">StrClickable</abbr></a> at line 61</div>
        <code>        static    protected        
    <strong>make_email_clickable_cb</strong>($matches)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Callback for clickable</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$matches</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_clickable">
        <div class="location">in <a href="../../../app/services/utilities/StrClickable.html#method_clickable">
<abbr title="app\services\utilities\StrClickable">StrClickable</abbr></a> at line 73</div>
        <code>        static            string
    <strong>clickable</strong>(string $ret)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check for links/emails/ftp in string to wrap in href</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$ret</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>formatted string with href in any found</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_startsWith">
        <div class="location">at line 15</div>
        <code>        static            
    <strong>startsWith</strong>($haystack, $needle)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$haystack</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$needle</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_endsWith">
        <div class="location">at line 20</div>
        <code>        static            
    <strong>endsWith</strong>($haystack, $needle)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$haystack</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$needle</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_isHtml">
        <div class="location">at line 26</div>
        <code>        static            
    <strong>isHtml</strong>($string)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$string</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_after">
        <div class="location">at line 31</div>
        <code>        static            
    <strong>after</strong>($string, $substring)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$string</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$substring</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_before">
        <div class="location">at line 41</div>
        <code>        static            
    <strong>before</strong>($string, $substring)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$string</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$substring</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_replaceLast">
        <div class="location">at line 51</div>
        <code>        static            
    <strong>replaceLast</strong>($search, $replace, $subject)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$search</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$replace</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$subject</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_between">
        <div class="location">at line 61</div>
        <code>        static            
    <strong>between</strong>($string, $start, $end)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$string</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$start</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$end</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_similarity">
        <div class="location">at line 74</div>
        <code>        static            
    <strong>similarity</strong>($str1, $str2)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str1</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$str2</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_slug">
        <div class="location">at line 102</div>
        <code>        static            
    <strong>slug</strong>($str, $options = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$options</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

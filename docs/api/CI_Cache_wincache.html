<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Cache_wincache | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Cache_wincache" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Cache_wincache    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Cache_wincache</strong>        extends <a href="CI_Driver.html">CI_Driver</a>
</p>

        
    
        

            <div class="description">
            <p><p>CodeIgniter Wincache Caching Class</p></p>            <p><p>Read more about Wincache functions here:
<a href="http://www.php.net/manual/en/ref.wincache.php">http://www.php.net/manual/en/ref.wincache.php</a></p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property__parent">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$_parent</td>
                <td class="last"><p>Instance of the parent class</p></td>
                <td><small>from&nbsp;<a href="CI_Driver.html#property__parent">
CI_Driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__methods">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_methods</td>
                <td class="last"><p>List of methods in the parent class</p></td>
                <td><small>from&nbsp;<a href="CI_Driver.html#property__methods">
CI_Driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__properties">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_properties</td>
                <td class="last"><p>List of properties in the parent class</p></td>
                <td><small>from&nbsp;<a href="CI_Driver.html#property__properties">
CI_Driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__reflections">
                    static                    protected                                        array
                                                                                
                                    </td>
                <td>$_reflections</td>
                <td class="last"><p>Array of methods and properties for the parent class(es)</p></td>
                <td><small>from&nbsp;<a href="CI_Driver.html#property__reflections">
CI_Driver</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_decorate">decorate</a>($parent)
        
                                            <p>Decorate</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Driver.html#method_decorate">
CI_Driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method___call">__call</a>($method, $args = array())
        
                                            <p><p>__call magic method</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Driver.html#method___call">
CI_Driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method___get">__get</a>($var)
        
                                            <p><p>__get magic method</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Driver.html#method___get">
CI_Driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method___set">__set</a>($var, $val)
        
                                            <p><p>__set magic method</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Driver.html#method___set">
CI_Driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_get">get</a>(string $id)
        
                                            <p>Get</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_save">save</a>(string $id, mixed $data, int $ttl = 60, bool $raw = FALSE)
        
                                            <p><p>Cache Save</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_delete">delete</a>($id)
        
                                            <p><p>Delete from Cache</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_increment">increment</a>(string $id, int $offset = 1)
        
                                            <p><p>Increment a raw value</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_decrement">decrement</a>(string $id, int $offset = 1)
        
                                            <p><p>Decrement a raw value</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_clean">clean</a>()
        
                                            <p><p>Clean the cache</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_cache_info">cache_info</a>()
        
                                            <p><p>Cache Info</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_get_metadata">get_metadata</a>($id)
        
                                            <p><p>Get Cache Metadata</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_supported">is_supported</a>()
        
                                            <p>is_supported()</p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method_decorate">
        <div class="location">in <a href="CI_Driver.html#method_decorate">
CI_Driver</a> at line 247</div>
        <code>                    void
    <strong>decorate</strong>($parent)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Decorate</p>                <p><p>Decorates the child with the parent driver lib's methods and properties</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$parent</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___call">
        <div class="location">in <a href="CI_Driver.html#method___call">
CI_Driver</a> at line 295</div>
        <code>                    mixed
    <strong>__call</strong>($method, $args = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>__call magic method</p></p>                <p><p>Handles access to the parent driver library's methods</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$method</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$args</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___get">
        <div class="location">in <a href="CI_Driver.html#method___get">
CI_Driver</a> at line 315</div>
        <code>                    mixed
    <strong>__get</strong>($var)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>__get magic method</p></p>                <p><p>Handles reading of the parent driver library's properties</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$var</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___set">
        <div class="location">in <a href="CI_Driver.html#method___set">
CI_Driver</a> at line 334</div>
        <code>                    mixed
    <strong>__set</strong>($var, $val)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>__set magic method</p></p>                <p><p>Handles writing to the parent driver library's properties</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$var</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$val</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 62</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                <p><p>Only present so that an error message is logged
if APC is not available.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get">
        <div class="location">at line 81</div>
        <code>                    mixed
    <strong>get</strong>(string $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Get</p>                <p><p>Look for a value in the cache. If it exists, return the data,
if not, return FALSE</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$id</td>
                <td><p>Cache Ide</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>Value that is stored/FALSE on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_save">
        <div class="location">at line 101</div>
        <code>                    bool
    <strong>save</strong>(string $id, mixed $data, int $ttl = 60, bool $raw = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Cache Save</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$id</td>
                <td><p>Cache ID</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$data</td>
                <td><p>Data to store</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$ttl</td>
                <td><p>Time to live (in seconds)</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$raw</td>
                <td><p>Whether to store the raw value (unused)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td><p>true on success/false on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_delete">
        <div class="location">at line 114</div>
        <code>                    bool
    <strong>delete</strong>($id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Delete from Cache</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td><p>true on success/false on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_increment">
        <div class="location">at line 128</div>
        <code>                    mixed
    <strong>increment</strong>(string $id, int $offset = 1)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Increment a raw value</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$id</td>
                <td><p>Cache ID</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$offset</td>
                <td><p>Step/value to add</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>New value on success or FALSE on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_decrement">
        <div class="location">at line 145</div>
        <code>                    mixed
    <strong>decrement</strong>(string $id, int $offset = 1)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Decrement a raw value</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$id</td>
                <td><p>Cache ID</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$offset</td>
                <td><p>Step/value to reduce by</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>New value on success or FALSE on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_clean">
        <div class="location">at line 160</div>
        <code>                    bool
    <strong>clean</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Clean the cache</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td><p>false on failure/true on success</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_cache_info">
        <div class="location">at line 172</div>
        <code>                    mixed
    <strong>cache_info</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Cache Info</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>array on success, false on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_metadata">
        <div class="location">at line 185</div>
        <code>                    mixed
    <strong>get_metadata</strong>($id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Cache Metadata</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>array on success/false on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_supported">
        <div class="location">at line 213</div>
        <code>                    bool
    <strong>is_supported</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>is_supported()</p>                <p><p>Check to see if WinCache is available on this system, bail if it isn't.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

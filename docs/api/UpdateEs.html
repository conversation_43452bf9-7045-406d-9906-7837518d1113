<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>UpdateEs | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:UpdateEs" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>UpdateEs    
            </h1>
    </div>

    
    <p>        class
    <strong>UpdateEs</strong>
</p>

        
    
        

            
        
            <h2>Constants</h2>    <table class="table table-condensed">
                    <tr>
                <td>
                                                                                                                        INDEX_EXCEPTION_FOLDER
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        CREATE_OP
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        UPDATE_OP
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        DELETE_OP
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        SUPPORT_OP
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
            </table>

    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"><p>Codeigniter instance</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_esConfigs">
                                        protected                                        
                                                                                
                                    </td>
                <td>$esConfigs</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_execute">execute</a>(array $body)
        
                                            <p><p>Execute index when changing data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_updateRelationsIndex">updateRelationsIndex</a>($payloads)
        
                                            <p><p>Perform insert/update index of the requested table</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_updateIndex">updateIndex</a>(string $index, string $table, int $id, $isDeleted = false)
        
                                            <p><p>Perform insert/update index of the requested table</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_validate">validate</a>($payload)
        
                                            <p><p>Validate scheme, tables</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 21</div>
        <code>                    
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_execute">
        <div class="location">at line 35</div>
        <code>                    
    <strong>execute</strong>(array $body)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Execute index when changing data</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$body</td>
                <td><p>message body that parsed from RabbitMQ</p></td>
            </tr>
            </table>

            
            
                            <h4>Exceptions</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><a target="_blank" rel="noopener" href="https://www.php.net/Exception">Exception</a></td>
                <td></td>
            </tr>
            </table>

            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_updateRelationsIndex">
        <div class="location">at line 103</div>
        <code>            protected        
    <strong>updateRelationsIndex</strong>($payloads)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Perform insert/update index of the requested table</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$payloads</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_updateIndex">
        <div class="location">at line 145</div>
        <code>            protected        
    <strong>updateIndex</strong>(string $index, string $table, int $id, $isDeleted = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Perform insert/update index of the requested table</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$index</td>
                <td><p>index will be updated</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>table will be updated</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$id</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$isDeleted</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_validate">
        <div class="location">at line 163</div>
        <code>            protected        
    <strong>validate</strong>($payload)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validate scheme, tables</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$payload</td>
                <td></td>
            </tr>
            </table>

            
            
                            <h4>Exceptions</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><a target="_blank" rel="noopener" href="https://www.php.net/InvalidArgumentException">InvalidArgumentException</a></td>
                <td></td>
            </tr>
            </table>

            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Javascript | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Javascript" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Javascript    <small><span class="label label-danger">deprecated</span></small>
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Javascript</strong>
</p>

        
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td>3.0.0</td>
                    <td>This was never a good idea in the first place.</td>
                </tr>
                    </p>
    
        

            <div class="description">
            <p><p>Javascript Class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property__javascript_location">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_javascript_location</td>
                <td class="last"><p>JavaScript location</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $params = array())
        
                                            <p>Constructor</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_blur">blur</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Blur</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_change">change</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Change</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_click">click</a>($element = &#039;this&#039;, $js = &#039;&#039;, $ret_false = TRUE)
        
                                            <p>Click</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_dblclick">dblclick</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p><p>Double Click</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_error">error</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Error</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_focus">focus</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Focus</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_hover">hover</a>($element = &#039;this&#039;, $over = &#039;&#039;, $out = &#039;&#039;)
        
                                            <p>Hover</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_keydown">keydown</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Keydown</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_keyup">keyup</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Keyup</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_load">load</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Load</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_mousedown">mousedown</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Mousedown</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_mouseout">mouseout</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p><p>Mouse Out</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_mouseover">mouseover</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p><p>Mouse Over</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_mouseup">mouseup</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Mouseup</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_output">output</a>($js)
        
                                            <p>Output</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_ready">ready</a>(string $js)
        
                                            <p>Ready</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_resize">resize</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Resize</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_scroll">scroll</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Scroll</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_unload">unload</a>($element = &#039;this&#039;, $js = &#039;&#039;)
        
                                            <p>Unload</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_addClass">addClass</a>($element = &#039;this&#039;, $class = &#039;&#039;)
        
                                            <p><p>Add Class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_animate">animate</a>(string $element = &#039;this&#039;, array $params = array(), mixed $speed = &#039;&#039;, string $extra = &#039;&#039;)
        
                                            <p>Animate</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_fadeIn">fadeIn</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Fade In</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_fadeOut">fadeOut</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Fade Out</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_slideUp">slideUp</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Slide Up</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_removeClass">removeClass</a>($element = &#039;this&#039;, $class = &#039;&#039;)
        
                                            <p><p>Remove Class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_slideDown">slideDown</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Slide Down</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_slideToggle">slideToggle</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p><p>Slide Toggle</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_hide">hide</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p>Hide</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_toggle">toggle</a>($element = &#039;this&#039;)
        
                                            <p>Toggle</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_toggleClass">toggleClass</a>(string $element = &#039;this&#039;, string $class = &#039;&#039;)
        
                                            <p><p>Toggle Class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_show">show</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p>Show</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_compile">compile</a>(string $view_var = &#039;script_foot&#039;, bool $script_tags = TRUE)
        
                                            <p>Compile</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_clear_compile">clear_compile</a>()
        
                                            <p><p>Clear Compile</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_external">external</a>(string $external_file = &#039;&#039;, bool $relative = FALSE)
        
                                            <p>External</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_inline">inline</a>($script, $cdata = TRUE)
        
                                            <p>Inline</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__open_script">_open_script</a>($src = &#039;&#039;)
        
                                            <p><p>Open Script</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__close_script">_close_script</a>($extra = &quot;\n&quot;)
        
                                            <p><p>Close Script</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_update">update</a>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        
                                            <p>Update</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_generate_json">generate_json</a>($result = NULL, $match_array_type = FALSE)
        
                                            <p><p>Generate JSON</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__is_associative_array">_is_associative_array</a>($arr)
        
                                            <p><p>Is associative array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__prep_args">_prep_args</a>(mixed $result, bool $is_key = FALSE)
        
                                            <p><p>Prep Args</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 67</div>
        <code>                    void
    <strong>__construct</strong>(array $params = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Constructor</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_blur">
        <div class="location">at line 104</div>
        <code>                    string
    <strong>blur</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Blur</p>                <p><p>Outputs a javascript library blur event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_change">
        <div class="location">at line 120</div>
        <code>                    string
    <strong>change</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Change</p>                <p><p>Outputs a javascript library change event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_click">
        <div class="location">at line 137</div>
        <code>                    string
    <strong>click</strong>($element = &#039;this&#039;, $js = &#039;&#039;, $ret_false = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Click</p>                <p><p>Outputs a javascript library click event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$ret_false</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_dblclick">
        <div class="location">at line 153</div>
        <code>                    string
    <strong>dblclick</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Double Click</p></p>                <p><p>Outputs a javascript library dblclick event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_error">
        <div class="location">at line 169</div>
        <code>                    string
    <strong>error</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Error</p>                <p><p>Outputs a javascript library error event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_focus">
        <div class="location">at line 185</div>
        <code>                    string
    <strong>focus</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Focus</p>                <p><p>Outputs a javascript library focus event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_hover">
        <div class="location">at line 202</div>
        <code>                    string
    <strong>hover</strong>($element = &#039;this&#039;, $over = &#039;&#039;, $out = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Hover</p>                <p><p>Outputs a javascript library hover event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$over</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$out</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_keydown">
        <div class="location">at line 218</div>
        <code>                    string
    <strong>keydown</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Keydown</p>                <p><p>Outputs a javascript library keydown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_keyup">
        <div class="location">at line 234</div>
        <code>                    string
    <strong>keyup</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Keyup</p>                <p><p>Outputs a javascript library keydown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_load">
        <div class="location">at line 250</div>
        <code>                    string
    <strong>load</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Load</p>                <p><p>Outputs a javascript library load event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mousedown">
        <div class="location">at line 266</div>
        <code>                    string
    <strong>mousedown</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Mousedown</p>                <p><p>Outputs a javascript library mousedown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mouseout">
        <div class="location">at line 282</div>
        <code>                    string
    <strong>mouseout</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Mouse Out</p></p>                <p><p>Outputs a javascript library mouseout event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mouseover">
        <div class="location">at line 298</div>
        <code>                    string
    <strong>mouseover</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Mouse Over</p></p>                <p><p>Outputs a javascript library mouseover event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mouseup">
        <div class="location">at line 314</div>
        <code>                    string
    <strong>mouseup</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Mouseup</p>                <p><p>Outputs a javascript library mouseup event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_output">
        <div class="location">at line 329</div>
        <code>                    string
    <strong>output</strong>($js)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Output</p>                <p><p>Outputs the called javascript to the screen</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_ready">
        <div class="location">at line 344</div>
        <code>                    string
    <strong>ready</strong>(string $js)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Ready</p>                <p><p>Outputs a javascript library mouseup event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$js</td>
                <td><p>Code to execute</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_resize">
        <div class="location">at line 360</div>
        <code>                    string
    <strong>resize</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Resize</p>                <p><p>Outputs a javascript library resize event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_scroll">
        <div class="location">at line 376</div>
        <code>                    string
    <strong>scroll</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Scroll</p>                <p><p>Outputs a javascript library scroll event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_unload">
        <div class="location">at line 392</div>
        <code>                    string
    <strong>unload</strong>($element = &#039;this&#039;, $js = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Unload</p>                <p><p>Outputs a javascript library unload event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$js</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_addClass">
        <div class="location">at line 410</div>
        <code>                    string
    <strong>addClass</strong>($element = &#039;this&#039;, $class = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Class</p></p>                <p><p>Outputs a javascript library addClass event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$class</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_animate">
        <div class="location">at line 428</div>
        <code>                    string
    <strong>animate</strong>(string $element = &#039;this&#039;, array $params = array(), mixed $speed = &#039;&#039;, string $extra = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Animate</p>                <p><p>Outputs a javascript library animate event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$element</td>
                <td><p>= 'this'</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>= array()</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$speed</td>
                <td><p>'slow', 'normal', 'fast', or time in milliseconds</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$extra</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_fadeIn">
        <div class="location">at line 445</div>
        <code>                    string
    <strong>fadeIn</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fade In</p></p>                <p><p>Outputs a javascript library hide event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_fadeOut">
        <div class="location">at line 462</div>
        <code>                    string
    <strong>fadeOut</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fade Out</p></p>                <p><p>Outputs a javascript library hide event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_slideUp">
        <div class="location">at line 478</div>
        <code>                    string
    <strong>slideUp</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Slide Up</p></p>                <p><p>Outputs a javascript library slideUp event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_removeClass">
        <div class="location">at line 495</div>
        <code>                    string
    <strong>removeClass</strong>($element = &#039;this&#039;, $class = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Remove Class</p></p>                <p><p>Outputs a javascript library removeClass event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$class</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_slideDown">
        <div class="location">at line 512</div>
        <code>                    string
    <strong>slideDown</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Slide Down</p></p>                <p><p>Outputs a javascript library slideDown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_slideToggle">
        <div class="location">at line 529</div>
        <code>                    string
    <strong>slideToggle</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Slide Toggle</p></p>                <p><p>Outputs a javascript library slideToggle event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_hide">
        <div class="location">at line 547</div>
        <code>                    string
    <strong>hide</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Hide</p>                <p><p>Outputs a javascript library hide action</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_toggle">
        <div class="location">at line 562</div>
        <code>                    string
    <strong>toggle</strong>($element = &#039;this&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Toggle</p>                <p><p>Outputs a javascript library toggle event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_toggleClass">
        <div class="location">at line 579</div>
        <code>                    string
    <strong>toggleClass</strong>(string $element = &#039;this&#039;, string $class = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Toggle Class</p></p>                <p><p>Outputs a javascript library toggle class event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$element</td>
                <td><p>= 'this'</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$class</td>
                <td><p>= ''</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_show">
        <div class="location">at line 596</div>
        <code>                    string
    <strong>show</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Show</p>                <p><p>Outputs a javascript library show event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_compile">
        <div class="location">at line 612</div>
        <code>                    string
    <strong>compile</strong>(string $view_var = &#039;script_foot&#039;, bool $script_tags = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Compile</p>                <p><p>gather together all script needing to be output</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$view_var</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$script_tags</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_clear_compile">
        <div class="location">at line 626</div>
        <code>                    void
    <strong>clear_compile</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Clear Compile</p></p>                <p><p>Clears any previous javascript collected for output</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_external">
        <div class="location">at line 642</div>
        <code>                    string
    <strong>external</strong>(string $external_file = &#039;&#039;, bool $relative = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>External</p>                <p><p>Outputs a <script> tag with the source as an external js file</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$external_file</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$relative</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_inline">
        <div class="location">at line 680</div>
        <code>                    string
    <strong>inline</strong>($script, $cdata = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Inline</p>                <p><p>Outputs a <script> tag</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$script</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$cdata</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__open_script">
        <div class="location">at line 697</div>
        <code>            protected        string
    <strong>_open_script</strong>($src = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Open Script</p></p>                <p><p>Outputs an opening <script></p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$src</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__close_script">
        <div class="location">at line 713</div>
        <code>            protected        string
    <strong>_close_script</strong>($extra = &quot;\n&quot;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Close Script</p></p>                <p><p>Outputs an closing </script></p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$extra</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_update">
        <div class="location">at line 732</div>
        <code>                    string
    <strong>update</strong>($element = &#039;this&#039;, $speed = &#039;&#039;, $callback = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Update</p>                <p><p>Outputs a javascript library slideDown event</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$element</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$speed</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_generate_json">
        <div class="location">at line 748</div>
        <code>                    string
    <strong>generate_json</strong>($result = NULL, $match_array_type = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Generate JSON</p></p>                <p><p>Can be passed a database result or associative array and returns a JSON formatted string</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$result</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$match_array_type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>a json formatted string</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__is_associative_array">
        <div class="location">at line 812</div>
        <code>            protected        bool
    <strong>_is_associative_array</strong>($arr)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is associative array</p></p>                <p><p>Checks for an associative array</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$arr</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__prep_args">
        <div class="location">at line 836</div>
        <code>            protected        string
    <strong>_prep_args</strong>(mixed $result, bool $is_key = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prep Args</p></p>                <p><p>Ensures a standard json value and escapes values</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$result</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$is_key</td>
                <td><p>= FALSE</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_DB_pdo_sqlite_forge | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_DB_pdo_sqlite_forge" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_DB_pdo_sqlite_forge    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_DB_pdo_sqlite_forge</strong>        extends <a href="CI_DB_pdo_forge.html">CI_DB_pdo_forge</a>
</p>

        
    
        

            <div class="description">
            <p><p>PDO SQLite Forge Class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_db">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$db</td>
                <td class="last"><p>Database object</p></td>
                <td><small>from&nbsp;<a href="CI_DB_forge.html#property_db">
CI_DB_forge</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_fields">
                                                                                array
                                                                                
                                    </td>
                <td>$fields</td>
                <td class="last"><p>Fields data</p></td>
                <td><small>from&nbsp;<a href="CI_DB_forge.html#property_fields">
CI_DB_forge</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_keys">
                                                                                array
                                                                                
                                    </td>
                <td>$keys</td>
                <td class="last"><p>Keys data</p></td>
                <td><small>from&nbsp;<a href="CI_DB_forge.html#property_keys">
CI_DB_forge</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_primary_keys">
                                                                                array
                                                                                
                                    </td>
                <td>$primary_keys</td>
                <td class="last"><p>Primary Keys data</p></td>
                <td><small>from&nbsp;<a href="CI_DB_forge.html#property_primary_keys">
CI_DB_forge</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_db_char_set">
                                                                                string
                                                                                
                                    </td>
                <td>$db_char_set</td>
                <td class="last"><p>Database character set</p></td>
                <td><small>from&nbsp;<a href="CI_DB_forge.html#property_db_char_set">
CI_DB_forge</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__create_database">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_create_database</td>
                <td class="last"><p>CREATE DATABASE statement</p></td>
                <td><small>from&nbsp;<a href="CI_DB_forge.html#property__create_database">
CI_DB_forge</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__drop_database">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_drop_database</td>
                <td class="last"><p>DROP DATABASE statement</p></td>
                <td><small>from&nbsp;<a href="CI_DB_forge.html#property__drop_database">
CI_DB_forge</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__create_table">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_create_table</td>
                <td class="last"><p>CREATE TABLE statement</p></td>
                <td><small>from&nbsp;<a href="CI_DB_forge.html#property__create_table">
CI_DB_forge</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__create_table_if">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_create_table_if</td>
                <td class="last"><p>CREATE TABLE IF statement</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__create_table_keys">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_create_table_keys</td>
                <td class="last"><p>CREATE TABLE keys flag</p></td>
                <td><small>from&nbsp;<a href="CI_DB_forge.html#property__create_table_keys">
CI_DB_forge</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__drop_table_if">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_drop_table_if</td>
                <td class="last"><p>DROP TABLE IF statement</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__rename_table">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_rename_table</td>
                <td class="last"><p>RENAME TABLE statement</p></td>
                <td><small>from&nbsp;<a href="CI_DB_forge.html#property__rename_table">
CI_DB_forge</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__unsigned">
                                        protected                                        bool|array
                                                                                
                                    </td>
                <td>$_unsigned</td>
                <td class="last"><p>UNSIGNED support</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__null">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_null</td>
                <td class="last"><p>NULL value representation in CREATE/ALTER TABLE statements</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__default">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_default</td>
                <td class="last"><p>DEFAULT value representation in CREATE/ALTER TABLE statements</p></td>
                <td><small>from&nbsp;<a href="CI_DB_forge.html#property__default">
CI_DB_forge</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(object $db)
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_create_database">create_database</a>(string $db_name)
        
                                            <p><p>Create database</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_drop_database">drop_database</a>(string $db_name)
        
                                            <p><p>Drop database</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_DB_forge.html">CI_DB_forge</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_add_key">add_key</a>(string $key, bool $primary = FALSE)
        
                                            <p><p>Add Key</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method_add_key">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_DB_forge.html">CI_DB_forge</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_add_field">add_field</a>(array $field)
        
                                            <p><p>Add Field</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method_add_field">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_create_table">create_table</a>(string $table, bool $if_not_exists = FALSE, array $attributes = array())
        
                                            <p><p>Create Table</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method_create_table">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method__create_table">_create_table</a>(string $table, bool $if_not_exists, array $attributes)
        
                                            <p><p>Create Table</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method__create_table">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__create_table_attr">_create_table_attr</a>(array $attributes)
        
                                            <p><p>CREATE TABLE attributes</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method__create_table_attr">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_drop_table">drop_table</a>(string $table_name, bool $if_exists = FALSE)
        
                                            <p><p>Drop Table</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method_drop_table">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method__drop_table">_drop_table</a>(string $table, bool $if_exists)
        
                                            <p><p>Drop Table</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method__drop_table">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_rename_table">rename_table</a>(string $table_name, string $new_table_name)
        
                                            <p><p>Rename Table</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method_rename_table">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_add_column">add_column</a>(string $table, array $field, string $_after = NULL)
        
                                            <p><p>Column Add</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method_add_column">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_drop_column">drop_column</a>(string $table, string $column_name)
        
                                            <p><p>Column Drop</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method_drop_column">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_modify_column">modify_column</a>(string $table, string $field)
        
                                            <p><p>Column Modify</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method_modify_column">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string|string[]
                </div>
                <div class="col-md-8">
                    <a href="#method__alter_table">_alter_table</a>(string $alter_type, string $table, mixed $field)
        
                                            <p><p>ALTER TABLE</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method__process_fields">_process_fields</a>(bool $create_table = FALSE)
        
                                            <p><p>Process fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method__process_fields">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__process_column">_process_column</a>(array $field)
        
                                            <p><p>Process column</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__attr_type">_attr_type</a>(array $attributes)
        
                                            <p><p>Field attribute TYPE</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__attr_unsigned">_attr_unsigned</a>(array $attributes, array $field)
        
                                            <p><p>Field attribute UNSIGNED</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method__attr_unsigned">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__attr_default">_attr_default</a>(array $attributes, array $field)
        
                                            <p><p>Field attribute DEFAULT</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method__attr_default">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__attr_unique">_attr_unique</a>(array $attributes, array $field)
        
                                            <p><p>Field attribute UNIQUE</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method__attr_unique">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__attr_auto_increment">_attr_auto_increment</a>(array $attributes, array $field)
        
                                            <p><p>Field attribute AUTO_INCREMENT</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__process_primary_keys">_process_primary_keys</a>(string $table)
        
                                            <p><p>Process primary keys</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method__process_primary_keys">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string[]
                </div>
                <div class="col-md-8">
                    <a href="#method__process_indexes">_process_indexes</a>(string $table)
        
                                            <p><p>Process indexes</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method__process_indexes">
CI_DB_forge</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__reset">_reset</a>()
        
                                            <p>Reset</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_forge.html#method__reset">
CI_DB_forge</a></small></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 85</div>
        <code>                    void
    <strong>__construct</strong>(object $db)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>object</td>
                <td>$db</td>
                <td><p>Database object</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_create_database">
        <div class="location">at line 104</div>
        <code>                    bool
    <strong>create_database</strong>(string $db_name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create database</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$db_name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_drop_database">
        <div class="location">at line 119</div>
        <code>                    bool
    <strong>drop_database</strong>(string $db_name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Drop database</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$db_name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_key">
        <div class="location">in <a href="CI_DB_forge.html#method_add_key">
CI_DB_forge</a> at line 240</div>
        <code>                    <a href="CI_DB_forge.html">CI_DB_forge</a>
    <strong>add_key</strong>(string $key, bool $primary = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Key</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$primary</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_DB_forge.html">CI_DB_forge</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_field">
        <div class="location">in <a href="CI_DB_forge.html#method_add_field">
CI_DB_forge</a> at line 278</div>
        <code>                    <a href="CI_DB_forge.html">CI_DB_forge</a>
    <strong>add_field</strong>(array $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Field</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$field</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_DB_forge.html">CI_DB_forge</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_create_table">
        <div class="location">in <a href="CI_DB_forge.html#method_create_table">
CI_DB_forge</a> at line 322</div>
        <code>                    bool
    <strong>create_table</strong>(string $table, bool $if_not_exists = FALSE, array $attributes = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create Table</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>Table name</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$if_not_exists</td>
                <td><p>Whether to add IF NOT EXISTS condition</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$attributes</td>
                <td><p>Associative array of table attributes</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__create_table">
        <div class="location">in <a href="CI_DB_forge.html#method__create_table">
CI_DB_forge</a> at line 380</div>
        <code>            protected        mixed
    <strong>_create_table</strong>(string $table, bool $if_not_exists, array $attributes)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create Table</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>Table name</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$if_not_exists</td>
                <td><p>Whether to add 'IF NOT EXISTS' condition</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$attributes</td>
                <td><p>Associative array of table attributes</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__create_table_attr">
        <div class="location">in <a href="CI_DB_forge.html#method__create_table_attr">
CI_DB_forge</a> at line 432</div>
        <code>            protected        string
    <strong>_create_table_attr</strong>(array $attributes)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>CREATE TABLE attributes</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$attributes</td>
                <td><p>Associative array of table attributes</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_drop_table">
        <div class="location">in <a href="CI_DB_forge.html#method_drop_table">
CI_DB_forge</a> at line 456</div>
        <code>                    bool
    <strong>drop_table</strong>(string $table_name, bool $if_exists = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Drop Table</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table_name</td>
                <td><p>Table name</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$if_exists</td>
                <td><p>Whether to add an IF EXISTS condition</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__drop_table">
        <div class="location">in <a href="CI_DB_forge.html#method__drop_table">
CI_DB_forge</a> at line 494</div>
        <code>            protected        mixed
    <strong>_drop_table</strong>(string $table, bool $if_exists)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Drop Table</p></p>                <p><p>Generates a platform-specific DROP TABLE string</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>Table name</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$if_exists</td>
                <td><p>Whether to add an IF EXISTS condition</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>(Returns a platform-specific DROP table string, or TRUE to indicate there's nothing to do)</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_rename_table">
        <div class="location">in <a href="CI_DB_forge.html#method_rename_table">
CI_DB_forge</a> at line 525</div>
        <code>                    bool
    <strong>rename_table</strong>(string $table_name, string $new_table_name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Rename Table</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table_name</td>
                <td><p>Old table name</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$new_table_name</td>
                <td><p>New table name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_column">
        <div class="location">in <a href="CI_DB_forge.html#method_add_column">
CI_DB_forge</a> at line 565</div>
        <code>                    bool
    <strong>add_column</strong>(string $table, array $field, string $_after = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Column Add</p></p>                        
                    <p>
                        
                                    <tr>
                        <td>Remove</td>
                        <td>deprecated $_after option in 3.1+</td>
                        </tr>
                            </p>
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>Table name</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$field</td>
                <td><p>Column definition</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$_after</td>
                <td><p>Column for AFTER clause (deprecated)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_drop_column">
        <div class="location">in <a href="CI_DB_forge.html#method_drop_column">
CI_DB_forge</a> at line 608</div>
        <code>                    bool
    <strong>drop_column</strong>(string $table, string $column_name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Column Drop</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>Table name</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$column_name</td>
                <td><p>Column name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_modify_column">
        <div class="location">in <a href="CI_DB_forge.html#method_modify_column">
CI_DB_forge</a> at line 628</div>
        <code>                    bool
    <strong>modify_column</strong>(string $table, string $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Column Modify</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>Table name</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$field</td>
                <td><p>Column definition</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__alter_table">
        <div class="location">at line 155</div>
        <code>            protected        string|string[]
    <strong>_alter_table</strong>(string $alter_type, string $table, mixed $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>ALTER TABLE</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$alter_type</td>
                <td><p>ALTER type</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>Table name</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$field</td>
                <td><p>Column definition</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string|string[]</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__process_fields">
        <div class="location">in <a href="CI_DB_forge.html#method__process_fields">
CI_DB_forge</a> at line 703</div>
        <code>            protected        array
    <strong>_process_fields</strong>(bool $create_table = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Process fields</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$create_table</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__process_column">
        <div class="location">at line 183</div>
        <code>            protected        string
    <strong>_process_column</strong>(array $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Process column</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$field</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__attr_type">
        <div class="location">at line 203</div>
        <code>            protected        void
    <strong>_attr_type</strong>(array $attributes)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Field attribute TYPE</p></p>                <p><p>Performs a data type mapping between different databases.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$attributes</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__attr_unsigned">
        <div class="location">in <a href="CI_DB_forge.html#method__attr_unsigned">
CI_DB_forge</a> at line 850</div>
        <code>            protected        void
    <strong>_attr_unsigned</strong>(array $attributes, array $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Field attribute UNSIGNED</p></p>                <p><p>Depending on the _unsigned property value:</p>
<ul>
<li>TRUE will always set $field['unsigned'] to 'UNSIGNED'</li>
<li>FALSE will always set $field['unsigned'] to ''</li>
<li>array(TYPE) will set $field['unsigned'] to 'UNSIGNED',
if $attributes['TYPE'] is found in the array</li>
<li>array(TYPE =&gt; UTYPE) will change $field['type'],
from TYPE to UTYPE in case of a match</li>
</ul></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$attributes</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$field</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__attr_default">
        <div class="location">in <a href="CI_DB_forge.html#method__attr_default">
CI_DB_forge</a> at line 891</div>
        <code>            protected        void
    <strong>_attr_default</strong>(array $attributes, array $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Field attribute DEFAULT</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$attributes</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$field</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__attr_unique">
        <div class="location">in <a href="CI_DB_forge.html#method__attr_unique">
CI_DB_forge</a> at line 924</div>
        <code>            protected        void
    <strong>_attr_unique</strong>(array $attributes, array $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Field attribute UNIQUE</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$attributes</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$field</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__attr_auto_increment">
        <div class="location">at line 224</div>
        <code>            protected        void
    <strong>_attr_auto_increment</strong>(array $attributes, array $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Field attribute AUTO_INCREMENT</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$attributes</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$field</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__process_primary_keys">
        <div class="location">in <a href="CI_DB_forge.html#method__process_primary_keys">
CI_DB_forge</a> at line 957</div>
        <code>            protected        string
    <strong>_process_primary_keys</strong>(string $table)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Process primary keys</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>Table name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__process_indexes">
        <div class="location">in <a href="CI_DB_forge.html#method__process_indexes">
CI_DB_forge</a> at line 986</div>
        <code>            protected        string[]
    <strong>_process_indexes</strong>(string $table)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Process indexes</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>Table name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string[]</td>
            <td><p>list of SQL statements</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__reset">
        <div class="location">in <a href="CI_DB_forge.html#method__reset">
CI_DB_forge</a> at line 1028</div>
        <code>            protected        void
    <strong>_reset</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Reset</p>                <p><p>Resets table creation vars</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

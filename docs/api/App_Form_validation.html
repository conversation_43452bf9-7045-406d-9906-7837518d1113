<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>App_Form_validation | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:App_Form_validation" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>App_Form_validation    
            </h1>
    </div>

    
    <p>        class
    <strong>App_Form_validation</strong>        extends <a href="CI_Form_validation.html">CI_Form_validation</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_CI">
                                        protected                                        
                                                                                
                                    </td>
                <td>$CI</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__field_data">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_field_data</td>
                <td class="last"><p>Validation data for the current form submission</p></td>
                <td><small>from&nbsp;<a href="CI_Form_validation.html#property__field_data">
CI_Form_validation</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__config_rules">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_config_rules</td>
                <td class="last"><p>Validation rules for the current form</p></td>
                <td><small>from&nbsp;<a href="CI_Form_validation.html#property__config_rules">
CI_Form_validation</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__error_array">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_error_array</td>
                <td class="last"><p>Array of validation errors</p></td>
                <td><small>from&nbsp;<a href="CI_Form_validation.html#property__error_array">
CI_Form_validation</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__error_messages">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_error_messages</td>
                <td class="last"><p>Array of custom error messages</p></td>
                <td><small>from&nbsp;<a href="CI_Form_validation.html#property__error_messages">
CI_Form_validation</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__error_prefix">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_error_prefix</td>
                <td class="last"><p>Start tag for error wrapping</p></td>
                <td><small>from&nbsp;<a href="CI_Form_validation.html#property__error_prefix">
CI_Form_validation</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__error_suffix">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_error_suffix</td>
                <td class="last"><p>End tag for error wrapping</p></td>
                <td><small>from&nbsp;<a href="CI_Form_validation.html#property__error_suffix">
CI_Form_validation</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_error_string">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$error_string</td>
                <td class="last"><p>Custom error message</p></td>
                <td><small>from&nbsp;<a href="CI_Form_validation.html#property_error_string">
CI_Form_validation</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__safe_form_data">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_safe_form_data</td>
                <td class="last"><p>Whether the form data has been validated as safe</p></td>
                <td><small>from&nbsp;<a href="CI_Form_validation.html#property__safe_form_data">
CI_Form_validation</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_validation_data">
                                                                                array
                                                                                
                                    </td>
                <td>$validation_data</td>
                <td class="last"><p>Custom data to validate</p></td>
                <td><small>from&nbsp;<a href="CI_Form_validation.html#property_validation_data">
CI_Form_validation</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_cfk_hidden">
                                        protected                                        
                                                                                
                                    </td>
                <td>$cfk_hidden</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Initialize Form_Validation class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Form_validation.html">CI_Form_validation</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_rules">set_rules</a>(mixed $field, string $label = &#039;&#039;, mixed $rules = array(), array $errors = array())
        
                                            <p><p>Set Rules</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_set_rules">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Form_validation.html">CI_Form_validation</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_data">set_data</a>(array $data)
        
                                            <p><p>By default, form validation uses the $_POST array to validate</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_set_data">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Form_validation.html">CI_Form_validation</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_message">set_message</a>($lang, $val = &#039;&#039;)
        
                                            <p><p>Set Error Message</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_set_message">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Form_validation.html">CI_Form_validation</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_error_delimiters">set_error_delimiters</a>($prefix = &#039;&lt;p&gt;&#039;, $suffix = &#039;&lt;/p&gt;&#039;)
        
                                            <p><p>Set The Error Delimiter</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_set_error_delimiters">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_error">error</a>(string $field, string $prefix = &#039;&#039;, string $suffix = &#039;&#039;)
        
                                            <p><p>Get Error Message</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_error">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_error_array">error_array</a>()
        
                                            <p><p>Get Array of Error Messages</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_error_array">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_error_string">error_string</a>($prefix = &#039;&#039;, $suffix = &#039;&#039;)
        
                                            <p><p>Error String</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_error_string">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_run">run</a>(string $group = &#039;&#039;)
        
                                            <p><p>Run the Validator</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method__prepare_rules">_prepare_rules</a>(array $rules)
        
                                            <p><p>Prepare rules</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method__prepare_rules">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method__reduce_array">_reduce_array</a>($array, $keys, $i = 0)
        
                                            <p><p>Traverse a multidimensional $_POST array index until the data is found</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method__reduce_array">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__reset_post_array">_reset_post_array</a>()
        
                                            <p><p>Re-populate the _POST array with our finalized and processed data</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method__reset_post_array">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method__execute">_execute</a>($row, $rules, $postdata = NULL, $cycles = 0)
        
                                            <p><p>Executes the Validation routines</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method__execute">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_error_message">_get_error_message</a>(string $rule, string $field)
        
                                            <p><p>Get the error message for the rule</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method__get_error_message">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__translate_fieldname">_translate_fieldname</a>($fieldname)
        
                                            <p><p>Translate a field name</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method__translate_fieldname">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__build_error_msg">_build_error_msg</a>($line, $field = &#039;&#039;, $param = &#039;&#039;)
        
                                            <p><p>Build an error message using the field and param.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method__build_error_msg">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_has_rule">has_rule</a>($field)
        
                                            <p><p>Checks if the rule is present within the validator</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_has_rule">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_set_value">set_value</a>($field = &#039;&#039;, $default = &#039;&#039;)
        
                                            <p><p>Get the value from a form</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_set_value">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_set_select">set_select</a>($field = &#039;&#039;, $value = &#039;&#039;, $default = FALSE)
        
                                            <p><p>Set Select</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_set_select">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_set_radio">set_radio</a>($field = &#039;&#039;, $value = &#039;&#039;, $default = FALSE)
        
                                            <p><p>Set Radio</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_set_radio">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_set_checkbox">set_checkbox</a>($field = &#039;&#039;, $value = &#039;&#039;, $default = FALSE)
        
                                            <p><p>Set Checkbox</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_set_checkbox">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_required">required</a>($str)
        
                                            <p>Required</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_required">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_regex_match">regex_match</a>($str, $regex)
        
                                            <p><p>Performs a Regular Expression match test.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_regex_match">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_matches">matches</a>(string $str, string $field)
        
                                            <p><p>Match one field to another</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_matches">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_differs">differs</a>($str, $field)
        
                                            <p><p>Differs from another field</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_differs">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_unique">is_unique</a>(string $str, string $field)
        
                                            <p><p>Is Unique</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_is_unique">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_min_length">min_length</a>($str, $val)
        
                                            <p><p>Minimum Length</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_min_length">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_max_length">max_length</a>($str, $val)
        
                                            <p><p>Max Length</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_max_length">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_exact_length">exact_length</a>($str, $val)
        
                                            <p><p>Exact Length</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_exact_length">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_valid_url">valid_url</a>(string $str)
        
                                            <p><p>Valid URL</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_valid_url">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_valid_email">valid_email</a>($str)
        
                                            <p><p>Valid Email</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_valid_email">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_valid_emails">valid_emails</a>($str)
        
                                            <p><p>Valid Emails</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_valid_emails">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_valid_ip">valid_ip</a>($ip, $which = &#039;&#039;)
        
                                            <p><p>Validate IP Address</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_valid_ip">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_alpha">alpha</a>($str)
        
                                            <p>Alpha</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_alpha">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_alpha_numeric">alpha_numeric</a>($str)
        
                                            <p>Alpha-numeric</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_alpha_numeric">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_alpha_numeric_spaces">alpha_numeric_spaces</a>($str)
        
                                            <p><p>Alpha-numeric w/ spaces</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_alpha_numeric_spaces">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_alpha_dash">alpha_dash</a>($str)
        
                                            <p><p>Alpha-numeric with underscores and dashes</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_alpha_dash">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_numeric">numeric</a>($str)
        
                                            <p>Numeric</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_numeric">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_integer">integer</a>($str)
        
                                            <p>Integer</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_integer">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_decimal">decimal</a>($str)
        
                                            <p><p>Decimal number</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_decimal">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_greater_than">greater_than</a>($str, $min)
        
                                            <p><p>Greater than</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_greater_than">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_greater_than_equal_to">greater_than_equal_to</a>($str, $min)
        
                                            <p><p>Equal to or Greater than</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_greater_than_equal_to">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_less_than">less_than</a>($str, $max)
        
                                            <p><p>Less than</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_less_than">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_less_than_equal_to">less_than_equal_to</a>($str, $max)
        
                                            <p><p>Equal to or Less than</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_less_than_equal_to">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_in_list">in_list</a>($value, $list)
        
                                            <p><p>Value should be within an array of values</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_in_list">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_natural">is_natural</a>($str)
        
                                            <p><p>Is a Natural number  (0,1,2,3, etc.)</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_is_natural">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_natural_no_zero">is_natural_no_zero</a>($str)
        
                                            <p><p>Is a Natural number, but not a zero  (1,2,3, etc.)</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_is_natural_no_zero">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_valid_base64">valid_base64</a>($str)
        
                                            <p><p>Valid Base64</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_valid_base64">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_prep_for_form">prep_for_form</a>(mixed $data)
        <small><span class="label label-danger">deprecated</span></small>
                                            <p><p>Prep data for form</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_prep_for_form">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_prep_url">prep_url</a>($str = &#039;&#039;)
        
                                            <p><p>Prep URL</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_prep_url">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_strip_image_tags">strip_image_tags</a>($str)
        
                                            <p><p>Strip Image Tags</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_strip_image_tags">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_encode_php_tags">encode_php_tags</a>($str)
        
                                            <p><p>Convert PHP tags to entities</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_encode_php_tags">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Form_validation.html">CI_Form_validation</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_reset_validation">reset_validation</a>()
        
                                            <p><p>Reset validation vars</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Form_validation.html#method_reset_validation">
CI_Form_validation</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_errors_array">errors_array</a>()
        
                                            <p><p>Custom method for error messages in array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 12</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize Form_Validation class</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_rules">
        <div class="location">in <a href="CI_Form_validation.html#method_set_rules">
CI_Form_validation</a> at line 167</div>
        <code>                    <a href="CI_Form_validation.html">CI_Form_validation</a>
    <strong>set_rules</strong>(mixed $field, string $label = &#039;&#039;, mixed $rules = array(), array $errors = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Rules</p></p>                <p><p>This function takes an array of field names and validation
rules as input, any custom error messages, validates the info,
and stores it</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$field</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$label</td>
                <td></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$rules</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$errors</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Form_validation.html">CI_Form_validation</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_data">
        <div class="location">in <a href="CI_Form_validation.html#method_set_data">
CI_Form_validation</a> at line 267</div>
        <code>                    <a href="CI_Form_validation.html">CI_Form_validation</a>
    <strong>set_data</strong>(array $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>By default, form validation uses the $_POST array to validate</p></p>                <p><p>If an array is set through this method, then this array will
be used instead of the $_POST array</p>
<p>Note that if you are validating multiple arrays, then the
reset_validation() function should be called after validating
each array due to the limitations of CI's singleton</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Form_validation.html">CI_Form_validation</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_message">
        <div class="location">in <a href="CI_Form_validation.html#method_set_message">
CI_Form_validation</a> at line 289</div>
        <code>                    <a href="CI_Form_validation.html">CI_Form_validation</a>
    <strong>set_message</strong>($lang, $val = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Error Message</p></p>                <p><p>Lets users set their own error messages on the fly. Note:
The key name has to match the function name that it corresponds to.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$lang</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$val</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Form_validation.html">CI_Form_validation</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_error_delimiters">
        <div class="location">in <a href="CI_Form_validation.html#method_set_error_delimiters">
CI_Form_validation</a> at line 311</div>
        <code>                    <a href="CI_Form_validation.html">CI_Form_validation</a>
    <strong>set_error_delimiters</strong>($prefix = &#039;&lt;p&gt;&#039;, $suffix = &#039;&lt;/p&gt;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set The Error Delimiter</p></p>                <p><p>Permits a prefix/suffix to be added to each error message</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$prefix</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$suffix</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Form_validation.html">CI_Form_validation</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_error">
        <div class="location">in <a href="CI_Form_validation.html#method_error">
CI_Form_validation</a> at line 330</div>
        <code>                    string
    <strong>error</strong>(string $field, string $prefix = &#039;&#039;, string $suffix = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Error Message</p></p>                <p><p>Gets the error message associated with a particular field</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$field</td>
                <td><p>Field name</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$prefix</td>
                <td><p>HTML start tag</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$suffix</td>
                <td><p>HTML end tag</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_error_array">
        <div class="location">in <a href="CI_Form_validation.html#method_error_array">
CI_Form_validation</a> at line 359</div>
        <code>                    array
    <strong>error_array</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Array of Error Messages</p></p>                <p><p>Returns the error messages as an array</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_error_string">
        <div class="location">in <a href="CI_Form_validation.html#method_error_string">
CI_Form_validation</a> at line 375</div>
        <code>                    string
    <strong>error_string</strong>($prefix = &#039;&#039;, $suffix = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Error String</p></p>                <p><p>Returns the error messages as a string, wrapped in the error delimiters</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$prefix</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$suffix</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_run">
        <div class="location">at line 25</div>
        <code>                    bool
    <strong>run</strong>(string $group = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Run the Validator</p></p>                <p><p>This function does all the work.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$group</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__prepare_rules">
        <div class="location">in <a href="CI_Form_validation.html#method__prepare_rules">
CI_Form_validation</a> at line 509</div>
        <code>            protected        array
    <strong>_prepare_rules</strong>(array $rules)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prepare rules</p></p>                <p><p>Re-orders the provided rules in order of importance, so that
they can easily be executed later without weird checks ...</p>
<p>&quot;Callbacks&quot; are given the highest priority (always called),
followed by 'required' (called if callbacks didn't fail),
and then every next rule depends on the previous one passing.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$rules</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__reduce_array">
        <div class="location">in <a href="CI_Form_validation.html#method__reduce_array">
CI_Form_validation</a> at line 561</div>
        <code>            protected        mixed
    <strong>_reduce_array</strong>($array, $keys, $i = 0)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Traverse a multidimensional $_POST array index until the data is found</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$array</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$keys</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$i</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__reset_post_array">
        <div class="location">in <a href="CI_Form_validation.html#method__reset_post_array">
CI_Form_validation</a> at line 579</div>
        <code>            protected        void
    <strong>_reset_post_array</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Re-populate the _POST array with our finalized and processed data</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__execute">
        <div class="location">in <a href="CI_Form_validation.html#method__execute">
CI_Form_validation</a> at line 624</div>
        <code>            protected        mixed
    <strong>_execute</strong>($row, $rules, $postdata = NULL, $cycles = 0)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Executes the Validation routines</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$row</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$rules</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$postdata</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$cycles</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_error_message">
        <div class="location">in <a href="CI_Form_validation.html#method__get_error_message">
CI_Form_validation</a> at line 832</div>
        <code>            protected        string
    <strong>_get_error_message</strong>(string $rule, string $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the error message for the rule</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$rule</td>
                <td><p>The rule name</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$field</td>
                <td><p>The field name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__translate_fieldname">
        <div class="location">in <a href="CI_Form_validation.html#method__translate_fieldname">
CI_Form_validation</a> at line 865</div>
        <code>            protected        string
    <strong>_translate_fieldname</strong>($fieldname)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Translate a field name</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$fieldname</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__build_error_msg">
        <div class="location">in <a href="CI_Form_validation.html#method__build_error_msg">
CI_Form_validation</a> at line 887</div>
        <code>            protected        string
    <strong>_build_error_msg</strong>($line, $field = &#039;&#039;, $param = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Build an error message using the field and param.</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$line</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$field</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$param</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_has_rule">
        <div class="location">in <a href="CI_Form_validation.html#method_has_rule">
CI_Form_validation</a> at line 908</div>
        <code>                    bool
    <strong>has_rule</strong>($field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Checks if the rule is present within the validator</p></p>                <p><p>Permits you to check if a rule is present within the validator</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$field</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_value">
        <div class="location">in <a href="CI_Form_validation.html#method_set_value">
CI_Form_validation</a> at line 925</div>
        <code>                    string
    <strong>set_value</strong>($field = &#039;&#039;, $default = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the value from a form</p></p>                <p><p>Permits you to repopulate a form field with the value it was submitted
with, or, if that value doesn't exist, with the default</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$field</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$default</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_select">
        <div class="location">in <a href="CI_Form_validation.html#method_set_select">
CI_Form_validation</a> at line 955</div>
        <code>                    string
    <strong>set_select</strong>($field = &#039;&#039;, $value = &#039;&#039;, $default = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Select</p></p>                <p><p>Enables pull-down lists to be set to the value the user
selected in the event of an error</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$field</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$value</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$default</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_radio">
        <div class="location">in <a href="CI_Form_validation.html#method_set_radio">
CI_Form_validation</a> at line 998</div>
        <code>                    string
    <strong>set_radio</strong>($field = &#039;&#039;, $value = &#039;&#039;, $default = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Radio</p></p>                <p><p>Enables radio buttons to be set to the value the user
selected in the event of an error</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$field</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$value</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$default</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_checkbox">
        <div class="location">in <a href="CI_Form_validation.html#method_set_checkbox">
CI_Form_validation</a> at line 1041</div>
        <code>                    string
    <strong>set_checkbox</strong>($field = &#039;&#039;, $value = &#039;&#039;, $default = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Checkbox</p></p>                <p><p>Enables checkboxes to be set to the value the user
selected in the event of an error</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$field</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$value</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$default</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_required">
        <div class="location">in <a href="CI_Form_validation.html#method_required">
CI_Form_validation</a> at line 1055</div>
        <code>                    bool
    <strong>required</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Required</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_regex_match">
        <div class="location">in <a href="CI_Form_validation.html#method_regex_match">
CI_Form_validation</a> at line 1071</div>
        <code>                    bool
    <strong>regex_match</strong>($str, $regex)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Performs a Regular Expression match test.</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$regex</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_matches">
        <div class="location">in <a href="CI_Form_validation.html#method_matches">
CI_Form_validation</a> at line 1085</div>
        <code>                    bool
    <strong>matches</strong>(string $str, string $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Match one field to another</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td><p>string to compare against</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$field</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_differs">
        <div class="location">in <a href="CI_Form_validation.html#method_differs">
CI_Form_validation</a> at line 1101</div>
        <code>                    bool
    <strong>differs</strong>($str, $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Differs from another field</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$field</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_unique">
        <div class="location">in <a href="CI_Form_validation.html#method_is_unique">
CI_Form_validation</a> at line 1118</div>
        <code>                    bool
    <strong>is_unique</strong>(string $str, string $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is Unique</p></p>                <p><p>Check if the input value doesn't already exist
in the specified database field.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$field</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_min_length">
        <div class="location">in <a href="CI_Form_validation.html#method_min_length">
CI_Form_validation</a> at line 1135</div>
        <code>                    bool
    <strong>min_length</strong>($str, $val)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Minimum Length</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$val</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_max_length">
        <div class="location">in <a href="CI_Form_validation.html#method_max_length">
CI_Form_validation</a> at line 1154</div>
        <code>                    bool
    <strong>max_length</strong>($str, $val)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Max Length</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$val</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_exact_length">
        <div class="location">in <a href="CI_Form_validation.html#method_exact_length">
CI_Form_validation</a> at line 1173</div>
        <code>                    bool
    <strong>exact_length</strong>($str, $val)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Exact Length</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$val</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_valid_url">
        <div class="location">in <a href="CI_Form_validation.html#method_valid_url">
CI_Form_validation</a> at line 1191</div>
        <code>                    bool
    <strong>valid_url</strong>(string $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Valid URL</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_valid_email">
        <div class="location">in <a href="CI_Form_validation.html#method_valid_email">
CI_Form_validation</a> at line 1237</div>
        <code>                    bool
    <strong>valid_email</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Valid Email</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_valid_emails">
        <div class="location">in <a href="CI_Form_validation.html#method_valid_emails">
CI_Form_validation</a> at line 1262</div>
        <code>                    bool
    <strong>valid_emails</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Valid Emails</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_valid_ip">
        <div class="location">in <a href="CI_Form_validation.html#method_valid_ip">
CI_Form_validation</a> at line 1289</div>
        <code>                    bool
    <strong>valid_ip</strong>($ip, $which = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validate IP Address</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$ip</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$which</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_alpha">
        <div class="location">in <a href="CI_Form_validation.html#method_alpha">
CI_Form_validation</a> at line 1302</div>
        <code>                    bool
    <strong>alpha</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Alpha</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_alpha_numeric">
        <div class="location">in <a href="CI_Form_validation.html#method_alpha_numeric">
CI_Form_validation</a> at line 1315</div>
        <code>                    bool
    <strong>alpha_numeric</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Alpha-numeric</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_alpha_numeric_spaces">
        <div class="location">in <a href="CI_Form_validation.html#method_alpha_numeric_spaces">
CI_Form_validation</a> at line 1328</div>
        <code>                    bool
    <strong>alpha_numeric_spaces</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Alpha-numeric w/ spaces</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_alpha_dash">
        <div class="location">in <a href="CI_Form_validation.html#method_alpha_dash">
CI_Form_validation</a> at line 1341</div>
        <code>                    bool
    <strong>alpha_dash</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Alpha-numeric with underscores and dashes</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_numeric">
        <div class="location">in <a href="CI_Form_validation.html#method_numeric">
CI_Form_validation</a> at line 1354</div>
        <code>                    bool
    <strong>numeric</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Numeric</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_integer">
        <div class="location">in <a href="CI_Form_validation.html#method_integer">
CI_Form_validation</a> at line 1368</div>
        <code>                    bool
    <strong>integer</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Integer</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_decimal">
        <div class="location">in <a href="CI_Form_validation.html#method_decimal">
CI_Form_validation</a> at line 1381</div>
        <code>                    bool
    <strong>decimal</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Decimal number</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_greater_than">
        <div class="location">in <a href="CI_Form_validation.html#method_greater_than">
CI_Form_validation</a> at line 1395</div>
        <code>                    bool
    <strong>greater_than</strong>($str, $min)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Greater than</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$min</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_greater_than_equal_to">
        <div class="location">in <a href="CI_Form_validation.html#method_greater_than_equal_to">
CI_Form_validation</a> at line 1409</div>
        <code>                    bool
    <strong>greater_than_equal_to</strong>($str, $min)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Equal to or Greater than</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$min</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_less_than">
        <div class="location">in <a href="CI_Form_validation.html#method_less_than">
CI_Form_validation</a> at line 1423</div>
        <code>                    bool
    <strong>less_than</strong>($str, $max)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Less than</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$max</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_less_than_equal_to">
        <div class="location">in <a href="CI_Form_validation.html#method_less_than_equal_to">
CI_Form_validation</a> at line 1437</div>
        <code>                    bool
    <strong>less_than_equal_to</strong>($str, $max)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Equal to or Less than</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$max</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_in_list">
        <div class="location">in <a href="CI_Form_validation.html#method_in_list">
CI_Form_validation</a> at line 1451</div>
        <code>                    bool
    <strong>in_list</strong>($value, $list)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Value should be within an array of values</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$value</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$list</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_natural">
        <div class="location">in <a href="CI_Form_validation.html#method_is_natural">
CI_Form_validation</a> at line 1464</div>
        <code>                    bool
    <strong>is_natural</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is a Natural number  (0,1,2,3, etc.)</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_natural_no_zero">
        <div class="location">in <a href="CI_Form_validation.html#method_is_natural_no_zero">
CI_Form_validation</a> at line 1477</div>
        <code>                    bool
    <strong>is_natural_no_zero</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is a Natural number, but not a zero  (1,2,3, etc.)</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_valid_base64">
        <div class="location">in <a href="CI_Form_validation.html#method_valid_base64">
CI_Form_validation</a> at line 1493</div>
        <code>                    bool
    <strong>valid_base64</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Valid Base64</p></p>                <p><p>Tests a string for characters outside of the Base64 alphabet
as defined by RFC 2045 <a href="http://www.faqs.org/rfcs/rfc2045">http://www.faqs.org/rfcs/rfc2045</a></p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_prep_for_form">
        <div class="location">in <a href="CI_Form_validation.html#method_prep_for_form">
CI_Form_validation</a> at line 1510</div>
        <code>                    mixed
    <strong>prep_for_form</strong>(mixed $data)
        <small><span class="label label-danger">deprecated</span></small></code>
    </h3>
    <div class="details">    
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td>3.0.6</td>
                    <td>Not used anywhere within the framework and pretty much useless</td>
                </tr>
                    </p>
    
            

        <div class="method-description">
                            <p><p>Prep data for form</p></p>                <p><p>This function allows HTML to be safely shown in a form.
Special characters are converted.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$data</td>
                <td><p>Input data</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_prep_url">
        <div class="location">in <a href="CI_Form_validation.html#method_prep_url">
CI_Form_validation</a> at line 1538</div>
        <code>                    string
    <strong>prep_url</strong>($str = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prep URL</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_strip_image_tags">
        <div class="location">in <a href="CI_Form_validation.html#method_strip_image_tags">
CI_Form_validation</a> at line 1561</div>
        <code>                    string
    <strong>strip_image_tags</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Strip Image Tags</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_encode_php_tags">
        <div class="location">in <a href="CI_Form_validation.html#method_encode_php_tags">
CI_Form_validation</a> at line 1574</div>
        <code>                    string
    <strong>encode_php_tags</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Convert PHP tags to entities</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_reset_validation">
        <div class="location">in <a href="CI_Form_validation.html#method_reset_validation">
CI_Form_validation</a> at line 1589</div>
        <code>                    <a href="CI_Form_validation.html">CI_Form_validation</a>
    <strong>reset_validation</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Reset validation vars</p></p>                <p><p>Prevents subsequent validation routines from being affected by the
results of any previous validation routine due to the CI singleton.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Form_validation.html">CI_Form_validation</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_errors_array">
        <div class="location">at line 139</div>
        <code>                    mixed
    <strong>errors_array</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Custom method for error messages in array</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

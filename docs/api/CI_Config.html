<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Config | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Config" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Config    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Config</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Config Class</p></p>            <p><p>This class contains functions that enable config files to be managed</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_config">
                                                                                array
                                                                                
                                    </td>
                <td>$config</td>
                <td class="last"><p>List of all loaded config values</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_is_loaded">
                                                                                array
                                                                                
                                    </td>
                <td>$is_loaded</td>
                <td class="last"><p>List of all loaded config files</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__config_paths">
                                                                                array
                                                                                
                                    </td>
                <td>$_config_paths</td>
                <td class="last"><p>List of paths to search when trying to load a config file.</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_load">load</a>(string $file = &#039;&#039;, bool $use_sections = FALSE, bool $fail_gracefully = FALSE)
        
                                            <p><p>Load Config File</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string|null
                </div>
                <div class="col-md-8">
                    <a href="#method_item">item</a>(string $item, string $index = &#039;&#039;)
        
                                            <p><p>Fetch a config file item</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string|null
                </div>
                <div class="col-md-8">
                    <a href="#method_slash_item">slash_item</a>(string $item)
        
                                            <p><p>Fetch a config file item with slash appended (if not empty)</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_site_url">site_url</a>(string|string[] $uri = &#039;&#039;, string $protocol = NULL)
        
                                            <p><p>Site URL</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_base_url">base_url</a>(string|string[] $uri = &#039;&#039;, string $protocol = NULL)
        
                                            <p><p>Base URL</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__uri_string">_uri_string</a>(string|string[] $uri)
        
                                            <p><p>Build URI string</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_system_url">system_url</a>()
        <small><span class="label label-danger">deprecated</span></small>
                                            <p><p>System URL</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_item">set_item</a>(string $item, string $value)
        
                                            <p><p>Set a config file item</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 84</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                <p><p>Sets the $config data from the primary config.php file as a class variable.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_load">
        <div class="location">at line 126</div>
        <code>                    bool
    <strong>load</strong>(string $file = &#039;&#039;, bool $use_sections = FALSE, bool $fail_gracefully = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load Config File</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$file</td>
                <td><p>Configuration file name</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$use_sections</td>
                <td><p>Whether configuration values should be loaded into their own section</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$fail_gracefully</td>
                <td><p>Whether to just return FALSE or display an error message</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td><p>TRUE if the file was loaded correctly or FALSE on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_item">
        <div class="location">at line 197</div>
        <code>                    string|null
    <strong>item</strong>(string $item, string $index = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch a config file item</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$item</td>
                <td><p>Config item name</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$index</td>
                <td><p>Index name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string|null</td>
            <td><p>The configuration item or NULL if the item doesn't exist</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_slash_item">
        <div class="location">at line 215</div>
        <code>                    string|null
    <strong>slash_item</strong>(string $item)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch a config file item with slash appended (if not empty)</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$item</td>
                <td><p>Config item name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string|null</td>
            <td><p>The configuration item or NULL if the item doesn't exist</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_site_url">
        <div class="location">at line 242</div>
        <code>                    string
    <strong>site_url</strong>(string|string[] $uri = &#039;&#039;, string $protocol = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Site URL</p></p>                <p><p>Returns base_url . index_page [. uri_string]</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string|string[]</td>
                <td>$uri</td>
                <td><p>URI string or an array of segments</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$protocol</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_base_url">
        <div class="location">at line 305</div>
        <code>                    string
    <strong>base_url</strong>(string|string[] $uri = &#039;&#039;, string $protocol = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Base URL</p></p>                <p><p>Returns base_url [. uri_string]</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string|string[]</td>
                <td>$uri</td>
                <td><p>URI string or an array of segments</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$protocol</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__uri_string">
        <div class="location">at line 336</div>
        <code>            protected        string
    <strong>_uri_string</strong>(string|string[] $uri)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Build URI string</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string|string[]</td>
                <td>$uri</td>
                <td><p>URI string or an array of segments</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_system_url">
        <div class="location">at line 359</div>
        <code>                    string
    <strong>system_url</strong>()
        <small><span class="label label-danger">deprecated</span></small></code>
    </h3>
    <div class="details">    
            <p>
                <small><span class="label label-danger">deprecated</span></small>
                            <tr>
                    <td>3.0.0</td>
                    <td>Encourages insecure practices</td>
                </tr>
                    </p>
    
            

        <div class="method-description">
                            <p><p>System URL</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_item">
        <div class="location">at line 374</div>
        <code>                    void
    <strong>set_item</strong>(string $item, string $value)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set a config file item</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$item</td>
                <td><p>Config item key</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$value</td>
                <td><p>Config item value</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_URI | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_URI" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_URI    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_URI</strong>
</p>

        
    
        

            <div class="description">
            <p><p>URI Class</p></p>            <p><p>Parses URIs and determines routing</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_keyval">
                                                                                array
                                                                                
                                    </td>
                <td>$keyval</td>
                <td class="last"><p>List of cached URI segments</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_uri_string">
                                                                                string
                                                                                
                                    </td>
                <td>$uri_string</td>
                <td class="last"><p>Current URI string</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_segments">
                                                                                array
                                                                                
                                    </td>
                <td>$segments</td>
                <td class="last"><p>List of URI segments</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_rsegments">
                                                                                array
                                                                                
                                    </td>
                <td>$rsegments</td>
                <td class="last"><p>List of routed URI segments</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__permitted_uri_chars">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_permitted_uri_chars</td>
                <td class="last"><p>Permitted URI chars</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__set_uri_string">_set_uri_string</a>(string $str)
        
                                            <p><p>Set URI String</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__parse_request_uri">_parse_request_uri</a>()
        
                                            <p><p>Parse REQUEST_URI</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__parse_query_string">_parse_query_string</a>()
        
                                            <p><p>Parse QUERY_STRING</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__parse_argv">_parse_argv</a>()
        
                                            <p><p>Parse CLI arguments</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__remove_relative_directory">_remove_relative_directory</a>(string $uri)
        
                                            <p><p>Remove relative directory (../) and multi slashes (///)</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_filter_uri">filter_uri</a>(string $str)
        
                                            <p><p>Filter URI</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_segment">segment</a>(int $n, mixed $no_result = NULL)
        
                                            <p><p>Fetch URI Segment</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_rsegment">rsegment</a>(int $n, mixed $no_result = NULL)
        
                                            <p><p>Fetch URI &quot;routed&quot; Segment</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_uri_to_assoc">uri_to_assoc</a>(int $n = 3, array $default = array())
        
                                            <p><p>URI to assoc</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_ruri_to_assoc">ruri_to_assoc</a>(int $n = 3, array $default = array())
        
                                            <p><p>Routed URI to assoc</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method__uri_to_assoc">_uri_to_assoc</a>(int $n = 3, array $default = array(), string $which = &#039;segment&#039;)
        
                                            <p><p>Internal URI-to-assoc</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_assoc_to_uri">assoc_to_uri</a>(array $array)
        
                                            <p><p>Assoc to URI</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_slash_segment">slash_segment</a>(int $n, string $where = &#039;trailing&#039;)
        
                                            <p><p>Slash segment</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_slash_rsegment">slash_rsegment</a>(int $n, string $where = &#039;trailing&#039;)
        
                                            <p><p>Slash routed segment</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__slash_segment">_slash_segment</a>(int $n, string $where = &#039;trailing&#039;, string $which = &#039;segment&#039;)
        
                                            <p><p>Internal Slash segment</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_segment_array">segment_array</a>()
        
                                            <p><p>Segment Array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_rsegment_array">rsegment_array</a>()
        
                                            <p><p>Routed Segment Array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_total_segments">total_segments</a>()
        
                                            <p><p>Total number of segments</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_total_rsegments">total_rsegments</a>()
        
                                            <p><p>Total number of routed segments</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_uri_string">uri_string</a>()
        
                                            <p><p>Fetch URI string</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_ruri_string">ruri_string</a>()
        
                                            <p><p>Fetch Re-routed URI string</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 99</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_uri_string">
        <div class="location">at line 151</div>
        <code>            protected        void
    <strong>_set_uri_string</strong>(string $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set URI String</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__parse_request_uri">
        <div class="location">at line 197</div>
        <code>            protected        string
    <strong>_parse_request_uri</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Parse REQUEST_URI</p></p>                <p><p>Will parse REQUEST_URI and automatically detect the URI from it,
while fixing the query string if necessary.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__parse_query_string">
        <div class="location">at line 255</div>
        <code>            protected        string
    <strong>_parse_query_string</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Parse QUERY_STRING</p></p>                <p><p>Will parse QUERY_STRING and automatically detect the URI from it.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__parse_argv">
        <div class="location">at line 284</div>
        <code>            protected        string
    <strong>_parse_argv</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Parse CLI arguments</p></p>                <p><p>Take each command line argument and assume it is a URI segment.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__remove_relative_directory">
        <div class="location">at line 300</div>
        <code>            protected        string
    <strong>_remove_relative_directory</strong>(string $uri)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Remove relative directory (../) and multi slashes (///)</p></p>                <p><p>Do some final cleaning of the URI and return it, currently only used in self::_parse_request_uri()</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$uri</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_filter_uri">
        <div class="location">at line 326</div>
        <code>                    void
    <strong>filter_uri</strong>(string $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Filter URI</p></p>                <p><p>Filters segments for malicious characters.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_segment">
        <div class="location">at line 344</div>
        <code>                    mixed
    <strong>segment</strong>(int $n, mixed $no_result = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch URI Segment</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td>Index</td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$no_result</td>
                <td><p>What to return if the segment index is not found</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
                            <h4>See also</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>
                                            CI_URI::$segments
                                    </td>
                <td></td>
            </tr>
            </table>

            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_rsegment">
        <div class="location">at line 364</div>
        <code>                    mixed
    <strong>rsegment</strong>(int $n, mixed $no_result = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch URI &quot;routed&quot; Segment</p></p>                <p><p>Returns the re-routed URI segment (assuming routing rules are used)
based on the index provided. If there is no routing, will return
the same result as CI_URI::segment().</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td>Index</td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$no_result</td>
                <td><p>What to return if the segment index is not found</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
                            <h4>See also</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>
                                            CI_URI::$rsegments
                                    </td>
                <td></td>
            </tr>
                    <tr>
                <td>
                                            <a href="CI_URI.html#method_segment">
CI_URI::segment</a>
                                    </td>
                <td></td>
            </tr>
            </table>

            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_uri_to_assoc">
        <div class="location">at line 391</div>
        <code>                    array
    <strong>uri_to_assoc</strong>(int $n = 3, array $default = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>URI to assoc</p></p>                <p><p>Generates an associative array of URI data starting at the supplied
segment index. For example, if this is your URI:</p>
<p>example.com/user/search/name/joe/location/UK/gender/male</p>
<p>You can use this method to generate an array with this prototype:</p>
<p>array (
name =&gt; joe
location =&gt; UK
gender =&gt; male
)</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td><p>Index (default: 3)</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$default</td>
                <td><p>Default values</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_ruri_to_assoc">
        <div class="location">at line 409</div>
        <code>                    array
    <strong>ruri_to_assoc</strong>(int $n = 3, array $default = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Routed URI to assoc</p></p>                <p><p>Identical to CI_URI::uri_to_assoc(), only it uses the re-routed
segment array.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td><p>Index (default: 3)</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$default</td>
                <td><p>Default values</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
                            <h4>See also</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>
                                            <a href="CI_URI.html#method_uri_to_assoc">
CI_URI::uri_to_assoc</a>
                                    </td>
                <td></td>
            </tr>
            </table>

            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__uri_to_assoc">
        <div class="location">at line 428</div>
        <code>            protected        array
    <strong>_uri_to_assoc</strong>(int $n = 3, array $default = array(), string $which = &#039;segment&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Internal URI-to-assoc</p></p>                <p><p>Generates a key/value pair from the URI string or re-routed URI string.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td><p>Index (default: 3)</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$default</td>
                <td><p>Default values</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$which</td>
                <td><p>Array name ('segment' or 'rsegment')</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_assoc_to_uri">
        <div class="location">at line 496</div>
        <code>                    string
    <strong>assoc_to_uri</strong>(array $array)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Assoc to URI</p></p>                <p><p>Generates a URI string from an associative array.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$array</td>
                <td><p>Input array of key/value pairs</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>URI string</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_slash_segment">
        <div class="location">at line 519</div>
        <code>                    string
    <strong>slash_segment</strong>(int $n, string $where = &#039;trailing&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Slash segment</p></p>                <p><p>Fetches an URI segment with a slash.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td>Index</td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$where</td>
                <td><p>Where to add the slash ('trailing' or 'leading')</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_slash_rsegment">
        <div class="location">at line 535</div>
        <code>                    string
    <strong>slash_rsegment</strong>(int $n, string $where = &#039;trailing&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Slash routed segment</p></p>                <p><p>Fetches an URI routed segment with a slash.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td>Index</td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$where</td>
                <td><p>Where to add the slash ('trailing' or 'leading')</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__slash_segment">
        <div class="location">at line 555</div>
        <code>            protected        string
    <strong>_slash_segment</strong>(int $n, string $where = &#039;trailing&#039;, string $which = &#039;segment&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Internal Slash segment</p></p>                <p><p>Fetches an URI Segment and adds a slash to it.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td>Index</td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$where</td>
                <td><p>Where to add the slash ('trailing' or 'leading')</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$which</td>
                <td><p>Array name ('segment' or 'rsegment')</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_segment_array">
        <div class="location">at line 578</div>
        <code>                    array
    <strong>segment_array</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Segment Array</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td>CI_URI::$segments</td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_rsegment_array">
        <div class="location">at line 590</div>
        <code>                    array
    <strong>rsegment_array</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Routed Segment Array</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td>CI_URI::$rsegments</td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_total_segments">
        <div class="location">at line 602</div>
        <code>                    int
    <strong>total_segments</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Total number of segments</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_total_rsegments">
        <div class="location">at line 614</div>
        <code>                    int
    <strong>total_rsegments</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Total number of routed segments</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_uri_string">
        <div class="location">at line 626</div>
        <code>                    string
    <strong>uri_string</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch URI string</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td>CI_URI::$uri_string</td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_ruri_string">
        <div class="location">at line 638</div>
        <code>                    string
    <strong>ruri_string</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch Re-routed URI string</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

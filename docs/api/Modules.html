<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Modules | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Modules" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Modules    
            </h1>
    </div>

    
    <p>        class
    <strong>Modules</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Modular Extensions - HMVC</p></p>            <p><p>Adapted from the CodeIgniter Core Classes</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_routes">
                    static                                                            
                                                                                
                                    </td>
                <td>$routes</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_registry">
                    static                                                            
                                                                                
                                    </td>
                <td>$registry</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_locations">
                    static                                                            
                                                                                
                                    </td>
                <td>$locations</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_run">run</a>($module)
        
                                            <p><p>Run a module controller method
Output from module is buffered and returned.</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_load">load</a>($module)
        
                                            <p><p>Load a module controller *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_autoload">autoload</a>($class)
        
                                            <p><p>Library base class autoload *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_load_file">load_file</a>($file, $path, $type = &#039;other&#039;, $result = true)
        
                                            <p><p>Load a module file *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_find">find</a>($file, $module, $base)
        
                                            <p><p>Find a file
Scans for files located within modules directories.</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;
                </div>
                <div class="col-md-8">
                    <a href="#method_parse_routes">parse_routes</a>($module, $uri)
        
                                            <p><p>Parse module routes *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method_run">
        <div class="location">at line 62</div>
        <code>        static            
    <strong>run</strong>($module)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Run a module controller method
Output from module is buffered and returned.</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$module</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_load">
        <div class="location">at line 86</div>
        <code>        static            
    <strong>load</strong>($module)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load a module controller *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$module</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_autoload">
        <div class="location">at line 124</div>
        <code>        static            
    <strong>autoload</strong>($class)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Library base class autoload *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$class</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_load_file">
        <div class="location">at line 157</div>
        <code>        static            
    <strong>load_file</strong>($file, $path, $type = &#039;other&#039;, $result = true)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load a module file *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$file</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$path</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$type</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$result</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_find">
        <div class="location">at line 190</div>
        <code>        static            
    <strong>find</strong>($file, $module, $base)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Find a file
Scans for files located within modules directories.</p></p>                <p><p>Also scans application directories for models, plugins and views.
Generates fatal error if file not found.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$file</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$module</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$base</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_parse_routes">
        <div class="location">at line 223</div>
        <code>        static            
    <strong>parse_routes</strong>($module, $uri)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Parse module routes *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$module</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$uri</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

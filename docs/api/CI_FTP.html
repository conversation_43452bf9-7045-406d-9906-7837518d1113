<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_FTP | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_FTP" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_FTP    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_FTP</strong>
</p>

        
    
        

            <div class="description">
            <p><p>FTP Class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_hostname">
                                                                                string
                                                                                
                                    </td>
                <td>$hostname</td>
                <td class="last"><p>FTP Server hostname</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_username">
                                                                                string
                                                                                
                                    </td>
                <td>$username</td>
                <td class="last"><p>FTP Username</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_password">
                                                                                string
                                                                                
                                    </td>
                <td>$password</td>
                <td class="last"><p>FTP Password</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_port">
                                                                                int
                                                                                
                                    </td>
                <td>$port</td>
                <td class="last"><p>FTP Server port</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_passive">
                                                                                bool
                                                                                
                                    </td>
                <td>$passive</td>
                <td class="last"><p>Passive mode flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_debug">
                                                                                bool
                                                                                
                                    </td>
                <td>$debug</td>
                <td class="last"><p>Debug flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_conn_id">
                                        protected                                        resource
                                                                                
                                    </td>
                <td>$conn_id</td>
                <td class="last"><p>Connection ID</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $config = array())
        
                                            <p>Constructor</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_initialize">initialize</a>(array $config = array())
        
                                            <p><p>Initialize preferences</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_connect">connect</a>(array $config = array())
        
                                            <p><p>FTP Connect</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__login">_login</a>()
        
                                            <p><p>FTP Login</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__is_conn">_is_conn</a>()
        
                                            <p><p>Validates the connection ID</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_changedir">changedir</a>(string $path, bool $suppress_debug = FALSE)
        
                                            <p><p>Change directory</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_mkdir">mkdir</a>(string $path, int $permissions = NULL)
        
                                            <p><p>Create a directory</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_upload">upload</a>(string $locpath, string $rempath, string $mode = &#039;auto&#039;, int $permissions = NULL)
        
                                            <p><p>Upload a file to the server</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_download">download</a>(string $rempath, string $locpath, string $mode = &#039;auto&#039;)
        
                                            <p><p>Download a file from a remote server to the local server</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_rename">rename</a>(string $old_file, string $new_file, bool $move = FALSE)
        
                                            <p><p>Rename (or move) a file</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_move">move</a>(string $old_file, string $new_file)
        
                                            <p><p>Move a file</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_delete_file">delete_file</a>(string $filepath)
        
                                            <p><p>Rename (or move) a file</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_delete_dir">delete_dir</a>(string $filepath)
        
                                            <p><p>Delete a folder and recursively delete everything (including sub-folders)
contained within it.</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_chmod">chmod</a>(string $path, int $perm)
        
                                            <p><p>Set file permissions</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_list_files">list_files</a>(string $path = &#039;.&#039;)
        
                                            <p><p>FTP List files in the specified directory</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_mirror">mirror</a>(string $locpath, string $rempath)
        
                                            <p><p>Read a directory and recreate it remotely</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__getext">_getext</a>(string $filename)
        
                                            <p><p>Extract the file extension</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__settype">_settype</a>(string $ext)
        
                                            <p><p>Set the upload type</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_close">close</a>()
        
                                            <p><p>Close the connection</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__error">_error</a>(string $line)
        
                                            <p><p>Display error message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 112</div>
        <code>                    void
    <strong>__construct</strong>(array $config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Constructor</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initialize">
        <div class="location">at line 126</div>
        <code>                    void
    <strong>initialize</strong>(array $config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize preferences</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_connect">
        <div class="location">at line 148</div>
        <code>                    bool
    <strong>connect</strong>(array $config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>FTP Connect</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td><p>Connection values</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__login">
        <div class="location">at line 191</div>
        <code>            protected        bool
    <strong>_login</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>FTP Login</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__is_conn">
        <div class="location">at line 203</div>
        <code>            protected        bool
    <strong>_is_conn</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validates the connection ID</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_changedir">
        <div class="location">at line 233</div>
        <code>                    bool
    <strong>changedir</strong>(string $path, bool $suppress_debug = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Change directory</p></p>                <p><p>The second parameter lets us momentarily turn off debugging so that
this function can be used to test for the existence of a folder
without throwing an error. There's no FTP equivalent to is_dir()
so we do it by trying to change to a particular directory.
Internally, this parameter is only used by the &quot;mirror&quot; function below.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$suppress_debug</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mkdir">
        <div class="location">at line 264</div>
        <code>                    bool
    <strong>mkdir</strong>(string $path, int $permissions = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create a directory</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$permissions</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_upload">
        <div class="location">at line 303</div>
        <code>                    bool
    <strong>upload</strong>(string $locpath, string $rempath, string $mode = &#039;auto&#039;, int $permissions = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Upload a file to the server</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$locpath</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$rempath</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$mode</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$permissions</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_download">
        <div class="location">at line 357</div>
        <code>                    bool
    <strong>download</strong>(string $rempath, string $locpath, string $mode = &#039;auto&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Download a file from a remote server to the local server</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$rempath</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$locpath</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$mode</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_rename">
        <div class="location">at line 399</div>
        <code>                    bool
    <strong>rename</strong>(string $old_file, string $new_file, bool $move = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Rename (or move) a file</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$old_file</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$new_file</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$move</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_move">
        <div class="location">at line 430</div>
        <code>                    bool
    <strong>move</strong>(string $old_file, string $new_file)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Move a file</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$old_file</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$new_file</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_delete_file">
        <div class="location">at line 443</div>
        <code>                    bool
    <strong>delete_file</strong>(string $filepath)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Rename (or move) a file</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$filepath</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_delete_dir">
        <div class="location">at line 474</div>
        <code>                    bool
    <strong>delete_dir</strong>(string $filepath)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Delete a folder and recursively delete everything (including sub-folders)
contained within it.</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$filepath</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_chmod">
        <div class="location">at line 520</div>
        <code>                    bool
    <strong>chmod</strong>(string $path, int $perm)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set file permissions</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td><p>File path</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$perm</td>
                <td>Permissions</td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_list_files">
        <div class="location">at line 548</div>
        <code>                    array
    <strong>list_files</strong>(string $path = &#039;.&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>FTP List files in the specified directory</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mirror">
        <div class="location">at line 569</div>
        <code>                    bool
    <strong>mirror</strong>(string $locpath, string $rempath)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Read a directory and recreate it remotely</p></p>                <p><p>This function recursively reads a folder and everything it contains
(including sub-folders) and creates a mirror via FTP based on it.
Whatever the directory structure of the original file path will be
recreated on the server.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$locpath</td>
                <td><p>Path to source with trailing slash</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$rempath</td>
                <td><p>Path to destination - include the base folder with trailing slash</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__getext">
        <div class="location">at line 616</div>
        <code>            protected        string
    <strong>_getext</strong>(string $filename)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Extract the file extension</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$filename</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__settype">
        <div class="location">at line 631</div>
        <code>            protected        string
    <strong>_settype</strong>(string $ext)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the upload type</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$ext</td>
                <td><p>Filename extension</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_close">
        <div class="location">at line 645</div>
        <code>                    bool
    <strong>close</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Close the connection</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__error">
        <div class="location">at line 660</div>
        <code>            protected        void
    <strong>_error</strong>(string $line)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Display error message</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$line</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

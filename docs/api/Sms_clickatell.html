<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Sms_clickatell | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Sms_clickatell" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Sms_clickatell    
            </h1>
    </div>

    
    <p>        class
    <strong>Sms_clickatell</strong>        extends <a href="App_sms.html">App_sms</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_client">
                                        protected                                        
                                                                                
                                    </td>
                <td>$client</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_sms.html#property_client">
App_sms</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_sms.html#property_ci">
App_sms</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_trigger_being_sent">
                    static                                                            
                                                                                
                                    </td>
                <td>$trigger_being_sent</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_sms.html#property_trigger_being_sent">
App_sms</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_test_mode">
                                                                                
                                                                                
                                    </td>
                <td>$test_mode</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="App_sms.html#property_test_mode">
App_sms</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_add_gateway">add_gateway</a>($id, $data = [])
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_add_gateway">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_option">get_option</a>($id, $option)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_get_option">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_gateway">get_gateway</a>($id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_get_gateway">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_test_mode">set_test_mode</a>($value)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_set_test_mode">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_gateways">get_gateways</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_get_gateways">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_trigger_value">get_trigger_value</a>($trigger)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_get_trigger_value">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_add_trigger">add_trigger</a>($trigger)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_add_trigger">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_available_triggers">get_available_triggers</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_get_available_triggers">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_trigger">trigger</a>($trigger, $phone, $merge_fields = [])
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_trigger">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_parse_merge_fields">parse_merge_fields</a>(array $merge_fields, string $message)
        
                                            <p><p>Parse sms gateway merge fields
We will use the email templates merge fields function because they are the same</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_parse_merge_fields">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_option_name">option_name</a>($id, $option)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_option_name">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_trigger_option_name">trigger_option_name</a>($trigger)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_trigger_option_name">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_is_any_trigger_active">is_any_trigger_active</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_is_any_trigger_active">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_error">set_error</a>($error, $log_message = true)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_set_error">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_is_trigger_active">is_trigger_active</a>($trigger)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_is_trigger_active">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_active_gateway">get_active_gateway</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_get_active_gateway">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_logSuccess">logSuccess</a>(string $number, string $message)
        
                                            <p><p>Log success message</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_sms.html#method_logSuccess">
App_sms</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_send">send</a>($number, $message)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 11</div>
        <code>                    
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_gateway">
        <div class="location">in <a href="App_sms.html#method_add_gateway">
App_sms</a> at line 50</div>
        <code>                    
    <strong>add_gateway</strong>($id, $data = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_option">
        <div class="location">in <a href="App_sms.html#method_get_option">
App_sms</a> at line 66</div>
        <code>                    
    <strong>get_option</strong>($id, $option)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$option</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_gateway">
        <div class="location">in <a href="App_sms.html#method_get_gateway">
App_sms</a> at line 71</div>
        <code>                    
    <strong>get_gateway</strong>($id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_test_mode">
        <div class="location">in <a href="App_sms.html#method_set_test_mode">
App_sms</a> at line 78</div>
        <code>                    
    <strong>set_test_mode</strong>($value)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$value</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_gateways">
        <div class="location">in <a href="App_sms.html#method_get_gateways">
App_sms</a> at line 85</div>
        <code>                    
    <strong>get_gateways</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_trigger_value">
        <div class="location">in <a href="App_sms.html#method_get_trigger_value">
App_sms</a> at line 90</div>
        <code>                    
    <strong>get_trigger_value</strong>($trigger)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$trigger</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_trigger">
        <div class="location">in <a href="App_sms.html#method_add_trigger">
App_sms</a> at line 102</div>
        <code>                    
    <strong>add_trigger</strong>($trigger)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$trigger</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_available_triggers">
        <div class="location">in <a href="App_sms.html#method_get_available_triggers">
App_sms</a> at line 107</div>
        <code>                    
    <strong>get_available_triggers</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_trigger">
        <div class="location">in <a href="App_sms.html#method_trigger">
App_sms</a> at line 121</div>
        <code>                    
    <strong>trigger</strong>($trigger, $phone, $merge_fields = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$trigger</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$phone</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$merge_fields</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_parse_merge_fields">
        <div class="location">in <a href="App_sms.html#method_parse_merge_fields">
App_sms</a> at line 161</div>
        <code>                    string
    <strong>parse_merge_fields</strong>(array $merge_fields, string $message)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Parse sms gateway merge fields
We will use the email templates merge fields function because they are the same</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$merge_fields</td>
                <td><p>merge fields</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$message</td>
                <td><p>the message to bind the merge fields</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_option_name">
        <div class="location">in <a href="App_sms.html#method_option_name">
App_sms</a> at line 171</div>
        <code>                    
    <strong>option_name</strong>($id, $option)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$option</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_trigger_option_name">
        <div class="location">in <a href="App_sms.html#method_trigger_option_name">
App_sms</a> at line 176</div>
        <code>                    
    <strong>trigger_option_name</strong>($trigger)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$trigger</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_any_trigger_active">
        <div class="location">in <a href="App_sms.html#method_is_any_trigger_active">
App_sms</a> at line 181</div>
        <code>                    
    <strong>is_any_trigger_active</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_error">
        <div class="location">in <a href="App_sms.html#method_set_error">
App_sms</a> at line 196</div>
        <code>            protected        
    <strong>set_error</strong>($error, $log_message = true)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$error</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$log_message</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_trigger_active">
        <div class="location">in <a href="App_sms.html#method_is_trigger_active">
App_sms</a> at line 216</div>
        <code>                    
    <strong>is_trigger_active</strong>($trigger)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$trigger</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_active_gateway">
        <div class="location">in <a href="App_sms.html#method_get_active_gateway">
App_sms</a> at line 229</div>
        <code>                    
    <strong>get_active_gateway</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_logSuccess">
        <div class="location">in <a href="App_sms.html#method_logSuccess">
App_sms</a> at line 270</div>
        <code>            protected        void
    <strong>logSuccess</strong>(string $number, string $message)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Log success message</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$number</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$message</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send">
        <div class="location">at line 29</div>
        <code>                    
    <strong>send</strong>($number, $message)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$number</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$message</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

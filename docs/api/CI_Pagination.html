<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Pagination | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Pagination" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Pagination    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Pagination</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Pagination Class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_base_url">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$base_url</td>
                <td class="last"><p>Base URL</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_prefix">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$prefix</td>
                <td class="last">Prefix</td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_suffix">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$suffix</td>
                <td class="last">Suffix</td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_total_rows">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$total_rows</td>
                <td class="last"><p>Total number of items</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_num_links">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$num_links</td>
                <td class="last"><p>Number of links to show</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_per_page">
                                                                                int
                                                                                
                                    </td>
                <td>$per_page</td>
                <td class="last"><p>Items per page</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_cur_page">
                                                                                int
                                                                                
                                    </td>
                <td>$cur_page</td>
                <td class="last"><p>Current page</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_use_page_numbers">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$use_page_numbers</td>
                <td class="last"><p>Use page numbers flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_first_link">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$first_link</td>
                <td class="last"><p>First link</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_next_link">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$next_link</td>
                <td class="last"><p>Next link</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_prev_link">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$prev_link</td>
                <td class="last"><p>Previous link</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_last_link">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$last_link</td>
                <td class="last"><p>Last link</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_uri_segment">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$uri_segment</td>
                <td class="last"><p>URI Segment</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_full_tag_open">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$full_tag_open</td>
                <td class="last"><p>Full tag open</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_full_tag_close">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$full_tag_close</td>
                <td class="last"><p>Full tag close</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_first_tag_open">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$first_tag_open</td>
                <td class="last"><p>First tag open</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_first_tag_close">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$first_tag_close</td>
                <td class="last"><p>First tag close</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_last_tag_open">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$last_tag_open</td>
                <td class="last"><p>Last tag open</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_last_tag_close">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$last_tag_close</td>
                <td class="last"><p>Last tag close</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_first_url">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$first_url</td>
                <td class="last"><p>First URL</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_cur_tag_open">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$cur_tag_open</td>
                <td class="last"><p>Current tag open</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_cur_tag_close">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$cur_tag_close</td>
                <td class="last"><p>Current tag close</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_next_tag_open">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$next_tag_open</td>
                <td class="last"><p>Next tag open</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_next_tag_close">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$next_tag_close</td>
                <td class="last"><p>Next tag close</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_prev_tag_open">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$prev_tag_open</td>
                <td class="last"><p>Previous tag open</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_prev_tag_close">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$prev_tag_close</td>
                <td class="last"><p>Previous tag close</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_num_tag_open">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$num_tag_open</td>
                <td class="last"><p>Number tag open</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_num_tag_close">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$num_tag_close</td>
                <td class="last"><p>Number tag close</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_page_query_string">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$page_query_string</td>
                <td class="last"><p>Page query string flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_query_string_segment">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$query_string_segment</td>
                <td class="last"><p>Query string segment</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_display_pages">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$display_pages</td>
                <td class="last"><p>Display pages flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__attributes">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_attributes</td>
                <td class="last">Attributes</td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__link_types">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_link_types</td>
                <td class="last"><p>Link types</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_reuse_query_string">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$reuse_query_string</td>
                <td class="last"><p>Reuse query string flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_use_global_url_suffix">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$use_global_url_suffix</td>
                <td class="last"><p>Use global URL suffix flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_data_page_attr">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$data_page_attr</td>
                <td class="last"><p>Data page attribute</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_CI">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$CI</td>
                <td class="last"><p>CI Singleton</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $params = array())
        
                                            <p>Constructor</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Pagination.html">CI_Pagination</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_initialize">initialize</a>(array $params = array())
        
                                            <p><p>Initialize Preferences</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_create_links">create_links</a>()
        
                                            <p><p>Generate the pagination links</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__parse_attributes">_parse_attributes</a>(array $attributes)
        
                                            <p><p>Parse attributes</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__attr_rel">_attr_rel</a>(string $type)
        
                                            <p><p>Add &quot;rel&quot; attribute</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 330</div>
        <code>                    void
    <strong>__construct</strong>(array $params = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Constructor</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Initialization parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initialize">
        <div class="location">at line 358</div>
        <code>                    <a href="CI_Pagination.html">CI_Pagination</a>
    <strong>initialize</strong>(array $params = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize Preferences</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Initialization parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Pagination.html">CI_Pagination</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_create_links">
        <div class="location">at line 402</div>
        <code>                    string
    <strong>create_links</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Generate the pagination links</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__parse_attributes">
        <div class="location">at line 669</div>
        <code>            protected        void
    <strong>_parse_attributes</strong>(array $attributes)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Parse attributes</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$attributes</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__attr_rel">
        <div class="location">at line 693</div>
        <code>            protected        string
    <strong>_attr_rel</strong>(string $type)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add &quot;rel&quot; attribute</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

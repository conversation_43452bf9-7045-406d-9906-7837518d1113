<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>MX_Loader | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:MX_Loader" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>MX_Loader    
            </h1>
    </div>

    
    <p>        class
    <strong>MX_Loader</strong>        extends <a href="CI_Loader.html">CI_Loader</a>
</p>

        
    
        

            <div class="description">
            <p><p>Modular Extensions - HMVC</p></p>            <p><p>Adapted from the CodeIgniter Core Classes</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property__ci_ob_level">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$_ci_ob_level</td>
                <td class="last"><p>Nesting level of the output buffering mechanism</p></td>
                <td><small>from&nbsp;<a href="CI_Loader.html#property__ci_ob_level">
CI_Loader</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__ci_view_paths">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_ci_view_paths</td>
                <td class="last"><p>List of paths to load views from</p></td>
                <td><small>from&nbsp;<a href="CI_Loader.html#property__ci_view_paths">
CI_Loader</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__ci_library_paths">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_ci_library_paths</td>
                <td class="last"><p>List of paths to load libraries from</p></td>
                <td><small>from&nbsp;<a href="CI_Loader.html#property__ci_library_paths">
CI_Loader</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__ci_model_paths">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_ci_model_paths</td>
                <td class="last"><p>List of paths to load models from</p></td>
                <td><small>from&nbsp;<a href="CI_Loader.html#property__ci_model_paths">
CI_Loader</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__ci_helper_paths">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_ci_helper_paths</td>
                <td class="last"><p>List of paths to load helpers from</p></td>
                <td><small>from&nbsp;<a href="CI_Loader.html#property__ci_helper_paths">
CI_Loader</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__ci_cached_vars">
                                                                                
                                                                                
                                    </td>
                <td>$_ci_cached_vars</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__ci_classes">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_ci_classes</td>
                <td class="last"><p>List of loaded classes</p></td>
                <td><small>from&nbsp;<a href="CI_Loader.html#property__ci_classes">
CI_Loader</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__ci_models">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_ci_models</td>
                <td class="last"><p>List of loaded models</p></td>
                <td><small>from&nbsp;<a href="CI_Loader.html#property__ci_models">
CI_Loader</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__ci_helpers">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_ci_helpers</td>
                <td class="last"><p>List of loaded helpers</p></td>
                <td><small>from&nbsp;<a href="CI_Loader.html#property__ci_helpers">
CI_Loader</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__ci_varmap">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_ci_varmap</td>
                <td class="last"><p>List of class name mappings</p></td>
                <td><small>from&nbsp;<a href="CI_Loader.html#property__ci_varmap">
CI_Loader</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__module">
                                        protected                                        
                                                                                
                                    </td>
                <td>$_module</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__ci_plugins">
                                                                                
                                                                                
                                    </td>
                <td>$_ci_plugins</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method___construct">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_initialize">initialize</a>($controller = null)
        
                                            <p><p>Initialize the loader variables *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string|bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_loaded">is_loaded</a>(string $class)
        
                                            <p><p>Is Loaded</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_is_loaded">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_library">library</a>(mixed $library, array $params = null, string $object_name = null)
        
                                            <p><p>Load a module library *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_model">model</a>(mixed $model, $object_name = null, $connect = false)
        
                                            <p><p>Load a module model *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object|bool
                </div>
                <div class="col-md-8">
                    <a href="#method_database">database</a>(mixed $params = &#039;&#039;, bool $return = false, bool $query_builder = null)
        
                                            <p><p>Load the database drivers *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_dbutil">dbutil</a>(object $db = NULL, bool $return = FALSE)
        
                                            <p><p>Load the Database Utilities Class</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_dbutil">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_dbforge">dbforge</a>(object $db = NULL, bool $return = FALSE)
        
                                            <p><p>Load the Database Forge Class</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_dbforge">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object|string
                </div>
                <div class="col-md-8">
                    <a href="#method_view">view</a>(string $view, array $vars = [], bool $return = false)
        
                                            <p><p>Load a module view *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object|string
                </div>
                <div class="col-md-8">
                    <a href="#method_file">file</a>(string $path, bool $return = FALSE)
        
                                            <p><p>Generic File Loader</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_file">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_vars">vars</a>(array|object|string $vars, string $val = &#039;&#039;)
        
                                            <p><p>Set Variables</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_vars">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Loader.html">CI_Loader</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_clear_vars">clear_vars</a>()
        
                                            <p><p>Clear Cached Variables</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_clear_vars">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_get_var">get_var</a>(string $key)
        
                                            <p><p>Get Variable</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_get_var">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_vars">get_vars</a>()
        
                                            <p><p>Get Variables</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_get_vars">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_helper">helper</a>($helper = [])
        
                                            <p><p>Load a module helper *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_helpers">helpers</a>(string|string[] $helpers = [])
        
                                            <p><p>Load an array of helpers *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_language">language</a>($langfile, $idiom = &#039;&#039;, $return = false, $add_suffix = true, $alt_path = &#039;&#039;)
        
                                            <p><p>Load a module language file *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_config">config</a>(string $file, bool $use_sections = false, bool $fail_gracefully = false)
        
                                            <p><p>Load a module config file *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object|bool
                </div>
                <div class="col-md-8">
                    <a href="#method_driver">driver</a>(string|string[] $library, array $params = NULL, string $object_name = NULL)
        
                                            <p><p>Driver Loader</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_driver">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_add_package_path">add_package_path</a>(string $path, bool $view_cascade = TRUE)
        
                                            <p><p>Add Package Path</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_add_package_path">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_package_paths">get_package_paths</a>(bool $include_base = FALSE)
        
                                            <p><p>Get Package Paths</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_get_package_paths">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_remove_package_path">remove_package_path</a>(string $path = &#039;&#039;)
        
                                            <p><p>Remove Package Path</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method_remove_package_path">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method__ci_load">_ci_load</a>(array $_ci_data)
        
                                            <p><p>Internal CI Data Loader</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__ci_load_library">_ci_load_library</a>(string $class, mixed $params = NULL, string $object_name = NULL)
        
                                            <p><p>Internal CI Library Loader</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method__ci_load_library">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__ci_load_stock_library">_ci_load_stock_library</a>(string $library_name, string $file_path, mixed $params, string $object_name)
        
                                            <p><p>Internal CI Stock Library Loader</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method__ci_load_stock_library">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__ci_init_library">_ci_init_library</a>(string $class, string $prefix, array|null|bool $config = FALSE, string $object_name = NULL)
        
                                            <p><p>Internal CI Library Instantiator</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method__ci_init_library">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__ci_autoloader">_ci_autoloader</a>()
        
                                            <p><p>CI Autoloader</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method__ci_autoloader">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method__ci_prepare_view_vars">_ci_prepare_view_vars</a>(mixed $vars)
        
                                            <p><p>Prepare variables for _ci_vars, to be later extract()-ed inside views</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Loader.html#method__ci_prepare_view_vars">
CI_Loader</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__ci_get_component">_ci_get_component</a>(string $component)
        
                                            <p><p>CI Component getter</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method__add_module_paths">_add_module_paths</a>($module = &#039;&#039;)
        
                                            <p><p>Add a module path loader variables *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_languages">languages</a>($languages)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_libraries">libraries</a>($libraries)
        
                                            <p><p>Load an array of libraries *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_models">models</a>($models)
        
                                            <p><p>Load an array of models *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_module">module</a>($module, $params = null)
        
                                            <p><p>Load a module controller *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_modules">modules</a>($modules)
        
                                            <p><p>Load an array of controllers *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_plugin">plugin</a>($plugin)
        
                                            <p><p>Load a module plugin *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_plugins">plugins</a>($plugins)
        
                                            <p><p>Load an array of plugins *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___get">__get</a>($class)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method__autoloader">_autoloader</a>($autoload)
        
                                            <p><p>Autoload module items *</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">in <a href="CI_Loader.html#method___construct">
CI_Loader</a> at line 136</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                <p><p>Sets component load paths, gets the initial output buffering level.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initialize">
        <div class="location">at line 47</div>
        <code>                    void
    <strong>initialize</strong>($controller = null)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize the loader variables *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$controller</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_loaded">
        <div class="location">in <a href="CI_Loader.html#method_is_loaded">
CI_Loader</a> at line 172</div>
        <code>                    string|bool
    <strong>is_loaded</strong>(string $class)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Is Loaded</p></p>                <p><p>A utility method to test if a class is in the self::$_ci_classes array.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$class</td>
                <td><p>Class name to check for</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string|bool</td>
            <td><p>Class object name if loaded or FALSE</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_library">
        <div class="location">at line 164</div>
        <code>                    object
    <strong>library</strong>(mixed $library, array $params = null, string $object_name = null)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load a module library *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$library</td>
                <td><p>Library name</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Optional parameters to pass to the library class constructor</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$object_name</td>
                <td><p>An optional object name to assign to</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_model">
        <div class="location">at line 211</div>
        <code>                    object
    <strong>model</strong>(mixed $model, $object_name = null, $connect = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load a module model *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$model</td>
                <td><p>Model name</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$object_name</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$connect</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_database">
        <div class="location">at line 95</div>
        <code>                    object|bool
    <strong>database</strong>(mixed $params = &#039;&#039;, bool $return = false, bool $query_builder = null)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load the database drivers *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$params</td>
                <td><p>Database configuration options</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$return</td>
                <td><p>Whether to return the database object</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$query_builder</td>
                <td><p>Whether to enable Query Builder
(overrides the configuration setting)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object|bool</td>
            <td><p>Database object if $return is set to TRUE,
FALSE on failure, CI_Loader instance in any other case</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_dbutil">
        <div class="location">in <a href="CI_Loader.html#method_dbutil">
CI_Loader</a> at line 412</div>
        <code>                    object
    <strong>dbutil</strong>(object $db = NULL, bool $return = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load the Database Utilities Class</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>object</td>
                <td>$db</td>
                <td><p>Database object</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$return</td>
                <td><p>Whether to return the DB Utilities class object or not</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_dbforge">
        <div class="location">in <a href="CI_Loader.html#method_dbforge">
CI_Loader</a> at line 444</div>
        <code>                    object
    <strong>dbforge</strong>(object $db = NULL, bool $return = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load the Database Forge Class</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>object</td>
                <td>$db</td>
                <td><p>Database object</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$return</td>
                <td><p>Whether to return the DB Forge class object or not</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_view">
        <div class="location">at line 317</div>
        <code>                    object|string
    <strong>view</strong>(string $view, array $vars = [], bool $return = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load a module view *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$view</td>
                <td><p>View name</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$vars</td>
                <td><p>An associative array of data
to be extracted for use in the view</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$return</td>
                <td><p>Whether to return the view output
or leave it to the Output class</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object|string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_file">
        <div class="location">in <a href="CI_Loader.html#method_file">
CI_Loader</a> at line 507</div>
        <code>                    object|string
    <strong>file</strong>(string $path, bool $return = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Generic File Loader</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td><p>File path</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$return</td>
                <td><p>Whether to return the file output</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object|string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_vars">
        <div class="location">in <a href="CI_Loader.html#method_vars">
CI_Loader</a> at line 526</div>
        <code>                    object
    <strong>vars</strong>(array|object|string $vars, string $val = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Variables</p></p>                <p><p>Once variables are set they become available within
the controller class and its &quot;view&quot; files.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array|object|string</td>
                <td>$vars</td>
                <td><p>An associative array or object containing values
to be set, or a value's name if string</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$val</td>
                <td><p>Value to set, only used if $vars is a string</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_clear_vars">
        <div class="location">in <a href="CI_Loader.html#method_clear_vars">
CI_Loader</a> at line 549</div>
        <code>                    <a href="CI_Loader.html">CI_Loader</a>
    <strong>clear_vars</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Clear Cached Variables</p></p>                <p><p>Clears the cached variables.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Loader.html">CI_Loader</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_var">
        <div class="location">in <a href="CI_Loader.html#method_get_var">
CI_Loader</a> at line 565</div>
        <code>                    mixed
    <strong>get_var</strong>(string $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Variable</p></p>                <p><p>Check if a variable is set and retrieve it.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td><p>Variable name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>The variable or NULL if not found</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_vars">
        <div class="location">in <a href="CI_Loader.html#method_get_vars">
CI_Loader</a> at line 579</div>
        <code>                    array
    <strong>get_vars</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Variables</p></p>                <p><p>Retrieves all loaded variables.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_helper">
        <div class="location">at line 114</div>
        <code>                    object
    <strong>helper</strong>($helper = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load a module helper *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$helper</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_helpers">
        <div class="location">at line 137</div>
        <code>                    object
    <strong>helpers</strong>(string|string[] $helpers = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load an array of helpers *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string|string[]</td>
                <td>$helpers</td>
                <td><p>Helper name(s)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_language">
        <div class="location">at line 147</div>
        <code>                    object
    <strong>language</strong>($langfile, $idiom = &#039;&#039;, $return = false, $add_suffix = true, $alt_path = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load a module language file *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$langfile</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$idiom</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$return</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$add_suffix</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$alt_path</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_config">
        <div class="location">at line 89</div>
        <code>                    bool
    <strong>config</strong>(string $file, bool $use_sections = false, bool $fail_gracefully = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load a module config file *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$file</td>
                <td><p>Configuration file name</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$use_sections</td>
                <td><p>Whether configuration values should be loaded into their own section</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$fail_gracefully</td>
                <td><p>Whether to just return FALSE or display an error message</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td><p>TRUE if the file was loaded correctly or FALSE on failure</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_driver">
        <div class="location">in <a href="CI_Loader.html#method_driver">
CI_Loader</a> at line 723</div>
        <code>                    object|bool
    <strong>driver</strong>(string|string[] $library, array $params = NULL, string $object_name = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Driver Loader</p></p>                <p><p>Loads a driver library.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string|string[]</td>
                <td>$library</td>
                <td><p>Driver name(s)</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Optional parameters to pass to the driver</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$object_name</td>
                <td><p>An optional object name to assign to</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object|bool</td>
            <td><p>Object or FALSE on failure if $library is a string
and $object_name is set. CI_Loader instance otherwise.</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_package_path">
        <div class="location">in <a href="CI_Loader.html#method_add_package_path">
CI_Loader</a> at line 779</div>
        <code>                    object
    <strong>add_package_path</strong>(string $path, bool $view_cascade = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Package Path</p></p>                <p><p>Prepends a parent path to the library, model, helper and config
path arrays.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td><p>Path to add</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$view_cascade</td>
                <td><p>(default: TRUE)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
                            <h4>See also</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>
                                            CI_Loader::$_ci_library_paths
                                    </td>
                <td></td>
            </tr>
                    <tr>
                <td>
                                            CI_Loader::$_ci_model_paths
                                    </td>
                <td></td>
            </tr>
                    <tr>
                <td>
                                            CI_Loader::$_ci_helper_paths
                                    </td>
                <td></td>
            </tr>
                    <tr>
                <td>
                                            CI_Config::$_config_paths
                                    </td>
                <td></td>
            </tr>
            </table>

            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_package_paths">
        <div class="location">in <a href="CI_Loader.html#method_get_package_paths">
CI_Loader</a> at line 806</div>
        <code>                    array
    <strong>get_package_paths</strong>(bool $include_base = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Package Paths</p></p>                <p><p>Return a list of all package paths.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$include_base</td>
                <td><p>Whether to include BASEPATH (default: FALSE)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_remove_package_path">
        <div class="location">in <a href="CI_Loader.html#method_remove_package_path">
CI_Loader</a> at line 823</div>
        <code>                    object
    <strong>remove_package_path</strong>(string $path = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Remove Package Path</p></p>                <p><p>Remove a path from the library, model, helper and/or config
path arrays if it exists. If no path is provided, the most recently
added path will be removed removed.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td><p>Path to remove</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__ci_load">
        <div class="location">at line 343</div>
        <code>                    object
    <strong>_ci_load</strong>(array $_ci_data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Internal CI Data Loader</p></p>                <p><p>Used to load views and files.</p>
<p>Variables are prefixed with <em>ci</em> to avoid symbol collision with
variables made available to view files.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$_ci_data</td>
                <td><p>Data to load</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__ci_load_library">
        <div class="location">in <a href="CI_Loader.html#method__ci_load_library">
CI_Loader</a> at line 1016</div>
        <code>            protected        void
    <strong>_ci_load_library</strong>(string $class, mixed $params = NULL, string $object_name = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Internal CI Library Loader</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$class</td>
                <td><p>Class name to load</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$params</td>
                <td><p>Optional parameters to pass to the class constructor</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$object_name</td>
                <td><p>Optional object name to assign to</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__ci_load_stock_library">
        <div class="location">in <a href="CI_Loader.html#method__ci_load_stock_library">
CI_Loader</a> at line 1111</div>
        <code>            protected        void
    <strong>_ci_load_stock_library</strong>(string $library_name, string $file_path, mixed $params, string $object_name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Internal CI Stock Library Loader</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$library_name</td>
                <td><p>Library name to load</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$file_path</td>
                <td><p>Path to the library filename, relative to libraries/</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$params</td>
                <td><p>Optional parameters to pass to the class constructor</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$object_name</td>
                <td><p>Optional object name to assign to</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__ci_init_library">
        <div class="location">in <a href="CI_Loader.html#method__ci_init_library">
CI_Loader</a> at line 1198</div>
        <code>            protected        void
    <strong>_ci_init_library</strong>(string $class, string $prefix, array|null|bool $config = FALSE, string $object_name = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Internal CI Library Instantiator</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$class</td>
                <td><p>Class name</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$prefix</td>
                <td><p>Class name prefix</p></td>
            </tr>
                    <tr>
                <td>array|null|bool</td>
                <td>$config</td>
                <td><p>Optional configuration to pass to the class constructor:
FALSE to skip;
NULL to search in config paths;
array containing configuration data</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$object_name</td>
                <td><p>Optional object name to assign to</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__ci_autoloader">
        <div class="location">in <a href="CI_Loader.html#method__ci_autoloader">
CI_Loader</a> at line 1298</div>
        <code>            protected        void
    <strong>_ci_autoloader</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>CI Autoloader</p></p>                <p><p>Loads component listed in the config/autoload.php file.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__ci_prepare_view_vars">
        <div class="location">in <a href="CI_Loader.html#method__ci_prepare_view_vars">
CI_Loader</a> at line 1389</div>
        <code>            protected        array
    <strong>_ci_prepare_view_vars</strong>(mixed $vars)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prepare variables for _ci_vars, to be later extract()-ed inside views</p></p>                <p><p>Converts objects to associative arrays and filters-out internal
variable names (i.e. keys prefixed with '<em>ci</em>').</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$vars</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__ci_get_component">
        <div class="location">at line 333</div>
        <code>            protected        bool
    <strong>_ci_get_component</strong>(string $component)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>CI Component getter</p></p>                <p><p>Get a reference to a specific library or model.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$component</td>
                <td><p>Component name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__add_module_paths">
        <div class="location">at line 74</div>
        <code>                    
    <strong>_add_module_paths</strong>($module = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add a module path loader variables *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$module</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_languages">
        <div class="location">at line 154</div>
        <code>                    
    <strong>languages</strong>($languages)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$languages</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_libraries">
        <div class="location">at line 201</div>
        <code>                    
    <strong>libraries</strong>($libraries)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load an array of libraries *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$libraries</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_models">
        <div class="location">at line 251</div>
        <code>                    
    <strong>models</strong>($models)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load an array of models *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$models</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_module">
        <div class="location">at line 261</div>
        <code>                    
    <strong>module</strong>($module, $params = null)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load a module controller *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$module</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$params</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_modules">
        <div class="location">at line 274</div>
        <code>                    
    <strong>modules</strong>($modules)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load an array of controllers *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$modules</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_plugin">
        <div class="location">at line 284</div>
        <code>                    
    <strong>plugin</strong>($plugin)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load a module plugin *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$plugin</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_plugins">
        <div class="location">at line 307</div>
        <code>                    
    <strong>plugins</strong>($plugins)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load an array of plugins *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$plugins</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___get">
        <div class="location">at line 338</div>
        <code>                    
    <strong>__get</strong>($class)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$class</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__autoloader">
        <div class="location">at line 402</div>
        <code>                    
    <strong>_autoloader</strong>($autoload)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Autoload module items *</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$autoload</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Elasticsearch | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Elasticsearch" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Elasticsearch    
            </h1>
    </div>

    
    <p>        class
    <strong>Elasticsearch</strong>
</p>

        
    
        

            
        
            <h2>Constants</h2>    <table class="table table-condensed">
                    <tr>
                <td>
                                                                                                                        INDEX_EXCEPTION_FOLDER
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
            </table>

    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"><p>Codeigniter instance</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_run">run</a>(string $command, array $args)
        
                                            <p><p>Run valid command</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_createIndice">createIndice</a>(string $indiceName = &#039;all&#039;)
        
                                            <p><p>Create indice by name, it can create multiple indices</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_updateIndice">updateIndice</a>(string $indiceName = &#039;all&#039;)
        
                                            <p><p>Update mapping of the existing indice</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_prepareIndexAll">prepareIndexAll</a>(string $table = &#039;all&#039;, string $prefix = &#039;idx&#039;, int $chunkSize = 1000)
        
                                            <p><p>Make .txt file that include ids of the table will be indexed</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_prepareIndexTable">prepareIndexTable</a>(string $table, string $prefix = &#039;idx&#039;, int $chunkSize = 1000)
        
                                            <p><p>Perform get ids and write id to the specific .txt file based on the table</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_indexEsAllFiles">indexEsAllFiles</a>(string $table = &#039;all&#039;, string $prefix = &#039;idx&#039;)
        
                                            <p><p>Get all file based on table and then perform index</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_indexEsFile">indexEsFile</a>(string $table, string $fileName)
        
                                            <p><p>Get prepared file and then perform index data for the table. 5 processes are ran at the same time</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_indexEs">indexEs</a>($uuid, string $table, string $idStr)
        
                                            <p><p>Perform index es by ids</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 17</div>
        <code>                    
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_run">
        <div class="location">at line 31</div>
        <code>                    void
    <strong>run</strong>(string $command, array $args)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Run valid command</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$command</td>
                <td><p>name of command</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$args</td>
                <td><p>param of the command</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_createIndice">
        <div class="location">at line 41</div>
        <code>                    void
    <strong>createIndice</strong>(string $indiceName = &#039;all&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create indice by name, it can create multiple indices</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$indiceName</td>
                <td><p>name of the indice</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_updateIndice">
        <div class="location">at line 67</div>
        <code>                    void
    <strong>updateIndice</strong>(string $indiceName = &#039;all&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Update mapping of the existing indice</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$indiceName</td>
                <td><p>name of the indice</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_prepareIndexAll">
        <div class="location">at line 95</div>
        <code>                    void
    <strong>prepareIndexAll</strong>(string $table = &#039;all&#039;, string $prefix = &#039;idx&#039;, int $chunkSize = 1000)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Make .txt file that include ids of the table will be indexed</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>table want to prepared, valid value are:
Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
Tag, Task, Ticket. Default is all</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$prefix</td>
                <td><p>prefix of the file will be created, default is &quot;idx&quot;. Example idx_clients_1.txt</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$chunkSize</td>
                <td><p>Record per file, default is 1000</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_prepareIndexTable">
        <div class="location">at line 137</div>
        <code>                    void
    <strong>prepareIndexTable</strong>(string $table, string $prefix = &#039;idx&#039;, int $chunkSize = 1000)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Perform get ids and write id to the specific .txt file based on the table</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>table will be prepared, valid value are:
Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
Tag, Task, Ticket.</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$prefix</td>
                <td><p>prefix of the file, default is 'idx'. Example idx_client_1.txt</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$chunkSize</td>
                <td><p>Record per file, default is 1000</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_indexEsAllFiles">
        <div class="location">at line 154</div>
        <code>                    void
    <strong>indexEsAllFiles</strong>(string $table = &#039;all&#039;, string $prefix = &#039;idx&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get all file based on table and then perform index</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>table will be indexed, valid value are:
Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
Tag, Task, Ticket. Default is 'all'</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$prefix</td>
                <td><p>prefix of the created file</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_indexEsFile">
        <div class="location">at line 235</div>
        <code>                    void
    <strong>indexEsFile</strong>(string $table, string $fileName)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get prepared file and then perform index data for the table. 5 processes are ran at the same time</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>table will be indexed, valid value are:
Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
Tag, Task, Ticket.</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$fileName</td>
                <td><p>name of the file will be read for indexing, sperator by &quot;:&quot;</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_indexEs">
        <div class="location">at line 283</div>
        <code>                    void
    <strong>indexEs</strong>($uuid, string $table, string $idStr)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Perform index es by ids</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$uuid</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>table will be indexed, valid value are:
Client, Contact, Contract, CreditNote, CustomFieldsValue, Estimate, Expense,
Goal, Invoice, Itemable, KnowledgeBase, Lead, Project, Proposal, Staff, Survey,
Tag, Task, Ticket.</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$idStr</td>
                <td><p>id will be indexed, sperate by &quot;:&quot;</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

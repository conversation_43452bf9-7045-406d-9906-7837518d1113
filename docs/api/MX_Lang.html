<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>MX_Lang | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:MX_Lang" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>MX_Lang    
            </h1>
    </div>

    
    <p>        class
    <strong>MX_Lang</strong>        extends <a href="CI_Lang.html">CI_Lang</a>
</p>

        
    
        

            <div class="description">
            <p><p>Modular Extensions - HMVC</p></p>            <p><p>Adapted from the CodeIgniter Core Classes</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_language">
                                                                                array
                                                                                
                                    </td>
                <td>$language</td>
                <td class="last"><p>List of translations</p></td>
                <td><small>from&nbsp;<a href="CI_Lang.html#property_language">
CI_Lang</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_is_loaded">
                                                                                array
                                                                                
                                    </td>
                <td>$is_loaded</td>
                <td class="last"><p>List of loaded language files</p></td>
                <td><small>from&nbsp;<a href="CI_Lang.html#property_is_loaded">
CI_Lang</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Lang.html#method___construct">
CI_Lang</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void|string[]
                </div>
                <div class="col-md-8">
                    <a href="#method_load">load</a>(mixed $langfile, $lang = &#039;&#039;, bool $return = false, bool $add_suffix = true, string $alt_path = &#039;&#039;, $_module = &#039;&#039;)
        
                                            <p><p>Load a language file</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_line">line</a>(string $line, bool $log_errors = TRUE)
        
                                            <p><p>Language line</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Lang.html#method_line">
CI_Lang</a></small></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">in <a href="CI_Lang.html#method___construct">
CI_Lang</a> at line 70</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_load">
        <div class="location">at line 51</div>
        <code>                    void|string[]
    <strong>load</strong>(mixed $langfile, $lang = &#039;&#039;, bool $return = false, bool $add_suffix = true, string $alt_path = &#039;&#039;, $_module = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load a language file</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$langfile</td>
                <td><p>Language file name</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$lang</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$return</td>
                <td><p>Whether to return the loaded array of translations</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$add_suffix</td>
                <td><p>Whether to add suffix to $langfile</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$alt_path</td>
                <td><p>Alternative path to look for the language file</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$_module</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void|string[]</td>
            <td><p>Array containing translations, if $return is set to TRUE</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_line">
        <div class="location">in <a href="CI_Lang.html#method_line">
CI_Lang</a> at line 190</div>
        <code>                    string
    <strong>line</strong>(string $line, bool $log_errors = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Language line</p></p>                <p><p>Fetches a single line of text from the language array</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$line</td>
                <td><p>Language line key</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$log_errors</td>
                <td><p>Whether to log an error message if the line is not found</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td>Translation</td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>RabbitMqReceive | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:RabbitMqReceive" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>RabbitMqReceive    
            </h1>
    </div>

    
    <p>        class
    <strong>RabbitMqReceive</strong>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_connection">
                                        protected                                        <abbr title="PhpAmqpLib\Connection\AbstractConnection">AbstractConnection</abbr>
                                                                                
                                    </td>
                <td>$connection</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_channel">
                                        protected                                        <abbr title="PhpAmqpLib\Channel\AMQPChannel">AMQPChannel</abbr>
                                                                                
                                    </td>
                <td>$channel</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_callback">
                                        protected                                        
                                                                                
                                    </td>
                <td>$callback</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(<abbr title="PhpAmqpLib\Connection\AbstractConnection">AbstractConnection</abbr> $connection, $callback)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_consume">consume</a>(string $queue, string $consumerTag = &#039;crm&#039;)
        
                                            <p><p>Consume message from rabbitMQ</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_postMessage">postMessage</a>(<abbr title="PhpAmqpLib\Message\AMQPMessage">AMQPMessage</abbr> $message)
        
                                            <p><p>Handle consume message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_shutdown">shutdown</a>(<abbr title="PhpAmqpLib\Channel\AMQPChannel">AMQPChannel</abbr> $channel)
        
                                            <p><p>Close conenction when done</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 15</div>
        <code>                    
    <strong>__construct</strong>(<abbr title="PhpAmqpLib\Connection\AbstractConnection">AbstractConnection</abbr> $connection, $callback)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><abbr title="PhpAmqpLib\Connection\AbstractConnection">AbstractConnection</abbr></td>
                <td>$connection</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$callback</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_consume">
        <div class="location">at line 37</div>
        <code>                    void
    <strong>consume</strong>(string $queue, string $consumerTag = &#039;crm&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Consume message from rabbitMQ</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$queue</td>
                <td><p>name of the queue will be consume</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$consumerTag</td>
                <td><p>name of the consumer</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_postMessage">
        <div class="location">at line 50</div>
        <code>                    
    <strong>postMessage</strong>(<abbr title="PhpAmqpLib\Message\AMQPMessage">AMQPMessage</abbr> $message)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Handle consume message</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><abbr title="PhpAmqpLib\Message\AMQPMessage">AMQPMessage</abbr></td>
                <td>$message</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_shutdown">
        <div class="location">at line 69</div>
        <code>                    
    <strong>shutdown</strong>(<abbr title="PhpAmqpLib\Channel\AMQPChannel">AMQPChannel</abbr> $channel)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Close conenction when done</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><abbr title="PhpAmqpLib\Channel\AMQPChannel">AMQPChannel</abbr></td>
                <td>$channel</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

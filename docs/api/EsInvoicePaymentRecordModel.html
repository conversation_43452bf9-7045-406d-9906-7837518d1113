<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>EsInvoicePaymentRecordModel | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:EsInvoicePaymentRecordModel" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>EsInvoicePaymentRecordModel    
            </h1>
    </div>

    
    <p>        class
    <strong>EsInvoicePaymentRecordModel</strong>        extends <a href="EsBaseModel.html">EsBaseModel</a>
</p>

        
    
        

            
        
            <h2>Constants</h2>    <table class="table table-condensed">
                    <tr>
                <td>
                                                                                                                        INDEX_TMP_FOLDER
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
            </table>

    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"><p>Codeigniter instance</p></td>
                <td><small>from&nbsp;<a href="EsBaseModel.html#property_ci">
EsBaseModel</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_table">
                                        protected                                        
                                                                                
                                    </td>
                <td>$table</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_primaryKey">
                                        protected                                        
                                                                                
                                    </td>
                <td>$primaryKey</td>
                <td class="last"></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_relations">
                                        protected                                        
                                                                                
                                    </td>
                <td>$relations</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="EsBaseModel.html#property_relations">
EsBaseModel</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_hasCustomDeleteQuery">
                                        protected                                        
                                                                                
                                    </td>
                <td>$hasCustomDeleteQuery</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="EsBaseModel.html#property_hasCustomDeleteQuery">
EsBaseModel</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="EsBaseModel.html#method___construct">
EsBaseModel</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_indexEs">indexEs</a>($ids)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getPrimaryKeyValue">getPrimaryKeyValue</a>($payload)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="EsBaseModel.html#method_getPrimaryKeyValue">
EsBaseModel</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_customDeleteQuery">customDeleteQuery</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="EsBaseModel.html#method_customDeleteQuery">
EsBaseModel</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getDeletePayload">getDeletePayload</a>(array $id)
        
                                            <p><p>Perform search in elastic for deleting records then update related relations</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="EsBaseModel.html#method_getDeletePayload">
EsBaseModel</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getUpdatePayload">getUpdatePayload</a>($id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="EsBaseModel.html#method_getUpdatePayload">
EsBaseModel</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getChangePayload">getChangePayload</a>(array $payload)
        
                                            <p><p>Get ids based on operation</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="EsBaseModel.html#method_getChangePayload">
EsBaseModel</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_fetchTableIds">fetchTableIds</a>(int $fromId, int $chunkSize = 1000)
        
                                            <p><p>Fetch ids of the table to prepare index</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="EsBaseModel.html#method_fetchTableIds">
EsBaseModel</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_createIndexFiles">createIndexFiles</a>($table, $prefix = &#039;idx&#039;, $chunkSize = 1000)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="EsBaseModel.html#method_createIndexFiles">
EsBaseModel</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_needUpdate">needUpdate</a>(array $payload)
        
                                            <p><p>Check update fields to perform re-index this id or not</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="EsBaseModel.html#method_needUpdate">
EsBaseModel</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_formatInvoiceNumber">formatInvoiceNumber</a>($invoice)
        
                                            <p><p>Format invoice number based on description</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">in <a href="EsBaseModel.html#method___construct">
EsBaseModel</a> at line 25</div>
        <code>                    
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_indexEs">
        <div class="location">at line 9</div>
        <code>                    
    <strong>indexEs</strong>($ids)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$ids</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getPrimaryKeyValue">
        <div class="location">in <a href="EsBaseModel.html#method_getPrimaryKeyValue">
EsBaseModel</a> at line 35</div>
        <code>                    
    <strong>getPrimaryKeyValue</strong>($payload)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$payload</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_customDeleteQuery">
        <div class="location">in <a href="EsBaseModel.html#method_customDeleteQuery">
EsBaseModel</a> at line 49</div>
        <code>            protected        
    <strong>customDeleteQuery</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getDeletePayload">
        <div class="location">in <a href="EsBaseModel.html#method_getDeletePayload">
EsBaseModel</a> at line 59</div>
        <code>                    array
    <strong>getDeletePayload</strong>(array $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Perform search in elastic for deleting records then update related relations</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td><p>list of data</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getUpdatePayload">
        <div class="location">in <a href="EsBaseModel.html#method_getUpdatePayload">
EsBaseModel</a> at line 86</div>
        <code>                    
    <strong>getUpdatePayload</strong>($id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getChangePayload">
        <div class="location">in <a href="EsBaseModel.html#method_getChangePayload">
EsBaseModel</a> at line 96</div>
        <code>                    array
    <strong>getChangePayload</strong>(array $payload)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get ids based on operation</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$payload</td>
                <td><p>change payload</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td><p>change payload from db/es</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_fetchTableIds">
        <div class="location">in <a href="EsBaseModel.html#method_fetchTableIds">
EsBaseModel</a> at line 114</div>
        <code>                    array
    <strong>fetchTableIds</strong>(int $fromId, int $chunkSize = 1000)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch ids of the table to prepare index</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$fromId</td>
                <td><p>next id use to fetch as page</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$chunkSize</td>
                <td><p>item per pages</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td><p>list of ids</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_createIndexFiles">
        <div class="location">in <a href="EsBaseModel.html#method_createIndexFiles">
EsBaseModel</a> at line 130</div>
        <code>                    
    <strong>createIndexFiles</strong>($table, $prefix = &#039;idx&#039;, $chunkSize = 1000)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$table</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$prefix</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$chunkSize</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_needUpdate">
        <div class="location">in <a href="EsBaseModel.html#method_needUpdate">
EsBaseModel</a> at line 159</div>
        <code>                    bool
    <strong>needUpdate</strong>(array $payload)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check update fields to perform re-index this id or not</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$payload</td>
                <td><p>payload of the changes</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td><p>true - need update index. Otherwise, do nothing</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_formatInvoiceNumber">
        <div class="location">at line 74</div>
        <code>            protected        string
    <strong>formatInvoiceNumber</strong>($invoice)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Format invoice number based on description</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$invoice</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

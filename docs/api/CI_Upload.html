<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Upload | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Upload" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Upload    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Upload</strong>
</p>

        
    
        

            <div class="description">
            <p><p>File Uploading Class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_max_size">
                                                                                int
                                                                                
                                    </td>
                <td>$max_size</td>
                <td class="last"><p>Maximum file size</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_max_width">
                                                                                int
                                                                                
                                    </td>
                <td>$max_width</td>
                <td class="last"><p>Maximum image width</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_max_height">
                                                                                int
                                                                                
                                    </td>
                <td>$max_height</td>
                <td class="last"><p>Maximum image height</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_min_width">
                                                                                int
                                                                                
                                    </td>
                <td>$min_width</td>
                <td class="last"><p>Minimum image width</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_min_height">
                                                                                int
                                                                                
                                    </td>
                <td>$min_height</td>
                <td class="last"><p>Minimum image height</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_max_filename">
                                                                                int
                                                                                
                                    </td>
                <td>$max_filename</td>
                <td class="last"><p>Maximum filename length</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_max_filename_increment">
                                                                                int
                                                                                
                                    </td>
                <td>$max_filename_increment</td>
                <td class="last"><p>Maximum duplicate filename increment ID</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_allowed_types">
                                                                                string
                                                                                
                                    </td>
                <td>$allowed_types</td>
                <td class="last"><p>Allowed file types</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_file_temp">
                                                                                string
                                                                                
                                    </td>
                <td>$file_temp</td>
                <td class="last"><p>Temporary filename</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_file_name">
                                                                                string
                                                                                
                                    </td>
                <td>$file_name</td>
                <td class="last">Filename</td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_orig_name">
                                                                                string
                                                                                
                                    </td>
                <td>$orig_name</td>
                <td class="last"><p>Original filename</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_file_type">
                                                                                string
                                                                                
                                    </td>
                <td>$file_type</td>
                <td class="last"><p>File type</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_file_size">
                                                                                int
                                                                                
                                    </td>
                <td>$file_size</td>
                <td class="last"><p>File size</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_file_ext">
                                                                                string
                                                                                
                                    </td>
                <td>$file_ext</td>
                <td class="last"><p>Filename extension</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_file_ext_tolower">
                                                                                string
                                                                                
                                    </td>
                <td>$file_ext_tolower</td>
                <td class="last"><p>Force filename extension to lowercase</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_upload_path">
                                                                                string
                                                                                
                                    </td>
                <td>$upload_path</td>
                <td class="last"><p>Upload path</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_overwrite">
                                                                                bool
                                                                                
                                    </td>
                <td>$overwrite</td>
                <td class="last"><p>Overwrite flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_encrypt_name">
                                                                                bool
                                                                                
                                    </td>
                <td>$encrypt_name</td>
                <td class="last"><p>Obfuscate filename flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_is_image">
                                                                                bool
                                                                                
                                    </td>
                <td>$is_image</td>
                <td class="last"><p>Is image flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_image_width">
                                                                                int
                                                                                
                                    </td>
                <td>$image_width</td>
                <td class="last"><p>Image width</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_image_height">
                                                                                int
                                                                                
                                    </td>
                <td>$image_height</td>
                <td class="last"><p>Image height</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_image_type">
                                                                                string
                                                                                
                                    </td>
                <td>$image_type</td>
                <td class="last"><p>Image type</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_image_size_str">
                                                                                string
                                                                                
                                    </td>
                <td>$image_size_str</td>
                <td class="last"><p>Image size string</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_error_msg">
                                                                                array
                                                                                
                                    </td>
                <td>$error_msg</td>
                <td class="last"><p>Error messages list</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_remove_spaces">
                                                                                bool
                                                                                
                                    </td>
                <td>$remove_spaces</td>
                <td class="last"><p>Remove spaces flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_detect_mime">
                                                                                bool
                                                                                
                                    </td>
                <td>$detect_mime</td>
                <td class="last"><p>MIME detection flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_xss_clean">
                                                                                bool
                                                                                
                                    </td>
                <td>$xss_clean</td>
                <td class="last"><p>XSS filter flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_mod_mime_fix">
                                                                                bool
                                                                                
                                    </td>
                <td>$mod_mime_fix</td>
                <td class="last"><p>Apache mod_mime fix flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_temp_prefix">
                                                                                string
                                                                                
                                    </td>
                <td>$temp_prefix</td>
                <td class="last"><p>Temporary filename prefix</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_client_name">
                                                                                bool
                                                                                
                                    </td>
                <td>$client_name</td>
                <td class="last"><p>Filename sent by the client</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__file_name_override">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_file_name_override</td>
                <td class="last"><p>Filename override</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__mimes">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_mimes</td>
                <td class="last"><p>MIME types list</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__CI">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$_CI</td>
                <td class="last"><p>CI Singleton</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $config = array())
        
                                            <p>Constructor</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_initialize">initialize</a>(array $config = array(), bool $reset = TRUE)
        
                                            <p><p>Initialize preferences</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_do_upload">do_upload</a>(string $field = &#039;userfile&#039;)
        
                                            <p><p>Perform the file upload</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_data">data</a>(string $index = NULL)
        
                                            <p><p>Finalized Data Array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_upload_path">set_upload_path</a>(string $path)
        
                                            <p><p>Set Upload Path</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_set_filename">set_filename</a>(string $path, string $filename)
        
                                            <p><p>Set the file name</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_max_filesize">set_max_filesize</a>(int $n)
        
                                            <p><p>Set Maximum File Size</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_max_size">set_max_size</a>(int $n)
        
                                            <p><p>Set Maximum File Size</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_max_filename">set_max_filename</a>(int $n)
        
                                            <p><p>Set Maximum File Name Length</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_max_width">set_max_width</a>(int $n)
        
                                            <p><p>Set Maximum Image Width</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_max_height">set_max_height</a>(int $n)
        
                                            <p><p>Set Maximum Image Height</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_min_width">set_min_width</a>(int $n)
        
                                            <p><p>Set minimum image width</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_min_height">set_min_height</a>(int $n)
        
                                            <p><p>Set minimum image height</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_allowed_types">set_allowed_types</a>(mixed $types)
        
                                            <p><p>Set Allowed File Types</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_image_properties">set_image_properties</a>(string $path = &#039;&#039;)
        
                                            <p><p>Set Image Properties</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_xss_clean">set_xss_clean</a>(bool $flag = FALSE)
        
                                            <p><p>Set XSS Clean</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_image">is_image</a>()
        
                                            <p><p>Validate the image</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_allowed_filetype">is_allowed_filetype</a>(bool $ignore_mime = FALSE)
        
                                            <p><p>Verify that the filetype is allowed</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_allowed_filesize">is_allowed_filesize</a>()
        
                                            <p><p>Verify that the file is within the allowed size</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_allowed_dimensions">is_allowed_dimensions</a>()
        
                                            <p><p>Verify that the image is within the allowed width/height</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_validate_upload_path">validate_upload_path</a>()
        
                                            <p><p>Validate Upload Path</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_extension">get_extension</a>(string $filename)
        
                                            <p><p>Extract the file extension</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_limit_filename_length">limit_filename_length</a>(string $filename, int $length)
        
                                            <p><p>Limit the File Name Length</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_do_xss_clean">do_xss_clean</a>()
        
                                            <p><p>Runs the file through the XSS clean function</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Upload.html">CI_Upload</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_error">set_error</a>(string $msg, $log_level = &#039;error&#039;)
        
                                            <p><p>Set an error message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_display_errors">display_errors</a>(string $open = &#039;&lt;p&gt;&#039;, string $close = &#039;&lt;/p&gt;&#039;)
        
                                            <p><p>Display the error message</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__prep_filename">_prep_filename</a>(string $filename)
        
                                            <p><p>Prep Filename</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__file_mime_type">_file_mime_type</a>(array $file)
        
                                            <p><p>File MIME type</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 292</div>
        <code>                    void
    <strong>__construct</strong>(array $config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Constructor</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initialize">
        <div class="location">at line 311</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>initialize</strong>(array $config = array(), bool $reset = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize preferences</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$reset</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_do_upload">
        <div class="location">at line 374</div>
        <code>                    bool
    <strong>do_upload</strong>(string $field = &#039;userfile&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Perform the file upload</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$field</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_data">
        <div class="location">at line 597</div>
        <code>                    mixed
    <strong>data</strong>(string $index = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Finalized Data Array</p></p>                <p><p>Returns an associative array containing all of the information
related to the upload, allowing the developer easy access in one array.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$index</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_upload_path">
        <div class="location">at line 632</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_upload_path</strong>(string $path)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Upload Path</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_filename">
        <div class="location">at line 652</div>
        <code>                    string
    <strong>set_filename</strong>(string $path, string $filename)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the file name</p></p>                <p><p>This function takes a filename/path as input and looks for the
existence of a file with the same name. If found, it will append a
number to the end of the filename to avoid overwriting a pre-existing file.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$filename</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_max_filesize">
        <div class="location">at line 693</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_max_filesize</strong>(int $n)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Maximum File Size</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_max_size">
        <div class="location">at line 710</div>
        <code>            protected        <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_max_size</strong>(int $n)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Maximum File Size</p></p>                <p><p>An internal alias to set_max<em>filesize() to help with configuration
as initialize() will look for a set</em><property_name>() method ...</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_max_filename">
        <div class="location">at line 723</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_max_filename</strong>(int $n)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Maximum File Name Length</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_max_width">
        <div class="location">at line 737</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_max_width</strong>(int $n)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Maximum Image Width</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_max_height">
        <div class="location">at line 751</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_max_height</strong>(int $n)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Maximum Image Height</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_min_width">
        <div class="location">at line 765</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_min_width</strong>(int $n)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set minimum image width</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_min_height">
        <div class="location">at line 779</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_min_height</strong>(int $n)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set minimum image height</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$n</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_allowed_types">
        <div class="location">at line 793</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_allowed_types</strong>(mixed $types)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Allowed File Types</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$types</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_image_properties">
        <div class="location">at line 811</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_image_properties</strong>(string $path = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Image Properties</p></p>                <p><p>Uses GD to determine the width/height/type of image</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$path</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_xss_clean">
        <div class="location">at line 840</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_xss_clean</strong>(bool $flag = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set XSS Clean</p></p>                <p><p>Enables the XSS flag so that the file that was uploaded
will be run through the XSS filter.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$flag</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_image">
        <div class="location">at line 853</div>
        <code>                    bool
    <strong>is_image</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validate the image</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_allowed_filetype">
        <div class="location">at line 883</div>
        <code>                    bool
    <strong>is_allowed_filetype</strong>(bool $ignore_mime = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Verify that the filetype is allowed</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$ignore_mime</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_allowed_filesize">
        <div class="location">at line 931</div>
        <code>                    bool
    <strong>is_allowed_filesize</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Verify that the file is within the allowed size</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_allowed_dimensions">
        <div class="location">at line 943</div>
        <code>                    bool
    <strong>is_allowed_dimensions</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Verify that the image is within the allowed width/height</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_validate_upload_path">
        <div class="location">at line 987</div>
        <code>                    bool
    <strong>validate_upload_path</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Validate Upload Path</p></p>                <p><p>Verifies that it is a valid upload path with proper permissions.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_extension">
        <div class="location">at line 1024</div>
        <code>                    string
    <strong>get_extension</strong>(string $filename)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Extract the file extension</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$filename</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_limit_filename_length">
        <div class="location">at line 1046</div>
        <code>                    string
    <strong>limit_filename_length</strong>(string $filename, int $length)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Limit the File Name Length</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$filename</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$length</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_do_xss_clean">
        <div class="location">at line 1075</div>
        <code>                    string
    <strong>do_xss_clean</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Runs the file through the XSS clean function</p></p>                <p><p>This prevents people from embedding malicious code in their files.
I'm not sure that it won't negatively affect certain files in unexpected ways,
but so far I haven't found that it causes trouble.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_error">
        <div class="location">at line 1149</div>
        <code>                    <a href="CI_Upload.html">CI_Upload</a>
    <strong>set_error</strong>(string $msg, $log_level = &#039;error&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set an error message</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$msg</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$log_level</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Upload.html">CI_Upload</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_display_errors">
        <div class="location">at line 1173</div>
        <code>                    string
    <strong>display_errors</strong>(string $open = &#039;&lt;p&gt;&#039;, string $close = &#039;&lt;/p&gt;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Display the error message</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$open</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$close</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__prep_filename">
        <div class="location">at line 1191</div>
        <code>            protected        string
    <strong>_prep_filename</strong>(string $filename)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prep Filename</p></p>                <p><p>Prevents possible script execution from Apache's handling
of files' multiple extensions.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$filename</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__file_mime_type">
        <div class="location">at line 1214</div>
        <code>            protected        void
    <strong>_file_mime_type</strong>(array $file)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>File MIME type</p></p>                <p><p>Detects the (actual) MIME type of the uploaded file, if possible.
The input array is expected to be $_FILES[$field]</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$file</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>XML_RPC_Response | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:XML_RPC_Response" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>XML_RPC_Response    
            </h1>
    </div>

    
    <p>        class
    <strong>XML_RPC_Response</strong>
</p>

        
    
        

            <div class="description">
            <p><p>XML-RPC Response class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_val">
                                                                                mixed
                                                                                
                                    </td>
                <td>$val</td>
                <td class="last">Value</td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_errno">
                                                                                int
                                                                                
                                    </td>
                <td>$errno</td>
                <td class="last"><p>Error number</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_errstr">
                                                                                string
                                                                                
                                    </td>
                <td>$errstr</td>
                <td class="last"><p>Error message</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_headers">
                                                                                array
                                                                                
                                    </td>
                <td>$headers</td>
                <td class="last"><p>Headers list</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_xss_clean">
                                                                                bool
                                                                                
                                    </td>
                <td>$xss_clean</td>
                <td class="last"><p>XSS Filter flag</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(mixed $val, int $code = 0, string $fstr = &#039;&#039;)
        
                                            <p>Constructor</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_faultCode">faultCode</a>()
        
                                            <p><p>Fault code</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_faultString">faultString</a>()
        
                                            <p><p>Fault string</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_value">value</a>()
        
                                            <p>Value</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_prepare_response">prepare_response</a>()
        
                                            <p><p>Prepare response</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_decode">decode</a>(mixed $array = NULL)
        
                                            <p>Decode</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_xmlrpc_decoder">xmlrpc_decoder</a>($xmlrpc_val)
        
                                            <p><p>XML-RPC Object to PHP Types</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_iso8601_decode">iso8601_decode</a>($time, $utc = FALSE)
        
                                            <p><p>ISO-8601 time to server or UTC time</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 833</div>
        <code>                    void
    <strong>__construct</strong>(mixed $val, int $code = 0, string $fstr = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Constructor</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$val</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$code</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$fstr</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_faultCode">
        <div class="location">at line 862</div>
        <code>                    int
    <strong>faultCode</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fault code</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_faultString">
        <div class="location">at line 874</div>
        <code>                    string
    <strong>faultString</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fault string</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_value">
        <div class="location">at line 886</div>
        <code>                    mixed
    <strong>value</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Value</p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_prepare_response">
        <div class="location">at line 898</div>
        <code>                    string
    <strong>prepare_response</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prepare response</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td>xml</td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_decode">
        <div class="location">at line 928</div>
        <code>                    array
    <strong>decode</strong>(mixed $array = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Decode</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$array</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_xmlrpc_decoder">
        <div class="location">at line 971</div>
        <code>                    array
    <strong>xmlrpc_decoder</strong>($xmlrpc_val)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>XML-RPC Object to PHP Types</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$xmlrpc_val</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_iso8601_decode">
        <div class="location">at line 1014</div>
        <code>                    int
    <strong>iso8601_decode</strong>($time, $utc = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>ISO-8601 time to server or UTC time</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$time</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$utc</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td><p>unix timestamp</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

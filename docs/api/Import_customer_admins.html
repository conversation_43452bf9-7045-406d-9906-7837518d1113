<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Import_customer_admins | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Import_customer_admins" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Import_customer_admins    
            </h1>
    </div>

    
    <p>        class
    <strong>Import_customer_admins</strong>        extends <a href="App_import.html">App_import</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"><p>Codeigniter Instance</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_ci">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_importGuidelines">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$importGuidelines</td>
                <td class="last"><p>Stores the import guidelines</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_importGuidelines">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_sampleDataText">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$sampleDataText</td>
                <td class="last"><p>Text used for sample data tables and CSV</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_sampleDataText">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_totalImported">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$totalImported</td>
                <td class="last"><p>Total imported leads</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_totalImported">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_appTmpFolder">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$appTmpFolder</td>
                <td class="last"><p>App temp folder location</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_appTmpFolder">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_tmpFileStoragePath">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$tmpFileStoragePath</td>
                <td class="last"><p>After the uploaded file is moved to the $appTmpFolder, we will store the full file path here
for further usage</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_tmpFileStoragePath">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_temporaryFileFromFormLocation">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$temporaryFileFromFormLocation</td>
                <td class="last"><p>Temporary file location from $_FILES
Used when intializing the import</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_temporaryFileFromFormLocation">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_tmpDir">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$tmpDir</td>
                <td class="last"><p>This is actually the temporary dir in the $appTempFolder used when moving the file into $appTempFOlder</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_tmpDir">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_filename">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$filename</td>
                <td class="last"><p>Uploaded file name</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_filename">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_rows">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$rows</td>
                <td class="last"><p>The actual .csv file rows</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_rows">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_totalRows">
                                        protected                                        mixed
                                                                                
                                    </td>
                <td>$totalRows</td>
                <td class="last"><p>Total rows
Total count from $rows</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_totalRows">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_isSimulation">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$isSimulation</td>
                <td class="last"><p>Indicating does this import/upload is simulation</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_isSimulation">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_maxSimulationRows">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$maxSimulationRows</td>
                <td class="last"><p>Total rows to show when simulating data
For example if user have 2500 rows in the .csv file in the simulate HTML table will be shown only $maxSimulationRows</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_maxSimulationRows">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_simulationData">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$simulationData</td>
                <td class="last"><p>This is tha actual simulation data that will be shown the preview simulation tabe</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_simulationData">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_databaseFields">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$databaseFields</td>
                <td class="last"><p>Database fields that will be used for import</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_databaseFields">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_customFields">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$customFields</td>
                <td class="last"><p>Custom fields that will be used for import</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_customFields">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_fieldNames">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$fieldNames</td>
                <td class="last"><p>Field names that will be used for render header</p></td>
                <td><small>from&nbsp;<a href="App_import.html#property_fieldNames">
App_import</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_requiredFields">
                                        protected                                        
                                                                                
                                    </td>
                <td>$requiredFields</td>
                <td class="last"></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_perform">perform</a>()
        
                                            <p><p>This method must be implemented on the child import class
This method will perform all the import actions and checks</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_failureRedirectURL">failureRedirectURL</a>()
        
                                            <p><p>In some cases there will be some errors that we need to catch, after we catch the errors, we will redirect the user to this URL
This method is required and must be implemented in the child class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_formatFieldNameForHeading">formatFieldNameForHeading</a>(string $field)
        
                                            <p><p>Format column/field name for table heading/csv</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_setDatabaseFields">setDatabaseFields</a>($fields)
        
                                            <p><p>Sets database fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_setDatabaseFields">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_setFieldNames">setFieldNames</a>($fieldNames)
        
                                            <p><p>Sets database fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_setFieldNames">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getDatabaseFields">getDatabaseFields</a>()
        
                                            <p><p>Get database fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_getDatabaseFields">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getImportableDatabaseFields">getImportableDatabaseFields</a>()
        
                                            <p><p>Get importable database fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_getImportableDatabaseFields">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_setCustomFields">setCustomFields</a>(object $fields)
        
                                            <p><p>Set custom fields that will be used for import</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_setCustomFields">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getCustomFields">getCustomFields</a>()
        
                                            <p><p>Get custom fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_getCustomFields">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_setSimulation">setSimulation</a>(bool $bool)
        
                                            <p><p>Set simulation</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_setSimulation">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_isSimulation">isSimulation</a>()
        
                                            <p><p>Check whether the request is simulation</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_isSimulation">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getSimulationData">getSimulationData</a>()
        
                                            <p><p>Get all stored simulation data that will be shown in table preview for simulation</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_getSimulationData">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_setRows">setRows</a>(array $rows)
        
                                            <p><p>Set the rows from the .csv file</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_setRows">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_getRows">getRows</a>()
        
                                            <p><p>Get the rows stored from the .csv file</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_getRows">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_totalRows">totalRows</a>()
        
                                            <p><p>Get total rows</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_totalRows">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_setTemporaryFileLocation">setTemporaryFileLocation</a>(string $location)
        
                                            <p><p>Sets temporary file location from the form ($_FILES)</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_setTemporaryFileLocation">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_getTemporaryFileLocation">getTemporaryFileLocation</a>()
        
                                            <p><p>Get temporary file location</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_getTemporaryFileLocation">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_setFilename">setFilename</a>(string $name)
        
                                            <p><p>Sets filename from the form ($_FILES)</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_setFilename">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_getFilename">getFilename</a>()
        
                                            <p><p>Get filename from the form ($_FILES)</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_getFilename">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_incrementImported">incrementImported</a>()
        
                                            <p><p>Increment the total imported number</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_incrementImported">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_totalImported">totalImported</a>()
        
                                            <p><p>Get total imported</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_totalImported">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getNotImportableFields">getNotImportableFields</a>()
        
                                            <p><p>Get not importable fields
Child class should define property e.q. protected $notImportableFields = ['name'];</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_getNotImportableFields">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_maxInputVarsWarningHtml">maxInputVarsWarningHtml</a>()
        
                                            <p><p>Checks and show HTML warning for max_input_vars based on total rows</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_maxInputVarsWarningHtml">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_downloadSampleFormHtml">downloadSampleFormHtml</a>()
        
                                            <p><p>Get HTML form for download sample .csv file</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_downloadSampleFormHtml">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_simulationDataInfo">simulationDataInfo</a>()
        
                                            <p><p>General info for simulation data</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_simulationDataInfo">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_addImportGuidelinesInfo">addImportGuidelinesInfo</a>($text, $isImportant = false)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_addImportGuidelinesInfo">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_importGuidelinesInfoHtml">importGuidelinesInfoHtml</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_importGuidelinesInfoHtml">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_downloadSample">downloadSample</a>()
        
                                            <p><p>Download sample .csv file</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_downloadSample">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_createSampleTableHtml">createSampleTableHtml</a>(bool $simulation = false)
        
                                            <p><p>Create sample table for sample data and simulation table results</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_createSampleTableHtml">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_getRequiredFields">getRequiredFields</a>()
        
                                            <p><p>Get required import fields
Child class should define property e.q. protected $requiredFields = ['name'];</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_getRequiredFields">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_initialize">initialize</a>()
        
                                            <p><p>This is the main function that will initialize the import before parsing all data
*<strong>* IMPORTANT ***</strong> The child class must call this method inside the perform method</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_initialize">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_readFileRows">readFileRows</a>()
        
                                            <p><p>Read the rows and store them into $rows</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_readFileRows">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_checkNullValueAddedByUser">checkNullValueAddedByUser</a>(string $val)
        
                                            <p><p>Some users enter in the .csv rows data e.q. NULL or null
To prevent storing this as string in database we shoul make the value empty
This is useful too when checking for required fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_checkNullValueAddedByUser">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_trimInsertValues">trimInsertValues</a>(array $insert)
        
                                            <p><p>Trim the values before inserting</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_trimInsertValues">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    null
                </div>
                <div class="col-md-8">
                    <a href="#method_handleCustomFieldsInsert">handleCustomFieldsInsert</a>(mixed $rel_id, array $row, mixed $fieldNumber, mixed $rowNumber, string $customFieldTo)
        
                                            <p><p>Function responsible to store the import custom fields</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method_handleCustomFieldsInsert">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___destruct">__destruct</a>()
        
                                            <p><p>Clear the temporary dir if exists while moved the uploaded file</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="App_import.html#method___destruct">
App_import</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_staff_email_formatSampleData">staff_email_formatSampleData</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 15</div>
        <code>                    
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_perform">
        <div class="location">at line 20</div>
        <code>                    mixed
    <strong>perform</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>This method must be implemented on the child import class
This method will perform all the import actions and checks</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_failureRedirectURL">
        <div class="location">at line 154</div>
        <code>            protected        string
    <strong>failureRedirectURL</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>In some cases there will be some errors that we need to catch, after we catch the errors, we will redirect the user to this URL
This method is required and must be implemented in the child class</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_formatFieldNameForHeading">
        <div class="location">at line 132</div>
        <code>                    string
    <strong>formatFieldNameForHeading</strong>(string $field)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Format column/field name for table heading/csv</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$field</td>
                <td><p>the actual field name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_setDatabaseFields">
        <div class="location">in <a href="App_import.html#method_setDatabaseFields">
App_import</a> at line 158</div>
        <code>                    
    <strong>setDatabaseFields</strong>($fields)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sets database fields</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$fields</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_setFieldNames">
        <div class="location">in <a href="App_import.html#method_setFieldNames">
App_import</a> at line 169</div>
        <code>                    
    <strong>setFieldNames</strong>($fieldNames)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sets database fields</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$fieldNames</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getDatabaseFields">
        <div class="location">in <a href="App_import.html#method_getDatabaseFields">
App_import</a> at line 180</div>
        <code>                    array
    <strong>getDatabaseFields</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get database fields</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getImportableDatabaseFields">
        <div class="location">in <a href="App_import.html#method_getImportableDatabaseFields">
App_import</a> at line 189</div>
        <code>            protected        array
    <strong>getImportableDatabaseFields</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get importable database fields</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_setCustomFields">
        <div class="location">in <a href="App_import.html#method_setCustomFields">
App_import</a> at line 211</div>
        <code>                    
    <strong>setCustomFields</strong>(object $fields)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set custom fields that will be used for import</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>object</td>
                <td>$fields</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getCustomFields">
        <div class="location">in <a href="App_import.html#method_getCustomFields">
App_import</a> at line 222</div>
        <code>                    array
    <strong>getCustomFields</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get custom fields</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_setSimulation">
        <div class="location">in <a href="App_import.html#method_setSimulation">
App_import</a> at line 231</div>
        <code>                    
    <strong>setSimulation</strong>(bool $bool)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set simulation</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$bool</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_isSimulation">
        <div class="location">in <a href="App_import.html#method_isSimulation">
App_import</a> at line 242</div>
        <code>                    bool
    <strong>isSimulation</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check whether the request is simulation</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getSimulationData">
        <div class="location">in <a href="App_import.html#method_getSimulationData">
App_import</a> at line 251</div>
        <code>                    array
    <strong>getSimulationData</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get all stored simulation data that will be shown in table preview for simulation</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_setRows">
        <div class="location">in <a href="App_import.html#method_setRows">
App_import</a> at line 260</div>
        <code>                    
    <strong>setRows</strong>(array $rows)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the rows from the .csv file</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$rows</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getRows">
        <div class="location">in <a href="App_import.html#method_getRows">
App_import</a> at line 271</div>
        <code>                    
    <strong>getRows</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the rows stored from the .csv file</p></p>                        
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_totalRows">
        <div class="location">in <a href="App_import.html#method_totalRows">
App_import</a> at line 280</div>
        <code>                    mixed
    <strong>totalRows</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get total rows</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_setTemporaryFileLocation">
        <div class="location">in <a href="App_import.html#method_setTemporaryFileLocation">
App_import</a> at line 289</div>
        <code>                    
    <strong>setTemporaryFileLocation</strong>(string $location)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sets temporary file location from the form ($_FILES)</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$location</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getTemporaryFileLocation">
        <div class="location">in <a href="App_import.html#method_getTemporaryFileLocation">
App_import</a> at line 300</div>
        <code>                    mixed
    <strong>getTemporaryFileLocation</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get temporary file location</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_setFilename">
        <div class="location">in <a href="App_import.html#method_setFilename">
App_import</a> at line 309</div>
        <code>                    
    <strong>setFilename</strong>(string $name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sets filename from the form ($_FILES)</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$name</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getFilename">
        <div class="location">in <a href="App_import.html#method_getFilename">
App_import</a> at line 320</div>
        <code>                    mixed
    <strong>getFilename</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get filename from the form ($_FILES)</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_incrementImported">
        <div class="location">in <a href="App_import.html#method_incrementImported">
App_import</a> at line 329</div>
        <code>                    object
    <strong>incrementImported</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Increment the total imported number</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_totalImported">
        <div class="location">in <a href="App_import.html#method_totalImported">
App_import</a> at line 340</div>
        <code>                    mixed
    <strong>totalImported</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get total imported</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getNotImportableFields">
        <div class="location">in <a href="App_import.html#method_getNotImportableFields">
App_import</a> at line 350</div>
        <code>                    array
    <strong>getNotImportableFields</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get not importable fields
Child class should define property e.q. protected $notImportableFields = ['name'];</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_maxInputVarsWarningHtml">
        <div class="location">in <a href="App_import.html#method_maxInputVarsWarningHtml">
App_import</a> at line 359</div>
        <code>                    mixed
    <strong>maxInputVarsWarningHtml</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Checks and show HTML warning for max_input_vars based on total rows</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_downloadSampleFormHtml">
        <div class="location">in <a href="App_import.html#method_downloadSampleFormHtml">
App_import</a> at line 379</div>
        <code>                    string
    <strong>downloadSampleFormHtml</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get HTML form for download sample .csv file</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td>x</td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_simulationDataInfo">
        <div class="location">in <a href="App_import.html#method_simulationDataInfo">
App_import</a> at line 395</div>
        <code>                    string
    <strong>simulationDataInfo</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>General info for simulation data</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_addImportGuidelinesInfo">
        <div class="location">in <a href="App_import.html#method_addImportGuidelinesInfo">
App_import</a> at line 401</div>
        <code>                    
    <strong>addImportGuidelinesInfo</strong>($text, $isImportant = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$text</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$isImportant</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_importGuidelinesInfoHtml">
        <div class="location">in <a href="App_import.html#method_importGuidelinesInfoHtml">
App_import</a> at line 409</div>
        <code>                    
    <strong>importGuidelinesInfoHtml</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_downloadSample">
        <div class="location">in <a href="App_import.html#method_downloadSample">
App_import</a> at line 425</div>
        <code>                    mixed
    <strong>downloadSample</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Download sample .csv file</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_createSampleTableHtml">
        <div class="location">in <a href="App_import.html#method_createSampleTableHtml">
App_import</a> at line 473</div>
        <code>                    string
    <strong>createSampleTableHtml</strong>(bool $simulation = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create sample table for sample data and simulation table results</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$simulation</td>
                <td><p>where the table data should be taken from simultion data</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_getRequiredFields">
        <div class="location">in <a href="App_import.html#method_getRequiredFields">
App_import</a> at line 555</div>
        <code>                    array
    <strong>getRequiredFields</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get required import fields
Child class should define property e.q. protected $requiredFields = ['name'];</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initialize">
        <div class="location">in <a href="App_import.html#method_initialize">
App_import</a> at line 565</div>
        <code>            protected        object
    <strong>initialize</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>This is the main function that will initialize the import before parsing all data
*<strong>* IMPORTANT ***</strong> The child class must call this method inside the perform method</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_readFileRows">
        <div class="location">in <a href="App_import.html#method_readFileRows">
App_import</a> at line 642</div>
        <code>            protected        mixed
    <strong>readFileRows</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Read the rows and store them into $rows</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_checkNullValueAddedByUser">
        <div class="location">in <a href="App_import.html#method_checkNullValueAddedByUser">
App_import</a> at line 679</div>
        <code>            protected        mixed
    <strong>checkNullValueAddedByUser</strong>(string $val)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Some users enter in the .csv rows data e.q. NULL or null
To prevent storing this as string in database we shoul make the value empty
This is useful too when checking for required fields</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$val</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_trimInsertValues">
        <div class="location">in <a href="App_import.html#method_trimInsertValues">
App_import</a> at line 693</div>
        <code>            protected        array
    <strong>trimInsertValues</strong>(array $insert)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Trim the values before inserting</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$insert</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_handleCustomFieldsInsert">
        <div class="location">in <a href="App_import.html#method_handleCustomFieldsInsert">
App_import</a> at line 711</div>
        <code>            protected        null
    <strong>handleCustomFieldsInsert</strong>(mixed $rel_id, array $row, mixed $fieldNumber, mixed $rowNumber, string $customFieldTo)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Function responsible to store the import custom fields</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$rel_id</td>
                <td><p>the ID e.q. lead_id or item_id</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$row</td>
                <td><p>the actual row from the loop in the child class</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$fieldNumber</td>
                <td><p>field number</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$rowNumber</td>
                <td><p>the row number, used for simulation data</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$customFieldTo</td>
                <td><p>where this custom fields belongs</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>null</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___destruct">
        <div class="location">in <a href="App_import.html#method___destruct">
App_import</a> at line 744</div>
        <code>                    
    <strong>__destruct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Clear the temporary dir if exists while moved the uploaded file</p></p>                        
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_staff_email_formatSampleData">
        <div class="location">at line 149</div>
        <code>            protected        
    <strong>staff_email_formatSampleData</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

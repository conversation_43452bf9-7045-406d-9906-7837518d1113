<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Table | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Table" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Table    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Table</strong>
</p>

        
    
        

            <div class="description">
            <p><p>HTML Table Generating Class</p></p>            <p><p>Lets you create tables manually or from database result objects, or arrays.</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_rows">
                                                                                array
                                                                                
                                    </td>
                <td>$rows</td>
                <td class="last"><p>Data for table rows</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_heading">
                                                                                array
                                                                                
                                    </td>
                <td>$heading</td>
                <td class="last"><p>Data for table heading</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_auto_heading">
                                                                                bool
                                                                                
                                    </td>
                <td>$auto_heading</td>
                <td class="last"><p>Whether or not to automatically create the table header</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_caption">
                                                                                string
                                                                                
                                    </td>
                <td>$caption</td>
                <td class="last"><p>Table caption</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_template">
                                                                                array
                                                                                
                                    </td>
                <td>$template</td>
                <td class="last"><p>Table layout template</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_newline">
                                                                                string
                                                                                
                                    </td>
                <td>$newline</td>
                <td class="last"><p>Newline setting</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_empty_cells">
                                                                                string
                                                                                
                                    </td>
                <td>$empty_cells</td>
                <td class="last"><p>Contents of empty cells</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_function">
                                                                                function
                                                                                
                                    </td>
                <td>$function</td>
                <td class="last"><p>Callback for custom table layout</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $config = array())
        
                                            <p><p>Set the template from the table config file if it exists</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_set_template">set_template</a>(array $template)
        
                                            <p><p>Set the template</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Table.html">CI_Table</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_heading">set_heading</a>($args = array())
        
                                            <p><p>Set the table heading</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_make_columns">make_columns</a>(array $array = array(), int $col_limit = 0)
        
                                            <p><p>Set columns. Takes a one-dimensional array as input and creates
a multi-dimensional array with a depth equal to the number of
columns. This allows a single array with many elements to be
displayed in a table that has a fixed column count.</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Table.html">CI_Table</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_empty">set_empty</a>(mixed $value)
        
                                            <p><p>Set &quot;empty&quot; cells</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Table.html">CI_Table</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_add_row">add_row</a>($args = array())
        
                                            <p><p>Add a table row</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method__prep_args">_prep_args</a>($args)
        
                                            <p><p>Prep Args</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Table.html">CI_Table</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_set_caption">set_caption</a>(string $caption)
        
                                            <p><p>Add a table caption</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_generate">generate</a>(mixed $table_data = NULL)
        
                                            <p><p>Generate the table</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Table.html">CI_Table</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_clear">clear</a>()
        
                                            <p><p>Clears the table arrays.  Useful if multiple tables are being generated</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__set_from_db_result">_set_from_db_result</a>(<a href="CI_DB_result.html">CI_DB_result</a> $object)
        
                                            <p><p>Set table data from a database result object</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__set_from_array">_set_from_array</a>(array $data)
        
                                            <p><p>Set table data from an array</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__compile_template">_compile_template</a>()
        
                                            <p><p>Compile Template</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method__default_template">_default_template</a>()
        
                                            <p><p>Default Template</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 115</div>
        <code>                    void
    <strong>__construct</strong>(array $config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the template from the table config file if it exists</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td><p>(default: array())</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_template">
        <div class="location">at line 134</div>
        <code>                    bool
    <strong>set_template</strong>(array $template)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the template</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$template</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_heading">
        <div class="location">at line 155</div>
        <code>                    <a href="CI_Table.html">CI_Table</a>
    <strong>set_heading</strong>($args = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the table heading</p></p>                <p><p>Can be passed as an array or discreet params</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$args</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Table.html">CI_Table</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_make_columns">
        <div class="location">at line 173</div>
        <code>                    array
    <strong>make_columns</strong>(array $array = array(), int $col_limit = 0)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set columns. Takes a one-dimensional array as input and creates
a multi-dimensional array with a depth equal to the number of
columns. This allows a single array with many elements to be
displayed in a table that has a fixed column count.</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$array</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$col_limit</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_empty">
        <div class="location">at line 219</div>
        <code>                    <a href="CI_Table.html">CI_Table</a>
    <strong>set_empty</strong>(mixed $value)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set &quot;empty&quot; cells</p></p>                <p><p>Can be passed as an array or discreet params</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$value</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Table.html">CI_Table</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_row">
        <div class="location">at line 235</div>
        <code>                    <a href="CI_Table.html">CI_Table</a>
    <strong>add_row</strong>($args = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add a table row</p></p>                <p><p>Can be passed as an array or discreet params</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$args</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Table.html">CI_Table</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__prep_args">
        <div class="location">at line 251</div>
        <code>            protected        array
    <strong>_prep_args</strong>($args)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Prep Args</p></p>                <p><p>Ensures a standard associative array format for all cell data</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$args</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_caption">
        <div class="location">at line 277</div>
        <code>                    <a href="CI_Table.html">CI_Table</a>
    <strong>set_caption</strong>(string $caption)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add a table caption</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$caption</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Table.html">CI_Table</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_generate">
        <div class="location">at line 291</div>
        <code>                    string
    <strong>generate</strong>(mixed $table_data = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Generate the table</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$table_data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_clear">
        <div class="location">at line 425</div>
        <code>                    <a href="CI_Table.html">CI_Table</a>
    <strong>clear</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Clears the table arrays.  Useful if multiple tables are being generated</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Table.html">CI_Table</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_from_db_result">
        <div class="location">at line 441</div>
        <code>            protected        void
    <strong>_set_from_db_result</strong>(<a href="CI_DB_result.html">CI_DB_result</a> $object)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set table data from a database result object</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td><a href="CI_DB_result.html">CI_DB_result</a></td>
                <td>$object</td>
                <td><p>Database result object</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__set_from_array">
        <div class="location">at line 463</div>
        <code>            protected        void
    <strong>_set_from_array</strong>(array $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set table data from an array</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__compile_template">
        <div class="location">at line 483</div>
        <code>            protected        void
    <strong>_compile_template</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Compile Template</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__default_template">
        <div class="location">at line 508</div>
        <code>            protected        array
    <strong>_default_template</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Default Template</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

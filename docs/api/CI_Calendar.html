<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Calendar | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Calendar" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Calendar    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Calendar</strong>
</p>

        
    
        

            <div class="description">
            <p><p>CodeIgniter Calendar Class</p></p>            <p><p>This class enables the creation of calendars</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_template">
                                                                                mixed
                                                                                
                                    </td>
                <td>$template</td>
                <td class="last"><p>Calendar layout template</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_replacements">
                                                                                array
                                                                                
                                    </td>
                <td>$replacements</td>
                <td class="last"><p>Replacements array for template</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_start_day">
                                                                                string
                                                                                
                                    </td>
                <td>$start_day</td>
                <td class="last"><p>Day of the week to start the calendar on</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_month_type">
                                                                                string
                                                                                
                                    </td>
                <td>$month_type</td>
                <td class="last"><p>How to display months</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_day_type">
                                                                                string
                                                                                
                                    </td>
                <td>$day_type</td>
                <td class="last"><p>How to display names of days</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_show_next_prev">
                                                                                bool
                                                                                
                                    </td>
                <td>$show_next_prev</td>
                <td class="last"><p>Whether to show next/prev month links</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_next_prev_url">
                                                                                bool
                                                                                
                                    </td>
                <td>$next_prev_url</td>
                <td class="last"><p>Url base to use for next/prev month links</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_show_other_days">
                                                                                bool
                                                                                
                                    </td>
                <td>$show_other_days</td>
                <td class="last"><p>Show days of other months</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_CI">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$CI</td>
                <td class="last"><p>CI Singleton</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $config = array())
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Calendar.html">CI_Calendar</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_initialize">initialize</a>($config = array())
        
                                            <p><p>Initialize the user preferences</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_generate">generate</a>($year = &#039;&#039;, $month = &#039;&#039;, $data = array())
        
                                            <p><p>Generate the calendar</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_month_name">get_month_name</a>($month)
        
                                            <p><p>Get Month Name</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_day_names">get_day_names</a>($day_type = &#039;&#039;)
        
                                            <p><p>Get Day Names</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_adjust_date">adjust_date</a>($month, $year)
        
                                            <p><p>Adjust Date</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_get_total_days">get_total_days</a>($month, $year)
        
                                            <p><p>Total days in a given month</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_default_template">default_template</a>()
        
                                            <p><p>Set Default Template Data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    <a href="CI_Calendar.html">CI_Calendar</a>
                </div>
                <div class="col-md-8">
                    <a href="#method_parse_template">parse_template</a>()
        
                                            <p><p>Parse Template</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 130</div>
        <code>                    void
    <strong>__construct</strong>(array $config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                <p><p>Loads the calendar language file and sets the default time reference.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td><p>Calendar options</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initialize">
        <div class="location">at line 150</div>
        <code>                    <a href="CI_Calendar.html">CI_Calendar</a>
    <strong>initialize</strong>($config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize the user preferences</p></p>                <p><p>Accepts an associative array as input, containing display preferences</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$config</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Calendar.html">CI_Calendar</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_generate">
        <div class="location">at line 179</div>
        <code>                    string
    <strong>generate</strong>($year = &#039;&#039;, $month = &#039;&#039;, $data = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Generate the calendar</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$year</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$month</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_month_name">
        <div class="location">at line 353</div>
        <code>                    string
    <strong>get_month_name</strong>($month)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Month Name</p></p>                <p><p>Generates a textual month name based on the numeric
month provided.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$month</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_day_names">
        <div class="location">at line 380</div>
        <code>                    array
    <strong>get_day_names</strong>($day_type = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get Day Names</p></p>                <p><p>Returns an array of day names (Sunday, Monday, etc.) based
on the type. Options: long, short, abr</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$day_type</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_adjust_date">
        <div class="location">at line 422</div>
        <code>                    array
    <strong>adjust_date</strong>($month, $year)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Adjust Date</p></p>                <p><p>This function makes sure that we have a valid month/year.
For example, if you submit 13 as the month, the year will
increment and the month will become January.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$month</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$year</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_total_days">
        <div class="location">at line 458</div>
        <code>                    int
    <strong>get_total_days</strong>($month, $year)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Total days in a given month</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$month</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$year</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_default_template">
        <div class="location">at line 473</div>
        <code>                    array
    <strong>default_template</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Default Template Data</p></p>                <p><p>This is used in the event that the user has not created their own template</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_parse_template">
        <div class="location">at line 513</div>
        <code>                    <a href="CI_Calendar.html">CI_Calendar</a>
    <strong>parse_template</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Parse Template</p></p>                <p><p>Harvests the data within the template {pseudo-variables}
used to display the calendar</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td><a href="CI_Calendar.html">CI_Calendar</a></td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

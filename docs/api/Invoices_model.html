<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>Invoices_model | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:Invoices_model" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>Invoices_model    
            </h1>
    </div>

    
    <p>        class
    <strong>Invoices_model</strong>        extends <a href="App_Model.html">App_Model</a>
</p>

        
    
        

            
        
            <h2>Constants</h2>    <table class="table table-condensed">
                    <tr>
                <td>
                                                                                                                        STATUS_UNPAID
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        STATUS_PAID
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        STATUS_PARTIALLY
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        STATUS_OVERDUE
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        STATUS_CANCELLED
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        STATUS_DRAFT
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        STATUS_DRAFT_NUMBER
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        REQUEST_STATUS_1
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        REQUEST_STATUS_2
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
                    <tr>
                <td>
                                                                                                                        STATUS_INVOICE_DRAFT
                                    </td>
                <td class="last">
                    <p><em></em></p>
                    <p></p>
                </td>
            </tr>
            </table>

    
    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___get">__get</a>(string $key)
        
                                            <p><p>__get magic</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Model.html#method___get">
CI_Model</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_statuses">get_statuses</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_request_change_status">request_change_status</a>($id, $value)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_sale_agents">get_sale_agents</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_unpaid_invoices">get_unpaid_invoices</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array|object
                </div>
                <div class="col-md-8">
                    <a href="#method_get">get</a>(mixed $id = &#039;&#039;, array $where = [], array $relations = [])
        
                                            <p><p>Get invoice by id</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_invoice_item">get_invoice_item</a>($id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_mark_as_cancelled">mark_as_cancelled</a>($id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_unmark_as_cancelled">unmark_as_cancelled</a>($id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_invoice_recurring_invoices">get_invoice_recurring_invoices</a>(mixed $id)
        
                                            <p><p>Get this invoice generated recurring invoices</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_invoices_total">get_invoices_total</a>(mixed $data)
        
                                            <p><p>Get invoice total from all statuses</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_add_request_invoice">add_request_invoice</a>(array $data, $expense = false)
        
                                            <p><p>Insert new add_request invoice to database</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_permission_request_invoice">permission_request_invoice</a>(array $invoiceid)
        
                                            <p><p>check permission request invoiceid
Nhân viên sale, trạng thái chưa thanh toán thì mới có quyền tạo yêu cầu xuất hóa đơn</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_add">add</a>(array $data, $expense = false)
        
                                            <p><p>Insert new invoice to database</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_expenses_to_bill">get_expenses_to_bill</a>($clientid)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_check_for_merge_invoice">check_for_merge_invoice</a>($client_id, $current_invoice = &#039;&#039;)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_copy">copy</a>(mixed $id)
        
                                            <p><p>Copy invoice</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_update">update</a>(array $data, mixed $id)
        
                                            <p><p>Update invoice data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_attachments">get_attachments</a>($invoiceid, $id = &#039;&#039;)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    total
                </div>
                <div class="col-md-8">
                    <a href="#method_get_total_price_invoice">get_total_price_invoice</a>($request)
        
                                            <p>get_total_price_invoice</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_list_invoice_request">list_invoice_request</a>()
        
                                            <p>List_invoice_request</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_delete_attachment">delete_attachment</a>(mixed $id)
        
                                            <p><p>Delete invoice attachment</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_delete">delete</a>(mixed $id, $simpleDelete = false)
        
                                            <p><p>Delete invoice items and all connections</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_set_invoice_sent">set_invoice_sent</a>(mixed $id, mixed $manually = false, $emails_sent = [], $is_status_updated = false)
        
                                            <p><p>Set invoice to sent when email is successfuly sended to client</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_send_invoice_overdue_notice">send_invoice_overdue_notice</a>(mxied $id)
        
                                            <p><p>Send overdue notice to client for this invoice</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_send_invoice_due_notice">send_invoice_due_notice</a>(mxied $id)
        
                                            <p><p>Send due notice to client for the given invoice</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_send_invoice_to_client">send_invoice_to_client</a>(mixed $id, $template_name = &#039;&#039;, bool $attachpdf = true, $cc = &#039;&#039;, $manually = false, $attachStatement = [])
        
                                            <p><p>Send invoice to client</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_draft">is_draft</a>(int $id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_invoice_activity">get_invoice_activity</a>(mixed $id)
        
                                            <p><p>All invoice activity</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_creator">get_creator</a>($id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_log_invoice_activity">log_invoice_activity</a>(mixed $id, string $description = &#039;&#039;, $client = false, $additional_data = &#039;&#039;, $customer_admin_id = &#039;&#039;)
        
                                            <p><p>Log invoice activity to database</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_invoices_years">get_invoices_years</a>()
        
                                            <p><p>Get the invoices years</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_change_invoice_number_when_status_draft">change_invoice_number_when_status_draft</a>(int $id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_increment_next_number">increment_next_number</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_decrement_next_number">decrement_next_number</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_contacts_for_invoice_emails">get_contacts_for_invoice_emails</a>(int $client_id)
        
                                            <p><p>Get the contacts that should receive invoice related emails</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_check_permission_customer_source_invoice">check_permission_customer_source_invoice</a>($invoice)
        
                                            <p><p>Check permission customer source invoice</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_customer_source">get_customer_source</a>($invoice_id)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 48</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___get">
        <div class="location">in <a href="CI_Model.html#method___get">
CI_Model</a> at line 67</div>
        <code>                    
    <strong>__get</strong>(string $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>__get magic</p></p>                <p><p>Allows models to access CI's loaded classes using the same
syntax as controllers.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_statuses">
        <div class="location">at line 53</div>
        <code>                    
    <strong>get_statuses</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_request_change_status">
        <div class="location">at line 59</div>
        <code>                    
    <strong>request_change_status</strong>($id, $value)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$value</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_sale_agents">
        <div class="location">at line 67</div>
        <code>                    
    <strong>get_sale_agents</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_unpaid_invoices">
        <div class="location">at line 72</div>
        <code>                    
    <strong>get_unpaid_invoices</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get">
        <div class="location">at line 107</div>
        <code>                    array|object
    <strong>get</strong>(mixed $id = &#039;&#039;, array $where = [], array $relations = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get invoice by id</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$id</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$where</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$relations</td>
                <td><p>custom relation for the query</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array|object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_invoice_item">
        <div class="location">at line 173</div>
        <code>                    
    <strong>get_invoice_item</strong>($id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mark_as_cancelled">
        <div class="location">at line 180</div>
        <code>                    
    <strong>mark_as_cancelled</strong>($id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_unmark_as_cancelled">
        <div class="location">at line 205</div>
        <code>                    
    <strong>unmark_as_cancelled</strong>($id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_invoice_recurring_invoices">
        <div class="location">at line 229</div>
        <code>                    array
    <strong>get_invoice_recurring_invoices</strong>(mixed $id)
        </code>
    </h3>
    <div class="details"><i>Since: Version 1.0.1</i>
            <br>    
    
            

        <div class="method-description">
                            <p><p>Get this invoice generated recurring invoices</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$id</td>
                <td><p>main invoice id</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_invoices_total">
        <div class="location">at line 249</div>
        <code>                    array
    <strong>get_invoices_total</strong>(mixed $data)
        </code>
    </h3>
    <div class="details"><i>Since: Version 1.0.2</i>
            <br>    
    
            

        <div class="method-description">
                            <p><p>Get invoice total from all statuses</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$data</td>
                <td><p>$_POST data</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_request_invoice">
        <div class="location">at line 334</div>
        <code>                    mixed
    <strong>add_request_invoice</strong>(array $data, $expense = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Insert new add_request invoice to database</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$data</td>
                <td><p>add_request invoice</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$expense</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><ul>
<li>false if not insert, invoice ID if succes</li>
</ul></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_permission_request_invoice">
        <div class="location">at line 351</div>
        <code>                    mixed
    <strong>permission_request_invoice</strong>(array $invoiceid)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>check permission request invoiceid
Nhân viên sale, trạng thái chưa thanh toán thì mới có quyền tạo yêu cầu xuất hóa đơn</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$invoiceid</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><ul>
<li>bool</li>
</ul></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add">
        <div class="location">at line 378</div>
        <code>                    mixed
    <strong>add</strong>(array $data, $expense = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Insert new invoice to database</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$data</td>
                <td><p>invoice data</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$expense</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><ul>
<li>false if not insert, invoice ID if succes</li>
</ul></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_expenses_to_bill">
        <div class="location">at line 626</div>
        <code>                    
    <strong>get_expenses_to_bill</strong>($clientid)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$clientid</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_check_for_merge_invoice">
        <div class="location">at line 637</div>
        <code>                    
    <strong>check_for_merge_invoice</strong>($client_id, $current_invoice = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$client_id</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$current_invoice</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_copy">
        <div class="location">at line 687</div>
        <code>                    mixed
    <strong>copy</strong>(mixed $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Copy invoice</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$id</td>
                <td><p>invoice id to copy</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_update">
        <div class="location">at line 804</div>
        <code>                    bool
    <strong>update</strong>(array $data, mixed $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Update invoice data</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$data</td>
                <td><p>invoice data</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$id</td>
                <td>invoiceid</td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_attachments">
        <div class="location">at line 1163</div>
        <code>                    
    <strong>get_attachments</strong>($invoiceid, $id = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$invoiceid</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_total_price_invoice">
        <div class="location">at line 1187</div>
        <code>                    total
    <strong>get_total_price_invoice</strong>($request)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>get_total_price_invoice</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$request</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>total</td>
            <td><p>price invoice</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_list_invoice_request">
        <div class="location">at line 1239</div>
        <code>                    array
    <strong>list_invoice_request</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>List_invoice_request</p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_delete_attachment">
        <div class="location">at line 1260</div>
        <code>                    bool
    <strong>delete_attachment</strong>(mixed $id)
        </code>
    </h3>
    <div class="details"><i>Since: Version 1.0.4</i>
            <br>    
    
            

        <div class="method-description">
                            <p><p>Delete invoice attachment</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$id</td>
                <td>attachmentid</td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_delete">
        <div class="location">at line 1293</div>
        <code>                    bool
    <strong>delete</strong>(mixed $id, $simpleDelete = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Delete invoice items and all connections</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$id</td>
                <td>invoiceid</td>
            </tr>
                    <tr>
                <td></td>
                <td>$simpleDelete</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_invoice_sent">
        <div class="location">at line 1466</div>
        <code>                    bool
    <strong>set_invoice_sent</strong>(mixed $id, mixed $manually = false, $emails_sent = [], $is_status_updated = false)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set invoice to sent when email is successfuly sended to client</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$id</td>
                <td>invoiceid</td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$manually</td>
                <td><p>is staff manually marking this invoice as sent</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$emails_sent</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$is_status_updated</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send_invoice_overdue_notice">
        <div class="location">at line 1515</div>
        <code>                    bool
    <strong>send_invoice_overdue_notice</strong>(mxied $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send overdue notice to client for this invoice</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mxied</td>
                <td>$id</td>
                <td>invoiceid</td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send_invoice_due_notice">
        <div class="location">at line 1599</div>
        <code>                    bool
    <strong>send_invoice_due_notice</strong>(mxied $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send due notice to client for the given invoice</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mxied</td>
                <td>$id</td>
                <td>invoiceid</td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send_invoice_to_client">
        <div class="location">at line 1683</div>
        <code>                    bool
    <strong>send_invoice_to_client</strong>(mixed $id, $template_name = &#039;&#039;, bool $attachpdf = true, $cc = &#039;&#039;, $manually = false, $attachStatement = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send invoice to client</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$id</td>
                <td>invoiceid</td>
            </tr>
                    <tr>
                <td></td>
                <td>$template_name</td>
                <td></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$attachpdf</td>
                <td><p>attach invoice pdf or not</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$cc</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$manually</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$attachStatement</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_draft">
        <div class="location">at line 1825</div>
        <code>                    bool
    <strong>is_draft</strong>(int $id)
        </code>
    </h3>
    <div class="details"><i>Since: 2.7.0 Check whether the given invoice is draft</i>
            <br>    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_invoice_activity">
        <div class="location">at line 1835</div>
        <code>                    array
    <strong>get_invoice_activity</strong>(mixed $id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>All invoice activity</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$id</td>
                <td>invoiceid</td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_creator">
        <div class="location">at line 1844</div>
        <code>                    
    <strong>get_creator</strong>($id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_log_invoice_activity">
        <div class="location">at line 1864</div>
        <code>                    
    <strong>log_invoice_activity</strong>(mixed $id, string $description = &#039;&#039;, $client = false, $additional_data = &#039;&#039;, $customer_admin_id = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Log invoice activity to database</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$id</td>
                <td>invoiceid</td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$description</td>
                <td><p>activity description</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$client</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$additional_data</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$customer_admin_id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_invoices_years">
        <div class="location">at line 1896</div>
        <code>                    array
    <strong>get_invoices_years</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the invoices years</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_change_invoice_number_when_status_draft">
        <div class="location">at line 1945</div>
        <code>                    int
    <strong>change_invoice_number_when_status_draft</strong>(int $id)
        </code>
    </h3>
    <div class="details"><i>Since: 2.7.0 Change the invoice number for status when it&#039;s draft</i>
            <br>    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_increment_next_number">
        <div class="location">at line 1967</div>
        <code>                    void
    <strong>increment_next_number</strong>()
        </code>
    </h3>
    <div class="details"><i>Since: 2.7.0 Increment the invoies next nubmer</i>
            <br>    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_decrement_next_number">
        <div class="location">at line 1982</div>
        <code>                    void
    <strong>decrement_next_number</strong>()
        </code>
    </h3>
    <div class="details"><i>Since: 2.7.0 Decrement the invoies next number</i>
            <br>    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_contacts_for_invoice_emails">
        <div class="location">at line 1996</div>
        <code>            protected        array
    <strong>get_contacts_for_invoice_emails</strong>(int $client_id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the contacts that should receive invoice related emails</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$client_id</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_check_permission_customer_source_invoice">
        <div class="location">at line 2010</div>
        <code>                    bool
    <strong>check_permission_customer_source_invoice</strong>($invoice)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Check permission customer source invoice</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$invoice</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_customer_source">
        <div class="location">at line 2046</div>
        <code>                    
    <strong>get_customer_source</strong>($invoice_id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$invoice_id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

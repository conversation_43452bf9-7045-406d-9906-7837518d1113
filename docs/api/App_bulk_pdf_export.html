<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>App_bulk_pdf_export | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:App_bulk_pdf_export" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>App_bulk_pdf_export    
            </h1>
    </div>

    
    <p>        class
    <strong>App_bulk_pdf_export</strong>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_config">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$config</td>
                <td class="last"><p>The export config</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"><p>Codeigniter instance</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_pdf_zip">
                                                                                object
                                                                                
                                    </td>
                <td>$pdf_zip</td>
                <td class="last"><p>This property is used to store the PDF</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_can_view">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$can_view</td>
                <td class="last"><p>Can view based on the $type property</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_type">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$type</td>
                <td class="last"><p>Export type
Possible values: invoices, estimates, credit_notes, payments, proposal</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_date_from">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$date_from</td>
                <td class="last"><p>Filter period from</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_date_to">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$date_to</td>
                <td class="last"><p>Filter period to</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_payment_mode">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$payment_mode</td>
                <td class="last"><p>Payments export payment mode</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_status">
                                        protected                                        mixed
                                                                                
                                    </td>
                <td>$status</td>
                <td class="last"><p>Status for estimates, invoices, credit notes, proposals</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_status_param_required">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$status_param_required</td>
                <td class="last"><p>Required status parameter</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_pdf_tag">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$pdf_tag</td>
                <td class="last"><p>PDF tag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_zip_dir">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$zip_dir</td>
                <td class="last"><p>Zip directory</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_years">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$years</td>
                <td class="last"><p>Unique years for the data that is exporting
Used to create the folders</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_client_id">
                                        protected                                        mixed
                                                                                
                                    </td>
                <td>$client_id</td>
                <td class="last"><p>Client ID if exporting for specific client</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_client_id_column">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$client_id_column</td>
                <td class="last"><p>Client ID Column</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_in_folder">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$in_folder</td>
                <td class="last"><p>Add the main folder contents in folder
e.q. customer-name/YEAR/INVOICE.pdf</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_redirect_on_error">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$redirect_on_error</td>
                <td class="last"><p>Redirect user to specific url after error
This parameter is required</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>($config)
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_export">export</a>()
        
                                            <p><p>The main function for exporting</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_payments">payments</a>()
        
                                            <p><p>Create payment export</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_expenses">expenses</a>()
        
                                            <p><p>Create payment export</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_estimates">estimates</a>()
        
                                            <p><p>Create estimates export</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_invoices">invoices</a>()
        
                                            <p><p>Create invoices export</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_proposals">proposals</a>()
        
                                            <p><p>Create proposals export</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_credit_notes">credit_notes</a>()
        
                                            <p><p>Create credit notes export</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_client_id_column">set_client_id_column</a>(string $column)
        
                                            <p><p>Sets the client id column</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_client_id">set_client_id</a>(mixed $client_id)
        
                                            <p><p>Set client id</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_in_folder">in_folder</a>(string $folder)
        
                                            <p><p>Set export contents in folder</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    null
                </div>
                <div class="col-md-8">
                    <a href="#method_zip">zip</a>()
        
                                            <p><p>Used to zip the data in the folder</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_save_to_dir">save_to_dir</a>(object $object, mixed $pdf, $file_name, string $folderName = &#039;&#039;)
        
                                            <p><p>Save the PDF to the temporary directory to zip later</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_date_query">set_date_query</a>()
        
                                            <p><p>Set date query for the data that is exported</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_finalize">finalize</a>()
        
                                            <p><p>Finalize all the query and necessary actions, used for common export options</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_years_and_create_directories">set_years_and_create_directories</a>(array $years)
        
                                            <p><p>Set years property and create years directories</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_get_date_column">get_date_column</a>()
        
                                            <p><p>Get the date column for the exported feature</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    null
                </div>
                <div class="col-md-8">
                    <a href="#method_clear">clear</a>(string $dir)
        
                                            <p><p>Clear the temporary folder</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 117</div>
        <code>                    
    <strong>__construct</strong>($config)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$config</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_export">
        <div class="location">at line 177</div>
        <code>                    mixed
    <strong>export</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>The main function for exporting</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_payments">
        <div class="location">at line 203</div>
        <code>            protected        object
    <strong>payments</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create payment export</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_expenses">
        <div class="location">at line 248</div>
        <code>            protected        object
    <strong>expenses</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create payment export</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_estimates">
        <div class="location">at line 332</div>
        <code>            protected        object
    <strong>estimates</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create estimates export</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_invoices">
        <div class="location">at line 364</div>
        <code>            protected        object
    <strong>invoices</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create invoices export</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_proposals">
        <div class="location">at line 401</div>
        <code>                    object
    <strong>proposals</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create proposals export</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_credit_notes">
        <div class="location">at line 433</div>
        <code>                    object
    <strong>credit_notes</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Create credit notes export</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_client_id_column">
        <div class="location">at line 463</div>
        <code>                    
    <strong>set_client_id_column</strong>(string $column)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sets the client id column</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$column</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_client_id">
        <div class="location">at line 474</div>
        <code>                    
    <strong>set_client_id</strong>(mixed $client_id)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set client id</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$client_id</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_in_folder">
        <div class="location">at line 486</div>
        <code>                    object
    <strong>in_folder</strong>(string $folder)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set export contents in folder</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$folder</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_zip">
        <div class="location">at line 497</div>
        <code>            protected        null
    <strong>zip</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Used to zip the data in the folder</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>null</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_save_to_dir">
        <div class="location">at line 513</div>
        <code>            protected        string
    <strong>save_to_dir</strong>(object $object, mixed $pdf, $file_name, string $folderName = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Save the PDF to the temporary directory to zip later</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>object</td>
                <td>$object</td>
                <td><p>the data object, e.q. invoice, estimate</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$pdf</td>
                <td><p>the actual PDF</p></td>
            </tr>
                    <tr>
                <td></td>
                <td>$file_name</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$folderName</td>
                <td><p>additional folder for storage</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_date_query">
        <div class="location">at line 550</div>
        <code>            protected        
    <strong>set_date_query</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set date query for the data that is exported</p></p>                        
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_finalize">
        <div class="location">at line 566</div>
        <code>            protected        array
    <strong>finalize</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Finalize all the query and necessary actions, used for common export options</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_years_and_create_directories">
        <div class="location">at line 596</div>
        <code>            protected        
    <strong>set_years_and_create_directories</strong>(array $years)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set years property and create years directories</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$years</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_date_column">
        <div class="location">at line 618</div>
        <code>            protected        string
    <strong>get_date_column</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get the date column for the exported feature</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_clear">
        <div class="location">at line 634</div>
        <code>                    null
    <strong>clear</strong>(string $dir)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Clear the temporary folder</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$dir</td>
                <td><p>directory to clear</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>null</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

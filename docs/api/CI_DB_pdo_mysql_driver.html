<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_DB_pdo_mysql_driver | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_DB_pdo_mysql_driver" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_DB_pdo_mysql_driver    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_DB_pdo_mysql_driver</strong>        extends <a href="CI_DB_pdo_driver.html">CI_DB_pdo_driver</a>
</p>

        
    
        

            <div class="description">
            <p><p>PDO MySQL Database Adapter Class</p></p>            <p><p>Note: _DB is an extender class that the app controller
creates dynamically based on whether the query builder
class is being used or not.</p></p>        </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_dsn">
                                                                                string
                                                                                
                                    </td>
                <td>$dsn</td>
                <td class="last"><p>Data Source Name / Connect string</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_dsn">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_username">
                                                                                string
                                                                                
                                    </td>
                <td>$username</td>
                <td class="last">Username</td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_username">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_password">
                                                                                string
                                                                                
                                    </td>
                <td>$password</td>
                <td class="last">Password</td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_password">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_hostname">
                                                                                string
                                                                                
                                    </td>
                <td>$hostname</td>
                <td class="last">Hostname</td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_hostname">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_database">
                                                                                string
                                                                                
                                    </td>
                <td>$database</td>
                <td class="last"><p>Database name</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_database">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_dbdriver">
                                                                                string
                                                                                
                                    </td>
                <td>$dbdriver</td>
                <td class="last"><p>Database driver</p></td>
                <td><small>from&nbsp;<a href="CI_DB_pdo_driver.html#property_dbdriver">
CI_DB_pdo_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_subdriver">
                                                                                string
                                                                                
                                    </td>
                <td>$subdriver</td>
                <td class="last">Sub-driver</td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_dbprefix">
                                                                                string
                                                                                
                                    </td>
                <td>$dbprefix</td>
                <td class="last"><p>Table prefix</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_dbprefix">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_char_set">
                                                                                string
                                                                                
                                    </td>
                <td>$char_set</td>
                <td class="last"><p>Character set</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_char_set">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_dbcollat">
                                                                                string
                                                                                
                                    </td>
                <td>$dbcollat</td>
                <td class="last">Collation</td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_dbcollat">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_encrypt">
                                                                                mixed
                                                                                
                                    </td>
                <td>$encrypt</td>
                <td class="last"><p>Encryption flag/data</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_encrypt">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_swap_pre">
                                                                                string
                                                                                
                                    </td>
                <td>$swap_pre</td>
                <td class="last"><p>Swap Prefix</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_swap_pre">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_port">
                                                                                int
                                                                                
                                    </td>
                <td>$port</td>
                <td class="last"><p>Database port</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_port">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_pconnect">
                                                                                bool
                                                                                
                                    </td>
                <td>$pconnect</td>
                <td class="last"><p>Persistent connection flag</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_pconnect">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_conn_id">
                                                                                object|resource
                                                                                
                                    </td>
                <td>$conn_id</td>
                <td class="last"><p>Connection ID</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_conn_id">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_result_id">
                                                                                object|resource
                                                                                
                                    </td>
                <td>$result_id</td>
                <td class="last"><p>Result ID</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_result_id">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_db_debug">
                                                                                bool
                                                                                
                                    </td>
                <td>$db_debug</td>
                <td class="last"><p>Debug flag</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_db_debug">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_benchmark">
                                                                                int
                                                                                
                                    </td>
                <td>$benchmark</td>
                <td class="last"><p>Benchmark time</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_benchmark">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_query_count">
                                                                                int
                                                                                
                                    </td>
                <td>$query_count</td>
                <td class="last"><p>Executed queries count</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_query_count">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_bind_marker">
                                                                                string
                                                                                
                                    </td>
                <td>$bind_marker</td>
                <td class="last"><p>Bind marker</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_bind_marker">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_save_queries">
                                                                                bool
                                                                                
                                    </td>
                <td>$save_queries</td>
                <td class="last"><p>Save queries flag</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_save_queries">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_queries">
                                                                                string[]
                                                                                
                                    </td>
                <td>$queries</td>
                <td class="last"><p>Queries list</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_queries">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_query_times">
                                                                                array
                                                                                
                                    </td>
                <td>$query_times</td>
                <td class="last"><p>Query times</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_query_times">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_data_cache">
                                                                                array
                                                                                
                                    </td>
                <td>$data_cache</td>
                <td class="last"><p>Data cache</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_data_cache">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_trans_enabled">
                                                                                bool
                                                                                
                                    </td>
                <td>$trans_enabled</td>
                <td class="last"><p>Transaction enabled flag</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_trans_enabled">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_trans_strict">
                                                                                bool
                                                                                
                                    </td>
                <td>$trans_strict</td>
                <td class="last"><p>Strict transaction mode flag</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_trans_strict">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__trans_depth">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$_trans_depth</td>
                <td class="last"><p>Transaction depth level</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property__trans_depth">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__trans_status">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_trans_status</td>
                <td class="last"><p>Transaction status flag</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property__trans_status">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__trans_failure">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_trans_failure</td>
                <td class="last"><p>Transaction failure flag</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property__trans_failure">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_cache_on">
                                                                                bool
                                                                                
                                    </td>
                <td>$cache_on</td>
                <td class="last"><p>Cache On flag</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_cache_on">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_cachedir">
                                                                                bool
                                                                                
                                    </td>
                <td>$cachedir</td>
                <td class="last"><p>Cache directory path</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_cachedir">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_cache_autodel">
                                                                                bool
                                                                                
                                    </td>
                <td>$cache_autodel</td>
                <td class="last"><p>Cache auto-delete flag</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_cache_autodel">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_CACHE">
                                                                                object
                                                                                
                                    </td>
                <td>$CACHE</td>
                <td class="last"><p>DB Cache object</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property_CACHE">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__protect_identifiers">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_protect_identifiers</td>
                <td class="last"><p>Protect identifiers flag</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property__protect_identifiers">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__reserved_identifiers">
                                        protected                                        string[]
                                                                                
                                    </td>
                <td>$_reserved_identifiers</td>
                <td class="last"><p>List of reserved identifiers</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property__reserved_identifiers">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__escape_char">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_escape_char</td>
                <td class="last"><p>Identifier escape character</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__like_escape_str">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_like_escape_str</td>
                <td class="last"><p>ESCAPE statement string</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property__like_escape_str">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__like_escape_chr">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_like_escape_chr</td>
                <td class="last"><p>ESCAPE character</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property__like_escape_chr">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__random_keyword">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_random_keyword</td>
                <td class="last"><p>ORDER BY random keyword</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property__random_keyword">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__count_string">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_count_string</td>
                <td class="last"><p>COUNT string</p></td>
                <td><small>from&nbsp;<a href="CI_DB_driver.html#property__count_string">
CI_DB_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_options">
                                                                                array
                                                                                
                                    </td>
                <td>$options</td>
                <td class="last"><p>PDO Options</p></td>
                <td><small>from&nbsp;<a href="CI_DB_pdo_driver.html#property_options">
CI_DB_pdo_driver</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_compress">
                                                                                bool
                                                                                
                                    </td>
                <td>$compress</td>
                <td class="last"><p>Compression flag</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_stricton">
                                                                                bool
                                                                                
                                    </td>
                <td>$stricton</td>
                <td class="last"><p>Strict ON flag</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $params)
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_initialize">initialize</a>()
        
                                            <p><p>Initialize Database Settings</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_initialize">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_db_connect">db_connect</a>(bool $persistent = FALSE)
        
                                            <p><p>Database connection</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_db_pconnect">db_pconnect</a>()
        
                                            <p><p>Persistent database connection</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_db_pconnect">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_reconnect">reconnect</a>()
        
                                            <p>Reconnect</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_reconnect">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_db_select">db_select</a>(string $database = &#039;&#039;)
        
                                            <p><p>Select the database</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_error">error</a>()
        
                                            <p>Error</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_pdo_driver.html#method_error">
CI_DB_pdo_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_db_set_charset">db_set_charset</a>($charset)
        
                                            <p><p>Set client character set</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_db_set_charset">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_platform">platform</a>()
        
                                            <p><p>The name of the platform in use (mysql, mssql, etc...)</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_platform">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_version">version</a>()
        
                                            <p><p>Database version number</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_pdo_driver.html#method_version">
CI_DB_pdo_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__version">_version</a>()
        
                                            <p><p>Version number query string</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method__version">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_query">query</a>(string $sql, array $binds = FALSE, bool $return_object = NULL)
        
                                            <p><p>Execute the query</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_query">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_load_rdriver">load_rdriver</a>()
        
                                            <p><p>Load the result drivers</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_load_rdriver">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_simple_query">simple_query</a>($sql)
        
                                            <p><p>Simple Query
This is a simplified version of the query() function. Internally
we only use it when running transaction commands since they do
not require all the features of the main query() function.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_simple_query">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_trans_off">trans_off</a>()
        
                                            <p><p>Disable Transactions
This permits transactions to be disabled at run-time.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_trans_off">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_trans_strict">trans_strict</a>(bool $mode = TRUE)
        
                                            <p><p>Enable/disable Transaction Strict Mode</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_trans_strict">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_trans_start">trans_start</a>(bool $test_mode = FALSE)
        
                                            <p><p>Start Transaction</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_trans_start">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_trans_complete">trans_complete</a>()
        
                                            <p><p>Complete Transaction</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_trans_complete">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_trans_status">trans_status</a>()
        
                                            <p><p>Lets you retrieve the transaction flag to determine if it has failed</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_trans_status">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_trans_begin">trans_begin</a>(bool $test_mode = FALSE)
        
                                            <p><p>Begin Transaction</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_trans_begin">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_trans_commit">trans_commit</a>()
        
                                            <p><p>Commit Transaction</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_trans_commit">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_trans_rollback">trans_rollback</a>()
        
                                            <p><p>Rollback Transaction</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_trans_rollback">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_compile_binds">compile_binds</a>($sql, $binds)
        
                                            <p><p>Compile Bindings</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_compile_binds">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_is_write_type">is_write_type</a>($sql)
        
                                            <p><p>Determines if a query is a &quot;write&quot; type.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_is_write_type">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_elapsed_time">elapsed_time</a>($decimals = 6)
        
                                            <p><p>Calculate the aggregate query elapsed time</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_elapsed_time">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_total_queries">total_queries</a>()
        
                                            <p><p>Returns the total number of queries</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_total_queries">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_last_query">last_query</a>()
        
                                            <p><p>Returns the last query that was executed</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_last_query">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_escape">escape</a>($str)
        
                                            <p><p>&quot;Smart&quot; Escape String</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_escape">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_escape_str">escape_str</a>(string|string[] $str, bool $like = FALSE)
        
                                            <p><p>Escape String</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_escape_str">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_escape_like_str">escape_like_str</a>($str)
        
                                            <p><p>Escape LIKE String</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_escape_like_str">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__escape_str">_escape_str</a>($str)
        
                                            <p><p>Platform-dependent string escape</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_pdo_driver.html#method__escape_str">
CI_DB_pdo_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_primary">primary</a>(string $table)
        
                                            <p>Primary</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_primary">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_count_all">count_all</a>($table = &#039;&#039;)
        
                                            <p><p>&quot;Count All&quot; query</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_count_all">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_list_tables">list_tables</a>(string $constrain_by_prefix = FALSE)
        
                                            <p><p>Returns an array of table names</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_list_tables">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_table_exists">table_exists</a>(string $table_name)
        
                                            <p><p>Determine if a particular table exists</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_table_exists">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_list_fields">list_fields</a>(string $table)
        
                                            <p><p>Fetch Field Names</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_list_fields">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_field_exists">field_exists</a>($field_name, $table_name)
        
                                            <p><p>Determine if a particular field exists</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_field_exists">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_field_data">field_data</a>(string $table)
        
                                            <p><p>Returns an object with field data</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_escape_identifiers">escape_identifiers</a>($item)
        
                                            <p><p>Escape the SQL Identifiers</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_escape_identifiers">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_insert_string">insert_string</a>($table, $data)
        
                                            <p><p>Generate an insert string</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_insert_string">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__insert">_insert</a>($table, $keys, $values)
        
                                            <p><p>Insert statement</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method__insert">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_update_string">update_string</a>($table, $data, $where)
        
                                            <p><p>Generate an update string</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_update_string">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__update">_update</a>($table, $values)
        
                                            <p><p>Update statement</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method__update">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__has_operator">_has_operator</a>($str)
        
                                            <p><p>Tests whether the string has an SQL operator</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method__has_operator">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__get_operator">_get_operator</a>($str)
        
                                            <p><p>Returns the SQL string operator</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method__get_operator">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_call_function">call_function</a>(string $function)
        
                                            <p><p>Enables a native PHP function to be run, using a platform agnostic wrapper.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_call_function">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_cache_set_path">cache_set_path</a>($path = &#039;&#039;)
        
                                            <p><p>Set Cache Directory Path</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_cache_set_path">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_cache_on">cache_on</a>()
        
                                            <p><p>Enable Query Caching</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_cache_on">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_cache_off">cache_off</a>()
        
                                            <p><p>Disable Query Caching</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_cache_off">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_cache_delete">cache_delete</a>(string $segment_one = &#039;&#039;, string $segment_two = &#039;&#039;)
        
                                            <p><p>Delete the cache files associated with a particular URI</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_cache_delete">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_cache_delete_all">cache_delete_all</a>()
        
                                            <p><p>Delete All cache files</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_cache_delete_all">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__cache_init">_cache_init</a>()
        
                                            <p><p>Initialize the Cache Class</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method__cache_init">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_close">close</a>()
        
                                            <p><p>Close DB Connection</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_close">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__close">_close</a>()
        
                                            <p><p>Close DB Connection</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method__close">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_display_error">display_error</a>($error = &#039;&#039;, $swap = &#039;&#039;, $native = FALSE)
        
                                            <p><p>Display an error message</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_display_error">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_protect_identifiers">protect_identifiers</a>($item, $prefix_single = FALSE, $protect_identifiers = NULL, $field_exists = TRUE)
        
                                            <p><p>Protect Identifiers</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method_protect_identifiers">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__reset_select">_reset_select</a>()
        
                                            <p><p>Dummy method that allows Query Builder class to be disabled
and keep count_all() working.</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_driver.html#method__reset_select">
CI_DB_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method__execute">_execute</a>(string $sql)
        
                                            <p><p>Execute the query</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_pdo_driver.html#method__execute">
CI_DB_pdo_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__trans_begin">_trans_begin</a>()
        
                                            <p><p>Begin Transaction</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__trans_commit">_trans_commit</a>()
        
                                            <p><p>Commit Transaction</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method__trans_rollback">_trans_rollback</a>()
        
                                            <p><p>Rollback Transaction</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_affected_rows">affected_rows</a>()
        
                                            <p><p>Affected Rows</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_pdo_driver.html#method_affected_rows">
CI_DB_pdo_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    int
                </div>
                <div class="col-md-8">
                    <a href="#method_insert_id">insert_id</a>(string $name = NULL)
        
                                            <p><p>Insert ID</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_pdo_driver.html#method_insert_id">
CI_DB_pdo_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__field_data">_field_data</a>(string $table)
        
                                            <p><p>Field data query</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_DB_pdo_driver.html#method__field_data">
CI_DB_pdo_driver</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__truncate">_truncate</a>(string $table)
        
                                            <p><p>Truncate statement</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__list_tables">_list_tables</a>(bool $prefix_limit = FALSE)
        
                                            <p><p>Show table query</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__list_columns">_list_columns</a>(string $table = &#039;&#039;)
        
                                            <p><p>Show column query</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__from_tables">_from_tables</a>()
        
                                            <p><p>FROM tables</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 97</div>
        <code>                    void
    <strong>__construct</strong>(array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                <p><p>Builds the DSN if not already set.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initialize">
        <div class="location">in <a href="CI_DB_driver.html#method_initialize">
CI_DB_driver</a> at line 385</div>
        <code>                    bool
    <strong>initialize</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize Database Settings</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_db_connect">
        <div class="location">at line 123</div>
        <code>                    mixed
    <strong>db_connect</strong>(bool $persistent = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Database connection</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$persistent</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_db_pconnect">
        <div class="location">in <a href="CI_DB_driver.html#method_db_pconnect">
CI_DB_driver</a> at line 468</div>
        <code>                    mixed
    <strong>db_pconnect</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Persistent database connection</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_reconnect">
        <div class="location">in <a href="CI_DB_driver.html#method_reconnect">
CI_DB_driver</a> at line 486</div>
        <code>                    void
    <strong>reconnect</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Reconnect</p>                <p><p>Keep / reestablish the db connection if no queries have been
sent for a length of time exceeding the server's idle timeout.</p>
<p>This is just a dummy method to allow drivers without such
functionality to not declare it, while others will override it.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_db_select">
        <div class="location">at line 204</div>
        <code>                    bool
    <strong>db_select</strong>(string $database = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Select the database</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$database</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_error">
        <div class="location">in <a href="CI_DB_pdo_driver.html#method_error">
CI_DB_pdo_driver</a> at line 292</div>
        <code>                    array
    <strong>error</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Error</p>                <p><p>Returns an array containing code and message of the last
database error that has occurred.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_db_set_charset">
        <div class="location">in <a href="CI_DB_driver.html#method_db_set_charset">
CI_DB_driver</a> at line 525</div>
        <code>                    bool
    <strong>db_set_charset</strong>($charset)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set client character set</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$charset</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_platform">
        <div class="location">in <a href="CI_DB_driver.html#method_platform">
CI_DB_driver</a> at line 549</div>
        <code>                    string
    <strong>platform</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>The name of the platform in use (mysql, mssql, etc...)</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_version">
        <div class="location">in <a href="CI_DB_pdo_driver.html#method_version">
CI_DB_pdo_driver</a> at line 156</div>
        <code>                    string
    <strong>version</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Database version number</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__version">
        <div class="location">in <a href="CI_DB_driver.html#method__version">
CI_DB_driver</a> at line 587</div>
        <code>            protected        string
    <strong>_version</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Version number query string</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_query">
        <div class="location">in <a href="CI_DB_driver.html#method_query">
CI_DB_driver</a> at line 608</div>
        <code>                    mixed
    <strong>query</strong>(string $sql, array $binds = FALSE, bool $return_object = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Execute the query</p></p>                <p><p>Accepts an SQL string as input and returns a result object upon
successful execution of a &quot;read&quot; type query. Returns boolean TRUE
upon successful execution of a &quot;write&quot; type query. Returns boolean
FALSE upon failure, and if the $db_debug variable is set to TRUE
will raise an error.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$sql</td>
                <td></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$binds</td>
                <td><p>= FALSE     An array of binding data</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$return_object</td>
                <td><p>= NULL</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_load_rdriver">
        <div class="location">in <a href="CI_DB_driver.html#method_load_rdriver">
CI_DB_driver</a> at line 758</div>
        <code>                    string
    <strong>load_rdriver</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Load the result drivers</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>the name of the result class</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_simple_query">
        <div class="location">in <a href="CI_DB_driver.html#method_simple_query">
CI_DB_driver</a> at line 782</div>
        <code>                    mixed
    <strong>simple_query</strong>($sql)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Simple Query
This is a simplified version of the query() function. Internally
we only use it when running transaction commands since they do
not require all the features of the main query() function.</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$sql</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_trans_off">
        <div class="location">in <a href="CI_DB_driver.html#method_trans_off">
CI_DB_driver</a> at line 803</div>
        <code>                    void
    <strong>trans_off</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Disable Transactions
This permits transactions to be disabled at run-time.</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_trans_strict">
        <div class="location">in <a href="CI_DB_driver.html#method_trans_strict">
CI_DB_driver</a> at line 823</div>
        <code>                    void
    <strong>trans_strict</strong>(bool $mode = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Enable/disable Transaction Strict Mode</p></p>                <p><p>When strict mode is enabled, if you are running multiple groups of
transactions, if one group fails all subsequent groups will be
rolled back.</p>
<p>If strict mode is disabled, each group is treated autonomously,
meaning a failure of one group will not affect any others</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$mode</td>
                <td><p>= TRUE</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_trans_start">
        <div class="location">in <a href="CI_DB_driver.html#method_trans_start">
CI_DB_driver</a> at line 836</div>
        <code>                    bool
    <strong>trans_start</strong>(bool $test_mode = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Start Transaction</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$test_mode</td>
                <td><p>= FALSE</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_trans_complete">
        <div class="location">in <a href="CI_DB_driver.html#method_trans_complete">
CI_DB_driver</a> at line 853</div>
        <code>                    bool
    <strong>trans_complete</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Complete Transaction</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_trans_status">
        <div class="location">in <a href="CI_DB_driver.html#method_trans_status">
CI_DB_driver</a> at line 887</div>
        <code>                    bool
    <strong>trans_status</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Lets you retrieve the transaction flag to determine if it has failed</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_trans_begin">
        <div class="location">in <a href="CI_DB_driver.html#method_trans_begin">
CI_DB_driver</a> at line 900</div>
        <code>                    bool
    <strong>trans_begin</strong>(bool $test_mode = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Begin Transaction</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$test_mode</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_trans_commit">
        <div class="location">in <a href="CI_DB_driver.html#method_trans_commit">
CI_DB_driver</a> at line 935</div>
        <code>                    bool
    <strong>trans_commit</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Commit Transaction</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_trans_rollback">
        <div class="location">in <a href="CI_DB_driver.html#method_trans_rollback">
CI_DB_driver</a> at line 958</div>
        <code>                    bool
    <strong>trans_rollback</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Rollback Transaction</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_compile_binds">
        <div class="location">in <a href="CI_DB_driver.html#method_compile_binds">
CI_DB_driver</a> at line 983</div>
        <code>                    string
    <strong>compile_binds</strong>($sql, $binds)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Compile Bindings</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$sql</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$binds</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_is_write_type">
        <div class="location">in <a href="CI_DB_driver.html#method_is_write_type">
CI_DB_driver</a> at line 1047</div>
        <code>                    bool
    <strong>is_write_type</strong>($sql)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Determines if a query is a &quot;write&quot; type.</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$sql</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_elapsed_time">
        <div class="location">in <a href="CI_DB_driver.html#method_elapsed_time">
CI_DB_driver</a> at line 1060</div>
        <code>                    string
    <strong>elapsed_time</strong>($decimals = 6)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Calculate the aggregate query elapsed time</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$decimals</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_total_queries">
        <div class="location">in <a href="CI_DB_driver.html#method_total_queries">
CI_DB_driver</a> at line 1072</div>
        <code>                    int
    <strong>total_queries</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns the total number of queries</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_last_query">
        <div class="location">in <a href="CI_DB_driver.html#method_last_query">
CI_DB_driver</a> at line 1084</div>
        <code>                    string
    <strong>last_query</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns the last query that was executed</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_escape">
        <div class="location">in <a href="CI_DB_driver.html#method_escape">
CI_DB_driver</a> at line 1100</div>
        <code>                    mixed
    <strong>escape</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>&quot;Smart&quot; Escape String</p></p>                <p><p>Escapes data based on type
Sets boolean and null types</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_escape_str">
        <div class="location">in <a href="CI_DB_driver.html#method_escape_str">
CI_DB_driver</a> at line 1132</div>
        <code>                    string
    <strong>escape_str</strong>(string|string[] $str, bool $like = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Escape String</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string|string[]</td>
                <td>$str</td>
                <td><p>Input string</p></td>
            </tr>
                    <tr>
                <td>bool</td>
                <td>$like</td>
                <td><p>Whether or not the string will be used in a LIKE condition</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_escape_like_str">
        <div class="location">in <a href="CI_DB_driver.html#method_escape_like_str">
CI_DB_driver</a> at line 1170</div>
        <code>                    mixed
    <strong>escape_like_str</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Escape LIKE String</p></p>                <p><p>Calls the individual driver for platform
specific escaping for LIKE conditions</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__escape_str">
        <div class="location">in <a href="CI_DB_pdo_driver.html#method__escape_str">
CI_DB_pdo_driver</a> at line 231</div>
        <code>            protected        string
    <strong>_escape_str</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Platform-dependent string escape</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_primary">
        <div class="location">in <a href="CI_DB_driver.html#method_primary">
CI_DB_driver</a> at line 1199</div>
        <code>                    string
    <strong>primary</strong>(string $table)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Primary</p>                <p><p>Retrieves the primary key. It assumes that the row in the first
position is the primary key</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>Table name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_count_all">
        <div class="location">in <a href="CI_DB_driver.html#method_count_all">
CI_DB_driver</a> at line 1216</div>
        <code>                    int
    <strong>count_all</strong>($table = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>&quot;Count All&quot; query</p></p>                <p><p>Generates a platform-specific query string that counts all records in
the specified database</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$table</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_list_tables">
        <div class="location">in <a href="CI_DB_driver.html#method_list_tables">
CI_DB_driver</a> at line 1242</div>
        <code>                    array
    <strong>list_tables</strong>(string $constrain_by_prefix = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns an array of table names</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$constrain_by_prefix</td>
                <td><p>= FALSE</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_table_exists">
        <div class="location">in <a href="CI_DB_driver.html#method_table_exists">
CI_DB_driver</a> at line 1297</div>
        <code>                    bool
    <strong>table_exists</strong>(string $table_name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Determine if a particular table exists</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table_name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_list_fields">
        <div class="location">in <a href="CI_DB_driver.html#method_list_fields">
CI_DB_driver</a> at line 1310</div>
        <code>                    array
    <strong>list_fields</strong>(string $table)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Fetch Field Names</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>Table name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_field_exists">
        <div class="location">in <a href="CI_DB_driver.html#method_field_exists">
CI_DB_driver</a> at line 1355</div>
        <code>                    bool
    <strong>field_exists</strong>($field_name, $table_name)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Determine if a particular field exists</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$field_name</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$table_name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_field_data">
        <div class="location">at line 315</div>
        <code>                    array
    <strong>field_data</strong>(string $table)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns an object with field data</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td><p>the table name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_escape_identifiers">
        <div class="location">in <a href="CI_DB_driver.html#method_escape_identifiers">
CI_DB_driver</a> at line 1384</div>
        <code>                    mixed
    <strong>escape_identifiers</strong>($item)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Escape the SQL Identifiers</p></p>                <p><p>This function escapes column and table names</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$item</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_insert_string">
        <div class="location">in <a href="CI_DB_driver.html#method_insert_string">
CI_DB_driver</a> at line 1445</div>
        <code>                    string
    <strong>insert_string</strong>($table, $data)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Generate an insert string</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$table</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__insert">
        <div class="location">in <a href="CI_DB_driver.html#method__insert">
CI_DB_driver</a> at line 1470</div>
        <code>            protected        string
    <strong>_insert</strong>($table, $keys, $values)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Insert statement</p></p>                <p><p>Generates a platform-specific insert string from the supplied data</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$table</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$keys</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$values</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_update_string">
        <div class="location">in <a href="CI_DB_driver.html#method_update_string">
CI_DB_driver</a> at line 1485</div>
        <code>                    string
    <strong>update_string</strong>($table, $data, $where)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Generate an update string</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$table</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$where</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__update">
        <div class="location">in <a href="CI_DB_driver.html#method__update">
CI_DB_driver</a> at line 1516</div>
        <code>            protected        string
    <strong>_update</strong>($table, $values)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Update statement</p></p>                <p><p>Generates a platform-specific update string from the supplied data</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$table</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$values</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__has_operator">
        <div class="location">in <a href="CI_DB_driver.html#method__has_operator">
CI_DB_driver</a> at line 1537</div>
        <code>            protected        bool
    <strong>_has_operator</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Tests whether the string has an SQL operator</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__get_operator">
        <div class="location">in <a href="CI_DB_driver.html#method__get_operator">
CI_DB_driver</a> at line 1550</div>
        <code>            protected        string
    <strong>_get_operator</strong>($str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns the SQL string operator</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_call_function">
        <div class="location">in <a href="CI_DB_driver.html#method_call_function">
CI_DB_driver</a> at line 1588</div>
        <code>                    mixed
    <strong>call_function</strong>(string $function)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Enables a native PHP function to be run, using a platform agnostic wrapper.</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$function</td>
                <td><p>Function name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_cache_set_path">
        <div class="location">in <a href="CI_DB_driver.html#method_cache_set_path">
CI_DB_driver</a> at line 1615</div>
        <code>                    void
    <strong>cache_set_path</strong>($path = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Cache Directory Path</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$path</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_cache_on">
        <div class="location">in <a href="CI_DB_driver.html#method_cache_on">
CI_DB_driver</a> at line 1627</div>
        <code>                    bool
    <strong>cache_on</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Enable Query Caching</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td><p>cache_on value</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_cache_off">
        <div class="location">in <a href="CI_DB_driver.html#method_cache_off">
CI_DB_driver</a> at line 1639</div>
        <code>                    bool
    <strong>cache_off</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Disable Query Caching</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td><p>cache_on value</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_cache_delete">
        <div class="location">in <a href="CI_DB_driver.html#method_cache_delete">
CI_DB_driver</a> at line 1653</div>
        <code>                    bool
    <strong>cache_delete</strong>(string $segment_one = &#039;&#039;, string $segment_two = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Delete the cache files associated with a particular URI</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$segment_one</td>
                <td><p>= ''</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$segment_two</td>
                <td><p>= ''</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_cache_delete_all">
        <div class="location">in <a href="CI_DB_driver.html#method_cache_delete_all">
CI_DB_driver</a> at line 1667</div>
        <code>                    bool
    <strong>cache_delete_all</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Delete All cache files</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__cache_init">
        <div class="location">in <a href="CI_DB_driver.html#method__cache_init">
CI_DB_driver</a> at line 1681</div>
        <code>            protected        bool
    <strong>_cache_init</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize the Cache Class</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_close">
        <div class="location">in <a href="CI_DB_driver.html#method_close">
CI_DB_driver</a> at line 1703</div>
        <code>                    void
    <strong>close</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Close DB Connection</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__close">
        <div class="location">in <a href="CI_DB_driver.html#method__close">
CI_DB_driver</a> at line 1721</div>
        <code>            protected        void
    <strong>_close</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Close DB Connection</p></p>                <p><p>This method would be overridden by most of the drivers.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_display_error">
        <div class="location">in <a href="CI_DB_driver.html#method_display_error">
CI_DB_driver</a> at line 1736</div>
        <code>                    string
    <strong>display_error</strong>($error = &#039;&#039;, $swap = &#039;&#039;, $native = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Display an error message</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$error</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$swap</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$native</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>sends the application/views/errors/error_db.php template</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_protect_identifiers">
        <div class="location">in <a href="CI_DB_driver.html#method_protect_identifiers">
CI_DB_driver</a> at line 1809</div>
        <code>                    string
    <strong>protect_identifiers</strong>($item, $prefix_single = FALSE, $protect_identifiers = NULL, $field_exists = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Protect Identifiers</p></p>                <p><p>This function is used extensively by the Query Builder class, and by
a couple functions in this class.
It takes a column or table name (optionally with an alias) and inserts
the table prefix onto it. Some logic is necessary in order to deal with
column names that include the path. Consider a query like this:</p>
<p>SELECT hostname.database.table.column AS c FROM hostname.database.table</p>
<p>Or a query with aliasing:</p>
<p>SELECT m.member_id, m.member_name FROM members AS m</p>
<p>Since the column name can include up to four segments (host, DB, table, column)
or also have an alias prefix, we need to do a bit of work to figure this out and
insert the table prefix (if it exists) in the proper position, and escape only
the correct identifiers.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$item</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$prefix_single</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$protect_identifiers</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$field_exists</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__reset_select">
        <div class="location">in <a href="CI_DB_driver.html#method__reset_select">
CI_DB_driver</a> at line 1982</div>
        <code>            protected        void
    <strong>_reset_select</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Dummy method that allows Query Builder class to be disabled
and keep count_all() working.</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__execute">
        <div class="location">in <a href="CI_DB_pdo_driver.html#method__execute">
CI_DB_pdo_driver</a> at line 182</div>
        <code>            protected        mixed
    <strong>_execute</strong>(string $sql)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Execute the query</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$sql</td>
                <td><p>SQL query</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__trans_begin">
        <div class="location">at line 228</div>
        <code>            protected        bool
    <strong>_trans_begin</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Begin Transaction</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__trans_commit">
        <div class="location">at line 241</div>
        <code>            protected        bool
    <strong>_trans_commit</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Commit Transaction</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__trans_rollback">
        <div class="location">at line 259</div>
        <code>            protected        bool
    <strong>_trans_rollback</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Rollback Transaction</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_affected_rows">
        <div class="location">in <a href="CI_DB_pdo_driver.html#method_affected_rows">
CI_DB_pdo_driver</a> at line 249</div>
        <code>                    int
    <strong>affected_rows</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Affected Rows</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_insert_id">
        <div class="location">in <a href="CI_DB_pdo_driver.html#method_insert_id">
CI_DB_pdo_driver</a> at line 262</div>
        <code>                    int
    <strong>insert_id</strong>(string $name = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Insert ID</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$name</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__field_data">
        <div class="location">in <a href="CI_DB_pdo_driver.html#method__field_data">
CI_DB_pdo_driver</a> at line 277</div>
        <code>            protected        string
    <strong>_field_data</strong>(string $table)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Field data query</p></p>                <p><p>Generates a platform-specific query so that the column data can be retrieved</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__truncate">
        <div class="location">at line 354</div>
        <code>            protected        string
    <strong>_truncate</strong>(string $table)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Truncate statement</p></p>                <p><p>Generates a platform-specific truncate string from the supplied data</p>
<p>If the database does not support the TRUNCATE statement,
then this method maps to 'DELETE FROM table'</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__list_tables">
        <div class="location">at line 280</div>
        <code>            protected        string
    <strong>_list_tables</strong>(bool $prefix_limit = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Show table query</p></p>                <p><p>Generates a platform-specific query string so that the table names can be fetched</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$prefix_limit</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__list_columns">
        <div class="location">at line 302</div>
        <code>            protected        string
    <strong>_list_columns</strong>(string $table = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Show column query</p></p>                <p><p>Generates a platform-specific query string so that the column names can be fetched</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$table</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__from_tables">
        <div class="location">at line 369</div>
        <code>            protected        string
    <strong>_from_tables</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>FROM tables</p></p>                <p><p>Groups tables in FROM clauses if needed, so there is no confusion
about operator precedence.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

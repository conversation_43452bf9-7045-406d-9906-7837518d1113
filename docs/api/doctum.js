var Doctum = {
    treeJson: {"tree":{"l":0,"n":"","p":"","c":[{"l":1,"n":"[Global Namespace]","p":"[Global_Namespace]","c":[{"l":2,"n":"Action_hooks","p":"Action_hooks"},{"l":2,"n":"Activity_notes","p":"Activity_notes"},{"l":2,"n":"AdminController","p":"AdminController"},{"l":2,"n":"Admin_controller","p":"Admin_controller"},{"l":2,"n":"Ams","p":"Ams"},{"l":2,"n":"Announcements","p":"Announcements"},{"l":2,"n":"Announcements_model","p":"Announcements_model"},{"l":2,"n":"App","p":"App"},{"l":2,"n":"App_Autoloader","p":"App_Autoloader"},{"l":2,"n":"App_Controller","p":"App_Controller"},{"l":2,"n":"App_Email","p":"App_Email"},{"l":2,"n":"App_Form_validation","p":"App_Form_validation"},{"l":2,"n":"App_Input","p":"App_Input"},{"l":2,"n":"App_Lang","p":"App_Lang"},{"l":2,"n":"App_Loader","p":"App_Loader"},{"l":2,"n":"App_Migration","p":"App_Migration"},{"l":2,"n":"App_Model","p":"App_Model"},{"l":2,"n":"App_Router","p":"App_Router"},{"l":2,"n":"App_Security","p":"App_Security"},{"l":2,"n":"App_Session","p":"App_Session"},{"l":2,"n":"App_assets","p":"App_assets"},{"l":2,"n":"App_bulk_pdf_export","p":"App_bulk_pdf_export"},{"l":2,"n":"App_clients_area_constructor","p":"App_clients_area_constructor"},{"l":2,"n":"App_css","p":"App_css"},{"l":2,"n":"App_gateway","p":"App_gateway"},{"l":2,"n":"App_import","p":"App_import"},{"l":2,"n":"App_items_table","p":"App_items_table"},{"l":2,"n":"App_items_table_estimate","p":"App_items_table_estimate"},{"l":2,"n":"App_items_table_invoice","p":"App_items_table_invoice"},{"l":2,"n":"App_items_table_template","p":"App_items_table_template"},{"l":2,"n":"App_mail_template","p":"App_mail_template"},{"l":2,"n":"App_mailer","p":"App_mailer"},{"l":2,"n":"App_menu","p":"App_menu"},{"l":2,"n":"App_merge_fields","p":"App_merge_fields"},{"l":2,"n":"App_module_installer","p":"App_module_installer"},{"l":2,"n":"App_module_migration","p":"App_module_migration"},{"l":2,"n":"App_modules","p":"App_modules"},{"l":2,"n":"App_number_to_word","p":"App_number_to_word"},{"l":2,"n":"App_object_cache","p":"App_object_cache"},{"l":2,"n":"App_pdf","p":"App_pdf"},{"l":2,"n":"App_pusher","p":"App_pusher"},{"l":2,"n":"App_scripts","p":"App_scripts"},{"l":2,"n":"App_sms","p":"App_sms"},{"l":2,"n":"App_tabs","p":"App_tabs"},{"l":2,"n":"App_tags","p":"App_tags"},{"l":2,"n":"Authentication","p":"Authentication"},{"l":2,"n":"Authentication_model","p":"Authentication_model"},{"l":2,"n":"Authorize_acceptjs","p":"Authorize_acceptjs"},{"l":2,"n":"Authorize_acceptjs_gateway","p":"Authorize_acceptjs_gateway"},{"l":2,"n":"Auto_update","p":"Auto_update"},{"l":2,"n":"Braintree","p":"Braintree"},{"l":2,"n":"CI","p":"CI"},{"l":2,"n":"CI_Benchmark","p":"CI_Benchmark"},{"l":2,"n":"CI_Cache","p":"CI_Cache"},{"l":2,"n":"CI_Cache_apc","p":"CI_Cache_apc"},{"l":2,"n":"CI_Cache_dummy","p":"CI_Cache_dummy"},{"l":2,"n":"CI_Cache_file","p":"CI_Cache_file"},{"l":2,"n":"CI_Cache_memcached","p":"CI_Cache_memcached"},{"l":2,"n":"CI_Cache_redis","p":"CI_Cache_redis"},{"l":2,"n":"CI_Cache_wincache","p":"CI_Cache_wincache"},{"l":2,"n":"CI_Calendar","p":"CI_Calendar"},{"l":2,"n":"CI_Cart","p":"CI_Cart"},{"l":2,"n":"CI_Config","p":"CI_Config"},{"l":2,"n":"CI_Controller","p":"CI_Controller"},{"l":2,"n":"CI_DB","p":"CI_DB"},{"l":2,"n":"CI_DB_Cache","p":"CI_DB_Cache"},{"l":2,"n":"CI_DB_cubrid_driver","p":"CI_DB_cubrid_driver"},{"l":2,"n":"CI_DB_cubrid_forge","p":"CI_DB_cubrid_forge"},{"l":2,"n":"CI_DB_cubrid_result","p":"CI_DB_cubrid_result"},{"l":2,"n":"CI_DB_cubrid_utility","p":"CI_DB_cubrid_utility"},{"l":2,"n":"CI_DB_driver","p":"CI_DB_driver"},{"l":2,"n":"CI_DB_forge","p":"CI_DB_forge"},{"l":2,"n":"CI_DB_ibase_driver","p":"CI_DB_ibase_driver"},{"l":2,"n":"CI_DB_ibase_forge","p":"CI_DB_ibase_forge"},{"l":2,"n":"CI_DB_ibase_result","p":"CI_DB_ibase_result"},{"l":2,"n":"CI_DB_ibase_utility","p":"CI_DB_ibase_utility"},{"l":2,"n":"CI_DB_mssql_driver","p":"CI_DB_mssql_driver"},{"l":2,"n":"CI_DB_mssql_forge","p":"CI_DB_mssql_forge"},{"l":2,"n":"CI_DB_mssql_result","p":"CI_DB_mssql_result"},{"l":2,"n":"CI_DB_mssql_utility","p":"CI_DB_mssql_utility"},{"l":2,"n":"CI_DB_mysql_driver","p":"CI_DB_mysql_driver"},{"l":2,"n":"CI_DB_mysql_forge","p":"CI_DB_mysql_forge"},{"l":2,"n":"CI_DB_mysql_result","p":"CI_DB_mysql_result"},{"l":2,"n":"CI_DB_mysql_utility","p":"CI_DB_mysql_utility"},{"l":2,"n":"CI_DB_mysqli_driver","p":"CI_DB_mysqli_driver"},{"l":2,"n":"CI_DB_mysqli_forge","p":"CI_DB_mysqli_forge"},{"l":2,"n":"CI_DB_mysqli_result","p":"CI_DB_mysqli_result"},{"l":2,"n":"CI_DB_mysqli_utility","p":"CI_DB_mysqli_utility"},{"l":2,"n":"CI_DB_oci8_driver","p":"CI_DB_oci8_driver"},{"l":2,"n":"CI_DB_oci8_forge","p":"CI_DB_oci8_forge"},{"l":2,"n":"CI_DB_oci8_result","p":"CI_DB_oci8_result"},{"l":2,"n":"CI_DB_oci8_utility","p":"CI_DB_oci8_utility"},{"l":2,"n":"CI_DB_odbc_driver","p":"CI_DB_odbc_driver"},{"l":2,"n":"CI_DB_odbc_forge","p":"CI_DB_odbc_forge"},{"l":2,"n":"CI_DB_odbc_result","p":"CI_DB_odbc_result"},{"l":2,"n":"CI_DB_odbc_utility","p":"CI_DB_odbc_utility"},{"l":2,"n":"CI_DB_pdo_4d_driver","p":"CI_DB_pdo_4d_driver"},{"l":2,"n":"CI_DB_pdo_4d_forge","p":"CI_DB_pdo_4d_forge"},{"l":2,"n":"CI_DB_pdo_cubrid_driver","p":"CI_DB_pdo_cubrid_driver"},{"l":2,"n":"CI_DB_pdo_cubrid_forge","p":"CI_DB_pdo_cubrid_forge"},{"l":2,"n":"CI_DB_pdo_dblib_driver","p":"CI_DB_pdo_dblib_driver"},{"l":2,"n":"CI_DB_pdo_dblib_forge","p":"CI_DB_pdo_dblib_forge"},{"l":2,"n":"CI_DB_pdo_driver","p":"CI_DB_pdo_driver"},{"l":2,"n":"CI_DB_pdo_firebird_driver","p":"CI_DB_pdo_firebird_driver"},{"l":2,"n":"CI_DB_pdo_firebird_forge","p":"CI_DB_pdo_firebird_forge"},{"l":2,"n":"CI_DB_pdo_forge","p":"CI_DB_pdo_forge"},{"l":2,"n":"CI_DB_pdo_ibm_driver","p":"CI_DB_pdo_ibm_driver"},{"l":2,"n":"CI_DB_pdo_ibm_forge","p":"CI_DB_pdo_ibm_forge"},{"l":2,"n":"CI_DB_pdo_informix_driver","p":"CI_DB_pdo_informix_driver"},{"l":2,"n":"CI_DB_pdo_informix_forge","p":"CI_DB_pdo_informix_forge"},{"l":2,"n":"CI_DB_pdo_mysql_driver","p":"CI_DB_pdo_mysql_driver"},{"l":2,"n":"CI_DB_pdo_mysql_forge","p":"CI_DB_pdo_mysql_forge"},{"l":2,"n":"CI_DB_pdo_oci_driver","p":"CI_DB_pdo_oci_driver"},{"l":2,"n":"CI_DB_pdo_oci_forge","p":"CI_DB_pdo_oci_forge"},{"l":2,"n":"CI_DB_pdo_odbc_driver","p":"CI_DB_pdo_odbc_driver"},{"l":2,"n":"CI_DB_pdo_odbc_forge","p":"CI_DB_pdo_odbc_forge"},{"l":2,"n":"CI_DB_pdo_pgsql_driver","p":"CI_DB_pdo_pgsql_driver"},{"l":2,"n":"CI_DB_pdo_pgsql_forge","p":"CI_DB_pdo_pgsql_forge"},{"l":2,"n":"CI_DB_pdo_result","p":"CI_DB_pdo_result"},{"l":2,"n":"CI_DB_pdo_sqlite_driver","p":"CI_DB_pdo_sqlite_driver"},{"l":2,"n":"CI_DB_pdo_sqlite_forge","p":"CI_DB_pdo_sqlite_forge"},{"l":2,"n":"CI_DB_pdo_sqlsrv_driver","p":"CI_DB_pdo_sqlsrv_driver"},{"l":2,"n":"CI_DB_pdo_sqlsrv_forge","p":"CI_DB_pdo_sqlsrv_forge"},{"l":2,"n":"CI_DB_pdo_utility","p":"CI_DB_pdo_utility"},{"l":2,"n":"CI_DB_postgre_driver","p":"CI_DB_postgre_driver"},{"l":2,"n":"CI_DB_postgre_forge","p":"CI_DB_postgre_forge"},{"l":2,"n":"CI_DB_postgre_result","p":"CI_DB_postgre_result"},{"l":2,"n":"CI_DB_postgre_utility","p":"CI_DB_postgre_utility"},{"l":2,"n":"CI_DB_query_builder","p":"CI_DB_query_builder"},{"l":2,"n":"CI_DB_result","p":"CI_DB_result"},{"l":2,"n":"CI_DB_sqlite3_driver","p":"CI_DB_sqlite3_driver"},{"l":2,"n":"CI_DB_sqlite3_forge","p":"CI_DB_sqlite3_forge"},{"l":2,"n":"CI_DB_sqlite3_result","p":"CI_DB_sqlite3_result"},{"l":2,"n":"CI_DB_sqlite3_utility","p":"CI_DB_sqlite3_utility"},{"l":2,"n":"CI_DB_sqlite_driver","p":"CI_DB_sqlite_driver"},{"l":2,"n":"CI_DB_sqlite_forge","p":"CI_DB_sqlite_forge"},{"l":2,"n":"CI_DB_sqlite_result","p":"CI_DB_sqlite_result"},{"l":2,"n":"CI_DB_sqlite_utility","p":"CI_DB_sqlite_utility"},{"l":2,"n":"CI_DB_sqlsrv_driver","p":"CI_DB_sqlsrv_driver"},{"l":2,"n":"CI_DB_sqlsrv_forge","p":"CI_DB_sqlsrv_forge"},{"l":2,"n":"CI_DB_sqlsrv_result","p":"CI_DB_sqlsrv_result"},{"l":2,"n":"CI_DB_sqlsrv_utility","p":"CI_DB_sqlsrv_utility"},{"l":2,"n":"CI_DB_utility","p":"CI_DB_utility"},{"l":2,"n":"CI_Driver","p":"CI_Driver"},{"l":2,"n":"CI_Driver_Library","p":"CI_Driver_Library"},{"l":2,"n":"CI_Email","p":"CI_Email"},{"l":2,"n":"CI_Encrypt","p":"CI_Encrypt"},{"l":2,"n":"CI_Encryption","p":"CI_Encryption"},{"l":2,"n":"CI_Exceptions","p":"CI_Exceptions"},{"l":2,"n":"CI_FTP","p":"CI_FTP"},{"l":2,"n":"CI_Form_validation","p":"CI_Form_validation"},{"l":2,"n":"CI_Hooks","p":"CI_Hooks"},{"l":2,"n":"CI_Image_lib","p":"CI_Image_lib"},{"l":2,"n":"CI_Input","p":"CI_Input"},{"l":2,"n":"CI_Javascript","p":"CI_Javascript"},{"l":2,"n":"CI_Jquery","p":"CI_Jquery"},{"l":2,"n":"CI_Lang","p":"CI_Lang"},{"l":2,"n":"CI_Loader","p":"CI_Loader"},{"l":2,"n":"CI_Log","p":"CI_Log"},{"l":2,"n":"CI_Migration","p":"CI_Migration"},{"l":2,"n":"CI_Model","p":"CI_Model"},{"l":2,"n":"CI_Output","p":"CI_Output"},{"l":2,"n":"CI_Pagination","p":"CI_Pagination"},{"l":2,"n":"CI_Parser","p":"CI_Parser"},{"l":2,"n":"CI_Profiler","p":"CI_Profiler"},{"l":2,"n":"CI_Router","p":"CI_Router"},{"l":2,"n":"CI_Security","p":"CI_Security"},{"l":2,"n":"CI_Session","p":"CI_Session"},{"l":2,"n":"CI_Session_database_driver","p":"CI_Session_database_driver"},{"l":2,"n":"CI_Session_driver","p":"CI_Session_driver"},{"l":2,"n":"CI_Session_files_driver","p":"CI_Session_files_driver"},{"l":2,"n":"CI_Session_memcached_driver","p":"CI_Session_memcached_driver"},{"l":2,"n":"CI_Session_redis_driver","p":"CI_Session_redis_driver"},{"l":2,"n":"CI_Table","p":"CI_Table"},{"l":2,"n":"CI_Trackback","p":"CI_Trackback"},{"l":2,"n":"CI_Typography","p":"CI_Typography"},{"l":2,"n":"CI_URI","p":"CI_URI"},{"l":2,"n":"CI_Unit_test","p":"CI_Unit_test"},{"l":2,"n":"CI_Upload","p":"CI_Upload"},{"l":2,"n":"CI_User_agent","p":"CI_User_agent"},{"l":2,"n":"CI_Utf8","p":"CI_Utf8"},{"l":2,"n":"CI_Xmlrpc","p":"CI_Xmlrpc"},{"l":2,"n":"CI_Xmlrpcs","p":"CI_Xmlrpcs"},{"l":2,"n":"CI_Zip","p":"CI_Zip"},{"l":2,"n":"CRM_Controller","p":"CRM_Controller"},{"l":2,"n":"CRM_Model","p":"CRM_Model"},{"l":2,"n":"Call_center_logs","p":"Call_center_logs"},{"l":2,"n":"Call_center_model","p":"Call_center_model"},{"l":2,"n":"Check_emails","p":"Check_emails"},{"l":2,"n":"Client_groups_model","p":"Client_groups_model"},{"l":2,"n":"Client_merge_fields","p":"Client_merge_fields"},{"l":2,"n":"Client_requests_model","p":"Client_requests_model"},{"l":2,"n":"Client_vault_entries_model","p":"Client_vault_entries_model"},{"l":2,"n":"Clients","p":"Clients"},{"l":2,"n":"ClientsController","p":"ClientsController"},{"l":2,"n":"Clients_controller","p":"Clients_controller"},{"l":2,"n":"Clients_model","p":"Clients_model"},{"l":2,"n":"Command","p":"Command"},{"l":2,"n":"Command_model","p":"Command_model"},{"l":2,"n":"Consent","p":"Consent"},{"l":2,"n":"Contacts","p":"Contacts"},{"l":2,"n":"Contract","p":"Contract"},{"l":2,"n":"Contract_comment_to_customer","p":"Contract_comment_to_customer"},{"l":2,"n":"Contract_comment_to_staff","p":"Contract_comment_to_staff"},{"l":2,"n":"Contract_expiration_reminder_to_customer","p":"Contract_expiration_reminder_to_customer"},{"l":2,"n":"Contract_expiration_reminder_to_staff","p":"Contract_expiration_reminder_to_staff"},{"l":2,"n":"Contract_merge_fields","p":"Contract_merge_fields"},{"l":2,"n":"Contract_pdf","p":"Contract_pdf"},{"l":2,"n":"Contract_send_to_customer","p":"Contract_send_to_customer"},{"l":2,"n":"Contract_signed_to_staff","p":"Contract_signed_to_staff"},{"l":2,"n":"Contract_types_model","p":"Contract_types_model"},{"l":2,"n":"Contracts","p":"Contracts"},{"l":2,"n":"Contracts_model","p":"Contracts_model"},{"l":2,"n":"Credit_note_merge_fields","p":"Credit_note_merge_fields"},{"l":2,"n":"Credit_note_pdf","p":"Credit_note_pdf"},{"l":2,"n":"Credit_note_send_to_customer","p":"Credit_note_send_to_customer"},{"l":2,"n":"Credit_notes","p":"Credit_notes"},{"l":2,"n":"Credit_notes_model","p":"Credit_notes_model"},{"l":2,"n":"Cron","p":"Cron"},{"l":2,"n":"CronJob","p":"CronJob"},{"l":2,"n":"Cron_model","p":"Cron_model"},{"l":2,"n":"Currencies","p":"Currencies"},{"l":2,"n":"Currencies_model","p":"Currencies_model"},{"l":2,"n":"Custom_fields","p":"Custom_fields"},{"l":2,"n":"Custom_fields_model","p":"Custom_fields_model"},{"l":2,"n":"Customer_admins_model","p":"Customer_admins_model"},{"l":2,"n":"Customer_contact_forgot_password","p":"Customer_contact_forgot_password"},{"l":2,"n":"Customer_contact_password_resetted","p":"Customer_contact_password_resetted"},{"l":2,"n":"Customer_contact_set_password","p":"Customer_contact_set_password"},{"l":2,"n":"Customer_contact_verification","p":"Customer_contact_verification"},{"l":2,"n":"Customer_created_welcome_mail","p":"Customer_created_welcome_mail"},{"l":2,"n":"Customer_new_registration_to_admins","p":"Customer_new_registration_to_admins"},{"l":2,"n":"Customer_profile_uploaded_file_to_staff","p":"Customer_profile_uploaded_file_to_staff"},{"l":2,"n":"Customer_registration_confirmed","p":"Customer_registration_confirmed"},{"l":2,"n":"Customer_statement","p":"Customer_statement"},{"l":2,"n":"DB","p":"DB"},{"l":2,"n":"Dashboard","p":"Dashboard"},{"l":2,"n":"Dashboard_model","p":"Dashboard_model"},{"l":2,"n":"Departments","p":"Departments"},{"l":2,"n":"Departments_model","p":"Departments_model"},{"l":2,"n":"Download","p":"Download"},{"l":2,"n":"Elasticsearch","p":"Elasticsearch"},{"l":2,"n":"Email_schedule_estimate","p":"Email_schedule_estimate"},{"l":2,"n":"Email_schedule_invoice","p":"Email_schedule_invoice"},{"l":2,"n":"Email_schedule_model","p":"Email_schedule_model"},{"l":2,"n":"Emails","p":"Emails"},{"l":2,"n":"Emails_model","p":"Emails_model"},{"l":2,"n":"EnhanceSecurity","p":"EnhanceSecurity"},{"l":2,"n":"Es","p":"Es"},{"l":2,"n":"EsBaseModel","p":"EsBaseModel"},{"l":2,"n":"EsClientModel","p":"EsClientModel"},{"l":2,"n":"EsContactModel","p":"EsContactModel"},{"l":2,"n":"EsContractModel","p":"EsContractModel"},{"l":2,"n":"EsCreditNoteModel","p":"EsCreditNoteModel"},{"l":2,"n":"EsCurrencyModel","p":"EsCurrencyModel"},{"l":2,"n":"EsCustomFieldsValueModel","p":"EsCustomFieldsValueModel"},{"l":2,"n":"EsDepartmentModel","p":"EsDepartmentModel"},{"l":2,"n":"EsEstimateModel","p":"EsEstimateModel"},{"l":2,"n":"EsExpenseModel","p":"EsExpenseModel"},{"l":2,"n":"EsExpensesCategoryModel","p":"EsExpensesCategoryModel"},{"l":2,"n":"EsGoalModel","p":"EsGoalModel"},{"l":2,"n":"EsInvoiceModel","p":"EsInvoiceModel"},{"l":2,"n":"EsInvoicePaymentRecordModel","p":"EsInvoicePaymentRecordModel"},{"l":2,"n":"EsItemableModel","p":"EsItemableModel"},{"l":2,"n":"EsKnowledgeBaseModel","p":"EsKnowledgeBaseModel"},{"l":2,"n":"EsLeadModel","p":"EsLeadModel"},{"l":2,"n":"EsPaymentModeModel","p":"EsPaymentModeModel"},{"l":2,"n":"EsProjectModel","p":"EsProjectModel"},{"l":2,"n":"EsProposalModel","p":"EsProposalModel"},{"l":2,"n":"EsStaffModel","p":"EsStaffModel"},{"l":2,"n":"EsSurveyModel","p":"EsSurveyModel"},{"l":2,"n":"EsTagModel","p":"EsTagModel"},{"l":2,"n":"EsTaggableModel","p":"EsTaggableModel"},{"l":2,"n":"EsTaskModel","p":"EsTaskModel"},{"l":2,"n":"EsTicketModel","p":"EsTicketModel"},{"l":2,"n":"Estimate","p":"Estimate"},{"l":2,"n":"Estimate_accepted_to_customer","p":"Estimate_accepted_to_customer"},{"l":2,"n":"Estimate_accepted_to_staff","p":"Estimate_accepted_to_staff"},{"l":2,"n":"Estimate_declined_to_staff","p":"Estimate_declined_to_staff"},{"l":2,"n":"Estimate_expiration_reminder","p":"Estimate_expiration_reminder"},{"l":2,"n":"Estimate_merge_fields","p":"Estimate_merge_fields"},{"l":2,"n":"Estimate_pdf","p":"Estimate_pdf"},{"l":2,"n":"Estimate_request","p":"Estimate_request"},{"l":2,"n":"Estimate_request_assigned","p":"Estimate_request_assigned"},{"l":2,"n":"Estimate_request_form_submitted","p":"Estimate_request_form_submitted"},{"l":2,"n":"Estimate_request_merge_fields","p":"Estimate_request_merge_fields"},{"l":2,"n":"Estimate_request_model","p":"Estimate_request_model"},{"l":2,"n":"Estimate_request_received_to_user","p":"Estimate_request_received_to_user"},{"l":2,"n":"Estimate_send_to_customer","p":"Estimate_send_to_customer"},{"l":2,"n":"Estimate_send_to_customer_already_sent","p":"Estimate_send_to_customer_already_sent"},{"l":2,"n":"Estimates","p":"Estimates"},{"l":2,"n":"Estimates_model","p":"Estimates_model"},{"l":2,"n":"Event_merge_fields","p":"Event_merge_fields"},{"l":2,"n":"Expense_pdf","p":"Expense_pdf"},{"l":2,"n":"Expenses","p":"Expenses"},{"l":2,"n":"Expenses_model","p":"Expenses_model"},{"l":2,"n":"Favorite_clients","p":"Favorite_clients"},{"l":2,"n":"Forms","p":"Forms"},{"l":2,"n":"Gdpr","p":"Gdpr"},{"l":2,"n":"Gdpr_contact","p":"Gdpr_contact"},{"l":2,"n":"Gdpr_contracts","p":"Gdpr_contracts"},{"l":2,"n":"Gdpr_credit_notes","p":"Gdpr_credit_notes"},{"l":2,"n":"Gdpr_estimates","p":"Gdpr_estimates"},{"l":2,"n":"Gdpr_expenses","p":"Gdpr_expenses"},{"l":2,"n":"Gdpr_invoices","p":"Gdpr_invoices"},{"l":2,"n":"Gdpr_lead","p":"Gdpr_lead"},{"l":2,"n":"Gdpr_model","p":"Gdpr_model"},{"l":2,"n":"Gdpr_projects","p":"Gdpr_projects"},{"l":2,"n":"Gdpr_proposals","p":"Gdpr_proposals"},{"l":2,"n":"Gdpr_removal_request_by_customer","p":"Gdpr_removal_request_by_customer"},{"l":2,"n":"Gdpr_removal_request_by_lead","p":"Gdpr_removal_request_by_lead"},{"l":2,"n":"Gdpr_subscriptions","p":"Gdpr_subscriptions"},{"l":2,"n":"Gdpr_tickets","p":"Gdpr_tickets"},{"l":2,"n":"Import_customer_admins","p":"Import_customer_admins"},{"l":2,"n":"Import_customers","p":"Import_customers"},{"l":2,"n":"Import_expenses","p":"Import_expenses"},{"l":2,"n":"Import_items","p":"Import_items"},{"l":2,"n":"Import_leads","p":"Import_leads"},{"l":2,"n":"Import_salesperson","p":"Import_salesperson"},{"l":2,"n":"InitModules","p":"InitModules"},{"l":2,"n":"Instamojo","p":"Instamojo"},{"l":2,"n":"Instamojo_gateway","p":"Instamojo_gateway"},{"l":2,"n":"Invoice","p":"Invoice"},{"l":2,"n":"Invoice_batch_payments","p":"Invoice_batch_payments"},{"l":2,"n":"Invoice_batch_payments_merge_fields","p":"Invoice_batch_payments_merge_fields"},{"l":2,"n":"Invoice_due_notice","p":"Invoice_due_notice"},{"l":2,"n":"Invoice_items","p":"Invoice_items"},{"l":2,"n":"Invoice_items_model","p":"Invoice_items_model"},{"l":2,"n":"Invoice_merge_fields","p":"Invoice_merge_fields"},{"l":2,"n":"Invoice_overdue_notice","p":"Invoice_overdue_notice"},{"l":2,"n":"Invoice_payment_recorded_to_customer","p":"Invoice_payment_recorded_to_customer"},{"l":2,"n":"Invoice_payment_recorded_to_staff","p":"Invoice_payment_recorded_to_staff"},{"l":2,"n":"Invoice_pdf","p":"Invoice_pdf"},{"l":2,"n":"Invoice_pdf_v2","p":"Invoice_pdf_v2"},{"l":2,"n":"Invoice_send_to_customer","p":"Invoice_send_to_customer"},{"l":2,"n":"Invoice_send_to_customer_already_sent","p":"Invoice_send_to_customer_already_sent"},{"l":2,"n":"Invoices","p":"Invoices"},{"l":2,"n":"Invoices_model","p":"Invoices_model"},{"l":2,"n":"JD_Geocoder_Request","p":"JD_Geocoder_Request"},{"l":2,"n":"Knowledge_base","p":"Knowledge_base"},{"l":2,"n":"Knowledge_base_model","p":"Knowledge_base_model"},{"l":2,"n":"Lead_assigned","p":"Lead_assigned"},{"l":2,"n":"Lead_web_form_submitted","p":"Lead_web_form_submitted"},{"l":2,"n":"Leads","p":"Leads"},{"l":2,"n":"Leads_merge_fields","p":"Leads_merge_fields"},{"l":2,"n":"Leads_model","p":"Leads_model"},{"l":2,"n":"MX_Config","p":"MX_Config"},{"l":2,"n":"MX_Controller","p":"MX_Controller"},{"l":2,"n":"MX_Lang","p":"MX_Lang"},{"l":2,"n":"MX_Loader","p":"MX_Loader"},{"l":2,"n":"MX_Router","p":"MX_Router"},{"l":2,"n":"Migrate","p":"Migrate"},{"l":2,"n":"Migration","p":"Migration"},{"l":2,"n":"Migration_Add_Columns_To_Clients_Table_20230727082740","p":"Migration_Add_Columns_To_Clients_Table_20230727082740"},{"l":2,"n":"Migration_Add_Columns_To_Invoice_20230913172909","p":"Migration_Add_Columns_To_Invoice_20230913172909"},{"l":2,"n":"Migration_Add_Customer_Source_Invoice_20230919163250","p":"Migration_Add_Customer_Source_Invoice_20230919163250"},{"l":2,"n":"Migration_Add_Field_Ext_To_Contacts_Table_20231228094757","p":"Migration_Add_Field_Ext_To_Contacts_Table_20231228094757"},{"l":2,"n":"Migration_Add_Field_Parent_Id_To_Estimate_Tables_20230911173334","p":"Migration_Add_Field_Parent_Id_To_Estimate_Tables_20230911173334"},{"l":2,"n":"Migration_Add_Field_Status_Updated_At_To_Estimates_Table_20231109162941","p":"Migration_Add_Field_Status_Updated_At_To_Estimates_Table_20231109162941"},{"l":2,"n":"Migration_Add_Fields_To_Contact_Requests_Table_20230814141722","p":"Migration_Add_Fields_To_Contact_Requests_Table_20230814141722"},{"l":2,"n":"Migration_Add_Index_Column_Last_Login_Table_Contacts_20231106101306","p":"Migration_Add_Index_Column_Last_Login_Table_Contacts_20231106101306"},{"l":2,"n":"Migration_Add_Index_For_Custom_Field_Values_Table_20231109161348","p":"Migration_Add_Index_For_Custom_Field_Values_Table_20231109161348"},{"l":2,"n":"Migration_Add_Index_For_Statistic_20230810113330","p":"Migration_Add_Index_For_Statistic_20230810113330"},{"l":2,"n":"Migration_Add_Index_Idx_Call_Id_To_3c_Call_Table_20240105103928","p":"Migration_Add_Index_Idx_Call_Id_To_3c_Call_Table_20240105103928"},{"l":2,"n":"Migration_Add_Index_To_Activity_Log_Table_20230807082639","p":"Migration_Add_Index_To_Activity_Log_Table_20230807082639"},{"l":2,"n":"Migration_Add_Index_To_Client_Ams_Companies_20231106093715","p":"Migration_Add_Index_To_Client_Ams_Companies_20231106093715"},{"l":2,"n":"Migration_Add_Index_To_Contacts_Notes_Tables_20230804171219","p":"Migration_Add_Index_To_Contacts_Notes_Tables_20230804171219"},{"l":2,"n":"Migration_Add_New_Call_Log_Failed_Jobs_Table_20240104141649","p":"Migration_Add_New_Call_Log_Failed_Jobs_Table_20240104141649"},{"l":2,"n":"Migration_Add_New_Call_Log_Synced_Time_Table_20240104180703","p":"Migration_Add_New_Call_Log_Synced_Time_Table_20240104180703"},{"l":2,"n":"Migration_Add_New_Clients_Permission_20230829171911","p":"Migration_Add_New_Clients_Permission_20230829171911"},{"l":2,"n":"Migration_Create_Client_Ams_Jobs_Table_20231016102201","p":"Migration_Create_Client_Ams_Jobs_Table_20231016102201"},{"l":2,"n":"Migration_Create_Ext_Version_Table_20230822133908","p":"Migration_Create_Ext_Version_Table_20230822133908"},{"l":2,"n":"Migration_Create_New_Client_Ams_Companies_Table_20231003144742","p":"Migration_Create_New_Client_Ams_Companies_Table_20231003144742"},{"l":2,"n":"Migration_Create_Table_Kpi_Team_Revenue_20231030111749","p":"Migration_Create_Table_Kpi_Team_Revenue_20231030111749"},{"l":2,"n":"Migration_Invoice_Request_20231109164328","p":"Migration_Invoice_Request_20231109164328"},{"l":2,"n":"Migration_Remove_20231026094055","p":"Migration_Remove_20231026094055"},{"l":2,"n":"Migration_Remove_Permission_Payments_Create_B2bsale_20231117111008","p":"Migration_Remove_Permission_Payments_Create_B2bsale_20231117111008"},{"l":2,"n":"Migration_Setting_Kpi_20230911090537","p":"Migration_Setting_Kpi_20230911090537"},{"l":2,"n":"Migration_Update_Tables_Contact_Requests_20230731101232","p":"Migration_Update_Tables_Contact_Requests_20230731101232"},{"l":2,"n":"Misc","p":"Misc"},{"l":2,"n":"Misc_model","p":"Misc_model"},{"l":2,"n":"Mods","p":"Mods"},{"l":2,"n":"Modules","p":"Modules"},{"l":2,"n":"Mollie","p":"Mollie"},{"l":2,"n":"Mollie_gateway","p":"Mollie_gateway"},{"l":2,"n":"Newsfeed","p":"Newsfeed"},{"l":2,"n":"Newsfeed_model","p":"Newsfeed_model"},{"l":2,"n":"Non_billed_tasks_reminder_to_staff","p":"Non_billed_tasks_reminder_to_staff"},{"l":2,"n":"Notifications_merge_fields","p":"Notifications_merge_fields"},{"l":2,"n":"Other_merge_fields","p":"Other_merge_fields"},{"l":2,"n":"PDF_Signature","p":"PDF_Signature"},{"l":2,"n":"PasswordHash","p":"PasswordHash"},{"l":2,"n":"Payment_modes_model","p":"Payment_modes_model"},{"l":2,"n":"Payment_pdf","p":"Payment_pdf"},{"l":2,"n":"Paymentmodes","p":"Paymentmodes"},{"l":2,"n":"Payments","p":"Payments"},{"l":2,"n":"Payments_model","p":"Payments_model"},{"l":2,"n":"Paypal","p":"Paypal"},{"l":2,"n":"Paypal_braintree_gateway","p":"Paypal_braintree_gateway"},{"l":2,"n":"Paypal_checkout","p":"Paypal_checkout"},{"l":2,"n":"Paypal_checkout_gateway","p":"Paypal_checkout_gateway"},{"l":2,"n":"Paypal_gateway","p":"Paypal_gateway"},{"l":2,"n":"Payu_money","p":"Payu_money"},{"l":2,"n":"Payu_money_gateway","p":"Payu_money_gateway"},{"l":2,"n":"Privacy_policy","p":"Privacy_policy"},{"l":2,"n":"Project_created_to_customer","p":"Project_created_to_customer"},{"l":2,"n":"Project_data_pdf","p":"Project_data_pdf"},{"l":2,"n":"Project_discussion_created_to_customer","p":"Project_discussion_created_to_customer"},{"l":2,"n":"Project_discussion_created_to_staff","p":"Project_discussion_created_to_staff"},{"l":2,"n":"Project_file_to_customer","p":"Project_file_to_customer"},{"l":2,"n":"Project_file_to_staff","p":"Project_file_to_staff"},{"l":2,"n":"Project_marked_as_finished_to_customer","p":"Project_marked_as_finished_to_customer"},{"l":2,"n":"Project_new_discussion_comment_to_customer","p":"Project_new_discussion_comment_to_customer"},{"l":2,"n":"Project_new_discussion_comment_to_staff","p":"Project_new_discussion_comment_to_staff"},{"l":2,"n":"Project_staff_added_as_member","p":"Project_staff_added_as_member"},{"l":2,"n":"Projects","p":"Projects"},{"l":2,"n":"Projects_merge_fields","p":"Projects_merge_fields"},{"l":2,"n":"Projects_model","p":"Projects_model"},{"l":2,"n":"Proposal","p":"Proposal"},{"l":2,"n":"Proposal_accepted_to_customer","p":"Proposal_accepted_to_customer"},{"l":2,"n":"Proposal_accepted_to_staff","p":"Proposal_accepted_to_staff"},{"l":2,"n":"Proposal_comment_to_customer","p":"Proposal_comment_to_customer"},{"l":2,"n":"Proposal_comment_to_staff","p":"Proposal_comment_to_staff"},{"l":2,"n":"Proposal_declined_to_staff","p":"Proposal_declined_to_staff"},{"l":2,"n":"Proposal_expiration_reminder","p":"Proposal_expiration_reminder"},{"l":2,"n":"Proposal_pdf","p":"Proposal_pdf"},{"l":2,"n":"Proposal_send_to_customer","p":"Proposal_send_to_customer"},{"l":2,"n":"Proposals","p":"Proposals"},{"l":2,"n":"Proposals_merge_fields","p":"Proposals_merge_fields"},{"l":2,"n":"Proposals_model","p":"Proposals_model"},{"l":2,"n":"RabbitMq","p":"RabbitMq"},{"l":2,"n":"RabbitMqAbstract","p":"RabbitMqAbstract"},{"l":2,"n":"RabbitMqDbChange","p":"RabbitMqDbChange"},{"l":2,"n":"RabbitMqPublisher","p":"RabbitMqPublisher"},{"l":2,"n":"RabbitMqReceive","p":"RabbitMqReceive"},{"l":2,"n":"RabbitMqSyncCallLog","p":"RabbitMqSyncCallLog"},{"l":2,"n":"Ranking","p":"Ranking"},{"l":2,"n":"Reports","p":"Reports"},{"l":2,"n":"Reports_model","p":"Reports_model"},{"l":2,"n":"Roles","p":"Roles"},{"l":2,"n":"Roles_model","p":"Roles_model"},{"l":2,"n":"SessionHandlerInterface","p":"SessionHandlerInterface"},{"l":2,"n":"Session_database_driver","p":"Session_database_driver"},{"l":2,"n":"Settings","p":"Settings"},{"l":2,"n":"Settings_model","p":"Settings_model"},{"l":2,"n":"Sms_clickatell","p":"Sms_clickatell"},{"l":2,"n":"Sms_msg91","p":"Sms_msg91"},{"l":2,"n":"Sms_twilio","p":"Sms_twilio"},{"l":2,"n":"Spam_filters","p":"Spam_filters"},{"l":2,"n":"Spam_filters_model","p":"Spam_filters_model"},{"l":2,"n":"Staff","p":"Staff"},{"l":2,"n":"Staff_created","p":"Staff_created"},{"l":2,"n":"Staff_event_notification","p":"Staff_event_notification"},{"l":2,"n":"Staff_forgot_password","p":"Staff_forgot_password"},{"l":2,"n":"Staff_merge_fields","p":"Staff_merge_fields"},{"l":2,"n":"Staff_model","p":"Staff_model"},{"l":2,"n":"Staff_password_resetted","p":"Staff_password_resetted"},{"l":2,"n":"Staff_reminder","p":"Staff_reminder"},{"l":2,"n":"Staff_statistics_model","p":"Staff_statistics_model"},{"l":2,"n":"Staff_teams_model","p":"Staff_teams_model"},{"l":2,"n":"Staff_two_factor_auth_key","p":"Staff_two_factor_auth_key"},{"l":2,"n":"Statement_model","p":"Statement_model"},{"l":2,"n":"Statement_pdf","p":"Statement_pdf"},{"l":2,"n":"Stripe","p":"Stripe"},{"l":2,"n":"Stripe_core","p":"Stripe_core"},{"l":2,"n":"Stripe_gateway","p":"Stripe_gateway"},{"l":2,"n":"Stripe_ideal","p":"Stripe_ideal"},{"l":2,"n":"Stripe_ideal_gateway","p":"Stripe_ideal_gateway"},{"l":2,"n":"Stripe_subscriptions","p":"Stripe_subscriptions"},{"l":2,"n":"Subscription","p":"Subscription"},{"l":2,"n":"Subscription_cancelled_to_customer","p":"Subscription_cancelled_to_customer"},{"l":2,"n":"Subscription_customer_subscribed_to_staff","p":"Subscription_customer_subscribed_to_staff"},{"l":2,"n":"Subscription_payment_failed_to_customer","p":"Subscription_payment_failed_to_customer"},{"l":2,"n":"Subscription_payment_requires_action","p":"Subscription_payment_requires_action"},{"l":2,"n":"Subscription_payment_succeeded","p":"Subscription_payment_succeeded"},{"l":2,"n":"Subscription_send_to_customer","p":"Subscription_send_to_customer"},{"l":2,"n":"Subscriptions","p":"Subscriptions"},{"l":2,"n":"Subscriptions_merge_fields","p":"Subscriptions_merge_fields"},{"l":2,"n":"Subscriptions_model","p":"Subscriptions_model"},{"l":2,"n":"Task_added_as_follower_to_staff","p":"Task_added_as_follower_to_staff"},{"l":2,"n":"Task_assigned_to_staff","p":"Task_assigned_to_staff"},{"l":2,"n":"Task_deadline_reminder_to_staff","p":"Task_deadline_reminder_to_staff"},{"l":2,"n":"Task_new_attachment_to_customer","p":"Task_new_attachment_to_customer"},{"l":2,"n":"Task_new_attachment_to_staff","p":"Task_new_attachment_to_staff"},{"l":2,"n":"Task_new_comment_to_customer","p":"Task_new_comment_to_customer"},{"l":2,"n":"Task_new_comment_to_staff","p":"Task_new_comment_to_staff"},{"l":2,"n":"Task_status_changed_to_customer","p":"Task_status_changed_to_customer"},{"l":2,"n":"Task_status_changed_to_staff","p":"Task_status_changed_to_staff"},{"l":2,"n":"Tasks","p":"Tasks"},{"l":2,"n":"Tasks_merge_fields","p":"Tasks_merge_fields"},{"l":2,"n":"Tasks_model","p":"Tasks_model"},{"l":2,"n":"Taxes","p":"Taxes"},{"l":2,"n":"Taxes_model","p":"Taxes_model"},{"l":2,"n":"Templates","p":"Templates"},{"l":2,"n":"Templates_model","p":"Templates_model"},{"l":2,"n":"Terms_and_conditions","p":"Terms_and_conditions"},{"l":2,"n":"TicketTemplate","p":"TicketTemplate"},{"l":2,"n":"Ticket_assigned_to_staff","p":"Ticket_assigned_to_staff"},{"l":2,"n":"Ticket_auto_close_to_customer","p":"Ticket_auto_close_to_customer"},{"l":2,"n":"Ticket_autoresponse","p":"Ticket_autoresponse"},{"l":2,"n":"Ticket_created_to_customer","p":"Ticket_created_to_customer"},{"l":2,"n":"Ticket_created_to_staff","p":"Ticket_created_to_staff"},{"l":2,"n":"Ticket_merge_fields","p":"Ticket_merge_fields"},{"l":2,"n":"Ticket_new_reply_to_customer","p":"Ticket_new_reply_to_customer"},{"l":2,"n":"Ticket_new_reply_to_staff","p":"Ticket_new_reply_to_staff"},{"l":2,"n":"Tickets","p":"Tickets"},{"l":2,"n":"Tickets_model","p":"Tickets_model"},{"l":2,"n":"Todo","p":"Todo"},{"l":2,"n":"Todo_model","p":"Todo_model"},{"l":2,"n":"Two_checkout","p":"Two_checkout"},{"l":2,"n":"Twocheckout_model","p":"Twocheckout_model"},{"l":2,"n":"UpdateEs","p":"UpdateEs"},{"l":2,"n":"User_Autologin","p":"User_Autologin"},{"l":2,"n":"Utilities","p":"Utilities"},{"l":2,"n":"Utilities_model","p":"Utilities_model"},{"l":2,"n":"Verification","p":"Verification"},{"l":2,"n":"XML_RPC_Client","p":"XML_RPC_Client"},{"l":2,"n":"XML_RPC_Message","p":"XML_RPC_Message"},{"l":2,"n":"XML_RPC_Response","p":"XML_RPC_Response"},{"l":2,"n":"XML_RPC_Values","p":"XML_RPC_Values"},{"l":2,"n":"simple_html_dom","p":"simple_html_dom"},{"l":2,"n":"simple_html_dom_node","p":"simple_html_dom_node"}]},{"l":1,"n":"Entities","p":"Entities","c":[{"l":2,"n":"Ams","p":"Entities/Ams","c":[{"l":3,"n":"AmsBaseEntity","p":"Entities/Ams/AmsBaseEntity"},{"l":3,"n":"Company","p":"Entities/Ams/Company"},{"l":3,"n":"Job","p":"Entities/Ams/Job"}]},{"l":2,"n":"ActivityLog","p":"Entities/ActivityLog"},{"l":2,"n":"Announcement","p":"Entities/Announcement"},{"l":2,"n":"BaseEntity","p":"Entities/BaseEntity"},{"l":2,"n":"Client","p":"Entities/Client"},{"l":2,"n":"ClientAffiliate","p":"Entities/ClientAffiliate"},{"l":2,"n":"ClientAmsCompany","p":"Entities/ClientAmsCompany"},{"l":2,"n":"ClientAmsJob","p":"Entities/ClientAmsJob"},{"l":2,"n":"ClientBranch","p":"Entities/ClientBranch"},{"l":2,"n":"ClientContactPool","p":"Entities/ClientContactPool"},{"l":2,"n":"ClientIndusty","p":"Entities/ClientIndusty"},{"l":2,"n":"ClientNationality","p":"Entities/ClientNationality"},{"l":2,"n":"ClientOffice","p":"Entities/ClientOffice"},{"l":2,"n":"ClientRequest","p":"Entities/ClientRequest"},{"l":2,"n":"Consent","p":"Entities/Consent"},{"l":2,"n":"ConsentPurpose","p":"Entities/ConsentPurpose"},{"l":2,"n":"Contact","p":"Entities/Contact"},{"l":2,"n":"ContactPermission","p":"Entities/ContactPermission"},{"l":2,"n":"ContactRequest","p":"Entities/ContactRequest"},{"l":2,"n":"ContactTerminated","p":"Entities/ContactTerminated"},{"l":2,"n":"Contract","p":"Entities/Contract"},{"l":2,"n":"ContractComment","p":"Entities/ContractComment"},{"l":2,"n":"ContractRenewal","p":"Entities/ContractRenewal"},{"l":2,"n":"ContractType","p":"Entities/ContractType"},{"l":2,"n":"Country","p":"Entities/Country"},{"l":2,"n":"Credit","p":"Entities/Credit"},{"l":2,"n":"CreditNote","p":"Entities/CreditNote"},{"l":2,"n":"CreditNoteRefund","p":"Entities/CreditNoteRefund"},{"l":2,"n":"Currency","p":"Entities/Currency"},{"l":2,"n":"CustomField","p":"Entities/CustomField"},{"l":2,"n":"CustomFieldValue","p":"Entities/CustomFieldValue"},{"l":2,"n":"CustomerAdmin","p":"Entities/CustomerAdmin"},{"l":2,"n":"CustomerGroup","p":"Entities/CustomerGroup"},{"l":2,"n":"Department","p":"Entities/Department"},{"l":2,"n":"DismissedAnnouncement","p":"Entities/DismissedAnnouncement"},{"l":2,"n":"EmailList","p":"Entities/EmailList"},{"l":2,"n":"EmailTemplate","p":"Entities/EmailTemplate"},{"l":2,"n":"Estimate","p":"Entities/Estimate"},{"l":2,"n":"EstimateRequest","p":"Entities/EstimateRequest"},{"l":2,"n":"EstimateRequestForm","p":"Entities/EstimateRequestForm"},{"l":2,"n":"EstimateRequestStatus","p":"Entities/EstimateRequestStatus"},{"l":2,"n":"Event","p":"Entities/Event"},{"l":2,"n":"Expense","p":"Entities/Expense"},{"l":2,"n":"ExpenseCategory","p":"Entities/ExpenseCategory"},{"l":2,"n":"ExtVersion","p":"Entities/ExtVersion"},{"l":2,"n":"FavoriteClient","p":"Entities/FavoriteClient"},{"l":2,"n":"FeedbackNote","p":"Entities/FeedbackNote"},{"l":2,"n":"File","p":"Entities/File"},{"l":2,"n":"FormQuestion","p":"Entities/FormQuestion"},{"l":2,"n":"FormQuestionBox","p":"Entities/FormQuestionBox"},{"l":2,"n":"FormQuestionBoxDescription","p":"Entities/FormQuestionBoxDescription"},{"l":2,"n":"FormResult","p":"Entities/FormResult"},{"l":2,"n":"GdprRequest","p":"Entities/GdprRequest"},{"l":2,"n":"Goal","p":"Entities/Goal"},{"l":2,"n":"Invoice","p":"Entities/Invoice"},{"l":2,"n":"InvoicePaymentRecord","p":"Entities/InvoicePaymentRecord"},{"l":2,"n":"InvoiceRequest","p":"Entities/InvoiceRequest"},{"l":2,"n":"Item","p":"Entities/Item"},{"l":2,"n":"ItemGroup","p":"Entities/ItemGroup"},{"l":2,"n":"ItemTax","p":"Entities/ItemTax"},{"l":2,"n":"Itemable","p":"Entities/Itemable"},{"l":2,"n":"KnowedgeBaseArticleFeedback","p":"Entities/KnowedgeBaseArticleFeedback"},{"l":2,"n":"KnowledgeBase","p":"Entities/KnowledgeBase"},{"l":2,"n":"KnowledgeBaseGroup","p":"Entities/KnowledgeBaseGroup"},{"l":2,"n":"Lead","p":"Entities/Lead"},{"l":2,"n":"LeadActivityLog","p":"Entities/LeadActivityLog"},{"l":2,"n":"LeadEmailIntegration","p":"Entities/LeadEmailIntegration"},{"l":2,"n":"LeadIntegrationEmail","p":"Entities/LeadIntegrationEmail"},{"l":2,"n":"LeadSource","p":"Entities/LeadSource"},{"l":2,"n":"LeadStatus","p":"Entities/LeadStatus"},{"l":2,"n":"ListEmail","p":"Entities/ListEmail"},{"l":2,"n":"M3cCall","p":"Entities/M3cCall"},{"l":2,"n":"M3cCallLogFailedJob","p":"Entities/M3cCallLogFailedJob"},{"l":2,"n":"M3cCallLogSyncedTime","p":"Entities/M3cCallLogSyncedTime"},{"l":2,"n":"MailListCustomField","p":"Entities/MailListCustomField"},{"l":2,"n":"MailListCustomFieldValue","p":"Entities/MailListCustomFieldValue"},{"l":2,"n":"MailQueue","p":"Entities/MailQueue"},{"l":2,"n":"Milestone","p":"Entities/Milestone"},{"l":2,"n":"Module","p":"Entities/Module"},{"l":2,"n":"NewFeedCommentLike","p":"Entities/NewFeedCommentLike"},{"l":2,"n":"NewFeedPost","p":"Entities/NewFeedPost"},{"l":2,"n":"NewFeedPostComment","p":"Entities/NewFeedPostComment"},{"l":2,"n":"NewFeedPostLike","p":"Entities/NewFeedPostLike"},{"l":2,"n":"Note","p":"Entities/Note"},{"l":2,"n":"Notification","p":"Entities/Notification"},{"l":2,"n":"Option","p":"Entities/Option"},{"l":2,"n":"PaymentMode","p":"Entities/PaymentMode"},{"l":2,"n":"PinnedProject","p":"Entities/PinnedProject"},{"l":2,"n":"Project","p":"Entities/Project"},{"l":2,"n":"ProjectActivity","p":"Entities/ProjectActivity"},{"l":2,"n":"ProjectDiscussion","p":"Entities/ProjectDiscussion"},{"l":2,"n":"ProjectDiscussionComment","p":"Entities/ProjectDiscussionComment"},{"l":2,"n":"ProjectFile","p":"Entities/ProjectFile"},{"l":2,"n":"ProjectMember","p":"Entities/ProjectMember"},{"l":2,"n":"ProjectNote","p":"Entities/ProjectNote"},{"l":2,"n":"ProjectSetting","p":"Entities/ProjectSetting"},{"l":2,"n":"Proposal","p":"Entities/Proposal"},{"l":2,"n":"ProposalComment","p":"Entities/ProposalComment"},{"l":2,"n":"RelatedItem","p":"Entities/RelatedItem"},{"l":2,"n":"Reminder","p":"Entities/Reminder"},{"l":2,"n":"Role","p":"Entities/Role"},{"l":2,"n":"SaleActivity","p":"Entities/SaleActivity"},{"l":2,"n":"ScheduledEmail","p":"Entities/ScheduledEmail"},{"l":2,"n":"Service","p":"Entities/Service"},{"l":2,"n":"Session","p":"Entities/Session"},{"l":2,"n":"SharedCustomerFile","p":"Entities/SharedCustomerFile"},{"l":2,"n":"SpamFilter","p":"Entities/SpamFilter"},{"l":2,"n":"Staff","p":"Entities/Staff"},{"l":2,"n":"StaffDepartment","p":"Entities/StaffDepartment"},{"l":2,"n":"StaffPermission","p":"Entities/StaffPermission"},{"l":2,"n":"StaffStatistic","p":"Entities/StaffStatistic"},{"l":2,"n":"StaffTeam","p":"Entities/StaffTeam"},{"l":2,"n":"Subscription","p":"Entities/Subscription"},{"l":2,"n":"Survey","p":"Entities/Survey"},{"l":2,"n":"SurveyEmailSendCron","p":"Entities/SurveyEmailSendCron"},{"l":2,"n":"SurveyResultSet","p":"Entities/SurveyResultSet"},{"l":2,"n":"SurveySendLog","p":"Entities/SurveySendLog"},{"l":2,"n":"Tag","p":"Entities/Tag"},{"l":2,"n":"Taggable","p":"Entities/Taggable"},{"l":2,"n":"Task","p":"Entities/Task"},{"l":2,"n":"TaskAssigned","p":"Entities/TaskAssigned"},{"l":2,"n":"TaskChecklistItem","p":"Entities/TaskChecklistItem"},{"l":2,"n":"TaskChecklistTemplate","p":"Entities/TaskChecklistTemplate"},{"l":2,"n":"TaskComment","p":"Entities/TaskComment"},{"l":2,"n":"TaskFollower","p":"Entities/TaskFollower"},{"l":2,"n":"TaskTimer","p":"Entities/TaskTimer"},{"l":2,"n":"Tax","p":"Entities/Tax"},{"l":2,"n":"Team","p":"Entities/Team"},{"l":2,"n":"Template","p":"Entities/Template"},{"l":2,"n":"Ticket","p":"Entities/Ticket"},{"l":2,"n":"TicketAttachment","p":"Entities/TicketAttachment"},{"l":2,"n":"TicketPipeLog","p":"Entities/TicketPipeLog"},{"l":2,"n":"TicketPredefinedReply","p":"Entities/TicketPredefinedReply"},{"l":2,"n":"TicketPriority","p":"Entities/TicketPriority"},{"l":2,"n":"TicketReply","p":"Entities/TicketReply"},{"l":2,"n":"TicketStatus","p":"Entities/TicketStatus"},{"l":2,"n":"Todo","p":"Entities/Todo"},{"l":2,"n":"TrackedMail","p":"Entities/TrackedMail"},{"l":2,"n":"TwoCheckoutLog","p":"Entities/TwoCheckoutLog"},{"l":2,"n":"UserAutoLogin","p":"Entities/UserAutoLogin"},{"l":2,"n":"UserMeta","p":"Entities/UserMeta"},{"l":2,"n":"Vault","p":"Entities/Vault"},{"l":2,"n":"ViewTracking","p":"Entities/ViewTracking"},{"l":2,"n":"WebToLead","p":"Entities/WebToLead"}]},{"l":1,"n":"app","p":"app","c":[{"l":2,"n":"libraries","p":"app/libraries","c":[{"l":3,"n":"elasticsearch","p":"app/libraries/elasticsearch","c":[{"l":4,"n":"CombinedFieldsQuery","p":"app/libraries/elasticsearch/CombinedFieldsQuery"}]}]},{"l":2,"n":"services","p":"app/services","c":[{"l":3,"n":"estimates","p":"app/services/estimates","c":[{"l":4,"n":"EstimatesPipeline","p":"app/services/estimates/EstimatesPipeline"}]},{"l":3,"n":"imap","p":"app/services/imap","c":[{"l":4,"n":"Connection","p":"app/services/imap/Connection"},{"l":4,"n":"ConnectionErrorException","p":"app/services/imap/ConnectionErrorException"},{"l":4,"n":"Imap","p":"app/services/imap/Imap"},{"l":4,"n":"ImapResource","p":"app/services/imap/ImapResource"},{"l":4,"n":"Mailbox","p":"app/services/imap/Mailbox"},{"l":4,"n":"Server","p":"app/services/imap/Server"}]},{"l":3,"n":"leads","p":"app/services/leads","c":[{"l":4,"n":"LeadsKanban","p":"app/services/leads/LeadsKanban"}]},{"l":3,"n":"messages","p":"app/services/messages","c":[{"l":4,"n":"AbstractMessage","p":"app/services/messages/AbstractMessage"},{"l":4,"n":"AbstractPopupMessage","p":"app/services/messages/AbstractPopupMessage"},{"l":4,"n":"CloudFlare","p":"app/services/messages/CloudFlare"},{"l":4,"n":"CronJobFailure","p":"app/services/messages/CronJobFailure"},{"l":4,"n":"DatabaseNameHasDot","p":"app/services/messages/DatabaseNameHasDot"},{"l":4,"n":"DevelopmentEnvironment","p":"app/services/messages/DevelopmentEnvironment"},{"l":4,"n":"FirstLeadCreated","p":"app/services/messages/FirstLeadCreated"},{"l":4,"n":"FirstTagCreated","p":"app/services/messages/FirstTagCreated"},{"l":4,"n":"FirstTicketCreated","p":"app/services/messages/FirstTicketCreated"},{"l":4,"n":"Iconv","p":"app/services/messages/Iconv"},{"l":4,"n":"IsBaseUrlChangeRequired","p":"app/services/messages/IsBaseUrlChangeRequired"},{"l":4,"n":"IsCronSetupRequired","p":"app/services/messages/IsCronSetupRequired"},{"l":4,"n":"MailConfigured","p":"app/services/messages/MailConfigured"},{"l":4,"n":"Message","p":"app/services/messages/Message"},{"l":4,"n":"ModSecurityEnabled","p":"app/services/messages/ModSecurityEnabled"},{"l":4,"n":"PhpVersionNotice","p":"app/services/messages/PhpVersionNotice"},{"l":4,"n":"PopupMessage","p":"app/services/messages/PopupMessage"},{"l":4,"n":"ReOrderTaskChecklistItems","p":"app/services/messages/ReOrderTaskChecklistItems"},{"l":4,"n":"StartTimersWithNoTasks","p":"app/services/messages/StartTimersWithNoTasks"},{"l":4,"n":"StaticIndexHtml","p":"app/services/messages/StaticIndexHtml"},{"l":4,"n":"TcpdfFileMissing","p":"app/services/messages/TcpdfFileMissing"},{"l":4,"n":"Timezone","p":"app/services/messages/Timezone"}]},{"l":3,"n":"projects","p":"app/services/projects","c":[{"l":4,"n":"AbstractGantt","p":"app/services/projects/AbstractGantt"},{"l":4,"n":"AllProjectsGantt","p":"app/services/projects/AllProjectsGantt"},{"l":4,"n":"Gantt","p":"app/services/projects/Gantt"},{"l":4,"n":"HoursOverviewChart","p":"app/services/projects/HoursOverviewChart"}]},{"l":3,"n":"proposals","p":"app/services/proposals","c":[{"l":4,"n":"ProposalsPipeline","p":"app/services/proposals/ProposalsPipeline"}]},{"l":3,"n":"tasks","p":"app/services/tasks","c":[{"l":4,"n":"TasksKanban","p":"app/services/tasks/TasksKanban"}]},{"l":3,"n":"upgrade","p":"app/services/upgrade","c":[{"l":4,"n":"AbstractCurlAdapter","p":"app/services/upgrade/AbstractCurlAdapter"},{"l":4,"n":"Config","p":"app/services/upgrade/Config"},{"l":4,"n":"CoreInterface","p":"app/services/upgrade/CoreInterface"},{"l":4,"n":"CurlCoreUpgradeAdapter","p":"app/services/upgrade/CurlCoreUpgradeAdapter"},{"l":4,"n":"GuzzleCoreUpgradeAdapter","p":"app/services/upgrade/GuzzleCoreUpgradeAdapter"},{"l":4,"n":"Response","p":"app/services/upgrade/Response"},{"l":4,"n":"UpgradeCore","p":"app/services/upgrade/UpgradeCore"}]},{"l":3,"n":"utilities","p":"app/services/utilities","c":[{"l":4,"n":"Arr","p":"app/services/utilities/Arr"},{"l":4,"n":"Clockwork","p":"app/services/utilities/Clockwork"},{"l":4,"n":"Date","p":"app/services/utilities/Date"},{"l":4,"n":"Format","p":"app/services/utilities/Format"},{"l":4,"n":"Locale","p":"app/services/utilities/Locale"},{"l":4,"n":"Str","p":"app/services/utilities/Str"},{"l":4,"n":"StrClickable","p":"app/services/utilities/StrClickable"},{"l":4,"n":"Utils","p":"app/services/utilities/Utils"}]},{"l":3,"n":"zip","p":"app/services/zip","c":[{"l":4,"n":"ExtractException","p":"app/services/zip/ExtractException"},{"l":4,"n":"Unzip","p":"app/services/zip/Unzip"}]},{"l":3,"n":"AbstractKanban","p":"app/services/AbstractKanban"},{"l":3,"n":"ActivityLogger","p":"app/services/ActivityLogger"},{"l":3,"n":"AmsService","p":"app/services/AmsService"},{"l":3,"n":"CustomerProfileBadges","p":"app/services/CustomerProfileBadges"},{"l":3,"n":"LeadProfileBadges","p":"app/services/LeadProfileBadges"},{"l":3,"n":"MergeTickets","p":"app/services/MergeTickets"},{"l":3,"n":"TicketsReportByStaff","p":"app/services/TicketsReportByStaff"},{"l":3,"n":"Timezones","p":"app/services/Timezones"},{"l":3,"n":"ValidatesContact","p":"app/services/ValidatesContact"},{"l":3,"n":"ViewsTracking","p":"app/services/ViewsTracking"}]}]}]},"treeOpenLevel":2},
    /** @var boolean */
    treeLoaded: false,
    /** @var boolean */
    listenersRegistered: false,
    autoCompleteData: null,
    /** @var boolean */
    autoCompleteLoading: false,
    /** @var boolean */
    autoCompleteLoaded: false,
    /** @var string|null */
    rootPath: null,
    /** @var string|null */
    autoCompleteDataUrl: null,
    /** @var HTMLElement|null */
    doctumSearchAutoComplete: null,
    /** @var HTMLElement|null */
    doctumSearchAutoCompleteProgressBarContainer: null,
    /** @var HTMLElement|null */
    doctumSearchAutoCompleteProgressBar: null,
    /** @var number */
    doctumSearchAutoCompleteProgressBarPercent: 0,
    /** @var autoComplete|null */
    autoCompleteJS: null,
    querySearchSecurityRegex: /([^0-9a-zA-Z:\\\\_\s])/gi,
    buildTreeNode: function (treeNode, htmlNode, treeOpenLevel) {
        var ulNode = document.createElement('ul');
        for (var childKey in treeNode.c) {
            var child = treeNode.c[childKey];
            var liClass = document.createElement('li');
            var hasChildren = child.hasOwnProperty('c');
            var nodeSpecialName = (hasChildren ? 'namespace:' : 'class:') + child.p.replace(/\//g, '_');
            liClass.setAttribute('data-name', nodeSpecialName);

            // Create the node that will have the text
            var divHd = document.createElement('div');
            var levelCss = child.l - 1;
            divHd.className = hasChildren ? 'hd' : 'hd leaf';
            divHd.style.paddingLeft = (hasChildren ? (levelCss * 18) : (8 + (levelCss * 18))) + 'px';
            if (hasChildren) {
                if (child.l <= treeOpenLevel) {
                    liClass.className = 'opened';
                }
                var spanIcon = document.createElement('span');
                spanIcon.className = 'icon icon-play';
                divHd.appendChild(spanIcon);
            }
            var aLink = document.createElement('a');

            // Edit the HTML link to work correctly based on the current depth
            aLink.href = Doctum.rootPath + child.p + '.html';
            aLink.innerText = child.n;
            divHd.appendChild(aLink);
            liClass.appendChild(divHd);

            // It has children
            if (hasChildren) {
                var divBd = document.createElement('div');
                divBd.className = 'bd';
                Doctum.buildTreeNode(child, divBd, treeOpenLevel);
                liClass.appendChild(divBd);
            }
            ulNode.appendChild(liClass);
        }
        htmlNode.appendChild(ulNode);
    },
    initListeners: function () {
        if (Doctum.listenersRegistered) {
            // Quick exit, already registered
            return;
        }
                Doctum.listenersRegistered = true;
    },
    loadTree: function () {
        if (Doctum.treeLoaded) {
            // Quick exit, already registered
            return;
        }
        Doctum.rootPath = document.body.getAttribute('data-root-path');
        Doctum.buildTreeNode(Doctum.treeJson.tree, document.getElementById('api-tree'), Doctum.treeJson.treeOpenLevel);

        // Toggle left-nav divs on click
        $('#api-tree .hd span').on('click', function () {
            $(this).parent().parent().toggleClass('opened');
        });

        // Expand the parent namespaces of the current page.
        var expected = $('body').attr('data-name');

        if (expected) {
            // Open the currently selected node and its parents.
            var container = $('#api-tree');
            var node = $('#api-tree li[data-name="' + expected + '"]');
            // Node might not be found when simulating namespaces
            if (node.length > 0) {
                node.addClass('active').addClass('opened');
                node.parents('li').addClass('opened');
                var scrollPos = node.offset().top - container.offset().top + container.scrollTop();
                // Position the item nearer to the top of the screen.
                scrollPos -= 200;
                container.scrollTop(scrollPos);
            }
        }
        Doctum.treeLoaded = true;
    },
    pagePartiallyLoaded: function (event) {
        Doctum.initListeners();
        Doctum.loadTree();
        Doctum.loadAutoComplete();
    },
    pageFullyLoaded: function (event) {
        // it may not have received DOMContentLoaded event
        Doctum.initListeners();
        Doctum.loadTree();
        Doctum.loadAutoComplete();
        // Fire the event in the search page too
        if (typeof DoctumSearch === 'object') {
            DoctumSearch.pageFullyLoaded();
        }
    },
    loadAutoComplete: function () {
        if (Doctum.autoCompleteLoaded) {
            // Quick exit, already loaded
            return;
        }
        Doctum.autoCompleteDataUrl = document.body.getAttribute('data-search-index-url');
        Doctum.doctumSearchAutoComplete = document.getElementById('doctum-search-auto-complete');
        Doctum.doctumSearchAutoCompleteProgressBarContainer = document.getElementById('search-progress-bar-container');
        Doctum.doctumSearchAutoCompleteProgressBar = document.getElementById('search-progress-bar');
        if (Doctum.doctumSearchAutoComplete !== null) {
            // Wait for it to be loaded
            Doctum.doctumSearchAutoComplete.addEventListener('init', function (_) {
                Doctum.autoCompleteLoaded = true;
                Doctum.doctumSearchAutoComplete.addEventListener('selection', function (event) {
                    // Go to selection page
                    window.location = Doctum.rootPath + event.detail.selection.value.p;
                });
                Doctum.doctumSearchAutoComplete.addEventListener('navigate', function (event) {
                    // Set selection in text box
                    if (typeof event.detail.selection.value === 'object') {
                        Doctum.doctumSearchAutoComplete.value = event.detail.selection.value.n;
                    }
                });
                Doctum.doctumSearchAutoComplete.addEventListener('results', function (event) {
                    Doctum.markProgressFinished();
                });
            });
        }
        // Check if the lib is loaded
        if (typeof autoComplete === 'function') {
            Doctum.bootAutoComplete();
        }
    },
    markInProgress: function () {
            Doctum.doctumSearchAutoCompleteProgressBarContainer.className = 'search-bar';
            Doctum.doctumSearchAutoCompleteProgressBar.className = 'progress-bar indeterminate';
            if (typeof DoctumSearch === 'object' && DoctumSearch.pageFullyLoaded) {
                DoctumSearch.doctumSearchPageAutoCompleteProgressBarContainer.className = 'search-bar';
                DoctumSearch.doctumSearchPageAutoCompleteProgressBar.className = 'progress-bar indeterminate';
            }
    },
    markProgressFinished: function () {
        Doctum.doctumSearchAutoCompleteProgressBarContainer.className = 'search-bar hidden';
        Doctum.doctumSearchAutoCompleteProgressBar.className = 'progress-bar';
        if (typeof DoctumSearch === 'object' && DoctumSearch.pageFullyLoaded) {
            DoctumSearch.doctumSearchPageAutoCompleteProgressBarContainer.className = 'search-bar hidden';
            DoctumSearch.doctumSearchPageAutoCompleteProgressBar.className = 'progress-bar';
        }
    },
    makeProgess: function () {
        Doctum.makeProgressOnProgressBar(
            Doctum.doctumSearchAutoCompleteProgressBarPercent,
            Doctum.doctumSearchAutoCompleteProgressBar
        );
        if (typeof DoctumSearch === 'object' && DoctumSearch.pageFullyLoaded) {
            Doctum.makeProgressOnProgressBar(
                Doctum.doctumSearchAutoCompleteProgressBarPercent,
                DoctumSearch.doctumSearchPageAutoCompleteProgressBar
            );
        }
    },
    loadAutoCompleteData: function (query) {
        return new Promise(function (resolve, reject) {
            if (Doctum.autoCompleteData !== null) {
                resolve(Doctum.autoCompleteData);
                return;
            }
            Doctum.markInProgress();
            function reqListener() {
                Doctum.autoCompleteLoading = false;
                Doctum.autoCompleteData = JSON.parse(this.responseText).items;
                Doctum.markProgressFinished();

                setTimeout(function () {
                    resolve(Doctum.autoCompleteData);
                }, 50);// Let the UI render once before sending the results for processing. This gives time to the progress bar to hide
            }
            function reqError(err) {
                Doctum.autoCompleteLoading = false;
                Doctum.autoCompleteData = null;
                console.error(err);
                reject(err);
            }

            var oReq = new XMLHttpRequest();
            oReq.onload = reqListener;
            oReq.onerror = reqError;
            oReq.onprogress = function (pe) {
                if (pe.lengthComputable) {
                    Doctum.doctumSearchAutoCompleteProgressBarPercent = parseInt(pe.loaded / pe.total * 100, 10);
                    Doctum.makeProgess();
                }
            };
            oReq.onloadend = function (_) {
                Doctum.markProgressFinished();
            };
            oReq.open('get', Doctum.autoCompleteDataUrl, true);
            oReq.send();
        });
    },
    /**
     * Make some progress on a progress bar
     *
     * @param number percentage
     * @param HTMLElement progressBar
     * @return void
     */
    makeProgressOnProgressBar: function(percentage, progressBar) {
        progressBar.className = 'progress-bar';
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute(
            'aria-valuenow', percentage
        );
    },
    searchEngine: function (query, record) {
        if (typeof query !== 'string') {
            return '';
        }
        // replace all (mode = g) spaces and non breaking spaces (\s) by pipes
        // g = global mode to mark also the second word searched
        // i = case insensitive
        // how this function works:
        // First: search if the query has the keywords in sequence
        // Second: replace the keywords by a mark and leave all the text in between non marked
        
        if (record.match(new RegExp('(' + query.replace(/\s/g, ').*(') + ')', 'gi')) === null) {
            return '';// Does not match
        }

        var replacedRecord = record.replace(new RegExp('(' + query.replace(/\s/g, '|') + ')', 'gi'), function (group) {
            return '<mark class="auto-complete-highlight">' + group + '</mark>';
        });

        if (replacedRecord !== record) {
            return replacedRecord;// This should not happen but just in case there was no match done
        }

        return '';
    },
    /**
     * Clean the search query
     *
     * @param string|null query
     * @return string
     */
    cleanSearchQuery: function (query) {
        if (typeof query !== 'string') {
            return '';
        }
        // replace any chars that could lead to injecting code in our regex
        // remove start or end spaces
        // replace backslashes by an escaped version, use case in search: \myRootFunction
        return query.replace(Doctum.querySearchSecurityRegex, '').trim().replace(/\\/g, '\\\\');
    },
    bootAutoComplete: function () {
        Doctum.autoCompleteJS = new autoComplete(
            {
                selector: '#doctum-search-auto-complete',
                searchEngine: function (query, record) {
                    return Doctum.searchEngine(query, record);
                },
                submit: true,
                data: {
                    src: function (q) {
                        Doctum.markInProgress();
                        return Doctum.loadAutoCompleteData(q);
                    },
                    keys: ['n'],// Data 'Object' key to be searched
                    cache: false, // Is not compatible with async fetch of data
                },
                query: (input) => {
                    return Doctum.cleanSearchQuery(input);
                },
                trigger: (query) => {
                    return Doctum.cleanSearchQuery(query).length > 0;
                },
                resultsList: {
                    tag: 'ul',
                    class: 'auto-complete-dropdown-menu',
                    destination: '#auto-complete-results',
                    position: 'afterbegin',
                    maxResults: 500,
                    noResults: false,
                },
                resultItem: {
                    tag: 'li',
                    class: 'auto-complete-result',
                    highlight: 'auto-complete-highlight',
                    selected: 'auto-complete-selected'
                },
            }
        );
    }
};


document.addEventListener('DOMContentLoaded', Doctum.pagePartiallyLoaded, false);
window.addEventListener('load', Doctum.pageFullyLoaded, false);

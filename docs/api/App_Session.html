<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>App_Session | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:App_Session" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>App_Session    
            </h1>
    </div>

    
    <p>        class
    <strong>App_Session</strong>        extends <a href="CI_Session.html">CI_Session</a>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_userdata">
                                                                                
                                                                                
                                    </td>
                <td>$userdata</td>
                <td class="last"><p>Userdata array</p></td>
                <td><small>from&nbsp;<a href="CI_Session.html#property_userdata">
CI_Session</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__driver">
                                        protected                                        
                                                                                
                                    </td>
                <td>$_driver</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="CI_Session.html#property__driver">
CI_Session</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__config">
                                        protected                                        
                                                                                
                                    </td>
                <td>$_config</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="CI_Session.html#property__config">
CI_Session</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property__sid_regexp">
                                        protected                                        
                                                                                
                                    </td>
                <td>$_sid_regexp</td>
                <td class="last"></td>
                <td><small>from&nbsp;<a href="CI_Session.html#property__sid_regexp">
CI_Session</a></small></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $params = [])
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__ci_load_classes">_ci_load_classes</a>(string $driver)
        
                                            <p><p>CI Load Classes</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method__ci_load_classes">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__configure">_configure</a>(array $params)
        
                                            <p>Configuration</p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__configure_sid_length">_configure_sid_length</a>()
        
                                            <p><p>Configure session ID length</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method__configure_sid_length">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method__ci_init_vars">_ci_init_vars</a>()
        
                                            <p><p>Handle temporary variables</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_mark_as_flash">mark_as_flash</a>(mixed $key)
        
                                            <p><p>Mark as flash</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_mark_as_flash">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_flash_keys">get_flash_keys</a>()
        
                                            <p><p>Get flash keys</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_get_flash_keys">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_unmark_flash">unmark_flash</a>(mixed $key)
        
                                            <p><p>Unmark flash</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_unmark_flash">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_mark_as_temp">mark_as_temp</a>(mixed $key, int $ttl = 300)
        
                                            <p><p>Mark as temp</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_mark_as_temp">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_temp_keys">get_temp_keys</a>()
        
                                            <p><p>Get temp keys</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_get_temp_keys">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_unmark_temp">unmark_temp</a>(mixed $key)
        
                                            <p><p>Unmark temp</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_unmark_temp">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method___get">__get</a>(string $key)
        
                                            <p>__get()</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method___get">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method___isset">__isset</a>(string $key)
        
                                            <p>__isset()</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method___isset">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___set">__set</a>(string $key, mixed $value)
        
                                            <p>__set()</p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method___set">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_sess_destroy">sess_destroy</a>()
        
                                            <p><p>Session destroy</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_sess_destroy">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_sess_regenerate">sess_regenerate</a>(bool $destroy = FALSE)
        
                                            <p><p>Session regenerate</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_sess_regenerate">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_get_userdata">get_userdata</a>()
        
                                            <p><p>Get userdata reference</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_get_userdata">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_userdata">userdata</a>(string $key = NULL)
        
                                            <p><p>Userdata (fetch)</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_userdata">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_userdata">set_userdata</a>(mixed $data, mixed $value = NULL)
        
                                            <p><p>Set userdata</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_set_userdata">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_unset_userdata">unset_userdata</a>(mixed $key)
        
                                            <p><p>Unset userdata</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_unset_userdata">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_all_userdata">all_userdata</a>()
        
                                            <p><p>All userdata (fetch)</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_all_userdata">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_has_userdata">has_userdata</a>(string $key)
        
                                            <p><p>Has userdata</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_has_userdata">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_flashdata">flashdata</a>(string $key = NULL)
        
                                            <p><p>Flashdata (fetch)</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_flashdata">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_flashdata">set_flashdata</a>(mixed $data, mixed $value = NULL)
        
                                            <p><p>Set flashdata</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_set_flashdata">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_keep_flashdata">keep_flashdata</a>(mixed $key)
        
                                            <p><p>Keep flashdata</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_keep_flashdata">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_tempdata">tempdata</a>(string $key = NULL)
        
                                            <p><p>Temp data (fetch)</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_tempdata">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_tempdata">set_tempdata</a>(mixed $data, mixed $value = NULL, int $ttl = 300)
        
                                            <p><p>Set tempdata</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_set_tempdata">
CI_Session</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_unset_tempdata">unset_tempdata</a>($key)
        
                                            <p><p>Unset tempdata</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Session.html#method_unset_tempdata">
CI_Session</a></small></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 13</div>
        <code>                    void
    <strong>__construct</strong>(array $params = [])
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Configuration parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__ci_load_classes">
        <div class="location">in <a href="CI_Session.html#method__ci_load_classes">
CI_Session</a> at line 191</div>
        <code>            protected        string
    <strong>_ci_load_classes</strong>(string $driver)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>CI Load Classes</p></p>                <p><p>An internal method to load all possible dependency and extension
classes. It kind of emulates the CI_Driver library, but is
self-sufficient.</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$driver</td>
                <td><p>Driver name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>Driver class name</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__configure">
        <div class="location">at line 143</div>
        <code>            protected        void
    <strong>_configure</strong>(array $params)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>Configuration</p>                <p><p>Handle input parameters and configuration defaults</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$params</td>
                <td><p>Input parameters</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__configure_sid_length">
        <div class="location">in <a href="CI_Session.html#method__configure_sid_length">
CI_Session</a> at line 339</div>
        <code>            protected        void
    <strong>_configure_sid_length</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Configure session ID length</p></p>                <p><p>To make life easier, we used to force SHA-1 and 4 bits per
character on everyone. And of course, someone was unhappy.</p>
<p>Then PHP 7.1 broke backwards-compatibility because ext/session
is such a mess that nobody wants to touch it with a pole stick,
and the one guy who does, nobody has the energy to argue with.</p>
<p>So we were forced to make changes, and OF COURSE something was
going to break and now we have this pile of shit. -- Narf</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__ci_init_vars">
        <div class="location">at line 224</div>
        <code>            protected        void
    <strong>_ci_init_vars</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Handle temporary variables</p></p>                <p><p>Clears old &quot;flash&quot; data, marks the new one for deletion and handles
&quot;temp&quot; data deletion.</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mark_as_flash">
        <div class="location">in <a href="CI_Session.html#method_mark_as_flash">
CI_Session</a> at line 443</div>
        <code>                    bool
    <strong>mark_as_flash</strong>(mixed $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Mark as flash</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$key</td>
                <td><p>Session data key(s)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_flash_keys">
        <div class="location">in <a href="CI_Session.html#method_get_flash_keys">
CI_Session</a> at line 480</div>
        <code>                    array
    <strong>get_flash_keys</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get flash keys</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_unmark_flash">
        <div class="location">in <a href="CI_Session.html#method_unmark_flash">
CI_Session</a> at line 504</div>
        <code>                    void
    <strong>unmark_flash</strong>(mixed $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Unmark flash</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$key</td>
                <td><p>Session data key(s)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_mark_as_temp">
        <div class="location">in <a href="CI_Session.html#method_mark_as_temp">
CI_Session</a> at line 536</div>
        <code>                    bool
    <strong>mark_as_temp</strong>(mixed $key, int $ttl = 300)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Mark as temp</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$key</td>
                <td><p>Session data key(s)</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$ttl</td>
                <td><p>Time-to-live in seconds</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_temp_keys">
        <div class="location">in <a href="CI_Session.html#method_get_temp_keys">
CI_Session</a> at line 588</div>
        <code>                    array
    <strong>get_temp_keys</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get temp keys</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_unmark_temp">
        <div class="location">in <a href="CI_Session.html#method_unmark_temp">
CI_Session</a> at line 612</div>
        <code>                    void
    <strong>unmark_temp</strong>(mixed $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Unmark temp</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$key</td>
                <td><p>Session data key(s)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___get">
        <div class="location">in <a href="CI_Session.html#method___get">
CI_Session</a> at line 643</div>
        <code>                    mixed
    <strong>__get</strong>(string $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>__get()</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td><p>'session_id' or a session data key</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___isset">
        <div class="location">in <a href="CI_Session.html#method___isset">
CI_Session</a> at line 667</div>
        <code>                    bool
    <strong>__isset</strong>(string $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>__isset()</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td><p>'session_id' or a session data key</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___set">
        <div class="location">in <a href="CI_Session.html#method___set">
CI_Session</a> at line 686</div>
        <code>                    void
    <strong>__set</strong>(string $key, mixed $value)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p>__set()</p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td><p>Session data key</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$value</td>
                <td><p>Session data value</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_sess_destroy">
        <div class="location">in <a href="CI_Session.html#method_sess_destroy">
CI_Session</a> at line 700</div>
        <code>                    void
    <strong>sess_destroy</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Session destroy</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_sess_regenerate">
        <div class="location">in <a href="CI_Session.html#method_sess_regenerate">
CI_Session</a> at line 715</div>
        <code>                    void
    <strong>sess_regenerate</strong>(bool $destroy = FALSE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Session regenerate</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$destroy</td>
                <td><p>Destroy old session data flag</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_userdata">
        <div class="location">in <a href="CI_Session.html#method_get_userdata">
CI_Session</a> at line 730</div>
        <code>                    
    <strong>get_userdata</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get userdata reference</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_userdata">
        <div class="location">in <a href="CI_Session.html#method_userdata">
CI_Session</a> at line 745</div>
        <code>                    mixed
    <strong>userdata</strong>(string $key = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Userdata (fetch)</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td><p>Session data key</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>Session data value or NULL if not found</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_userdata">
        <div class="location">in <a href="CI_Session.html#method_set_userdata">
CI_Session</a> at line 785</div>
        <code>                    void
    <strong>set_userdata</strong>(mixed $data, mixed $value = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set userdata</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$data</td>
                <td><p>Session data key or an associative array</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$value</td>
                <td><p>Value to store</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_unset_userdata">
        <div class="location">in <a href="CI_Session.html#method_unset_userdata">
CI_Session</a> at line 810</div>
        <code>                    void
    <strong>unset_userdata</strong>(mixed $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Unset userdata</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$key</td>
                <td><p>Session data key(s)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_all_userdata">
        <div class="location">in <a href="CI_Session.html#method_all_userdata">
CI_Session</a> at line 834</div>
        <code>                    array
    <strong>all_userdata</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>All userdata (fetch)</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td><p>$_SESSION, excluding flash data items</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_has_userdata">
        <div class="location">in <a href="CI_Session.html#method_has_userdata">
CI_Session</a> at line 849</div>
        <code>                    bool
    <strong>has_userdata</strong>(string $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Has userdata</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td><p>Session data key</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_flashdata">
        <div class="location">in <a href="CI_Session.html#method_flashdata">
CI_Session</a> at line 864</div>
        <code>                    mixed
    <strong>flashdata</strong>(string $key = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Flashdata (fetch)</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td><p>Session data key</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>Session data value or NULL if not found</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_flashdata">
        <div class="location">in <a href="CI_Session.html#method_set_flashdata">
CI_Session</a> at line 897</div>
        <code>                    void
    <strong>set_flashdata</strong>(mixed $data, mixed $value = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set flashdata</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$data</td>
                <td><p>Session data key or an associative array</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$value</td>
                <td><p>Value to store</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_keep_flashdata">
        <div class="location">in <a href="CI_Session.html#method_keep_flashdata">
CI_Session</a> at line 913</div>
        <code>                    void
    <strong>keep_flashdata</strong>(mixed $key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Keep flashdata</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$key</td>
                <td><p>Session data key(s)</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_tempdata">
        <div class="location">in <a href="CI_Session.html#method_tempdata">
CI_Session</a> at line 928</div>
        <code>                    mixed
    <strong>tempdata</strong>(string $key = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Temp data (fetch)</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$key</td>
                <td><p>Session data key</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td><p>Session data value or NULL if not found</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_tempdata">
        <div class="location">in <a href="CI_Session.html#method_set_tempdata">
CI_Session</a> at line 962</div>
        <code>                    void
    <strong>set_tempdata</strong>(mixed $data, mixed $value = NULL, int $ttl = 300)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set tempdata</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$data</td>
                <td><p>Session data key or an associative array of items</p></td>
            </tr>
                    <tr>
                <td>mixed</td>
                <td>$value</td>
                <td><p>Value to store</p></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$ttl</td>
                <td><p>Time-to-live in seconds</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_unset_tempdata">
        <div class="location">in <a href="CI_Session.html#method_unset_tempdata">
CI_Session</a> at line 978</div>
        <code>                    void
    <strong>unset_tempdata</strong>($key)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Unset tempdata</p></p>                <p><p>Legacy CI_Session compatibility method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$key</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

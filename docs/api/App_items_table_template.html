<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>App_items_table_template | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:App_items_table_template" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>App_items_table_template    
            </h1>
    </div>

    
    <p>    abstract     class
    <strong>App_items_table_template</strong>
</p>

        
    
        

            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_ci">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$ci</td>
                <td class="last"><p>Codeigniter instance</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_items">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$items</td>
                <td class="last"><p>The transaction items</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_transaction">
                                        protected                                        object
                                                                                
                                    </td>
                <td>$transaction</td>
                <td class="last"><p>Invoice, estimate</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_tax_per_item">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$tax_per_item</td>
                <td class="last"><p>Whether tax per item should be shown</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_custom_fields_for_table">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$custom_fields_for_table</td>
                <td class="last"><p>Custom fields for the items</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_taxes">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$taxes</td>
                <td class="last"><p>All taxes used for the preview
This is used to display the taxes on the bottom of the invoice</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_for">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$for</td>
                <td class="last"><p>Where the items will be shown? pdf or html</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_type">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$type</td>
                <td class="last"><p>Preview type, e.q. invoice, estimate etc.</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_admin_preview">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$admin_preview</td>
                <td class="last"><p>Is the preview for admin area?</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_headings">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$headings</td>
                <td class="last"><p>Headings language texts</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_items">items</a>()
        
                                            <p><p>Builds the actual table items rows preview</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_html_headings">html_headings</a>()
        
                                            <p><p>Html headings preview</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_pdf_headings">pdf_headings</a>()
        
                                            <p><p>PDF headings preview</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_taxes">taxes</a>()
        
                                            <p><p>All taxes used for the preview
This is used to display the taxes on the bottom of the invoice</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_order_taxes">order_taxes</a>(array $taxes)
        
                                            <p><p>Order taxes by taxrate
Lowest tax rate will be on top (if multiple)</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_get_item_taxes">get_item_taxes</a>(array $item)
        
                                            <p><p>Get specific item applied taxes</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_taxes_html">taxes_html</a>(array $item, $width)
        
                                            <p><p>Helper method for taxes HTML, because is commonly used for all preview types</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_items">set_items</a>(array $items)
        
                                            <p><p>Set the initial items to work with
This function also sets the taxes which is required</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_tr_attributes">tr_attributes</a>(array $item)
        
                                            <p><p>Table items row attributes</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_td_attributes">td_attributes</a>()
        
                                            <p><p>Table items table data attributes</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method_get_pdf_font_size">get_pdf_font_size</a>()
        
                                            <p><p>Get PDF font size if the items are for PDF</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_all_taxes">set_all_taxes</a>(array $taxes, array $item)
        
                                            <p><p>Sets all taxes</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    array
                </div>
                <div class="col-md-8">
                    <a href="#method_get_custom_fields_for_table">get_custom_fields_for_table</a>()
        
                                            <p><p>Get items custom fields applied on the transaction</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_transaction">set_transaction</a>(object $rel)
        
                                            <p><p>Set transaction for the items</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_table">table</a>()
        
                                            <p><p>Helper function to build the whole table</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_pdf_table_open">pdf_table_open</a>()
        
                                            <p><p>PDF table opening tag</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_html_table_open">html_table_open</a>()
        
                                            <p><p>HTML table opening tag</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_add_table_class">add_table_class</a>(string $class)
        
                                            <p><p>Add additional table class
Only for HTML table</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_table_close">table_close</a>()
        
                                            <p><p>Closing table tag</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_number_heading">number_heading</a>()
        
                                            <p><p>Get number heading</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_item_heading">item_heading</a>()
        
                                            <p><p>Get item heading</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_qty_heading">qty_heading</a>()
        
                                            <p><p>Get quantity heading</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_rate_heading">rate_heading</a>()
        
                                            <p><p>Get rate heading</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_tax_heading">tax_heading</a>()
        
                                            <p><p>Get tax heading</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_amount_heading">amount_heading</a>()
        
                                            <p><p>Get amount heading</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_set_headings">set_headings</a>(string $alias = &#039;&#039;)
        
                                            <p><p>Set headings for the items
Can be used outside this class for example when alias is needed to take the language texts for</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_exclude_currency">exclude_currency</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    
                </div>
                <div class="col-md-8">
                    <a href="#method_show_tax_per_item">show_tax_per_item</a>()
        
                                            <p class="no-description">No description</p>
                                    </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method___call">__call</a>(string $name, array $arguments)
        
                                            <p><p>Custom __call magic method</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 91</div>
        <code>                    
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_items">
        <div class="location">at line 103</div>
        <code>    abstract                string
    <strong>items</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Builds the actual table items rows preview</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_html_headings">
        <div class="location">at line 109</div>
        <code>    abstract                string
    <strong>html_headings</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Html headings preview</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_pdf_headings">
        <div class="location">at line 115</div>
        <code>    abstract                string
    <strong>pdf_headings</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>PDF headings preview</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_taxes">
        <div class="location">at line 122</div>
        <code>                    
    <strong>taxes</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>All taxes used for the preview
This is used to display the taxes on the bottom of the invoice</p></p>                        
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_order_taxes">
        <div class="location">at line 146</div>
        <code>            protected        array
    <strong>order_taxes</strong>(array $taxes)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Order taxes by taxrate
Lowest tax rate will be on top (if multiple)</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$taxes</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_item_taxes">
        <div class="location">at line 160</div>
        <code>            protected        mixed
    <strong>get_item_taxes</strong>(array $item)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get specific item applied taxes</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$item</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_taxes_html">
        <div class="location">at line 184</div>
        <code>            protected        string
    <strong>taxes_html</strong>(array $item, $width)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Helper method for taxes HTML, because is commonly used for all preview types</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$item</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$width</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_items">
        <div class="location">at line 216</div>
        <code>            protected        
    <strong>set_items</strong>(array $items)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set the initial items to work with
This function also sets the taxes which is required</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$items</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_tr_attributes">
        <div class="location">at line 231</div>
        <code>            protected        string
    <strong>tr_attributes</strong>(array $item)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Table items row attributes</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$item</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_td_attributes">
        <div class="location">at line 249</div>
        <code>            protected        string
    <strong>td_attributes</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Table items table data attributes</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_pdf_font_size">
        <div class="location">at line 265</div>
        <code>            protected        mixed
    <strong>get_pdf_font_size</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get PDF font size if the items are for PDF</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_all_taxes">
        <div class="location">at line 284</div>
        <code>            protected        
    <strong>set_all_taxes</strong>(array $taxes, array $item)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sets all taxes</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$taxes</td>
                <td><p>$item taxes</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$item</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_get_custom_fields_for_table">
        <div class="location">at line 309</div>
        <code>            protected        array
    <strong>get_custom_fields_for_table</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get items custom fields applied on the transaction</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>array</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_transaction">
        <div class="location">at line 318</div>
        <code>            protected        
    <strong>set_transaction</strong>(object $rel)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set transaction for the items</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>object</td>
                <td>$rel</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_table">
        <div class="location">at line 330</div>
        <code>                    string
    <strong>table</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Helper function to build the whole table</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_pdf_table_open">
        <div class="location">at line 348</div>
        <code>                    string
    <strong>pdf_table_open</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>PDF table opening tag</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_html_table_open">
        <div class="location">at line 357</div>
        <code>                    string
    <strong>html_table_open</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>HTML table opening tag</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_table_class">
        <div class="location">at line 370</div>
        <code>                    
    <strong>add_table_class</strong>(string $class)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add additional table class
Only for HTML table</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$class</td>
                <td></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_table_close">
        <div class="location">at line 381</div>
        <code>                    string
    <strong>table_close</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Closing table tag</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_number_heading">
        <div class="location">at line 390</div>
        <code>                    string
    <strong>number_heading</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get number heading</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_item_heading">
        <div class="location">at line 399</div>
        <code>                    string
    <strong>item_heading</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get item heading</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_qty_heading">
        <div class="location">at line 408</div>
        <code>                    string
    <strong>qty_heading</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get quantity heading</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_rate_heading">
        <div class="location">at line 417</div>
        <code>                    string
    <strong>rate_heading</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get rate heading</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_tax_heading">
        <div class="location">at line 426</div>
        <code>                    string
    <strong>tax_heading</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get tax heading</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_amount_heading">
        <div class="location">at line 435</div>
        <code>                    string
    <strong>amount_heading</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Get amount heading</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_headings">
        <div class="location">at line 445</div>
        <code>                    
    <strong>set_headings</strong>(string $alias = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set headings for the items
Can be used outside this class for example when alias is needed to take the language texts for</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$alias</td>
                <td><p>e.q. estimates and proposals are using the same language text</p></td>
            </tr>
            </table>

            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_exclude_currency">
        <div class="location">at line 470</div>
        <code>            protected        
    <strong>exclude_currency</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_show_tax_per_item">
        <div class="location">at line 479</div>
        <code>            protected        
    <strong>show_tax_per_item</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p class="no-description">No description</p>
                    
        </div>
        <div class="tags">
            
            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method___call">
        <div class="location">at line 493</div>
        <code>                    mixed
    <strong>__call</strong>(string $name, array $arguments)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Custom __call magic method</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$name</td>
                <td><p>the called non existing method</p></td>
            </tr>
                    <tr>
                <td>array</td>
                <td>$arguments</td>
                <td><p>the arguments passed</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

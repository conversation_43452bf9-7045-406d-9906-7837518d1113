<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Xmlrpcs | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Xmlrpcs" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Xmlrpcs    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Xmlrpcs</strong>        extends <a href="CI_Xmlrpc.html">CI_Xmlrpc</a>
</p>

        
    
        

            <div class="description">
            <p><p>XML-RPC server class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property_debug">
                                                                                bool
                                                                                
                                    </td>
                <td>$debug</td>
                <td class="last"><p>Debug flag</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_debug">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcI4">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpcI4</td>
                <td class="last"><p>I4 data type</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcI4">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcInt">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpcInt</td>
                <td class="last"><p>Integer data type</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcInt">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcBoolean">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpcBoolean</td>
                <td class="last"><p>Boolean data type</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcBoolean">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcDouble">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpcDouble</td>
                <td class="last"><p>Double data type</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcDouble">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcString">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpcString</td>
                <td class="last"><p>String data type</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcString">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcDateTime">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpcDateTime</td>
                <td class="last"><p>DateTime format</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcDateTime">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcBase64">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpcBase64</td>
                <td class="last"><p>Base64 data type</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcBase64">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcArray">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpcArray</td>
                <td class="last"><p>Array data type</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcArray">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcStruct">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpcStruct</td>
                <td class="last"><p>Struct data type</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcStruct">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcTypes">
                                                                                array
                                                                                
                                    </td>
                <td>$xmlrpcTypes</td>
                <td class="last"><p>Data types list</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcTypes">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_valid_parents">
                                                                                array
                                                                                
                                    </td>
                <td>$valid_parents</td>
                <td class="last"><p>Valid parents list</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_valid_parents">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcerr">
                                                                                array
                                                                                
                                    </td>
                <td>$xmlrpcerr</td>
                <td class="last"><p>Response error numbers list</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcerr">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcstr">
                                                                                string[]
                                                                                
                                    </td>
                <td>$xmlrpcstr</td>
                <td class="last"><p>Response error messages list</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcstr">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpc_defencoding">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpc_defencoding</td>
                <td class="last"><p>Encoding charset</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpc_defencoding">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcName">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpcName</td>
                <td class="last"><p>XML-RPC client name</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcName">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcVersion">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpcVersion</td>
                <td class="last"><p>XML-RPC version</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcVersion">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcerruser">
                                                                                int
                                                                                
                                    </td>
                <td>$xmlrpcerruser</td>
                <td class="last"><p>Start of user errors</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcerruser">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpcerrxml">
                                                                                int
                                                                                
                                    </td>
                <td>$xmlrpcerrxml</td>
                <td class="last"><p>Start of XML parse errors</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpcerrxml">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xmlrpc_backslash">
                                                                                string
                                                                                
                                    </td>
                <td>$xmlrpc_backslash</td>
                <td class="last"><p>Backslash replacement value</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xmlrpc_backslash">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_client">
                                                                                object
                                                                                
                                    </td>
                <td>$client</td>
                <td class="last"><p>XML-RPC Client object</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_client">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_method">
                                                                                string
                                                                                
                                    </td>
                <td>$method</td>
                <td class="last"><p>XML-RPC Method name</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_method">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_data">
                                                                                array
                                                                                
                                    </td>
                <td>$data</td>
                <td class="last"><p>XML-RPC Data</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_data">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_message">
                                                                                string
                                                                                
                                    </td>
                <td>$message</td>
                <td class="last"><p>XML-RPC Message</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_message">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_error">
                                                                                string
                                                                                
                                    </td>
                <td>$error</td>
                <td class="last"><p>Request error message</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_error">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_result">
                                                                                object
                                                                                
                                    </td>
                <td>$result</td>
                <td class="last"><p>XML-RPC result object</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_result">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_response">
                                                                                array
                                                                                
                                    </td>
                <td>$response</td>
                <td class="last"><p>XML-RPC Response</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_response">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_xss_clean">
                                                                                bool
                                                                                
                                    </td>
                <td>$xss_clean</td>
                <td class="last"><p>XSS Filter flag</p></td>
                <td><small>from&nbsp;<a href="CI_Xmlrpc.html#property_xss_clean">
CI_Xmlrpc</a></small></td>
            </tr>
                    <tr>
                <td class="type" id="property_methods">
                                                                                array
                                                                                
                                    </td>
                <td>$methods</td>
                <td class="last"><p>Array of methods mapped to function names and signatures</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_debug_msg">
                                                                                string
                                                                                
                                    </td>
                <td>$debug_msg</td>
                <td class="last"><p>Debug Message</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_system_methods">
                                                                                array
                                                                                
                                    </td>
                <td>$system_methods</td>
                <td class="last"><p>XML RPC Server methods</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_object">
                                                                                object
                                                                                
                                    </td>
                <td>$object</td>
                <td class="last"><p>Configuration object</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>(array $config = array())
        
                                            <p><p>Initialize XMLRPC class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_initialize">initialize</a>(array $config = array())
        
                                            <p><p>Initialize Prefs and Serve</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_server">server</a>(string $url, int $port = 80, string $proxy = FALSE, int $proxy_port = 8080)
        
                                            <p><p>Parse server URL</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Xmlrpc.html#method_server">
CI_Xmlrpc</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_timeout">timeout</a>(int $seconds = 5)
        
                                            <p><p>Set Timeout</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Xmlrpc.html#method_timeout">
CI_Xmlrpc</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_method">method</a>(string $function)
        
                                            <p><p>Set Methods</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Xmlrpc.html#method_method">
CI_Xmlrpc</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_request">request</a>(array $incoming)
        
                                            <p><p>Take Array of Data and Create Objects</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Xmlrpc.html#method_request">
CI_Xmlrpc</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_debug">set_debug</a>(bool $flag = TRUE)
        
                                            <p><p>Set Debug</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Xmlrpc.html#method_set_debug">
CI_Xmlrpc</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_values_parsing">values_parsing</a>(mixed $value)
        
                                            <p><p>Values Parsing</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Xmlrpc.html#method_values_parsing">
CI_Xmlrpc</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_send_request">send_request</a>()
        
                                            <p><p>Sends XML-RPC Request</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Xmlrpc.html#method_send_request">
CI_Xmlrpc</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_display_error">display_error</a>()
        
                                            <p><p>Returns Error</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Xmlrpc.html#method_display_error">
CI_Xmlrpc</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method_display_response">display_response</a>()
        
                                            <p><p>Returns Remote Server Response</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Xmlrpc.html#method_display_response">
CI_Xmlrpc</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_send_error_message">send_error_message</a>(int $number, string $message)
        
                                            <p><p>Sends an Error Message for Server Request</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Xmlrpc.html#method_send_error_message">
CI_Xmlrpc</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_send_response">send_response</a>(array $response)
        
                                            <p><p>Send Response for Server Request</p></p>                </div>
                <div class="col-md-2"><small>from&nbsp;<a href="CI_Xmlrpc.html#method_send_response">
CI_Xmlrpc</a></small></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_set_system_methods">set_system_methods</a>()
        
                                            <p><p>Setting of System Methods</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_serve">serve</a>()
        
                                            <p><p>Main Server Function</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method_add_to_map">add_to_map</a>($methodname, $function, $sig, $doc)
        
                                            <p><p>Add Method to Class</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_parseRequest">parseRequest</a>($data = &#039;&#039;)
        
                                            <p><p>Parse Server Request</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    mixed
                </div>
                <div class="col-md-8">
                    <a href="#method__execute">_execute</a>($m)
        
                                            <p><p>Executes the Method</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_listMethods">listMethods</a>($m)
        
                                            <p><p>Server Function: List Methods</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_methodSignature">methodSignature</a>($m)
        
                                            <p><p>Server Function: Return Signature for Method</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_methodHelp">methodHelp</a>($m)
        
                                            <p><p>Server Function: Doc String for Method</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_multicall">multicall</a>($m)
        
                                            <p><p>Server Function: Multi-call</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_multicall_error">multicall_error</a>($err)
        
                                            <p><p>Multi-call Function: Error Handling</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    object
                </div>
                <div class="col-md-8">
                    <a href="#method_do_multicall">do_multicall</a>($call)
        
                                            <p><p>Multi-call Function: Processes method</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 97</div>
        <code>                    void
    <strong>__construct</strong>(array $config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize XMLRPC class</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_initialize">
        <div class="location">at line 118</div>
        <code>                    void
    <strong>initialize</strong>(array $config = array())
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Initialize Prefs and Serve</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$config</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_server">
        <div class="location">in <a href="CI_Xmlrpc.html#method_server">
CI_Xmlrpc</a> at line 353</div>
        <code>                    void
    <strong>server</strong>(string $url, int $port = 80, string $proxy = FALSE, int $proxy_port = 8080)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Parse server URL</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$url</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$port</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$proxy</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$proxy_port</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_timeout">
        <div class="location">in <a href="CI_Xmlrpc.html#method_timeout">
CI_Xmlrpc</a> at line 385</div>
        <code>                    void
    <strong>timeout</strong>(int $seconds = 5)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Timeout</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$seconds</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_method">
        <div class="location">in <a href="CI_Xmlrpc.html#method_method">
CI_Xmlrpc</a> at line 401</div>
        <code>                    void
    <strong>method</strong>(string $function)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Methods</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$function</td>
                <td><p>Method name</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_request">
        <div class="location">in <a href="CI_Xmlrpc.html#method_request">
CI_Xmlrpc</a> at line 414</div>
        <code>                    void
    <strong>request</strong>(array $incoming)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Take Array of Data and Create Objects</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$incoming</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_debug">
        <div class="location">in <a href="CI_Xmlrpc.html#method_set_debug">
CI_Xmlrpc</a> at line 438</div>
        <code>                    void
    <strong>set_debug</strong>(bool $flag = TRUE)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Set Debug</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>bool</td>
                <td>$flag</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_values_parsing">
        <div class="location">in <a href="CI_Xmlrpc.html#method_values_parsing">
CI_Xmlrpc</a> at line 451</div>
        <code>                    object
    <strong>values_parsing</strong>(mixed $value)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Values Parsing</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>mixed</td>
                <td>$value</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send_request">
        <div class="location">in <a href="CI_Xmlrpc.html#method_send_request">
CI_Xmlrpc</a> at line 487</div>
        <code>                    bool
    <strong>send_request</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sends XML-RPC Request</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_display_error">
        <div class="location">in <a href="CI_Xmlrpc.html#method_display_error">
CI_Xmlrpc</a> at line 509</div>
        <code>                    string
    <strong>display_error</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns Error</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_display_response">
        <div class="location">in <a href="CI_Xmlrpc.html#method_display_response">
CI_Xmlrpc</a> at line 521</div>
        <code>                    string
    <strong>display_response</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Returns Remote Server Response</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send_error_message">
        <div class="location">in <a href="CI_Xmlrpc.html#method_send_error_message">
CI_Xmlrpc</a> at line 535</div>
        <code>                    object
    <strong>send_error_message</strong>(int $number, string $message)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Sends an Error Message for Server Request</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>int</td>
                <td>$number</td>
                <td></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$message</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_send_response">
        <div class="location">in <a href="CI_Xmlrpc.html#method_send_response">
CI_Xmlrpc</a> at line 548</div>
        <code>                    object
    <strong>send_response</strong>(array $response)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Send Response for Server Request</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>array</td>
                <td>$response</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_set_system_methods">
        <div class="location">at line 148</div>
        <code>                    void
    <strong>set_system_methods</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Setting of System Methods</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_serve">
        <div class="location">at line 177</div>
        <code>                    void
    <strong>serve</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Main Server Function</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_add_to_map">
        <div class="location">at line 198</div>
        <code>                    void
    <strong>add_to_map</strong>($methodname, $function, $sig, $doc)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Add Method to Class</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$methodname</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$function</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$sig</td>
                <td></td>
            </tr>
                    <tr>
                <td></td>
                <td>$doc</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_parseRequest">
        <div class="location">at line 215</div>
        <code>                    object
    <strong>parseRequest</strong>($data = &#039;&#039;)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Parse Server Request</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$data</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td><p>xmlrpc response</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__execute">
        <div class="location">at line 316</div>
        <code>            protected        mixed
    <strong>_execute</strong>($m)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Executes the Method</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$m</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>mixed</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_listMethods">
        <div class="location">at line 420</div>
        <code>                    object
    <strong>listMethods</strong>($m)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Server Function: List Methods</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$m</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_methodSignature">
        <div class="location">at line 447</div>
        <code>                    object
    <strong>methodSignature</strong>($m)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Server Function: Return Signature for Method</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$m</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_methodHelp">
        <div class="location">at line 487</div>
        <code>                    object
    <strong>methodHelp</strong>($m)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Server Function: Doc String for Method</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$m</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_multicall">
        <div class="location">at line 510</div>
        <code>                    object
    <strong>multicall</strong>($m)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Server Function: Multi-call</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$m</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_multicall_error">
        <div class="location">at line 551</div>
        <code>                    object
    <strong>multicall_error</strong>($err)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Multi-call Function: Error Handling</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$err</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_do_multicall">
        <div class="location">at line 570</div>
        <code>                    object
    <strong>do_multicall</strong>($call)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Multi-call Function: Processes method</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td></td>
                <td>$call</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>object</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="robots" content="index, follow, all" />
    <title>CI_Log | API</title>

            <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="css/bootstrap-theme.min.css">
        <link rel="stylesheet" type="text/css" href="css/doctum.css">
        <link rel="stylesheet" type="text/css" href="fonts/doctum-font.css">
        <script src="js/jquery-3.5.1.slim.min.js"></script>
        <script async defer src="doctum.js"></script>
        <script async defer src="js/bootstrap.min.js"></script>
        <script async defer src="js/autocomplete.min.js"></script>
        <meta name="MobileOptimized" content="width">
        <meta name="HandheldFriendly" content="true">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

    </head>

    <body id="class" data-name="class:CI_Log" data-root-path="" data-search-index-url="doctum-search.json">
            <div id="content">
        <div id="left-column">
                <div id="control-panel">
                <div class="search-bar hidden" id="search-progress-bar-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar" id="search-progress-bar"
                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
            </div>
        </div>
        <form id="search-form" action="search.html">
            <span class="icon icon-search"></span>
            <input name="search"
                   id="doctum-search-auto-complete"
                   class="typeahead form-control"
                   type="search"
                   placeholder="Search"
                   spellcheck="false"
                   autocorrect="off"
                   autocomplete="off"
                   autocapitalize="off">
            <div class="auto-complete-results" id="auto-complete-results"></div>
        </form>
    </div>

                <div id="api-tree"></div>

        </div>
        <div id="right-column">
                <nav id="site-nav" class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-elements">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index.html">API</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar-elements">
                <ul class="nav navbar-nav">
                    <li><a href="classes.html">Classes</a></li>
                    <li><a href="namespaces.html">Namespaces</a></li>
                    <li><a href="interfaces.html">Interfaces</a></li>
                    <li><a href="traits.html">Traits</a></li>
                    <li><a href="doc-index.html">Index</a></li>
                    <li><a href="search.html">Search</a></li>
                </ul>
            </div>
        </div>
    </nav>

                            <div id="page-content">
    <div class="page-header">
        <h1>CI_Log    
            </h1>
    </div>

    
    <p>        class
    <strong>CI_Log</strong>
</p>

        
    
        

            <div class="description">
            <p><p>Logging Class</p></p>                    </div>
            
        
    
            <h2>Properties</h2>

            <table class="table table-condensed">
                    <tr>
                <td class="type" id="property__log_path">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_log_path</td>
                <td class="last"><p>Path to save log files</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__file_permissions">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$_file_permissions</td>
                <td class="last"><p>File permissions</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__threshold">
                                        protected                                        int
                                                                                
                                    </td>
                <td>$_threshold</td>
                <td class="last"><p>Level of logging</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__threshold_array">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_threshold_array</td>
                <td class="last"><p>Array of threshold levels to log</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__date_fmt">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_date_fmt</td>
                <td class="last"><p>Format of timestamp for log files</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__file_ext">
                                        protected                                        string
                                                                                
                                    </td>
                <td>$_file_ext</td>
                <td class="last"><p>Filename extension</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__enabled">
                                        protected                                        bool
                                                                                
                                    </td>
                <td>$_enabled</td>
                <td class="last"><p>Whether or not the logger can write to the log files</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property__levels">
                                        protected                                        array
                                                                                
                                    </td>
                <td>$_levels</td>
                <td class="last"><p>Predefined logging levels</p></td>
                <td></td>
            </tr>
                    <tr>
                <td class="type" id="property_func_overload">
                    static                    protected                                        bool
                                                                                
                                    </td>
                <td>$func_overload</td>
                <td class="last"><p>mbstring.func_overload flag</p></td>
                <td></td>
            </tr>
            </table>

    
            <h2>Methods</h2>

            <div class="container-fluid underlined">
                    <div class="row">
                <div class="col-md-2 type">
                    void
                </div>
                <div class="col-md-8">
                    <a href="#method___construct">__construct</a>()
        
                                            <p><p>Class constructor</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    bool
                </div>
                <div class="col-md-8">
                    <a href="#method_write_log">write_log</a>(string $level, string $msg)
        
                                            <p><p>Write Log File</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    string
                </div>
                <div class="col-md-8">
                    <a href="#method__format_line">_format_line</a>(string $level, string $date, string $message)
        
                                            <p><p>Format the log line.</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;int
                </div>
                <div class="col-md-8">
                    <a href="#method_strlen">strlen</a>(string $str)
        
                                            <p><p>Byte-safe strlen()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
                    <div class="row">
                <div class="col-md-2 type">
                    static&nbsp;string
                </div>
                <div class="col-md-8">
                    <a href="#method_substr">substr</a>(string $str, int $start, int $length = NULL)
        
                                            <p><p>Byte-safe substr()</p></p>                </div>
                <div class="col-md-2"></div>
            </div>
            </div>


        <h2>Details</h2>

            <div id="method-details">
                    <div class="method-item">
                    <h3 id="method___construct">
        <div class="location">at line 121</div>
        <code>                    void
    <strong>__construct</strong>()
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Class constructor</p></p>                        
        </div>
        <div class="tags">
            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>void</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_write_log">
        <div class="location">at line 170</div>
        <code>                    bool
    <strong>write_log</strong>(string $level, string $msg)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Write Log File</p></p>                <p><p>Generally this function will be called using the global log_message() function</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$level</td>
                <td><p>The error level: 'error', 'debug' or 'info'</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$msg</td>
                <td><p>The error message</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>bool</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method__format_line">
        <div class="location">at line 252</div>
        <code>            protected        string
    <strong>_format_line</strong>(string $level, string $date, string $message)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Format the log line.</p></p>                <p><p>This is for extensibility of log formatting
If you want to change the log format, extend the CI_Log class and override this method</p></p>        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$level</td>
                <td><p>The error level</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$date</td>
                <td><p>Formatted date string</p></td>
            </tr>
                    <tr>
                <td>string</td>
                <td>$message</td>
                <td><p>The log message</p></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td><p>Formatted log line with a new line character at the end</p></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_strlen">
        <div class="location">at line 265</div>
        <code>        static    protected        int
    <strong>strlen</strong>(string $str)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe strlen()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>int</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
                    <div class="method-item">
                    <h3 id="method_substr">
        <div class="location">at line 282</div>
        <code>        static    protected        string
    <strong>substr</strong>(string $str, int $start, int $length = NULL)
        </code>
    </h3>
    <div class="details">    
    
            

        <div class="method-description">
                            <p><p>Byte-safe substr()</p></p>                        
        </div>
        <div class="tags">
                            <h4>Parameters</h4>

                    <table class="table table-condensed">
                    <tr>
                <td>string</td>
                <td>$str</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$start</td>
                <td></td>
            </tr>
                    <tr>
                <td>int</td>
                <td>$length</td>
                <td></td>
            </tr>
            </table>

            
                            <h4>Return Value</h4>

                    <table class="table table-condensed">
        <tr>
            <td>string</td>
            <td></td>
        </tr>
    </table>

            
            
            
                    </div>
    </div>

            </div>
            </div>

    
</div><div id="footer">
        Generated by <a href="https://github.com/code-lts/doctum">Doctum, a API Documentation generator and fork of Sami</a>.</div></div>
    </div>
    </body>

</html>

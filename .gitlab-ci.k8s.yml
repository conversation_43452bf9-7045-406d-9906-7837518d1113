variables:
  TEST_DISABLED: 'true'
  CODE_INTELLIGENCE_DISABLED: 'true'
  BROWSER_PERFORMANCE_DISABLED: 'true'

include:
  - template: Auto-DevOps.gitlab-ci.yml  # https://gitlab.com/gitlab-org/gitlab/blob/master/lib/gitlab/ci/templates/Auto-DevOps.gitlab-ci.yml

.auto-deploy:
  variables:
    # K8S_SECRET_APP_CONFIG: "<?php "
    # K8S_SECRET_ENV_PHP: "<?php define('ENVIRONMENT', 'testing');"
    # K8S_SECRET_PROXY_IPS: "**********/16,************"
    HELM_UPGRADE_EXTRA_ARGS: --values .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml
  before_script:
    - |
      set -ex
      sed "s/secretName: env\.secret/secretName: ${CI_ENVIRONMENT_SLUG}-secret/g ${SED_SCRIPT}" .gitlab/dotenv-secret-volume.yaml > .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml

canary:
  before_script:
    - |
      set -ex
      sed "s/secretName: env\.secret/secretName: ${CI_ENVIRONMENT_SLUG}-canary-secret/g ${SED_SCRIPT}" .gitlab/dotenv-secret-volume.yaml > .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml

build:
  # Use the Docker executor with Docker socket binding
  tags: ["102-stg-docker"]
  services: []
  variables:
    AUTO_DEVOPS_BUILD_IMAGE_FORWARDED_CI_VARIABLES: CI_COMMIT_SHA
    AUTO_DEVOPS_BUILD_IMAGE_CNB_BUILDER: heroku/buildpacks:20
    BUILDPACK_URL: heroku/php,heroku/nodejs
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "develop"'

code_quality:
  tags: ["102-stg-docker"]

review:
  tags: ["102-stg-docker"]
  variables:
    WEB_CONCURRENCY: 7
    APP_BASE_URL: $CI_ENVIRONMENT_URL
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'

build production:
  stage: build
  variables:
    GIT_STRATEGY: none
  environment:
    name: production
    url: https://crm.topdev.asia/admin
  tags:
    - 20-inhouse
  script:
    - source "$HOME/.nvm/nvm.sh"
    - nvm use 15
    - cd /var/www/html/crm
    - git pull origin main
    - npm install
    - npm run build-assets
  rules:
    - if: "$CI_DEPLOY_FREEZE"
      when: never
    - if: "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $STAGING_ENABLED"
      when: manual
    - if: "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CANARY_ENABLED"
      when: manual
    - if: "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH"
      when: manual

build tesing:
  stage: build
  tags:
    - 102-stg
  only:
    - develop
  variables:
    GIT_STRATEGY: none
  script:
    - cd /var/www/crm
    - git pull origin develop
    - /usr/bin/php7.4 /usr/bin/composer install
    - source "$HOME/.nvm/nvm.sh"
    - nvm use 15
    - npm install
    - npm run build-assets
  environment:
    name: testing
    url: https://crmdev.topdev.asia


!function(e,t){void 0===e&&void 0!==window&&(e=window),"function"==typeof define&&define.amd?define(["jquery"],function(e){return t(e)}):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(e.jQuery)}(this,function(e){!function(L){"use strict";var p=["sanitize","whiteList","sanitizeFn"],l=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],r=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,a=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i;function g(e,t){var i=e.nodeName.toLowerCase();if(-1!==L.inArray(i,t))return-1===L.inArray(i,l)||Boolean(e.nodeValue.match(r)||e.nodeValue.match(a));for(var s=L(t).filter(function(e,t){return t instanceof RegExp}),n=0,o=s.length;n<o;n++)if(i.match(s[n]))return!0;return!1}function G(e,t,i){if(i&&"function"==typeof i)return i(e);for(var s=Object.keys(t),n=0,o=e.length;n<o;n++)for(var l=e[n].querySelectorAll("*"),r=0,a=l.length;r<a;r++){var c=l[r],p=c.nodeName.toLowerCase();if(-1!==s.indexOf(p))for(var h=[].slice.call(c.attributes),d=[].concat(t["*"]||[],t[p]||[]),u=0,f=h.length;u<f;u++){var m=h[u];g(m,d)||c.removeAttribute(m.nodeName)}else c.parentNode.removeChild(c)}}"classList"in document.createElement("_")||function(e){if("Element"in e){var t="classList",i="prototype",s=e.Element[i],n=Object,o=function(){var i=L(this);return{add:function(e){return e=Array.prototype.slice.call(arguments).join(" "),i.addClass(e)},remove:function(e){return e=Array.prototype.slice.call(arguments).join(" "),i.removeClass(e)},toggle:function(e,t){return i.toggleClass(e,t)},contains:function(e){return i.hasClass(e)}}};if(n.defineProperty){var l={get:o,enumerable:!0,configurable:!0};try{n.defineProperty(s,t,l)}catch(e){void 0!==e.number&&-2146823252!==e.number||(l.enumerable=!1,n.defineProperty(s,t,l))}}else n[i].__defineGetter__&&s.__defineGetter__(t,o)}}(window);var e,c,t=document.createElement("_");if(t.classList.add("c1","c2"),!t.classList.contains("c2")){var i=DOMTokenList.prototype.add,s=DOMTokenList.prototype.remove;DOMTokenList.prototype.add=function(){Array.prototype.forEach.call(arguments,i.bind(this))},DOMTokenList.prototype.remove=function(){Array.prototype.forEach.call(arguments,s.bind(this))}}if(t.classList.toggle("c3",!1),t.classList.contains("c3")){var n=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(e,t){return 1 in arguments&&!this.contains(e)==!t?t:n.call(this,e)}}function o(e){if(null==this)throw new TypeError;var t=String(this);if(e&&"[object RegExp]"==c.call(e))throw new TypeError;var i=t.length,s=String(e),n=s.length,o=1<arguments.length?arguments[1]:void 0,l=o?Number(o):0;l!=l&&(l=0);var r=Math.min(Math.max(l,0),i);if(i<n+r)return!1;for(var a=-1;++a<n;)if(t.charCodeAt(r+a)!=s.charCodeAt(a))return!1;return!0}function C(e,t){var i,s=e.selectedOptions,n=[];if(t){for(var o=0,l=s.length;o<l;o++)(i=s[o]).disabled||"OPTGROUP"===i.parentNode.tagName&&i.parentNode.disabled||n.push(i);return n}return s}function T(e,t){for(var i,s=[],n=t||e.selectedOptions,o=0,l=n.length;o<l;o++)(i=n[o]).disabled||"OPTGROUP"===i.parentNode.tagName&&i.parentNode.disabled||s.push(i.value||i.text);return e.multiple?s:s.length?s[0]:null}t=null,String.prototype.startsWith||(e=function(){try{var e={},t=Object.defineProperty,i=t(e,e,e)&&t}catch(e){}return i}(),c={}.toString,e?e(String.prototype,"startsWith",{value:o,configurable:!0,writable:!0}):String.prototype.startsWith=o),Object.keys||(Object.keys=function(e,t,i){for(t in i=[],e)i.hasOwnProperty.call(e,t)&&i.push(t);return i}),HTMLSelectElement&&!HTMLSelectElement.prototype.hasOwnProperty("selectedOptions")&&Object.defineProperty(HTMLSelectElement.prototype,"selectedOptions",{get:function(){return this.querySelectorAll(":checked")}});var h={useDefault:!1,_set:L.valHooks.select.set};L.valHooks.select.set=function(e,t){return t&&!h.useDefault&&L(e).data("selected",!0),h._set.apply(this,arguments)};var A=null,d=function(){try{return new Event("change"),!0}catch(e){return!1}}();function y(e,t,i,s){for(var n=["display","subtext","tokens"],o=!1,l=0;l<n.length;l++){var r=n[l],a=e[r];if(a&&(a=a.toString(),"display"===r&&(a=a.replace(/<[^>]+>/g,"")),s&&(a=b(a)),a=a.toUpperCase(),o="contains"===i?0<=a.indexOf(t):a.startsWith(t)))break}return o}function D(e){return parseInt(e,10)||0}L.fn.triggerNative=function(e){var t,i=this[0];i.dispatchEvent?(d?t=new Event(e,{bubbles:!0}):(t=document.createEvent("Event")).initEvent(e,!0,!1),i.dispatchEvent(t)):i.fireEvent?((t=document.createEventObject()).eventType=e,i.fireEvent("on"+e,t)):this.trigger(e)};var u={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"},f=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,m=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\u1ab0-\\u1aff\\u1dc0-\\u1dff]","g");function v(e){return u[e]}function b(e){return(e=e.toString())&&e.replace(f,v).replace(m,"")}var w,x,I,k,O=(w={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},x="(?:"+Object.keys(w).join("|")+")",I=RegExp(x),k=RegExp(x,"g"),function(e){return e=null==e?"":""+e,I.test(e)?e.replace(k,$):e});function $(e){return w[e]}var S={32:" ",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",59:";",65:"A",66:"B",67:"C",68:"D",69:"E",70:"F",71:"G",72:"H",73:"I",74:"J",75:"K",76:"L",77:"M",78:"N",79:"O",80:"P",81:"Q",82:"R",83:"S",84:"T",85:"U",86:"V",87:"W",88:"X",89:"Y",90:"Z",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9"},N={success:!1,major:"3"};try{N.full=(L.fn.dropdown.Constructor.VERSION||"").split(" ")[0].split("."),N.major=N.full[0],N.success=!0}catch(e){}var E=0,P=".bs.select",U={DISABLED:"disabled",DIVIDER:"divider",SHOW:"open",DROPUP:"dropup",MENU:"dropdown-menu",MENURIGHT:"dropdown-menu-right",MENULEFT:"dropdown-menu-left",BUTTONCLASS:"btn-default",POPOVERHEADER:"popover-title",ICONBASE:"glyphicon",TICKICON:"glyphicon-ok"},z={MENU:"."+U.MENU},R={span:document.createElement("span"),i:document.createElement("i"),subtext:document.createElement("small"),a:document.createElement("a"),li:document.createElement("li"),whitespace:document.createTextNode(" "),fragment:document.createDocumentFragment()};R.a.setAttribute("role","option"),R.subtext.className="text-muted",R.text=R.span.cloneNode(!1),R.text.className="text",R.checkMark=R.span.cloneNode(!1);var H=new RegExp("38|40"),B=new RegExp("^9$|27"),j=function(e,t,i){var s=R.li.cloneNode(!1);return e&&(1===e.nodeType||11===e.nodeType?s.appendChild(e):s.innerHTML=e),void 0!==t&&""!==t&&(s.className=t),null!=i&&s.classList.add("optgroup-"+i),s},W=function(e,t){var i,s,n=R.text.cloneNode(!1);if(e.content)n.innerHTML=e.content;else{if(n.textContent=e.text,e.icon){var o=R.whitespace.cloneNode(!1);(s=(!0===t?R.i:R.span).cloneNode(!1)).className=e.iconBase+" "+e.icon,R.fragment.appendChild(s),R.fragment.appendChild(o)}e.subtext&&((i=R.subtext.cloneNode(!1)).textContent=e.subtext,n.appendChild(i))}if(!0===t)for(;0<n.childNodes.length;)R.fragment.appendChild(n.childNodes[0]);else R.fragment.appendChild(n);return R.fragment},_=function(e,t){var i=this;h.useDefault||(L.valHooks.select.set=h._set,h.useDefault=!0),this.$element=L(e),this.$newElement=null,this.$button=null,this.$menu=null,this.options=t,this.selectpicker={main:{},search:{},current:{},view:{},keydown:{keyHistory:"",resetKeyHistory:{start:function(){return setTimeout(function(){i.selectpicker.keydown.keyHistory=""},800)}}}},null===this.options.title&&(this.options.title=this.$element.attr("title"));var s=this.options.windowPadding;"number"==typeof s&&(this.options.windowPadding=[s,s,s,s]),this.val=_.prototype.val,this.render=_.prototype.render,this.refresh=_.prototype.refresh,this.setStyle=_.prototype.setStyle,this.selectAll=_.prototype.selectAll,this.deselectAll=_.prototype.deselectAll,this.destroy=_.prototype.destroy,this.remove=_.prototype.remove,this.show=_.prototype.show,this.hide=_.prototype.hide,this.init()};function M(e){var r,a=arguments,c=e;if([].shift.apply(a),!N.success){try{N.full=(L.fn.dropdown.Constructor.VERSION||"").split(" ")[0].split(".")}catch(e){_.BootstrapVersion?N.full=_.BootstrapVersion.split(" ")[0].split("."):(N.full=[N.major,"0","0"],console.warn("There was an issue retrieving Bootstrap's version. Ensure Bootstrap is being loaded before bootstrap-select and there is no namespace collision. If loading Bootstrap asynchronously, the version may need to be manually specified via $.fn.selectpicker.Constructor.BootstrapVersion.",e))}N.major=N.full[0],N.success=!0}if("4"===N.major){var t=[];_.DEFAULTS.style===U.BUTTONCLASS&&t.push({name:"style",className:"BUTTONCLASS"}),_.DEFAULTS.iconBase===U.ICONBASE&&t.push({name:"iconBase",className:"ICONBASE"}),_.DEFAULTS.tickIcon===U.TICKICON&&t.push({name:"tickIcon",className:"TICKICON"}),U.DIVIDER="dropdown-divider",U.SHOW="show",U.BUTTONCLASS="btn-light",U.POPOVERHEADER="popover-header",U.ICONBASE="",U.TICKICON="bs-ok-default";for(var i=0;i<t.length;i++)e=t[i],_.DEFAULTS[e.name]=U[e.className]}var s=this.each(function(){var e=L(this);if(e.is("select")){var t=e.data("selectpicker"),i="object"==typeof c&&c;if(t){if(i)for(var s in i)i.hasOwnProperty(s)&&(t.options[s]=i[s])}else{var n=e.data();for(var o in n)n.hasOwnProperty(o)&&-1!==L.inArray(o,p)&&delete n[o];var l=L.extend({},_.DEFAULTS,L.fn.selectpicker.defaults||{},n,i);l.template=L.extend({},_.DEFAULTS.template,L.fn.selectpicker.defaults?L.fn.selectpicker.defaults.template:{},n.template,i.template),e.data("selectpicker",t=new _(this,l))}"string"==typeof c&&(r=t[c]instanceof Function?t[c].apply(t,a):t.options[c])}});return void 0!==r?r:s}_.VERSION="1.13.12",_.DEFAULTS={noneSelectedText:"Nothing selected",noneResultsText:"No results matched {0}",countSelectedText:function(e,t){return 1==e?"{0} item selected":"{0} items selected"},maxOptionsText:function(e,t){return[1==e?"Limit reached ({n} item max)":"Limit reached ({n} items max)",1==t?"Group limit reached ({n} item max)":"Group limit reached ({n} items max)"]},selectAllText:"Select All",deselectAllText:"Deselect All",doneButton:!1,doneButtonText:"Close",multipleSeparator:", ",styleBase:"btn",style:U.BUTTONCLASS,size:"auto",title:null,selectedTextFormat:"values",width:!1,container:!1,hideDisabled:!1,showSubtext:!1,showIcon:!0,showContent:!0,dropupAuto:!0,header:!1,liveSearch:!1,liveSearchPlaceholder:null,liveSearchNormalize:!1,liveSearchStyle:"contains",actionsBox:!1,iconBase:U.ICONBASE,tickIcon:U.TICKICON,showTick:!1,template:{caret:'<span class="caret"></span>'},maxOptions:!1,mobile:!1,selectOnTab:!1,dropdownAlignRight:!1,windowPadding:0,virtualScroll:600,display:!1,sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role","tabindex","style",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]}},_.prototype={constructor:_,init:function(){var i=this,e=this.$element.attr("id");E++,this.selectId="bs-select-"+E,this.$element[0].classList.add("bs-select-hidden"),this.multiple=this.$element.prop("multiple"),this.autofocus=this.$element.prop("autofocus"),this.$element[0].classList.contains("show-tick")&&(this.options.showTick=!0),this.$newElement=this.createDropdown(),this.$element.after(this.$newElement).prependTo(this.$newElement),this.$button=this.$newElement.children("button"),this.$menu=this.$newElement.children(z.MENU),this.$menuInner=this.$menu.children(".inner"),this.$searchbox=this.$menu.find("input"),this.$element[0].classList.remove("bs-select-hidden"),!0===this.options.dropdownAlignRight&&this.$menu[0].classList.add(U.MENURIGHT),void 0!==e&&this.$button.attr("data-id",e),this.checkDisabled(),this.clickListener(),this.options.liveSearch?(this.liveSearchListener(),this.focusedParent=this.$searchbox[0]):this.focusedParent=this.$menuInner[0],this.setStyle(),this.render(),this.setWidth(),this.options.container?this.selectPosition():this.$element.on("hide"+P,function(){if(i.isVirtual()){var e=i.$menuInner[0],t=e.firstChild.cloneNode(!1);e.replaceChild(t,e.firstChild),e.scrollTop=0}}),this.$menu.data("this",this),this.$newElement.data("this",this),this.options.mobile&&this.mobile(),this.$newElement.on({"hide.bs.dropdown":function(e){i.$element.trigger("hide"+P,e)},"hidden.bs.dropdown":function(e){i.$element.trigger("hidden"+P,e)},"show.bs.dropdown":function(e){i.$element.trigger("show"+P,e)},"shown.bs.dropdown":function(e){i.$element.trigger("shown"+P,e)}}),i.$element[0].hasAttribute("required")&&this.$element.on("invalid"+P,function(){i.$button[0].classList.add("bs-invalid"),i.$element.on("shown"+P+".invalid",function(){i.$element.val(i.$element.val()).off("shown"+P+".invalid")}).on("rendered"+P,function(){this.validity.valid&&i.$button[0].classList.remove("bs-invalid"),i.$element.off("rendered"+P)}),i.$button.on("blur"+P,function(){i.$element.trigger("focus").trigger("blur"),i.$button.off("blur"+P)})}),setTimeout(function(){i.createLi(),i.$element.trigger("loaded"+P)})},createDropdown:function(){var e=this.multiple||this.options.showTick?" show-tick":"",t=this.multiple?' aria-multiselectable="true"':"",i="",s=this.autofocus?" autofocus":"";N.major<4&&this.$element.parent().hasClass("input-group")&&(i=" input-group-btn");var n,o="",l="",r="",a="";return this.options.header&&(o='<div class="'+U.POPOVERHEADER+'"><button type="button" class="close" aria-hidden="true">&times;</button>'+this.options.header+"</div>"),this.options.liveSearch&&(l='<div class="bs-searchbox"><input type="search" class="form-control" autocomplete="off"'+(null===this.options.liveSearchPlaceholder?"":' placeholder="'+O(this.options.liveSearchPlaceholder)+'"')+' role="combobox" aria-label="Search" aria-controls="'+this.selectId+'" aria-autocomplete="list"></div>'),this.multiple&&this.options.actionsBox&&(r='<div class="bs-actionsbox"><div class="btn-group btn-group-sm btn-block"><button type="button" class="actions-btn bs-select-all btn '+U.BUTTONCLASS+'">'+this.options.selectAllText+'</button><button type="button" class="actions-btn bs-deselect-all btn '+U.BUTTONCLASS+'">'+this.options.deselectAllText+"</button></div></div>"),this.multiple&&this.options.doneButton&&(a='<div class="bs-donebutton"><div class="btn-group btn-block"><button type="button" class="btn btn-sm '+U.BUTTONCLASS+'">'+this.options.doneButtonText+"</button></div></div>"),n='<div class="dropdown bootstrap-select'+e+i+'"><button type="button" class="'+this.options.styleBase+' dropdown-toggle" '+("static"===this.options.display?'data-display="static"':"")+'data-toggle="dropdown"'+s+' role="combobox" aria-owns="'+this.selectId+'" aria-haspopup="listbox" aria-expanded="false"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner"></div></div> </div>'+("4"===N.major?"":'<span class="bs-caret">'+this.options.template.caret+"</span>")+'</button><div class="'+U.MENU+" "+("4"===N.major?"":U.SHOW)+'">'+o+l+r+'<div class="inner '+U.SHOW+'" role="listbox" id="'+this.selectId+'" tabindex="-1" '+t+'><ul class="'+U.MENU+" inner "+("4"===N.major?U.SHOW:"")+'" role="presentation"></ul></div>'+a+"</div></div>",L(n)},setPositionData:function(){this.selectpicker.view.canHighlight=[];for(var e=this.selectpicker.view.size=0;e<this.selectpicker.current.data.length;e++){var t=this.selectpicker.current.data[e],i=!0;"divider"===t.type?(i=!1,t.height=this.sizeInfo.dividerHeight):"optgroup-label"===t.type?(i=!1,t.height=this.sizeInfo.dropdownHeaderHeight):t.height=this.sizeInfo.liHeight,t.disabled&&(i=!1),this.selectpicker.view.canHighlight.push(i),i&&(this.selectpicker.view.size++,t.posinset=this.selectpicker.view.size),t.position=(0===e?0:this.selectpicker.current.data[e-1].position)+t.height}},isVirtual:function(){return!1!==this.options.virtualScroll&&this.selectpicker.main.elements.length>=this.options.virtualScroll||!0===this.options.virtualScroll},createView:function(N,e,t){var z,R,H=this,i=0,B=[];if(this.selectpicker.current=N?this.selectpicker.search:this.selectpicker.main,this.setPositionData(),e)if(t)i=this.$menuInner[0].scrollTop;else if(!H.multiple){var s=H.$element[0],n=(s.options[s.selectedIndex]||{}).liIndex;if("number"==typeof n&&!1!==H.options.size){var o=H.selectpicker.main.data[n],l=o&&o.position;l&&(i=l-(H.sizeInfo.menuInnerHeight+H.sizeInfo.liHeight)/2)}}function r(e,t){var i,s,n,o,l,r,a,c,p,h,d=H.selectpicker.current.elements.length,u=[],f=!0,m=H.isVirtual();H.selectpicker.view.scrollTop=e,i=Math.ceil(H.sizeInfo.menuInnerHeight/H.sizeInfo.liHeight*1.5),s=Math.round(d/i)||1;for(var g=0;g<s;g++){var v=(g+1)*i;if(g===s-1&&(v=d),u[g]=[g*i+(g?1:0),v],!d)break;void 0===l&&e-1<=H.selectpicker.current.data[v-1].position-H.sizeInfo.menuInnerHeight&&(l=g)}if(void 0===l&&(l=0),r=[H.selectpicker.view.position0,H.selectpicker.view.position1],n=Math.max(0,l-1),o=Math.min(s-1,l+1),H.selectpicker.view.position0=!1===m?0:Math.max(0,u[n][0])||0,H.selectpicker.view.position1=!1===m?d:Math.min(d,u[o][1])||0,a=r[0]!==H.selectpicker.view.position0||r[1]!==H.selectpicker.view.position1,void 0!==H.activeIndex&&(R=H.selectpicker.main.elements[H.prevActiveIndex],B=H.selectpicker.main.elements[H.activeIndex],z=H.selectpicker.main.elements[H.selectedIndex],t&&(H.activeIndex!==H.selectedIndex&&H.defocusItem(B),H.activeIndex=void 0),H.activeIndex&&H.activeIndex!==H.selectedIndex&&H.defocusItem(z)),void 0!==H.prevActiveIndex&&H.prevActiveIndex!==H.activeIndex&&H.prevActiveIndex!==H.selectedIndex&&H.defocusItem(R),(t||a)&&(c=H.selectpicker.view.visibleElements?H.selectpicker.view.visibleElements.slice():[],H.selectpicker.view.visibleElements=!1===m?H.selectpicker.current.elements:H.selectpicker.current.elements.slice(H.selectpicker.view.position0,H.selectpicker.view.position1),H.setOptionStatus(),(N||!1===m&&t)&&(p=c,h=H.selectpicker.view.visibleElements,f=!(p.length===h.length&&p.every(function(e,t){return e===h[t]}))),(t||!0===m)&&f)){var b,w,x=H.$menuInner[0],I=document.createDocumentFragment(),y=x.firstChild.cloneNode(!1),k=H.selectpicker.view.visibleElements,O=[];x.replaceChild(y,x.firstChild),g=0;for(var $=k.length;g<$;g++){var S,E,C=k[g];H.options.sanitize&&(S=C.lastChild)&&(E=H.selectpicker.current.data[g+H.selectpicker.view.position0])&&E.content&&!E.sanitized&&(O.push(S),E.sanitized=!0),I.appendChild(C)}if(H.options.sanitize&&O.length&&G(O,H.options.whiteList,H.options.sanitizeFn),!0===m?(b=0===H.selectpicker.view.position0?0:H.selectpicker.current.data[H.selectpicker.view.position0-1].position,w=H.selectpicker.view.position1>d-1?0:H.selectpicker.current.data[d-1].position-H.selectpicker.current.data[H.selectpicker.view.position1-1].position,x.firstChild.style.marginTop=b+"px",x.firstChild.style.marginBottom=w+"px"):(x.firstChild.style.marginTop=0,x.firstChild.style.marginBottom=0),x.firstChild.appendChild(I),!0===m&&H.sizeInfo.hasScrollBar){var L=x.firstChild.offsetWidth;if(t&&L<H.sizeInfo.menuInnerInnerWidth&&H.sizeInfo.totalMenuWidth>H.sizeInfo.selectWidth)x.firstChild.style.minWidth=H.sizeInfo.menuInnerInnerWidth+"px";else if(L>H.sizeInfo.menuInnerInnerWidth){H.$menu[0].style.minWidth=0;var T=x.firstChild.offsetWidth;T>H.sizeInfo.menuInnerInnerWidth&&(H.sizeInfo.menuInnerInnerWidth=T,x.firstChild.style.minWidth=H.sizeInfo.menuInnerInnerWidth+"px"),H.$menu[0].style.minWidth=""}}}if(H.prevActiveIndex=H.activeIndex,H.options.liveSearch){if(N&&t){var A,D=0;H.selectpicker.view.canHighlight[D]||(D=1+H.selectpicker.view.canHighlight.slice(1).indexOf(!0)),A=H.selectpicker.view.visibleElements[D],H.defocusItem(H.selectpicker.view.currentActive),H.activeIndex=(H.selectpicker.current.data[D]||{}).index,H.focusItem(A)}}else H.$menuInner.trigger("focus")}r(i,!0),this.$menuInner.off("scroll.createView").on("scroll.createView",function(e,t){H.noScroll||r(this.scrollTop,t),H.noScroll=!1}),L(window).off("resize"+P+"."+this.selectId+".createView").on("resize"+P+"."+this.selectId+".createView",function(){H.$newElement.hasClass(U.SHOW)&&r(H.$menuInner[0].scrollTop)})},focusItem:function(e,t,i){if(e){t=t||this.selectpicker.main.data[this.activeIndex];var s=e.firstChild;s&&(s.setAttribute("aria-setsize",this.selectpicker.view.size),s.setAttribute("aria-posinset",t.posinset),!0!==i&&(this.focusedParent.setAttribute("aria-activedescendant",s.id),e.classList.add("active"),s.classList.add("active")))}},defocusItem:function(e){e&&(e.classList.remove("active"),e.firstChild&&e.firstChild.classList.remove("active"))},setPlaceholder:function(){var e=!1;if(this.options.title&&!this.multiple){this.selectpicker.view.titleOption||(this.selectpicker.view.titleOption=document.createElement("option")),e=!0;var t=this.$element[0],i=!1,s=!this.selectpicker.view.titleOption.parentNode;s&&(this.selectpicker.view.titleOption.className="bs-title-option",this.selectpicker.view.titleOption.value="",i=void 0===L(t.options[t.selectedIndex]).attr("selected")&&void 0===this.$element.data("selected")),!s&&0===this.selectpicker.view.titleOption.index||t.insertBefore(this.selectpicker.view.titleOption,t.firstChild),i&&(t.selectedIndex=0)}return e},createLi:function(){var u=this,f=this.options.iconBase,m=':not([hidden]):not([data-hidden="true"])',g=[],v=[],b=0,w=0,e=this.setPlaceholder()?1:0;this.options.hideDisabled&&(m+=":not(:disabled)"),!u.options.showTick&&!u.multiple||R.checkMark.parentNode||(R.checkMark.className=f+" "+u.options.tickIcon+" check-mark",R.a.appendChild(R.checkMark));var t=this.$element[0].querySelectorAll("select > *"+m);function x(e){var t=v[v.length-1];t&&"divider"===t.type&&(t.optID||e.optID)||((e=e||{}).type="divider",g.push(j(!1,U.DIVIDER,e.optID?e.optID+"div":void 0)),v.push(e))}function I(e,t){if((t=t||{}).divider="true"===e.getAttribute("data-divider"),t.divider)x({optID:t.optID});else{var i=v.length,s=e.style.cssText,n=s?O(s):"",o=(e.className||"")+(t.optgroupClass||"");t.optID&&(o="opt "+o),t.text=e.textContent,t.content=e.getAttribute("data-content"),t.tokens=e.getAttribute("data-tokens"),t.subtext=e.getAttribute("data-subtext"),t.icon=e.getAttribute("data-icon"),t.iconBase=f;var l=W(t),r=j((c=l,p=o,h=n,d=R.a.cloneNode(!0),c&&(11===c.nodeType?d.appendChild(c):d.insertAdjacentHTML("beforeend",c)),void 0!==p&&""!==p&&(d.className=p),"4"===N.major&&d.classList.add("dropdown-item"),h&&d.setAttribute("style",h),d),"",t.optID);r.firstChild&&(r.firstChild.id=u.selectId+"-"+i),g.push(r),e.liIndex=i,t.display=t.content||t.text,t.type="option",t.index=i,t.option=e,t.disabled=t.disabled||e.disabled,v.push(t);var a=0;t.display&&(a+=t.display.length),t.subtext&&(a+=t.subtext.length),t.icon&&(a+=1),b<a&&(b=a,u.selectpicker.view.widestOption=g[g.length-1])}var c,p,h,d}function i(e,t){var i=t[e],s=t[e-1],n=t[e+1],o=i.querySelectorAll("option"+m);if(o.length){var l,r,a={label:O(i.label),subtext:i.getAttribute("data-subtext"),icon:i.getAttribute("data-icon"),iconBase:f},c=" "+(i.className||"");w++,s&&x({optID:w});var p=function(e){var t,i,s=R.text.cloneNode(!1);if(s.innerHTML=e.label,e.icon){var n=R.whitespace.cloneNode(!1);(i=R.span.cloneNode(!1)).className=e.iconBase+" "+e.icon,R.fragment.appendChild(i),R.fragment.appendChild(n)}return e.subtext&&((t=R.subtext.cloneNode(!1)).textContent=e.subtext,s.appendChild(t)),R.fragment.appendChild(s),R.fragment}(a);g.push(j(p,"dropdown-header"+c,w)),v.push({display:a.label,subtext:a.subtext,type:"optgroup-label",optID:w});for(var h=0,d=o.length;h<d;h++){var u=o[h];0===h&&(r=(l=v.length-1)+d),I(u,{headerIndex:l,lastIndex:r,optID:w,optgroupClass:c,disabled:i.disabled})}n&&x({optID:w})}}for(var s=t.length;e<s;e++){var n=t[e];"OPTGROUP"!==n.tagName?I(n,{}):i(e,t)}this.selectpicker.main.elements=g,this.selectpicker.main.data=v,this.selectpicker.current=this.selectpicker.main},findLis:function(){return this.$menuInner.find(".inner > li")},render:function(){this.setPlaceholder();var e,t=this,i=this.$element[0],s=C(i,this.options.hideDisabled),n=s.length,o=this.$button[0],l=o.querySelector(".filter-option-inner-inner"),r=document.createTextNode(this.options.multipleSeparator),a=R.fragment.cloneNode(!1),c=!1;if(o.classList.toggle("bs-placeholder",t.multiple?!n:!T(i,s)),this.tabIndex(),"static"===this.options.selectedTextFormat)a=W({text:this.options.title},!0);else if(!1===(this.multiple&&-1!==this.options.selectedTextFormat.indexOf("count")&&1<n&&(1<(e=this.options.selectedTextFormat.split(">")).length&&n>e[1]||1===e.length&&2<=n))){for(var p=0;p<n&&p<50;p++){var h=s[p],d={},u={content:h.getAttribute("data-content"),subtext:h.getAttribute("data-subtext"),icon:h.getAttribute("data-icon")};this.multiple&&0<p&&a.appendChild(r.cloneNode(!1)),h.title?d.text=h.title:u.content&&t.options.showContent?(d.content=u.content.toString(),c=!0):(t.options.showIcon&&(d.icon=u.icon,d.iconBase=this.options.iconBase),t.options.showSubtext&&!t.multiple&&u.subtext&&(d.subtext=" "+u.subtext),d.text=h.textContent.trim()),a.appendChild(W(d,!0))}49<n&&a.appendChild(document.createTextNode("..."))}else{var f=':not([hidden]):not([data-hidden="true"]):not([data-divider="true"])';this.options.hideDisabled&&(f+=":not(:disabled)");var m=this.$element[0].querySelectorAll("select > option"+f+", optgroup"+f+" option"+f).length,g="function"==typeof this.options.countSelectedText?this.options.countSelectedText(n,m):this.options.countSelectedText;a=W({text:g.replace("{0}",n.toString()).replace("{1}",m.toString())},!0)}if(null==this.options.title&&(this.options.title=this.$element.attr("title")),a.childNodes.length||(a=W({text:void 0!==this.options.title?this.options.title:this.options.noneSelectedText},!0)),o.title=a.textContent.replace(/<[^>]*>?/g,"").trim(),this.options.sanitize&&c&&G([a],t.options.whiteList,t.options.sanitizeFn),l.innerHTML="",l.appendChild(a),N.major<4&&this.$newElement[0].classList.contains("bs3-has-addon")){var v=o.querySelector(".filter-expand"),b=l.cloneNode(!0);b.className="filter-expand",v?o.replaceChild(b,v):o.appendChild(b)}this.$element.trigger("rendered"+P)},setStyle:function(e,t){var i,s=this.$button[0],n=this.$newElement[0],o=this.options.style.trim();this.$element.attr("class")&&this.$newElement.addClass(this.$element.attr("class").replace(/selectpicker|mobile-device|bs-select-hidden|validate\[.*\]/gi,"")),N.major<4&&(n.classList.add("bs3"),n.parentNode.classList.contains("input-group")&&(n.previousElementSibling||n.nextElementSibling)&&(n.previousElementSibling||n.nextElementSibling).classList.contains("input-group-addon")&&n.classList.add("bs3-has-addon")),i=e?e.trim():o,"add"==t?i&&s.classList.add.apply(s.classList,i.split(" ")):"remove"==t?i&&s.classList.remove.apply(s.classList,i.split(" ")):(o&&s.classList.remove.apply(s.classList,o.split(" ")),i&&s.classList.add.apply(s.classList,i.split(" ")))},liHeight:function(e){if(e||!1!==this.options.size&&!this.sizeInfo){this.sizeInfo||(this.sizeInfo={});var t=document.createElement("div"),i=document.createElement("div"),s=document.createElement("div"),n=document.createElement("ul"),o=document.createElement("li"),l=document.createElement("li"),r=document.createElement("li"),a=document.createElement("a"),c=document.createElement("span"),p=this.options.header&&0<this.$menu.find("."+U.POPOVERHEADER).length?this.$menu.find("."+U.POPOVERHEADER)[0].cloneNode(!0):null,h=this.options.liveSearch?document.createElement("div"):null,d=this.options.actionsBox&&this.multiple&&0<this.$menu.find(".bs-actionsbox").length?this.$menu.find(".bs-actionsbox")[0].cloneNode(!0):null,u=this.options.doneButton&&this.multiple&&0<this.$menu.find(".bs-donebutton").length?this.$menu.find(".bs-donebutton")[0].cloneNode(!0):null,f=this.$element.find("option")[0];if(this.sizeInfo.selectWidth=this.$newElement[0].offsetWidth,c.className="text",a.className="dropdown-item "+(f?f.className:""),t.className=this.$menu[0].parentNode.className+" "+U.SHOW,t.style.width=0,"auto"===this.options.width&&(i.style.minWidth=0),i.className=U.MENU+" "+U.SHOW,s.className="inner "+U.SHOW,n.className=U.MENU+" inner "+("4"===N.major?U.SHOW:""),o.className=U.DIVIDER,l.className="dropdown-header",c.appendChild(document.createTextNode("​")),a.appendChild(c),r.appendChild(a),l.appendChild(c.cloneNode(!0)),this.selectpicker.view.widestOption&&n.appendChild(this.selectpicker.view.widestOption.cloneNode(!0)),n.appendChild(r),n.appendChild(o),n.appendChild(l),p&&i.appendChild(p),h){var m=document.createElement("input");h.className="bs-searchbox",m.className="form-control",h.appendChild(m),i.appendChild(h)}d&&i.appendChild(d),s.appendChild(n),i.appendChild(s),u&&i.appendChild(u),t.appendChild(i),document.body.appendChild(t);var g,v=r.offsetHeight,b=l?l.offsetHeight:0,w=p?p.offsetHeight:0,x=h?h.offsetHeight:0,I=d?d.offsetHeight:0,y=u?u.offsetHeight:0,k=L(o).outerHeight(!0),O=!!window.getComputedStyle&&window.getComputedStyle(i),$=i.offsetWidth,S=O?null:L(i),E={vert:D(O?O.paddingTop:S.css("paddingTop"))+D(O?O.paddingBottom:S.css("paddingBottom"))+D(O?O.borderTopWidth:S.css("borderTopWidth"))+D(O?O.borderBottomWidth:S.css("borderBottomWidth")),horiz:D(O?O.paddingLeft:S.css("paddingLeft"))+D(O?O.paddingRight:S.css("paddingRight"))+D(O?O.borderLeftWidth:S.css("borderLeftWidth"))+D(O?O.borderRightWidth:S.css("borderRightWidth"))},C={vert:E.vert+D(O?O.marginTop:S.css("marginTop"))+D(O?O.marginBottom:S.css("marginBottom"))+2,horiz:E.horiz+D(O?O.marginLeft:S.css("marginLeft"))+D(O?O.marginRight:S.css("marginRight"))+2};s.style.overflowY="scroll",g=i.offsetWidth-$,document.body.removeChild(t),this.sizeInfo.liHeight=v,this.sizeInfo.dropdownHeaderHeight=b,this.sizeInfo.headerHeight=w,this.sizeInfo.searchHeight=x,this.sizeInfo.actionsHeight=I,this.sizeInfo.doneButtonHeight=y,this.sizeInfo.dividerHeight=k,this.sizeInfo.menuPadding=E,this.sizeInfo.menuExtras=C,this.sizeInfo.menuWidth=$,this.sizeInfo.menuInnerInnerWidth=$-E.horiz,this.sizeInfo.totalMenuWidth=this.sizeInfo.menuWidth,this.sizeInfo.scrollBarWidth=g,this.sizeInfo.selectHeight=this.$newElement[0].offsetHeight,this.setPositionData()}},getSelectPosition:function(){var e,t=L(window),i=this.$newElement.offset(),s=L(this.options.container);this.options.container&&s.length&&!s.is("body")?((e=s.offset()).top+=parseInt(s.css("borderTopWidth")),e.left+=parseInt(s.css("borderLeftWidth"))):e={top:0,left:0};var n=this.options.windowPadding;this.sizeInfo.selectOffsetTop=i.top-e.top-t.scrollTop(),this.sizeInfo.selectOffsetBot=t.height()-this.sizeInfo.selectOffsetTop-this.sizeInfo.selectHeight-e.top-n[2],this.sizeInfo.selectOffsetLeft=i.left-e.left-t.scrollLeft(),this.sizeInfo.selectOffsetRight=t.width()-this.sizeInfo.selectOffsetLeft-this.sizeInfo.selectWidth-e.left-n[1],this.sizeInfo.selectOffsetTop-=n[0],this.sizeInfo.selectOffsetLeft-=n[3]},setMenuSize:function(e){this.getSelectPosition();var t,i,s,n,o,l,r,a=this.sizeInfo.selectWidth,c=this.sizeInfo.liHeight,p=this.sizeInfo.headerHeight,h=this.sizeInfo.searchHeight,d=this.sizeInfo.actionsHeight,u=this.sizeInfo.doneButtonHeight,f=this.sizeInfo.dividerHeight,m=this.sizeInfo.menuPadding,g=0;if(this.options.dropupAuto&&(r=c*this.selectpicker.current.elements.length+m.vert,this.$newElement.toggleClass(U.DROPUP,this.sizeInfo.selectOffsetTop-this.sizeInfo.selectOffsetBot>this.sizeInfo.menuExtras.vert&&r+this.sizeInfo.menuExtras.vert+50>this.sizeInfo.selectOffsetBot)),"auto"===this.options.size)n=3<this.selectpicker.current.elements.length?3*this.sizeInfo.liHeight+this.sizeInfo.menuExtras.vert-2:0,i=this.sizeInfo.selectOffsetBot-this.sizeInfo.menuExtras.vert,s=n+p+h+d+u,l=Math.max(n-m.vert,0),this.$newElement.hasClass(U.DROPUP)&&(i=this.sizeInfo.selectOffsetTop-this.sizeInfo.menuExtras.vert),t=(o=i)-p-h-d-u-m.vert;else if(this.options.size&&"auto"!=this.options.size&&this.selectpicker.current.elements.length>this.options.size){for(var v=0;v<this.options.size;v++)"divider"===this.selectpicker.current.data[v].type&&g++;t=(i=c*this.options.size+g*f+m.vert)-m.vert,o=i+p+h+d+u,s=l=""}this.$menu.css({"max-height":o+"px",overflow:"hidden","min-height":s+"px"}),this.$menuInner.css({"max-height":t+"px","overflow-y":"auto","min-height":l+"px"}),this.sizeInfo.menuInnerHeight=Math.max(t,1),this.selectpicker.current.data.length&&this.selectpicker.current.data[this.selectpicker.current.data.length-1].position>this.sizeInfo.menuInnerHeight&&(this.sizeInfo.hasScrollBar=!0,this.sizeInfo.totalMenuWidth=this.sizeInfo.menuWidth+this.sizeInfo.scrollBarWidth),"auto"===this.options.dropdownAlignRight&&this.$menu.toggleClass(U.MENURIGHT,this.sizeInfo.selectOffsetLeft>this.sizeInfo.selectOffsetRight&&this.sizeInfo.selectOffsetRight<this.sizeInfo.totalMenuWidth-a),this.dropdown&&this.dropdown._popper&&this.dropdown._popper.update()},setSize:function(e){if(this.liHeight(e),this.options.header&&this.$menu.css("padding-top",0),!1!==this.options.size){var t=this,i=L(window);this.setMenuSize(),this.options.liveSearch&&this.$searchbox.off("input.setMenuSize propertychange.setMenuSize").on("input.setMenuSize propertychange.setMenuSize",function(){return t.setMenuSize()}),"auto"===this.options.size?i.off("resize"+P+"."+this.selectId+".setMenuSize scroll"+P+"."+this.selectId+".setMenuSize").on("resize"+P+"."+this.selectId+".setMenuSize scroll"+P+"."+this.selectId+".setMenuSize",function(){return t.setMenuSize()}):this.options.size&&"auto"!=this.options.size&&this.selectpicker.current.elements.length>this.options.size&&i.off("resize"+P+"."+this.selectId+".setMenuSize scroll"+P+"."+this.selectId+".setMenuSize"),t.createView(!1,!0,e)}},setWidth:function(){var i=this;"auto"===this.options.width?requestAnimationFrame(function(){i.$menu.css("min-width","0"),i.$element.on("loaded"+P,function(){i.liHeight(),i.setMenuSize();var e=i.$newElement.clone().appendTo("body"),t=e.css("width","auto").children("button").outerWidth();e.remove(),i.sizeInfo.selectWidth=Math.max(i.sizeInfo.totalMenuWidth,t),i.$newElement.css("width",i.sizeInfo.selectWidth+"px")})}):"fit"===this.options.width?(this.$menu.css("min-width",""),this.$newElement.css("width","").addClass("fit-width")):this.options.width?(this.$menu.css("min-width",""),this.$newElement.css("width",this.options.width)):(this.$menu.css("min-width",""),this.$newElement.css("width","")),this.$newElement.hasClass("fit-width")&&"fit"!==this.options.width&&this.$newElement[0].classList.remove("fit-width")},selectPosition:function(){function e(e){var t={},i=l.options.display||!!L.fn.dropdown.Constructor.Default&&L.fn.dropdown.Constructor.Default.display;l.$bsContainer.addClass(e.attr("class").replace(/form-control|fit-width/gi,"")).toggleClass(U.DROPUP,e.hasClass(U.DROPUP)),s=e.offset(),r.is("body")?n={top:0,left:0}:((n=r.offset()).top+=parseInt(r.css("borderTopWidth"))-r.scrollTop(),n.left+=parseInt(r.css("borderLeftWidth"))-r.scrollLeft()),o=e.hasClass(U.DROPUP)?0:e[0].offsetHeight,(N.major<4||"static"===i)&&(t.top=s.top-n.top+o,t.left=s.left-n.left),t.width=e[0].offsetWidth,l.$bsContainer.css(t)}this.$bsContainer=L('<div class="bs-container" />');var s,n,o,l=this,r=L(this.options.container);this.$button.on("click.bs.dropdown.data-api",function(){l.isDisabled()||(e(l.$newElement),l.$bsContainer.appendTo(l.options.container).toggleClass(U.SHOW,!l.$button.hasClass(U.SHOW)).append(l.$menu))}),L(window).off("resize"+P+"."+this.selectId+" scroll"+P+"."+this.selectId).on("resize"+P+"."+this.selectId+" scroll"+P+"."+this.selectId,function(){l.$newElement.hasClass(U.SHOW)&&e(l.$newElement)}),this.$element.on("hide"+P,function(){l.$menu.data("height",l.$menu.height()),l.$bsContainer.detach()})},setOptionStatus:function(e){var t=this;if(t.noScroll=!1,t.selectpicker.view.visibleElements&&t.selectpicker.view.visibleElements.length)for(var i=0;i<t.selectpicker.view.visibleElements.length;i++){var s=t.selectpicker.current.data[i+t.selectpicker.view.position0],n=s.option;n&&(!0!==e&&t.setDisabled(s.index,s.disabled),t.setSelected(s.index,n.selected))}},setSelected:function(e,t){var i,s,n=this.selectpicker.main.elements[e],o=this.selectpicker.main.data[e],l=void 0!==this.activeIndex,r=this.activeIndex===e||t&&!this.multiple&&!l;o.selected=t,s=n.firstChild,t&&(this.selectedIndex=e),n.classList.toggle("selected",t),r?(this.focusItem(n,o),this.selectpicker.view.currentActive=n,this.activeIndex=e):this.defocusItem(n),s&&(s.classList.toggle("selected",t),t?s.setAttribute("aria-selected",!0):this.multiple?s.setAttribute("aria-selected",!1):s.removeAttribute("aria-selected")),r||l||!t||void 0===this.prevActiveIndex||(i=this.selectpicker.main.elements[this.prevActiveIndex],this.defocusItem(i))},setDisabled:function(e,t){var i,s=this.selectpicker.main.elements[e];this.selectpicker.main.data[e].disabled=t,i=s.firstChild,s.classList.toggle(U.DISABLED,t),i&&("4"===N.major&&i.classList.toggle(U.DISABLED,t),t?(i.setAttribute("aria-disabled",t),i.setAttribute("tabindex",-1)):(i.removeAttribute("aria-disabled"),i.setAttribute("tabindex",0)))},isDisabled:function(){return this.$element[0].disabled},checkDisabled:function(){this.isDisabled()?(this.$newElement[0].classList.add(U.DISABLED),this.$button.addClass(U.DISABLED).attr("tabindex",-1).attr("aria-disabled",!0)):(this.$button[0].classList.contains(U.DISABLED)&&(this.$newElement[0].classList.remove(U.DISABLED),this.$button.removeClass(U.DISABLED).attr("aria-disabled",!1)),-1!=this.$button.attr("tabindex")||this.$element.data("tabindex")||this.$button.removeAttr("tabindex"))},tabIndex:function(){this.$element.data("tabindex")!==this.$element.attr("tabindex")&&-98!==this.$element.attr("tabindex")&&"-98"!==this.$element.attr("tabindex")&&(this.$element.data("tabindex",this.$element.attr("tabindex")),this.$button.attr("tabindex",this.$element.data("tabindex"))),this.$element.attr("tabindex",-98)},clickListener:function(){var E=this,t=L(document);function e(){E.options.liveSearch?E.$searchbox.trigger("focus"):E.$menuInner.trigger("focus")}function i(){E.dropdown&&E.dropdown._popper&&E.dropdown._popper.state.isCreated?e():requestAnimationFrame(i)}t.data("spaceSelect",!1),this.$button.on("keyup",function(e){/(32)/.test(e.keyCode.toString(10))&&t.data("spaceSelect")&&(e.preventDefault(),t.data("spaceSelect",!1))}),this.$newElement.on("show.bs.dropdown",function(){3<N.major&&!E.dropdown&&(E.dropdown=E.$button.data("bs.dropdown"),E.dropdown._menu=E.$menu[0])}),this.$button.on("click.bs.dropdown.data-api",function(){E.$newElement.hasClass(U.SHOW)||E.setSize()}),this.$element.on("shown"+P,function(){E.$menuInner[0].scrollTop!==E.selectpicker.view.scrollTop&&(E.$menuInner[0].scrollTop=E.selectpicker.view.scrollTop),3<N.major?requestAnimationFrame(i):e()}),this.$menuInner.on("mouseenter","li a",function(e){var t=this.parentElement,i=E.isVirtual()?E.selectpicker.view.position0:0,s=Array.prototype.indexOf.call(t.parentElement.children,t),n=E.selectpicker.current.data[s+i];E.focusItem(t,n,!0)}),this.$menuInner.on("click","li a",function(e,t){var i=L(this),s=E.$element[0],n=E.isVirtual()?E.selectpicker.view.position0:0,o=E.selectpicker.current.data[i.parent().index()+n],l=o.index,r=T(s),a=s.selectedIndex,c=s.options[a],p=!0;if(E.multiple&&1!==E.options.maxOptions&&e.stopPropagation(),e.preventDefault(),!E.isDisabled()&&!i.parent().hasClass(U.DISABLED)){var h=o.option,d=L(h),u=h.selected,f=d.parent("optgroup"),m=f.find("option"),g=E.options.maxOptions,v=f.data("maxOptions")||!1;if(l===E.activeIndex&&(t=!0),t||(E.prevActiveIndex=E.activeIndex,E.activeIndex=void 0),E.multiple){if(h.selected=!u,E.setSelected(l,!u),i.trigger("blur"),!1!==g||!1!==v){var b=g<C(s).length,w=v<f.find("option:selected").length;if(g&&b||v&&w)if(g&&1==g)s.selectedIndex=-1,h.selected=!0,E.setOptionStatus(!0);else if(v&&1==v){for(var x=0;x<m.length;x++){var I=m[x];I.selected=!1,E.setSelected(I.liIndex,!1)}h.selected=!0,E.setSelected(l,!0)}else{var y="string"==typeof E.options.maxOptionsText?[E.options.maxOptionsText,E.options.maxOptionsText]:E.options.maxOptionsText,k="function"==typeof y?y(g,v):y,O=k[0].replace("{n}",g),$=k[1].replace("{n}",v),S=L('<div class="notify"></div>');k[2]&&(O=O.replace("{var}",k[2][1<g?0:1]),$=$.replace("{var}",k[2][1<v?0:1])),h.selected=!1,E.$menu.append(S),g&&b&&(S.append(L("<div>"+O+"</div>")),p=!1,E.$element.trigger("maxReached"+P)),v&&w&&(S.append(L("<div>"+$+"</div>")),p=!1,E.$element.trigger("maxReachedGrp"+P)),setTimeout(function(){E.setSelected(l,!1)},10),S[0].classList.add("fadeOut"),setTimeout(function(){S.remove()},1050)}}}else c&&(c.selected=!1),h.selected=!0,E.setSelected(l,!0);!E.multiple||E.multiple&&1===E.options.maxOptions?E.$button.trigger("focus"):E.options.liveSearch&&E.$searchbox.trigger("focus"),p&&(!E.multiple&&a===s.selectedIndex||(A=[h.index,d.prop("selected"),r],E.$element.triggerNative("change")))}}),this.$menu.on("click","li."+U.DISABLED+" a, ."+U.POPOVERHEADER+", ."+U.POPOVERHEADER+" :not(.close)",function(e){e.currentTarget==this&&(e.preventDefault(),e.stopPropagation(),E.options.liveSearch&&!L(e.target).hasClass("close")?E.$searchbox.trigger("focus"):E.$button.trigger("focus"))}),this.$menuInner.on("click",".divider, .dropdown-header",function(e){e.preventDefault(),e.stopPropagation(),E.options.liveSearch?E.$searchbox.trigger("focus"):E.$button.trigger("focus")}),this.$menu.on("click","."+U.POPOVERHEADER+" .close",function(){E.$button.trigger("click")}),this.$searchbox.on("click",function(e){e.stopPropagation()}),this.$menu.on("click",".actions-btn",function(e){E.options.liveSearch?E.$searchbox.trigger("focus"):E.$button.trigger("focus"),e.preventDefault(),e.stopPropagation(),L(this).hasClass("bs-select-all")?E.selectAll():E.deselectAll()}),this.$element.on("change"+P,function(){E.render(),E.$element.trigger("changed"+P,A),A=null}).on("focus"+P,function(){E.options.mobile||E.$button.trigger("focus")})},liveSearchListener:function(){var u=this,f=document.createElement("li");this.$button.on("click.bs.dropdown.data-api",function(){u.$searchbox.val()&&u.$searchbox.val("")}),this.$searchbox.on("click.bs.dropdown.data-api focus.bs.dropdown.data-api touchend.bs.dropdown.data-api",function(e){e.stopPropagation()}),this.$searchbox.on("input propertychange",function(){var e=u.$searchbox.val();if(u.selectpicker.search.elements=[],u.selectpicker.search.data=[],e){var t=[],i=e.toUpperCase(),s={},n=[],o=u._searchStyle(),l=u.options.liveSearchNormalize;l&&(i=b(i)),u._$lisSelected=u.$menuInner.find(".selected");for(var r=0;r<u.selectpicker.main.data.length;r++){var a=u.selectpicker.main.data[r];s[r]||(s[r]=y(a,i,o,l)),s[r]&&void 0!==a.headerIndex&&-1===n.indexOf(a.headerIndex)&&(0<a.headerIndex&&(s[a.headerIndex-1]=!0,n.push(a.headerIndex-1)),s[a.headerIndex]=!0,n.push(a.headerIndex),s[a.lastIndex+1]=!0),s[r]&&"optgroup-label"!==a.type&&n.push(r)}r=0;for(var c=n.length;r<c;r++){var p=n[r],h=n[r-1],d=(a=u.selectpicker.main.data[p],u.selectpicker.main.data[h]);("divider"!==a.type||"divider"===a.type&&d&&"divider"!==d.type&&c-1!==r)&&(u.selectpicker.search.data.push(a),t.push(u.selectpicker.main.elements[p]))}u.activeIndex=void 0,u.noScroll=!0,u.$menuInner.scrollTop(0),u.selectpicker.search.elements=t,u.createView(!0),t.length||(f.className="no-results",f.innerHTML=u.options.noneResultsText.replace("{0}",'"'+O(e)+'"'),u.$menuInner[0].firstChild.appendChild(f))}else u.$menuInner.scrollTop(0),u.createView(!1)})},_searchStyle:function(){return this.options.liveSearchStyle||"contains"},val:function(e){var t=this.$element[0];if(void 0===e)return this.$element.val();var i=T(t);if(A=[null,null,i],this.$element.val(e).trigger("changed"+P,A),this.$newElement.hasClass(U.SHOW))if(this.multiple)this.setOptionStatus(!0);else{var s=(t.options[t.selectedIndex]||{}).liIndex;"number"==typeof s&&(this.setSelected(this.selectedIndex,!1),this.setSelected(s,!0))}return this.render(),A=null,this.$element},changeAll:function(e){if(this.multiple){void 0===e&&(e=!0);var t=this.$element[0],i=0,s=0,n=T(t);t.classList.add("bs-select-hidden");for(var o=0,l=this.selectpicker.current.elements.length;o<l;o++){var r=this.selectpicker.current.data[o],a=r.option;a&&!r.disabled&&"divider"!==r.type&&(r.selected&&i++,(a.selected=e)&&s++)}t.classList.remove("bs-select-hidden"),i!==s&&(this.setOptionStatus(),A=[null,null,n],this.$element.triggerNative("change"))}},selectAll:function(){return this.changeAll(!0)},deselectAll:function(){return this.changeAll(!1)},toggle:function(e){(e=e||window.event)&&e.stopPropagation(),this.$button.trigger("click.bs.dropdown.data-api")},keydown:function(e){var t,i,s,n,o,l=L(this),r=l.hasClass("dropdown-toggle"),a=(r?l.closest(".dropdown"):l.closest(z.MENU)).data("this"),c=a.findLis(),p=!1,h=9===e.which&&!r&&!a.options.selectOnTab,d=H.test(e.which)||h,u=a.$menuInner[0].scrollTop,f=!0===a.isVirtual()?a.selectpicker.view.position0:0;if(!(112<=e.which&&e.which<=123))if(!(i=a.$newElement.hasClass(U.SHOW))&&(d||48<=e.which&&e.which<=57||96<=e.which&&e.which<=105||65<=e.which&&e.which<=90)&&(a.$button.trigger("click.bs.dropdown.data-api"),a.options.liveSearch))a.$searchbox.trigger("focus");else{if(27===e.which&&i&&(e.preventDefault(),a.$button.trigger("click.bs.dropdown.data-api").trigger("focus")),d){if(!c.length)return;-1!==(t=(s=a.selectpicker.main.elements[a.activeIndex])?Array.prototype.indexOf.call(s.parentElement.children,s):-1)&&a.defocusItem(s),38===e.which?(-1!==t&&t--,t+f<0&&(t+=c.length),a.selectpicker.view.canHighlight[t+f]||-1==(t=a.selectpicker.view.canHighlight.slice(0,t+f).lastIndexOf(!0)-f)&&(t=c.length-1)):40!==e.which&&!h||(++t+f>=a.selectpicker.view.canHighlight.length&&(t=0),a.selectpicker.view.canHighlight[t+f]||(t=t+1+a.selectpicker.view.canHighlight.slice(t+f+1).indexOf(!0))),e.preventDefault();var m=f+t;38===e.which?0===f&&t===c.length-1?(a.$menuInner[0].scrollTop=a.$menuInner[0].scrollHeight,m=a.selectpicker.current.elements.length-1):p=(o=(n=a.selectpicker.current.data[m]).position-n.height)<u:40!==e.which&&!h||(0===t?m=a.$menuInner[0].scrollTop=0:p=u<(o=(n=a.selectpicker.current.data[m]).position-a.sizeInfo.menuInnerHeight)),s=a.selectpicker.current.elements[m],a.activeIndex=a.selectpicker.current.data[m].index,a.focusItem(s),a.selectpicker.view.currentActive=s,p&&(a.$menuInner[0].scrollTop=o),a.options.liveSearch?a.$searchbox.trigger("focus"):l.trigger("focus")}else if(!l.is("input")&&!B.test(e.which)||32===e.which&&a.selectpicker.keydown.keyHistory){var g,v,b=[];e.preventDefault(),a.selectpicker.keydown.keyHistory+=S[e.which],a.selectpicker.keydown.resetKeyHistory.cancel&&clearTimeout(a.selectpicker.keydown.resetKeyHistory.cancel),a.selectpicker.keydown.resetKeyHistory.cancel=a.selectpicker.keydown.resetKeyHistory.start(),v=a.selectpicker.keydown.keyHistory,/^(.)\1+$/.test(v)&&(v=v.charAt(0));for(var w=0;w<a.selectpicker.current.data.length;w++){var x=a.selectpicker.current.data[w];y(x,v,"startsWith",!0)&&a.selectpicker.view.canHighlight[w]&&b.push(x.index)}if(b.length){var I=0;c.removeClass("active").find("a").removeClass("active"),1===v.length&&(-1===(I=b.indexOf(a.activeIndex))||I===b.length-1?I=0:I++),g=b[I],p=0<u-(n=a.selectpicker.main.data[g]).position?(o=n.position-n.height,!0):(o=n.position-a.sizeInfo.menuInnerHeight,n.position>u+a.sizeInfo.menuInnerHeight),s=a.selectpicker.main.elements[g],a.activeIndex=b[I],a.focusItem(s),s&&s.firstChild.focus(),p&&(a.$menuInner[0].scrollTop=o),l.trigger("focus")}}i&&(32===e.which&&!a.selectpicker.keydown.keyHistory||13===e.which||9===e.which&&a.options.selectOnTab)&&(32!==e.which&&e.preventDefault(),a.options.liveSearch&&32===e.which||(a.$menuInner.find(".active a").trigger("click",!0),l.trigger("focus"),a.options.liveSearch||(e.preventDefault(),L(document).data("spaceSelect",!0))))}},mobile:function(){this.$element[0].classList.add("mobile-device")},refresh:function(){var e=L.extend({},this.options,this.$element.data());this.options=e,this.checkDisabled(),this.setStyle(),this.render(),this.createLi(),this.setWidth(),this.setSize(!0),this.$element.trigger("refreshed"+P)},hide:function(){this.$newElement.hide()},show:function(){this.$newElement.show()},remove:function(){this.$newElement.remove(),this.$element.remove()},destroy:function(){this.$newElement.before(this.$element).remove(),this.$bsContainer?this.$bsContainer.remove():this.$menu.remove(),this.$element.off(P).removeData("selectpicker").removeClass("bs-select-hidden selectpicker"),L(window).off(P+"."+this.selectId)}};var q=L.fn.selectpicker;L.fn.selectpicker=M,L.fn.selectpicker.Constructor=_,L.fn.selectpicker.noConflict=function(){return L.fn.selectpicker=q,this},L(document).off("keydown.bs.dropdown.data-api",'.bootstrap-select [data-toggle="dropdown"], .bootstrap-select .dropdown-menu').on("keydown"+P,'.bootstrap-select [data-toggle="dropdown"], .bootstrap-select [role="listbox"], .bootstrap-select .bs-searchbox input',_.prototype.keydown).on("focusin.modal",'.bootstrap-select [data-toggle="dropdown"], .bootstrap-select [role="listbox"], .bootstrap-select .bs-searchbox input',function(e){e.stopPropagation()}),L(window).on("load"+P+".data-api",function(){L(".selectpicker").each(function(){var e=L(this);M.call(e,e.data())})})}(e)}),function(x,I){var e=function(e,t){var i,s,n=this;t=t||{},this.$element=x(e),this.options=x.extend(!0,{},x.fn.ajaxSelectPicker.defaults,t),this.LOG_ERROR=1,this.LOG_WARNING=2,this.LOG_INFO=3,this.LOG_DEBUG=4,this.lastRequest=!1,this.previousQuery="",this.query="",this.request=!1;var o=[{from:"ajaxResultsPreHook",to:"preprocessData"},{from:"ajaxSearchUrl",to:{ajax:{url:"{{{value}}}"}}},{from:"ajaxOptions",to:"ajax"},{from:"debug",to:function(e){var t={};t.log=Boolean(n.options[e.from])?n.LOG_DEBUG:0,n.options=x.extend(!0,{},n.options,t),delete n.options[e.from],n.log(n.LOG_WARNING,'Deprecated option "'+e.from+'". Update code to use:',t)}},{from:"mixWithCurrents",to:"preserveSelected"},{from:"placeHolderOption",to:{locale:{emptyTitle:"{{{value}}}"}}}];o.length&&x.map(o,function(e){if(n.options[e.from])if(x.isPlainObject(e.to))n.replaceValue(e.to,"{{{value}}}",n.options[e.from]),n.options=x.extend(!0,{},n.options,e.to),n.log(n.LOG_WARNING,'Deprecated option "'+e.from+'". Update code to use:',e.to),delete n.options[e.from];else if(x.isFunction(e.to))e.to.apply(n,[e]);else{var t={};t[e.to]=n.options[e.from],n.options=x.extend(!0,{},n.options,t),n.log(n.LOG_WARNING,'Deprecated option "'+e.from+'". Update code to use:',t),delete n.options[e.from]}});var l=this.$element.data();l.searchUrl&&(n.log(n.LOG_WARNING,'Deprecated attribute name: "data-search-url". Update markup to use: \' data-abs-ajax-url="'+l.searchUrl+"\" '"),this.options.ajax.url=l.searchUrl);var r=function(e,t){return t.toLowerCase()},a=function(e,t,i){var s=[].concat(e),n=s.length,o=i||{};if(n){var l=s.shift();o[l]=a(s,t,o[l])}return n?o:t},c=Object.keys(l).filter(/./.test.bind(new RegExp("^abs[A-Z]")));if(c.length){var p={},h=["locale"];for(i=0,s=c.length;i<s;i++){var d=c[i].replace(/^abs([A-Z])/,r).replace(/([A-Z])/g,"-$1").toLowerCase(),u=d.split("-");if(u[0]&&1<u.length&&-1!==h.indexOf(u[0])){for(var f=[u.shift()],m="",g=0;g<u.length;g++)m+=0===g?u[g]:u[g].charAt(0).toUpperCase()+u[g].slice(1);f.push(m),u=f}this.log(this.LOG_DEBUG,'Processing data attribute "data-abs-'+d+'":',l[c[i]]),a(u,l[c[i]],p)}this.options=x.extend(!0,{},this.options,p),this.log(this.LOG_DEBUG,"Merged in the data attribute options: ",p,this.options)}if(this.selectpicker=l.selectpicker,!this.selectpicker)return this.log(this.LOG_ERROR,"Cannot instantiate an AjaxBootstrapSelect instance without selectpicker first being initialized!"),null;if(!this.options.ajax.url)return this.log(this.LOG_ERROR,'Option "ajax.url" must be set! Options:',this.options),null;if(this.locale=x.extend(!0,{},x.fn.ajaxSelectPicker.locale),this.options.langCode=this.options.langCode||I.navigator.userLanguage||I.navigator.language||"en",!this.locale[this.options.langCode]){var v=this.options.langCode;this.options.langCode="en";var b=v.split("-");for(i=0,s=b.length;i<s;i++){var w=b.join("-");if(w.length&&this.locale[w]){this.options.langCode=w;break}b.pop()}this.log(this.LOG_WARNING,'Unknown langCode option: "'+v+'". Using the following langCode instead: "'+this.options.langCode+'".')}this.locale[this.options.langCode]=x.extend(!0,{},this.locale[this.options.langCode],this.options.locale),this.list=new I.AjaxBootstrapSelectList(this),this.list.refresh(),setTimeout(function(){n.init()},500)};e.prototype.init=function(){var s,n=this;this.options.preserveSelected&&this.selectpicker.$menu.off("click",".actions-btn").on("click",".actions-btn",function(e){n.selectpicker.options.liveSearch?n.selectpicker.$searchbox.focus():n.selectpicker.$button.focus(),e.preventDefault(),e.stopPropagation(),x(this).is(".bs-select-all")?(null===n.selectpicker.$lis&&(n.selectpicker.$lis=n.selectpicker.$menu.find("li")),n.$element.find("option:enabled").prop("selected",!0),x(n.selectpicker.$lis).not(".disabled").addClass("selected")):(null===n.selectpicker.$lis&&(n.selectpicker.$lis=n.selectpicker.$menu.find("li")),n.$element.find("option:enabled").prop("selected",!1),x(n.selectpicker.$lis).not(".disabled").removeClass("selected")),n.selectpicker.render(),n.selectpicker.$element.change()}),this.selectpicker.$searchbox.attr("placeholder",this.t("searchPlaceholder")).off("input propertychange"),this.selectpicker.$searchbox.on(this.options.bindEvent,function(e){var t=n.selectpicker.$searchbox.val();if(n.log(n.LOG_DEBUG,'Bind event fired: "'+n.options.bindEvent+'", keyCode:',e.keyCode,e),n.options.cache||(n.options.ignoredKeys[13]="enter"),n.options.ignoredKeys[e.keyCode])n.log(n.LOG_DEBUG,"Key ignored.");else if(t.length<n.options.minLength)n.list.setStatus(n.t("statusTooShort"));else if(clearTimeout(s),t.length||(n.options.clearOnEmpty&&n.list.destroy(),n.options.emptyRequest)){if(n.previousQuery=n.query,n.query=t,n.options.cache&&13!==e.keyCode){var i=n.list.cacheGet(n.query);if(i)return n.list.setStatus(i.length?"":n.t("statusNoResults")),n.list.replaceOptions(i),void n.log(n.LOG_INFO,"Rebuilt options from cached data.")}s=setTimeout(function(){n.lastRequest&&n.lastRequest.jqXHR&&x.isFunction(n.lastRequest.jqXHR.abort)&&n.lastRequest.jqXHR.abort(),n.request=new I.AjaxBootstrapSelectRequest(n),n.request.jqXHR.always(function(){n.lastRequest=n.request,n.request=!1})},n.options.requestDelay||300)}})},e.prototype.log=function(e,t){if(I.console&&this.options.log){if("number"!=typeof this.options.log)switch("string"==typeof this.options.log&&(this.options.log=this.options.log.toLowerCase()),this.options.log){case!0:case"debug":this.options.log=this.LOG_DEBUG;break;case"info":this.options.log=this.LOG_INFO;break;case"warn":case"warning":this.options.log=this.LOG_WARNING;break;default:case!1:case"error":this.options.log=this.LOG_ERROR}if(e<=this.options.log){var i=[].slice.apply(arguments,[2]);switch(e){case this.LOG_DEBUG:e="debug";break;case this.LOG_INFO:e="info";break;case this.LOG_WARNING:e="warn";break;default:case this.LOG_ERROR:e="error"}var s="["+e.toUpperCase()+"] AjaxBootstrapSelect:";"string"==typeof t?i.unshift(s+" "+t):(i.unshift(t),i.unshift(s)),I.console[e].apply(I.console,i)}}},e.prototype.replaceValue=function(i,s,n,o){var l=this;o=x.extend({recursive:!0,depth:!1,limit:!1},o),x.each(i,function(e,t){return!(!1!==o.limit&&"number"==typeof o.limit&&o.limit<=0)&&void(x.isArray(i[e])||x.isPlainObject(i[e])?(o.recursive&&!1===o.depth||o.recursive&&"number"==typeof o.depth&&0<o.depth)&&l.replaceValue(i[e],s,n,o):t===s&&(!1!==o.limit&&"number"==typeof o.limit&&o.limit--,i[e]=n))})},e.prototype.t=function(e,t){return t=t||this.options.langCode,this.locale[t]&&this.locale[t].hasOwnProperty(e)?this.locale[t][e]:(this.log(this.LOG_WARNING,"Unknown translation key:",e),e)},I.AjaxBootstrapSelect=I.AjaxBootstrapSelect||e;var t=function(i){var s=this;this.$status=x(i.options.templates.status).hide().appendTo(i.selectpicker.$menu);var e=i.t("statusInitialized");e&&e.length&&this.setStatus(e),this.cache={},this.plugin=i,this.selected=[],this.title=null,this.selectedTextFormat=i.selectpicker.options.selectedTextFormat;var n=[];i.$element.find("option").each(function(){var e=x(this),t=e.attr("value");n.push({value:t,text:e.text(),class:e.attr("class")||"",data:e.data()||{},preserved:i.options.preserveSelected,selected:!!e.attr("selected")})}),this.cacheSet("",n),i.options.preserveSelected&&(s.selected=n,i.$element.on("change.abs.preserveSelected",function(e){var t=i.$element.find(":selected");s.selected=[],i.selectpicker.multiple||(t=t.last()),t.each(function(){var e=x(this),t=e.attr("value");s.selected.push({value:t,text:e.text(),class:e.attr("class")||"",data:e.data()||{},preserved:!0,selected:!0})}),s.replaceOptions(s.cacheGet(s.plugin.query))}))};t.prototype.build=function(e){var t,i,s=e.length,n=x("<select/>"),o=x("<optgroup/>").attr("label",this.plugin.t("currentlySelected"));for(this.plugin.log(this.plugin.LOG_DEBUG,"Building the select list options from data:",e),i=0;i<s;i++){var l=e[i],r=x("<option/>").appendTo(l.preserved?o:n);if(l.hasOwnProperty("divider"))r.attr("data-divider","true");else for(t in r.val(l.value).text(l.text).attr("title",l.text),l.class.length&&r.attr("class",l.class),l.disabled&&r.attr("disabled",!0),l.selected&&!this.plugin.selectpicker.multiple&&n.find(":selected").prop("selected",!1),l.selected&&r.attr("selected",!0),l.data)l.data.hasOwnProperty(t)&&r.attr("data-"+t,l.data[t])}o.find("option").length&&o["before"===this.plugin.options.preserveSelectedPosition?"prependTo":"appendTo"](n);var a=n.html();return this.plugin.log(this.plugin.LOG_DEBUG,a),a},t.prototype.cacheGet=function(e,t){var i=this.cache[e]||t;return this.plugin.log(this.LOG_DEBUG,"Retrieving cache:",e,i),i},t.prototype.cacheSet=function(e,t){this.cache[e]=t,this.plugin.log(this.LOG_DEBUG,"Saving to cache:",e,t)},t.prototype.destroy=function(){this.replaceOptions(),this.plugin.list.setStatus(),this.plugin.log(this.plugin.LOG_DEBUG,"Destroyed select list.")},t.prototype.refresh=function(e){this.plugin.selectpicker.$menu.css("minHeight",0),this.plugin.selectpicker.$menu.find("> .inner").css("minHeight",0);var t=this.plugin.t("emptyTitle");!this.plugin.$element.find("option").length&&t&&t.length?this.setTitle(t):(this.title||"static"!==this.selectedTextFormat&&this.selectedTextFormat!==this.plugin.selectpicker.options.selectedTextFormat)&&this.restoreTitle(),this.plugin.selectpicker.refresh(),this.plugin.selectpicker.findLis(),e&&(this.plugin.log(this.plugin.LOG_DEBUG,"Triggering Change"),this.plugin.$element.trigger("change.$")),this.plugin.log(this.plugin.LOG_DEBUG,"Refreshed select list.")},t.prototype.replaceOptions=function(e){var t,i,s,n="",o=[],l=[],r=[];if(e=e||[],this.selected&&this.selected.length){for(this.plugin.log(this.plugin.LOG_INFO,"Processing preserved selections:",this.selected),i=(l=[].concat(this.selected,e)).length,t=0;t<i;t++)(s=l[t]).hasOwnProperty("value")&&-1===r.indexOf(s.value+"")?(r.push(s.value+""),o.push(s)):this.plugin.log(this.plugin.LOG_DEBUG,"Duplicate item found, ignoring.");e=o}e.length&&(n=this.plugin.list.build(e)),this.plugin.$element.html(n),this.refresh(),this.plugin.log(this.plugin.LOG_DEBUG,"Replaced options with data:",e)},t.prototype.restore=function(){var e=this.plugin.list.cacheGet(this.plugin.previousQuery);return e&&this.plugin.list.replaceOptions(e)&&this.plugin.log(this.plugin.LOG_DEBUG,"Restored select list to the previous query: ",this.plugin.previousQuery),this.plugin.log(this.plugin.LOG_DEBUG,"Unable to restore select list to the previous query:",this.plugin.previousQuery),!1},t.prototype.restoreTitle=function(){this.plugin.request||(this.plugin.selectpicker.options.selectedTextFormat=this.selectedTextFormat,this.title?this.plugin.$element.attr("title",this.title):this.plugin.$element.removeAttr("title"),this.title=null)},t.prototype.setTitle=function(e){this.plugin.request||(this.title=this.plugin.$element.attr("title"),this.plugin.selectpicker.options.selectedTextFormat="static",this.plugin.$element.attr("title",e))},t.prototype.setStatus=function(e){(e=e||"").length?this.$status.html(e).show():this.$status.html("").hide()},I.AjaxBootstrapSelectList=I.AjaxBootstrapSelectList||t;var i=function(e){var t,i=this,s=function(e){return function(){i.plugin.log(i.plugin.LOG_INFO,"Invoking AjaxBootstrapSelectRequest."+e+" callback:",arguments),i[e].apply(i,arguments),i.callbacks[e]&&(i.plugin.log(i.plugin.LOG_INFO,"Invoking ajax."+e+" callback:",arguments),i.callbacks[e].apply(i,arguments))}},n=["beforeSend","success","error","complete"],o=n.length;for(this.plugin=e,this.options=x.extend(!0,{},e.options.ajax),this.callbacks={},t=0;t<o;t++){var l=n[t];this.options[l]&&x.isFunction(this.options[l])&&(this.callbacks[l]=this.options[l]),this.options[l]=s(l)}this.options.data&&x.isFunction(this.options.data)&&(this.options.data=this.options.data.apply(this)||{q:"{{{q}}}"}),this.plugin.replaceValue(this.options.data,"{{{q}}}",this.plugin.query),this.jqXHR=x.ajax(this.options)};i.prototype.beforeSend=function(e){this.plugin.list.destroy(),this.plugin.list.setStatus(this.plugin.t("statusSearching"))},i.prototype.complete=function(e,t){if("abort"!==t){var i=this.plugin.list.cacheGet(this.plugin.query);if(i){if(!i.length)return this.plugin.list.destroy(),this.plugin.list.setStatus(this.plugin.t("statusNoResults")),void this.plugin.log(this.plugin.LOG_INFO,"No results were returned.");this.plugin.list.setStatus()}this.plugin.list.refresh(!0)}},i.prototype.error=function(e,t,i){"abort"!==t&&(this.plugin.list.cacheSet(this.plugin.query),this.plugin.options.clearOnError&&this.plugin.list.destroy(),this.plugin.list.setStatus(this.plugin.t("errorText")),this.plugin.options.restoreOnError&&(this.plugin.list.restore(),this.plugin.list.setStatus()))},i.prototype.process=function(e){var t,i,s,n,o,l,r=[],a=[];if(this.plugin.log(this.plugin.LOG_INFO,"Processing raw data for:",this.plugin.query,e),o=e,x.isFunction(this.plugin.options.preprocessData)&&(this.plugin.log(this.plugin.LOG_DEBUG,"Invoking preprocessData callback:",this.plugin.options.processData),null!=(s=this.plugin.options.preprocessData.apply(this,[o]))&&!1!==s&&(o=s)),!x.isArray(o))return this.plugin.log(this.plugin.LOG_ERROR,'The data returned is not an Array. Use the "preprocessData" callback option to parse the results and construct a proper array for this plugin.',o),!1;for(i=o.length,t=0;t<i;t++)n=o[t],this.plugin.log(this.plugin.LOG_DEBUG,"Processing item:",n),x.isPlainObject(n)&&(n.hasOwnProperty("divider")||n.hasOwnProperty("data")&&x.isPlainObject(n.data)&&n.data.divider?(this.plugin.log(this.plugin.LOG_DEBUG,"Item is a divider, ignoring provided data."),r.push({divider:!0})):n.hasOwnProperty("value")?-1===a.indexOf(n.value+"")?(a.push(n.value+""),n=x.extend({text:n.value,class:"",data:{},disabled:!1,selected:!1},n),r.push(n)):this.plugin.log(this.plugin.LOG_DEBUG,"Duplicate item found, ignoring."):this.plugin.log(this.plugin.LOG_DEBUG,'Data item must have a "value" property, skipping.'));if(l=[].concat(r),x.isFunction(this.plugin.options.processData)&&(this.plugin.log(this.plugin.LOG_DEBUG,"Invoking processData callback:",this.plugin.options.processData),null!=(s=this.plugin.options.processData.apply(this,[l]))&&!1!==s)){if(!x.isArray(s))return this.plugin.log(this.plugin.LOG_ERROR,"The processData callback did not return an array.",s),!1;l=s}return this.plugin.list.cacheSet(this.plugin.query,l),this.plugin.log(this.plugin.LOG_INFO,"Processed data:",l),l},i.prototype.success=function(e,t,i){if(!x.isArray(e)&&!x.isPlainObject(e))return this.plugin.log(this.plugin.LOG_ERROR,"Request did not return a JSON Array or Object.",e),void this.plugin.list.destroy();var s=this.process(e);this.plugin.list.replaceOptions(s)},I.AjaxBootstrapSelectRequest=I.AjaxBootstrapSelectRequest||i,x.fn.ajaxSelectPicker=function(e){return this.each(function(){x(this).data("AjaxBootstrapSelect")||x(this).data("AjaxBootstrapSelect",new I.AjaxBootstrapSelect(this,e))})},x.fn.ajaxSelectPicker.locale={},x.fn.ajaxSelectPicker.defaults={ajax:{url:null,type:"POST",dataType:"json",data:{q:"{{{q}}}"}},minLength:0,bindEvent:"keyup",cache:!0,clearOnEmpty:!0,clearOnError:!0,emptyRequest:!1,ignoredKeys:{9:"tab",16:"shift",17:"ctrl",18:"alt",27:"esc",37:"left",39:"right",38:"up",40:"down",91:"meta"},langCode:null,locale:null,log:"error",preprocessData:function(){},preserveSelected:!0,preserveSelectedPosition:"after",processData:function(){},requestDelay:300,restoreOnError:!1,templates:{status:'<div class="status"></div>'}},x.fn.ajaxSelectPicker.locale["en-US"]={currentlySelected:"Currently Selected",emptyTitle:"Select and begin typing",errorText:"Unable to retrieve results",searchPlaceholder:"Search...",statusInitialized:"Start typing a search query",statusNoResults:"No Results",statusSearching:"Searching...",statusTooShort:"Please enter more characters"},x.fn.ajaxSelectPicker.locale.en=x.fn.ajaxSelectPicker.locale["en-US"]}(jQuery,window);
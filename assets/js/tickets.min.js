function insert_ticket_knowledgebase_link(t){var e=$(t).val();""!=e&&requestGetJSON("knowledge_base/get_article_by_id_ajax/"+e).done(function(e){$('textarea[name="message"]');tinymce.activeEditor.execCommand("mceInsertContent",!1,'<a href="'+site_url+"knowledge_base/"+e.slug+'">'+e.subject+"</a>"),$(t).selectpicker("val","")})}function tickets_bulk_action(e){if(confirm_delete()){var t=$("#mass_delete").prop("checked"),a=$("#merge_tickets").prop("checked"),i=[],n={};if(void 0!==a&&1==a){if(n.merge_tickets=!0,n.primary_ticket=$("#primary_ticket_id").val(),n.primary_ticket_status=$("#primary_ticket_status").val(),""==n.primary_ticket)return void console.log("empty")}else if(0==t||void 0===t){if(n.status=$("#move_to_status_tickets_bulk").val(),n.department=$("#move_to_department_tickets_bulk").val(),n.priority=$("#move_to_priority_tickets_bulk").val(),n.service=$("#move_to_service_tickets_bulk").val(),n.tags=$("#tags_bulk").tagit("assignedTags"),""==n.status&&""==n.department&&""==n.priority&&""==n.service&&""==n.tags)return}else n.mass_delete=!0;var r=$(".table-tickets").find("tbody tr");$.each(r,function(){var e=$($(this).find("td").eq(0)).find("input");1==e.prop("checked")&&i.push(e.val())}),n.ids=i,$(e).addClass("disabled"),setTimeout(function(){$.post(admin_url+"tickets/bulk_action",n).done(function(){window.location.reload()})},50)}}function show_ticket_no_contact_email_warning(e,t){0==$("#contact_email_notifications_warning").length&&$("#new_ticket_form, #single-ticket-form").prepend('<div class="alert alert-warning" id="contact_email_notifications_warning">Email notifications for tickets is disabled for this contact, if you want the contact to receive ticket emails you must enable by clicking <a href="'+admin_url+"clients/client/"+e+"?contactid="+t+'" target="_blank">here</a>.</div>')}function clear_ticket_no_contact_email_warning(){$("#contact_email_notifications_warning").remove()}function validate_new_ticket_form(){$("#new_ticket_form").appFormValidator(),setTimeout(function(){$.each($("#new_ticket_form").find('[data-custom-field-required="1"]'),function(){$(this).rules("add","required")})},10)}$(function(){$("#tickets_bulk_actions").on("show.bs.modal",function(){$("#primary_ticket_id").find("option").remove().end().append("<option></option>"),$("#merge_tickets").prop("checked",!1),$("#merge_tickets").trigger("change")}),$("#merge_tickets").on("change",function(){var e=$(this).prop("checked"),t=$("#bulk_change"),a=$("#primary_ticket_id"),i=$(".table-tickets").find("tbody tr");a.find("option").remove().end().append("<option></option>"),e?($("#bulk_change").addClass("hide"),$("#merge_tickets_wrapper").removeClass("hide"),$(".mass_delete_checkbox").addClass("hide"),$("#mass_delete").prop("checked",!1),t.addClass("hide"),$.each(i,function(){var e=$($(this).find("td").eq(0)).find("input");1==e.prop("checked")&&a.append('<option value="'+e.val()+'" data-status="'+e.data("status")+'">'+e.data("name")+"</option")}),a.selectpicker("refresh")):($("#merge_tickets_wrapper").addClass("hide"),t.removeClass("hide"),$(".mass_delete_checkbox").removeClass("hide"))}),$("#primary_ticket_id").on("change",function(){var e=$(this).find("option:selected").data("status");$("#primary_ticket_status").selectpicker("val",e)}),$("#insert_predefined_reply").on("change",function(e){e.preventDefault();var t=$(this),a=t.val();""!=a&&requestGetJSON("tickets/get_predefined_reply_ajax/"+a).done(function(e){tinymce.activeEditor.execCommand("mceInsertContent",!1,e.message),t.selectpicker("val","")})}),$("#ticket_no_contact").on("click",function(e){e.preventDefault(),validate_new_ticket_form(),$("#name, #email").prop("disabled",!1),$("#name").val("").rules("add",{required:!0}),$("#email").val("").rules("add",{required:!0}),$(this).addClass("hide"),$("#contactid").removeAttr("required"),$("#contactid").selectpicker("val",""),$('input[name="userid"]').val(""),$("#ticket_to_contact").removeClass("hide"),$("#ticket_contact_w").addClass("hide")}),$("#ticket_to_contact").on("click",function(e){e.preventDefault(),$("#name, #email").prop("disabled",!0),$("#ticket_no_contact").removeClass("hide"),$("#contactid").attr("required",!0),$("#name").rules("remove","required"),$("#email").rules("remove","required"),$("#ticket_no_contact, #ticket_contact_w").removeClass("hide"),$(this).addClass("hide")}),$(".block-sender").on("click",function(){var e=$(this).data("sender");if(""==e)return alert("No Sender Found"),!1;$.post(admin_url+"tickets/block_sender",{sender:e}).done(function(){window.location.reload()})}),$(".add_note_ticket").on("click",function(e){e.preventDefault();var t=$('textarea[name="note_description"]').val(),a=$('input[name="ticketid"]').val();""!=t&&($(e.target).addClass("disabled"),$.post(admin_url+"misc/add_note/"+a+"/ticket",{description:t}).done(function(){window.location.reload()}))}),$(".save_changes_settings_single_ticket").on("click",function(e){e.preventDefault();var t={},a=$("#settings"),i=!1;""==a.find('input[name="subject"]').val()?(i=!0,a.find('input[name="subject"]').parents(".form-group").addClass("has-error")):a.find('input[name="subject"]').parents(".form-group").removeClass("has-error");var n=["department","priority"];1!=$("#contactid").data("no-contact")&&n.push("contactid");for(var r=0;r<n.length;r++){var c=a.find('select[name="'+n[r]+'"]');""==c.selectpicker("val")?(i=!0,c.parents(".form-group").addClass("has-error")):c.parents(".form-group").removeClass("has-error")}var s=a.find('[data-custom-field-required="1"]');$.each(s,function(){var e=$(this),t=e.parents(".form-group");e.is(":checkbox")?0==t.find('input[type="checkbox"]:checked').length?(i=!0,t.addClass("has-error")):t.removeClass("has-error"):e.is("input")||e.is("textarea")?""===e.val()?(i=!0,t.addClass("has-error")):t.removeClass("has-error"):e.is("select")&&(""==e.selectpicker("val")?(i=!0,t.addClass("has-error")):t.removeClass("has-error"))}),1!=i&&(t=$("#settings *").serialize(),t+="&ticketid="+$('input[name="ticketid"]').val(),"undefined"!=typeof csrfData&&(t+="&"+csrfData.token_name+"="+csrfData.hash),$.post(admin_url+"tickets/update_single_ticket_settings",t).done(function(e){1==(e=JSON.parse(e)).success?void 0!==e.department_reassigned?window.location.href=admin_url+"tickets/":window.location.reload():void 0!==e.message&&alert_float("warning",e.message)}))}),$("#new_ticket_form").submit(function(){return $("#project_id").prop("disabled",!1),!0}),$('select[name="status_top"]').on("change",function(){var t=$(this),a=t.val();if((prevStatus=t.data("status"),5==a)&&0==confirm("Are you sure you want to mask this ticket as closed?"))return $(t).val(prevStatus),$(t).selectpicker("refresh"),!1;var e=$('input[name="ticketid"]').val();requestGetJSON("tickets/change_status_ajax/"+e+"/"+a).done(function(e){t.data("status",a),alert_float(e.alert,e.message)})}),$('body.ticket select[name="contactid"]').on("change",function(){var e,t=$(this).val(),a=$('select[name="project_id"]'),i=a.attr("data-auto-project"),n=$(".projects-wrapper");a.attr("disabled")||(i?(e=a.clone()).prop("disabled",!0):e=a.html("").clone(),a.selectpicker("destroy").remove(),a=e,$("#project_ajax_search_wrapper").append(e),init_ajax_search("project",a,{customer_id:function(){return $('input[name="userid"]').val()}}));""!=t?$.post(admin_url+"tickets/ticket_change_data/",{contact_id:t}).done(function(e){(e=JSON.parse(e)).contact_data&&($('input[name="name"]').val(e.contact_data.firstname+" "+e.contact_data.lastname),$('input[name="email"]').val(e.contact_data.email),$('input[name="userid"]').val(e.contact_data.userid),"0"==e.contact_data.ticket_emails?show_ticket_no_contact_email_warning(e.contact_data.userid,e.contact_data.id):clear_ticket_no_contact_email_warning()),i?n.removeClass("hide"):e.customer_has_projects?n.removeClass("hide"):n.addClass("hide")}):($('input[name="name"]').val(""),$('input[name="email"]').val(""),$('input[name="contactid"]').val(""),i?n.removeClass("hide"):n.addClass("hide"),clear_ticket_no_contact_email_warning())})});
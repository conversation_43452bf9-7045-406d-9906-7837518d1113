$.fn.dataTable.ext.type.order["task-status-pre"]=function(e){switch(e){case"2":return 1;case"4":return 2;case"3":return 3;case"1":return 4;case"5":return 6}return 5};var salesChart,project_id=$('input[name="project_id"]').val(),discussion_user_profile_image_url=$('input[name="discussion_user_profile_image_url"]').val(),discussion_id=$('input[name="discussion_id"]').val();function new_discussion(){$("#discussion").modal("show"),$("#discussion .edit-title").addClass("hide")}function manage_discussion(e){var t=$(e).serialize(),a=e.action;return $.post(a,t).done(function(e){1==(e=JSON.parse(e)).success&&alert_float("success",e.message),$(".table-project-discussions").DataTable().ajax.reload(null,!1),$("#discussion").modal("hide")}),!1}function remove_task_comment(e){$.get(site_url+"clients/remove_task_comment/"+e,function(e){1==e.success&&window.location.reload()},"json")}function edit_task_comment(e){$('[data-edit-comment="'+e+'"]').removeClass("hide"),$('[data-comment-content="'+e+'"]').addClass("hide")}function cancel_edit_comment(e){$('[data-edit-comment="'+e+'"]').addClass("hide"),$('[data-comment-content="'+e+'"]').removeClass("hide")}function save_edited_comment(t){var e={};e.id=t,e.content=$('[data-edit-comment="'+t+'"]').find("textarea").val(),$.post(site_url+"clients/edit_comment",e).done(function(e){1==(e=JSON.parse(e)).success?window.location.reload():cancel_edit_comment(t)})}function initDataTable(){appDataTableInline()}function dt_custom_view(e,t,a){var n=$(e).DataTable();Array.isArray(a)?n.column(t).search(a.join("|"),!0,!1).draw():n.column(t).search(a).draw()}function fix_phases_height(){if(!is_mobile()){var e=Math.max.apply(null,$("div.tasks-phases .panel-body").map(function(){return $(this).outerHeight()}).get());$("div.tasks-phases .panel-body").css("min-height",e+"px")}}function taskTable(){$(".tasks-table").toggleClass("hide"),$(".tasks-phases").toggleClass("hide")}function discussion_comments(e,r,c){var t=_get_jquery_comments_default_config(app.lang.discussions_lang),a={getComments:function(t,e){$.post(site_url+"clients/project/"+project_id,{action:"discussion_comments",discussion_id:r,discussion_type:c}).done(function(e){e=JSON.parse(e),t(e)})},postComment:function(e,t,a){e.action="new_discussion_comment",e.discussion_id=r,e.discussion_type=c,$.ajax({type:"post",url:site_url+"clients/project/"+project_id,data:e,success:function(e){e=JSON.parse(e),t(e)},error:a})},putComment:function(e,t,a){e.action="update_discussion_comment",$.ajax({type:"post",url:site_url+"clients/project/"+project_id,data:e,success:function(e){e=JSON.parse(e),t(e)},error:a})},deleteComment:function(e,t,a){$.ajax({type:"post",url:site_url+"clients/project/"+project_id,success:t,error:a,data:{id:e.id,action:"delete_discussion_comment"}})},uploadAttachments:function(e,t,a){var n=0,i=[],s=function(){++n==e.length&&(0==i.length?a():(i=JSON.parse(i),t(i)))};$(e).each(function(e,n){if(n.file.size&&n.file.size>app.max_php_ini_upload_size_bytes)alert_float("danger",app.lang.file_exceeds_max_filesize),s();else{var o=new FormData;$(Object.keys(n)).each(function(e,t){var a=n[t];a&&o.append(t,a)}),o.append("action","new_discussion_comment"),o.append("discussion_id",r),o.append("discussion_type",c),"undefined"!=typeof csrfData&&o.append(csrfData.token_name,csrfData.hash),$.ajax({url:site_url+"clients/project/"+project_id,type:"POST",data:o,cache:!1,contentType:!1,processData:!1,success:function(e){i.push(e),s()},error:function(e){var t=JSON.parse(e.responseText);alert_float("danger",t.message),s()}})}})}},n=$.extend({},t,a);$(e).comments(n)}function view_project_file(e,t){$.post(site_url+"clients/project/"+t,{action:"get_file",id:e,project_id:t}).done(function(e){$("#project_file_data").html(e)}).fail(function(e){alert_float("danger",e.statusText)})}function update_file_data(e){var t={};t.id=e,t.subject=$('body input[name="file_subject"]').val(),t.description=$('body textarea[name="file_description"]').val(),t.action="update_file_data",$.post(site_url+"clients/project/"+project_id,t)}function render_customer_statement(){var e=$("#range").selectpicker("val"),t=new Array,a=!1;if("period"!=e)t=JSON.parse(e);else{if(t[0]=$('input[name="period-from"]').val(),t[1]=$('input[name="period-to"]').val(),""==t[0]||""==t[1])return!1;a=!0}var n=site_url+"clients/statement",o=new Array;o.from=t[0],o.to=t[1],a&&(o.custom_period=!0),window.location.href=buildUrl(n,o)}function client_home_chart(){var t=$("#client-home-chart");if(0!=t.length){void 0!==salesChart&&salesChart.destroy();var e={},a=$("#currency"),n=$("#payments_year");0<a.length&&(e.report_currency=$('select[name="currency"]').val()),0<n.length&&(e.year=$("#payments_year").val()),$.post(site_url+"clients/client_home_chart",e).done(function(e){e=JSON.parse(e),salesChart=new Chart(t,{type:"bar",data:e,options:{responsive:!0,maintainAspectRatio:!1}})})}}function projectFileGoogleDriveSave(e){projectExternalFileUpload(e,"gdrive")}function customerFileGoogleDriveSave(e){customerExternalFileUpload(e,"gdrive")}function taskFileGoogleDriveSave(e){taskExternalFileUpload(e,"gdrive")}function projectExternalFileUpload(e,t){$.post(site_url+"clients/project/"+project_id,{files:e,external:t,action:"project_external_file"}).done(function(){var e=window.location.href;window.location.href=e.split("?")[0]+"?group=project_files"})}function taskExternalFileUpload(e,t){$.post(site_url+"clients/project/"+project_id,{files:e,task_id:$('input[name="task_id"]').val(),external:t,action:"add_task_external_file"}).done(function(){window.location.reload()})}function customerExternalFileUpload(e,t){$.post(site_url+"clients/upload_files",{files:e,external:t}).done(function(){window.location.reload()})}function task_attachments_toggle(){var e=$("#task").find(".task_attachments_wrapper");e.find(".task-attachments-more").toggleClass("hide"),e.find(".task-attachments-less").toggleClass("hide")}Dropzone.options.projectFilesUpload=!1,Dropzone.options.taskFileUpload=!1,Dropzone.options.filesUpload=!1,"1"==app.options.enable_google_picker&&($.fn.googleDrivePicker.defaults.clientId=app.options.google_client_id,$.fn.googleDrivePicker.defaults.developerKey=app.options.google_api),$(function(){moment.locale(app.locale),moment().tz(app.options.timezone).format(),fix_phases_height(),initDataTable(),client_home_chart(),$('select[name="currency"],select[name="payments_years"]').on("change",function(){client_home_chart()}),$("#open-new-ticket-form").appFormValidator(),$("#ticket-reply").appFormValidator(),$("#task-form").appFormValidator(),$("#discussion_form").appFormValidator({rules:{subject:"required"}});var e=get_url_param("file_id");if(e&&view_project_file(e,project_id),discussion_comments("#discussion-comments",discussion_id,"regular"),$("body").on("show.bs.modal","._project_file",function(){discussion_comments("#project-file-discussion",discussion_id,"file")}),"undefined"!=typeof Dropbox&&(0<$("#dropbox-chooser-task").length&&document.getElementById("dropbox-chooser-task").appendChild(Dropbox.createChooseButton({success:function(e){taskExternalFileUpload(e,"dropbox")},linkType:"preview",extensions:app.options.allowed_files.split(",")})),0<$("#files-upload").length&&document.getElementById("dropbox-chooser-files").appendChild(Dropbox.createChooseButton({success:function(e){customerExternalFileUpload(e,"dropbox")},linkType:"preview",extensions:app.options.allowed_files.split(",")})),"undefined"!=typeof Dropbox&&0<$("#dropbox-chooser-project-files").length&&document.getElementById("dropbox-chooser-project-files").appendChild(Dropbox.createChooseButton({success:function(e){projectExternalFileUpload(e,"dropbox")},linkType:"preview",extensions:app.options.allowed_files.split(",")}))),$("#calendar").length){var t={headerToolbar:{left:"prev,next today",center:"title",right:"dayGridMonth,timeGridWeek,timeGridDay"},editable:!1,dayMaxEventRows:parseInt(app.options.calendar_events_limit)+1,views:{day:{dayMaxEventRows:!1}},initialView:app.options.default_view_calendar,moreLinkClick:function(e){a.gotoDate(e.date),a.changeView("dayGridDay"),setTimeout(function(){$(".fc-popover-close").click()},250)},loading:function(e,t){e?$(".dt-loader").removeClass("hide"):$(".dt-loader").addClass("hide")},direction:"true"==isRTL?"rtl":"ltr",eventStartEditable:!1,firstDay:parseInt(app.options.calendar_first_day),events:function(e,t,a){return $.getJSON(site_url+"clients/get_calendar_data",{start:e.startStr,end:e.endStr}).then(function(e){t(e.map(function(e){return $.extend({},e,{start:e.start||e.date,end:e.end||e.date})}))})},eventDidMount:function(e){var t=$(e.el);t.attr("title",e.event.extendedProps._tooltip),t.attr("onclick",e.event.extendedProps.onclick),t.attr("data-toggle","tooltip")}},a=new FullCalendar.Calendar(document.getElementById("calendar"),t);a.render()}var n=get_url_param("group");n&&($("body").find(".nav-tabs li").removeClass("active"),$("body").find('.nav-tabs [data-group="'+n+'"]').parents("li").addClass("active"));for(var o=-10;o<$(".task-phase").not(".color-not-auto-adjusted").length/2;o++){$(".task-phase:eq("+(o+10)+")").not(".color-not-auto-adjusted").css("background",color(120-13*o,169-13*o,56-13*o)).css("border","1px solid "+color(120-13*o,169-13*o,56-13*o))}$(".project-progress").circleProgress({fill:{gradient:["#84c529","#84c529"]}}).on("circle-animation-progress",function(e,t,a){$(this).find("strong.project-percent").html(parseInt(100*a)+"<i>%</i>")});$(".toggle-change-ticket-status").on("click",function(){$(".ticket-status,.ticket-status-inline").toggleClass("hide")}),$("#ticket_status_single").on("change",function(){data={},data.status_id=$(this).val(),data.ticket_id=$('input[name="ticket_id"]').val(),$.post(site_url+"clients/change_ticket_status/",data).done(function(){window.location.reload()})}),"undefined"!=typeof contracts_by_type&&new Chart($("#contracts-by-type-chart"),{type:"bar",data:JSON.parse(contracts_by_type),options:{responsive:!0,maintainAspectRatio:!1,scales:{yAxes:[{display:!0,ticks:{beginAtZero:!0}}]}}}),0<$("#files-upload").length&&createDropzone("#files-upload"),0<$("#task-file-upload").length&&createDropzone("#task-file-upload",{sending:function(e,t,a){a.append("action","upload_task_file"),a.append("task_id",$('input[name="task_id"]').val())}}),0<$("#project-files-upload").length&&createDropzone("#project-files-upload",{sending:function(e,t,a){a.append("action","upload_file")}}),$('body.viewinvoice input[name="amount"]').on("keyup",function(){var e=$(this).data("total"),t=$(this).val(),a=$(this).parents(".form-group");e<t?(a.addClass("has-error"),0==a.find("p.text-danger").length&&(a.append('<p class="text-danger">Maximum pay value passed</p>'),$(this).parents("form").find('input[name="make_payment"]').attr("disabled",!0))):(a.removeClass("has-error"),a.find("p.text-danger").remove(),$(this).parents("form").find('input[name="make_payment"]').attr("disabled",!1))}),$("#discussion").on("hidden.bs.modal",function(e){$('#discussion input[name="subject"]').val(""),$('#discussion textarea[name="description"]').val(""),$("#discussion .add-title").removeClass("hide"),$("#discussion .edit-title").removeClass("hide")})});
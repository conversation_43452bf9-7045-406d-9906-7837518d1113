function createDropzone(e,t){var a=appCreateDropzoneOptions({paramName:"file",uploadMultiple:!0,parallelUploads:20,maxFiles:20,accept:function(e,t){t()},success:function(e,t){0===this.getUploadingFiles().length&&0===this.getQueuedFiles().length&&window.location.reload()}}),o=$.extend({},a,t);new Dropzone(e,o)}function change_contact_language(e){var t="authentication/change_language/"+$(e).val();window.location.href=site_url+t}$.fn.appFormValidator.internal_options.on_required_add_symbol=!1,$(function(){$("body").on("shown.bs.dropdown",".btn-group",function(){$(this).closest(".table-responsive").css("overflow","inherit")}),$("body").on("hidden.bs.dropdown",".btn-group",function(){$(this).closest(".table-responsive").css("overflow","auto")}),appSelectPicker($("body").find("select")),appProgressBar(),appLightbox(),appColorPicker(),appDatepicker(),$.each($(".kb-article").find("img"),function(){$(this).parent().is("a")||$(this).wrap('<a href="'+$(this).attr("src")+'" data-lightbox="kb-attachment"></a>')}),$("body").tooltip({selector:'[data-toggle="tooltip"]'}),$("body").popover({selector:'[data-toggle="popover"]'}),$(".article_useful_buttons button").on("click",function(e){e.preventDefault();var t={};t.answer=$(this).data("answer"),t.articleid=$('input[name="articleid"]').val(),$.post(site_url+"knowledge_base/add_kb_answer",t).done(function(e){1==(e=JSON.parse(e)).success&&$(this).focusout(),$(".answer_response").html(e.message)})}),$("#identityConfirmationForm").appFormValidator({rules:{acceptance_firstname:"required",acceptance_lastname:"required",signature:"required",acceptance_email:{email:!0,required:!0}},messages:{signature:{required:app.lang.sign_document_validation}}}),$("body.identity-confirmation #accept_action").on("click",function(){var e=$("#identityConfirmationForm");return e.length&&!e.validate().checkForm()?$("#identityConfirmationModal").modal({show:!0,backdrop:"static",keyboard:!1}):($(this).prop("disabled",!0),e.submit()),!1})});
.btn-default {
  color: #415164;
  background-color: #F1F5F7;
  border-color: #E6E9EB;
}

.btn-default:hover,
.btn-default:focus,
.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
  color: #415164;
  background-color: #E1E4E6;
  border-color: #E6E9EB;
}

.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
  background-image: none;
}

.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
  background-color: #F1F5F7;
  border-color: #E6E9EB;
}

.btn-default .badge {
  color: #F1F5F7;
  background-color: #415164;
}

.btn-primary {
    color: #ffffff;
    background-color: #28b8da;
    border: 0px;
}

.btn-primary:focus,
.btn-primary.focus {
    color: #ffffff;
    background-color: #1e95b1;
}

.btn-primary:hover {
    color: #ffffff;
    background-color: #1e95b1;
}

.btn-primary:active,
.btn-primary.active,
.open>.dropdown-toggle.btn-primary {
    color: #ffffff;
    background-color: #1e95b1;
}

.btn-primary:active:hover,
.btn-primary.active:hover,
.open>.dropdown-toggle.btn-primary:hover,
.btn-primary:active:focus,
.btn-primary.active:focus,
.open>.dropdown-toggle.btn-primary:focus,
.btn-primary:active.focus,
.btn-primary.active.focus,
.open>.dropdown-toggle.btn-primary.focus {
    color: #ffffff;
    background-color: #197b92;
}

.btn-primary:active,
.btn-primary.active,
.open>.dropdown-toggle.btn-primary {
    background-image: none;
}

.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
    background-color: #28b8da;
}

.btn-primary .badge {
    color: #28b8da;
    background-color: #ffffff;
}

.btn-success {
    color: #FFFFFF;
    background-color: #84C529;
    border: 0px;
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success {
    color: #FFFFFF;
    background-color: #74B31B;
    border-color: #84C529;
}

.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success {
    background-image: none;
}

.btn-success.disabled,
.btn-success[disabled],
fieldset[disabled] .btn-success,
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled:active,
.btn-success[disabled]:active,
fieldset[disabled] .btn-success:active,
.btn-success.disabled.active,
.btn-success[disabled].active,
fieldset[disabled] .btn-success.active {
    background-color: #84C529;
    border-color: #84C529;
}

.btn-success .badge {
    color: #84C529;
    background-color: #FFFFFF;
}

.btn-info {
    color: #ffffff;
    background-color: #03a9f4;
    border: 0px;
}

.btn-info:focus,
.btn-info.focus {
    color: #ffffff;
    background-color: #0286c2;
}

.btn-info:hover {
    color: #ffffff;
    background-color: #0286c2;
}

.btn-info:active,
.btn-info.active,
.open>.dropdown-toggle.btn-info {
    color: #ffffff;
    background-color: #0286c2;
}

.btn-info:active:hover,
.btn-info.active:hover,
.open>.dropdown-toggle.btn-info:hover,
.btn-info:active:focus,
.btn-info.active:focus,
.open>.dropdown-toggle.btn-info:focus,
.btn-info:active.focus,
.btn-info.active.focus,
.open>.dropdown-toggle.btn-info.focus {
    color: #ffffff;
    background-color: #026e9e;
}

.btn-info:active,
.btn-info.active,
.open>.dropdown-toggle.btn-info {
    background-image: none;
}

.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled.focus,
.btn-info[disabled].focus,
fieldset[disabled] .btn-info.focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active {
    background-color: #03a9f4;
}

.btn-info .badge {
    color: #03a9f4;
}

.btn-warning {
    color: #ffffff;
    background-color: #ff6f00;
    border: 0px;
}

.btn-warning:focus,
.btn-warning.focus {
    color: #ffffff;
    background-color: #cc5900;
}

.btn-warning:hover {
    color: #ffffff;
    background-color: #cc5900;
}

.btn-warning:active,
.btn-warning.active,
.open>.dropdown-toggle.btn-warning {
    color: #ffffff;
    background-color: #cc5900;
}

.btn-warning:active:hover,
.btn-warning.active:hover,
.open>.dropdown-toggle.btn-warning:hover,
.btn-warning:active:focus,
.btn-warning.active:focus,
.open>.dropdown-toggle.btn-warning:focus,
.btn-warning:active.focus,
.btn-warning.active.focus,
.open>.dropdown-toggle.btn-warning.focus {
    color: #ffffff;
    background-color: #a84900;
}

.btn-warning:active,
.btn-warning.active,
.open>.dropdown-toggle.btn-warning {
    background-image: none;
}

.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled.focus,
.btn-warning[disabled].focus,
fieldset[disabled] .btn-warning.focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active {
    background-color: #ff6f00;
}

.btn-warning .badge {
    color: #ff6f00;
    background-color: #ffffff;
}

.btn-danger {
    color: #ffffff;
    background-color: #fc2d42;
    border: 0px;
}

.btn-danger:focus,
.btn-danger.focus {
    color: #ffffff;
    background-color: #f3031c;
}

.btn-danger:hover {
    color: #ffffff;
    background-color: #f3031c;
}

.btn-danger:active,
.btn-danger.active,
.open>.dropdown-toggle.btn-danger {
    color: #ffffff;
    background-color: #f3031c;
}

.btn-danger:active:hover,
.btn-danger.active:hover,
.open>.dropdown-toggle.btn-danger:hover,
.btn-danger:active:focus,
.btn-danger.active:focus,
.open>.dropdown-toggle.btn-danger:focus,
.btn-danger:active.focus,
.btn-danger.active.focus,
.open>.dropdown-toggle.btn-danger.focus {
    color: #ffffff;
    background-color: #cf0318;
}

.btn-danger:active,
.btn-danger.active,
.open>.dropdown-toggle.btn-danger {
    background-image: none;
}

.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger.focus,
.btn-danger.disabled:active,
.btn-danger[disabled]:active,
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger.active {
    background-color: #fc2d42;
    border-color: #fc142b;
}

.btn-danger .badge {
    color: #fc2d42;
    background-color: #ffffff;
}

.pagination>li:last-child>a,
.pagination>li:last-child>span {}

.pagination>li>a:hover,
.pagination>li>span:hover,
.pagination>li>a:focus,
.pagination>li>span:focus {
    color: #1b829b;
    background-color: #eeeeee;
    border-color: #dddddd;
}

.pagination>.active>a,
.pagination>.active>span,
.pagination>.active>a:hover,
.pagination>.active>span:hover,
.pagination>.active>a:focus,
.pagination>.active>span:focus {
    color: #ffffff;
    background-color: #03a9f4;
    border-color: #03a9f4;
}

.pagination>.disabled>span,
.pagination>.disabled>span:hover,
.pagination>.disabled>span:focus,
.pagination>.disabled>a,
.pagination>.disabled>a:hover,
.pagination>.disabled>a:focus {
    color: #777777;
    background-color: #ffffff;
    border-color: #dddddd;
}

.progress {
    background-color: #f9fafc;
}

.progress-bar {
    color: #ffffff;
    background-color: #28b8da;
}

.progress-striped .progress-bar,
.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 40px 40px;
}

.progress.active .progress-bar,
.progress-bar.active {
    -webkit-animation: progress-bar-stripes 2s linear infinite;
    animation: progress-bar-stripes 2s linear infinite;
}

.progress-bar-success {
    background-color: #84c529;
}

.progress-striped .progress-bar-success {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress-bar-info {
    background-color: #03a9f4;
}

.progress-striped .progress-bar-info {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress-bar-warning {
    background-color: #ff6f00;
}

.progress-striped .progress-bar-warning {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress-bar-danger {
    background-color: #fc2d42;
}

.progress-striped .progress-bar-danger {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress-bar-default {
    background-color: #727b86;
}

.label-default {
    background: transparent;
    border: 1px solid #727b86;
    color: #323A45;
}

.label-default[href]:hover,
.label-default[href]:focus {
    background-color: #727b86;
}

.label-light-green {
    background: transparent;
    border: 1px solid #adca65;
    color: #adca65;
}

.label-light-green[href]:hover,
.label-light-green[href]:focus {
    background-color: #adca65;
}

.label-primary {
    background: transparent;
    border: 1px solid #28b8da;
    color: #28b8da;
}

.label-primary[href]:hover,
.label-primary[href]:focus {
    background-color: #1e95b1;
}

.label-success {
    background: transparent;
    border: 1px solid #84c529;
    color: #84c529;
}

.label-success[href]:hover,
.label-success[href]:focus {
    background-color: #1b721a;
}

.label-info {
    background: transparent;
    border: 1px solid #03a9f4;
    color: #03a9f4;
}

.label-info[href]:hover,
.label-info[href]:focus {
    background-color: #0286c2;
}

.label-warning {
    background: transparent;
    border: 1px solid #ff6f00;
    color: #ff6f00;
}

.label-warning[href]:hover,
.label-warning[href]:focus {
    background-color: #cc5900;
}

.label-danger {
    background: transparent;
    border: 1px solid #fc2d42;
    color: #fc2d42;
}

.label-danger[href]:hover,
.label-danger[href]:focus {
    background-color: #f3031c;
}

.text-danger {
    color: #fc2d42;
}

.text-danger.important {
    color: #fc2d42 !important;
}

.text-info {
    color: #03a9f4;
}

.text-success {
    color: #84c529;
}

.text-warning {
    color: #ff6f00;
}

.has-error .checkbox,
.has-error .checkbox-inline,
.has-error .control-label,
.has-error .help-block,
.has-error .radio,
.has-error .radio-inline,
.has-error.checkbox label,
.has-error.checkbox-inline label,
.has-error.radio label,
.has-error.radio-inline label {
    color: #fc2d42;
}

.has-error .form-control {
    border-color: #fc2d42;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}

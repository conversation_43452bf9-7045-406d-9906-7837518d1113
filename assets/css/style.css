/*
    Admin Area CSS
*/
.btn-default {
    color: #415164;
    background-color: #F1F5F7;
    border-color: #E6E9EB;
}

.btn-default:hover,
.btn-default:focus,
.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
    color: #415164;
    background-color: #E1E4E6;
    border-color: #E6E9EB;
}

.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
    background-image: none;
}

.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
    background-color: #F1F5F7;
    border-color: #E6E9EB;
}

.btn-default .badge {
    color: #F1F5F7;
    background-color: #415164;
}

.btn-primary {
    color: #ffffff;
    background-color: #28b8da;
    border: 0;
}

.btn-primary:focus,
.btn-primary.focus {
    color: #ffffff;
    background-color: #1e95b1;
}

.btn-primary:hover {
    color: #ffffff;
    background-color: #1e95b1;
}

.btn-primary:active,
.btn-primary.active,
.open>.dropdown-toggle.btn-primary {
    color: #ffffff;
    background-color: #1e95b1;
}

.btn-primary:active:hover,
.btn-primary.active:hover,
.open>.dropdown-toggle.btn-primary:hover,
.btn-primary:active:focus,
.btn-primary.active:focus,
.open>.dropdown-toggle.btn-primary:focus,
.btn-primary:active.focus,
.btn-primary.active.focus,
.open>.dropdown-toggle.btn-primary.focus {
    color: #ffffff;
    background-color: #197b92;
}

.btn-primary:active,
.btn-primary.active,
.open>.dropdown-toggle.btn-primary {
    background-image: none;
}

.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
    background-color: #28b8da;
}

.btn-primary .badge {
    color: #28b8da;
    background-color: #ffffff;
}

.btn-success {
    color: #FFFFFF;
    background-color: #84C529;
    border: 0;
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success {
    color: #FFFFFF;
    background-color: #74B31B;
    border-color: #84C529;
}

.btn-success:active,
.btn-success.active,
.open .dropdown-toggle.btn-success {
    background-image: none;
}

.btn-success.disabled,
.btn-success[disabled],
fieldset[disabled] .btn-success,
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled:active,
.btn-success[disabled]:active,
fieldset[disabled] .btn-success:active,
.btn-success.disabled.active,
.btn-success[disabled].active,
fieldset[disabled] .btn-success.active {
    background-color: #84C529;
    border-color: #84C529;
}

.btn-success .badge {
    color: #84C529;
    background-color: #FFFFFF;
}

.btn-info {
    color: #ffffff;
    background-color: #03a9f4;
    border: 0;
}

.btn-info:focus,
.btn-info.focus {
    color: #ffffff;
    background-color: #0286c2;
}

.btn-info:hover {
    color: #ffffff;
    background-color: #0286c2;
}

.btn-info:active,
.btn-info.active,
.open>.dropdown-toggle.btn-info {
    color: #ffffff;
    background-color: #0286c2;
}

.btn-info:active:hover,
.btn-info.active:hover,
.open>.dropdown-toggle.btn-info:hover,
.btn-info:active:focus,
.btn-info.active:focus,
.open>.dropdown-toggle.btn-info:focus,
.btn-info:active.focus,
.btn-info.active.focus,
.open>.dropdown-toggle.btn-info.focus {
    color: #ffffff;
    background-color: #026e9e;
}

.btn-info:active,
.btn-info.active,
.open>.dropdown-toggle.btn-info {
    background-image: none;
}

.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled.focus,
.btn-info[disabled].focus,
fieldset[disabled] .btn-info.focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active {
    background-color: #03a9f4;
}

.btn-info .badge {
    color: #03a9f4;
}

.btn-warning {
    color: #ffffff;
    background-color: #ff6f00;
    border: 0;
}

.btn-warning:focus,
.btn-warning.focus {
    color: #ffffff;
    background-color: #cc5900;
}

.btn-warning:hover {
    color: #ffffff;
    background-color: #cc5900;
}

.btn-warning:active,
.btn-warning.active,
.open>.dropdown-toggle.btn-warning {
    color: #ffffff;
    background-color: #cc5900;
}

.btn-warning:active:hover,
.btn-warning.active:hover,
.open>.dropdown-toggle.btn-warning:hover,
.btn-warning:active:focus,
.btn-warning.active:focus,
.open>.dropdown-toggle.btn-warning:focus,
.btn-warning:active.focus,
.btn-warning.active.focus,
.open>.dropdown-toggle.btn-warning.focus {
    color: #ffffff;
    background-color: #a84900;
}

.btn-warning:active,
.btn-warning.active,
.open>.dropdown-toggle.btn-warning {
    background-image: none;
}

.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled.focus,
.btn-warning[disabled].focus,
fieldset[disabled] .btn-warning.focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active {
    background-color: #ff6f00;
}

.btn-warning .badge {
    color: #ff6f00;
    background-color: #ffffff;
}

.btn-danger {
    color: #ffffff;
    background-color: #fc2d42;
    border: 0;
}

.btn-danger:focus,
.btn-danger.focus {
    color: #ffffff;
    background-color: #f3031c;
}

.btn-danger:hover {
    color: #ffffff;
    background-color: #f3031c;
}

.btn-danger:active,
.btn-danger.active,
.open>.dropdown-toggle.btn-danger {
    color: #ffffff;
    background-color: #f3031c;
}

.btn-danger:active:hover,
.btn-danger.active:hover,
.open>.dropdown-toggle.btn-danger:hover,
.btn-danger:active:focus,
.btn-danger.active:focus,
.open>.dropdown-toggle.btn-danger:focus,
.btn-danger:active.focus,
.btn-danger.active.focus,
.open>.dropdown-toggle.btn-danger.focus {
    color: #ffffff;
    background-color: #cf0318;
}

.btn-danger:active,
.btn-danger.active,
.open>.dropdown-toggle.btn-danger {
    background-image: none;
}

.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger.focus,
.btn-danger.disabled:active,
.btn-danger[disabled]:active,
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger.active {
    background-color: #fc2d42;
    border-color: #fc142b;
}

.btn-danger .badge {
    color: #fc2d42;
    background-color: #ffffff;
}

.pagination>li>a:hover,
.pagination>li>span:hover,
.pagination>li>a:focus,
.pagination>li>span:focus {
    color: #1b829b;
    background-color: #eeeeee;
    border-color: #dddddd;
}

.pagination>.active>a,
.pagination>.active>span,
.pagination>.active>a:hover,
.pagination>.active>span:hover,
.pagination>.active>a:focus,
.pagination>.active>span:focus {
    color: #ffffff;
    background-color: #03a9f4;
    border-color: #03a9f4;
}

.pagination>.disabled>span,
.pagination>.disabled>span:hover,
.pagination>.disabled>span:focus,
.pagination>.disabled>a,
.pagination>.disabled>a:hover,
.pagination>.disabled>a:focus {
    color: #777777;
    background-color: #ffffff;
    border-color: #dddddd;
}

.progress {
    background-color: #f9fafc;
}

.progress-bar {
    color: #ffffff;
    background-color: #28b8da;
}

.progress-striped .progress-bar,
.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 40px 40px;
}

.progress.active .progress-bar,
.progress-bar.active {
    -webkit-animation: progress-bar-stripes 2s linear infinite;
    animation: progress-bar-stripes 2s linear infinite;
}

.progress-bar-success {
    background-color: #84c529;
}

.progress-striped .progress-bar-success {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress-bar-info {
    background-color: #03a9f4;
}

.progress-striped .progress-bar-info {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress-bar-warning {
    background-color: #ff6f00;
}

.progress-striped .progress-bar-warning {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress-bar-danger {
    background-color: #fc2d42;
}

.progress-striped .progress-bar-danger {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}

.progress-bar-default {
    background-color: #727b86;
}

.label-default {
    background: transparent;
    border: 1px solid #d2d5dc;
    color: #63686f;
}

.label-default[href]:hover,
.label-default[href]:focus {
    background-color: #727b86;
}

.label-light-green {
    background: transparent;
    border: 1px solid #adca65;
    color: #adca65;
}

.label-light-green[href]:hover,
.label-light-green[href]:focus {
    background-color: #adca65;
}

.label-primary {
    background: transparent;
    border: 1px solid #28b8da;
    color: #28b8da;
}

.label-primary[href]:hover,
.label-primary[href]:focus {
    background-color: #1e95b1;
}

.label-success {
    background: transparent;
    border: 1px solid #84c529;
    color: #84c529;
}

.label-success[href]:hover,
.label-success[href]:focus {
    background-color: #1b721a;
}

.label-info {
    background: transparent;
    border: 1px solid #03a9f4;
    color: #03a9f4;
}

.label-info[href]:hover,
.label-info[href]:focus {
    background-color: #0286c2;
}

.label-warning {
    background: transparent;
    border: 1px solid #ff6f00;
    color: #ff6f00;
}

.label-warning[href]:hover,
.label-warning[href]:focus {
    background-color: #cc5900;
}

.label-danger {
    background: transparent;
    border: 1px solid #fc2d42;
    color: #fc2d42;
}

.label-danger[href]:hover,
.label-danger[href]:focus {
    background-color: #f3031c;
}

.text-danger {
    color: #fc2d42;
}

.text-danger.important {
    color: #fc2d42 !important;
}

.text-info {
    color: #03a9f4;
}

.text-success {
    color: #84c529;
}

.text-warning {
    color: #ff6f00;
}

.has-error .checkbox,
.has-error .checkbox-inline,
.has-error .control-label,
.has-error .help-block,
.has-error .radio,
.has-error .radio-inline,
.has-error.checkbox label,
.has-error.checkbox-inline label,
.has-error.radio label,
.has-error.radio-inline label {
    color: #fc2d42;
}

.has-error .form-control {
    border-color: #fc2d42;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}

body {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    background-color: #fff;
    background: #626f80;
    font-size: 13px;
    color: #323a45;
}

body,
html {
    margin: 0;
    padding: 0;
    height: 100%;
    padding-right: 0 !important;
}

.h4,
h4,
.h3,
h3 {
    font-weight: 400;
}

.noscroll {
    overflow: hidden !important;
}

.dt-table {
    width: 100% !important;
}

a {
    color: #008ece;
    text-decoration: none;
}

a.disabled {
    pointer-events: none;
}

#small-table .table>tbody>tr>td a {
    font-size: 13.6px;
}

@media (min-width: 768px) {
    .modal-xl {
        width: 90%;
        max-width: 1200px;
    }

    .modal-xxl {
        width: 98%;
        max-width: 1500px;
    }
}

.modal {
    z-index: 10000;
}


.media-body {
    word-break: break-all;
}

.modal-header {
    background: #fbfbfb;
    border-radius: 6px;
    padding: 18px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.modal-content {
    border-radius: 6px;
    border: 0;
    -webkit-box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05), 0 2px 4px 0 rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05), 0 2px 4px 0 rgba(0, 0, 0, 0.2);
}

a:hover,
a:focus {
    color: #004B6D;
    text-decoration: none;
}

body.modal-open {
    overflow: hidden;
}

body.modal-open.mobile>#wrapper {
    overflow: hidden;
    height: 100%;
}

body.rtl {
    overflow-x: hidden;
}

@media (min-width:801px) {
    body.small-table .small-table-right-col.col-md-7 {
        padding-left: 0;
    }
}

b,
strong {
    font-weight: 500;
}

h5 {
    font-size: 13px;
}


.animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

@-webkit-keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.fadeIn {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
}

@-webkit-keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

.fadeOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut;
}


@-webkit-keyframes fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.fadeInRight {
    -webkit-animation-name: fadeInRight;
    animation-name: fadeInRight;
}

@-webkit-keyframes fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

.fadeInLeft {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft;
}

@-webkit-keyframes fadeOutLeft {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
}

@keyframes fadeOutLeft {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
}

.fadeOutLeft {
    -webkit-animation-name: fadeOutLeft;
    animation-name: fadeOutLeft;
}

@-webkit-keyframes fadeOutRight {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }
}

@keyframes fadeOutRight {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }
}

.fadeOutRight {
    -webkit-animation-name: fadeOutRight;
    animation-name: fadeOutRight;
}

.table .alert-danger {
    color: #a94442 !important;
    background-color: #f2dede !important;
    border-color: #ebccd1 !important;
}

.alert {
    padding: 10px 15px;
    font-size: 14px;
}

.alert:not(.float-alert) span[data-notify="icon"] {
    float: left;
    font-size: 18px;
    margin-top: 0;
}

.float-alert.alert span[data-notify="icon"] {
    font-size: 20px;
    display: block;
    left: 13px;
    position: absolute;
    top: 50%;
    margin-top: -11px;
}

.alert.float-alert .alert-title {
    margin-left: 30px;
    font-weight: 500;
}

body.rtl .alert.float-alert .alert-title {
    float: left;
}

.alert:not(.float-alert) .alert-title {
    margin-left: 10px;
}

.alert.float-alert button.close {
    position: absolute;
    right: 10px;
    top: 50%;
    margin-top: -13px;
    z-index: 1033;
    background-color: #FFFFFF;
    display: block;
    border-radius: 50%;
    opacity: .4;
    line-height: 11px;
    width: 25px;
    height: 25px;
    outline: 0 !important;
    text-align: center;
    padding: 3px;
    font-weight: 400;
}

.alert.float-alert button.close:hover {
    opacity: .55;
}

.alert.float-alert .close~span {
    display: block;
    max-width: 89%;
}

.alert.alert-dismissible button.close {
    right: -2px;
}

.announcement .alert-dismissible .close {
    top: -4px;
}

body.rtl .announcement .alert-dismissible .close {
    right: 13px;
}

.small,
small {
    font-size: 70%;
}

@-webkit-keyframes loader {
    0% {
        background: #f0f0f0;
    }

    10% {
        background: #c5c5c5;
    }

    40% {
        background: #f0f0f0;
    }
}

@keyframes loader {
    0% {
        background: #f0f0f0;
    }

    10% {
        background: #c5c5c5;
    }

    40% {
        background: #f0f0f0;
    }
}

.table-bordered>thead>tr>th,
.table-bordered>tbody>tr>th,
.table-bordered>tfoot>tr>th,
.table-bordered>thead>tr>td,
.table-bordered>tbody>tr>td,
.table-bordered>tfoot>tr>td {
    border: 1px solid #f0f0f0;
}

.table>tbody>tr>td,
.table>tbody>tr>th,
.table>tfoot>tr>td,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>thead>tr>th {
    border-top: 1px solid #ebf5ff;
}

table .row-options {
    margin-top: 5px;
    padding: 2px 0 0;
    position: relative;
    left: -9999em;
}

body.rtl table .row-options {
    left: initial;
    right: -9999em;
}

table tr:hover td .row-options,
.mobile table tr td .row-options {
    position: static;
}

.dt-loader:not(:required) {
    -webkit-animation: loader 2000ms 300ms infinite ease-out;
    animation: loader 2000ms 300ms infinite ease-out;
    background: #f0f0f0;
    text-indent: -9999px;
    width: 0.9em;
    height: 1.5em;
    margin: 0 auto;
    display: block;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 999999999999999;
}

.dt-loader:not(:required):before,
.dt-loader:not(:required):after {
    background: #f0f0f0;
    content: '\x200B';
    display: inline-block;
    width: 0.9em;
    height: 1.5em;
    position: absolute;
    top: 0;
}

.dt-loader:not(:required):before {
    -webkit-animation: loader 2000ms 150ms infinite ease-out;
    animation: loader 2000ms 150ms infinite ease-out;
    left: -1.6em;
}

.dt-loader:not(:required):after {
    -webkit-animation: loader 2000ms 450ms infinite ease-out;
    animation: loader 2000ms 450ms infinite ease-out;
    right: -1.6em;
}

.dropzone,
.dropzone * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.dropzone {
    position: relative;
}

.dropzone .dz-preview {
    position: relative;
    display: inline-block;
    width: 120px;
    margin: 0.5em;
}

.dropzone .dz-preview .dz-progress {
    display: block;
    height: 15px;
    border: 1px solid #aaa;
}

.dropzone .dz-preview .dz-progress .dz-upload {
    display: block;
    height: 100%;
    width: 0;
    background: green;
}

.dropzone .dz-preview .dz-error-message {
    color: red;
    display: none;
}

.dropzone .dz-preview.dz-error .dz-error-message,
.dropzone .dz-preview.dz-error .dz-error-mark {
    display: block;
}

.dropzone .dz-preview.dz-success .dz-success-mark {
    display: block;
}

.dropzone .dz-preview .dz-error-mark,
.dropzone .dz-preview .dz-success-mark {
    position: absolute;
    display: none;
    top: 30px;
    width: 54px;
    height: 58px;
    left: 50%;
    margin-left: -27px;
}

body.hide-sidebar:not(.show-sidebar) #menu {
    margin-left: -210px;
}

body.hide-sidebar:not(.show-sidebar) #wrapper {
    margin-left: 0;
}

body.page-small #menu {
    margin-left: -210px;
}

body.page-small #wrapper {
    margin-left: 0;
}

body.page-small.show-sidebar #menu {
    margin-left: 0;
}

body.page-small.show-sidebar #wrapper {
    margin-left: 210px;
}

#header {
    background: #415165;
    /* Old browsers */
    /* FF3.6-15 */
    /* Chrome10-25,Safari5.1-6 */
    background: -webkit-gradient(linear, left top, right top, from(#415165), color-stop(26%, #51647c), color-stop(73%, #51647c), to(#4f5d7a));
    background: linear-gradient(to right, #415165 0%, #51647c 26%, #51647c 73%, #4f5d7a 100%);
    /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#415165', endColorstr='#4f5d7a', GradientType=1);
    /* IE6-9 */
    display: block;
    height: 63px;
    margin: 0;
    padding: 0;
    position: relative;
    z-index: 99;
}

#header li>a.active {
    color: inherit;
    background: #f7f8fa;
    border-radius: 80px;
}

#logo {
    float: left;
    padding: 10px 15px;
    height: 63px;
    text-align: center;
}

#logo img {
    width: auto;
    height: 34px;
    margin-top: 3px;
}

#logo span {
    font-weight: 400;
    font-size: 25px;
    color: #323a45;
}

.small-logo {
    display: none;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
}

.navbar-nav>li>a {
    color: #fff;
    font-size: 13px;
    padding: 4px 12px 4px 12px;
    line-height: 57px;
    height: 63px;
}

.navbar-nav>li a>.icon-total-indicator {
    line-height: 12px;
    padding: 2px 5px;
    padding-bottom: 3px;
    color: #fff;
    position: absolute;
    right: 1px;
    font-size: 11px;
    top: 13px;
    font-family: Verdana, serif;
    vertical-align: middle;
    text-align: center;
    border-radius: 8px;
}

.badge {
    background-color: #6c7888;
    line-height: 12px;
    padding: 2px 5px;
    padding-bottom: 3px;
    color: #fff;
    right: 1px;
    font-size: 11px;
    top: 13px;
    font-family: Verdana, serif;
    vertical-align: middle;
    text-align: center;
    border-radius: 8px;
    font-weight: 400;
}


.bg-warning {
    background-color: #FF6F00;
}

.bg-info {
    background-color: #03a9f4;
}

.bg-success {
    background-color: #84c529;
}

.bg-white {
    background-color: #fff;
}

.bg-light-gray {
    background-color: #f6f8fa;
}

.dropdown-menu>li>a {
    width: 100%;
    word-wrap: break-word;
    white-space: normal;
}

.dropdown-menu {
    margin: 0;
    margin-top: 5px;
    padding: 0;
    border-radius: 6px;
    z-index: 9000;
    -webkit-box-shadow: 1px 2px 3px rgba(0, 0, 0, 0.125);
    box-shadow: 1px 2px 3px rgba(0, 0, 0, 0.125);
    border-color: #bfcbd9;
}

.dropdown-menu>li>a {
    padding: 8px 16px;
    color: #333333;
}

#estimate .dropdown-menu-estimates>li>a {
    white-space: nowrap;
}

.dropdown-menu>li>a:focus {
    outline: 0 !important;
}

.dropdown-menu>li:first-child>a {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

#header .dropdown-menu>li:first-child>a {
    border-radius: 0;
}

.bootstrap-select .dropdown-menu {
    margin-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-top: 0;
}

.bootstrap-select .btn-default[aria-expanded="true"] {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.bootstrap-select.fit-width .dropdown-toggle .caret {
    position: absolute;
    right: -6px;
}

.bootstrap-select .dropdown-menu>li:first-child>a {
    border-radius: 0;
}

.bootstrap-select .selectpicker.clean-select + button {
    border:0px !important;
}

.bootstrap-select.open .selectpicker.clean-select + button + .dropdown-menu {
    border-radius:4px !important;
    border-top-width: 1px !important;
}

.dropdown-menu>li:last-child>a {
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
}

.dropdown-menu>li>a:hover,
.dropdown-menu>li>a:focus {
    background-color: #F5F5F5;
    color: #333333;
    opacity: 1;
    text-decoration: none;
}

.dropdown-menu>.active>a,
.dropdown-menu>.active>a:focus,
.dropdown-menu>.active>a:hover {
    background-color: #03a9f4;
}

.navbar-nav>li>.dropdown-menu.notifications {
    max-height: 600px;
    border-top: 0;
    overflow: auto;
    z-index: 100;
}

.navbar-nav>li>.dropdown-menu.notifications li {
    z-index: 100;
}

.navbar-nav>li>.dropdown-menu.notifications li .media-body {
    font-size: 13px;
}

.navbar-nav>li>.dropdown-menu>li:last-child>a {
    border-radius: 0;
}

.navbar-nav>li>.dropdown-menu.notifications li img {
    margin-right: 10px;
}

.notification_link {
    cursor: pointer;
}

.notification-box-all {
    border: 1px solid #f0f0f0;
    padding: 7px 10px;
    border-radius: 4px;
    margin-bottom: 10px;
}

.notification-box-all:hover {
    background: #fbfbfb;
}

.notification-box-all img {
    margin-right: 10px;
}

.notification-box-all a {
    display: block;
}

.notification-box {
    padding: 10px 10px;
    border-bottom: 1px solid #f0f0f0;
}

.notification-box.unread,
.notification-box-all.unread {
    background: #f0f4fd;
    border-bottom: 1px solid #F7F9FA;
}

a.notification-top {
    cursor: pointer;
}

.navbar-nav>li>a:hover,
.navbar-nav>li>a:focus,
.navbar-nav .open>a,
.navbar-nav .open>a:hover,
.navbar-nav .open>a:focus {
    background: none;
    border-radius: 0;
    color: #D0D0D0;
}

.navbar-nav>li>.profile {
    display: block;
    font-size: 15px;
}

.navbar-nav>li>a .staff-profile-image-small {
    margin-top: 11px;
}

.navbar-nav.navbar-right {
    margin-right: 26px;
}

.navbar-right .dropdown-menu.notifications li:not(:last-child)>a {
    padding: 0;
}

.navbar-right .dropdown-menu.notifications .divider {
    margin: 0;
}

.navbar-right .dropdown-menu.notifications li:last-child {
    padding: 15px;
    font-weight: 500;
    text-align: center;
}

.mobile-navbar {
    position: absolute;
    top: 63px;
    left: 0;
    right: 0;
    background-color: #ffffff;
    z-index: 100;
}

.mobile-navbar .navbar-nav {
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #e4e5e7;
}

.mobile-navbar .navbar-nav>li>a {
    padding: 10px 12px;
    font-size: 14px;
    color: #333 !important;
    border-bottom: 1px solid #f0f0f0;
    height: inherit;
}

.mobile-navbar .navbar-nav>li:last-child>a {
    border-bottom: 0;
}

.mobile-navbar .navbar-nav>li>a:hover,
.mobile-navbar .navbar-nav>li>a:focus,
.mobile-navbar .navbar-nav .open>a,
.mobile-navbar .navbar-nav .open>a:hover,
.mobile-navbar .navbar-nav .open>a:focus {
    border-bottom: none;
}

#menu {
    width: 210px;
    bottom: 0;
    float: left;
    left: 0;
    position: absolute;
    top: 63px;
    -webkit-transition: all 0.5s ease 0s;
    transition: all 0.5s ease 0s;
}

#side-menu {
    background: #626f80;
}

#side-menu .dashboard {
    border-top: 0;
}

#side-menu li a {
    color: #fff;
    text-transform: uppercase;
    padding: 11px 20px 11px 16px;
    font-size: 13px;
    font-family: 'Roboto';
}

#side-menu li:first-child {
    border-bottom: 0 !important;
}

li a i.menu-icon {
    margin-right: 16px;
    display: block;
    float: left;
    width: 18px;
    font-size: 17px;
}

#side-menu li.active>a {
    border-radius: 0;
    color: #323a45;
    background: #e3e8ee;
}

#side-menu li .nav-second-level li {
    border-bottom: 0 !important;
}

#side-menu.nav>li>a:hover,
#side-menu.nav>li>a:focus {
    background: #e3e8ee;
    border-bottom: 0 !important;
    color: #323a45;
    transition: ease-in-out 0.2s;
    -webkit-transition: ease-in-out 0.2s;
    -moz-transition: ease-in-out 0.2s;
    -o-transition: ease-in-out 0.2s;
}

#side-menu li.active,
li .nav-second-level li {
    background: #fff;
    border-bottom: 0;
}

#side-menu li .nav-second-level li a {
    padding: 7px 10px 7px 45px;
    color: #0181BB;
    text-transform: none;
    font-size: 14px;
}

#side-menu li .nav-second-level li.active a {
    color: #fff !important;
    border-radius: 50px;
    background-color: #03a9f4;
    border-left: 0 !important;
    display: inline-block;
    padding: 5px 15px;
    margin: 8px 0 8px 30px;
}

#side-menu li .nav-second-level a:hover {
    background-color: #e3e8ee;
}

#side-menu li.quick-links {
    left: -12px;
    position: absolute;
    top: 0;
    border-bottom: 0 !important;
}

#side-menu li.dashboard_user {
    padding: 11px 10px 11px 40px;
    color: #fff;
    font-size: 14px;
    font-weight: 500;
}

#side-menu li.quick-links a {
    padding: 10px 10px 11px 23px;
    font-size: 17px;
}

#side-menu li.quick-links .dropdown-toggle {
    line-height: 40px;
}

.nav li.quick-links .dropdown-quick-links.open a.dropdown-toggle,
.nav li.quick-links .dropdown-quick-links a.dropdown-toggle:hover {
    background: transparent;
    opacity: 0.6;
}

.nav li.quick-links .dropdown-quick-links.open a.dropdown-toggle i {
    -webkit-transform: rotate(-44deg);
    transform: rotate(-44deg);
}

#side-menu li.quick-links .dropdown-menu li {
    padding: 0;
    border-bottom: 0;
}

#side-menu li.quick-links .dropdown-menu li a {
    color: #333;
    text-transform: none;
    padding: 10px 20px;
    font-size: 14px;
}

#side-menu li.quick-links .open>.dropdown-menu {
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 12px;
    margin-left: 13px;
}

#side-menu li.quick-links .dropdown-menu:before {
    position: absolute;
    top: -7px;
    left: 9px;
    display: inline-block;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #ccc;
    border-left: 7px solid transparent;
    border-bottom-color: rgba(0, 0, 0, 0.2);
    content: '';
}

#side-menu li.quick-links .dropdown-menu:after {
    position: absolute;
    top: -6px;
    left: 10px;
    display: inline-block;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #ffffff;
    border-left: 6px solid transparent;
    content: '';
}

#side-menu .arrow,
#setup-menu .arrow {
    float: right;
    padding-top: 3px;
}

.fa.arrow:before {
    content: "\f104";
}

.active>a>.fa.arrow:before {
    content: "\f107";
}

#side-menu li.quick-links li:first-child.active>a,
#side-menu li.quick-links li:first-child.active {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

#setup-menu-wrapper {
    background-color: #626f80;
    position: absolute;
    top: 63px;
    width: 210px !important;
    bottom: 0;
    left: 0;
    overflow: auto;
    display: none;
    z-index: 9;
    min-height: 100%;
}

[dir='rtl'] #setup-menu-wrapper {
    right: 0;
    left: auto;
    z-index: 999;
}

#setup-menu li:first-child {
    border-bottom: 1px solid #626e7b;
    color: #fff;
}

#setup-menu .nav-second-level li:first-child {
    background: #FDFDFD;
    color: #323a45;
    border-left: 0 !important;
}

#setup-menu li.active {
    border-bottom: 0;
}

#setup-menu>li>a {
    color: #fff;
    font-size: 14px;
    font-family: 'Roboto';
    text-transform: uppercase;
    padding: 7px 16px;
}

#setup-menu li .nav-second-level li {
    border-bottom: 0;
}

#setup-menu li .nav-second-level li a:hover,
#setup-menu li .nav-second-level li a.active {
    color: #323a45;
    background-color: #f7f9fa;
}

#setup-menu li .nav-second-level li a {
    padding: 8px 10px 8px 30px;
    color: #323a45;
    font-size: 13px;
}

#setup-menu .close-customizer {
    display: inline-block;
    margin: 8px;
    padding: 3px 8px;
    border-radius: 4px;
}

#setup-menu li.active>a {
    color: #333;
    background-color: #e3e8ee;
}

#setup-menu li .nav-second-level li.active a {
    color: #fff !important;
    border-radius: 50px;
    background-color: #03a9f4;
    display: inline-block;
    padding: 5px 15px;
    margin: 8px 0 8px 15px;
}

#setup-menu li>a:hover,
#setup-menu li>a:active,
#setup-menu li>a:focus,
#setup-menu li>a.active {
    color: #333 !important;
    background-color: #e3e8ee;
}

#setup-menu li .nav-second-level li:last-child {
    border-bottom: 0;
}

#setup-menu .customizer-heading {
    position: absolute;
    top: 11px;
}

#wrapper {
    margin: 0 0 0 210px;
    padding: 0;
    background: #e3e8ee;
    -webkit-transition: all 0.4s ease 0s;
    transition: all 0.4s ease 0s;
    position: relative;
    min-height: 100%;
}

.content {
    padding: 10px 25px 25px 25px;
    min-width: 320px;
}

.client-profile-image-small {
    height: 32px;
    width: 32px;
    border-radius: 50%;
}

.staff-profile-xs-image {
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.staff-profile-image-small {
    height: 32px;
    width: 32px;
    border-radius: 50%;
}

.staff-profile-image-xs {
    height: 20px;
    width: 20px;
    border-radius: 50%;
}

.staff-profile-image-thumb {
    height: 160px;
    width: 160px;
    border-radius: 50%;
}

.client-profile-image-thumb {
    height: 160px;
    width: 160px;
    border-radius: 50%;
}

.btn {
    text-transform: uppercase;
    font-size: 13.5px;
    outline-offset: 0;
    border: 1px solid transparent;
    transition: all .15s ease-in-out;
    -o-transition: all .15s ease-in-out;
    -moz-transition: all .15s ease-in-out;
    -webkit-transition: all .15s ease-in-out;
}

.mobile .btn {
    font-size: 13px;
    padding: 5px 7px;
}

.btn.btn-xs {
    padding: 4px 8px;
    font-size: 13px;
}

.btn:focus,
.btn:active,
.btn.active,
.btn:active:focus,
.btn.active:focus {
    outline: 0;
    outline-offset: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.btn.btn-default {
    border: 1px solid #E6E9EB;
}

textarea.form-control,
select.form-control,
.dataTables_length select,
input[type="text"],
input[type="file"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.uneditable-input,
input[type="color"] {
    border: 1px solid #bfcbd9;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #494949;
    font-size: 14px;
    -webkit-transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
    transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
    line-height: 1;
    height: 36px;
}

textarea.form-control {
    height: inherit;
    padding-top: 10px;
}

.input-group-addon {
    border: 1px solid #bfcbd9;
    background-color: #fbfdff;
    color: #97a8be;
}

.input-group-addon .btn {
    padding: 1px 5px !important;
}

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
    background-color: #eef1f6;
    border-color: #d1dbe5;
    color: #8babcc;
    cursor: not-allowed;
}

.form-control::-webkit-input-placeholder {
    color: #a1b4cc;
    font-size: 14px;
}

.form-control:-moz-placeholder {
    color: #a1b4cc;
    font-size: 14px;
}

.form-control::-moz-placeholder {
    color: #a1b4cc;
    font-size: 14px;
}

.form-control:-ms-input-placeholder {
    color: #a1b4cc;
    font-size: 14px;
}

textarea.form-control:focus,
select.form-control:focus,
.dataTables_length select:focus,
input[type="text"]:focus,
input[type="file"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus,
.uneditable-input:focus,
input[type="color"]:focus {
    border-color: #03a9f4;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: 0 none;
}

body.rtl .input-group-addon:last-child {
    border-left: 1px solid #ccc;
}

.dataTables_length {
    float: left;
    margin-right: 5px;
}

.dataTables_length select {
    padding-top: 5px;
    height: inherit;
}

body.rtl .dataTables_length {
    float: right;
    margin-left: 5px;
}

.checkbox {
    padding-left: 20px;
}

.checkbox label {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    padding-left: 5px;
}

.checkbox label::before {
    content: "";
    display: inline-block;
    position: absolute;
    width: 17px;
    height: 17px;
    left: 0;
    margin-left: -20px;
    border: 1px solid #bfcbd9;
    border-radius: 3px;
    background-color: #fff;
    -webkit-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
    transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
}

.checkbox label::after {
    display: inline-block;
    position: absolute;
    width: 16px;
    height: 16px;
    left: 0;
    top: 0;
    margin-left: -20px;
    padding-left: 3px;
    padding-top: 1px;
    font-size: 11px;
    color: #555555;
}

.checkbox input[type="checkbox"]:focus+label::before {
    outline: thin dotted;
    outline: 5px auto transparent;
    outline-offset: -10px;
}

.checkbox input[type="checkbox"],
.checkbox input[type="radio"] {
    opacity: 0;
    z-index: 1;
}

.checkbox input[type="checkbox"]:checked+label::after,
.checkbox input[type="radio"]:checked+label::after {
    font-family: 'Glyphicons Halflings';
    content: "\e013";
}

.checkbox input[type="checkbox"]:disabled+label,
.checkbox input[type="radio"]:disabled+label {
    opacity: 0.65;
}

.checkbox input[type="checkbox"]:disabled+label::before,
.checkbox input[type="radio"]:disabled+label::before {
    background-color: #eeeeee;
    cursor: not-allowed;
}

.checkbox.checkbox-circle label::before {
    border-radius: 50%;
}

.checkbox.checkbox-inline {
    margin-top: 0;
}

.checkbox-primary input[type="checkbox"]:checked+label::before,
.checkbox-primary input[type="radio"]:checked+label::before {
    background-color: #03a9f4;
    border-color: #03a9f4;
}

.checkbox-primary input[type="checkbox"]:checked+label::after,
.checkbox-primary input[type="radio"]:checked+label::after {
    color: #fff;
}

.checkbox-danger input[type="checkbox"]:checked+label::before,
.checkbox-danger input[type="radio"]:checked+label::before {
    background-color: #FC2D42;
    border-color: #FC2D42;
}

.checkbox-danger input[type="checkbox"]:checked+label::after,
.checkbox-danger input[type="radio"]:checked+label::after {
    color: #fff;
}

.checkbox-info input[type="checkbox"]:checked+label::before,
.checkbox-info input[type="radio"]:checked+label::before {
    background-color: #03A9F4;
    border-color: #03A9F4;
}

.checkbox-info input[type="checkbox"]:checked+label::after,
.checkbox-info input[type="radio"]:checked+label::after {
    color: #fff;
}

.checkbox-warning input[type="checkbox"]:checked+label::before,
.checkbox-warning input[type="radio"]:checked+label::before {
    background-color: #FF6F00;
    border-color: #FF6F00;
}

.checkbox-warning input[type="checkbox"]:checked+label::after,
.checkbox-warning input[type="radio"]:checked+label::after {
    color: #fff;
}

.checkbox-success input[type="checkbox"]:checked+label::before,
.checkbox-success input[type="radio"]:checked+label::before {
    background-color: #84c529;
    border-color: #84c529;
}

.checkbox-success input[type="checkbox"]:checked+label::after,
.checkbox-success input[type="radio"]:checked+label::after {
    color: #fff;
}

.radio {
    padding-left: 20px;
}

.radio label {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    padding-left: 5px;
}

.radio label::before {
    content: "";
    display: inline-block;
    position: absolute;
    width: 17px;
    height: 17px;
    left: 0;
    margin-left: -20px;
    border: 1px solid #bfcbd9;
    border-radius: 50%;
    background-color: #fff;
    -webkit-transition: border 0.15s ease-in-out;
    transition: border 0.15s ease-in-out;
}

.radio label::after {
    display: inline-block;
    position: absolute;
    content: " ";
    width: 11px;
    height: 11px;
    left: 3px;
    top: 3px;
    margin-left: -20px;
    border-radius: 50%;
    background-color: #555555;
    -webkit-transform: scale(0, 0);
    transform: scale(0, 0);
    -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
    transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33), -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
}

.radio input[type="radio"] {
    opacity: 0;
    z-index: 1;
}

.radio input[type="radio"]:focus+label::before {
    outline: thin dotted;
    outline: 5px auto transparent;
    outline-offset: -5px;
}

.radio input[type="radio"]:checked+label::after {
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1);
}

.radio input[type="radio"]:disabled+label {
    opacity: 0.65;
}

.radio input[type="radio"]:disabled+label::before {
    cursor: not-allowed;
}

.radio.radio-inline {
    margin-top: 0;
}

.radio-primary input[type="radio"]+label::after {
    background-color: #03a9f4;
}

.radio-primary input[type="radio"]:checked+label::before {
    border-color: #03a9f4;
}

.radio-primary input[type="radio"]:checked+label::after {
    background-color: #03a9f4;
}

.radio-danger input[type="radio"]+label::after {
    background-color: #FC2D42;
}

.radio-danger input[type="radio"]:checked+label::before {
    border-color: #FC2D42;
}

.radio-danger input[type="radio"]:checked+label::after {
    background-color: #FC2D42;
}

.radio-info input[type="radio"]+label::after {
    background-color: #03A9F4;
}

.radio-info input[type="radio"]:checked+label::before {
    border-color: #03A9F4;
}

.radio-info input[type="radio"]:checked+label::after {
    background-color: #03A9F4;
}

.radio-warning input[type="radio"]+label::after {
    background-color: #FF6F00;
}

.radio-warning input[type="radio"]:checked+label::before {
    border-color: #FF6F00;
}

.radio-warning input[type="radio"]:checked+label::after {
    background-color: #FF6F00;
}

.radio-success input[type="radio"]+label::after {
    background-color: #84c529;
}

.radio-success input[type="radio"]:checked+label::before {
    border-color: #84c529;
}

.radio-success input[type="radio"]:checked+label::after {
    background-color: #84c529;
}

input[type="checkbox"].styled:checked+label:after,
input[type="radio"].styled:checked+label:after {
    font-family: 'Glyphicons Halflings';
    content: "\e013";
}

input[type="checkbox"] .styled:checked+label::before,
input[type="radio"] .styled:checked+label::before {
    color: #fff;
}

input[type="checkbox"] .styled:checked+label::after,
input[type="radio"] .styled:checked+label::after {
    color: #fff;
}

.input-group-addon .checkbox,
.input-group-addon .radio {
    margin: 0;
}

.input-group-addon .checkbox label {
    padding-top: 2px;
}

.input-group-addon .checkbox label::after {
    padding-top: 5px;
    padding-left: 2px;
}

.label {
    font-size: 12px;
    font-weight: 400;
    padding: .3em .7em .3em;
}

.btn-ticket-label {
    padding: 4px 10px 3px 10px;
    font-size: 14px;
    margin-top: -2px;
}

.label.ticket-label {
    font-size: 13px;
    padding: 6px 9px;
}

.label.ticket-label.label-default {
    border: 1px solid #bfcbd9;
}

.panel_s>.panel-heading,
.panel_s>.panel-heading-bg {
    color: inherit;
    padding: 10px;
    transition: all .3s;
    -moz-transition: all .3s;
    -webkit-transition: all .3s;
    -o-transition: all .3s;
    border: 1px solid #e4e5e7;
}

.panel_s>.panel-heading+.panel-body {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-top: 0;
}

.panel_s>.panel-heading {
    font-weight: 500;
    background: #f7f9fa;
}

.panel-no-heading {
    margin-top: 40px;
}

.label-margin {
    margin-top: 23px;
}

.panel_s>.panel-heading-bg {
    background: #f7f9fa;
    border: 1px solid #dce1ef;
    padding: 7px 15px;
    border-bottom: 0;
    color: #323a45;
}

.panel_s .panel-body {
    background: #fff;
    border: 1px solid #dce1ef;
    border-radius: 4px;
    padding: 20px;
    position: relative;
}

.panel_s>.panel-footer {
    background: #f7f9fa;
    border: 1px solid #dce1ef;
    border-top: none;
    color: inherit;
    font-size: 90%;
    padding: 10px 15px;
}

.panel_s {
    background-color: none;
    border: none;
    -webkit-box-shadow: 0 1px 15px 1px rgba(90, 90, 90, 0.08);
    box-shadow: 0 1px 15px 1px rgba(90, 90, 90, 0.08);
    margin-bottom: 25px;
}

.chip-circle {
    display: inline-block;
    height: 32px;
    font-size: 13px;
    line-height: 32px;
    padding: 0 12px;
    color: #fff;
    border-radius: 16px;
    background-color: #4b5158;
}

.chip-small {
    line-height: 6px;
    padding: 6px 10px 6px 10px;
    height: 20px;
}

label,
.control-label {
    font-weight: 400;
    font-size: 13px;
    color: #2d2d2d;
    margin-bottom: 7px;
}

.full-width {
    width: 100%;
}

.text-throught {
    text-decoration: line-through;
    opacity: 0.6;
}

.ie-dt-fix {
    overflow-x: hidden;
}

.valign {
    vertical-align: middle;
}

.ptop10 {
    padding-top: 10px;
}

.ptop15 {
    padding-top: 15px;
}

.p7 {
    padding: 7px;
}

.p8 {
    padding: 8px;
}

.p8-half {
    padding: 8.5px;
}

.p9 {
    padding: 9px;
}

.mright5 {
    margin-right: 5px;
}

.mright10 {
    margin-right: 10px;
}

.mright15 {
    margin-right: 15px;
}

.mright25 {
    margin-right: 25px;
}

.mright30 {
    margin-right: 30px;
}

.mright35 {
    margin-right: 35px;
}

.mright20 {
    margin-right: 20px;
}

.mleft5 {
    margin-left: 5px;
}

.mleft10 {
    margin-left: 10px !important;
}

.mleft15 {
    margin-left: 15px;
}

.mleft20 {
    margin-left: 20px;
}

.mleft25 {
    margin-left: 25px;
}

.mleft30 {
    margin-left: 30px;
}

.pleft5 {
    padding-left: 5px;
}

.mleft4 {
    margin-left: 4px !important;
}

.mleft40 {
    margin-left: 40px;
}

body.rtl .mright5 {
    margin-left: 5px;
    margin-right: 0;
}

body.rtl .mright10 {
    margin-left: 10px;
    margin-right: 0;
}

body.rtl .mright15 {
    margin-left: 15px;
    margin-right: 0;
}

body.rtl .mright25 {
    margin-left: 25px;
    margin-right: 0;
}

body.rtl .mright35 {
    margin-left: 35px;
    margin-right: 0;
}

body.rtl .mright20 {
    margin-left: 20px;
    margin-right: 0;
}

body.rtl .mleft5 {
    margin-right: 5px !important;
    margin-left: 0;
}

body.rtl .mleft10 {
    margin-right: 10px !important;
    margin-left: 0;
}

body.rtl .mleft15 {
    margin-right: 15px !important;
    margin-left: 0;
}

body.rtl .mleft20 {
    margin-right: 20px !important;
    margin-left: 0;
}

body.rtl .mleft25 {
    margin-right: 25px !important;
    margin-left: 0;
}

body.rtl .mleft30 {
    margin-right: 30px !important;
    margin-left: 0;
}

body.rtl .mleft4 {
    margin-right: 4px !important;
}

body.rtl .mleft40 {
    margin-right: 40px;
    margin-left: 0;
}

.mbot5 {
    margin-bottom: 5px;
}

.mbot10 {
    margin-bottom: 10px;
}

.mbot15 {
    margin-bottom: 15px !important;
}

.mbot20 {
    margin-bottom: 20px;
}

.mbot25 {
    margin-bottom: 25px;
}

.mbot30 {
    margin-bottom: 30px;
}

.no-mleft {
    margin-left: 0 !important;
}

.no-mright {
    margin-right: 0 !important;
}

.mntop15 {
    margin-top: -15px;
}

.no-mtop {
    margin-top: 0 !important;
}

.no-mbot {
    margin-bottom: 0 !important;
}

.mtop1 {
    margin-top: 1px;
}

.mtop2 {
    margin-top: 2px;
}

.mtop3 {
    margin-top: 3px;
}

.mtop4 {
    margin-top: 4px;
}

.mtop5 {
    margin-top: 5px;
}

.mtop6 {
    margin-top: 6px;
}

.mtop7 {
    margin-top: 7px;
}

.mtop8 {
    margin-top: 8px;
}

.mtop10 {
    margin-top: 10px;
}

.mtop15 {
    margin-top: 15px;
}

.mtop20 {
    margin-top: 20px;
}

.mtop25 {
    margin-top: 25px;
}

.mtop30 {
    margin-top: 30px;
}

.mtop35 {
    margin-top: 35px;
}

.mtop40 {
    margin-top: 40px;
}

.mtop45 {
    margin-top: 45px;
}

.no-border {
    border: 0;
}

.no-border-color {
    border-color: transparent !important;
}

.no-border-bottom {
    border-bottom: 0 !important;
}

.no-margin {
    margin: 0 !important;
}

.no-padding {
    padding: 0 !important;
}

.no-padding-bottom {
    padding-bottom: 0 !important;
}

.no-padding-top {
    padding-top: 0 !important;
}

.no-padding-left {
    padding-left: 0 !important;
}

.no-padding-right {
    padding-right: 0 !important;
}

body.rtl .no-padding-right {
    padding-left: 0 !important;
}

body.rtl .no-padding-left {
    padding-right: 0 !important;
}

.s_table {
    overflow-x: visible;
}

.inline-block {
    display: inline-block;
}

.display-block {
    display: block !important;
}

@media (max-width: 767px) {
    .hidden-xs {
        display: none !important;
    }

    .view-all-timesheets {
        height: 45px;
        margin-top: 0;
    }
}

.border-right {
    border-right: 1px solid #f0f0f0;
}

body.rtl .border-right {
    border-right: 0;
    border-left: 1px solid #f0f0f0;
}

.bold {
    font-weight: 500;
}

label:not(.control-label) {
    font-weight: 400;
}

.table-invoices tr {
    min-width: 100%;
}

.padding {
    padding: 20px;
}

.padding-10 {
    padding: 10px !important;
}

.padding-5 {
    padding: 5px;
}

.line-throught {
    text-decoration: line-through;
}

.medium-icon {
    font-size: 26px;
}

.tooltip-pointer {
    border-bottom: 1px dashed;
}

.modal .email-template {
    overflow: visible;
}

.email-templates li>a {
    font-size: 14px;
}

.email-templates li {
    border-bottom: 1px solid #f0f0f0;
    padding: 5px;
}

.client-reply {
    background: #FFFFE6 !important;
    color: #323a45;
}

.question.form-group {
    padding: 25px;
    background: #FDFDFD;
    border: 1px solid #F1F1F1;
}

.question .required {
    margin-top: 0;
}

.question_update {
    margin-left: 15px;
}

.note {
    background: #FFFFE6;
    color: #323a45;
    padding: 5px 10px 5px 10px;
    margin-bottom: 8px;
    border: 1px solid #DACD83;
    border-radius: 2px;
}

.fc-event {
    padding: 8px !important;
    border: 0 !important;
    border-radius: 2px !important;
    margin: 5px 10px 5px 10px !important;
    font-size: 11px;
    cursor: pointer;
}

.fc-view {
    overflow-y: scroll;
}

#calendar .fc-header-toolbar button,
#calendar .fc-day-header,
#calendar .fc-toolbar .fc-center {
   text-transform: capitalize;
}

.bootstrap-select .btn-default {
    background: #fff !important;
    color: #415164 !important;
    border: 1px solid #bfcbd9 !important;
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 4px 10px;
    line-height: 2;
    height: 36px;
    text-transform: inherit;
    padding-left: 10px;
}

.bootstrap-select>.dropdown-toggle.bs-placeholder,
.bootstrap-select>.dropdown-toggle.bs-placeholder:active,
.bootstrap-select>.dropdown-toggle.bs-placeholder:focus,
.bootstrap-select>.dropdown-toggle.bs-placeholder:hover {
    color: #a1b4cc;
}

.bootstrap-select.btn-group.disabled,
.bootstrap-select.btn-group>.disabled,
.bootstrap-select.disabled,
.bootstrap-select>.disabled {
    background-color: #eef1f6 !important;
    border-color: #d1dbe5 !important;
    cursor: not-allowed;
}

.bootstrap-select .dropdown-toggle:focus {
    outline: 0 !important;
}

.bootstrap-select .status {
    background: #e4e8f1;
    color: #48576a;
    margin-bottom: 0;
    font-size: 12px;
    font-style: normal;
}

.bootstrap-select .actions-btn {
    height: 34px;
}

[dir="rtl"] .bootstrap-select .dropdown-toggle .filter-option {
    right: 0;
    margin-right: auto;
    text-align: right;
    padding-right: 16px;
}

.dropdown-menu>li>a:focus,
.dropdown-menu>li>a:hover {
    outline: 0 !important;
    background: #e4e8f1;
}

.btn-icon {
    padding: 3px 6px !important;
    margin-right: 2px;
}

.hide-menu {
    padding: 18px 14px 18px 14px;
    font-size: 14px;
    float: left;
    color: #D0D0D0;
    /*border-right: 1px solid #54606f;*/
    cursor: pointer;
    line-height: 27px;
}

body.rtl .hide-menu {
    float: right;
    border-left: 1px solid #5E646D;
}

.hide-menu:hover {
    opacity: 0.7;
}

.spinning {
    animation: spin 1s infinite linear;
    -webkit-animation: spin2 1s infinite linear;
}

@-webkit-keyframes spin {
    from {
        -webkit-transform: scale(1) rotate(0deg);
        transform: scale(1) rotate(0deg);
    }

    to {
        -webkit-transform: scale(1) rotate(360deg);
        transform: scale(1) rotate(360deg);
    }
}

@keyframes spin {
    from {
        -webkit-transform: scale(1) rotate(0deg);
        transform: scale(1) rotate(0deg);
    }

    to {
        -webkit-transform: scale(1) rotate(360deg);
        transform: scale(1) rotate(360deg);
    }
}

@-webkit-keyframes spin2 {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@media (max-width: 1024px) {
    .s_table {
        overflow-y: scroll;
    }
}

body.rtl.hide-sidebar #side-menu {
    display: none;
}

body.rtl.hide-sidebar #wrapper {
    margin-right: 0 !important;
}

#mobile-search ul {
    margin-bottom: 0;
    margin-top: 10px;
}

#mobile-search #top_search {
    width: 100% !important;
}

#mobile-search #top_search_button button {
    color: #fff !important;
    height: 42px;
    border-right: 0;
    margin-right: 17px;
}

#mobile-search #top_search input {
    margin-top: -2px;
    height: 42px;
}

.safari #mobile-search #top_search input {
    padding-top: 11px;
}

#mobile-search #top_search_button {
    position: absolute;
    right: 0;
    top: 55px;
    z-index: 5;
}











/* iPad Landscape */

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: landscape) {
    #mobile-search ul {
        margin-top: 0px;
    }

    #mobile-search #top_search_button {
        top: 53px;
    }

    body.rtl #mobile-search #top_search_button {
        left: -24px;
        right: auto;
    }

    .total-column,
    .top_stats_wrapper {
        margin-bottom: 10px;
    }

    .total-column:last-child {
        margin-bottom: 0;
    }
}










/* iPad Pro Portrait and Landscape */

@media only screen and (min-device-width: 1024px) and (max-device-width: 1366px) and (-webkit-min-device-pixel-ratio: 1.5) {
    #mobile-search #top_search_button {
        top: 53px;
    }

    #mobile-search ul {
        margin-top: 0px;
    }

    body.rtl #mobile-search #top_search_button {
        left: -24px;
        right: auto;
    }

    .total-column,
    .top_stats_wrapper {
        margin-bottom: 10px;
    }

    .total-column:last-child {
        margin-bottom: 0;
    }
}

#mobile-search #mobile-search ul {
    margin-bottom: 0;
}

@media (min-width: 1366px) {
    #mobile-search ul {
        margin-top: 0px;
    }
}

@media (min-width: 768px) {
    #mobile-search #top_search_button button {
        margin-top: 6px;
    }
}

@media (max-width: 768px) {

    body.show-sidebar #wrapper:after {
        content: '';
        display: block;
        position: absolute;
        left: 0;
        top: -42px;
        width: 100%;
        height: calc(100% + 42px);
        background-color: rgba(45, 62, 80, .79);
        pointer-events: auto;
        opacity: 1;
        z-index: 55;
        -webkit-transition: opacity 0.3s cubic-bezier(0, 0, 0.3, 1);
        transition: opacity 0.3s cubic-bezier(0, 0, 0.3, 1);
    }

    html+body.show-sidebar,
    body.show-sidebar,
    body.show-sidebar #wrapper {
        overflow-x: hidden;
    }

    body.show-sidebar #mobile-search {
        display: none;
    }

    .content {
        padding: 15px 15px 15px 15px;
        min-width: 320px;
    }

    .s_table {
        overflow-y: scroll;
    }

    .hide-menu {
        margin-right: 5px;
    }

    .navbar-nav>li>a {
        line-height: 20px;
    }

    .project-heading {
        margin-bottom: 15px;
        text-align: center;
    }

    .post-image-wrapper-1,
    .post-image-wrapper-2,
    .post-image-wrapper-3 {
        width: 100% !important;
    }

    .leads-search {
        margin-top: 15px;
    }

    .kan-ban-col {
        width: 260px !important;
    }

    #top_search {
        width: 100% !important;
    }

    #top_search_button button {
        color: #fff !important;
    }

    #top_search input {
        margin-top: -10px !important;
        padding-top: 3px;
    }

    #mobile-search #top_search_button button {
        margin-top: 5px;
        font-size: 17px;
        margin-right: 0;
    }

    .total-column,
    .top_stats_wrapper {
        margin-bottom: 10px;
    }

    .total-column:last-child {
        margin-bottom: 0;
    }

    #top_search_button {
        position: absolute;
        right: 0;
        top: 63px;
        z-index: 999;
    }

    #top_search_button button {
        border-right: 0;
    }

    #mobile-search ul {
        margin-bottom: 0;
    }

    .checklist .remove-checklist {
        position: absolute;
        right: 0;
    }

    .checklist {
        cursor: initial;
    }

    .task-info {
        width: 100%;
        margin-left: 0 !important;
        margin-bottom: 7px !important;
    }

    .toggle_view {
        display: none !important;
    }

    .notification-box {
        line-height: 15px;
    }

    .project-progress-bars {
        margin-top: 20px;
    }

    body.show-sidebar .screen-options-area,
    body.show-sidebar .screen-options-btn {
        display: none !important;
    }

    body.rtl #top_search_button {
        right: initial;
        left: 0;
        text-align: left;
        border-right: 0;
    }

    body.rtl #top_search_button button {
        border-right: 0;
        height: 40px !important;
        float: left;
        width: 39px;
    }

    body.rtl #top_search {
        z-index: 98;
        width: 80%;
    }

    body.rtl .hide-menu {
        margin-right: 0;
        float: right;
    }

    body.rtl.show-sidebar #menu {
        z-index: 100;
    }

    body.rtl .small-logo {
        margin-left: 10px;
    }

    body.rtl .mobile-menu-toggle {
        float: right;
        margin-left: 0;
    }

    body.rtl #wrapper {
        margin-right: 0 !important;
    }

    body.rtl.page-small.show-sidebar #wrapper {
        margin-left: 0;
    }

    body.rtl #side-menu li.quick-links .btn {
        padding: 8px 9px 9px 12px;
    }

    body.rtl.show-sidebar #side-menu {
        display: block;
    }
}

@media (min-width: 769px) {

    .mobile-navbar,
    .mobile-navbar.collapse.in {
        display: none;
    }
}

@media (max-width: 768px) {

    .navbar-right,
    #logo {
        display: none;
    }

    .small-logo {
        display: block;
        float: left;
        height: 63px;
        padding: 10px 0;
    }

    .small-logo img {
        width: auto;
        max-height: 100%;
        width: auto\9;
        margin-top: 6px;
        height: 34px;
    }

    .mce-floatpanel {
        width: 70% !important;
    }

    body.rtl input[type="file"].dz-hidden-input {
        display: none;
    }
}

@media (max-width: 768px) {
    .leads-filter-column {
        margin-bottom: 5px;
    }
}

@media (max-width: 320px) {
    #wrapper {
        overflow: hidden;
    }
}

.mobile-menu {
    display: none;
}

.add-post-attachments {
    padding: 8px 14px;
    font-size: 14px;
}

@media (max-width: 768px) {

    .mobile-menu-toggle,
    .open-customizer-mobile {
        display: block;
        color: #fff;
        margin-right: 0;
        line-height: 25px;
    }

    .mobile-menu {
        display: block;
    }

    ul.mobile-icon-menu {
        position: relative;
        display: inline-block;
        float: right;
        margin-bottom: 0px;
    }

    ul.mobile-icon-menu>li {
        float: right;
        line-height: 63px;
        border-right: 1px solid #505c6b;
        position: relative;
    }

    ul.mobile-icon-menu>li>a {
        padding: 0 6px 0 6px;
        color: #fff;
    }

    ul.mobile-icon-menu>li:last-child {
        border-left: 1px solid #505c6b;
    }

    ul.mobile-icon-menu .dropdown-menu {
        right: -3px;
        left: auto;
        margin-top: 1px;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
    }

    ul.mobile-icon-menu .icon-total-indicator {
        line-height: 12px;
        padding: 2px 5px;
        padding-bottom: 3px;
        color: #fff;
        position: absolute;
        right: -4px;
        font-size: 11px;
        top: 14px;
        font-family: Verdana, serif;
        vertical-align: middle;
        text-align: center;
        border-radius: 8px;
    }

    ul.mobile-icon-menu ul.started-timers-top li a._timer+p {
        margin-top: -25px;
        margin-bottom: 0;
    }

    ul.mobile-icon-menu #top_start_timer p {
        line-height: 17px;
    }

    ul.mobile-icon-menu .notifications .notification-box img {
        margin-right: 8px;
    }

    ul.mobile-icon-menu .divider-top-started-timers {
        display: block;
        margin-top: 5px;
        margin-bottom: 15px;
    }

    ul.mobile-icon-menu li.timer {
        line-height: 20px;
    }

    .notifications.width400 {
        width: 300px;
    }

    .mobile-menu .navbar-nav>li,
    .mobile-menu .navbar-nav {
        float: none;
    }

    #new-post-form button[type="submit"],
    #new-post-form .add-post-attachments,
    #new-post-form .bootstrap-select {
        display: inline-block;
        margin-bottom: 10px;
        width: 100%;
    }
}

.attachments .attachment:last-child .form-group {
    margin-bottom: 0;
}

form.dropzone {
    background-color: #fbfdff;
    border: 1px dashed #c0ccda;
    border-radius: 6px;
}

form.dropzone input[type="file"] {
    display: none;
}

.dropzone .dz-message {
    margin-top: 45px;
    color: #03a9f4;
}

.dropzone .dz-preview .dz-image {
    border-radius: 0;
    background: #03a9f4;
}

.dz-details,
.dz-details:hover {
    background: #03a9f4;
    opacity: 1;
    color: #fff;
    height: 120px;
}

.dropzone .dz-preview .dz-details .dz-filename span,
.dropzone .dz-preview .dz-details .dz-size span {
    background: #323a45;
    color: #fff;
}

.dropzone .dz-preview .dz-details .dz-filename:hover span {
    color: #323a45;
}

table.table {
    margin-top: 25px;
}

.chart {
    max-width: 100%;
    min-width: 100%;
}

.warning-bg {
    background: #FF6F00 !important;
    color: #fff !important;
    border: 1px solid #FF6F00 !important;
}

.success-bg {
    background: #84c529 !important;
    color: #fff !important;
    border: 1px solid #84c529 !important;
}

.primary-bg {
    background: #03a9f4;
    color: #fff !important;
    border: 1px solid #03a9f4;
}

.info-bg {
    background: #03A9F4 !important;
    color: #fff !important;
    border: 1px solid #03A9F4 !important;
}

.danger-bg {
    background: #FC2D42 !important;
    color: #fff !important;
    border: 1px solid #FC2D42 !important;
}

.panel-body.todo-body {
    padding: 0;
}

.todo-title {
    margin: 0;
    line-height: 30px;
    padding: 0 0 0 17px;
    font-weight: 500;
    font-size: 13px;
}

ul.todo {
    margin-bottom: 0;
}

ul.todo li {
    display: block;
    position: relative;
    overflow: hidden;
    margin: 0 5px;
    border-bottom: 1px solid #f0f0f0;
}

.dragger {
    cursor: move;
    background: url(../images/dragger.png) 1px 11px no-repeat;
}

li.no-todos {
    background: none;
}

ul.todo {
    list-style: none !important;
}

ul.todo li.ui-sortable-handle:last-child,
ul.todo li:last-child {
    border-bottom: 0;
}

.todo-description {
    position: relative;
    padding: 10px 10px 0 10px;
    width: 100%;
    display: block;
}


body.rtl .todo-date {
    padding-top: 10px;
}

body.rtl .todo-checkbox {
    padding-right: 40px !important;
}

.todo-date {
    padding: 0 0 10px 0;
    display: block;
}

.todo-checkbox {
    padding-left: 40px;
    float: left;
}

.sortable {
    min-height: 60px;
}

.no-radius {
    border-radius: 0;
}

.table>thead>tr>th {
    padding: 10px;
    vertical-align: middle;
    color: #2d2d2d;
    font-weight: 400;
    border-top: 1px solid #f0f0f0 !important;
    border-left: 0;
    border-right: 1px solid #f0f0f0;
    border-bottom: 0;
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_desc_disabled:after {
    top: 12px;
}

table.dataTable thead .sorting:after {
    opacity: 0.05;
}

.table thead tr th:first-child {
    border-left: 1px solid #dcdcdc;
}

.table thead tr th:last-child {
    border-right: 1px solid #dcdcdc;
}

.table>thead>tr>th {
    border-bottom: 0;
    vertical-align: middle;
    color: #2d2d2d;
    font-weight: 400;
}

.table>tbody>tr>td,
.table>tfoot>tr>td {
    padding: 10px 10px 5px 10px;
}

.selecpicker:active,
.selecpicker:focus {
    outline: 0;
    outline-offset: 0;
    border: 0;
}

#post,
#post:focus {
    margin-top: 25px;
    -moz-appearance: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 0;
    -webkit-transition: none;
    transition: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: 0;
    outline: 0;
    overflow: auto;
    resize: none;
}

.post-image-wrapper {
    position: relative;
    float: left;
    width: 100%;
    overflow: hidden;
    cursor: pointer;
    border: 1px solid #EEF2F4;
}

.post-image-wrapper-1 {
    width: 100%;
    height: auto;
}

.post-image-wrapper-2 {
    width: 50%;
    max-height: 150px;
}

.post-image-wrapper-3 {
    width: 33.333333%;
    max-height: 150px;
}

.post-image-wrapper img,
img.img-gallery {
    display: block;
    min-width: 100% !important;
}

.btn-post-options {
    margin-top: -15px;
}

#newsfeed {
    background: #e3e8ee;
    width: 100%;
    height: 100%;
    z-index: 999;
    position: fixed;
    overflow-y: scroll;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow-x: hidden;
    padding: 15px;
}

#newsfeed_images_modal,
body.noscroll .backdrop {
    z-index: 99999999999;
}

.newsfeed_wrapper {
    height: 100%;
    width: 100%;
    overflow: scroll;
}

#newsfeed .close_newsfeed {
    position: absolute;
    right: 30px;
    z-index: 1000;
    width: 50px;
    height: 50px;
    border-radius: 100%;
    background: #868686;
    border: none;
    outline: none;
    color: #FFF;
    font-size: 36px;
    -webkit-box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    -webkit-transition: .3s;
    transition: .3s;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

#newsfeed .close_newsfeed:hover,
#newsfeed .close_newsfeed:active {
    color: #f0f0f0;
    background: #6b6b6b;
}

#newsfeed .close_newsfeed i {
    position: absolute;
    top: 13px;
    left: 15px;
    font-size: 25px;
}

#newsfeed .newsfeed_post.pinned>.panel-body {
    background: #E6FAFF;
    border: 1px dashed #B9DAE2;
}

#newsfeed .newsfeed_post .dropdown {
    position: absolute;
    right: 20px;
}

#newsfeed #new-post-form .dz-message {
    display: none;
}

#newsfeed #new-post-form.dropzone .dz-preview {
    margin-bottom: 0;
}

#newsfeed #new-post-form.dropzone .dz-progress {
    display: none;
}

#newsfeed #new-post-form.dropzone {
    padding: 0;
}

#newsfeed form#new-post-form.dropzone {
    border: 0;
}

#new-post-form.dropzone-active {
    border: 3px dashed #eef2f4;
    padding: 10px;
}

#newsfeed .post-time-ago {
    position: absolute;
    right: 55px;
    top: 34px;
}

#newsfeed .post-comment {
    padding: 10px 15px !important;
}

#newsfeed .comment-image {
    margin-right: 10px;
}

#newsfeed .comment-input {
    padding-top: 1px;
}

#newsfeed .comment {
    margin-top: 15px;
}

#newsfeed .comment:first-child {
    margin-top: 0;
}

#newsfeed .newsfeed_post .panel-footer {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

#newsfeed .newsfeed_post .post-comment.panel-footer,
#newsfeed .newsfeed_post .post-likes.panel-footer {
    background: #f7f9fa;
    border: 1px solid #e4e5e7;
    padding: 7px 15px;
    border-bottom: 0;
    color: #323a45;
}

#newsfeed .newsfeed_post .post-content {
    border-bottom: 0;
    font-size: 14px;
}

#newsfeed .newsfeed_post .user-comment.panel-footer {
    border-bottom: 1px solid #e4e5e7;
    border-top: 1px solid #e4e5e7;
}

#newsfeed .newsfeed_post .user-post-like {
    background: #fff;
    border-left: 1px solid #e4e5e7;
    border-right: 1px solid #e4e5e7;
    border-top: 0;
}

.modal_like_area {
    padding: 10px;
    border-bottom: 1px solid #e4e5e7;
}

.modal_like_area img {
    margin-right: 10px;
    float: left;
}

.modal_like_area:last-child {
    border-bottom: 0;
}

.likes_modal .modal-body {
    padding-top: 0;
    padding-bottom: 0;
}

.likes_modal .modal-footer,
.likes_modal .modal-footer {
    text-align: center;
}

#_task_modal {
    z-index: 99999;
}

.mce-floatpanel {
    z-index: 999999 !important;
}

#mce-modal-block.mce-in {
    background-color: rgba(45, 62, 80, .79);
    opacity: 0.8 !important;
}

.remove-task-user {
    position: absolute;
    top: 35px;
    left: 12px;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: visibility 0s, opacity 0.5s linear;
    transition: visibility 0s, opacity 0.5s linear;
}

.task-user {
    border: 1px solid #F0F0F0;
    padding: 2px;
    border-radius: 50%;
    vertical-align: middle;
    margin-bottom: 5px;
    display: inline-block;
    position: relative;
}

.task-user:hover,
.task-user:active {
    border: 1px dashed #03a9f4;
}

.task-user:hover>a+.remove-task-user {
    visibility: visible;
    opacity: 1;
}

.task-info {
    padding: 6px;
}

.task-info .task-info-icon {
    text-align: center;
    margin-left: -9px;
    margin-right: 8px;
    font-size: 15px;
    color: #6f6f6f;
}

.task-info-separator {
    margin: 10px -20px 10px 0;
    border-top: 1px solid #e6e6e6;
    -webkit-box-shadow: 0 1px 6px 1px rgba(255, 255, 255, 0.5);
    box-shadow: 0 1px 6px 1px rgba(255, 255, 255, 0.5);
}

.task-info-col {
    padding: 5px;
    border: 1px solid #03A9F4;
}

.task-info-col:last-child {
    border-left: 0;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}

.task-info-col:first-child {
    border-right: 0;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.task-info-col i {
    font-size: 16px;
}

.task-staff-name {
    vertical-align: middle;
    font-size: 11px;
    color: #616161;
    padding-left: 5px;
    padding-right: 5px;
}

.proposal-comments .proposal-comment {
    padding: 5px;
    border-radius: 4px;
    background: #fff;
}

#task .panel-body {
    padding-bottom: 0;
}

#task #name {
    font-size: 18px;
    font-weight: 500;
}

@media (max-width: 768px) {
    .task-user {
        margin-top: 10px;
    }

    .remove-task-user {
        visibility: visible;
        opacity: 1;
    }
}

.btn-group.open .dropdown-toggle {
    border: 0;
    outline: 0;
    box-shadow: inset 0 0 0 rgba(0, 0, 0, .125);
    -webkit-box-shadow: inset 0 0 0 rgba(0, 0, 0, .125);
}

.quick-top-stats h5 {
    margin: 0 0 8px 0;
}

.invoice-top {
    margin-bottom: 5px;
}

.home-activity .tab-content {
    height: 420px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 15px;
}

body.rtl .home-activity .tab-content {
    padding-right: 0;
    padding-left: 15px;
}

.user-data .home-activity.panel-body {
    padding-top: 10px;
}

.user-data .home-activity .nav.nav-tabs>li>a {
    color: #333 !important;
}

.firefox .user-data .home-activity .nav.nav-tabs>li>a {
    line-height: 14px;
}

.user-data .home-activity .nav.nav-tabs {
    border: 0;
    margin-top: -10px;
    margin-bottom: 40px;
    padding-top: 5px;
    background: #fff !important;
}

.user-data .hr-user-data-tabs {
    margin-top: -40px;
    z-index: 1;
    position: relative;
}

.mobile .user-data .hr-user-data-tabs {
    margin-top: -26px;
}

.user-data .arrow-right {
    margin-right: -20px;
}

.user-data .arrow-left {
    margin-left: -20px;
}

.user-data .horizontal-scrollable-tabs .scroller {
    border-top: 0px !important;
    padding: 11.5px 10px;
}

.firefox .user-data .horizontal-scrollable-tabs .scroller {
    padding: 9px 10px;
}

.user-data .home-activity .nav-tabs>li.active>a,
.user-data .home-activity .nav-tabs>li.active>a:focus,
.user-data .home-activity .nav-tabs>li.active>a:hover,
.user-data .home-activity .nav-tabs>li>a:focus,
.user-data .home-activity .nav-tabs>li>a:hover {
    border-bottom: 0;
    color: #008ece !important;
    margin-bottom: 2px;
}

.user-data .home-activity .nav-tabs>li.active>a,
.user-data .home-activity .nav-tabs>li.active>a:focus,
.user-data .home-activity .nav-tabs>li.active>a:hover {
    font-weight: 500;
}

.user-data .home-activity i.menu-icon {
    width: 10px;
    font-size: 16px;
}

.table.items input:not(.input-transparent),
.table.items textarea,
.table.items .bootstrap-select .btn-default {
    border: 1px solid #bfcbd9 !important;
}

.table.items thead {
    background: #415164;
    color: #fff;
    border: 0;
}

.table.items thead>tr>th {
    border: 0 !important;
    color: #fff !important;
}

._transaction_form .table.items thead>tr>th {
    min-width: 100px;
}

._transaction_form .table.items thead>tr>th:last-child,
._transaction_form .table.items thead>tr>th:first-child {
    min-width: auto;
}

.table.items tbody>tr>td,
.table.items thead>tr>th {
    padding: 8px 10px;
}

.table.items .main {
    margin-top: 35px;
    background-color: #fbfdff;
}

.table.items tbody>tr>td>.btn-icon {
    margin-right: 10px;
    padding: 0 4px 0;
}

.bs-searchbox {
    border-bottom: 1px solid #f0f0f0;
    padding: 10px;
}

.bs-actionsbox {
    padding: 10px;
}

.bs-searchbox+.bs-actionsbox {
    padding: 10px 8px 4px;
}

.payment-preview-wrapper {
    background: #84c529;
    padding: 15px;
    text-align: center;
    color: #fff;
    margin-top: 25px;
    font-size: 16px;
}

#top_search {
    display: inline-block;
    width: 320px;
}

#top_search input {
    border-radius: 0;
    background: transparent;
    height: 63px;
    border: 0;
    width: 100%;
    color: #fff;
    font-size: 15px;
}

#top_search input:active,
#top_search input:focus,
#top_search input:hover {
    border: 0;
    background-color: rgba(0, 0, 0, 0.2);
    color: #fff;
    -webkit-transition: background-color 0.2s ease-out 0.2s;
    transition: background-color 0.2s ease-out 0.2s;
}

#top_search_button button {
    border-radius: 0;
    height: 63px;
    width: 60px;
    padding: 8px 0 5px 5px;
    font-size: 20px;
    color: rgba(255, 255, 255, 0.5);
    background: transparent;
    font-weight: 500;
}

body.rtl #top_search_button button {
    margin-right: 0;
}

#top_search_dropdown {
    width: 400px;
}

ul.search-results {
    max-height: 600px;
    overflow-y: scroll;
    margin-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

ul.search-results .dropdown-header {
    font-size: 13px;
    padding-left: 15px;
}

ul.search-results .dropdown-header:first-child {
    padding-top: 10px;
}

ul.search-results li.divider:first-child {
    display: none;
}

ul.search-results a {
    color: #03a9f4;
}

body.rtl .colorpicker.colorpicker-visible.dropdown-menu {
    left: 0 !important;
    right: auto;
    padding-left: 3px;
    margin-left: 45px;
}

body.rtl #side-menu .arrow,
body.rtl #setup-menu .arrow {
    margin-left: 15px;
}

body.rtl .navbar-nav>li>a .staff-profile-image-small {
    margin-left: 15px;
}

body.rtl .mleft10 {
    margin-right: 10px;
}

body.rtl .quick-links .dropdown-menu {
    float: right;
    right: auto;
}

body.rtl .checkbox {
    padding-left: 0;
    padding-right: 20px;
}

body.rtl .checkbox label {
    padding-right: 5px;
}

body.rtl table .checkbox label {
    padding-right: 0;
}

body.rtl .checkbox label::before {
    left: auto;
    right: 0;
    margin-left: 0;
    margin-right: -20px;
}

body.rtl .checkbox label::after {
    left: auto;
    right: 0;
    margin-left: 0;
    margin-right: -20px;
    padding-left: 0;
    padding-right: 3px;
}

body.rtl .radio {
    padding-left: 0;
    padding-right: 20px;
}

body.rtl .radio label {
    padding-left: 0;
    padding-right: 5px;
}

body.rtl .radio label::before {
    left: auto;
    right: 0;
    margin-left: 0;
    margin-right: -20px;
}

body.rtl .radio label::after {
    left: auto;
    right: 3px;
    margin-left: 0;
    margin-right: -20px;
}

body.rtl #logo {
    float: right;
}

body.rtl .header-left {
    float: right;
}

body.rtl .navbar-right {
    margin-left: 0;
}

body.rtl #menu {
    right: 0;
    left: auto;
}

body.rtl #wrapper {
    margin-right: 210px;
    margin-left: 0;
}

body.rtl #setup-menu {
    right: 0;
}

body.rtl .datepicker.dropdown-menu {
    right: auto;
}

body.rtl div[class^="toolbar-container"] {
    direction: rtl;
}

body.rtl .table thead th {
    text-align: right;
}

body.rtl .btn {
    text-align: right;
}

.col-xs-5ths,
.col-sm-5ths,
.col-md-5ths,
.col-lg-5ths {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
}

.col-xs-5ths {
    width: 20%;
    float: left;
}

@media (min-width: 768px) {
    .col-sm-5ths {
        width: 20%;
        float: left;
    }
}

@media (min-width: 992px) {
    .col-md-5ths {
        width: 20%;
        float: left;
    }
}

@media (min-width: 1200px) {
    .col-lg-5ths {
        width: 20%;
        float: left;
    }
}

.total-column {
    word-break: break-all;
    text-align: center;
}

h3._total {
    font-weight: 400;
    margin: 0;
    font-size: 20px;
}

.stats-total-currency .panel_s .panel-body {
    padding: 13px;
}

.total-column .panel-body {
    background: #f9fafc;
    padding: 10px 5px;
}

.total-column span {
    word-break: break-word;
}

.nav-tabs {
    padding-bottom: 0;
    margin-bottom: 25px;
    background: transparent;
    border-radius: 1px;
    padding-left: 0;
    padding-right: 0;
    border-top: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
}

.nav-tabs>li {
    border: 0;
}

.nav-tabs>li>a {
    border: 0;
    border-bottom: 2px solid transparent;
    background: transparent;
    color: #333;
    padding: 12px 13px 12px 13px;
    font-weight: 400;
}

.nav-tabs>li.active>a,
.nav-tabs>li.active>a:focus,
.nav-tabs>li.active>a:hover,
.nav-tabs>li>a:focus,
.nav-tabs>li>a:hover {
    border: 0;
    border-radius: 0;
    border-bottom: 1px solid #02a9f4;
    background: transparent;
    color: #008ece;
    margin-bottom: 1px;
}

.navbar-pills.nav-tabs>li>a {
    border-bottom: 1px solid #f0f0f0;
}

.nav-tabs>li.active>a,
.nav-tabs>li.active>a:focus,
.nav-tabs>li.active>a:hover,
.navbar-pills.nav-tabs>li>a:focus,
.navbar-pills.nav-tabs>li>a:hover {
    border-bottom: 1px solid #02a9f4;
}

.navbar-pills-flat {
    background: #fff;
    -webkit-box-shadow: 0 1px 15px 1px rgba(90, 90, 90, 0.08);
    box-shadow: 0 1px 15px 1px rgba(90, 90, 90, 0.08);
}

.nav-tabs.navbar-pills-flat>li a i.menu-icon {
    font-size: 15px;
    width: 12px;
    margin-top: 1px;
}

.nav-tabs.navbar-pills-flat>li.active>a,
.nav-tabs.navbar-pills-flat>li.active>a:focus,
.nav-tabs.navbar-pills-flat>li.active>a:hover {
    font-weight: 500;
}

.navbar-pills.navbar-pills-flat.nav-tabs>li:first-child>a {
    border-top: 1px solid #e5e5e5;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.navbar-pills.navbar-pills-flat.nav-tabs>li:last-child>a {
    border-bottom: 1px solid #e5e5e5;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.navbar-pills.navbar-pills-flat.nav-tabs>li>a {
    border-bottom: 1px solid #e5e5e5;
    margin-right: 0;
    padding: 10px 17px 10px 17px;
    border-right: 1px solid #e5e5e5;
    border-left: 1px solid #e5e5e5;
    border-radius: 0;
}

.home-stats .col-md-5ths {
    width: 19.5%;
}

.table .alert-info {
    color: #31708f !important;
    background-color: #f4faff !important;
    border-color: #ebf5ff !important;
}

.goal-progress {
    text-align: center;
    position: relative;
}

.goal-percent {
    position: absolute;
    top: 100px;
    left: 0;
    width: 100%;
    text-align: center;
    line-height: 40px;
    font-size: 50px;
}

table .goal-progress {
    display: inline-block;
}

table .goal-percent {
    font-size: 10px;
    text-align: center;
    left: 1px;
    top: 22px;
    line-height: 0;
}

@media (max-width: 768px) {
    .nav-tabs>li {
        width: 100%;
    }

    .nav-tabs>li>a {
        border-radius: 0;
    }

    .single-option-buttons {
        display: inline-block;
        margin: 15px 0 15px 0;
    }

    .single-option-buttons a,
    .single-option-buttons button {
        width: 100%;
        margin-bottom: 5px;
    }

    .option-buttons {
        float: right;
    }

    .option-buttons .mleft4 {
        margin-left: 0 !important;
    }

    .option-buttons a,
    .option-buttons .pull-right {
        float: left !important;
        margin-right: 4px;
    }

    #top_search_dropdown {
        width: 300px;
    }

    .table-responsive {
        padding: 15px 0 15px 0;
        border: 1px solid #F7F7F7;
    }

    #task .panel-body {
        padding-bottom: 20px;
    }

    .task-info-col {
        padding: 5px;
        border: 1px solid #03A9F4 !important;
    }

    .task-info-col:nth-child(3) {
        margin-bottom: 20px;
    }

    .home-stats .col-md-5ths {
        width: 100%;
        margin-bottom: 4px;
    }
}

.ribbon {
    position: absolute;
    right: -5px;
    top: -5px;
    z-index: 1;
    overflow: hidden;
    width: 75px;
    height: 75px;
    text-align: right;
}

.ribbon span {
    font-size: 11px;
    font-weight: 500;
    color: #FFF;
    text-transform: uppercase;
    text-align: center;
    line-height: 20px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    width: 100px;
    display: block;
    position: absolute;
    top: 19px;
    right: -21px;
}

.ribbon span::before {
    content: "";
    position: absolute;
    left: 0;
    top: 100%;
    z-index: -1;
    border-left: 3px solid #1C841B;
    border-right: 3px solid transparent;
    border-bottom: 3px solid transparent;
    border-top: 3px solid #1C841B;
}

.ribbon span::after {
    content: "";
    position: absolute;
    right: 0;
    top: 100%;
    z-index: -1;
    border-left: 3px solid transparent;
    border-bottom: 3px solid transparent;
}

.ribbon.success span {
    background: #84c529;
}

.ribbon.success span::before {
    border-left: 3px solid #1C841B;
    border-top: 3px solid #1C841B;
}

.ribbon.success span::after {
    border-right: 3px solid #1C841B;
    border-top: 3px solid #1C841B;
}

.ribbon.muted span {
    background: #6c7888;
}

.ribbon.muted span::before {
    border-left: 3px solid #606973;
    border-top: 3px solid #606973;
}

.ribbon.muted span::after {
    border-right: 3px solid #606973;
    border-top: 3px solid #606973;
}

.ribbon.danger span {
    background: #FC2D42;
}

.ribbon.danger span::before {
    border-left: 3px solid #DE1D30;
    border-top: 3px solid #DE1D30;
}

.ribbon.danger span::after {
    border-right: 3px solid #DE1D30;
    border-top: 3px solid #DE1D30;
}

.ribbon.warning span {
    background: #FF6F00;
}

.ribbon.warning span::before {
    border-left: 3px solid #CA5800;
    border-top: 3px solid #CA5800;
}

.ribbon.warning span::after {
    border-right: 3px solid #CA5800;
    border-top: 3px solid #CA5800;
}

.ribbon.info span {
    background: #03a9f4;
}

.ribbon.info span::before {
    border-left: 3px solid #03a9f4;
    border-top: 3px solid #03a9f4;
}

.ribbon.info span::after {
    border-right: 3px solid #03a9f4;
    border-top: 3px solid #03a9f4;
}

.ribbon.default span {
    background: #6c7888;
}

.ribbon.default span::before {
    border-left: 3px solid #6c7888;
    border-top: 3px solid #6c7888;
}

.ribbon.default span::after {
    border-right: 3px solid #6c7888;
    border-top: 3px solid #6c7888;
}

.feed_description img {
    display: none;
}

.attachment-icon-preview {
    font-size: 35px;
    color: #CACACA;
    margin-right: 15px;
}

.attachment-preview-icon-small {
    color: #CACACA;
    margin-right: 5px;
    font-size: 13px;
}

#dropzoneDragArea,
.dropzoneDragArea {
    background-color: #fbfdff;
    border: 1px dashed #c0ccda;
    border-radius: 4px;
    padding: 60px;
    text-align: center;
    margin-bottom: 15px;
    cursor: pointer;
}

.dropzone-manual {
    border: 0 !important;
    padding: 0;
    background: transparent;
}

.dropzone-manual.dropzone .dz-message {
    margin-top: 0;
}

.font-medium {
    font-size: 15px !important;
}

.font-medium-xs {
    font-size: 13px !important;
}

.dropzone-expense-preview {
    height: 250px;
}

.ticket-reply-tools .panel-body {
    background: #f9f9f9;
}

.ticket-reply-tools .panel-body .btn-default,
.table.items tr.main .btn-default {
    border: 1px solid #CACACA;
}

.ticketstaffnotes {
    margin: 0 0 5px 0;
    padding: 0;
    background-color: #FFFFE6;
    border: 1px dashed #DACD83;
}

.ticketstaffnotes table {
    margin: 0;
    padding: 0;
    width: 100%;
    border-bottom: 1px dashed #DACD83;
}

.ticketstaffnotes table td {
    padding: 5px 10px;
    color: #695F1F;
}

.home-summary a {
    text-transform: uppercase;
}

.home-summary a:hover,
.home-summary a:active {
    opacity: 0.7;
    color: inherit;
}

.home-summary-separator {
    border-top: 1px dashed #EEF2F4;
}

.home-summary ._total {
    font-size: 14px;
}

.home .home-summary {
    font-size: 12px;
}

.expenses-report {
    margin-top: 0;
}

#expenses-report-table.table tbody td {
    padding: 12px 10px 12px 10px;
}

#expenses-report-table.table>thead>tr>th {
    background: #323a45;
    color: #fff;
    border: 0;
}

.mime {
    background-repeat: no-repeat;
    background-position: 0 0;
    padding: 1px 0 4px 26px;
}

.mime-word {
    background-image: url(../images/mime/word.png);
}

.mime-excel {
    background-image: url(../images/mime/excel.png);
}

.mime-powerpoint {
    background-image: url(../images/mime/powerpoint.png);
}

.mime-pdf {
    background-image: url(../images/mime/pdf.png);
}

.mime-zip {
    background-image: url(../images/mime/zip.png);
}

.mime-image {
    background-image: url(../images/mime/image.png);
}

.mime-file {
    background-image: url(../images/mime/file.png);
}

.mime-video {
    background-image: url(../images/mime/video.png);
}

.mime-audio {
    background-image: url(../images/mime/audio.png);
}

.files-container {
    margin: 0 0 10px;
}

.files {
    padding-left: 0;
    list-style: none;
    margin: 0;
}

.mime>a {
    color: #555 !important;
    text-decoration: none;
}

.lead-attachment-wrapper hr,
.invoice-attachment-wrapper hr {
    margin-top: 0;
}

#lead_attachments .lead-attachment-wrapper:first-child {
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

#sales_attach_file .sales-attach-file-preview {
    padding-top: 5px;
}

#sales_attach_file .sales-attach-file-preview:first-child {
    border-top: 1px solid #f0f0f0;
    padding-top: 30px;
}

#contract_attachments .contract-attachment-wrapper {
    padding-bottom: 8px;
}

#contract_attachments .contract-attachment-wrapper:first-child {
    border-top: 1px solid #f0f0f0;
    padding-top: 30px;
}

.contract-tab {
    margin-top: -21px;
}

.tabs-in-body-no-margin {
    margin-left: -20px;
    margin-right: -20px;
}

.safari .tabs-in-body-no-margin {
    margin-left: 0px;
    margin-right: 0px;
}

.table .item.ui-sortable-helper {
    background: #f0f0f0;
    opacity: 0.5;
    border: 0 !important;
}

.table .item.ui-sortable-helper input,
.table .item.ui-sortable-helper textarea {
    background: transparent;
}

table.items tr.main td {
    padding-top: 25px;
    padding-bottom: 25px;
}

.notification_link:hover {
    color: #03a9f4;
}

.customer_map {
    height: 500px;
}

.customer-profile-tabs {
    margin-top: -16px;
    margin-left: -20px;
    margin-right: -20px;
}

.customer-profile-attachment {
    border: 1px solid #f0f0f0;
    padding: 20px;
    height: 200px;
}

.customeer-profile-attachment-image {
    max-height: 200px;
    overflow: hidden;
}

.customeer-profile-attachment-image img {
    width: 100%;
    height: 100%;
}


.kan-ban-wrapper {
    overflow: auto;
}

#kan-ban .panel-heading-bg {
    padding: 9px;
    position: relative;
    border-bottom: 1px solid #e4e5e7;
}

.kb-kan-ban#kan-ban {
    overflow: auto;
}

#kan-ban .panel-body {
    border-top: 0;
    border-radius: 4px;
    padding: 15px 20px;
    margin: 10px 5px 10px 5px;
    -webkit-box-shadow: 0 1px 15px 1px rgba(90, 90, 90, 0.08);
    box-shadow: 0 1px 15px 1px rgba(90, 90, 90, 0.08);
}

.kan-ban-content {
    background: #e3e8ee;
    padding: 7px;
}

.kan-ban-content .kanban-empty h4 {
    color: #50637c;
}

#kan-ban .panel-body .lead-name,
#kan-ban .panel-body .task-name {
    background: url(../images/dragger.png) 10px 10px no-repeat;
    padding-left: 25px;
}

#kan-ban .not-sortable .panel-body .task-name {
    background: none;
}

.ms-task .sortable {
    background: url(../images/dragger.png) 1px 2px no-repeat;
    padding-left: 14px;
}

#kan-ban .border-right:last-child {
    border-right: 0;
}

.kan-ban-col {
    width: 340px;
    margin-right: 6px;
    display: inline-block;
    float: left;
}

.kan-ban-col:last-child {
    margin-right: 0;
}

.kan-ban-content-wrapper {
    word-break: break-word;
    word-wrap: break-word;
    overflow-x: hidden;
    overflow-y: auto;
}

.kan-ban-expand-top {
    position: absolute;
    top: 0px;
    right: 0px;
    background: #f0f0f0;
    padding: 3px 7px;
    border-top-right-radius: 4px;
    font-size: 11px;
}

#kan-ban li,
#kan-ban ul {
    list-style: none !important;
}

#kan-ban .panel-heading-bg {
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}

.cpicker {
    cursor: pointer;
    border-radius: 2%;
}

.cpicker {
    float: left;
    margin-right: 15px;
    border-radius: 1px;
}

.kan-ban-settings {
    display: inline-block;
    padding: 18px 0 7px 10px;
}

.cpicker-small {
    width: 10px;
    height: 10px;
}

.cpicker-big {
    width: 20px;
    height: 20px;
}

.color-white[href]:hover,
.color-white[href]:active {
    color: #f0f0f0;
}

.color-white,
.color-white[href] {
    color: #fff;
}

.task-icon {
    border-radius: 50%;
    padding: 3px;
    margin-right: 6px;
    vertical-align: middle;
}

.table .task-icon {
    font-size: 11px;
    margin-left: 7px;
    margin-right: -3px;
}

body.rtl .table .task-icon {
    margin-left: -3px;
    margin-right: 7px;
}

.task-finished-icon {
    color: #84c529;
    border: 1px dashed #84c529;
}

.task-unfinished-icon {
    color: #6F6F6F;
    border: 1px dashed #6F6F6F;
}

.main-tasks-table-href-name {
    display: inherit;
    font-size: 14.5px;
}

body.rtl .main-tasks-table-href-name {
    float: right;
}

body.rtl a+.main-tasks-table-href-name {
    float: right;
}


.kan-ban-body .panel_s {
    margin-bottom: 0;
    position: relative;
}

#kan-ban .not-sortable {
    opacity: 0.7;
}

#kan-ban .popover,
.milestone-column .popover {
    width: 340px;
    max-width: 340px;
    text-align: center;
}

.popover-250 .popover {
    width: 250px;
    max-width: 250px;
}

.ui-state-highlight-card {
    height: 130px !important;
    border: 2px dashed #D1D1D1;
    margin-bottom: 15px;
    display: block;
    width: 95.5%;
    margin-left: 7px;
}

.ui-state-highlight-kan-ban-kb {
    height: 100px;
    border: 2px dashed #D1D1D1;
    margin-bottom: 15px;
    display: block;
    width: 95.5%;
    margin-left: 7px;
}

.modal .task-single-col {
    z-index: 999999999999;
}

.task-modal-single {
    z-index: 10001;
}


#task-comments .comment-wrapper {
    display: block;
    width: 100%;
}

#task-comments .comment-content {
    word-break: break-word;
    overflow-x: scroll;
    overflow-wrap: break-word;
    overflow: auto;
}

.firefox #task-comments .comment-content {
    word-break: initial;
}

#task_comment {
    line-height: initial;
    height: initial;
    opacity: 0.7;
}

.task-modal-single .edit-task-comment {
    padding-top: 15px;
}

.task-modal-single .task-comment {
    padding: 10px 8px 0 8px;
    margin-bottom: 10px;
}

.task-modal-single .task-comment.highlight-bg:last-child {
    padding-bottom: 10px;
}

.task-modal-single .task-comment.highlight-bg {
    border-radius: 5px;
}

.task-modal-single .task-comment.highlight-bg .task-info-separator {
    border: 1px solid transparent;
}

.task-single-col-right {
    background: #F0F5F7;
    padding: 13px 20px;
    border-bottom-right-radius: 6px;
}

.task-single-col-right .task-menu-options {
    position: absolute;
    margin-top: 10px;
    right: 10px;
}

.task-single-col-left {
    padding: 25px;
    background: #fff;
    min-height: 600px;
    border-bottom-left-radius: 6px;
}

.task-info-heading {
    font-size: 15px;
}

.task-modal-single .modal-body {
    padding-top: 0;
    padding-bottom: 0;
    background: #f0f5f7;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
}

.tasks-btn-settings-fix {
    padding: 6px 12px;
    font-size: 13px;
    border-radius: 4px;
}

.task-modal-single .task-m-close {
    margin-right: 43px;
    margin-top: 9px;
}

.task-modal-single .ribbon {
    top: -20px;
    right: -5px;
}

.task-modal-single .ribbon span {
    right: -22px;
}

.checklist {
    padding: 5px 15px 5px 5px;
    margin-top: 10px;
    border-radius: 4px;
    cursor: move;
}

.checklist .checkbox.checklist-checkbox {
    margin-top: 0;
    margin-bottom: 0;
    height: 25px;
}

.checklist:hover,
.checklist:hover textarea[name="checklist-description"]:disabled {
    background: #eef2f4;
}

.checkbox.checklist-checkbox label::before {
    width: 20px;
    height: 20px;
    margin-left: -21px;
    border-radius: 50%;
}

.checklist label:not(.control-label) {
    font-weight: normal;
}

.checkbox.checklist-checkbox label::after {
    margin-left: -20px;
    padding-left: 4.5px;
    padding-top: 3px;
    font-size: 10px;
}

.checklist .remove-checklist,
.checklist .save-checklist-template {
    margin-top: 2px;
}

.checklist .remove-checklist {
    margin-right: -5px;
}

body.rtl .checklist .remove-checklist {
    margin-right: 0px;
}

textarea[name="checklist-box"] {
    cursor: pointer;
}

textarea[name="checklist-description"] {
    resize: none;
    overflow: hidden;
    font-size: 14px;
    border-radius: 3px;
    padding: 0 5px 0px 5px;
    border: 0;
    outline: 0;
    width: 100%;
}

textarea[name="checklist-description"]:focus,
textarea[name="checklist-description"]:hover,
textarea[name="checklist-description"]:active {
    outline: 0;
}

textarea[name="checklist-description"]:disabled {
    background: #fff;
    color: #212121;
    opacity: 1;
}

.task-info {
    font-size: 10px;
    vertical-align: middle;
}

.task-info h5 {
    font-size: 13px;
    margin: 0;
    font-weight: 400;
}

.label-task-action {
    padding: 9px;
    display: block;
}

.reminder-modal {
    overflow: visible !important;
}

.alert {
    z-index: 99999999999 !important;
}

.dd-nochildren .dd-placeholder {
    display: none;
}

.dd {
    position: relative;
    display: block;
    margin: 0;
    padding: 0;
    max-width: 600px;
    list-style: none;
    font-size: 13px;
    line-height: 20px;
}

.dd-list {
    display: block;
    position: relative;
    margin: 0;
    padding: 0;
    list-style: none;
}

.dd-list .dd-list {
    padding-left: 30px;
}

.dd-collapsed .dd-list {
    display: none;
}

.dd-item,
.dd-empty,
.dd-placeholder {
    display: block;
    position: relative;
    margin: 0;
    padding: 0;
    min-height: 20px;
    font-size: 13px;
    line-height: 20px;
}

.dd-handle {
    display: block;
    height: 30px;
    margin: 5px 0;
    padding: 5px 10px;
    color: #333;
    text-decoration: none;
    font-weight: bold;
    border: 1px solid #ccc;
    background: #fafafa;
    background: -webkit-gradient(linear, left top, left bottom, from(#fafafa), to(#eee));
    background: linear-gradient(to bottom, #fafafa 0%, #eee 100%);
    border-radius: 3px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
}

.dd-handle:hover {
    color: #2ea8e5;
    background: #fff;
}

.dd-item>button {
    display: block;
    position: relative;
    cursor: pointer;
    float: left;
    width: 25px;
    height: 20px;
    margin: 5px 0;
    padding: 0;
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;
    border: 0;
    background: transparent;
    font-size: 12px;
    line-height: 1;
    text-align: center;
    font-weight: bold;
}

.dd-item>button:before {
    content: '+';
    display: block;
    position: absolute;
    width: 100%;
    text-align: center;
    text-indent: 0;
}

.dd-item>button[data-action="collapse"]:before {
    content: '-';
}

.dd-placeholder,
.dd-empty {
    margin: 5px 0;
    padding: 0;
    min-height: 30px;
    background: #f2fbff;
    border: 1px dashed #b6bcbf;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
}

.dd-empty {
    border: 1px dashed #bbb;
    min-height: 40px;
}

.dd-dragel {
    position: absolute;
    pointer-events: none;
    z-index: 9999;
}

.dd-dragel>.dd-item .dd-handle {
    margin-top: 0;
}

.dd-dragel .dd-handle {
    -webkit-box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, .1);
    box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, .1);
}

.nestable-lists {
    display: block;
    clear: both;
    padding: 30px 0;
    width: 100%;
    border: 0;
    border-top: 2px solid #ddd;
    border-bottom: 2px solid #ddd;
}

#nestable-menu {
    padding: 0;
    margin: 20px 0;
}

#nestable-output,
#nestable2-output {
    width: 100%;
    height: 7em;
    font-size: 0.75em;
    line-height: 1.333333em;
    font-family: Consolas, monospace;
    padding: 5px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
}

#nestable2 .dd-handle {
    color: #fff;
    border: 1px solid #999;
    background: #bbb;
    background: -webkit-gradient(linear, left top, left bottom, from(#bbb), to(#999));
    background: linear-gradient(to bottom, #bbb 0%, #999 100%);
}

#nestable2 .dd-handle:hover {
    background: #bbb;
}

#nestable2 .dd-item>button:before {
    color: #fff;
}

@media only screen and (min-width: 700px) {
    .dd {
        float: left;
        width: 48%;
    }

    .dd+.dd {
        margin-left: 2%;
    }
}

.dd-hover>.dd-handle {
    background: #2ea8e5 !important;
}

.dd3-content {
    display: block;
    height: 30px;
    margin: 5px 0;
    padding: 5px 10px 5px 40px;
    color: #333;
    text-decoration: none;
    font-weight: bold;
    border: 1px solid #ccc;
    background: #fafafa;
    background: -webkit-gradient(linear, left top, left bottom, from(#fafafa), to(#eee));
    background: linear-gradient(to bottom, #fafafa 0%, #eee 100%);
    border-radius: 3px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
}

.dd-dragel>.dd3-item>.dd3-content {
    margin: 0;
}

.dd3-item>button {
    margin-left: 30px;
}

.dd3-handle {
    position: absolute;
    margin: 0;
    left: 0;
    top: 0;
    cursor: pointer;
    width: 30px;
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;
    background: #fbfdff;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-color: #e0e0e0;
}

.dd3-handle:before {
    content: '≡';
    display: block;
    position: absolute;
    left: 0;
    top: 3px;
    width: 100%;
    text-align: center;
    text-indent: 0;
    color: #fff;
    font-size: 20px;
    font-weight: normal;
}

.dd3-handle:hover {
    background: #ebf0f5;
}

.dd3-content {
    font-weight: 500;
    height: 40px;
    padding-top: 9px;
    background: #fbfbfb !important;
    border-color: #e0e0e0;
}

.sub-items .dd3-content {
    background: #fff !important;
}

.dd-handle {
    height: 40px;
}

.dd3-handle:before {
    color: #444444;
    top: 8px;
}

.dd3-empty {
    height: 1px;
}

@media only screen and (min-width: 700px) {
    .dd {
        width: 100%;
    }
}

.dd-item>button:before {
    top: 10px;
}

.dd-item>button {
    height: 30px;
    font-family: 'Helvetica Neue', Arial, sans-serif;
}

.dd-item>button:nth-child(2) {
    margin-left: -5px;
}

.menu-options {
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

a>.label-default:hover,
a>.label-default:focus {
    background-color: #6c7888;
    color: #fff;
}

.bootstrap-select .dropdown-header+li a {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.bootstrap-select.task-action-select .btn-default,
.bootstrap-select.task-action-select.btn-default:active,
.bootstrap-select.task-action-select>.btn-default:hover,
.bootstrap-select.task-action-select.btn-default.active,
.bootstrap-select.task-action-select.open>.dropdown-toggle.btn-default {
    background-color: #F0F5F7 !important;
    background: #F0F5F7 !important;
    color: #777 !important;
    font-size: 13px;
    margin-bottom: 0;
    margin-top: -7px;
}

.bootstrap-select.task-action-select .dropdown-menu.open {
    border-top: 2px solid #c0cbda;
}

.bootstrap-select.btn-group .dropdown-toggle .filter-option {
    text-transform: none;
}

.bootstrap-select.btn-group .bs-placeholder .filter-option {
    color: #a1b4cc;
}

.bootstrap-select.ajax-search li a.newitem:hover,
.bootstrap-select.ajax-search li a.newitem:active {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}

.ct-control--crop,
.ct-control--rotate-ccw,
.ct-control--rotate-cw {
    display: none !important;
}

.ct-control--fetch {
    float: right;
}

.ct-widget .ct-control--upload {
    position: absolute !important;
    top: 300px;
    left: 285px;
}

.ct-widget.ct-image-dialog--uploading .ct-control--fetch {
    display: none;
}

.ct-widget.ct-image-dialog--populated .ct-control--fetch {
    display: none;
}

.ct-control--fetch {
    top: -33px;
    border-radius: 0;
}

.proposal-convert-modal .panel_s,
.invoice-project .panel_s,
.estimate-pipeline .panel_s,
.proposal-pipeline-modal .panel_s {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.proposal-convert-modal .panel_s .panel-body,
.invoice-project .panel_s .panel-body,
.estimate-pipeline .panel_s .panel-body,
.proposal-pipeline-modal .panel_s .panel-body {
    padding: 5px;
    border: 0;
}

.proposal-pipeline-modal .toggle_view,
.estimate-pipeline .toggle_view {
    display: none;
}

.estimate-pipeline .nav-tabs {
    margin-left: -10px !important;
    margin-right: -10px !important;
}

.proposal-convert-modal .btn-tr,
.invoice-project .btn-tr,
.estimate-pipeline .panel_s .panel-body .single-option-buttons,
.proposals-pipeline .panel_s .panel-body .single-option-buttons {
    display: none;
}

.proposal-convert-modal .modal-lg,
.invoice-project .modal-lg,
.estimate-pipeline .modal-lg,
.proposal-pipeline-modal .modal-lg {
    width: 1100px;
}

.proposals-pipeline .proposal-convert-modal .modal-lg {
    width: 1000px;
}

@media (max-width: 768px) {

    .proposal-convert-modal .modal-lg,
    .invoice-project .modal-lg,
    .estimate-pipeline .modal-lg,
    .proposal-pipeline-modal .modal-lg {
        width: auto;
    }

    .col-total-home-stats {
        text-align: left !important;
    }

    .team-members {
        margin-top: 15px;
    }
}

@media only screen and (max-width: 1200px) {

    .proposal-convert-modal .modal-lg,
    .invoice-project .modal-lg,
    .estimate-pipeline .modal-lg,
    .proposal-pipeline-modal .modal-lg {
        width: auto;
    }
}

.top-left-logout {
    cursor: pointer;
    margin-top: 3px;
}

.text-purple {
    color: #B72974;
}

.label-default-light {
    background: transparent;
    border: 1px solid #D8D8D8;
    color: #A5A5A5;
}

.announcement small {
    font-size: 11px;
    color: #333;
}

.announcement {
    font-size: 14px;
}

.kan-ban-content .article-group:first-child a+p {
    margin-top: 10px;
}

.kanban-leads-sort {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 15px;
}


div.dataTables_wrapper div.dataTables_processing {
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    background: transparent;
}

div.dataTables_wrapper div.dataTables_length select {
    width: auto;
    color: #4e75ad;
}

body.rtl div.dataTables_wrapper div.dataTables_filter {
    text-align: left;
}

body.rtl div.dataTables_wrapper div.dataTables_filter .input-group .input-group-addon {
    border-right: 1px solid #cccccc;
}

body.rtl .dt-button-collection.dropdown-menu {
    right: auto;
}

body.rtl div.dataTables_wrapper {
    direction: rtl;
}

.relative {
    position: relative;
}

.project-percent {
    position: absolute;
    font-size: 33px;
    font-weight: 500;
    top: 35%;
    left: 0;
    right: 0;
}

.width300 {
    width: 300px;
}

.width350 {
    width: 350px;
}

.width400 {
    width: 400px;
}

.width500 {
    width: 500px;
}

.width200 {
    width: 200px;
}

.width250 {
    width: 200px;
}

.divider.top-dropdown-btn-divider {
    background-color: #f0f0f0;
    margin-left: -15px;
    margin-right: -15px;
}

.started-timers-top {
    padding: 15px !important;
}

.started-timers-top .started-timers-button {
    margin-bottom: 10px;
    border-radius: 4px !important;
}

.started-timers-top .started-timers-button:last-child {
    margin-bottom: 0;
}

.started-timers-top .top-dropdown-btn {
    display: block;
    padding-top: 8px;
    padding-bottom: 8px;
    font-size: 15px;
    min-width: 50%;
    margin: 0 auto;
    margin-top: 14px;
    border-radius: 3px !important;
}

.started-timers-top {
    max-height: 600px;
    overflow-y: scroll;
}

.top-timers.text-success {
    color: #84c529 !important;
}

.timer {
    padding-bottom: 1px;
}

.timer a._timer {
    padding-left: 0 !important;
    padding-top: 0;
}

.timer a._timer:hover,
.timer a._timer:active,
a._timer:focus {
    background: transparent !important;
    color: #0081BB !important;
}

.timers-modal-logout .modal-footer {
    text-align: center !important;
}

.project-info-bg {
    background: #FBFBFB !important;
    color: #333 !important;
    border-top: 1px solid #E4E5E7;
    border-left: 1px solid #E4E5E7;
    border-right: 1px solid #E4E5E7;
    font-weight: 500;
}

body.rtl .project-info {
    margin-left: -15px;
    margin-right: -20px;
}

body.rtl .hr-panel-heading.project-area-separation {
    margin-left: -15px;
    margin-right: -20px;
}

.team-members .panel-body {
    padding: 0;
}

.team-members .media {
    margin-top: 0;
    border-bottom: 1px solid #f0f0f0;
}

.team-members .media:last-child {
    border-bottom: 0;
}

.team-members .media-left {
    padding: 10px;
    padding-left: 0;
}

.team-members .media-body {
    padding-right: 10px;
    padding-top: 10px;
}

.project-file-image {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    margin-right: 15px;
}

#task-tracking-stats-modal {
    z-index: 999999999999;
    margin-top: 4.5%;
}

.progress-bg-dark {
    background-color: #6c7888;
}

.project-progress-bars i {
    font-size: 24px;
}

.project-progress-bars i:not(.text-success) {
    color: #bfbfbf;
}

.text-light-green {
    color: #adca65;
}

.project_invoices .new-invoice-list {
    display: none;
}

.project_estimates .new-estimate-btn,
.project_estimates .switch-pipeline {
    display: none;
}

.project_proposals .new-proposal-btn,
.project_proposals .switch-pipeline,
.project_proposals .customers-related,
.project_proposals .leads-related {
    display: none !important;
}

.project_invoices .panel-body,
.project_estimates .panel-body,
.project_proposals .panel-body {
    border: 0;
    padding: 0;
}

.project_invoices .toggle-small-view,
.project_estimates .toggle-small-view,
.project_proposals .toggle-small-view {
    display: none;
}

.project_invoices,
.project_estimates,
.project_proposals {
    margin-left: -15px;
    margin-right: -15px;
}

@media(max-width:768px) {
    .jquery-comments ul.main li.comment .wrapper {
        overflow: visible;
        margin-top: 25px;
    }
}

.jquery-comments [contentEditable=true]:empty:not(:focus):before {
    color: #a1b4cc;
    font-size: 14px;
}

.jquery-comments .textarea-wrapper .upload.inline-button {
    display: none !important;
}

.jquery-comments ul.navigation li,
.jquery-comments ul.main li.comment .actions>*,
.jquery-comments ul.main li.comment .name,
.jquery-comments .highlight-font-bold {
    font-weight: 500 !important;
}

.jquery-comments ul.main li.comment .name {
    color: #0081BB;
}

.jquery-comments ul.main {
    list-style: none !important;
}

.jquery-comments.tc-content ul.main ul:not(.child-comments) {
    list-style: initial !important;
}

.jquery-comments.tc-content ul.main ul:not(.child-comments),
.jquery-comments.tc-content ul.main ol {
    line-height: 0.8;
}

.jquery-comments ul.main li.comment .wrapper .content {
    padding: 5px 0 5px 0;
}

.jquery-comments ul.navigation li {
    color: #323A45;
}

.jquery-comments .textarea-wrapper .control-row>span.upload {
    padding: 5px 20px;
    background-color: #7D838B;
}

.jquery-comments .highlight-background {
    background: #03A9F4 !important;
}

.jquery-comments .textarea-wrapper .control-row>span {
    padding: 5px 20px !important;
    border-radius: 4px;
}

.jquery-comments .textarea-wrapper .control-row {
    margin-top: 10px;
}

.jquery-comments ul.main li.comment .actions>* {
    color: #6c7888;
}

.jquery-comments ul.navigation .navigation-wrapper {
    padding: 10px 0 0 0;
}

.jquery-comments ul.navigation {
    border-bottom: 1px solid #EFEFEF;
    margin-bottom: 1.5em;
}

#pre_invoice_project_settings .popover {
    width: 300px !important;
}

.td-border-left-transparent {
    border-left: 10px solid transparent;
}

.highlight {
    border: 1px solid #C5BE22;
    padding: 1px 4px;
}

.delete-text.text-danger {
    color: #fc2d42;
}

.projects-activity .activity-feed {
    overflow-y: scroll;
    height: 750px;
}

.error .bootstrap-select .dropdown-toggle,
.has-error .bootstrap-select .dropdown-toggle {
    border-color: #fc2d42 !important;
}

#header .icon {
    display: inline-block;
}

#estimate-pipeline,
#proposals-pipeline {
    overflow-x: scroll;
}

.pipeline-heading {
    font-size: 15px;
}

.pipeline-heading a {
    color: #333;
}

.xdsoft_ {
    z-index: 99999999999999;
}

.xdsoft_datetimepicker {
    border-radius: 4px;
    padding: 10px;
    font-family: 'Roboto';
    border: 1px solid #bfcbd9;
    margin-top: 5px;
}

.xdsoft_datetimepicker .xdsoft_calendar td,
.xdsoft_datetimepicker .xdsoft_calendar th {
    color: #48576a;
}

.xdsoft_datetimepicker .xdsoft_timepicker .xdsoft_time_box>div>div {
    background: #fbfdff;
}

.xdsoft_datetimepicker .xdsoft_calendar td>div {
    padding-top: 1px;
    padding-bottom: 1px;
}

.xdsoft_datetimepicker .xdsoft_datepicker {
    width: 260px;
}

.xdsoft_datetimepicker .xdsoft_calendar td,
.xdsoft_datetimepicker .xdsoft_calendar th {
    background: #fff;
    text-align: center;
    border: 0;
}

.xdsoft_datetimepicker .xdsoft_calendar td {
    padding: 5px;
    padding-left: 7px;
}

.xdsoft_datetimepicker .xdsoft_calendar th {
    color: #8391a5;
    font-weight: 400;
    padding: 7px;
    padding-top: 10px;
    padding-bottom: 10px;
}

.xdsoft_datetimepicker .xdsoft_datepicker.active+.xdsoft_timepicker {
    margin-top: 7px;
}

.xdsoft_datetimepicker .xdsoft_timepicker .xdsoft_time_box {
    height: 151px;
}

.xdsoft_datetimepicker .xdsoft_label {
    font-weight: 400;
}

.xdsoft_datetimepicker .xdsoft_calendar td.xdsoft_default,
.xdsoft_datetimepicker .xdsoft_calendar td.xdsoft_current,
.xdsoft_datetimepicker .xdsoft_timepicker .xdsoft_time_box>div>div.xdsoft_current {
    -webkit-box-shadow: none;
    box-shadow: none;
    background: #33aaff;
    border-radius: 3px;
}

.xdsoft_datetimepicker .xdsoft_next,
.xdsoft_datetimepicker .xdsoft_prev,
.xdsoft_datetimepicker .xdsoft_today_button {
    color: #97a8be !important;
}

.xdsoft_datetimepicker .xdsoft_calendar td:hover,
.xdsoft_datetimepicker .xdsoft_timepicker .xdsoft_time_box>div>div:hover {
    background: #e4e8f1 !important;
    border-radius: 3px;
    color: #2d2d2d !important;
}

.xdsoft_datetimepicker .xdsoft_label>.xdsoft_select>div>.xdsoft_option:hover {
    background: #e4e8f1;
    color: #333;
}

.pointer {
    cursor: pointer;
}

.customer_profile .nav-tabs>li>a:nth-child(3):after {
    color: red;
}

.font-normal {
    font-weight: 400 !important;
}

.todo-dragger {
    width: 15px;
    height: 27px;
    position: absolute;
    z-index: 50;
}

.text-dark {
    color: #464646;
}

.pinned-separator {
    border-bottom: 2px solid #fff !important;
}

#side-menu.nav>li.pinned_project {
    padding-bottom: 15px;
}

#side-menu.nav>li.pinned_project>a:hover,
#side-menu.nav>li.pinned_project>a:focus,
#side-menu.nav>li.pinned_project.active,
#side-menu.nav>li.pinned_project.active>a {
    background: transparent;
    color: #fff;
}

#side-menu.nav>li.pinned_project:last-child {
    border-bottom: 1px solid #85898E;
}

.tasks-overview thead {
    border: 0;
}

.tasks-overview tbody td {
    padding: 10px !important;
    vertical-align: middle !important;
}

.stripped-table-data {
    background-color: #f9f9f9;
}

table.dataTable>tbody>tr.child span.dtr-title {
    font-weight: 500;
}

.dt-buttons.btn-group:empty {
    display: none;
}

.dt-buttons.btn-group .btn {
    color: #4e75ad;
    border-radius: 3px;
    font-size: 11.5px;
    padding-bottom: 6px;
    padding-top: 7px;
}

.dt-buttons.btn-group .btn:hover,
.dt-buttons.btn-group .btn:focus,
.dt-buttons.btn-group .btn:active {
    background: #fff;
    border: 1px solid #03a9f4;
}

.btn.btn-default-dt-options {
    color: #333;
    background-color: #fff !important;
    border: 1px solid #bfcbd9 !important;
}

.dt-button-collection.dropdown-menu {
    padding-top: 0;
    padding-bottom: 0;
}

.dt-button-collection.dropdown-menu a {
    color: #4e75ad;
}

.dt-button-collection.dropdown-menu>li.active>a,
.dt-button-collection.dropdown-menu>li>a:hover,
.dt-button-collection.dropdown-menu>li>a:focus {
    background-color: #f6f8fa;
    color: #424242;
}

@media(max-width:768px) {

    ._buttons .pull-left,
    ._buttons .pull-right {
        float: none !important;
    }

    ._buttons .btn,
    ._buttons .btn-group {
        display: inline-block;
        margin-left: 0 !important;
        width: 100%;
        margin-bottom: 5px !important;
        float: none !important;
        text-align: left;
    }

    ._buttons .mleft5 {
        margin-left: 0 !important;
    }

    ._buttons a,
    ._buttons button {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    body.rtl .post-time-ago {
        left: 55px;
        right: auto;
    }

    body.rtl .btn-post-options-wrapper {
        right: auto;
        left: 20px;
        top: 38px;
    }

    body.rtl ._filter_data.mleft4 {
        margin-right: 0 !important;
    }

    ._buttons .mright5 {
        margin-right: 0 !important;
    }

    .proposal_buttons {
        margin-top: 10px !important;
        display: inline-block;
    }

    ._buttons .btn-group>.btn {
        margin-bottom: 0 !important;
    }

    .custom-close-btn {
        margin-right: 0 !important;
        border: 1px solid #6c7888 !important;
        width: 100%;
        display: inline-block;
        margin-top: 0 !important;
        border-radius: 3px;
        padding: 5px !important;
    }

    table .goal-percent {
        top: -3px;
    }

    div.dt-buttons a.btn {
        padding: 4px 6px !important;
        font-size: 11px !important;
    }
}

@media screen and (max-width: 767px) {
    div.dt-buttons {
        text-align: right;
        width: auto;
        float: right;
        z-index: 55;
    }

    .dt-buttons.btn-group .btn {
        padding-top: 6.5px !important;
        padding-bottom: 6.5px !important;
    }

    div.dataTables_wrapper div.dataTables_length {
        position: absolute;
        top: 0;
        z-index: 55;
    }

    body.rtl div.dataTables_wrapper div.dataTables_length {
        left: 10px;
    }
}

.container-fluid .table-responsive {
    overflow-x: inherit;
}

@media only screen and (max-device-width: 549px) {
    .mce-window {
        width: auto !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: none !important;
        margin: 15px !important;
    }

    .mce-window-head {
        background: #fff !important;
    }

    .mce-window-body {
        background: #fff !important;
    }

    .mce-foot>.mce-container-body {
        padding: 10px !important;
    }

    .mce-panel {
        max-width: 100% !important;
    }

    .mce-container {
        max-width: 100% !important;
        height: auto !important;
    }

    .mce-container-body {
        max-width: 100% !important;
        height: auto !important;
    }

    .mce-form {
        padding: 10px !important;
    }

    .mce-tabs {
        max-width: 100% !important;
    }

    .mce-tabs .mce-tab,
    .mce-formitem {
        margin: 10px 0 !important;
    }

    .mce-abs-layout-item {
        position: static !important;
        width: auto !important;
    }

    .mce-abs-layout-item.mce-label {
        display: block !important;
    }

    .mce-abs-layout-item.mce-textbox {
        -webkit-box-sizing: border-box !important;
        box-sizing: border-box !important;
        display: block !important;
        width: 100% !important;
    }

    .mce-abs-layout-item.mce-combobox {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
    }

    .mce-abs-layout-item.mce-combobox>.mce-textbox {
        -ms-flex: 1 1 auto;
        -webkit-box-flex: 1;
        flex: 1 1 auto;
        height: 29px !important;
    }
}

.colorpicker {
    z-index: 99999;
}

.kan-ban-step-indicator {
    width: 38px;
    height: 38px;
    position: absolute;
    top: -1px;
    right: -1px;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    z-index: 39;
    background: url(../images/stage.svg) no-repeat top left;
}

body.rtl .kan-ban-step-indicator {
    background: url(../images/stage.svg) no-repeat top right;
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1);
    -webkit-filter: FlipH;
    filter: FlipH;
    -ms-filter: "FlipH";
    right: auto;
    left: -1px;
}

.kan-ban-step-indicator-full {
    background: #4B5158;
}

.kanban-load-more {
    padding-left: 5px !important;
    padding-right: 5px !important;
}

.kanban-color-picker {
    position: relative;
    z-index: 40;
}

.kanban-stage-color-picker {
    margin-right: 24px;
}

.kanban-stage-color-picker-last {
    margin-right: 5px;
}

.modal-content.theme_style_modal_example {
    -webkit-box-shadow: 0 5px 8px rgba(0, 0, 0, .2) !important;
    box-shadow: 0 5px 8px rgba(0, 0, 0, .2) !important;
    border-top: 1px solid #d8d8d8 !important;
    margin-top: 12px !important;
}

.modal-content.theme_style_modal_example .modal {
    display: block !important;
    position: relative !important;
    margin-top: 0 !important;
}

.modal-content.theme_style_modal_example .modal .modal-header {
    border-bottom: 0 !important;
}

.s-status,
.label-big {
    display: inline-block;
    padding: 6px 18px;
    text-transform: uppercase;
}

.project_progress_slider_horizontal {
    height: 20px;
}

.project_progress_slider_horizontal.ui-widget.ui-widget-content {
    height: 20px;
    border-color: #bfcbd9;
    background: #fff;
}

.project_progress_slider_horizontal .ui-slider-handle {
    height: 25px;
    background: #84c529;
    border: 1px solid #84c529;
}

.jquery-comments .textarea-wrapper {
    padding-left: 21px;
}

.jquery-comments .textarea-wrapper:before {
    border: 0;
}

.jquery-comments .textarea-wrapper .textarea {
    border-radius: 3px;
    border: 1px solid #bfcbd9;
}

.jquery-comments .textarea-wrapper .textarea:focus,
.jquery-comments .textarea-wrapper .textarea:active {
    border-color: #03a9f4;
}

ul.dt-button-collection.dropdown-menu {
    z-index: 20005;
}

.activity-feed {
    padding: 15px;
    word-wrap: break-word;
}

.activity-feed .feed-item {
    position: relative;
    padding-bottom: 30px;
    padding-left: 30px;
    border-left: 2px solid #84c529;
}

.feed-item .text-has-action {
    margin-bottom: 7px;
    display: inline-block;
}

.activity-feed .feed-item:last-child {
    border-color: transparent;
}

.activity-feed .feed-item:after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: -6px;
    width: 10px;
    height: 10px;
    border-radius: 6px;
    background: #fff;
    border: 1px solid #4B5158;
}

.activity-feed .feed-item .date {
    position: relative;
    top: -5px;
    color: #4B5158;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 500;
}

.activity-feed .feed-item .text {
    position: relative;
    top: -3px;
}

.lead-modal .ribbon {
    z-index: 9;
}

.lead-modal .lead-top-btn {
    margin-top: -14px;
    z-index: 10;
    position: relative;
}

.lead-modal .lead-actions-left {
    margin-top: -7px;
    margin-bottom: 21px;
}

.lead-modal .activity-feed .feed-item {
    padding-top: 20px;
    border-right: 1px solid #eaeaea;
    border-top: 1px solid #eaeaea;
    padding-bottom: 15px;
    background: #fdfdfd;
}

.lead-modal .activity-feed .feed-item:first-child {
    border-top-right-radius: 3px;
}

.lead-modal .activity-feed .feed-item:last-child {
    border: 1px solid #eaeaea;
    border-bottom-right-radius: 3px;
}

.lead-modal .activity-feed .feed-item:after {
    top: -5px;
    border: 1px solid #84c529;
}

.email-template-heading {
    padding: 10px;
    background-color: #fbfbfb;
    border: 1px solid #ececec;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-size: 15px;
}

.milestone-column .ui-sortable-placeholder {
    height: 50px;
    overflow: hidden;
    border: 2px dashed #D1D1D1;
    background: transparent !important;
    margin-top: 10px;
}

li.task.current-user-task .panel-body,
li.lead-kan-ban.current-user-lead .panel-body {
    background: #ecf6fb !important;
    border: 1px solid #c2e7ef !important;
}

li.task.overdue-task .panel-body {
    background: #f2dede !important;
    border: 1px solid #eab8b7 !important;
}

.dropdown-submenu {
    position: relative;
}

.dropdown-submenu>.dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -6px;
    margin-left: -1px;
    border-radius: 0 6px 6px 6px;
    max-height: 500px;
    overflow-y: auto;
}

.dropdown-submenu:hover>.dropdown-menu {
    display: block;
}

.dropdown-submenu>a:after {
    display: block;
    content: " ";
    float: right;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-left-color: #ccc;
    margin-top: 5px;
    margin-right: -10px;
}

.dropdown-submenu:hover>a:after {
    border-left-color: #333;
}

.dropdown-submenu.pull-left {
    float: none;
}

.dropdown-submenu.pull-left>.dropdown-menu {
    border-radius: 6px;
    margin-top: 0.2px;
    border-top-left-radius: 0;
}

.dropdown-submenu.pull-left>.dropdown-menu li.active a:hover,
.dropdown-submenu.pull-left>.dropdown-menu li a:hover,
.dropdown-submenu.pull-left>.dropdown-menu li.active a {
    border-top-left-radius: 0;
}

.show_quantity_as_wrapper span {
    font-weight: 500;
}

._filter_data .dropdown-menu.height500 {
    max-height: 500px;
    overflow-y: scroll;
}

._filter_data .dropdown-menu li a,
.bootstrap-select .dropdown-menu li a {
    padding: 6px 16px;
}


@media (max-width: 768px) {
    .dropdown-submenu.pull-left>.dropdown-menu {
        display: block;
        left: 1px;
        position: relative;
        border: 0;
        -webkit-box-shadow: none;
        box-shadow: none;
    }

    ._filter_data .dropdown-menu {
        max-height: 500px;
        overflow-y: scroll;
    }

    .dropdown-submenu>a:after {
        border-left-color: transparent;
    }

    .dropdown-submenu>a {
        background: #f0f0f0;
    }

    .show_quantity_as_wrapper {
        display: inline-block;
        width: 100%;
        margin-bottom: 15px;
    }

    .show_quantity_as_wrapper span {
        float: left;
        margin-bottom: 15px;
    }

    .show_quantity_as_wrapper div.radio {
        display: block;
        clear: both;
        float: left;
        margin-left: 0;
    }

    .lead-modal .lead-top-btn {
        width: 100%;
        margin-top: 5px;
    }
}

.progress-bar-mini {
    height: 5px !important;
}

.top_stats_wrapper {
    padding: 5px 15px 15px 15px;
    background: #fff;
    border-radius: 3px;
    -webkit-box-shadow: 0 1px 15px 1px rgba(90, 90, 90, 0.08);
    box-shadow: 0 1px 15px 1px rgba(90, 90, 90, 0.08);
    border: 1px solid #dce1ef;
    margin-bottom: 15px;
}

.progress-finance-status {
    margin-top: -25px;
    margin-bottom: 16px;
    z-index: 1;
}

.text-stats-wrapper {
    z-index: 2;
}

#task_single_timesheets tbody {
    background: #fdfdfd;
    border: 1px solid #f0f0f0;
}

.dataTables_filter input {
    margin-left: 0 !important;
    width: 80px !important;
    -webkit-transition: width 0.3s ease;
    transition: width 0.3s ease;
    height: 31px;
}

.dataTables_filter input:focus {
    width: 160px !important;
}

.dataTables_filter label {
    text-align: right;
}

@media (max-width: 768px) {

    .dataTables_filter input,
    .dataTables_filter input:focus {
        width: 100% !important;
    }
}

#inline-editor-save-btn button {
    color: #ffffff;
    border: 1px solid #84c529;
    background: #84c529;
    border-radius: 2px;
    padding: 4px 10px;
}

#inline-editor-save-btn button:hover,
#inline-editor-save-btn button:active,
#inline-editor-save-btn button:active:focus {
    background-color: #74B31B;
}

.staff_logged_time h3._total {
    font-size: 19px;
    word-break: break-all;
}

.staff_logged_time .staff_logged_time_text {
    word-break: break-all;
}

.staff_logged_time .panel-body {
    padding: 20px 5px;
}

@media only screen and (max-width: 760px),
(min-device-width: 768px) and (max-device-width: 1024px) {

    .s_table table,
    .s_table .table.items .main {
        margin-top: 0;
        margin-bottom: 35px;
        clear: both;
    }

    .s_table.table-responsive {
        padding: 0;
        background: #f9f9f9;
    }

    .s_table .table.items tbody>tr>td:not(:first-child) {
        display: inline-block;
        width: 100%;
    }

    /* Force table to not be like tables anymore */
    .s_table table,
    .s_table thead,
    .s_table tbody,
    .s_table th,
    .s_table td,
    .s_table tr {
        display: block;
    }

    /* Hide table headers (but not display: none;, for accessibility) */
    .s_table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    body.rtl .s_table thead tr {
        left: auto;
    }

    .s_table td {
        /* Behave  like a "row" */
        border: none;
        border-bottom: 1px solid #eee;
        position: relative;
        padding-left: 50%;
    }

    .s_table td.dragger {
        display: none;
    }

    .s_table td:last-child {
        clear: both;
    }

    .s_table td:last-child button,
    .s_table td:last-child a {
        display: block;
        width: 100%;
        margin-top: 15px;
        margin-bottom: 15px;
    }

    .s_table td:before {
        /* Now like a table header */
        position: absolute;
        /* Top/left values mimic padding */
        top: 6px;
        left: 6px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
    }

    .project-percent-col {
        margin-top: 25px;
    }
}

div.dataTables_wrapper div.dataTables_info,
div.dataTables_wrapper div.dataTables_length label,
.dataTables_empty {
    color: #2d2d2d;
}

.dataTables_empty {
    padding-top: 25px !important;
    padding-bottom: 180px !important;
    text-align: left !important;
    color: #777777;
    font-size: 15px;
    background: url(../images/table-no-data.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: auto 161px;
}

.app_dt_empty .dataTables_paginate,
.app_dt_empty table tfoot,
.app_dt_empty .dataTables_info {
    display: none;
}

.app_dt_empty table thead,
.app_dt_empty .dataTables_length,
.app_dt_empty .dt-buttons {
    opacity: .5;
}

.modal-backdrop {
    background-color: rgba(45, 62, 80, .79);
}

.modal-backdrop.in {
    opacity: .9;
}

.full-screen-modal {
    width: 90%;
    height: 90%;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    overflow-y: initial !important;
}

.full-screen-modal .modal-content {
    height: 100%;
    min-height: 100%;
    max-height: 100%;
}

.full-screen-modal .modal-footer:not(.modal-not-full-width) {
    bottom: 0;
    position: absolute;
    width: 100%;
}

.full-screen-modal .modal-footer:not(.modal-not-full-width):after {
    content: " ";
    margin-top: -7px;
    padding-bottom: 3px;
}

.project_file_discusssions_area,
.project_file_area {
    overflow-y: scroll;
    height: 400px;
}

@media(max-width:768px) {

    .project_file_discusssions_area,
    .project_file_area {
        height: auto !important;
    }

    .full-screen-modal {
        width: auto;
        height: auto;
        position: relative;
        left: auto;
        right: auto;
        top: auto;
        bottom: auto;
        margin: 10px;
        overflow-y: initial !important;
    }

    .project_file_discusssions_area {
        margin-top: 30px;
    }

    .full-screen-modal .modal-footer {
        width: auto;
        position: relative !important;
    }
}

.preview_image {
    height: auto;
    width: 250px;
    overflow: hidden;
    margin-bottom: 15px;
    margin-top: 15px;
}

.preview_image:last-child {
    margin-top: 0;
    margin-bottom: 0;
}

.preview_image img {
    width: 100%;
    height: auto;
}

.task-attachment {
    border: 1px solid #f0f0f0;
    padding: 10px;
    border-radius: 3px;
    max-height: 195px;
    min-height: 195px;
    overflow: hidden;
}

.task-attachment .preview_image {
    margin: 0;
    width: 100%;
}

.task-attachment .task-attachment-no-preview {
    margin-top: 45px;
    text-align: center;
}

.task-attachment .task-attachment-user {
    padding-bottom: 10px;
    display: inline-block;
    width: 100%;
    margin-left: 0;
    border-bottom: 1px solid #f0f0f0;
}

.task-attachment-wrapper {
    max-height: 200px;
    overflow: hidden;
}

.task-modal-single .trigger i {
    font-size: 10px;
}

.task-single-menu .popover {
    width: 400px;
}

.popover-inner {
    position: relative;
}

.task-single-menu ul {
    margin: 0;
    padding: 5px;
}

.task-single-menu ul li {
    margin-bottom: 10px;
}

.task-single-menu ul li:last-child {
    margin-bottom: 0;
}

.task-single-menu ul li a {
    font-size: 14px;
}

.tc-content ul,
.tc-content ol {
    list-style: inherit;
    margin-left: 16px;
}

.tc-content ol {
    list-style-type: decimal;
}

.tc-content table {
    margin-top: 0;
    border-collapse: initial;
}

.tc-content img {
    max-width: 100%;
}

.tc-content table>tbody>tr>td {
    padding: 5px 10px 5px 10px;
}

.tc-content table[border="1"],
.tc-content table[border="1"] td {
    border: 1px solid;
}

.tc-content em {
    font-style: italic;
}

.task-modal-single .comment-content img {
    max-width: 100%;
    height: auto;
}

.task-modal-single .comment-content iframe {
    width: 100%;
}

@media (min-width: 768px) {
    .form-inline textarea.form-control {
        display: block !important;
        width: 100%;
    }

    .page-leads-admin .float-alert {
        width: 18%;
    }

    .form-inline .popover-content .form-group {
        margin-bottom: 15px;
    }
}

div.dataTables_wrapper div.dataTables_paginate {
    padding-right: 15px;
}

.tooltip {
    position: fixed;
}

.tooltip>.tooltip-inner {
    padding: 7px 10px;
    background: #1f2d3d;
}

.email-template-heading {
    margin-bottom: 0;
    border-radius: 3px;
    margin-left: -20px;
    margin-right: -20px;
    border-left: 0;
    border-right: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    padding-left: 25px;
}

.customer-heading-profile {
    margin-bottom: 15px;
    margin-top: 0;
}

#invoice_top_info .panel-body {
    background: #f5fcff;
    padding: 5px 15px;
    border: 1px solid #c7dde6;
}

.onoffswitch {
    position: relative;
    width: 50px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.onoffswitch-checkbox {
    display: none;
}

.onoffswitch-label {
    display: block;
    overflow: hidden;
    cursor: pointer;
    height: 20px;
    padding: 0;
    line-height: 22px;
    border: 1px solid #bfcbd9;
    border-radius: 22px;
    background-color: #bfcbd9;
    -webkit-transition: background-color 0.3s ease-in;
    transition: background-color 0.3s ease-in;
}

.onoffswitch-label:before {
    content: "";
    display: block;
    width: 20px;
    margin: 0;
    background: #FFFFFF;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 30px;
    border: 1px solid #bfcbd9;
    border-radius: 20px;
    -webkit-transition: all 0.3s ease-in 0s;
    transition: all 0.3s ease-in 0s;
}

.onoffswitch-checkbox:checked+.onoffswitch-label {
    background-color: #84c529;
}

.onoffswitch-checkbox:checked+.onoffswitch-label,
.onoffswitch-checkbox:checked+.onoffswitch-label:before {
    border-color: #84c529;
}

.onoffswitch-checkbox:checked+.onoffswitch-label:before {
    right: 0;
}

.onoffswitch-checkbox:disabled+.onoffswitch-label {
    opacity: 0.5;
}

.menu-dots a>i {
    font-size: 11px;
}

.tilt.right {
    transform: rotate(3deg);
    -moz-transform: rotate(3deg);
    -ms-transform: rotate(-3deg);
    -webkit-transform: rotate(3deg);
}

.tilt.left {
    transform: rotate(-3deg);
    -moz-transform: rotate(-3deg);
    -ms-transform: rotate(-3deg);
    -webkit-transform: rotate(-3deg);
}

.float-alert {
    display: inline-block;
    margin: 0 auto;
    position: fixed;
    -webkit-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    z-index: 1031;
    top: 20px;
    right: 20px;
}

.table-image {
    height: 75px;
    width: 250px;
    margin: 15px 0;
}

.lead-field-heading {
    margin-top: 10px;
    margin-bottom: 0;
}

.lead-info-heading:first-child {
    margin-left: -15px;
    padding-left: 15px;
}

.lead-info-heading {
    background: #f9fafc;
    padding: 7px;
    border-radius: 3px;
    margin-bottom: 15px;
}

.lead-info-heading h4 {
    color: #005c86;
}

.top-lead-menu {
    margin-left: -15px;
    margin-right: -15px;
    margin-top: -15px;
}

.firefox .top-lead-menu {
    margin-bottom: 18px;
}

.top-lead-menu .nav-tabs {
    border-top: 0;
}

.lead-latest-activity {
    margin-top: 15px;
    margin-bottom: 10px;
    border-top: 1px solid #f0f0f0;
    padding-top: 20px;
}

.nav-tabs.lead-new {
    display: none;
    margin-bottom: 10px;
}

.bootstrap-select.btn-group.show-tick .dropdown-menu li.selected a span.check-mark {
    margin-top: 8px;
}

.input-transparent {
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border: 0 !important;
    background: transparent;
}

.frmb .field-actions .del-button {
    float: right;
}

.web-to-lead-form .frmb .prev-holder .checkbox label::before,
.web-to-lead-form .frmb .prev-holder .radio label::before {
    display: none;
}

.web-to-lead-form .frmb .checkbox input[type="checkbox"],
.web-to-lead-form .frmb .checkbox input[type="radio"] {
    opacity: 1 !important;
}

.frm-holder .access-wrap,
.frm-holder .toggle-wrap,
.web-to-lead-form .other-wrap,
.web-to-lead-form .maxlength-wrap,
.web-to-lead-form .copy-button,
.web-to-lead-form .frmb-control .fb-separator,
.subtype-wrap,
.web-to-lead-form .option-actions,
.web-to-lead-form .field-options a.remove,
.web-to-lead-form li[type="datetime-local"] .value-wrap,
.web-to-lead-form li[type="datetime"] .value-wrap {
    display: none !important;
}

.form-wrap.form-builder .frmb>li:hover {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    border: 1px solid #c5c5c5 !important;
}

.form-wrap.form-builder .frmb .prev-holder input[type=number] {
    width: 100% !important;
}

.frmb-control li.input-set-control:first-child {
    border-top-right-radius: 4px !important;
    border-top-left-radius: 4px !important;
}

.form-wrap.form-builder .frmb .field-actions .btn {
    font-size: 14px !important;
}

.form-wrap.form-builder .frmb .field-actions .btn:first-child {
    border-radius: 0 !important;
}

.form-wrap.form-builder .frmb .field-label,
.form-wrap.form-builder .frmb .legend {
    font-size: 13px !important;
}

.name-wrap input,
.stage-wrap .form-field input[type="color"],
.stage-wrap .form-field input[type="date"],
.stage-wrap .form-field input[type="file"],
.stage-wrap .form-field input[type="datetime-local"],
.stage-wrap .form-field select[multiple="true"],
.web-to-lead-form [id^=frmb-][id$='-form-wrap'] .frmb .form-field .form-group.field-options input[type="text"] {
    cursor: not-allowed;
    pointer-events: none;
    background-color: #EEE !important;
    color: #9E9999;
}

.cb-wrap li.disabled {
    pointer-events: none;
    opacity: 0.6;
    background: #eef1f6;
}

.web-to-lead-form .stage-wrap .checkbox input[type="checkbox"]:checked+label::after,
.web-to-lead-form .stage-wrap .checkbox input[type="radio"]:checked+label::after {
    content: " ";
}

.stage-wrap .form-field input[type="color"],
.stage-wrap .form-field input[type="date"] {
    width: 100% !important;
    appearance: textfield;
    -moz-appearance: textfield;
    -webkit-appearance: textfield;
}

.stage-wrap .form-field input[type="color"]::-webkit-color-swatch-wrapper,
.stage-wrap .form-field input[type="color"]::-moz-color-swatch-wrapper {
    display: none;
}

@-webkit-keyframes PLACEHOLDER {
    0% {
        height: 0;
    }

    100% {
        height: 0;
    }
}

@keyframes PLACEHOLDER {
    0% {
        height: 0;
    }

    100% {
        height: 0;
    }
}


.stage-wrap .form-field input[type="color"]::-webkit-color-swatch,
.stage-wrap .form-field input[type="color"]::-moz-color-swatch {
    display: none;
}

​.stage-wrap .form-field input[type="date"] {
    color: #eeeeee;
}

body.rtl [id^=frmb-][id$='-form-wrap'] .frmb .field-actions {
    left: 0 !important;
    right: inherit;
}

[id^='frmb-'][id$='-form-wrap'] .frmb li.ui-sortable-handle {
    border: 2px solid transparent;
}

[id^='frmb-'][id$='-form-wrap'] .frmb li.ui-sortable-handle:hover {
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: 0 none;
    border: 2px dashed #d6d6d6 !important;
}

[id^=frmb-][id$='-form-wrap'] .btn-primary {
    color: #fff;
}

[id^=frmb-][id$='-form-wrap'] .frmb {
    margin: 0 0 0 20px !important;
}

[id^=frmb-][id$='-form-wrap'] .frmb .form-elements input[type=text],
[id^=frmb-][id$='-form-wrap'] .frmb .form-elements input[type=color],
[id^=frmb-][id$='-form-wrap'] .frmb .form-elements input[type=date],
[id^=frmb-][id$='-form-wrap'] .frmb .form-elements input[type=number],
[id^=frmb-][id$='-form-wrap'] .frmb .form-elements .fld-label,
[id^=frmb-][id$='-form-wrap'] .frmb .form-elements select,
[id^=frmb-][id$='-form-wrap'] .frmb .form-elements textarea {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    border: 1px solid #d6d6d6 !important;
}

.required-asterisk {
    color: #fc2d42 !important;
}

[id^=frmb-][id$='-form-wrap'] .frmb .form-elements .false-label:first-child,
[id^=frmb-][id$='-form-wrap'] .frmb .form-elements label:first-child {
    font-weight: 500 !important;
}

#toplink,
#botlink {
    position: fixed;
    right: 7.5%;
    bottom: 50%;
    padding: 10px;
    margin: 0 -20px 0 0;
    color: #666;
    background: #e3e8ee;
    font-size: 1.5em;
    border: 1px solid #b4b4b4;
    border-bottom: 1px solid #b8b8b8;
    border-radius: 6px 6px 0 0;
    z-index: 99;
    -webkit-box-shadow: 1px 2px 3px rgba(0, 0, 0, 0.25);
    box-shadow: 1px 2px 3px rgba(0, 0, 0, 0.25);
}

#botlink {
    top: 50%;
    padding: 10px;
    bottom: auto;
    border: 1px solid #b4b4b4;
    border-top: 1px solid #ddd;
    border-radius: 0 0 6px 6px;
}

#toplink:hover,
#botlink:hover {
    color: #84C529;
    background: #fcfcfc;
    text-decoration: none;
}

body.rtl #toplink,
body.rtl #botlink {
    left: 7.5%;
    right: auto;
}

.project-tabs .dropdown-menu>li:first-child>a {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.lightboxOverlay,
#lightbox {
    z-index: 100002 !important;
}

ul.tagit {
    border: 0 !important;
    background: transparent;
}

ul.tagit input[type="text"] {
    background: transparent;
    border-left: 1px dashed #b3b3b3;
    height: 20px;
    padding-left: 5px;
}

body.rtl ul.tagit input[type="text"] {
    border-left: 0;
    border-right: 1px dashed #b3b3b3;
}

ul.tagit li.tagit-new {
    padding-top: 2px;
}

ul.tagit li.tagit-choice-editable:hover {
    border: 1px solid #bfcbd9;
}

ul.tagit li.tagit-choice-editable,
ul.tagit li.tagit-choice-read-only {
    padding: 2px 20px 2px 10px;
    background: #fff;
    color: #2d2d2d;
    border: 1px solid #bfcbd9;
    font-weight: 400;
    font-size: 13px;
    border-radius: 3px;
}

ul.tagit li.tagit-choice-editable a {
    color: #2d2d2d;
}

ul.tagit-autocomplete {
    border-radius: 4px;
    background: #fff;
    padding: 5px 0;
    border-color: #bfcbd9 !important;
    max-height: 270px;
    min-width: 240px;
    font-family: 'Roboto';
    overflow-y: scroll;
}

ul.tagit-autocomplete li {
    border-bottom: 1px solid #f0f0f0;
    padding: 5px !important;
}

ul.tagit-autocomplete .ui-menu-item-wrapper {
    margin-right: -6px !important;
}

ul.tagit-autocomplete .ui-menu-item-wrapper {
    margin-right: -6px !important;
}

ul.tagit-autocomplete .ui-menu-item-wrapper.ui-state-active,
ul.tagit-autocomplete .ui-menu-item-wrapper:hover,
ul.tagit-autocomplete .ui-menu-item-wrapper:focus {
    background: #e4e8f1 !important;
    border-color: #e4e8f1 !important;
    color: #2d2d2d;
}

ul.tagit-autocomplete li:last-child {
    border-bottom: 0;
    padding-bottom: 0;
}

ul.tagit-autocomplete {
    z-index: 999999;
}

.tagit-autocomplete .ui-state-active {
    border: 1px solid #f0f0f0;
    font-weight: 400;
    color: #333;
    background: #F5F5F5;
}

.tagit-autocomplete .ui-widget-content {
    border: 1px solid #f0f0f0;
}

.tagit-autocomplete .ui-menu .ui-menu-item a.ui-state-hover,
.tagit-autocomplete .ui-menu .ui-menu-item a.ui-state-active {
    color: #333;
}

.task-modal-single ul.tagit,
.task-modal-single ul.tagit li.tagit-new {
    padding-bottom: 0;
    margin-top: 5px;
    margin-bottom: 5px;
}

ul.tagit li.tagit-choice .tagit-label:not(a) {
    font-family: 'Roboto';
}

body.rtl ul.tagit {
    display: block;
    width: 100%;
}

body.rtl ul.tagit,
body.rtl ul.tagit li.tagit-choice-editable,
body.rtl ul.tagit li.tagit-new {
    float: right;
}

ul.tagit li.tagit-choice:hover {
    background: #f0f0f0 !important;
}

.project-overview-tags ul.tagit {
    margin-bottom: -10px;
}

.tags-read-only-custom a.tagit-close {
    display: none;
}

.tags-read-only-custom ul.tagit input[type="text"] {
    display: none;
}

.tags-read-only-custom ul.tagit li.tagit-choice:hover {
    background: transparent !important;
}

.tags-read-only-custom ul.tagit li.tagit-choice-editable {
    padding-right: 10px;
}

.task-modal-single .tags-labels .label-tag,
.lead-wrapper .tags-labels .label-tag {
    float: left;
    margin-top: 5px;
}

.label-tag {
    margin-right: 5px;
    color: #2d2d2d;
    border: 1px solid #bfcbd9;
    background: #fff;
    font-size: 12.5px;
}

.label-tag:last-child {
    margin-right: 0;
}

table .tags-labels {
    float: left;
    display: inline-block;
    max-width: 180px;
    word-wrap: break-word;
    word-break: break-word;
    min-width: 100%;
    white-space: pre-wrap;
}

table .tags-labels .label-tag,
.kanban-tags .tags-labels .label-tag {
    display: inline-block;
    margin-top: 4px;
}

table .tags-in-table-row .label-tag {
    font-size: 12.5px;
}

table.dataTable tbody .tags-labels .label-tag:hover {
    cursor: pointer;
    opacity: 0.8;
}

table .dtr-data .tags-labels {
    float: none;
}

.table.project-overview-table>tbody>tr>td {
    padding-left: 2px;
}

.table.project-overview-table>tbody>tr:first-child>td {
    border-top: 0;
}

.kanban-tags .tags-labels {
    max-width: 268px;
    min-width: 100%;
}

.kanban-tags .tags-labels .label-tag {
    padding: 3px 7px;
}

.highlight-bg {
    background: #edfaff;
    border: 1px solid #def6ff;
}

#project_view_name .bootstrap-select button[data-id="project_top"] {
    border: 0 !important;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 0px;
    padding-top: 4px;
    padding-left: 17px;
}

#project_view_name .bootstrap-select .bs-caret {
    margin-left: 10px;
}

#project_view_name .bootstrap-select .bs-caret .caret {
    margin-top: 4px;
}

.custom-field-inline-edit-link {
    float: left;
    margin-right: 5px;
}

body.rtl .custom-field-inline-edit-link {
    float: right;
    margin-left: 5px;
}

.progress-bar[data-percent="0.00"] {
    color: #2d2d2d;
    margin-left: 5px;
}

.simple-bootstrap-select .bootstrap-select .btn-default {
    border: 0 !important;
    padding-left: 0;
}

.simple-bootstrap-select .bootstrap-select .dropdown-menu.open {
    border-top: 1px solid #c0cbda;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.popover-top-timer-note {
    z-index: 88888;
}

.fake-autofill-field {
    height: 1px !important;
    width: 1px !important;
    border: 0 !important;
    padding: 0 !important;
}

.notifications .not_mark_all_as_read {
    padding: 5px;
    text-align: right;
    border-bottom: 1px solid #f0f0f0;
}

.notifications .not_mark_all_as_read a:hover,
.notifications .not_mark_all_as_read a:active,
.notifications .not_mark_all_as_read a:focus {
    border-radius: 0 !important;
    color: #888;
    background-color: transparent !important;
}

.not-mark-as-read-inline {
    width: auto !important;
    position: absolute;
    right: 5px;
    bottom: 7px;
}

.not-mark-as-read-inline:hover,
.not-mark-as-read-inline:active,
.not-mark-as-read-inline:focus {
    background-color: transparent !important;
}

.not-mark-as-read-inline.notification-profile {
    position: relative;
    right: auto;
    bottom: auto;
}

.back-to-from-task {
    float: right;
    margin-left: 10px;
    margin-top: 1px;
    font-size: 17px;
}

.setup-menu-loading {
    position: absolute;
    top: 0;
    background: rgba(0, 0, 0, 0.2);
    width: 100%;
    height: 100%;
    z-index: 99;
    left: 0;
    text-align: center;
    padding-top: 15px;
}

.setup-menu-loading i {
    color: #fff;
}

body.rtl .bootstrap-select.btn-group .dropdown-toggle .filter-option {
    text-align: right;
}

body.rtl .bootstrap-select.btn-group .dropdown-toggle .caret {
    right: auto;
    left: 12px;
}

.vault_password_change_notice {
    margin-top: -10px;
}

.panel-vault .panel-body {
    border: 0;
}

.customer-profile-group-heading {
    background: #f9fafc;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-bottom: 1px solid #e4e5e7;
    padding: 15px 15px 15px 20px;
    margin: -20px -20px 15px -20px;
    color: #656565;
}

.panel_s.project-menu-panel,
.project-top-panel {
    margin-bottom: 12px;
}

.panel_s.project-menu-panel .panel-body {
    padding-top: 10px;
    padding-bottom: 10px;
}

.sub-staff-assigned-milestone {
    margin-top: 5px;
    margin-left: 5px;
}

.mce-container.mce-panel {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    -webkit-box-shadow: 0 1px 15px 1px rgba(90, 90, 90, 0.08);
    box-shadow: 0 1px 15px 1px rgba(90, 90, 90, 0.08);
}

.mce-btn button {
    font-size: 13px !important;
}

.simple-editor .mce-container.mce-panel.mce-edit-area {
    border-right-width: 1px !important;
}

.simple-editor .mce-toolbar-grp {
    padding-top: 2px !important;
    padding-bottom: 2px !important;
}

.statement_tfoot td:last-child {
    border-left: 0;
}

.statement_tfoot td:first-child {
    border-right: 0;
}

.statement-account-summary tbody>tr>td {
    padding: 3px 6px;
    border-top: 0;
}

.statement-account-summary thead>tr>th {
    border: 0 !important;
}

#project_vault_entries hr:last-child {
    display: none;
}

.font-size-14 {
    font-size: 14px;
}

.ajax-remove-values-option.bootstrap-select.ajax-search.btn-group .dropdown-toggle .caret {
    margin-right: 17px;
}

.ajax-remove-values-option .ajax-clear-values {
    font-size: 10px;
    position: absolute;
    top: 11px;
    right: 30px;
    z-index: 99;
    color: #415163;
}

body.rtl .ajax-remove-values-option .ajax-clear-values {
    left: 30px;
}

.hr-panel-heading {
    margin-left: -20px;
    margin-right: -20px;
    margin-top: 17px;
}

.hr-panel-heading.project-area-separation {
    margin-right: -15px;
}

.hr-margin-n-15 {
    margin-left: -15px;
    margin-right: -15px;
}

.hr-panel-heading-dashboard {
    margin-left: -10px;
    margin-right: -10px;
    margin-top: 0;
}

.hr-10 {
    margin-top: 10px;
    margin-bottom: 10px;
}

.modal-header .close:hover,
.modal-header .close:active {
    background: rgb(230 230 230);
    border-radius: 50%;
    outline: 0;
}

.modal-header .close {
    background: rgb(234 234 234 / 80%);
    border-radius: 50%;
    margin-top: -3px;
    font-size: 24px;
    font-weight: 400;
    opacity: 0.7;
    width: 28px;
    height: 28px;
    padding-bottom: 0;
    padding-left: 1px;
}

select.ajax-search {
    display: none;
}

.tagsinput,
input#tags {
    width: 100%;
    opacity: 0;
    height: 31.56px;
}

.bootstrap-select.btn-group .dropdown-toggle .caret {
    color: #bfcbd9;
}

#expense-form,
#new-post-form,
#project-expense-form {
    background-color: transparent;
}

.project-overview-left {
    margin-top: -20px;
    padding-top: 20px;
}

.project-overview-right .hr-panel-heading {
    margin-left: -15px;
}

.popover-title {
    background: #f9fafc;
    border-bottom: 1px solid #e4e5e7;
}

@media (min-width: 769px) {
    .tasks-page ._filter_data {
        margin-bottom: 0 !important;
    }
}

.dt-table-loading.table,
.table-loading table thead th,
.table-loading table tbody tr,
.table-loading .dataTables_length,
.table-loading .dt-buttons,
.table-loading .dataTables_filter {
    opacity: 0 !important;
}

.table-loading table thead tr {
    min-height: 120px;
    height: 120px;
}

.table-loading table thead tr th:first-child {
    border-left: 0;
}

.table-loading table thead tr>th {
    border: 0;
}

.table-loading {
    background: url(../images/table-loading.png);
    background-repeat: repeat-x;
}

.task-info-created small {
    font-size: 85%;
}

textarea[data-task-ae-editor="1"] {
    opacity: 0.7;
}

.panel-body.bottom-transaction {
    background: #fbfdff;
}

.modal .panel-body.bottom-transaction {
    background: #fff;
}

.modal .btn-bottom-toolbar,
.modal .btn-bottom-pusher {
    display: none;
}

.btn-bottom-pusher {
    margin-top: 35px;
}

.btn-bottom-toolbar {
    position: fixed;
    bottom: 0;
    padding: 15px;
    padding-right: 41px;
    margin: 0 0 0 -46px;
    -webkit-box-shadow: 0 -4px 1px -4px rgba(0, 0, 0, .1);
    box-shadow: 0 -4px 1px -4px rgba(0, 0, 0, .1);
    background: #fff;
    width: calc(100% - 211px);
    z-index: 5;
    border-top: 1px solid #ededed;
}

.btn-toolbar-container-out {
    margin-left: -10px;
}

body.hide-sidebar .btn-bottom-toolbar {
    width: 100%;
    min-width: 100%;
}

body.rtl .btn-bottom-toolbar {
    margin-right: -46px;
}

body.rtl .btn-toolbar-container-out {
    margin-left: -10px;
    margin-right: -10px;
}

@media (max-width: 768px) {
    .btn-bottom-toolbar {
        margin-left: -36px;
    }

    .btn-bottom-toolbar.btn-toolbar-container-out {
        margin-left: 0;
    }

    body.rtl .btn-bottom-toolbar.btn-toolbar-container-out {
        margin-right: 0 !important;
    }

    body.rtl .btn-bottom-toolbar {
        margin-right: -36px;
    }
}

@media (max-width: 500px) {
    .btn-bottom-toolbar .btn-toolbar-notice {
        display: none;
    }

    .btn-bottom-toolbar {
        padding-right: 6px;
        text-align: center;
    }

    .btn-bottom-toolbar .btn:first-child {
        margin-right: 10px;
    }

    .btn-bottom-toolbar .btn-group .btn:first-child {
        margin-right: 0;
    }

    .btn-bottom-toolbar .btn {
        text-align: center;
        margin-left: 5px !important;
    }

    .btn-bottom-toolbar .btn-group .btn {
        margin-left: 0 !important;
    }

    body.rtl .btn-bottom-toolbar {
        margin-right: -36px !important;
    }

    body.rtl .btn-bottom-toolbar .btn:first-child {
        margin-right: 5px !important;
    }

    body.rtl .btn-bottom-toolbar .btn-group .btn:first-child {
        margin-right: 0 !important;
    }

    body.rtl .btn-bottom-toolbar .btn {
        margin-right: 5px !important;
    }

    body.rtl .btn-bottom-toolbar .btn-group .btn {
        margin-right: 0 !important;
    }
}

table tbody tr .checkbox {
    margin-top: 0;
}

.project-info {
    margin-left: -20px;
    margin-right: -15px;
    margin-top: -10px;
    padding: 8px 22px;
    font-size: 13px;
    text-transform: uppercase;
}

.checklist-template-remove {
    position: absolute;
    right: 5px;
    top: 2px;
}

.task-single-checklist-templates {
    margin-top: -12px;
}

@media (max-width: 767px) {
    .save-checklist-template {
        position: absolute;
        right: 8px;
    }
}

.checklist-items-template-select .checklist-item-template-remove {
    position: absolute;
    right: 17px;
    top: 10px;
}

.checklist-items-template-select.show-tick .checklist-item-template-remove {
    margin-right: 20px;
    margin-top: -2px;
}

.customer-heading-profile {
    background: #f9fafc;
    margin-left: -20px;
    margin-right: -20px;
    padding: 15px 15px 15px 30px;
    border-bottom: 1px solid #e4e5e7;
}


.dashboard-user-no-qa .top-left-logout {
    float: left !important;
    margin-left: -24px;
}

body.rtl .dashboard-user-no-qa .top-left-logout {
    margin-left: -22px;
}

.dt-page-jump {
    text-align: right;
    margin-right: 15px;
}

.dt-page-jump .dt-page-jump-select {
    height: 32px;
    float: right;
    margin-top: 2px;
    margin-left: 15px;
    border: 1px solid #dddddd;
    width: auto;
}

body.rtl .dataTables_paginate {
    text-align: left !important;
    margin-left: 15px !important;
}

body.rtl .dt-page-jump .dt-page-jump-select {
    float: left;
    margin-right: 15px;
}

body.rtl .task-info .task-info-icon {
    margin-left: 6px !important;
}

body.rtl .task-single-menu {
    left: 21px;
    right: inherit;
}

body.rtl .main-tasks-table-href-name {
    margin-left: 3px;
    margin-top: -2px;
}

body.rtl .task-table-related {
    margin-top: 5px;
    display: block;
}

body.rtl .task-table-related:before {
    content: '\a';
    white-space: pre;
}

.checklist.ui-sortable-helper.relative {
    position: inherit;
}

.checklist-item-info {
    margin: 5px 0px 0px 0px;
}

.mobile .checklist-item-info {
    display: none;
}

body.system-popup {
    overflow: hidden;
}

.system-popup {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: #fff;
    z-index: 99999;
    text-align: center;
}

.system-popup .popup-wrapper {
    width: 60%;
    margin: 0 auto;
    display: table;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    /* works with row or column */
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.system-popup .popup-content {
    margin-top: 25px;
    width: 100%;
}

.system-popup .popup-message {
    font-family: 'Helvetica';
    font-weight: 600;
    font-size: 38px;
}

.system-popup .system-popup-close {
    position: absolute;
    width: 60px;
    height: 60px;
    overflow: hidden;
    top: 30px;
    right: 150px;
    background: none;
    border: 0;
    opacity: 0.2;
    outline: 0;
}

.system-popup .system-popup-close:hover::before,
.system-popup .system-popup-close:hover::after {
    background: #000;
    opacity: 1;
}

.system-popup .system-popup-close::before,
.system-popup .system-popup-close::after {
    content: '';
    position: absolute;
    height: 3px;
    width: 100%;
    top: 50%;
    left: 0;
    margin-top: -1px;
    background: #000;
}

.system-popup .system-popup-close::before {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}

.system-popup .system-popup-close::after {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

@media(max-width:768px) {
    .system-popup .popup-wrapper {
        width: 100%;
    }

    .system-popup .popup-message {
        font-size: 18px;
        max-width: 100%;
    }

    .system-popup .close {
        right: auto;
    }
}

.bg-odd {
    background-color: #f3f3f3;
}

.nav.navbar-right .open>a,
.nav.navbar-right .open>a:focus,
.nav.navbar-right .open>a:hover {
    border-color: #535f6d;
}

.tab-separator {
    border-right: 1px solid #f0f0f0 !important;
}

.dashboard .goal {
    padding-bottom: 25px;
    margin-top: 25px;
}

.dashboard .goal:first-child {
    margin-top: 0;
}

.no-shadow {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.kan-ban-col-wrapper .panel_s:first-child {
    -webkit-box-shadow: none;
    box-shadow: none;
}

table.dataTable thead>tr>th {
    color: #4e75ad;
    background: #f6f8fa;
    vertical-align: middle;
    border-bottom: 1px solid;
    border-color: #ebf5ff !important;
    font-size: 13px;
    padding-top: 9px;
    padding-bottom: 8px;
}

table.dataTable thead>tr>th.sorting_asc,
table.dataTable thead>tr>th.sorting_desc,
table.dataTable thead>tr>td.sorting_asc,
table.dataTable thead>tr>td.sorting_desc {
    background: #ebf5ff;
    font-weight: 500;
}

table.dataTable thead th:first-child {
    border-top-left-radius: 2px;
}

table.dataTable thead th:last-child {
    border-top-right-radius: 2px;
}

table.dataTable tbody tr:first-child td {
    border-top: 0;
}

div[data-container] {
    min-height: 20px;
    min-width: 20px;
}

.preview-widgets .widget {
    display: none;
}

.placeholder-dashboard-widgets {
    border: 2px dashed #b9b9b9;
    margin-bottom: 25px;
    min-height: 130px;
}

.widget-dragger:before {
    content: "\f0c9";
    font: normal normal normal 14px/1 FontAwesome;
}

.widget-dragger {
    position: absolute;
    top: 15px;
    left: -15px;
    color: #a9afbb;
    cursor: move;
    z-index: 2;
}

.widget-dragger:hover,
.widget-dragger:active {
    color: #969aa2;
}

.mobile .widget-dragger {
    display: none;
}

.screen-options-area {
    background: #fff;
    padding: 10px 30px 10px 30px;
    -webkit-box-shadow: 0 1px 0 #ccc;
    box-shadow: 0 1px 0 #ccc;
    display: none;
    border: 1px solid #ddd;
}

.screen-options-btn {
    position: absolute;
    background: #ffffff;
    color: #636363;
    padding: 5px 15px;
    display: inline-block;
    right: 38px;
    z-index: 98;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
    cursor: pointer;
    -webkit-box-shadow: 0 1px 0 #ccc;
    box-shadow: 0 1px 0 #ccc;
    margin-top: -1px;
}

body.rtl .screen-options-btn {
    right: auto;
    left: 38px;
}

.project_estimates .panel_s,
.project_invoices .panel_s,
.project_proposals .panel_s{
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}

.save_predefined_reply_from_message {
    clear: both !important;
    float: right !important;
    margin-top: 11px !important;
    margin-right: 18px !important;
    font-size: 13px !important;
    cursor: pointer !important;
}

.text-has-action {
    border-bottom: 1px dashed #bbbbbb;
    padding-bottom: 2px;
}

.task-info-inline-input-edit {
    margin-top: -1px;
    background: #F0F5F7;
    border: 0;
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-bottom: 1px dashed #bbbbbb;
}

.input-group-select .input-group-addon {
    padding-top: 8px;
    opacity: 0;
}

.input-group-select .bootstrap-select .btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

body.rtl .input-group-select .bootstrap-select .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

body.rtl .input-group .form-control:first-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

body.rtl .input-group .input-group-addon {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.kb-article .kb-group-disable-option {
    display: none;
}

.tags-in-table-row {
    width: 100%;
    margin-top: 5px;
}

.panel-full {
    margin-top: -15px;
    margin-left: -25px;
    margin-right: -25px;
}

.panel-full .panel-body {
    border-radius: 0;
    padding-left: 25px;
    padding-right: 25px;
}

@media(max-width:768px) {
    .panel-full {
        margin-left: -15px;
        margin-right: -15px;
    }
}

body.project.project-milestones .kan-ban-col .panel_s {
    margin-bottom: 0;
}

td.custom_field label:not(.cf-chk-label) {
    display: none;
}

th[align="left"],
td[align="left"] {
    text-align: left;
}

th[align="right"],
td[align="right"] {
    text-align: right;
}

th[align="center"],
td[align="center"] {
    text-align: center !important;
}

body.rtl th[align="left"],
body.rtl td[align="left"] {
    text-align: right !important;
}

body.rtl th[align="right"],
body.rtl td[align="right"] {
    text-align: left !important;
}

.no-br-tlr,
.panel_s .panel-body.no-br-tlr {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.elfinder-quicklook-titlebar .ui-icon-circle-close,
.elfinder-quicklook .ui-icon-gripsmall-diagonal-se {
    z-index: 99;
}

.elfinder-upload-dialog-wrapper .ui-button {
    padding: 6px !important;
    margin-left: 5px !important;
    margin-right: 5px !important;
}

.select-placeholder:after {
    content: " ";
    background: #ecf0f5;
    display: block !important;
    opacity: 1 !important;
    background-size: 800px 104px;
    height: 36px !important;
    position: relative;
    width: 100% !important;
    border: 1px solid #c0cbda;
    border-radius: 4px;
}

.select-placeholder .input-group {
    display: none;
}

.select-placeholder label,
.select-placeholder label small {
    color: transparent;
    text-shadow: 0 0 10px rgb(145, 162, 189);
}

#discount_area .input-group .form-control:nth-child(2) {
    border-top-left-radius: 4px !important;
    border-bottom-left-radius: 4px !important;
}

.tasks-comments .mce-container.mce-panel {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.logo-text {
    font-size: 24px;
    margin-top: 5px;
    display: inline-block;
    color: #fff;
}

.logo-text:hover,
.logo-text:focus,
.logo-text:active {
    color: #fff;
}

.items-select-wrapper select+button {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.subscription .dragger {
    background: none;
    pointer-events: none;
}

.bg-stripe {
    background: #f6f9fc;
    border: 1px solid #e5f0fb;
    display: inline-block;
    width: 100%;
    padding: 10px 15px;
    border-radius: 4px;
}

.task-comment-dropzone {
    min-height: 120px;
    max-height: 120px;
    padding: 50px;
    margin-top: 10px !important;
    margin-bottom: 10px !important;
}

.task-comment .task-attachment-wrapper {
    margin-left: 0;
}

.task-comment .task-attachment-col {
    margin-left: 0;
    padding-left: 0;
}

.task-comment .task-attachment-col[data-num="1"],
.task-comment .task-attachment-col[data-num="2"] {
    margin-top: 15px;
}

.task-comment .task-attachment-col-more {
    display: block !important;
}

.task-comment .task-attachment-wrapper .task-attachment-user {
    display: none;
}

.task-comment .task-attachment {
    max-height: 135px;
    min-height: 135px;
    padding: 5px;
}

.gdpr-purpose {
    border: 1px solid #d8d8d8;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 4px;
}

.gdpr-purpose .gdpr-purpose-heading {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 500;
    color: #5a5a5a;
}

#th-consent {
    min-width: 150px;
}

.horizontal-scrollable-tabs .scroller {
    background: transparent;
    font-weight: 600;
    cursor: pointer;
    color: #50637c;
    border-bottom: 1px solid #f0f0f0;
    border-top: 1px solid #f0f0f0;
    padding: 9px 10px;
}

.horizontal-scrollable-tabs .scroller.disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.firefox .horizontal-scrollable-tabs .horizontal-tabs .nav-tabs-horizontal {
    overflow: -moz-scrollbars-none;
}

.horizontal-scrollable-tabs .horizontal-tabs .nav-tabs-horizontal::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
}

.horizontal-scrollable-tabs .tabs-submenu-wrapper li {
    position: static;
}

.horizontal-scrollable-tabs .tabs-submenu-wrapper {
    position: absolute;
    z-index: 10;
    display: none;
}

.firefox .preview-tabs-top {
    margin-bottom: 25px;
}

.ribbon+.horizontal-scrollable-tabs .arrow-right {
    z-index: 2;
    border-top: 0px;
    position: relative;
}

.contract .scroller.arrow-left {
    margin-left: -20px;
}

.contract .scroller.arrow-right {
    margin-right: -20px;
}

body.media {
    margin-top: 0px !important;
    overflow: inherit;
    zoom: initial;
}

.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group:first-of-type,
.tinymce-mobile-toolstrip .tinymce-mobile-toolbar:not(.tinymce-mobile-context-toolbar) .tinymce-mobile-toolbar-group:last-of-type {
    background: #03a9f4 !important;
}

.mce-menubar .mce-menubtn:hover,
.mce-menubar .mce-menubtn.mce-active,
.mce-menubar .mce-menubtn:focus {
    border-radius: 4px;
}

body.rtl .input-group-select .inner.open {
    overflow-y: initial !important;
}

body.rtl .bs-searchbox input {
    margin-bottom: 10px;
}

.border-radius-4 {
    border-radius: 4px;
}

body.gantt {
    margin: inherit;
    border: inherit;
    position: inherit;
    width: inherit;
}

.gpicker {
    opacity: 0;
}

.gpicker+div[id^="dropbox-chooser"] {
    float: right;
    margin-left: 5px;
}

.gpicker {
    height: 14px;
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 11px;
    color: #4c4c4c;
    text-decoration: none;
    padding: 1px 7px 5px 3px;
    border: 1px solid #ebebeb;
    border-radius: 2px;
    border-bottom-color: #d4d4d4;
    background: #fcfcfc;
    background: -webkit-gradient(linear, left top, left bottom, from(#fcfcfc), to(#f5f5f5));
    background: linear-gradient(to bottom, #fcfcfc 0%, #f5f5f5 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fcfcfc', endColorstr='#f5f5f5', GradientType=0);
    line-height: 18px !important;
    text-decoration: none !important;
    box-sizing: content-box !important;
    -webkit-box-sizing: content-box !important;
    -moz-box-sizing: content-box !important;
}

.info-block {
    background: #fbfdff;
    border-radius: 4px;
    padding: 10px 12px;
    padding-top: 13px;
    border: 1px solid #eff5fb;
}

.info-block .info-icon-wrapper {
    float: right;
}

.info-block .info-icon {
    font-size: 18px;
}

.ticket-submitter-info {
    word-wrap: break-word;
}

.menu-badge {
    margin-left: 7px;
}

.read-more {
    overflow: hidden;
    max-height: 150px;
}

.mce-notification {
    z-index: 99999 !important;
}

.custom-fields-form-row .bootstrap-select .btn-default {
    height: 34px;
    padding: 3px 12px;
}

.table-vertical-scroll {
    height: 420px;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 15px;
}

.d-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
}

.flex-grow-1 {
    -webkit-box-flex: 1 !important;
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
}

.flex-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important;
}

.gantt .ganttGreen .bar {
    fill: #84C529;
}

.gantt .ganttRed .bar {
    fill: #cc5900;
}

.mce-content-body.tc-content ul,
.mce-content-body.tc-content ol {
    line-height: 18px;
}

.rte-autocomplete {
    z-index: 999999 !important;
}

.rte-autocomplete>li.loading {
    background: none !important;
    height: 16px;
}

span.mention {
    background-color: #eeeeee;
    padding: 3px;
}

#side-menu li .nav-second-level li >.badge,
#setup-menu li .nav-second-level li >.badge {
        position: absolute;
    right: 11px;
    top: 8px;
}

#side-menu li .nav-second-level li.active >.badge,
#setup-menu li .nav-second-level li.active >.badge {
    position: absolute;
    right: 11px;
    top: 15px;
}

.bg-danger {
    background-color: #f3031c;
}

.fc-header-toolbar.fc-toolbar.fc-toolbar-ltr {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
}

.fc-toolbar-chunk {
    margin: 5px 0px;
}

#add_note_modal .form-success,
#add_note_modal .form-failed {
    display: none;
}

#note_internal_modal #internal_note .description,
#note_history_modal #note_history .description,
#contact_history_modal #contact_history .description {
    margin-top: 5px;
    border-radius: 3px;
    border: 1px solid #dce1ef;
    padding: 5px;
}

.modal .modal-header .modal-title {
    text-transform: capitalize;
}

.content_kpi .kpi_process {
    border-top: 1px solid #ccc;
    padding-top: 20px;
    margin-top: 10px;
}
.content_kpi .title_kpi {
    margin-bottom: 20px;
    font-weight: 500;
}
.content_kpi .kpi_revenue_title {
    background: #415164;
    height: 41px;
    line-height: 42px;
    color: white;
    text-indent: 20px;
    font-weight: 500;
}
.content_kpi .kpi_revenue_content .kpi_item {
    border-bottom: 1px solid #ccc;
    padding: 10px 0px;
}
.content_kpi .day_off_titile span{
    color: #092E8C;
    cursor: pointer;
    text-indent: 10px;
}
.item_day_off .col-md-5 {
    padding: 0px 10px;
}
.content_kpi .item_day_off i.fa.fa-trash.calendar-icon {
    color: white;
    background: #F50D25;
    font-size: 20px;
    padding: 8px 12px;
    border-radius: 5px;
    cursor: pointer;
}
.item_day_off {
    margin-bottom: 10px;
}
.kpi_item_name {
    margin-top: 10px;
}
.kpi_item_weight{
    text-indent: 20px;
}
.day_off_titile span{
    margin-top: 10px;
    display: inline-block;
}
.error_input_kpi {
    border: 1px solid red !important;
    border-radius: 5px;
}

th.datepicker-switch {
    text-align: center;
}
.datepicker-months thead {
    font-size: 20px;
    font-weight: 400;
}
.datepicker-months span.month {
    display: block;
    width: 23%;
    height: 54px;
    line-height: 54px;
    float: left;
    margin: 1%;
    cursor: pointer;
    border-radius: 4px;
    text-align: center;
}
.datepicker-months span.month:hover {
    background: #eee;
}
.datepicker-months table.table-condensed {
    background: white;
}
.datepicker-months th.prev {
    cursor: pointer;
    text-align: center;
}
.datepicker-months th.next {
    cursor: pointer;
    text-align: center;
}
.datepicker-months span.month.active {
    background: red;
    color: white;
}
.datepicker-months {
    position: absolute;
    width: 270px;
    z-index: 1;
    left: 0px;
    top: 36px;
    display: none;
}
.content_tab_revenue .nav-tabs {
    border-bottom: 2px solid #ccc;
}
.content_tab_revenue li.active a,.content_tab_revenue .nav-tabs>li.active>a,.content_tab_revenue .nav-tabs>li.active>a:focus,.content_tab_revenue .nav-tabs>li.active>a:hover,.content_tab_revenue .nav-tabs>li>a:focus,.content_tab_revenue .nav-tabs>li>a:hover {
    border-bottom: 2px solid black;
    margin-bottom: 0;
    color: #000;
    font-weight: 500;
}

.result_revenue_title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom:20px
}
.col-ng-date {
    width: 30px;
    float: left;
    position: relative;
    top: 10px;
}
.result_revenue {
    margin-bottom: 40px;
    display: inline-block;
}

.content_search_day,.result_get_revenue_month{
    display: inline-block;
    width: 100%;
}
.result_get_table td,.result_get_table td{
    vertical-align: middle !important;
}
.result_get_table thead,.result_get_table thead{
    background: #C2C2C2;
    font-weight: 500;
}
.title_team_group{font-weight: 500;}
.result_get_table tbody td{
    padding: 6px 15px !important;
}
.result_get_revenue_day table td {
    padding: 9px 15px!important;
}
span.kpi_renenue {
    padding: 2px 5px;
    width: 70px;
    float: right;
    text-align: center;
    overflow: hidden;
}
.kpi_renenue_number{
    position: relative;
    top: 7px;
}
span.kpi_renenue.kpi_1 {
    border: 2px solid #09bd99;
    color: #09bd99;
    font-weight: 500;
}
span.kpi_renenue.kpi_2 {
    border: 2px solid #0ca1d0;
    color: #0ca1d0;
    font-weight: 500;
}
span.kpi_renenue.kpi_3 {
    border: 2px solid #e79223;
    color: #e79223;
    font-weight: 500;
}
span.kpi_renenue.kpi_4 {
    border: 2px solid #e45743;
    color: #e45743;
    font-weight: 500;
}
span.kpi_renenue.kpi_null {
    border: 2px solid #aba8a8;
    height: 26.56px;
}
.content_tab_revenue .nav-tabs{
    border-top: none;
}
tr.full_team_kpi {
    background: #f5f5f5;
    font-weight: 500;
    font-size: 14px;
}
tr.full_all_team_kpi {
    background: #dff7ff;
    font-size: 16px;
    font-weight: 500;
}
span.kpi_activities {
    color: #ccc;
}

td.font-weight-bold {
    font-weight: 500;
}
.note_thead_activities{
    font-size: 12px;
    font-weight: 100;
}

.table-estimates i.collapsed,
.table-estimates-single-client i.collapsed {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transition: -webkit-transform 0.3s;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
}

.table-estimates i.expand,
.table-estimates-single-client i.expand {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
}

table.dataTable tbody tr.info>.dtfc-fixed-left,
table.dataTable tbody tr.info>.dtfc-fixed-right {
    background: #d9edf7;
}

.dataTables_scrollBody .table-responsive {
    overflow-x: unset;
}

/* Server add */
.button_add_level1 {
    margin-top: 20px;
}

.button_add_level1 span {
    background: #DFF7FF;
    padding: 10px;
    color: #0CA1D0;
    cursor: pointer;
} 
.item_name_level1_left {
    position: absolute;
    font-size: 17px;
    border-right: 1px solid #ccc;
    padding-right: 5px;
    top: 5px;
    padding: 0px 10px;
}
.item_name_level1_right input {
    text-indent: 35px;
}
.result_level1 .row {
    margin: 10px 10px 20px -5px;
}
.result_level1 .row i.fa.fa-minus-circle {
    position: absolute;
    right:20px;
    font-size: 20px;
    margin-top: 7px;
    cursor: pointer;
}
.result_level1 .row i.fa.fa-minus-circle:hover{
    color: #0ca1d0;
}
.item_content_level2 {
    background: #f1f1f1;
    padding: 10px;
    margin-bottom: 40px;
}
.content_level2 {
    border-left: 1px solid #ccc;
    padding-left: 10px;
}
.button_add_level2 span {
    color: #0ca1d0;
    cursor: pointer;
}
.dropdown_sub:hover {
    color: #0ca1d0;
}
.dropdown_sub {
    position: absolute;
    font-size: 20px;
    margin-top: 10px;
    left: 20px;
    cursor: pointer;
}
.row.item_row1 {
    margin-right: 23px;
    margin-left: 17px;
}
.button_submit {
    margin-top: 40px;
    border-top: 1px solid #ccc;
    padding-top: 40px;
    font-weight: 500;
    text-align: right;
    margin-bottom: 20px;
}
span.level_submit {
    background: #0CA1D0;
    color: white;
    padding: 20px 40px;
    margin-left: 40px;
    cursor: pointer;
}
span.close_submit {
    cursor: pointer;
}
.button_add_level2 span i {
    margin-right: 5px;
}
.button_add_level1 span i {
    margin-right: 2px;
}
.group_bold_lable label.control-label {
    font-weight: 500;
}
select.form-control.type_search_select {
    margin-left: 5px;
    height: 31px;
}

/* Yêu cầu xuất hóa đơn */
#request_invoice_form .item_request_status input {
    position: relative;
    top: 2px;
    margin-right: 5px;
    margin-left: 10px;
}
.item_contract_payment_date {
    margin-left: 40px;
}
span.email-ids {
    float: left;
    margin-right: 5px;
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 5px;
    background: #f5f5f5;
    padding-top: 5px;
    padding-bottom: 5px;
    border-radius: 5px;
    border-radius: 20px;
    margin-top: 2px;
}
.request_invoice_email {
    border: 1px solid #ccc;
    height: 36px;
}
.has-error .request_invoice_email {
    border: 1px solid #fc2d42;
}
span.cancel-email {
    border: 1px solid #ccc;
    width: 18px;
    display: block;
    float: right;
    text-align: center;
    margin-left: 20px;
    border-radius: 49%;
    height: 18px;
    line-height: 15px;
    margin-top: 1px;    cursor: pointer;
}
span.to-input {
    display: block;
    float: left;
    padding-right: 11px;
}
.request_invoice_email input{
    height: 34px
}
.item_input_email input {
    border: none;
    margin-top: 0px;
    -webkit-box-shadow: inset 0 0 0 rgba(0,0,0,.075), 0 0 0px #ce8483 !important;
    box-shadow: inset 0 0 0 rgba(0,0,0,.075), 0 0 0px #ce8483 !important;
}
.all-mail {
    float: left;
}
.item_input_email{
    padding-left: 0px;
}
.item_contract_payment_date{
    display: none;
}
.top2_relative{
    position: relative;
    top: 2px
}
button.btn.request_status0 {
    background: #b8b8b8;
    text-transform: none;
}
button.btn.request_status1 {
    background: #3eb0ee;
    color:white;
    text-transform: none;
}
button.btn.request_status2 {
    background: #31b64d;
    color:white;
    text-transform: none;
}
.total_price_invoice {
    font-weight: 500;
    clear: both;
    padding: 5px 15px
}
.total_price_invoice span {
    color: blue;
    font-size: 16px;
}
.item_trang_thai_yc {
    margin-top: 5px;
}

.justify-content-between {
    -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
            justify-content: space-between !important;
}

.align-items-center {
    -webkit-box-align: center !important;
        -ms-flex-align: center !important;
            align-items: center !important;
}

.input-with-icon {
    position: relative;
}

.input-with-icon .right-icon {
    position: absolute;
    right: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
}

.text-overflow-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.estimate-items-table.promotion-v3 .discount_table {
    display: none;
}

/*! jQuery Validation Plugin - v1.17.0 - 7/29/2017
 * https://jqueryvalidation.org/
 * Copyright (c) 2017 <PERSON><PERSON><PERSON>; Licensed MIT */
!function(a){"function"==typeof define&&define.amd?define(["jquery","../jquery.validate.min"],a):"object"==typeof module&&module.exports?module.exports=a(require("jquery")):a(jQuery)}(function(a){return a.extend(a.validator.messages,{required:"Hãy nhập.",remote:"<PERSON><PERSON>y sửa cho đúng.",email:"<PERSON><PERSON>y nhập email.",url:"H<PERSON>y nhập URL.",date:"H<PERSON>y nhập ngày.",dateISO:"<PERSON><PERSON><PERSON> nhập ngày (ISO).",number:"<PERSON><PERSON><PERSON> nhập số.",digits:"Hãy nhập chữ số.",creditcard:"<PERSON><PERSON><PERSON> nhập số thẻ tín dụng.",equalTo:"<PERSON><PERSON><PERSON> nhập thêm lần nữa.",extension:"Phần mở rộng không đúng.",maxlength:a.validator.format("<PERSON><PERSON><PERSON> nhập từ {0} kí tự trở xuống."),minlength:a.validator.format("Hãy nhập từ {0} kí tự trở lên."),rangelength:a.validator.format("Hãy nhập từ {0} đến {1} kí tự."),range:a.validator.format("Hãy nhập từ {0} đến {1}."),max:a.validator.format("Hãy nhập từ {0} trở xuống."),min:a.validator.format("Hãy nhập từ {1} trở lên.")}),a});
/*!
 * Ajax Bootstrap Select
 *
 * Extends existing [Bootstrap Select] implementations by adding the ability to search via AJAX requests as you type. Originally for CROSCON.
 *
 * @version 1.4.3
 * <AUTHOR> - https://github.com/truckingsim
 * @link https://github.com/truckingsim/Ajax-Bootstrap-Select
 * @copyright 2017 Adam <PERSON>im
 * @license Released under the MIT license.
 *
 * Contributors:
 *   <PERSON> - https://github.com/markcarver
 *
 * Last build: 2017-11-15 1:19:45 PM EST
 */
!(function ($) {
/*!
 * Japanese translation for the "ja-JP" and "ja" language codes.
 * Haginaga <<EMAIL>>
 */
$.fn.ajaxSelectPicker.locale["ja-JP"]={currentlySelected:"現在の値",emptyTitle:"未選択",errorText:"検索できません",searchPlaceholder:"検索する",statusInitialized:"選択肢を入力",statusNoResults:"見つかりません",statusSearching:"検索中...",statusTooShort:"入力文字数不足"},$.fn.ajaxSelectPicker.locale.ja=$.fn.ajaxSelectPicker.locale["ja-JP"];})(jQuery);

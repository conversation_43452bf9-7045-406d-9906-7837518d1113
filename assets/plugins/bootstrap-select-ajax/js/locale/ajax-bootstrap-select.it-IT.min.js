/*!
 * Ajax Bootstrap Select
 *
 * Extends existing [Bootstrap Select] implementations by adding the ability to search via AJAX requests as you type. Originally for CROSCON.
 *
 * @version 1.4.3
 * <AUTHOR> - https://github.com/truckingsim
 * @link https://github.com/truckingsim/Ajax-Bootstrap-Select
 * @copyright 2017 Adam <PERSON>im
 * @license Released under the MIT license.
 *
 * Contributors:
 *   <PERSON> - https://github.com/markcarver
 *
 * Last build: 2017-11-15 1:19:45 PM EST
 */
!(function ($) {
/*!
 * Italian translation for the "it-IT" and "it" language codes.
 * <PERSON> <<EMAIL>>
 */
$.fn.ajaxSelectPicker.locale["it-IT"]={currentlySelected:"Selezionati",emptyTitle:"Clicca qui e scrivi...",errorText:"Impossibile recuperare dei risultati",searchPlaceholder:"Cerca...",statusInitialized:"Inizia a digitare...",statusNoResults:"Non ci sono risultati",statusSearching:"Ricerca in corso...",statusTooShort:"Inserisci altri caratteri"},$.fn.ajaxSelectPicker.locale.it=$.fn.ajaxSelectPicker.locale["it-IT"];})(jQuery);

/*!
 * Ajax Bootstrap Select
 *
 * Extends existing [Bootstrap Select] implementations by adding the ability to search via AJAX requests as you type. Originally for CROSCON.
 *
 * @version 1.4.3
 * <AUTHOR> - https://github.com/truckingsim
 * @link https://github.com/truckingsim/Ajax-Bootstrap-Select
 * @copyright 2017 Adam <PERSON>im
 * @license Released under the MIT license.
 *
 * Contributors:
 *   <PERSON> - https://github.com/markcarver
 *
 * Last build: 2017-11-15 1:19:45 PM EST
 */
!(function ($) {
/*!
 * French translation for the "fr-FR" and "fr" language codes.
 * <PERSON><PERSON><PERSON> (https://github.com/lisurc)
 */
$.fn.ajaxSelectPicker.locale["fr-FR"]={currentlySelected:"Actuellement sélectionné",emptyTitle:"Sélectionner et commencer à taper",errorText:"Impossible de récupérer les résultats",searchPlaceholder:"Rechercher...",statusInitialized:"Commencer à taper une recherche",statusNoResults:"Aucun résultat",statusSearching:"Recherche en cours...",statusTooShort:"Entrez plus de caractères"},$.fn.ajaxSelectPicker.locale.fr=$.fn.ajaxSelectPicker.locale["fr-FR"];})(jQuery);

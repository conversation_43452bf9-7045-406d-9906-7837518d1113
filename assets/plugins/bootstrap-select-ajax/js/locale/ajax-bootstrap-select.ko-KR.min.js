/*!
 * Ajax Bootstrap Select
 *
 * Extends existing [Bootstrap Select] implementations by adding the ability to search via AJAX requests as you type. Originally for CROSCON.
 *
 * @version 1.4.3
 * <AUTHOR> - https://github.com/truckingsim
 * @link https://github.com/truckingsim/Ajax-Bootstrap-Select
 * @copyright 2017 Adam <PERSON>
 * @license Released under the MIT license.
 *
 * Contributors:
 *   <PERSON> - https://github.com/markcarver
 *
 * Last build: 2017-11-15 1:19:45 PM EST
 */
!(function ($) {
/*!
 * Korean translation for the "ko-KR" and "ko" language codes.
 * <PERSON> <<EMAIL>>
 */
$.fn.ajaxSelectPicker.locale["ko-KR"]={currentlySelected:"현재 선택된 항목",emptyTitle:"클릭하고 입력 시작",errorText:"결과를 검색할 수 없습니다",searchPlaceholder:"검색",statusInitialized:"검색어를 입력",statusNoResults:"검색결과가 없습니다",statusSearching:"검색중",statusTooShort:"추가 문자를 입력하십시오."},$.fn.ajaxSelectPicker.locale.ko=$.fn.ajaxSelectPicker.locale["ko-KR"];})(jQuery);

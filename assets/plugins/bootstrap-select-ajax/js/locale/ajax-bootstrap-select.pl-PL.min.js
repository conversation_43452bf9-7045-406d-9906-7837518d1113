/*!
 * Ajax Bootstrap Select
 *
 * Extends existing [Bootstrap Select] implementations by adding the ability to search via AJAX requests as you type. Originally for CROSCON.
 *
 * @version 1.4.3
 * <AUTHOR> - https://github.com/truckingsim
 * @link https://github.com/truckingsim/Ajax-Bootstrap-Select
 * @copyright 2017 Adam <PERSON>im
 * @license Released under the MIT license.
 *
 * Contributors:
 *   <PERSON> - https://github.com/markcarver
 *
 * Last build: 2017-11-15 1:19:45 PM EST
 */
!(function ($) {
/*!
 * Polish translation for the "pl-PL" and "pl" language codes.
 * <PERSON> <rjar<PERSON>@treksoft.pl>
 */
$.fn.ajaxSelectPicker.locale["pl-PL"]={currentlySelected:"Aktualny wybór",emptyTitle:"Wybierz i zacznij pisać",errorText:"Nie można pobrać wyników",searchPlaceholder:"Szukaj...",statusInitialized:"<PERSON>ac<PERSON><PERSON><PERSON> pisać warunek wyszukiwania",statusNoResults:"<PERSON>rak wyników",statusSearching:"Szukam...",statusTooShort:"Wprowadź więcej znaków"},$.fn.ajaxSelectPicker.locale.pl=$.fn.ajaxSelectPicker.locale["pl-PL"];})(jQuery);

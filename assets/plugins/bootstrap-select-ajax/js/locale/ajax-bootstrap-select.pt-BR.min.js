/*!
 * Ajax Bootstrap Select
 *
 * Extends existing [Bootstrap Select] implementations by adding the ability to search via AJAX requests as you type. Originally for CROSCON.
 *
 * @version 1.4.3
 * <AUTHOR> - https://github.com/truckingsim
 * @link https://github.com/truckingsim/Ajax-Bootstrap-Select
 * @copyright 2017 Adam <PERSON>im
 * @license Released under the MIT license.
 *
 * Contributors:
 *   <PERSON> - https://github.com/markcarver
 *
 * Last build: 2017-11-15 1:19:45 PM EST
 */
!(function ($) {
/*!
 * Brazilian portuguese translation for the "pt-BR" and "pt" language codes.
 * <PERSON><PERSON> <<EMAIL>>
 */
$.fn.ajaxSelectPicker.locale["pt-BR"]={currentlySelected:"Selecionado Atualmente",emptyTitle:"Clique e comece a digitar",errorText:"Incapaz de encontrar resultados",searchPlaceholder:"Buscar...",statusInitialized:"Comece a digitar",statusNoResults:"Sem Resultados",statusSearching:"Buscando...",statusTooShort:"Digite mais caracteres"},$.fn.ajaxSelectPicker.locale.pt=$.fn.ajaxSelectPicker.locale["pt-BR"];})(jQuery);

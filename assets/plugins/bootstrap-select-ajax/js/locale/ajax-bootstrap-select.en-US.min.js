/*!
 * Ajax Bootstrap Select
 *
 * Extends existing [Bootstrap Select] implementations by adding the ability to search via AJAX requests as you type. Originally for CROSCON.
 *
 * @version 1.4.3
 * <AUTHOR> - https://github.com/truckingsim
 * @link https://github.com/truckingsim/Ajax-Bootstrap-Select
 * @copyright 2017 Adam <PERSON>
 * @license Released under the MIT license.
 *
 * Contributors:
 *   <PERSON> - https://github.com/markcarver
 *
 * Last build: 2017-11-15 1:19:45 PM EST
 */
!(function ($) {
/*!
 * English translation for the "en-US" and "en" language codes.
 * <PERSON> <<EMAIL>>
 */
$.fn.ajaxSelectPicker.locale["en-US"]={currentlySelected:"Currently Selected",emptyTitle:"Select and begin typing",errorText:"Unable to retrieve results",searchPlaceholder:"Search...",statusInitialized:"Start typing a search query",statusNoResults:"No Results",statusSearching:"Searching...",statusTooShort:"Please enter more characters"},$.fn.ajaxSelectPicker.locale.en=$.fn.ajaxSelectPicker.locale["en-US"];})(jQuery);

/*!
 * Ajax Bootstrap Select
 *
 * Extends existing [Bootstrap Select] implementations by adding the ability to search via AJAX requests as you type. Originally for CROSCON.
 *
 * @version 1.4.3
 * <AUTHOR> - https://github.com/truckingsim
 * @link https://github.com/truckingsim/Ajax-Bootstrap-Select
 * @copyright 2017 Adam <PERSON>im
 * @license Released under the MIT license.
 *
 * Contributors:
 *   <PERSON> - https://github.com/markcarver
 *
 * Last build: 2017-11-15 1:19:45 PM EST
 */
!(function ($) {
/*!
 * Turkish translation for the "tr-TR" and "tr" language codes.
 * Burak Çakırel <<EMAIL>>
 */
$.fn.ajaxSelectPicker.locale["tr-TR"]={currentlySelected:"Seçili olanlar",emptyTitle:"Seç ve yazmaya başla",errorText:"Sonuçlar alınamadı",searchPlaceholder:"Ara...",statusInitialized:"Arama i<PERSON>in yazmaya ba<PERSON>la",statusNoResults:"<PERSON><PERSON><PERSON> yok",statusSearching:"Aranıyor...",statusTooShort:"Lütfen daha fazla karakter girin"},$.fn.ajaxSelectPicker.locale.tr=$.fn.ajaxSelectPicker.locale["tr-TR"];})(jQuery);

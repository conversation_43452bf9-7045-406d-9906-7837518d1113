/*!
 * Ajax Bootstrap Select
 *
 * Extends existing [Bootstrap Select] implementations by adding the ability to search via AJAX requests as you type. Originally for CROSCON.
 *
 * @version 1.4.3
 * <AUTHOR> - https://github.com/truckingsim
 * @link https://github.com/truckingsim/Ajax-Bootstrap-Select
 * @copyright 2017 Adam <PERSON>
 * @license Released under the MIT license.
 *
 * Contributors:
 *   <PERSON> - https://github.com/markcarver
 *
 * Last build: 2017-11-15 1:19:45 PM EST
 */
!(function ($) {
/*!
 * Dutch translation for the "nl-NL" and "nl" language codes.
 * <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 */
$.fn.ajaxSelectPicker.locale["nl-NL"]={currentlySelected:"Momenteel geselecteerd",emptyTitle:"Selecteer en begin met typen",errorText:"Kon geen resultaten ophalen",searchPlaceholder:"Zoeken...",statusInitialized:"Begin met typen om te zoeken",statusNoResults:"Geen resultaten",statusSearching:"Zoeken...",statusTooShort:"U dient meer karakters in te voeren"},$.fn.ajaxSelectPicker.locale.nl=$.fn.ajaxSelectPicker.locale["nl-NL"];})(jQuery);

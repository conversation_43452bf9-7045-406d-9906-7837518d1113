{"version": 3, "sources": ["../../../js/i18n/defaults-nl_NL.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAChB,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;AAC5C,IAAI,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;AAC1D,IAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;AACnD,IAAI,cAAc,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC;AACnH,IAAI,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;AACvC,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;AAC3C,IAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,EAAE,EAAE,CAAC;AACL,GAAG,MAAM,EAAE,CAAC", "file": "defaults-nl_NL.js", "sourcesContent": ["(function ($) {\r\n  $.fn.selectpicker.defaults = {\r\n    noneSelectedText: 'Niets geselecteerd',\r\n    noneResultsText: 'Geen resultaten gevonden voor {0}',\r\n    countSelectedText: '{0} van {1} geselecteerd',\r\n    maxOptionsText: ['Limiet bereikt ({n} {var} max)', 'Groep limiet bereikt ({n} {var} max)', ['items', 'item']],\r\n    selectAllText: 'Alles selecteren',\r\n    deselectAllText: 'Alles deselecteren',\r\n    multipleSeparator: ', '\r\n  };\r\n})(jQuery);\r\n"]}
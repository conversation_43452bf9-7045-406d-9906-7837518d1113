/*!
 * Bootstrap-select v1.13.12 (https://developer.snapappointments.com/bootstrap-select)
 *
 * Copyright 2012-2019 SnapAppointments, LLC
 * Licensed under MIT (https://github.com/snapappointments/bootstrap-select/blob/master/LICENSE)
 */

!function(e,t){void 0===e&&void 0!==window&&(e=window),"function"==typeof define&&define.amd?define(["jquery"],function(e){return t(e)}):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(e.jQuery)}(this,function(e){e.fn.selectpicker.defaults={noneSelectedText:"\u041d\u0456\u0447\u043e\u0433\u043e \u043d\u0435 \u0432\u0438\u0431\u0440\u0430\u043d\u043e",noneResultsText:"\u0417\u0431\u0456\u0433\u0456\u0432 \u043d\u0435 \u0437\u043d\u0430\u0439\u0434\u0435\u043d\u043e {0}",countSelectedText:"\u0412\u0438\u0431\u0440\u0430\u043d\u043e {0} \u0456\u0437 {1}",maxOptionsText:["\u0414\u043e\u0441\u044f\u0433\u043d\u0443\u0442\u0430 \u043c\u0435\u0436\u0430 ({n} {var} \u043c\u0430\u043a\u0441\u0438\u043c\u0443\u043c)","\u0414\u043e\u0441\u044f\u0433\u043d\u0443\u0442\u0430 \u043c\u0435\u0436\u0430 \u0432 \u0433\u0440\u0443\u043f\u0456 ({n} {var} \u043c\u0430\u043a\u0441\u0438\u043c\u0443\u043c)",["items","item"]],multipleSeparator:", ",selectAllText:"\u0412\u0438\u0431\u0440\u0430\u0442\u0438 \u0432\u0441\u0435",deselectAllText:"\u0421\u043a\u0430\u0441\u0443\u0432\u0430\u0442\u0438 \u0432\u0438\u0431\u0456\u0440 \u0443\u0441\u0456"}});
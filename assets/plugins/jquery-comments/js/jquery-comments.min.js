/*!     jquery-comments.js 1.4.0
 *
 *     (c) 2017 <PERSON><PERSON>, Viima Solutions Oy
 *     jquery-comments may be freely distributed under the MIT license.
 *     For all details and documentation:
 *     http://viima.github.io/jquery-comments/
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=function(t,n){return void 0===n&&(n="undefined"!=typeof window?require("jquery"):require("jquery")(t)),e(n),n}:e(jQuery)}(function(e){var t={$el:null,commentsById:{},dataFetched:!1,currentSortKey:"",options:{},events:{click:"closeDropdowns","keydown [contenteditable]":"saveOnKeydown","focus [contenteditable]":"saveEditableContent","keyup [contenteditable]":"checkEditableContentForChange","paste [contenteditable]":"checkEditableContentForChange","input [contenteditable]":"checkEditableContentForChange","blur [contenteditable]":"checkEditableContentForChange","click .navigation li[data-sort-key]":"navigationElementClicked","click .navigation li.title":"toggleNavigationDropdown","click .commenting-field.main .textarea":"showMainCommentingField","click .commenting-field.main .close":"hideMainCommentingField","click .commenting-field .textarea":"increaseTextareaHeight","change .commenting-field .textarea":"increaseTextareaHeight textareaContentChanged","click .commenting-field:not(.main) .close":"removeCommentingField","click .commenting-field .send.enabled":"postComment","click .commenting-field .update.enabled":"putComment","click .commenting-field .delete.enabled":"deleteComment",'change .commenting-field .upload.enabled input[type="file"]':"fileInputChanged","click li.comment button.upvote":"upvoteComment","click li.comment button.delete.enabled":"deleteComment","click li.comment .hashtag":"hashtagClicked","click li.comment .ping":"pingClicked","click li.comment ul.child-comments .toggle-all":"toggleReplies","click li.comment button.reply":"replyButtonClicked","click li.comment button.edit":"editButtonClicked",dragenter:"showDroppableOverlay","dragenter .droppable-overlay":"handleDragEnter","dragleave .droppable-overlay":"handleDragLeaveForOverlay","dragenter .droppable-overlay .droppable":"handleDragEnter","dragleave .droppable-overlay .droppable":"handleDragLeaveForDroppable","dragover .droppable-overlay":"handleDragOverForOverlay","drop .droppable-overlay":"handleDrop","click .dropdown.autocomplete":"stopPropagation","mousedown .dropdown.autocomplete":"stopPropagation","touchstart .dropdown.autocomplete":"stopPropagation"},getDefaultOptions:function(){return{profilePictureURL:"",currentUserIsAdmin:!1,currentUserId:null,spinnerIconURL:"",upvoteIconURL:"",replyIconURL:"",uploadIconURL:"",attachmentIconURL:"",fileIconURL:"",noCommentsIconURL:"",textareaPlaceholderText:"Add a comment",newestText:"Newest",oldestText:"Oldest",popularText:"Popular",attachmentsText:"Attachments",sendText:"Send",replyText:"Reply",editText:"Edit",editedText:"Edited",youText:"You",saveText:"Save",deleteText:"Delete",newText:"New",viewAllRepliesText:"View all __replyCount__ replies",hideRepliesText:"Hide replies",noCommentsText:"No comments",noAttachmentsText:"No attachments",attachmentDropText:"Drop files here",textFormatter:function(e){return e},enableReplying:!0,enableEditing:!0,enableUpvoting:!0,enableDeleting:!0,enableAttachments:!1,enableHashtags:!1,enablePinging:!1,enableDeletingCommentWithReplies:!1,enableNavigation:!0,postCommentOnEnter:!1,forceResponsive:!1,readOnly:!1,defaultNavigationSortKey:"newest",highlightColor:"#2793e6",deleteButtonColor:"#C9302C",scrollContainer:this.$el,roundProfilePictures:!1,textareaRows:2,textareaRowsOnFocus:2,textareaMaxRows:5,maxRepliesVisible:2,wysiwyg_editor:{opts:{enable:!1,is_html:!0},init:function(e,t){},get_container:function(e){},get_contents:function(e){},on_post_comment:function(e,t,n){},on_put_comment:function(e,t,n){},on_close_button:function(e,t){}},fieldMappings:{id:"id",parent:"parent",created:"created",modified:"modified",content:"content",file:"file",fileURL:"file_url",fileMimeType:"file_mime_type",pings:"pings",creator:"creator",fullname:"fullname",profileURL:"profile_url",profilePictureURL:"profile_picture_url",isNew:"is_new",createdByAdmin:"created_by_admin",createdByCurrentUser:"created_by_current_user",upvoteCount:"upvote_count",userHasUpvoted:"user_has_upvoted"},searchUsers:function(e,t,n){t([])},getComments:function(e,t){e([])},postComment:function(e,t,n){t(e)},putComment:function(e,t,n){t(e)},deleteComment:function(e,t,n){t()},upvoteComment:function(e,t,n){t(e)},hashtagClicked:function(e){},pingClicked:function(e){},uploadAttachments:function(e,t,n){t(e)},refresh:function(){},timeFormatter:function(e){return new Date(e).toLocaleDateString()}}},init:function(t,n){var i;this.$el=e(n),this.$el.addClass("jquery-comments"),this.undelegateEvents(),this.delegateEvents(),i=navigator.userAgent||navigator.vendor||window.opera,(jQuery.browser=jQuery.browser||{}).mobile=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(i)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(i.substr(0,4)),e.browser.mobile&&this.$el.addClass("mobile"),this.options=e.extend(!0,{},this.getDefaultOptions(),t),this.options.readOnly&&this.$el.addClass("read-only"),this.currentSortKey=this.options.defaultNavigationSortKey,this.createCssDeclarations(),this.fetchDataAndRender()},delegateEvents:function(){this.bindEvents(!1)},undelegateEvents:function(){this.bindEvents(!0)},bindEvents:function(t){var n=t?"off":"on";for(var i in this.events){var a=i.split(" ")[0],o=i.split(" ").slice(1).join(" "),s=this.events[i].split(" ");for(var r in s)if(s.hasOwnProperty(r)){var l=this[s[r]];l=e.proxy(l,this),""==o?this.$el[n](a,l):this.$el[n](a,o,l)}}},fetchDataAndRender:function(){var t=this;this.commentsById={},this.$el.empty(),this.createHTML(),this.options.getComments(function(n){var i=n.map(function(e){return t.createCommentModel(e)});t.sortComments(i,"oldest"),e(i).each(function(e,n){t.addCommentToDataModel(n)}),t.dataFetched=!0,t.render()})},fetchNext:function(){var t=this,n=this.createSpinner();this.$el.find("ul#comment-list").append(n);this.options.getComments(function(i){e(i).each(function(e,n){t.createComment(n)}),n.remove()},function(){n.remove()})},createCommentModel:function(e){var t=this.applyInternalMappings(e);return t.childs=[],t},addCommentToDataModel:function(e){e.id in this.commentsById||(this.commentsById[e.id]=e,e.parent&&this.getOutermostParent(e.parent).childs.push(e.id))},updateCommentModel:function(t){e.extend(this.commentsById[t.id],t)},render:function(){this.dataFetched&&(this.showActiveContainer(),this.createComments(),this.options.enableAttachments&&this.createAttachments(),this.$el.find("> .spinner").remove(),this.options.refresh())},showActiveContainer:function(){var e=this.$el.find(".navigation li[data-container-name].active").data("container-name"),t=this.$el.find('[data-container="'+e+'"]');t.siblings("[data-container]").hide(),t.show()},createComments:function(){var t=this;this.$el.find("#comment-list").remove();var n=e("<ul/>",{id:"comment-list",class:"main"}),i=[],a=[];e(this.getComments()).each(function(e,t){null==t.parent?i.push(t):a.push(t)}),this.sortComments(i,this.currentSortKey),i.reverse(),e(i).each(function(e,i){t.addComment(i,n)}),this.sortComments(a,"oldest"),e(a).each(function(e,i){t.addComment(i,n)}),this.$el.find('[data-container="comments"]').prepend(n)},createAttachments:function(){var t=this;this.$el.find("#attachment-list").remove();var n=e("<ul/>",{id:"attachment-list",class:"main"}),i=this.getAttachments();this.sortComments(i,"newest"),i.reverse(),e(i).each(function(e,i){t.addAttachment(i,n)}),this.$el.find('[data-container="attachments"]').prepend(n)},addComment:function(e,t){t=t||this.$el.find("#comment-list");var n=this.createCommentElement(e);if(e.parent){var i=t.find('.comment[data-id="'+e.parent+'"]');this.reRenderCommentActionBar(e.parent);var a=i.parents(".comment").last();0==a.length&&(a=i);var o=a.find(".child-comments"),s=o.find(".commenting-field");s.length?s.before(n):o.append(n),this.updateToggleAllButton(a)}else"newest"==this.currentSortKey?t.prepend(n):t.append(n)},addAttachment:function(e,t){t=t||this.$el.find("#attachment-list");var n=this.createCommentElement(e);t.prepend(n)},removeComment:function(t){var n=this,i=this.commentsById[t],a=this.getChildComments(i.id);if(e(a).each(function(e,t){n.removeComment(t.id)}),i.parent){var o=this.getOutermostParent(i.parent),s=o.childs.indexOf(i.id);o.childs.splice(s,1)}delete this.commentsById[t];var r=this.$el.find('li.comment[data-id="'+t+'"]'),l=r.parents("li.comment").last();r.remove(),this.updateToggleAllButton(l)},uploadAttachments:function(t,n){var i=this;n||(n=this.$el.find(".commenting-field.main"));var a=n.find(".upload"),o=!n.hasClass("main"),s=t.length;if(s){var r=n.find(".textarea");a.removeClass("enabled");var l=this.createSpinner(),d=this.createSpinner();this.$el.find("ul#attachment-list").prepend(l),o?n.before(d):this.$el.find("ul#comment-list").prepend(d);var c=[];e(t).each(function(e,t){var n=i.createCommentJSON(r);n.id+="-"+e,n.content="",n.file=t,n.fileURL="C:/fakepath/"+t.name,n.fileMimeType=t.type,n=i.applyExternalMappings(n),c.push(n)}),i.options.uploadAttachments(c,function(t){e(t).each(function(e,t){var n=i.createCommentModel(t);i.addCommentToDataModel(n),i.addComment(n),i.addAttachment(n)}),t.length==s&&0==i.getTextareaContent(r).length&&n.find(".close").trigger("click"),a.addClass("enabled"),d.remove(),l.remove()},function(){a.addClass("enabled"),d.remove(),l.remove()})}a.find("input").val("")},updateToggleAllButton:function(t){if(null!=this.options.maxRepliesVisible){var n=t.find(".child-comments"),i=n.find(".comment").not(".hidden"),a=n.find("li.toggle-all");if(i.removeClass("togglable-reply"),0===this.options.maxRepliesVisible)var o=i;else o=i.slice(0,-this.options.maxRepliesVisible);if(o.addClass("togglable-reply"),a.find("span.text").text()==this.options.textFormatter(this.options.hideRepliesText)&&o.addClass("visible"),i.length>this.options.maxRepliesVisible){if(!a.length){a=e("<li/>",{class:"toggle-all highlight-font-bold"});var s=e("<span/>",{class:"text"}),r=e("<span/>",{class:"caret"});a.append(s).append(r),n.prepend(a)}this.setToggleAllButtonText(a,!1)}else a.remove()}},updateToggleAllButtons:function(){var t=this,n=this.$el.find("#comment-list");n.find(".comment").removeClass("visible"),n.children(".comment").each(function(n,i){t.updateToggleAllButton(e(i))})},sortComments:function(e,t){var n=this;"popularity"==t?e.sort(function(e,t){var i=e.childs.length,a=t.childs.length;if(n.options.enableUpvoting&&(i+=e.upvoteCount,a+=t.upvoteCount),a!=i)return a-i;var o=new Date(e.created).getTime();return new Date(t.created).getTime()-o}):e.sort(function(e,n){var i=new Date(e.created).getTime(),a=new Date(n.created).getTime();return"oldest"==t?i-a:a-i})},sortAndReArrangeComments:function(t){var n=this.$el.find("#comment-list"),i=this.getComments().filter(function(e){return!e.parent});this.sortComments(i,t),e(i).each(function(e,t){var i=n.find("> li.comment[data-id="+t.id+"]");n.append(i)})},showActiveSort:function(){var e=this.$el.find('.navigation li[data-sort-key="'+this.currentSortKey+'"]');this.$el.find(".navigation li").removeClass("active"),e.addClass("active");var t=this.$el.find(".navigation .title");if("attachments"!=this.currentSortKey)t.addClass("active"),t.find("header").html(e.first().html());else{var n=this.$el.find(".navigation ul.dropdown").children().first();t.find("header").html(n.html())}this.showActiveContainer()},forceResponsive:function(){this.$el.addClass("responsive")},closeDropdowns:function(){this.$el.find(".dropdown").hide()},saveOnKeydown:function(t){if(13==t.keyCode){var n=t.metaKey||t.ctrlKey;if(this.options.postCommentOnEnter||n)e(t.currentTarget).siblings(".control-row").find(".save").trigger("click"),t.stopPropagation(),t.preventDefault()}},saveEditableContent:function(t){var n=e(t.currentTarget);n.data("before",n.html())},checkEditableContentForChange:function(t){var n=e(t.currentTarget);e(n[0].childNodes).each(function(){this.nodeType==Node.TEXT_NODE&&0==this.length&&this.removeNode&&this.removeNode()}),n.data("before")!=n.html()&&(n.data("before",n.html()),n.trigger("change"))},navigationElementClicked:function(t){var n=e(t.currentTarget).data().sortKey;"attachments"!=n&&this.sortAndReArrangeComments(n),this.currentSortKey=n,this.showActiveSort()},toggleNavigationDropdown:function(t){t.stopPropagation(),e(t.currentTarget).find("~ .dropdown").toggle()},showMainCommentingField:function(t){var n=e(t.currentTarget);n.siblings(".control-row").show(),n.parent().find(".close").show(),n.parent().find(".upload.inline-button").hide(),n.focus()},hideMainCommentingField:function(t){var n=e(t.currentTarget),i=this.$el.find(".commenting-field.main .textarea"),a=this.$el.find(".commenting-field.main .control-row"),o=i.data("wysiwyg_editor");o&&this.options.wysiwyg_editor.opts.enable&&this.options.wysiwyg_editor.on_close_button(o,t),this.clearTextarea(i),this.adjustTextareaHeight(i,!1),a.hide(),n.hide(),i.parent().find(".upload.inline-button").show(),i.blur()},increaseTextareaHeight:function(t){var n=e(t.currentTarget);this.adjustTextareaHeight(n,!0)},textareaContentChanged:function(t){var n=e(t.currentTarget),i=n.data("wysiwyg_editor"),a=n.siblings(".control-row").find(".save");if(!n.find(".reply-to.tag").length)if(n.attr("data-comment")){var o=n.parents("li.comment");if(o.length>1){var s=o.last().data("id");n.attr("data-parent",s)}}else{s=n.parents("li.comment").last().data("id");n.attr("data-parent",s)}var r=n.parents(".commenting-field").first();n[0].scrollHeight>n.outerHeight()?r.addClass("commenting-field-scrollable"):r.removeClass("commenting-field-scrollable");var l,d=!0;if(l=i&&this.options.wysiwyg_editor.opts.enable?this.options.wysiwyg_editor.get_contents(i):this.getTextareaContent(n,!0),commentModel=this.commentsById[n.attr("data-comment")]){var c,p=l!=commentModel.content;commentModel.parent&&(c=commentModel.parent.toString());var m=n.attr("data-parent")!=c;d=p||m}l.length&&d?a.addClass("enabled"):a.removeClass("enabled")},removeCommentingField:function(t){var n=e(t.currentTarget);n.siblings(".textarea").attr("data-comment")&&n.parents("li.comment").first().removeClass("edit"),n.parents(".commenting-field").first().remove()},postComment:function(t){var n=this,i=e(t.currentTarget),a=i.parents(".commenting-field").first(),o=a.find(".textarea"),s=o.data("wysiwyg_editor");i.removeClass("enabled");var r=this.createCommentJSON(o);r=this.applyExternalMappings(r);var l=function(e){s&&n.options.wysiwyg_editor.opts.enable&&n.options.wysiwyg_editor.on_post_comment(s,t,e)};this.options.postComment(r,function(e){l("success"),n.createComment(e),a.find(".close").trigger("click")},function(){l("error"),i.addClass("enabled")}),l()},createComment:function(e){var t=this.createCommentModel(e);this.addCommentToDataModel(t),this.addComment(t)},putComment:function(t){var n=this,i=e(t.currentTarget),a=i.parents(".commenting-field").first(),o=a.find(".textarea"),s=o.data("wysiwyg_editor");i.removeClass("enabled");var r=e.extend({},this.commentsById[o.attr("data-comment")]);e.extend(r,{parent:o.attr("data-parent")||null,content:this.getTextareaContent(o),pings:this.getPings(o),modified:(new Date).getTime()}),r=this.applyExternalMappings(r);var l=function(e){s&&n.options.wysiwyg_editor.opts.enable&&n.options.wysiwyg_editor.on_put_comment(s,t,e)};this.options.putComment(r,function(e){l("success");var t=n.createCommentModel(e);delete t.childs,n.updateCommentModel(t),a.find(".close").trigger("click"),n.reRenderComment(t.id)},function(){l("error"),i.addClass("enabled")}),l()},deleteComment:function(t){var n=this,i=e(t.currentTarget),a=i.parents(".comment").first(),o=e.extend({},this.commentsById[a.attr("data-id")]),s=o.id,r=o.parent;i.removeClass("enabled"),o=this.applyExternalMappings(o);this.options.deleteComment(o,function(){n.removeComment(s),r&&n.reRenderCommentActionBar(r)},function(){i.addClass("enabled")})},hashtagClicked:function(t){var n=e(t.currentTarget).attr("data-value");this.options.hashtagClicked(n)},pingClicked:function(t){var n=e(t.currentTarget).attr("data-value");this.options.pingClicked(n)},fileInputChanged:function(t,n){n=t.currentTarget.files;var i=e(t.currentTarget).parents(".commenting-field").first();this.uploadAttachments(n,i)},upvoteComment:function(t){var n,i=this,a=e(t.currentTarget).parents("li.comment").first().data().model,o=a.upvoteCount;n=a.userHasUpvoted?o-1:o+1,a.userHasUpvoted=!a.userHasUpvoted,a.upvoteCount=n,this.reRenderUpvotes(a.id);var s=e.extend({},a);s=this.applyExternalMappings(s);this.options.upvoteComment(s,function(e){var t=i.createCommentModel(e);i.updateCommentModel(t),i.reRenderUpvotes(t.id)},function(){a.userHasUpvoted=!a.userHasUpvoted,a.upvoteCount=o,i.reRenderUpvotes(a.id)})},toggleReplies:function(t){var n=e(t.currentTarget);n.siblings(".togglable-reply").toggleClass("visible"),this.setToggleAllButtonText(n,!0)},replyButtonClicked:function(t){var n=e(t.currentTarget),i=n.parents("li.comment").last(),a=n.parents(".comment").first().data().id,o=i.find(".child-comments > .commenting-field");if(o.length&&o.remove(),o.find(".textarea").attr("data-parent")!=a){o=this.createCommentingFieldElement(a),i.find(".child-comments").append(o);var s=o.find(".textarea");this.options.wysiwyg_editor.opts.enable&&s.data("wysiwyg_editor",this.options.wysiwyg_editor.init(s)),this.moveCursorToEnd(s);var r=this.options.scrollContainer.scrollTop(),l=r+o.position().top+o.outerHeight(),d=r+this.options.scrollContainer.outerHeight();if(l>d){var c=r+(l-d);this.options.scrollContainer.scrollTop(c)}}},editButtonClicked:function(t){var n=e(t.currentTarget).parents("li.comment").first(),i=n.data().model;n.addClass("edit");var a=this.createCommentingFieldElement(i.parent,i.id);n.find(".comment-wrapper").first().append(a);var o=a.find(".textarea");o.attr("data-comment",i.id),o.append(this.getFormattedCommentContent(i,!0)),this.options.wysiwyg_editor.opts.enable&&o.data("wysiwyg_editor",this.options.wysiwyg_editor.init(o,i.content)),this.moveCursorToEnd(o)},showDroppableOverlay:function(e){this.options.enableAttachments&&(this.$el.find(".droppable-overlay").css("top",this.$el[0].scrollTop),this.$el.find(".droppable-overlay").show(),this.$el.addClass("drag-ongoing"))},handleDragEnter:function(t){var n=e(t.currentTarget).data("dnd-count")||0;n++,e(t.currentTarget).data("dnd-count",n),e(t.currentTarget).addClass("drag-over")},handleDragLeave:function(t,n){var i=e(t.currentTarget).data("dnd-count");i--,e(t.currentTarget).data("dnd-count",i),0==i&&(e(t.currentTarget).removeClass("drag-over"),n&&n())},handleDragLeaveForOverlay:function(e){var t=this;this.handleDragLeave(e,function(){t.hideDroppableOverlay()})},handleDragLeaveForDroppable:function(e){this.handleDragLeave(e)},handleDragOverForOverlay:function(e){e.stopPropagation(),e.preventDefault(),e.originalEvent.dataTransfer.dropEffect="copy"},hideDroppableOverlay:function(){this.$el.find(".droppable-overlay").hide(),this.$el.removeClass("drag-ongoing")},handleDrop:function(t){t.preventDefault(),e(t.target).trigger("dragleave"),this.hideDroppableOverlay(),this.uploadAttachments(t.originalEvent.dataTransfer.files)},stopPropagation:function(e){e.stopPropagation()},createHTML:function(){var t=this.createMainCommentingFieldElement();if(this.$el.append(t),t.find(".control-row").hide(),t.find(".close").hide(),this.options.wysiwyg_editor.opts.enable){var n=t.find(".textarea");n.data("wysiwyg_editor",this.options.wysiwyg_editor.init(n))}this.options.enableNavigation&&(this.$el.append(this.createNavigationElement()),this.showActiveSort());var i=this.createSpinner();this.$el.append(i);var a=e("<div/>",{class:"data-container","data-container":"comments"});this.$el.append(a);var o=e("<div/>",{class:"no-comments no-data",text:this.options.textFormatter(this.options.noCommentsText)}),s=e("<i/>",{class:"fa fa-comments fa-2x"});if(this.options.noCommentsIconURL.length&&(s.css("background-image",'url("'+this.options.noCommentsIconURL+'")'),s.addClass("image")),o.prepend(e("<br/>")).prepend(s),a.append(o),this.options.enableAttachments){var r=e("<div/>",{class:"data-container","data-container":"attachments"});this.$el.append(r);var l=e("<div/>",{class:"no-attachments no-data",text:this.options.textFormatter(this.options.noAttachmentsText)}),d=e("<i/>",{class:"fa fa-paperclip fa-2x"});this.options.attachmentIconURL.length&&(d.css("background-image",'url("'+this.options.attachmentIconURL+'")'),d.addClass("image")),l.prepend(e("<br/>")).prepend(d),r.append(l);var c=e("<div/>",{class:"droppable-overlay"}),p=e("<div/>",{class:"droppable-container"}),m=e("<div/>",{class:"droppable"}),h=e("<i/>",{class:"fa fa-paperclip fa-4x"});this.options.uploadIconURL.length&&(h.css("background-image",'url("'+this.options.uploadIconURL+'")'),h.addClass("image"));var u=e("<div/>",{text:this.options.textFormatter(this.options.attachmentDropText)});m.append(h),m.append(u),c.html(p.html(m)).hide(),this.$el.append(c)}},createProfilePictureElement:function(t,n){if(t)var i=e("<div/>").css({"background-image":"url("+t+")"});else i=e("<i/>",{class:"fa fa-user"});return i.addClass("profile-picture"),i.attr("data-user-id",n),this.options.roundProfilePictures&&i.addClass("round"),i},createMainCommentingFieldElement:function(){return this.createCommentingFieldElement(void 0,void 0,!0)},createCommentingFieldElement:function(t,n,i){var a=this,o=e("<div/>",{class:"commenting-field"});if(i&&o.addClass("main"),n)var s=this.commentsById[n].profilePictureURL,r=this.commentsById[n].creator;else s=this.options.profilePictureURL,r=this.options.creator;var l=this.createProfilePictureElement(s,r),d=e("<div/>",{class:"textarea-wrapper"}),c=e("<div/>",{class:"control-row"}),p=e("<div/>",{class:"textarea","data-placeholder":this.options.textFormatter(this.options.textareaPlaceholderText),contenteditable:!0});this.adjustTextareaHeight(p,!1);var m=e("<span/>",{class:"close inline-button"}).append(e('<span class="left"/>')).append(e('<span class="right"/>'));if(n){var h=this.options.textFormatter(this.options.saveText),u=e("<span/>",{class:"delete",text:this.options.textFormatter(this.options.deleteText)}).css("background-color",this.options.deleteButtonColor);c.append(u),this.isAllowedToDelete(n)&&u.addClass("enabled")}else{h=this.options.textFormatter(this.options.sendText);if(this.options.enableAttachments){var g=e("<span/>",{class:"enabled upload"}),f=e("<i/>",{class:"fa fa-paperclip"}),v=e("<input/>",{type:"file","data-role":"none"});e.browser.mobile||v.attr("multiple","multiple"),this.options.uploadIconURL.length&&(f.css("background-image",'url("'+this.options.uploadIconURL+'")'),f.addClass("image")),g.append(f).append(v),c.append(g.clone()),i&&d.append(g.clone().addClass("inline-button"))}}var C=e("<span/>",{class:(n?"update":"send")+" save highlight-background",text:h});if(c.prepend(C),d.append(m).append(p).append(c),this.options.wysiwyg_editor.opts.enable&&(p.hide(),d.append(this.options.wysiwyg_editor.get_container(p)).append(c)),o.append(l).append(d),t){p.attr("data-parent",t);var y=this.commentsById[t];if(y.parent){p.html("&nbsp;");var b="@"+y.fullname,w=this.createTagElement(b,"reply-to",y.creator,{"data-user-id":y.creator});p.prepend(w)}}return this.options.enablePinging&&(p.textcomplete([{match:/(^|\s)@([^@]*)$/i,index:2,search:function(e,t){e=a.normalizeSpaces(e);a.options.searchUsers(e,t,function(){t([])})},template:function(t){var n=e("<div/>"),i=a.createProfilePictureElement(t.profile_picture_url),o=e("<div/>",{class:"details"}),s=e("<div/>",{class:"name"}).html(t.fullname),r=e("<div/>",{class:"email"}).html(t.email);return t.email?o.append(s).append(r):(o.addClass("no-email"),o.append(s)),n.append(i).append(o),n.html()},replace:function(e){return" "+a.createTagElement("@"+e.fullname,"ping",e.id,{"data-user-id":e.id})[0].outerHTML+" "}}],{appendTo:".jquery-comments",dropdownClassName:"dropdown autocomplete",maxCount:5,rightEdgeOffset:0,debounce:250}),e.fn.textcomplete.Dropdown.prototype.render=function(t){var n=this._buildContents(t),i=e.map(t,function(e){return e.value});if(t.length){var o=t[0].strategy;o.id?this.$el.attr("data-strategy",o.id):this.$el.removeAttr("data-strategy"),this._renderHeader(i),this._renderFooter(i),n&&(this._renderContents(n),this._fitToBottom(),this._fitToRight(),this._activateIndexedItem()),this._setScroll()}else this.noResultsMessage?this._renderNoResultsMessage(i):this.shown&&this.deactivate();var s=parseInt(this.$el.css("top"))+a.options.scrollContainer.scrollTop();this.$el.css("top",s);var r=this.$el.css("left");this.$el.css("left",0);var l=a.$el.width()-this.$el.outerWidth(),d=Math.min(l,parseInt(r));this.$el.css("left",d)},e.fn.textcomplete.ContentEditable.prototype._skipSearch=function(e){switch(e.keyCode){case 9:case 13:case 16:case 17:case 33:case 34:case 40:case 38:case 27:return!0}if(e.ctrlKey)switch(e.keyCode){case 78:case 80:return!0}}),o},createNavigationElement:function(){var t=e("<ul/>",{class:"navigation"}),n=e("<div/>",{class:"navigation-wrapper"});t.append(n);var i=e("<li/>",{text:this.options.textFormatter(this.options.newestText),"data-sort-key":"newest","data-container-name":"comments"}),a=e("<li/>",{text:this.options.textFormatter(this.options.oldestText),"data-sort-key":"oldest","data-container-name":"comments"}),o=e("<li/>",{text:this.options.textFormatter(this.options.popularText),"data-sort-key":"popularity","data-container-name":"comments"}),s=e("<li/>",{text:this.options.textFormatter(this.options.attachmentsText),"data-sort-key":"attachments","data-container-name":"attachments"}),r=e("<i/>",{class:"fa fa-paperclip"});this.options.attachmentIconURL.length&&(r.css("background-image",'url("'+this.options.attachmentIconURL+'")'),r.addClass("image")),s.prepend(r);var l=e("<div/>",{class:"navigation-wrapper responsive"}),d=e("<ul/>",{class:"dropdown"}),c=e("<li/>",{class:"title"}),p=e("<header/>");return c.append(p),l.append(c),l.append(d),t.append(l),n.append(i).append(a),d.append(i.clone()).append(a.clone()),(this.options.enableReplying||this.options.enableUpvoting)&&(n.append(o),d.append(o.clone())),this.options.enableAttachments&&(n.append(s),l.append(s.clone())),this.options.forceResponsive&&this.forceResponsive(),t},createSpinner:function(){var t=e("<div/>",{class:"spinner"}),n=e("<i/>",{class:"fa fa-spinner fa-spin"});return this.options.spinnerIconURL.length&&(n.css("background-image",'url("'+this.options.spinnerIconURL+'")'),n.addClass("image")),t.html(n),t},createCommentElement:function(t){var n=e("<li/>",{"data-id":t.id,class:"comment"}).data("model",t);t.createdByCurrentUser&&n.addClass("by-current-user"),t.createdByAdmin&&n.addClass("by-admin");var i=e("<ul/>",{class:"child-comments"}),a=this.createCommentWrapperElement(t);return n.append(a),null==t.parent&&n.append(i),n},createCommentWrapperElement:function(t){var n=e("<div/>",{class:"comment-wrapper"}),i=this.createProfilePictureElement(t.profilePictureURL,t.creator),a=e("<time/>",{text:this.options.timeFormatter(t.created),"data-original":t.created}),o=e("<span/>",{"data-user-id":t.creator,text:t.createdByCurrentUser?this.options.textFormatter(this.options.youText):t.fullname});t.profileURL&&(o=e("<a/>",{href:t.profileURL,html:o}));var s=e("<div/>",{class:"name",html:o});if(t.createdByAdmin&&s.addClass("highlight-font-bold"),t.parent){var r=this.commentsById[t.parent];if(r.parent){var l=e("<span/>",{class:"reply-to",text:r.fullname,"data-user-id":r.creator}),d=e("<i/>",{class:"fa fa-share"});this.options.replyIconURL.length&&(d.css("background-image",'url("'+this.options.replyIconURL+'")'),d.addClass("image")),l.prepend(d),s.append(l)}}if(t.isNew){var c=e("<span/>",{class:"new highlight-background",text:this.options.textFormatter(this.options.newText)});s.append(c)}var p=e("<div/>",{class:"wrapper"}),m=e("<div/>",{class:"content"}),h=null!=t.fileURL;if(h){var u=null,g=null;if(t.fileMimeType){var f=t.fileMimeType.split("/");2==f.length&&(u=f[1],g=f[0])}var v=e("<a/>",{class:"attachment",href:t.fileURL,target:"_blank"});if("image"==g){var C=e("<img/>",{src:t.fileURL});v.html(C)}else if("video"==g){var y=e("<video/>",{src:t.fileURL,type:t.fileMimeType,controls:"controls"});v.html(y)}else{var b=["archive","audio","code","excel","image","movie","pdf","photo","picture","powerpoint","sound","video","word","zip"],w="fa fa-file-o";b.indexOf(u)>0?w="fa fa-file-"+u+"-o":b.indexOf(g)>0&&(w="fa fa-file-"+g+"-o");var x=e("<i/>",{class:w});this.options.fileIconURL.length&&(x.css("background-image",'url("'+this.options.fileIconURL+'")'),x.addClass("image"));var T=t.fileURL.split("/"),k=T[T.length-1];k=k.split("?")[0],k=decodeURIComponent(k),v.text(k),v.prepend(x)}m.html(v)}else this.options.wysiwyg_editor.opts.is_html?m.html(t.content):m.html(this.getFormattedCommentContent(t));if(t.modified&&t.modified!=t.created){var R=this.options.timeFormatter(t.modified),_=e("<time/>",{class:"edited",text:this.options.textFormatter(this.options.editedText)+" "+R,"data-original":t.modified});m.append(_)}var U=e("<span/>",{class:"actions"}),$=e("<span/>",{class:"separator",text:"·"}),E=e("<button/>",{class:"action reply",type:"button",text:this.options.textFormatter(this.options.replyText)}),A=e("<i/>",{class:"fa fa-thumbs-up"});this.options.upvoteIconURL.length&&(A.css("background-image",'url("'+this.options.upvoteIconURL+'")'),A.addClass("image"));var I=this.createUpvoteElement(t);if(this.options.enableReplying&&U.append(E),this.options.enableUpvoting&&U.append(I),t.createdByCurrentUser||this.options.currentUserIsAdmin)if(h&&this.isAllowedToDelete(t.id)){var D=e("<button/>",{class:"action delete enabled",text:this.options.textFormatter(this.options.deleteText)});U.append(D)}else if(!h&&this.options.enableEditing){var L=e("<button/>",{class:"action edit",text:this.options.textFormatter(this.options.editText)});U.append(L)}return U.children().each(function(t,n){e(n).is(":last-child")||e(n).after($.clone())}),p.append(m),p.append(U),n.append(i).append(a).append(s).append(p),n},createUpvoteElement:function(t){var n=e("<i/>",{class:"fa fa-thumbs-up"});return this.options.upvoteIconURL.length&&(n.css("background-image",'url("'+this.options.upvoteIconURL+'")'),n.addClass("image")),e("<button/>",{class:"action upvote"+(t.userHasUpvoted?" highlight-font":"")}).append(e("<span/>",{text:t.upvoteCount,class:"upvote-count"})).append(n)},createTagElement:function(t,n,i,a){var o=e("<input/>",{class:"tag",type:"button","data-role":"none"});return n&&o.addClass(n),o.val(t),o.attr("data-value",i),a&&o.attr(a),o},reRenderComment:function(t){var n=this.commentsById[t],i=this.$el.find('li.comment[data-id="'+n.id+'"]'),a=this;i.each(function(t,i){var o=a.createCommentWrapperElement(n);e(i).find(".comment-wrapper").first().replaceWith(o)})},reRenderCommentActionBar:function(t){var n=this.commentsById[t],i=this.$el.find('li.comment[data-id="'+n.id+'"]'),a=this;i.each(function(t,i){var o=a.createCommentWrapperElement(n);e(i).find(".actions").first().replaceWith(o.find(".actions"))})},reRenderUpvotes:function(t){var n=this.commentsById[t],i=this.$el.find('li.comment[data-id="'+n.id+'"]'),a=this;i.each(function(t,i){var o=a.createUpvoteElement(n);e(i).find(".upvote").first().replaceWith(o)})},createCssDeclarations:function(){e("head style.jquery-comments-css").remove(),this.createCss(".jquery-comments ul.navigation li.active:after {background: "+this.options.highlightColor+" !important;",NaN),this.createCss(".jquery-comments ul.navigation ul.dropdown li.active {background: "+this.options.highlightColor+" !important;",NaN),this.createCss(".jquery-comments .highlight-background {background: "+this.options.highlightColor+" !important;",NaN),this.createCss(".jquery-comments .highlight-font {color: "+this.options.highlightColor+" !important;}"),this.createCss(".jquery-comments .highlight-font-bold {color: "+this.options.highlightColor+" !important;font-weight: bold;}")},createCss:function(t){var n=e("<style/>",{type:"text/css",class:"jquery-comments-css",text:t});e("head").append(n)},getComments:function(){var e=this;return Object.keys(this.commentsById).map(function(t){return e.commentsById[t]})},getChildComments:function(e){return this.getComments().filter(function(t){return t.parent==e})},getAttachments:function(){return this.getComments().filter(function(e){return null!=e.fileURL})},getOutermostParent:function(e){var t=e;do{var n=this.commentsById[t];t=n.parent}while(null!=n.parent);return n},createCommentJSON:function(e){var t=(new Date).toISOString();return{id:"c"+(this.getComments().length+1),parent:e.attr("data-parent")||null,created:t,modified:t,content:this.getTextareaContent(e),pings:this.getPings(e),fullname:this.options.textFormatter(this.options.youText),profilePictureURL:this.options.profilePictureURL,createdByCurrentUser:!0,upvoteCount:0,userHasUpvoted:!1}},isAllowedToDelete:function(t){if(this.options.enableDeleting){var n=!0;return this.options.enableDeletingCommentWithReplies||e(this.getComments()).each(function(e,i){i.parent==t&&(n=!1)}),n}return!1},setToggleAllButtonText:function(e,t){var n=this,i=e.find("span.text"),a=e.find(".caret"),o=function(){var t=n.options.textFormatter(n.options.viewAllRepliesText),a=e.siblings(".comment").not(".hidden").length;t=t.replace("__replyCount__",a),i.text(t)},s=this.options.textFormatter(this.options.hideRepliesText);t?(i.text()==s?o():i.text(s),a.toggleClass("up")):i.text()!=s&&o()},adjustTextareaHeight:function(t,n){t=e(t);var i,a=1==n?this.options.textareaRowsOnFocus:this.options.textareaRows;do{i=void 0,i=2.2+1.45*(a-1),t.css("height",i+"em"),a++;var o=t[0].scrollHeight>t.outerHeight(),s=0!=this.options.textareaMaxRows&&a>this.options.textareaMaxRows}while(o&&!s)},clearTextarea:function(e){e.empty().trigger("input")},getTextareaContent:function(t,n){var i=t.data("wysiwyg_editor");if(i&&this.options.wysiwyg_editor.opts.enable)return this.options.wysiwyg_editor.get_contents(i);t.data("parent");var a=t.clone();a.find(".reply-to.tag").remove(),a.find(".tag.hashtag").replaceWith(function(){return n?e(this).val():"#"+e(this).attr("data-value")}),a.find(".tag.ping").replaceWith(function(){return n?e(this).val():"@"+e(this).attr("data-value")});var o=e("<pre/>").html(a.html());o.find("div, p, br").replaceWith(function(){return"\n"+this.innerHTML});var s=o.text().replace(/^\s+/g,"");return s=this.normalizeSpaces(s)},getFormattedCommentContent:function(e,t){var n=this.escape(e.content);return n=this.linkify(n),n=this.highlightTags(e,n),t&&(n=n.replace(/(?:\n)/g,"<br>")),n},getPings:function(t){var n={};return t.find(".ping").each(function(t,i){var a=parseInt(e(i).attr("data-value")),o=e(i).val();n[a]=o.slice(1)}),n},moveCursorToEnd:function(t){if(t=e(t)[0],e(t).trigger("input"),e(t).scrollTop(t.scrollHeight),void 0!==window.getSelection&&void 0!==document.createRange){var n=document.createRange();n.selectNodeContents(t),n.collapse(!1);var i=window.getSelection();i.removeAllRanges(),i.addRange(n)}else if(void 0!==document.body.createTextRange){var a=document.body.createTextRange();a.moveToElementText(t),a.collapse(!1),a.select()}t.focus()},escape:function(t){return e("<pre/>").text(this.normalizeSpaces(t)).html()},normalizeSpaces:function(e){return e.replace(new RegExp(" ","g")," ")},after:function(e,t){var n=this;return function(){if(0==--e)return t.apply(n,arguments)}},highlightTags:function(e,t){return this.options.enableHashtags&&(t=this.highlightHashtags(e,t)),this.options.enablePinging&&(t=this.highlightPings(e,t)),t},highlightHashtags:function(e,t){var n=this;if(-1!=t.indexOf("#")){t=t.replace(/(^|\s)#([a-zäöüß\d-_]+)/gim,function(e,t,i){return t+(a=i,(a=n.createTagElement("#"+a,"hashtag",a))[0].outerHTML);var a})}return t},highlightPings:function(t,n){var i=this;if(-1!=n.indexOf("@")){e(Object.keys(t.pings)).each(function(e,a){var o="@"+t.pings[a];n=n.replace(new RegExp(o,"g"),function(e,t){return i.createTagElement(e,"ping",t,{"data-user-id":t})[0].outerHTML}(o,a))})}return n},linkify:function(e){var t,n,i,a;if(n=/(\b(https?|ftp|file):\/\/[-A-ZÄÖÅ0-9+&@#\/%?=~_|!:,.;]*[-A-ZÄÖÅ0-9+&@#\/%=~_|])/gim,i=/(^|[^\/f])(www\.[-A-ZÄÖÅ0-9+&@#\/%?=~_|!:,.;]*[-A-ZÄÖÅ0-9+&@#\/%=~_|])/gim,a=/(([A-ZÄÖÅ0-9\-\_\.])+@[A-ZÄÖÅ\_]+?(\.[A-ZÄÖÅ]{2,6})+)/gim,t=(t=(t=e.replace(n,'<a href="$1" target="_blank">$1</a>')).replace(i,'$1<a href="https://$2" target="_blank">$2</a>')).replace(a,'<a href="mailto:$1">$1</a>'),(e.match(/<a href/g)||[]).length>0){for(var o=e.split(/(<\/a>)/g),s=0;s<o.length;s++)null==o[s].match(/<a href/g)&&(o[s]=o[s].replace(n,'<a href="$1" target="_blank">$1</a>').replace(i,'$1<a href="https://$2" target="_blank">$2</a>').replace(a,'<a href="mailto:$1">$1</a>'));return o.join("")}return t},waitUntil:function(e,t){var n=this;e()?t():setTimeout(function(){n.waitUntil(e,t)},100)},applyInternalMappings:function(e){var t={},n=this.options.fieldMappings;for(var i in n)n.hasOwnProperty(i)&&(t[n[i]]=i);return this.applyMappings(t,e)},applyExternalMappings:function(e){var t=this.options.fieldMappings;return this.applyMappings(t,e)},applyMappings:function(e,t){var n={};for(var i in t){if(i in e)n[e[i]]=t[i]}return n}};e.fn.comments=function(n){return this.each(function(){var i=Object.create(t);e.data(this,"comments",i),i.init(n||{},this)})}});

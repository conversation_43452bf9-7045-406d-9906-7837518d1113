!function(r){r.fn.googleDrivePicker=function(e){var o,c=!1,n={initGooglePickerAPI:function(e){gapi.load("auth2",function(){n.onAuthApiLoad(e)}),gapi.load("picker",n.onPickerApiLoad)},onAuthApiLoad:function(e){e.disabled=!1,e.addEventListener("click",function(){gapi.auth2.authorize({client_id:t.clientId,scope:t.scope},n.handleAuthResult)})},onPickerApiLoad:function(){c=!0,n.createPicker()},handleAuthResult:function(e){e&&!e.error?(o=e.access_token,n.createPicker()):e.error&&console.error(e)},createPicker:function(){if(c&&o){var e=(new google.picker.DocsView).setIncludeFolders(!0),i=(new google.picker.DocsUploadView).setIncludeFolders(!0);t.mimeTypes&&(e.setMimeTypes(t.mimeTypes),i.setMimeTypes(t.mimeTypes)),(new google.picker.PickerBuilder).addView(e).addView(i).setOAuthToken(o).setDeveloperKey(t.developerKey).setCallback(n.pickerCallback).build().setVisible(!0),setTimeout(function(){r(".picker-dialog").css("z-index",10002)},20)}},pickerCallback:function(e){if(e[google.picker.Response.ACTION]==google.picker.Action.PICKED){var i=[];e[google.picker.Response.DOCUMENTS].forEach(function(e){i.push({name:e[google.picker.Document.NAME],link:e[google.picker.Document.URL],mime:e[google.picker.Document.MIME_TYPE]})}),"function"==typeof t.onPick?t.onPick(i):window[t.onPick](i)}}},t=r.extend({},r.fn.googleDrivePicker.defaults,e);return this.each(function(){t.clientId?(r(this).data("on-pick")&&(t.onPick=r(this).data("on-pick")),n.initGooglePickerAPI(r(this)[0]),r(this).css("opacity",1)):r(this).css("opacity",0)})}}(jQuery),$.fn.googleDrivePicker.defaults={scope:"https://www.googleapis.com/auth/drive",mimeTypes:null,developerKey:"",clientId:"",onPick:function(e){}};
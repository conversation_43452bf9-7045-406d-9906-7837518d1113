if(void 0===$.validator)throw new Error('jQuery Validation plugin not found. "appFormValidator" requires jQuery Validation >= v1.17.0');!function(n){var o=!1;n.fn.appFormValidator=function(a){var e=this,r={email:{remote:n.fn.appFormValidator.internal_options.localization.email_exists}},i={rules:[],messages:[],ignore:[],onSubmit:!1,submitHandler:function(a){var e=n(a);e.hasClass("disable-on-submit")&&e.find('[type="submit"]').prop("disabled",!0);var r=e.find("[data-loading-text]");if(0<r.length&&r.button("loading"),!t.onSubmit)return!0;t.onSubmit(a)}},t=n.extend({},i,a);return void 0===t.messages.email&&(t.messages.email=r.email),e.configureJqueryValidationDefaults=function(){if(o)return!0;o=!0,n.validator.setDefaults({highlight:n.fn.appFormValidator.internal_options.error_highlight,unhighlight:n.fn.appFormValidator.internal_options.error_unhighlight,errorElement:n.fn.appFormValidator.internal_options.error_element,errorClass:n.fn.appFormValidator.internal_options.error_class,errorPlacement:n.fn.appFormValidator.internal_options.error_placement}),e.addMethodFileSize(),e.addMethodExtension()},e.addMethodFileSize=function(){n.validator.addMethod("filesize",function(a,e,r){return this.optional(e)||e.files[0].size<=r},n.fn.appFormValidator.internal_options.localization.file_exceeds_max_filesize)},e.addMethodExtension=function(){n.validator.addMethod("extension",function(a,e,r){return r="string"==typeof r?r.replace(/,/g,"|"):"png|jpe?g|gif",this.optional(e)||a.match(new RegExp("\\.("+r+")$","i"))},n.fn.appFormValidator.internal_options.localization.validation_extension_not_allowed)},e.validateCustomFields=function(a){n.each(a.find(n.fn.appFormValidator.internal_options.required_custom_fields_selector),function(){if(!n(this).parents("tr.main").length&&!n(this).hasClass("do-not-validate")&&(n(this).rules("add",{required:!0}),n.fn.appFormValidator.internal_options.on_required_add_symbol)){var a=n(this).parents("."+n.fn.appFormValidator.internal_options.field_wrapper_class).find('[for="'+n(this).attr("name")+'"]');0<a.length&&0===a.find(".req").length&&a.prepend('<small class="req text-danger">* </small>')}})},e.addRequiredFieldSymbol=function(i){n.fn.appFormValidator.internal_options.on_required_add_symbol&&n.each(t.rules,function(a,e){if("required"==e&&!jQuery.isPlainObject(e)||jQuery.isPlainObject(e)&&e.hasOwnProperty("required")){var r=i.find('[for="'+a+'"]');0<r.length&&0===r.find(".req").length&&r.prepend(' <small class="req text-danger">* </small>')}})},e.configureJqueryValidationDefaults(),e.each(function(){var a=n(this);a.data("validator")&&a.data("validator").destroy(),a.validate(t),e.validateCustomFields(a),e.addRequiredFieldSymbol(a),n(document).trigger("app.form-validate",a)})}}(jQuery),$.fn.appFormValidator.internal_options={localization:{email_exists:"undefined"!=typeof app?app.lang.email_exists:"Please fix this field",file_exceeds_max_filesize:"undefined"!=typeof app?app.lang.file_exceeds_max_filesize:"File Exceeds Max Filesize",validation_extension_not_allowed:"undefined"!=typeof app?$.validator.format(app.lang.validation_extension_not_allowed):$.validator.format("Extension not allowed")},on_required_add_symbol:!0,error_class:"text-danger",error_element:"p",required_custom_fields_selector:"[data-custom-field-required]",field_wrapper_class:"form-group",field_wrapper_error_class:"has-error",tab_panel_wrapper:"tab-pane",validated_tab_class:"tab-validated",error_placement:function(a,e){e.parent(".input-group").length||e.parents(".chk").length?e.parents(".chk").length?a.insertAfter(e.parents(".chk")):a.insertAfter(e.parent()):e.is("select")&&(e.hasClass("selectpicker")||e.hasClass("ajax-search"))?a.insertAfter(e.parents("."+$.fn.appFormValidator.internal_options.field_wrapper_class+" *").last()):a.insertAfter(e)},error_highlight:function(a){var e=$(a).parents("."+$.fn.appFormValidator.internal_options.tab_panel_wrapper);e.length&&!e.is(":visible")&&$('a[href="#'+e.attr("id")+'"]').css("border-bottom","1px solid red").css("color","red").addClass($.fn.appFormValidator.internal_options.validated_tab_class),$(a).is("select")?delay(function(){$(a).closest("."+$.fn.appFormValidator.internal_options.field_wrapper_class).addClass($.fn.appFormValidator.internal_options.field_wrapper_error_class)},400):$(a).closest("."+$.fn.appFormValidator.internal_options.field_wrapper_class).addClass($.fn.appFormValidator.internal_options.field_wrapper_error_class)},error_unhighlight:function(a){var e=(a=$(a)).parents("."+$.fn.appFormValidator.internal_options.tab_panel_wrapper);e.length&&$('a[href="#'+e.attr("id")+'"]').removeAttr("style").removeClass($.fn.appFormValidator.internal_options.validated_tab_class),a.closest("."+$.fn.appFormValidator.internal_options.field_wrapper_class).removeClass($.fn.appFormValidator.internal_options.field_wrapper_error_class)}};
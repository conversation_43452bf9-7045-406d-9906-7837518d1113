!function(u){var t=void 0;"undefined"!=typeof app&&(t=app.browser);var f,a={backspace:8,tab:9,enter:13,pause:19,capslock:20,esc:27,space:32,pageup:33,pagedown:34,end:35,home:36,left:37,up:38,right:39,down:40,insert:45,delete:46,f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123,"?":191,minus:"opera"==t?[109,45]:"mozilla"==t?109:[189,109],plus:"opera"==t?[61,43]:"mozilla"==t?[61,107]:[187,107]},o={},r={},n=!1,p=function(e,t){var r=e;t.ctrl&&(r+="_ctrl"),t.alt&&(r+="_alt"),t.shift&&(r+="_shift");var a=function(e,t){return t&&16!==t&&17!==t&&18!==t&&(e+="_"+t),e};if(u.isArray(t.which)){var n=[];return u.each(t.which,function(e,t){n.push(a(r,t))}),n}return a(r,t.which)},c=function(e){var r={},t=e.split("+");return u.each(t,function(e,t){"ctrl"===t||"alt"===t||"shift"===t?r[t]=!0:r.which=a[t]||t.toUpperCase().charCodeAt()}),r},i=function(e,r){if(f){var t={ctrl:r.ctrlKey,alt:r.altKey,shift:r.shiftKey,which:r.which},a=p(e,t),n=f[a];if(n){var i,s,o,c=(i=r.target,s=i.tagName.toLowerCase(),o=i.type,"input"===s&&-1<u.inArray(o,["text","password","file","search","email","number","tel","url","week","time","month","datetime-local","date"])||"textarea"===s),h=!1;u.each(n,function(e,t){c&&!t.enableInInput||(h||(r.preventDefault(),h=!0),t.handler(r))})}}};u.Shortcuts={},u.Shortcuts.start=function(e){if(f=o[e=e||"default"],!n)return u(document).on(("opera"==t?"keypress":"keydown")+".shortcuts",function(e){"keypress"===e.type&&97<=e.which&&e.which<=122&&(e.which=e.which-32),r[e.which]||i("down",e),r[e.which]=!0,i("hold",e)}),u(document).on("keyup.shortcuts",function(e){r[e.which]=!1,i("up",e)}),n=!0,this},u.Shortcuts.stop=function(){return u(document).off("keypress.shortcuts keydown.shortcuts keyup.shortcuts"),n=!1,this},u.Shortcuts.add=function(i){if(!i.mask)throw new Error("$.Shortcuts.add: required parameter 'params.mask' is undefined.");if(!i.handler)throw new Error("$.Shortcuts.add: required parameter 'params.handler' is undefined.");var s=i.type||"down",e=i.list?i.list.replace(/\s+/g,"").split(","):["default"];return u.each(e,function(e,t){o[t]||(o[t]={});var n=o[t],r=i.mask.toLowerCase().replace(/\s+/g,"").split(",");u.each(r,function(e,t){var r=c(t),a=p(s,r);u.isArray(a)||(a=[a]),u.each(a,function(e,t){n[t]||(n[t]=[]),n[t].push(i)})})}),this},u.Shortcuts.remove=function(r){if(!r.mask)throw new Error("$.Shortcuts.remove: required parameter 'params.mask' is undefined.");var i=r.type||"down",e=r.list?r.list.replace(/\s+/g,"").split(","):["default"];return u.each(e,function(e,n){if(!o[n])return!0;var t=r.mask.toLowerCase().replace(/\s+/g,"").split(",");u.each(t,function(e,t){var r=c(t),a=p(i,r);u.isArray(a)||(a=[a]),u.each(a,function(e,t){delete o[n][t]})})}),this}}(jQuery);
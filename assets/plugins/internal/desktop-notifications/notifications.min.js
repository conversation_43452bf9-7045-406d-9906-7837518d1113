(function(){var o,r,c;o=jQuery,r={title:"Notification",body:"Body",closeTime:null,icon:""},c={create_notification:function(i){return new Notification(i.title,i)},close_notification:function(i,t){if(t.closeTime)return setTimeout(i.close.bind(i),t.closeTime)},set_default_icon:function(i){return r.icon=i},isSupported:function(){return"Notification"in window&&"denied"!==Notification.permission},permission_request:function(){if("default"===Notification.permission)return Notification.requestPermission()}},o.extend({notify:function(i,t){var n,e;if(arguments.length<1)throw"Notification: few arguments";if("string"!=typeof i)throw"Notification: body must 'String'";if(r.body=i,e=o.extend(r,t),c.isSupported())return c.permission_request(),n=c.create_notification(e),c.close_notification(n,e),{click:function(t){return n.addEventListener("click",function(i){return t(i)}),this},show:function(t){return n.addEventListener("show",function(i){return t(i)}),this},close:function(t){return n.addEventListener("close",function(i){return t(i)}),this},error:function(t){return n.addEventListener("error",function(i){return t(i)}),this}}}})}).call(this);
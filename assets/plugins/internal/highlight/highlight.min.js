jQuery.extend({highlight:function(e,t,n,a){if(3===e.nodeType){var i=e.data.match(t);if(i){var r=document.createElement(n||"span");r.className=a||"highlight";var h=e.splitText(i.index);h.splitText(i[0].length);var s=h.cloneNode(!0);return h.parentNode.tagName&&"textarea"!==h.parentNode.tagName.toLowerCase()&&(r.appendChild(s),h.parentNode.replaceChild(r,h)),1}}else if(1===e.nodeType&&e.childNodes&&!/(script|style)/i.test(e.tagName)&&(e.tagName!==n.toUpperCase()||e.className!==a))for(var l=0;l<e.childNodes.length;l++)l+=jQuery.highlight(e.childNodes[l],t,n,a);return 0}}),jQuery.fn.highlight=function(e,t){var n={className:"highlight animated flash",element:"span",caseSensitive:!1,wordsOnly:!1};if(jQuery.extend(n,t),e.constructor===String&&(e=[e]),e=jQuery.grep(e,function(e,t){return""!=e}),0==(e=jQuery.map(e,function(e,t){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")})).length)return this;var a=n.caseSensitive?"":"i",i="("+e.join("|")+")";n.wordsOnly&&(i="\\b"+i+"\\b");var r=new RegExp(i,a);return this.each(function(){jQuery.highlight(this,r,n.element,n.className)})},jQuery.fn.unhighlight=function(e){var t={className:"highlight",element:"span"};return jQuery.extend(t,e),this.find(t.element+"."+t.className).each(function(){var e=this.parentNode;e.replaceChild(this.firstChild,this),e.normalize()}).end()};
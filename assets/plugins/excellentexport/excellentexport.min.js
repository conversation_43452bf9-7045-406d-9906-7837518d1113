/**
 * ExcellentExport 2.0.0
 * A client side Javascript export to Excel.
 *
 * @author: <PERSON><PERSON> (<EMAIL>)
 * @url: https://github.com/jmaister/excellentexport
 */
(function(l){var k=function(){function k(d,a,b){a=a||"";b=b||512;d=window.atob(d);var c=[],f;for(f=0;f<d.length;f+=b){var h=d.slice(f,f+b),g=Array(h.length),e;for(e=0;e<h.length;e+=1)g[e]=h.charCodeAt(e);h=new window.Uint8Array(g);c.push(h)}return new window.Blob(c,{type:a})}function n(d,a,b,c){if(window.navigator.msSaveBlob)return a=k(a,b),window.navigator.msSaveBlob(a,c),!1;window.URL.createObjectURL?(a=k(a,b),b=window.URL.createObjectURL(a,b,c),d.href=b):(d.download=c,d.href="data:"+b+";base64,"+
a);return!0}var m=",",p="\r\n",l=function(d,a){return d.replace(RegExp("{(\\w+)}","g"),function(b,c){return a[c]})};return{excel:function(d,a,b){a=a.nodeType?a:document.getElementById(a);a=window.btoa(window.unescape(encodeURIComponent(l('<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta name=ProgId content=Excel.Sheet> <meta name=Generator content="Microsoft Excel 11"><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">\x3c!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--\x3e</head><body><table>{table}</table></body></html>',
{worksheet:b||"Worksheet",table:a.innerHTML}))));return n(d,a,"application/vnd.ms-excel","export.xls")},csv:function(d,a,b,c){void 0!==b&&b&&(m=b);void 0!==c&&c&&(p=c);a=a.nodeType?a:document.getElementById(a);var f="",h,g;for(b=0;b<a.rows.length;b+=1){h=a.rows[b];for(c=0;c<h.cells.length;c+=1){g=h.cells[c];var f=f+(c?m:""),e=g.textContent.trim();g=e;var k=-1!==e.indexOf(m)||-1!==e.indexOf("\r")||-1!==e.indexOf("\n");(e=-1!==e.indexOf('"'))&&(g=g.replace(/"/g,'""'));if(k||e)g='"'+g+'"';f+=g}f+=p}a=
window.btoa(window.unescape(encodeURIComponent(f)));return n(d,a,"application/csv","export.csv")}}}();"function"===typeof define&&define.amd?define(function(){return k}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(exports=module.exports=k),exports.ExcellentExport=k):l.ExcellentExport=k})(this);
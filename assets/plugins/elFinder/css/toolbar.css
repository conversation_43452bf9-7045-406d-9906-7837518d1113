/*********************************************/
/*               TOOLBAR STYLES              */
/*********************************************/
/* toolbar container */
.elfinder-toolbar {
    padding: 4px 0 3px 0;
    border-left: 0 solid transparent;
    border-top: 0 solid transparent;
    border-right: 0 solid transparent;
    max-height: 50%;
    overflow-y: auto;
}

/* container for button's group */
.elfinder-buttonset {
    margin: 1px 4px;
    float: left;
    background: transparent;
    padding: 0;
    overflow: hidden;
}

/*.elfinder-buttonset:first-child { margin:0; }*/

/* button */
.elfinder .elfinder-button {
    min-width: 16px;
    height: 16px;
    margin: 0;
    padding: 4px;
    float: left;
    overflow: hidden;
    position: relative;
    border: 0 solid;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    line-height: 1;
    cursor: default;
}

.elfinder-rtl .elfinder-button {
    float: right;
}

.elfinder-touch .elfinder-button {
    min-width: 20px;
    height: 20px;
}

.elfinder .ui-icon-search {
    cursor: pointer;
}

/* separator between buttons, required for berder between button with ui color */
.elfinder-toolbar-button-separator {
    float: left;
    padding: 0;
    height: 24px;
    border-top: 0 solid;
    border-right: 0 solid;
    border-bottom: 0 solid;
    width: 0;
}

.elfinder-rtl .elfinder-toolbar-button-separator {
    float: right;
}

.elfinder-touch .elfinder-toolbar-button-separator {
    height: 28px;
}

/* change icon opacity^ not button */
.elfinder .elfinder-button.ui-state-disabled {
    opacity: 1;
    filter: Alpha(Opacity=100);
}

.elfinder .elfinder-button.ui-state-disabled .elfinder-button-icon,
.elfinder .elfinder-button.ui-state-disabled .elfinder-button-text {
    opacity: .4;
    filter: Alpha(Opacity=40);
}

/* rtl enviroment */
.elfinder-rtl .elfinder-buttonset {
    float: right;
}

/* icon inside button */
.elfinder-button-icon {
    width: 16px;
    height: 16px;
    /*display:block;*/
    display: inline-block;
    background: url('../img/toolbar.png') no-repeat;
}

.elfinder-button-text {
    position: relative;
    display: inline-block;
    top: -4px;
    margin: 0 2px;
    font-size: 12px;
}

.elfinder-touch .elfinder-button-icon {
    transform: scale(1.25);
    transform-origin: top left;
}

.elfinder-rtl.elfinder-touch .elfinder-button-icon {
    transform-origin: top right;
}

.elfinder-touch .elfinder-button-text {
    transform: translate(3px, 3px);
    top: -5px;
}

.elfinder-rtl.elfinder-touch .elfinder-button-text {
    transform: translate(-3px, 3px);
}

.elfinder-touch .elfinder-button-icon.elfinder-contextmenu-extra-icon {
    transform: scale(2);
    transform-origin: 12px 8px;
}

.elfinder-rtl.elfinder-touch .elfinder-button-icon.elfinder-contextmenu-extra-icon {
    transform-origin: 4px 8px;
}

/* buttons icons */
.elfinder-button-icon-home {
    background-position: 0 0;
}

.elfinder-button-icon-back {
    background-position: 0 -112px;
}

.elfinder-button-icon-forward {
    background-position: 0 -128px;
}

.elfinder-button-icon-up {
    background-position: 0 -144px;
}

.elfinder-button-icon-dir {
    background-position: 0 -16px;
}

.elfinder-button-icon-opendir {
    background-position: 0 -32px;
}

.elfinder-button-icon-reload {
    background-position: 0 -160px;
}

.elfinder-button-icon-open {
    background-position: 0 -176px;
}

.elfinder-button-icon-mkdir {
    background-position: 0 -192px;
}

.elfinder-button-icon-mkfile {
    background-position: 0 -208px;
}

.elfinder-button-icon-rm {
    background-position: 0 -832px;
}

.elfinder-button-icon-trash {
    background-position: 0 -224px;
}

.elfinder-button-icon-restore {
    background-position: 0 -816px;
}

.elfinder-button-icon-copy {
    background-position: 0 -240px;
}

.elfinder-button-icon-cut {
    background-position: 0 -256px;
}

.elfinder-button-icon-paste {
    background-position: 0 -272px;
}

.elfinder-button-icon-getfile {
    background-position: 0 -288px;
}

.elfinder-button-icon-duplicate {
    background-position: 0 -304px;
}

.elfinder-button-icon-rename {
    background-position: 0 -320px;
}

.elfinder-button-icon-edit {
    background-position: 0 -336px;
}

.elfinder-button-icon-quicklook {
    background-position: 0 -352px;
}

.elfinder-button-icon-upload {
    background-position: 0 -368px;
}

.elfinder-button-icon-download {
    background-position: 0 -384px;
}

.elfinder-button-icon-info {
    background-position: 0 -400px;
}

.elfinder-button-icon-extract {
    background-position: 0 -416px;
}

.elfinder-button-icon-archive {
    background-position: 0 -432px;
}

.elfinder-button-icon-view {
    background-position: 0 -448px;
}

.elfinder-button-icon-view-list {
    background-position: 0 -464px;
}

.elfinder-button-icon-help {
    background-position: 0 -480px;
}

.elfinder-button-icon-resize {
    background-position: 0 -512px;
}

.elfinder-button-icon-link {
    background-position: 0 -528px;
}

.elfinder-button-icon-search {
    background-position: 0 -561px;
}

.elfinder-button-icon-sort {
    background-position: 0 -577px;
}

.elfinder-button-icon-rotate-r {
    background-position: 0 -625px;
}

.elfinder-button-icon-rotate-l {
    background-position: 0 -641px;
}

.elfinder-button-icon-netmount {
    background-position: 0 -688px;
}

.elfinder-button-icon-netunmount {
    background-position: 0 -96px;
}

.elfinder-button-icon-places {
    background-position: 0 -704px;
}

.elfinder-button-icon-chmod {
    background-position: 0 -48px;
}

.elfinder-button-icon-accept {
    background-position: 0 -736px;
}

.elfinder-button-icon-menu {
    background-position: 0 -752px;
}

.elfinder-button-icon-colwidth {
    background-position: 0 -768px;
}

.elfinder-button-icon-fullscreen {
    background-position: 0 -784px;
}

.elfinder-button-icon-unfullscreen {
    background-position: 0 -800px;
}

.elfinder-button-icon-empty {
    background-position: 0 -848px;
}

.elfinder-button-icon-undo {
    background-position: 0 -864px;
}

.elfinder-button-icon-redo {
    background-position: 0 -880px;
}

.elfinder-button-icon-preference {
    background-position: 0 -896px;
}

.elfinder-button-icon-mkdirin {
    background-position: 0 -912px;
}

.elfinder-button-icon-selectall {
    background-position: 0 -928px;
}

.elfinder-button-icon-selectnone {
    background-position: 0 -944px;
}

.elfinder-button-icon-selectinvert {
    background-position: 0 -960px;
}

.elfinder-button-icon-opennew {
    background-position: 0 -976px;
}

.elfinder-button-icon-hide {
    background-position: 0 -992px;
}

.elfinder-button-icon-text {
    background-position: 0 -1008px;
}

/* button icon mirroring for rtl */
.elfinder-rtl .elfinder-button-icon-back,
.elfinder-rtl .elfinder-button-icon-forward,
.elfinder-rtl .elfinder-button-icon-getfile,
.elfinder-rtl .elfinder-button-icon-help,
.elfinder-rtl .elfinder-button-icon-redo,
.elfinder-rtl .elfinder-button-icon-rename,
.elfinder-rtl .elfinder-button-icon-search,
.elfinder-rtl .elfinder-button-icon-undo,
.elfinder-rtl .elfinder-button-icon-view-list,
.elfinder-rtl .ui-icon-search {
    -ms-transform: scale(-1, 1);
    -webkit-transform: scale(-1, 1);
    transform: scale(-1, 1);
}

.elfinder-rtl.elfinder-touch .elfinder-button-icon-back,
.elfinder-rtl.elfinder-touch .elfinder-button-icon-forward,
.elfinder-rtl.elfinder-touch .elfinder-button-icon-getfile,
.elfinder-rtl.elfinder-touch .elfinder-button-icon-help,
.elfinder-rtl.elfinder-touch .elfinder-button-icon-redo,
.elfinder-rtl.elfinder-touch .elfinder-button-icon-rename,
.elfinder-rtl.elfinder-touch .elfinder-button-icon-search,
.elfinder-rtl.elfinder-touch .elfinder-button-icon-undo,
.elfinder-rtl.elfinder-touch .elfinder-button-icon-view-list,
.elfinder-rtl.elfinder-touch .ui-icon-search {
    -ms-transform: scale(-1.25, 1.25) translateX(16px);
    -webkit-transform: scale(-1.25, 1.25) translateX(16px);
    transform: scale(-1.25, 1.25) translateX(16px);
}

/* button with dropdown menu*/
.elfinder .elfinder-menubutton {
    overflow: visible;
}

/* button with spinner icon */
.elfinder-button-icon-spinner {
    background: url("../img/spinner-mini.gif") center center no-repeat;
}

/* menu */
.elfinder-button-menu {
    position: absolute;
    margin-top: 24px;
    padding: 3px 0;
    overflow-y: auto;
}

.elfinder-touch .elfinder-button-menu {
    margin-top: 30px;
}

/* menu item */
.elfinder-button-menu-item {
    white-space: nowrap;
    cursor: default;
    padding: 5px 19px;
    position: relative;
}

.elfinder-touch .elfinder-button-menu-item {
    padding: 12px 19px
}

/* fix hover ui class */
.elfinder-button-menu .ui-state-hover {
    border: 0 solid;
}

.elfinder-button-menu-item-separated {
    border-top: 1px solid #ccc;
}

.elfinder-button-menu-item .ui-icon {
    width: 16px;
    height: 16px;
    position: absolute;
    left: 2px;
    top: 50%;
    margin-top: -8px;
    display: none;
}

.elfinder-button-menu-item-selected .ui-icon {
    display: block;
}

.elfinder-button-menu-item-selected-asc .ui-icon-arrowthick-1-s {
    display: none;
}

.elfinder-button-menu-item-selected-desc .ui-icon-arrowthick-1-n {
    display: none;
}

/* hack for upload button */
.elfinder-button form {
    position: absolute;
    top: 0;
    right: 0;
    opacity: 0;
    filter: Alpha(Opacity=0);
    cursor: pointer;
}

.elfinder .elfinder-button form input {
    background: transparent;
    cursor: default;
}

/* search "button" */
.elfinder .elfinder-button-search {
    border: 0 solid;
    background: transparent;
    padding: 0;
    margin: 1px 4px;
    height: auto;
    min-height: 26px;
    width: 70px;
    overflow: visible;
}

.elfinder .elfinder-button-search.ui-state-active {
    width: 220px;
}

/* search "pull down menu" */
.elfinder .elfinder-button-search-menu {
    font-size: 8pt;
    text-align: center;
    width: auto;
    min-width: 180px;
    position: absolute;
    top: 30px;
    padding-right: 5px;
    padding-left: 5px;
}

.elfinder-ltr .elfinder-button-search-menu {
    right: 22px;
    left: auto;
}

.elfinder-rtl .elfinder-button-search-menu {
    right: auto;
    left: 22px;
}

.elfinder-touch .elfinder-button-search-menu {
    top: 34px;
}

.elfinder .elfinder-button-search-menu div {
    margin-left: auto;
    margin-right: auto;
    margin-top: 5px;
    margin-bottom: 5px;
    display: table;
}

.elfinder .elfinder-button-search-menu div .ui-state-hover {
    border: 1px solid;
}

/* ltr/rte enviroment */
.elfinder-ltr .elfinder-button-search {
    float: right;
    margin-right: 10px;
}

.elfinder-rtl .elfinder-button-search {
    float: left;
    margin-left: 10px;
}

.elfinder-rtl .ui-controlgroup > .ui-controlgroup-item {
    float: right;
}

/* search text field */
.elfinder-button-search input[type=text] {
    box-sizing: border-box;
    width: 100%;
    height: 26px;
    padding: 0 20px;
    line-height: 22px;
    border: 0 solid;
    border: 1px solid #aaa;
    -moz-border-radius: 12px;
    -webkit-border-radius: 12px;
    border-radius: 12px;
    outline: 0px solid;
}

.elfinder-button-search input::-ms-clear {
    display: none;
}

.elfinder-touch .elfinder-button-search input {
    height: 30px;
    line-height: 28px;
}

.elfinder-rtl .elfinder-button-search input {
    direction: rtl;
}

/* icons */
.elfinder-button-search .ui-icon {
    position: absolute;
    height: 18px;
    top: 50%;
    margin: -8px 4px 0 4px;
    opacity: .6;
    filter: Alpha(Opacity=60);
}

.elfinder-button-search-menu .ui-checkboxradio-icon {
    display: none;
}

/* search/close icons */
.elfinder-ltr .elfinder-button-search .ui-icon-search {
    left: 0;
}

.elfinder-rtl .elfinder-button-search .ui-icon-search {
    right: 0;
}

.elfinder-ltr .elfinder-button-search .ui-icon-close {
    right: 0;
}

.elfinder-rtl .elfinder-button-search .ui-icon-close {
    left: 0;
}

/* toolbar swipe handle */
.elfinder-toolbar-swipe-handle {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 50px;
    width: 100%;
    pointer-events: none;
    background: linear-gradient(to bottom,
    rgba(221, 228, 235, 1) 0,
    rgba(221, 228, 235, 0.8) 2px,
    rgba(216, 223, 230, 0.3) 5px,
    rgba(0, 0, 0, 0.1) 95%,
    rgba(0, 0, 0, 0) 100%);
}


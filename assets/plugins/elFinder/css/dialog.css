/*********************************************/
/*                DIALOGS STYLES             */
/*********************************************/

/* common dialogs class */
.std42-dialog {
    padding: 0;
    position: absolute;
    left: auto;
    right: auto;
    box-sizing: border-box;
}

.std42-dialog.elfinder-dialog-minimized {
    overFlow: hidden;
    position: relative;
    float: left;
    width: auto;
    cursor: pointer;
}

.elfinder-rtl .std42-dialog.elfinder-dialog-minimized {
    float: right;
}

.std42-dialog input {
    border: 1px solid;
}

/* titlebar */
.std42-dialog .ui-dialog-titlebar {
    border-left: 0 solid transparent;
    border-top: 0 solid transparent;
    border-right: 0 solid transparent;
    font-weight: normal;
    padding: .2em 1em;
}

.std42-dialog.elfinder-dialog-minimized .ui-dialog-titlebar {
    padding: 0 .5em;
    height: 20px;
}

.elfinder-touch .std42-dialog.elfinder-dialog-minimized .ui-dialog-titlebar {
    padding: .3em .5em;
}

.std42-dialog.ui-draggable-disabled .ui-dialog-titlebar {
    cursor: default;
}

.std42-dialog .ui-dialog-titlebar .ui-widget-header {
    border: none;
    cursor: pointer;
}

.std42-dialog .ui-dialog-titlebar span.elfinder-dialog-title {
    display: inherit;
    word-break: break-all;
}

.std42-dialog.elfinder-dialog-minimized .ui-dialog-titlebar span.elfinder-dialog-title {
    display: list-item;
    display: -moz-inline-box;
    white-space: nowrap;
    word-break: normal;
    overflow: hidden;
    word-wrap: normal;
    overflow-wrap: normal;
    max-width: -webkit-calc(100% - 24px);
    max-width: -moz-calc(100% - 24px);
    max-width: calc(100% - 24px);
}

.elfinder-touch .std42-dialog .ui-dialog-titlebar span.elfinder-dialog-title {
    padding-top: .15em;
}

.elfinder-touch .std42-dialog.elfinder-dialog-minimized .ui-dialog-titlebar span.elfinder-dialog-title {
    max-width: -webkit-calc(100% - 36px);
    max-width: -moz-calc(100% - 36px);
    max-width: calc(100% - 36px);
}

.std42-dialog .ui-dialog-titlebar .elfinder-titlebar-button {
    position: relative;
    float: left;
    top: 10px;
    left: -10px;
    right: 10px;
    width: 20px;
    height: 20px;
    padding: 1px;
    margin: -10px 1px 0 1px;
    background-color: transparent;
    background-image: none;
}

.elfinder-touch .std42-dialog .ui-dialog-titlebar .elfinder-titlebar-button {
    -moz-transform: scale(1.2);
    zoom: 1.2;
    padding-left: 6px;
    padding-right: 6px;
    height: 24px;
}

.std42-dialog .ui-dialog-titlebar .elfinder-titlebar-button-right {
    float: right;
}

.std42-dialog .ui-dialog-titlebar .elfinder-titlebar-button.elfinder-titlebar-button-right {
    left: 10px;
    right: -10px;
}

.std42-dialog .ui-dialog-titlebar .elfinder-titlebar-button .ui-icon {
    width: 17px;
    height: 17px;
    border-width: 1px;
    opacity: .7;
    filter: Alpha(Opacity=70);
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
}

.elfinder-mobile .std42-dialog .ui-dialog-titlebar .elfinder-titlebar-button .ui-icon {
    opacity: .5;
    filter: Alpha(Opacity=50);
}

.std42-dialog.elfinder-dialog-minimized .ui-dialog-titlebar .elfinder-titlebar-button .ui-icon {
    opacity: 1;
    filter: Alpha(Opacity=100);
}

.std42-dialog.elfinder-dialog-minimized .ui-dialog-titlebar select {
    display: none;
}

.elfinder-spinner {
    width: 14px;
    height: 14px;
    background: url("../img/spinner-mini.gif") center center no-repeat;
    margin: 0 5px;
    display: inline-block;
    vertical-align: middle;
}

.elfinder-ltr .elfinder-spinner,
.elfinder-ltr .elfinder-spinner-text {
    float: left;
}

.elfinder-rtl .elfinder-spinner,
.elfinder-rtl .elfinder-spinner-text  {
    float: right;
}



/* resize handle for touch devices */
.elfinder-touch .std42-dialog.ui-dialog:not(ui-resizable-disabled) .ui-resizable-se {
    width: 12px;
    height: 12px;
    -moz-transform-origin: bottom right;
    -moz-transform: scale(1.5);
    zoom: 1.5;
    right: -7px;
    bottom: -7px;
    margin: 3px 7px 7px 3px;
    background-position: -64px -224px;
}

.elfinder-rtl .elfinder-dialog .ui-dialog-titlebar {
    text-align: right;
}

/* content */
.std42-dialog .ui-dialog-content {
    padding: .3em .5em;
}

.elfinder .std42-dialog .ui-dialog-content,
.elfinder .std42-dialog .ui-dialog-content * {
    -webkit-user-select: auto;
    -moz-user-select: text;
    -khtml-user-select: text;
    user-select: text;
}

.elfinder .std42-dialog .ui-dialog-content label {
    border: none;
}

/* buttons */
.std42-dialog .ui-dialog-buttonpane {
    border: 0 solid;
    margin: 0;
    padding: .5em;
    text-align: right;
}

.elfinder-rtl .std42-dialog .ui-dialog-buttonpane {
    text-align: left;
}

.std42-dialog .ui-dialog-buttonpane button {
    margin: .2em 0 0 .4em;
    padding: .2em;
    outline: 0px solid;
}

.std42-dialog .ui-dialog-buttonpane button span {
    padding: 2px 9px;
}

.std42-dialog .ui-dialog-buttonpane button span.ui-icon {
    padding: 2px;
}

.elfinder-dialog .ui-resizable-e,
.elfinder-dialog .ui-resizable-s {
    width: 0;
    height: 0;
}

.std42-dialog .ui-button input {
    cursor: pointer;
}

.std42-dialog select {
    border: 1px solid #ccc;
}

/* error/notify/confirm dialogs icon */
.elfinder-dialog-icon {
    position: absolute;
    width: 32px;
    height: 32px;
    left: 10px;
    top: 50%;
    margin-top: -15px;
    background: url("../img/dialogs.png") 0 0 no-repeat;
}

.elfinder-rtl .elfinder-dialog-icon {
    left: auto;
    right: 10px;
}

/*********************** ERROR DIALOG **************************/

.elfinder-dialog-error .ui-dialog-content,
.elfinder-dialog-confirm .ui-dialog-content {
    padding-left: 56px;
    min-height: 35px;
}

.elfinder-rtl .elfinder-dialog-error .ui-dialog-content,
.elfinder-rtl .elfinder-dialog-confirm .ui-dialog-content {
    padding-left: 0;
    padding-right: 56px;
}

.elfinder-dialog-error .elfinder-err-var {
    word-break: break-all;
}

/*********************** NOTIFY DIALOG **************************/

.elfinder-dialog-notify {
    top : 36px;
    width : 280px;
}

.elfinder-ltr .elfinder-dialog-notify {
    right : 12px;
}

.elfinder-rtl .elfinder-dialog-notify {
    left : 12px;
}

.elfinder-dialog-notify .ui-dialog-titlebar {
    height: 5px;
    overflow: hidden;
}

.elfinder.elfinder-touch > .elfinder-dialog-notify .ui-dialog-titlebar {
    height: 10px;
}

.elfinder > .elfinder-dialog-notify .ui-dialog-titlebar .elfinder-titlebar-button {
    top: 2px;
}

.elfinder.elfinder-touch > .elfinder-dialog-notify .ui-dialog-titlebar .elfinder-titlebar-button {
    top: 4px;
}

.elfinder > .elfinder-dialog-notify .ui-dialog-titlebar .elfinder-titlebar-button {
    left: -18px;
    right: 18px;
}

.elfinder > .elfinder-dialog-notify .ui-dialog-titlebar .elfinder-titlebar-button.elfinder-titlebar-button-right {
    left: 18px;
    right: -18px;
}

.ui-dialog-titlebar .elfinder-ui-progressbar {
    position: absolute;
    top: 17px;
}

.elfinder-touch .ui-dialog-titlebar .elfinder-ui-progressbar {
    top: 26px;
}

.elfinder-dialog-notify.elfinder-titlebar-button-hide .ui-dialog-titlebar-close {
    display: none;
}

.elfinder-dialog-notify.elfinder-dialog-minimized.elfinder-titlebar-button-hide .ui-dialog-titlebar span.elfinder-dialog-title {
    max-width: initial;
}

.elfinder-dialog-notify .ui-dialog-content {
    padding: 0;
}

/* one notification container */
.elfinder-notify {
    border-bottom: 1px solid #ccc;
    position: relative;
    padding: .5em;

    text-align: center;
    overflow: hidden;
}

.elfinder-ltr .elfinder-notify {
    padding-left: 36px;
}

.elfinder-rtl .elfinder-notify {
    padding-right: 36px;
}

.elfinder-notify:last-child {
    border: 0 solid;
}

/* progressbar */
.elfinder-notify-progressbar {
    width: 180px;
    height: 8px;
    border: 1px solid #aaa;
    background: #f5f5f5;
    margin: 5px auto;
    overflow: hidden;
}

.elfinder-notify-progress {
    width: 100%;
    height: 8px;
    background: url(../img/progress.gif) center center repeat-x;
}

.elfinder-notify-progressbar, .elfinder-notify-progress {
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
}

.elfinder-notify-cancel {
    position: relative;
    top: -18px;
    right: calc(-50% + 15px);
}

.elfinder-notify-cancel .ui-icon-close {
    background-position: -80px -128px;
    width: 18px;
    height: 18px;
    border-radius: 9px;
    border: none;
    background-position: -80px -128px;
    cursor: pointer;
}

/* icons */
.elfinder-dialog-icon-open,
.elfinder-dialog-icon-readdir,
.elfinder-dialog-icon-file {
    background-position: 0 -225px;
}

.elfinder-dialog-icon-reload {
    background-position: 0 -225px;
}

.elfinder-dialog-icon-mkdir {
    background-position: 0 -64px;
}

.elfinder-dialog-icon-mkfile {
    background-position: 0 -96px;
}

.elfinder-dialog-icon-copy,
.elfinder-dialog-icon-prepare,
.elfinder-dialog-icon-move {
    background-position: 0 -128px;
}

.elfinder-dialog-icon-upload {
    background-position: 0 -160px;
}

.elfinder-dialog-icon-chunkmerge {
    background-position: 0 -160px;
}

.elfinder-dialog-icon-rm {
    background-position: 0 -192px;
}

.elfinder-dialog-icon-download {
    background-position: 0 -260px;
}

.elfinder-dialog-icon-save {
    background-position: 0 -295px;
}

.elfinder-dialog-icon-rename,
.elfinder-dialog-icon-chkcontent {
    background-position: 0 -330px;
}

.elfinder-dialog-icon-zipdl,
.elfinder-dialog-icon-archive,
.elfinder-dialog-icon-extract {
    background-position: 0 -365px;
}

.elfinder-dialog-icon-search {
    background-position: 0 -402px;
}

.elfinder-dialog-icon-resize,
.elfinder-dialog-icon-loadimg,
.elfinder-dialog-icon-netmount,
.elfinder-dialog-icon-netunmount,
.elfinder-dialog-icon-chmod,
.elfinder-dialog-icon-preupload,
.elfinder-dialog-icon-url,
.elfinder-dialog-icon-dim {
    background-position: 0 -434px;
}

/*********************** CONFIRM DIALOG **************************/

.elfinder-dialog-confirm-applyall,
.elfinder-dialog-confirm-encoding {
    padding: 0 1em;
    margin: 0;
}

.elfinder-ltr .elfinder-dialog-confirm-applyall,
.elfinder-ltr .elfinder-dialog-confirm-encoding {
    text-align: left;
}

.elfinder-rtl .elfinder-dialog-confirm-applyall,
.elfinder-rtl .elfinder-dialog-confirm-encoding {
    text-align: right;
}

.elfinder-dialog-confirm .elfinder-dialog-icon {
    background-position: 0 -32px;
}

.elfinder-dialog-confirm .ui-dialog-buttonset {
    width: auto;
}

/*********************** FILE INFO DIALOG **************************/

.elfinder-info-title .elfinder-cwd-icon {
    float: left;
    width: 48px;
    height: 48px;
    margin-right: 1em;
}

.elfinder-rtl .elfinder-info-title .elfinder-cwd-icon {
    float: right;
    margin-right: 0;
    margin-left: 1em;
}

.elfinder-info-title strong {
    display: block;
    padding: .3em 0 .5em 0;
}

.elfinder-info-tb {
    min-width: 200px;
    border: 0 solid;
    margin: 1em .2em 1em .2em;
    width: 100%;
}

.elfinder-info-tb td {
    white-space: pre-wrap;
    padding: 2px;
}

.elfinder-info-tb td.elfinder-info-label {
    white-space: nowrap;
}

.elfinder-info-tb td.elfinder-info-hash {
    display: inline-block;
    word-break: break-all;
    max-width: 32ch;
}

.elfinder-ltr .elfinder-info-tb tr td:first-child {
    text-align: right;
}

.elfinder-ltr .elfinder-info-tb span {
    float: left;
}

.elfinder-rtl .elfinder-info-tb tr td:first-child {
    text-align: left;
}

.elfinder-rtl .elfinder-info-tb span {
    float: right;
}

.elfinder-info-tb a {
    outline: none;
    text-decoration: underline;
}

.elfinder-info-tb a:hover {
    text-decoration: none;
}

.elfinder-netmount-tb {
    margin: 0 auto;
}

.elfinder-netmount-tb select,
.elfinder-netmount-tb .elfinder-button-icon {
    cursor: pointer;
}

button.elfinder-info-button {
    margin: -3.5px 0;
    cursor: pointer;
}

/*********************** UPLOAD DIALOG **************************/

.elfinder-upload-dropbox {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
    padding: 0.5em;
    border: 3px dashed #aaa;
    width: 9999px;
    height: 80px;
    overflow: hidden;
    word-break: keep-all;
}

.elfinder-upload-dropbox.ui-state-hover {
    background: #dfdfdf;
    border: 3px dashed #555;
}

.elfinder-upload-dialog-or {
    margin: .3em 0;
    text-align: center;
}

.elfinder-upload-dialog-wrapper {
    text-align: center;
}

.elfinder-upload-dialog-wrapper .ui-button {
    position: relative;
    overflow: hidden;
}

.elfinder-upload-dialog-wrapper .ui-button form {
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    opacity: 0;
    filter: Alpha(Opacity=0);
}

.elfinder-upload-dialog-wrapper .ui-button form input {
    padding: 50px 0 0;
    font-size: 3em;
    width: 100%;
}

/* dialog for elFinder itself */
.dialogelfinder .dialogelfinder-drag {
    border-left: 0 solid;
    border-top: 0 solid;
    border-right: 0 solid;
    font-weight: normal;
    padding: 2px 12px;
    cursor: move;
    position: relative;
    text-align: left;
}

.elfinder-rtl .dialogelfinder-drag {
    text-align: right;
}

.dialogelfinder-drag-close {
    position: absolute;
    top: 50%;
    margin-top: -8px;
}

.elfinder-ltr .dialogelfinder-drag-close {
    right: 12px;
}

.elfinder-rtl .dialogelfinder-drag-close {
    left: 12px;
}

/*********************** RM CONFIRM **************************/
.elfinder-rm-title {
    margin-bottom: .5ex;
}

.elfinder-rm-title .elfinder-cwd-icon {
    float: left;
    width: 48px;
    height: 48px;
    margin-right: 1em;
}

.elfinder-rtl .elfinder-rm-title .elfinder-cwd-icon {
    float: right;
    margin-right: 0;
    margin-left: 1em;
}

.elfinder-rm-title strong {
    display: block;
    /*word-wrap: break-word;*/
    white-space: pre-wrap;
    word-break: normal;
    overflow: hidden;
    text-overflow: ellipsis;
}

.elfinder-rm-title + br {
    display: none;
}

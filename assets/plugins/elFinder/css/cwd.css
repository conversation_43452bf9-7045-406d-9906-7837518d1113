/******************************************************************/
/*                     CURRENT DIRECTORY STYLES                   */
/******************************************************************/
/* cwd container to avoid selectable on scrollbar */
.elfinder-cwd-wrapper {
    overflow: auto;
    position: relative;
    padding: 2px;
    margin: 0;
}

.elfinder-cwd-wrapper-list {
    padding: 0;
}

/* container */
.elfinder-cwd {
    position: absolute;
    top: 0;
    cursor: default;
    padding: 0;
    margin: 0;
    -ms-touch-action: auto;
    touch-action: auto;
    min-width: 100%;
}

.elfinder-ltr .elfinder-cwd {
    left: 0;
}

.elfinder-rtl .elfinder-cwd {
    right: 0;
}

.elfinder-cwd.elfinder-table-header-sticky {
    position: -webkit-sticky;
    position: -ms-sticky;
    position: sticky;
    top: 0;
    left: auto;
    right: auto;
    width: -webkit-max-content;
    width: -moz-max-content;
    width: -ms-max-content;
    width: max-content;
    height: 0;
    overflow: visible;
}

.elfinder-cwd.elfinder-table-header-sticky table {
    border-top: 2px solid;
    padding-top: 0;
}

.elfinder-cwd.elfinder-table-header-sticky td {
    display: inline-block;
}

.elfinder-droppable-active .elfinder-cwd.elfinder-table-header-sticky table {
    border-top: 2px solid transparent;
}

/* fixed table header container */
.elfinder-cwd-fixheader .elfinder-cwd {
    position: relative;
}

/* container active on dropenter */
.elfinder .elfinder-cwd-wrapper.elfinder-droppable-active {
    outline: 2px solid #8cafed;
    outline-offset: -2px;
}

.elfinder-cwd-wrapper-empty .elfinder-cwd:after {
    display: block;
    position: absolute;
    height: auto;
    width: 90%;
    width: calc(100% - 20px);
    position: absolute;
    top: 50%;
    left: 50%;
    -ms-transform: translateY(-50%) translateX(-50%);
    -webkit-transform: translateY(-50%) translateX(-50%);
    transform: translateY(-50%) translateX(-50%);
    line-height: 1.5em;
    text-align: center;
    white-space: pre-wrap;
    opacity: 0.6;
    filter: Alpha(Opacity=60);
    font-weight: bold;
}

.elfinder-cwd-file .elfinder-cwd-select {
    position: absolute;
    top: 0px;
    left: 0px;
    background-color: transparent;
    opacity: .4;
    filter: Alpha(Opacity=40);
}

.elfinder-mobile .elfinder-cwd-file .elfinder-cwd-select {
    width: 30px;
    height: 30px;
}

.elfinder-cwd-file.ui-selected .elfinder-cwd-select {
    opacity: .8;
    filter: Alpha(Opacity=80);
}

.elfinder-rtl .elfinder-cwd-file .elfinder-cwd-select {
    left: auto;
    right: 0px;
}

.elfinder .elfinder-cwd-selectall {
    position: absolute;
    width: 30px;
    height: 30px;
    top: 0px;
    opacity: .8;
    filter: Alpha(Opacity=80);
}

.elfinder .elfinder-workzone.elfinder-cwd-wrapper-empty .elfinder-cwd-selectall {
    display: none;
}

/************************** ICONS VIEW ********************************/

.elfinder-ltr .elfinder-workzone .elfinder-cwd-selectall {
    text-align: right;
    right: 18px;
    left: auto;
}

.elfinder-rtl .elfinder-workzone .elfinder-cwd-selectall {
    text-align: left;
    right: auto;
    left: 18px;
}

.elfinder-ltr.elfinder-mobile .elfinder-workzone .elfinder-cwd-selectall {
    right: 0px;
}

.elfinder-rtl.elfinder-mobile .elfinder-workzone .elfinder-cwd-selectall {
    left: 0px;
}

.elfinder-cwd-view-icons .elfinder-cwd-file .elfinder-cwd-select.ui-state-hover {
    background-color: transparent;
}

/* file container */
.elfinder-cwd-view-icons .elfinder-cwd-file {
    width: 120px;
    height: 90px;
    padding-bottom: 2px;
    cursor: default;
    border: none;
    position: relative;
}

.elfinder-cwd-view-icons .elfinder-cwd-file .ui-state-active {
    border: none;
}

/* ltr/rtl enviroment */
.elfinder-ltr .elfinder-cwd-view-icons .elfinder-cwd-file {
    float: left;
    margin: 0 3px 2px 0;
}

.elfinder-rtl .elfinder-cwd-view-icons .elfinder-cwd-file {
    float: right;
    margin: 0 0 5px 3px;
}

/* remove ui hover class border */
.elfinder-cwd-view-icons .elfinder-cwd-file .ui-state-hover {
    border: 0 solid;
}

/* icon wrapper to create selected highlight around icon */
.elfinder-cwd-view-icons .elfinder-cwd-file-wrapper {
    width: 52px;
    height: 52px;
    margin: 1px auto 1px auto;
    padding: 2px;
    position: relative;
}

/*** Custom Icon Size size1 - size3 ***/
/* type badge */
.elfinder-cwd-size1 .elfinder-cwd-icon:before,
.elfinder-cwd-size2 .elfinder-cwd-icon:before,
.elfinder-cwd-size3 .elfinder-cwd-icon:before {
    top: 3px;
    display: block;
}

/* size1 */
.elfinder-cwd-size1.elfinder-cwd-view-icons .elfinder-cwd-file {
    width: 120px;
    height: 112px;
}

.elfinder-cwd-size1.elfinder-cwd-view-icons .elfinder-cwd-file-wrapper {
    width: 74px;
    height: 74px;
}

.elfinder-cwd-size1 .elfinder-cwd-icon {
    -ms-transform-origin: top center;
    -ms-transform: scale(1.5);
    -webkit-transform-origin: top center;
    -webkit-transform: scale(1.5);
    transform-origin: top center;
    transform: scale(1.5);
}

.elfinder-cwd-size1 .elfinder-cwd-icon.elfinder-cwd-bgurl:before {
    -ms-transform-origin: top left;
    -ms-transform: scale(1.35) translate(-4px, 15%);
    -webkit-transform-origin: top left;
    -webkit-transform: scale(1.35) translate(-4px, 15%);
    transform-origin: top left;
    transform: scale(1.35) translate(-4px, 15%);
}

.elfinder-cwd-size1 .elfinder-cwd-icon.elfinder-cwd-bgurl:after {
    -ms-transform: scale(1) translate(10px, -5px);
    -webkit-transform: scale(1) translate(10px, -5px);
    transform: scale(1) translate(10px, -5px);
}

.elfinder-cwd-size1 .elfinder-cwd-icon.elfinder-cwd-bgurl {
    -ms-transform-origin: center center;
    -ms-transform: scale(1);
    -webkit-transform-origin: center center;
    -webkit-transform: scale(1);
    transform-origin: center center;
    transform: scale(1);
    width: 72px;
    height: 72px;
    -moz-border-radius: 6px;
    -webkit-border-radius: 6px;
    border-radius: 6px;
}

/* size2 */
.elfinder-cwd-size2.elfinder-cwd-view-icons .elfinder-cwd-file {
    width: 140px;
    height: 134px;
}

.elfinder-cwd-size2.elfinder-cwd-view-icons .elfinder-cwd-file-wrapper {
    width: 98px;
    height: 98px;
}

.elfinder-cwd-size2 .elfinder-cwd-icon {
    -ms-transform-origin: top center;
    -ms-transform: scale(2);
    -webkit-transform-origin: top center;
    -webkit-transform: scale(2);
    transform-origin: top center;
    transform: scale(2);
}

.elfinder-cwd-size2 .elfinder-cwd-icon.elfinder-cwd-bgurl:before {
    -ms-transform-origin: top left;
    -ms-transform: scale(1.8) translate(-5px, 18%);
    -webkit-transform-origin: top left;
    -webkit-transform: scale(1.8) translate(-5px, 18%);
    transform-origin: top left;
    transform: scale(1.8) translate(-5px, 18%);
}

.elfinder-cwd-size2 .elfinder-cwd-icon.elfinder-cwd-bgurl:after {
    -ms-transform: scale(1.1) translate(0px, 10px);
    -webkit-transform: scale(1.1) translate(0px, 10px);
    transform: scale(1.1) translate(0px, 10px);
}

.elfinder-cwd-size2 .elfinder-cwd-icon.elfinder-cwd-bgurl {
    -ms-transform-origin: center center;
    -ms-transform: scale(1);
    -webkit-transform-origin: center center;
    -webkit-transform: scale(1);
    transform-origin: center center;
    transform: scale(1);
    width: 96px;
    height: 96px;
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
}

/* size3 */
.elfinder-cwd-size3.elfinder-cwd-view-icons .elfinder-cwd-file {
    width: 174px;
    height: 158px;
}

.elfinder-cwd-size3.elfinder-cwd-view-icons .elfinder-cwd-file-wrapper {
    width: 122px;
    height: 122px;
}

.elfinder-cwd-size3 .elfinder-cwd-icon {
    -ms-transform-origin: top center;
    -ms-transform: scale(2.5);
    -webkit-transform-origin: top center;
    -webkit-transform: scale(2.5);
    transform-origin: top center;
    transform: scale(2.5);
}

.elfinder-cwd-size3 .elfinder-cwd-icon.elfinder-cwd-bgurl:before {
    -ms-transform-origin: top left;
    -ms-transform: scale(2.25) translate(-6px, 20%);
    -webkit-transform-origin: top left;
    -webkit-transform: scale(2.25) translate(-6px, 20%);
    transform-origin: top left;
    transform: scale(2.25) translate(-6px, 20%);
}

.elfinder-cwd-size3 .elfinder-cwd-icon.elfinder-cwd-bgurl:after {
    -ms-transform: scale(1.2) translate(-9px, 22px);
    -webkit-transform: scale(1.2) translate(-9px, 22px);
    transform: scale(1.2) translate(-9px, 22px);
}

.elfinder-cwd-size3 .elfinder-cwd-icon.elfinder-cwd-bgurl {
    -ms-transform-origin: center center;
    -ms-transform: scale(1);
    -webkit-transform-origin: center center;
    -webkit-transform: scale(1);
    transform-origin: center center;
    transform: scale(1);
    width: 120px;
    height: 120px;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}

/* file name place */
.elfinder-cwd-view-icons .elfinder-cwd-filename {
    text-align: center;
    max-height: 2.4em;
    line-height: 1.2em;
    white-space: pre-line;
    overflow: hidden;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    margin: 3px 1px 0 1px;
    padding: 1px;
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
    /* for webkit CSS3 */
    word-break: break-word;
    overflow-wrap: break-word;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* permissions/symlink markers */
.elfinder-cwd-view-icons .elfinder-perms {
    bottom: 4px;
    right: 2px;
}

.elfinder-cwd-view-icons .elfinder-lock {
    top: -3px;
    right: -2px;
}

.elfinder-cwd-view-icons .elfinder-symlink {
    bottom: 6px;
    left: 0px;
}

/* icon/thumbnail */
.elfinder-cwd-icon {
    display: block;
    width: 48px;
    height: 48px;
    margin: 0 auto;
    background-image: url('../img/icons-big.svg');
    background-image: url('../img/icons-big.png') \9;
    background-position: 0 0;
    background-repeat: no-repeat;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}

/* volume icon of root in folder */
.elfinder-navbar-root-local .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-local.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-local td .elfinder-cwd-icon {
    background-image: url("../img/volume_icon_local.svg");
    background-image: url("../img/volume_icon_local.png") \9;
    background-position: 0 0;
    background-size: contain;
}

.elfinder-cwd .elfinder-navbar-root-local.elfinder-droppable-active .elfinder-cwd-icon {
    background-position: 1px -1px;
}

.elfinder-navbar-root-trash .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-trash.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-trash td .elfinder-cwd-icon {
    background-image: url("../img/volume_icon_trash.svg");
    background-image: url("../img/volume_icon_trash.png") \9;
    background-position: 0 0;
    background-size: contain;
}

.elfinder-cwd .elfinder-navbar-root-trash.elfinder-droppable-active .elfinder-cwd-icon {
    background-position: 1px -1px;
}

.elfinder-navbar-root-ftp .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-ftp.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-ftp td .elfinder-cwd-icon {
    background-image: url("../img/volume_icon_ftp.svg");
    background-image: url("../img/volume_icon_ftp.png") \9;
    background-position: 0 0;
    background-size: contain;
}

.elfinder-cwd .elfinder-navbar-root-ftp.elfinder-droppable-active .elfinder-cwd-icon {
    background-position: 1px -1px;
}

.elfinder-navbar-root-sql .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-sql.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-sql td .elfinder-cwd-icon {
    background-image: url("../img/volume_icon_sql.svg");
    background-image: url("../img/volume_icon_sql.png") \9;
    background-position: 0 0;
    background-size: contain;
}

.elfinder-cwd .elfinder-navbar-root-sql.elfinder-droppable-active .elfinder-cwd-icon {
    background-position: 1px -1px;
}

.elfinder-navbar-root-dropbox .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-dropbox.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-dropbox td .elfinder-cwd-icon {
    background-image: url("../img/volume_icon_dropbox.svg");
    background-image: url("../img/volume_icon_dropbox.png") \9;
    background-position: 0 0;
    background-size: contain;
}

.elfinder-cwd .elfinder-navbar-root-dropbox.elfinder-droppable-active .elfinder-cwd-icon {
    background-position: 1px -1px;
}

.elfinder-navbar-root-googledrive .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-googledrive.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-googledrive td .elfinder-cwd-icon {
    background-image: url("../img/volume_icon_googledrive.svg");
    background-image: url("../img/volume_icon_googledrive.png") \9;
    background-position: 0 0;
    background-size: contain;
}

.elfinder-navbar-root-onedrive .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-onedrive.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-onedrive td .elfinder-cwd-icon {
    background-image: url("../img/volume_icon_onedrive.svg");
    background-image: url("../img/volume_icon_onedrive.png") \9;
    background-position: 0 0;
    background-size: contain;
}

.elfinder-navbar-root-box .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-box.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-box td .elfinder-cwd-icon {
    background-image: url("../img/volume_icon_box.svg");
    background-image: url("../img/volume_icon_box.png") \9;
    background-position: 0 0;
    background-size: contain;
}

.elfinder-navbar-root-zip .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-zip.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-zip td .elfinder-cwd-icon {
    background-image: url("../img/volume_icon_zip.svg");
    background-image: url("../img/volume_icon_zip.png") \9;
    background-position: 0 0;
    background-size: contain;
}

.elfinder-cwd .elfinder-navbar-root-googledrive.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-onedrive.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-box.elfinder-droppable-active .elfinder-cwd-icon {
    background-position: 1px -1px;
}

.elfinder-navbar-root-network .elfinder-cwd-icon,
.elfinder-cwd .elfinder-navbar-root-network.elfinder-droppable-active .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-network td .elfinder-cwd-icon {
    background-image: url("../img/volume_icon_network.svg");
    background-image: url("../img/volume_icon_network.png") \9;
    background-position: 0 0;
    background-size: contain;
}

.elfinder-cwd .elfinder-navbar-root-network.elfinder-droppable-active .elfinder-cwd-icon {
    background-position: 1px -1px;
}

/* type badge in "icons" view */
.elfinder-cwd-icon:before {
    content: none;
    position: absolute;
    left: 0px;
    top: 5px;
    min-width: 20px;
    max-width: 84px;
    text-align: center;
    padding: 0px 4px 1px;
    border-radius: 4px;
    font-family: Verdana;
    font-size: 10px;
    line-height: 1.3em;
    -webkit-transform: scale(0.9);
    -moz-transform: scale(0.9);
    -ms-transform: scale(0.9);
    -o-transform: scale(0.9);
    transform: scale(0.9);
}

.elfinder-cwd-view-icons .elfinder-cwd-icon.elfinder-cwd-bgurl:before {
    left: -10px;
}

/* addtional type badge name */
.elfinder-cwd-icon.elfinder-cwd-icon-mp2t:before {
    content: 'ts'
}

.elfinder-cwd-icon.elfinder-cwd-icon-dash-xml:before {
    content: 'dash'
}

.elfinder-cwd-icon.elfinder-cwd-icon-x-mpegurl:before {
    content: 'hls'
}

.elfinder-cwd-icon.elfinder-cwd-icon-x-c:before {
    content: 'c++'
}

/* thumbnail image */
.elfinder-cwd-icon.elfinder-cwd-bgurl {
    background-position: center center;
    background-repeat: no-repeat;
    -moz-background-size: contain;
    background-size: contain;
}

/* thumbnail self */
.elfinder-cwd-icon.elfinder-cwd-bgurl.elfinder-cwd-bgself {
    -moz-background-size: cover;
    background-size: cover;
}

/* thumbnail crop*/
.elfinder-cwd-icon.elfinder-cwd-bgurl {
    -moz-background-size: cover;
    background-size: cover;
}

.elfinder-cwd-icon.elfinder-cwd-bgurl:after {
    content: ' ';
}

.elfinder-cwd-bgurl:after {
    position: relative;
    display: inline-block;
    top: 36px;
    left: -38px;
    width: 48px;
    height: 48px;
    background-image: url('../img/icons-big.svg');
    background-image: url('../img/icons-big.png') \9;
    background-repeat: no-repeat;
    background-size: auto !important;
    opacity: .8;
    filter: Alpha(Opacity=60);
    -webkit-transform-origin: 54px -24px;
    -webkit-transform: scale(.6);
    -moz-transform-origin: 54px -24px;
    -moz-transform: scale(.6);
    -ms-transform-origin: 54px -24px;
    -ms-transform: scale(.6);
    -o-transform-origin: 54px -24px;
    -o-transform: scale(.6);
    transform-origin: 54px -24px;
    transform: scale(.6);
}

/* thumbnail image and draging icon */
.elfinder-cwd-icon.elfinder-cwd-icon-drag {
    width: 48px;
    height: 48px;
}

/* thumbnail image and draging icon overlay none */
.elfinder-cwd-icon.elfinder-cwd-icon-drag:before,
.elfinder-cwd-icon.elfinder-cwd-icon-drag:after,
.elfinder-cwd-icon-image.elfinder-cwd-bgurl:after,
.elfinder-cwd-icon-directory.elfinder-cwd-bgurl:after {
    content: none;
}

/* "opened folder" icon on dragover */
.elfinder-cwd .elfinder-droppable-active .elfinder-cwd-icon {
    background-position: 0 -100px;
}

.elfinder-cwd .elfinder-droppable-active {
    outline: 2px solid #8cafed;
    outline-offset: -2px;
}

/* mimetypes icons */
.elfinder-cwd-icon-directory {
    background-position: 0 -50px;
}

.elfinder-cwd-icon-application:after,
.elfinder-cwd-icon-application {
    background-position: 0 -150px;
}

.elfinder-cwd-icon-text:after,
.elfinder-cwd-icon-text {
    background-position: 0 -1350px;
}

.elfinder-cwd-icon-plain:after,
.elfinder-cwd-icon-plain,
.elfinder-cwd-icon-x-empty:after,
.elfinder-cwd-icon-x-empty {
    background-position: 0 -200px;
}

.elfinder-cwd-icon-image:after,
.elfinder-cwd-icon-vnd-adobe-photoshop:after,
.elfinder-cwd-icon-image,
.elfinder-cwd-icon-vnd-adobe-photoshop {
    background-position: 0 -250px;
}

.elfinder-cwd-icon-postscript:after,
.elfinder-cwd-icon-postscript {
    background-position: 0 -1550px;
}

.elfinder-cwd-icon-audio:after,
.elfinder-cwd-icon-audio {
    background-position: 0 -300px;
}

.elfinder-cwd-icon-video:after,
.elfinder-cwd-icon-video,
.elfinder-cwd-icon-flash-video,
.elfinder-cwd-icon-dash-xml,
.elfinder-cwd-icon-vnd-apple-mpegurl,
.elfinder-cwd-icon-x-mpegurl {
    background-position: 0 -350px;
}

.elfinder-cwd-icon-rtf:after,
.elfinder-cwd-icon-rtfd:after,
.elfinder-cwd-icon-rtf,
.elfinder-cwd-icon-rtfd {
    background-position: 0 -400px;
}

.elfinder-cwd-icon-pdf:after,
.elfinder-cwd-icon-pdf {
    background-position: 0 -450px;
}

.elfinder-cwd-icon-ms-excel,
.elfinder-cwd-icon-ms-excel:after,
.elfinder-cwd-icon-vnd-ms-excel,
.elfinder-cwd-icon-vnd-ms-excel-addin-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-excel-addin-macroEnabled-12:after,
.elfinder-cwd-icon-vnd-ms-excel-sheet-binary-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-excel-sheet-binary-macroEnabled-12:after,
.elfinder-cwd-icon-vnd-ms-excel-sheet-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-excel-sheet-macroEnabled-12:after,
.elfinder-cwd-icon-vnd-ms-excel-template-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-excel-template-macroEnabled-12:after,
.elfinder-cwd-icon-vnd-ms-excel:after,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-spreadsheetml-sheet,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-spreadsheetml-sheet:after,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-spreadsheetml-template,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-spreadsheetml-template:after {
    background-position: 0 -1450px
}

.elfinder-cwd-icon-vnd-oasis-opendocument-spreadsheet,
.elfinder-cwd-icon-vnd-oasis-opendocument-spreadsheet-template,
.elfinder-cwd-icon-vnd-oasis-opendocument-spreadsheet-template:after,
.elfinder-cwd-icon-vnd-oasis-opendocument-spreadsheet:after {
    background-position: 0 -1700px
}

.elfinder-cwd-icon-vnd-ms-powerpoint,
.elfinder-cwd-icon-vnd-ms-powerpoint-addin-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-powerpoint-addin-macroEnabled-12:after,
.elfinder-cwd-icon-vnd-ms-powerpoint-presentation-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-powerpoint-presentation-macroEnabled-12:after,
.elfinder-cwd-icon-vnd-ms-powerpoint-slide-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-powerpoint-slide-macroEnabled-12:after,
.elfinder-cwd-icon-vnd-ms-powerpoint-slideshow-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-powerpoint-slideshow-macroEnabled-12:after,
.elfinder-cwd-icon-vnd-ms-powerpoint-template-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-powerpoint-template-macroEnabled-12:after,
.elfinder-cwd-icon-vnd-ms-powerpoint:after,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-presentation,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-presentation:after,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-slide,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-slide:after,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-slideshow,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-slideshow:after,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-template,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-template:after {
    background-position: 0 -1400px
}

.elfinder-cwd-icon-vnd-oasis-opendocument-presentation,
.elfinder-cwd-icon-vnd-oasis-opendocument-presentation-template,
.elfinder-cwd-icon-vnd-oasis-opendocument-presentation-template:after,
.elfinder-cwd-icon-vnd-oasis-opendocument-presentation:after {
    background-position: 0 -1650px
}

.elfinder-cwd-icon-msword,
.elfinder-cwd-icon-msword:after,
.elfinder-cwd-icon-vnd-ms-word,
.elfinder-cwd-icon-vnd-ms-word-document-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-word-document-macroEnabled-12:after,
.elfinder-cwd-icon-vnd-ms-word-template-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-word-template-macroEnabled-12:after,
.elfinder-cwd-icon-vnd-ms-word:after,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-wordprocessingml-document,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-wordprocessingml-document:after,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-wordprocessingml-template,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-wordprocessingml-template:after {
    background-position: 0 -1500px
}

.elfinder-cwd-icon-vnd-oasis-opendocument-text,
.elfinder-cwd-icon-vnd-oasis-opendocument-text-master,
.elfinder-cwd-icon-vnd-oasis-opendocument-text-master:after,
.elfinder-cwd-icon-vnd-oasis-opendocument-text-template,
.elfinder-cwd-icon-vnd-oasis-opendocument-text-template:after,
.elfinder-cwd-icon-vnd-oasis-opendocument-text-web,
.elfinder-cwd-icon-vnd-oasis-opendocument-text-web:after,
.elfinder-cwd-icon-vnd-oasis-opendocument-text:after {
    background-position: 0 -1750px
}

.elfinder-cwd-icon-vnd-ms-office,
.elfinder-cwd-icon-vnd-ms-office:after {
    background-position: 0 -500px
}

.elfinder-cwd-icon-vnd-oasis-opendocument-chart,
.elfinder-cwd-icon-vnd-oasis-opendocument-chart:after,
.elfinder-cwd-icon-vnd-oasis-opendocument-database,
.elfinder-cwd-icon-vnd-oasis-opendocument-database:after,
.elfinder-cwd-icon-vnd-oasis-opendocument-formula,
.elfinder-cwd-icon-vnd-oasis-opendocument-formula:after,
.elfinder-cwd-icon-vnd-oasis-opendocument-graphics,
.elfinder-cwd-icon-vnd-oasis-opendocument-graphics-template,
.elfinder-cwd-icon-vnd-oasis-opendocument-graphics-template:after,
.elfinder-cwd-icon-vnd-oasis-opendocument-graphics:after,
.elfinder-cwd-icon-vnd-oasis-opendocument-image,
.elfinder-cwd-icon-vnd-oasis-opendocument-image:after,
.elfinder-cwd-icon-vnd-openofficeorg-extension,
.elfinder-cwd-icon-vnd-openofficeorg-extension:after {
    background-position: 0 -1600px
}

.elfinder-cwd-icon-html:after,
.elfinder-cwd-icon-html {
    background-position: 0 -550px;
}

.elfinder-cwd-icon-css:after,
.elfinder-cwd-icon-css {
    background-position: 0 -600px;
}

.elfinder-cwd-icon-javascript:after,
.elfinder-cwd-icon-x-javascript:after,
.elfinder-cwd-icon-javascript,
.elfinder-cwd-icon-x-javascript {
    background-position: 0 -650px;
}

.elfinder-cwd-icon-x-perl:after,
.elfinder-cwd-icon-x-perl {
    background-position: 0 -700px;
}

.elfinder-cwd-icon-x-python:after,
.elfinder-cwd-icon-x-python {
    background-position: 0 -750px;
}

.elfinder-cwd-icon-x-ruby:after,
.elfinder-cwd-icon-x-ruby {
    background-position: 0 -800px;
}

.elfinder-cwd-icon-x-sh:after,
.elfinder-cwd-icon-x-shellscript:after,
.elfinder-cwd-icon-x-sh,
.elfinder-cwd-icon-x-shellscript {
    background-position: 0 -850px;
}

.elfinder-cwd-icon-x-c:after,
.elfinder-cwd-icon-x-csrc:after,
.elfinder-cwd-icon-x-chdr:after,
.elfinder-cwd-icon-x-c--:after,
.elfinder-cwd-icon-x-c--src:after,
.elfinder-cwd-icon-x-c--hdr:after,
.elfinder-cwd-icon-x-java:after,
.elfinder-cwd-icon-x-java-source:after,
.elfinder-cwd-icon-x-c,
.elfinder-cwd-icon-x-csrc,
.elfinder-cwd-icon-x-chdr,
.elfinder-cwd-icon-x-c--,
.elfinder-cwd-icon-x-c--src,
.elfinder-cwd-icon-x-c--hdr,
.elfinder-cwd-icon-x-java,
.elfinder-cwd-icon-x-java-source {
    background-position: 0 -900px;
}

.elfinder-cwd-icon-x-php:after,
.elfinder-cwd-icon-x-php {
    background-position: 0 -950px;
}

.elfinder-cwd-icon-xml:after,
.elfinder-cwd-icon-xml {
    background-position: 0 -1000px;
}

.elfinder-cwd-icon-zip:after,
.elfinder-cwd-icon-x-zip:after,
.elfinder-cwd-icon-x-xz:after,
.elfinder-cwd-icon-x-7z-compressed:after,
.elfinder-cwd-icon-zip,
.elfinder-cwd-icon-x-zip,
.elfinder-cwd-icon-x-xz,
.elfinder-cwd-icon-x-7z-compressed {
    background-position: 0 -1050px;
}

.elfinder-cwd-icon-x-gzip:after,
.elfinder-cwd-icon-x-tar:after,
.elfinder-cwd-icon-x-gzip,
.elfinder-cwd-icon-x-tar {
    background-position: 0 -1100px;
}

.elfinder-cwd-icon-x-bzip:after,
.elfinder-cwd-icon-x-bzip2:after,
.elfinder-cwd-icon-x-bzip,
.elfinder-cwd-icon-x-bzip2 {
    background-position: 0 -1150px;
}

.elfinder-cwd-icon-x-rar:after,
.elfinder-cwd-icon-x-rar-compressed:after,
.elfinder-cwd-icon-x-rar,
.elfinder-cwd-icon-x-rar-compressed {
    background-position: 0 -1200px;
}

.elfinder-cwd-icon-x-shockwave-flash:after,
.elfinder-cwd-icon-x-shockwave-flash {
    background-position: 0 -1250px;
}

.elfinder-cwd-icon-group {
    background-position: 0 -1300px;
}

/* textfield inside icon */
.elfinder-cwd-filename input {
    width: 100%;
    border: none;
    margin: 0;
    padding: 0;
}

.elfinder-cwd-view-icons input {
    text-align: center;
}

.elfinder-cwd-view-icons textarea {
    width: 100%;
    border: 0px solid;
    margin: 0;
    padding: 0;
    text-align: center;
    overflow: hidden;
    resize: none;
}

.elfinder-cwd-view-icons {
    text-align: center;
}

/************************************  LIST VIEW ************************************/

/*.elfinder-cwd-view-list { padding:0 0 4px 0; }*/

.elfinder-cwd-wrapper.elfinder-cwd-fixheader .elfinder-cwd::after {
    display: none;
}

.elfinder-cwd table {
    width: 100%;
    border-collapse: separate;
    border: 0 solid;
    margin: 0 0 10px 0;
    border-spacing: 0;
    box-sizing: padding-box;
    padding: 2px;
    position: relative;
}

.elfinder-cwd table td {
    /* fix conflict with Bootstrap CSS */
    box-sizing: content-box;
}

.elfinder-cwd-wrapper-list.elfinder-cwd-fixheader {
    position: absolute;
    overflow: hidden;
}

.elfinder-cwd-wrapper-list.elfinder-cwd-fixheader:before {
    content: '';
    position: absolute;
    width: 100%;
    top: 0;
    height: 3px;
    background-color: white;
}

.elfinder-droppable-active + .elfinder-cwd-wrapper-list.elfinder-cwd-fixheader:before {
    background-color: #8cafed;
}

.elfinder .elfinder-workzone div.elfinder-cwd-fixheader table {
    table-layout: fixed;
}

.elfinder .elfinder-cwd table tbody.elfinder-cwd-fixheader {
    position: relative;
}

.elfinder-ltr .elfinder-cwd thead .elfinder-cwd-selectall {
    text-align: left;
    right: auto;
    left: 0px;
    padding-top: 3px;
}

.elfinder-rtl .elfinder-cwd thead .elfinder-cwd-selectall {
    text-align: right;
    right: 0px;
    left: auto;
    padding-top: 3px;
}

.elfinder-touch .elfinder-cwd thead .elfinder-cwd-selectall {
    padding-top: 4px;
}

.elfinder .elfinder-cwd table thead tr {
    border-left: 0 solid;
    border-top: 0 solid;
    border-right: 0 solid;
}

.elfinder .elfinder-cwd table thead td {
    padding: 4px 14px;
}

.elfinder-ltr .elfinder-cwd.elfinder-has-checkbox table thead td:first-child {
    padding: 4px 14px 4px 22px;
}

.elfinder-rtl .elfinder-cwd.elfinder-has-checkbox table thead td:first-child {
    padding: 4px 22px 4px 14px;
}

.elfinder-touch .elfinder-cwd table thead td,
.elfinder-touch .elfinder-cwd.elfinder-has-checkbox table thead td:first-child {
    padding-top: 8px;
    padding-bottom: 8px;
}

.elfinder .elfinder-cwd table thead td.ui-state-active {
    background: #ebf1f6;
    background: -moz-linear-gradient(top, #ebf1f6 0%, #abd3ee 50%, #89c3eb 51%, #d5ebfb 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ebf1f6), color-stop(50%, #abd3ee), color-stop(51%, #89c3eb), color-stop(100%, #d5ebfb));
    background: -webkit-linear-gradient(top, #ebf1f6 0%, #abd3ee 50%, #89c3eb 51%, #d5ebfb 100%);
    background: -o-linear-gradient(top, #ebf1f6 0%, #abd3ee 50%, #89c3eb 51%, #d5ebfb 100%);
    background: -ms-linear-gradient(top, #ebf1f6 0%, #abd3ee 50%, #89c3eb 51%, #d5ebfb 100%);
    background: linear-gradient(to bottom, #ebf1f6 0%, #abd3ee 50%, #89c3eb 51%, #d5ebfb 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ebf1f6', endColorstr='#d5ebfb', GradientType=0);
}

.elfinder .elfinder-cwd table td {
    padding: 0 12px;
    white-space: pre;
    overflow: hidden;
    text-align: right;
    cursor: default;
    border: 0 solid;
}

.elfinder .elfinder-cwd table tbody td:first-child {
    position: relative
}

.elfinder .elfinder-cwd table td div {
    box-sizing: content-box;
}

tr.elfinder-cwd-file td .elfinder-cwd-select {
    padding-top: 3px;
}

.elfinder-mobile tr.elfinder-cwd-file td .elfinder-cwd-select {
    width: 40px;
}

.elfinder-touch tr.elfinder-cwd-file td .elfinder-cwd-select {
    padding-top: 10px;
}

.elfinder-touch .elfinder-cwd tr td {
    padding: 10px 12px;
}

.elfinder-touch .elfinder-cwd tr.elfinder-cwd-file td {
    padding: 13px 12px;
}

.elfinder-ltr .elfinder-cwd table td {
    text-align: right;
}

.elfinder-ltr .elfinder-cwd table td:first-child {
    text-align: left;
}

.elfinder-rtl .elfinder-cwd table td {
    text-align: left;
}

.elfinder-rtl .elfinder-cwd table td:first-child {
    text-align: right;
}

.elfinder-odd-row {
    background: #eee;
}

/* filename container */
.elfinder-cwd-view-list .elfinder-cwd-file-wrapper {
    width: 97%;
    position: relative;
}

/* filename container in ltr/rtl enviroment */
.elfinder-ltr .elfinder-cwd-view-list.elfinder-has-checkbox .elfinder-cwd-file-wrapper {
    margin-left: 8px;
}

.elfinder-rtl .elfinder-cwd-view-list.elfinder-has-checkbox .elfinder-cwd-file-wrapper {
    margin-right: 8px;
}

.elfinder-cwd-view-list .elfinder-cwd-filename {
    padding-top: 4px;
    padding-bottom: 4px;
    display: inline-block;
}

.elfinder-ltr .elfinder-cwd-view-list .elfinder-cwd-filename {
    padding-left: 23px;
}

.elfinder-rtl .elfinder-cwd-view-list .elfinder-cwd-filename {
    padding-right: 23px;
}

/* premissions/symlink marker */
.elfinder-cwd-view-list .elfinder-perms,
.elfinder-cwd-view-list .elfinder-lock,
.elfinder-cwd-view-list .elfinder-symlink {
    margin-top: -6px;
    opacity: .6;
    filter: Alpha(Opacity=60);
}

.elfinder-cwd-view-list .elfinder-perms {
    bottom: -4px;
}

.elfinder-cwd-view-list .elfinder-lock {
    top: 0px;
}

.elfinder-cwd-view-list .elfinder-symlink {
    bottom: -4px;
}

/* markers in ltr/rtl enviroment */
.elfinder-ltr .elfinder-cwd-view-list .elfinder-perms {
    left: 8px;
}

.elfinder-rtl .elfinder-cwd-view-list .elfinder-perms {
    right: -8px;
}

.elfinder-ltr .elfinder-cwd-view-list .elfinder-lock {
    left: 10px;
}

.elfinder-rtl .elfinder-cwd-view-list .elfinder-lock {
    right: -10px;
}

.elfinder-ltr .elfinder-cwd-view-list .elfinder-symlink {
    left: -7px;
}

.elfinder-rtl .elfinder-cwd-view-list .elfinder-symlink {
    right: 7px;
}

/* file icon */
.elfinder-cwd-view-list td .elfinder-cwd-icon {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 50%;
    margin-top: -8px;
    background-image: url(../img/icons-small.png);
}

/* icon in ltr/rtl enviroment */
.elfinder-ltr .elfinder-cwd-view-list .elfinder-cwd-icon {
    left: 0;
}

.elfinder-rtl .elfinder-cwd-view-list .elfinder-cwd-icon {
    right: 0;
}

/* type badge, thumbnail image overlay */
.elfinder-cwd-view-list .elfinder-cwd-icon:before,
.elfinder-cwd-view-list .elfinder-cwd-icon:after {
    content: none;
}

/* table header resize handle */
.elfinder-cwd-view-list thead td .ui-resizable-handle {
    height: 100%;
    top: 6px;
}

.elfinder-touch .elfinder-cwd-view-list thead td .ui-resizable-handle {
    top: -4px;
    margin: 10px;
}

.elfinder-cwd-view-list thead td .ui-resizable-e {
    right: -7px;
}

.elfinder-cwd-view-list thead td .ui-resizable-w {
    left: -7px;
}

.elfinder-touch .elfinder-cwd-view-list thead td .ui-resizable-e {
    right: -16px;
}

.elfinder-touch .elfinder-cwd-view-list thead td .ui-resizable-w {
    left: -16px;
}

/* empty message */
.elfinder-cwd-wrapper-empty .elfinder-cwd-view-list.elfinder-cwd:after {
    margin-top: 0;
}

/* overlay message board */
.elfinder-cwd-message-board {
    position: absolute;
    position: -webkit-sticky;
    position: sticky;
    width: 100%;
    height: calc(100% - 0.01px); /* for Firefox scroll problem */
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
    pointer-events: none;
    background-color: transparent;
}

/* overlay message board for trash */
.elfinder-cwd-wrapper-trash .elfinder-cwd-message-board {
    background-image: url(../img/trashmesh.png);
}

.elfinder-cwd-message-board .elfinder-cwd-trash {
    position: absolute;
    bottom: 0;
    font-size: 30px;
    width: 100%;
    text-align: right;
    display: none;
}

.elfinder-rtl .elfinder-cwd-message-board .elfinder-cwd-trash {
    text-align: left;
}

.elfinder-mobile .elfinder-cwd-message-board .elfinder-cwd-trash {
    font-size: 20px;
}

.elfinder-cwd-wrapper-trash .elfinder-cwd-message-board .elfinder-cwd-trash {
    display: block;
    opacity: .3;
}

/* overlay message board for expires */
.elfinder-cwd-message-board .elfinder-cwd-expires {
    position: absolute;
    bottom: 0;
    font-size: 24px;
    width: 100%;
    text-align: right;
    opacity: .25;
}

.elfinder-rtl .elfinder-cwd-message-board .elfinder-cwd-expires {
    text-align: left;
}

.elfinder-mobile .elfinder-cwd-message-board .elfinder-cwd-expires {
    font-size: 20px;
}

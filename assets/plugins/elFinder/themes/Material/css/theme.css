/*!
 * elFinder-Material-Theme (Default) v2.1.7 (https://github.com/RobiNN1/elFinder-Material-Theme)
 * Copyright 2016-2018 <PERSON><PERSON><PERSON> {RobiNN}
 * Licensed under MIT (https://github.com/RobiNN1/elFinder-Material-Theme/blob/master/LICENSE)
 */
@import url("https://fonts.googleapis.com/css?family=Noto+Sans:400,400i,700,700i&subset=cyrillic,cyrillic-ext,devanagari,greek,greek-ext,latin-ext,vietnamese");
.elfinder {
  color: #546e7a;
  font-family: "Noto Sans", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.elfinder.ui-widget.ui-widget-content {
  font-family: "Noto Sans", sans-serif;
  -webkit-box-shadow: 0 1px 8px rgba(0, 0, 0, 0.6);
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.6);
  -webkit-border-radius: 0;
  border-radius: 0;
  border: 0;
}
.elfinder * {
  outline: 0 !important;
}
/**
 * Input & Select
 */
input.elfinder-tabstop,
input.elfinder-tabstop.ui-state-hover,
select.elfinder-tabstop,
select.elfinder-tabstop.ui-state-hover {
  padding: 5px;
  color: #666;
  background: #fff;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  font-weight: normal;
  border-color: #888;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}
.elfinder input[type="checkbox"] {
  position: relative;
}
.elfinder input[type="checkbox"]:after,
.elfinder input[type="checkbox"]:focus:after {
  content: "";
  display: block;
  width: 12px;
  height: 12px;
  border: 1px solid #707070;
  background-color: #fff;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.elfinder input[type="checkbox"]:checked:before {
  content: "";
  position: absolute;
  top: -3px;
  left: 6px;
  display: table;
  width: 4px;
  height: 12px;
  border: 2px solid #707070;
  border-top-width: 0;
  border-left-width: 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
/**
 * Loading
 */
.elfinder-info-spinner,
.elfinder-navbar-spinner,
.elfinder-button-icon-spinner {
  background: url("../images/loading.svg") center center no-repeat !important;
  width: 16px;
  height: 16px;
}
/**
 * Progress Bar
 */
@-webkit-keyframes progress-animation {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}
@keyframes progress-animation {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}
.elfinder-notify-progressbar {
  border: 0;
}
.elfinder-notify-progress,
.elfinder-notify-progressbar {
  -webkit-border-radius: 0;
  border-radius: 0;
}
.elfinder-notify-progress,
.elfinder-resize-spinner {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  -webkit-background-size: 1rem 1rem;
  background-size: 1rem 1rem;
  -webkit-animation: progress-animation 1s linear infinite;
  animation: progress-animation 1s linear infinite;
  background-color: #0275d8;
  height: 1rem;
}
/**
 * Quick Look
 */
.elfinder-quicklook {
  background: #232323;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.elfinder-quicklook-titlebar {
  background: inherit;
}
.elfinder-quicklook-fullscreen .elfinder-quicklook-navbar {
  border: inherit;
  opacity: inherit;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  background: rgba(66, 66, 66, 0.73);
}
.elfinder .elfinder-navdock {
  border: 0;
}
.std42-dialog .ui-dialog-titlebar .ui-dialog-titlebar-close:hover .ui-icon,
.elfinder-mobile .std42-dialog .ui-dialog-titlebar .ui-dialog-titlebar-close .ui-icon,
.elfinder-quicklook-titlebar-icon .ui-icon.elfinder-icon-close:hover,
.elfinder-mobile .elfinder-quicklook-titlebar-icon .ui-icon.elfinder-icon-close,
.std42-dialog .ui-dialog-titlebar .elfinder-titlebar-minimize:hover .ui-icon,
.elfinder-mobile .std42-dialog .ui-dialog-titlebar .elfinder-titlebar-minimize .ui-icon,
.elfinder-quicklook-titlebar-icon .ui-icon.elfinder-icon-minimize:hover,
.elfinder-mobile .elfinder-quicklook-titlebar-icon .ui-icon.elfinder-icon-minimize,
.std42-dialog .ui-dialog-titlebar .elfinder-titlebar-full:hover .ui-icon,
.elfinder-mobile .std42-dialog .ui-dialog-titlebar .elfinder-titlebar-full .ui-icon,
.elfinder-quicklook-titlebar-icon .ui-icon.elfinder-icon-full:hover,
.elfinder-mobile .elfinder-quicklook-titlebar-icon .ui-icon.elfinder-icon-full {
  background-image: none;
}
/**
 * Toast Notification
 */
.elfinder .elfinder-toast > div {
  background-color: #323232 !important;
  color: #d6d6d6;
  -webkit-box-shadow: none;
  box-shadow: none;
  opacity: inherit;
  padding: 10px 60px;
}
.elfinder .elfinder-toast > div button.ui-button {
  color: #fff;
}
.elfinder .elfinder-toast > .toast-info button.ui-button {
  background-color: #3498db;
}
.elfinder .elfinder-toast > .toast-error button.ui-button {
  background-color: #f44336;
}
.elfinder .elfinder-toast > .toast-success button.ui-button {
  background-color: #4caf50;
}
.elfinder .elfinder-toast > .toast-warning button.ui-button {
  background-color: #ff9800;
}
.elfinder-toast-msg {
  font-family: "Noto Sans", sans-serif;
  font-size: 17px;
}
/**
 * For Ace Editor
 */
#ace_settingsmenu {
  font-family: "Noto Sans", sans-serif;
  -webkit-box-shadow: 0 1px 30px rgba(0, 0, 0, 0.6) !important;
  box-shadow: 0 1px 30px rgba(0, 0, 0, 0.6) !important;
  background-color: #1d2736 !important;
  color: #e6e6e6 !important;
}
#ace_settingsmenu,
#kbshortcutmenu {
  padding: 0;
}
.ace_optionsMenuEntry {
  padding: 5px 10px;
}
.ace_optionsMenuEntry:hover {
  background-color: #111721;
}
.ace_optionsMenuEntry label {
  font-size: 13px;
}
#ace_settingsmenu input[type="text"],
#ace_settingsmenu select {
  margin: 1px 2px 2px;
  padding: 2px 5px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  border: 0;
  background: rgba(9, 53, 121, 0.75);
  color: white;
}
/**
 * Icons
 */
@font-face {
  font-family: material;
  src: url("../icons/material.eot?91804974");
  src: url("../icons/material.eot?91804974#iefix") format("embedded-opentype"), url("../icons/material.woff2?91804974") format("woff2"), url("../icons/material.woff?91804974") format("woff"), url("../icons/material.ttf?91804974") format("truetype"), url("../icons/material.svg?91804974#material") format("svg");
  font-weight: normal;
  font-style: normal;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  @font-face {
    font-family: material;
    src: url("../icons/material.svg?91804974#material") format("svg");
  }
}
.ui-icon,
.elfinder-button-icon,
.ui-widget-header .ui-icon,
.ui-widget-content .ui-icon {
  font: normal normal normal 14px/1 material;
  background-image: inherit;
  text-indent: inherit;
}
.ui-button-icon-only .ui-icon {
  font: normal normal normal 14px/1 material;
  background-image: inherit !important;
  text-indent: 0;
  font-size: 16px;
}
.elfinder-button-icon {
  background: inherit;
}
.elfinder-button-icon-home:before {
  content: '\e800';
}
.elfinder-button-icon-back:before {
  content: '\e801';
}
.elfinder-button-icon-forward:before {
  content: '\e802';
}
.elfinder-button-icon-up:before {
  content: '\e803';
}
.elfinder-button-icon-dir:before {
  content: '\e804';
}
.elfinder-button-icon-opendir:before {
  content: '\e805';
}
.elfinder-button-icon-reload:before {
  content: '\e806';
}
.elfinder-button-icon-open:before {
  content: '\e807';
}
.elfinder-button-icon-mkdir:before {
  content: '\e808';
}
.elfinder-button-icon-mkfile:before {
  content: '\e809';
}
.elfinder-button-icon-rm:before {
  content: '\e80a';
}
.elfinder-button-icon-trash:before {
  content: '\e80b';
}
.elfinder-button-icon-restore:before {
  content: '\e80c';
}
.elfinder-button-icon-copy:before {
  content: '\e80d';
}
.elfinder-button-icon-cut:before {
  content: '\e80e';
}
.elfinder-button-icon-paste:before {
  content: '\e80f';
}
.elfinder-button-icon-getfile:before {
  content: '\e810';
}
.elfinder-button-icon-duplicate:before {
  content: '\e811';
}
.elfinder-button-icon-rename:before {
  content: '\e812';
}
.elfinder-button-icon-edit:before {
  content: '\e813';
}
.elfinder-button-icon-quicklook:before {
  content: '\e814';
}
.elfinder-button-icon-upload:before {
  content: '\e815';
}
.elfinder-button-icon-download:before {
  content: '\e816';
}
.elfinder-button-icon-info:before {
  content: '\e817';
}
.elfinder-button-icon-extract:before {
  content: '\e818';
}
.elfinder-button-icon-archive:before {
  content: '\e819';
}
.elfinder-button-icon-view:before {
  content: '\e81a';
}
.elfinder-button-icon-view-list:before {
  content: '\e81b';
}
.elfinder-button-icon-help:before {
  content: '\e81c';
}
.elfinder-button-icon-resize:before {
  content: '\e81d';
}
.elfinder-button-icon-link:before {
  content: '\e81e';
}
.elfinder-button-icon-search:before {
  content: '\e81f';
}
.elfinder-button-icon-sort:before {
  content: '\e820';
}
.elfinder-button-icon-rotate-r:before {
  content: '\e821';
}
.elfinder-button-icon-rotate-l:before {
  content: '\e822';
}
.elfinder-button-icon-netmount:before {
  content: '\e823';
}
.elfinder-button-icon-netunmount:before {
  content: '\e824';
}
.elfinder-button-icon-places:before {
  content: '\e825';
}
.elfinder-button-icon-chmod:before {
  content: '\e826';
}
.elfinder-button-icon-accept:before {
  content: '\e827';
}
.elfinder-button-icon-menu:before {
  content: '\e828';
}
.elfinder-button-icon-colwidth:before {
  content: '\e829';
}
.elfinder-button-icon-fullscreen:before {
  content: '\e82a';
}
.elfinder-button-icon-unfullscreen:before {
  content: '\e82b';
}
.elfinder-button-icon-empty:before {
  content: '\e82c';
}
.elfinder-button-icon-undo:before {
  content: '\e82d';
}
.elfinder-button-icon-redo:before {
  content: '\e82e';
}
.elfinder-button-icon-preference:before {
  content: '\e82f';
}
.elfinder-button-icon-mkdirin:before {
  content: '\e830';
}
.elfinder-button-icon-selectall:before {
  content: '\e831';
}
.elfinder-button-icon-selectnone:before {
  content: '\e832';
}
.elfinder-button-icon-selectinvert:before {
  content: '\e833';
}
.elfinder-button-icon-logout:before {
  content: '\e85a';
}
.elfinder-button-icon-opennew:before {
  content: '\e85b';
}
.elfinder-button-icon-hide:before {
  content: '\e85d';
}
.elfinder-button-search .ui-icon.ui-icon-search {
  font-size: 17px;
}
.elfinder-button-search .ui-icon:hover {
  opacity: 1;
}
.elfinder-navbar-icon {
  font: normal normal normal 16px/1 material;
  background-image: inherit !important;
}
.elfinder-navbar-icon:before {
  content: '\e804';
}
.elfinder-droppable-active .elfinder-navbar-icon:before,
.ui-state-active .elfinder-navbar-icon:before,
.ui-state-hover .elfinder-navbar-icon:before {
  content: '\e805';
}
.elfinder-navbar-root-local .elfinder-navbar-icon:before {
  content: '\e83d';
}
.elfinder-navbar-root-ftp .elfinder-navbar-icon:before {
  content: '\e823';
}
.elfinder-navbar-root-sql .elfinder-navbar-icon:before {
  content: '\e83e';
}
.elfinder-navbar-root-dropbox .elfinder-navbar-icon:before {
  content: '\e83f';
}
.elfinder-navbar-root-googledrive .elfinder-navbar-icon:before {
  content: '\e840';
}
.elfinder-navbar-root-onedrive .elfinder-navbar-icon:before {
  content: '\e841';
}
.elfinder-navbar-root-box .elfinder-navbar-icon:before {
  content: '\e842';
}
.elfinder-navbar-root-trash .elfinder-navbar-icon:before {
  content: '\e80b';
}
.elfinder-navbar-root-zip .elfinder-navbar-icon:before {
  content: '\e85c';
}
.elfinder-navbar-root-network .elfinder-navbar-icon:before {
  content: '\e823';
}
.elfinder-places .elfinder-navbar-root .elfinder-navbar-icon:before {
  content: '\e825';
}
.elfinder-navbar-arrow {
  background-image: inherit !important;
  font: normal normal normal 14px/1 material;
  font-size: 10px;
  padding-top: 3px;
  padding-left: 2px;
  color: #a9a9a9;
}
.ui-state-active .elfinder-navbar-arrow {
  color: #fff;
}
.elfinder-ltr .elfinder-navbar-collapsed .elfinder-navbar-arrow:before {
  content: '\e857';
}
.elfinder-rtl .elfinder-navbar-collapsed .elfinder-navbar-arrow:before {
  content: '\e858';
}
.elfinder-ltr .elfinder-navbar-expanded .elfinder-navbar-arrow:before,
.elfinder-rtl .elfinder-navbar-expanded .elfinder-navbar-arrow:before {
  content: '\e851';
}
div.elfinder-cwd-wrapper-list tr.ui-state-default td span.ui-icon {
  font-size: 8px;
  margin-top: 5px;
  margin-right: 5px;
}
div.elfinder-cwd-wrapper-list .ui-icon-grip-dotted-vertical {
  margin: 2px;
}
.elfinder-cwd-view-list .elfinder-navbar-root-local td .elfinder-cwd-icon,
.elfinder-navbar-root-local .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-ftp td .elfinder-cwd-icon,
.elfinder-navbar-root-ftp .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-sql td .elfinder-cwd-icon,
.elfinder-navbar-root-sql .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-dropbox td .elfinder-cwd-icon,
.elfinder-navbar-root-dropbox .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-googledrive td .elfinder-cwd-icon,
.elfinder-navbar-root-googledrive .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-onedrive td .elfinder-cwd-icon,
.elfinder-navbar-root-onedrive .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-box td .elfinder-cwd-icon,
.elfinder-navbar-root-box .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-trash td .elfinder-cwd-icon,
.elfinder-navbar-root-trash .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-zip td .elfinder-cwd-icon,
.elfinder-navbar-root-zip .elfinder-cwd-icon,
.elfinder-cwd-view-list .elfinder-navbar-root-network td .elfinder-cwd-icon,
.elfinder-navbar-root-network .elfinder-cwd-icon {
  background-image: inherit;
}
.elfinder-cwd-view-list .elfinder-navbar-root-local td .elfinder-cwd-icon:before,
.elfinder-navbar-root-local .elfinder-cwd-icon:before,
.elfinder-cwd-view-list .elfinder-navbar-root-ftp td .elfinder-cwd-icon:before,
.elfinder-navbar-root-ftp .elfinder-cwd-icon:before,
.elfinder-cwd-view-list .elfinder-navbar-root-sql td .elfinder-cwd-icon:before,
.elfinder-navbar-root-sql .elfinder-cwd-icon:before,
.elfinder-cwd-view-list .elfinder-navbar-root-dropbox td .elfinder-cwd-icon:before,
.elfinder-navbar-root-dropbox .elfinder-cwd-icon:before,
.elfinder-cwd-view-list .elfinder-navbar-root-googledrive td .elfinder-cwd-icon:before,
.elfinder-navbar-root-googledrive .elfinder-cwd-icon:before,
.elfinder-cwd-view-list .elfinder-navbar-root-onedrive td .elfinder-cwd-icon:before,
.elfinder-navbar-root-onedrive .elfinder-cwd-icon:before,
.elfinder-cwd-view-list .elfinder-navbar-root-box td .elfinder-cwd-icon:before,
.elfinder-navbar-root-box .elfinder-cwd-icon:before,
.elfinder-cwd-view-list .elfinder-navbar-root-trash td .elfinder-cwd-icon:before,
.elfinder-navbar-root-trash .elfinder-cwd-icon:before,
.elfinder-cwd-view-list .elfinder-navbar-root-zip td .elfinder-cwd-icon:before,
.elfinder-navbar-root-zip .elfinder-cwd-icon:before,
.elfinder-cwd-view-list .elfinder-navbar-root-network td .elfinder-cwd-icon:before,
.elfinder-navbar-root-network .elfinder-cwd-icon:before {
  font-family: material;
  background-color: transparent;
  color: #525252;
  font-size: 55px;
  position: relative;
  top: -10px !important;
  padding: 0;
  display: contents !important;
}
.elfinder-cwd-view-list .elfinder-navbar-root-local td .elfinder-cwd-icon:before,
.elfinder-navbar-root-local .elfinder-cwd-icon:before {
  content: '\e83d';
}
.elfinder-cwd-view-list .elfinder-navbar-root-ftp td .elfinder-cwd-icon:before,
.elfinder-navbar-root-ftp .elfinder-cwd-icon:before {
  content: '\e823';
}
.elfinder-cwd-view-list .elfinder-navbar-root-sql td .elfinder-cwd-icon:before,
.elfinder-navbar-root-sql .elfinder-cwd-icon:before {
  content: '\e83e';
}
.elfinder-cwd-view-list .elfinder-navbar-roor-dropbox td .elfinder-cwd-icon:before,
.elfinder-navbar-roor-dropbox .elfinder-cwd-icon:before {
  content: '\e83f';
}
.elfinder-cwd-view-list .elfinder-navbar-roor-googledrive td .elfinder-cwd-icon:before,
.elfinder-navbar-roor-googledrive .elfinder-cwd-icon:before {
  content: '\e840';
}
.elfinder-cwd-view-list .elfinder-navbar-roor-onedrive td .elfinder-cwd-icon:before,
.elfinder-navbar-roor-onedrive .elfinder-cwd-icon:before {
  content: '\e841';
}
.elfinder-cwd-view-list .elfinder-navbar-roor-box td .elfinder-cwd-icon:before,
.elfinder-navbar-roor-box .elfinder-cwd-icon:before {
  content: '\e842';
}
.elfinder-cwd-view-list .elfinder-navbar-root-trash td .elfinder-cwd-icon:before,
.elfinder-navbar-root-trash .elfinder-cwd-icon:before {
  content: '\e80b';
}
.elfinder-cwd-view-list .elfinder-navbar-root-zip td .elfinder-cwd-icon:before,
.elfinder-navbar-root-zip .elfinder-cwd-icon:before {
  content: '\e85c';
}
.elfinder-cwd-view-list .elfinder-navbar-root-network td .elfinder-cwd-icon:before,
.elfinder-navbar-root-network .elfinder-cwd-icon:before {
  content: '\e823';
}
.elfinder-dialog-icon {
  font: normal normal normal 14px/1 material;
  background: inherit;
  color: #524949;
  font-size: 37px;
}
.elfinder-dialog-icon:before {
  content: '\e843';
}
.elfinder-dialog-icon-mkdir:before {
  content: '\e808';
}
.elfinder-dialog-icon-mkfile:before {
  content: '\e809';
}
.elfinder-dialog-icon-copy:before {
  content: '\e80d';
}
.elfinder-dialog-icon-prepare:before,
.elfinder-dialog-icon-move:before {
  content: '\e844';
}
.elfinder-dialog-icon-upload:before,
.elfinder-dialog-icon-chunkmerge:before {
  content: '\e815';
}
.elfinder-dialog-icon-rm:before {
  content: '\e80a';
}
.elfinder-dialog-icon-open:before,
.elfinder-dialog-icon-readdir:before,
.elfinder-dialog-icon-file:before {
  content: '\e807';
}
.elfinder-dialog-icon-reload:before {
  content: '\e806';
}
.elfinder-dialog-icon-download:before {
  content: '\e816';
}
.elfinder-dialog-icon-save:before {
  content: '\e845';
}
.elfinder-dialog-icon-rename:before {
  content: '\e812';
}
.elfinder-dialog-icon-zipdl:before,
.elfinder-dialog-icon-archive:before {
  content: '\e819';
}
.elfinder-dialog-icon-extract:before {
  content: '\e818';
}
.elfinder-dialog-icon-search:before {
  content: '\e81f';
}
.elfinder-dialog-icon-loadimg:before {
  content: '\e846';
}
.elfinder-dialog-icon-url:before {
  content: '\e81e';
}
.elfinder-dialog-icon-resize:before {
  content: '\e81d';
}
.elfinder-dialog-icon-netmount:before {
  content: '\e823';
}
.elfinder-dialog-icon-netunmount:before {
  content: '\e824';
}
.elfinder-dialog-icon-chmod:before {
  content: '\e826';
}
.elfinder-dialog-icon-preupload:before,
.elfinder-dialog-icon-dim:before {
  content: '\e847';
}
.elfinder-contextmenu .elfinder-contextmenu-item span.elfinder-contextmenu-icon {
  font-size: 16px;
}
.elfinder-contextmenu .elfinder-contextmenu-item .elfinder-contextsubmenu-item .ui-icon {
  font-size: 15px;
}
.elfinder-contextmenu .elfinder-contextmenu-item .elfinder-button-icon-link:before {
  content: '\e837';
}
.elfinder .elfinder-contextmenu-extra-icon {
  margin-top: -6px;
}
.elfinder .elfinder-contextmenu-extra-icon a {
  padding: 5px;
  margin: -16px;
}
.elfinder-button-icon-link:before {
  content: '\e81e' !important;
}
.elfinder .elfinder-contextmenu-arrow {
  font: normal normal normal 14px/1 material;
  background-image: inherit;
  font-size: 10px !important;
  padding-top: 3px;
}
.elfinder .elfinder-contextmenu-arrow:before {
  content: '\e857';
}
.elfinder-contextmenu .ui-state-hover .elfinder-contextmenu-arrow {
  background-image: inherit;
}
.elfinder-quicklook .ui-resizable-se {
  background: inherit;
}
.elfinder-quicklook-navbar-icon {
  background: transparent;
  font: normal normal normal 14px/1 material;
  font-size: 32px;
  color: #fff;
}
.elfinder-quicklook-titlebar-icon {
  margin-top: -8px;
}
.elfinder-quicklook-titlebar-icon .ui-icon {
  border: 0;
  opacity: 0.8;
  font-size: 15px;
  padding: 1px;
}
.elfinder-quicklook-titlebar .ui-icon-circle-close,
.elfinder-quicklook .ui-icon-gripsmall-diagonal-se {
  color: #f1f1f1;
}
.elfinder-quicklook-navbar-icon-prev:before {
  content: '\e848';
}
.elfinder-quicklook-navbar-icon-next:before {
  content: '\e849';
}
.elfinder-quicklook-navbar-icon-fullscreen:before {
  content: '\e84a';
}
.elfinder-quicklook-navbar-icon-fullscreen-off:before {
  content: '\e84b';
}
.elfinder-quicklook-navbar-icon-close:before {
  content: '\e84c';
}
.ui-button-icon {
  background-image: inherit;
}
.ui-icon-search:before {
  content: '\e81f';
}
.ui-icon-closethick:before,
.ui-icon-close:before {
  content: '\e839';
}
.ui-icon-circle-close:before {
  content: '\e84c';
}
.ui-icon-gear:before {
  content: '\e82f';
}
.ui-icon-gripsmall-diagonal-se:before {
  content: '\e838';
}
.ui-icon-locked:before {
  content: '\e834';
}
.ui-icon-unlocked:before {
  content: '\e836';
}
.ui-icon-arrowrefresh-1-n:before {
  content: '\e821';
}
.ui-icon-plusthick:before {
  content: '\e83a';
}
.ui-icon-arrowreturnthick-1-s:before {
  content: '\e83b';
}
.ui-icon-minusthick:before {
  content: '\e83c';
}
.ui-icon-pin-s:before {
  content: '\e84d';
}
.ui-icon-check:before {
  content: '\e84e';
}
.ui-icon-arrowthick-1-s:before {
  content: '\e84f';
}
.ui-icon-arrowthick-1-n:before {
  content: '\e850';
}
.ui-icon-triangle-1-s:before {
  content: '\e851';
}
.ui-icon-triangle-1-n:before {
  content: '\e852';
}
.ui-icon-grip-dotted-vertical:before {
  content: '\e853';
}
.elfinder-lock,
.elfinder-perms,
.elfinder-symlink {
  background-image: inherit;
  font: normal normal normal 18px/1 material;
  color: #d8d8d8;
}
.elfinder-na .elfinder-perms:before {
  content: '\e824';
}
.elfinder-ro .elfinder-perms:before {
  content: '\e835';
}
.elfinder-wo .elfinder-perms:before {
  content: '\e854';
}
.elfinder-group .elfinder-perms:before {
  content: '\e800';
}
.elfinder-lock:before {
  content: '\e84d';
}
.elfinder-symlink:before {
  content: '\e837';
}
.elfinder .elfinder-toast > div {
  font: normal normal normal 14px/1 material;
}
.elfinder .elfinder-toast > div:before {
  font-size: 45px;
  position: absolute;
  left: 5px;
  top: 15px;
}
.elfinder .elfinder-toast > .toast-info,
.elfinder .elfinder-toast > .toast-error,
.elfinder .elfinder-toast > .toast-success,
.elfinder .elfinder-toast > .toast-warning {
  background-image: inherit !important;
}
.elfinder .elfinder-toast > .toast-info:before {
  content: '\e817';
  color: #3498db;
}
.elfinder .elfinder-toast > .toast-error:before {
  content: '\e855';
  color: #f44336;
}
.elfinder .elfinder-toast > .toast-success:before {
  content: '\e84e';
  color: #4caf50;
}
.elfinder .elfinder-toast > .toast-warning:before {
  content: '\e856';
  color: #ff9800;
}
.elfinder-drag-helper-icon-status {
  font: normal normal normal 14px/1 material;
  background: inherit;
}
.elfinder-drag-helper-icon-status:before {
  content: '\e824';
}
.elfinder-drag-helper-move .elfinder-drag-helper-icon-status {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.elfinder-drag-helper-move .elfinder-drag-helper-icon-status:before {
  content: '\e854';
}
.elfinder-drag-helper-plus .elfinder-drag-helper-icon-status {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
.elfinder-drag-helper-plus .elfinder-drag-helper-icon-status:before {
  content: '\e84c';
}
/**
 * MIME Types
 */
.elfinder-cwd-view-list td .elfinder-cwd-icon {
  background-image: url("../images/icons-small.png");
}
.elfinder-cwd-icon {
  background: url("../images/icons-big.png") 0 0 no-repeat;
}
.elfinder-cwd-icon:before {
  font-size: 10px;
  position: relative;
  top: 27px;
  left: inherit;
  padding: 1px;
  background-color: transparent;
}
.elfinder-info-title .elfinder-cwd-icon:before {
  top: 32px;
  display: block;
  margin: 0 auto;
}
.elfinder-info-title .elfinder-cwd-icon.elfinder-cwd-bgurl:before {
  background-color: #313131 !important;
}
.elfinder-cwd-view-icons .elfinder-cwd-icon.elfinder-cwd-bgurl:before {
  left: inherit;
  background-color: #313131;
}
.elfinder-quicklook .elfinder-cwd-icon:before {
  top: 33px;
  left: 50% !important;
  position: relative;
  display: block;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}
.elfinder-cwd-icon-zip:before,
.elfinder-cwd-icon-x-zip:before {
  content: 'zip' !important;
}
.elfinder-cwd-icon-x-xz:before {
  content: 'xz' !important;
}
.elfinder-cwd-icon-x-7z-compressed:before {
  content: '7z' !important;
}
.elfinder-cwd-icon-x-gzip:before {
  content: 'gzip' !important;
}
.elfinder-cwd-icon-x-tar:before {
  content: 'tar' !important;
}
.elfinder-cwd-icon-x-bzip:before,
.elfinder-cwd-icon-x-bzip2:before {
  content: 'bzip' !important;
}
.elfinder-cwd-icon-x-rar:before,
.elfinder-cwd-icon-x-rar-compressed:before {
  content: 'rar' !important;
}
.elfinder-cwd-icon-directory {
  background-position: 0 -50px;
}
.elfinder-cwd-icon-application {
  background-position: 0 -150px;
}
.elfinder-cwd-icon-text {
  background-position: 0 -200px;
}
.elfinder-cwd-icon-plain,
.elfinder-cwd-icon-x-empty {
  background-position: 0 -250px;
}
.elfinder-cwd-icon-image {
  background-position: 0 -300px;
}
.elfinder-cwd-icon-vnd-adobe-photoshop {
  background-position: 0 -350px;
}
.elfinder-cwd-icon-vnd-adobe-photoshop:before {
  content: none !important;
}
.elfinder-cwd-icon-postscript {
  background-position: 0 -400px;
}
.elfinder-cwd-icon-audio {
  background-position: 0 -450px;
}
.elfinder-cwd-icon-video,
.elfinder-cwd-icon-flash-video,
.elfinder-cwd-icon-dash-xml,
.elfinder-cwd-icon-vnd-apple-mpegurl,
.elfinder-cwd-icon-x-mpegurl {
  background-position: 0 -500px;
}
.elfinder-cwd-icon-rtf,
.elfinder-cwd-icon-rtfd {
  background-position: 0 -550px;
}
.elfinder-cwd-icon-pdf {
  background-position: 0 -600px;
}
.elfinder-cwd-icon-x-msaccess {
  background-position: 0 -650px;
}
.elfinder-cwd-icon-x-msaccess:before {
  content: none !important;
}
.elfinder-cwd-icon-msword,
.elfinder-cwd-icon-vnd-ms-word,
.elfinder-cwd-icon-vnd-ms-word-document-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-word-template-macroEnabled-12 {
  background-position: 0 -700px;
}
.elfinder-cwd-icon-msword:before,
.elfinder-cwd-icon-vnd-ms-word:before,
.elfinder-cwd-icon-vnd-ms-word-document-macroEnabled-12:before,
.elfinder-cwd-icon-vnd-ms-word-template-macroEnabled-12:before {
  content: none !important;
}
.elfinder-cwd-icon-ms-excel,
.elfinder-cwd-icon-vnd-ms-excel,
.elfinder-cwd-icon-vnd-ms-excel-addin-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-excel-sheet-binary-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-excel-sheet-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-excel-template-macroEnabled-12 {
  background-position: 0 -750px;
}
.elfinder-cwd-icon-ms-excel:before,
.elfinder-cwd-icon-vnd-ms-excel:before,
.elfinder-cwd-icon-vnd-ms-excel-addin-macroEnabled-12:before,
.elfinder-cwd-icon-vnd-ms-excel-sheet-binary-macroEnabled-12:before,
.elfinder-cwd-icon-vnd-ms-excel-sheet-macroEnabled-12:before,
.elfinder-cwd-icon-vnd-ms-excel-template-macroEnabled-12:before {
  content: none !important;
}
.elfinder-cwd-icon-vnd-ms-powerpoint,
.elfinder-cwd-icon-vnd-ms-powerpoint-addin-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-powerpoint-presentation-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-powerpoint-slide-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-powerpoint-slideshow-macroEnabled-12,
.elfinder-cwd-icon-vnd-ms-powerpoint-template-macroEnabled-12 {
  background-position: 0 -800px;
}
.elfinder-cwd-icon-vnd-ms-powerpoint:before,
.elfinder-cwd-icon-vnd-ms-powerpoint-addin-macroEnabled-12:before,
.elfinder-cwd-icon-vnd-ms-powerpoint-presentation-macroEnabled-12:before,
.elfinder-cwd-icon-vnd-ms-powerpoint-slide-macroEnabled-12:before,
.elfinder-cwd-icon-vnd-ms-powerpoint-slideshow-macroEnabled-12:before,
.elfinder-cwd-icon-vnd-ms-powerpoint-template-macroEnabled-12:before {
  content: none !important;
}
.elfinder-cwd-icon-vnd-ms-office,
.elfinder-cwd-icon-vnd-oasis-opendocument-chart,
.elfinder-cwd-icon-vnd-oasis-opendocument-database,
.elfinder-cwd-icon-vnd-oasis-opendocument-formula,
.elfinder-cwd-icon-vnd-oasis-opendocument-graphics,
.elfinder-cwd-icon-vnd-oasis-opendocument-graphics-template,
.elfinder-cwd-icon-vnd-oasis-opendocument-image,
.elfinder-cwd-icon-vnd-oasis-opendocument-presentation,
.elfinder-cwd-icon-vnd-oasis-opendocument-presentation-template,
.elfinder-cwd-icon-vnd-oasis-opendocument-spreadsheet,
.elfinder-cwd-icon-vnd-oasis-opendocument-spreadsheet-template,
.elfinder-cwd-icon-vnd-oasis-opendocument-text,
.elfinder-cwd-icon-vnd-oasis-opendocument-text-master,
.elfinder-cwd-icon-vnd-oasis-opendocument-text-template,
.elfinder-cwd-icon-vnd-oasis-opendocument-text-web,
.elfinder-cwd-icon-vnd-openofficeorg-extension,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-presentation,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-slide,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-slideshow,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-presentationml-template,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-spreadsheetml-sheet,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-spreadsheetml-template,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-wordprocessingml-document,
.elfinder-cwd-icon-vnd-openxmlformats-officedocument-wordprocessingml-template {
  background-position: 0 -850px;
}
.elfinder-cwd-icon-html {
  background-position: 0 -900px;
}
.elfinder-cwd-icon-css {
  background-position: 0 -950px;
}
.elfinder-cwd-icon-javascript,
.elfinder-cwd-icon-x-javascript {
  background-position: 0 -1000px;
}
.elfinder-cwd-icon-x-perl {
  background-position: 0 -1050px;
}
.elfinder-cwd-icon-x-python:after,
.elfinder-cwd-icon-x-python {
  background-position: 0 -1100px;
}
.elfinder-cwd-icon-x-ruby {
  background-position: 0 -1150px;
}
.elfinder-cwd-icon-x-sh,
.elfinder-cwd-icon-x-shellscript {
  background-position: 0 -1200px;
}
.elfinder-cwd-icon-x-c,
.elfinder-cwd-icon-x-csrc,
.elfinder-cwd-icon-x-chdr,
.elfinder-cwd-icon-x-c--,
.elfinder-cwd-icon-x-c--src,
.elfinder-cwd-icon-x-c--hdr {
  background-position: 0 -1250px;
}
.elfinder-cwd-icon-x-jar,
.elfinder-cwd-icon-x-java,
.elfinder-cwd-icon-x-java-source {
  background-position: 0 -1300px;
}
.elfinder-cwd-icon-x-jar:before,
.elfinder-cwd-icon-x-java:before,
.elfinder-cwd-icon-x-java-source:before {
  content: none !important;
}
.elfinder-cwd-icon-x-php {
  background-position: 0 -1350px;
}
.elfinder-cwd-icon-xml:after,
.elfinder-cwd-icon-xml {
  background-position: 0 -1400px;
}
.elfinder-cwd-icon-zip,
.elfinder-cwd-icon-x-zip,
.elfinder-cwd-icon-x-xz,
.elfinder-cwd-icon-x-7z-compressed,
.elfinder-cwd-icon-x-gzip,
.elfinder-cwd-icon-x-tar,
.elfinder-cwd-icon-x-bzip,
.elfinder-cwd-icon-x-bzip2,
.elfinder-cwd-icon-x-rar,
.elfinder-cwd-icon-x-rar-compressed {
  background-position: 0 -1450px;
}
.elfinder-cwd-icon-x-shockwave-flash {
  background-position: 0 -1500px;
}
.elfinder-cwd-icon-group {
  background-position: 0 -1550px;
}
.elfinder-cwd-icon-json {
  background-position: 0 -1600px;
}
.elfinder-cwd-icon-json:before {
  content: none !important;
}
.elfinder-cwd-icon-markdown,
.elfinder-cwd-icon-x-markdown {
  background-position: 0 -1650px;
}
.elfinder-cwd-icon-markdown:before,
.elfinder-cwd-icon-x-markdown:before {
  content: none !important;
}
.elfinder-cwd-icon-sql {
  background-position: 0 -1700px;
}
.elfinder-cwd-icon-sql:before {
  content: none !important;
}
.elfinder-cwd-icon-svg,
.elfinder-cwd-icon-svg-xml {
  background-position: 0 -1750px;
}
.elfinder-cwd-icon-svg:before,
.elfinder-cwd-icon-svg-xml:before {
  content: none !important;
}
/**
 * Toolbar
 */
.elfinder-toolbar {
  background: #061325;
  -webkit-border-radius: 0;
  border-radius: 0;
  border: 0;
  padding: 5px 0;
}
.elfinder-toolbar .elfinder-button-icon {
  font-size: 20px;
  color: #ddd;
  margin-top: -2px;
}
.elfinder-buttonset {
  -webkit-border-radius: 0;
  border-radius: 0;
  border: 0;
  margin: 0 5px;
  height: 24px;
}
.elfinder .elfinder-button {
  background: transparent;
  -webkit-border-radius: 0;
  border-radius: 0;
  cursor: pointer;
  color: #efefef;
}
.elfinder-toolbar-button-separator {
  border: 0;
}
.elfinder-button-menu {
  -webkit-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3);
  border: none;
  margin-top: 5px;
}
.elfinder-button-menu-item {
  color: #666;
  padding: 6px 19px;
}
.elfinder-button-menu-item.ui-state-hover {
  color: #141414;
  background-color: #f5f4f4;
}
.elfinder-button-menu-item-separated {
  border-top: 1px solid #e5e5e5;
}
.elfinder-button-menu-item-separated.ui-state-hover {
  border-top: 1px solid #e5e5e5;
}
.elfinder .elfinder-button-search {
  margin: 0 10px;
  min-height: inherit;
  overflow: hidden;
}
.elfinder .elfinder-button-search input {
  background: rgba(22, 43, 76, 0.75);
  -webkit-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  border: 0;
  margin: 0;
  padding: 0 23px;
  height: 24px;
  color: #fff;
}
.elfinder .elfinder-button-search .elfinder-button-menu {
  margin-top: 4px;
  border: none;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}
.elfinder .elfinder-button-search-menu {
  -webkit-border-radius: 0;
  border-radius: 0;
}
/**
 * Navbar
 */
.elfinder .elfinder-navbar {
  background: #2a384d;
  -webkit-box-shadow: 0 1px 8px rgba(0, 0, 0, 0.6);
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.6);
  border: none;
}
.elfinder .elfinder-navbar .elfinder-lock,
.elfinder .elfinder-navbar .elfinder-perms,
.elfinder .elfinder-navbar .elfinder-symlink {
  color: #a9a9a9;
  opacity: 0.8;
}
.elfinder-navbar-dir {
  color: #e6e6e6;
  cursor: pointer;
  -webkit-border-radius: 2px;
  border-radius: 2px;
  padding: 5px;
  border: none;
}
.elfinder-navbar-dir.ui-state-hover,
.elfinder-navbar-dir.ui-state-active.ui-state-hover {
  background: #17202c;
  color: #e6e6e6;
  border: none;
}
.elfinder-navbar .ui-state-active,
.elfinder-disabled .elfinder-navbar .ui-state-active {
  background: #1b2533;
  border: none;
}
/**
 * Workzone
 */
.elfinder-workzone {
  background: #0e1827;
}
.elfinder-cwd-file {
  color: #ddd;
}
.elfinder-cwd-file.ui-state-hover,
.elfinder-cwd-file.ui-selected.ui-state-hover {
  background: #1a283c;
  color: #ddd;
}
.elfinder-cwd-file.ui-selected {
  background: #152131;
  color: #ddd;
}
.elfinder-cwd-filename input,
.elfinder-cwd-filename textarea {
  padding: 2px;
  -webkit-border-radius: 2px !important;
  border-radius: 2px !important;
  background: #fff;
  color: #222;
}
.elfinder-cwd-filename input:focus,
.elfinder-cwd-filename textarea:focus {
  outline: none;
  border: 1px solid #555;
}
.elfinder-cwd-view-icons .elfinder-cwd-file .ui-state-hover,
.elfinder-cwd-view-icons .elfinder-cwd-file .elfinder-cwd-filename.ui-state-hover,
.elfinder-disabled .elfinder-cwd-view-icons .elfinder-cwd-file .elfinder-cwd-filename.ui-state-hover,
.elfinder-disabled .elfinder-cwd table td.ui-state-hover,
.elfinder-cwd-view-icons .elfinder-cwd-file .ui-state-active {
  background: transparent;
  color: #ddd;
}
.elfinder-cwd table {
  padding: 0;
}
.elfinder-cwd table thead td {
  padding: 5px 14px;
}
.elfinder-cwd table tr {
  border: 0 !important;
}
.elfinder-cwd table tr:nth-child(odd) {
  background-color: transparent;
}
.elfinder-cwd table tr:nth-child(odd).ui-state-hover {
  background-color: #1a283c;
}
.elfinder-cwd.elfinder-table-header-sticky table {
  border: 0;
}
.elfinder-cwd .elfinder-lock,
.elfinder-cwd .elfinder-perms,
.elfinder-cwd .elfinder-symlink {
  color: #d8d8d8;
}
.elfinder-cwd-view-icons .elfinder-lock {
  top: 0;
}
.elfinder-cwd-view-list thead td .ui-resizable-handle {
  top: 3px;
}
.elfinder-cwd-view-list .elfinder-lock,
.elfinder-cwd-view-list .elfinder-perms,
.elfinder-cwd-view-list .elfinder-symlink {
  font-size: 14px;
  opacity: 0.7;
}
.elfinder-cwd-view-list .elfinder-perms {
  left: inherit;
}
#elfinder-elfinder-cwd-thead td {
  background: #010e21;
  color: #ddd;
}
#elfinder-elfinder-cwd-thead td.ui-state-hover,
#elfinder-elfinder-cwd-thead td.ui-state-active {
  background: #000308;
}
#elfinder-elfinder-cwd-thead td.ui-state-active.ui-state-hover {
  background: #010812;
}
.ui-selectable-helper {
  border: 1px solid #022861;
  background-color: rgba(3, 62, 150, 0.38);
}
.elfinder-cwd-wrapper.elfinder-cwd-wrapper-trash {
  background-color: #e4e4e4;
}
.elfinder-cwd-wrapper.elfinder-cwd-wrapper-trash .elfinder-cwd-file {
  color: #333;
}
.elfinder-cwd-wrapper.elfinder-cwd-wrapper-trash .elfinder-cwd-file.ui-state-hover,
.elfinder-cwd-wrapper.elfinder-cwd-wrapper-trash .elfinder-cwd-file.ui-selected.ui-state-hover {
  background: #1a283c;
  color: #ddd;
}
.elfinder-cwd-wrapper.elfinder-cwd-wrapper-trash .elfinder-cwd-file.ui-selected {
  background: #152131;
  color: #ddd;
}
.elfinder .elfinder-cwd-view-icons .elfinder-cwd-bgurl:after,
.elfinder .elfinder-quicklook-info-wrapper .elfinder-cwd-bgurl:after {
  display: none;
}
/**
 * Status Bar
 */
.elfinder .elfinder-statusbar {
  background: #061325;
  -webkit-border-radius: 0;
  border-radius: 0;
  border: 0;
  color: #cfd2d4;
}
.elfinder-path,
.elfinder-stat-size {
  margin: 0 15px;
}
/**
 * Buttons
 */
.ui-button,
.ui-button:active,
.ui-button.ui-state-default {
  display: inline-block;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  white-space: nowrap;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  text-transform: uppercase;
  -webkit-box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.4) !important;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.4) !important;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  background: #fff;
  color: #222;
  border: none;
}
.ui-button .ui-icon,
.ui-button:active .ui-icon,
.ui-button.ui-state-default .ui-icon {
  color: #222;
}
.ui-button:hover,
a.ui-button:active,
.ui-button:active,
.ui-button:focus,
.ui-button.ui-state-hover,
.ui-button.ui-state-active {
  background: #3498db !important;
  color: #fff !important;
  border: none;
}
.ui-button:hover .ui-icon,
a.ui-button:active .ui-icon,
.ui-button:active .ui-icon,
.ui-button:focus .ui-icon,
.ui-button.ui-state-hover .ui-icon,
.ui-button.ui-state-active .ui-icon {
  color: #fff;
}
.ui-button.ui-state-active:hover {
  background: #217dbb;
  color: #fff;
  border: none;
}
.ui-button:focus {
  outline: none !important;
}
.ui-controlgroup-horizontal .ui-button {
  -webkit-border-radius: 0;
  border-radius: 0;
  border: 0;
}
/**
 * Context Menu
 */
.elfinder .elfinder-contextmenu,
.elfinder .elfinder-contextmenu-sub {
  -webkit-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3);
  border: none;
}
.elfinder .elfinder-contextmenu-separator,
.elfinder .elfinder-contextmenu-sub-separator {
  border-top: 1px solid #e5e5e5;
}
.elfinder .elfinder-contextmenu-item {
  color: #666;
  padding: 5px 30px;
}
.elfinder .elfinder-contextmenu-item.ui-state-hover {
  background-color: #f5f4f4;
  color: #141414;
}
.elfinder .elfinder-contextmenu-item.ui-state-active {
  background-color: #2196f3;
  color: #fff;
}
/**
 * Dialogs
 */
.elfinder .elfinder-dialog {
  -webkit-border-radius: 0;
  border-radius: 0;
  border: 0;
  -webkit-box-shadow: 0 1px 30px rgba(0, 0, 0, 0.6);
  box-shadow: 0 1px 30px rgba(0, 0, 0, 0.6);
}
.elfinder .elfinder-dialog .ui-dialog-content[id*="edit-elfinder-elfinder-"] {
  padding: 0;
}
.elfinder .elfinder-dialog .ui-tabs {
  -webkit-border-radius: 0;
  border-radius: 0;
  border: 0;
  padding: 0;
}
.elfinder .elfinder-dialog .ui-tabs-nav {
  -webkit-border-radius: 0;
  border-radius: 0;
  border: 0;
  background: transparent;
  border-bottom: 1px solid #ddd;
}
.elfinder .elfinder-dialog .ui-tabs-nav li {
  border: 0;
  font-weight: normal;
  background: transparent;
  margin: 0;
  padding: 3px 0;
}
.elfinder .elfinder-dialog .ui-tabs-nav li.ui-tabs-active {
  padding-bottom: 7px;
}
.elfinder .elfinder-dialog .ui-tabs-nav .ui-tabs-selected a,
.elfinder .elfinder-dialog .ui-tabs-nav .ui-state-active a,
.elfinder .elfinder-dialog .ui-tabs-nav li:hover a {
  -webkit-box-shadow: inset 0 -2px 0 #3498db;
  box-shadow: inset 0 -2px 0 #3498db;
  color: #3498db;
}
.elfinder .elfinder-dialog .ui-tabs .elfinder-tabstop.ui-state-hover {
  background: transparent;
  -webkit-box-shadow: inset 0 -2px 0 #3498db;
  box-shadow: inset 0 -2px 0 #3498db;
  color: #3498db;
}
.elfinder .elfinder-dialog label.ui-state-hover {
  background: transparent;
}
.elfinder .elfinder-dialog .ui-resizable-se {
  display: none;
}
.std42-dialog .ui-dialog-titlebar {
  background: #0f1f2f;
  -webkit-border-radius: 0;
  border-radius: 0;
  border: 0;
}
.std42-dialog .ui-dialog-titlebar .elfinder-titlebar-button .ui-icon {
  border-color: inherit;
  -webkit-transition: 0.2s ease-out;
  -o-transition: 0.2s ease-out;
  transition: 0.2s ease-out;
  opacity: 0.8;
  color: #fff;
  width: auto;
  height: auto;
  font-size: 12px;
  padding: 3px;
}
.std42-dialog,
.std42-dialog .ui-dialog-content,
.std42-dialog.elfinder-bg-translucent,
.std42-dialog.elfinder-bg-translucent .ui-widget-content {
  background-color: #fff;
}
.std42-dialog .ui-dialog-buttonpane button {
  margin: 2px;
}
.std42-dialog .ui-dialog-buttonpane button span.ui-icon {
  padding: 0;
}
.std42-dialog .ui-dialog-buttonpane .ui-dialog-buttonset.elfinder-edit-extras {
  margin-top: -4px;
}
.std42-dialog,
.std42-dialog .ui-widget-content {
  background-color: #fff;
}
.elfinder-mobile .std42-dialog .ui-dialog-titlebar .ui-dialog-titlebar-close .ui-icon,
.std42-dialog .ui-dialog-titlebar .ui-dialog-titlebar-close:hover .ui-icon {
  background-color: #f44336;
}
.elfinder-mobile .std42-dialog .ui-dialog-titlebar .elfinder-titlebar-full .ui-icon,
.std42-dialog .ui-dialog-titlebar .elfinder-titlebar-full:hover .ui-icon {
  background-color: #4caf50;
}
.elfinder-mobile .std42-dialog .ui-dialog-titlebar .elfinder-titlebar-minimize .ui-icon,
.std42-dialog .ui-dialog-titlebar .elfinder-titlebar-minimize:hover .ui-icon {
  background-color: #ff9800;
}
.elfinder-dialog-title {
  color: #f1f1f1;
}
.ui-widget-content {
  font-family: "Noto Sans", sans-serif;
  color: #546e7a;
}
.elfinder-upload-dialog-wrapper .elfinder-upload-dirselect {
  width: inherit;
  height: inherit;
  padding: 0.4em;
  margin-left: 5px;
  color: #222;
}
.elfinder-upload-dialog-wrapper .elfinder-upload-dirselect.ui-state-hover {
  background: #888;
  color: #fff;
  outline: none;
  -webkit-border-radius: 2px;
  border-radius: 2px;
}
.elfinder-upload-dialog-wrapper .ui-button {
  padding: 0.4em 3px;
  margin: 0 -15px 0 19px;
}
.elfinder-upload-dropbox {
  border: 2px dashed #bbb;
}
.elfinder-upload-dropbox:focus {
  outline: none;
}
.elfinder-upload-dropbox.ui-state-hover {
  background: #f1f1f1;
  border: 2px dashed #bbb;
}
.elfinder-help * {
  color: #546e7a;
}
.elfinder-help a {
  color: #3498db;
}
.elfinder-help a:hover {
  color: #217dbb;
}
.ui-slider.ui-slider-horizontal {
  height: 2px;
  border: 0;
  background-color: #bababa !important;
}
.ui-slider .ui-slider-handle {
  background-image: none;
  background-color: #5d5858;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  border: 0;
  margin-top: -3px;
}
.ui-slider .ui-slider-handle.ui-state-hover {
  background: #5d5858 !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  cursor: pointer;
}

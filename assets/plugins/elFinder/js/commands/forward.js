/**
 * @class  elFinder command "forward"
 * Open next visited folder
 *
 * <AUTHOR> (dio) <PERSON><PERSON><PERSON>
 **/
(elFinder.prototype.commands.forward = function() {
	"use strict";
	this.alwaysEnabled = true;
	this.updateOnSelect = true;
	this.shortcuts = [{
		pattern     : 'ctrl+right'
	}];
	
	this.getstate = function() {
		return this.fm.history.canForward() ? 0 : -1;
	};
	
	this.exec = function() {
		return this.fm.history.forward();
	};
	
}).prototype = { forceLoad : true }; // this is required command

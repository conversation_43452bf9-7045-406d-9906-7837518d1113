// Variables
// Syntax: <control>-(<sub control>)-<bg|border|text>-(<state>)-(<extra>);
// Example: @btn-primary-bg-hover-hlight;

@prefix:                         mce;

// Default font
@font-family:                    "Helvetica Neue", Helvetica, Arial, sans-serif;
@font-size:                      14px;
@line-height:                    20px;
@has-gradients:                  false;
@has-radius:                     true;
@has-boxshadow:                  false;
@has-button-borders:             true;

// Text colors
@text:                           #333333;
@text-inverse:                   #ffffff;
@text-disabled:                  #aaaaaa;
@text-shadow:                    0 1px 1px hsla(hue(@text-inverse), saturation(@text-inverse), lightness(@text-inverse), 0.75);
@text-error:                     #b94a48;
@text-warning:                   #c09853;
@text-success:                   #468847;
@text-link:                      #2980b9;

// Button
@btn-text:                       #333333;
@btn-text-shadow:                #ffffff;
@btn-border-top:                 transparent;
@btn-border-right:               transparent;
@btn-border-bottom:              transparent;
@btn-border-left:                transparent;
@btn-caret-border:               @btn-text;
@btn-text-disabled:              @text-disabled;
@btn-box-shadow:                 inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 2px rgba(0, 0, 0, .05);
@btn-box-shadow-active:          inset 0 2px 4px rgba(0, 0, 0, .15), 0 1px 2px rgba(0, 0, 0, .05);
@btn-box-disabled-opacity:       0.4;
@btn-bg:                         #ffffff;
@btn-bg-hlight:                  #ffffff;
@btn-bg-hover:                   darken(@btn-bg, 5%);
@btn-bg-hlight-hover:            darken(@btn-bg-hlight, 5%);
@btn-border-hover:               darken(@btn-bg, 20%);
@btn-border-active:              darken(@btn-bg, 20%);
@btn-padding:                    4px 8px;

@btn-primary-bg:                 #0088cc;
@btn-primary-bg-hlight:          #03a9f4;
@btn-primary-bg-hover:           darken(@btn-primary-bg, 5%);
@btn-primary-bg-hover-hlight:    darken(@btn-primary-bg-hlight, 5%);
@btn-primary-text:               #ffffff;
@btn-primary-text-shadow:        #333333;
@btn-primary-border-top:         mix(@btn-border-top, @btn-primary-bg, 50%);
@btn-primary-border-right:       mix(@btn-border-right, @btn-primary-bg, 50%);
@btn-primary-border-bottom:      mix(@btn-border-bottom, @btn-primary-bg, 50%);
@btn-primary-border-left:        mix(@btn-border-left, @btn-primary-bg, 50%);
@btn-primary-border:             transparent;
@btn-primary-border-hover:       transparent;

// Button group
@btn-group-border-width:         1px;

// Menu
@menuitem-text:                  #333333;
@menu-bg:                        #ffffff;
@menu-margin:                    -1px 0 0;
@menu-border:                    rgba(0,0,0,0.2);
@menubar-border:                 mix(@panel-border, @panel-bg, 60%);
@menuitem-text-inverse:          #ffffff;
@menubar-bg-active:              darken(@btn-bg, 10%);
@menuitem-bg-hover:              #0081C2;
@menuitem-bg-selected:           #03a9f4;
@menuitem-bg-selected-hlight:    #0077b3;
@menuitem-bg-disabled:           #CCC;
@menuitem-caret:                 @menuitem-text;
@menuitem-caret-selected:        @menuitem-text-inverse;
@menuitem-separator-top:         #cbcbcb;
@menuitem-separator-bottom:      #ffffff;
@menuitem-bg-active:             #c8def4;
@menuitem-text-active:           #333333;
@menuitem-preview-border-active: #aaaaaa;
@menubar-menubtn-text:           #dddddd;

// Panel
@panel-border:                   #e8e8e8;
@panel-bg:                       #fbfdff;
@panel-bg-hlight:                #ffffff;

// Tabs
@tab-border:                     #e8e8e8;
@tab-bg:                         #ffffff;
@tab-bg-hover:                   #f2f2f2;
@tab-bg-active:                  #f2f2f2;
@tabs-bg:             #ffffff;

// Tooltip
@tooltip-bg:                     #000;
@tooltip-text:                   white;
@tooltip-font-size:              11px;

// Notification
@notification-font-size:         14px;
@notification-bg:                #f0f0f0;
@notification-border:            #cccccc;
@notification-text:              #333333;
@notification-success-bg:        #dff0d8;
@notification-success-border:    #d6e9c6;
@notification-success-text:      #3c763d;
@notification-info-bg:           #d9edf7;
@notification-info-border:       #779ecb;
@notification-info-text:         #31708f;
@notification-warning-bg:        #fcf8e3;
@notification-warning-border:    #faebcc;
@notification-warning-text:      #8a6d3b;
@notification-error-bg:          #f2dede;
@notification-error-border:      #ebccd1;
@notification-error-text:        #a94442;

// Infobox
@infobox-bg:                     @notification-bg;
@infobox-border:                 @notification-border;
@infobox-text:                   @notification-text;
@infobox-success-bg:             @notification-success-bg;
@infobox-success-border:         @notification-success-border;
@infobox-success-text:           @notification-success-text;
@infobox-info-bg:                @notification-info-bg;
@infobox-info-border:            @notification-info-border;
@infobox-info-text:              @notification-info-text;
@infobox-warning-bg:             @notification-warning-bg;
@infobox-warning-border:         @notification-warning-border;
@infobox-warning-text:           @notification-warning-text;
@infobox-error-bg:               @notification-error-bg;
@infobox-error-border:           @notification-error-border;
@infobox-error-text:             @notification-error-text;

// Window
@window-border:                  #c4c4c4;
@window-head-border:             @window-border;
@window-head-close:              mix(@text, @window-bg, 60%);
@window-head-close-hover:        mix(@text, @window-bg, 40%);
@window-foot-border:             @window-border;
@window-foot-bg:                 @window-bg;
@window-fullscreen-bg:           #FFF;
@window-modalblock-bg:           #000;
@window-modalblock-opacity:      0.3;
@window-box-shadow:              0 3px 7px rgba(0, 0, 0, 0.3);
@window-bg:                      #ffffff;
@window-title-font-size:         20px;

// Popover
@popover-bg:                     @window-bg;
@popover-arrow-width:            10px;
@popover-arrow:                  @window-bg;
@popover-arrow-outer-width:      @popover-arrow-width + 1;
@popover-arrow-outer:            rgba(0, 0, 0, 0.25);

// Floatpanel
@floatpanel-box-shadow:          0 5px 10px rgba(0, 0, 0, .2);

// Checkbox
@checkbox-bg:                    @btn-bg;
@checkbox-bg-hlight:             @btn-bg-hlight;
@checkbox-box-shadow:            inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 2px rgba(0, 0, 0, .05);
@checkbox-border:                #c5c5c5;
@checkbox-border-focus:          #59a5e1;

// Path
@path-text:                      @text;
@path-bg-focus:                  #666;
@path-text-focus:                #fff;

// Textbox
@textbox-text-placeholder:       #aaa;
@textbox-box-shadow:             inset 0 1px 1px rgba(0, 0, 0, 0.075);
@textbox-bg:                     #ffffff;
@textbox-border:                 #ababab;
@textbox-border-focus:           #59a5e1;

// Selectbox
@selectbox-bg:                   @textbox-bg;
@selectbox-border:               @textbox-border;

// Throbber
@throbber-bg:                    #fff url('img/loader.gif') no-repeat center center;

// Combobox
@combobox-border:                @textbox-border;
@combobox-error-text:            @text-error;
@combobox-warning-text:          @text-warning;
@combobox-success-text:          @text-success;

// Colorpicker
@colorpicker-border:             @textbox-border;
@colorpicker-hue-bg:             #fff;
@colorpicker-hue-border:         #333;

// Grid
@grid-bg-active:                 @menuitem-bg-active;
@grid-border-active:             #d6d6d6;
@grid-border:                    #9c2525;

// Misc
@colorbtn-backcolor-bg:          #bbbbbb;
@iframe-border:                  @panel-border;

// Slider
@slider-border:                  #aaaaaa;
@slider-bg:                      #eeeeee;
@slider-handle-border:           #bbbbbb;
@slider-handle-bg:               #dddddd;
@slider-handle-bg-focus:         #BBB;

// Progress
@progress-border:                #cccccc;
@progress-bar-bg:                #dfdfdf;
@progress-bar-bg-hlight:         #cccccc;
@progress-text:                  #333333;

// Flow layout
@flow-layout-spacing:            2px;

// Table

@table-row-even:                  #fafafa;
@table-row-hover:                 darken(@table-row-even, 10%);
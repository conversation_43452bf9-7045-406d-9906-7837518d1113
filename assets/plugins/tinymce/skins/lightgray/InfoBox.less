// InfoBox

.@{prefix}-infobox {
	.inline-block();
	text-shadow: @text-shadow;
	overflow: hidden;
  border: 1px solid red;

  div {
    display: block;
    margin: 5px;

    button {
      position: absolute;
      top: 50%; right: 4px;
      cursor: pointer;
      margin-top: -8px;
      display: none;
    }

		button:focus {
      outline: 2px solid @btn-border-hover;
    }
  }
}

.@{prefix}-infobox.@{prefix}-has-help {
  div {
    margin-right: 25px;
  }

  button {
    display: block;
  }
}

.@{prefix}-infobox.@{prefix}-success {
  background: @infobox-success-bg;
  border-color: @infobox-success-border;

  div {
    color: @infobox-success-text;
  }
}

.@{prefix}-infobox.@{prefix}-warning {
  background: @infobox-warning-bg;
  border-color: @infobox-warning-border;

  div {
    color: @infobox-warning-text;
  }
}

.@{prefix}-infobox.@{prefix}-error {
  background: @infobox-error-bg;
  border-color: @infobox-error-border;

  div {
    color: @infobox-error-text;
  }
}

// RTL

.@{prefix}-rtl .@{prefix}-infobox {
  div {
  	text-align: right;
  	direction: rtl;
  }
}

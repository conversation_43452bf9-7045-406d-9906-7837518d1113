.@{prefix}-tinymce {
	// Avoid FOUC
	visibility: inherit !important;
	position: relative;
}

.@{prefix}-fullscreen {
	border: 0; padding: 0; margin: 0;
	overflow: hidden;
	height: 100%;
	z-index: 100;
}

div.@{prefix}-fullscreen {
	position: fixed;
	top: 0; left: 0;
	width: 100%;
	height: auto;
}

.@{prefix}-tinymce {
	display: block;
	.border-radius(2px);
}

.@{prefix}-wordcount {
	position: absolute;
	top: 0;
	right: 0;
	padding: 8px;
}

div.@{prefix}-edit-area {
	background: #FFF;
	filter: none;
}

.@{prefix}-statusbar {
	position: relative;
}

.@{prefix}-statusbar .@{prefix}-container-body {
	position: relative;
}

.@{prefix}-fullscreen .@{prefix}-resizehandle {
	display: none;
}

// Charmap

.@{prefix}-charmap {
	border-collapse: collapse;
}

.@{prefix}-charmap td {
	cursor: default;
	border: 1px solid @panel-border;
	width: 20px;
	height: 20px;
	line-height: 20px;
	text-align: center;
	vertical-align: middle;
	padding: 2px;
}

.@{prefix}-charmap td div {
	text-align: center;
}

.@{prefix}-charmap td:hover {
	background: @btn-bg-hlight;
}

.@{prefix}-grid td.@{prefix}-grid-cell div {
	border: 1px solid @grid-border;
	width: 15px; height: 15px;
	margin: 0px;
	cursor: pointer;

	&:focus {
		border-color: @grid-border-active;
	}

	&[disabled] {
		cursor: not-allowed;
	}
}

.@{prefix}-grid {
	border-spacing: 2px;
	border-collapse: separate;

	a {
		display: block;
		border: 1px solid transparent;

		&:hover, &:focus {
			border-color: @grid-border-active;
		}
	}
}

.@{prefix}-grid-border {
	margin: 0 4px 0 4px;

	a {
		border-color: @grid-border;
		width: 13px; height: 13px;
	}

	a:hover, a.@{prefix}-active {
		border-color: @grid-border-active;
		background: @grid-bg-active;
	}
}

.@{prefix}-text-center {
	text-align: center;
}

div.@{prefix}-tinymce-inline {
	width: 100%;
	.box-shadow(none);
}

.@{prefix}-colorbtn-trans div {
	text-align: center;
	vertical-align: middle;
	font-weight: bold;
	font-size: 20px;
	line-height: 16px;
	color: mix(@text, #fff, 70%);
}

.@{prefix}-monospace {
	font-family: "Courier New", Courier, monospace;
}

// Reduce double margins between toolbar rows

.@{prefix}-toolbar-grp when (@has-button-borders = false) {
	padding: @flow-layout-spacing 0;
}

.@{prefix}-toolbar-grp when (@has-button-borders = true) {
	padding-bottom: 2px;
}

.@{prefix}-toolbar-grp .@{prefix}-flow-layout-item {
	margin-bottom: 0;
}

// RTL

.@{prefix}-rtl .@{prefix}-wordcount {
	left: 0;
	right: auto;
}

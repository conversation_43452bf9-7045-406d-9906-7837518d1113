// Label

.@{prefix}-label {
	.inline-block();
	text-shadow: @text-shadow;
	overflow: hidden;
}

.@{prefix}-label.@{prefix}-autoscroll {
	overflow: auto;
}

.@{prefix}-label.@{prefix}-disabled {
	color: @text-disabled;
}

.@{prefix}-label.@{prefix}-multiline {
	white-space: pre-wrap;
}

.@{prefix}-label.@{prefix}-success {
	color: @text-success;
}

.@{prefix}-label.@{prefix}-warning {
	color: @text-warning;
}

.@{prefix}-label.@{prefix}-error {
	color: @text-error;
}

// RTL

.@{prefix}-rtl .@{prefix}-label {
	text-align: right;
	direction: rtl;
}

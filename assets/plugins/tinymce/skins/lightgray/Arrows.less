// Arrows

.@{prefix}-arrow-up {
	margin-top: 12px;
}

.@{prefix}-arrow-down {
	margin-top: -12px;
}

.@{prefix}-arrow:before,
.@{prefix}-arrow:after {
	position: absolute;
	left: 50%;
	display: block;
	width: 0;
	height: 0;
	border-style: solid;
	border-color: transparent;
	content: "";
}

.@{prefix}-arrow.@{prefix}-arrow-up:before {
	top: -9px;
	border-bottom-color: @panel-border;
	border-width: 0 9px 9px;
	margin-left: -9px;
}

.@{prefix}-arrow.@{prefix}-arrow-down:before {
	bottom: -9px;
	border-top-color: @panel-border;
	border-width: 9px 9px 0;
	margin-left: -9px;
}

.@{prefix}-arrow.@{prefix}-arrow-up:after {
	top: -8px;
	border-bottom-color: mix(@panel-bg, @panel-bg-hlight, 60%);
	border-width: 0 8px 8px;
	margin-left: -8px;
}

.@{prefix}-arrow.@{prefix}-arrow-down:after {
	bottom: -8px;
	border-top-color: mix(@panel-bg, @panel-bg-hlight, 60%);
	border-width: 8px 8px 0;
	margin-left: -8px;
}

.@{prefix}-arrow.@{prefix}-arrow-left:before,
.@{prefix}-arrow.@{prefix}-arrow-left:after {
	margin: 0;
}

.@{prefix}-arrow.@{prefix}-arrow-left:before {
	left: 8px;
}
.@{prefix}-arrow.@{prefix}-arrow-left:after {
	left: 9px;
}

.@{prefix}-arrow.@{prefix}-arrow-right:before,
.@{prefix}-arrow.@{prefix}-arrow-right:after {
	left: auto;
	margin: 0;
}

.@{prefix}-arrow.@{prefix}-arrow-right:before {
	right: 8px;
}

.@{prefix}-arrow.@{prefix}-arrow-right:after {
	right: 9px;
}

.@{prefix}-arrow.@{prefix}-arrow-center.@{prefix}-arrow.@{prefix}-arrow-left:before {
	left: -9px;
	top: 50%;
	border-right-color: @panel-border;
	border-width: 9px 9px 9px 0;
	margin-top: -9px;
}

.@{prefix}-arrow.@{prefix}-arrow-center.@{prefix}-arrow.@{prefix}-arrow-left:after {
	left: -8px;
	top: 50%;
	border-right-color: mix(@panel-bg, @panel-bg-hlight, 60%);
	border-width: 8px 8px 8px 0;
	margin-top: -8px;
}

.@{prefix}-arrow.@{prefix}-arrow-center.@{prefix}-arrow.@{prefix}-arrow-left {
	margin-left: 12px;
}

.@{prefix}-arrow.@{prefix}-arrow-center.@{prefix}-arrow.@{prefix}-arrow-right:before {
	right: -9px;
	top: 50%;
	border-left-color: @panel-border;
	border-width: 9px 0 9px 9px;
	margin-top: -9px;
}

.@{prefix}-arrow.@{prefix}-arrow-center.@{prefix}-arrow.@{prefix}-arrow-right:after {
	right: -8px;
	top: 50%;
	border-left-color: mix(@panel-bg, @panel-bg-hlight, 60%);
	border-width: 8px 0 8px 8px;
	margin-top: -8px;
}

.@{prefix}-arrow.@{prefix}-arrow-center.@{prefix}-arrow.@{prefix}-arrow-right {
	margin-left: -14px;
}

!function(u){"use strict";var e,n,t,r,o,i,s,a,c,f=tinymce.util.Tools.resolve("tinymce.PluginManager"),d=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),l=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),m=tinymce.util.Tools.resolve("tinymce.util.VK"),p=tinymce.util.Tools.resolve("tinymce.dom.BookmarkManager"),v=tinymce.util.Tools.resolve("tinymce.util.Tools"),g=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),h=function(e){return e&&"BR"===e.nodeName},y=function(e){return e&&3===e.nodeType},N=function(e){return e&&/^(OL|UL|DL)$/.test(e.nodeName)},S=function(e){return e&&/^(OL|UL)$/.test(e.nodeName)},C=function(e){return e&&/^(DT|DD)$/.test(e.nodeName)},O=function(e){return e&&/^(LI|DT|DD)$/.test(e.nodeName)},b=function(e){return e&&/^(TH|TD)$/.test(e.nodeName)},T=h,E=function(e,n){return n&&!!e.schema.getTextBlockElements()[n.nodeName]},L=function(e,n){return e&&e.nodeName in n},D=function(e,n){return!!h(n)&&!(!e.isBlock(n.nextSibling)||h(n.previousSibling))},w=function(e,n,t){var r=e.isEmpty(n);return!(t&&0<e.select("span[data-mce-type=bookmark]",n).length)&&r},k=function(e,n){return e.isChildOf(n,e.getRoot())},A=function(e,n){if(y(e))return{container:e,offset:n};var t=d.getNode(e,n);return y(t)?{container:t,offset:n>=e.childNodes.length?t.data.length:0}:t.previousSibling&&y(t.previousSibling)?{container:t.previousSibling,offset:t.previousSibling.data.length}:t.nextSibling&&y(t.nextSibling)?{container:t.nextSibling,offset:0}:{container:e,offset:n}},x=function(e){var n=e.cloneRange(),t=A(e.startContainer,e.startOffset);n.setStart(t.container,t.offset);var r=A(e.endContainer,e.endOffset);return n.setEnd(r.container,r.offset),n},R=g.DOM,I=function(o){var i={},e=function(e){var n,t,r;t=o[e?"startContainer":"endContainer"],r=o[e?"startOffset":"endOffset"],1===t.nodeType&&(n=R.create("span",{"data-mce-type":"bookmark"}),t.hasChildNodes()?(r=Math.min(r,t.childNodes.length-1),e?t.insertBefore(n,t.childNodes[r]):R.insertAfter(n,t.childNodes[r])):t.appendChild(n),t=n,r=0),i[e?"startContainer":"endContainer"]=t,i[e?"startOffset":"endOffset"]=r};return e(!0),o.collapsed||e(),i},_=function(o){function e(e){var n,t,r;n=r=o[e?"startContainer":"endContainer"],t=o[e?"startOffset":"endOffset"],n&&(1===n.nodeType&&(t=function(e){for(var n=e.parentNode.firstChild,t=0;n;){if(n===e)return t;1===n.nodeType&&"bookmark"===n.getAttribute("data-mce-type")||t++,n=n.nextSibling}return-1}(n),n=n.parentNode,R.remove(r),!n.hasChildNodes()&&R.isBlock(n)&&n.appendChild(R.create("br"))),o[e?"startContainer":"endContainer"]=n,o[e?"startOffset":"endOffset"]=t)}e(!0),e();var n=R.createRng();return n.setStart(o.startContainer,o.startOffset),o.endContainer&&n.setEnd(o.endContainer,o.endOffset),x(n)},B=function(){},P=function(e){return function(){return e}},M=function(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return!t.apply(null,e)}},U=P(!1),F=P(!0),j=function(){return H},H=(e=function(e){return e.isNone()},r={fold:function(e,n){return e()},is:U,isSome:U,isNone:F,getOr:t=function(e){return e},getOrThunk:n=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:P(null),getOrUndefined:P(undefined),or:t,orThunk:n,map:j,each:B,bind:j,exists:U,forall:F,filter:j,equals:e,equals_:e,toArray:function(){return[]},toString:P("none()")},Object.freeze&&Object.freeze(r),r),$=function(t){var e=P(t),n=function(){return o},r=function(e){return e(t)},o={fold:function(e,n){return n(t)},is:function(e){return t===e},isSome:F,isNone:U,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:n,orThunk:n,map:function(e){return $(e(t))},each:function(e){e(t)},bind:r,exists:r,forall:r,filter:function(e){return e(t)?o:H},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(e){return e.is(t)},equals_:function(e,n){return e.fold(U,function(e){return n(t,e)})}};return o},q={some:$,none:j,from:function(e){return null===e||e===undefined?H:$(e)}},W=function(n){return function(e){return function(e){if(null===e)return"null";var n=typeof e;return"object"===n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n}(e)===n}},V=W("string"),z=W("array"),K=W("boolean"),X=W("function"),Q=W("number"),Y=Array.prototype.slice,G=Array.prototype.push,J=function(e,n){for(var t=e.length,r=new Array(t),o=0;o<t;o++){var i=e[o];r[o]=n(i,o)}return r},Z=function(e,n){for(var t=0,r=e.length;t<r;t++)n(e[t],t)},ee=function(e,n){for(var t=[],r=0,o=e.length;r<o;r++){var i=e[r];n(i,r)&&t.push(i)}return t},ne=function(e,n,t){return Z(e,function(e){t=n(t,e)}),t},te=function(e,n){for(var t=0,r=e.length;t<r;t++){var o=e[t];if(n(o,t))return q.some(o)}return q.none()},re=function(e,n){return function(e){for(var n=[],t=0,r=e.length;t<r;++t){if(!z(e[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+e);G.apply(n,e[t])}return n}(J(e,n))},oe=function(e){return 0===e.length?q.none():q.some(e[0])},ie=function(e){return 0===e.length?q.none():q.some(e[e.length-1])},ue=(X(Array.from)&&Array.from,"undefined"!=typeof u.window?u.window:Function("return this;")()),se=function(e,n){return function(e,n){for(var t=n!==undefined&&null!==n?n:ue,r=0;r<e.length&&t!==undefined&&null!==t;++r)t=t[e[r]];return t}(e.split("."),n)},ae=function(e,n){var t=se(e,n);if(t===undefined||null===t)throw new Error(e+" not available on this browser");return t},ce=function(e){var n,t=se("ownerDocument.defaultView",e);return(n=t,ae("HTMLElement",n)).prototype.isPrototypeOf(e)},fe=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),de=function(e){var n=e.selection.getStart(!0);return e.dom.getParent(n,"OL,UL,DL",me(e,n))},le=function(e){var t,n,r,o=e.selection.getSelectedBlocks();return v.grep((t=e,n=o,r=v.map(n,function(e){var n=t.dom.getParent(e,"li,dd,dt",me(t,e));return n||e}),fe.unique(r)),function(e){return O(e)})},me=function(e,n){var t=e.dom.getParents(n,"TD,TH");return 0<t.length?t[0]:e.getBody()},ge=function(e,n){var t=e.dom.getParents(n,"ol,ul",me(e,n));return ie(t)},pe=function(n,e){var t=J(e,function(e){return ge(n,e).getOr(e)});return fe.unique(t)},ve={isList:function(e){var n=de(e);return ce(n)},getParentList:de,getSelectedSubLists:function(e){var n,t,r,o=de(e),i=e.selection.getSelectedBlocks();return r=i,(t=o)&&1===r.length&&r[0]===t?(n=o,v.grep(n.querySelectorAll("ol,ul,dl"),function(e){return N(e)})):v.grep(i,function(e){return N(e)&&o!==e})},getSelectedListItems:le,getClosestListRootElm:me,getSelectedDlItems:function(e){return ee(le(e),C)},getSelectedListRoots:function(e){var n,t,r,o=(t=ge(n=e,n.selection.getStart()),r=ee(n.selection.getSelectedBlocks(),S),t.toArray().concat(r));return pe(e,o)}},he=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:P(e)}},ye={fromHtml:function(e,n){var t=(n||u.document).createElement("div");if(t.innerHTML=e,!t.hasChildNodes()||1<t.childNodes.length)throw u.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return he(t.childNodes[0])},fromTag:function(e,n){var t=(n||u.document).createElement(e);return he(t)},fromText:function(e,n){var t=(n||u.document).createTextNode(e);return he(t)},fromDom:he,fromPoint:function(e,n,t){var r=e.dom();return q.from(r.elementFromPoint(n,t)).map(he)}},Ne=function(e,n,t){return e.isSome()&&n.isSome()?q.some(t(e.getOrDie(),n.getOrDie())):q.none()},Se=Object.keys,Ce=function(){return ae("Node")},Oe=function(e,n,t){return 0!=(e.compareDocumentPosition(n)&t)},be=function(e,n){return Oe(e,n,Ce().DOCUMENT_POSITION_CONTAINED_BY)},Te=function(e,n){var t=function(e,n){for(var t=0;t<e.length;t++){var r=e[t];if(r.test(n))return r}return undefined}(e,n);if(!t)return{major:0,minor:0};var r=function(e){return Number(n.replace(t,"$"+e))};return Le(r(1),r(2))},Ee=function(){return Le(0,0)},Le=function(e,n){return{major:e,minor:n}},De={nu:Le,detect:function(e,n){var t=String(n).toLowerCase();return 0===e.length?Ee():Te(e,t)},unknown:Ee},we="Firefox",ke=function(e,n){return function(){return n===e}},Ae=function(e){var n=e.current;return{current:n,version:e.version,isEdge:ke("Edge",n),isChrome:ke("Chrome",n),isIE:ke("IE",n),isOpera:ke("Opera",n),isFirefox:ke(we,n),isSafari:ke("Safari",n)}},xe={unknown:function(){return Ae({current:undefined,version:De.unknown()})},nu:Ae,edge:P("Edge"),chrome:P("Chrome"),ie:P("IE"),opera:P("Opera"),firefox:P(we),safari:P("Safari")},Re="Windows",Ie="Android",_e="Solaris",Be="FreeBSD",Pe=function(e,n){return function(){return n===e}},Me=function(e){var n=e.current;return{current:n,version:e.version,isWindows:Pe(Re,n),isiOS:Pe("iOS",n),isAndroid:Pe(Ie,n),isOSX:Pe("OSX",n),isLinux:Pe("Linux",n),isSolaris:Pe(_e,n),isFreeBSD:Pe(Be,n)}},Ue={unknown:function(){return Me({current:undefined,version:De.unknown()})},nu:Me,windows:P(Re),ios:P("iOS"),android:P(Ie),linux:P("Linux"),osx:P("OSX"),solaris:P(_e),freebsd:P(Be)},Fe=function(e,n){var t=String(n).toLowerCase();return te(e,function(e){return e.search(t)})},je=function(e,t){return Fe(e,t).map(function(e){var n=De.detect(e.versionRegexes,t);return{current:e.name,version:n}})},He=function(e,t){return Fe(e,t).map(function(e){var n=De.detect(e.versionRegexes,t);return{current:e.name,version:n}})},$e=function(e,n){return-1!==e.indexOf(n)},qe=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,We=function(n){return function(e){return $e(e,n)}},Ve=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return $e(e,"edge/")&&$e(e,"chrome")&&$e(e,"safari")&&$e(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,qe],search:function(e){return $e(e,"chrome")&&!$e(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return $e(e,"msie")||$e(e,"trident")}},{name:"Opera",versionRegexes:[qe,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:We("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:We("firefox")},{name:"Safari",versionRegexes:[qe,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return($e(e,"safari")||$e(e,"mobile/"))&&$e(e,"applewebkit")}}],ze=[{name:"Windows",search:We("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return $e(e,"iphone")||$e(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:We("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:We("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:We("linux"),versionRegexes:[]},{name:"Solaris",search:We("sunos"),versionRegexes:[]},{name:"FreeBSD",search:We("freebsd"),versionRegexes:[]}],Ke={browsers:P(Ve),oses:P(ze)},Xe=function(e){var n,t,r,o,i,u,s,a,c,f,d,l=Ke.browsers(),m=Ke.oses(),g=je(l,e).fold(xe.unknown,xe.nu),p=He(m,e).fold(Ue.unknown,Ue.nu);return{browser:g,os:p,deviceType:(t=g,r=e,o=(n=p).isiOS()&&!0===/ipad/i.test(r),i=n.isiOS()&&!o,u=n.isAndroid()&&3===n.version.major,s=n.isAndroid()&&4===n.version.major,a=o||u||s&&!0===/mobile/i.test(r),c=n.isiOS()||n.isAndroid(),f=c&&!a,d=t.isSafari()&&n.isiOS()&&!1===/safari/i.test(r),{isiPad:P(o),isiPhone:P(i),isTablet:P(a),isPhone:P(f),isTouch:P(c),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:P(d)})}},Qe={detect:(o=function(){var e=u.navigator.userAgent;return Xe(e)},s=!1,function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return s||(s=!0,i=o.apply(null,e)),i})},Ye=(u.Node.ATTRIBUTE_NODE,u.Node.CDATA_SECTION_NODE,u.Node.COMMENT_NODE,u.Node.DOCUMENT_NODE,u.Node.DOCUMENT_TYPE_NODE,u.Node.DOCUMENT_FRAGMENT_NODE,u.Node.ELEMENT_NODE),Ge=(u.Node.TEXT_NODE,u.Node.PROCESSING_INSTRUCTION_NODE,u.Node.ENTITY_REFERENCE_NODE,u.Node.ENTITY_NODE,u.Node.NOTATION_NODE,Ye),Je=function(e,n){return e.dom()===n.dom()},Ze=Qe.detect().browser.isIE()?function(e,n){return be(e.dom(),n.dom())}:function(e,n){var t=e.dom(),r=n.dom();return t!==r&&t.contains(r)},en=function(e,n){var t=e.dom();if(t.nodeType!==Ge)return!1;var r=t;if(r.matches!==undefined)return r.matches(n);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(n);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(n);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")},nn=function(e){return q.from(e.dom().parentNode).map(ye.fromDom)},tn=function(e){return J(e.dom().childNodes,ye.fromDom)},rn=function(e,n){var t=e.dom().childNodes;return q.from(t[n]).map(ye.fromDom)},on=function(e){return rn(e,0)},un=function(e){return rn(e,e.dom().childNodes.length-1)},sn=(function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n]}("element","offset"),function(n,t){nn(n).each(function(e){e.dom().insertBefore(t.dom(),n.dom())})}),an=function(e,n){e.dom().appendChild(n.dom())},cn=function(n,e){Z(e,function(e){an(n,e)})},fn=function(e){var n=e.dom();null!==n.parentNode&&n.parentNode.removeChild(n)},dn=function(e){return e.dom().nodeName.toLowerCase()},ln=(a=Ye,function(e){return e.dom().nodeType===a}),mn=function(e,n){var t=e.dom();!function(e,n){for(var t=Se(e),r=0,o=t.length;r<o;r++){var i=t[r];n(e[i],i)}}(n,function(e,n){!function(e,n,t){if(!(V(t)||K(t)||Q(t)))throw u.console.error("Invalid call to Attr.set. Key ",n,":: Value ",t,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(n,t+"")}(t,n,e)})},gn=function(e){return ne(e.dom().attributes,function(e,n){return e[n.name]=n.value,e},{})},pn=function(e,n,t){if(!V(t))throw u.console.error("Invalid call to CSS.set. Property ",n,":: Value ",t,":: Element ",e),new Error("CSS value must be a string: "+t);var r;(r=e).style!==undefined&&X(r.style.getPropertyValue)&&e.style.setProperty(n,t)},vn=function(e){return n=e,t=!0,ye.fromDom(n.dom().cloneNode(t));var n,t},hn=function(e,n){var t,r,o,i,u=(t=e,r=n,o=ye.fromTag(r),i=gn(t),mn(o,i),o);sn(e,u);var s=tn(e);return cn(u,s),fn(e),u},yn=function(e,n){an(e.item,n.list)},Nn=function(f,e,d){var n=e.slice(0,d.depth);return ie(n).each(function(e){var n,t,r,o,i,u,s,a,c=(n=f,t=d.itemAttributes,r=d.content,o=ye.fromTag("li",n),mn(o,t),cn(o,r),o);u=c,an((i=e).list,u),i.item=u,a=d,dn((s=e).list)!==a.listType&&(s.list=hn(s.list,a.listType)),mn(s.list,a.listAttributes)}),n},Sn=function(e,n,t){var r,o=function(e,n,t){for(var r,o,i,u=[],s=0;s<t;s++)u.push((r=e,o=n.listType,i={list:ye.fromTag(o,r),item:ye.fromTag("li",r)},an(i.list,i.item),i));return u}(e,t,t.depth-n.length);return function(e){for(var n=1;n<e.length;n++)yn(e[n-1],e[n])}(o),function(e,n){for(var t=0;t<e.length-1;t++)r=e[t].item,o="list-style-type",i="none",u=r.dom(),pn(u,o,i);var r,o,i,u;ie(e).each(function(e){mn(e.list,n.listAttributes),mn(e.item,n.itemAttributes),cn(e.item,n.content)})}(o,t),r=o,Ne(ie(n),oe(r),yn),n.concat(o)},Cn=function(e){return en(e,"OL,UL")},On=function(e){return on(e).map(Cn).getOr(!1)},bn=function(e){return 0<e.depth},Tn=function(e){return e.isSelected},En=function(e){var n=tn(e),t=un(e).map(Cn).getOr(!1)?n.slice(0,-1):n;return J(t,vn)},Ln=Object.prototype.hasOwnProperty,Dn=(c=function(e,n){return n},function(){for(var e=new Array(arguments.length),n=0;n<e.length;n++)e[n]=arguments[n];if(0===e.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<e.length;r++){var o=e[r];for(var i in o)Ln.call(o,i)&&(t[i]=c(t[i],o[i]))}return t}),wn=function(n){Z(n,function(r,e){(function(e,n){for(var t=e[n].depth,r=n-1;0<=r;r--){if(e[r].depth===t)return q.some(e[r]);if(e[r].depth<t)break}return q.none()})(n,e).each(function(e){var n,t;t=e,(n=r).listType=t.listType,n.listAttributes=Dn({},t.listAttributes)})})},kn=function(e){var n=e,t=function(){return n};return{get:t,set:function(e){n=e},clone:function(){return kn(t())}}},An=function(i,u,s,a){return on(a).filter(Cn).fold(function(){u.each(function(e){Je(e.start,a)&&s.set(!0)});var n,t,r,e=(n=a,t=i,r=s.get(),nn(n).filter(ln).map(function(e){return{depth:t,isSelected:r,content:En(n),itemAttributes:gn(n),listAttributes:gn(e),listType:dn(e)}}));u.each(function(e){Je(e.end,a)&&s.set(!1)});var o=un(a).filter(Cn).map(function(e){return xn(i,u,s,e)}).getOr([]);return e.toArray().concat(o)},function(e){return xn(i,u,s,e)})},xn=function(n,t,r,e){return re(tn(e),function(e){return(Cn(e)?xn:An)(n+1,t,r,e)})},Rn=tinymce.util.Tools.resolve("tinymce.Env"),In=function(e,n){var t,r,o,i,u=e.dom,s=e.schema.getBlockElements(),a=u.createFragment();if(e.settings.forced_root_block&&(o=e.settings.forced_root_block),o&&((r=u.create(o)).tagName===e.settings.forced_root_block&&u.setAttribs(r,e.settings.forced_root_block_attrs),L(n.firstChild,s)||a.appendChild(r)),n)for(;t=n.firstChild;){var c=t.nodeName;i||"SPAN"===c&&"bookmark"===t.getAttribute("data-mce-type")||(i=!0),L(t,s)?(a.appendChild(t),r=null):o?(r||(r=u.create(o),a.appendChild(r)),r.appendChild(t)):a.appendChild(t)}return e.settings.forced_root_block?i||Rn.ie&&!(10<Rn.ie)||r.appendChild(u.create("br",{"data-mce-bogus":"1"})):a.appendChild(u.create("br")),a},_n=function(i,e){return J(e,function(e){var n,t,r,o=(n=e.content,r=(t||u.document).createDocumentFragment(),Z(n,function(e){r.appendChild(e.dom())}),ye.fromDom(r));return ye.fromDom(In(i,o.dom()))})},Bn=function(e,n){return wn(n),(t=e.contentDocument,r=n,o=ne(r,function(e,n){return n.depth>e.length?Sn(t,e,n):Nn(t,e,n)},[]),oe(o).map(function(e){return e.list})).toArray();var t,r,o},Pn=function(e){var n,t,r=J(ve.getSelectedListItems(e),ye.fromDom);return Ne(te(r,M(On)),te((n=r,(t=Y.call(n,0)).reverse(),t),M(On)),function(e,n){return{start:e,end:n}})},Mn=function(s,e,a){var n,t,r,o=(n=e,t=Pn(s),r=kn(!1),J(n,function(e){return{sourceList:e,entries:xn(0,t,r,e)}}));Z(o,function(e){var n,t,r,o,i,u;n=e.entries,t=a,Z(ee(n,Tn),function(e){return function(e,n){switch(e){case"Indent":n.depth++;break;case"Outdent":n.depth--;break;case"Flatten":n.depth=0}}(t,e)}),r=e.sourceList,i=s,u=e.entries,o=re(function(e,n){if(0===e.length)return[];for(var t=n(e[0]),r=[],o=[],i=0,u=e.length;i<u;i++){var s=e[i],a=n(s);a!==t&&(r.push(o),o=[]),t=a,o.push(s)}return 0!==o.length&&r.push(o),r}(u,bn),function(e){return oe(e).map(bn).getOr(!1)?Bn(i,e):_n(i,e)}),Z(o,function(e){sn(r,e)}),fn(e.sourceList)})},Un=g.DOM,Fn=function(e,n,t){var r,o,i,u,s,a;for(i=Un.select('span[data-mce-type="bookmark"]',n),s=In(e,t),(r=Un.createRng()).setStartAfter(t),r.setEndAfter(n),u=(o=r.extractContents()).firstChild;u;u=u.firstChild)if("LI"===u.nodeName&&e.dom.isEmpty(u)){Un.remove(u);break}e.dom.isEmpty(o)||Un.insertAfter(o,n),Un.insertAfter(s,n),w(e.dom,t.parentNode)&&(a=t.parentNode,v.each(i,function(e){a.parentNode.insertBefore(e,t.parentNode)}),Un.remove(a)),Un.remove(t),w(e.dom,n)&&Un.remove(n)},jn=function(e){en(e,"dt")&&hn(e,"dd")},Hn=function(r,e,n){Z(n,"Indent"===e?jn:function(e){return n=r,void(en(t=e,"dd")?hn(t,"dt"):en(t,"dt")&&nn(t).each(function(e){return Fn(n,e.dom(),t.dom())}));var n,t})},$n=function(e,n){var t=J(ve.getSelectedListRoots(e),ye.fromDom),r=J(ve.getSelectedDlItems(e),ye.fromDom),o=!1;if(t.length||r.length){var i=e.selection.getBookmark();Mn(e,t,n),Hn(e,n,r),e.selection.moveToBookmark(i),e.selection.setRng(x(e.selection.getRng())),e.nodeChanged(),o=!0}return o},qn=function(e){return $n(e,"Indent")},Wn=function(e){return $n(e,"Outdent")},Vn=function(e){return $n(e,"Flatten")},zn=function(t,e){v.each(e,function(e,n){t.setAttribute(n,e)})},Kn=function(e,n,t){var r,o,i,u,s,a,c;r=e,o=n,u=(i=t)["list-style-type"]?i["list-style-type"]:null,r.setStyle(o,"list-style-type",u),s=e,zn(a=n,(c=t)["list-attributes"]),v.each(s.select("li",a),function(e){zn(e,c["list-item-attributes"])})},Xn=function(e,n,t,r){var o,i;for(o=n[t?"startContainer":"endContainer"],i=n[t?"startOffset":"endOffset"],1===o.nodeType&&(o=o.childNodes[Math.min(i,o.childNodes.length-1)]||o),!t&&T(o.nextSibling)&&(o=o.nextSibling);o.parentNode!==r;){if(E(e,o))return o;if(/^(TD|TH)$/.test(o.parentNode.nodeName))return o;o=o.parentNode}return o},Qn=function(f,d,l){void 0===l&&(l={});var e,n=f.selection.getRng(!0),m="LI",t=ve.getClosestListRootElm(f,f.selection.getStart(!0)),g=f.dom;"false"!==g.getContentEditable(f.selection.getNode())&&("DL"===(d=d.toUpperCase())&&(m="DT"),e=I(n),v.each(function(t,e,r){for(var o,i=[],u=t.dom,n=Xn(t,e,!0,r),s=Xn(t,e,!1,r),a=[],c=n;c&&(a.push(c),c!==s);c=c.nextSibling);return v.each(a,function(e){if(E(t,e))return i.push(e),void(o=null);if(u.isBlock(e)||T(e))return T(e)&&u.remove(e),void(o=null);var n=e.nextSibling;p.isBookmarkNode(e)&&(E(t,n)||!n&&e.parentNode===r)?o=null:(o||(o=u.create("p"),e.parentNode.insertBefore(o,e),i.push(o)),o.appendChild(e))}),i}(f,n,t),function(e){var n,t,r,o,i,u,s,a,c;(t=e.previousSibling)&&N(t)&&t.nodeName===d&&(r=t,o=l,i=g.getStyle(r,"list-style-type"),u=o?o["list-style-type"]:"",i===(u=null===u?"":u))?(n=t,e=g.rename(e,m),t.appendChild(e)):(n=g.create(d),e.parentNode.insertBefore(n,e),n.appendChild(e),e=g.rename(e,m)),s=g,a=e,c=["margin","margin-right","margin-bottom","margin-left","margin-top","padding","padding-right","padding-bottom","padding-left","padding-top"],v.each(c,function(e){var n;return s.setStyle(a,((n={})[e]="",n))}),Kn(g,n,l),Gn(f.dom,n)}),f.selection.setRng(_(e)))},Yn=function(e,n,t){return a=t,(s=n)&&a&&N(s)&&s.nodeName===a.nodeName&&(i=n,u=t,(o=e).getStyle(i,"list-style-type",!0)===o.getStyle(u,"list-style-type",!0))&&(r=t,n.className===r.className);var r,o,i,u,s,a},Gn=function(e,n){var t,r;if(t=n.nextSibling,Yn(e,n,t)){for(;r=t.firstChild;)n.appendChild(r);e.remove(t)}if(t=n.previousSibling,Yn(e,n,t)){for(;r=t.lastChild;)n.insertBefore(r,n.firstChild);e.remove(t)}},Jn=function(n,e,t,r,o){if(e.nodeName!==r||Zn(o)){var i=I(n.selection.getRng(!0));v.each([e].concat(t),function(e){!function(e,n,t,r){if(n.nodeName!==t){var o=e.rename(n,t);Kn(e,o,r)}else Kn(e,n,r)}(n.dom,e,r,o)}),n.selection.setRng(_(i))}else Vn(n)},Zn=function(e){return"list-style-type"in e},et={toggleList:function(e,n,t){var r=ve.getParentList(e),o=ve.getSelectedSubLists(e);t=t||{},r&&0<o.length?Jn(e,r,o,n,t):function(e,n,t,r){if(n!==e.getBody())if(n)if(n.nodeName!==t||Zn(r)){var o=I(e.selection.getRng(!0));Kn(e.dom,n,r),Gn(e.dom,e.dom.rename(n,t)),e.selection.setRng(_(o))}else Vn(e);else Qn(e,t,r)}(e,r,n,t)},mergeWithAdjacentLists:Gn},nt=g.DOM,tt=function(e,n){var t,r=n.parentNode;"LI"===r.nodeName&&r.firstChild===n&&((t=r.previousSibling)&&"LI"===t.nodeName?(t.appendChild(n),w(e,r)&&nt.remove(r)):nt.setStyle(r,"listStyleType","none")),N(r)&&(t=r.previousSibling)&&"LI"===t.nodeName&&t.appendChild(n)},rt=function(n,e){v.each(v.grep(n.select("ol,ul",e)),function(e){tt(n,e)})},ot=function(e,n,t,r){var o,i,u=n.startContainer,s=n.startOffset;if(3===u.nodeType&&(t?s<u.data.length:0<s))return u;for(o=e.schema.getNonEmptyElements(),1===u.nodeType&&(u=d.getNode(u,s)),i=new l(u,r),t&&D(e.dom,u)&&i.next();u=i[t?"next":"prev2"]();){if("LI"===u.nodeName&&!u.hasChildNodes())return u;if(o[u.nodeName])return u;if(3===u.nodeType&&0<u.data.length)return u}},it=function(e,n){var t=n.childNodes;return 1===t.length&&!N(t[0])&&e.isBlock(t[0])},ut=function(e,n,t){var r,o,i,u;if(o=it(e,t)?t.firstChild:t,it(i=e,u=n)&&i.remove(u.firstChild,!0),!w(e,n,!0))for(;r=n.firstChild;)o.appendChild(r)},st=function(n,e,t){var r,o,i=e.parentNode;if(k(n,e)&&k(n,t)){N(t.lastChild)&&(o=t.lastChild),i===t.lastChild&&T(i.previousSibling)&&n.remove(i.previousSibling),(r=t.lastChild)&&T(r)&&e.hasChildNodes()&&n.remove(r),w(n,t,!0)&&n.$(t).empty(),ut(n,e,t),o&&t.appendChild(o);var u=Ze(ye.fromDom(t),ye.fromDom(e))?n.getParents(e,N,t):[];n.remove(e),Z(u,function(e){w(n,e)&&e!==n.getRoot()&&n.remove(e)})}},at=function(e,n,t,r){var o,i,u,s=e.dom;if(s.isEmpty(r))i=t,u=r,(o=e).dom.$(u).empty(),st(o.dom,i,u),o.selection.setCursorLocation(u);else{var a=I(n);st(s,t,r),e.selection.setRng(_(a))}},ct=function(e,n){var t,r,o,i=e.dom,u=e.selection,s=u.getStart(),a=ve.getClosestListRootElm(e,s),c=i.getParent(u.getStart(),"LI",a);if(c){if((t=c.parentNode)===e.getBody()&&w(i,t))return!0;if(r=x(u.getRng(!0)),(o=i.getParent(ot(e,r,n,a),"LI",a))&&o!==c)return n?at(e,r,o,c):function(e,n,t,r){var o=I(n);st(e.dom,t,r);var i=_(o);e.selection.setRng(i)}(e,r,c,o),!0;if(!o&&!n)return Vn(e),!0}return!1},ft=function(e,n){return ct(e,n)||function(o,i){var u=o.dom,e=o.selection.getStart(),s=ve.getClosestListRootElm(o,e),a=u.getParent(e,u.isBlock,s);if(a&&u.isEmpty(a)){var n=x(o.selection.getRng(!0)),c=u.getParent(ot(o,n,i,s),"LI",s);if(c)return o.undoManager.transact(function(){var e,n,t,r;n=a,t=s,r=(e=u).getParent(n.parentNode,e.isBlock,t),e.remove(n),r&&e.isEmpty(r)&&e.remove(r),et.mergeWithAdjacentLists(u,c.parentNode),o.selection.select(c,!0),o.selection.collapse(i)}),!0}return!1}(e,n)},dt=function(e,n){return e.selection.isCollapsed()?ft(e,n):(r=(t=e).selection.getStart(),o=ve.getClosestListRootElm(t,r),!!(t.dom.getParent(r,"LI,DT,DD",o)||0<ve.getSelectedListItems(t).length)&&(t.undoManager.transact(function(){t.execCommand("Delete"),rt(t.dom,t.getBody())}),!0));var t,r,o},lt=function(n){n.on("keydown",function(e){e.keyCode===m.BACKSPACE?dt(n,!1)&&e.preventDefault():e.keyCode===m.DELETE&&dt(n,!0)&&e.preventDefault()})},mt=dt,gt=function(n){return{backspaceDelete:function(e){mt(n,e)}}},pt=function(n,t){return function(){var e=n.dom.getParent(n.selection.getStart(),"UL,OL,DL");return e&&e.nodeName===t}},vt=function(t){t.on("BeforeExecCommand",function(e){var n=e.command.toLowerCase();"indent"===n?qn(t):"outdent"===n&&Wn(t)}),t.addCommand("InsertUnorderedList",function(e,n){et.toggleList(t,"UL",n)}),t.addCommand("InsertOrderedList",function(e,n){et.toggleList(t,"OL",n)}),t.addCommand("InsertDefinitionList",function(e,n){et.toggleList(t,"DL",n)}),t.addCommand("RemoveList",function(){Vn(t)}),t.addQueryStateHandler("InsertUnorderedList",pt(t,"UL")),t.addQueryStateHandler("InsertOrderedList",pt(t,"OL")),t.addQueryStateHandler("InsertDefinitionList",pt(t,"DL"))},ht=function(e){return e.getParam("lists_indent_on_tab",!0)},yt=function(e){var n;ht(e)&&(n=e).on("keydown",function(e){e.keyCode!==m.TAB||m.metaKeyPressed(e)||n.undoManager.transact(function(){(e.shiftKey?Wn(n):qn(n))&&e.preventDefault()})}),lt(e)},Nt=function(n,i){return function(e){var o=e.control;n.on("NodeChange",function(e){var n=function(e,n){for(var t=0;t<e.length;t++)if(n(e[t]))return t;return-1}(e.parents,b),t=-1!==n?e.parents.slice(0,n):e.parents,r=v.grep(t,N);o.active(0<r.length&&r[0].nodeName===i)})}},St=function(e){var n,t,r;t="advlist",r=(n=e).settings.plugins?n.settings.plugins:"",-1===v.inArray(r.split(/[ ,]/),t)&&(e.addButton("numlist",{active:!1,title:"Numbered list",cmd:"InsertOrderedList",onPostRender:Nt(e,"OL")}),e.addButton("bullist",{active:!1,title:"Bullet list",cmd:"InsertUnorderedList",onPostRender:Nt(e,"UL")})),e.addButton("indent",{icon:"indent",title:"Increase indent",cmd:"Indent"})};f.add("lists",function(e){return yt(e),St(e),vt(e),gt(e)})}(window);
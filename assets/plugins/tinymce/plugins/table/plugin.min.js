!function(m){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),y=function(){},x=function(n,r){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(r.apply(null,e))}},C=function(e){return function(){return e}},o=function(e){return e};function b(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=o.concat(e);return r.apply(null,n)}}var t,n,r,i,g=function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return!n.apply(null,e)}},f=C(!1),u=C(!0),a=function(){return c},c=(t=function(e){return e.isNone()},i={fold:function(e,t){return e()},is:f,isSome:f,isNone:u,getOr:r=function(e){return e},getOrThunk:n=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:C(null),getOrUndefined:C(undefined),or:r,orThunk:n,map:a,each:y,bind:a,exists:f,forall:u,filter:a,equals:t,equals_:t,toArray:function(){return[]},toString:C("none()")},Object.freeze&&Object.freeze(i),i),l=function(n){var e=C(n),t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:u,isNone:f,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return l(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?o:c},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(f,function(e){return t(n,e)})}};return o},R={some:l,none:a,from:function(e){return null===e||e===undefined?c:l(e)}},s=function(t){return function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t}(e)===t}},d=s("string"),h=s("array"),p=s("boolean"),v=s("function"),w=s("number"),S=Array.prototype.slice,T=Array.prototype.indexOf,D=Array.prototype.push,O=function(e,t){return n=e,r=t,-1<T.call(n,r);var n,r},N=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n],n))return!0;return!1},E=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o)}return r},k=function(e,t){for(var n=0,r=e.length;n<r;n++)t(e[n],n)},A=function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r)&&n.push(i)}return n},P=function(e,t,n){return function(e,t){for(var n=e.length-1;0<=n;n--)t(e[n],n)}(e,function(e){n=t(n,e)}),n},I=function(e,t,n){return k(e,function(e){n=t(n,e)}),n},B=function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n))return R.some(o)}return R.none()},W=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n],n))return R.some(n);return R.none()},M=function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!h(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);D.apply(t,e[n])}return t},_=function(e,t){var n=E(e,t);return M(n)},L=function(e,t){for(var n=0,r=e.length;n<r;++n)if(!0!==t(e[n],n))return!1;return!0},F=function(e){var t=S.call(e,0);return t.reverse(),t},j=(v(Array.from)&&Array.from,Object.keys),z=function(e,t){for(var n=j(e),r=0,o=n.length;r<o;r++){var i=n[r];t(e[i],i)}},H=function(e,n){return U(e,function(e,t){return{k:t,v:n(e,t)}})},U=function(e,r){var o={};return z(e,function(e,t){var n=r(e,t);o[n.k]=n.v}),o},q=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(t.length!==n.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+n.length+" arguments");var r={};return k(t,function(e,t){r[e]=C(n[t])}),r}},V=function(e){return e.slice(0).sort()},G=function(e,t){throw new Error("All required keys ("+V(e).join(", ")+") were not specified. Specified keys were: "+V(t).join(", ")+".")},Y=function(e){throw new Error("Unsupported keys for object: "+V(e).join(", "))},X=function(t,e){if(!h(e))throw new Error("The "+t+" fields must be an array. Was: "+e+".");k(e,function(e){if(!d(e))throw new Error("The value "+e+" in the "+t+" fields was not a string.")})},K=function(e){var n=V(e);B(n,function(e,t){return t<n.length-1&&e===n[t+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+n.join(", ")+"].")})},J=function(o,i){var u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return X("required",o),X("optional",i),K(u),function(t){var n=j(t);L(o,function(e){return O(n,e)})||G(o,n);var e=A(n,function(e){return!O(u,e)});0<e.length&&Y(e);var r={};return k(o,function(e){r[e]=C(t[e])}),k(i,function(e){r[e]=C(Object.prototype.hasOwnProperty.call(t,e)?R.some(t[e]):R.none())}),r}},$=(m.Node.ATTRIBUTE_NODE,m.Node.CDATA_SECTION_NODE,m.Node.COMMENT_NODE),Q=m.Node.DOCUMENT_NODE,Z=(m.Node.DOCUMENT_TYPE_NODE,m.Node.DOCUMENT_FRAGMENT_NODE,m.Node.ELEMENT_NODE),ee=m.Node.TEXT_NODE,te=(m.Node.PROCESSING_INSTRUCTION_NODE,m.Node.ENTITY_REFERENCE_NODE,m.Node.ENTITY_NODE,m.Node.NOTATION_NODE,"undefined"!=typeof m.window?m.window:Function("return this;")()),ne=function(e,t){return function(e,t){for(var n=t!==undefined&&null!==t?t:te,r=0;r<e.length&&n!==undefined&&null!==n;++r)n=n[e[r]];return n}(e.split("."),t)},re=function(e,t){var n=ne(e,t);if(n===undefined||null===n)throw new Error(e+" not available on this browser");return n},oe=function(e){return e.dom().nodeName.toLowerCase()},ie=function(e){return e.dom().nodeType},ue=function(t){return function(e){return ie(e)===t}},ae=function(e){return ie(e)===$||"#comment"===oe(e)},ce=ue(Z),le=ue(ee),fe=function(e,t,n){if(!(d(n)||p(n)||w(n)))throw m.console.error("Invalid call to Attr.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},se=function(e,t,n){fe(e.dom(),t,n)},de=function(e,t){var n=e.dom();z(t,function(e,t){fe(n,t,e)})},me=function(e,t){var n=e.dom().getAttribute(t);return null===n?undefined:n},ge=function(e,t){var n=e.dom();return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},he=function(e,t){e.dom().removeAttribute(t)},pe=function(e){return I(e.dom().attributes,function(e,t){return e[t.name]=t.value,e},{})},ve=function(e,t){return-1!==e.indexOf(t)},be=function(e){return e.style!==undefined&&v(e.style.getPropertyValue)},we=function(n){var r,o=!1;return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o||(o=!0,r=n.apply(null,e)),r}},ye=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:C(e)}},xe={fromHtml:function(e,t){var n=(t||m.document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw m.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return ye(n.childNodes[0])},fromTag:function(e,t){var n=(t||m.document).createElement(e);return ye(n)},fromText:function(e,t){var n=(t||m.document).createTextNode(e);return ye(n)},fromDom:ye,fromPoint:function(e,t,n){var r=e.dom();return R.from(r.elementFromPoint(t,n)).map(ye)}},Ce=function(e){var t=le(e)?e.dom().parentNode:e.dom();return t!==undefined&&null!==t&&t.ownerDocument.body.contains(t)},Re=we(function(){return Se(xe.fromDom(m.document))}),Se=function(e){var t=e.dom().body;if(null===t||t===undefined)throw new Error("Body is not available yet");return xe.fromDom(t)},Te=function(e,t,n){if(!d(n))throw m.console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);be(e)&&e.style.setProperty(t,n)},De=function(e,t,n){var r=e.dom();Te(r,t,n)},Oe=function(e,t){var n=e.dom();z(t,function(e,t){Te(n,t,e)})},Ne=function(e,t){var n=e.dom(),r=m.window.getComputedStyle(n).getPropertyValue(t),o=""!==r||Ce(e)?r:Ee(n,t);return null===o?undefined:o},Ee=function(e,t){return be(e)?e.style.getPropertyValue(t):""},ke=function(e,t){var n=e.dom(),r=Ee(n,t);return R.from(r).filter(function(e){return 0<e.length})},Ae=function(e,t){var n,r,o=e.dom();r=t,be(n=o)&&n.style.removeProperty(r),ge(e,"style")&&""===me(e,"style").replace(/^\s+|\s+$/g,"")&&he(e,"style")},Pe=function(){return re("Node")},Ie=function(e,t,n){return 0!=(e.compareDocumentPosition(t)&n)},Be=function(e,t){return Ie(e,t,Pe().DOCUMENT_POSITION_CONTAINED_BY)},We=function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}return undefined}(e,t);if(!n)return{major:0,minor:0};var r=function(e){return Number(t.replace(n,"$"+e))};return _e(r(1),r(2))},Me=function(){return _e(0,0)},_e=function(e,t){return{major:e,minor:t}},Le={nu:_e,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?Me():We(e,n)},unknown:Me},Fe="Firefox",je=function(e,t){return function(){return t===e}},ze=function(e){var t=e.current;return{current:t,version:e.version,isEdge:je("Edge",t),isChrome:je("Chrome",t),isIE:je("IE",t),isOpera:je("Opera",t),isFirefox:je(Fe,t),isSafari:je("Safari",t)}},He={unknown:function(){return ze({current:undefined,version:Le.unknown()})},nu:ze,edge:C("Edge"),chrome:C("Chrome"),ie:C("IE"),opera:C("Opera"),firefox:C(Fe),safari:C("Safari")},Ue="Windows",qe="Android",Ve="Solaris",Ge="FreeBSD",Ye=function(e,t){return function(){return t===e}},Xe=function(e){var t=e.current;return{current:t,version:e.version,isWindows:Ye(Ue,t),isiOS:Ye("iOS",t),isAndroid:Ye(qe,t),isOSX:Ye("OSX",t),isLinux:Ye("Linux",t),isSolaris:Ye(Ve,t),isFreeBSD:Ye(Ge,t)}},Ke={unknown:function(){return Xe({current:undefined,version:Le.unknown()})},nu:Xe,windows:C(Ue),ios:C("iOS"),android:C(qe),linux:C("Linux"),osx:C("OSX"),solaris:C(Ve),freebsd:C(Ge)},Je=function(e,t){var n=String(t).toLowerCase();return B(e,function(e){return e.search(n)})},$e=function(e,n){return Je(e,n).map(function(e){var t=Le.detect(e.versionRegexes,n);return{current:e.name,version:t}})},Qe=function(e,n){return Je(e,n).map(function(e){var t=Le.detect(e.versionRegexes,n);return{current:e.name,version:t}})},Ze=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,et=function(t){return function(e){return ve(e,t)}},tt=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return ve(e,"edge/")&&ve(e,"chrome")&&ve(e,"safari")&&ve(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Ze],search:function(e){return ve(e,"chrome")&&!ve(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return ve(e,"msie")||ve(e,"trident")}},{name:"Opera",versionRegexes:[Ze,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:et("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:et("firefox")},{name:"Safari",versionRegexes:[Ze,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(ve(e,"safari")||ve(e,"mobile/"))&&ve(e,"applewebkit")}}],nt=[{name:"Windows",search:et("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return ve(e,"iphone")||ve(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:et("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:et("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:et("linux"),versionRegexes:[]},{name:"Solaris",search:et("sunos"),versionRegexes:[]},{name:"FreeBSD",search:et("freebsd"),versionRegexes:[]}],rt={browsers:C(tt),oses:C(nt)},ot=function(e){var t,n,r,o,i,u,a,c,l,f,s,d=rt.browsers(),m=rt.oses(),g=$e(d,e).fold(He.unknown,He.nu),h=Qe(m,e).fold(Ke.unknown,Ke.nu);return{browser:g,os:h,deviceType:(n=g,r=e,o=(t=h).isiOS()&&!0===/ipad/i.test(r),i=t.isiOS()&&!o,u=t.isAndroid()&&3===t.version.major,a=t.isAndroid()&&4===t.version.major,c=o||u||a&&!0===/mobile/i.test(r),l=t.isiOS()||t.isAndroid(),f=l&&!c,s=n.isSafari()&&t.isiOS()&&!1===/safari/i.test(r),{isiPad:C(o),isiPhone:C(i),isTablet:C(c),isPhone:C(f),isTouch:C(l),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:C(s)})}},it={detect:we(function(){var e=m.navigator.userAgent;return ot(e)})},ut=Z,at=Q,ct=function(e,t){var n=e.dom();if(n.nodeType!==ut)return!1;var r=n;if(r.matches!==undefined)return r.matches(t);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(t);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(t);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},lt=function(e){return e.nodeType!==ut&&e.nodeType!==at||0===e.childElementCount},ft=function(e,t){return e.dom()===t.dom()},st=it.detect().browser.isIE()?function(e,t){return Be(e.dom(),t.dom())}:function(e,t){var n=e.dom(),r=t.dom();return n!==r&&n.contains(r)},dt=ct,mt=function(e){return xe.fromDom(e.dom().ownerDocument)},gt=function(e){return R.from(e.dom().parentNode).map(xe.fromDom)},ht=function(e,t){for(var n=v(t)?t:f,r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=xe.fromDom(i);if(o.push(u),!0===n(u))break;r=i}return o},pt=function(e){return R.from(e.dom().previousSibling).map(xe.fromDom)},vt=function(e){return R.from(e.dom().nextSibling).map(xe.fromDom)},bt=function(e){return E(e.dom().childNodes,xe.fromDom)},wt=function(e,t){var n=e.dom().childNodes;return R.from(n[t]).map(xe.fromDom)},yt=(q("element","offset"),function(t,n){gt(t).each(function(e){e.dom().insertBefore(n.dom(),t.dom())})}),xt=function(e,t){vt(e).fold(function(){gt(e).each(function(e){Rt(e,t)})},function(e){yt(e,t)})},Ct=function(t,n){wt(t,0).fold(function(){Rt(t,n)},function(e){t.dom().insertBefore(n.dom(),e.dom())})},Rt=function(e,t){e.dom().appendChild(t.dom())},St=function(e,t){yt(e,t),Rt(t,e)},Tt=function(r,o){k(o,function(e,t){var n=0===t?r:o[t-1];xt(n,e)})},Dt=function(t,e){k(e,function(e){Rt(t,e)})},Ot=function(e){e.dom().textContent="",k(bt(e),function(e){Nt(e)})},Nt=function(e){var t=e.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},Et=function(e){var t,n=bt(e);0<n.length&&(t=e,k(n,function(e){yt(t,e)})),Nt(e)},kt=(q("width","height"),q("width","height"),q("rows","columns")),At=q("row","column"),Pt=(q("x","y"),q("element","rowspan","colspan")),It=q("element","rowspan","colspan","isNew"),Bt=q("element","rowspan","colspan","row","column"),Wt=q("element","cells","section"),Mt=q("element","isNew"),_t=q("element","cells","section","isNew"),Lt=q("cells","section"),Ft=q("details","section"),jt=q("startRow","startCol","finishRow","finishCol"),zt=function(e,t){var n=[];return k(bt(e),function(e){t(e)&&(n=n.concat([e])),n=n.concat(zt(e,t))}),n},Ht=function(e,t,n){return r=function(e){return ct(e,t)},A(ht(e,n),r);var r},Ut=function(e,t){return n=function(e){return ct(e,t)},A(bt(e),n);var n},qt=function(e,t){return n=t,o=(r=e)===undefined?m.document:r.dom(),lt(o)?[]:E(o.querySelectorAll(n),xe.fromDom);var n,r,o};function Vt(e,t,n,r,o){return e(n,r)?R.some(n):v(o)&&o(n)?R.none():t(n,r,o)}var Gt,Yt,Xt,Kt=function(e,t,n){for(var r=e.dom(),o=v(n)?n:C(!1);r.parentNode;){r=r.parentNode;var i=xe.fromDom(r);if(t(i))return R.some(i);if(o(i))break}return R.none()},Jt=function(e,t,n){return Kt(e,function(e){return ct(e,t)},n)},$t=function(e,t){return n=function(e){return ct(e,t)},B(e.dom().childNodes,function(e){return n(xe.fromDom(e))}).map(xe.fromDom);var n},Qt=function(e,t){return n=t,o=(r=e)===undefined?m.document:r.dom(),lt(o)?R.none():R.from(o.querySelector(n)).map(xe.fromDom);var n,r,o},Zt=function(e,t,n){return Vt(ct,Jt,e,t,n)},en=function(e,t,n){return _(bt(e),function(e){return ct(e,t)?n(e)?[e]:[]:en(e,t,n)})},tn={firstLayer:function(e,t){return en(e,t,C(!0))},filterFirstLayer:en},nn=function(e,t,n){return void 0===n&&(n=f),n(t)?R.none():O(e,oe(t))?R.some(t):Jt(t,e.join(","),function(e){return ct(e,"table")||n(e)})},rn=function(t,e){return gt(e).map(function(e){return Ut(e,t)})},on=b(rn,"th,td"),un=b(rn,"tr"),an=function(e,t){return parseInt(me(e,t),10)},cn={cell:function(e,t){return nn(["td","th"],e,t)},firstCell:function(e){return Qt(e,"th,td")},cells:function(e){return tn.firstLayer(e,"th,td")},neighbourCells:on,table:function(e,t){return Zt(e,"table",t)},row:function(e,t){return nn(["tr"],e,t)},rows:function(e){return tn.firstLayer(e,"tr")},notCell:function(e,t){return nn(["caption","tr","tbody","tfoot","thead"],e,t)},neighbourRows:un,attr:an,grid:function(e,t,n){var r=an(e,t),o=an(e,n);return kt(r,o)}},ln=function(e){var t=cn.rows(e);return E(t,function(e){var t=e,n=gt(t).map(function(e){var t=oe(e);return"tfoot"===t||"thead"===t||"tbody"===t?t:"tbody"}).getOr("tbody"),r=E(cn.cells(e),function(e){var t=ge(e,"rowspan")?parseInt(me(e,"rowspan"),10):1,n=ge(e,"colspan")?parseInt(me(e,"colspan"),10):1;return Pt(e,t,n)});return Wt(t,r,n)})},fn=function(e,n){return E(e,function(e){var t=E(cn.cells(e),function(e){var t=ge(e,"rowspan")?parseInt(me(e,"rowspan"),10):1,n=ge(e,"colspan")?parseInt(me(e,"colspan"),10):1;return Pt(e,t,n)});return Wt(e,t,n.section())})},sn=function(e,t){return e+","+t},dn=function(e,t){var n=_(e.all(),function(e){return e.cells()});return A(n,t)},mn={generate:function(e){var l={},t=[],n=e.length,f=0;k(e,function(e,a){var c=[];k(e.cells(),function(e){for(var t=0;l[sn(a,t)]!==undefined;)t++;for(var n=Bt(e.element(),e.rowspan(),e.colspan(),a,t),r=0;r<e.colspan();r++)for(var o=0;o<e.rowspan();o++){var i=t+r,u=sn(a+o,i);l[u]=n,f=Math.max(f,i+1)}c.push(n)}),t.push(Wt(e.element(),c,e.section()))});var r=kt(n,f);return{grid:C(r),access:C(l),all:C(t)}},getAt:function(e,t,n){var r=e.access()[sn(t,n)];return r!==undefined?R.some(r):R.none()},findItem:function(e,t,n){var r=dn(e,function(e){return n(t,e.element())});return 0<r.length?R.some(r[0]):R.none()},filterItems:dn,justCells:function(e){var t=E(e.all(),function(e){return e.cells()});return M(t)}},gn=q("minRow","minCol","maxRow","maxCol"),hn=function(e,t){var n,i,r,u,a,c,l,o,f,s,d=function(e){return ct(e.element(),t)},m=ln(e),g=mn.generate(m),h=(i=d,r=(n=g).grid().columns(),u=n.grid().rows(),a=r,l=c=0,z(n.access(),function(e){if(i(e)){var t=e.row(),n=t+e.rowspan()-1,r=e.column(),o=r+e.colspan()-1;t<u?u=t:c<n&&(c=n),r<a?a=r:l<o&&(l=o)}}),gn(u,a,c,l)),p="th:not("+t+"),td:not("+t+")",v=tn.filterFirstLayer(e,"th,td",function(e){return ct(e,p)});return k(v,Nt),function(e,t,n,r){for(var o,i,u,a=t.grid().columns(),c=t.grid().rows(),l=0;l<c;l++)for(var f=!1,s=0;s<a;s++)l<n.minRow()||l>n.maxRow()||s<n.minCol()||s>n.maxCol()||(mn.getAt(t,l,s).filter(r).isNone()?(o=f,i=e[l].element(),u=xe.fromTag("td"),Rt(u,xe.fromTag("br")),(o?Rt:Ct)(i,u)):f=!0)}(m,g,h,d),o=e,f=h,s=A(tn.firstLayer(o,"tr"),function(e){return 0===e.dom().childElementCount}),k(s,Nt),f.minCol()!==f.maxCol()&&f.minRow()!==f.maxRow()||k(tn.firstLayer(o,"th,td"),function(e){he(e,"rowspan"),he(e,"colspan")}),he(o,"width"),he(o,"height"),Ae(o,"width"),Ae(o,"height"),e},pn=(Gt=le,Yt="text",{get:function(e){if(!Gt(e))throw new Error("Can only get "+Yt+" value of a "+Yt+" node");return Xt(e).getOr("")},getOption:Xt=function(e){return Gt(e)?R.from(e.dom().nodeValue):R.none()},set:function(e,t){if(!Gt(e))throw new Error("Can only set raw "+Yt+" value of a "+Yt+" node");e.dom().nodeValue=t}}),vn=function(e){return pn.get(e)},bn=function(e){return pn.getOption(e)},wn=function(e,t){pn.set(e,t)},yn=function(e){return"img"===oe(e)?1:bn(e).fold(function(){return bt(e).length},function(e){return e.length})},xn=["img","br"],Cn=function(e){return bn(e).filter(function(e){return 0!==e.trim().length||-1<e.indexOf("\xa0")}).isSome()||O(xn,oe(e))},Rn=function(e){return o=Cn,(i=function(e){for(var t=0;t<e.childNodes.length;t++){var n=xe.fromDom(e.childNodes[t]);if(o(n))return R.some(n);var r=i(e.childNodes[t]);if(r.isSome())return r}return R.none()})(e.dom());var o,i},Sn=function(e){return Tn(e,Cn)},Tn=function(e,i){var u=function(e){for(var t=bt(e),n=t.length-1;0<=n;n--){var r=t[n];if(i(r))return R.some(r);var o=u(r);if(o.isSome())return o}return R.none()};return u(e)},Dn=function(e,t){return xe.fromDom(e.dom().cloneNode(t))},On=function(e){return Dn(e,!1)},Nn=function(e){return Dn(e,!0)},En=function(e,t){var n,r,o,i,u=(n=e,r=t,o=xe.fromTag(r),i=pe(n),de(o,i),o),a=bt(Nn(e));return Dt(u,a),u},kn=function(){var e=xe.fromTag("td");return Rt(e,xe.fromTag("br")),e},An=function(e,t,n){var r=En(e,t);return z(n,function(e,t){null===e?he(r,t):se(r,t,e)}),r},Pn=function(e){return e},In=function(e){return function(){return xe.fromTag("tr",e.dom())}},Bn=function(d,e,m){return{row:In(e),cell:function(e){var r,o,i,t,n,u,a,c=mt(e.element()),l=xe.fromTag(oe(e.element()),c.dom()),f=m.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),s=0<f.length?(r=e.element(),o=l,i=f,Rn(r).map(function(e){var t=i.join(","),n=Ht(e,t,function(e){return ft(e,r)});return P(n,function(e,t){var n=On(t);return he(n,"contenteditable"),Rt(e,n),n},o)}).getOr(o)):l;return Rt(s,xe.fromTag("br")),t=e.element(),n=l,u=t.dom(),a=n.dom(),be(u)&&be(a)&&(a.style.cssText=u.style.cssText),Ae(l,"height"),1!==e.colspan()&&Ae(e.element(),"width"),d(e.element(),l),l},replace:An,gap:kn}},Wn=function(e){return{row:In(e),cell:kn,replace:Pn,gap:kn}},Mn=function(e,t){return t.column()>=e.startCol()&&t.column()+t.colspan()-1<=e.finishCol()&&t.row()>=e.startRow()&&t.row()+t.rowspan()-1<=e.finishRow()},_n=function(e,t){var n=t.column(),r=t.column()+t.colspan()-1,o=t.row(),i=t.row()+t.rowspan()-1;return n<=e.finishCol()&&r>=e.startCol()&&o<=e.finishRow()&&i>=e.startRow()},Ln=function(e,t){for(var n=!0,r=b(Mn,t),o=t.startRow();o<=t.finishRow();o++)for(var i=t.startCol();i<=t.finishCol();i++)n=n&&mn.getAt(e,o,i).exists(r);return n?R.some(t):R.none()},Fn=function(e,t,n){var r=mn.findItem(e,t,ft),o=mn.findItem(e,n,ft);return r.bind(function(r){return o.map(function(e){return t=r,n=e,jt(Math.min(t.row(),n.row()),Math.min(t.column(),n.column()),Math.max(t.row()+t.rowspan()-1,n.row()+n.rowspan()-1),Math.max(t.column()+t.colspan()-1,n.column()+n.colspan()-1));var t,n})})},jn=Fn,zn=function(t,e,n){return Fn(t,e,n).bind(function(e){return Ln(t,e)})},Hn=function(r,e,o,i){return mn.findItem(r,e,ft).bind(function(e){var t=0<o?e.row()+e.rowspan()-1:e.row(),n=0<i?e.column()+e.colspan()-1:e.column();return mn.getAt(r,t+o,n+i).map(function(e){return e.element()})})},Un=function(n,e,t){return jn(n,e,t).map(function(e){var t=mn.filterItems(n,b(_n,e));return E(t,function(e){return e.element()})})},qn=function(e,t){return mn.findItem(e,t,function(e,t){return st(t,e)}).map(function(e){return e.element()})},Vn=function(e){var t=ln(e);return mn.generate(t)},Gn=function(n,r,o){return cn.table(n).bind(function(e){var t=Vn(e);return Hn(t,n,r,o)})},Yn=function(e,t,n){var r=Vn(e);return Un(r,t,n)},Xn=function(e,t,n,r,o){var i=Vn(e),u=ft(e,n)?R.some(t):qn(i,t),a=ft(e,o)?R.some(r):qn(i,r);return u.bind(function(t){return a.bind(function(e){return Un(i,t,e)})})},Kn=function(e,t,n){var r=Vn(e);return zn(r,t,n)},Jn=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function $n(){return{up:C({selector:Jt,closest:Zt,predicate:Kt,all:ht}),down:C({selector:qt,predicate:zt}),styles:C({get:Ne,getRaw:ke,set:De,remove:Ae}),attrs:C({get:me,set:se,remove:he,copyTo:function(e,t){var n=pe(e);de(t,n)}}),insert:C({before:yt,after:xt,afterAll:Tt,append:Rt,appendAll:Dt,prepend:Ct,wrap:St}),remove:C({unwrap:Et,remove:Nt}),create:C({nu:xe.fromTag,clone:function(e){return xe.fromDom(e.dom().cloneNode(!1))},text:xe.fromText}),query:C({comparePosition:function(e,t){return e.dom().compareDocumentPosition(t.dom())},prevSibling:pt,nextSibling:vt}),property:C({children:bt,name:oe,parent:gt,document:function(e){return e.dom().ownerDocument},isText:le,isComment:ae,isElement:ce,getText:vn,setText:wn,isBoundary:function(e){return!!ce(e)&&("body"===oe(e)||O(Jn,oe(e)))},isEmptyTag:function(e){return!!ce(e)&&O(["br","img","hr","input"],oe(e))}}),eq:ft,is:dt}}var Qn=q("left","right"),Zn=q("first","second","splits"),er=function(e,t,n){var r=e.property().children(t);return W(r,b(e.eq,n)).map(function(e){return{before:C(r.slice(0,e)),after:C(r.slice(e+1))}})},tr=function(r,o,e,t){var n=o(r,e);return P(t,function(e,t){var n=o(r,t);return nr(r,e,n)},n)},nr=function(t,e,n){return e.bind(function(e){return n.filter(b(t.eq,e))})},rr=function(e,t){return b(e.eq,t)},or=function(t,e,n,r){void 0===r&&(r=f);var o=[e].concat(t.up().all(e)),i=[n].concat(t.up().all(n)),u=function(t){return W(t,r).fold(function(){return t},function(e){return t.slice(0,e+1)})},a=u(o),c=u(i),l=B(a,function(e){return N(c,rr(t,e))});return{firstpath:C(a),secondpath:C(c),shared:C(l)}},ir={sharedOne:function(e,t,n){return 0<n.length?tr(e,t,(r=n)[0],r.slice(1)):R.none();var r},subset:function(t,e,n){var r=or(t,e,n);return r.shared().bind(function(e){return function(o,i,e,t){var u=o.property().children(i);if(o.eq(i,e[0]))return R.some([e[0]]);if(o.eq(i,t[0]))return R.some([t[0]]);var n=function(e){var t=F(e),n=W(t,rr(o,i)).getOr(-1),r=n<t.length-1?t[n+1]:t[n];return W(u,rr(o,r))},r=n(e),a=n(t);return r.bind(function(r){return a.map(function(e){var t=Math.min(r,e),n=Math.max(r,e);return u.slice(t,n+1)})})}(t,e,r.firstpath(),r.secondpath())})},ancestors:or,breakToLeft:function(n,r,o){return er(n,r,o).map(function(e){var t=n.create().clone(r);return n.insert().appendAll(t,e.before().concat([o])),n.insert().appendAll(r,e.after()),n.insert().before(r,t),Qn(t,r)})},breakToRight:function(n,r,e){return er(n,r,e).map(function(e){var t=n.create().clone(r);return n.insert().appendAll(t,e.after()),n.insert().after(r,t),Qn(r,t)})},breakPath:function(i,e,u,a){var c=function(e,t,o){var n=Zn(e,R.none(),o);return u(e)?Zn(e,t,o):i.property().parent(e).bind(function(r){return a(i,r,e).map(function(e){var t=[{first:e.left,second:e.right}],n=u(r)?r:e.left();return c(n,R.some(e.right()),o.concat(t))})}).getOr(n)};return c(e,R.none(),[])}},ur=$n(),ar={sharedOne:function(n,e){return ir.sharedOne(ur,function(e,t){return n(t)},e)},subset:function(e,t){return ir.subset(ur,e,t)},ancestors:function(e,t,n){return ir.ancestors(ur,e,t,n)},breakToLeft:function(e,t){return ir.breakToLeft(ur,e,t)},breakToRight:function(e,t){return ir.breakToRight(ur,e,t)},breakPath:function(e,t,r){return ir.breakPath(ur,e,t,function(e,t,n){return r(t,n)})}},cr={create:J(["boxes","start","finish"],[])},lr=function(e){return Jt(e,"table")},fr=function(a,c,r){var l=function(t){return function(e){return r!==undefined&&r(e)||ft(e,t)}};return ft(a,c)?R.some(cr.create({boxes:R.some([a]),start:a,finish:c})):lr(a).bind(function(u){return lr(c).bind(function(i){if(ft(u,i))return R.some(cr.create({boxes:Yn(u,a,c),start:a,finish:c}));if(st(u,i)){var e=0<(t=Ht(c,"td,th",l(u))).length?t[t.length-1]:c;return R.some(cr.create({boxes:Xn(u,a,u,c,i),start:a,finish:e}))}if(st(i,u)){var t,n=0<(t=Ht(a,"td,th",l(i))).length?t[t.length-1]:a;return R.some(cr.create({boxes:Xn(i,a,u,c,i),start:a,finish:n}))}return ar.ancestors(a,c).shared().bind(function(e){return Zt(e,"table",r).bind(function(e){var t=Ht(c,"td,th",l(e)),n=0<t.length?t[t.length-1]:c,r=Ht(a,"td,th",l(e)),o=0<r.length?r[r.length-1]:a;return R.some(cr.create({boxes:Xn(e,a,u,c,i),start:o,finish:n}))})})})})},sr=fr,dr=function(e,t){var n=qt(e,t);return 0<n.length?R.some(n):R.none()},mr=function(e,t,n,r,o){return(i=e,u=o,B(i,function(e){return ct(e,u)})).bind(function(e){return Gn(e,t,n).bind(function(e){return n=r,Jt(t=e,"table").bind(function(e){return Qt(e,n).bind(function(e){return fr(e,t).bind(function(t){return t.boxes().map(function(e){return{boxes:C(e),start:C(t.start()),finish:C(t.finish())}})})})});var t,n})});var i,u},gr=function(e,t,r){return Qt(e,t).bind(function(n){return Qt(e,r).bind(function(t){return ar.sharedOne(lr,[n,t]).map(function(e){return{first:C(n),last:C(t),table:C(e)}})})})},hr=function(e,t){return dr(e,t)},pr=function(o,e,t){return gr(o,e,t).bind(function(n){var e=function(e){return ft(o,e)},t=Jt(n.first(),"thead,tfoot,tbody,table",e),r=Jt(n.last(),"thead,tfoot,tbody,table",e);return t.bind(function(t){return r.bind(function(e){return ft(t,e)?Kn(n.table(),n.first(),n.last()):R.none()})})})},vr="data-mce-selected",br="data-mce-first-selected",wr="data-mce-last-selected",yr={selected:C(vr),selectedSelector:C("td[data-mce-selected],th[data-mce-selected]"),attributeSelector:C("[data-mce-selected]"),firstSelected:C(br),firstSelectedSelector:C("td[data-mce-first-selected],th[data-mce-first-selected]"),lastSelected:C(wr),lastSelectedSelector:C("td[data-mce-last-selected],th[data-mce-last-selected]")},xr=function(u){if(!h(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],n={};return k(u,function(e,r){var t=j(e);if(1!==t.length)throw new Error("one and only one name per case");var o=t[0],i=e[o];if(n[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!h(i))throw new Error("case arguments must be an array");a.push(o),n[o]=function(){var e=arguments.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+e);for(var n=new Array(e),t=0;t<n.length;t++)n[t]=arguments[t];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,n)},match:function(e){var t=j(e);if(a.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+t.join(","));if(!L(a,function(e){return O(t,e)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+a.join(", "));return e[o].apply(null,n)},log:function(e){m.console.log(e,{constructors:a,constructor:o,params:n})}}}}),n},Cr=xr([{none:[]},{multiple:["elements"]},{single:["selection"]}]),Rr={cata:function(e,t,n,r){return e.fold(t,n,r)},none:Cr.none,multiple:Cr.multiple,single:Cr.single},Sr=function(e,t){return Rr.cata(t.get(),C([]),o,C([e]))},Tr=function(n,e){return Rr.cata(e.get(),R.none,function(t,e){return 0===t.length?R.none():pr(n,yr.firstSelectedSelector(),yr.lastSelectedSelector()).bind(function(e){return 1<t.length?R.some({bounds:C(e),cells:C(t)}):R.none()})},R.none)},Dr=function(e,t){var n=Sr(e,t);return 0<n.length&&L(n,function(e){return ge(e,"rowspan")&&1<parseInt(me(e,"rowspan"),10)||ge(e,"colspan")&&1<parseInt(me(e,"colspan"),10)})?R.some(n):R.none()},Or=Sr,Nr=function(e){return{element:C(e),mergable:R.none,unmergable:R.none,selection:C([e])}},Er=q("element","clipboard","generators"),kr={noMenu:Nr,forMenu:function(e,t,n){return{element:C(n),mergable:C(Tr(t,e)),unmergable:C(Dr(n,e)),selection:C(Or(n,e))}},notCell:function(e){return Nr(e)},paste:Er,pasteRows:function(e,t,n,r,o){return{element:C(n),mergable:R.none,unmergable:R.none,selection:C(Or(n,e)),clipboard:C(r),generators:C(o)}}},Ar=function(f,e,s,d){f.on("BeforeGetContent",function(n){!0===n.selection&&Rr.cata(e.get(),y,function(e){var t;n.preventDefault(),(t=e,cn.table(t[0]).map(Nn).map(function(e){return[hn(e,yr.attributeSelector())]})).each(function(e){var t;n.content="text"===n.format?E(e,function(e){return e.dom().innerText}).join(""):(t=f,E(e,function(e){return t.selection.serializer.serialize(e.dom(),{})}).join(""))})},y)}),f.on("BeforeSetContent",function(l){!0===l.selection&&!0===l.paste&&R.from(f.dom.getParent(f.selection.getStart(),"th,td")).each(function(e){var c=xe.fromDom(e);cn.table(c).each(function(t){var e,n,r,o=A((e=l.content,(r=(n||m.document).createElement("div")).innerHTML=e,bt(xe.fromDom(r))),function(e){return"meta"!==oe(e)});if(1===o.length&&"table"===oe(o[0])){l.preventDefault();var i=xe.fromDom(f.getDoc()),u=Wn(i),a=kr.paste(c,o[0],u);s.pasteCells(t,a).each(function(e){f.selection.setRng(e),f.focus(),d.clear(t)})}})})})};function Pr(r,o){var e=function(e){var t=o(e);if(t<=0||null===t){var n=Ne(e,r);return parseFloat(n)||0}return t},i=function(o,e){return I(e,function(e,t){var n=Ne(o,t),r=n===undefined?0:parseInt(n,10);return isNaN(r)?e:e+r},0)};return{set:function(e,t){if(!w(t)&&!t.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+t);var n=e.dom();be(n)&&(n.style[r]=t+"px")},get:e,getOuter:e,aggregate:i,max:function(e,t,n){var r=i(e,n);return r<t?t-r:0}}}var Ir=Pr("height",function(e){var t=e.dom();return Ce(e)?t.getBoundingClientRect().height:t.offsetHeight}),Br=function(e){return Ir.get(e)},Wr=function(e){return Ir.getOuter(e)},Mr=Pr("width",function(e){return e.dom().offsetWidth}),_r=function(e){return Mr.get(e)},Lr=function(e){return Mr.getOuter(e)},Fr=it.detect(),jr=function(e,t,n){return r=Ne(e,t),o=n,i=parseFloat(r),isNaN(i)?o:i;var r,o,i},zr=function(e){return Fr.browser.isIE()||Fr.browser.isEdge()?(n=jr(t=e,"padding-top",0),r=jr(t,"padding-bottom",0),o=jr(t,"border-top-width",0),i=jr(t,"border-bottom-width",0),u=t.dom().getBoundingClientRect().height,"border-box"===Ne(t,"box-sizing")?u:u-n-r-(o+i)):jr(e,"height",Br(e));var t,n,r,o,i,u},Hr=/(\d+(\.\d+)?)(\w|%)*/,Ur=/(\d+(\.\d+)?)%/,qr=/(\d+(\.\d+)?)px|em/,Vr=function(e,t){De(e,"height",t+"px")},Gr=function(e,t,n,r){var o,i,u,a,c,l,f,s,d,m=parseInt(e,10);return s=l="%",d=(f=e).length-l.length,""!==s&&(f.length<s.length||f.substr(d,d+s.length)!==s)||"table"===oe(t)?m:(o=t,i=m,u=n,a=r,c=cn.table(o).map(function(e){var t=u(e);return Math.floor(i/100*t)}).getOr(i),a(o,c),c)},Yr=function(e){var t,n=ke(t=e,"height").getOrThunk(function(){return zr(t)+"px"});return n?Gr(n,e,Br,Vr):Br(e)},Xr=function(e,t){return ge(e,t)?parseInt(me(e,t),10):1},Kr=function(e){return ke(e,"width").fold(function(){return R.from(me(e,"width"))},function(e){return R.some(e)})},Jr=function(e,t){return e/t.pixelWidth()*100},$r={percentageBasedSizeRegex:C(Ur),pixelBasedSizeRegex:C(qr),setPixelWidth:function(e,t){De(e,"width",t+"px")},setPercentageWidth:function(e,t){De(e,"width",t+"%")},setHeight:Vr,getPixelWidth:function(t,n){return Kr(t).fold(function(){return _r(t)},function(e){return function(e,t,n){var r=qr.exec(t);if(null!==r)return parseInt(r[1],10);var o=Ur.exec(t);if(null!==o){var i=parseFloat(o[1]);return i/100*n.pixelWidth()}return _r(e)}(t,e,n)})},getPercentageWidth:function(t,n){return Kr(t).fold(function(){var e=_r(t);return Jr(e,n)},function(e){return function(e,t,n){var r=Ur.exec(t);if(null!==r)return parseFloat(r[1]);var o=_r(e);return Jr(o,n)}(t,e,n)})},getGenericWidth:function(e){return Kr(e).bind(function(e){var t=Hr.exec(e);return null!==t?R.some({width:C(parseFloat(t[1])),unit:C(t[3])}):R.none()})},setGenericWidth:function(e,t,n){De(e,"width",t+n)},getHeight:function(e){return n="rowspan",Yr(t=e)/Xr(t,n);var t,n},getRawWidth:Kr},Qr=function(n,r){$r.getGenericWidth(n).each(function(e){var t=e.width()/2;$r.setGenericWidth(n,t,e.unit()),$r.setGenericWidth(r,t,e.unit())})},Zr=function(n,r){return{left:C(n),top:C(r),translate:function(e,t){return Zr(n+e,r+t)}}},eo=Zr,to=function(e,t){return e!==undefined?e:t!==undefined?t:0},no=function(e){var t=e.dom().ownerDocument,n=t.body,r=t.defaultView,o=t.documentElement,i=to(r.pageYOffset,o.scrollTop),u=to(r.pageXOffset,o.scrollLeft),a=to(o.clientTop,n.clientTop),c=to(o.clientLeft,n.clientLeft);return ro(e).translate(u-c,i-a)},ro=function(e){var t,n=e.dom(),r=n.ownerDocument.body;return r===n?eo(r.offsetLeft,r.offsetTop):Ce(e)?(t=n.getBoundingClientRect(),eo(t.left,t.top)):eo(0,0)},oo=q("row","y"),io=q("col","x"),uo=function(e){return no(e).left()+Lr(e)},ao=function(e){return no(e).left()},co=function(e,t){return io(e,ao(t))},lo=function(e,t){return io(e,uo(t))},fo=function(e){return no(e).top()},so=function(e,t){return oo(e,fo(t))},mo=function(e,t){return oo(e,fo(t)+Wr(t))},go=function(n,t,r){if(0===r.length)return[];var e=E(r.slice(1),function(e,t){return e.map(function(e){return n(t,e)})}),o=r[r.length-1].map(function(e){return t(r.length-1,e)});return e.concat([o])},ho={height:{delta:o,positions:function(e){return go(so,mo,e)},edge:fo},rtl:{delta:function(e){return-e},edge:uo,positions:function(e){return go(lo,co,e)}},ltr:{delta:o,edge:ao,positions:function(e){return go(co,lo,e)}}},po={ltr:ho.ltr,rtl:ho.rtl};function vo(t){var n=function(e){return t(e).isRtl()?po.rtl:po.ltr};return{delta:function(e,t){return n(t).delta(e,t)},edge:function(e){return n(e).edge(e)},positions:function(e,t){return n(t).positions(e,t)}}}var bo=function(e){var t=ln(e);return mn.generate(t).grid()},wo=function(){return(wo=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},yo=function(e){for(var t=[],n=function(e){t.push(e)},r=0;r<e.length;r++)e[r].each(n);return t},xo=function(e,t){for(var n=0;n<e.length;n++){var r=t(e[n],n);if(r.isSome())return r}return R.none()},Co=function(e,t,n,r){n===r?he(e,t):se(e,t,n)},Ro=function(o,e){var i=[],u=[],t=function(e,t){0<e.length?function(e,t){var n=$t(o,t).getOrThunk(function(){var e=xe.fromTag(t,mt(o).dom());return Rt(o,e),e});Ot(n);var r=E(e,function(e){e.isNew()&&i.push(e.element());var t=e.element();return Ot(t),k(e.cells(),function(e){e.isNew()&&u.push(e.element()),Co(e.element(),"colspan",e.colspan(),1),Co(e.element(),"rowspan",e.rowspan(),1),Rt(t,e.element())}),t});Dt(n,r)}(e,t):$t(o,t).each(Nt)},n=[],r=[],a=[];return k(e,function(e){switch(e.section()){case"thead":n.push(e);break;case"tbody":r.push(e);break;case"tfoot":a.push(e)}}),t(n,"thead"),t(r,"tbody"),t(a,"tfoot"),{newRows:C(i),newCells:C(u)}},So=function(e){return E(e,function(e){var n=On(e.element());return k(e.cells(),function(e){var t=Nn(e.element());Co(t,"colspan",e.colspan(),1),Co(t,"rowspan",e.rowspan(),1),Rt(n,t)}),n})},To=function(e,t){var n=me(e,t);return n===undefined||""===n?[]:n.split(" ")},Do=function(e){return e.dom().classList!==undefined},Oo=function(e,t){return o=t,i=To(n=e,r="class").concat([o]),se(n,r,i.join(" ")),!0;var n,r,o,i},No=function(e,t){return o=t,0<(i=A(To(n=e,r="class"),function(e){return e!==o})).length?se(n,r,i.join(" ")):he(n,r),!1;var n,r,o,i},Eo=function(e,t){Do(e)?e.dom().classList.add(t):Oo(e,t)},ko=function(e){0===(Do(e)?e.dom().classList:To(e,"class")).length&&he(e,"class")},Ao=function(e,t){return Do(e)&&e.dom().classList.contains(t)},Po=function(e,t){for(var n=[],r=e;r<t;r++)n.push(r);return n},Io=function(t,n){if(n<0||n>=t.length-1)return R.none();var e=t[n].fold(function(){var e=F(t.slice(0,n));return xo(e,function(e,t){return e.map(function(e){return{value:e,delta:t+1}})})},function(e){return R.some({value:e,delta:0})}),r=t[n+1].fold(function(){var e=t.slice(n+1);return xo(e,function(e,t){return e.map(function(e){return{value:e,delta:t+1}})})},function(e){return R.some({value:e,delta:1})});return e.bind(function(n){return r.map(function(e){var t=e.delta+n.delta;return Math.abs(e.value-n.value)/t})})},Bo=function(e,t,n){var r=e();return B(r,t).orThunk(function(){return R.from(r[0]).orThunk(n)}).map(function(e){return e.element()})},Wo=function(n){var e=n.grid(),t=Po(0,e.columns()),r=Po(0,e.rows());return E(t,function(t){return Bo(function(){return _(r,function(e){return mn.getAt(n,e,t).filter(function(e){return e.column()===t}).fold(C([]),function(e){return[e]})})},function(e){return 1===e.colspan()},function(){return mn.getAt(n,0,t)})})},Mo=function(n){var e=n.grid(),t=Po(0,e.rows()),r=Po(0,e.columns());return E(t,function(t){return Bo(function(){return _(r,function(e){return mn.getAt(n,t,e).filter(function(e){return e.row()===t}).fold(C([]),function(e){return[e]})})},function(e){return 1===e.rowspan()},function(){return mn.getAt(n,t,0)})})},_o=function(e){var t=e.replace(/\./g,"-");return{resolve:function(e){return t+"-"+e}}},Lo={resolve:_o("ephox-snooker").resolve},Fo=function(e,t,n,r,o){var i=xe.fromTag("div");return Oe(i,{position:"absolute",left:t-r/2+"px",top:n+"px",height:o+"px",width:r+"px"}),de(i,{"data-column":e,role:"presentation"}),i},jo=function(e,t,n,r,o){var i=xe.fromTag("div");return Oe(i,{position:"absolute",left:t+"px",top:n-o/2+"px",height:o+"px",width:r+"px"}),de(i,{"data-row":e,role:"presentation"}),i},zo=Lo.resolve("resizer-bar"),Ho=Lo.resolve("resizer-rows"),Uo=Lo.resolve("resizer-cols"),qo=function(e){var t=qt(e.parent(),"."+zo);k(t,Nt)},Vo=function(n,e,r){var o=n.origin();k(e,function(e,t){e.each(function(e){var t=r(o,e);Eo(t,zo),Rt(n.parent(),t)})})},Go=function(e,t,n,r,o,i){var u,a,c,l,f=no(t),s=0<n.length?o.positions(n,t):[];u=e,a=s,c=f,l=Lr(t),Vo(u,a,function(e,t){var n=jo(t.row(),c.left()-e.left(),t.y()-e.top(),l,7);return Eo(n,Ho),n});var d,m,g,h,p=0<r.length?i.positions(r,t):[];d=e,m=p,g=f,h=Wr(t),Vo(d,m,function(e,t){var n=Fo(t.col(),t.x()-e.left(),g.top()-e.top(),7,h);return Eo(n,Uo),n})},Yo=function(e,t){var n=qt(e.parent(),"."+zo);k(n,t)},Xo=function(e,t,n,r){qo(e);var o=ln(t),i=mn.generate(o),u=Mo(i),a=Wo(i);Go(e,t,u,a,n,r)},Ko=function(e){Yo(e,function(e){De(e,"display","none")})},Jo=function(e){Yo(e,function(e){De(e,"display","block")})},$o=qo,Qo=function(e){return Ao(e,Ho)},Zo=function(e){return Ao(e,Uo)},ei=function(e,t){return Lt(t,e.section())},ti=function(e,t){return e.cells()[t]},ni={addCell:function(e,t,n){var r=e.cells(),o=r.slice(0,t),i=r.slice(t),u=o.concat([n]).concat(i);return ei(e,u)},setCells:ei,mutateCell:function(e,t,n){e.cells()[t]=n},getCell:ti,getCellElement:function(e,t){return ti(e,t).element()},mapCells:function(e,t){var n=e.cells(),r=E(n,t);return Lt(r,e.section())},cellLength:function(e){return e.cells().length}},ri=function(e,t){if(0===e.length)return 0;var n=e[0];return W(e,function(e){return!t(n.element(),e.element())}).fold(function(){return e.length},function(e){return e})},oi=function(e,t,n,r){var o,i,u,a,c=(o=e,i=t,o[i]).cells().slice(n),l=ri(c,r),f=(u=e,a=n,E(u,function(e){return ni.getCell(e,a)})).slice(t),s=ri(f,r);return{colspan:C(l),rowspan:C(s)}},ii=function(o,i){var u=E(o,function(e,t){return E(e.cells(),function(e,t){return!1})});return E(o,function(e,r){var t=_(e.cells(),function(e,t){if(!1===u[r][t]){var n=oi(o,r,t,i);return function(e,t,n,r){for(var o=e;o<e+n;o++)for(var i=t;i<t+r;i++)u[o][i]=!0}(r,t,n.rowspan(),n.colspan()),[It(e.element(),n.rowspan(),n.colspan(),e.isNew())]}return[]});return Ft(t,e.section())})},ui=function(e,t,n){for(var r=[],o=0;o<e.grid().rows();o++){for(var i=[],u=0;u<e.grid().columns();u++){var a=mn.getAt(e,o,u).map(function(e){return Mt(e.element(),n)}).getOrThunk(function(){return Mt(t.gap(),!0)});i.push(a)}var c=Lt(i,e.all()[o].section());r.push(c)}return r},ai=function(e,r){return E(e,function(e){var t,n=(t=e.details(),xo(t,function(e){return gt(e.element()).map(function(e){var t=gt(e).isNone();return Mt(e,t)})}).getOrThunk(function(){return Mt(r.row(),!0)}));return _t(n.element(),e.details(),e.section(),n.isNew())})},ci=function(e,t){var n=ii(e,ft);return ai(n,t)},li=function(e,t){var n=M(E(e.all(),function(e){return e.cells()}));return B(n,function(e){return ft(t,e.element())})},fi=function(a,c,l,f,s){return function(n,r,e,o,i){var t=ln(r),u=mn.generate(t);return c(u,e).map(function(e){var t=ui(u,o,!1),n=a(t,e,ft,s(o)),r=ci(n.grid(),o);return{grid:C(r),cursor:n.cursor}}).fold(function(){return R.none()},function(e){var t=Ro(r,e.grid());return l(r,e.grid(),i),f(r),Xo(n,r,ho.height,i),R.some({cursor:e.cursor,newRows:t.newRows,newCells:t.newCells})})}},si=function(t,e){return cn.cell(e.element()).bind(function(e){return li(t,e)})},di=function(t,e){var n=E(e.selection(),function(e){return cn.cell(e).bind(function(e){return li(t,e)})}),r=yo(n);return 0<r.length?R.some({cells:r,generators:e.generators,clipboard:e.clipboard}):R.none()},mi=function(t,e){var n=E(e.selection(),function(e){return cn.cell(e).bind(function(e){return li(t,e)})}),r=yo(n);return 0<r.length?R.some(r):R.none()},gi=function(n){return{is:function(e){return n===e},isValue:u,isError:f,getOr:C(n),getOrThunk:C(n),getOrDie:C(n),or:function(e){return gi(n)},orThunk:function(e){return gi(n)},fold:function(e,t){return t(n)},map:function(e){return gi(e(n))},mapError:function(e){return gi(n)},each:function(e){e(n)},bind:function(e){return e(n)},exists:function(e){return e(n)},forall:function(e){return e(n)},toOption:function(){return R.some(n)}}},hi=function(n){return{is:f,isValue:f,isError:u,getOr:o,getOrThunk:function(e){return e()},getOrDie:function(){return e=String(n),function(){throw new Error(e)}();var e},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,t){return e(n)},map:function(e){return hi(n)},mapError:function(e){return hi(e(n))},each:y,bind:function(e){return hi(n)},exists:f,forall:u,toOption:R.none}},pi={value:gi,error:hi,fromOption:function(e,t){return e.fold(function(){return hi(t)},gi)}},vi=function(e,t){return E(e,function(){return Mt(t.cell(),!0)})},bi=function(t,e,n){return t.concat(function(e,t){for(var n=[],r=0;r<e;r++)n.push(t(r));return n}(e,function(e){return ni.setCells(t[t.length-1],vi(t[t.length-1].cells(),n))}))},wi=function(e,t,n){return E(e,function(e){return ni.setCells(e,e.cells().concat(vi(Po(0,t),n)))})},yi=function(e,t,n){if(e.row()>=t.length||e.column()>ni.cellLength(t[0]))return pi.error("invalid start address out of table bounds, row: "+e.row()+", column: "+e.column());var r=t.slice(e.row()),o=r[0].cells().slice(e.column()),i=ni.cellLength(n[0]),u=n.length;return pi.value({rowDelta:C(r.length-u),colDelta:C(o.length-i)})},xi=function(e,t){var n=ni.cellLength(e[0]),r=ni.cellLength(t[0]);return{rowDelta:C(0),colDelta:C(n-r)}},Ci=function(e,t,n){var r=t.colDelta()<0?wi:o;return(t.rowDelta()<0?bi:o)(r(e,Math.abs(t.colDelta()),n),Math.abs(t.rowDelta()),n)},Ri=function(e,t,n,r){if(0===e.length)return e;for(var o=t.startRow();o<=t.finishRow();o++)for(var i=t.startCol();i<=t.finishCol();i++)ni.mutateCell(e[o],i,Mt(r(),!1));return e},Si=function(e,t,n,r){for(var o=!0,i=0;i<e.length;i++)for(var u=0;u<ni.cellLength(e[0]);u++){var a=n(ni.getCellElement(e[i],u),t);!0===a&&!1===o?ni.mutateCell(e[i],u,Mt(r(),!0)):!0===a&&(o=!1)}return e},Ti=function(i,n,u,a){if(0<n&&n<i.length){var e=i[n-1].cells(),t=(r=u,I(e,function(e,t){return N(e,function(e){return r(e.element(),t.element())})?e:e.concat([t])},[]));k(t,function(r){for(var o=R.none(),e=function(n){for(var e=function(t){var e=i[n].cells()[t];u(e.element(),r.element())&&(o.isNone()&&(o=R.some(a())),o.each(function(e){ni.mutateCell(i[n],t,Mt(e,!0))}))},t=0;t<ni.cellLength(i[0]);t++)e(t)},t=n;t<i.length;t++)e(t)})}var r;return i},Di=function(n,r,o,i,u){return yi(n,r,o).map(function(e){var t=Ci(r,e,i);return function(e,t,n,r,o){for(var i,u,a,c,l,f=e.row(),s=e.column(),d=f+n.length,m=s+ni.cellLength(n[0]),g=f;g<d;g++)for(var h=s;h<m;h++){i=t,u=g,a=h,l=c=void 0,c=b(o,ni.getCell(i[u],a).element()),l=i[u],1<i.length&&1<ni.cellLength(l)&&(0<a&&c(ni.getCellElement(l,a-1))||a<l.cells().length-1&&c(ni.getCellElement(l,a+1))||0<u&&c(ni.getCellElement(i[u-1],a))||u<i.length-1&&c(ni.getCellElement(i[u+1],a)))&&Si(t,ni.getCellElement(t[g],h),o,r.cell);var p=ni.getCellElement(n[g-f],h-s),v=r.replace(p);ni.mutateCell(t[g],h,Mt(v,!0))}return t}(n,t,o,i,u)})},Oi=function(e,t,n,r,o){Ti(t,e,o,r.cell);var i=xi(n,t),u=Ci(n,i,r),a=xi(t,u),c=Ci(t,a,r);return c.slice(0,e).concat(u).concat(c.slice(e,c.length))},Ni=function(n,r,e,o,i){var t=n.slice(0,r),u=n.slice(r),a=ni.mapCells(n[e],function(e,t){return 0<r&&r<n.length&&o(ni.getCellElement(n[r-1],t),ni.getCellElement(n[r],t))?ni.getCell(n[r],t):Mt(i(e.element(),o),!0)});return t.concat([a]).concat(u)},Ei=function(e,n,r,o,i){return E(e,function(e){var t=0<n&&n<ni.cellLength(e)&&o(ni.getCellElement(e,n-1),ni.getCellElement(e,n))?ni.getCell(e,n):Mt(i(ni.getCellElement(e,r),o),!0);return ni.addCell(e,n,t)})},ki=function(e,r,o,i,u){var a=o+1;return E(e,function(e,t){var n=t===r?Mt(u(ni.getCellElement(e,o),i),!0):ni.getCell(e,o);return ni.addCell(e,a,n)})},Ai=function(e,t,n,r,o){var i=t+1,u=e.slice(0,i),a=e.slice(i),c=ni.mapCells(e[t],function(e,t){return t===n?Mt(o(e.element(),r),!0):e});return u.concat([c]).concat(a)},Pi=function(e,t,n){return e.slice(0,t).concat(e.slice(n+1))},Ii=function(e,n,r){var t=E(e,function(e){var t=e.cells().slice(0,n).concat(e.cells().slice(r+1));return Lt(t,e.section())});return A(t,function(e){return 0<e.cells().length})},Bi=function(e,n,r,o){return E(e,function(e){return ni.mapCells(e,function(e){return t=e,N(n,function(e){return r(t.element(),e.element())})?Mt(o(e.element(),r),!0):e;var t})})},Wi=function(e,t,n,r){return ni.getCellElement(e[t],n)!==undefined&&0<t&&r(ni.getCellElement(e[t-1],n),ni.getCellElement(e[t],n))},Mi=function(e,t,n){return 0<t&&n(ni.getCellElement(e,t-1),ni.getCellElement(e,t))},_i=function(n,r,o,e){var t=_(n,function(e,t){return Wi(n,t,r,o)||Mi(e,r,o)?[]:[ni.getCell(e,r)]});return Bi(n,t,o,e)},Li=function(n,r,o,e){var i=n[r],t=_(i.cells(),function(e,t){return Wi(n,r,t,o)||Mi(i,t,o)?[]:[e]});return Bi(n,t,o,e)},Fi=xr([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),ji=wo({},Fi),zi=function(e,t,i,u){var n,r,a=e.slice(0),o=(r=t,0===(n=e).length?ji.none():1===n.length?ji.only(0):0===r?ji.left(0,1):r===n.length-1?ji.right(r-1,r):0<r&&r<n.length-1?ji.middle(r-1,r,r+1):ji.none()),c=function(e){return E(e,C(0))},l=C(c(a)),f=function(e,t){if(0<=i){var n=Math.max(u.minCellWidth(),a[t]-i);return c(a.slice(0,e)).concat([i,n-a[t]]).concat(c(a.slice(t+1)))}var r=Math.max(u.minCellWidth(),a[e]+i),o=a[e]-r;return c(a.slice(0,e)).concat([r-a[e],o]).concat(c(a.slice(t+1)))},s=f;return o.fold(l,function(e){return u.singleColumnWidth(a[e],i)},s,function(e,t,n){return f(t,n)},function(e,t){if(0<=i)return c(a.slice(0,t)).concat([i]);var n=Math.max(u.minCellWidth(),a[t]+i);return c(a.slice(0,t)).concat([n-a[t]])})},Hi=function(e,t){return ge(e,t)&&1<parseInt(me(e,t),10)},Ui={hasColspan:function(e){return Hi(e,"colspan")},hasRowspan:function(e){return Hi(e,"rowspan")},minWidth:C(10),minHeight:C(10),getInt:function(e,t){return parseInt(Ne(e,t),10)}},qi=function(e,t,n){return ke(e,t).fold(function(){return n(e)+"px"},function(e){return e})},Vi=function(e,t){return qi(e,"width",function(e){return $r.getPixelWidth(e,t)})},Gi=function(e){return qi(e,"height",$r.getHeight)},Yi=function(e,t,n,r,o){var i=Wo(e),u=E(i,function(e){return e.map(t.edge)});return E(i,function(e,t){return e.filter(g(Ui.hasColspan)).fold(function(){var e=Io(u,t);return r(e)},function(e){return n(e,o)})})},Xi=function(e){return e.map(function(e){return e+"px"}).getOr("")},Ki=function(e,t,n,r){var o=Mo(e),i=E(o,function(e){return e.map(t.edge)});return E(o,function(e,t){return e.filter(g(Ui.hasRowspan)).fold(function(){var e=Io(i,t);return r(e)},function(e){return n(e)})})},Ji={getRawWidths:function(e,t,n){return Yi(e,t,Vi,Xi,n)},getPixelWidths:function(e,t,n){return Yi(e,t,$r.getPixelWidth,function(e){return e.getOrThunk(n.minCellWidth)},n)},getPercentageWidths:function(e,t,n){return Yi(e,t,$r.getPercentageWidth,function(e){return e.fold(function(){return n.minCellWidth()},function(e){return e/n.pixelWidth()*100})},n)},getPixelHeights:function(e,t){return Ki(e,t,$r.getHeight,function(e){return e.getOrThunk(Ui.minHeight)})},getRawHeights:function(e,t){return Ki(e,t,Gi,Xi)}},$i=function(e,t,n){for(var r=0,o=e;o<t;o++)r+=n[o]!==undefined?n[o]:0;return r},Qi=function(e,n){var t=mn.justCells(e);return E(t,function(e){var t=$i(e.column(),e.column()+e.colspan(),n);return{element:e.element,width:C(t),colspan:e.colspan}})},Zi=function(e,n){var t=mn.justCells(e);return E(t,function(e){var t=$i(e.row(),e.row()+e.rowspan(),n);return{element:e.element,height:C(t),rowspan:e.rowspan}})},eu=function(e,n){return E(e.all(),function(e,t){return{element:e.element,height:C(n[t])}})},tu=function(e){var t=o;return{width:C(e),pixelWidth:C(e),getWidths:Ji.getPixelWidths,getCellDelta:t,singleColumnWidth:function(e,t){return[Math.max(Ui.minWidth(),e+t)-e]},minCellWidth:Ui.minWidth,setElementWidth:$r.setPixelWidth,setTableWidth:function(e,t,n){var r=P(t,function(e,t){return e+t},0);$r.setPixelWidth(e,r)}}},nu=function(e,t){var n,r,o,i,u=$r.percentageBasedSizeRegex().exec(t);if(null!==u)return n=u[1],r=e,o=parseFloat(n),i=_r(r),{width:C(o),pixelWidth:C(i),getWidths:Ji.getPercentageWidths,getCellDelta:function(e){return e/i*100},singleColumnWidth:function(e,t){return[100-e]},minCellWidth:function(){return Ui.minWidth()/i*100},setElementWidth:$r.setPercentageWidth,setTableWidth:function(e,t,n){var r=n/100*o;$r.setPercentageWidth(e,o+r)}};var a=$r.pixelBasedSizeRegex().exec(t);if(null!==a){var c=parseInt(a[1],10);return tu(c)}var l=_r(e);return tu(l)},ru=function(t){return $r.getRawWidth(t).fold(function(){var e=_r(t);return tu(e)},function(e){return nu(t,e)})},ou=function(e){return mn.generate(e)},iu=function(e){var t=ln(e);return ou(t)},uu=function(e,t,n,r){var o=ru(e),i=o.getCellDelta(t),u=iu(e),a=o.getWidths(u,r,o),c=zi(a,n,i,o),l=E(c,function(e,t){return e+a[t]}),f=Qi(u,l);k(f,function(e){o.setElementWidth(e.element(),e.width())}),n===u.grid().columns()-1&&o.setTableWidth(e,l,i)},au=function(e,n,r,t){var o=iu(e),i=Ji.getPixelHeights(o,t),u=E(i,function(e,t){return r===t?Math.max(n+e,Ui.minHeight()):e}),a=Zi(o,u),c=eu(o,u);k(c,function(e){$r.setHeight(e.element(),e.height())}),k(a,function(e){$r.setHeight(e.element(),e.height())});var l=P(u,function(e,t){return e+t},0);$r.setHeight(e,l)},cu=function(e,t,n){var r=ru(e),o=ou(t),i=r.getWidths(o,n,r),u=Qi(o,i);k(u,function(e){r.setElementWidth(e.element(),e.width())}),0<u.length&&r.setTableWidth(e,i,r.getCellDelta(0))},lu=function(e){var t=e,n=function(){return t};return{get:n,set:function(e){t=e},clone:function(){return lu(n())}}},fu=function(r,o,i){if(0===o.length)throw new Error("You must specify at least one required field.");return X("required",o),K(o),function(t){var n=j(t);L(o,function(e){return O(n,e)})||G(o,n),r(o,n);var e=A(o,function(e){return!i.validate(t[e],e)});return 0<e.length&&function(e,t){throw new Error("All values need to be of type: "+t+". Keys ("+V(e).join(", ")+") were not.")}(e,i.label),t}},su=function(t,e){var n=A(e,function(e){return!O(t,e)});0<n.length&&Y(n)},du=function(e){return fu(su,e,{validate:v,label:"function"})},mu=du(["cell","row","replace","gap"]),gu=function(e){var t=ge(e,"colspan")?parseInt(me(e,"colspan"),10):1,n=ge(e,"rowspan")?parseInt(me(e,"rowspan"),10):1;return{element:C(e),colspan:C(t),rowspan:C(n)}},hu=function(r,o){void 0===o&&(o=gu),mu(r);var n=lu(R.none()),i=function(e){var t,n=o(e);return t=n,r.cell(t)},u=function(e){var t=i(e);return n.get().isNone()&&n.set(R.some(t)),a=R.some({item:e,replacement:t}),t},a=R.none();return{getOrInit:function(t,n){return a.fold(function(){return u(t)},function(e){return n(t,e.item)?e.replacement:u(t)})},cursor:n.get}},pu=function(a,c){return function(r){var o=lu(R.none());mu(r);var i=[],u=function(e){var t={scope:a},n=r.replace(e,c,t);return i.push({item:e,sub:n}),o.get().isNone()&&o.set(R.some(n)),n};return{replaceOrInit:function(t,n){return(r=t,o=n,B(i,function(e){return o(e.item,r)})).fold(function(){return u(t)},function(e){return n(t,e.item)?e.sub:u(t)});var r,o},cursor:o.get}}},vu=function(n){mu(n);var e=lu(R.none());return{combine:function(t){return e.get().isNone()&&e.set(R.some(t)),function(){var e=n.cell({element:C(t),colspan:C(1),rowspan:C(1)});return Ae(e,"width"),Ae(t,"width"),e}},cursor:e.get}},bu=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],wu=function(e,t){var n=e.property().name(t);return O(bu,n)},yu=function(e,t){return O(["br","img","hr","input"],e.property().name(t))},xu=wu,Cu=function(e,t){var n=e.property().name(t);return O(["ol","ul"],n)},Ru=yu,Su=$n(),Tu=function(e){return xu(Su,e)},Du=function(e){return Cu(Su,e)},Ou=function(e){return Ru(Su,e)},Nu=function(e){var t,i=function(e){return"br"===oe(e)},n=function(o){return Sn(o).bind(function(n){var r=vt(n).map(function(e){return!!Tu(e)||!!Ou(e)&&"img"!==oe(e)}).getOr(!1);return gt(n).map(function(e){return!0===r||"li"===oe(t=e)||Kt(t,Du).isSome()||i(n)||Tu(e)&&!ft(o,e)?[]:[xe.fromTag("br")];var t})}).getOr([])},r=0===(t=_(e,function(e){var t=bt(e);return L(t,function(e){return i(e)||le(e)&&0===vn(e).trim().length})?[]:t.concat(n(e))})).length?[xe.fromTag("br")]:t;Ot(e[0]),Dt(e[0],r)},Eu=function(e){0===cn.cells(e).length&&Nt(e)},ku=q("grid","cursor"),Au=function(e,t,n){return Pu(e,t,n).orThunk(function(){return Pu(e,0,0)})},Pu=function(e,t,n){return R.from(e[t]).bind(function(e){return R.from(e.cells()[n]).bind(function(e){return R.from(e.element())})})},Iu=function(e,t,n){return ku(e,Pu(e,t,n))},Bu=function(e){return I(e,function(e,t){return N(e,function(e){return e.row()===t.row()})?e:e.concat([t])},[]).sort(function(e,t){return e.row()-t.row()})},Wu=function(e){return I(e,function(e,t){return N(e,function(e){return e.column()===t.column()})?e:e.concat([t])},[]).sort(function(e,t){return e.column()-t.column()})},Mu=function(e,t,n){var r=fn(e,n),o=mn.generate(r);return ui(o,t,!0)},_u=cu,Lu={insertRowBefore:fi(function(e,t,n,r){var o=t.row(),i=t.row(),u=Ni(e,i,o,n,r.getOrInit);return Iu(u,i,t.column())},si,y,y,hu),insertRowsBefore:fi(function(e,t,n,r){var o=t[0].row(),i=t[0].row(),u=Bu(t),a=I(u,function(e,t){return Ni(e,i,o,n,r.getOrInit)},e);return Iu(a,i,t[0].column())},mi,y,y,hu),insertRowAfter:fi(function(e,t,n,r){var o=t.row(),i=t.row()+t.rowspan(),u=Ni(e,i,o,n,r.getOrInit);return Iu(u,i,t.column())},si,y,y,hu),insertRowsAfter:fi(function(e,t,n,r){var o=Bu(t),i=o[o.length-1].row(),u=o[o.length-1].row()+o[o.length-1].rowspan(),a=I(o,function(e,t){return Ni(e,u,i,n,r.getOrInit)},e);return Iu(a,u,t[0].column())},mi,y,y,hu),insertColumnBefore:fi(function(e,t,n,r){var o=t.column(),i=t.column(),u=Ei(e,i,o,n,r.getOrInit);return Iu(u,t.row(),i)},si,_u,y,hu),insertColumnsBefore:fi(function(e,t,n,r){var o=Wu(t),i=o[0].column(),u=o[0].column(),a=I(o,function(e,t){return Ei(e,u,i,n,r.getOrInit)},e);return Iu(a,t[0].row(),u)},mi,_u,y,hu),insertColumnAfter:fi(function(e,t,n,r){var o=t.column(),i=t.column()+t.colspan(),u=Ei(e,i,o,n,r.getOrInit);return Iu(u,t.row(),i)},si,_u,y,hu),insertColumnsAfter:fi(function(e,t,n,r){var o=t[t.length-1].column(),i=t[t.length-1].column()+t[t.length-1].colspan(),u=Wu(t),a=I(u,function(e,t){return Ei(e,i,o,n,r.getOrInit)},e);return Iu(a,t[0].row(),i)},mi,_u,y,hu),splitCellIntoColumns:fi(function(e,t,n,r){var o=ki(e,t.row(),t.column(),n,r.getOrInit);return Iu(o,t.row(),t.column())},si,_u,y,hu),splitCellIntoRows:fi(function(e,t,n,r){var o=Ai(e,t.row(),t.column(),n,r.getOrInit);return Iu(o,t.row(),t.column())},si,y,y,hu),eraseColumns:fi(function(e,t,n,r){var o=Wu(t),i=Ii(e,o[0].column(),o[o.length-1].column()),u=Au(i,t[0].row(),t[0].column());return ku(i,u)},mi,_u,Eu,hu),eraseRows:fi(function(e,t,n,r){var o=Bu(t),i=Pi(e,o[0].row(),o[o.length-1].row()),u=Au(i,t[0].row(),t[0].column());return ku(i,u)},mi,y,Eu,hu),makeColumnHeader:fi(function(e,t,n,r){var o=_i(e,t.column(),n,r.replaceOrInit);return Iu(o,t.row(),t.column())},si,y,y,pu("row","th")),unmakeColumnHeader:fi(function(e,t,n,r){var o=_i(e,t.column(),n,r.replaceOrInit);return Iu(o,t.row(),t.column())},si,y,y,pu(null,"td")),makeRowHeader:fi(function(e,t,n,r){var o=Li(e,t.row(),n,r.replaceOrInit);return Iu(o,t.row(),t.column())},si,y,y,pu("col","th")),unmakeRowHeader:fi(function(e,t,n,r){var o=Li(e,t.row(),n,r.replaceOrInit);return Iu(o,t.row(),t.column())},si,y,y,pu(null,"td")),mergeCells:fi(function(e,t,n,r){var o=t.cells();Nu(o);var i=Ri(e,t.bounds(),n,C(o[0]));return ku(i,R.from(o[0]))},function(e,t){return t.mergable()},y,y,vu),unmergeCells:fi(function(e,t,n,r){var o=P(t,function(e,t){return Si(e,t,n,r.combine(t))},e);return ku(o,R.from(t[0]))},function(e,t){return t.unmergable()},_u,y,vu),pasteCells:fi(function(e,n,t,r){var o,i,u,a,c=(o=n.clipboard(),i=n.generators(),u=ln(o),a=mn.generate(u),ui(a,i,!0)),l=At(n.row(),n.column());return Di(l,e,c,n.generators(),t).fold(function(){return ku(e,R.some(n.element()))},function(e){var t=Au(e,n.row(),n.column());return ku(e,t)})},function(t,n){return cn.cell(n.element()).bind(function(e){return li(t,e).map(function(e){return wo(wo({},e),{generators:n.generators,clipboard:n.clipboard})})})},_u,y,hu),pasteRowsBefore:fi(function(e,t,n,r){var o=e[t.cells[0].row()],i=t.cells[0].row(),u=Mu(t.clipboard(),t.generators(),o),a=Oi(i,e,u,t.generators(),n),c=Au(a,t.cells[0].row(),t.cells[0].column());return ku(a,c)},di,y,y,hu),pasteRowsAfter:fi(function(e,t,n,r){var o=e[t.cells[0].row()],i=t.cells[t.cells.length-1].row()+t.cells[t.cells.length-1].rowspan(),u=Mu(t.clipboard(),t.generators(),o),a=Oi(i,e,u,t.generators(),n),c=Au(a,t.cells[0].row(),t.cells[0].column());return ku(a,c)},di,y,y,hu)},Fu=function(e){return xe.fromDom(e.getBody())},ju=function(e){return e.getBoundingClientRect().width},zu=function(e){return e.getBoundingClientRect().height},Hu=function(t){return function(e){return ft(e,Fu(t))}},Uu=function(e){return/^[0-9]+$/.test(e)&&(e+="px"),e},qu=function(e){var t=qt(e,"td[data-mce-style],th[data-mce-style]");he(e,"data-mce-style"),k(t,function(e){he(e,"data-mce-style")})},Vu={isRtl:C(!1)},Gu={isRtl:C(!0)},Yu={directionAt:function(e){return"rtl"==("rtl"===Ne(e,"direction")?"rtl":"ltr")?Gu:Vu}},Xu=["tableprops","tabledelete","|","tableinsertrowbefore","tableinsertrowafter","tabledeleterow","|","tableinsertcolbefore","tableinsertcolafter","tabledeletecol"],Ku={"border-collapse":"collapse",width:"100%"},Ju={border:"1"},$u=function(e){return e.getParam("table_cell_advtab",!0,"boolean")},Qu=function(e){return e.getParam("table_row_advtab",!0,"boolean")},Zu=function(e){return e.getParam("table_advtab",!0,"boolean")},ea=function(e){return e.getParam("table_style_by_css",!1,"boolean")},ta=function(e){return e.getParam("table_cell_class_list",[],"array")},na=function(e){return e.getParam("table_row_class_list",[],"array")},ra=function(e){return e.getParam("table_class_list",[],"array")},oa=function(e){return!1===e.getParam("table_responsive_width")},ia=function(e,t){return e.fire("newrow",{node:t})},ua=function(e,t){return e.fire("newcell",{node:t})},aa=function(e,t,n,r){e.fire("ObjectResizeStart",{target:t,width:n,height:r})},ca=function(e,t,n,r){e.fire("ObjectResized",{target:t,width:n,height:r})},la=function(f,e){var t,n=function(e){return"table"===oe(Fu(e))},s=(t=f.getParam("table_clone_elements"),d(t)?R.some(t.split(/[ ,]/)):Array.isArray(t)?R.some(t):R.none()),r=function(u,a,c,l){return function(e,t){qu(e);var n=l(),r=xe.fromDom(f.getDoc()),o=vo(Yu.directionAt),i=Bn(c,r,s);return a(e)?u(n,e,t,i,o).bind(function(e){return k(e.newRows(),function(e){ia(f,e.dom())}),k(e.newCells(),function(e){ua(f,e.dom())}),e.cursor().map(function(e){var t=f.dom.createRng();return t.setStart(e.dom(),0),t.setEnd(e.dom(),0),t})}):R.none()}};return{deleteRow:r(Lu.eraseRows,function(e){var t=bo(e);return!1===n(f)||1<t.rows()},y,e),deleteColumn:r(Lu.eraseColumns,function(e){var t=bo(e);return!1===n(f)||1<t.columns()},y,e),insertRowsBefore:r(Lu.insertRowsBefore,u,y,e),insertRowsAfter:r(Lu.insertRowsAfter,u,y,e),insertColumnsBefore:r(Lu.insertColumnsBefore,u,Qr,e),insertColumnsAfter:r(Lu.insertColumnsAfter,u,Qr,e),mergeCells:r(Lu.mergeCells,u,y,e),unmergeCells:r(Lu.unmergeCells,u,y,e),pasteRowsBefore:r(Lu.pasteRowsBefore,u,y,e),pasteRowsAfter:r(Lu.pasteRowsAfter,u,y,e),pasteCells:r(Lu.pasteCells,u,y,e)}},fa=function(e,t,r){var n=ln(e),o=mn.generate(n);return mi(o,t).map(function(e){var t=ui(o,r,!1).slice(e[0].row(),e[e.length-1].row()+e[e.length-1].rowspan()),n=ci(t,r);return So(n)})},sa=tinymce.util.Tools.resolve("tinymce.util.Tools"),da=function(e,t,n){n&&e.formatter.apply("align"+n,{},t)},ma=function(e,t,n){n&&e.formatter.apply("valign"+n,{},t)},ga=function(t,n){sa.each("left center right".split(" "),function(e){t.formatter.remove("align"+e,{},n)})},ha=function(t,n){sa.each("top middle bottom".split(" "),function(e){t.formatter.remove("valign"+e,{},n)})},pa=function(o,e,i){var t;return t=function(e,t){for(var n=0;n<t.length;n++){var r=o.getStyle(t[n],i);if(void 0===e&&(e=r),e!==r)return""}return e}(t,o.select("td,th",e))},va=function(e,t){var n=e.dom,r=t.control.rootControl,o=r.toJSON(),i=n.parseStyle(o.style);i["border-style"]=o.borderStyle,i["border-color"]=o.borderColor,i["background-color"]=o.backgroundColor,i.width=o.width?Uu(o.width):"",i.height=o.height?Uu(o.height):"",r.find("#style").value(n.serializeStyle(n.parseStyle(n.serializeStyle(i))))},ba=function(e,t){var n=e.dom,r=t.control.rootControl,o=r.toJSON(),i=n.parseStyle(o.style);r.find("#borderStyle").value(i["border-style"]||""),r.find("#borderColor").value(i["border-color"]||""),r.find("#backgroundColor").value(i["background-color"]||""),r.find("#width").value(i.width||""),r.find("#height").value(i.height||"")},wa={createStyleForm:function(n){var e=function(){var e=n.getParam("color_picker_callback");if(e)return function(t){return e.call(n,function(e){t.control.value(e).fire("change")},t.control.value())}};return{title:"Advanced",type:"form",defaults:{onchange:b(va,n)},items:[{label:"Style",name:"style",type:"textbox",onchange:b(ba,n)},{type:"form",padding:0,formItemDefaults:{layout:"grid",alignH:["start","right"]},defaults:{size:7},items:[{label:"Border style",type:"listbox",name:"borderStyle",width:90,onselect:b(va,n),values:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{label:"Border color",type:"colorbox",name:"borderColor",onaction:e()},{label:"Background color",type:"colorbox",name:"backgroundColor",onaction:e()}]}]}},buildListItems:function(e,r,t){var o=function(e,n){return n=n||[],sa.each(e,function(e){var t={text:e.text||e.title};e.menu?t.menu=o(e.menu):(t.value=e.value,r&&r(t)),n.push(t)}),n};return o(e,t||[])},updateStyleField:va,extractAdvancedStyles:function(e,t){var n=e.parseStyle(e.getAttrib(t,"style")),r={};return n["border-style"]&&(r.borderStyle=n["border-style"]),n["border-color"]&&(r.borderColor=n["border-color"]),n["background-color"]&&(r.backgroundColor=n["background-color"]),r.style=e.serializeStyle(n),r},updateAdvancedFields:ba,syncAdvancedStyleFields:function(e,t){t.control.rootControl.find("#style")[0].getEl().isEqualNode(m.document.activeElement)?ba(e,t):va(e,t)}},ya=function(r,o,e){var i,u=r.dom;function a(e,t,n){(1===o.length||n)&&u.setAttrib(e,t,n)}function c(e,t,n){(1===o.length||n)&&u.setStyle(e,t,n)}$u(r)&&wa.syncAdvancedStyleFields(r,e),i=e.control.rootControl.toJSON(),r.undoManager.transact(function(){sa.each(o,function(e){var t,n;a(e,"scope",i.scope),1===o.length?a(e,"style",i.style):(t=e,n=i.style,delete t.dataset.mceStyle,t.style.cssText+=";"+n),a(e,"class",i["class"]),c(e,"width",Uu(i.width)),c(e,"height",Uu(i.height)),i.type&&e.nodeName.toLowerCase()!==i.type&&(e=u.rename(e,i.type)),1===o.length&&(ga(r,e),ha(r,e)),i.align&&da(r,e,i.align),i.valign&&ma(r,e,i.valign)}),r.focus()})},xa=function(t){var e,n,r,o=[];if(o=t.dom.select("td[data-mce-selected],th[data-mce-selected]"),e=t.dom.getParent(t.selection.getStart(),"td,th"),!o.length&&e&&o.push(e),e=e||o[0]){var i,u,a,c;1<o.length?n={width:"",height:"",scope:"","class":"",align:"",valign:"",style:"",type:e.nodeName.toLowerCase()}:(u=e,a=(i=t).dom,c={width:a.getStyle(u,"width")||a.getAttrib(u,"width"),height:a.getStyle(u,"height")||a.getAttrib(u,"height"),scope:a.getAttrib(u,"scope"),"class":a.getAttrib(u,"class"),type:u.nodeName.toLowerCase(),style:"",align:"",valign:""},sa.each("left center right".split(" "),function(e){i.formatter.matchNode(u,"align"+e)&&(c.align=e)}),sa.each("top middle bottom".split(" "),function(e){i.formatter.matchNode(u,"valign"+e)&&(c.valign=e)}),$u(i)&&sa.extend(c,wa.extractAdvancedStyles(a,u)),n=c),0<ta(t).length&&(r={name:"class",type:"listbox",label:"Class",values:wa.buildListItems(ta(t),function(e){e.value&&(e.textStyle=function(){return t.formatter.getCssText({block:"td",classes:[e.value]})})})});var l={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",layout:"grid",columns:2,labelGapCalc:!1,padding:0,defaults:{type:"textbox",maxWidth:50},items:[{label:"Width",name:"width",onchange:b(wa.updateStyleField,t)},{label:"Height",name:"height",onchange:b(wa.updateStyleField,t)},{label:"Cell type",name:"type",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{label:"Scope",name:"scope",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{label:"H Align",name:"align",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"V Align",name:"valign",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}]},r]};$u(t)?t.windowManager.open({title:"Cell properties",bodyType:"tabpanel",data:n,body:[{title:"General",type:"form",items:l},wa.createStyleForm(t)],onsubmit:b(ya,t,o)}):t.windowManager.open({title:"Cell properties",data:n,body:l,onsubmit:b(ya,t,o)})}};function Ca(f,s,d,e){var m=f.dom;function g(e,t,n){(1===s.length||n)&&m.setAttrib(e,t,n)}Qu(f)&&wa.syncAdvancedStyleFields(f,e);var h=e.control.rootControl.toJSON();f.undoManager.transact(function(){sa.each(s,function(e){var t,n,r,o,i,u,a,c,l;g(e,"scope",h.scope),g(e,"style",h.style),g(e,"class",h["class"]),t=e,n="height",r=Uu(h.height),(1===s.length||r)&&m.setStyle(t,n,r),h.type!==e.parentNode.nodeName.toLowerCase()&&(o=f.dom,i=e,u=h.type,a=o.getParent(i,"table"),c=i.parentNode,(l=o.select(u,a)[0])||(l=o.create(u),a.firstChild?"CAPTION"===a.firstChild.nodeName?o.insertAfter(l,a.firstChild):a.insertBefore(l,a.firstChild):a.appendChild(l)),l.appendChild(i),c.hasChildNodes()||o.remove(c)),h.align!==d.align&&(ga(f,e),da(f,e,h.align))}),f.focus()})}var Ra=function(t){var e,n,r,o,i,u,a,c,l,f,s=t.dom,d=[];e=s.getParent(t.selection.getStart(),"table"),n=s.getParent(t.selection.getStart(),"td,th"),sa.each(e.rows,function(t){sa.each(t.cells,function(e){if(s.getAttrib(e,"data-mce-selected")||e===n)return d.push(t),!1})}),(r=d[0])&&(1<d.length?i={height:"",scope:"",style:"","class":"",align:"",type:r.parentNode.nodeName.toLowerCase()}:(c=r,l=(a=t).dom,f={height:l.getStyle(c,"height")||l.getAttrib(c,"height"),scope:l.getAttrib(c,"scope"),"class":l.getAttrib(c,"class"),align:"",style:"",type:c.parentNode.nodeName.toLowerCase()},sa.each("left center right".split(" "),function(e){a.formatter.matchNode(c,"align"+e)&&(f.align=e)}),Qu(a)&&sa.extend(f,wa.extractAdvancedStyles(l,c)),i=f),0<na(t).length&&(o={name:"class",type:"listbox",label:"Class",values:wa.buildListItems(na(t),function(e){e.value&&(e.textStyle=function(){return t.formatter.getCssText({block:"tr",classes:[e.value]})})})}),u={type:"form",columns:2,padding:0,defaults:{type:"textbox"},items:[{type:"listbox",name:"type",label:"Row type",text:"Header",maxWidth:null,values:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"listbox",name:"align",label:"Alignment",text:"None",maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height"},o]},Qu(t)?t.windowManager.open({title:"Row properties",data:i,bodyType:"tabpanel",body:[{title:"General",type:"form",items:u},wa.createStyleForm(t)],onsubmit:b(Ca,t,d,i)}):t.windowManager.open({title:"Row properties",data:i,body:u,onsubmit:b(Ca,t,d,i)}))},Sa=tinymce.util.Tools.resolve("tinymce.Env"),Ta={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},percentages:!0},Da=function(e,t,n,r,o){void 0===o&&(o=Ta);var i=xe.fromTag("table");Oe(i,o.styles),de(i,o.attributes);var u=xe.fromTag("tbody");Rt(i,u);for(var a=[],c=0;c<e;c++){for(var l=xe.fromTag("tr"),f=0;f<t;f++){var s=c<n||f<r?xe.fromTag("th"):xe.fromTag("td");f<r&&se(s,"scope","row"),c<n&&se(s,"scope","col"),Rt(s,xe.fromTag("br")),o.percentages&&De(s,"width",100/t+"%"),Rt(l,s)}a.push(l)}return Dt(u,a),i},Oa=function(e,t){e.selection.select(t.dom(),!0),e.selection.collapse(!0)},Na=function(r,e,t){var n,o,i=r.getParam("table_default_styles",Ku,"object"),u={styles:i,attributes:(o=r,o.getParam("table_default_attributes",Ju,"object")),percentages:(n=i.width,d(n)&&-1!==n.indexOf("%")&&!oa(r))},a=Da(t,e,0,0,u);se(a,"data-mce-id","__mce");var c,l,f,s=(c=a,l=xe.fromTag("div"),f=xe.fromDom(c.dom().cloneNode(!0)),Rt(l,f),l.dom().innerHTML);return r.insertContent(s),Qt(Fu(r),'table[data-mce-id="__mce"]').map(function(e){var t,n;return oa(r)&&De(e,"width",Ne(e,"width")),he(e,"data-mce-id"),t=r,k(qt(e,"tr"),function(e){ia(t,e.dom()),k(qt(e,"th,td"),function(e){ua(t,e.dom())})}),n=r,Qt(e,"td,th").each(b(Oa,n)),e.dom()}).getOr(null)};function Ea(e,t,n,r){if("TD"===t.tagName||"TH"===t.tagName)e.setStyle(t,n,r);else if(t.children)for(var o=0;o<t.children.length;o++)Ea(e,t.children[o],n,r)}var ka,Aa=function(e,t,n){var r,o,i=e.dom;Zu(e)&&wa.syncAdvancedStyleFields(e,n),!1===(o=n.control.rootControl.toJSON())["class"]&&delete o["class"],e.undoManager.transact(function(){t||(t=Na(e,o.cols||1,o.rows||1)),function(e,t,n){var r,o=e.dom,i={},u={};if(i["class"]=n["class"],u.height=Uu(n.height),o.getAttrib(t,"width")&&!ea(e)?i.width=(r=n.width)?r.replace(/px$/,""):"":u.width=Uu(n.width),ea(e)?(u["border-width"]=Uu(n.border),u["border-spacing"]=Uu(n.cellspacing),sa.extend(i,{"data-mce-border-color":n.borderColor,"data-mce-cell-padding":n.cellpadding,"data-mce-border":n.border})):sa.extend(i,{border:n.border,cellpadding:n.cellpadding,cellspacing:n.cellspacing}),ea(e)&&t.children)for(var a=0;a<t.children.length;a++)Ea(o,t.children[a],{"border-width":Uu(n.border),"border-color":n.borderColor,padding:Uu(n.cellpadding)});n.style?sa.extend(u,o.parseStyle(n.style)):u=sa.extend({},o.parseStyle(o.getAttrib(t,"style")),u),i.style=o.serializeStyle(u),o.setAttribs(t,i)}(e,t,o),(r=i.select("caption",t)[0])&&!o.caption&&i.remove(r),!r&&o.caption&&((r=i.create("caption")).innerHTML=Sa.ie?"\xa0":'<br data-mce-bogus="1"/>',t.insertBefore(r,t.firstChild)),ga(e,t),o.align&&da(e,t,o.align),e.focus(),e.addVisual()})},Pa=function(t,e){var n,r,o,i,u,a,c,l,f,s,d=t.dom,m={};!0===e?(n=d.getParent(t.selection.getStart(),"table"))&&(c=n,l=(a=t).dom,f={width:l.getStyle(c,"width")||l.getAttrib(c,"width"),height:l.getStyle(c,"height")||l.getAttrib(c,"height"),cellspacing:l.getStyle(c,"border-spacing")||l.getAttrib(c,"cellspacing"),cellpadding:l.getAttrib(c,"data-mce-cell-padding")||l.getAttrib(c,"cellpadding")||pa(a.dom,c,"padding"),border:l.getAttrib(c,"data-mce-border")||l.getAttrib(c,"border")||pa(a.dom,c,"border"),borderColor:l.getAttrib(c,"data-mce-border-color"),caption:!!l.select("caption",c)[0],"class":l.getAttrib(c,"class")},sa.each("left center right".split(" "),function(e){a.formatter.matchNode(c,"align"+e)&&(f.align=e)}),Zu(a)&&sa.extend(f,wa.extractAdvancedStyles(l,c)),m=f):(r={label:"Cols",name:"cols"},o={label:"Rows",name:"rows"}),0<ra(t).length&&(m["class"]&&(m["class"]=m["class"].replace(/\s*mce\-item\-table\s*/g,"")),i={name:"class",type:"listbox",label:"Class",values:wa.buildListItems(ra(t),function(e){e.value&&(e.textStyle=function(){return t.formatter.getCssText({block:"table",classes:[e.value]})})})}),u={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",labelGapCalc:!1,padding:0,layout:"grid",columns:2,defaults:{type:"textbox",maxWidth:50},items:(s=t,s.getParam("table_appearance_options",!0,"boolean")?[r,o,{label:"Width",name:"width",onchange:b(wa.updateStyleField,t)},{label:"Height",name:"height",onchange:b(wa.updateStyleField,t)},{label:"Cell spacing",name:"cellspacing"},{label:"Cell padding",name:"cellpadding"},{label:"Border",name:"border"},{label:"Caption",name:"caption",type:"checkbox"}]:[r,o,{label:"Width",name:"width",onchange:b(wa.updateStyleField,t)},{label:"Height",name:"height",onchange:b(wa.updateStyleField,t)}])},{label:"Alignment",name:"align",type:"listbox",text:"None",values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},i]},Zu(t)?t.windowManager.open({title:"Table properties",data:m,bodyType:"tabpanel",body:[{title:"General",type:"form",items:u},wa.createStyleForm(t)],onsubmit:b(Aa,t,n)}):t.windowManager.open({title:"Table properties",data:m,body:u,onsubmit:b(Aa,t,n)})},Ia=sa.each,Ba=function(a,t,c,l,n){var r=Hu(a),e=function(e){return function(){return R.from(a.dom.getParent(a.selection.getStart(),e)).map(xe.fromDom)}},o=e("caption"),f=e("th,td"),s=function(e){return cn.table(e,r)},d=function(e){return{width:ju(e.dom()),height:ju(e.dom())}},i=function(n){f().each(function(t){s(t).each(function(i){var e=kr.forMenu(l,i,t),u=d(i);n(i,e).each(function(e){var t,n,r,o;t=a,n=u,o=d(r=i),n.width===o.width&&n.height===o.height||(aa(t,r.dom(),n.width,n.height),ca(t,r.dom(),o.width,o.height)),a.selection.setRng(e),a.focus(),c.clear(i),qu(i)})})})},u=function(e){return f().bind(function(o){return s(o).bind(function(e){var t=xe.fromDom(a.getDoc()),n=kr.forMenu(l,e,o),r=Bn(y,t,R.none());return fa(e,n,r)})})},m=function(u){n.get().each(function(e){var i=E(e,function(e){return Nn(e)});f().each(function(o){s(o).each(function(t){var e=xe.fromDom(a.getDoc()),n=Wn(e),r=kr.pasteRows(l,t,o,i,n);u(t,r).each(function(e){a.selection.setRng(e),a.focus(),c.clear(t)})})})})};Ia({mceTableSplitCells:function(){i(t.unmergeCells)},mceTableMergeCells:function(){i(t.mergeCells)},mceTableInsertRowBefore:function(){i(t.insertRowsBefore)},mceTableInsertRowAfter:function(){i(t.insertRowsAfter)},mceTableInsertColBefore:function(){i(t.insertColumnsBefore)},mceTableInsertColAfter:function(){i(t.insertColumnsAfter)},mceTableDeleteCol:function(){i(t.deleteColumn)},mceTableDeleteRow:function(){i(t.deleteRow)},mceTableCutRow:function(e){n.set(u()),i(t.deleteRow)},mceTableCopyRow:function(e){n.set(u())},mceTablePasteRowBefore:function(e){m(t.pasteRowsBefore)},mceTablePasteRowAfter:function(e){m(t.pasteRowsAfter)},mceTableDelete:function(){f().orThunk(o).each(function(e){cn.table(e,r).filter(g(r)).each(function(e){var t=xe.fromText("");xt(e,t),Nt(e);var n=a.dom.createRng();n.setStart(t.dom(),0),n.setEnd(t.dom(),0),a.selection.setRng(n)})})}},function(e,t){a.addCommand(t,e)}),Ia({mceInsertTable:b(Pa,a),mceTableProps:b(Pa,a,!0),mceTableRowProps:b(Ra,a),mceTableCellProps:b(xa,a)},function(n,e){a.addCommand(e,function(e,t){n(t)})})},Wa=function(e){var t=R.from(e.dom().documentElement).map(xe.fromDom).getOr(e);return{parent:C(t),view:C(e),origin:C(eo(0,0))}},Ma=function(e,t){return{parent:C(t),view:C(e),origin:C(eo(0,0))}},_a=function(e){var r=q.apply(null,e),o=[];return{bind:function(e){if(e===undefined)throw new Error("Event bind error: undefined handler");o.push(e)},unbind:function(t){o=A(o,function(e){return e!==t})},trigger:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=r.apply(null,e);k(o,function(e){e(n)})}}},La={create:function(e){return{registry:H(e,function(e){return{bind:e.bind,unbind:e.unbind}}),trigger:H(e,function(e){return e.trigger})}}},Fa=function(m,g){return function(e){if(m(e)){var t,n,r,o,i,u,a,c=xe.fromDom(e.target),l=function(){e.stopPropagation()},f=function(){e.preventDefault()},s=x(f,l),d=(t=c,n=e.clientX,r=e.clientY,o=l,i=f,u=s,a=e,{target:C(t),x:C(n),y:C(r),stop:o,prevent:i,kill:u,raw:C(a)});g(d)}}},ja=function(e,t,n,r){return o=e,i=t,u=!1,a=Fa(n,r),o.dom().addEventListener(i,a,u),{unbind:b(za,o,i,a,u)};var o,i,u,a},za=function(e,t,n,r){e.dom().removeEventListener(t,n,r)},Ha=C(!0),Ua=function(e,t,n){return ja(e,t,Ha,n)},qa=Object.prototype.hasOwnProperty,Va=(ka=function(e,t){return t},function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];if(0===e.length)throw new Error("Can't merge zero objects");for(var n={},r=0;r<e.length;r++){var o=e[r];for(var i in o)qa.call(o,i)&&(n[i]=ka(n[i],o[i]))}return n}),Ga={resolve:_o("ephox-dragster").resolve},Ya=du(["compare","extract","mutate","sink"]),Xa=du(["element","start","stop","destroy"]),Ka=du(["forceDrop","drop","move","delayDrop"]),Ja=Ya({compare:function(e,t){return eo(t.left()-e.left(),t.top()-e.top())},extract:function(e){return R.some(eo(e.x(),e.y()))},sink:function(e,t){var n,r,o,i=(n=t,r=Va({layerClass:Ga.resolve("blocker")},n),o=xe.fromTag("div"),se(o,"role","presentation"),Oe(o,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),Eo(o,Ga.resolve("blocker")),Eo(o,r.layerClass),{element:function(){return o},destroy:function(){Nt(o)}}),u=Ua(i.element(),"mousedown",e.forceDrop),a=Ua(i.element(),"mouseup",e.drop),c=Ua(i.element(),"mousemove",e.move),l=Ua(i.element(),"mouseout",e.delayDrop);return Xa({element:i.element,start:function(e){Rt(e,i.element())},stop:function(){Nt(i.element())},destroy:function(){i.destroy(),a.unbind(),c.unbind(),l.unbind(),u.unbind()}})},mutate:function(e,t){e.mutate(t.left(),t.top())}});function $a(){var i=R.none(),u=La.create({move:_a(["info"])});return{onEvent:function(e,o){o.extract(e).each(function(e){var t,n,r;(t=o,n=e,r=i.map(function(e){return t.compare(e,n)}),i=R.some(n),r).each(function(e){u.trigger.move(e)})})},reset:function(){i=R.none()},events:u.registry}}function Qa(){var e={onEvent:y,reset:y},t=$a(),n=e;return{on:function(){n.reset(),n=t},off:function(){n.reset(),n=e},isOn:function(){return n===t},onEvent:function(e,t){n.onEvent(e,t)},events:t.events}}var Za=function(t,n,e){var r,o,i,u=!1,a=La.create({start:_a([]),stop:_a([])}),c=Qa(),l=function(){d.stop(),c.isOn()&&(c.off(),a.trigger.stop())},f=(r=l,o=200,i=null,{cancel:function(){null!==i&&(m.clearTimeout(i),i=null)},throttle:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];null!==i&&m.clearTimeout(i),i=m.setTimeout(function(){r.apply(null,e),i=null},o)}});c.events.move.bind(function(e){n.mutate(t,e.info())});var s=function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];u&&n.apply(null,e)}},d=n.sink(Ka({forceDrop:l,drop:s(l),move:s(function(e){f.cancel(),c.onEvent(e,n)}),delayDrop:s(f.throttle)}),e);return{element:d.element,go:function(e){d.start(e),c.on(),a.trigger.start()},on:function(){u=!0},off:function(){u=!1},destroy:function(){d.destroy()},events:a.registry}},ec=function(e,t){void 0===t&&(t={});var n=t.mode!==undefined?t.mode:Ja;return Za(e,n,t)},tc=function(){var n,r=La.create({drag:_a(["xDelta","yDelta","target"])}),o=R.none(),e={mutate:function(e,t){n.trigger.drag(e,t)},events:(n=La.create({drag:_a(["xDelta","yDelta"])})).registry};return e.events.drag.bind(function(t){o.each(function(e){r.trigger.drag(t.xDelta(),t.yDelta(),e)})}),{assign:function(e){o=R.some(e)},get:function(){return o},mutate:e.mutate,events:r.registry}},nc=function(e){return"true"===me(e,"contenteditable")},rc=Lo.resolve("resizer-bar-dragging"),oc=function(o,t,i){var n=tc(),r=ec(n,{}),u=R.none(),e=function(e,t){return R.from(me(e,t))};n.events.drag.bind(function(n){e(n.target(),"data-row").each(function(e){var t=Ui.getInt(n.target(),"top");De(n.target(),"top",t+n.yDelta()+"px")}),e(n.target(),"data-column").each(function(e){var t=Ui.getInt(n.target(),"left");De(n.target(),"left",t+n.xDelta()+"px")})});var a=function(e,t){return Ui.getInt(e,t)-parseInt(me(e,"data-initial-"+t),10)};r.events.stop.bind(function(){n.get().each(function(r){u.each(function(n){e(r,"data-row").each(function(e){var t=a(r,"top");he(r,"data-initial-top"),m.trigger.adjustHeight(n,t,parseInt(e,10))}),e(r,"data-column").each(function(e){var t=a(r,"left");he(r,"data-initial-left"),m.trigger.adjustWidth(n,t,parseInt(e,10))}),Xo(o,n,i,t)})})});var c=function(e,t){m.trigger.startAdjust(),n.assign(e),se(e,"data-initial-"+t,parseInt(Ne(e,t),10)),Eo(e,rc),De(e,"opacity","0.2"),r.go(o.parent())},l=Ua(o.parent(),"mousedown",function(e){Qo(e.target())&&c(e.target(),"top"),Zo(e.target())&&c(e.target(),"left")}),f=function(e){return ft(e,o.view())},s=function(e){return Zt(e,"table",f).filter(function(e){return(t=e,n=f,Zt(t,"[contenteditable]",n)).exists(nc);var t,n})},d=Ua(o.view(),"mouseover",function(e){s(e.target()).fold(function(){Ce(e.target())&&$o(o)},function(e){u=R.some(e),Xo(o,e,i,t)})}),m=La.create({adjustHeight:_a(["table","delta","row"]),adjustWidth:_a(["table","delta","column"]),startAdjust:_a([])});return{destroy:function(){l.unbind(),d.unbind(),r.destroy(),$o(o)},refresh:function(e){Xo(o,e,i,t)},on:r.on,off:r.off,hideBars:b(Ko,o),showBars:b(Jo,o),events:m.registry}},ic=function(e,n){var r=ho.height,t=oc(e,n,r),o=La.create({beforeResize:_a(["table"]),afterResize:_a(["table"]),startDrag:_a([])});return t.events.adjustHeight.bind(function(e){o.trigger.beforeResize(e.table());var t=r.delta(e.delta(),e.table());au(e.table(),t,e.row(),r),o.trigger.afterResize(e.table())}),t.events.startAdjust.bind(function(e){o.trigger.startDrag()}),t.events.adjustWidth.bind(function(e){o.trigger.beforeResize(e.table());var t=n.delta(e.delta(),e.table());uu(e.table(),t,e.column(),n),o.trigger.afterResize(e.table())}),{on:t.on,off:t.off,hideBars:t.hideBars,showBars:t.showBars,destroy:t.destroy,events:o.registry}},uc=function(e,t){return e.inline?Ma(Fu(e),(n=xe.fromTag("div"),Oe(n,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Rt(Re(),n),n)):Wa(xe.fromDom(e.getDoc()));var n},ac=function(e,t){e.inline&&Nt(t.parent())},cc=function(u){var a,c,o=R.none(),i=R.none(),l=R.none(),f=/(\d+(\.\d+)?)%/,s=function(e){return"TABLE"===e.nodeName};return u.on("init",function(){var e,t=vo(Yu.directionAt),n=uc(u);if(l=R.some(n),("table"===(e=u.getParam("object_resizing",!0))||e)&&u.getParam("table_resize_bars",!0,"boolean")){var r=ic(n,t);r.on(),r.events.startDrag.bind(function(e){o=R.some(u.selection.getRng())}),r.events.beforeResize.bind(function(e){var t=e.table().dom();aa(u,t,ju(t),zu(t))}),r.events.afterResize.bind(function(e){var t=e.table(),n=t.dom();qu(t),o.each(function(e){u.selection.setRng(e),u.focus()}),ca(u,n,ju(n),zu(n)),u.undoManager.add()}),i=R.some(r)}}),u.on("ObjectResizeStart",function(e){var t,n=e.target;s(n)&&(a=e.width,t=n,c=u.dom.getStyle(t,"width")||u.dom.getAttrib(t,"width"))}),u.on("ObjectResized",function(e){var t=e.target;if(s(t)){var n=t;if(f.test(c)){var r=parseFloat(f.exec(c)[1]),o=e.width*r/a;u.dom.setStyle(n,"width",o+"%")}else{var i=[];sa.each(n.rows,function(e){sa.each(e.cells,function(e){var t=u.dom.getStyle(e,"width",!0);i.push({cell:e,width:t})})}),sa.each(i,function(e){u.dom.setStyle(e.cell,"width",e.width),u.dom.setAttrib(e.cell,"width",null)})}}}),{lazyResize:function(){return i},lazyWire:function(){return l.getOr(Wa(xe.fromDom(u.getBody())))},destroy:function(){i.each(function(e){e.destroy()}),l.each(function(e){ac(u,e)})}}},lc=xr([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),fc=wo(wo({},lc),{none:function(e){return void 0===e&&(e=undefined),lc.none(e)}}),sc=function(n,e){return cn.table(n,e).bind(function(e){var t=cn.cells(e);return W(t,function(e){return ft(n,e)}).map(function(e){return{index:C(e),all:C(t)}})})},dc=function(t,e){return sc(t,e).fold(function(){return fc.none(t)},function(e){return e.index()+1<e.all().length?fc.middle(t,e.all()[e.index()+1]):fc.last(t)})},mc=function(t,e){return sc(t,e).fold(function(){return fc.none()},function(e){return 0<=e.index()-1?fc.middle(t,e.all()[e.index()-1]):fc.first(t)})},gc={create:q("start","soffset","finish","foffset")},hc=xr([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),pc={before:hc.before,on:hc.on,after:hc.after,cata:function(e,t,n,r){return e.fold(t,n,r)},getStart:function(e){return e.fold(o,o,o)}},vc=xr([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),bc={domRange:vc.domRange,relative:vc.relative,exact:vc.exact,exactFromRange:function(e){return vc.exact(e.start(),e.soffset(),e.finish(),e.foffset())},getWin:function(e){var t,n=e.match({domRange:function(e){return xe.fromDom(e.startContainer)},relative:function(e,t){return pc.getStart(e)},exact:function(e,t,n,r){return e}});return t=n,xe.fromDom(t.dom().ownerDocument.defaultView)},range:gc.create},wc=function(e,t){e.selectNodeContents(t.dom())},yc=function(e,t,n){var r,o,i=e.document.createRange();return r=i,t.fold(function(e){r.setStartBefore(e.dom())},function(e,t){r.setStart(e.dom(),t)},function(e){r.setStartAfter(e.dom())}),o=i,n.fold(function(e){o.setEndBefore(e.dom())},function(e,t){o.setEnd(e.dom(),t)},function(e){o.setEndAfter(e.dom())}),i},xc=function(e,t,n,r,o){var i=e.document.createRange();return i.setStart(t.dom(),n),i.setEnd(r.dom(),o),i},Cc=function(e){return{left:C(e.left),top:C(e.top),right:C(e.right),bottom:C(e.bottom),width:C(e.width),height:C(e.height)}},Rc=xr([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Sc=function(e,t,n){return t(xe.fromDom(n.startContainer),n.startOffset,xe.fromDom(n.endContainer),n.endOffset)},Tc=function(e,t){var o,n,r,i=(o=e,t.match({domRange:function(e){return{ltr:C(e),rtl:R.none}},relative:function(e,t){return{ltr:we(function(){return yc(o,e,t)}),rtl:we(function(){return R.some(yc(o,t,e))})}},exact:function(e,t,n,r){return{ltr:we(function(){return xc(o,e,t,n,r)}),rtl:we(function(){return R.some(xc(o,n,r,e,t))})}}}));return(r=(n=i).ltr()).collapsed?n.rtl().filter(function(e){return!1===e.collapsed}).map(function(e){return Rc.rtl(xe.fromDom(e.endContainer),e.endOffset,xe.fromDom(e.startContainer),e.startOffset)}).getOrThunk(function(){return Sc(0,Rc.ltr,r)}):Sc(0,Rc.ltr,r)},Dc=function(i,e){return Tc(i,e).match({ltr:function(e,t,n,r){var o=i.document.createRange();return o.setStart(e.dom(),t),o.setEnd(n.dom(),r),o},rtl:function(e,t,n,r){var o=i.document.createRange();return o.setStart(n.dom(),r),o.setEnd(e.dom(),t),o}})},Oc=function(e,t,n){return t>=e.left&&t<=e.right&&n>=e.top&&n<=e.bottom},Nc=function(n,r,e,t,o){var i=function(e){var t=n.dom().createRange();return t.setStart(r.dom(),e),t.collapse(!0),t},u=vn(r).length,a=function(e,t,n,r,o){if(0===o)return 0;if(t===r)return o-1;for(var i=r,u=1;u<o;u++){var a=e(u),c=Math.abs(t-a.left);if(n<=a.bottom){if(n<a.top||i<c)return u-1;i=c}}return 0}(function(e){return i(e).getBoundingClientRect()},e,t,o.right,u);return i(a)},Ec=function(e,t,n,r){return le(t)?function(t,n,r,o){var e=t.dom().createRange();e.selectNode(n.dom());var i=e.getClientRects();return xo(i,function(e){return Oc(e,r,o)?R.some(e):R.none()}).map(function(e){return Nc(t,n,r,o,e)})}(e,t,n,r):(i=t,u=n,a=r,c=(o=e).dom().createRange(),l=bt(i),xo(l,function(e){return c.selectNode(e.dom()),Oc(c.getBoundingClientRect(),u,a)?Ec(o,e,u,a):R.none()}));var o,i,u,a,c,l},kc=function(e,t){return t-e.left<e.right-t},Ac=function(e,t,n){var r=e.dom().createRange();return r.selectNode(t.dom()),r.collapse(n),r},Pc=function(t,e,n){var r=t.dom().createRange();r.selectNode(e.dom());var o=r.getBoundingClientRect(),i=kc(o,n);return(!0===i?Rn:Sn)(e).map(function(e){return Ac(t,e,i)})},Ic=function(e,t,n){var r=t.dom().getBoundingClientRect(),o=kc(r,n);return R.some(Ac(e,t,o))},Bc=function(e,t,n,r){var o=e.dom().createRange();o.selectNode(t.dom());var i=o.getBoundingClientRect();return function(e,t,n,r){var o=e.dom().createRange();o.selectNode(t.dom());var i=o.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,n)),a=Math.max(i.top,Math.min(i.bottom,r));return Ec(e,t,u,a)}(e,t,Math.max(i.left,Math.min(i.right,n)),Math.max(i.top,Math.min(i.bottom,r)))},Wc=document.caretPositionFromPoint?function(n,e,t){return R.from(n.dom().caretPositionFromPoint(e,t)).bind(function(e){if(null===e.offsetNode)return R.none();var t=n.dom().createRange();return t.setStart(e.offsetNode,e.offset),t.collapse(),R.some(t)})}:document.caretRangeFromPoint?function(e,t,n){return R.from(e.dom().caretRangeFromPoint(t,n))}:function(o,i,t){return xe.fromPoint(o,i,t).bind(function(r){var e=function(){return e=o,n=i,(0===bt(t=r).length?Ic:Pc)(e,t,n);var e,t,n};return 0===bt(r).length?e():Bc(o,r,i,t).orThunk(e)})},Mc=function(e,t){var n=oe(e);return"input"===n?pc.after(e):O(["br","img"],n)?0===t?pc.before(e):pc.after(e):pc.on(e,t)},_c=function(e,t){var n=e.fold(pc.before,Mc,pc.after),r=t.fold(pc.before,Mc,pc.after);return bc.relative(n,r)},Lc=function(e,t,n,r){var o=Mc(e,t),i=Mc(n,r);return bc.relative(o,i)},Fc=function(e,t,n,r){var o,i,u,a,c,l=(i=t,u=n,a=r,(c=mt(o=e).dom().createRange()).setStart(o.dom(),i),c.setEnd(u.dom(),a),c),f=ft(e,n)&&t===r;return l.collapsed&&!f},jc=function(e,t){R.from(e.getSelection()).each(function(e){e.removeAllRanges(),e.addRange(t)})},zc=function(e,t,n,r,o){var i=xc(e,t,n,r,o);jc(e,i)},Hc=function(s,e){return Tc(s,e).match({ltr:function(e,t,n,r){zc(s,e,t,n,r)},rtl:function(e,t,n,r){var o,i,u,a,c,l=s.getSelection();if(l.setBaseAndExtent)l.setBaseAndExtent(e.dom(),t,n.dom(),r);else if(l.extend)try{i=e,u=t,a=n,c=r,(o=l).collapse(i.dom(),u),o.extend(a.dom(),c)}catch(f){zc(s,n,r,e,t)}else zc(s,n,r,e,t)}})},Uc=function(e){var o=bc.getWin(e).dom(),t=function(e,t,n,r){return xc(o,e,t,n,r)},n=e.match({domRange:function(e){var t=xe.fromDom(e.startContainer),n=xe.fromDom(e.endContainer);return Lc(t,e.startOffset,n,e.endOffset)},relative:_c,exact:Lc});return Tc(o,n).match({ltr:t,rtl:t})},qc=function(e){var t=xe.fromDom(e.anchorNode),n=xe.fromDom(e.focusNode);return Fc(t,e.anchorOffset,n,e.focusOffset)?R.some(gc.create(t,e.anchorOffset,n,e.focusOffset)):function(e){if(0<e.rangeCount){var t=e.getRangeAt(0),n=e.getRangeAt(e.rangeCount-1);return R.some(gc.create(xe.fromDom(t.startContainer),t.startOffset,xe.fromDom(n.endContainer),n.endOffset))}return R.none()}(e)},Vc=function(e,t){var n,r,o=(n=t,r=e.document.createRange(),wc(r,n),r);jc(e,o)},Gc=function(e){return(t=e,R.from(t.getSelection()).filter(function(e){return 0<e.rangeCount}).bind(qc)).map(function(e){return bc.exact(e.start(),e.soffset(),e.finish(),e.foffset())});var t},Yc=function(e,t){var n,r,o,i=Dc(e,t);return r=(n=i).getClientRects(),0<(o=0<r.length?r[0]:n.getBoundingClientRect()).width||0<o.height?R.some(o).map(Cc):R.none()},Xc=function(e,t,n){return r=e,o=t,i=n,u=xe.fromDom(r.document),Wc(u,o,i).map(function(e){return gc.create(xe.fromDom(e.startContainer),e.startOffset,xe.fromDom(e.endContainer),e.endOffset)});var r,o,i,u},Kc=tinymce.util.Tools.resolve("tinymce.util.VK"),Jc=function(e,t,n,r){return el(e,t,dc(n),r)},$c=function(e,t,n,r){return el(e,t,mc(n),r)},Qc=function(e,t){var n=bc.exact(t,0,t,0);return Uc(n)},Zc=function(e,t){var n,r=qt(t,"tr");return(n=r,0===n.length?R.none():R.some(n[n.length-1])).bind(function(e){return Qt(e,"td,th").map(function(e){return Qc(0,e)})})},el=function(r,e,t,o,n){return t.fold(R.none,R.none,function(e,t){return Rn(t).map(function(e){return Qc(0,e)})},function(n){return cn.table(n,e).bind(function(e){var t=kr.noMenu(n);return r.undoManager.transact(function(){o.insertRowsAfter(e,t)}),Zc(0,e)})})},tl=["table","li","dl"],nl=function(t,n,r,o){if(t.keyCode===Kc.TAB){var i=Fu(n),u=function(e){var t=oe(e);return ft(e,i)||O(tl,t)},e=n.selection.getRng();if(e.collapsed){var a=xe.fromDom(e.startContainer);cn.cell(a,u).each(function(e){t.preventDefault(),(t.shiftKey?$c:Jc)(n,u,e,r,o).each(function(e){n.selection.setRng(e)})})}}},rl={create:q("selection","kill")},ol=function(e,t,n,r){return{start:C(pc.on(e,t)),finish:C(pc.on(n,r))}},il={convertToRange:function(e,t){var n=Dc(e,t);return gc.create(xe.fromDom(n.startContainer),n.startOffset,xe.fromDom(n.endContainer),n.endOffset)},makeSitus:ol},ul=function(n,e,r,t,o){return ft(r,t)?R.none():sr(r,t,e).bind(function(e){var t=e.boxes().getOr([]);return 0<t.length?(o(n,t,e.start(),e.finish()),R.some(rl.create(R.some(il.makeSitus(r,0,r,yn(r))),!0))):R.none()})},al={sync:function(n,r,e,t,o,i,u){return ft(e,o)&&t===i?R.none():Zt(e,"td,th",r).bind(function(t){return Zt(o,"td,th",r).bind(function(e){return ul(n,r,t,e,u)})})},detect:ul,update:function(e,t,n,r,o){return mr(r,e,t,o.firstSelectedSelector(),o.lastSelectedSelector()).map(function(e){return o.clear(n),o.selectRange(n,e.boxes(),e.start(),e.finish()),e.boxes()})}},cl=q("item","mode"),ll=function(e,t,n,r){return void 0===r&&(r=fl),e.property().parent(t).map(function(e){return cl(e,r)})},fl=function(e,t,n,r){return void 0===r&&(r=sl),n.sibling(e,t).map(function(e){return cl(e,r)})},sl=function(e,t,n,r){void 0===r&&(r=sl);var o=e.property().children(t);return n.first(o).map(function(e){return cl(e,r)})},dl=[{current:ll,next:fl,fallback:R.none()},{current:fl,next:sl,fallback:R.some(ll)},{current:sl,next:sl,fallback:R.some(fl)}],ml=function(t,n,r,o,e){return void 0===e&&(e=dl),B(e,function(e){return e.current===r}).bind(function(e){return e.current(t,n,o,e.next).orThunk(function(){return e.fallback.bind(function(e){return ml(t,n,e,o)})})})},gl=function(){return{sibling:function(e,t){return e.query().prevSibling(t)},first:function(e){return 0<e.length?R.some(e[e.length-1]):R.none()}}},hl=function(){return{sibling:function(e,t){return e.query().nextSibling(t)},first:function(e){return 0<e.length?R.some(e[0]):R.none()}}},pl=function(t,e,n,r,o,i){return ml(t,e,r,o).bind(function(e){return i(e.item())?R.none():n(e.item())?R.some(e.item()):pl(t,e.item(),n,e.mode(),o,i)})},vl=function(t){return function(e){return 0===t.property().children(e).length}},bl=function(e,t,n,r){return pl(e,t,n,fl,gl(),r)},wl=function(e,t,n,r){return pl(e,t,n,fl,hl(),r)},yl=$n(),xl=function(e,t){return r=t,bl(n=yl,e,vl(n),r);var n,r},Cl=function(e,t){return r=t,wl(n=yl,e,vl(n),r);var n,r},Rl=q("element","offset"),Sl=(q("element","deltaOffset"),q("element","start","finish"),q("begin","end"),q("element","text"),xr([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}])),Tl=function(e){return Zt(e,"tr")},Dl=wo(wo({},Sl),{verify:function(a,e,t,n,r,c,o){return Zt(n,"td,th",o).bind(function(u){return Zt(e,"td,th",o).map(function(i){return ft(u,i)?ft(n,u)&&yn(u)===r?c(i):Sl.none("in same cell"):ar.sharedOne(Tl,[u,i]).fold(function(){return t=i,n=u,r=(e=a).getRect(t),(o=e.getRect(n)).right>r.left&&o.left<r.right?Sl.success():c(i);var e,t,n,r,o},function(e){return c(i)})})}).getOr(Sl.none("default"))},cata:function(e,t,n,r,o){return e.fold(t,n,r,o)}}),Ol=(q("ancestor","descendants","element","index"),q("parent","children","element","index")),Nl=function(e,t){return W(e,b(ft,t))},El=function(e){return"br"===oe(e)},kl=function(e,t,n){return t(e,n).bind(function(e){return le(e)&&0===vn(e).trim().length?kl(e,t,n):R.some(e)})},Al=function(t,e,n,r){return(o=e,i=n,wt(o,i).filter(El).orThunk(function(){return wt(o,i-1).filter(El)})).bind(function(e){return r.traverse(e).fold(function(){return kl(e,r.gather,t).map(r.relative)},function(e){return(r=e,gt(r).bind(function(t){var n=bt(t);return Nl(n,r).map(function(e){return Ol(t,n,r,e)})})).map(function(e){return pc.on(e.parent(),e.index())});var r})});var o,i},Pl=function(e,t,n,r){var o,i,u;return(El(t)?(o=e,i=t,(u=r).traverse(i).orThunk(function(){return kl(i,u.gather,o)}).map(u.relative)):Al(e,t,n,r)).map(function(e){return{start:C(e),finish:C(e)}})},Il=function(e){return Dl.cata(e,function(e){return R.none()},function(){return R.none()},function(e){return R.some(Rl(e,0))},function(e){return R.some(Rl(e,yn(e)))})},Bl=J(["left","top","right","bottom"],[]),Wl={nu:Bl,moveUp:function(e,t){return Bl({left:e.left(),top:e.top()-t,right:e.right(),bottom:e.bottom()-t})},moveDown:function(e,t){return Bl({left:e.left(),top:e.top()+t,right:e.right(),bottom:e.bottom()+t})},moveBottomTo:function(e,t){var n=e.bottom()-e.top();return Bl({left:e.left(),top:t-n,right:e.right(),bottom:t})},moveTopTo:function(e,t){var n=e.bottom()-e.top();return Bl({left:e.left(),top:t,right:e.right(),bottom:t+n})},getTop:function(e){return e.top()},getBottom:function(e){return e.bottom()},translate:function(e,t,n){return Bl({left:e.left()+t,top:e.top()+n,right:e.right()+t,bottom:e.bottom()+n})},toString:function(e){return"("+e.left()+", "+e.top()+") -> ("+e.right()+", "+e.bottom()+")"}},Ml=function(e){return Wl.nu({left:e.left,top:e.top,right:e.right,bottom:e.bottom})},_l=function(e,t){return R.some(e.getRect(t))},Ll=function(e,t,n){return ce(t)?_l(e,t).map(Ml):le(t)?(r=e,o=t,i=n,0<=i&&i<yn(o)?r.getRangedRect(o,i,o,i+1):0<i?r.getRangedRect(o,i-1,o,i):R.none()).map(Ml):R.none();var r,o,i},Fl=function(e,t){return ce(t)?_l(e,t).map(Ml):le(t)?e.getRangedRect(t,0,t,yn(t)).map(Ml):R.none()},jl=xr([{none:[]},{retry:["caret"]}]),zl=function(t,e,r){return(n=e,o=Tu,Vt(function(e,t){return t(e)},Kt,n,o,i)).fold(C(!1),function(e){return Fl(t,e).exists(function(e){return n=e,(t=r).left()<n.left()||Math.abs(n.right()-t.left())<1||t.left()>n.right();var t,n})});var n,o,i},Hl={point:Wl.getTop,adjuster:function(e,t,n,r,o){var i=Wl.moveUp(o,5);return Math.abs(n.top()-r.top())<1?jl.retry(i):n.bottom()<o.top()?jl.retry(i):n.bottom()===o.top()?jl.retry(Wl.moveUp(o,1)):zl(e,t,o)?jl.retry(Wl.translate(i,5,0)):jl.none()},move:Wl.moveUp,gather:xl},Ul={point:Wl.getBottom,adjuster:function(e,t,n,r,o){var i=Wl.moveDown(o,5);return Math.abs(n.bottom()-r.bottom())<1?jl.retry(i):n.top()>o.bottom()?jl.retry(i):n.top()===o.bottom()?jl.retry(Wl.moveDown(o,1)):zl(e,t,o)?jl.retry(Wl.translate(i,5,0)):jl.none()},move:Wl.moveDown,gather:Cl},ql=function(n,r,o,i,u){return 0===u?R.some(i):(c=n,l=i.left(),f=r.point(i),c.elementFromPoint(l,f).filter(function(e){return"table"===oe(e)}).isSome()?(t=i,a=u-1,ql(n,e=r,o,e.move(t,5),a)):n.situsFromPoint(i.left(),r.point(i)).bind(function(e){return e.start().fold(R.none,function(t){return Fl(n,t).bind(function(e){return r.adjuster(n,t,e,o,i).fold(R.none,function(e){return ql(n,r,o,e,u-1)})}).orThunk(function(){return R.some(i)})},R.none)}));var e,t,a,c,l,f},Vl=function(t,n,e){var r,o,i,u=t.move(e,5),a=ql(n,t,e,u,100).getOr(u);return(r=t,o=a,i=n,r.point(o)>i.getInnerHeight()?R.some(r.point(o)-i.getInnerHeight()):r.point(o)<0?R.some(-r.point(o)):R.none()).fold(function(){return n.situsFromPoint(a.left(),t.point(a))},function(e){return n.scrollBy(0,e),n.situsFromPoint(a.left(),t.point(a)-e)})},Gl={tryUp:b(Vl,Hl),tryDown:b(Vl,Ul),ieTryUp:function(e,t){return e.situsFromPoint(t.left(),t.top()-5)},ieTryDown:function(e,t){return e.situsFromPoint(t.left(),t.bottom()+5)},getJumpSize:C(5)},Yl=it.detect(),Xl=function(r,o,i,u,a,c){return 0===c?R.none():$l(r,o,i,u,a).bind(function(e){var t=r.fromSitus(e),n=Dl.verify(r,i,u,t.finish(),t.foffset(),a.failure,o);return Dl.cata(n,function(){return R.none()},function(){return R.some(e)},function(e){return ft(i,e)&&0===u?Kl(r,i,u,Wl.moveUp,a):Xl(r,o,e,0,a,c-1)},function(e){return ft(i,e)&&u===yn(e)?Kl(r,i,u,Wl.moveDown,a):Xl(r,o,e,yn(e),a,c-1)})})},Kl=function(t,e,n,r,o){return Ll(t,e,n).bind(function(e){return Jl(t,o,r(e,Gl.getJumpSize()))})},Jl=function(e,t,n){return Yl.browser.isChrome()||Yl.browser.isSafari()||Yl.browser.isFirefox()||Yl.browser.isEdge()?t.otherRetry(e,n):Yl.browser.isIE()?t.ieRetry(e,n):R.none()},$l=function(t,e,n,r,o){return Ll(t,n,r).bind(function(e){return Jl(t,o,e)})},Ql=function(t,n,r){return(o=t,i=n,u=r,o.getSelection().bind(function(r){return Pl(i,r.finish(),r.foffset(),u).fold(function(){return R.some(Rl(r.finish(),r.foffset()))},function(e){var t=o.fromSitus(e),n=Dl.verify(o,r.finish(),r.foffset(),t.finish(),t.foffset(),u.failure,i);return Il(n)})})).bind(function(e){return Xl(t,n,e.element(),e.offset(),r,20).map(t.fromSitus)});var o,i,u},Zl=it.detect(),ef=function(e,t){return Kt(e,function(e){return gt(e).exists(function(e){return ft(e,t)})},n).isSome();var n},tf=function(t,r,o,e,i){return Zt(e,"td,th",r).bind(function(n){return Zt(n,"table",r).bind(function(e){return ef(i,e)?Ql(t,r,o).bind(function(t){return Zt(t.finish(),"td,th",r).map(function(e){return{start:C(n),finish:C(e),range:C(t)}})}):R.none()})})},nf=function(e,t,n,r,o,i){return Zl.browser.isIE()?R.none():i(r,t).orThunk(function(){return tf(e,t,n,r,o).map(function(e){var t=e.range();return rl.create(R.some(il.makeSitus(t.start(),t.soffset(),t.finish(),t.foffset())),!0)})})},rf=function(e,t,n,r,o,i,u){return tf(e,n,r,o,i).bind(function(e){return al.detect(t,n,e.start(),e.finish(),u)})},of=function(e,u){return Zt(e,"tr",u).bind(function(i){return Zt(i,"table",u).bind(function(e){var t,n,r,o=qt(e,"tr");return ft(i,o[0])?(t=e,n=function(e){return Sn(e).isSome()},r=u,bl(yl,t,n,r)).map(function(e){var t=yn(e);return rl.create(R.some(il.makeSitus(e,t,e,t)),!0)}):R.none()})})},uf=function(e,u){return Zt(e,"tr",u).bind(function(i){return Zt(i,"table",u).bind(function(e){var t,n,r,o=qt(e,"tr");return ft(i,o[o.length-1])?(t=e,n=function(e){return Rn(e).isSome()},r=u,wl(yl,t,n,r)).map(function(e){return rl.create(R.some(il.makeSitus(e,0,e,0)),!0)}):R.none()})})},af=function(e,t){return Zt(e,"td,th",t)},cf={down:{traverse:vt,gather:Cl,relative:pc.before,otherRetry:Gl.tryDown,ieRetry:Gl.ieTryDown,failure:Dl.failedDown},up:{traverse:pt,gather:xl,relative:pc.before,otherRetry:Gl.tryUp,ieRetry:Gl.ieTryUp,failure:Dl.failedUp}},lf=function(t){return function(e){return e===t}},ff=lf(38),sf=lf(40),df={ltr:{isBackward:lf(37),isForward:lf(39)},rtl:{isBackward:lf(39),isForward:lf(37)},isUp:ff,isDown:sf,isNavigation:function(e){return 37<=e&&e<=40}},mf=function(e){return{left:e.left(),top:e.top(),right:e.right(),bottom:e.bottom(),width:e.width(),height:e.height()}},gf=(it.detect().browser.isSafari(),function(a){return{elementFromPoint:function(e,t){return xe.fromPoint(xe.fromDom(a.document),e,t)},getRect:function(e){return e.dom().getBoundingClientRect()},getRangedRect:function(e,t,n,r){var o=bc.exact(e,t,n,r);return Yc(a,o).map(mf)},getSelection:function(){return Gc(a).map(function(e){return il.convertToRange(a,e)})},fromSitus:function(e){var t=bc.relative(e.start(),e.finish());return il.convertToRange(a,t)},situsFromPoint:function(e,t){return Xc(a,e,t).map(function(e){return ol(e.start(),e.soffset(),e.finish(),e.foffset())})},clearSelection:function(){a.getSelection().removeAllRanges()},setSelection:function(e){var t,n,r,o,i,u;t=a,n=e.start(),r=e.soffset(),o=e.finish(),i=e.foffset(),u=Lc(n,r,o,i),Hc(t,u)},setRelativeSelection:function(e,t){var n,r;n=a,r=_c(e,t),Hc(n,r)},selectContents:function(e){Vc(a,e)},getInnerHeight:function(){return a.innerHeight},getScrollY:function(){var e,t,n,r;return(e=xe.fromDom(a.document),t=e!==undefined?e.dom():m.document,n=t.body.scrollLeft||t.documentElement.scrollLeft,r=t.body.scrollTop||t.documentElement.scrollTop,eo(n,r)).top()},scrollBy:function(e,t){var n,r,o;n=e,r=t,((o=xe.fromDom(a.document))!==undefined?o.dom():m.document).defaultView.scrollBy(n,r)}}}),hf=q("rows","cols"),pf={mouse:function(e,t,n,r){var o,i,u,a,c,l,f=gf(e),s=(o=f,i=t,u=n,a=r,c=R.none(),l=function(){c=R.none()},{mousedown:function(e){a.clear(i),c=af(e.target(),u)},mouseover:function(e){c.each(function(r){a.clear(i),af(e.target(),u).each(function(n){sr(r,n,u).each(function(e){var t=e.boxes().getOr([]);(1<t.length||1===t.length&&!ft(r,n))&&(a.selectRange(i,t,e.start(),e.finish()),o.selectContents(n))})})})},mouseup:function(e){c.each(l)}});return{mousedown:s.mousedown,mouseover:s.mouseover,mouseup:s.mouseup}},keyboard:function(e,l,f,s){var d=gf(e),m=function(){return s.clear(l),R.none()};return{keydown:function(e,t,n,r,o,i){var u=e.raw(),a=u.which,c=!0===u.shiftKey;return dr(l,s.selectedSelector()).fold(function(){return df.isDown(a)&&c?b(rf,d,l,f,cf.down,r,t,s.selectRange):df.isUp(a)&&c?b(rf,d,l,f,cf.up,r,t,s.selectRange):df.isDown(a)?b(nf,d,f,cf.down,r,t,uf):df.isUp(a)?b(nf,d,f,cf.up,r,t,of):R.none},function(t){var e=function(e){return function(){return xo(e,function(e){return al.update(e.rows(),e.cols(),l,t,s)}).fold(function(){return gr(l,s.firstSelectedSelector(),s.lastSelectedSelector()).map(function(e){var t=df.isDown(a)||i.isForward(a)?pc.after:pc.before;return d.setRelativeSelection(pc.on(e.first(),0),t(e.table())),s.clear(l),rl.create(R.none(),!0)})},function(e){return R.some(rl.create(R.none(),!0))})}};return df.isDown(a)&&c?e([hf(1,0)]):df.isUp(a)&&c?e([hf(-1,0)]):i.isBackward(a)&&c?e([hf(0,-1),hf(-1,0)]):i.isForward(a)&&c?e([hf(0,1),hf(1,0)]):df.isNavigation(a)&&!1===c?m:R.none})()},keyup:function(n,r,o,i,u){return dr(l,s.selectedSelector()).fold(function(){var e=n.raw(),t=e.which;return 0==(!0===e.shiftKey)?R.none():df.isNavigation(t)?al.sync(l,f,r,o,i,u,s.selectRange):R.none()},R.none)}}}},vf=function(r,e){k(e,function(e){var t,n;n=e,Do(t=r)?t.dom().classList.remove(n):No(t,n),ko(t)})},bf={byClass:function(o){var t,n,i=(t=o.selected(),function(e){Eo(e,t)}),r=(n=[o.selected(),o.lastSelected(),o.firstSelected()],function(e){vf(e,n)}),u=function(e){var t=qt(e,o.selectedSelector());k(t,r)};return{clear:u,selectRange:function(e,t,n,r){u(e),k(t,i),Eo(n,o.firstSelected()),Eo(r,o.lastSelected())},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}},byAttr:function(o){var n=function(e){he(e,o.selected()),he(e,o.firstSelected()),he(e,o.lastSelected())},i=function(e){se(e,o.selected(),"1")},u=function(e){var t=qt(e,o.selectedSelector());k(t,n)};return{clear:u,selectRange:function(e,t,n,r){u(e),k(t,i),se(n,o.firstSelected(),"1"),se(r,o.lastSelected(),"1")},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}}},wf=function(e){return!1===Ao(xe.fromDom(e.target),"ephox-snooker-resizer-bar")};function yf(h,p){var v=J(["mousedown","mouseover","mouseup","keyup","keydown"],[]),b=R.none(),w=bf.byAttr(yr);return h.on("init",function(e){var r=h.getWin(),o=Fu(h),t=Hu(h),n=pf.mouse(r,o,t,w),a=pf.keyboard(r,o,t,w),c=function(e,t){!0===e.raw().shiftKey&&(t.kill()&&e.kill(),t.selection().each(function(e){var t=bc.relative(e.start(),e.finish()),n=Dc(r,t);h.selection.setRng(n)}))},i=function(e){var t=f(e);if(t.raw().shiftKey&&df.isNavigation(t.raw().which)){var n=h.selection.getRng(),r=xe.fromDom(n.startContainer),o=xe.fromDom(n.endContainer);a.keyup(t,r,n.startOffset,o,n.endOffset).each(function(e){c(t,e)})}},u=function(e){var t=f(e);p().each(function(e){e.hideBars()});var n=h.selection.getRng(),r=xe.fromDom(h.selection.getStart()),o=xe.fromDom(n.startContainer),i=xe.fromDom(n.endContainer),u=Yu.directionAt(r).isRtl()?df.rtl:df.ltr;a.keydown(t,o,n.startOffset,i,n.endOffset,u).each(function(e){c(t,e)}),p().each(function(e){e.showBars()})},l=function(e){return e.hasOwnProperty("x")&&e.hasOwnProperty("y")},f=function(e){var t=xe.fromDom(e.target),n=function(){e.stopPropagation()},r=function(){e.preventDefault()},o=x(r,n);return{target:C(t),x:C(l(e)?e.x:null),y:C(l(e)?e.y:null),stop:n,prevent:r,kill:o,raw:C(e)}},s=function(e){return 0===e.button},d=function(e){s(e)&&wf(e)&&n.mousedown(f(e))},m=function(e){var t;((t=e).buttons===undefined||Sa.ie&&12<=Sa.ie&&0===t.buttons||0!=(1&t.buttons))&&wf(e)&&n.mouseover(f(e))},g=function(e){s(e)&&wf(e)&&n.mouseup(f(e))};h.on("mousedown",d),h.on("mouseover",m),h.on("mouseup",g),h.on("keyup",i),h.on("keydown",u),h.on("nodechange",function(){var e=h.selection,t=xe.fromDom(e.getStart()),n=xe.fromDom(e.getEnd());ar.sharedOne(cn.table,[t,n]).fold(function(){w.clear(o)},y)}),b=R.some(v({mousedown:d,mouseover:m,mouseup:g,keyup:i,keydown:u}))}),{clear:w.clear,destroy:function(){b.each(function(e){})}}}var xf=sa.each,Cf=function(t){var n=[];function e(e){return function(){t.execCommand(e)}}xf("inserttable tableprops deletetable | cell row column".split(" "),function(e){"|"===e?n.push({text:"-"}):n.push(t.menuItems[e])}),t.addButton("table",{type:"menubutton",title:"Table",menu:n}),t.addButton("tableprops",{title:"Table properties",onclick:e("mceTableProps"),icon:"table"}),t.addButton("tabledelete",{title:"Delete table",onclick:e("mceTableDelete")}),t.addButton("tablecellprops",{title:"Cell properties",onclick:e("mceTableCellProps")}),t.addButton("tablemergecells",{title:"Merge cells",onclick:e("mceTableMergeCells")}),t.addButton("tablesplitcells",{title:"Split cell",onclick:e("mceTableSplitCells")}),t.addButton("tableinsertrowbefore",{title:"Insert row before",onclick:e("mceTableInsertRowBefore")}),t.addButton("tableinsertrowafter",{title:"Insert row after",onclick:e("mceTableInsertRowAfter")}),t.addButton("tabledeleterow",{title:"Delete row",onclick:e("mceTableDeleteRow")}),t.addButton("tablerowprops",{title:"Row properties",onclick:e("mceTableRowProps")}),t.addButton("tablecutrow",{title:"Cut row",onclick:e("mceTableCutRow")}),t.addButton("tablecopyrow",{title:"Copy row",onclick:e("mceTableCopyRow")}),t.addButton("tablepasterowbefore",{title:"Paste row before",onclick:e("mceTablePasteRowBefore")}),t.addButton("tablepasterowafter",{title:"Paste row after",onclick:e("mceTablePasteRowAfter")}),t.addButton("tableinsertcolbefore",{title:"Insert column before",onclick:e("mceTableInsertColBefore")}),t.addButton("tableinsertcolafter",{title:"Insert column after",onclick:e("mceTableInsertColAfter")}),t.addButton("tabledeletecol",{title:"Delete column",onclick:e("mceTableDeleteCol")})},Rf=function(t){var e,n=""===(e=t.getParam("table_toolbar",Xu))||!1===e?[]:d(e)?e.split(/[ ,]/):h(e)?e:[];0<n.length&&t.addContextToolbar(function(e){return t.dom.is(e,"table")&&t.getBody().contains(e)},n.join(" "))},Sf=function(o,n){var r=R.none(),i=[],u=[],a=[],c=[],l=function(e){e.disabled(!0)},f=function(e){e.disabled(!1)},e=function(){var t=this;i.push(t),r.fold(function(){l(t)},function(e){f(t)})},t=function(){var t=this;u.push(t),r.fold(function(){l(t)},function(e){f(t)})};o.on("init",function(){o.on("nodechange",function(e){var t=R.from(o.dom.getParent(o.selection.getStart(),"th,td"));(r=t.bind(function(e){var t=xe.fromDom(e);return cn.table(t).map(function(e){return kr.forMenu(n,e,t)})})).fold(function(){k(i,l),k(u,l),k(a,l),k(c,l)},function(t){k(i,f),k(u,f),k(a,function(e){e.disabled(t.mergable().isNone())}),k(c,function(e){e.disabled(t.unmergable().isNone())})})})});var s=function(e,t,n,r){var o,i,u,a,c,l=r.getEl().getElementsByTagName("table")[0],f=r.isRtl()||"tl-tr"===r.parent().rel;for(l.nextSibling.innerHTML=t+1+" x "+(n+1),f&&(t=9-t),i=0;i<10;i++)for(o=0;o<10;o++)a=l.rows[i].childNodes[o].firstChild,c=(f?t<=o:o<=t)&&i<=n,e.dom.toggleClass(a,"mce-active",c),c&&(u=a);return u.parentNode},d=!1===o.getParam("table_grid",!0,"boolean")?{text:"Table",icon:"table",context:"table",onclick:m("mceInsertTable")}:{text:"Table",icon:"table",context:"table",ariaHideMenu:!0,onclick:function(e){e.aria&&(this.parent().hideAll(),e.stopImmediatePropagation(),o.execCommand("mceInsertTable"))},onshow:function(){s(o,0,0,this.menu.items()[0])},onhide:function(){var e=this.menu.items()[0].getEl().getElementsByTagName("a");o.dom.removeClass(e,"mce-active"),o.dom.addClass(e[0],"mce-active")},menu:[{type:"container",html:function(){var e="";e='<table role="grid" class="mce-grid mce-grid-border" aria-readonly="true">';for(var t=0;t<10;t++){e+="<tr>";for(var n=0;n<10;n++)e+='<td role="gridcell" tabindex="-1"><a id="mcegrid'+(10*t+n)+'" href="#" data-mce-x="'+n+'" data-mce-y="'+t+'"></a></td>';e+="</tr>"}return e+="</table>",e+='<div class="mce-text-center" role="presentation">1 x 1</div>'}(),onPostRender:function(){this.lastX=this.lastY=0},onmousemove:function(e){var t,n,r=e.target;"A"===r.tagName.toUpperCase()&&(t=parseInt(r.getAttribute("data-mce-x"),10),n=parseInt(r.getAttribute("data-mce-y"),10),(this.isRtl()||"tl-tr"===this.parent().rel)&&(t=9-t),t===this.lastX&&n===this.lastY||(s(o,t,n,e.control),this.lastX=t,this.lastY=n))},onclick:function(e){var t=this;"A"===e.target.tagName.toUpperCase()&&(e.preventDefault(),e.stopPropagation(),t.parent().cancel(),o.undoManager.transact(function(){Na(o,t.lastX+1,t.lastY+1)}),o.addVisual())}}]};function m(e){return function(){o.execCommand(e)}}var g={text:"Table properties",context:"table",onPostRender:e,onclick:m("mceTableProps")},h={text:"Delete table",context:"table",onPostRender:e,cmd:"mceTableDelete"},p={text:"Row",context:"table",menu:[{text:"Insert row before",onclick:m("mceTableInsertRowBefore"),onPostRender:t},{text:"Insert row after",onclick:m("mceTableInsertRowAfter"),onPostRender:t},{text:"Delete row",onclick:m("mceTableDeleteRow"),onPostRender:t},{text:"Row properties",onclick:m("mceTableRowProps"),onPostRender:t},{text:"-"},{text:"Cut row",onclick:m("mceTableCutRow"),onPostRender:t},{text:"Copy row",onclick:m("mceTableCopyRow"),onPostRender:t},{text:"Paste row before",onclick:m("mceTablePasteRowBefore"),onPostRender:t},{text:"Paste row after",onclick:m("mceTablePasteRowAfter"),onPostRender:t}]},v={text:"Column",context:"table",menu:[{text:"Insert column before",onclick:m("mceTableInsertColBefore"),onPostRender:t},{text:"Insert column after",onclick:m("mceTableInsertColAfter"),onPostRender:t},{text:"Delete column",onclick:m("mceTableDeleteCol"),onPostRender:t}]},b={separator:"before",text:"Cell",context:"table",menu:[{text:"Cell properties",onclick:m("mceTableCellProps"),onPostRender:t},{text:"Merge cells",onclick:m("mceTableMergeCells"),onPostRender:function(){var t=this;a.push(t),r.fold(function(){l(t)},function(e){t.disabled(e.mergable().isNone())})}},{text:"Split cell",onclick:m("mceTableSplitCells"),onPostRender:function(){var t=this;c.push(t),r.fold(function(){l(t)},function(e){t.disabled(e.unmergable().isNone())})}}]};o.addMenuItem("inserttable",d),o.addMenuItem("tableprops",g),o.addMenuItem("deletetable",h),o.addMenuItem("row",p),o.addMenuItem("column",v),o.addMenuItem("cell",b)},Tf=function(n,r){return{insertTable:function(e,t){return Na(n,e,t)},setClipboardRows:function(e){return t=r,n=E(e,xe.fromDom),void t.set(R.from(n));var t,n},getClipboardRows:function(){return r.get().fold(function(){},function(e){return E(e,function(e){return e.dom()})})}}};e.add("table",function(t){var n,r=cc(t),e=yf(t,r.lazyResize),o=la(t,r.lazyWire),i=(n=t,{get:function(){var e=Fu(n);return hr(e,yr.selectedSelector()).fold(function(){return n.selection.getStart()===undefined?Rr.none():Rr.single(n.selection)},function(e){return Rr.multiple(e)})}}),u=lu(R.none());return Ba(t,o,e,i,u),Ar(t,i,o,e),Sf(t,i),Cf(t),Rf(t),t.on("PreInit",function(){t.serializer.addTempAttr(yr.firstSelected()),t.serializer.addTempAttr(yr.lastSelected())}),t.getParam("table_tab_navigation",!0,"boolean")&&t.on("keydown",function(e){nl(e,t,o,r.lazyWire)}),t.on("remove",function(){r.destroy(),e.destroy()}),Tf(t,u)})}(window);
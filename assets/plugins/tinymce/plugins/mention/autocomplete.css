.rte-autocomplete{
    position: absolute;
    top: 0px;
    left: 0px;
    display: block;
    z-index: 1000;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    background-color: #fff;
    border: 1px solid #ccc;
    border: 1px solid rgba(0,0,0,0.2);
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    -webkit-box-shadow: 0 5px 10px rgba(0,0,0,0.2);
    -moz-box-shadow: 0 5px 10px rgba(0,0,0,0.2);
    box-shadow: 0 5px 10px rgba(0,0,0,0.2);
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
}

.rte-autocomplete:before {
    content: '';
    display: inline-block;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #ccc;
    border-bottom-color: rgba(0, 0, 0, 0.2);
    position: absolute;
    top: -7px;
    left: 9px;
}

.rte-autocomplete:after {
    content: '';
    display: inline-block;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid white;
    position: absolute;
    top: -6px;
    left: 10px;
}

.rte-autocomplete > li.loading {
	background: url("https://www.ajaxload.info/cache/FF/FF/FF/00/00/00/1-0.gif") center no-repeat;
	height: 16px;
}

.rte-autocomplete > li > a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 20px;
    color: #333;
    white-space: nowrap;
    text-decoration: none;
}

.rte-autocomplete >li > a:hover, .rte-autocomplete > li > a:focus, .rte-autocomplete:hover > a, .rte-autocomplete:focus > a {
    color: #fff;
    text-decoration: none;
    background-color: #0081c2;
    background-image: -moz-linear-gradient(top,#08c,#0077b3);
    background-image: -webkit-gradient(linear,0 0,0 100%,from(#08c),to(#0077b3));
    background-image: -webkit-linear-gradient(top,#08c,#0077b3);
    background-image: -o-linear-gradient(top,#08c,#0077b3);
    background-image: linear-gradient(to bottom,#08c,#0077b3);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft;
}

.rte-autocomplete >.active > a, .rte-autocomplete > .active > a:hover, .rte-autocomplete > .active > a:focus {
    color: #fff;
    text-decoration: none;
    background-color: #0081c2;
    background-image: -moz-linear-gradient(top,#08c,#0077b3);
    background-image: -webkit-gradient(linear,0 0,0 100%,from(#08c),to(#0077b3));
    background-image: -webkit-linear-gradient(top,#08c,#0077b3);
    background-image: -o-linear-gradient(top,#08c,#0077b3);
    background-image: linear-gradient(to bottom,#08c,#0077b3);
    background-repeat: repeat-x;
    outline: 0;
    filter: progid:DXImageTransform.Microsoft;
}

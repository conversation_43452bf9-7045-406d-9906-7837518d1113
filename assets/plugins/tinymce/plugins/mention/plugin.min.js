!function(t){"use strict";if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t(require("jquery"));else if("function"==typeof define&&define.amd)define(["jquery"],t);else{t(("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).jQuery)}}(function(t){"use strict";var e=function(e,i){this.editor=e,this.options=t.extend({},{source:[],delay:500,queryBy:"name",items:10},i),this.options.insertFrom=this.options.insertFrom||this.options.queryBy,this.matcher=this.options.matcher||this.matcher,this.sorter=this.options.sorter||this.sorter,this.renderDropdown=this.options.renderDropdown||this.renderDropdown,this.render=this.options.render||this.render,this.insert=this.options.insert||this.insert,this.highlighter=this.options.highlighter||this.highlighter,this.query="",this.hasFocus=!0,this.renderInput(),this.bindEvents()};e.prototype={constructor:e,renderInput:function(){var t='<span id="autocomplete"><span id="autocomplete-delimiter">'+this.options.delimiter+'</span><span id="autocomplete-searchtext"><span class="dummy">\ufeff</span></span></span>';this.editor.execCommand("mceInsertContent",!1,t),this.editor.focus(),this.editor.selection.select(this.editor.selection.dom.select("span#autocomplete-searchtext span")[0]),this.editor.selection.collapse(0)},bindEvents:function(){this.editor.on("keyup",this.editorKeyUpProxy=t.proxy(this.rteKeyUp,this)),this.editor.on("keydown",this.editorKeyDownProxy=t.proxy(this.rteKeyDown,this),!0),this.editor.on("click",this.editorClickProxy=t.proxy(this.rteClicked,this)),t("body").on("click",this.bodyClickProxy=t.proxy(this.rteLostFocus,this)),t(this.editor.getWin()).on("scroll",this.rteScroll=t.proxy(function(){this.cleanUp(!0)},this))},unbindEvents:function(){this.editor.off("keyup",this.editorKeyUpProxy),this.editor.off("keydown",this.editorKeyDownProxy),this.editor.off("click",this.editorClickProxy),t("body").off("click",this.bodyClickProxy),t(this.editor.getWin()).off("scroll",this.rteScroll)},rteKeyUp:function(t){switch(t.which||t.keyCode){case 40:case 38:case 16:case 17:case 18:break;case 8:""===this.query?this.cleanUp(!0):this.lookup();break;case 9:case 13:var e=void 0!==this.$dropdown?this.$dropdown.find("li.active"):[];e.length?(this.select(e.data()),this.cleanUp(!1)):this.cleanUp(!0);break;case 27:this.cleanUp(!0);break;default:this.lookup()}},rteKeyDown:function(t){switch(t.which||t.keyCode){case 9:case 13:case 27:t.preventDefault();break;case 38:t.preventDefault(),void 0!==this.$dropdown&&this.highlightPreviousResult();break;case 40:t.preventDefault(),void 0!==this.$dropdown&&this.highlightNextResult()}t.stopPropagation()},rteClicked:function(e){var i=t(e.target);this.hasFocus&&"autocomplete-searchtext"!==i.parent().attr("id")&&this.cleanUp(!0)},rteLostFocus:function(){this.hasFocus&&this.cleanUp(!0)},lookup:function(){this.query=t.trim(t(this.editor.getBody()).find("#autocomplete-searchtext").text()).replace("\ufeff",""),void 0===this.$dropdown&&this.show(),clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout(t.proxy(function(){var e=t.isFunction(this.options.source)?this.options.source(this.query,t.proxy(this.process,this),this.options.delimiter):this.options.source;e&&this.process(e)},this),this.options.delay)},matcher:function(t){return~t[this.options.queryBy].toLowerCase().indexOf(this.query.toLowerCase())},sorter:function(t){for(var e,i=[],o=[],s=[];void 0!==(e=t.shift());)e[this.options.queryBy].toLowerCase().indexOf(this.query.toLowerCase())?~e[this.options.queryBy].indexOf(this.query)?o.push(e):s.push(e):i.push(e);return i.concat(o,s)},highlighter:function(t){return t.replace(new RegExp("("+this.query.replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1")+")","ig"),function(t,e){return"<strong>"+e+"</strong>"})},show:function(){var e=this.editor.inline?this.offsetInline():this.offset();this.$dropdown=t(this.renderDropdown()).css({top:e.top,left:e.left}),t("body").append(this.$dropdown),this.$dropdown.on("click",t.proxy(this.autoCompleteClick,this))},process:function(e){if(this.hasFocus){var i=this,o=[],s=t.grep(e,function(t){return i.matcher(t)});s=i.sorter(s),s=s.slice(0,this.options.items),t.each(s,function(e,n){var r=t(i.render(n,e));r.html(r.html().replace(r.text(),i.highlighter(r.text()))),t.each(s[e],function(t,e){r.attr("data-"+t,e)}),o.push(r[0].outerHTML)}),o.length?this.$dropdown.html(o.join("")).show():this.$dropdown.hide()}},renderDropdown:function(){return'<ul class="rte-autocomplete dropdown-menu"><li class="loading"></li></ul>'},render:function(t,e){return'<li><a href="javascript:;"><span>'+t[this.options.queryBy]+"</span></a></li>"},autoCompleteClick:function(e){var i=t(e.target).closest("li").data();t.isEmptyObject(i)||(this.select(i),this.cleanUp(!1)),e.stopPropagation(),e.preventDefault()},highlightPreviousResult:function(){var t=this.$dropdown.find("li.active").index(),e=0===t?this.$dropdown.find("li").length-1:--t;this.$dropdown.find("li").removeClass("active").eq(e).addClass("active")},highlightNextResult:function(){var t=this.$dropdown.find("li.active").index(),e=t===this.$dropdown.find("li").length-1?0:++t;this.$dropdown.find("li").removeClass("active").eq(e).addClass("active")},select:function(t){this.editor.focus();var e=this.editor.dom.select("span#autocomplete")[0];this.editor.dom.remove(e),this.editor.execCommand("mceInsertContent",!1,this.insert(t))},insert:function(t){return"<span>"+t[this.options.insertFrom]+"</span>&nbsp;"},cleanUp:function(e){if(this.unbindEvents(),this.hasFocus=!1,void 0!==this.$dropdown&&(this.$dropdown.remove(),delete this.$dropdown),e){var i=this.query,o=t(this.editor.dom.select("span#autocomplete"));if(!o.length)return;var s=t("<p>"+this.options.delimiter+i+"</p>")[0].firstChild,n=t(this.editor.selection.getNode()).offset().top===o.offset().top+(o.outerHeight()-o.height())/2;this.editor.dom.replace(s,o[0]),n&&(this.editor.selection.select(s),this.editor.selection.collapse())}},offset:function(){var e=t(this.editor.getContainer()).offset(),i=t(this.editor.getContentAreaContainer()).position(),o=t(this.editor.dom.select("span#autocomplete")).position();return{top:e.top+i.top+o.top+t(this.editor.selection.getNode()).innerHeight()-t(this.editor.getDoc()).scrollTop()+5,left:e.left+i.left+o.left}},offsetInline:function(){var e=t(this.editor.dom.select("span#autocomplete")).offset();return{top:e.top+t(this.editor.selection.getNode()).innerHeight()+5,left:e.left}}},tinymce.create("tinymce.plugins.Mention",{init:function(i){function o(){var e=i.selection.getRng(!0).startOffset,o=(i.selection.getRng(!0).startContainer.data||"").substr(e>0?e-1:0,1);return!t.trim(o).length}var s,n=i.getParam("mentions");n.delimiter=void 0!==n.delimiter?t.isArray(n.delimiter)?n.delimiter:[n.delimiter]:["@"],i.on("keypress",function(r){var h=t.inArray(String.fromCharCode(r.which||r.keyCode),n.delimiter);h>-1&&o()&&(void 0===s||void 0!==s.hasFocus&&!s.hasFocus)&&(r.preventDefault(),s=new e(i,t.extend({},n,{delimiter:n.delimiter[h]})))})},getInfo:function(){return{longname:"mention",author:"Steven Devooght",version:tinymce.majorVersion+"."+tinymce.minorVersion}}}),tinymce.PluginManager.add("mention",tinymce.plugins.Mention)});
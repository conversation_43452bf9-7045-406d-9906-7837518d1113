tinymce.addI18n('ko_KR',{
"Redo": "\ub2e4\uc2dc\uc2e4\ud589",
"Undo": "\uc2e4\ud589\ucde8\uc18c",
"Cut": "\uc798\ub77c\ub0b4\uae30",
"Copy": "\ubcf5\uc0ac\ud558\uae30",
"Paste": "\ubd99\uc5ec\ub123\uae30",
"Select all": "\uc804\uccb4\uc120\ud0dd",
"New document": "\uc0c8 \ubb38\uc11c",
"Ok": "\ud655\uc778",
"Cancel": "\ucde8\uc18c",
"Visual aids": "\uc2dc\uac01\uad50\uc7ac",
"Bold": "\uad75\uac8c",
"Italic": "\uae30\uc6b8\uc784\uaf34",
"Underline": "\ubc11\uc904",
"Strikethrough": "\ucde8\uc18c\uc120",
"Superscript": "\uc717\ucca8\uc790",
"Subscript": "\uc544\ub798\ucca8\uc790",
"Clear formatting": "\ud3ec\ub9f7\ucd08\uae30\ud654",
"Align left": "\uc67c\ucabd\uc815\ub82c",
"Align center": "\uac00\uc6b4\ub370\uc815\ub82c",
"Align right": "\uc624\ub978\ucabd\uc815\ub82c",
"Justify": "\uc591\ucabd\uc815\ub82c",
"Bullet list": "\uc810\ub9ac\uc2a4\ud2b8",
"Numbered list": "\uc22b\uc790\ub9ac\uc2a4\ud2b8",
"Decrease indent": "\ub0b4\uc5b4\uc4f0\uae30",
"Increase indent": "\ub4e4\uc5ec\uc4f0\uae30",
"Close": "\ub2eb\uae30",
"Formats": "\ud3ec\ub9f7",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\ube0c\ub77c\uc6b0\uc838\uac00 \ud074\ub9bd\ubcf4\ub4dc \uc811\uadfc\uc744 \ud5c8\uc6a9\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4. Ctrl+X\/C\/V \ud0a4\ub97c \uc774\uc6a9\ud574 \uc8fc\uc138\uc694.",
"Headers": "\uc2a4\ud0c0\uc77c",
"Header 1": "\uc81c\ubaa9 1",
"Header 2": "\uc81c\ubaa9 2",
"Header 3": "\uc81c\ubaa9 3",
"Header 4": "\uc81c\ubaa9 4",
"Header 5": "\uc81c\ubaa9 5",
"Header 6": "\uc81c\ubaa9 6",
"Headings": "\uc81c\ubaa9",
"Heading 1": "\uc81c\ubaa9 1",
"Heading 2": "\uc81c\ubaa9 2",
"Heading 3": "\uc81c\ubaa9 3",
"Heading 4": "\uc81c\ubaa9 4",
"Heading 5": "\uc81c\ubaa9 5",
"Heading 6": "\uc81c\ubaa9 6",
"Div": "\uad6c\ubd84",
"Pre": "Pre",
"Code": "\ucf54\ub4dc",
"Paragraph": "\ub2e8\ub77d",
"Blockquote": "\uad6c\ud68d",
"Inline": "\ub77c\uc778 \uc124\uc815",
"Blocks": "\ube14\ub85d \uc124\uc815",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\uc2a4\ud0c0\uc77c\ubcf5\uc0ac \ub044\uae30. \uc774 \uc635\uc158\uc744 \ub044\uae30 \uc804\uc5d0\ub294 \ubcf5\uc0ac \uc2dc, \uc2a4\ud0c0\uc77c\uc774 \ubcf5\uc0ac\ub418\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.",
"Font Family": "\uae00\uaf34",
"Font Sizes": "\ud3f0\ud2b8 \uc0ac\uc774\uc988",
"Class": "\ud074\ub798\uc2a4",
"Browse for an image": "\uc774\ubbf8\uc9c0 \ucc3e\uae30",
"OR": "\ud639\uc740",
"Drop an image here": "\uc774\ubbf8\uc9c0 \ub4dc\ub86d",
"Upload": "\uc5c5\ub85c\ub4dc",
"Block": "\ube14\ub85d",
"Align": "\uc815\ub82c",
"Default": "\uae30\ubcf8",
"Circle": "\uc6d0",
"Disc": "\uc6d0\ubc18",
"Square": "\uc0ac\uac01",
"Lower Alpha": "\uc54c\ud30c\ubcb3 \uc18c\ubb38\uc790",
"Lower Greek": "\uadf8\ub9ac\uc2a4\uc5b4 \uc18c\ubb38\uc790",
"Lower Roman": "\ub85c\ub9c8\uc790 \uc18c\ubb38\uc790",
"Upper Alpha": "\uc54c\ud30c\ubcb3 \uc18c\ubb38\uc790",
"Upper Roman": "\ub85c\ub9c8\uc790 \ub300\ubb38\uc790",
"Anchor": "\uc575\ucee4",
"Name": "\uc774\ub984",
"Id": "\uc544\uc774\ub514",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "\uc544\uc774\ub514\ub294 \ubb38\uc790, \uc22b\uc790, \ub300\uc2dc, \uc810, \ucf5c\ub860 \ub610\ub294 \ubc11\uc904\ub85c \uc2dc\uc791\ud574\uc57c\ud569\ub2c8\ub2e4.",
"You have unsaved changes are you sure you want to navigate away?": "\uc800\uc7a5\ud558\uc9c0 \uc54a\uc740 \uc815\ubcf4\uac00 \uc788\uc2b5\ub2c8\ub2e4. \uc774 \ud398\uc774\uc9c0\ub97c \ubc97\uc5b4\ub098\uc2dc\uaca0\uc2b5\ub2c8\uae4c?",
"Restore last draft": "\ub9c8\uc9c0\ub9c9 \ucd08\uc548 \ubcf5\uc6d0",
"Special character": "\ud2b9\uc218\ubb38\uc790",
"Source code": "\uc18c\uc2a4\ucf54\ub4dc",
"Insert\/Edit code sample": "\ucf54\ub4dc\uc0d8\ud50c \uc0bd\uc785\/\ud3b8\uc9d1",
"Language": "\uc5b8\uc5b4",
"Code sample": "\ucf54\ub4dc\uc0d8\ud50c",
"Color": "\uc0c9\uc0c1",
"R": "R",
"G": "G",
"B": "B",
"Left to right": "\uc67c\ucabd\uc5d0\uc11c \uc624\ub978\ucabd",
"Right to left": "\uc624\ub978\ucabd\uc5d0\uc11c \uc67c\ucabd",
"Emoticons": "\uc774\ubaa8\ud2f0\ucf58",
"Document properties": "\ubb38\uc11c \uc18d\uc131",
"Title": "\uc81c\ubaa9",
"Keywords": "\ud0a4\uc6cc\ub4dc",
"Description": "\uc124\uba85",
"Robots": "\ub85c\ubd07",
"Author": "\uc800\uc790",
"Encoding": "\uc778\ucf54\ub529",
"Fullscreen": "\uc804\uccb4\ud654\uba74",
"Action": "\ub3d9\uc791",
"Shortcut": "\ub2e8\ucd95\ud0a4",
"Help": "\ub3c4\uc6c0\ub9d0",
"Address": "\uc8fc\uc18c",
"Focus to menubar": "\uba54\ub274\uc5d0 \ud3ec\ucee4\uc2a4",
"Focus to toolbar": "\ud234\ubc14\uc5d0 \ud3ec\ucee4\uc2a4",
"Focus to element path": "element path\uc5d0 \ud3ec\ucee4\uc2a4",
"Focus to contextual toolbar": "\ucf04\ud14d\uc2a4\ud2b8 \ud234\ubc14\uc5d0 \ud3ec\ucee4\uc2a4",
"Insert link (if link plugin activated)": "\ub9c1\ud06c \uc0bd\uc785 (link \ud50c\ub7ec\uadf8\uc778\uc774 \ud65c\uc131\ud654\ub41c \uc0c1\ud0dc\uc5d0\uc11c)",
"Save (if save plugin activated)": "\uc800\uc7a5 (save \ud50c\ub7ec\uadf8\uc778\uc774 \ud65c\uc131\ud654\ub41c \uc0c1\ud0dc\uc5d0\uc11c)",
"Find (if searchreplace plugin activated)": "\ucc3e\uae30(searchreplace \ud50c\ub7ec\uadf8\uc778\uc774 \ud65c\uc131\ud654\ub41c \uc0c1\ud0dc\uc5d0\uc11c)",
"Plugins installed ({0}):": "\uc124\uce58\ub41c \ud50c\ub7ec\uadf8\uc778 ({0}):",
"Premium plugins:": "\uace0\uae09 \ud50c\ub7ec\uadf8\uc778",
"Learn more...": "\uc880 \ub354 \uc0b4\ud3b4\ubcf4\uae30",
"You are using {0}": "{0}\ub97c \uc0ac\uc6a9\uc911",
"Plugins": "\ud50c\ub7ec\uadf8\uc778",
"Handy Shortcuts": "\ub2e8\ucd95\ud0a4",
"Horizontal line": "\uac00\ub85c",
"Insert\/edit image": "\uc774\ubbf8\uc9c0 \uc0bd\uc785\/\uc218\uc815",
"Image description": "\uc774\ubbf8\uc9c0 \uc124\uba85",
"Source": "\uc18c\uc2a4",
"Dimensions": "\ud06c\uae30",
"Constrain proportions": "\uc791\uc5c5 \uc81c\ud55c",
"General": "\uc77c\ubc18",
"Advanced": "\uace0\uae09",
"Style": "\uc2a4\ud0c0\uc77c",
"Vertical space": "\uc218\uc9c1 \uacf5\ubc31",
"Horizontal space": "\uc218\ud3c9 \uacf5\ubc31",
"Border": "\ud14c\ub450\ub9ac",
"Insert image": "\uc774\ubbf8\uc9c0 \uc0bd\uc785",
"Image": "\uc774\ubbf8\uc9c0",
"Image list": "\uc774\ubbf8\uc9c0 \ubaa9\ub85d",
"Rotate counterclockwise": "\uc2dc\uacc4\ubc18\ub300\ubc29\ud5a5\uc73c\ub85c \ud68c\uc804",
"Rotate clockwise": "\uc2dc\uacc4\ubc29\ud5a5\uc73c\ub85c \ud68c\uc804",
"Flip vertically": "\uc218\uc9c1 \ub4a4\uc9d1\uae30",
"Flip horizontally": "\uc218\ud3c9 \ub4a4\uc9d1\uae30",
"Edit image": "\uc774\ubbf8\uc9c0 \ud3b8\uc9d1",
"Image options": "\uc774\ubbf8\uc9c0 \uc635\uc158",
"Zoom in": "\ud655\ub300",
"Zoom out": "\ucd95\uc18c",
"Crop": "\uc790\ub974\uae30",
"Resize": "\ud06c\uae30 \uc870\uc808",
"Orientation": "\ubc29\ud5a5",
"Brightness": "\ubc1d\uae30",
"Sharpen": "\uc120\uba85\ud558\uac8c",
"Contrast": "\ub300\ube44",
"Color levels": "\uc0c9\uc0c1\ub808\ubca8",
"Gamma": "\uac10\ub9c8",
"Invert": "\ubc18\uc804",
"Apply": "\uc801\uc6a9",
"Back": "\ub4a4\ub85c",
"Insert date\/time": "\ub0a0\uc9dc\/\uc2dc\uac04\uc0bd\uc785",
"Date\/time": "\ub0a0\uc9dc\/\uc2dc\uac04",
"Insert link": "\ub9c1\ud06c \uc0bd\uc785 ",
"Insert\/edit link": "\ub9c1\ud06c \uc0bd\uc785\/\uc218\uc815",
"Text to display": "\ubcf8\ubb38",
"Url": "\uc8fc\uc18c",
"Target": "\ub300\uc0c1",
"None": "\uc5c6\uc74c",
"New window": "\uc0c8\ucc3d",
"Remove link": "\ub9c1\ud06c\uc0ad\uc81c",
"Anchors": "\ucc45\uac08\ud53c",
"Link": "\ub9c1\ud06c",
"Paste or type a link": "\ub9c1\ud06c\ub97c \ubd99\uc5ec\ub123\uac70\ub098 \uc785\ub825\ud558\uc138\uc694",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\ud604\uc7ac E-mail\uc8fc\uc18c\ub97c \uc785\ub825\ud558\uc168\uc2b5\ub2c8\ub2e4. E-mail \uc8fc\uc18c\uc5d0 \ub9c1\ud06c\ub97c \uac78\uae4c\uc694?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\ud604\uc7ac \uc6f9\uc0ac\uc774\ud2b8 \uc8fc\uc18c\ub97c \uc785\ub825\ud558\uc168\uc2b5\ub2c8\ub2e4. \ud574\ub2f9 \uc8fc\uc18c\uc5d0 \ub9c1\ud06c\ub97c \uac78\uae4c\uc694?",
"Link list": "\ub9c1\ud06c \ub9ac\uc2a4\ud2b8",
"Insert video": "\ube44\ub514\uc624 \uc0bd\uc785",
"Insert\/edit video": "\ube44\ub514\uc624 \uc0bd\uc785\/\uc218\uc815",
"Insert\/edit media": "\ubbf8\ub514\uc5b4 \uc0bd\uc785\/\uc218\uc815",
"Alternative source": "\ub300\uccb4 \uc18c\uc2a4",
"Poster": "\ud3ec\uc2a4\ud130",
"Paste your embed code below:": "\uc544\ub798\uc5d0 \ucf54\ub4dc\ub97c \ubd99\uc5ec\ub123\uc73c\uc138\uc694:",
"Embed": "\uc0bd\uc785",
"Media": "\ubbf8\ub514\uc5b4",
"Nonbreaking space": "\ub744\uc5b4\uc4f0\uae30",
"Page break": "\ud398\uc774\uc9c0 \uad6c\ubd84\uc790",
"Paste as text": "\ud14d\uc2a4\ud2b8\ub85c \ubd99\uc5ec\ub123\uae30",
"Preview": "\ubbf8\ub9ac\ubcf4\uae30",
"Print": "\ucd9c\ub825",
"Save": "\uc800\uc7a5",
"Find": "\ucc3e\uae30",
"Replace with": "\uad50\uccb4",
"Replace": "\uad50\uccb4",
"Replace all": "\uc804\uccb4 \uad50\uccb4",
"Prev": "\uc774\uc804",
"Next": "\ub2e4\uc74c",
"Find and replace": "\ucc3e\uc544\uc11c \uad50\uccb4",
"Could not find the specified string.": "\ubb38\uc790\ub97c \ucc3e\uc744 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",
"Match case": "\ub300\uc18c\ubb38\uc790 \uc77c\uce58",
"Whole words": "\uc804\uccb4 \ub2e8\uc5b4",
"Spellcheck": "\ubb38\ubc95\uccb4\ud06c",
"Ignore": "\ubb34\uc2dc",
"Ignore all": "\uc804\uccb4\ubb34\uc2dc",
"Finish": "\uc644\ub8cc",
"Add to Dictionary": "\uc0ac\uc804\uc5d0 \ucd94\uac00",
"Insert table": "\ud14c\uc774\ube14 \uc0bd\uc785",
"Table properties": "\ud14c\uc774\ube14 \uc18d\uc131",
"Delete table": "\ud14c\uc774\ube14 \uc0ad\uc81c",
"Cell": "\uc140",
"Row": "\uc5f4",
"Column": "\ud589",
"Cell properties": "\uc140 \uc18d",
"Merge cells": "\uc140 \ud569\uce58\uae30",
"Split cell": "\uc140 \ub098\ub204\uae30",
"Insert row before": "\uc774\uc804\uc5d0 \ud589 \uc0bd\uc785",
"Insert row after": "\ub2e4\uc74c\uc5d0 \ud589 \uc0bd\uc785",
"Delete row": "\ud589 \uc9c0\uc6b0\uae30",
"Row properties": "\ud589 \uc18d\uc131",
"Cut row": "\ud589 \uc798\ub77c\ub0b4\uae30",
"Copy row": "\ud589 \ubcf5\uc0ac",
"Paste row before": "\uc774\uc804\uc5d0 \ud589 \ubd99\uc5ec\ub123\uae30",
"Paste row after": "\ub2e4\uc74c\uc5d0 \ud589 \ubd99\uc5ec\ub123\uae30",
"Insert column before": "\uc774\uc804\uc5d0 \ud589 \uc0bd\uc785",
"Insert column after": "\ub2e4\uc74c\uc5d0 \uc5f4 \uc0bd\uc785",
"Delete column": "\uc5f4 \uc9c0\uc6b0\uae30",
"Cols": "\uc5f4",
"Rows": "\ud589",
"Width": "\ub113\uc774",
"Height": "\ub192\uc774",
"Cell spacing": "\uc140 \uac04\uaca9",
"Cell padding": "\uc140 \uc548\ucabd \uc5ec\ubc31",
"Caption": "\ucea1\uc158",
"Left": "\uc67c\ucabd",
"Center": "\uac00\uc6b4\ub370",
"Right": "\uc624\ub978\ucabd",
"Cell type": "\uc140 \ud0c0\uc785",
"Scope": "\ubc94\uc704",
"Alignment": "\uc815\ub82c",
"H Align": "\uac00\ub85c \uc815\ub82c",
"V Align": "\uc138\ub85c \uc815\ub82c",
"Top": "\uc0c1\ub2e8",
"Middle": "\uc911\uac04",
"Bottom": "\ud558\ub2e8",
"Header cell": "\ud5e4\ub354 \uc140",
"Row group": "\ud589 \uadf8\ub8f9",
"Column group": "\uc5f4 \uadf8\ub8f9",
"Row type": "\ud589 \ud0c0\uc785",
"Header": "\ud5e4\ub354",
"Body": "\ubc14\ub514",
"Footer": "\ud478\ud130",
"Border color": "\ud14c\ub450\ub9ac \uc0c9",
"Insert template": "\ud15c\ud50c\ub9bf \uc0bd\uc785",
"Templates": "\ud15c\ud50c\ub9bf",
"Template": "\ud15c\ud50c\ub9bf",
"Text color": "\ubb38\uc790 \uc0c9\uae54",
"Background color": "\ubc30\uacbd\uc0c9",
"Custom...": "\uc9c1\uc811 \uc0c9\uae54 \uc9c0\uc815\ud558\uae30",
"Custom color": "\uc9c1\uc811 \uc9c0\uc815\ud55c \uc0c9\uae54",
"No color": "\uc0c9\uc0c1 \uc5c6\uc74c",
"Table of Contents": "\ubaa9\ucc28",
"Show blocks": "\ube14\ub7ed \ubcf4\uc5ec\uc8fc\uae30",
"Show invisible characters": "\uc548\ubcf4\uc774\ub294 \ubb38\uc790 \ubcf4\uc774\uae30",
"Words: {0}": "\ub2e8\uc5b4: {0}",
"{0} words": "{0} \ub2e8\uc5b4",
"File": "\ud30c\uc77c",
"Edit": "\uc218\uc815",
"Insert": "\uc0bd\uc785",
"View": "\ubcf4\uae30",
"Format": "\ud3ec\ub9f7",
"Table": "\ud14c\uc774\ube14",
"Tools": "\ub3c4\uad6c",
"Powered by {0}": "Powered by {0}",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\uc11c\uc2dd \uc788\ub294 \ud14d\uc2a4\ud2b8 \ud3b8\uc9d1\uae30 \uc785\ub2c8\ub2e4. ALT-F9\ub97c \ub204\ub974\uba74 \uba54\ub274, ALT-F10\ub97c \ub204\ub974\uba74 \ud234\ubc14, ALT-0\uc744 \ub204\ub974\uba74 \ub3c4\uc6c0\ub9d0\uc744 \ubcfc \uc218 \uc788\uc2b5\ub2c8\ub2e4."
});
tinymce.addI18n('ml',{
"Cut": "\u0d2e\u0d41\u0d31\u0d3f\u0d2f\u0d4d\u0d15\u0d4d\u0d15\u0d41\u0d15 ",
"Header 2": "Header 2",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.",
"Div": "Div",
"Paste": "\u0d12\u0d1f\u0d4d\u0d1f\u0d3f\u0d2f\u0d4d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Close": "\u0d05\u0d1f\u0d2f\u0d4d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Font Family": "Font Family",
"Pre": "Pre",
"Align right": "\u0d35\u0d32\u0d24\u0d4d\u0d24\u0d4b\u0d1f\u0d4d\u0d1f\u0d41\u0d4d",
"New document": "\u0d2a\u0d41\u0d24\u0d3f\u0d2f \u0d30\u0d1a\u0d28",
"Blockquote": "Blockquote",
"Numbered list": "\u0d0e\u0d23\u0d4d\u0d23\u0d2e\u0d3f\u0d1f\u0d4d\u0d1f \u0d2a\u0d1f\u0d4d\u0d1f\u0d3f\u0d15",
"Increase indent": "\u0d35\u0d3f\u0d1f\u0d35\u0d41\u0d4d \u0d15\u0d42\u0d1f\u0d4d\u0d1f\u0d41\u0d15",
"Formats": "Formats",
"Headers": "Headers",
"Select all": "\u0d0e\u0d32\u0d4d\u0d32\u0d3e\u0d02",
"Header 3": "Header 3",
"Blocks": "Blocks",
"Undo": "\u0d35\u0d47\u0d23\u0d4d\u0d1f",
"Strikethrough": "\u0d35\u0d46\u0d1f\u0d4d\u0d1f\u0d41\u0d15",
"Bullet list": "\u0d05\u0d1f\u0d2f\u0d3e\u0d33\u0d2e\u0d3f\u0d1f\u0d4d\u0d1f \u0d2a\u0d1f\u0d4d\u0d1f\u0d3f\u0d15",
"Header 1": "Header 1",
"Superscript": "\u0d38\u0d42\u0d2a\u0d4d\u0d2a\u0d30\u0d4d\u200d\u0d38\u0d4d\u0d15\u0d4d\u0d30\u0d3f\u0d2a\u0d4d\u0d31\u0d4d\u0d31\u0d4d",
"Clear formatting": "\u0d35\u0d46\u0d1f\u0d3f\u0d2a\u0d4d\u0d2a\u0d3e\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Font Sizes": "Font Sizes",
"Subscript": "\u0d38\u0d2c\u0d4d\u200c\u0d38\u0d4d\u0d15\u0d4d\u0d30\u0d3f\u0d2a\u0d4d\u0d31\u0d4d\u0d31\u0d4d",
"Header 6": "Header 6",
"Redo": "\u0d35\u0d40\u0d23\u0d4d\u0d1f\u0d41\u0d02",
"Paragraph": "Paragraph",
"Ok": "\u0d36\u0d30\u0d3f",
"Bold": "\u0d15\u0d28\u0d24\u0d4d\u0d24",
"Code": "Code",
"Italic": "\u0d1a\u0d46\u0d30\u0d3f\u0d1e\u0d4d\u0d1e",
"Align center": "\u0d28\u0d1f\u0d41\u0d35\u0d3f\u0d32\u0d4b\u0d1f\u0d4d\u0d1f\u0d41\u0d4d",
"Header 5": "Header 5",
"Decrease indent": "\u0d35\u0d3f\u0d1f\u0d35\u0d41\u0d4d \u0d15\u0d41\u0d31\u0d2f\u0d4d\u0d15\u0d4d\u0d15\u0d41\u0d15 ",
"Header 4": "Header 4",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.",
"Underline": "\u0d05\u0d1f\u0d3f\u0d35\u0d30",
"Cancel": "\u0d31\u0d26\u0d4d\u0d26\u0d3e\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Justify": "\u0d38\u0d28\u0d4d\u0d24\u0d41\u0d32\u0d3f\u0d24\u0d2e\u0d3e\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Inline": "Inline",
"Copy": "\u0d2a\u0d15\u0d30\u0d4d\u200d\u0d24\u0d4d\u0d24\u0d41\u0d15",
"Align left": "\u0d07\u0d1f\u0d24\u0d4d\u0d24\u0d4b\u0d1f\u0d4d\u0d1f\u0d41\u0d4d",
"Visual aids": "\u0d26\u0d43\u0d36\u0d4d\u0d2f\u0d38\u0d39\u0d3e\u0d2f\u0d3f\u0d15\u0d33\u0d4d\u200d",
"Lower Greek": "Lower Greek",
"Square": "Square",
"Default": "Default",
"Lower Alpha": "Lower Alpha",
"Circle": "Circle",
"Disc": "Disc",
"Upper Alpha": "Upper Alpha",
"Upper Roman": "Upper Roman",
"Lower Roman": "Lower Roman",
"Name": "Name",
"Anchor": "Anchor",
"You have unsaved changes are you sure you want to navigate away?": "You have unsaved changes are you sure you want to navigate away?",
"Restore last draft": "Restore last draft",
"Special character": "Special character",
"Source code": "Source code",
"Right to left": "Right to left",
"Left to right": "Left to right",
"Emoticons": "Emoticons",
"Robots": "Robots",
"Document properties": "Document properties",
"Title": "Title",
"Keywords": "Keywords",
"Encoding": "Encoding",
"Description": "Description",
"Author": "Author",
"Fullscreen": "Fullscreen",
"Horizontal line": "Horizontal line",
"Horizontal space": "Horizontal space",
"Insert\/edit image": "Insert\/edit image",
"General": "General",
"Advanced": "Advanced",
"Source": "Source",
"Border": "Border",
"Constrain proportions": "Constrain proportions",
"Vertical space": "Vertical space",
"Image description": "Image description",
"Style": "Style",
"Dimensions": "Dimensions",
"Insert image": "Insert image",
"Insert date\/time": "Insert date\/time",
"Remove link": "Remove link",
"Url": "Url",
"Text to display": "Text to display",
"Anchors": "Anchors",
"Insert link": "Insert link",
"New window": "New window",
"None": "None",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?",
"Target": "Target",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",
"Insert\/edit link": "Insert\/edit link",
"Insert\/edit video": "Insert\/edit video",
"Poster": "Poster",
"Alternative source": "Alternative source",
"Paste your embed code below:": "Paste your embed code below:",
"Insert video": "Insert video",
"Embed": "Embed",
"Nonbreaking space": "Nonbreaking space",
"Page break": "Page break",
"Paste as text": "Paste as text",
"Preview": "Preview",
"Print": "Print",
"Save": "Save",
"Could not find the specified string.": "Could not find the specified string.",
"Replace": "Replace",
"Next": "Next",
"Whole words": "Whole words",
"Find and replace": "Find and replace",
"Replace with": "Replace with",
"Find": "Find",
"Replace all": "Replace all",
"Match case": "Match case",
"Prev": "Prev",
"Spellcheck": "Spellcheck",
"Finish": "Finish",
"Ignore all": "Ignore all",
"Ignore": "Ignore",
"Insert row before": "Insert row before",
"Rows": "Rows",
"Height": "Height",
"Paste row after": "Paste row after",
"Alignment": "Alignment",
"Column group": "Column group",
"Row": "Row",
"Insert column before": "Insert column before",
"Split cell": "Split cell",
"Cell padding": "Cell padding",
"Cell spacing": "Cell spacing",
"Row type": "Row type",
"Insert table": "Insert table",
"Body": "Body",
"Caption": "Caption",
"Footer": "Footer",
"Delete row": "Delete row",
"Paste row before": "Paste row before",
"Scope": "Scope",
"Delete table": "Delete table",
"Header cell": "Header cell",
"Column": "Column",
"Cell": "Cell",
"Header": "Header",
"Cell type": "Cell type",
"Copy row": "Copy row",
"Row properties": "Row properties",
"Table properties": "Table properties",
"Row group": "Row group",
"Right": "Right",
"Insert column after": "Insert column after",
"Cols": "Cols",
"Insert row after": "Insert row after",
"Width": "Width",
"Cell properties": "Cell properties",
"Left": "Left",
"Cut row": "Cut row",
"Delete column": "Delete column",
"Center": "Center",
"Merge cells": "Merge cells",
"Insert template": "Insert template",
"Templates": "Templates",
"Background color": "Background color",
"Text color": "Text color",
"Show blocks": "Show blocks",
"Show invisible characters": "Show invisible characters",
"Words: {0}": "Words: {0}",
"Insert": "Insert",
"File": "File",
"Edit": "Edit",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help",
"Tools": "Tools",
"View": "View",
"Table": "Table",
"Format": "Format"
});
tinymce.addI18n('ko',{
"Cut": "\uc790\ub974\uae30",
"Heading 5": "\uc81c\ubaa9 5",
"Header 2": "\uc81c\ubaa9 2",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\ud074\ub9bd\ubcf4\ub4dc\uc5d0 \ubc14\ub85c \uc811\uadfc\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4. \ud0a4\ubcf4\ub4dc \ub2e8\ucd95\ud0a4 Ctrl+X\/C\/V \ub97c \uc774\uc6a9\ud574 \uc8fc\uc2ed\uc2dc\uc624.",
"Heading 4": "\uc81c\ubaa9 4",
"Div": "\ubc14\ud0d5\uae00",
"Heading 2": "\uc81c\ubaa9 2",
"Paste": "\ubd99\uc774\uae30",
"Close": "\ub2eb\uae30",
"Font Family": "\uae00\uaf34",
"Pre": "\uc815\ud615 \ubb38\ub2e8",
"Align right": "\uc624\ub978\ucabd \ub9de\ucda4",
"New document": "\uc0c8\ubb38\uc11c",
"Blockquote": "\uc778\uc6a9 \ub2e8\ub77d",
"Numbered list": "\uc21c\uc11c \uc788\ub294 \ubaa9\ub85d",
"Heading 1": "\uc81c\ubaa9 1",
"Headings": "\uc81c\ubaa9",
"Increase indent": "\ub4e4\uc5ec\uc4f0\uae30",
"Formats": "\ud615\uc2dd",
"Headers": "\ubb38\ub2e8 \ud615\uc2dd",
"Select all": "\uc804\uccb4\uc120\ud0dd",
"Header 3": "\uc81c\ubaa9 3",
"Blocks": "\ub2e8\ub77d",
"Undo": "\ub418\ub3cc\ub9ac\uae30",
"Strikethrough": "\ucde8\uc18c\uc120",
"Bullet list": "\uae00\uba38\ub9ac \uae30\ud638",
"Header 1": "\uc81c\ubaa9 1",
"Superscript": "\uc704 \ucca8\uc790",
"Clear formatting": "\ud615\uc2dd \uc9c0\uc6b0\uae30",
"Font Sizes": "\uae00\uc790 \ud06c\uae30",
"Subscript": "\uc544\ub798 \ucca8\uc790",
"Header 6": "\uc81c\ubaa9 6",
"Redo": "\ub2e4\uc2dc \uc2e4\ud589",
"Paragraph": "\ubcf8\ubb38",
"Ok": "\ud655\uc778",
"Bold": "\uad75\uac8c",
"Code": "\ucf54\ub4dc",
"Italic": "\uae30\uc6b8\uc784 \uaf34",
"Align center": "\uac00\uc6b4\ub370 \ub9de\ucda4",
"Header 5": "\uc81c\ubaa9 5",
"Heading 6": "\uc81c\ubaa9 6",
"Heading 3": "\uc81c\ubaa9 3",
"Decrease indent": "\ub0b4\uc5b4\uc4f0\uae30",
"Header 4": "\uc81c\ubaa9 4",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\ud14d\uc2a4\ud2b8\ub9cc \uc720\uc9c0\ud558\uace0 \ubd99\uc5ec\ub123\uc2b5\ub2c8\ub2e4. \uc774 \ub2e8\ucd94\ub97c \ub044\uae30 \uc804\uae4c\uc9c0 \ubd99\uc5ec\ub123\ub294 \ubaa8\ub4e0 \ub0b4\uc6a9\uc758 \ud615\uc2dd\uc774 \uc81c\uac70\ub429\ub2c8\ub2e4.",
"Underline": "\ubc11\uc904",
"Cancel": "\ucde8\uc18c",
"Justify": "\uc591\ucabd \ub9de\ucda4",
"Inline": "\uc778\ub77c\uc778",
"Copy": "\ubcf5\uc0ac\ud558\uae30",
"Align left": "\uc67c\ucabd \ub9de\ucda4",
"Visual aids": "\uc2dc\uac01\uc790\ub8cc",
"Lower Greek": "\uadf8\ub9ac\uc2a4 \uc18c\ubb38\uc790",
"Square": "\uc0ac\uac01\ud615",
"Default": "\uae30\ubcf8",
"Lower Alpha": "\uc601\uc18c\ubb38\uc790",
"Circle": "\uc6d0",
"Disc": "\uc6d0\ubc18",
"Upper Alpha": "\uc601\ub300\ubb38\uc790",
"Upper Roman": "\ub85c\ub9c8 \ub300\ubb38\uc790",
"Lower Roman": "\ub85c\ub9c8 \uc18c\ubb38\uc790",
"Name": "\uc774\ub984",
"Anchor": "\ucc45\uac08\ud53c",
"You have unsaved changes are you sure you want to navigate away?": "\uc800\uc7a5 \ub418\uc9c0 \uc54a\uc740 \ubcc0\uacbd\uc0ac\ud56d\uc774 \uc788\uc2b5\ub2c8\ub2e4. \uc774 \ud398\uc774\uc9c0\ub97c \ub098\uac00\uc2dc\uaca0\uc2b5\ub2c8\uae4c?",
"Restore last draft": "\ucd5c\uadfc \uc218\uc815 \ubd88\ub7ec\uc624\uae30",
"Special character": "\ud2b9\uc218\ubb38\uc790",
"Source code": "\uc18c\uc2a4\ucf54\ub4dc",
"B": "\uccad",
"R": "\uc801",
"G": "\ub179",
"Color": "\uc0c9\uc0c1",
"Right to left": "\uc624\ub978\ucabd\uc5d0\uc11c \uc67c\ucabd",
"Left to right": "\uc67c\ucabd\uc5d0\uc11c \uc624\ub978\ucabd",
"Emoticons": "\uc774\ubaa8\ud2f0\ucf58",
"Robots": "\ub85c\ubd07",
"Document properties": "\ubb38\uc11c \uc18d\uc131",
"Title": "\uc81c\ubaa9",
"Keywords": "\ud575\uc2ec\uc5b4",
"Encoding": "\uc778\ucf54\ub529",
"Description": "\uc124\uba85",
"Author": "\uae00\uc4f4\uc774",
"Fullscreen": "\uc804\uccb4 \ud654\uba74",
"Horizontal line": "\uac00\ub85c \uc904",
"Horizontal space": "\uac00\ub85c \uac04\uaca9",
"Insert\/edit image": "\uc774\ubbf8\uc9c0 \uc0bd\uc785\/\ud3b8\uc9d1",
"General": "\uc77c\ubc18",
"Advanced": "\uace0\uae09",
"Source": "\uc18c\uc2a4",
"Border": "\ud14c\ub450\ub9ac",
"Constrain proportions": "\ube44\uc728 \uc720\uc9c0",
"Vertical space": "\uc138\ub85c \uac04\uaca9",
"Image description": "\uc774\ubbf8\uc9c0 \uc124\uba85",
"Style": "\ub9f5\uc528",
"Dimensions": "\ud06c\uae30",
"Insert image": "\uc774\ubbf8\uc9c0 \uc0bd\uc785",
"Zoom in": "\ud655\ub300",
"Contrast": "\ub300\ube44",
"Back": "\ub4a4\ub85c",
"Gamma": "\uac10\ub9c8",
"Flip horizontally": "\uc218\ud3c9 \ubc18\uc804",
"Resize": "\uadf8\ub9bc \uc555\ucd95",
"Sharpen": "\uc120\uba85\ub3c4",
"Zoom out": "\ucd95\uc18c",
"Image options": "\uc774\ubbf8\uc9c0 \uc635\uc158",
"Apply": "\uc801\uc6a9",
"Brightness": "\uba85\ub3c4",
"Rotate clockwise": "\uc2dc\uacc4 \ubc29\ud5a5\uc73c\ub85c \ud68c\uc804",
"Rotate counterclockwise": "\ubc18\uc2dc\uacc4 \ubc29\ud5a5\uc73c\ub85c \ud68c\uc804",
"Edit image": "\uc774\ubbf8\uc9c0 \ud3b8\uc9d1",
"Color levels": "\ucc44\ub3c4",
"Crop": "\uc790\ub974\uae30",
"Orientation": "\ubc29\ud5a5",
"Flip vertically": "\uc218\uc9c1 \ubc18\uc804",
"Invert": "\ubc18\uc804",
"Insert date\/time": "\ub0a0\uc9dc\/\uc2dc\uac04 \uc0bd\uc785",
"Remove link": "\ub9c1\ud06c \uc81c\uac70",
"Url": "\uc8fc\uc18c",
"Text to display": "\ub098\ud0c0\ub0bc \ubb38\uc790\uc5f4",
"Anchors": "\ucc45\uac08\ud53c",
"Insert link": "\ub9c1\ud06c \uc0bd\uc785",
"New window": "\uc0c8 \ucc3d",
"None": "\uc5c6\uc74c",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\uc8fc\uc18c(URL)\uac00 \uc678\ubd80 \ub9c1\ud06c\uc778 \uac83 \uac19\uc2b5\ub2c8\ub2e4. http:\/\/ \uc55e\uba38\ub9ac\ub97c \ubd99\uc774\uaca0\uc2b5\ub2c8\uae4c?",
"Target": "\ud0c0\uac9f",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\uc8fc\uc18c(URL)\uac00 \uc774\uba54\uc77c \uc8fc\uc18c\uc778 \uac83 \uac19\uc2b5\ub2c8\ub2e4. mailto: \uc55e\uba38\ub9ac\ub97c \ubd99\uc774\uaca0\uc2b5\ub2c8\uae4c?",
"Insert\/edit link": "\ub9c1\ud06c \uc0bd\uc785\/\ud3b8\uc9d1",
"Insert\/edit video": "\ube44\ub514\uc624 \uc0bd\uc785\/\ud3b8\uc9d1",
"Poster": "\ud3ec\uc2a4\ud130",
"Alternative source": "\ub300\uccb4 \uc18c\uc2a4",
"Paste your embed code below:": "\uc544\ub798\uc5d0 \ucf54\ub4dc\ub97c \ubd99\uc5ec\ub123\uc73c\uc138\uc694:",
"Insert video": "\ube44\ub514\uc624 \uc0bd\uc785",
"Embed": "\uc0bd\uc785",
"Nonbreaking space": " \uc904 \ubc14\uafc8 \uc5c6\ub294 \uacf5\ubc31",
"Page break": "\ud398\uc774\uc9c0 \ub098\ub214",
"Paste as text": "\ubb38\uc790\uc5f4\ub9cc \ubd99\uc5ec\ub123\uae30",
"Preview": "\ubbf8\ub9ac\ubcf4\uae30",
"Print": "\uc778\uc1c4",
"Save": "\uc800\uc7a5",
"Could not find the specified string.": "\ubb38\uc790\uc5f4\uc744 \ucc3e\uc744 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",
"Replace": "\ubc14\uafb8\uae30",
"Next": "\ub2e4\uc74c",
"Whole words": "\uc628\uc804\ud55c \ub2e8\uc5b4",
"Find and replace": "\ucc3e\uae30 \ubc0f \ubc14\uafb8\uae30",
"Replace with": "\ucc3e\uc744 \ub0b4\uc6a9",
"Find": "\ucc3e\uae30",
"Replace all": "\ubaa8\ub450 \ubc14\uafb8\uae30",
"Match case": "\ub300\uc18c\ubb38\uc790 \uad6c\ubd84",
"Prev": "\uc774\uc804",
"Spellcheck": "\ub9de\ucda4\ubc95",
"Finish": "\ub9c8\uce68",
"Ignore all": "\ubaa8\ub450 \ubb34\uc2dc",
"Ignore": "\ubb34\uc2dc",
"Add to Dictionary": "\uc0ac\uc804\uc5d0 \ucd94\uac00",
"Insert row before": "\uc704\uc5d0 \ud589 \uc0bd\uc785",
"Rows": "\ud589",
"Height": "\ub192\uc774",
"Paste row after": "\uc544\ub798\uc5d0 \ud589 \ubd99\uc5ec\ub123\uae30",
"Alignment": "\uc815\ub82c",
"Border color": "\ud14c\ub450\ub9ac \uc0c9",
"Column group": "\uc5f4 \uadf8\ub8f9",
"Row": "\ud589",
"Insert column before": "\uc67c\ucabd\uc5d0 \uc5f4 \uc0bd\uc785",
"Split cell": "\uc140 \ub098\ub204\uae30",
"Cell padding": "\uc140 \ub0b4\ubd80 \uc5ec\ubc31",
"Cell spacing": "\uc140 \uac04\uaca9",
"Row type": "\ud589 \uc885\ub958",
"Insert table": "\ud45c \uc0bd\uc785",
"Body": "\ubcf8\ubb38",
"Caption": "\uc8fc\uc11d",
"Footer": "\ubc14\ub2e5\uae00",
"Delete row": "\ud589 \uc0ad\uc81c",
"Paste row before": "\uc704\uc5d0 \ud589 \ubd99\uc5ec\ub123\uae30",
"Scope": "\ubc94\uc704",
"Delete table": "\ud45c \uc0ad\uc81c",
"H Align": "\uac00\ub85c \uc815\ub82c",
"Top": "\uc0c1\ub2e8",
"Header cell": "\uba38\ub9bf\uce78",
"Column": "\uc5f4",
"Row group": "\ud589 \uadf8\ub8f9",
"Cell": "\uc140",
"Middle": "\uc911\uac04",
"Cell type": "\uc140 \uc885\ub958",
"Copy row": "\ud589 \ubcf5\uc0ac",
"Row properties": "\ud589 \uc18d\uc131",
"Table properties": "\ud45c \uc18d\uc131",
"Bottom": "\ud558\ub2e8",
"V Align": "\uc138\ub85c \uc815\ub82c",
"Header": "\uba38\ub9bf\uae00",
"Right": "\uc624\ub978\ucabd",
"Insert column after": "\uc624\ub978\ucabd\uc5d0 \uc5f4 \uc0bd\uc785",
"Cols": "\uc5f4",
"Insert row after": "\uc544\ub798\uc5d0 \ud589 \uc0bd\uc785",
"Width": "\ub108\ube44",
"Cell properties": "\uc140 \uc18d\uc131",
"Left": "\uc67c\ucabd",
"Cut row": "\ud589 \uc798\ub77c\ub0b4\uae30",
"Delete column": "\uc5f4 \uc0ad\uc81c",
"Center": "\uac00\uc6b4\ub370",
"Merge cells": "\uc140 \ud569\uce58\uae30",
"Insert template": "\ud15c\ud50c\ub9bf \uc0bd\uc785",
"Templates": "\ud15c\ud50c\ub9bf",
"Background color": "\ubc30\uacbd \uc0c9",
"Custom...": "\uc0ac\uc6a9\uc790...",
"Custom color": "\uc0ac\uc6a9\uc790 \uc0c9\uc0c1",
"No color": "\uc0c9 \uc5c6\uc74c",
"Text color": "\uae00\uc790 \uc0c9",
"Show blocks": "\ub2e8\ub77d \ubcf4\uae30",
"Show invisible characters": "\ubcf4\uc774\uc9c0 \uc54a\ub294 \ubb38\uc790 \ubcf4\uae30",
"Words: {0}": "{0}\uac1c \ub2e8\uc5b4",
"Insert": "\uc0bd\uc785",
"File": "\ud30c\uc77c",
"Edit": "\ud3b8\uc9d1",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\ub9ac\uce58 \ud14d\uc2a4\ud2b8 \uc601\uc5ed. \uba54\ub274\ub85c \uc774\ub3d9\ud558\ub824\uba74 ALT-F9 \ud0a4\ub97c \ub204\ub974\uc138\uc694. \ud234\ubc14\ub85c \uc774\ub3d9\ud558\ub824\uba74 ALT-F10 \ud0a4\ub97c \ub204\ub974\uc138\uc694. \ub3c4\uc6c0\ub9d0\uc744 \ubcf4\ub824\uba74 ALT-0 \ud0a4\ub97c \ub204\ub974\uc138\uc694.",
"Tools": "\ub3c4\uad6c",
"View": "\ubcf4\uae30",
"Table": "\ud45c",
"Format": "\ud615\uc2dd"
});
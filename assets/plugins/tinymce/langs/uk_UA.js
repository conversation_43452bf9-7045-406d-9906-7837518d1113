tinymce.addI18n('uk_UA',{
"Redo": "\u0412\u0456\u0434\u043d\u043e\u0432\u0438\u0442\u0438",
"Undo": "\u0412\u0456\u0434\u043c\u0456\u043d\u0438\u0442\u0438",
"Cut": "\u0412\u0438\u0440\u0456\u0437\u0430\u0442\u0438",
"Copy": "\u041a\u043e\u043f\u0456\u044e\u0432\u0430\u0442\u0438",
"Paste": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438",
"Select all": "\u0412\u0438\u0431\u0435\u0440\u0456\u0442\u044c \u0443\u0441\u0435",
"New document": "\u041d\u043e\u0432\u0438\u0439 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442",
"Ok": "Ok",
"Cancel": "\u0412\u0456\u0434\u043c\u0456\u043d\u0438\u0442\u0438",
"Visual aids": "\u0412\u0456\u0437\u0443\u0430\u043b\u044c\u043d\u0456 \u0437\u0430\u0441\u043e\u0431\u0438",
"Bold": "\u0416\u0438\u0440\u043d\u0438\u0439",
"Italic": "\u041a\u0443\u0440\u0441\u0438\u0432",
"Underline": "\u041f\u0456\u0434\u043a\u0440\u0435\u0441\u043b\u0435\u043d\u0438\u0439",
"Strikethrough": "\u041f\u0435\u0440\u0435\u043a\u0440\u0435\u0441\u043b\u0435\u043d\u0438\u0439",
"Superscript": "\u0412\u0435\u0440\u0445\u043d\u0456\u0439 \u0456\u043d\u0434\u0435\u043a\u0441",
"Subscript": "\u0406\u043d\u0434\u0435\u043a\u0441",
"Clear formatting": "\u041e\u0447\u0438\u0441\u0442\u0438\u0442\u0438 \u0444\u043e\u0440\u043c\u0430\u0442\u0443\u0432\u0430\u043d\u043d\u044f",
"Align left": "\u041b\u0456\u0432\u043e\u0440\u0443\u0447",
"Align center": "\u041f\u043e \u0446\u0435\u043d\u0442\u0440\u0443",
"Align right": "\u041f\u0440\u0430\u0432\u043e\u0440\u0443\u0447",
"Justify": "\u0412\u0438\u0440\u0456\u0432\u043d\u044f\u0442\u0438",
"Bullet list": "\u041c\u0430\u0440\u043a\u0456\u0440\u043e\u0432\u0430\u043d\u0438\u0439 \u0441\u043f\u0438\u0441\u043e\u043a",
"Numbered list": "\u041f\u0440\u043e\u043d\u0443\u043c\u0435\u0440\u043e\u0432\u0430\u043d\u0438\u0439 \u0441\u043f\u0438\u0441\u043e\u043a",
"Decrease indent": "\u0417\u043c\u0435\u043d\u0448\u0438\u0442\u0438 \u0432\u0456\u0434\u0441\u0442\u0443\u043f",
"Increase indent": "\u0417\u0431\u0456\u043b\u044c\u0448\u0438\u0442\u0438 \u0432\u0456\u0434\u0441\u0442\u0443\u043f",
"Close": "\u0417\u0430\u043a\u0440\u0438\u0442\u0438",
"Formats": "\u0424\u043e\u0440\u043c\u0430\u0442\u0438",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\u0412\u0430\u0448 \u0431\u0440\u0430\u0443\u0437\u0435\u0440 \u043d\u0435 \u043f\u0456\u0434\u0442\u0440\u0438\u043c\u0443\u0454 \u043f\u0440\u044f\u043c\u0438\u0439 \u0434\u043e\u0441\u0442\u0443\u043f \u0434\u043e \u0431\u0443\u0444\u0435\u0440\u0430 \u043e\u0431\u043c\u0456\u043d\u0443. \u0417\u0430\u043c\u0456\u0441\u0442\u044c \u0446\u044c\u043e\u0433\u043e \u0432\u0438\u043a\u043e\u0440\u0438\u0441\u0442\u043e\u0432\u0443\u0439\u0442\u0435 \u043f\u043e\u0454\u0434\u043d\u0430\u043d\u043d\u044f \u043a\u043b\u0430\u0432\u0456\u0448 Ctrl + X\/C\/V.",
"Headers": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043a\u0438",
"Header 1": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 1",
"Header 2": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 2",
"Header 3": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 3",
"Header 4": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 4",
"Header 5": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 5",
"Header 6": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 6",
"Headings": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043a\u0438",
"Heading 1": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 1",
"Heading 2": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 2",
"Heading 3": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 3",
"Heading 4": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 4",
"Heading 5": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 5",
"Heading 6": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a 6",
"Preformatted": "\u041f\u043e\u043f\u0435\u0440\u0435\u0434\u043d\u044c\u043e \u0432\u0456\u0434\u0444\u043e\u0440\u043c\u0430\u0442\u043e\u0432\u0430\u043d\u0438\u0439",
"Div": "Div",
"Pre": "Pre",
"Code": "\u041a\u043e\u0434",
"Paragraph": "\u0410\u0431\u0437\u0430\u0446",
"Blockquote": "\u0426\u0438\u0442\u0430\u0442\u0430",
"Inline": "\u0412\u0431\u0443\u0434\u043e\u0432\u0430\u043d\u0438\u0439",
"Blocks": "\u0411\u043b\u043e\u043a\u0438",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\u0412\u0441\u0442\u0430\u0432\u043a\u0430 \u0437\u0430\u0440\u0430\u0437 \u0432 \u0440\u0435\u0436\u0438\u043c\u0456 \u0437\u0432\u0438\u0447\u0430\u0439\u043d\u043e\u0433\u043e \u0442\u0435\u043a\u0441\u0442\u0443. \u0417\u043c\u0456\u0441\u0442 \u0431\u0443\u0434\u0435 \u0432\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0438\u0439 \u044f\u043a \u043f\u0440\u043e\u0441\u0442\u0438\u0439 \u0442\u0435\u043a\u0441\u0442, \u043f\u043e\u043a\u0438 \u0412\u0438 \u043d\u0435 \u0432\u0438\u043c\u043a\u043d\u0435\u0442\u0435 \u0446\u044e \u043e\u043f\u0446\u0456\u044e.",
"Font Family": "\u0428\u0440\u0438\u0444\u0442",
"Font Sizes": "\u0420\u043e\u0437\u043c\u0456\u0440 \u0448\u0440\u0438\u0444\u0442\u0430",
"Class": "\u041a\u043b\u0430\u0441",
"Browse for an image": "\u0412\u0438\u0431\u0456\u0440 \u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u043d\u044f",
"OR": "\u0410\u0411\u041e",
"Drop an image here": "\u041f\u0435\u0440\u0435\u043c\u0456\u0441\u0442\u0456\u0442\u044c \u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u043d\u044f \u0441\u044e\u0434\u0438",
"Upload": "\u0417\u0430\u0432\u0430\u043d\u0442\u0430\u0436\u0438\u0442\u0438",
"Block": "\u0411\u043b\u043e\u043a",
"Align": "\u0412\u0438\u0440\u0456\u0432\u043d\u044e\u0432\u0430\u043d\u043d\u044f",
"Default": "\u0423\u043c\u043e\u0432\u0447\u0430\u043d\u043d\u044f",
"Circle": "\u041a\u043e\u043b\u043e",
"Disc": "\u0414\u0438\u0441\u043a",
"Square": "\u041a\u0432\u0430\u0434\u0440\u0430\u0442",
"Lower Alpha": "\u041d\u0438\u0436\u043d\u0456\u0439 \u0440\u0435\u0433\u0456\u0441\u0442\u0440",
"Lower Greek": "\u041c\u0430\u043b\u0456 \u0433\u0440\u0435\u0446\u044c\u043a\u0456 \u043b\u0456\u0442\u0435\u0440\u0438",
"Lower Roman": "\u0420\u0438\u043c\u0441\u044c\u043a\u0456 \u0446\u0438\u0444\u0440\u0438 \u0443 \u043d\u0438\u0436\u043d\u044c\u043e\u043c\u0443 \u0440\u0435\u0433\u0456\u0441\u0442\u0440\u0456",
"Upper Alpha": "\u0412\u0435\u0440\u0445\u043d\u0456\u0439 \u0440\u0435\u0433\u0456\u0441\u0442\u0440",
"Upper Roman": "\u0420\u0438\u043c\u0441\u044c\u043a\u0456 \u0446\u0438\u0444\u0440\u0438 \u0443 \u0432\u0435\u0440\u0445\u043d\u044c\u043e\u043c\u0443 \u0440\u0435\u0433\u0456\u0441\u0442\u0440\u0456",
"Anchor": "\u041f\u0440\u0438\u0432'\u044f\u0437\u043a\u0430",
"Name": "\u0406\u043c'\u044f",
"Id": "\u041a\u043e\u0434",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "\u041a\u043e\u0434 \u043c\u0430\u0454 \u043f\u043e\u0447\u0438\u043d\u0430\u0442\u0438\u0441\u044f \u0437 \u043b\u0456\u0442\u0435\u0440\u0438 \u0456 \u043c\u043e\u0436\u0435 \u043c\u0456\u0441\u0442\u0438\u0442\u0438 \u043b\u0438\u0448\u0435 \u0441\u0438\u043c\u0432\u043e\u043b\u0438 \u043b\u0456\u0442\u0435\u0440, \u0446\u0438\u0444\u0440, \u0434\u0435\u0444\u0456\u0441\u0443, \u043a\u0440\u0430\u043f\u043a\u0438, \u043a\u043e\u043c\u0438 \u0430\u0431\u043e \u043d\u0438\u0436\u043d\u044c\u043e\u0433\u043e \u043f\u0456\u0434\u043a\u0440\u0435\u0441\u043b\u0435\u043d\u043d\u044f.",
"You have unsaved changes are you sure you want to navigate away?": "\u0423 \u0432\u0430\u0441 \u0454 \u043d\u0435\u0437\u0431\u0435\u0440\u0435\u0436\u0435\u043d\u0456 \u0437\u043c\u0456\u043d\u0438. \u0412\u0438 \u0432\u043f\u0435\u0432\u043d\u0435\u043d\u0456, \u0449\u043e \u0445\u043e\u0447\u0435\u0442\u0435 \u043f\u0456\u0442\u0438 ?",
"Restore last draft": "\u0412\u0456\u0434\u043d\u043e\u0432\u0438\u0442\u0438 \u043e\u0441\u0442\u0430\u043d\u043d\u0456\u0439 \u043f\u0440\u043e\u0435\u043a\u0442",
"Special character": "\u0421\u043f\u0435\u0446\u0456\u0430\u043b\u044c\u043d\u0438\u0439 \u0441\u0438\u043c\u0432\u043e\u043b",
"Source code": "\u0414\u0436\u0435\u0440\u0435\u043b\u043e",
"Insert\/Edit code sample": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438\/\u041d\u0430\u043f\u0438\u0441\u0430\u0442\u0438 \u043f\u0440\u0438\u043a\u043b\u0430\u0434 \u043a\u043e\u0434\u0443",
"Language": "\u041c\u043e\u0432\u0430",
"Code sample": "\u041f\u0440\u0438\u043a\u043b\u0430\u0434 \u043a\u043e\u0434\u0443",
"Color": "\u041a\u043e\u043b\u0456\u0440",
"R": "R",
"G": "G",
"B": "B",
"Left to right": "\u0417\u043b\u0456\u0432\u0430 \u043d\u0430\u043f\u0440\u0430\u0432\u043e",
"Right to left": "\u0421\u043f\u0440\u0430\u0432\u0430 \u043d\u0430\u043b\u0456\u0432\u043e",
"Emoticons": "\u0421\u043c\u0430\u0439\u043b\u0438",
"Document properties": "\u0412\u043b\u0430\u0441\u0442\u0438\u0432\u043e\u0441\u0442\u0456 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0443",
"Title": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a",
"Keywords": "\u041a\u043b\u044e\u0447\u043e\u0432\u0456 \u0441\u043b\u043e\u0432\u0430",
"Description": "\u041e\u043f\u0438\u0441",
"Robots": "\u0420\u043e\u0431\u043e\u0442\u0438",
"Author": "\u0410\u0432\u0442\u043e\u0440",
"Encoding": "\u041a\u043e\u0434\u0443\u0432\u0430\u043d\u043d\u044f",
"Fullscreen": "\u041d\u0430 \u0432\u0435\u0441\u044c \u0435\u043a\u0440\u0430\u043d",
"Action": "\u0414\u0456\u044f",
"Shortcut": "\u042f\u0440\u043b\u0438\u043a",
"Help": "\u0414\u043e\u043f\u043e\u043c\u043e\u0433\u0430",
"Address": "\u0410\u0434\u0440\u0435\u0441\u0430",
"Focus to menubar": "\u0424\u043e\u043a\u0443\u0441 \u043d\u0430 \u043c\u0435\u043d\u044e",
"Focus to toolbar": "\u0424\u043e\u043a\u0443\u0441 \u043d\u0430 \u0456\u043d\u0441\u0442\u0442\u0440\u0443\u043c\u0435\u043d\u0442\u0430\u0445",
"Focus to element path": "\u0424\u043e\u043a\u0443\u0441 \u043d\u0430 \u0448\u043b\u044f\u0445\u0443",
"Focus to contextual toolbar": "\u0424\u043e\u043a\u0443\u0441 \u043d\u0430 \u043a\u043e\u043d\u0442\u0435\u043a\u0441\u0442",
"Insert link (if link plugin activated)": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u043f\u043e\u0441\u0438\u043b\u0430\u043d\u043d\u044f (\u044f\u043a\u0449\u043e \u0434\u043e\u0437\u0432\u043e\u043b\u0435\u043d\u043e)",
"Save (if save plugin activated)": "\u0417\u0431\u0435\u0440\u0435\u0433\u0442\u0438 (\u044f\u043a\u0449\u043e \u0434\u043e\u0437\u0432\u043e\u043b\u0435\u043d\u043e)",
"Find (if searchreplace plugin activated)": "\u0417\u043d\u0430\u0439\u0442\u0438 (\u044f\u043a\u0449\u043e \u0434\u043e\u0437\u0432\u043e\u043b\u0435\u043d\u043e)",
"Plugins installed ({0}):": "\u041d\u0430\u044f\u0432\u043d\u0456 \u0434\u043e\u0434\u0430\u0442\u043a\u0438 ({0}):",
"Premium plugins:": "\u041f\u0440\u0435\u043c\u0456\u0430\u043b\u044c\u043d\u0456 \u0434\u043e\u0434\u0430\u0442\u043a\u0438:",
"Learn more...": "\u0414\u043e\u0434\u0430\u0442\u043a\u043e\u0432\u043e...",
"You are using {0}": "\u0423 \u0432\u0438\u043a\u043e\u0440\u0438\u0441\u0442\u0430\u043d\u043d\u0456 {0}",
"Plugins": "\u041f\u043b\u0430\u0433\u0456\u043d\u0438",
"Handy Shortcuts": "\u041a\u043e\u0440\u0438\u0441\u043d\u0456 \u044f\u0440\u043b\u0438\u043a\u0438",
"Horizontal line": "\u0413\u043e\u0440\u0438\u0437\u043e\u043d\u0442\u0430\u043b\u044c\u043d\u0430 \u043b\u0456\u043d\u0456\u044f",
"Insert\/edit image": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438\/\u0440\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438 \u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u043d\u044f",
"Image description": "\u041e\u043f\u0438\u0441 \u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u043d\u044f",
"Source": "\u0414\u0436\u0435\u0440\u0435\u043b\u043e",
"Dimensions": "\u0420\u043e\u0437\u043c\u0456\u0440",
"Constrain proportions": "\u0417\u0431\u0435\u0440\u0456\u0433\u0430\u0442\u0438 \u043f\u0440\u043e\u043f\u043e\u0440\u0446\u0456\u0457",
"General": "\u0417\u0430\u0433\u0430\u043b\u044c\u043d\u0435",
"Advanced": "\u0414\u043e\u0434\u0430\u0442\u043a\u043e\u0432\u043e",
"Style": "\u0421\u0442\u0438\u043b\u044c",
"Vertical space": "\u0412\u0435\u0440\u0442\u0438\u043a\u0430\u043b\u044c\u043d\u0438\u0439 \u043f\u0440\u043e\u043f\u0443\u0441\u043a",
"Horizontal space": "\u0413\u043e\u0440\u0438\u0437\u043e\u043d\u0442\u0430\u043b\u044c\u043d\u0438\u0439 \u043f\u0440\u043e\u043f\u0443\u0441\u043a",
"Border": "\u041c\u0435\u0436\u0430",
"Insert image": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u043d\u044f",
"Image": "\u0417\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u043d\u044f",
"Image list": "\u0421\u043f\u0438\u0441\u043e\u043a \u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u044c",
"Rotate counterclockwise": "\u041f\u043e\u0432\u0435\u0440\u043d\u0443\u0442\u0438 \u043f\u0440\u043e\u0442\u0438 \u0433\u043e\u0434\u0438\u043d\u043d\u0438\u043a\u043e\u0432\u043e\u0457 \u0441\u0442\u0440\u0456\u043b\u043a\u0438",
"Rotate clockwise": "\u041f\u043e\u0432\u0435\u0440\u043d\u0443\u0442\u0438 \u0437\u0430 \u0433\u043e\u0434\u0438\u043d\u043d\u0438\u043a\u043e\u0432\u043e\u044e \u0441\u0442\u0440\u0456\u043b\u043a\u043e\u044e",
"Flip vertically": "\u0412\u0456\u0434\u043e\u0431\u0440\u0430\u0437\u0438\u0442\u0438 \u043f\u043e \u0432\u0435\u0440\u0442\u0438\u043a\u0430\u043b\u0456",
"Flip horizontally": "\u0412\u0456\u0434\u043e\u0431\u0440\u0430\u0437\u0438\u0442\u0438 \u043f\u043e \u0433\u043e\u0440\u0438\u0437\u043e\u043d\u0442\u0430\u043b\u0456",
"Edit image": "\u0420\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438 \u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u043d\u044f",
"Image options": "\u041d\u0430\u043b\u0430\u0448\u0442\u0443\u0432\u0430\u043d\u043d\u044f \u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u043d\u044f",
"Zoom in": "\u0417\u0431\u0456\u043b\u044c\u0448\u0438\u0442\u0438",
"Zoom out": "\u0417\u043c\u0435\u043d\u0448\u0438\u0442\u0438",
"Crop": "\u041e\u0431\u0440\u0456\u0437\u0430\u0442\u0438",
"Resize": "\u0417\u043c\u0456\u043d\u0438\u0442\u0438 \u0440\u043e\u0437\u043c\u0456\u0440",
"Orientation": "\u041e\u0440\u0456\u0454\u043d\u0442\u0430\u0446\u0456\u044f",
"Brightness": "\u042f\u0441\u043a\u0440\u0430\u0432\u0456\u0441\u0442\u044c",
"Sharpen": "\u0427\u0456\u0442\u043a\u0456\u0441\u0442\u044c",
"Contrast": "\u041a\u043e\u043d\u0442\u0440\u0430\u0441\u0442",
"Color levels": "\u0420\u0456\u0432\u043d\u0456 \u043a\u043e\u043b\u044c\u043e\u0440\u0456\u0432",
"Gamma": "\u0413\u0430\u043c\u043c\u0430",
"Invert": "\u0406\u043d\u0432\u0435\u0440\u0441\u0456\u044f",
"Apply": "\u0417\u0430\u0441\u0442\u043e\u0441\u0443\u0432\u0430\u0442\u0438",
"Back": "\u041f\u043e\u0432\u0435\u0440\u043d\u0443\u0442\u0438\u0441\u044f",
"Insert date\/time": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u0434\u0430\u0442\u0443\/\u0447\u0430\u0441",
"Date\/time": "\u0414\u0430\u0442\u0430\/\u0447\u0430\u0441",
"Insert link": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u043f\u043e\u0441\u0438\u043b\u0430\u043d\u043d\u044f",
"Insert\/edit link": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438\/\u0440\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438 \u043f\u043e\u0441\u0438\u043b\u0430\u043d\u043d\u044f",
"Text to display": "\u0422\u0435\u043a\u0441\u0442 \u0434\u043b\u044f \u0432\u0456\u0434\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u043d\u044f",
"Url": "URL",
"Target": "\u041c\u0435\u0442\u0430",
"None": "\u041d\u0456",
"New window": "\u041d\u043e\u0432\u0435 \u0432\u0456\u043a\u043d\u043e",
"Remove link": "\u0412\u0438\u0434\u0430\u043b\u0438\u0442\u0438 \u043f\u043e\u0441\u0438\u043b\u0430\u043d\u043d\u044f",
"Anchors": "\u042f\u043a\u043e\u0440\u044f",
"Link": "\u041f\u043e\u0441\u0438\u043b\u0430\u043d\u043d\u044f",
"Paste or type a link": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u0430\u0431\u043e \u043d\u0430\u043f\u0438\u0441\u0430\u0442\u0438 \u043f\u043e\u0441\u0438\u043b\u0430\u043d\u043d\u044f",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\u0421\u0445\u043e\u0436\u0435, \u0449\u043e \u0432\u0438 \u0432\u0432\u0435\u043b\u0438 \u0430\u0434\u0440\u0435\u0441\u0443 \u0435\u043b\u0435\u043a\u0442\u0440\u043e\u043d\u043d\u043e\u0457 \u043f\u043e\u0448\u0442\u0438. \u0412\u0438 \u0431\u0430\u0436\u0430\u0454\u0442\u0435 \u0434\u043e\u0434\u0430\u0442\u0438 \u043f\u0440\u0435\u0444\u0456\u043a\u0441 mailto:?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\u0421\u0445\u043e\u0436\u0435, \u0449\u043e \u0432\u0438 \u0432\u0432\u0435\u043b\u0438 \u0437\u043e\u0432\u043d\u0456\u0448\u043d\u0454 \u043f\u043e\u0441\u0438\u043b\u0430\u043d\u043d\u044f. \u0412\u0438 \u0431\u0430\u0436\u0430\u0454\u0442\u0435 \u0434\u043e\u0434\u0430\u0442\u0438 \u043f\u0440\u0435\u0444\u0456\u043a\u0441 http:\/\/?",
"Link list": "\u0421\u043f\u0438\u0441\u043e\u043a \u043f\u043e\u0441\u0438\u043b\u0430\u043d\u044c",
"Insert video": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u0432\u0456\u0434\u0435\u043e",
"Insert\/edit video": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438\/\u0440\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438 \u0432\u0456\u0434\u0435\u043e",
"Insert\/edit media": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438\/\u0440\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438 \u043c\u0435\u0434\u0456\u0430\u0434\u0430\u043d\u0456",
"Alternative source": "\u0410\u043b\u044c\u0442\u0435\u0440\u043d\u0430\u0442\u0438\u0432\u043d\u0435 \u0434\u0436\u0435\u0440\u0435\u043b\u043e",
"Poster": "\u041f\u043b\u0430\u043a\u0430\u0442",
"Paste your embed code below:": "\u0412\u0441\u0442\u0430\u0432\u0442\u0435 \u0432\u0430\u0448 \u043a\u043e\u0434 \u043d\u0438\u0436\u0447\u0435:",
"Embed": "\u0412\u043f\u0440\u043e\u0432\u0430\u0434\u0438\u0442\u0438",
"Media": "\u041c\u0435\u0434\u0456\u0430\u0434\u0430\u043d\u0456",
"Nonbreaking space": "\u041d\u0435\u0440\u043e\u0437\u0440\u0438\u0432\u043d\u0438\u0439 \u043f\u0440\u043e\u043f\u0443\u0441\u043a",
"Page break": "\u0420\u043e\u0437\u0440\u0438\u0432 \u0441\u0442\u043e\u0440\u0456\u043d\u043a\u0438",
"Paste as text": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u044f\u043a \u0442\u0435\u043a\u0441\u0442",
"Preview": "\u041f\u043e\u043f\u0435\u0440\u0435\u0434\u043d\u0456\u0439 \u043f\u0435\u0440\u0435\u0433\u043b\u044f\u0434",
"Print": "\u0414\u0440\u0443\u043a",
"Save": "\u0417\u0431\u0435\u0440\u0435\u0433\u0442\u0438",
"Find": "\u0417\u043d\u0430\u0439\u0442\u0438",
"Replace with": "\u0417\u0430\u043c\u0456\u043d\u0438\u0442\u0438 \u043d\u0430",
"Replace": "\u0417\u0430\u043c\u0456\u043d\u0438\u0442\u0438",
"Replace all": "\u0417\u0430\u043c\u0456\u043d\u0438\u0442\u0438 \u0432\u0441\u0435",
"Prev": "\u041f\u043e\u043f\u0435\u0440\u0435\u0434\u043d\u0456\u0439",
"Next": "\u041d\u0430\u0441\u0442\u0443\u043f\u043d\u0438\u0439",
"Find and replace": "\u0417\u043d\u0430\u0439\u0442\u0438 \u0456 \u0437\u0430\u043c\u0456\u043d\u0438\u0442\u0438",
"Could not find the specified string.": "\u041d\u0435 \u0432\u0434\u0430\u043b\u043e\u0441\u044f \u0437\u043d\u0430\u0439\u0442\u0438 \u0437\u0430\u0437\u043d\u0430\u0447\u0435\u043d\u0438\u0439 \u0440\u044f\u0434\u043e\u043a.",
"Match case": "\u0417 \u0443\u0440\u0430\u0445\u0443\u0432\u0430\u043d\u043d\u044f\u043c \u0440\u0435\u0433\u0456\u0441\u0442\u0440\u0443",
"Whole words": "\u0426\u0456\u043b\u0456 \u0441\u043b\u043e\u0432\u0430",
"Spellcheck": "\u041f\u0435\u0440\u0435\u0432\u0456\u0440\u043a\u0430 \u043e\u0440\u0444\u043e\u0433\u0440\u0430\u0444\u0456\u0457",
"Ignore": "\u0406\u0433\u043d\u043e\u0440\u0443\u0432\u0430\u0442\u0438",
"Ignore all": "\u0406\u0433\u043d\u043e\u0440\u0443\u0432\u0430\u0442\u0438 \u0432\u0441\u0435",
"Finish": "\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438",
"Add to Dictionary": "\u0414\u043e\u0434\u0430\u0442\u0438 \u0432 \u0441\u043b\u043e\u0432\u043d\u0438\u043a",
"Insert table": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u0442\u0430\u0431\u043b\u0438\u0446\u044e",
"Table properties": "\u0412\u043b\u0430\u0441\u0442\u0438\u0432\u043e\u0441\u0442\u0456 \u0442\u0430\u0431\u043b\u0438\u0446\u0456",
"Delete table": "\u0412\u0438\u0434\u0430\u043b\u0438\u0442\u0438 \u0442\u0430\u0431\u043b\u0438\u0446\u044e",
"Cell": "\u041a\u043e\u043c\u0456\u0440\u043a\u0430",
"Row": "\u0420\u044f\u0434\u043e\u043a",
"Column": "\u0421\u0442\u043e\u0432\u043f\u0435\u0446\u044c",
"Cell properties": "\u0412\u043b\u0430\u0441\u0442\u0438\u0432\u043e\u0441\u0442\u0456 \u043a\u043e\u043c\u0456\u0440\u043a\u0438",
"Merge cells": "\u041e\u0431'\u0454\u0434\u043d\u0430\u0442\u0438 \u043a\u043e\u043c\u0456\u0440\u043a\u0438",
"Split cell": "\u0420\u043e\u0437\u0431\u0438\u0442\u0438 \u043a\u043e\u043c\u0456\u0440\u043a\u0443",
"Insert row before": "\u0412\u0441\u0442\u0430\u0432\u0442\u0435 \u0440\u044f\u0434\u043e\u043a \u043f\u0435\u0440\u0435\u0434",
"Insert row after": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u0440\u044f\u0434\u043e\u043a \u043f\u0456\u0441\u043b\u044f",
"Delete row": "\u0412\u0438\u0434\u0430\u043b\u0438\u0442\u0438 \u0440\u044f\u0434\u043e\u043a",
"Row properties": "\u0412\u043b\u0430\u0441\u0442\u0438\u0432\u043e\u0441\u0442\u0456 \u0440\u044f\u0434\u043a\u0430",
"Cut row": "\u0412\u0438\u0440\u0456\u0437\u0430\u0442\u0438 \u0440\u044f\u0434\u043e\u043a",
"Copy row": "\u041a\u043e\u043f\u0456\u044e\u0432\u0430\u0442\u0438 \u0440\u044f\u0434\u043e\u043a",
"Paste row before": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u0440\u044f\u0434\u043e\u043a \u043f\u0435\u0440\u0435\u0434",
"Paste row after": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u0440\u044f\u0434\u043e\u043a \u043f\u0456\u0441\u043b\u044f",
"Insert column before": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u0441\u0442\u043e\u0432\u043f\u0435\u0446\u044c \u043f\u0435\u0440\u0435\u0434",
"Insert column after": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u0441\u0442\u043e\u0432\u043f\u0435\u0446\u044c \u043f\u0456\u0441\u043b\u044f",
"Delete column": "\u0412\u0438\u0434\u0430\u043b\u0438\u0442\u0438 \u0441\u0442\u043e\u0432\u043f\u0435\u0446\u044c",
"Cols": "\u0421\u0442\u043e\u0432\u043f\u0446\u0456",
"Rows": "\u0420\u044f\u0434\u043a\u0438",
"Width": "\u0428\u0438\u0440\u0438\u043d\u0430",
"Height": "\u0412\u0438\u0441\u043e\u0442\u0430",
"Cell spacing": "\u0406\u043d\u0442\u0435\u0440\u0432\u0430\u043b \u043c\u0456\u0436 \u043a\u043e\u043c\u0456\u0440\u043a\u0430\u043c\u0438",
"Cell padding": "\u0417\u0430\u043f\u043e\u0432\u043d\u0435\u043d\u043d\u044f \u043a\u043e\u043c\u0456\u0440\u043e\u043a",
"Caption": "\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a",
"Left": "\u041b\u0456\u0432\u043e\u0440\u0443\u0447",
"Center": "\u0426\u0435\u043d\u0442\u0440",
"Right": "\u041f\u0440\u0430\u0432\u043e\u0440\u0443\u0447",
"Cell type": "\u0422\u0438\u043f \u043a\u043e\u043c\u0456\u0440\u043a\u0438",
"Scope": "\u0423 \u043c\u0435\u0436\u0430\u0445",
"Alignment": "\u0412\u0438\u0440\u0456\u0432\u043d\u044e\u0432\u0430\u043d\u043d\u044f",
"H Align": "\u0413\u043e\u0440\u0438\u0437\u043e\u043d\u0442\u0430\u043b\u044c\u043d\u0435 \u0432\u0438\u0440\u0456\u0432\u043d\u044e\u0432\u0430\u043d\u043d\u044f",
"V Align": "\u0412\u0435\u0440\u0442\u0438\u043a\u0430\u043b\u044c\u043d\u0435 \u0432\u0438\u0440\u0456\u0432\u043d\u044e\u0432\u0430\u043d\u043d\u044f",
"Top": "\u041f\u043e \u0432\u0435\u0440\u0445\u043d\u044c\u043e\u043c\u0443 \u043a\u0440\u0430\u044e",
"Middle": "\u041f\u043e \u0446\u0435\u043d\u0442\u0440\u0443",
"Bottom": "\u041f\u043e \u043d\u0438\u0436\u043d\u044c\u043e\u043c\u0443 \u043a\u0440\u0430\u044e",
"Header cell": "\u041a\u043e\u043c\u0456\u0440\u043a\u0430 \u0437\u0430\u0433\u043e\u043b\u043e\u0432\u043a\u0443",
"Row group": "\u0413\u0440\u0443\u043f\u0430 \u0440\u044f\u0434\u043a\u0456\u0432",
"Column group": "\u0413\u0440\u0443\u043f\u0430 \u0441\u0442\u043e\u0432\u043f\u0446\u0456\u0432",
"Row type": "\u0422\u0438\u043f \u0440\u044f\u0434\u043a\u0430",
"Header": "\u0412\u0435\u0440\u0445\u043d\u0456\u0439 \u043a\u043e\u043b\u043e\u043d\u0442\u0438\u0442\u0443\u043b",
"Body": "\u0422\u0456\u043b\u043e",
"Footer": "\u041d\u0438\u0436\u043d\u0456\u0439 \u043a\u043e\u043b\u043e\u043d\u0442\u0438\u0442\u0443\u043b",
"Border color": "\u041a\u043e\u043b\u0456\u0440 \u043c\u0435\u0436\u0456",
"Insert template": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438 \u0448\u0430\u0431\u043b\u043e\u043d",
"Templates": "\u0428\u0430\u0431\u043b\u043e\u043d\u0438",
"Template": "\u0428\u0430\u0431\u043b\u043e\u043d",
"Text color": "\u041a\u043e\u043b\u0456\u0440 \u0442\u0435\u043a\u0441\u0442\u0443",
"Background color": "\u041a\u043e\u043b\u0456\u0440 \u0444\u043e\u043d\u0443",
"Custom...": "\u0406\u043d\u0448\u0438\u0439...",
"Custom color": "\u041a\u043e\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0446\u044c\u043a\u0438\u0439 \u043a\u043e\u043b\u0456\u0440",
"No color": "\u0411\u0435\u0437 \u043a\u043e\u043b\u044c\u043e\u0440\u0443",
"Table of Contents": "\u0417\u043c\u0456\u0441\u0442",
"Show blocks": "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u0438 \u0431\u043b\u043e\u043a\u0438",
"Show invisible characters": "\u041f\u043e\u043a\u0430\u0437\u0430\u0442\u0438 \u043d\u0435\u0432\u0438\u0434\u0438\u043c\u0456 \u0441\u0438\u043c\u0432\u043e\u043b\u0438",
"Words: {0}": "\u0421\u043b\u043e\u0432\u0430: {0}",
"{0} words": "{0} \u0441\u043b\u0456\u0432",
"File": "\u0424\u0430\u0439\u043b",
"Edit": "\u041f\u0440\u0430\u0432\u043a\u0430",
"Insert": "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438",
"View": "\u0412\u0438\u0434",
"Format": "\u0424\u043e\u0440\u043c\u0430\u0442",
"Table": "\u0422\u0430\u0431\u043b\u0438\u0446\u044f",
"Tools": "\u0406\u043d\u0441\u0442\u0440\u0443\u043c\u0435\u043d\u0442\u0438",
"Powered by {0}": "\u0417\u0430 \u043f\u0456\u0434\u0442\u0440\u0438\u043c\u0438\u043a\u0438 {0}",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\u041e\u0431\u043b\u0430\u0441\u0442\u044c Rich \u0442\u0435\u043a\u0441\u0442\u0443. \u041d\u0430\u0442\u0438\u0441\u043d\u0456\u0442\u044c ALT-F9 - \u043c\u0435\u043d\u044e. \u041d\u0430\u0442\u0438\u0441\u043d\u0456\u0442\u044c ALT-F10 - \u043f\u0430\u043d\u0435\u043b\u044c \u0456\u043d\u0441\u0442\u0440\u0443\u043c\u0435\u043d\u0442\u0456\u0432. \u041d\u0430\u0442\u0438\u0441\u043d\u0456\u0442\u044c ALT-0 - \u0434\u043e\u0432\u0456\u0434\u043a\u0430"
});
tinymce.addI18n('en_CA',{
"Redo": "Redo",
"Undo": "Undo",
"Cut": "Cut",
"Copy": "Copy",
"Paste": "Paste",
"Select all": "Select all",
"New document": "New document",
"Ok": "Ok",
"Cancel": "Cancel",
"Visual aids": "Visual aids",
"Bold": "Bold",
"Italic": "Italic",
"Underline": "Underline",
"Strikethrough": "Strikethrough",
"Superscript": "Superscript",
"Subscript": "Subscript",
"Clear formatting": "Clear formatting",
"Align left": "Align left",
"Align center": "Align center",
"Align right": "Align right",
"Justify": "Justify",
"Bullet list": "Bullet list",
"Numbered list": "Numbered list",
"Decrease indent": "Decrease indent",
"Increase indent": "Increase indent",
"Close": "Close",
"Formats": "Formats",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.",
"Headers": "Headers",
"Header 1": "Header 1",
"Header 2": "Header 2",
"Header 3": "Header 3",
"Header 4": "Header 4",
"Header 5": "Header 5",
"Header 6": "Header 6",
"Headings": "Headings",
"Heading 1": "Heading 1",
"Heading 2": "Heading 2",
"Heading 3": "Heading 3",
"Heading 4": "Heading 4",
"Heading 5": "Heading 5",
"Heading 6": "Heading 6",
"Div": "Div",
"Pre": "Pre",
"Code": "Code",
"Paragraph": "Paragraph",
"Blockquote": "Blockquote",
"Inline": "Inline",
"Blocks": "Blocks",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.",
"Font Family": "Font Family",
"Font Sizes": "Font Sizes",
"Class": "Class",
"Browse for an image": "Browse for an image",
"OR": "OR",
"Drop an image here": "Drop an image here",
"Upload": "Upload",
"Block": "Blocks",
"Align": "Align",
"Default": "Default",
"Circle": "Circle",
"Disc": "Disc",
"Square": "Square",
"Lower Alpha": "Lower Alpha",
"Lower Greek": "Lower Greek",
"Lower Roman": "Lower Roman",
"Upper Alpha": "Upper Alpha",
"Upper Roman": "Upper Roman",
"Anchor": "Anchor",
"Name": "Name",
"Id": "ID",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "ID should start with a letter, followed only by letters, numbers, dashes, dots, colons, or underscores.",
"You have unsaved changes are you sure you want to navigate away?": "You have unsaved changes are you sure you want to navigate away?",
"Restore last draft": "Restore last draft",
"Special character": "Special character",
"Source code": "Source code",
"Insert\/Edit code sample": "Insert\/Edit code sample",
"Language": "Language",
"Code sample": "Code sample",
"Color": "Colour",
"R": "R",
"G": "G",
"B": "B",
"Left to right": "Left to right",
"Right to left": "Right to left",
"Emoticons": "Emoticons",
"Document properties": "Document properties",
"Title": "Title",
"Keywords": "Keywords",
"Description": "Description",
"Robots": "Robots",
"Author": "Author",
"Encoding": "Encoding",
"Fullscreen": "Fullscreen",
"Action": "Action",
"Shortcut": "Shortcut",
"Help": "Help",
"Address": "Address",
"Focus to menubar": "Focus to menubar",
"Focus to toolbar": "Focus to toolbar",
"Focus to element path": "Focus to element path",
"Focus to contextual toolbar": "Focus to contextual toolbar",
"Insert link (if link plugin activated)": "Insert link (if link plugin activated)",
"Save (if save plugin activated)": "Save (if save plugin activated)",
"Find (if searchreplace plugin activated)": "Find (if searchreplace plugin activated)",
"Plugins installed ({0}):": "Plugins installed ({0}):",
"Premium plugins:": "Premium plugins:",
"Learn more...": "Learn more...",
"You are using {0}": "You are using {0}",
"Plugins": "Plugins",
"Handy Shortcuts": "Handy Shortcuts",
"Horizontal line": "Horizontal line",
"Insert\/edit image": "Insert\/edit image",
"Image description": "Image description",
"Source": "Source",
"Dimensions": "Dimensions",
"Constrain proportions": "Constrain proportions",
"General": "General",
"Advanced": "Advanced",
"Style": "Style",
"Vertical space": "Vertical space",
"Horizontal space": "Horizontal space",
"Border": "Border",
"Insert image": "Insert image",
"Image": "Image",
"Image list": "Image list",
"Rotate counterclockwise": "Rotate counterclockwise",
"Rotate clockwise": "Rotate clockwise",
"Flip vertically": "Flip vertically",
"Flip horizontally": "Flip horizontally",
"Edit image": "Edit image",
"Image options": "Image options",
"Zoom in": "Zoom in",
"Zoom out": "Zoom out",
"Crop": "Crop",
"Resize": "Resize",
"Orientation": "Orientation",
"Brightness": "Brightness",
"Sharpen": "Sharpen",
"Contrast": "Contrast",
"Color levels": "Colour levels",
"Gamma": "Gamma",
"Invert": "Invert",
"Apply": "Apply",
"Back": "Back",
"Insert date\/time": "Insert date\/time",
"Date\/time": "Date\/time",
"Insert link": "Insert link",
"Insert\/edit link": "Insert\/edit link",
"Text to display": "Text to display",
"Url": "Url",
"Target": "Target",
"None": "None",
"New window": "New window",
"Remove link": "Remove link",
"Anchors": "Anchors",
"Link": "Link",
"Paste or type a link": "Paste or type a link",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?",
"Link list": "Link list",
"Insert video": "Insert video",
"Insert\/edit video": "Insert\/edit video",
"Insert\/edit media": "Insert\/edit media",
"Alternative source": "Alternative source",
"Poster": "Poster",
"Paste your embed code below:": "Paste your embed code below:",
"Embed": "Embed",
"Media": "Media",
"Nonbreaking space": "Nonbreaking space",
"Page break": "Page break",
"Paste as text": "Paste as text",
"Preview": "Preview",
"Print": "Print",
"Save": "Save",
"Find": "Find",
"Replace with": "Replace with",
"Replace": "Replace",
"Replace all": "Replace all",
"Prev": "Prev",
"Next": "Next",
"Find and replace": "Find and replace",
"Could not find the specified string.": "Could not find the specified string.",
"Match case": "Match case",
"Whole words": "Whole words",
"Spellcheck": "Spellcheck",
"Ignore": "Ignore",
"Ignore all": "Ignore all",
"Finish": "Finish",
"Add to Dictionary": "Add to Dictionary",
"Insert table": "Insert table",
"Table properties": "Table properties",
"Delete table": "Delete table",
"Cell": "Cell",
"Row": "Row",
"Column": "Column",
"Cell properties": "Cell properties",
"Merge cells": "Merge cells",
"Split cell": "Split cell",
"Insert row before": "Insert row before",
"Insert row after": "Insert row after",
"Delete row": "Delete row",
"Row properties": "Row properties",
"Cut row": "Cut row",
"Copy row": "Copy row",
"Paste row before": "Paste row before",
"Paste row after": "Paste row after",
"Insert column before": "Insert column before",
"Insert column after": "Insert column after",
"Delete column": "Delete column",
"Cols": "Cols",
"Rows": "Rows",
"Width": "Width",
"Height": "Height",
"Cell spacing": "Cell spacing",
"Cell padding": "Cell padding",
"Caption": "Caption",
"Left": "Left",
"Center": "Center",
"Right": "Right",
"Cell type": "Cell type",
"Scope": "Scope",
"Alignment": "Alignment",
"H Align": "H Align",
"V Align": "V Align",
"Top": "Top",
"Middle": "Middle",
"Bottom": "Bottom",
"Header cell": "Header cell",
"Row group": "Row group",
"Column group": "Column group",
"Row type": "Row type",
"Header": "Header",
"Body": "Body",
"Footer": "Footer",
"Border color": "Border colour",
"Insert template": "Insert template",
"Templates": "Templates",
"Template": "Template",
"Text color": "Text colour",
"Background color": "Background colour",
"Custom...": "Custom...",
"Custom color": "Custom colour",
"No color": "No colour",
"Table of Contents": "Table of Contents",
"Show blocks": "Show blocks",
"Show invisible characters": "Show invisible characters",
"Words: {0}": "Words: {0}",
"{0} words": "{0} words",
"File": "File",
"Edit": "Edit",
"Insert": "Insert",
"View": "View",
"Format": "Format",
"Table": "Table",
"Tools": "Tools",
"Powered by {0}": "Powered by {0}",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help"
});
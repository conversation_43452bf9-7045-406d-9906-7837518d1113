!function(p){"use strict";var I=function(){},v=function(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))}},A=function(n){return function(){return n}},h=function(n){return n};function l(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}var n,e,t,r,o,i,u,x=function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return!t.apply(null,n)}},c=function(n){return function(){throw new Error(n)}},a=function(n){return n()},s=A(!1),f=A(!0),d=function(e){return function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"===e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"===e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e}(n)===e}},y=d("string"),m=d("object"),g=d("array"),b=d("boolean"),w=d("function"),T=d("number"),S=Object.prototype.hasOwnProperty,O=function(u){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<n.length;r++){var o=n[r];for(var i in o)S.call(o,i)&&(t[i]=u(t[i],o[i]))}return t}},k=O(function(n,e){return m(n)&&m(e)?k(n,e):e}),C=O(function(n,e){return e}),E=function(){return D},D=(n=function(n){return n.isNone()},r={fold:function(n,e){return n()},is:s,isSome:s,isNone:f,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:A(null),getOrUndefined:A(undefined),or:t,orThunk:e,map:E,each:I,bind:E,exists:s,forall:f,filter:E,equals:n,equals_:n,toArray:function(){return[]},toString:A("none()")},Object.freeze&&Object.freeze(r),r),M=function(t){var n=A(t),e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:f,isNone:s,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return M(n(t))},each:function(n){n(t)},bind:r,exists:r,forall:r,filter:function(n){return n(t)?o:D},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(s,function(n){return e(t,n)})}};return o},F={some:M,none:E,from:function(n){return null===n||n===undefined?D:M(n)}},R=Object.keys,B=function(n,e){for(var t=R(n),r=0,o=t.length;r<o;r++){var i=t[r];e(n[i],i)}},V=function(n,t){return N(n,function(n,e){return{k:e,v:t(n,e)}})},N=function(n,r){var o={};return B(n,function(n,e){var t=r(n,e);o[t.k]=t.v}),o},_=function(n,t){var r=[];return B(n,function(n,e){r.push(t(n,e))}),r},j=A("touchstart"),H=A("touchmove"),z=A("touchend"),L=A("mousedown"),P=A("mousemove"),$=A("mouseup"),W=A("mouseover"),U=A("keydown"),G=A("input"),q=A("change"),Y=A("click"),K=A("transitionend"),X=A("selectstart"),J=function(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}},Q=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return nn(r(1),r(2))},Z=function(){return nn(0,0)},nn=function(n,e){return{major:n,minor:e}},en={nu:nn,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?Z():Q(n,t)},unknown:Z},tn="Firefox",rn=function(n,e){return function(){return e===n}},on=function(n){var e=n.current;return{current:e,version:n.version,isEdge:rn("Edge",e),isChrome:rn("Chrome",e),isIE:rn("IE",e),isOpera:rn("Opera",e),isFirefox:rn(tn,e),isSafari:rn("Safari",e)}},un={unknown:function(){return on({current:undefined,version:en.unknown()})},nu:on,edge:A("Edge"),chrome:A("Chrome"),ie:A("IE"),opera:A("Opera"),firefox:A(tn),safari:A("Safari")},cn="Windows",an="Android",sn="Solaris",fn="FreeBSD",ln=function(n,e){return function(){return e===n}},dn=function(n){var e=n.current;return{current:e,version:n.version,isWindows:ln(cn,e),isiOS:ln("iOS",e),isAndroid:ln(an,e),isOSX:ln("OSX",e),isLinux:ln("Linux",e),isSolaris:ln(sn,e),isFreeBSD:ln(fn,e)}},mn={unknown:function(){return dn({current:undefined,version:en.unknown()})},nu:dn,windows:A(cn),ios:A("iOS"),android:A(an),linux:A("Linux"),osx:A("OSX"),solaris:A(sn),freebsd:A(fn)},gn=Array.prototype.slice,pn=Array.prototype.indexOf,vn=Array.prototype.push,hn=function(n,e){return t=n,r=e,-1<pn.call(t,r);var t,r},yn=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var i=n[o];r[o]=e(i,o)}return r},bn=function(n,e){for(var t=0,r=n.length;t<r;t++)e(n[t],t)},wn=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var i=n[r];e(i,r)&&t.push(i)}return t},xn=function(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--)e(n[t],t)}(n,function(n){t=e(t,n)}),t},Tn=function(n,e,t){return bn(n,function(n){t=e(t,n)}),t},Sn=function(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];if(e(o,t))return F.some(o)}return F.none()},On=function(n,e){for(var t=0,r=n.length;t<r;t++)if(e(n[t],t))return F.some(t);return F.none()},kn=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!g(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);vn.apply(e,n[t])}return e},Cn=function(n,e){var t=yn(n,e);return kn(t)},En=function(n,e){for(var t=0,r=n.length;t<r;++t)if(!0!==e(n[t],t))return!1;return!0},Dn=function(n){var e=gn.call(n,0);return e.reverse(),e},In=function(n){return[n]},An=(w(Array.from)&&Array.from,function(n,e){var t=String(e).toLowerCase();return Sn(n,function(n){return n.search(t)})}),Mn=function(n,t){return An(n,t).map(function(n){var e=en.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Fn=function(n,t){return An(n,t).map(function(n){var e=en.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Rn=function(n,e){return-1!==n.indexOf(e)},Bn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Vn=function(e){return function(n){return Rn(n,e)}},Nn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Rn(n,"edge/")&&Rn(n,"chrome")&&Rn(n,"safari")&&Rn(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Bn],search:function(n){return Rn(n,"chrome")&&!Rn(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Rn(n,"msie")||Rn(n,"trident")}},{name:"Opera",versionRegexes:[Bn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Vn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Vn("firefox")},{name:"Safari",versionRegexes:[Bn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Rn(n,"safari")||Rn(n,"mobile/"))&&Rn(n,"applewebkit")}}],_n=[{name:"Windows",search:Vn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Rn(n,"iphone")||Rn(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Vn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Vn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Vn("linux"),versionRegexes:[]},{name:"Solaris",search:Vn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Vn("freebsd"),versionRegexes:[]}],jn={browsers:A(Nn),oses:A(_n)},Hn=function(n){var e,t,r,o,i,u,c,a,s,f,l,d=jn.browsers(),m=jn.oses(),g=Mn(d,n).fold(un.unknown,un.nu),p=Fn(m,n).fold(mn.unknown,mn.nu);return{browser:g,os:p,deviceType:(t=g,r=n,o=(e=p).isiOS()&&!0===/ipad/i.test(r),i=e.isiOS()&&!o,u=e.isAndroid()&&3===e.version.major,c=e.isAndroid()&&4===e.version.major,a=o||u||c&&!0===/mobile/i.test(r),s=e.isiOS()||e.isAndroid(),f=s&&!a,l=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),{isiPad:A(o),isiPhone:A(i),isTablet:A(a),isPhone:A(f),isTouch:A(s),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:A(l)})}},zn={detect:J(function(){var n=p.navigator.userAgent;return Hn(n)})},Ln={tap:A("alloy.tap")},Pn=A("alloy.focus"),$n=A("alloy.blur.post"),Wn=A("alloy.receive"),Un=A("alloy.execute"),Gn=A("alloy.focus.item"),qn=Ln.tap,Yn=zn.detect().deviceType.isTouch()?Ln.tap:Y,Kn=A("alloy.longpress"),Xn=A("alloy.system.init"),Jn=A("alloy.system.scroll"),Qn=A("alloy.system.attached"),Zn=A("alloy.system.detached"),ne=function(n,e){oe(n,n.element(),e,{})},ee=function(n,e,t){oe(n,n.element(),e,t)},te=function(n){ne(n,Un())},re=function(n,e,t){oe(n,e,t,{})},oe=function(n,e,t,r){var o=k({target:e},r);n.getSystem().triggerEvent(t,e,V(o,A))},ie=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:A(n)}},ue={fromHtml:function(n,e){var t=(e||p.document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw p.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return ie(t.childNodes[0])},fromTag:function(n,e){var t=(e||p.document).createElement(n);return ie(t)},fromText:function(n,e){var t=(e||p.document).createTextNode(n);return ie(t)},fromDom:ie,fromPoint:function(n,e,t){var r=n.dom();return F.from(r.elementFromPoint(e,t)).map(ie)}},ce=(p.Node.ATTRIBUTE_NODE,p.Node.CDATA_SECTION_NODE,p.Node.COMMENT_NODE,p.Node.DOCUMENT_NODE),ae=(p.Node.DOCUMENT_TYPE_NODE,p.Node.DOCUMENT_FRAGMENT_NODE,p.Node.ELEMENT_NODE),se=p.Node.TEXT_NODE,fe=(p.Node.PROCESSING_INSTRUCTION_NODE,p.Node.ENTITY_REFERENCE_NODE,p.Node.ENTITY_NODE,p.Node.NOTATION_NODE,"undefined"!=typeof p.window?p.window:Function("return this;")()),le=function(n,e){return function(n,e){for(var t=e!==undefined&&null!==e?e:fe,r=0;r<n.length&&t!==undefined&&null!==t;++r)t=t[n[r]];return t}(n.split("."),e)},de=function(n,e){var t=le(n,e);if(t===undefined||null===t)throw new Error(n+" not available on this browser");return t},me=function(n){return n.dom().nodeName.toLowerCase()},ge=function(e){return function(n){return n.dom().nodeType===e}},pe=ge(ae),ve=ge(se),he=function(n){var e=ve(n)?n.dom().parentNode:n.dom();return e!==undefined&&null!==e&&e.ownerDocument.body.contains(e)},ye=J(function(){return be(ue.fromDom(p.document))}),be=function(n){var e=n.dom().body;if(null===e||e===undefined)throw new Error("Body is not available yet");return ue.fromDom(e)},we=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var r={};return bn(e,function(n,e){r[n]=A(t[e])}),r}},xe=function(n){return n.slice(0).sort()},Te=function(n,e){throw new Error("All required keys ("+xe(n).join(", ")+") were not specified. Specified keys were: "+xe(e).join(", ")+".")},Se=function(n){throw new Error("Unsupported keys for object: "+xe(n).join(", "))},Oe=function(e,n){if(!g(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");bn(n,function(n){if(!y(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})},ke=function(n){var t=xe(n);Sn(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})},Ce=function(o,i){var u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return Oe("required",o),Oe("optional",i),ke(u),function(e){var t=R(e);En(o,function(n){return hn(t,n)})||Te(o,t);var n=wn(t,function(n){return!hn(u,n)});0<n.length&&Se(n);var r={};return bn(o,function(n){r[n]=A(e[n])}),bn(i,function(n){r[n]=A(Object.prototype.hasOwnProperty.call(e,n)?F.some(e[n]):F.none())}),r}},Ee=ae,De=ce,Ie=function(n,e){var t=n.dom();if(t.nodeType!==Ee)return!1;var r=t;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},Ae=function(n){return n.nodeType!==Ee&&n.nodeType!==De||0===n.childElementCount},Me=function(n,e){var t=e===undefined?p.document:e.dom();return Ae(t)?[]:yn(t.querySelectorAll(n),ue.fromDom)},Fe=function(n,e){var t=e===undefined?p.document:e.dom();return Ae(t)?F.none():F.from(t.querySelector(n)).map(ue.fromDom)},Re=function(n,e){return n.dom()===e.dom()},Be=(zn.detect().browser.isIE(),function(n){return ue.fromDom(n.dom().ownerDocument)}),Ve=function(n){return ue.fromDom(n.dom().ownerDocument.defaultView)},Ne=function(n){return F.from(n.dom().parentNode).map(ue.fromDom)},_e=function(n){return yn(n.dom().childNodes,ue.fromDom)},je=function(n){return e=0,t=n.dom().childNodes,F.from(t[e]).map(ue.fromDom);var e,t},He=(we("element","offset"),function(e,t){je(e).fold(function(){ze(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})}),ze=function(n,e){n.dom().appendChild(e.dom())},Le=function(e,n){bn(n,function(n){ze(e,n)})},Pe=function(n){n.dom().textContent="",bn(_e(n),function(n){$e(n)})},$e=function(n){var e=n.dom();null!==e.parentNode&&e.parentNode.removeChild(e)},We=function(n){ne(n,Zn());var e=n.components();bn(e,We)},Ue=function(n){var e=n.components();bn(e,Ue),ne(n,Qn())},Ge=function(n,e){qe(n,e,ze)},qe=function(n,e,t){n.getSystem().addToWorld(e),t(n.element(),e.element()),he(n.element())&&Ue(e),n.syncComponents()},Ye=function(n){We(n),$e(n.element()),n.getSystem().removeFromWorld(n)},Ke=function(e){var n=Ne(e.element()).bind(function(n){return e.getSystem().getByDom(n).fold(F.none,F.some)});Ye(e),n.each(function(n){n.syncComponents()})},Xe=function(t){return{is:function(n){return t===n},isValue:f,isError:s,getOr:A(t),getOrThunk:A(t),getOrDie:A(t),or:function(n){return Xe(t)},orThunk:function(n){return Xe(t)},fold:function(n,e){return e(t)},map:function(n){return Xe(n(t))},mapError:function(n){return Xe(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOption:function(){return F.some(t)}}},Je=function(t){return{is:s,isValue:s,isError:f,getOr:h,getOrThunk:function(n){return n()},getOrDie:function(){return c(String(t))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return Je(t)},mapError:function(n){return Je(n(t))},each:I,bind:function(n){return Je(t)},exists:s,forall:f,toOption:F.none}},Qe={value:Xe,error:Je,fromOption:function(n,e){return n.fold(function(){return Je(e)},Xe)}},Ze=function(u){if(!g(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return bn(u,function(n,r){var e=R(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],i=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!g(i))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=R(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!En(c,function(n){return hn(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){p.console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},nt=Ze([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),et=function(n){return nt.defaultedThunk(A(n))},tt=nt.strict,rt=nt.asOption,ot=nt.defaultedThunk,it=nt.mergeWithThunk,ut=(Ze([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){var e=[],t=[];return bn(n,function(n){n.fold(function(n){e.push(n)},function(n){t.push(n)})}),{errors:e,values:t}}),ct=function(n){return v(Qe.error,kn)(n)},at=function(n,e){var t,r,o=ut(n);return 0<o.errors.length?ct(o.errors):(t=o.values,r=e,Qe.value(k.apply(undefined,[r].concat(t))))},st=function(n){var e=ut(n);return 0<e.errors.length?ct(e.errors):Qe.value(e.values)},ft=function(e){return function(n){return n.hasOwnProperty(e)?F.from(n[e]):F.none()}},lt=function(n,e){return ft(e)(n)},dt=function(n,e){var t={};return t[n]=e,t},mt=function(n,e){return t=n,r={},bn(e,function(n){t[n]!==undefined&&t.hasOwnProperty(n)&&(r[n]=t[n])}),r;var t,r},gt=function(n,e){return t=e,r={},B(n,function(n,e){hn(t,e)||(r[e]=n)}),r;var t,r},pt=function(n){return ft(n)},vt=function(n,e){return t=n,r=e,function(n){return ft(t)(n).getOr(r)};var t,r},ht=function(n,e){return lt(n,e)},yt=function(n,e){return dt(n,e)},bt=function(n){return e={},bn(n,function(n){e[n.key]=n.value}),e;var e},wt=function(n,e){return at(n,e)},xt=function(n,e){return r=e,(t=n).hasOwnProperty(r)&&t[r]!==undefined&&null!==t[r];var t,r},Tt=Ze([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),St=Ze([{field:["name","presence","type"]},{state:["name"]}]),Ot=function(){return de("JSON")},kt=function(n,e,t){return Ot().stringify(n,e,t)},Ct=function(n){return m(n)&&100<R(n).length?" removed due to size":kt(n,null,2)},Et=function(n,e){return Qe.error([{path:n,getErrorInfo:e}])},Dt=Ze([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),It=function(t,r,o){return lt(r,o).fold(function(){return n=o,e=r,Et(t,function(){return'Could not find valid *strict* value for "'+n+'" in '+Ct(e)});var n,e},Qe.value)},At=function(n,e,t){var r=lt(n,e).fold(function(){return t(n)},h);return Qe.value(r)},Mt=function(o,c,n,a){return n.fold(function(i,e,n,t){var r=function(n){return t.extract(o.concat([i]),a,n).map(function(n){return dt(e,a(n))})},u=function(n){return n.fold(function(){var n=dt(e,a(F.none()));return Qe.value(n)},function(n){return t.extract(o.concat([i]),a,n).map(function(n){return dt(e,a(F.some(n)))})})};return n.fold(function(){return It(o,c,i).bind(r)},function(n){return At(c,i,n).bind(r)},function(){return(n=c,e=i,Qe.value(lt(n,e))).bind(u);var n,e},function(n){return(e=c,t=i,r=n,o=lt(e,t).map(function(n){return!0===n?r(e):n}),Qe.value(o)).bind(u);var e,t,r,o},function(n){var e=n(c);return At(c,i,A({})).map(function(n){return k(e,n)}).bind(r)})},function(n,e){var t=e(c);return Qe.value(dt(n,a(t)))})},Ft=function(r){return{extract:function(t,n,e){return r(e,n).fold(function(n){return e=n,Et(t,function(){return e});var e},Qe.value)},toString:function(){return"val"},toDsl:function(){return Tt.itemOf(r)}}},Rt=function(n){var a=Bt(n),s=xn(n,function(e,n){return n.fold(function(n){return k(e,yt(n,!0))},A(e))},{});return{extract:function(n,e,t){var r,o,i,u=b(t)?[]:(o=R(r=t),wn(o,function(n){return xt(r,n)})),c=wn(u,function(n){return!xt(s,n)});return 0===c.length?a.extract(n,e,t):(i=c,Et(n,function(){return"There are unsupported fields: ["+i.join(", ")+"] specified"}))},toString:a.toString,toDsl:a.toDsl}},Bt=function(c){return{extract:function(n,e,t){return r=n,o=t,i=e,u=yn(c,function(n){return Mt(r,o,n,i)}),at(u,{});var r,o,i,u},toString:function(){return"obj{\n"+yn(c,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return Tt.objOf(yn(c,function(n){return n.fold(function(n,e,t,r){return St.field(n,t,r)},function(n,e){return St.state(n)})}))}}},Vt=function(t,i){var e=function(n,e){return(o=Ft(t),{extract:function(t,r,n){var e=yn(n,function(n,e){return o.extract(t.concat(["["+e+"]"]),r,n)});return st(e)},toString:function(){return"array("+o.toString()+")"},toDsl:function(){return Tt.arrOf(o)}}).extract(n,h,e);var o};return{extract:function(t,r,o){var n=R(o);return e(t,n).bind(function(n){var e=yn(n,function(n){return Dt.field(n,n,tt(),i)});return Bt(e).extract(t,r,o)})},toString:function(){return"setOf("+i.toString()+")"},toDsl:function(){return Tt.setOf(t,i)}}},Nt=A(Ft(Qe.value)),_t=Dt.state,jt=Dt.field,Ht=function(t,e,r,o,i){return ht(o,i).fold(function(){return n=o,e=i,Et(t,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+Ct(n)});var n,e},function(n){return Bt(n).extract(t.concat(["branch: "+i]),e,r)})},zt=function(o,i){return{extract:function(e,t,r){return ht(r,o).fold(function(){return n=o,Et(e,function(){return'Choice schema did not contain choice key: "'+n+'"'});var n},function(n){return Ht(e,t,r,i,n)})},toString:function(){return"chooseOn("+o+"). Possible values: "+R(i)},toDsl:function(){return Tt.choiceOf(o,i)}}},Lt=Ft(Qe.value),Pt=function(n,e,t,r){return e.extract([n],t,r).fold(function(n){return Qe.error({input:r,errors:n})},Qe.value)},$t=function(n,e,t){return Pt(n,e,A,t)},Wt=function(n){return n.fold(function(n){throw new Error(qt(n))},h)},Ut=function(n,e,t){return Wt(Pt(n,e,h,t))},Gt=function(n,e,t){return Wt($t(n,e,t))},qt=function(n){return"Errors: \n"+(e=n.errors,t=10<e.length?e.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):e,yn(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()}))+"\n\nInput object: "+Ct(n.input);var e,t},Yt=function(n,e){return zt(n,e)},Kt=A(Lt),Xt=(o=w,i="function",Ft(function(n){var e=typeof n;return o(n)?Qe.value(n):Qe.error("Expected type: "+i+" but got: "+e)})),Jt=function(n){return jt(n,n,tt(),Nt())},Qt=function(n,e){return jt(n,n,tt(),e)},Zt=function(n){return Qt(n,Xt)},nr=function(n,e){return jt(n,n,tt(),Bt(e))},er=function(n){return jt(n,n,rt(),Nt())},tr=function(n,e){return jt(n,n,rt(),Bt(e))},rr=function(n,e){return jt(n,n,rt(),Rt(e))},or=function(n,e){return jt(n,n,et(e),Nt())},ir=function(n,e,t){return jt(n,n,et(e),t)},ur=function(n,e){return _t(n,e)},cr=function(n){if(!xt(n,"can")&&!xt(n,"abort")&&!xt(n,"run"))throw new Error("EventHandler defined by: "+kt(n,null,2)+" does not have can, abort, or run!");return Ut("Extracting event.handler",Rt([or("can",A(!0)),or("abort",A(!1)),or("run",I)]),n)},ar=function(t){var e,r,o,i,n=(e=t,r=function(n){return n.can},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Tn(e,function(n,e){return n&&r(e).apply(undefined,t)},!0)}),u=(o=t,i=function(n){return n.abort},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Tn(o,function(n,e){return n||i(e).apply(undefined,t)},!1)});return cr({can:n,abort:u,run:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];bn(t,function(n){n.run.apply(undefined,e)})}})},sr=function(n){return bt(n)},fr=function(n,e){return{key:n,value:cr({abort:e})}},lr=function(n,e){return{key:n,value:cr({run:e})}},dr=function(n,e,t){return{key:n,value:cr({run:function(n){e.apply(undefined,[n].concat(t))}})}},mr=function(n){return function(r){return{key:n,value:cr({run:function(n,e){var t;t=e,Re(n.element(),t.event().target())&&r(n,e)}})}}},gr=function(n,e,t){var u,r,o=e.partUids()[t];return r=o,lr(u=n,function(n,i){n.getSystem().getByUid(r).each(function(n){var e,t,r,o;t=(e=n).element(),r=u,o=i,e.getSystem().triggerEvent(r,t,o.event())})})},pr=function(n){return lr(n,function(n,e){e.cut()})},vr=mr(Qn()),hr=mr(Zn()),yr=mr(Xn()),br=(u=Un(),function(n){return lr(u,n)}),wr=function(n){return yn(n,function(n){return r=e="/*",o=(t=n).length-e.length,""!==r&&(t.length<r.length||t.substr(o,o+r.length)!==r)?n:n.substring(0,n.length-"/*".length);var e,t,r,o})},xr=function(n,e){var t=n.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:wr(i)}},n},Tr=Ce(["tag"],["classes","attributes","styles","value","innerHtml","domChildren","defChildren"]),Sr=function(n){return{tag:n.tag(),classes:n.classes().getOr([]),attributes:n.attributes().getOr({}),styles:n.styles().getOr({}),value:n.value().getOr("<none>"),innerHtml:n.innerHtml().getOr("<none>"),defChildren:n.defChildren().fold(function(){return"<none>"},function(n){return kt(n,null,2)}),domChildren:n.domChildren().fold(function(){return"<none>"},function(n){return 0===n.length?"0 children, but still specified":String(n.length)})}},Or=Ce([],["classes","attributes","styles","value","innerHtml","defChildren","domChildren"]),kr=function(e,n,t){return n.fold(function(){return t.fold(function(){return{}},function(n){return yt(e,n)})},function(n){return t.fold(function(){return yt(e,n)},function(n){return yt(e,n)})})},Cr=function(t,r,o){return yr(function(n,e){o(n,t,r)})},Er=function(n,e,t,r,o,i){var u,c,a=n,s=tr(e,[(u="config",c=n,jt(u,u,rt(),c))]);return Ar(a,s,e,t,r,o,i)},Dr=function(o,i,u){var n,e,t,r,c,a;return n=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];var r=[t].concat(n);return t.config({name:A(o)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+o+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(r,1);return i.apply(undefined,[t,n.config,n.state].concat(e))})},e=u,t=i.toString(),r=t.indexOf(")")+1,c=t.indexOf("("),a=t.substring(c+1,r-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:e,parameters:wr(a.slice(0,1).concat(a.slice(3)))}},n},Ir=function(n){return{key:n,value:undefined}},Ar=function(t,n,r,o,e,i,u){var c=function(n){return xt(n,r)?n[r]():F.none()},a=V(e,function(n,e){return Dr(r,n,e)}),s=V(i,function(n,e){return xr(n,e)}),f=k(s,a,{revoke:l(Ir,r),config:function(n){var e=Gt(r+"-config",t,n);return{key:r,value:{config:e,me:f,configAsRaw:J(function(){return Ut(r+"-config",t,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,t){return c(n).bind(function(e){return ht(o,"exhibit").map(function(n){return n(t,e.config,e.state)})}).getOr(Or({}))},name:function(){return r},handlers:function(n){return c(n).bind(function(e){return ht(o,"events").map(function(n){return n(e.config,e.state)})}).getOr({})}});return f},Mr=function(n,e){return Fr(n,e,{validate:w,label:"function"})},Fr=function(r,o,i){if(0===o.length)throw new Error("You must specify at least one required field.");return Oe("required",o),ke(o),function(e){var t=R(e);En(o,function(n){return hn(t,n)})||Te(o,t),r(o,t);var n=wn(o,function(n){return!i.validate(e[n],n)});return 0<n.length&&function(n,e){throw new Error("All values need to be of type: "+e+". Keys ("+xe(n).join(", ")+") were not.")}(n,i.label),e}},Rr=function(e,n){var t=wn(n,function(n){return!hn(e,n)});0<t.length&&Se(t)},Br=I,Vr=function(n){return Mr(Rr,n)},Nr={init:function(){return _r({readState:function(){return"No State required"}})}},_r=function(n){return Mr(Br,["readState"])(n),n},jr=function(n){return bt(n)},Hr=Rt([Jt("fields"),Jt("name"),or("active",{}),or("apis",{}),or("state",Nr),or("extra",{})]),zr=function(n){var e,t,r,o,i,u,c,a,s=Ut("Creating behaviour: "+n.name,Hr,n);return e=s.fields,t=s.name,r=s.active,o=s.apis,i=s.extra,u=s.state,c=Rt(e),a=tr(t,[rr("config",e)]),Ar(c,a,t,r,o,i,u)},Lr=Rt([Jt("branchKey"),Jt("branches"),Jt("name"),or("active",{}),or("apis",{}),or("state",Nr),or("extra",{})]),Pr=A(undefined),$r=function(n,e,t){if(!(y(t)||b(t)||T(t)))throw p.console.error("Invalid call to Attr.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},Wr=function(n,e,t){$r(n.dom(),e,t)},Ur=function(n,e){var t=n.dom();B(e,function(n,e){$r(t,e,n)})},Gr=function(n,e){var t=n.dom().getAttribute(e);return null===t?undefined:t},qr=function(n,e){var t=n.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(e)},Yr=function(n,e){n.dom().removeAttribute(e)},Kr=function(n,e){var t=Gr(n,e);return t===undefined||""===t?[]:t.split(" ")},Xr=function(n){return n.dom().classList!==undefined},Jr=function(n){return Kr(n,"class")},Qr=function(n,e){return o=e,i=Kr(t=n,r="class").concat([o]),Wr(t,r,i.join(" ")),!0;var t,r,o,i},Zr=function(n,e){return o=e,0<(i=wn(Kr(t=n,r="class"),function(n){return n!==o})).length?Wr(t,r,i.join(" ")):Yr(t,r),!1;var t,r,o,i},no=function(n,e){Xr(n)?n.dom().classList.add(e):Qr(n,e)},eo=function(n,e){var t;Xr(n)?n.dom().classList.remove(e):Zr(n,e),0===(Xr(t=n)?t.dom().classList:Jr(t)).length&&Yr(t,"class")},to=function(n,e){return Xr(n)?n.dom().classList.toggle(e):(r=e,hn(Jr(t=n),r)?Zr(t,r):Qr(t,r));var t,r},ro=function(n,e){return Xr(n)&&n.dom().classList.contains(e)},oo=function(n,e,t){eo(n,t),no(n,e)},io=Object.freeze({toAlpha:function(n,e,t){oo(n.element(),e.alpha(),e.omega())},toOmega:function(n,e,t){oo(n.element(),e.omega(),e.alpha())},isAlpha:function(n,e,t){return ro(n.element(),e.alpha())},isOmega:function(n,e,t){return ro(n.element(),e.omega())},clear:function(n,e,t){eo(n.element(),e.alpha()),eo(n.element(),e.omega())}}),uo=[Jt("alpha"),Jt("omega")],co=zr({fields:uo,name:"swapping",apis:io}),ao=function(n){var e=n,t=function(){return e};return{get:t,set:function(n){e=n},clone:function(){return ao(t())}}};function so(n,e,t,r,o){return n(t,r)?F.some(t):w(o)&&o(t)?F.none():e(t,r,o)}var fo=function(n,e,t){for(var r=n.dom(),o=w(t)?t:A(!1);r.parentNode;){r=r.parentNode;var i=ue.fromDom(r);if(e(i))return F.some(i);if(o(i))break}return F.none()},lo=function(n,e,t){return so(function(n,e){return e(n)},fo,n,e,t)},mo=function(n){n.dom().focus()},go=function(n){n.dom().blur()},po=function(n){var e=n!==undefined?n.dom():p.document;return F.from(e.activeElement).map(ue.fromDom)},vo=function(e){return po(Be(e)).filter(function(n){return e.dom().contains(n.dom())})},ho=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),yo=tinymce.util.Tools.resolve("tinymce.ThemeManager"),bo=function(n){var e=p.document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=p.document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,p.window,0,0,0,0,0,!1,!1,!1,!1,0,null),p.document.body.appendChild(e),e.dispatchEvent(t),p.document.body.removeChild(e)},wo={formatChanged:A("formatChanged"),orientationChanged:A("orientationChanged"),dropupDismissed:A("dropupDismissed")},xo=function(n){return n.dom().innerHTML},To=function(n,e){var t,r,o=Be(n).dom(),i=ue.fromDom(o.createDocumentFragment()),u=(t=e,(r=(o||p.document).createElement("div")).innerHTML=t,_e(ue.fromDom(r)));Le(i,u),Pe(n),ze(n,i)},So=function(n){return e=n,t=!1,ue.fromDom(e.dom().cloneNode(t));var e,t},Oo=function(n){var e,t,r,o=So(n);return e=o,t=ue.fromTag("div"),r=ue.fromDom(e.dom().cloneNode(!0)),ze(t,r),xo(t)},ko=function(n){return Oo(n)},Co=Object.freeze({events:function(c){return sr([lr(Wn(),function(o,i){var n,e,u=c.channels(),t=R(u),r=(n=t,(e=i).universal()?n:wn(n,function(n){return hn(e.channels(),n)}));bn(r,function(n){var e=u[n](),t=e.schema(),r=Gt("channel["+n+"] data\nReceiver: "+ko(o.element()),t,i.data());e.onReceive()(o,r)})})])}}),Eo=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e},Do="unknown",Io=[],Ao=["alloy/data/Fields","alloy/debugging/Debugging"],Mo={logEventCut:I,logEventStopped:I,logNoParent:I,logEventNoHandlers:I,logEventResponse:I,write:I},Fo=function(n,e,t){var r,o="*"===Io||hn(Io,n)?(r=[],{logEventCut:function(n,e,t){r.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){r.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){r.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){r.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){r.push({outcome:"response",purpose:t,target:e})},write:function(){hn(["mousemove","mouseover","mouseout",Xn()],n)||p.console.log(n,{event:n,target:e.dom(),sequence:yn(r,function(n){return hn(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+ko(n.target)+")":n.outcome})})}}):Mo,i=t(o);return o.write(),i},Ro=A([Jt("menu"),Jt("selectedMenu")]),Bo=A([Jt("item"),Jt("selectedItem")]),Vo=(A(Rt(Bo().concat(Ro()))),A(Rt(Bo()))),No=nr("initSize",[Jt("numColumns"),Jt("numRows")]),_o=function(n,e,t){var r;return function(){var n=new Error;if(n.stack!==undefined){var e=n.stack.split("\n");Sn(e,function(e){return 0<e.indexOf("alloy")&&!function(n,e){for(var t=0,r=n.length;t<r;t++)if(e(n[t],t))return!0;return!1}(Ao,function(n){return-1<e.indexOf(n)})}).getOr(Do)}}(),jt(e,e,t,(r=function(t){return Qe.value(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(undefined,n)})},Ft(function(n){return r(n)})))},jo=function(n){return _o(0,n,et(I))},Ho=function(n){return _o(0,n,et(F.none))},zo=function(n){return _o(0,n,tt())},Lo=function(n){return _o(0,n,tt())},Po=function(n,e){return ur(n,A(e))},$o=function(n){return ur(n,h)},Wo=A(No),Uo=[Qt("channels",Vt(Qe.value,Rt([zo("onReceive"),or("schema",Kt())])))],Go=zr({fields:Uo,name:"receiving",active:Co}),qo=function(n,e){var t=Jo(n,e),r=e.aria();r.update()(n,r,t)},Yo=function(n,e,t){to(n.element(),e.toggleClass()),qo(n,e)},Ko=function(n,e,t){no(n.element(),e.toggleClass()),qo(n,e)},Xo=function(n,e,t){eo(n.element(),e.toggleClass()),qo(n,e)},Jo=function(n,e){return ro(n.element(),e.toggleClass())},Qo=function(n,e,t){(e.selected()?Ko:Xo)(n,e,t)},Zo=Object.freeze({onLoad:Qo,toggle:Yo,isOn:Jo,on:Ko,off:Xo}),ni=Object.freeze({exhibit:function(n,e,t){return Or({})},events:function(n,e){var t,r,o,i=(t=n,r=e,o=Yo,br(function(n){o(n,t,r)})),u=Cr(n,e,Qo);return sr(kn([n.toggleOnExecute()?[i]:[],[u]]))}}),ei=function(n,e,t){Wr(n.element(),"aria-expanded",t)},ti=[or("selected",!1),Jt("toggleClass"),or("toggleOnExecute",!0),ir("aria",{mode:"none"},Yt("mode",{pressed:[or("syncWithExpanded",!1),Po("update",function(n,e,t){Wr(n.element(),"aria-pressed",t),e.syncWithExpanded()&&ei(n,e,t)})],checked:[Po("update",function(n,e,t){Wr(n.element(),"aria-checked",t)})],expanded:[Po("update",ei)],selected:[Po("update",function(n,e,t){Wr(n.element(),"aria-selected",t)})],none:[Po("update",I)]}))],ri=zr({fields:ti,name:"toggling",active:ni,apis:Zo}),oi=function(t,r){return Go.config({channels:yt(wo.formatChanged(),{onReceive:function(n,e){e.command===t&&r(n,e.state)}})})},ii=function(n){return Go.config({channels:yt(wo.orientationChanged(),{onReceive:n})})},ui=function(n,e){return{key:n,value:{onReceive:e}}},ci="tinymce-mobile",ai={resolve:function(n){return ci+"-"+n},prefix:A(ci)},si=function(n,e){e.ignore()||(mo(n.element()),e.onFocus()(n))},fi=Object.freeze({focus:si,blur:function(n,e){e.ignore()||go(n.element())},isFocused:function(n){return e=n.element(),t=Be(e).dom(),e.dom()===t.activeElement;var e,t}}),li=Object.freeze({exhibit:function(n,e){return e.ignore()?Or({}):Or({attributes:{tabindex:"-1"}})},events:function(t){return sr([lr(Pn(),function(n,e){si(n,t),e.stop()})])}}),di=[jo("onFocus"),or("ignore",!1)],mi=zr({fields:di,name:"focusing",active:li,apis:fi}),gi=function(n){return n.style!==undefined&&w(n.style.getPropertyValue)},pi=function(n,e,t){if(!y(t))throw p.console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);gi(n)&&n.style.setProperty(e,t)},vi=function(n,e,t){var r=n.dom();pi(r,e,t)},hi=function(n,e){var t=n.dom();B(e,function(n,e){pi(t,e,n)})},yi=function(n,e){var t=n.dom(),r=p.window.getComputedStyle(t).getPropertyValue(e),o=""!==r||he(n)?r:bi(t,e);return null===o?undefined:o},bi=function(n,e){return gi(n)?n.style.getPropertyValue(e):""},wi=function(n,e){var t=n.dom(),r=bi(t,e);return F.from(r).filter(function(n){return 0<n.length})},xi=function(n,e){var t,r,o=n.dom();r=e,gi(t=o)&&t.style.removeProperty(r),qr(n,"style")&&""===Gr(n,"style").replace(/^\s+|\s+$/g,"")&&Yr(n,"style")},Ti=function(n){return n.dom().offsetWidth};function Si(r,o){var n=function(n){var e=o(n);if(e<=0||null===e){var t=yi(n,r);return parseFloat(t)||0}return e},i=function(o,n){return Tn(n,function(n,e){var t=yi(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)};return{set:function(n,e){if(!T(e)&&!e.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+e);var t=n.dom();gi(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:i,max:function(n,e,t){var r=i(n,t);return r<e?e-r:0}}}var Oi,ki,Ci=Si("height",function(n){var e=n.dom();return he(n)?e.getBoundingClientRect().height:e.offsetHeight}),Ei=function(n){return Ci.get(n)},Di=function(n,e,t){return wn(function(n,e){for(var t=w(e)?e:s,r=n.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=ue.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}(n,t),e)},Ii=function(n,e){return wn(Ne(t=n).map(_e).map(function(n){return wn(n,function(n){return!Re(t,n)})}).getOr([]),e);var t},Ai=function(n,e){return Me(e,n)},Mi=function(n){return Fe(n)},Fi=function(n,e,t){return fo(n,function(n){return Ie(n,e)},t)},Ri=function(n,e){return Fe(e,n)},Bi=function(n,e,t){return so(Ie,Fi,n,e,t)},Vi=function(n,e,t){var r=Dn(n.slice(0,e)),o=Dn(n.slice(e+1));return Sn(r.concat(o),t)},Ni=function(n,e,t){var r=Dn(n.slice(0,e));return Sn(r,t)},_i=function(n,e,t){var r=n.slice(0,e),o=n.slice(e+1);return Sn(o.concat(r),t)},ji=function(n,e,t){var r=n.slice(e+1);return Sn(r,t)},Hi=function(t){return function(n){var e=n.raw();return hn(t,e.which)}},zi=function(n){return function(e){return En(n,function(n){return n(e)})}},Li=function(n){return!0===n.raw().shiftKey},Pi=function(n){return!0===n.raw().ctrlKey},$i=x(Li),Wi=function(n,e){return{matches:n,classification:e}},Ui=function(n,e,t,r){var o=n+e;return r<o?t:o<t?r:o},Gi=function(n,e,t){return n<=e?e:t<=n?t:n},qi=function(e,t,n){var r=Ai(e.element(),"."+t.highlightClass());bn(r,function(n){eo(n,t.highlightClass()),e.getSystem().getByDom(n).each(function(n){t.onDehighlight()(e,n)})})},Yi=function(n,e,t,r){var o=Ki(n,e,t,r);qi(n,e),no(r.element(),e.highlightClass()),o||e.onHighlight()(n,r)},Ki=function(n,e,t,r){return ro(r.element(),e.highlightClass())},Xi=function(n,e,t,r){var o=Ai(n.element(),"."+e.itemClass());return F.from(o[r]).fold(function(){return Qe.error("No element found with index "+r)},n.getSystem().getByDom)},Ji=function(e,n,t){return Ri(e.element(),"."+n.itemClass()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},Qi=function(e,n,t){var r=Ai(e.element(),"."+n.itemClass());return(0<r.length?F.some(r[r.length-1]):F.none()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},Zi=function(t,e,n,r){var o=Ai(t.element(),"."+e.itemClass());return On(o,function(n){return ro(n,e.highlightClass())}).bind(function(n){var e=Ui(n,r,0,o.length-1);return t.getSystem().getByDom(o[e]).toOption()})},nu=Object.freeze({dehighlightAll:qi,dehighlight:function(n,e,t,r){var o=Ki(n,e,t,r);eo(r.element(),e.highlightClass()),o&&e.onDehighlight()(n,r)},highlight:Yi,highlightFirst:function(e,t,r){Ji(e,t).each(function(n){Yi(e,t,r,n)})},highlightLast:function(e,t,r){Qi(e,t).each(function(n){Yi(e,t,r,n)})},highlightAt:function(e,t,r,n){Xi(e,t,r,n).fold(function(n){throw new Error(n)},function(n){Yi(e,t,r,n)})},highlightBy:function(e,t,r,n){var o=Ai(e.element(),"."+t.itemClass()),i=Eo(yn(o,function(n){return e.getSystem().getByDom(n).toOption()}));Sn(i,n).each(function(n){Yi(e,t,r,n)})},isHighlighted:Ki,getHighlighted:function(e,n,t){return Ri(e.element(),"."+n.highlightClass()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},getFirst:Ji,getLast:Qi,getPrevious:function(n,e,t){return Zi(n,e,0,-1)},getNext:function(n,e,t){return Zi(n,e,0,1)}}),eu=[Jt("highlightClass"),Jt("itemClass"),jo("onHighlight"),jo("onDehighlight")],tu=zr({fields:eu,name:"highlighting",apis:nu}),ru=function(){return{get:function(n){return vo(n.element())},set:function(n,e){n.getSystem().triggerFocus(e,n.element())}}},ou=function(n,e,c,t,r,i){var u=function(e,t,r,o){var n,i,u=c(e,t,r,o);return(n=u,i=t.event(),Sn(n,function(n){return n.matches(i)}).map(function(n){return n.classification})).bind(function(n){return n(e,t,r,o)})},o={schema:function(){return n.concat([or("focusManager",ru()),Po("handler",o),Po("state",e)])},processKey:u,toEvents:function(r,o){var n=t(r,o),e=sr(i.map(function(t){return lr(Pn(),function(n,e){t(n,r,o,e),e.stop()})}).toArray().concat([lr(U(),function(n,e){u(n,e,r,o).each(function(n){e.stop()})})]));return k(n,e)},toApis:r};return o},iu=function(n){var e=[er("onEscape"),er("onEnter"),or("selector",'[data-alloy-tabstop="true"]'),or("firstTabstop",0),or("useTabstopAt",A(!0)),er("visibilitySelector")].concat([n]),u=function(n,e){var t=n.visibilitySelector().bind(function(n){return Bi(e,n)}).getOr(e);return 0<Ei(t)},c=function(e,n,t,r,o){return o(n,t,function(n){return u(e=r,t=n)&&e.useTabstopAt()(t);var e,t}).fold(function(){return r.cyclic()?F.some(!0):F.none()},function(n){return r.focusManager().set(e,n),F.some(!0)})},i=function(e,n,t,r){var o,i,u=Ai(e.element(),t.selector());return(o=e,i=t,i.focusManager().get(o).bind(function(n){return Bi(n,i.selector())})).bind(function(n){return On(u,l(Re,n)).bind(function(n){return c(e,u,n,t,r)})})},t=A([Wi(zi([Li,Hi([9])]),function(n,e,t,r){var o=t.cyclic()?Vi:Ni;return i(n,0,t,o)}),Wi(Hi([9]),function(n,e,t,r){var o=t.cyclic()?_i:ji;return i(n,0,t,o)}),Wi(Hi([27]),function(e,t,n,r){return n.onEscape().bind(function(n){return n(e,t)})}),Wi(zi([$i,Hi([13])]),function(e,t,n,r){return n.onEnter().bind(function(n){return n(e,t)})})]),r=A({}),o=A({});return ou(e,Nr.init,t,r,o,F.some(function(e,t){var n,r,o,i;(n=e,r=t,o=Ai(n.element(),r.selector()),i=wn(o,function(n){return u(r,n)}),F.from(i[r.firstTabstop()])).each(function(n){t.focusManager().set(e,n)})}))},uu=iu(ur("cyclic",A(!1))),cu=iu(ur("cyclic",A(!0))),au=function(n){return"input"===me(n)&&"radio"!==Gr(n,"type")||"textarea"===me(n)},su=function(n,e,t){return au(t)&&Hi([32])(e.event())?F.none():(re(n,t,Un()),F.some(!0))},fu=[or("execute",su),or("useSpace",!1),or("useEnter",!0),or("useControlEnter",!1),or("useDown",!1)],lu=function(n,e,t){return t.execute()(n,e,n.element())},du=A({}),mu=A({}),gu=ou(fu,Nr.init,function(n,e,t,r){var o=t.useSpace()&&!au(n.element())?[32]:[],i=t.useEnter()?[13]:[],u=t.useDown()?[40]:[],c=o.concat(i).concat(u);return[Wi(Hi(c),lu)].concat(t.useControlEnter()?[Wi(zi([Pi,Hi([13])]),lu)]:[])},du,mu,F.none()),pu=function(n){var t=ao(F.none());return _r({readState:A({}),setGridSize:function(n,e){t.set(F.some({numRows:A(n),numColumns:A(e)}))},getNumRows:function(){return t.get().map(function(n){return n.numRows()})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns()})}})},vu=Object.freeze({flatgrid:pu,init:function(n){return n.state()(n)}}),hu=function(e,t){return function(n){return"rtl"===yu(n)?t:e}},yu=function(n){return"rtl"===yi(n,"direction")?"rtl":"ltr"},bu=function(i){return function(n,e,t,r){var o=i(n.element());return Su(o,n,e,t,r)}},wu=function(n,e){var t=hu(n,e);return bu(t)},xu=function(n,e){var t=hu(e,n);return bu(t)},Tu=function(o){return function(n,e,t,r){return Su(o,n,e,t,r)}},Su=function(e,t,n,r,o){return r.focusManager().get(t).bind(function(n){return e(t.element(),n,r,o)}).map(function(n){return r.focusManager().set(t,n),!0})},Ou=Tu,ku=Tu,Cu=Tu,Eu=function(n){var e,t=n.dom();return!((e=t).offsetWidth<=0&&e.offsetHeight<=0)},Du=Ce(["index","candidates"],[]),Iu=function(n,e,t){return Au(n,e,t)},Au=function(n,e,t,r){var o,i=l(Re,e),u=Ai(n,t),c=wn(u,Eu);return On(o=c,i).map(function(n){return Du({index:n,candidates:o})})},Mu=function(n,e){return On(n,function(n){return Re(e,n)})},Fu=function(t,n,r,e){return e(Math.floor(n/r),n%r).bind(function(n){var e=n.row()*r+n.column();return 0<=e&&e<t.length?F.some(t[e]):F.none()})},Ru=function(o,n,i,u,c){return Fu(o,n,u,function(n,e){var t=n===i-1?o.length-n*u:u,r=Ui(e,c,0,t-1);return F.some({row:A(n),column:A(r)})})},Bu=function(i,n,u,c,a){return Fu(i,n,c,function(n,e){var t=Ui(n,a,0,u-1),r=t===u-1?i.length-t*c:c,o=Gi(e,0,r-1);return F.some({row:A(t),column:A(o)})})},Vu=[Jt("selector"),or("execute",su),Ho("onEscape"),or("captureTab",!1),Wo()],Nu=function(o){return function(n,e,t,r){return Iu(n,e,t.selector()).bind(function(n){return o(n.candidates(),n.index(),r.getNumRows().getOr(t.initSize().numRows()),r.getNumColumns().getOr(t.initSize().numColumns()))})}},_u=function(n,e,t,r){return t.captureTab()?F.some(!0):F.none()},ju=Nu(function(n,e,t,r){return Ru(n,e,t,r,-1)}),Hu=Nu(function(n,e,t,r){return Ru(n,e,t,r,1)}),zu=Nu(function(n,e,t,r){return Bu(n,e,t,r,-1)}),Lu=Nu(function(n,e,t,r){return Bu(n,e,t,r,1)}),Pu=A([Wi(Hi([37]),wu(ju,Hu)),Wi(Hi([39]),xu(ju,Hu)),Wi(Hi([38]),Ou(zu)),Wi(Hi([40]),ku(Lu)),Wi(zi([Li,Hi([9])]),_u),Wi(zi([$i,Hi([9])]),_u),Wi(Hi([27]),function(n,e,t,r){return t.onEscape()(n,e)}),Wi(Hi([32].concat([13])),function(e,t,r,n){return(o=e,i=r,i.focusManager().get(o).bind(function(n){return Bi(n,i.selector())})).bind(function(n){return r.execute()(e,t,n)});var o,i})]),$u=A({}),Wu=ou(Vu,pu,Pu,$u,{},F.some(function(e,t,n){Ri(e.element(),t.selector()).each(function(n){t.focusManager().set(e,n)})})),Uu=function(n,e,t,o){return Iu(n,t,e).bind(function(n){var e=n.index(),t=n.candidates(),r=Ui(e,o,0,t.length-1);return F.from(t[r])})},Gu=[Jt("selector"),or("getInitial",F.none),or("execute",su),or("executeOnMove",!1),or("allowVertical",!0)],qu=function(e,t,r){return(n=e,o=r,o.focusManager().get(n).bind(function(n){return Bi(n,o.selector())})).bind(function(n){return r.execute()(e,t,n)});var n,o},Yu=function(n,e,t){return Uu(n,t.selector(),e,-1)},Ku=function(n,e,t){return Uu(n,t.selector(),e,1)},Xu=function(r){return function(n,e,t){return r(n,e,t).bind(function(){return t.executeOnMove()?qu(n,e,t):F.some(!0)})}},Ju=A({}),Qu=A({}),Zu=ou(Gu,Nr.init,function(n,e,t,r){var o=[37].concat(t.allowVertical()?[38]:[]),i=[39].concat(t.allowVertical()?[40]:[]);return[Wi(Hi(o),Xu(wu(Yu,Ku))),Wi(Hi(i),Xu(xu(Yu,Ku))),Wi(Hi([13]),qu),Wi(Hi([32]),qu)]},Ju,Qu,F.some(function(e,t){t.getInitial()(e).or(Ri(e.element(),t.selector())).each(function(n){t.focusManager().set(e,n)})})),nc=Ce(["rowIndex","columnIndex","cell"],[]),ec=function(n,e,t){return F.from(n[e]).bind(function(n){return F.from(n[t]).map(function(n){return nc({rowIndex:e,columnIndex:t,cell:n})})})},tc=function(n,e,t,r){var o=n[e].length,i=Ui(t,r,0,o-1);return ec(n,e,i)},rc=function(n,e,t,r){var o=Ui(t,r,0,n.length-1),i=n[o].length,u=Gi(e,0,i-1);return ec(n,o,u)},oc=function(n,e,t,r){var o=n[e].length,i=Gi(t+r,0,o-1);return ec(n,e,i)},ic=function(n,e,t,r){var o=Gi(t+r,0,n.length-1),i=n[o].length,u=Gi(e,0,i-1);return ec(n,o,u)},uc=[nr("selectors",[Jt("row"),Jt("cell")]),or("cycles",!0),or("previousSelector",F.none),or("execute",su)],cc=function(n,e){return function(t,r,i){var u=i.cycles()?n:e;return Bi(r,i.selectors().row()).bind(function(n){var e=Ai(n,i.selectors().cell());return Mu(e,r).bind(function(r){var o=Ai(t,i.selectors().row());return Mu(o,n).bind(function(n){var e,t=(e=i,yn(o,function(n){return Ai(n,e.selectors().cell())}));return u(t,n,r).map(function(n){return n.cell()})})})})}},ac=cc(function(n,e,t){return tc(n,e,t,-1)},function(n,e,t){return oc(n,e,t,-1)}),sc=cc(function(n,e,t){return tc(n,e,t,1)},function(n,e,t){return oc(n,e,t,1)}),fc=cc(function(n,e,t){return rc(n,t,e,-1)},function(n,e,t){return ic(n,t,e,-1)}),lc=cc(function(n,e,t){return rc(n,t,e,1)},function(n,e,t){return ic(n,t,e,1)}),dc=A([Wi(Hi([37]),wu(ac,sc)),Wi(Hi([39]),xu(ac,sc)),Wi(Hi([38]),Ou(fc)),Wi(Hi([40]),ku(lc)),Wi(Hi([32].concat([13])),function(e,t,r){return vo(e.element()).bind(function(n){return r.execute()(e,t,n)})})]),mc=A({}),gc=A({}),pc=ou(uc,Nr.init,dc,mc,gc,F.some(function(e,t){t.previousSelector()(e).orThunk(function(){var n=t.selectors();return Ri(e.element(),n.cell())}).each(function(n){t.focusManager().set(e,n)})})),vc=[Jt("selector"),or("execute",su),or("moveOnTab",!1)],hc=function(e,t,r){return r.focusManager().get(e).bind(function(n){return r.execute()(e,t,n)})},yc=function(n,e,t){return Uu(n,t.selector(),e,-1)},bc=function(n,e,t){return Uu(n,t.selector(),e,1)},wc=A([Wi(Hi([38]),Cu(yc)),Wi(Hi([40]),Cu(bc)),Wi(zi([Li,Hi([9])]),function(n,e,t){return t.moveOnTab()?Cu(yc)(n,e,t):F.none()}),Wi(zi([$i,Hi([9])]),function(n,e,t){return t.moveOnTab()?Cu(bc)(n,e,t):F.none()}),Wi(Hi([13]),hc),Wi(Hi([32]),hc)]),xc=A({}),Tc=A({}),Sc=ou(vc,Nr.init,wc,xc,Tc,F.some(function(e,t){Ri(e.element(),t.selector()).each(function(n){t.focusManager().set(e,n)})})),Oc=[Ho("onSpace"),Ho("onEnter"),Ho("onShiftEnter"),Ho("onLeft"),Ho("onRight"),Ho("onTab"),Ho("onShiftTab"),Ho("onUp"),Ho("onDown"),Ho("onEscape"),er("focusIn")],kc=ou(Oc,Nr.init,function(n,e,t){return[Wi(Hi([32]),t.onSpace()),Wi(zi([$i,Hi([13])]),t.onEnter()),Wi(zi([Li,Hi([13])]),t.onShiftEnter()),Wi(zi([Li,Hi([9])]),t.onShiftTab()),Wi(zi([$i,Hi([9])]),t.onTab()),Wi(Hi([38]),t.onUp()),Wi(Hi([40]),t.onDown()),Wi(Hi([37]),t.onLeft()),Wi(Hi([39]),t.onRight()),Wi(Hi([32]),t.onSpace()),Wi(Hi([27]),t.onEscape())]},function(){return{}},function(){return{}},F.some(function(e,t){return t.focusIn().bind(function(n){return n(e,t)})})),Cc=uu.schema(),Ec=cu.schema(),Dc=Zu.schema(),Ic=Wu.schema(),Ac=pc.schema(),Mc=gu.schema(),Fc=Sc.schema(),Rc=kc.schema(),Bc=(ki=Ut("Creating behaviour: "+(Oi={branchKey:"mode",branches:Object.freeze({acyclic:Cc,cyclic:Ec,flow:Dc,flatgrid:Ic,matrix:Ac,execution:Mc,menu:Fc,special:Rc}),name:"keying",active:{events:function(n,e){return n.handler().toEvents(n,e)}},apis:{focusIn:function(n){n.getSystem().triggerFocus(n.element(),n.element())},setGridSize:function(n,e,t,r,o){xt(t,"setGridSize")?t.setGridSize(r,o):p.console.error("Layout does not support setGridSize")}},state:vu}).name,Lr,Oi),Er(Yt(ki.branchKey,ki.branches),ki.name,ki.active,ki.apis,ki.extra,ki.state)),Vc=function(r,n){return e=r,t={},o=yn(n,function(n){return e=n.name(),t="Cannot configure "+n.name()+" for "+r,jt(e,e,rt(),Ft(function(n){return Qe.error("The field: "+e+" is forbidden. "+t)}));var e,t}).concat([ur("dump",h)]),jt(e,e,et(t),Bt(o));var e,t,o},Nc=function(n){return n.dump()},_c="placeholder",jc=Ze([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Hc=function(n,e,t,r){return t.uiType===_c?(i=t,u=r,(o=n).exists(function(n){return n!==i.owner})?jc.single(!0,A(i)):ht(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+R(u)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+kt(i,null,2))},function(n){return n.replace()})):jc.single(!1,A(t));var o,i,u},zc=function(i,u,c,a){return Hc(i,0,c,a).fold(function(n,e){var t=e(u,c.config,c.validated),r=ht(t,"components").getOr([]),o=Cn(r,function(n){return zc(i,u,n,a)});return[k(t,{components:o})]},function(n,e){return e(u,c.config,c.validated)})},Lc=function(e,t,n,r){var o,i,u,c=V(r,function(n,e){return r=n,o=!1,{name:A(t=e),required:function(){return r.fold(function(n,e){return n},function(n,e){return n})},used:function(){return o},replace:function(){if(!0===o)throw new Error("Trying to use the same placeholder more than once: "+t);return o=!0,r}};var t,r,o}),a=(o=e,i=t,u=c,Cn(n,function(n){return zc(o,i,n,u)}));return B(c,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+kt(t.components(),null,2))}),a},Pc=jc.single,$c=jc.multiple,Wc=A(_c),Uc=0,Gc=function(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Uc+String(e)},qc=Ze([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Yc=or("factory",{sketch:h}),Kc=or("schema",[]),Xc=Jt("name"),Jc=jt("pname","pname",ot(function(n){return"<alloy."+Gc(n.name)+">"}),Kt()),Qc=or("defaults",A({})),Zc=or("overrides",A({})),na=Bt([Yc,Kc,Xc,Jc,Qc,Zc]),ea=Bt([Yc,Kc,Xc,Jc,Qc,Zc]),ta=Bt([Yc,Kc,Xc,Jt("unit"),Jc,Qc,Zc]),ra=function(n){var e=function(n){return n.name()};return n.fold(e,e,e,e)},oa=function(t,r){return function(n){var e=Gt("Converting part type",r,n);return t(e)}},ia=oa(qc.required,na),ua=oa(qc.optional,ea),ca=oa(qc.group,ta),aa=A("entirety"),sa=function(n,e,t,r){var o=t;return k(e.defaults()(n,t,r),t,{uid:n.partUids()[e.name()]},e.overrides()(n,t,r),{"debug.sketcher":yt("part-"+e.name(),o)})},fa=function(o,n){var i={};return bn(n,function(n){var e;(e=n,e.fold(F.some,F.none,F.some,F.some)).each(function(t){var r=la(o,t.pname());i[t.name()]=function(n){var e=Ut("Part: "+t.name()+" in "+o,Bt(t.schema()),n);return k(r,{config:n,validated:e})}})}),i},la=function(n,e){return{uiType:Wc(),owner:n,name:e}},da=function(n,e,t){return r=e,i={},o={},bn(t,function(n){n.fold(function(r){i[r.pname()]=Pc(!0,function(n,e,t){return r.factory().sketch(sa(n,r,e,t))})},function(n){var e=r.parts()[n.name()]();o[n.name()]=A(sa(r,n,e[aa()]()))},function(r){i[r.pname()]=Pc(!1,function(n,e,t){return r.factory().sketch(sa(n,r,e,t))})},function(o){i[o.pname()]=$c(!0,function(e,n,t){var r=e[o.name()]();return yn(r,function(n){return o.factory().sketch(k(o.defaults()(e,n),n,o.overrides()(e,n)))})})})}),{internals:A(i),externals:A(o)};var r,i,o},ma=function(n,e,t){return Lc(F.some(n),e,e.components(),t)},ga=function(n,e,t){var r=e.partUids()[t];return n.getSystem().getByUid(r).toOption()},pa=function(n,e,t){return ga(n,e,t).getOrDie("Could not find part: "+t)},va=function(e,n){var t=yn(n,ra);return bt(yn(t,function(n){return{key:n,value:e+"-"+n}}))},ha=function(e){return jt("partUids","partUids",it(function(n){return va(n.uid,e)}),Kt())},ya=Gc("alloy-premade"),ba=Gc("api"),wa=function(n){return yt(ya,n)},xa=function(o){return n=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];var r=n.config(ba);return o.apply(undefined,[r].concat([n].concat(e)))},e=o.toString(),t=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,t-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:wr(i.slice(1))}},n;var n,e,t,r,i},Ta=A(ba),Sa=A("alloy-id-"),Oa=A("data-alloy-id"),ka=Sa(),Ca=Oa(),Ea=function(n){var e=pe(n)?Gr(n,Ca):null;return F.from(e)},Da=function(n){return Gc(n)},Ia=function(n,e,t,r,o){var i,u,c=(u=o,(0<(i=r).length?[nr("parts",i)]:[]).concat([Jt("uid"),or("dom",{}),or("components",[]),$o("originalSpec"),or("debug.sketcher",{})]).concat(u));return Gt(n+" [SpecSchema]",Rt(c.concat(e)),t)},Aa=function(n,e,t,r,o){var i=Ma(o),u=Cn(t,function(n){return n.fold(F.none,F.some,F.none,F.none).map(function(n){return nr(n.name(),n.schema().concat([$o(aa())]))}).toArray()}),c=ha(t),a=Ia(n,e,i,u,[c]),s=da(0,a,t),f=ma(n,a,s.internals());return k(r(a,f,i,s.externals()),{"debug.sketcher":yt(n,o)})},Ma=function(n){return k({uid:Da("uid")},n)},Fa=Rt([Jt("name"),Jt("factory"),Jt("configFields"),or("apis",{}),or("extraApis",{})]),Ra=Rt([Jt("name"),Jt("factory"),Jt("configFields"),Jt("partFields"),or("apis",{}),or("extraApis",{})]),Ba=function(n){var c=Ut("Sketcher for "+n.name,Fa,n),e=V(c.apis,xa),t=V(c.extraApis,function(n,e){return xr(n,e)});return k({name:A(c.name),partFields:A([]),configFields:A(c.configFields),sketch:function(n){return e=c.name,t=c.configFields,r=c.factory,i=Ma(o=n),u=Ia(e,t,i,[],[]),k(r(u,i),{"debug.sketcher":yt(e,o)});var e,t,r,o,i,u}},e,t)},Va=function(n){var e=Ut("Sketcher for "+n.name,Ra,n),t=fa(e.name,e.partFields),r=V(e.apis,xa),o=V(e.extraApis,function(n,e){return xr(n,e)});return k({name:A(e.name),partFields:A(e.partFields),configFields:A(e.configFields),sketch:function(n){return Aa(e.name,e.configFields,e.partFields,e.factory,n)},parts:A(t)},r,o)},Na=Ba({name:"Button",factory:function(n){var e,t,r,o=(e=n.action(),t=function(n,e){e.stop(),te(n)},r=zn.detect().deviceType.isTouch()?[lr(qn(),t)]:[lr(Y(),t),lr(L(),function(n,e){e.cut()})],sr(kn([e.map(function(t){return lr(Un(),function(n,e){t(n),e.stop()})}).toArray(),r]))),i=ht(n.dom(),"attributes").bind(pt("type")),u=ht(n.dom(),"tag");return{uid:n.uid(),dom:n.dom(),components:n.components(),events:o,behaviours:k(jr([mi.config({}),Bc.config({mode:"execution",useSpace:!0,useEnter:!0})]),Nc(n.buttonBehaviours())),domModification:{attributes:k(i.fold(function(){return u.is("button")?{type:"button"}:{}},function(n){return{}}),{role:n.role().getOr("button")})},eventOrder:n.eventOrder()}},configFields:[or("uid",undefined),Jt("dom"),or("components",[]),Vc("buttonBehaviours",[mi,Bc]),er("action"),er("role"),or("eventOrder",{})]}),_a=zr({fields:[],name:"unselecting",active:Object.freeze({events:function(n){return sr([fr(X(),A(!0))])},exhibit:function(n,e){return Or({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),ja=function(n){var e,t,r,o=ue.fromHtml(n),i=_e(o),u=(t=(e=o).dom().attributes!==undefined?e.dom().attributes:[],Tn(t,function(n,e){return"class"===e.name?n:k(n,yt(e.name,e.value))},{})),c=(r=o,Array.prototype.slice.call(r.dom().classList,0)),a=0===i.length?{}:{innerHtml:xo(o)};return k({tag:me(o),classes:c,attributes:u},a)},Ha=function(n){var e,o,t=(e=n,o={prefix:ai.prefix()},e.replace(/\$\{([^{}]*)\}/g,function(n,e){var t,r=o[e];return"string"==(t=typeof r)||"number"===t?r.toString():n}));return ja(t)},za=function(n){return{dom:Ha(n)}},La=function(n){return jr([ri.config({toggleClass:ai.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),oi(n,function(n,e){(e?ri.on:ri.off)(n)})])},Pa=function(n,e,t){return Na.sketch({dom:Ha('<span class="${prefix}-toolbar-button ${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:e,buttonBehaviours:k(jr([_a.config({})]),t)})},$a={forToolbar:Pa,forToolbarCommand:function(n,e){return Pa(e,function(){n.execCommand(e)},{})},forToolbarStateAction:function(n,e,t,r){var o=La(t);return Pa(e,r,o)},forToolbarStateCommand:function(n,e){var t=La(e);return Pa(e,function(){n.execCommand(e)},t)}},Wa=function(t,r){return{left:A(t),top:A(r),translate:function(n,e){return Wa(t+n,r+e)}}},Ua=Wa,Ga=function(n,e,t){return Math.max(e,Math.min(t,n))},qa=function(n,e,t,r,o,i,u){var c=t-e;if(r<n.left)return e-1;if(r>n.right)return t+1;var a,s,f,l,d=Math.min(n.right,Math.max(r,n.left))-n.left,m=Ga(d/n.width*c+e,e-1,t+1),g=Math.round(m);return i&&e<=m&&m<=t?(a=m,s=e,f=t,l=o,u.fold(function(){var n=a-s,e=Math.round(n/l)*l;return Ga(s+e,s-1,f+1)},function(n){var e=(a-n)%l,t=Math.round(e/l),r=Math.floor((a-n)/l),o=Math.floor((f-n)/l),i=n+Math.min(o,r+t)*l;return Math.max(n,i)})):g},Ya="slider.change.value",Ka=zn.detect().deviceType.isTouch(),Xa=function(n){return function(n){var e=n.event().raw();if(Ka){var t=e;return t.touches!==undefined&&1===t.touches.length?F.some(t.touches[0]).map(function(n){return Ua(n.clientX,n.clientY)}):F.none()}var r=e;return r.clientX!==undefined?F.some(r).map(function(n){return Ua(n.clientX,n.clientY)}):F.none()}(n).map(function(n){return n.left()})},Ja=function(n,e){ee(n,Ya,{value:e})},Qa=function(i,u,c,n){return Xa(n).map(function(n){var e,t,r,o;return e=i,r=n,o=qa(c,(t=u).min(),t.max(),r,t.stepSize(),t.snapToGrid(),t.snapStart()),Ja(e,o),n})},Za=function(n,e){var t,r,o,i,u=(t=e.value().get(),r=e.min(),o=e.max(),i=e.stepSize(),t<r?t:o<t?o:t===r?r-1:Math.max(r,t-i));Ja(n,u)},ns=function(n,e){var t,r,o,i,u=(t=e.value().get(),r=e.min(),o=e.max(),i=e.stepSize(),o<t?t:t<r?r:t===o?o+1:Math.min(o,t+i));Ja(n,u)},es=zn.detect().deviceType.isTouch(),ts=function(n,r){return ua({name:n+"-edge",overrides:function(n){var e=sr([dr(j(),r,[n])]),t=sr([dr(L(),r,[n]),dr(P(),function(n,e){e.mouseIsDown().get()&&r(n,e)},[n])]);return{events:es?e:t}}})},rs=[ts("left",function(n,e){Ja(n,e.min()-1)}),ts("right",function(n,e){Ja(n,e.max()+1)}),ia({name:"thumb",defaults:A({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:sr([gr(j(),n,"spectrum"),gr(H(),n,"spectrum"),gr(z(),n,"spectrum")])}}}),ia({schema:[ur("mouseIsDown",function(){return ao(!1)})],name:"spectrum",overrides:function(r){var t=function(n,e){var t=n.element().dom().getBoundingClientRect();Qa(n,r,t,e)},n=sr([lr(j(),t),lr(H(),t)]),e=sr([lr(L(),t),lr(P(),function(n,e){r.mouseIsDown().get()&&t(n,e)})]);return{behaviours:jr(es?[]:[Bc.config({mode:"special",onLeft:function(n){return Za(n,r),F.some(!0)},onRight:function(n){return ns(n,r),F.some(!0)}}),mi.config({})]),events:es?n:e}}})],os=function(n,e,t){e.store().manager().onLoad(n,e,t)},is=function(n,e,t){e.store().manager().onUnload(n,e,t)},us=Object.freeze({onLoad:os,onUnload:is,setValue:function(n,e,t,r){e.store().manager().setValue(n,e,t,r)},getValue:function(n,e,t){return e.store().manager().getValue(n,e,t)}}),cs=Object.freeze({events:function(t,r){var n=t.resetOnDom()?[vr(function(n,e){os(n,t,r)}),hr(function(n,e){is(n,t,r)})]:[Cr(t,r,os)];return sr(n)}}),as=function(){var n=ao(null);return _r({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})},ss=function(){var n=ao({});return _r({readState:function(){return{mode:"dataset",dataset:n.get()}},set:n.set,get:n.get})},fs=Object.freeze({memory:as,dataset:ss,manual:function(){return _r({readState:function(){}})},init:function(n){return n.store().manager().state(n)}}),ls=function(n,e,t,r){e.store().getDataKey(),t.set({}),e.store().setData()(n,r),e.onSetValue()(n,r)},ds=[er("initialValue"),Jt("getFallbackEntry"),Jt("getDataKey"),Jt("setData"),Po("manager",{setValue:ls,getValue:function(n,e,t){var r=e.store().getDataKey()(n),o=t.get();return ht(o,r).fold(function(){return e.store().getFallbackEntry()(r)},function(n){return n})},onLoad:function(e,t,r){t.store().initialValue().each(function(n){ls(e,t,r,n)})},onUnload:function(n,e,t){t.set({})},state:ss})],ms=[Jt("getValue"),or("setValue",I),er("initialValue"),Po("manager",{setValue:function(n,e,t,r){e.store().setValue()(n,r),e.onSetValue()(n,r)},getValue:function(n,e,t){return e.store().getValue()(n)},onLoad:function(e,t,n){t.store().initialValue().each(function(n){t.store().setValue()(e,n)})},onUnload:I,state:Nr.init})],gs=[er("initialValue"),Po("manager",{setValue:function(n,e,t,r){t.set(r),e.onSetValue()(n,r)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store().initialValue().each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:as})],ps=[ir("store",{mode:"memory"},Yt("mode",{memory:gs,manual:ms,dataset:ds})),jo("onSetValue"),or("resetOnDom",!1)],vs=zr({fields:ps,name:"representing",active:cs,apis:us,extra:{setValueFrom:function(n,e){var t=vs.getValue(e);vs.setValue(n,t)}},state:fs}),hs=zn.detect().deviceType.isTouch(),ys=[Jt("min"),Jt("max"),or("stepSize",1),or("onChange",I),or("onInit",I),or("onDragStart",I),or("onDragEnd",I),or("snapToGrid",!1),er("snapStart"),Jt("getInitialValue"),Vc("sliderBehaviours",[Bc,vs]),ur("value",function(n){return ao(n.min)})].concat(hs?[]:[ur("mouseIsDown",function(){return ao(!1)})]),bs=Si("width",function(n){return n.dom().offsetWidth}),ws=function(n,e){bs.set(n,e)},xs=function(n){return bs.get(n)},Ts=zn.detect().deviceType.isTouch(),Ss=Va({name:"Slider",configFields:ys,partFields:rs,factory:function(a,n,e,t){var s=a.max()-a.min(),f=function(n){var e=n.element().dom().getBoundingClientRect();return(e.left+e.right)/2},o=function(n){return pa(n,a,"thumb")},i=function(n){var e,t,r,o,i=pa(n,a,"spectrum").element().dom().getBoundingClientRect(),u=n.element().dom().getBoundingClientRect(),c=(e=n,t=i,(o=(r=a).value().get())<r.min()?ga(e,r,"left-edge").fold(function(){return 0},function(n){return f(n)-t.left}):o>r.max()?ga(e,r,"right-edge").fold(function(){return t.width},function(n){return f(n)-t.left}):(r.value().get()-r.min())/s*t.width);return i.left-u.left+c},u=function(n){var e=i(n),t=o(n),r=xs(t.element())/2;vi(t.element(),"left",e-r+"px")},r=function(n,e){var t=a.value().get(),r=o(n);return t!==e||wi(r.element(),"left").isNone()?(a.value().set(e),u(n),a.onChange()(n,r,e),F.some(!0)):F.none()},c=Ts?[lr(j(),function(n,e){a.onDragStart()(n,o(n))}),lr(z(),function(n,e){a.onDragEnd()(n,o(n))})]:[lr(L(),function(n,e){e.stop(),a.onDragStart()(n,o(n)),a.mouseIsDown().set(!0)}),lr($(),function(n,e){a.onDragEnd()(n,o(n)),a.mouseIsDown().set(!1)})];return{uid:a.uid(),dom:a.dom(),components:n,behaviours:k(jr(kn([Ts?[]:[Bc.config({mode:"special",focusIn:function(n){return ga(n,a,"spectrum").map(Bc.focusIn).map(A(!0))}})],[vs.config({store:{mode:"manual",getValue:function(n){return a.value().get()}}})]])),Nc(a.sliderBehaviours())),events:sr([lr(Ya,function(n,e){r(n,e.event().value())}),vr(function(n,e){a.value().set(a.getInitialValue()());var t=o(n);u(n),a.onInit()(n,t,a.value().get())})].concat(c)),apis:{resetToMin:function(n){r(n,a.min())},resetToMax:function(n){r(n,a.max())},refresh:u},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),Os=function(e,t,r){return $a.forToolbar(t,function(){var n=r();e.setContextToolbar([{label:t+" group",items:n}])},{})},ks=function(n){return[(o=n,i=function(n){return n<0?"black":360<n?"white":"hsl("+n+", 100%, 50%)"},Ss.sketch({dom:Ha('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[Ss.parts()["left-edge"](za('<div class="${prefix}-hue-slider-black"></div>')),Ss.parts().spectrum({dom:Ha('<div class="${prefix}-slider-gradient-container"></div>'),components:[za('<div class="${prefix}-slider-gradient"></div>')],behaviours:jr([ri.config({toggleClass:ai.resolve("thumb-active")})])}),Ss.parts()["right-edge"](za('<div class="${prefix}-hue-slider-white"></div>')),Ss.parts().thumb({dom:Ha('<div class="${prefix}-slider-thumb"></div>'),behaviours:jr([ri.config({toggleClass:ai.resolve("thumb-active")})])})],onChange:function(n,e,t){var r=i(t);vi(e.element(),"background-color",r),o.onChange(n,e,r)},onDragStart:function(n,e){ri.on(e)},onDragEnd:function(n,e){ri.off(e)},onInit:function(n,e,t){var r=i(t);vi(e.element(),"background-color",r)},stepSize:10,min:0,max:360,getInitialValue:o.getInitialValue,sliderBehaviours:jr([ii(Ss.refresh)])}))];var o,i},Cs=function(n,r){var e={onChange:function(n,e,t){r.undoManager.transact(function(){r.formatter.apply("forecolor",{value:t}),r.nodeChanged()})},getInitialValue:function(){return-1}};return Os(n,"color",function(){return ks(e)})},Es=Rt([Jt("getInitialValue"),Jt("onChange"),Jt("category"),Jt("sizes")]),Ds=function(n){var o=Ut("SizeSlider",Es,n);return Ss.sketch({dom:{tag:"div",classes:[ai.resolve("slider-"+o.category+"-size-container"),ai.resolve("slider"),ai.resolve("slider-size-container")]},onChange:function(n,e,t){var r;0<=(r=t)&&r<o.sizes.length&&o.onChange(t)},onDragStart:function(n,e){ri.on(e)},onDragEnd:function(n,e){ri.off(e)},min:0,max:o.sizes.length-1,stepSize:1,getInitialValue:o.getInitialValue,snapToGrid:!0,sliderBehaviours:jr([ii(Ss.refresh)]),components:[Ss.parts().spectrum({dom:Ha('<div class="${prefix}-slider-size-container"></div>'),components:[za('<div class="${prefix}-slider-size-line"></div>')]}),Ss.parts().thumb({dom:Ha('<div class="${prefix}-slider-thumb"></div>'),behaviours:jr([ri.config({toggleClass:ai.resolve("thumb-active")})])})]})},Is=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],As=function(n){var e,t,r=n.selection.getStart(),o=ue.fromDom(r),i=ue.fromDom(n.getBody()),u=(e=function(n){return Re(i,n)},(pe(t=o)?F.some(t):Ne(t).filter(pe)).map(function(n){return lo(n,function(n){return wi(n,"font-size").isSome()},e).bind(function(n){return wi(n,"font-size")}).getOrThunk(function(){return yi(n,"font-size")})}).getOr(""));return Sn(Is,function(n){return u===n}).getOr("medium")},Ms={candidates:A(Is),get:function(n){var e,t=As(n);return(e=t,On(Is,function(n){return n===e})).getOr(2)},apply:function(r,n){var e;(e=n,F.from(Is[e])).each(function(n){var e,t;t=n,As(e=r)!==t&&e.execCommand("fontSize",!1,t)})}},Fs=Ms.candidates(),Rs=function(n){return[za('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),(e=n,Ds({onChange:e.onChange,sizes:Fs,category:"font",getInitialValue:e.getInitialValue})),za('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')];var e},Bs=function(n){var e=n.uid!==undefined&&xt(n,"uid")?n.uid:Da("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).fold(F.none,F.some)},asSpec:function(){return k(n,{uid:e})}}},Vs=window.Promise?window.Promise:function(){var i=function(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],f(n,r(o,this),r(c,this))},n=i.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){p.setTimeout(n,1)};function r(n,e){return function(){return n.apply(e,arguments)}}var t=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)};function u(r){var o=this;null!==this._state?n(function(){var n=o._state?r.onFulfilled:r.onRejected;if(null!==n){var e;try{e=n(o._value)}catch(t){return void r.reject(t)}r.resolve(e)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function o(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void f(r(e,n),r(o,this),r(c,this))}this._state=!0,this._value=n,a.call(this)}catch(t){c.call(this,t)}}function c(n){this._state=!1,this._value=n,a.call(this)}function a(){for(var n=0,e=this._deferreds;n<e.length;n++){var t=e[n];u.call(this,t)}this._deferreds=[]}function s(n,e,t,r){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.resolve=t,this.reject=r}function f(n,e,t){var r=!1;try{n(function(n){r||(r=!0,e(n))},function(n){r||(r=!0,t(n))})}catch(o){if(r)return;r=!0,t(o)}}return i.prototype["catch"]=function(n){return this.then(null,n)},i.prototype.then=function(t,r){var o=this;return new i(function(n,e){u.call(o,new s(t,r,n,e))})},i.all=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var a=Array.prototype.slice.call(1===n.length&&t(n[0])?n[0]:n);return new i(function(o,i){if(0===a.length)return o([]);var u=a.length;function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}for(var n=0;n<a.length;n++)c(n,a[n])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(n){n(e)})},i.reject=function(t){return new i(function(n,e){e(t)})},i.race=function(o){return new i(function(n,e){for(var t=0,r=o;t<r.length;t++)r[t].then(n,e)})},i}();function Ns(n){return(t=n,new Vs(function(n){var e=new p.FileReader;e.onloadend=function(){n(e.result)},e.readAsDataURL(t)})).then(function(n){return n.split(",")[1]});var t}var _s,js,Hs,zs,Ls,Ps,$s=function(o,i){var n;(n=i,Ns(n)).then(function(r){o.undoManager.transact(function(){var n=o.editorUpload.blobCache,e=n.create(Gc("mceu"),i,r);n.add(e);var t=o.dom.createHTML("img",{src:e.blobUri()});o.insertContent(t)})})},Ws=function(i){var e=Bs({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:sr([pr(Y()),lr(q(),function(n,e){var t,r,o;(t=e,r=t.event(),o=r.raw().target.files||r.raw().dataTransfer.files,F.from(o[0])).each(function(n){$s(i,n)})})])});return Na.sketch({dom:Ha('<span class="${prefix}-toolbar-button ${prefix}-icon-image ${prefix}-icon"></span>'),components:[e.asSpec()],action:function(n){e.get(n).element().dom().click()}})},Us=function(n){return n.dom().textContent},Gs=function(n){return 0<n.length},qs=function(n){return n===undefined||null===n?"":n},Ys=function(e,t,n){return n.text.filter(Gs).fold(function(){return Gr(n=e,"href")===Us(n)?F.some(t):F.none();var n},F.some)},Ks=function(n){var e=ue.fromDom(n.selection.getStart());return Bi(e,"a")},Xs={getInfo:function(n){return Ks(n).fold(function(){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:F.none()}},function(n){return t=Us(e=n),r=Gr(e,"href"),o=Gr(e,"title"),i=Gr(e,"target"),{url:qs(r),text:t!==r?qs(t):"",title:qs(o),target:qs(i),link:F.some(e)};var e,t,r,o,i})},applyInfo:function(o,i){i.url.filter(Gs).fold(function(){var e;e=o,i.link.bind(h).each(function(n){e.execCommand("unlink")})},function(e){var n,t,r=(n=i,(t={}).href=e,n.title.filter(Gs).each(function(n){t.title=n}),n.target.filter(Gs).each(function(n){t.target=n}),t);i.link.bind(h).fold(function(){var n=i.text.filter(Gs).getOr(e);o.insertContent(o.dom.createHTML("a",r,o.dom.encode(n)))},function(t){var n=Ys(t,e,i);Ur(t,r),n.each(function(n){var e;e=n,t.dom().textContent=e})})})},query:Ks},Js=zn.detect(),Qs=function(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)},Zs=function(n,e){(Js.os.isAndroid()?Qs:a)(e,n)},nf=function(n,e){var t,r;return{key:n,value:{config:{},me:(t=n,r=sr(e),zr({fields:[Jt("enabled")],name:t,active:{events:A(r)}})),configAsRaw:A({}),initialConfig:{},state:Nr}}},ef=Object.freeze({getCurrent:function(n,e,t){return e.find()(n)}}),tf=[Jt("find")],rf=zr({fields:tf,name:"composing",apis:ef}),of=Ba({name:"Container",factory:function(n){return{uid:n.uid(),dom:k({tag:"div",attributes:{role:"presentation"}},n.dom()),components:n.components(),behaviours:Nc(n.containerBehaviours()),events:n.events(),domModification:n.domModification(),eventOrder:n.eventOrder()}},configFields:[or("components",[]),Vc("containerBehaviours",[]),or("events",{}),or("domModification",{}),or("eventOrder",{})]}),uf=Ba({name:"DataField",factory:function(t){return{uid:t.uid(),dom:t.dom(),behaviours:k(jr([vs.config({store:{mode:"memory",initialValue:t.getInitialValue()()}}),rf.config({find:F.some})]),Nc(t.dataBehaviours())),events:sr([vr(function(n,e){vs.setValue(n,t.getInitialValue()())})])}},configFields:[Jt("uid"),Jt("dom"),Jt("getInitialValue"),Vc("dataBehaviours",[vs,rf])]}),cf=function(n){return n.dom().value},af=function(n,e){if(e===undefined)throw new Error("Value.set was undefined");n.dom().value=e},sf=A([er("data"),or("inputAttributes",{}),or("inputStyles",{}),or("type","input"),or("tag","input"),or("inputClasses",[]),jo("onSetValue"),or("styles",{}),er("placeholder"),or("eventOrder",{}),Vc("inputBehaviours",[vs,mi]),or("selectOnFocus",!0)]),ff=function(n){return k(jr([vs.config({store:{mode:"manual",initialValue:n.data().getOr(undefined),getValue:function(n){return cf(n.element())},setValue:function(n,e){cf(n.element())!==e&&af(n.element(),e)}},onSetValue:n.onSetValue()})]),(e=n,jr([mi.config({onFocus:!1===e.selectOnFocus()?I:function(n){var e=n.element(),t=cf(e);e.dom().setSelectionRange(0,t.length)}})])),Nc(n.inputBehaviours()));var e},lf=Ba({name:"Input",configFields:sf(),factory:function(n,e){return{uid:n.uid(),dom:(t=n,{tag:t.tag(),attributes:k(bt([{key:"type",value:t.type()}].concat(t.placeholder().map(function(n){return{key:"placeholder",value:n}}).toArray())),t.inputAttributes()),styles:t.inputStyles(),classes:t.inputClasses()}),components:[],behaviours:ff(n),eventOrder:n.eventOrder()};var t}}),df=Object.freeze({exhibit:function(n,e){return Or({attributes:bt([{key:e.tabAttr(),value:"true"}])})}}),mf=[or("tabAttr","data-alloy-tabstop")],gf=zr({fields:mf,name:"tabstopping",active:df}),pf=function(n,e){var t=Bs(lf.sketch({placeholder:e,onSetValue:function(n,e){ne(n,G())},inputBehaviours:jr([rf.config({find:F.some}),gf.config({}),Bc.config({mode:"execution"})]),selectOnFocus:!1})),r=Bs(Na.sketch({dom:Ha('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);vs.setValue(e,"")}}));return{name:n,spec:of.sketch({dom:Ha('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),r.asSpec()],containerBehaviours:jr([ri.config({toggleClass:ai.resolve("input-container-empty")}),rf.config({find:function(n){return F.some(t.get(n))}}),nf("input-clearing",[lr(G(),function(n){var e=t.get(n);(0<vs.getValue(e).length?ri.off:ri.on)(n)})])])})}},vf=["input","button","textarea"],hf=function(n,e,t){e.disabled()&&Sf(n,e)},yf=function(n){return hn(vf,me(n.element()))},bf=function(n){Wr(n.element(),"disabled","disabled")},wf=function(n){Yr(n.element(),"disabled")},xf=function(n){Wr(n.element(),"aria-disabled","true")},Tf=function(n){Wr(n.element(),"aria-disabled","false")},Sf=function(e,n,t){n.disableClass().each(function(n){no(e.element(),n)}),(yf(e)?bf:xf)(e)},Of=function(n){return yf(n)?qr(n.element(),"disabled"):"true"===Gr(n.element(),"aria-disabled")},kf=Object.freeze({enable:function(e,n,t){n.disableClass().each(function(n){eo(e.element(),n)}),(yf(e)?wf:Tf)(e)},disable:Sf,isDisabled:Of,onLoad:hf}),Cf=Object.freeze({exhibit:function(n,e,t){return Or({classes:e.disabled()?e.disableClass().map(In).getOr([]):[]})},events:function(n,e){return sr([fr(Un(),function(n,e){return Of(n)}),Cr(n,e,hf)])}}),Ef=[or("disabled",!1),er("disableClass")],Df=zr({fields:Ef,name:"disabling",active:Cf,apis:kf}),If=[Vc("formBehaviours",[vs])],Af=function(n){return"<alloy.field."+n+">"},Mf=function(o,n,e){return k({"debug.sketcher":{Form:e},uid:o.uid(),dom:o.dom(),components:n,behaviours:k(jr([vs.config({store:{mode:"manual",getValue:function(n){var e,t,r=(e=o,t=n.getSystem(),V(e.partUids(),function(n,e){return A(t.getByUid(n))}));return V(r,function(n,e){return n().bind(rf.getCurrent).map(vs.getValue)})},setValue:function(t,n){B(n,function(e,n){ga(t,o,n).each(function(n){rf.getCurrent(n).each(function(n){vs.setValue(n,e)})})})}}})]),Nc(o.formBehaviours())),apis:{getField:function(n,e){return ga(n,o,e).bind(rf.getCurrent)}}})},Ff=(xa(function(n,e,t){return n.getField(e,t)}),function(n){var i,e=(i=[],{field:function(n,e){return i.push(n),t="form",r=Af(n),o=e,{uiType:Wc(),owner:t,name:r,config:o,validated:{}};var t,r,o},record:function(){return i}}),t=n(e),r=e.record(),o=yn(r,function(n){return ia({name:n,pname:Af(n)})});return Aa("form",If,o,Mf,t)}),Rf=function(){var e=ao(F.none()),t=function(){e.get().each(function(n){n.destroy()})};return{clear:function(){t(),e.set(F.none())},isSet:function(){return e.get().isSome()},set:function(n){t(),e.set(F.some(n))},run:function(n){e.get().each(n)}}},Bf=function(){var e=ao(F.none());return{clear:function(){e.set(F.none())},set:function(n){e.set(F.some(n))},isSet:function(){return e.get().isSome()},on:function(n){e.get().each(n)}}},Vf=function(n){return{xValue:n,points:[]}},Nf=function(n,e){if(e===n.xValue)return n;var t=0<e-n.xValue?1:-1,r={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([r])}},_f=function(n){if(0===n.points.length)return 0;var e=n.points[0].direction,t=n.points[n.points.length-1].direction;return-1===e&&-1===t?-1:1===e&&1===t?1:0},jf=function(n){var r="navigateEvent",e=Bt([Jt("fields"),or("maxFieldIndex",n.fields.length-1),Jt("onExecute"),Jt("getInitialValue"),ur("state",function(){return{dialogSwipeState:Bf(),currentScreen:ao(0)}})]),u=Ut("SerialisedDialog",e,n),o=function(e,n,t){return Na.sketch({dom:Ha('<span class="${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:function(n){ee(n,r,{direction:e})},buttonBehaviours:jr([Df.config({disableClass:ai.resolve("toolbar-navigation-disabled"),disabled:!t})])})},i=function(n,o){var i=Ai(n.element(),"."+ai.resolve("serialised-dialog-screen"));Ri(n.element(),"."+ai.resolve("serialised-dialog-chain")).each(function(r){0<=u.state.currentScreen.get()+o&&u.state.currentScreen.get()+o<i.length&&(wi(r,"left").each(function(n){var e=parseInt(n,10),t=xs(i[0]);vi(r,"left",e-o*t+"px")}),u.state.currentScreen.set(u.state.currentScreen.get()+o))})},c=function(r){var n=Ai(r.element(),"input");F.from(n[u.state.currentScreen.get()]).each(function(n){r.getSystem().getByDom(n).each(function(n){var e,t;e=r,t=n.element(),e.getSystem().triggerFocus(t,e.element())})});var e=s.get(r);tu.highlightAt(e,u.state.currentScreen.get())},a=Bs(Ff(function(t){return{dom:Ha('<div class="${prefix}-serialised-dialog"></div>'),components:[of.sketch({dom:Ha('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:yn(u.fields,function(n,e){return e<=u.maxFieldIndex?of.sketch({dom:Ha('<div class="${prefix}-serialised-dialog-screen"></div>'),components:kn([[o(-1,"previous",0<e)],[t.field(n.name,n.spec)],[o(1,"next",e<u.maxFieldIndex)]])}):t.field(n.name,n.spec)})})],formBehaviours:jr([ii(function(n,e){var t;t=e,Ri(n.element(),"."+ai.resolve("serialised-dialog-chain")).each(function(n){vi(n,"left",-u.state.currentScreen.get()*t.width+"px")})}),Bc.config({mode:"special",focusIn:function(n){c(n)},onTab:function(n){return i(n,1),F.some(!0)},onShiftTab:function(n){return i(n,-1),F.some(!0)}}),nf("form-events",[vr(function(e,n){u.state.currentScreen.set(0),u.state.dialogSwipeState.clear();var t=s.get(e);tu.highlightFirst(t),u.getInitialValue(e).each(function(n){vs.setValue(e,n)})}),br(u.onExecute),lr(K(),function(n,e){"left"===e.event().raw().propertyName&&c(n)}),lr(r,function(n,e){var t=e.event().direction();i(n,t)})])])}})),s=Bs({dom:Ha('<div class="${prefix}-dot-container"></div>'),behaviours:jr([tu.config({highlightClass:ai.resolve("dot-active"),itemClass:ai.resolve("dot-item")})]),components:Cn(u.fields,function(n,e){return e<=u.maxFieldIndex?[za('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:Ha('<div class="${prefix}-serializer-wrapper"></div>'),components:[a.asSpec(),s.asSpec()],behaviours:jr([Bc.config({mode:"special",focusIn:function(n){var e=a.get(n);Bc.focusIn(e)}}),nf("serializer-wrapper-events",[lr(j(),function(n,e){var t=e.event();u.state.dialogSwipeState.set(Vf(t.touches[0].clientX))}),lr(H(),function(n,e){var t=e.event();u.state.dialogSwipeState.on(function(n){e.event().prevent(),u.state.dialogSwipeState.set(Nf(n,t.raw().touches[0].clientX))})}),lr(z(),function(r){u.state.dialogSwipeState.on(function(n){var e=a.get(r),t=-1*_f(n);i(e,t)})})])])}},Hf=J(function(t,r){return[{label:"the link group",items:[jf({fields:[pf("url","Type or paste URL"),pf("text","Link text"),pf("title","Link title"),pf("target","Link target"),(n="link",{name:n,spec:uf.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return F.none()}})})],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return F.some(Xs.getInfo(r))},onExecute:function(n){var e=vs.getValue(n);Xs.applyInfo(r,e),t.restoreToolbar(),r.focus()}})]}];var n}),zf=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],Lf=sr([(_s=Pn(),js=function(n,e){var t,r,o=e.event().originator(),i=e.event().target();return r=i,!(Re(t=o,n.element())&&!Re(t,r)&&(p.console.warn(Pn()+" did not get interpreted by the desired target. \nOriginator: "+ko(o)+"\nTarget: "+ko(i)+"\nCheck the "+Pn()+" event handlers"),1))},{key:_s,value:cr({can:js})})]),Pf=Object.freeze({events:Lf}),$f=h,Wf=Vr(["debugInfo","triggerFocus","triggerEvent","triggerEscape","addToWorld","removeFromWorld","addToGui","removeFromGui","build","getByUid","getByDom","broadcast","broadcastOn","isConnected"]),Uf=function(e){var n=function(n){return function(){throw new Error("The component must be in a context to send: "+n+"\n"+ko(e().element())+" is not in context.")}};return Wf({debugInfo:A("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),isConnected:A(!1)})},Gf=function(n,o){var i={};return B(n,function(n,r){B(n,function(n,e){var t=vt(e,[])(i);i[e]=t.concat([o(r,n)])})}),i},qf=function(n,e){return 1<n.length?Qe.error('Multiple behaviours have tried to change DOM "'+e+'". The guilty behaviours are: '+kt(yn(n,function(n){return n.name()}))+". At this stage, this is not supported. Future releases might provide strategies for resolving this."):0===n.length?Qe.value({}):Qe.value(n[0].modification().fold(function(){return{}},function(n){return yt(e,n)}))},Yf=function(u,c){return Tn(u,function(n,e){var t=e.modification().getOr({});return n.bind(function(i){var n=_(t,function(n,e){return i[e]!==undefined?(t=c,r=e,o=u,Qe.error("Mulitple behaviours have tried to change the _"+r+'_ "'+t+'". The guilty behaviours are: '+kt(Cn(o,function(n){return n.modification().getOr({})[r]!==undefined?[n.name()]:[]}),null,2)+". This is not currently supported.")):Qe.value(yt(e,n));var t,r,o});return wt(n,i)})},Qe.value({})).map(function(n){return yt(c,n)})},Kf={classes:function(n,e){var t=Cn(n,function(n){return n.modification().getOr([])});return Qe.value(yt(e,t))},attributes:Yf,styles:Yf,domChildren:qf,defChildren:qf,innerHtml:qf,value:qf},Xf=function(n,e){return t=l.apply(undefined,[n.handler].concat(e)),r=n.purpose(),{cHandler:t,purpose:A(r)};var t,r},Jf=function(n){return n.cHandler},Qf=function(n,e){return{name:A(n),handler:A(e)}},Zf=function(n,e,t){var r,o,i=k(t,(r=n,o={},bn(e,function(n){o[n.name()]=n.handlers(r)}),o));return Gf(i,Qf)},nl=function(n){var e,i=w(e=n)?{can:A(!0),abort:A(!1),run:e}:e;return function(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];var o=[n,e].concat(t);i.abort.apply(undefined,o)?e.stop():i.can.apply(undefined,o)&&i.run.apply(undefined,o)}},el=function(n,e,t){var r,o,i=e[t];return i?function(u,c,n,a){var e=n.slice(0);try{var t=e.sort(function(n,e){var t=n[c](),r=e[c](),o=a.indexOf(t),i=a.indexOf(r);if(-1===o)throw new Error("The ordering for "+u+" does not have an entry for "+t+".\nOrder specified: "+kt(a,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+r+".\nOrder specified: "+kt(a,null,2));return o<i?-1:i<o?1:0});return Qe.value(t)}catch(r){return Qe.error([r])}}("Event: "+t,"name",n,i).map(function(n){var e=yn(n,function(n){return n.handler()});return ar(e)}):(r=t,o=n,Qe.error(["The event ("+r+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+kt(yn(o,function(n){return n.name()}),null,2)]))},tl=function(n,i){var e=_(n,function(r,o){return(1===r.length?Qe.value(r[0].handler()):el(r,i,o)).map(function(n){var e=nl(n),t=1<r.length?wn(i,function(e){return hn(r,function(n){return n.name()===e})}).join(" > "):r[0].name();return yt(o,{handler:e,purpose:A(t)})})});return wt(e,{})},rl=function(n){return $t("custom.definition",Rt([jt("dom","dom",tt(),Rt([Jt("tag"),or("styles",{}),or("classes",[]),or("attributes",{}),er("value"),er("innerHtml")])),Jt("components"),Jt("uid"),or("events",{}),or("apis",A({})),jt("eventOrder","eventOrder",(e={"alloy.execute":["disabling","alloy.base.behaviour","toggling"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing"]},nt.mergeWithThunk(A(e))),Kt()),er("domModification"),$o("originalSpec"),or("debug.sketcher","unknown")]),n);var e},ol=function(n){var e,t={tag:n.dom().tag(),classes:n.dom().classes(),attributes:k((e=n,yt(Oa(),e.uid())),n.dom().attributes()),styles:n.dom().styles(),domChildren:yn(n.components(),function(n){return n.element()})};return Tr(k(t,n.dom().innerHtml().map(function(n){return yt("innerHtml",n)}).getOr({}),n.dom().value().map(function(n){return yt("value",n)}).getOr({})))},il=function(e,n){bn(n,function(n){no(e,n)})},ul=function(e,n){bn(n,function(n){eo(e,n)})},cl=function(e){if(e.domChildren().isSome()&&e.defChildren().isSome())throw new Error("Cannot specify children and child specs! Must be one or the other.\nDef: "+(n=Sr(e),kt(n,null,2)));return e.domChildren().fold(function(){var n=e.defChildren().getOr([]);return yn(n,sl)},function(n){return n});var n},al=function(n){var e=ue.fromTag(n.tag());Ur(e,n.attributes().getOr({})),il(e,n.classes().getOr([])),hi(e,n.styles().getOr({})),To(e,n.innerHtml().getOr(""));var t=cl(n);return Le(e,t),n.value().each(function(n){af(e,n)}),e},sl=function(n){var e=Tr(n);return al(e)},fl=function(n,e){return t=n,o=yn(r=e,function(n){return tr(n.name(),[Jt("config"),or("state",Nr)])}),i=$t("component.behaviours",Bt(o),t.behaviours).fold(function(n){throw new Error(qt(n)+"\nComplete spec:\n"+kt(t,null,2))},function(n){return n}),{list:r,data:V(i,function(n){var e=n().map(function(n){return{config:n.config(),state:n.state().init(n.config())}});return function(){return e}})};var t,r,o,i},ll=function(n){var e,t,r=(e=ht(n,"behaviours").getOr({}),t=wn(R(e),function(n){return e[n]!==undefined}),yn(t,function(n){return e[n].me}));return fl(n,r)},dl=Vr(["getSystem","config","hasConfigured","spec","connect","disconnect","element","syncComponents","readState","components","events"]),ml=function(n,e,t){var r,o,i,u,c=ol(n),a=function(e,n,t,r){var o=k({},n);bn(t,function(n){o[n.name()]=n.exhibit(e,r)});var i=Gf(o,function(n,e){return{name:function(){return n},modification:e}}),u=V(i,function(n,e){return Cn(n,function(e){return e.modification().fold(function(){return[]},function(n){return[e]})})}),c=_(u,function(e,t){return ht(Kf,t).fold(function(){return Qe.error("Unknown field type: "+t)},function(n){return n(e,t)})});return wt(c,{}).map(Or)}(t,{"alloy.base.modification":(r=n,r.domModification().fold(function(){return Or({})},Or))},e,c).getOrDie();return i=a,u=k({tag:(o=c).tag(),classes:i.classes().getOr([]).concat(o.classes().getOr([])),attributes:C(o.attributes().getOr({}),i.attributes().getOr({})),styles:C(o.styles().getOr({}),i.styles().getOr({}))},i.innerHtml().or(o.innerHtml()).map(function(n){return yt("innerHtml",n)}).getOr({}),kr("domChildren",i.domChildren(),o.domChildren()),kr("defChildren",i.defChildren(),o.defChildren()),i.value().or(o.value()).map(function(n){return yt("value",n)}).getOr({})),Tr(u)},gl=function(n,e,t){var r,o,i,u,c,a,s={"alloy.base.behaviour":(r=n,r.events())};return(o=t,i=n.eventOrder(),u=e,c=s,a=Zf(o,u,c),tl(a,i)).getOrDie()},pl=function(n){var e,t,r,o,i,u,c,a,s,f,l,d,m,g,p=$f(n),v=(e=p,t=vt("components",[])(e),yn(t,yl)),h=k(Pf,p,yt("components",v));return Qe.value((r=h,i=ao(Uf(o=function(){return g})),u=Wt(rl(k(r,{behaviours:undefined}))),c=ll(r),a=c.list,s=c.data,f=ml(u,a,s),l=al(f),d=gl(u,a,s),m=ao(u.components()),g=dl({getSystem:i.get,config:function(n){if(n===Ta())return u.apis();if(y(n))throw new Error("Invalid input: only API constant is allowed");var e=s;return(w(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+kt(r,null,2))})()},hasConfigured:function(n){return w(s[n.name()])},spec:A(r),readState:function(n){return s[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},connect:function(n){i.set(n)},disconnect:function(){i.set(Uf(o))},element:A(l),syncComponents:function(){var n=_e(l),e=Cn(n,function(n){return i.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});m.set(e)},components:m.get,events:A(d)})))},vl=function(n){var e=ue.fromText(n);return hl({element:e})},hl=function(n){var t=Gt("external.component",Rt([Jt("element"),er("uid")]),n),e=ao(Uf());t.uid().each(function(n){var e;e=t.element(),Wr(e,Ca,n)});var r=dl({getSystem:e.get,config:F.none,hasConfigured:A(!1),connect:function(n){e.set(n)},disconnect:function(){e.set(Uf(function(){return r}))},element:A(t.element()),spec:A(n),readState:A("No state"),syncComponents:I,components:A([]),events:A({})});return wa(r)},yl=function(e){return(n=e,ht(n,ya)).fold(function(){var n=k({uid:Da("")},e);return pl(n).getOrDie()},function(n){return n});var n},bl=wa,wl="alloy.item-hover",xl="alloy.item-focus",Tl=function(n){(vo(n.element()).isNone()||mi.isFocused(n))&&(mi.isFocused(n)||mi.focus(n),ee(n,wl,{item:n}))},Sl=function(n){ee(n,xl,{item:n})},Ol=A(wl),kl=A(xl),Cl=[Jt("data"),Jt("components"),Jt("dom"),er("toggling"),or("itemBehaviours",{}),or("ignoreFocus",!1),or("domModification",{}),Po("builder",function(n){return{dom:k(n.dom(),{attributes:{role:n.toggling().isSome()?"menuitemcheckbox":"menuitem"}}),behaviours:k(jr([n.toggling().fold(ri.revoke,function(n){return ri.config(k({aria:{mode:"checked"}},n))}),mi.config({ignore:n.ignoreFocus(),onFocus:function(n){Sl(n)}}),Bc.config({mode:"execution"}),vs.config({store:{mode:"memory",initialValue:n.data()}})]),n.itemBehaviours()),events:sr([(e=Yn(),r=te,lr(e,function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){r(e,n,t)})})),pr(L()),lr(W(),Tl),lr(Gn(),mi.focus)]),components:n.components(),domModification:n.domModification(),eventOrder:n.eventOrder()};var e,r}),or("eventOrder",{})],El=[Jt("dom"),Jt("components"),Po("builder",function(n){return{dom:n.dom(),components:n.components(),events:sr([(e=Gn(),lr(e,function(n,e){e.stop()}))])};var e})],Dl=A([ia({name:"widget",overrides:function(e){return{behaviours:jr([vs.config({store:{mode:"manual",getValue:function(n){return e.data()},setValue:function(){}}})])}}})]),Il=[Jt("uid"),Jt("data"),Jt("components"),Jt("dom"),or("autofocus",!1),or("domModification",{}),ha(Dl()),Po("builder",function(t){var n=da(0,t,Dl()),e=ma("item-widget",t,n.internals()),r=function(n){return ga(n,t,"widget").map(function(n){return Bc.focusIn(n),n})},o=function(n,e){return au(e.event().target())||t.autofocus()&&e.setSource(n.element()),F.none()};return k({dom:t.dom(),components:e,domModification:t.domModification(),events:sr([br(function(n,e){r(n).each(function(n){e.stop()})}),lr(W(),Tl),lr(Gn(),function(n,e){t.autofocus()?r(n):mi.focus(n)})]),behaviours:jr([vs.config({store:{mode:"memory",initialValue:t.data()}}),mi.config({onFocus:function(n){Sl(n)}}),Bc.config({mode:"special",focusIn:t.autofocus()?function(n){r(n)}:Pr(),onLeft:o,onRight:o,onEscape:function(n,e){return mi.isFocused(n)||t.autofocus()?(t.autofocus()&&e.setSource(n.element()),F.none()):(mi.focus(n),F.some(!0))}})])})})],Al=Yt("type",{widget:Il,item:Cl,separator:El}),Ml=A([ca({factory:{sketch:function(n){var e=Gt("menu.spec item",Al,n);return e.builder()(e)}},name:"items",unit:"item",defaults:function(n,e){var t=Da("");return k({uid:t},e)},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus(),domModification:{classes:[n.markers().item()]}}}})]),Fl=A([Jt("value"),Jt("items"),Jt("dom"),Jt("components"),or("eventOrder",{}),Vc("menuBehaviours",[tu,vs,rf,Bc]),ir("movement",{mode:"menu",moveOnTab:!0},Yt("mode",{grid:[Wo(),Po("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers().item(),initSize:{numColumns:e.initSize().numColumns(),numRows:e.initSize().numRows()},focusManager:n.focusManager()}})],menu:[or("moveOnTab",!0),Po("config",function(n,e){return{mode:"menu",selector:"."+n.markers().item(),moveOnTab:e.moveOnTab(),focusManager:n.focusManager()}})]})),Qt("markers",Vo()),or("fakeFocus",!1),or("focusManager",ru()),jo("onHighlight")]),Rl=A("alloy.menu-focus"),Bl=Va({name:"Menu",configFields:Fl(),partFields:Ml(),factory:function(n,e,t,r){return k({dom:k(n.dom(),{attributes:{role:"menu"}}),uid:n.uid(),behaviours:k(jr([tu.config({highlightClass:n.markers().selectedItem(),itemClass:n.markers().item(),onHighlight:n.onHighlight()}),vs.config({store:{mode:"memory",initialValue:n.value()}}),rf.config({find:F.some}),Bc.config(n.movement().config()(n,n.movement()))]),Nc(n.menuBehaviours())),events:sr([lr(kl(),function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){tu.highlight(e,n),t.stop(),ee(e,Rl(),{menu:e,item:n})})}),lr(Ol(),function(n,e){var t=e.event().item();tu.highlight(n,t)})]),components:e,eventOrder:n.eventOrder()})}}),Vl=function(n,t){var r=Be(t),e=po(r).bind(function(e){var o,i,n=function(n){return Re(e,n)};return n(t)?F.some(t):(o=n,(i=function(n){for(var e=0;e<n.childNodes.length;e++){var t=ue.fromDom(n.childNodes[e]);if(o(t))return F.some(t);var r=i(n.childNodes[e]);if(r.isSome())return r}return F.none()})(t.dom()))}),o=n(t);return e.each(function(e){po(r).filter(function(n){return Re(n,e)}).fold(function(){mo(e)},I)}),o},Nl=function(n,e,t,r){var o=n.getSystem().build(r);qe(n,o,t)},_l=function(n,e){return n.components()},jl=zr({fields:[],name:"replacing",apis:Object.freeze({append:function(n,e,t,r){Nl(n,0,ze,r)},prepend:function(n,e,t,r){Nl(n,0,He,r)},remove:function(n,e,t,r){var o=_l(n);Sn(o,function(n){return Re(r.element(),n.element())}).each(Ke)},set:function(e,n,t,r){var o,i;i=(o=e).components(),bn(i,Ye),Pe(o.element()),o.syncComponents(),Vl(function(){var n=yn(r,e.getSystem().build);bn(n,function(n){Ge(e,n)})},e.element())},contents:_l})}),Hl=function(t,r,o,n){return ht(o,n).bind(function(n){return ht(t,n).bind(function(n){var e=Hl(t,r,o,n);return F.some([n].concat(e))})}).getOr([])},zl=function(n,e){var t={};B(n,function(n,e){bn(n,function(n){t[n]=e})});var r=e,o=N(e,function(n,e){return{k:n,v:e}}),i=V(o,function(n,e){return[e].concat(Hl(t,r,o,e))});return V(t,function(n){return ht(i,n).getOr([n])})},Ll=function(){var i=ao({}),u=ao({}),c=ao({}),a=ao(F.none()),s=ao({}),n=function(n){return ht(u.get(),n)};return{setContents:function(n,e,t,r){a.set(F.some(n)),i.set(t),u.set(e),s.set(r);var o=zl(r,t);c.set(o)},expand:function(t){return ht(i.get(),t).map(function(n){var e=ht(c.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return ht(c.get(),n)},collapse:function(n){return ht(c.get(),n).bind(function(n){return 1<n.length?F.some(n.slice(1)):F.none()})},lookupMenu:n,otherMenus:function(n){var e,t,r=s.get();return e=R(r),t=n,wn(e,function(n){return!hn(t,n)})},getPrimary:function(){return a.get().bind(n)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),c.set({}),a.set(F.none())},isClear:function(){return a.get().isNone()}}},Pl=A("collapse-item"),$l=Ba({name:"TieredMenu",configFields:[Lo("onExecute"),Lo("onEscape"),zo("onOpenMenu"),zo("onOpenSubmenu"),jo("onCollapseMenu"),or("openImmediately",!0),nr("data",[Jt("primary"),Jt("menus"),Jt("expansions")]),or("fakeFocus",!1),jo("onHighlight"),jo("onHover"),nr("markers",[Jt("backgroundMenu")].concat(Ro()).concat(Bo())),Jt("dom"),or("navigateOnHover",!0),or("stayInDom",!1),Vc("tmenuBehaviours",[Bc,tu,rf,jl]),or("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)}},factory:function(u,o){var i=function(r,n){return V(n,function(n,e){var t=Bl.sketch(k(n,{value:e,items:n.items,markers:mt(o.markers,["item","selectedItem"]),fakeFocus:u.fakeFocus(),onHighlight:u.onHighlight(),focusManager:u.fakeFocus()?{get:function(n){return tu.getHighlighted(n).map(function(n){return n.element()})},set:function(e,n){e.getSystem().getByDom(n).fold(I,function(n){tu.highlight(e,n)})}}:ru()}));return r.getSystem().build(t)})},c=Ll(),a=function(n){return vs.getValue(n).value},s=function(n){return V(u.data().menus(),function(n,e){return Cn(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})},f=function(e,n){tu.highlight(e,n),tu.getHighlighted(n).orThunk(function(){return tu.getFirst(n)}).each(function(n){re(e,n.element(),Gn())})},l=function(n,e){return Eo(yn(e,n.lookupMenu))},d=function(r,o,i){return F.from(i[0]).bind(o.lookupMenu).map(function(n){var e=l(o,i.slice(1));bn(e,function(n){no(n.element(),u.markers().backgroundMenu())}),he(n.element())||jl.append(r,bl(n)),ul(n.element(),[u.markers().backgroundMenu()]),f(r,n);var t=l(o,o.otherMenus(i));return bn(t,function(n){ul(n.element(),[u.markers().backgroundMenu()]),u.stayInDom()||jl.remove(r,n)}),n})},m=function(e,t){var n=a(t);return c.expand(n).bind(function(n){return F.from(n[0]).bind(c.lookupMenu).each(function(n){he(n.element())||jl.append(e,bl(n)),u.onOpenSubmenu()(e,t,n),tu.highlightFirst(n)}),d(e,c,n)})},r=function(e,t){var n=a(t);return c.collapse(n).bind(function(n){return d(e,c,n).map(function(n){return u.onCollapseMenu()(e,t,n),n})})},n=function(t){return function(e,n){return Bi(n.getSource(),"."+u.markers().item()).bind(function(n){return e.getSystem().getByDom(n).toOption().bind(function(n){return t(e,n).map(function(){return!0})})})}},e=sr([lr(Rl(),function(n,e){var t=e.event().menu();tu.highlight(n,t)}),br(function(e,n){var t=n.event().target();e.getSystem().getByDom(t).each(function(n){0===a(n).indexOf("collapse-item")&&r(e,n),m(e,n).fold(function(){u.onExecute()(e,n)},function(){})})}),vr(function(e,n){var t,r,o;(t=e,r=i(t,u.data().menus()),o=s(),c.setContents(u.data().primary(),r,u.data().expansions(),o),c.getPrimary()).each(function(n){jl.append(e,bl(n)),u.openImmediately()&&(f(e,n),u.onOpenMenu()(e,n))})})].concat(u.navigateOnHover()?[lr(Ol(),function(n,e){var t,r,o=e.event().item();t=n,r=a(o),c.refresh(r).bind(function(n){return d(t,c,n)}),m(n,o),u.onHover()(n,o)})]:[]));return{uid:u.uid(),dom:u.dom(),behaviours:k(jr([Bc.config({mode:"special",onRight:n(function(n,e){return au(e.element())?F.none():m(n,e)}),onLeft:n(function(n,e){return au(e.element())?F.none():r(n,e)}),onEscape:n(function(n,e){return r(n,e).orThunk(function(){return u.onEscape()(n,e).map(function(){return n})})}),focusIn:function(e,n){c.getPrimary().each(function(n){re(e,n.element(),Gn())})}}),tu.config({highlightClass:u.markers().selectedMenu(),itemClass:u.markers().menu()}),rf.config({find:function(n){return tu.getHighlighted(n)}}),jl.config({})]),Nc(u.tmenuBehaviours())),eventOrder:u.eventOrder(),apis:{collapseMenu:function(e){tu.getHighlighted(e).each(function(n){tu.getHighlighted(n).each(function(n){r(e,n)})})}},events:e}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:yt(n,e),expansions:{}}},collapseItem:function(n){return{value:Gc(Pl()),text:n}}}}),Wl=function(n,e,t,r){return ht(e.routes(),r.start()).map(a).bind(function(n){return ht(n,r.destination()).map(a)})},Ul=function(n,e,t,r){return Wl(0,e,0,r).bind(function(e){return e.transition().map(function(n){return{transition:A(n),route:A(e)}})})},Gl=function(t,r,n){var e,o,i;(e=t,o=r,i=n,ql(e,o).bind(function(n){return Ul(e,o,i,n)})).each(function(n){var e=n.transition();eo(t.element(),e.transitionClass()),Yr(t.element(),r.destinationAttr())})},ql=function(n,e,t){var r=n.element();return qr(r,e.destinationAttr())?F.some({start:A(Gr(n.element(),e.stateAttr())),destination:A(Gr(n.element(),e.destinationAttr()))}):F.none()},Yl=function(n,e,t,r){Gl(n,e,t),qr(n.element(),e.stateAttr())&&Gr(n.element(),e.stateAttr())!==r&&e.onFinish()(n,r),Wr(n.element(),e.stateAttr(),r)},Kl=Object.freeze({findRoute:Wl,disableTransition:Gl,getCurrentRoute:ql,jumpTo:Yl,progressTo:function(t,r,o,i){var n,e;e=r,qr((n=t).element(),e.destinationAttr())&&(Wr(n.element(),e.stateAttr(),Gr(n.element(),e.destinationAttr())),Yr(n.element(),e.destinationAttr()));var u,c,a=(u=r,c=i,{start:A(Gr(t.element(),u.stateAttr())),destination:A(c)});Ul(t,r,o,a).fold(function(){Yl(t,r,o,i)},function(n){Gl(t,r,o);var e=n.transition();no(t.element(),e.transitionClass()),Wr(t.element(),r.destinationAttr(),i)})},getState:function(n,e,t){var r=n.element();return qr(r,e.stateAttr())?F.some(Gr(r,e.stateAttr())):F.none()}}),Xl=Object.freeze({events:function(o,i){return sr([lr(K(),function(t,n){var r=n.event().raw();ql(t,o).each(function(e){Wl(0,o,0,e).each(function(n){n.transition().each(function(n){r.propertyName===n.property()&&(Yl(t,o,i,e.destination()),o.onTransition()(t,e))})})})}),vr(function(n,e){Yl(n,o,i,o.initialState())})])}}),Jl=[or("destinationAttr","data-transitioning-destination"),or("stateAttr","data-transitioning-state"),Jt("initialState"),jo("onTransition"),jo("onFinish"),Qt("routes",Vt(Qe.value,Vt(Qe.value,Rt([rr("transition",[Jt("property"),Jt("transitionClass")])]))))],Ql=zr({fields:Jl,name:"transitioning",active:Xl,apis:Kl,extra:{createRoutes:function(n){var r={};return B(n,function(n,e){var t=e.split("<->");r[t[0]]=yt(t[1],n),r[t[1]]=yt(t[0],n)}),r},createBistate:function(n,e,t){return bt([{key:n,value:yt(e,t)},{key:e,value:yt(n,t)}])},createTristate:function(n,e,t,r){return bt([{key:n,value:bt([{key:e,value:r},{key:t,value:r}])},{key:e,value:bt([{key:n,value:r},{key:t,value:r}])},{key:t,value:bt([{key:n,value:r},{key:e,value:r}])}])}}}),Zl=ai.resolve("scrollable"),nd={register:function(n){no(n,Zl)},deregister:function(n){eo(n,Zl)},scrollable:A(Zl)},ed=function(n){return ht(n,"format").getOr(n.title)},td=function(n,e,t,r,o){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:o?[ai.resolve("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:ai.resolve("format-matches"),selected:t},itemBehaviours:jr(o?[]:[oi(n,function(n,e){(e?ri.on:ri.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:r},innerHtml:e}}]}},rd=function(n,e,t,r){return{value:n,dom:{tag:"div"},components:[Na.sketch({dom:{tag:"div",classes:[ai.resolve("styles-collapser")]},components:r?[{dom:{tag:"span",classes:[ai.resolve("styles-collapse-icon")]}},vl(n)]:[vl(n)],action:function(n){if(r){var e=t().get(n);$l.collapseMenu(e)}}}),{dom:{tag:"div",classes:[ai.resolve("styles-menu-items-container")]},components:[Bl.parts().items({})],behaviours:jr([nf("adhoc-scrollable-menu",[vr(function(n,e){vi(n.element(),"overflow-y","auto"),vi(n.element(),"-webkit-overflow-scrolling","touch"),nd.register(n.element())}),hr(function(n){xi(n.element(),"overflow-y"),xi(n.element(),"-webkit-overflow-scrolling"),nd.deregister(n.element())})])])}],items:e,menuBehaviours:jr([Ql.config({initialState:"after",routes:Ql.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},od=function(r){var o,i,n,e,t,u=(o=r.formats,i=function(){return c},n=rd("Styles",[].concat(yn(o.items,function(n){return td(ed(n),n.title,n.isSelected(),n.getPreview(),xt(o.expansions,ed(n)))})),i,!1),e=V(o.menus,function(n,e){var t=yn(n,function(n){return td(ed(n),n.title,n.isSelected!==undefined&&n.isSelected(),n.getPreview!==undefined?n.getPreview():"",xt(o.expansions,ed(n)))});return rd(e,t,i,!0)}),t=k(e,yt("styles",n)),{tmenu:$l.tieredData("styles",t,o.expansions)}),c=Bs($l.sketch({dom:{tag:"div",classes:[ai.resolve("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(n,e){var t=vs.getValue(e);return r.handle(e,t.value),F.none()},onEscape:function(){return F.none()},onOpenMenu:function(n,e){var t=xs(n.element());ws(e.element(),t),Ql.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var r=xs(n.element()),o=Fi(e.element(),'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(o).getOrDie();ws(t.element(),r),Ql.progressTo(i,"before"),Ql.jumpTo(t,"after"),Ql.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var r=Fi(e.element(),'[role="menu"]').getOrDie("hacky"),o=n.getSystem().getByDom(r).getOrDie();Ql.progressTo(o,"after"),Ql.progressTo(t,"current")},navigateOnHover:!1,openImmediately:!0,data:u.tmenu,markers:{backgroundMenu:ai.resolve("styles-background-menu"),menu:ai.resolve("styles-menu"),selectedMenu:ai.resolve("styles-selected-menu"),item:ai.resolve("styles-item"),selectedItem:ai.resolve("styles-selected-item")}}));return c.asSpec()},id=function(n){return xt(n,"items")?(t=k(gt(e=n,["items"]),{menu:!0}),r=ud(e.items),{item:t,menus:k(r.menus,yt(e.title,r.items)),expansions:k(r.expansions,yt(e.title,e.title))}):{item:n,menus:{},expansions:{}};var e,t,r},ud=function(n){return xn(n,function(n,e){var t=id(e);return{menus:k(n.menus,t.menus),items:[t.item].concat(n.items),expansions:k(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},cd={expand:ud},ad=function(u,n){var c=function(n){return function(){return u.formatter.match(n)}},a=function(n){return function(){return u.formatter.getCssText(n)}},e=ht(n,"style_formats").getOr(zf),s=function(n){return yn(n,function(n){if(xt(n,"items")){var e=s(n.items);return k(k(n,{isSelected:A(!1),getPreview:A("")}),{items:e})}return xt(n,"format")?k(i=n,{isSelected:c(i.format),getPreview:a(i.format)}):(r=Gc((t=n).title),o=k(t,{format:r,isSelected:c(r),getPreview:a(r)}),u.formatter.register(r,o),o);var t,r,o,i})};return s(e)},sd=function(t,n,r){var e,o,i,u=(e=t,i=(o=function(n){return Cn(n,function(n){return n.items!==undefined?0<o(n.items).length?[n]:[]:!xt(n,"format")||e.formatter.canApply(n.format)?[n]:[]})})(n),cd.expand(i));return od({formats:u,handle:function(n,e){t.undoManager.transact(function(){ri.isOn(n)?t.formatter.remove(e):t.formatter.apply(e)}),r()}})},fd=["undo","bold","italic","link","image","bullist","styleselect"],ld=function(n){var e=n.replace(/\|/g," ").trim();return 0<e.length?e.split(/\s+/):[]},dd=function(n){return Cn(n,function(n){return g(n)?dd(n):ld(n)})},md=function(n){var e=n.toolbar!==undefined?n.toolbar:fd;return g(e)?dd(e):ld(e)},gd=function(r,o){var n=function(n){return function(){return $a.forToolbarCommand(o,n)}},e=function(n){return function(){return $a.forToolbarStateCommand(o,n)}},t=function(n,e,t){return function(){return $a.forToolbarStateAction(o,n,e,t)}},i=n("undo"),u=n("redo"),c=e("bold"),a=e("italic"),s=e("underline"),f=n("removeformat"),l=t("unlink","link",function(){o.execCommand("unlink",null,!1)}),d=t("unordered-list","ul",function(){o.execCommand("InsertUnorderedList",null,!1)}),m=t("ordered-list","ol",function(){o.execCommand("InsertOrderedList",null,!1)}),g=ad(o,o.settings),p=function(){return sd(o,g,function(){o.fire("scrollIntoView")})},v=function(n,e){return{isSupported:function(){return n.forall(function(n){return xt(o.buttons,n)})},sketch:e}};return{undo:v(F.none(),i),redo:v(F.none(),u),bold:v(F.none(),c),italic:v(F.none(),a),underline:v(F.none(),s),removeformat:v(F.none(),f),link:v(F.none(),function(){return e=r,t=o,$a.forToolbarStateAction(t,"link","link",function(){var n=Hf(e,t);e.setContextToolbar(n),Zs(t,function(){e.focusToolbar()}),Xs.query(t).each(function(n){t.selection.select(n.dom())})});var e,t}),unlink:v(F.none(),l),image:v(F.none(),function(){return Ws(o)}),bullist:v(F.some("bullist"),d),numlist:v(F.some("numlist"),m),fontsizeselect:v(F.none(),function(){return e=o,n={onChange:function(n){Ms.apply(e,n)},getInitialValue:function(){return Ms.get(e)}},Os(r,"font-size",function(){return Rs(n)});var e,n}),forecolor:v(F.none(),function(){return Cs(r,o)}),styleselect:v(F.none(),function(){return $a.forToolbar("style-formats",function(n){o.fire("toReading"),r.dropup().appear(p,ri.on,n)},jr([ri.config({toggleClass:ai.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Go.config({channels:bt([ui(wo.orientationChanged(),ri.off),ui(wo.dropupDismissed(),ri.off)])})]))})}},pd=function(n,t){var e=md(n),r={};return Cn(e,function(n){var e=!xt(r,n)&&xt(t,n)&&t[n].isSupported()?[t[n].sketch()]:[];return r[n]=!0,e})},vd=function(m,g){return function(n){if(m(n)){var e,t,r,o,i,u,c,a=ue.fromDom(n.target),s=function(){n.stopPropagation()},f=function(){n.preventDefault()},l=v(f,s),d=(e=a,t=n.clientX,r=n.clientY,o=s,i=f,u=l,c=n,{target:A(e),x:A(t),y:A(r),stop:o,prevent:i,kill:u,raw:A(c)});g(d)}}},hd=function(n,e,t,r,o){var i=vd(t,r);return n.dom().addEventListener(e,i,o),{unbind:l(yd,n,e,i,o)}},yd=function(n,e,t,r){n.dom().removeEventListener(e,t,r)},bd=A(!0),wd=function(n,e,t){return hd(n,e,bd,t,!1)},xd=function(n,e,t){return hd(n,e,bd,t,!0)},Td=function(n){var e=n.matchMedia("(orientation: portrait)").matches;return{isPortrait:A(e)}},Sd=Td,Od=function(r,e){var n=ue.fromDom(r),o=null,t=wd(n,"orientationchange",function(){clearInterval(o);var n=Td(r);e.onChange(n),i(function(){e.onReady(n)})}),i=function(n){clearInterval(o);var e=r.innerHeight,t=0;o=setInterval(function(){e!==r.innerHeight?(clearInterval(o),n(F.some(r.innerHeight))):20<t&&(clearInterval(o),n(F.none())),t++},50)};return{onAdjustment:i,destroy:function(){t.unbind()}}},kd=function(n){var e=zn.detect().os.isiOS(),t=Td(n).isPortrait();return e&&!t?n.screen.height:n.screen.width},Cd=function(n){var e=n.raw();return e.touches===undefined||1!==e.touches.length?F.none():F.some(e.touches[0])},Ed=function(t){var r,o,i,u=ao(F.none()),c=(r=function(n){u.set(F.none()),t.triggerEvent(Kn(),n)},o=400,i=null,{cancel:function(){null!==i&&(p.clearTimeout(i),i=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];i=p.setTimeout(function(){r.apply(null,n),i=null},o)}}),a=bt([{key:j(),value:function(t){return Cd(t).each(function(n){c.cancel();var e={x:A(n.clientX),y:A(n.clientY),target:t.target};c.schedule(t),u.set(F.some(e))}),F.none()}},{key:H(),value:function(n){return c.cancel(),Cd(n).each(function(i){u.get().each(function(n){var e,t,r,o;e=i,t=n,r=Math.abs(e.clientX-t.x()),o=Math.abs(e.clientY-t.y()),(5<r||5<o)&&u.set(F.none())})}),F.none()}},{key:z(),value:function(e){return c.cancel(),u.get().filter(function(n){return Re(n.target(),e.target())}).map(function(n){return t.triggerEvent(qn(),e)})}}]);return{fireIfReady:function(e,n){return ht(a,n).bind(function(n){return n(e)})}}},Dd=function(t){var e=Ed({triggerEvent:function(n,e){t.onTapContent(e)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return wd(t.body(),"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return wd(t.body(),"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}},Id=6<=zn.detect().os.version.major,Ad=function(r,e,t){var o=Dd(r),i=Be(e),u=function(n){return!Re(n.start(),n.finish())||n.soffset()!==n.foffset()},n=function(){var n=r.doc().dom().hasFocus()&&r.getSelection().exists(u);t.getByDom(e).each(!0===(n||po(i).filter(function(n){return"input"===me(n)}).exists(function(n){return n.dom().selectionStart!==n.dom().selectionEnd}))?ri.on:ri.off)},c=[wd(r.body(),"touchstart",function(n){r.onTouchContent(),o.fireTouchstart(n)}),o.onTouchmove(),o.onTouchend(),wd(e,"touchstart",function(n){r.onTouchToolstrip()}),r.onToReading(function(){go(r.body())}),r.onToEditing(I),r.onScrollToCursor(function(n){n.preventDefault(),r.getCursorBox().each(function(n){var e=r.win(),t=n.top()>e.innerHeight||n.bottom()>e.innerHeight?n.bottom()-e.innerHeight+50:0;0!==t&&e.scrollTo(e.pageXOffset,e.pageYOffset+t)})})].concat(!0===Id?[]:[wd(ue.fromDom(r.win()),"blur",function(){t.getByDom(e).each(ri.off)}),wd(i,"select",n),wd(r.doc(),"selectionchange",n)]);return{destroy:function(){bn(c,function(n){n.unbind()})}}},Md=function(n,e){var t=parseInt(Gr(n,e),10);return isNaN(t)?0:t},Fd=(Hs=ve,zs="text",{get:function(n){if(!Hs(n))throw new Error("Can only get "+zs+" value of a "+zs+" node");return Ls(n).getOr("")},getOption:Ls=function(n){return Hs(n)?F.from(n.dom().nodeValue):F.none()},set:function(n,e){if(!Hs(n))throw new Error("Can only set raw "+zs+" value of a "+zs+" node");n.dom().nodeValue=e}}),Rd=function(n){return"img"===me(n)?1:(e=n,Fd.getOption(e)).fold(function(){return _e(n).length},function(n){return n.length});var e},Bd={create:we("start","soffset","finish","foffset")},Vd=Ze([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Nd={before:Vd.before,on:Vd.on,after:Vd.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(h,h,h)}},_d=Ze([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),jd={domRange:_d.domRange,relative:_d.relative,exact:_d.exact,exactFromRange:function(n){return _d.exact(n.start(),n.soffset(),n.finish(),n.foffset())},getWin:function(n){var e=n.match({domRange:function(n){return ue.fromDom(n.startContainer)},relative:function(n,e){return Nd.getStart(n)},exact:function(n,e,t,r){return n}});return Ve(e)},range:Bd.create},Hd=function(n,e,t){var r,o,i=n.document.createRange();return r=i,e.fold(function(n){r.setStartBefore(n.dom())},function(n,e){r.setStart(n.dom(),e)},function(n){r.setStartAfter(n.dom())}),o=i,t.fold(function(n){o.setEndBefore(n.dom())},function(n,e){o.setEnd(n.dom(),e)},function(n){o.setEndAfter(n.dom())}),i},zd=function(n,e,t,r,o){var i=n.document.createRange();return i.setStart(e.dom(),t),i.setEnd(r.dom(),o),i},Ld=function(n){return{left:A(n.left),top:A(n.top),right:A(n.right),bottom:A(n.bottom),width:A(n.width),height:A(n.height)}},Pd=Ze([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),$d=function(n,e,t){return e(ue.fromDom(t.startContainer),t.startOffset,ue.fromDom(t.endContainer),t.endOffset)},Wd=function(n,e){var o,t,r,i=(o=n,e.match({domRange:function(n){return{ltr:A(n),rtl:F.none}},relative:function(n,e){return{ltr:J(function(){return Hd(o,n,e)}),rtl:J(function(){return F.some(Hd(o,e,n))})}},exact:function(n,e,t,r){return{ltr:J(function(){return zd(o,n,e,t,r)}),rtl:J(function(){return F.some(zd(o,t,r,n,e))})}}}));return(r=(t=i).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Pd.rtl(ue.fromDom(n.endContainer),n.endOffset,ue.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return $d(0,Pd.ltr,r)}):$d(0,Pd.ltr,r)},Ud=function(n,e){var t=me(n);return"input"===t?Nd.after(n):hn(["br","img"],t)?0===e?Nd.before(n):Nd.after(n):Nd.on(n,e)},Gd=function(n,e,t,r){var o,i,u,c,a,s=(i=e,u=t,c=r,(a=Be(o=n).dom().createRange()).setStart(o.dom(),i),a.setEnd(u.dom(),c),a),f=Re(n,t)&&e===r;return s.collapsed&&!f},qd=function(n,e,t,r,o){var i,u,c=zd(n,e,t,r,o);i=n,u=c,F.from(i.getSelection()).each(function(n){n.removeAllRanges(),n.addRange(u)})},Yd=function(n,e,t,r,o){var i,u,c,a,l,s=(i=r,u=o,c=Ud(e,t),a=Ud(i,u),jd.relative(c,a));Wd(l=n,s).match({ltr:function(n,e,t,r){qd(l,n,e,t,r)},rtl:function(n,e,t,r){var o,i,u,c,a,s=l.getSelection();if(s.setBaseAndExtent)s.setBaseAndExtent(n.dom(),e,t.dom(),r);else if(s.extend)try{i=n,u=e,c=t,a=r,(o=s).collapse(i.dom(),u),o.extend(c.dom(),a)}catch(f){qd(l,t,r,n,e)}else qd(l,t,r,n,e)}})},Kd=function(n){var e=ue.fromDom(n.anchorNode),t=ue.fromDom(n.focusNode);return Gd(e,n.anchorOffset,t,n.focusOffset)?F.some(Bd.create(e,n.anchorOffset,t,n.focusOffset)):function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return F.some(Bd.create(ue.fromDom(e.startContainer),e.startOffset,ue.fromDom(t.endContainer),t.endOffset))}return F.none()}(n)},Xd=function(n){return F.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(Kd)},Jd=function(n,e){var i,t,r,o,u=Wd(i=n,e).match({ltr:function(n,e,t,r){var o=i.document.createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o},rtl:function(n,e,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(n.dom(),e),o}});return r=(t=u).getClientRects(),0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?F.some(o).map(Ld):F.none()},Qd=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:A(2),height:n.height}},Zd=function(n){return{left:A(n.left),top:A(n.top),right:A(n.right),bottom:A(n.bottom),width:A(n.width),height:A(n.height)}},nm=function(n){var e=n.getSelection();return e!==undefined&&0<e.rangeCount?function(t){if(t.collapsed){var r=ue.fromDom(t.startContainer);return Ne(r).bind(function(n){var e=jd.exact(r,t.startOffset,n,Rd(n));return Jd(t.startContainer.ownerDocument.defaultView,e).map(Qd).map(In)}).getOr([])}return yn(t.getClientRects(),Zd)}(e.getRangeAt(0)):[]},em=function(n){n.focus();var e=ue.fromDom(n.document.body);(po().exists(function(n){return hn(["input","textarea"],me(n))})?function(n){setTimeout(function(){n()},0)}:a)(function(){po().each(go),mo(e)})},tm="data-"+ai.resolve("last-outer-height"),rm=function(n,e){Wr(n,tm,e)},om=function(n){return{top:A(n.top()),bottom:A(n.top()+n.height())}},im=function(n,e){var t=Md(e,tm),r=n.innerHeight;return r<t?F.some(t-r):F.none()},um=function(n,u){var e=ue.fromDom(u.document.body),t=wd(ue.fromDom(n),"resize",function(){im(n,e).each(function(i){var n,e;(n=u,e=nm(n),0<e.length?F.some(e[0]).map(om):F.none()).each(function(n){var e,t,r,o=(e=u,r=i,(t=n).top()>e.innerHeight||t.bottom()>e.innerHeight?Math.min(r,t.bottom()-e.innerHeight+50):0);0!==o&&u.scrollTo(u.pageXOffset,u.pageYOffset+o)})}),rm(e,n.innerHeight)});return rm(e,n.innerHeight),{toEditing:function(){em(u)},destroy:function(){t.unbind()}}},cm=function(n){return F.some(ue.fromDom(n.dom().contentWindow.document.body))},am=function(n){return F.some(ue.fromDom(n.dom().contentWindow.document))},sm=function(n){return F.from(n.dom().contentWindow)},fm=function(n){return sm(n).bind(Xd)},lm=function(n){return n.getFrame()},dm=function(n,t){return function(e){return e[n].getOrThunk(function(){var n=lm(e);return function(){return t(n)}})()}},mm=function(n,e,t,r){return n[t].getOrThunk(function(){return function(n){return wd(e,r,n)}})},gm=function(n){return{left:A(n.left),top:A(n.top),right:A(n.right),bottom:A(n.bottom),width:A(n.width),height:A(n.height)}},pm={getBody:dm("getBody",cm),getDoc:dm("getDoc",am),getWin:dm("getWin",sm),getSelection:dm("getSelection",fm),getFrame:lm,getActiveApi:function(c){var a=lm(c);return cm(a).bind(function(u){return am(a).bind(function(i){return sm(a).map(function(o){var n=ue.fromDom(i.dom().documentElement),e=c.getCursorBox.getOrThunk(function(){return function(){return(n=o,Xd(n).map(function(n){return jd.exact(n.start(),n.soffset(),n.finish(),n.foffset())})).bind(function(n){return Jd(o,n).orThunk(function(){return Xd(o).filter(function(n){return Re(n.start(),n.finish())&&n.soffset()===n.foffset()}).bind(function(n){var e=n.start().dom().getBoundingClientRect();return 0<e.width||0<e.height?F.some(e).map(gm):F.none()})})});var n}}),t=c.setSelection.getOrThunk(function(){return function(n,e,t,r){Yd(o,n,e,t,r)}}),r=c.clearSelection.getOrThunk(function(){return function(){o.getSelection().removeAllRanges()}});return{body:A(u),doc:A(i),win:A(o),html:A(n),getSelection:l(fm,a),setSelection:t,clearSelection:r,frame:A(a),onKeyup:mm(c,i,"onKeyup","keyup"),onNodeChanged:mm(c,i,"onNodeChanged","selectionchange"),onDomChanged:c.onDomChanged,onScrollToCursor:c.onScrollToCursor,onScrollToElement:c.onScrollToElement,onToReading:c.onToReading,onToEditing:c.onToEditing,onToolbarScrollStart:c.onToolbarScrollStart,onTouchContent:c.onTouchContent,onTapContent:c.onTapContent,onTouchToolstrip:c.onTouchToolstrip,getCursorBox:e}})})})}},vm="data-ephox-mobile-fullscreen-style",hm="position:absolute!important;",ym="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",bm=zn.detect().os.isAndroid(),wm=function(n,e){var t,r,o,i=function(r){return function(n){var e=Gr(n,"style"),t=e===undefined?"no-styles":e.trim();t!==r&&(Wr(n,vm,t),Wr(n,"style",r))}},u=(t="*",Di(n,function(n){return Ie(n,t)},r)),c=Cn(u,function(n){var e;return e="*",Ii(n,function(n){return Ie(n,e)})}),a=(o=yi(e,"background-color"))!==undefined&&""!==o?"background-color:"+o+"!important":"background-color:rgb(255,255,255)!important;";bn(c,i("display:none!important;")),bn(u,i(hm+ym+a)),i((!0===bm?"":hm)+ym+a)(n)},xm=function(){var n=Me("["+vm+"]");bn(n,function(n){var e=Gr(n,vm);"no-styles"!==e?Wr(n,"style",e):Yr(n,"style"),Yr(n,vm)})},Tm=function(){var e=Mi("head").getOrDie(),n=Mi('meta[name="viewport"]').getOrThunk(function(){var n=ue.fromTag("meta");return Wr(n,"name","viewport"),ze(e,n),n}),t=Gr(n,"content");return{maximize:function(){Wr(n,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){t!==undefined&&null!==t&&0<t.length?Wr(n,"content",t):Wr(n,"content","user-scalable=yes")}}},Sm=function(e,n){var t=Tm(),r=Rf(),o=Rf();return{enter:function(){n.hide(),no(e.container,ai.resolve("fullscreen-maximized")),no(e.container,ai.resolve("android-maximized")),t.maximize(),no(e.body,ai.resolve("android-scroll-reload")),r.set(um(e.win,pm.getWin(e.editor).getOrDie("no"))),pm.getActiveApi(e.editor).each(function(n){wm(e.container,n.body()),o.set(Ad(n,e.toolstrip,e.alloy))})},exit:function(){t.restore(),n.show(),eo(e.container,ai.resolve("fullscreen-maximized")),eo(e.container,ai.resolve("android-maximized")),xm(),eo(e.body,ai.resolve("android-scroll-reload")),o.clear(),r.clear()}}},Om=function(t,r){var o=null;return{cancel:function(){null!==o&&(p.clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==o&&p.clearTimeout(o),o=p.setTimeout(function(){t.apply(null,n),o=null},r)}}},km=function(n,e){var t,r,o,i=Bs(of.sketch({dom:Ha('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:jr([ri.config({toggleClass:ai.resolve("mask-tap-icon-selected"),toggleOnExecute:!1})])})),u=(t=n,r=200,o=null,{cancel:function(){null!==o&&(p.clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null===o&&(o=p.setTimeout(function(){t.apply(null,n),o=null},r))}});return of.sketch({dom:Ha('<div class="${prefix}-disabled-mask"></div>'),components:[of.sketch({dom:Ha('<div class="${prefix}-content-container"></div>'),components:[Na.sketch({dom:Ha('<div class="${prefix}-content-tap-section"></div>'),components:[i.asSpec()],action:function(n){u.throttle()},buttonBehaviours:jr([ri.config({toggleClass:ai.resolve("mask-tap-icon-selected")})])})]})]})},Cm=Bt([nr("editor",[Jt("getFrame"),er("getBody"),er("getDoc"),er("getWin"),er("getSelection"),er("setSelection"),er("clearSelection"),er("cursorSaver"),er("onKeyup"),er("onNodeChanged"),er("getCursorBox"),Jt("onDomChanged"),or("onTouchContent",I),or("onTapContent",I),or("onTouchToolstrip",I),or("onScrollToCursor",A({unbind:I})),or("onScrollToElement",A({unbind:I})),or("onToEditing",A({unbind:I})),or("onToReading",A({unbind:I})),or("onToolbarScrollStart",h)]),Jt("socket"),Jt("toolstrip"),Jt("dropup"),Jt("toolbar"),Jt("container"),Jt("alloy"),ur("win",function(n){return Be(n.socket).dom().defaultView}),ur("body",function(n){return ue.fromDom(n.socket.dom().ownerDocument.body)}),or("translate",h),or("setReadOnly",I),or("readOnlyOnInit",A(!0))]),Em=function(n){var e=Ut("Getting AndroidWebapp schema",Cm,n);vi(e.toolstrip,"width","100%");var t=yl(km(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};ze(e.container,t.element());var o=Sm(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:I,enter:o.enter,exit:o.exit,destroy:I}},Dm=A([or("shell",!0),Vc("toolbarBehaviours",[jl])]),Im=A([ua({name:"groups",overrides:function(n){return{behaviours:jr([jl.config({})])}}})]),Am=Va({name:"Toolbar",configFields:Dm(),partFields:Im(),factory:function(e,n,t,r){var o=function(n){return e.shell()?F.some(n):ga(n,e,"groups")},i=e.shell()?{behaviours:[jl.config({})],components:[]}:{behaviours:[],components:n};return{uid:e.uid(),dom:e.dom(),components:i.components,behaviours:k(jr(i.behaviours),Nc(e.toolbarBehaviours())),apis:{setGroups:function(n,e){o(n).fold(function(){throw p.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){jl.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}}),Mm=A([Jt("items"),(Ps=["itemClass"],nr("markers",yn(Ps,Jt))),Vc("tgroupBehaviours",[Bc])]),Fm=A([ca({name:"items",unit:"item",overrides:function(n){return{domModification:{classes:[n.markers().itemClass()]}}}})]),Rm=Va({name:"ToolbarGroup",configFields:Mm(),partFields:Fm(),factory:function(n,e,t,r){return k({dom:{attributes:{role:"toolbar"}}},{uid:n.uid(),dom:n.dom(),components:e,behaviours:k(jr([Bc.config({mode:"flow",selector:"."+n.markers().itemClass()})]),Nc(n.tgroupBehaviours())),"debug.sketcher":t["debug.sketcher"]})}}),Bm="data-"+ai.resolve("horizontal-scroll"),Vm=function(n){return"true"===Gr(n,Bm)?0<(t=n).dom().scrollLeft||function(n){n.dom().scrollLeft=1;var e=0!==n.dom().scrollLeft;return n.dom().scrollLeft=0,e}(t):0<(e=n).dom().scrollTop||function(n){n.dom().scrollTop=1;var e=0!==n.dom().scrollTop;return n.dom().scrollTop=0,e}(e);var e,t},Nm={exclusive:function(n,e){return wd(n,"touchmove",function(n){Bi(n.target(),e).filter(Vm).fold(function(){n.raw().preventDefault()},I)})},markAsHorizontal:function(n){Wr(n,Bm,"true")}};function _m(){var e=function(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:Ha('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:jr([nf("adhoc-scrollable-toolbar",!0===n.scrollable?[yr(function(n,e){vi(n.element(),"overflow-x","auto"),Nm.markAsHorizontal(n.element()),nd.register(n.element())})]:[])]),components:[of.sketch({components:[Rm.parts().items({})]})],markers:{itemClass:ai.resolve("toolbar-group-item")},items:n.items}},t=yl(Am.sketch({dom:Ha('<div class="${prefix}-toolbar"></div>'),components:[Am.parts().groups({})],toolbarBehaviours:jr([ri.config({toggleClass:ai.resolve("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),Bc.config({mode:"cyclic"})]),shell:!0})),n=yl(of.sketch({dom:{classes:[ai.resolve("toolstrip")]},components:[bl(t)],containerBehaviours:jr([ri.config({toggleClass:ai.resolve("android-selection-context-toolbar"),toggleOnExecute:!1})])})),r=function(){Am.setGroups(t,o.get()),ri.off(t)},o=ao([]);return{wrapper:A(n),toolbar:A(t),createGroups:function(n){return yn(n,v(Rm.sketch,e))},setGroups:function(n){o.set(n),r()},setContextToolbar:function(n){ri.on(t),Am.setGroups(t,n)},restoreToolbar:function(){ri.isOn(t)&&r()},refresh:function(){},focus:function(){Bc.focusIn(t)}}}var jm=function(n,e){jl.append(n,bl(e))},Hm=function(n,e){jl.remove(n,e)},zm=function(n){return yl(Na.sketch({dom:Ha('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))},Lm=function(){return yl(of.sketch({dom:Ha('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:jr([jl.config({})])}))},Pm=function(n,e,t,r){(!0===t?co.toAlpha:co.toOmega)(r),(t?jm:Hm)(n,e)},$m=function(e,n){return n.getAnimationRoot().fold(function(){return e.element()},function(n){return n(e)})},Wm=function(n){return n.dimension().property()},Um=function(n,e){return n.dimension().getDimension()(e)},Gm=function(n,e){var t=$m(n,e);ul(t,[e.shrinkingClass(),e.growingClass()])},qm=function(n,e){eo(n.element(),e.openClass()),no(n.element(),e.closedClass()),vi(n.element(),Wm(e),"0px"),Ti(n.element())},Ym=function(n,e){eo(n.element(),e.closedClass()),no(n.element(),e.openClass()),xi(n.element(),Wm(e))},Km=function(n,e,t){t.setCollapsed(),vi(n.element(),Wm(e),Um(e,n.element())),Ti(n.element());var r=$m(n,e);no(r,e.shrinkingClass()),qm(n,e),e.onStartShrink()(n)},Xm=function(n,e,t){var r=function(n,e){Ym(n,e);var t=Um(e,n.element());return qm(n,e),t}(n,e),o=$m(n,e);no(o,e.growingClass()),Ym(n,e),vi(n.element(),Wm(e),r),t.setExpanded(),e.onStartGrow()(n)},Jm=function(n,e,t){var r=$m(n,e);return!0===ro(r,e.growingClass())},Qm=function(n,e,t){var r=$m(n,e);return!0===ro(r,e.shrinkingClass())},Zm=Object.freeze({grow:function(n,e,t){t.isExpanded()||Xm(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&Km(n,e,t)},immediateShrink:function(n,e,t){var r,o;t.isExpanded()&&(r=n,o=e,t.setCollapsed(),vi(r.element(),Wm(o),Um(o,r.element())),Ti(r.element()),Gm(r,o),qm(r,o),o.onStartShrink()(r),o.onShrunk()(r))},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:Jm,isShrinking:Qm,isTransitioning:function(n,e,t){return!0===Jm(n,e)||!0===Qm(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?Km:Xm)(n,e,t)},disableTransitions:Gm}),ng=Object.freeze({exhibit:function(n,e){var t=e.expanded();return Or(t?{classes:[e.openClass()],styles:{}}:{classes:[e.closedClass()],styles:yt(e.dimension().property(),"0px")})},events:function(t,r){return sr([lr(K(),function(n,e){e.event().raw().propertyName===t.dimension().property()&&(Gm(n,t),r.isExpanded()&&xi(n.element(),t.dimension().property()),(r.isExpanded()?t.onGrown():t.onShrunk())(n))})])}}),eg=[Jt("closedClass"),Jt("openClass"),Jt("shrinkingClass"),Jt("growingClass"),er("getAnimationRoot"),jo("onShrunk"),jo("onStartShrink"),jo("onGrown"),jo("onStartGrow"),or("expanded",!1),Qt("dimension",Yt("property",{width:[Po("property","width"),Po("getDimension",function(n){return xs(n)+"px"})],height:[Po("property","height"),Po("getDimension",function(n){return Ei(n)+"px"})]}))],tg=zr({fields:eg,name:"sliding",active:ng,apis:Zm,state:Object.freeze({init:function(n){var e=ao(n.expanded());return _r({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:l(e.set,!1),setExpanded:l(e.set,!0),readState:function(){return"expanded: "+e.get()}})}})}),rg=function(e,t){var r=yl(of.sketch({dom:{tag:"div",classes:[ai.resolve("dropup")]},components:[],containerBehaviours:jr([jl.config({}),tg.config({closedClass:ai.resolve("dropup-closed"),openClass:ai.resolve("dropup-open"),shrinkingClass:ai.resolve("dropup-shrinking"),growingClass:ai.resolve("dropup-growing"),dimension:{property:"height"},onShrunk:function(n){e(),t(),jl.set(n,[])},onGrown:function(n){e(),t()}}),ii(function(n,e){o(I)})])})),o=function(n){p.window.requestAnimationFrame(function(){n(),tg.shrink(r)})};return{appear:function(n,e,t){!0===tg.hasShrunk(r)&&!1===tg.isTransitioning(r)&&p.window.requestAnimationFrame(function(){e(t),jl.set(r,[n()]),tg.grow(r)})},disappear:o,component:A(r),element:r.element}},og=zn.detect().browser.isFirefox(),ig=Rt([Zt("triggerEvent"),Zt("broadcastEvent"),or("stopBackspace",!0)]),ug=function(e,n){var t,r,o,i,u=Ut("Getting GUI events settings",ig,n),c=zn.detect().deviceType.isTouch()?["touchstart","touchmove","touchend","gesturestart"]:["mousedown","mouseup","mouseover","mousemove","mouseout","click"],a=Ed(u),s=yn(c.concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop"]),function(n){return wd(e,n,function(e){a.fireIfReady(e,n).each(function(n){n&&e.kill()}),u.triggerEvent(n,e)&&e.kill()})}),f=wd(e,"keydown",function(n){var e;u.triggerEvent("keydown",n)?n.kill():!0!==u.stopBackspace||8!==(e=n).raw().which||hn(["input","textarea"],me(e.target()))||n.prevent()}),l=(t=e,r=function(n){u.triggerEvent("focusin",n)&&n.kill()},og?xd(t,"focus",r):wd(t,"focusin",r)),d=(o=e,i=function(n){u.triggerEvent("focusout",n)&&n.kill(),p.setTimeout(function(){u.triggerEvent($n(),n)},0)},og?xd(o,"blur",i):wd(o,"focusout",i)),m=Ve(e),g=wd(m,"scroll",function(n){u.broadcastEvent(Jn(),n)&&n.kill()});return{unbind:function(){bn(s,function(n){n.unbind()}),f.unbind(),l.unbind(),d.unbind(),g.unbind()}}},cg=function(n,e){var t=ht(n,"target").map(function(n){return n()}).getOr(e);return ao(t)},ag=Ze([{stopped:[]},{resume:["element"]},{complete:[]}]),sg=function(n,r,e,t,o,i){var u,c,a,s,f=n(r,t),l=(u=e,c=o,a=ao(!1),s=ao(!1),{stop:function(){a.set(!0)},cut:function(){s.set(!0)},isStopped:a.get,isCut:s.get,event:A(u),setSource:c.set,getSource:c.get});return f.fold(function(){return i.logEventNoHandlers(r,t),ag.complete()},function(e){var t=e.descHandler();return Jf(t)(l),l.isStopped()?(i.logEventStopped(r,e.element(),t.purpose()),ag.stopped()):l.isCut()?(i.logEventCut(r,e.element(),t.purpose()),ag.complete()):Ne(e.element()).fold(function(){return i.logNoParent(r,e.element(),t.purpose()),ag.complete()},function(n){return i.logEventResponse(r,e.element(),t.purpose()),ag.resume(n)})})},fg=function(e,t,r,n,o,i){return sg(e,t,r,n,o,i).fold(function(){return!0},function(n){return fg(e,t,r,n,o,i)},function(){return!1})},lg=function(n,e,t){var r,o,i=(r=e,o=ao(!1),{stop:function(){o.set(!0)},cut:I,isStopped:o.get,isCut:A(!1),event:A(r),setSource:c("Cannot set source of a broadcasted event"),getSource:c("Cannot get source of a broadcasted event")});return bn(n,function(n){var e=n.descHandler();Jf(e)(i)}),i.isStopped()},dg=function(n,e,t,r,o){var i=cg(t,r);return fg(n,e,t,r,i,o)},mg=function(n,e,t){return lo(n,function(n){return e(n).isSome()},t).bind(e)},gg=we("element","descHandler"),pg=function(n,e){return{id:A(n),descHandler:A(e)}};function vg(){var i={};return{registerId:function(r,o,n){B(n,function(n,e){var t=i[e]!==undefined?i[e]:{};t[o]=Xf(n,r),i[e]=t})},unregisterId:function(t){B(i,function(n,e){n.hasOwnProperty(t)&&delete n[t]})},filterByType:function(n){return ht(i,n).map(function(n){return _(n,function(n,e){return pg(e,n)})}).getOr([])},find:function(n,e,t){var o=pt(e)(i);return mg(t,function(n){return t=o,Ea(r=n).fold(function(){return F.none()},function(n){var e=pt(n);return t.bind(e).map(function(n){return gg(r,n)})});var t,r},n)}}}function hg(){var r=vg(),o={},i=function(r){var n=r.element();return Ea(n).fold(function(){return n="uid-",e=r.element(),t=Gc(ka+n),Wr(e,Ca,t),t;var n,e,t},function(n){return n})},u=function(n){Ea(n.element()).each(function(n){o[n]=undefined,r.unregisterId(n)})};return{find:function(n,e,t){return r.find(n,e,t)},filter:function(n){return r.filterByType(n)},register:function(n){var e=i(n);xt(o,e)&&function(n,e){var t=o[e];if(t!==n)throw new Error('The tagId "'+e+'" is already used by: '+ko(t.element())+"\nCannot use it for: "+ko(n.element())+"\nThe conflicting element is"+(he(t.element())?" ":" not ")+"already in the DOM");u(n)}(n,e);var t=[n];r.registerId(t,e,n.events()),o[e]=n},unregister:u,getById:function(n){return pt(n)(o)}}}var yg=function(t){var r=function(e){return Ne(t.element()).fold(function(){return!0},function(n){return Re(e,n)})},o=hg(),s=function(n,e){return o.find(r,n,e)},n=ug(t.element(),{triggerEvent:function(u,c){return Fo(u,c.target(),function(n){return e=s,t=u,o=n,i=(r=c).target(),dg(e,t,r,i,o);var e,t,r,o,i})},broadcastEvent:function(n,e){var t=o.filter(n);return lg(t,e)}}),i=Wf({debugInfo:A("real"),triggerEvent:function(e,t,r){Fo(e,t,function(n){dg(s,e,r,t,n)})},triggerFocus:function(c,a){Ea(c).fold(function(){mo(c)},function(n){Fo(Pn(),c,function(n){var e,t,r,o,i,u;e=s,t=Pn(),r={originator:A(a),kill:I,prevent:I,target:A(c)},i=n,u=cg(r,o=c),sg(e,t,r,o,u,i)})})},triggerEscape:function(n,e){i.triggerEvent("keydown",n.element(),e.event())},getByUid:function(n){return m(n)},getByDom:function(n){return g(n)},build:yl,addToGui:function(n){c(n)},removeFromGui:function(n){a(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){u(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)},isConnected:A(!0)}),e=function(n){n.connect(i),ve(n.element())||(o.register(n),bn(n.components(),e),i.triggerEvent(Xn(),n.element(),{target:A(n.element())}))},u=function(n){ve(n.element())||(bn(n.components(),u),o.unregister(n)),n.disconnect()},c=function(n){Ge(t,n)},a=function(n){Ke(n)},f=function(t){var n=o.filter(Wn());bn(n,function(n){var e=n.descHandler();Jf(e)(t)})},l=function(n){f({universal:A(!0),data:A(n)})},d=function(n,e){f({universal:A(!1),channels:A(n),data:A(e)})},m=function(n){return o.getById(n).fold(function(){return Qe.error(new Error('Could not find component with uid: "'+n+'" in system.'))},Qe.value)},g=function(n){var e=Ea(n).getOr("not found");return m(e)};return e(t),{root:A(t),element:t.element,destroy:function(){n.unbind(),$e(t.element())},add:c,remove:a,getByUid:m,getByDom:g,addToWorld:e,removeFromWorld:u,broadcast:l,broadcastOn:d}},bg=A(ai.resolve("readonly-mode")),wg=A(ai.resolve("edit-mode"));function xg(n){var e=yl(of.sketch({dom:{classes:[ai.resolve("outer-container")].concat(n.classes)},containerBehaviours:jr([co.config({alpha:bg(),omega:wg()})])}));return yg(e)}var Tg,Sg,Og,kg,Cg=function(n,e){var t=ue.fromTag("input");hi(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),ze(n,t),mo(t),e(t),$e(t)},Eg=function(n){var e=n.getSelection();if(0<e.rangeCount){var t=e.getRangeAt(0),r=n.document.createRange();r.setStart(t.startContainer,t.startOffset),r.setEnd(t.endContainer,t.endOffset),e.removeAllRanges(),e.addRange(r)}},Dg=function(n,e){po().each(function(n){Re(n,e)||go(n)}),n.focus(),mo(ue.fromDom(n.document.body)),Eg(n)},Ig={stubborn:function(n,e,t,r){var o=function(){Dg(e,r)},i=wd(t,"keydown",function(n){hn(["input","textarea"],me(n.target()))||o()});return{toReading:function(){Cg(n,go)},toEditing:o,onToolbarTouch:function(){},destroy:function(){i.unbind()}}},timid:function(n,e,t,r){var o=function(){go(r)};return{toReading:function(){o()},toEditing:function(){Dg(e,r)},onToolbarTouch:function(){o()},destroy:I}}},Ag=function(t,r,o,i,n){var u=function(){r.run(function(n){n.refreshSelection()})},e=function(n,e){var t=n-i.dom().scrollTop;r.run(function(n){n.scrollIntoView(t,t+e)})},c=function(){r.run(function(n){n.clearSelection()})},a=function(){t.getCursorBox().each(function(n){e(n.top(),n.height())}),r.run(function(n){n.syncHeight()})},s=Dd(t),f=Om(a,300),l=[t.onKeyup(function(){c(),f.throttle()}),t.onNodeChanged(u),t.onDomChanged(f.throttle),t.onDomChanged(u),t.onScrollToCursor(function(n){n.preventDefault(),f.throttle()}),t.onScrollToElement(function(n){n.element(),e(r,i)}),t.onToEditing(function(){r.run(function(n){n.toEditing()})}),t.onToReading(function(){r.run(function(n){n.toReading()})}),wd(t.doc(),"touchend",function(n){Re(t.html(),n.target())||Re(t.body(),n.target())}),wd(o,"transitionend",function(n){var e;"height"===n.raw().propertyName&&(e=Ei(o),r.run(function(n){n.setViewportOffset(e)}),u(),a())}),xd(o,"touchstart",function(n){var e;r.run(function(n){n.highlightSelection()}),e=n,r.run(function(n){n.onToolbarTouch(e)}),t.onTouchToolstrip()}),wd(t.body(),"touchstart",function(n){c(),t.onTouchContent(),s.fireTouchstart(n)}),s.onTouchmove(),s.onTouchend(),wd(t.body(),"click",function(n){n.kill()}),wd(o,"touchmove",function(){t.onToolbarScrollStart()})];return{destroy:function(){bn(l,function(n){n.unbind()})}}},Mg={},Fg={exports:Mg};Tg=undefined,Sg=Mg,Og=Fg,kg=undefined,function(n){"object"==typeof Sg&&void 0!==Og?Og.exports=n():"function"==typeof Tg&&Tg.amd?Tg([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function i(u,c,a){function s(e,n){if(!c[e]){if(!u[e]){var t="function"==typeof kg&&kg;if(!n&&t)return t(e,!0);if(f)return f(e,!0);var r=new Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}var o=c[e]={exports:{}};u[e][0].call(o.exports,function(n){return s(u[e][1][n]||n)},o,o.exports,i,u,c,a)}return c[e].exports}for(var f="function"==typeof kg&&kg,n=0;n<a.length;n++)s(a[n]);return s}({1:[function(n,e,t){var r,o,i=e.exports={};function u(){throw new Error("setTimeout has not been defined")}function c(){throw new Error("clearTimeout has not been defined")}function a(n){if(r===setTimeout)return setTimeout(n,0);if((r===u||!r)&&setTimeout)return r=setTimeout,setTimeout(n,0);try{return r(n,0)}catch(e){try{return r.call(null,n,0)}catch(e){return r.call(this,n,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:u}catch(n){r=u}try{o="function"==typeof clearTimeout?clearTimeout:c}catch(n){o=c}}();var s,f=[],l=!1,d=-1;function m(){l&&s&&(l=!1,s.length?f=s.concat(f):d=-1,f.length&&g())}function g(){if(!l){var n=a(m);l=!0;for(var e=f.length;e;){for(s=f,f=[];++d<e;)s&&s[d].run();d=-1,e=f.length}s=null,l=!1,function(n){if(o===clearTimeout)return clearTimeout(n);if((o===c||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(n);try{o(n)}catch(e){try{return o.call(null,n)}catch(e){return o.call(this,n)}}}(n)}}function p(n,e){this.fun=n,this.array=e}function v(){}i.nextTick=function(n){var e=new Array(arguments.length-1);if(1<arguments.length)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];f.push(new p(n,e)),1!==f.length||l||a(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=v,i.addListener=v,i.once=v,i.off=v,i.removeListener=v,i.removeAllListeners=v,i.emit=v,i.prependListener=v,i.prependOnceListener=v,i.listeners=function(n){return[]},i.binding=function(n){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(n){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(n,l,e){(function(t){!function(n){var e=setTimeout;function r(){}function u(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],f(n,this)}function o(r,o){for(;3===r._state;)r=r._value;0!==r._state?(r._handled=!0,u._immediateFn(function(){var n=1===r._state?o.onFulfilled:o.onRejected;if(null!==n){var e;try{e=n(r._value)}catch(t){return void c(o.promise,t)}i(o.promise,e)}else(1===r._state?i:c)(o.promise,r._value)})):r._deferreds.push(o)}function i(n,e){try{if(e===n)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if(e instanceof u)return n._state=3,n._value=e,void a(n);if("function"==typeof t)return void f((r=t,o=e,function(){r.apply(o,arguments)}),n)}n._state=1,n._value=e,a(n)}catch(i){c(n,i)}var r,o}function c(n,e){n._state=2,n._value=e,a(n)}function a(n){2===n._state&&0===n._deferreds.length&&u._immediateFn(function(){n._handled||u._unhandledRejectionFn(n._value)});for(var e=0,t=n._deferreds.length;e<t;e++)o(n,n._deferreds[e]);n._deferreds=null}function s(n,e,t){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.promise=t}function f(n,e){var t=!1;try{n(function(n){t||(t=!0,i(e,n))},function(n){t||(t=!0,c(e,n))})}catch(r){if(t)return;t=!0,c(e,r)}}u.prototype["catch"]=function(n){return this.then(null,n)},u.prototype.then=function(n,e){var t=new this.constructor(r);return o(this,new s(n,e,t)),t},u.all=function(n){var a=Array.prototype.slice.call(n);return new u(function(o,i){if(0===a.length)return o([]);var u=a.length;function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}for(var n=0;n<a.length;n++)c(n,a[n])})},u.resolve=function(e){return e&&"object"==typeof e&&e.constructor===u?e:new u(function(n){n(e)})},u.reject=function(t){return new u(function(n,e){e(t)})},u.race=function(o){return new u(function(n,e){for(var t=0,r=o.length;t<r;t++)o[t].then(n,e)})},u._immediateFn="function"==typeof t?function(n){t(n)}:function(n){e(n,0)},u._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},u._setImmediateFn=function(n){u._immediateFn=n},u._setUnhandledRejectionFn=function(n){u._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=u:n.Promise||(n.Promise=u)}(this)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(a,n,s){(function(n,e){var r=a("process/browser.js").nextTick,t=Function.prototype.apply,o=Array.prototype.slice,i={},u=0;function c(n,e){this._id=n,this._clearFn=e}s.setTimeout=function(){return new c(t.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new c(t.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(n){n.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(n,e){clearTimeout(n._idleTimeoutId),n._idleTimeout=e},s.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},s._unrefActive=s.active=function(n){clearTimeout(n._idleTimeoutId);var e=n._idleTimeout;0<=e&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},e))},s.setImmediate="function"==typeof n?n:function(n){var e=u++,t=!(arguments.length<2)&&o.call(arguments,1);return i[e]=!0,r(function(){i[e]&&(t?n.apply(null,t):n.call(null),s.clearImmediate(e))}),e},s.clearImmediate="function"==typeof e?e:function(n){delete i[n]}}).call(this,a("timers").setImmediate,a("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,e,t){var r=n("promise-polyfill"),o="undefined"!=typeof window?window:Function("return this;")();e.exports={boltExport:o.Promise||r}},{"promise-polyfill":2}]},{},[4])(4)});var Rg=Fg.exports.boltExport,Bg=function(n){var t=F.none(),e=[],r=function(n){o()?u(n):e.push(n)},o=function(){return t.isSome()},i=function(n){bn(n,u)},u=function(e){t.each(function(n){p.setTimeout(function(){e(n)},0)})};return n(function(n){t=F.some(n),i(e),e=[]}),{get:r,map:function(t){return Bg(function(e){r(function(n){e(t(n))})})},isReady:o}},Vg={nu:Bg,pure:function(e){return Bg(function(n){n(e)})}},Ng=function(n){p.setTimeout(function(){throw n},0)},_g=function(t){var n=function(n){t().then(n,Ng)};return{map:function(n){return _g(function(){return t().then(n)})},bind:function(e){return _g(function(){return t().then(function(n){return e(n).toPromise()})})},anonBind:function(n){return _g(function(){return t().then(function(){return n.toPromise()})})},toLazy:function(){return Vg.nu(n)},toCached:function(){var n=null;return _g(function(){return null===n&&(n=t()),n})},toPromise:t,get:n}},jg=function(n){return _g(function(){return new Rg(n)})},Hg=function(n){return _g(function(){return Rg.resolve(n)})},zg=function(n,e,t){return Math.abs(n-e)<=t?F.none():n<e?F.some(n+t):F.some(n-t)},Lg=function(){var s=null;return{animate:function(r,o,n,i,e,t){var u=!1,c=function(n){u=!0,e(n)};clearInterval(s);var a=function(n){clearInterval(s),c(n)};s=setInterval(function(){var t=r();zg(t,o,n).fold(function(){clearInterval(s),c(o)},function(n){if(i(n,a),!u){var e=r();(e!==n||Math.abs(e-o)>Math.abs(t-o))&&(clearInterval(s),c(o))}})},t)}}},Pg=function(r,o){return function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return F.none()}([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(n){return e=r<=n.width&&o<=n.height,t=n.keyboard,e?F.some(t):F.none();var e,t}).getOr({portrait:o/5,landscape:r/4})},$g=function(n){var e,t=Sd(n).isPortrait(),r=Pg((e=n).screen.width,e.screen.height),o=t?r.portrait:r.landscape;return(t?n.screen.height:n.screen.width)-n.innerHeight>o?0:o},Wg=function(n,e){var t=Be(n).dom().defaultView;return Ei(n)+Ei(e)-$g(t)},Ug=Wg,Gg=function(n,e,t){var r=Wg(e,t),o=Ei(e)+Ei(t)-r;vi(n,"padding-bottom",o+"px")},qg=Ze([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),Yg="data-"+ai.resolve("position-y-fixed"),Kg="data-"+ai.resolve("y-property"),Xg="data-"+ai.resolve("scrolling"),Jg="data-"+ai.resolve("last-window-height"),Qg=function(n){return Md(n,Yg)},Zg=function(n,e){var t=Gr(n,Kg);return qg.fixed(n,t,e)},np=function(n,e){return qg.scroller(n,e)},ep=function(n){var e=Qg(n);return("true"===Gr(n,Xg)?np:Zg)(n,e)},tp=function(n,e,t){var r=Be(n).dom().defaultView.innerHeight;return Wr(n,Jg,r+"px"),r-e-t},rp=function(n){var e=Ai(n,"["+Yg+"]");return yn(e,ep)},op=function(r,o,i,u){var n,e,t,c,a,s,f,l,d=Be(r).dom().defaultView,m=(l=Gr(f=i,"style"),hi(f,{position:"absolute",top:"0px"}),Wr(f,Yg,"0px"),Wr(f,Kg,"top"),{restore:function(){Wr(f,"style",l||""),Yr(f,Yg),Yr(f,Kg)}}),g=Ei(i),p=Ei(u),v=(c=tp(r,t=g,p),s=Gr(a=r,"style"),nd.register(a),hi(a,{position:"absolute",height:c+"px",width:"100%",top:t+"px"}),Wr(a,Yg,t+"px"),Wr(a,Xg,"true"),Wr(a,Kg,"top"),{restore:function(){nd.deregister(a),Wr(a,"style",s||""),Yr(a,Yg),Yr(a,Xg),Yr(a,Kg)}}),h=(e=Gr(n=u,"style"),hi(n,{position:"absolute",bottom:"0px"}),Wr(n,Yg,"0px"),Wr(n,Kg,"bottom"),{restore:function(){Wr(n,"style",e||""),Yr(n,Yg),Yr(n,Kg)}}),y=!0,b=function(){var n=d.innerHeight;return Md(r,Jg)<n},w=function(){if(y){var n=Ei(i),e=Ei(u),t=tp(r,n,e);Wr(r,Yg,n+"px"),vi(r,"height",t+"px"),Gg(o,r,u)}};return Gg(o,r,u),{setViewportOffset:function(n){Wr(r,Yg,n+"px"),w()},isExpanding:b,isShrinking:x(b),refresh:w,restore:function(){y=!1,m.restore(),v.restore(),h.restore()}}},ip=Qg,up=Lg(),cp="data-"+ai.resolve("last-scroll-top"),ap=function(n){var e=wi(n,"top").getOr("0");return parseInt(e,10)},sp=function(n){return parseInt(n.dom().scrollTop,10)},fp=function(n,e){var t=e+ip(n)+"px";vi(n,"top",t)},lp=function(t,r,o){return jg(function(n){var e=l(sp,t);up.animate(e,r,15,function(n){t.dom().scrollTop=n,vi(t,"top",ap(t)+15+"px")},function(){t.dom().scrollTop=r,vi(t,"top",o+"px"),n(r)},10)})},dp=function(o,i){return jg(function(n){var e=l(sp,o);Wr(o,cp,e());var t=Math.abs(i-e()),r=Math.ceil(t/10);up.animate(e,i,r,function(n,e){Md(o,cp)!==o.dom().scrollTop?e(o.dom().scrollTop):(o.dom().scrollTop=n,Wr(o,cp,n))},function(){o.dom().scrollTop=i,Wr(o,cp,i),n(i)},10)})},mp=function(i,u){return jg(function(n){var e=l(ap,i),t=function(n){vi(i,"top",n+"px")},r=Math.abs(u-e()),o=Math.ceil(r/10);up.animate(e,u,o,t,function(){t(u),n(u)},10)})},gp=function(e,t,r){var o=Be(e).dom().defaultView;return jg(function(n){fp(e,r),fp(t,r),o.scrollTo(0,r),n(r)})},pp=function(n,e,t,r,o){var i=Ug(e,t),u=l(Eg,n);i<r||i<o?dp(e,e.dom().scrollTop-i+o).get(u):r<0&&dp(e,e.dom().scrollTop+r).get(u)},vp=function(u,n){return n(function(r){var o=[],i=0;0===u.length?r([]):bn(u,function(n,e){var t;n.get((t=e,function(n){o[t]=n,++i>=u.length&&r(o)}))})})},hp=function(n,a){return n.fold(function(n,e,t){return vi(n,e,a+(r=t)+"px"),Hg(r);var r},function(n,e){return o=a+(r=e),i=wi(t=n,"top").getOr(r),u=o-parseInt(i,10),c=t.dom().scrollTop+u,lp(t,c,o);var t,r,o,i,u,c})},yp=function(n,e){var t=rp(n),r=yn(t,function(n){return hp(n,e)});return vp(r,jg)},bp=function(e,t,n,r,o,i){var u,c,a=(u=function(n){return gp(e,t,n)},c=ao(Vg.pure({})),{start:function(e){var n=Vg.nu(function(n){return u(e).get(n)});c.set(n)},idle:function(n){c.get().get(function(){n()})}}),s=Om(function(){a.idle(function(){yp(n,r.pageYOffset).get(function(){var n;(n=nm(i),F.from(n[0]).bind(function(n){var e=n.top()-t.dom().scrollTop;return e>r.innerHeight+5||e<-5?F.some({top:A(e),bottom:A(e+n.height())}):F.none()})).each(function(n){t.dom().scrollTop=t.dom().scrollTop+n.top()}),a.start(0),o.refresh()})})},1e3),f=wd(ue.fromDom(r),"scroll",function(){r.pageYOffset<0||s.throttle()});return yp(n,r.pageYOffset).get(h),{unbind:f.unbind}},wp=function(n){var t=n.cWin(),e=n.ceBody(),r=n.socket(),o=n.toolstrip(),i=n.toolbar(),u=n.contentElement(),c=n.keyboardType(),a=n.outerWindow(),s=n.dropup(),f=op(r,e,o,s),l=c(n.outerBody(),t,ye(),u,o,i),d=Od(a,{onChange:I,onReady:f.refresh});d.onAdjustment(function(){f.refresh()});var m=wd(ue.fromDom(a),"resize",function(){f.isExpanding()&&f.refresh()}),g=bp(o,r,n.outerBody(),a,f,t),p=function(t,e){var n=t.document,r=ue.fromTag("div");no(r,ai.resolve("unfocused-selections")),ze(ue.fromDom(n.documentElement),r);var o=wd(r,"touchstart",function(n){n.prevent(),Dg(t,e),u()}),i=function(n){var e=ue.fromTag("span");return il(e,[ai.resolve("layer-editor"),ai.resolve("unfocused-selection")]),hi(e,{left:n.left()+"px",top:n.top()+"px",width:n.width()+"px",height:n.height()+"px"}),e},u=function(){Pe(r)};return{update:function(){u();var n=nm(t),e=yn(n,i);Le(r,e)},isActive:function(){return 0<_e(r).length},destroy:function(){o.unbind(),$e(r)},clear:u}}(t,u),v=function(){p.clear()};return{toEditing:function(){l.toEditing(),v()},toReading:function(){l.toReading()},onToolbarTouch:function(n){l.onToolbarTouch(n)},refreshSelection:function(){p.isActive()&&p.update()},clearSelection:v,highlightSelection:function(){p.update()},scrollIntoView:function(n,e){pp(t,r,s,n,e)},updateToolbarPadding:I,setViewportOffset:function(n){f.setViewportOffset(n),mp(r,n).get(h)},syncHeight:function(){vi(u,"height",u.dom().contentWindow.document.body.scrollHeight+"px")},refreshStructure:f.refresh,destroy:function(){f.restore(),d.destroy(),g.unbind(),m.unbind(),l.destroy(),p.destroy(),Cg(ye(),go)}}},xp=function(r,n){var o=Tm(),i=Bf(),u=Bf(),c=Rf(),a=Rf();return{enter:function(){n.hide();var t=ue.fromDom(p.document);pm.getActiveApi(r.editor).each(function(n){i.set({socketHeight:wi(r.socket,"height"),iframeHeight:wi(n.frame(),"height"),outerScroll:p.document.body.scrollTop}),u.set({exclusives:Nm.exclusive(t,"."+nd.scrollable())}),no(r.container,ai.resolve("fullscreen-maximized")),wm(r.container,n.body()),o.maximize(),vi(r.socket,"overflow","scroll"),vi(r.socket,"-webkit-overflow-scrolling","touch"),mo(n.body());var e=Ce(["cWin","ceBody","socket","toolstrip","toolbar","dropup","contentElement","cursor","keyboardType","isScrolling","outerWindow","outerBody"],[]);c.set(wp(e({cWin:n.win(),ceBody:n.body(),socket:r.socket,toolstrip:r.toolstrip,toolbar:r.toolbar,dropup:r.dropup.element(),contentElement:n.frame(),cursor:I,outerBody:r.body,outerWindow:r.win,keyboardType:Ig.stubborn,isScrolling:function(){return u.get().exists(function(n){return n.socket.isScrolling()})}}))),c.run(function(n){n.syncHeight()}),a.set(Ag(n,c,r.toolstrip,r.socket,r.dropup))})},refreshStructure:function(){c.run(function(n){n.refreshStructure()})},exit:function(){o.restore(),a.clear(),c.clear(),n.show(),i.on(function(n){n.socketHeight.each(function(n){vi(r.socket,"height",n)}),n.iframeHeight.each(function(n){vi(r.editor.getFrame(),"height",n)}),p.document.body.scrollTop=n.scrollTop}),i.clear(),u.on(function(n){n.exclusives.unbind()}),u.clear(),eo(r.container,ai.resolve("fullscreen-maximized")),xm(),nd.deregister(r.toolbar),xi(r.socket,"overflow"),xi(r.socket,"-webkit-overflow-scrolling"),go(r.editor.getFrame()),pm.getActiveApi(r.editor).each(function(n){n.clearSelection()})}}},Tp=function(n){var e=Ut("Getting IosWebapp schema",Cm,n);vi(e.toolstrip,"width","100%"),vi(e.container,"position","relative");var t=yl(km(function(){e.setReadOnly(e.readOnlyOnInit()),r.enter()},e.translate));e.alloy.add(t);var r=xp(e,{show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}});return{setReadOnly:e.setReadOnly,refreshStructure:r.refreshStructure,enter:r.enter,exit:r.exit,destroy:I}},Sp=tinymce.util.Tools.resolve("tinymce.EditorManager"),Op=function(n){var e=ht(n.settings,"skin_url").fold(function(){return Sp.baseURL+"/skins/lightgray"},function(n){return n});return{content:e+"/content.mobile.min.css",ui:e+"/skin.mobile.min.css"}},kp=function(n,e,t){n.system().broadcastOn([wo.formatChanged()],{command:e,state:t})},Cp=function(r,n){var e=R(n.formatter.get());bn(e,function(e){n.formatter.formatChanged(e,function(n){kp(r,e,n)})}),bn(["ul","ol"],function(t){n.selection.selectorChanged(t,function(n,e){kp(r,t,n)})})},Ep=(A(["x-small","small","medium","large","x-large"]),function(n){var e=function(){n._skinLoaded=!0,n.fire("SkinLoaded")};return function(){n.initialized?e():n.on("init",e)}}),Dp=A("toReading"),Ip=A("toEditing");yo.add("mobile",function(D){return{getNotificationManagerImpl:function(){return{open:A({progressBar:{value:I},close:I}),close:I,reposition:I,getArgs:h}},renderUI:function(n){var e=Op(D);0==(!1===D.settings.skin)?(D.contentCSS.push(e.content),ho.DOM.styleSheetLoader.load(e.ui,Ep(D))):Ep(D)();var t,r,o,i,u,c,a,s,f,l,d,m,g,p,v,h,y,b=function(){D.fire("scrollIntoView")},w=ue.fromTag("div"),x=zn.detect().os.isAndroid()?(s=b,f=xg({classes:[ai.resolve("android-container")]}),l=_m(),d=Rf(),m=zm(d),g=Lm(),p=rg(I,s),f.add(l.wrapper()),f.add(g),f.add(p.component()),{system:A(f),element:f.element,init:function(n){d.set(Em(n))},exit:function(){d.run(function(n){n.exit(),jl.remove(g,m)})},setToolbarGroups:function(n){var e=l.createGroups(n);l.setGroups(e)},setContextToolbar:function(n){var e=l.createGroups(n);l.setContextToolbar(e)},focusToolbar:function(){l.focus()},restoreToolbar:function(){l.restoreToolbar()},updateMode:function(n){Pm(g,m,n,f.root())},socket:A(g),dropup:A(p)}):(t=b,r=xg({classes:[ai.resolve("ios-container")]}),o=_m(),i=Rf(),u=zm(i),c=Lm(),a=rg(function(){i.run(function(n){n.refreshStructure()})},t),r.add(o.wrapper()),r.add(c),r.add(a.component()),{system:A(r),element:r.element,init:function(n){i.set(Tp(n))},exit:function(){i.run(function(n){jl.remove(c,u),n.exit()})},setToolbarGroups:function(n){var e=o.createGroups(n);o.setGroups(e)},setContextToolbar:function(n){var e=o.createGroups(n);o.setContextToolbar(e)},focusToolbar:function(){o.focus()},restoreToolbar:function(){o.restoreToolbar()},updateMode:function(n){Pm(c,u,n,r.root())},socket:A(c),dropup:A(a)}),T=ue.fromDom(n.targetNode);we("element","offset"),h=w,(y=v=T,F.from(y.dom().nextSibling).map(ue.fromDom)).fold(function(){Ne(v).each(function(n){ze(n,h)})},function(n){var e,t;t=h,Ne(e=n).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})}),function(n,e){ze(n,e.element());var t=_e(e.element());bn(t,function(n){e.getByDom(n).each(Ue)})}(w,x.system());var S=n.targetNode.ownerDocument.defaultView,O=Od(S,{onChange:function(){x.system().broadcastOn([wo.orientationChanged()],{width:kd(S)})},onReady:I}),k=function(n,e,t,r){!1===r&&D.selection.collapse();var o=C(n,e,t);x.setToolbarGroups(!0===r?o.readOnly:o.main),D.setMode(!0===r?"readonly":"design"),D.fire(!0===r?Dp():Ip()),x.updateMode(r)},C=function(n,e,t){var r=n.get();return{readOnly:r.backToMask.concat(e.get()),main:r.backToMask.concat(t.get())}},E=function(n,e){return D.on(n,e),{unbind:function(){D.off(n)}}};return D.on("init",function(){x.init({editor:{getFrame:function(){return ue.fromDom(D.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:I}},onToReading:function(n){return E(Dp(),n)},onToEditing:function(n){return E(Ip(),n)},onScrollToCursor:function(e){return D.on("scrollIntoView",function(n){e(n)}),{unbind:function(){D.off("scrollIntoView"),O.destroy()}}},onTouchToolstrip:function(){t()},onTouchContent:function(){var n,e=ue.fromDom(D.editorContainer.querySelector("."+ai.resolve("toolbar")));(n=e,vo(n).bind(function(n){return x.system().getByDom(n).toOption()})).each(te),x.restoreToolbar(),t()},onTapContent:function(n){var e=n.target();"img"===me(e)?(D.selection.select(e.dom()),n.kill()):"a"===me(e)&&x.system().getByDom(ue.fromDom(D.editorContainer)).each(function(n){co.isAlpha(n)&&bo(e.dom())})}},container:ue.fromDom(D.editorContainer),socket:ue.fromDom(D.contentAreaContainer),toolstrip:ue.fromDom(D.editorContainer.querySelector("."+ai.resolve("toolstrip"))),toolbar:ue.fromDom(D.editorContainer.querySelector("."+ai.resolve("toolbar"))),dropup:x.dropup(),alloy:x.system(),translate:I,setReadOnly:function(n){k(a,c,u,n)},readOnlyOnInit:function(){return!1}});var t=function(){x.dropup().disappear(function(){x.system().broadcastOn([wo.dropupDismissed()],{})})},n={label:"The first group",scrollable:!1,items:[$a.forToolbar("back",function(){D.selection.collapse(),x.exit()},{})]},e={label:"Back to read only",scrollable:!1,items:[$a.forToolbar("readonly-back",function(){k(a,c,u,!0)},{})]},r=gd(x,D),o=pd(D.settings,r),i={label:"The extra group",scrollable:!1,items:[]},u=ao([{label:"the action group",scrollable:!0,items:o},i]),c=ao([{label:"The read only mode group",scrollable:!0,items:[]},i]),a=ao({backToMask:[n],backToReadOnly:[e]});Cp(x,D)}),D.on("remove",function(){x.exit()}),D.on("detach",function(){var e,n;e=x.system(),n=_e(e.element()),bn(n,function(n){e.getByDom(n).each(We)}),$e(e.element()),x.system().destroy(),$e(w)}),{iframeContainer:x.socket().element().dom(),editorContainer:x.element().dom()}}}})}(window);
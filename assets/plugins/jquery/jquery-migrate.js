/*!
 * jQuery Migrate - v3.0.0 - 2016-06-09
 * Copyright jQuery Foundation and other contributors
 */
(function( jQuery, window ) {
"use strict";


jQuery.migrateVersion = "3.0.0";


( function() {

    // Support: IE9 only
    // IE9 only creates console object when dev tools are first opened
    // Also, avoid Function#bind here to simplify PhantomJS usage
    var log = window.console && window.console.log &&
            function() { window.console.log.apply( window.console, arguments ); },
        rbadVersions = /^[12]\./;

    if ( !log ) {
        return;
    }

    // Need jQuery 3.0.0+ and no older Migrate loaded
    if ( !jQuery || rbadVersions.test( jQuery.fn.jquery ) ) {
        log( "JQMIGRATE: jQuery 3.0.0+ REQUIRED" );
    }
    if ( jQuery.migrateWarnings ) {
        log( "JQMIGRATE: Migrate plugin loaded multiple times" );
    }

    // Show a message on the console so devs know we're active
    log( "JQMIGRATE: Migrate is installed" +
        ( jQuery.migrateMute ? "" : " with logging active" ) +
        ", version " + jQuery.migrateVersion );

} )();

var warnedAbout = {};

// List of warnings already given; public read only
jQuery.migrateWarnings = [];

// Set to false to disable traces that appear with warnings
if ( jQuery.migrateTrace === undefined ) {
    jQuery.migrateTrace = true;
}

// Forget any warnings we've already given; public
jQuery.migrateReset = function() {
    warnedAbout = {};
    jQuery.migrateWarnings.length = 0;
};

function migrateWarn( msg ) {
    var console = window.console;
    if ( !warnedAbout[ msg ] ) {
        warnedAbout[ msg ] = true;
        jQuery.migrateWarnings.push( msg );
        if ( console && console.warn && !jQuery.migrateMute ) {
            console.warn( "JQMIGRATE: " + msg );
            if ( jQuery.migrateTrace && console.trace ) {
                console.trace();
            }
        }
    }
}

function migrateWarnProp( obj, prop, value, msg ) {
    Object.defineProperty( obj, prop, {
        configurable: true,
        enumerable: true,
        get: function() {
            migrateWarn( msg );
            return value;
        }
    } );
}

if ( document.compatMode === "BackCompat" ) {

    // JQuery has never supported or tested Quirks Mode
    migrateWarn( "jQuery is not compatible with Quirks Mode" );
}


var oldInit = jQuery.fn.init,
    oldIsNumeric = jQuery.isNumeric,
    oldFind = jQuery.find,
    rattrHashTest = /\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/,
    rattrHashGlob = /\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/g;

jQuery.fn.init = function( arg1 ) {
    var args = Array.prototype.slice.call( arguments );

    if ( typeof arg1 === "string" && arg1 === "#" ) {

        // JQuery( "#" ) is a bogus ID selector, but it returned an empty set before jQuery 3.0
        migrateWarn( "jQuery( '#' ) is not a valid selector" );
        args[ 0 ] = [];
    }

    return oldInit.apply( this, args );
};
jQuery.fn.init.prototype = jQuery.fn;

jQuery.find = function( selector ) {
    var args = Array.prototype.slice.call( arguments );

    // Support: PhantomJS 1.x
    // String#match fails to match when used with a //g RegExp, only on some strings
    if ( typeof selector === "string" && rattrHashTest.test( selector ) ) {

        // The nonstandard and undocumented unquoted-hash was removed in jQuery 1.12.0
        // First see if qS thinks it's a valid selector, if so avoid a false positive
        try {
            document.querySelector( selector );
        } catch ( err1 ) {

            // Didn't *look* valid to qSA, warn and try quoting what we think is the value
            selector = selector.replace( rattrHashGlob, function( _, attr, op, value ) {
                return "[" + attr + op + "\"" + value + "\"]";
            } );

            // If the regexp *may* have created an invalid selector, don't update it
            // Note that there may be false alarms if selector uses jQuery extensions
            try {
                document.querySelector( selector );
                migrateWarn( "Attribute selector with '#' must be quoted: " + args[ 0 ] );
                args[ 0 ] = selector;
            } catch ( err2 ) {
                migrateWarn( "Attribute selector with '#' was not fixed: " + args[ 0 ] );
            }
        }
    }

    return oldFind.apply( this, args );
};

// Copy properties attached to original jQuery.find method (e.g. .attr, .isXML)
var findProp;
for ( findProp in oldFind ) {
    if ( Object.prototype.hasOwnProperty.call( oldFind, findProp ) ) {
        jQuery.find[ findProp ] = oldFind[ findProp ];
    }
}

// The number of elements contained in the matched element set
jQuery.fn.size = function() {
    migrateWarn( "jQuery.fn.size() is deprecated; use the .length property" );
    return this.length;
};

jQuery.parseJSON = function() {
    migrateWarn( "jQuery.parseJSON is deprecated; use JSON.parse" );
    return JSON.parse.apply( null, arguments );
};

jQuery.isNumeric = function( val ) {

    // The jQuery 2.2.3 implementation of isNumeric
    function isNumeric2( obj ) {
        var realStringObj = obj && obj.toString();
        return !jQuery.isArray( obj ) && ( realStringObj - parseFloat( realStringObj ) + 1 ) >= 0;
    }

    var newValue = oldIsNumeric( val ),
        oldValue = isNumeric2( val );

    if ( newValue !== oldValue ) {
        migrateWarn( "jQuery.isNumeric() should not be called on constructed objects" );
    }

    return oldValue;
};

migrateWarnProp( jQuery, "unique", jQuery.uniqueSort,
    "jQuery.unique is deprecated, use jQuery.uniqueSort" );

// Now jQuery.expr.pseudos is the standard incantation
migrateWarnProp( jQuery.expr, "filters", jQuery.expr.pseudos,
    "jQuery.expr.filters is now jQuery.expr.pseudos" );
migrateWarnProp( jQuery.expr, ":", jQuery.expr.pseudos,
    "jQuery.expr[\":\"] is now jQuery.expr.pseudos" );


var oldAjax = jQuery.ajax;

jQuery.ajax = function( ) {
    var jQXHR = oldAjax.apply( this, arguments );

    // Be sure we got a jQXHR (e.g., not sync)
    if ( jQXHR.promise ) {
        migrateWarnProp( jQXHR, "success", jQXHR.done,
            "jQXHR.success is deprecated and removed" );
        migrateWarnProp( jQXHR, "error", jQXHR.fail,
            "jQXHR.error is deprecated and removed" );
        migrateWarnProp( jQXHR, "complete", jQXHR.always,
            "jQXHR.complete is deprecated and removed" );
    }

    return jQXHR;
};


var oldRemoveAttr = jQuery.fn.removeAttr,
    oldToggleClass = jQuery.fn.toggleClass,
    rmatchNonSpace = /\S+/g;

jQuery.fn.removeAttr = function( name ) {
    var self = this;

    jQuery.each( name.match( rmatchNonSpace ), function( i, attr ) {
        if ( jQuery.expr.match.bool.test( attr ) ) {
            migrateWarn( "jQuery.fn.removeAttr no longer sets boolean properties: " + attr );
            self.prop( attr, false );
        }
    } );

    return oldRemoveAttr.apply( this, arguments );
};

jQuery.fn.toggleClass = function( state ) {

    // Only deprecating no-args or single boolean arg
    if ( state !== undefined && typeof state !== "boolean" ) {
        return oldToggleClass.apply( this, arguments );
    }

    migrateWarn( "jQuery.fn.toggleClass( boolean ) is deprecated" );

    // Toggle entire class name of each element
    return this.each( function() {
        var className = this.getAttribute && this.getAttribute( "class" ) || "";

        if ( className ) {
            jQuery.data( this, "__className__", className );
        }

        // If the element has a class name or if we're passed `false`,
        // then remove the whole classname (if there was one, the above saved it).
        // Otherwise bring back whatever was previously saved (if anything),
        // falling back to the empty string if nothing was stored.
        if ( this.setAttribute ) {
            this.setAttribute( "class",
                className || state === false ?
                "" :
                jQuery.data( this, "__className__" ) || ""
            );
        }
    } );
};


var internalSwapCall = false;

// If this version of jQuery has .swap(), don't false-alarm on internal uses
if ( jQuery.swap ) {
    jQuery.each( [ "height", "width", "reliableMarginRight" ], function( _, name ) {
        var oldHook = jQuery.cssHooks[ name ] && jQuery.cssHooks[ name ].get;

        if ( oldHook ) {
            jQuery.cssHooks[ name ].get = function() {
                var ret;

                internalSwapCall = true;
                ret = oldHook.apply( this, arguments );
                internalSwapCall = false;
                return ret;
            };
        }
    } );
}

jQuery.swap = function( elem, options, callback, args ) {
    var ret, name,
        old = {};

    if ( !internalSwapCall ) {
        migrateWarn( "jQuery.swap() is undocumented and deprecated" );
    }

    // Remember the old values, and insert the new ones
    for ( name in options ) {
        old[ name ] = elem.style[ name ];
        elem.style[ name ] = options[ name ];
    }

    ret = callback.apply( elem, args || [] );

    // Revert the old values
    for ( name in options ) {
        elem.style[ name ] = old[ name ];
    }

    return ret;
};

var oldData = jQuery.data;

jQuery.data = function( elem, name, value ) {
    var curData;

    // If the name is transformed, look for the un-transformed name in the data object
    if ( name && name !== jQuery.camelCase( name ) ) {
        curData = jQuery.hasData( elem ) && oldData.call( this, elem );
        if ( curData && name in curData ) {
            migrateWarn( "jQuery.data() always sets/gets camelCased names: " + name );
            if ( arguments.length > 2 ) {
                curData[ name ] = value;
            }
            return curData[ name ];
        }
    }

    return oldData.apply( this, arguments );
};

var oldTweenRun = jQuery.Tween.prototype.run;

jQuery.Tween.prototype.run = function( percent ) {
    if ( jQuery.easing[ this.easing ].length > 1 ) {
        migrateWarn(
            "easing function " +
            "\"jQuery.easing." + this.easing.toString() +
            "\" should use only first argument"
        );

        jQuery.easing[ this.easing ] = jQuery.easing[ this.easing ].bind(
            jQuery.easing,
            percent, this.options.duration * percent, 0, 1, this.options.duration
        );
    }

    oldTweenRun.apply( this, arguments );
};

var oldLoad = jQuery.fn.load,
    originalFix = jQuery.event.fix;

jQuery.event.props = [];
jQuery.event.fixHooks = {};

jQuery.event.fix = function( originalEvent ) {
    var event,
        type = originalEvent.type,
        fixHook = this.fixHooks[ type ],
        props = jQuery.event.props;

    if ( props.length ) {
        migrateWarn( "jQuery.event.props are deprecated and removed: " + props.join() );
        while ( props.length ) {
            jQuery.event.addProp( props.pop() );
        }
    }

    if ( fixHook && !fixHook._migrated_ ) {
        fixHook._migrated_ = true;
        migrateWarn( "jQuery.event.fixHooks are deprecated and removed: " + type );
        if ( ( props = fixHook.props ) && props.length ) {
            while ( props.length ) {
               jQuery.event.addProp( props.pop() );
            }
        }
    }

    event = originalFix.call( this, originalEvent );

    return fixHook && fixHook.filter ? fixHook.filter( event, originalEvent ) : event;
};

jQuery.each( [ "load", "unload", "error" ], function( _, name ) {

    jQuery.fn[ name ] = function() {
        var args = Array.prototype.slice.call( arguments, 0 );

        // If this is an ajax load() the first arg should be the string URL;
        // technically this could also be the "Anything" arg of the event .load()
        // which just goes to show why this dumb signature has been deprecated!
        // jQuery custom builds that exclude the Ajax module justifiably die here.
        if ( name === "load" && typeof args[ 0 ] === "string" ) {
            return oldLoad.apply( this, args );
        }

        migrateWarn( "jQuery.fn." + name + "() is deprecated" );

        args.splice( 0, 0, name );
        if ( arguments.length ) {
            return this.on.apply( this, args );
        }

        // Use .triggerHandler here because:
        // - load and unload events don't need to bubble, only applied to window or image
        // - error event should not bubble to window, although it does pre-1.7
        // See http://bugs.jquery.com/ticket/11820
        this.triggerHandler.apply( this, args );
        return this;
    };

} );

// Trigger "ready" event only once, on document ready
jQuery( function() {
    jQuery( document ).triggerHandler( "ready" );
} );

jQuery.event.special.ready = {
    setup: function() {
        if ( this === document ) {
            migrateWarn( "'ready' event is deprecated" );
        }
    }
};

jQuery.fn.extend( {

    bind: function( types, data, fn ) {
        migrateWarn( "jQuery.fn.bind() is deprecated" );
        return this.on( types, null, data, fn );
    },
    unbind: function( types, fn ) {
        migrateWarn( "jQuery.fn.unbind() is deprecated" );
        return this.off( types, null, fn );
    },
    delegate: function( selector, types, data, fn ) {
        migrateWarn( "jQuery.fn.delegate() is deprecated" );
        return this.on( types, selector, data, fn );
    },
    undelegate: function( selector, types, fn ) {
        migrateWarn( "jQuery.fn.undelegate() is deprecated" );
        return arguments.length === 1 ?
            this.off( selector, "**" ) :
            this.off( types, selector || "**", fn );
    }
} );


var oldOffset = jQuery.fn.offset;

jQuery.fn.offset = function() {
    var docElem,
        elem = this[ 0 ],
        origin = { top: 0, left: 0 };

    if ( !elem || !elem.nodeType ) {
        migrateWarn( "jQuery.fn.offset() requires a valid DOM element" );
        return origin;
    }

    docElem = ( elem.ownerDocument || document ).documentElement;
    if ( !jQuery.contains( docElem, elem ) ) {
        migrateWarn( "jQuery.fn.offset() requires an element connected to a document" );
        return origin;
    }

    return oldOffset.apply( this, arguments );
};


var oldParam = jQuery.param;

jQuery.param = function( data, traditional ) {
    var ajaxTraditional = jQuery.ajaxSettings && jQuery.ajaxSettings.traditional;

    if ( traditional === undefined && ajaxTraditional ) {

        migrateWarn( "jQuery.param() no longer uses jQuery.ajaxSettings.traditional" );
        traditional = ajaxTraditional;
    }

    return oldParam.call( this, data, traditional );
};

var oldSelf = jQuery.fn.andSelf || jQuery.fn.addBack;

jQuery.fn.andSelf = function() {
    migrateWarn( "jQuery.fn.andSelf() replaced by jQuery.fn.addBack()" );
    return oldSelf.apply( this, arguments );
};


var oldDeferred = jQuery.Deferred,
    tuples = [

        // Action, add listener, callbacks, .then handlers, final state
        [ "resolve", "done", jQuery.Callbacks( "once memory" ),
            jQuery.Callbacks( "once memory" ), "resolved" ],
        [ "reject", "fail", jQuery.Callbacks( "once memory" ),
            jQuery.Callbacks( "once memory" ), "rejected" ],
        [ "notify", "progress", jQuery.Callbacks( "memory" ),
            jQuery.Callbacks( "memory" ) ]
    ];

jQuery.Deferred = function( func ) {
    var deferred = oldDeferred(),
        promise = deferred.promise();

    deferred.pipe = promise.pipe = function( /* fnDone, fnFail, fnProgress */ ) {
        var fns = arguments;

        migrateWarn( "deferred.pipe() is deprecated" );

        return jQuery.Deferred( function( newDefer ) {
            jQuery.each( tuples, function( i, tuple ) {
                var fn = jQuery.isFunction( fns[ i ] ) && fns[ i ];

                // Deferred.done(function() { bind to newDefer or newDefer.resolve })
                // deferred.fail(function() { bind to newDefer or newDefer.reject })
                // deferred.progress(function() { bind to newDefer or newDefer.notify })
                deferred[ tuple[ 1 ] ]( function() {
                    var returned = fn && fn.apply( this, arguments );
                    if ( returned && jQuery.isFunction( returned.promise ) ) {
                        returned.promise()
                            .done( newDefer.resolve )
                            .fail( newDefer.reject )
                            .progress( newDefer.notify );
                    } else {
                        newDefer[ tuple[ 0 ] + "With" ](
                            this === promise ? newDefer.promise() : this,
                            fn ? [ returned ] : arguments
                        );
                    }
                } );
            } );
            fns = null;
        } ).promise();

    };

    if ( func ) {
        func.call( deferred, deferred );
    }

    return deferred;
};



})( jQuery, window );

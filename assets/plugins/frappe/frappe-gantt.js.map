{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///webpack/bootstrap 40f9d74a4b17f4d126f3", "webpack:///./src/Gantt.js", "webpack:///./src/gantt.scss?b27d", "webpack:///./src/gantt.scss", "webpack:///./~/css-loader/lib/css-base.js", "webpack:///./~/style-loader/addStyles.js", "webpack:///./src/Bar.js", "webpack:///./src/Arrow.js", "webpack:///./~/deepmerge/dist/umd.js"], "names": ["<PERSON><PERSON><PERSON>", "element", "tasks", "config", "self", "init", "set_defaults", "change_view_mode", "unselect_all", "view_is", "get_bar", "trigger_event", "refresh", "view_mode", "merge", "require", "defaults", "header_height", "column_width", "step", "view_modes", "bar", "height", "corner_radius", "arrow", "curve", "padding", "date_format", "custom_popup_html", "reset_variables", "document", "querySelector", "SVGElement", "HTMLElement", "TypeError", "_tasks", "_bars", "_arrows", "element_groups", "updated_tasks", "mode", "set_scale", "prepare", "render", "prepare_tasks", "prepare_dependencies", "prepare_dates", "prepare_canvas", "map", "task", "i", "_start", "moment", "start", "_end", "end", "diff", "_index", "startOf", "add", "clone", "invalid", "dependencies", "deps", "split", "d", "trim", "filter", "id", "generate_id", "dependency_map", "t", "push", "gantt_start", "gantt_end", "set_gantt_dates", "setup_dates", "canvas", "Snap", "addClass", "clear", "setup_groups", "make_grid", "make_dates", "make_bars", "make_arrows", "map_arrows_on_bars", "set_width", "set_scroll_position", "bind_grid_click", "subtract", "endOf", "dates", "cur_date", "groups", "group", "attr", "scale", "cur_width", "node", "getBoundingClientRect", "width", "actual_width", "select", "parent_element", "parentElement", "scroll_pos", "get_min_date", "scrollLeft", "reduce", "acc", "curr", "isSameOrBefore", "make_grid_background", "make_grid_rows", "make_grid_header", "make_grid_ticks", "make_grid_highlights", "grid_width", "length", "grid_height", "rect", "appendTo", "grid", "header_width", "rows", "lines", "row_width", "row_height", "row_y", "line", "tick_x", "tick_y", "tick_height", "date", "tick_class", "day", "month", "path", "format", "x", "y", "daysInMonth", "get_dates_to_draw", "text", "lower_x", "lower_y", "lower_text", "upper_text", "$upper_text", "upper_x", "upper_y", "getBBox", "x2", "remove", "last_date", "get_date_info", "date_text", "year", "base_pos", "x_pos", "arrows", "dependency", "get_task", "dep", "arr", "concat", "from_task", "to_task", "click", "details", "selectAll", "for<PERSON>ach", "el", "removeClass", "modes", "Array", "isArray", "find", "name", "Math", "random", "toString", "slice", "event", "args", "apply", "Bar", "gt", "draw", "bind", "action_completed", "prepare_values", "prepare_plugins", "compute_x", "compute_y", "duration", "progress_width", "progress", "custom_class", "bar_group", "handle_group", "plugin", "Element", "Paper", "global", "Fragment", "prototype", "getX", "getY", "getWidth", "getHeight", "getEndX", "draw_bar", "draw_progress_bar", "draw_label", "draw_resize_handles", "$bar", "$bar_progress", "update_label_position", "handle_width", "polygon", "get_progress_polygon_points", "bar_progress", "setup_click_event", "show_details", "bind_resize", "bind_drag", "bind_resize_progress", "popover_group", "details_box", "render_details", "f", "shadow", "e", "get_details_position", "transform", "html", "get_details_html", "foreign_object", "parse", "append", "isFunction", "start_date", "end_date", "heading", "line_1", "line_2", "get_handles", "left", "right", "drag", "onmove_left", "onstart", "onstop_left", "onmove_right", "onstop_right", "dx", "dy", "onmove_handle_right", "onstop_handle_right", "onmove_handle_left", "onstop_handle_left", "onmove", "onstop", "handle", "on_move", "on_start", "on_stop", "max_dx", "min_dx", "owidth", "finaldx", "progress_changed", "set_action_completed", "ox", "oy", "run_method_for_dependencies", "get_snap_position", "update_bar_position", "date_changed", "fn", "dm", "deptask", "dt", "xs", "valid_x", "prev", "update_attr", "update_handle_position", "update_progressbar_position", "update_arrow_position", "update_details_position", "hasClass", "toggleClass", "compute_start_end_date", "new_start_date", "new_end_date", "new_progress", "compute_progress", "setTimeout", "x_in_units", "width_in_units", "parseInt", "odx", "rem", "position", "value", "isNaN", "label", "update", "functionToCheck", "getType", "call", "Arrow", "start_x", "condition", "start_y", "end_x", "end_y", "from_is_below_to", "clockwise", "curve_y", "offset", "down_1", "down_2"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,uBAAe;AACf;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;;;;;;;;;;;mBCzBwBA,K;;AALxB;;AAEA;;;;AACA;;;;;;AAEe,UAASA,KAAT,CAAeC,OAAf,EAAwBC,KAAxB,EAA4C;AAAA,MAAbC,MAAa,uEAAJ,EAAI;;;AAE1D,MAAMC,OAAO,EAAb;;AAEA,WAASC,IAAT,GAAgB;AACfC;;AAEA;AACAF,QAAKG,gBAAL,GAAwBA,gBAAxB;AACAH,QAAKI,YAAL,GAAoBA,YAApB;AACAJ,QAAKK,OAAL,GAAeA,OAAf;AACAL,QAAKM,OAAL,GAAeA,OAAf;AACAN,QAAKO,aAAL,GAAqBA,aAArB;AACAP,QAAKQ,OAAL,GAAeA,OAAf;;AAEA;AACAL,oBAAiBH,KAAKD,MAAL,CAAYU,SAA7B;AACA;;AAED,WAASP,YAAT,GAAwB;;AAEvB,OAAMQ,QAAQ,mBAAAC,CAAQ,CAAR,CAAd;;AAEA,OAAMC,WAAW;AAChBC,mBAAe,EADC;AAEhBC,kBAAc,EAFE;AAGhBC,UAAM,EAHU;AAIhBC,gBAAY,CACX,aADW,EAEX,UAFW,EAGX,KAHW,EAIX,MAJW,EAKX,OALW,CAJI;AAWhBC,SAAK;AACJC,aAAQ,EADJ;AAEJC,oBAAe;AAFX,KAXW;AAehBC,WAAO;AACNC,YAAO;AADD,KAfS;AAkBhBC,aAAS,EAlBO;AAmBhBb,eAAW,KAnBK;AAoBhBc,iBAAa,YApBG;AAqBhBC,uBAAmB;AArBH,IAAjB;AAuBAxB,QAAKD,MAAL,GAAcW,MAAME,QAAN,EAAgBb,MAAhB,CAAd;;AAEA0B,mBAAgB3B,KAAhB;AACA;;AAED,WAAS2B,eAAT,CAAyB3B,KAAzB,EAAgC;AAC/B,OAAG,OAAOD,OAAP,KAAmB,QAAtB,EAAgC;AAC/BG,SAAKH,OAAL,GAAe6B,SAASC,aAAT,CAAuB9B,OAAvB,CAAf;AACA,IAFD,MAEO,IAAIA,mBAAmB+B,UAAvB,EAAmC;AACzC5B,SAAKH,OAAL,GAAeA,OAAf;AACA,IAFM,MAEA,IAAIA,mBAAmBgC,WAAvB,EAAoC;AAC1C7B,SAAKH,OAAL,GAAeA,QAAQ8B,aAAR,CAAsB,KAAtB,CAAf;AACA,IAFM,MAEA;AACN,UAAM,IAAIG,SAAJ,CAAc,+DACnB,oEADK,CAAN;AAEA;;AAED9B,QAAK+B,MAAL,GAAcjC,KAAd;;AAEAE,QAAKgC,KAAL,GAAa,EAAb;AACAhC,QAAKiC,OAAL,GAAe,EAAf;AACAjC,QAAKkC,cAAL,GAAsB,EAAtB;AACA;;AAED,WAAS1B,OAAT,CAAiB2B,aAAjB,EAAgC;AAC/BV,mBAAgBU,aAAhB;AACAhC,oBAAiBH,KAAKD,MAAL,CAAYU,SAA7B;AACA;;AAED,WAASN,gBAAT,CAA0BiC,IAA1B,EAAgC;AAC/BC,aAAUD,IAAV;AACAE;AACAC;AACA;AACAhC,iBAAc,aAAd,EAA6B,CAAC6B,IAAD,CAA7B;AACA;;AAED,WAASE,OAAT,GAAmB;AAClBE;AACAC;AACAC;AACAC;AACA;;AAED,WAASH,aAAT,GAAyB;;AAExB;AACAxC,QAAKF,KAAL,GAAaE,KAAK+B,MAAL,CAAYa,GAAZ,CAAgB,UAACC,IAAD,EAAOC,CAAP,EAAa;;AAEzC;AACAD,SAAKE,MAAL,GAAcC,OAAOH,KAAKI,KAAZ,EAAmBjD,KAAKD,MAAL,CAAYwB,WAA/B,CAAd;AACAsB,SAAKK,IAAL,GAAYF,OAAOH,KAAKM,GAAZ,EAAiBnD,KAAKD,MAAL,CAAYwB,WAA7B,CAAZ;;AAEA;AACA,QAAGsB,KAAKK,IAAL,CAAUE,IAAV,CAAeP,KAAKE,MAApB,EAA4B,OAA5B,IAAuC,EAA1C,EAA8C;AAC7CF,UAAKM,GAAL,GAAW,IAAX;AACA;;AAED;AACAN,SAAKQ,MAAL,GAAcP,CAAd;;AAEA;AACA,QAAG,CAACD,KAAKI,KAAN,IAAe,CAACJ,KAAKM,GAAxB,EAA6B;AAC5BN,UAAKE,MAAL,GAAcC,SAASM,OAAT,CAAiB,KAAjB,CAAd;AACAT,UAAKK,IAAL,GAAYF,SAASM,OAAT,CAAiB,KAAjB,EAAwBC,GAAxB,CAA4B,CAA5B,EAA+B,MAA/B,CAAZ;AACA;AACD,QAAG,CAACV,KAAKI,KAAN,IAAeJ,KAAKM,GAAvB,EAA4B;AAC3BN,UAAKE,MAAL,GAAcF,KAAKK,IAAL,CAAUM,KAAV,GAAkBD,GAAlB,CAAsB,CAAC,CAAvB,EAA0B,MAA1B,CAAd;AACA;AACD,QAAGV,KAAKI,KAAL,IAAc,CAACJ,KAAKM,GAAvB,EAA4B;AAC3BN,UAAKK,IAAL,GAAYL,KAAKE,MAAL,CAAYS,KAAZ,GAAoBD,GAApB,CAAwB,CAAxB,EAA2B,MAA3B,CAAZ;AACA;;AAED;AACA,QAAG,CAACV,KAAKI,KAAN,IAAe,CAACJ,KAAKM,GAAxB,EAA6B;AAC5BN,UAAKY,OAAL,GAAe,IAAf;AACA;;AAED;AACA,QAAG,OAAOZ,KAAKa,YAAZ,KAA6B,QAA7B,IAAyC,CAACb,KAAKa,YAAlD,EAAgE;AAC/D,SAAIC,OAAO,EAAX;AACA,SAAGd,KAAKa,YAAR,EAAsB;AACrBC,aAAOd,KAAKa,YAAL,CACLE,KADK,CACC,GADD,EAELhB,GAFK,CAED;AAAA,cAAKiB,EAAEC,IAAF,EAAL;AAAA,OAFC,EAGLC,MAHK,CAGE,UAACF,CAAD;AAAA,cAAOA,CAAP;AAAA,OAHF,CAAP;AAIA;AACDhB,UAAKa,YAAL,GAAoBC,IAApB;AACA;;AAED;AACA,QAAG,CAACd,KAAKmB,EAAT,EAAa;AACZnB,UAAKmB,EAAL,GAAUC,YAAYpB,IAAZ,CAAV;AACA;;AAED,WAAOA,IAAP;AACA,IAjDY,CAAb;AAkDA;;AAED,WAASJ,oBAAT,GAAgC;;AAE/BzC,QAAKkE,cAAL,GAAsB,EAAtB;AAF+B;AAAA;AAAA;;AAAA;AAG/B,yBAAalE,KAAKF,KAAlB,8HAAyB;AAAA,SAAjBqE,CAAiB;AAAA;AAAA;AAAA;;AAAA;AACxB,4BAAaA,EAAET,YAAf,mIAA6B;AAAA,WAArBG,CAAqB;;AAC5B7D,YAAKkE,cAAL,CAAoBL,CAApB,IAAyB7D,KAAKkE,cAAL,CAAoBL,CAApB,KAA0B,EAAnD;AACA7D,YAAKkE,cAAL,CAAoBL,CAApB,EAAuBO,IAAvB,CAA4BD,EAAEH,EAA9B;AACA;AAJuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKxB;AAR8B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAS/B;;AAED,WAAStB,aAAT,GAAyB;;AAExB1C,QAAKqE,WAAL,GAAmBrE,KAAKsE,SAAL,GAAiB,IAApC;AAFwB;AAAA;AAAA;;AAAA;AAGxB,0BAAgBtE,KAAKF,KAArB,mIAA4B;AAAA,SAApB+C,IAAoB;;AAC3B;AACA,SAAG,CAAC7C,KAAKqE,WAAN,IAAqBxB,KAAKE,MAAL,GAAc/C,KAAKqE,WAA3C,EAAwD;AACvDrE,WAAKqE,WAAL,GAAmBxB,KAAKE,MAAxB;AACA;AACD,SAAG,CAAC/C,KAAKsE,SAAN,IAAmBzB,KAAKK,IAAL,GAAYlD,KAAKsE,SAAvC,EAAkD;AACjDtE,WAAKsE,SAAL,GAAiBzB,KAAKK,IAAtB;AACA;AACD;AAXuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAYxBqB;AACAC;AACA;;AAED,WAAS7B,cAAT,GAA0B;AACzB,OAAG3C,KAAKyE,MAAR,EAAgB;AAChBzE,QAAKyE,MAAL,GAAcC,KAAK1E,KAAKH,OAAV,EAAmB8E,QAAnB,CAA4B,OAA5B,CAAd;AACA;;AAED,WAASpC,MAAT,GAAkB;AACjBqC;AACAC;AACAC;AACAC;AACAC;AACAC;AACAC;AACAC;AACAC;AACAC;AACA;;AAED,WAAST,KAAT,GAAiB;AAChB5E,QAAKyE,MAAL,CAAYG,KAAZ;AACA5E,QAAKgC,KAAL,GAAa,EAAb;AACAhC,QAAKiC,OAAL,GAAe,EAAf;AACA;;AAED,WAASsC,eAAT,GAA2B;;AAE1B,OAAGlE,QAAQ,CAAC,aAAD,EAAgB,UAAhB,CAAR,CAAH,EAAyC;AACxCL,SAAKqE,WAAL,GAAmBrE,KAAKqE,WAAL,CAAiBb,KAAjB,GAAyB8B,QAAzB,CAAkC,CAAlC,EAAqC,KAArC,CAAnB;AACAtF,SAAKsE,SAAL,GAAiBtE,KAAKsE,SAAL,CAAed,KAAf,GAAuBD,GAAvB,CAA2B,CAA3B,EAA8B,KAA9B,CAAjB;AACA,IAHD,MAGO,IAAGlD,QAAQ,OAAR,CAAH,EAAqB;AAC3BL,SAAKqE,WAAL,GAAmBrE,KAAKqE,WAAL,CAAiBb,KAAjB,GAAyBF,OAAzB,CAAiC,MAAjC,CAAnB;AACAtD,SAAKsE,SAAL,GAAiBtE,KAAKsE,SAAL,CAAed,KAAf,GAAuB+B,KAAvB,CAA6B,OAA7B,EAAsChC,GAAtC,CAA0C,CAA1C,EAA6C,MAA7C,CAAjB;AACA,IAHM,MAGA;AACNvD,SAAKqE,WAAL,GAAmBrE,KAAKqE,WAAL,CAAiBb,KAAjB,GAAyBF,OAAzB,CAAiC,OAAjC,EAA0CgC,QAA1C,CAAmD,CAAnD,EAAsD,OAAtD,CAAnB;AACAtF,SAAKsE,SAAL,GAAiBtE,KAAKsE,SAAL,CAAed,KAAf,GAAuB+B,KAAvB,CAA6B,OAA7B,EAAsChC,GAAtC,CAA0C,CAA1C,EAA6C,OAA7C,CAAjB;AACA;AACD;;AAED,WAASiB,WAAT,GAAuB;;AAEtBxE,QAAKwF,KAAL,GAAa,EAAb;AACA,OAAIC,WAAW,IAAf;;AAEA,UAAMA,aAAa,IAAb,IAAqBA,WAAWzF,KAAKsE,SAA3C,EAAsD;AACrD,QAAG,CAACmB,QAAJ,EAAc;AACbA,gBAAWzF,KAAKqE,WAAL,CAAiBb,KAAjB,EAAX;AACA,KAFD,MAEO;AACNiC,gBAAWpF,QAAQ,OAAR,IACVoF,SAASjC,KAAT,GAAiBD,GAAjB,CAAqB,CAArB,EAAwB,OAAxB,CADU,GAEVkC,SAASjC,KAAT,GAAiBD,GAAjB,CAAqBvD,KAAKD,MAAL,CAAYgB,IAAjC,EAAuC,OAAvC,CAFD;AAGA;AACDf,SAAKwF,KAAL,CAAWpB,IAAX,CAAgBqB,QAAhB;AACA;AACD;;AAED,WAASZ,YAAT,GAAwB;;AAEvB,OAAMa,SAAS,CAAC,MAAD,EAAS,MAAT,EAAiB,OAAjB,EAA0B,UAA1B,EAAsC,KAAtC,EAA6C,SAA7C,CAAf;AACA;AAHuB;AAAA;AAAA;;AAAA;AAIvB,0BAAiBA,MAAjB,mIAAyB;AAAA,SAAjBC,KAAiB;;AACxB3F,UAAKkC,cAAL,CAAoByD,KAApB,IAA6B3F,KAAKyE,MAAL,CAAYkB,KAAZ,GAAoBC,IAApB,CAAyB,EAAC,MAAMD,KAAP,EAAzB,CAA7B;AACA;AANsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOvB;;AAED,WAAStD,SAAT,CAAmBwD,KAAnB,EAA0B;AACzB7F,QAAKD,MAAL,CAAYU,SAAZ,GAAwBoF,KAAxB;;AAEA,OAAGA,UAAU,KAAb,EAAoB;AACnB7F,SAAKD,MAAL,CAAYgB,IAAZ,GAAmB,EAAnB;AACAf,SAAKD,MAAL,CAAYe,YAAZ,GAA2B,EAA3B;AACA,IAHD,MAGO,IAAG+E,UAAU,UAAb,EAAyB;AAC/B7F,SAAKD,MAAL,CAAYgB,IAAZ,GAAmB,KAAK,CAAxB;AACAf,SAAKD,MAAL,CAAYe,YAAZ,GAA2B,EAA3B;AACA,IAHM,MAGA,IAAG+E,UAAU,aAAb,EAA4B;AAClC7F,SAAKD,MAAL,CAAYgB,IAAZ,GAAmB,KAAK,CAAxB;AACAf,SAAKD,MAAL,CAAYe,YAAZ,GAA2B,EAA3B;AACA,IAHM,MAGA,IAAG+E,UAAU,MAAb,EAAqB;AAC3B7F,SAAKD,MAAL,CAAYgB,IAAZ,GAAmB,KAAK,CAAxB;AACAf,SAAKD,MAAL,CAAYe,YAAZ,GAA2B,GAA3B;AACA,IAHM,MAGA,IAAG+E,UAAU,OAAb,EAAsB;AAC5B7F,SAAKD,MAAL,CAAYgB,IAAZ,GAAmB,KAAK,EAAxB;AACAf,SAAKD,MAAL,CAAYe,YAAZ,GAA2B,GAA3B;AACA;AACD;;AAED,WAASqE,SAAT,GAAqB;AACpB,OAAMW,YAAY9F,KAAKyE,MAAL,CAAYsB,IAAZ,CAAiBC,qBAAjB,GAAyCC,KAA3D;AACA,OAAMC,eAAelG,KAAKyE,MAAL,CAAY0B,MAAZ,CAAmB,iBAAnB,EAAsCP,IAAtC,CAA2C,OAA3C,CAArB;AACA,OAAGE,YAAYI,YAAf,EAA6B;AAC5BlG,SAAKyE,MAAL,CAAYmB,IAAZ,CAAiB,OAAjB,EAA0BM,YAA1B;AACA;AACD;;AAED,WAASd,mBAAT,GAA+B;AAC9B,OAAMgB,iBAAiBpG,KAAKH,OAAL,CAAawG,aAApC;;AAEA,OAAG,CAACD,cAAJ,EAAoB;;AAEpB,OAAME,aAAaC,eAAenD,IAAf,CAAoBpD,KAAKqE,WAAzB,EAAsC,OAAtC,IAClBrE,KAAKD,MAAL,CAAYgB,IADM,GACCf,KAAKD,MAAL,CAAYe,YADb,GAC4Bd,KAAKD,MAAL,CAAYe,YAD3D;AAEAsF,kBAAeI,UAAf,GAA4BF,UAA5B;AACA;;AAED,WAASC,YAAT,GAAwB;AACvB,OAAM1D,OAAO7C,KAAKF,KAAL,CAAW2G,MAAX,CAAkB,UAACC,GAAD,EAAMC,IAAN,EAAe;AAC7C,WAAOA,KAAK5D,MAAL,CAAY6D,cAAZ,CAA2BF,IAAI3D,MAA/B,IAAyC4D,IAAzC,GAAgDD,GAAvD;AACA,IAFY,CAAb;AAGA,UAAO7D,KAAKE,MAAZ;AACA;;AAED,WAAS+B,SAAT,GAAqB;AACpB+B;AACAC;AACAC;AACAC;AACAC;AACA;;AAED,WAASJ,oBAAT,GAAgC;;AAE/B,OAAMK,aAAalH,KAAKwF,KAAL,CAAW2B,MAAX,GAAoBnH,KAAKD,MAAL,CAAYe,YAAnD;AAAA,OACCsG,cAAcpH,KAAKD,MAAL,CAAYc,aAAZ,GAA4Bb,KAAKD,MAAL,CAAYuB,OAAxC,GACb,CAACtB,KAAKD,MAAL,CAAYkB,GAAZ,CAAgBC,MAAhB,GAAyBlB,KAAKD,MAAL,CAAYuB,OAAtC,IAAiDtB,KAAKF,KAAL,CAAWqH,MAF9D;;AAIAnH,QAAKyE,MAAL,CAAY4C,IAAZ,CAAiB,CAAjB,EAAoB,CAApB,EAAuBH,UAAvB,EAAmCE,WAAnC,EACEzC,QADF,CACW,iBADX,EAEE2C,QAFF,CAEWtH,KAAKkC,cAAL,CAAoBqF,IAF/B;;AAIAvH,QAAKyE,MAAL,CAAYmB,IAAZ,CAAiB;AAChB1E,YAAQkG,cAAcpH,KAAKD,MAAL,CAAYuB,OAA1B,GAAoC,GAD5B;AAEhB2E,WAAO;AAFS,IAAjB;AAIA;;AAED,WAASc,gBAAT,GAA4B;AAC3B,OAAMS,eAAexH,KAAKwF,KAAL,CAAW2B,MAAX,GAAoBnH,KAAKD,MAAL,CAAYe,YAArD;AAAA,OACCD,gBAAgBb,KAAKD,MAAL,CAAYc,aAAZ,GAA4B,EAD7C;AAEAb,QAAKyE,MAAL,CAAY4C,IAAZ,CAAiB,CAAjB,EAAoB,CAApB,EAAuBG,YAAvB,EAAqC3G,aAArC,EACE8D,QADF,CACW,aADX,EAEE2C,QAFF,CAEWtH,KAAKkC,cAAL,CAAoBqF,IAF/B;AAGA;;AAED,WAAST,cAAT,GAA0B;;AAEzB,OAAMW,OAAOzH,KAAKyE,MAAL,CAAYkB,KAAZ,GAAoB2B,QAApB,CAA6BtH,KAAKkC,cAAL,CAAoBqF,IAAjD,CAAb;AAAA,OACCG,QAAQ1H,KAAKyE,MAAL,CAAYkB,KAAZ,GAAoB2B,QAApB,CAA6BtH,KAAKkC,cAAL,CAAoBqF,IAAjD,CADT;AAAA,OAECI,YAAY3H,KAAKwF,KAAL,CAAW2B,MAAX,GAAoBnH,KAAKD,MAAL,CAAYe,YAF7C;AAAA,OAGC8G,aAAa5H,KAAKD,MAAL,CAAYkB,GAAZ,CAAgBC,MAAhB,GAAyBlB,KAAKD,MAAL,CAAYuB,OAHnD;;AAKA,OAAIuG,QAAQ7H,KAAKD,MAAL,CAAYc,aAAZ,GAA4Bb,KAAKD,MAAL,CAAYuB,OAAZ,GAAsB,CAA9D;;AAPyB;AAAA;AAAA;;AAAA;AASzB,0BAAgBtB,KAAKF,KAArB,mIAA4B;AAAA,SAApB+C,IAAoB;AAAE;AAC7B7C,UAAKyE,MAAL,CAAY4C,IAAZ,CAAiB,CAAjB,EAAoBQ,KAApB,EAA2BF,SAA3B,EAAsCC,UAAtC,EACEjD,QADF,CACW,UADX,EAEE2C,QAFF,CAEWG,IAFX;;AAIAzH,UAAKyE,MAAL,CAAYqD,IAAZ,CAAiB,CAAjB,EAAoBD,QAAQD,UAA5B,EAAwCD,SAAxC,EAAmDE,QAAQD,UAA3D,EACEjD,QADF,CACW,UADX,EAEE2C,QAFF,CAEWI,KAFX;;AAIAG,cAAS7H,KAAKD,MAAL,CAAYkB,GAAZ,CAAgBC,MAAhB,GAAyBlB,KAAKD,MAAL,CAAYuB,OAA9C;AACA;AAnBwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBzB;;AAED,WAAS0F,eAAT,GAA2B;AAC1B,OAAIe,SAAS,CAAb;AAAA,OACCC,SAAShI,KAAKD,MAAL,CAAYc,aAAZ,GAA4Bb,KAAKD,MAAL,CAAYuB,OAAZ,GAAsB,CAD5D;AAAA,OAEC2G,cAAc,CAACjI,KAAKD,MAAL,CAAYkB,GAAZ,CAAgBC,MAAhB,GAAyBlB,KAAKD,MAAL,CAAYuB,OAAtC,IAAiDtB,KAAKF,KAAL,CAAWqH,MAF3E;;AAD0B;AAAA;AAAA;;AAAA;AAK1B,0BAAgBnH,KAAKwF,KAArB,mIAA4B;AAAA,SAApB0C,IAAoB;;AAC3B,SAAIC,aAAa,MAAjB;AACA;AACA,SAAG9H,QAAQ,KAAR,KAAkB6H,KAAKE,GAAL,OAAe,CAApC,EAAuC;AACtCD,oBAAc,QAAd;AACA;AACD;AACA,SAAG9H,QAAQ,MAAR,KAAmB6H,KAAKA,IAAL,MAAe,CAAlC,IAAuCA,KAAKA,IAAL,KAAc,CAAxD,EAA2D;AAC1DC,oBAAc,QAAd;AACA;AACD;AACA,SAAG9H,QAAQ,OAAR,KAAoB6H,KAAKG,KAAL,KAAe,CAAf,KAAqB,CAA5C,EAA+C;AAC9CF,oBAAc,QAAd;AACA;;AAEDnI,UAAKyE,MAAL,CAAY6D,IAAZ,CAAiB5D,KAAK6D,MAAL,CAAY,sBAAZ,EAAoC;AACpDC,SAAGT,MADiD;AAEpDU,SAAGT,MAFiD;AAGpD9G,cAAQ+G;AAH4C,MAApC,CAAjB,EAKCtD,QALD,CAKUwD,UALV,EAMCb,QAND,CAMUtH,KAAKkC,cAAL,CAAoBqF,IAN9B;;AAQA,SAAGlH,QAAQ,OAAR,CAAH,EAAqB;AACpB0H,gBAAUG,KAAKQ,WAAL,KAAqB1I,KAAKD,MAAL,CAAYe,YAAjC,GAAgD,EAA1D;AACA,MAFD,MAEO;AACNiH,gBAAU/H,KAAKD,MAAL,CAAYe,YAAtB;AACA;AACD;AAjCyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkC1B;;AAED,WAASmG,oBAAT,GAAgC;;AAE/B;AACA,OAAG5G,QAAQ,KAAR,CAAH,EAAmB;AAClB,QAAMmI,IAAIxF,SAASM,OAAT,CAAiB,KAAjB,EAAwBF,IAAxB,CAA6BpD,KAAKqE,WAAlC,EAA+C,OAA/C,IACRrE,KAAKD,MAAL,CAAYgB,IADJ,GACWf,KAAKD,MAAL,CAAYe,YADjC;AAEA,QAAM2H,IAAI,CAAV;AACA,QAAMxC,QAAQjG,KAAKD,MAAL,CAAYe,YAA1B;AACA,QAAMI,SAAS,CAAClB,KAAKD,MAAL,CAAYkB,GAAZ,CAAgBC,MAAhB,GAAyBlB,KAAKD,MAAL,CAAYuB,OAAtC,IAAiDtB,KAAKF,KAAL,CAAWqH,MAA5D,GACdnH,KAAKD,MAAL,CAAYc,aADE,GACcb,KAAKD,MAAL,CAAYuB,OAAZ,GAAsB,CADnD;;AAGAtB,SAAKyE,MAAL,CAAY4C,IAAZ,CAAiBmB,CAAjB,EAAoBC,CAApB,EAAuBxC,KAAvB,EAA8B/E,MAA9B,EACEyD,QADF,CACW,iBADX,EAEE2C,QAFF,CAEWtH,KAAKkC,cAAL,CAAoBqF,IAF/B;AAGA;AACD;;AAED,WAASxC,UAAT,GAAsB;AAAA;AAAA;AAAA;;AAAA;;AAErB,0BAAgB4D,mBAAhB,mIAAqC;AAAA,SAA7BT,IAA6B;;AACpClI,UAAKyE,MAAL,CAAYmE,IAAZ,CAAiBV,KAAKW,OAAtB,EAA+BX,KAAKY,OAApC,EAA6CZ,KAAKa,UAAlD,EACEpE,QADF,CACW,YADX,EAEE2C,QAFF,CAEWtH,KAAKkC,cAAL,CAAoBgG,IAF/B;;AAIA,SAAGA,KAAKc,UAAR,EAAoB;AACnB,UAAMC,cAAcjJ,KAAKyE,MAAL,CAAYmE,IAAZ,CAAiBV,KAAKgB,OAAtB,EAA+BhB,KAAKiB,OAApC,EAA6CjB,KAAKc,UAAlD,EAClBrE,QADkB,CACT,YADS,EAElB2C,QAFkB,CAETtH,KAAKkC,cAAL,CAAoBgG,IAFX,CAApB;;AAIA;AACA,UAAGe,YAAYG,OAAZ,GAAsBC,EAAtB,GAA2BrJ,KAAKkC,cAAL,CAAoBqF,IAApB,CAAyB6B,OAAzB,GAAmCnD,KAAjE,EAAwE;AACvEgD,mBAAYK,MAAZ;AACA;AACD;AACD;AAjBoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBrB;;AAED,WAASX,iBAAT,GAA6B;AAC5B,OAAIY,YAAY,IAAhB;AACA,OAAM/D,QAAQxF,KAAKwF,KAAL,CAAW5C,GAAX,CAAe,UAACsF,IAAD,EAAOpF,CAAP,EAAa;AACzC,QAAMe,IAAI2F,cAActB,IAAd,EAAoBqB,SAApB,EAA+BzG,CAA/B,CAAV;AACAyG,gBAAYrB,IAAZ;AACA,WAAOrE,CAAP;AACA,IAJa,CAAd;AAKA,UAAO2B,KAAP;AACA;;AAED,WAASgE,aAAT,CAAuBtB,IAAvB,EAA6BqB,SAA7B,EAAwCzG,CAAxC,EAA2C;AAC1C,OAAG,CAACyG,SAAJ,EAAe;AACdA,gBAAYrB,KAAK1E,KAAL,GAAaD,GAAb,CAAiB,CAAjB,EAAoB,MAApB,CAAZ;AACA;AACD,OAAMkG,YAAY;AACjB,yBAAqBvB,KAAKK,MAAL,CAAY,IAAZ,CADJ;AAEjB,sBAAkBL,KAAKK,MAAL,CAAY,IAAZ,CAFD;AAGjB,iBAAaL,KAAKA,IAAL,OAAgBqB,UAAUrB,IAAV,EAAhB,GAAmCA,KAAKK,MAAL,CAAY,GAAZ,CAAnC,GAAsD,EAHlD;AAIjB,kBAAcL,KAAKG,KAAL,OAAiBkB,UAAUlB,KAAV,EAAjB,GACbH,KAAKK,MAAL,CAAY,OAAZ,CADa,GACUL,KAAKK,MAAL,CAAY,GAAZ,CALP;AAMjB,mBAAeL,KAAKK,MAAL,CAAY,MAAZ,CANE;AAOjB,yBAAqBL,KAAKA,IAAL,OAAgBqB,UAAUrB,IAAV,EAAhB,GAAmCA,KAAKK,MAAL,CAAY,OAAZ,CAAnC,GAA0D,EAP9D;AAQjB,sBAAkBL,KAAKA,IAAL,OAAgBqB,UAAUrB,IAAV,EAAhB,GACjBA,KAAKG,KAAL,OAAiBkB,UAAUlB,KAAV,EAAjB,GACAH,KAAKK,MAAL,CAAY,OAAZ,CADA,GACuBL,KAAKK,MAAL,CAAY,GAAZ,CAFN,GAEyB,EAV1B;AAWjB,iBAAaL,KAAKG,KAAL,OAAiBkB,UAAUlB,KAAV,EAAjB,GAAqCH,KAAKK,MAAL,CAAY,MAAZ,CAArC,GAA2D,EAXvD;AAYjB,kBAAcL,KAAKG,KAAL,OAAiBkB,UAAUlB,KAAV,EAAjB,GAAqCH,KAAKK,MAAL,CAAY,MAAZ,CAArC,GAA2D,EAZxD;AAajB,mBAAeL,KAAKwB,IAAL,OAAgBH,UAAUG,IAAV,EAAhB,GAAmCxB,KAAKK,MAAL,CAAY,MAAZ,CAAnC,GAAyD;AAbvD,IAAlB;;AAgBA,OAAMoB,WAAW;AAChBnB,OAAG1F,IAAI9C,KAAKD,MAAL,CAAYe,YADH;AAEhBgI,aAAS9I,KAAKD,MAAL,CAAYc,aAFL;AAGhBsI,aAASnJ,KAAKD,MAAL,CAAYc,aAAZ,GAA4B;AAHrB,IAAjB;;AAMA,OAAM+I,QAAQ;AACb,yBAAsB5J,KAAKD,MAAL,CAAYe,YAAZ,GAA2B,CAA5B,GAAiC,CADzC;AAEb,yBAAqB,CAFR;AAGb,sBAAmBd,KAAKD,MAAL,CAAYe,YAAZ,GAA2B,CAA5B,GAAiC,CAHtC;AAIb,sBAAkB,CAJL;AAKb,iBAAad,KAAKD,MAAL,CAAYe,YAAZ,GAA2B,CAL3B;AAMb,iBAAcd,KAAKD,MAAL,CAAYe,YAAZ,GAA2B,EAA5B,GAAkC,CANlC;AAOb,kBAAc,CAPD;AAQb,kBAAed,KAAKD,MAAL,CAAYe,YAAZ,GAA2B,CAA5B,GAAiC,CARlC;AASb,mBAAed,KAAKD,MAAL,CAAYe,YAAZ,GAA2B,CAT7B;AAUb,mBAAgBd,KAAKD,MAAL,CAAYe,YAAZ,GAA2B,EAA5B,GAAkC;AAVpC,IAAd;;AAaA,UAAO;AACNkI,gBAAYS,UAAazJ,KAAKD,MAAL,CAAYU,SAAzB,YADN;AAENsI,gBAAYU,UAAazJ,KAAKD,MAAL,CAAYU,SAAzB,YAFN;AAGNyI,aAASS,SAASnB,CAAT,GAAaoB,MAAS5J,KAAKD,MAAL,CAAYU,SAArB,YAHhB;AAIN0I,aAASQ,SAASR,OAJZ;AAKNN,aAASc,SAASnB,CAAT,GAAaoB,MAAS5J,KAAKD,MAAL,CAAYU,SAArB,YALhB;AAMNqI,aAASa,SAASb;AANZ,IAAP;AAQA;;AAED,WAAS7D,WAAT,GAAuB;AACtBjF,QAAKiC,OAAL,GAAe,EAAf;AADsB;AAAA;AAAA;;AAAA;AAAA;AAAA,SAEdY,IAFc;;AAGrB,SAAIgH,SAAS,EAAb;AACAA,cAAShH,KAAKa,YAAL,CAAkBd,GAAlB,CAAsB,eAAO;AACrC,UAAMkH,aAAaC,SAASC,GAAT,CAAnB;AACA,UAAG,CAACF,UAAJ,EAAgB;;AAEhB,UAAM1I,QAAQ,qBACbpB,IADa,EACP;AACNA,WAAKgC,KAAL,CAAW8H,WAAWzG,MAAtB,CAFa,EAEkB;AAC/BrD,WAAKgC,KAAL,CAAWa,KAAKQ,MAAhB,CAHa,CAGW;AAHX,OAAd;AAKArD,WAAKkC,cAAL,CAAoBd,KAApB,CAA0BmC,GAA1B,CAA8BnC,MAAMvB,OAApC;AACA,aAAOuB,KAAP,CAVqC,CAUvB;AACd,MAXQ,EAWN2C,MAXM,CAWC;AAAA,aAAOkG,GAAP;AAAA,MAXD,CAAT,CAJqB,CAeE;AACvBjK,UAAKiC,OAAL,GAAejC,KAAKiC,OAAL,CAAaiI,MAAb,CAAoBL,MAApB,CAAf;AAhBqB;;AAEtB,0BAAgB7J,KAAKF,KAArB,mIAA4B;AAAA;AAe3B;AAjBqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBtB;;AAED,WAASkF,SAAT,GAAqB;;AAEpBhF,QAAKgC,KAAL,GAAahC,KAAKF,KAAL,CAAW8C,GAAX,CAAe,UAACC,IAAD,EAAU;AACrC,QAAM5B,MAAM,mBAAIjB,IAAJ,EAAU6C,IAAV,CAAZ;AACA7C,SAAKkC,cAAL,CAAoBjB,GAApB,CAAwBsC,GAAxB,CAA4BtC,IAAI0E,KAAhC;AACA,WAAO1E,GAAP;AACA,IAJY,CAAb;AAKA;;AAED,WAASiE,kBAAT,GAA8B;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,SACrBjE,GADqB;;AAE5BA,SAAI4I,MAAJ,GAAa7J,KAAKiC,OAAL,CAAa8B,MAAb,CAAoB,iBAAS;AACzC,aAAQ3C,MAAM+I,SAAN,CAAgBtH,IAAhB,CAAqBmB,EAArB,KAA4B/C,IAAI4B,IAAJ,CAASmB,EAAtC,IACL5C,MAAMgJ,OAAN,CAAcvH,IAAd,CAAmBmB,EAAnB,KAA0B/C,IAAI4B,IAAJ,CAASmB,EADrC;AAEA,MAHY,CAAb;AAF4B;;AAC7B,0BAAehE,KAAKgC,KAApB,mIAA2B;AAAA;AAK1B;AAN4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAO7B;;AAED,WAASqD,eAAT,GAA2B;AAC1BrF,QAAKkC,cAAL,CAAoBqF,IAApB,CAAyB8C,KAAzB,CAA+B,YAAM;AACpCjK;AACAJ,SAAKkC,cAAL,CAAoBoI,OAApB,CACEC,SADF,CACY,kBADZ,EAEEC,OAFF,CAEU;AAAA,YAAMC,GAAG9F,QAAH,CAAY,MAAZ,CAAN;AAAA,KAFV;AAGA,IALD;AAMA;;AAED,WAASvE,YAAT,GAAwB;AACvBJ,QAAKyE,MAAL,CAAY8F,SAAZ,CAAsB,cAAtB,EAAsCC,OAAtC,CAA8C,cAAM;AACnDC,OAAGC,WAAH,CAAe,QAAf;AACA,IAFD;AAGA;;AAED,WAASrK,OAAT,CAAiBsK,KAAjB,EAAwB;AACvB,OAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;AAC9B,WAAO3K,KAAKD,MAAL,CAAYU,SAAZ,KAA0BkK,KAAjC;AACA,IAFD,MAEO,IAAGC,MAAMC,OAAN,CAAcF,KAAd,CAAH,EAAyB;AAAA;AAAA;AAAA;;AAAA;AAC/B,4BAAiBA,KAAjB,wIAAwB;AAAA,UAAfvI,IAAe;;AACvB,UAAGpC,KAAKD,MAAL,CAAYU,SAAZ,KAA0B2B,IAA7B,EAAmC,OAAO,IAAP;AACnC;AAH8B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAI/B,WAAO,KAAP;AACA;AACD;;AAED,WAAS2H,QAAT,CAAkB/F,EAAlB,EAAsB;AACrB,UAAOhE,KAAKF,KAAL,CAAWgL,IAAX,CAAgB,UAACjI,IAAD,EAAU;AAChC,WAAOA,KAAKmB,EAAL,KAAYA,EAAnB;AACA,IAFM,CAAP;AAGA;;AAED,WAAS1D,OAAT,CAAiB0D,EAAjB,EAAqB;AACpB,UAAOhE,KAAKgC,KAAL,CAAW8I,IAAX,CAAgB,UAAC7J,GAAD,EAAS;AAC/B,WAAOA,IAAI4B,IAAJ,CAASmB,EAAT,KAAgBA,EAAvB;AACA,IAFM,CAAP;AAGA;;AAED,WAASC,WAAT,CAAqBpB,IAArB,EAA2B;AAC1B,UAAOA,KAAKkI,IAAL,GAAY,GAAZ,GAAkBC,KAAKC,MAAL,GAAcC,QAAd,CAAuB,EAAvB,EAA2BC,KAA3B,CAAiC,CAAjC,EAAoC,EAApC,CAAzB;AACA;;AAED,WAAS5K,aAAT,CAAuB6K,KAAvB,EAA8BC,IAA9B,EAAoC;AACnC,OAAGrL,KAAKD,MAAL,CAAY,QAAQqL,KAApB,CAAH,EAA+B;AAC9BpL,SAAKD,MAAL,CAAY,QAAQqL,KAApB,EAA2BE,KAA3B,CAAiC,IAAjC,EAAuCD,IAAvC;AACA;AACD;;AAEDpL;;AAEA,SAAOD,IAAP;AACA,E,CA1jBD;AACA;;;;;;;;;;;;;ACDA;;AAEA;AACA;AACA;AACA;AACA,gDAA8E;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA,iCAAgC,UAAU,EAAE;AAC5C,E;;;;;;ACpBA;AACA;;;AAGA;AACA,oDAAmD,eAAe,EAAE,yBAAyB,kBAAkB,oBAAoB,sBAAsB,EAAE,sBAAsB,kBAAkB,EAAE,sCAAsC,kBAAkB,EAAE,sBAAsB,oBAAoB,EAAE,kBAAkB,oBAAoB,sBAAsB,EAAE,wBAAwB,wBAAwB,EAAE,6BAA6B,kBAAkB,iBAAiB,EAAE,mBAAmB,eAAe,iBAAiB,sBAAsB,EAAE,iBAAiB,kBAAkB,oBAAoB,oBAAoB,sCAAsC,EAAE,0BAA0B,kBAAkB,EAAE,yBAAyB,sBAAsB,oBAAoB,oBAAoB,wBAAwB,EAAE,sCAAsC,iBAAiB,EAAE,uBAAuB,eAAe,+BAA+B,wBAAwB,oBAAoB,yBAAyB,EAAE,2BAA2B,iBAAiB,yBAAyB,EAAE,oBAAoB,eAAe,sBAAsB,eAAe,uBAAuB,iCAAiC,EAAE,yBAAyB,oBAAoB,EAAE,oCAAoC,sBAAsB,EAAE,uCAAuC,0BAA0B,iBAAiB,EAAE,qCAAqC,sBAAsB,EAAE,4CAA4C,oBAAoB,wBAAwB,EAAE,wBAAwB,eAAe,EAAE,wBAAwB,eAAe,EAAE,wCAAwC,qBAAqB,0BAA0B,kBAAkB,EAAE,iFAAiF,gBAAgB,EAAE,2CAA2C,sBAAsB,wBAAwB,0BAA0B,kBAAkB,EAAE,0CAA0C,sBAAsB,yBAAyB,kBAAkB,EAAE,qDAAqD,uBAAuB,EAAE,kBAAkB,kBAAkB,EAAE,UAAU,kGAAkG,gBAAgB,KAAK,UAAU,aAAa,qBAAqB,KAAK,gBAAgB,KAAK,oBAAoB,KAAK,sBAAsB,MAAM,cAAc,oBAAoB,MAAM,oBAAoB,MAAM,aAAa,kBAAkB,MAAM,WAAW,WAAW,oBAAoB,MAAM,aAAa,eAAe,aAAa,mBAAmB,MAAM,mBAAmB,MAAM,aAAa,cAAc,aAAa,kBAAkB,MAAM,mBAAmB,MAAM,WAAW,YAAY,aAAa,YAAY,kBAAkB,MAAM,YAAY,oBAAoB,MAAM,aAAa,cAAc,WAAW,YAAY,oBAAoB,MAAM,kBAAkB,MAAM,iBAAiB,MAAM,aAAa,gBAAgB,MAAM,kBAAkB,MAAM,YAAY,mBAAmB,MAAM,mBAAmB,MAAM,mBAAmB,MAAM,aAAa,aAAa,iBAAiB,MAAM,gBAAgB,MAAM,YAAY,YAAY,aAAa,kBAAkB,MAAM,YAAY,YAAY,kBAAkB,MAAM,oBAAoB,MAAM,4EAA4E,uBAAuB,yBAAyB,qBAAqB,+BAA+B,yBAAyB,oBAAoB,oBAAoB,oBAAoB,iBAAiB,sBAAsB,YAAY,wBAAwB,iBAAiB,KAAK,kBAAkB,oBAAoB,4BAA4B,wBAAwB,KAAK,eAAe,oBAAoB,KAAK,+BAA+B,sBAAsB,KAAK,eAAe,kCAAkC,KAAK,WAAW,4BAA4B,wBAAwB,eAAe,0BAA0B,OAAO,KAAK,sBAAsB,0BAA0B,mBAAmB,KAAK,cAAc,iBAAiB,0BAA0B,wBAAwB,KAAK,YAAY,uBAAuB,0BAA0B,sBAAsB,wCAAwC,KAAK,mBAAmB,kBAAkB,KAAK,kBAAkB,wBAAwB,0BAA0B,sBAAsB,0BAA0B,sBAAsB,0BAA0B,OAAO,KAAK,gBAAgB,iBAAiB,iCAAiC,0BAA0B,sBAAsB,2BAA2B,eAAe,0BAA0B,2BAA2B,OAAO,KAAK,eAAe,0BAA0B,wBAAwB,iBAAiB,yBAAyB,mCAAmC,KAAK,oBAAoB,sBAAsB,iBAAiB,cAAc,0BAA0B,SAAS,mBAAmB,8BAA8B,qBAAqB,SAAS,OAAO,kBAAkB,cAAc,0BAA0B,SAAS,OAAO,KAAK,gCAAgC,sBAAsB,0BAA0B,KAAK,iBAAiB,wBAAwB,KAAK,iBAAiB,wBAAwB,KAAK,mCAAmC,uBAAuB,4BAA4B,oBAAoB,eAAe,kBAAkB,OAAO,YAAY,wBAAwB,0BAA0B,4BAA4B,2BAA2B,OAAO,WAAW,wBAAwB,2BAA2B,2BAA2B,OAAO,sBAAsB,yBAAyB,OAAO,KAAK,aAAa,oBAAoB,KAAK,GAAG,qBAAqB;;AAExxL;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAgB,iBAAiB;AACjC;AACA;AACA,yCAAwC,gBAAgB;AACxD,KAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAgB,iBAAiB;AACjC;AACA;AACA;AACA;AACA,aAAY,oBAAoB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACjDA;AACA;AACA;AACA;AACA,qBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,GAAE;AACF;AACA;AACA,GAAE;AACF;AACA;AACA,GAAE;AACF;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,iBAAgB,mBAAmB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAgB,sBAAsB;AACtC;AACA;AACA,mBAAkB,2BAA2B;AAC7C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAe,mBAAmB;AAClC;AACA;AACA;AACA;AACA,kBAAiB,2BAA2B;AAC5C;AACA;AACA,SAAQ,uBAAuB;AAC/B;AACA;AACA,IAAG;AACH;AACA,kBAAiB,uBAAuB;AACxC;AACA;AACA,4BAA2B;AAC3B;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAe,iBAAiB;AAChC;AACA;AACA;AACA;AACA;AACA,eAAc;AACd;AACA,iCAAgC,sBAAsB;AACtD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA,IAAG;AACH;AACA;AACA;AACA,GAAE;AACF;AACA,GAAE;AACF;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,EAAC;;AAED;AACA;;AAEA;AACA;AACA,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,wDAAuD;AACvD;;AAEA,8BAA6B,mBAAmB;;AAEhD;;AAEA;;AAEA;AACA;AACA;;;;;;;;;;;;mBC5OwBuL,G;AATxB;AACA;;;;;;;;AAQe,UAASA,GAAT,CAAaC,EAAb,EAAiB3I,IAAjB,EAAuB;;AAErC,MAAM7C,OAAO,EAAb;;AAEA,WAASC,IAAT,GAAgB;AACfC;AACAoC;AACAmJ;AACAC;AACA;;AAED,WAASxL,YAAT,GAAwB;AACvBF,QAAK2L,gBAAL,GAAwB,KAAxB;AACA3L,QAAK6C,IAAL,GAAYA,IAAZ;AACA;;AAED,WAASP,OAAT,GAAmB;AAClBsJ;AACAC;AACA;;AAED,WAASD,cAAT,GAA0B;AACzB5L,QAAKyD,OAAL,GAAezD,KAAK6C,IAAL,CAAUY,OAAzB;AACAzD,QAAKkB,MAAL,GAAcsK,GAAGzL,MAAH,CAAUkB,GAAV,CAAcC,MAA5B;AACAlB,QAAKwI,CAAL,GAASsD,WAAT;AACA9L,QAAKyI,CAAL,GAASsD,WAAT;AACA/L,QAAKmB,aAAL,GAAqBqK,GAAGzL,MAAH,CAAUkB,GAAV,CAAcE,aAAnC;AACAnB,QAAKgM,QAAL,GAAgB,CAAChM,KAAK6C,IAAL,CAAUK,IAAV,CAAeE,IAAf,CAAoBpD,KAAK6C,IAAL,CAAUE,MAA9B,EAAsC,OAAtC,IAAiD,EAAlD,IAAwDyI,GAAGzL,MAAH,CAAUgB,IAAlF;AACAf,QAAKiG,KAAL,GAAauF,GAAGzL,MAAH,CAAUe,YAAV,GAAyBd,KAAKgM,QAA3C;AACAhM,QAAKiM,cAAL,GAAsBT,GAAGzL,MAAH,CAAUe,YAAV,GAAyBd,KAAKgM,QAA9B,IAA0ChM,KAAK6C,IAAL,CAAUqJ,QAAV,GAAqB,GAA/D,KAAuE,CAA7F;AACAlM,QAAK2F,KAAL,GAAa6F,GAAG/G,MAAH,CAAUkB,KAAV,GAAkBhB,QAAlB,CAA2B,aAA3B,EAA0CA,QAA1C,CAAmD3E,KAAK6C,IAAL,CAAUsJ,YAAV,IAA0B,EAA7E,CAAb;AACAnM,QAAKoM,SAAL,GAAiBZ,GAAG/G,MAAH,CAAUkB,KAAV,GAAkBhB,QAAlB,CAA2B,WAA3B,EAAwC2C,QAAxC,CAAiDtH,KAAK2F,KAAtD,CAAjB;AACA3F,QAAKqM,YAAL,GAAoBb,GAAG/G,MAAH,CAAUkB,KAAV,GAAkBhB,QAAlB,CAA2B,cAA3B,EAA2C2C,QAA3C,CAAoDtH,KAAK2F,KAAzD,CAApB;AACA;;AAED,WAASkG,eAAT,GAA2B;AAC1BnH,QAAK4H,MAAL,CAAY,UAAU5H,IAAV,EAAgB6H,OAAhB,EAAyBC,KAAzB,EAAgCC,MAAhC,EAAwCC,QAAxC,EAAkD;AAC7DH,YAAQI,SAAR,CAAkBC,IAAlB,GAAyB,YAAY;AACpC,YAAO,CAAC,KAAKhH,IAAL,CAAU,GAAV,CAAR;AACA,KAFD;AAGA2G,YAAQI,SAAR,CAAkBE,IAAlB,GAAyB,YAAY;AACpC,YAAO,CAAC,KAAKjH,IAAL,CAAU,GAAV,CAAR;AACA,KAFD;AAGA2G,YAAQI,SAAR,CAAkBG,QAAlB,GAA6B,YAAY;AACxC,YAAO,CAAC,KAAKlH,IAAL,CAAU,OAAV,CAAR;AACA,KAFD;AAGA2G,YAAQI,SAAR,CAAkBI,SAAlB,GAA8B,YAAY;AACzC,YAAO,CAAC,KAAKnH,IAAL,CAAU,QAAV,CAAR;AACA,KAFD;AAGA2G,YAAQI,SAAR,CAAkBK,OAAlB,GAA4B,YAAY;AACvC,YAAO,KAAKJ,IAAL,KAAc,KAAKE,QAAL,EAArB;AACA,KAFD;AAGA,IAhBD;AAiBA;;AAED,WAASrB,IAAT,GAAgB;AACfwB;AACAC;AACAC;AACAC;AACA;;AAED,WAASH,QAAT,GAAoB;AACnBjN,QAAKqN,IAAL,GAAY7B,GAAG/G,MAAH,CAAU4C,IAAV,CAAerH,KAAKwI,CAApB,EAAuBxI,KAAKyI,CAA5B,EACXzI,KAAKiG,KADM,EACCjG,KAAKkB,MADN,EAEXlB,KAAKmB,aAFM,EAESnB,KAAKmB,aAFd,EAGVwD,QAHU,CAGD,KAHC,EAIV2C,QAJU,CAIDtH,KAAKoM,SAJJ,CAAZ;AAKA,OAAIpM,KAAKyD,OAAT,EAAkB;AACjBzD,SAAKqN,IAAL,CAAU1I,QAAV,CAAmB,aAAnB;AACA;AACD;;AAED,WAASuI,iBAAT,GAA6B;AAC5B,OAAIlN,KAAKyD,OAAT,EAAkB;AAClBzD,QAAKsN,aAAL,GAAqB9B,GAAG/G,MAAH,CAAU4C,IAAV,CAAerH,KAAKwI,CAApB,EAAuBxI,KAAKyI,CAA5B,EACpBzI,KAAKiM,cADe,EACCjM,KAAKkB,MADN,EAEpBlB,KAAKmB,aAFe,EAEAnB,KAAKmB,aAFL,EAGnBwD,QAHmB,CAGV,cAHU,EAInB2C,QAJmB,CAIVtH,KAAKoM,SAJK,CAArB;AAKA;;AAED,WAASe,UAAT,GAAsB;AACrB3B,MAAG/G,MAAH,CAAUmE,IAAV,CAAe5I,KAAKwI,CAAL,GAASxI,KAAKiG,KAAL,GAAa,CAArC,EACCjG,KAAKyI,CAAL,GAASzI,KAAKkB,MAAL,GAAc,CADxB,EAEClB,KAAK6C,IAAL,CAAUkI,IAFX,EAGEpG,QAHF,CAGW,WAHX,EAIE2C,QAJF,CAIWtH,KAAKoM,SAJhB;AAKAmB;AACA;;AAED,WAASH,mBAAT,GAA+B;AAC9B,OAAIpN,KAAKyD,OAAT,EAAkB;;AAElB,OAAMxC,MAAMjB,KAAKqN,IAAjB;AAAA,OACCG,eAAe,CADhB;;AAGAhC,MAAG/G,MAAH,CAAU4C,IAAV,CAAepG,IAAI2L,IAAJ,KAAa3L,IAAI6L,QAAJ,EAAb,GAA8B,CAA7C,EAAgD7L,IAAI4L,IAAJ,KAAa,CAA7D,EACCW,YADD,EACexN,KAAKkB,MAAL,GAAc,CAD7B,EACgClB,KAAKmB,aADrC,EACoDnB,KAAKmB,aADzD,EAEEwD,QAFF,CAEW,cAFX,EAGE2C,QAHF,CAGWtH,KAAKqM,YAHhB;AAIAb,MAAG/G,MAAH,CAAU4C,IAAV,CAAepG,IAAI2L,IAAJ,KAAa,CAA5B,EAA+B3L,IAAI4L,IAAJ,KAAa,CAA5C,EACCW,YADD,EACexN,KAAKkB,MAAL,GAAc,CAD7B,EACgClB,KAAKmB,aADrC,EACoDnB,KAAKmB,aADzD,EAEEwD,QAFF,CAEW,aAFX,EAGE2C,QAHF,CAGWtH,KAAKqM,YAHhB;;AAKA,OAAIrM,KAAK6C,IAAL,CAAUqJ,QAAV,IAAsBlM,KAAK6C,IAAL,CAAUqJ,QAAV,GAAqB,GAA/C,EAAoD;AACnDV,OAAG/G,MAAH,CAAUgJ,OAAV,CAAkBC,6BAAlB,EACE/I,QADF,CACW,iBADX,EAEE2C,QAFF,CAEWtH,KAAKqM,YAFhB;AAGA;AACD;;AAED,WAASqB,2BAAT,GAAuC;AACtC,OAAMC,eAAe3N,KAAKsN,aAA1B;AACA,UAAO,CACNK,aAAaX,OAAb,KAAyB,CADnB,EACsBW,aAAad,IAAb,KAAsBc,aAAaZ,SAAb,EAD5C,EAENY,aAAaX,OAAb,KAAyB,CAFnB,EAEsBW,aAAad,IAAb,KAAsBc,aAAaZ,SAAb,EAF5C,EAGNY,aAAaX,OAAb,EAHM,EAGkBW,aAAad,IAAb,KAAsBc,aAAaZ,SAAb,EAAtB,GAAiD,IAHnE,CAAP;AAKA;;AAED,WAASrB,IAAT,GAAgB;AACf,OAAI1L,KAAKyD,OAAT,EAAkB;AAClBmK;AACAC;AACAC;AACAC;AACAC;AACA;;AAED,WAASH,YAAT,GAAwB;AACvB,OAAMI,gBAAgBzC,GAAGtJ,cAAH,CAAkBoI,OAAxC;AACAtK,QAAKkO,WAAL,GAAmBD,cACjB9H,MADiB,mCACqBnG,KAAK6C,IAAL,CAAUmB,EAD/B,SAAnB;;AAGA,OAAI,CAAChE,KAAKkO,WAAV,EAAuB;AACtBlO,SAAKkO,WAAL,GAAmB1C,GAAG/G,MAAH,CAAUkB,KAAV,GACjBhB,QADiB,CACR,sBADQ,EAEjBiB,IAFiB,CAEZ,WAFY,EAEC5F,KAAK6C,IAAL,CAAUmB,EAFX,EAGjBsD,QAHiB,CAGR2G,aAHQ,CAAnB;;AAKAE;;AAEA,QAAMC,IAAI5C,GAAG/G,MAAH,CAAUV,MAAV,CACTW,KAAKX,MAAL,CAAYsK,MAAZ,CAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,MAA5B,EAAoC,GAApC,CADS,CAAV;AAEArO,SAAKkO,WAAL,CAAiBtI,IAAjB,CAAsB;AACrB7B,aAAQqK;AADa,KAAtB;AAGA;;AAEDpO,QAAK2F,KAAL,CAAW0E,KAAX,CAAiB,UAACiE,CAAD,EAAO;AACvB,QAAItO,KAAK2L,gBAAT,EAA2B;AAC1B;AACA;AACA;AACDsC,kBAAc1D,SAAd,CAAwB,kBAAxB,EACEC,OADF,CACU;AAAA,YAAMC,GAAG9F,QAAH,CAAY,MAAZ,CAAN;AAAA,KADV;AAEA3E,SAAKkO,WAAL,CAAiBxD,WAAjB,CAA6B,MAA7B;AACA,IARD;AASA;;AAED,WAASyD,cAAT,GAA0B;AAAA,+BACVI,sBADU;AAAA,OAClB/F,CADkB,yBAClBA,CADkB;AAAA,OACfC,CADe,yBACfA,CADe;;AAEzBzI,QAAKkO,WAAL,CAAiBM,SAAjB,OAA+BhG,CAA/B,SAAoCC,CAApC;AACAzI,QAAKkO,WAAL,CAAiBtJ,KAAjB;;AAEA,OAAM6J,OAAOC,kBAAb;AACA,OAAMC,iBACLjK,KAAKkK,KAAL,iHAEIH,IAFJ,iDADD;AAMAzO,QAAKkO,WAAL,CAAiBW,MAAjB,CAAwBF,cAAxB;AACA;;AAED,WAASD,gBAAT,GAA4B;;AAE3B;AACA,OAAGlD,GAAGzL,MAAH,CAAUyB,iBAAb,EAAgC;AAC/B,QAAMiN,QAAOjD,GAAGzL,MAAH,CAAUyB,iBAAvB;AACA,QAAG,OAAOiN,KAAP,KAAgB,QAAnB,EAA6B;AAC5B,YAAOA,KAAP;AACA;AACD,QAAGK,WAAWL,KAAX,CAAH,EAAqB;AACpB,YAAOA,MAAK5L,IAAL,CAAP;AACA;AACD;;AAED,OAAMkM,aAAa/O,KAAK6C,IAAL,CAAUE,MAAV,CAAiBwF,MAAjB,CAAwB,OAAxB,CAAnB;AACA,OAAMyG,WAAWhP,KAAK6C,IAAL,CAAUK,IAAV,CAAeqF,MAAf,CAAsB,OAAtB,CAAjB;AACA,OAAM0G,UAAajP,KAAK6C,IAAL,CAAUkI,IAAvB,UAAgCgE,UAAhC,WAAgDC,QAAtD;;AAEA,OAAME,wBAAsBlP,KAAKgM,QAA3B,UAAN;AACA,OAAMmD,SAASnP,KAAK6C,IAAL,CAAUqJ,QAAV,kBAAkClM,KAAK6C,IAAL,CAAUqJ,QAA5C,GAAyD,IAAxE;;AAEA,OAAMuC,iEAEEQ,OAFF,0BAGCC,MAHD,uBAKHC,iBAAeA,MAAf,YAA8B,EAL3B,0BAAN;AASA,UAAOV,IAAP;AACA;;AAED,WAASF,oBAAT,GAAgC;AAC/B,UAAO;AACN/F,OAAGxI,KAAKqN,IAAL,CAAUL,OAAV,KAAsB,CADnB;AAENvE,OAAGzI,KAAKqN,IAAL,CAAUR,IAAV,KAAmB;AAFhB,IAAP;AAIA;;AAED,WAASiB,WAAT,GAAuB;AAAA,sBACEsB,aADF;AAAA,OACdC,IADc,gBACdA,IADc;AAAA,OACRC,KADQ,gBACRA,KADQ;;AAGtBD,QAAKE,IAAL,CAAUC,WAAV,EAAuBC,OAAvB,EAAgCC,WAAhC;AACAJ,SAAMC,IAAN,CAAWI,YAAX,EAAyBF,OAAzB,EAAkCG,YAAlC;;AAEA,YAASD,YAAT,CAAsBE,EAAtB,EAA0BC,EAA1B,EAA8B;AAC7BC,wBAAoBF,EAApB,EAAwBC,EAAxB;AACA;AACD,YAASF,YAAT,GAAwB;AACvBI;AACA;;AAED,YAASR,WAAT,CAAqBK,EAArB,EAAyBC,EAAzB,EAA6B;AAC5BG,uBAAmBJ,EAAnB,EAAuBC,EAAvB;AACA;AACD,YAASJ,WAAT,GAAuB;AACtBQ;AACA;AACD;;AAED,WAASd,WAAT,GAAuB;AACtB,UAAO;AACNC,UAAMrP,KAAKqM,YAAL,CAAkBlG,MAAlB,CAAyB,cAAzB,CADA;AAENmJ,WAAOtP,KAAKqM,YAAL,CAAkBlG,MAAlB,CAAyB,eAAzB;AAFD,IAAP;AAIA;;AAED,WAAS4H,SAAT,GAAqB;AACpB/N,QAAKoM,SAAL,CAAemD,IAAf,CAAoBY,MAApB,EAA4BV,OAA5B,EAAqCW,MAArC;AACA;;AAED,WAASpC,oBAAT,GAAgC;AAC/B,OAAM/M,MAAMjB,KAAKqN,IAAjB;AAAA,OACCM,eAAe3N,KAAKsN,aADrB;AAAA,OAEC+C,SAASrQ,KAAK2F,KAAL,CAAWQ,MAAX,CAAkB,kBAAlB,CAFV;AAGAkK,aAAUA,OAAOd,IAAP,CAAYe,OAAZ,EAAqBC,QAArB,EAA+BC,OAA/B,CAAV;;AAEA,YAASF,OAAT,CAAiBT,EAAjB,EAAqBC,EAArB,EAAyB;AACxB,QAAID,KAAKlC,aAAa8C,MAAtB,EAA8B;AAC7BZ,UAAKlC,aAAa8C,MAAlB;AACA;AACD,QAAIZ,KAAKlC,aAAa+C,MAAtB,EAA8B;AAC7Bb,UAAKlC,aAAa+C,MAAlB;AACA;;AAED/C,iBAAa/H,IAAb,CAAkB,OAAlB,EAA2B+H,aAAagD,MAAb,GAAsBd,EAAjD;AACAQ,WAAOzK,IAAP,CAAY,QAAZ,EAAsB8H,6BAAtB;AACAC,iBAAaiD,OAAb,GAAuBf,EAAvB;AACA;AACD,YAASW,OAAT,GAAmB;AAClB,QAAI,CAAC7C,aAAaiD,OAAlB,EAA2B;AAC3BC;AACAC;AACA;AACD,YAASP,QAAT,GAAoB;AACnB5C,iBAAaiD,OAAb,GAAuB,CAAvB;AACAjD,iBAAagD,MAAb,GAAsBhD,aAAab,QAAb,EAAtB;AACAa,iBAAa+C,MAAb,GAAsB,CAAC/C,aAAab,QAAb,EAAvB;AACAa,iBAAa8C,MAAb,GAAsBxP,IAAI6L,QAAJ,KAAiBa,aAAab,QAAb,EAAvC;AACA;AACD;;AAED,WAAS2C,OAAT,GAAmB;AAClB,OAAMxO,MAAMjB,KAAKqN,IAAjB;AACApM,OAAI8P,EAAJ,GAAS9P,IAAI2L,IAAJ,EAAT;AACA3L,OAAI+P,EAAJ,GAAS/P,IAAI4L,IAAJ,EAAT;AACA5L,OAAI0P,MAAJ,GAAa1P,IAAI6L,QAAJ,EAAb;AACA7L,OAAI2P,OAAJ,GAAc,CAAd;AACAK,+BAA4B,SAA5B;AACA;AACDjR,OAAKyP,OAAL,GAAeA,OAAf;;AAEA,WAASU,MAAT,CAAgBN,EAAhB,EAAoBC,EAApB,EAAwB;AACvB,OAAM7O,MAAMjB,KAAKqN,IAAjB;AACApM,OAAI2P,OAAJ,GAAcM,kBAAkBrB,EAAlB,CAAd;AACAsB,uBAAoB,EAAC3I,GAAGvH,IAAI8P,EAAJ,GAAS9P,IAAI2P,OAAjB,EAApB;AACAK,+BAA4B,QAA5B,EAAsC,CAACpB,EAAD,EAAKC,EAAL,CAAtC;AACA;AACD9P,OAAKmQ,MAAL,GAAcA,MAAd;;AAEA,WAASC,MAAT,GAAkB;AACjB,OAAMnP,MAAMjB,KAAKqN,IAAjB;AACA,OAAI,CAACpM,IAAI2P,OAAT,EAAkB;AAClBQ;AACAN;AACAG,+BAA4B,QAA5B;AACA;AACDjR,OAAKoQ,MAAL,GAAcA,MAAd;;AAEA,WAASH,kBAAT,CAA4BJ,EAA5B,EAAgCC,EAAhC,EAAoC;AACnC,OAAM7O,MAAMjB,KAAKqN,IAAjB;AACApM,OAAI2P,OAAJ,GAAcM,kBAAkBrB,EAAlB,CAAd;AACAsB,uBAAoB;AACnB3I,OAAGvH,IAAI8P,EAAJ,GAAS9P,IAAI2P,OADG;AAEnB3K,WAAOhF,IAAI0P,MAAJ,GAAa1P,IAAI2P;AAFL,IAApB;AAIAK,+BAA4B,QAA5B,EAAsC,CAACpB,EAAD,EAAKC,EAAL,CAAtC;AACA;AACD9P,OAAKiQ,kBAAL,GAA0BA,kBAA1B;;AAEA,WAASC,kBAAT,GAA8B;AAC7B,OAAMjP,MAAMjB,KAAKqN,IAAjB;AACA,OAAIpM,IAAI2P,OAAR,EAAiBQ;AACjBN;AACAG,+BAA4B,QAA5B;AACA;AACDjR,OAAKkQ,kBAAL,GAA0BA,kBAA1B;;AAEA,WAASe,2BAAT,CAAqCI,EAArC,EAAyChG,IAAzC,EAA+C;AAC9C,OAAMiG,KAAK9F,GAAGtH,cAAd;AACA,OAAIoN,GAAGtR,KAAK6C,IAAL,CAAUmB,EAAb,CAAJ,EAAsB;AAAA;AAAA;AAAA;;AAAA;AACrB,0BAAoBsN,GAAGtR,KAAK6C,IAAL,CAAUmB,EAAb,CAApB,8HAAsC;AAAA,UAA7BuN,OAA6B;;AACrC,UAAMC,KAAKhG,GAAGlL,OAAH,CAAWiR,OAAX,CAAX;AACAC,SAAGH,EAAH,EAAO/F,KAAP,CAAakG,EAAb,EAAiBnG,IAAjB;AACA;AAJoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKrB;AACD;;AAED,WAAS0E,mBAAT,CAA6BF,EAA7B,EAAiCC,EAAjC,EAAqC;AACpC,OAAM7O,MAAMjB,KAAKqN,IAAjB;AACApM,OAAI2P,OAAJ,GAAcM,kBAAkBrB,EAAlB,CAAd;AACAsB,uBAAoB,EAAClL,OAAOhF,IAAI0P,MAAJ,GAAa1P,IAAI2P,OAAzB,EAApB;AACA;;AAED,WAASZ,mBAAT,GAA+B;AAC9B,OAAM/O,MAAMjB,KAAKqN,IAAjB;AACA,OAAIpM,IAAI2P,OAAR,EAAiBQ;AACjBN;AACA;;AAED,WAASK,mBAAT,OAAuD;AAAA,qBAAzB3I,CAAyB;AAAA,OAAzBA,CAAyB,0BAArB,IAAqB;AAAA,yBAAfvC,KAAe;AAAA,OAAfA,KAAe,8BAAP,IAAO;;AACtD,OAAMhF,MAAMjB,KAAKqN,IAAjB;AACA,OAAI7E,CAAJ,EAAO;AACN;AACA,QAAMiJ,KAAK5O,KAAKa,YAAL,CAAkBd,GAAlB,CAAsB,eAAO;AACvC,YAAO4I,GAAGlL,OAAH,CAAW0J,GAAX,EAAgBqD,IAAhB,CAAqBT,IAArB,EAAP;AACA,KAFU,CAAX;AAGA;AACA,QAAM8E,UAAUD,GAAGhL,MAAH,CAAU,UAACkL,IAAD,EAAOhL,IAAP,EAAgB;AACzC,YAAO6B,KAAK7B,IAAZ;AACA,KAFe,EAEb6B,CAFa,CAAhB;AAGA,QAAG,CAACkJ,OAAJ,EAAa;AACZzL,aAAQ,IAAR;AACA;AACA;AACD2L,gBAAY3Q,GAAZ,EAAiB,GAAjB,EAAsBuH,CAAtB;AACA;AACD,OAAIvC,SAASA,SAASuF,GAAGzL,MAAH,CAAUe,YAAhC,EAA8C;AAC7C8Q,gBAAY3Q,GAAZ,EAAiB,OAAjB,EAA0BgF,KAA1B;AACA;AACDsH;AACAsE;AACAC;AACAC;AACAC;AACA;;AAED,WAASpE,iBAAT,GAA6B;AAC5B5N,QAAK2F,KAAL,CAAW0E,KAAX,CAAiB,YAAY;AAC5B,QAAIrK,KAAK2L,gBAAT,EAA2B;AAC1B;AACA;AACA;AACD,QAAI3L,KAAK2F,KAAL,CAAWsM,QAAX,CAAoB,QAApB,CAAJ,EAAmC;AAClCzG,QAAGjL,aAAH,CAAiB,OAAjB,EAA0B,CAACP,KAAK6C,IAAN,CAA1B;AACA;AACD2I,OAAGpL,YAAH;AACAJ,SAAK2F,KAAL,CAAWuM,WAAX,CAAuB,QAAvB;AACA,IAVD;AAWA;;AAED,WAASd,YAAT,GAAwB;AAAA,+BACkBe,wBADlB;AAAA,OACfC,cADe,yBACfA,cADe;AAAA,OACCC,YADD,yBACCA,YADD;;AAEvBrS,QAAK6C,IAAL,CAAUE,MAAV,GAAmBqP,cAAnB;AACApS,QAAK6C,IAAL,CAAUK,IAAV,GAAiBmP,YAAjB;AACAlE;AACA3C,MAAGjL,aAAH,CAAiB,aAAjB,EACC,CAACP,KAAK6C,IAAN,EAAYuP,cAAZ,EAA4BC,YAA5B,CADD;AAEA;;AAED,WAASxB,gBAAT,GAA4B;AAC3B,OAAMyB,eAAeC,kBAArB;AACAvS,QAAK6C,IAAL,CAAUqJ,QAAV,GAAqBoG,YAArB;AACAnE;AACA3C,MAAGjL,aAAH,CAAiB,iBAAjB,EACC,CAACP,KAAK6C,IAAN,EAAYyP,YAAZ,CADD;AAEA;;AAED,WAASxB,oBAAT,GAAgC;AAC/B9Q,QAAK2L,gBAAL,GAAwB,IAAxB;AACA6G,cAAW;AAAA,WAAMxS,KAAK2L,gBAAL,GAAwB,KAA9B;AAAA,IAAX,EAAgD,IAAhD;AACA;;AAED,WAASwG,sBAAT,GAAkC;AACjC,OAAMlR,MAAMjB,KAAKqN,IAAjB;AACA,OAAMoF,aAAaxR,IAAI2L,IAAJ,KAAapB,GAAGzL,MAAH,CAAUe,YAA1C;AACA,OAAMsR,iBAAiB5G,GAAGnH,WAAH,CAAeb,KAAf,GAAuBD,GAAvB,CAA2BkP,aAAajH,GAAGzL,MAAH,CAAUgB,IAAlD,EAAwD,OAAxD,CAAvB;AACA,OAAM2R,iBAAiBzR,IAAI6L,QAAJ,KAAiBtB,GAAGzL,MAAH,CAAUe,YAAlD;AACA,OAAMuR,eAAeD,eAAe5O,KAAf,GAAuBD,GAAvB,CAA2BmP,iBAAiBlH,GAAGzL,MAAH,CAAUgB,IAAtD,EAA4D,OAA5D,CAArB;AACA;AACA;AACA;AACA;AACA;AACAsR,gBAAa9O,GAAb,CAAiB,IAAjB,EAAuB,SAAvB;AACA,UAAO,EAAE6O,8BAAF,EAAkBC,0BAAlB,EAAP;AACA;;AAED,WAASE,gBAAT,GAA4B;AAC3B,OAAMrG,WAAWlM,KAAKsN,aAAL,CAAmBR,QAAnB,KAAgC9M,KAAKqN,IAAL,CAAUP,QAAV,EAAhC,GAAuD,GAAxE;AACA,UAAO6F,SAASzG,QAAT,EAAmB,EAAnB,CAAP;AACA;;AAED,WAASJ,SAAT,GAAqB;AACpB,OAAItD,IAAIxI,KAAK6C,IAAL,CAAUE,MAAV,CAAiBK,IAAjB,CAAsBoI,GAAGnH,WAAzB,EAAsC,OAAtC,IACPmH,GAAGzL,MAAH,CAAUgB,IADH,GACUyK,GAAGzL,MAAH,CAAUe,YAD5B;;AAGA,OAAI0K,GAAGnL,OAAH,CAAW,OAAX,CAAJ,EAAyB;AACxBmI,QAAIxI,KAAK6C,IAAL,CAAUE,MAAV,CAAiBK,IAAjB,CAAsBoI,GAAGnH,WAAzB,EAAsC,MAAtC,IACHmH,GAAGzL,MAAH,CAAUe,YADP,GACsB,EAD1B;AAEA;AACD,UAAO0H,CAAP;AACA;;AAED,WAASuD,SAAT,GAAqB;AACpB,UAAOP,GAAGzL,MAAH,CAAUc,aAAV,GAA0B2K,GAAGzL,MAAH,CAAUuB,OAApC,GACNtB,KAAK6C,IAAL,CAAUQ,MAAV,IAAoBrD,KAAKkB,MAAL,GAAcsK,GAAGzL,MAAH,CAAUuB,OAA5C,CADD;AAEA;;AAED,WAAS4P,iBAAT,CAA2BrB,EAA3B,EAA+B;AAC9B,OAAI+C,MAAM/C,EAAV;AAAA,OAAcgD,YAAd;AAAA,OAAmBC,iBAAnB;;AAEA,OAAItH,GAAGnL,OAAH,CAAW,MAAX,CAAJ,EAAwB;AACvBwS,UAAMhD,MAAMrE,GAAGzL,MAAH,CAAUe,YAAV,GAAyB,CAA/B,CAAN;AACAgS,eAAWF,MAAMC,GAAN,IACRA,MAAMrH,GAAGzL,MAAH,CAAUe,YAAV,GAAyB,EAAhC,GAAsC,CAAtC,GAA0C0K,GAAGzL,MAAH,CAAUe,YAAV,GAAyB,CAD1D,CAAX;AAEA,IAJD,MAIO,IAAI0K,GAAGnL,OAAH,CAAW,OAAX,CAAJ,EAAyB;AAC/BwS,UAAMhD,MAAMrE,GAAGzL,MAAH,CAAUe,YAAV,GAAyB,EAA/B,CAAN;AACAgS,eAAWF,MAAMC,GAAN,IACRA,MAAMrH,GAAGzL,MAAH,CAAUe,YAAV,GAAyB,EAAhC,GAAsC,CAAtC,GAA0C0K,GAAGzL,MAAH,CAAUe,YAAV,GAAyB,EAD1D,CAAX;AAEA,IAJM,MAIA;AACN+R,UAAMhD,KAAKrE,GAAGzL,MAAH,CAAUe,YAArB;AACAgS,eAAWF,MAAMC,GAAN,IACRA,MAAMrH,GAAGzL,MAAH,CAAUe,YAAV,GAAyB,CAAhC,GAAqC,CAArC,GAAyC0K,GAAGzL,MAAH,CAAUe,YAD1C,CAAX;AAEA;AACD,UAAOgS,QAAP;AACA;;AAED,WAASlB,WAAT,CAAqB/R,OAArB,EAA8B+F,IAA9B,EAAoCmN,KAApC,EAA2C;AAC1CA,WAAQ,CAACA,KAAT;AACA,OAAI,CAACC,MAAMD,KAAN,CAAL,EAAmB;AAClBlT,YAAQ+F,IAAR,CAAaA,IAAb,EAAmBmN,KAAnB;AACA;AACD,UAAOlT,OAAP;AACA;;AAED,WAASiS,2BAAT,GAAuC;AACtC9R,QAAKsN,aAAL,CAAmB1H,IAAnB,CAAwB,GAAxB,EAA6B5F,KAAKqN,IAAL,CAAUT,IAAV,EAA7B;AACA5M,QAAKsN,aAAL,CAAmB1H,IAAnB,CAAwB,OAAxB,EAAiC5F,KAAKqN,IAAL,CAAUP,QAAV,MAAwB9M,KAAK6C,IAAL,CAAUqJ,QAAV,GAAqB,GAA7C,CAAjC;AACA;;AAED,WAASqB,qBAAT,GAAiC;AAChC,OAAMtM,MAAMjB,KAAKqN,IAAjB;AAAA,OACC4F,QAAQjT,KAAK2F,KAAL,CAAWQ,MAAX,CAAkB,YAAlB,CADT;AAEA,OAAI8M,MAAM7J,OAAN,GAAgBnD,KAAhB,GAAwBhF,IAAI6L,QAAJ,EAA5B,EAA4C;AAC3CmG,UAAMtO,QAAN,CAAe,KAAf,EAAsBiB,IAAtB,CAA2B,GAA3B,EAAgC3E,IAAI2L,IAAJ,KAAa3L,IAAI6L,QAAJ,EAAb,GAA8B,CAA9D;AACA,IAFD,MAEO;AACNmG,UAAMvI,WAAN,CAAkB,KAAlB,EAAyB9E,IAAzB,CAA8B,GAA9B,EAAmC3E,IAAI2L,IAAJ,KAAa3L,IAAI6L,QAAJ,KAAiB,CAAjE;AACA;AACD;;AAED,WAAS+E,sBAAT,GAAkC;AACjC,OAAM5Q,MAAMjB,KAAKqN,IAAjB;AACArN,QAAKqM,YAAL,CAAkBlG,MAAlB,CAAyB,cAAzB,EAAyCP,IAAzC,CAA8C;AAC7C,SAAK3E,IAAI2L,IAAJ,KAAa;AAD2B,IAA9C;AAGA5M,QAAKqM,YAAL,CAAkBlG,MAAlB,CAAyB,eAAzB,EAA0CP,IAA1C,CAA+C;AAC9C,SAAK3E,IAAI+L,OAAJ,KAAgB;AADyB,IAA/C;AAGA,OAAMqD,SAASrQ,KAAK2F,KAAL,CAAWQ,MAAX,CAAkB,kBAAlB,CAAf;AACAkK,aAAUA,OAAOzK,IAAP,CAAY,QAAZ,EAAsB8H,6BAAtB,CAAV;AACA;;AAED,WAASqE,qBAAT,GAAiC;AAAA;AAAA;AAAA;;AAAA;AAChC,0BAAkB/R,KAAK6J,MAAvB,mIAA+B;AAAA,SAAtBzI,KAAsB;;AAC9BA,WAAM8R,MAAN;AACA;AAH+B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIhC;;AAED,WAASlB,uBAAT,GAAmC;AAAA,gCACnBzD,sBADmB;AAAA,OAC3B/F,CAD2B,0BAC3BA,CAD2B;AAAA,OACxBC,CADwB,0BACxBA,CADwB;;AAElCzI,QAAKkO,WAAL,IAAoBlO,KAAKkO,WAAL,CAAiBM,SAAjB,OAA+BhG,CAA/B,SAAoCC,CAApC,CAApB;AACA;;AAED,WAASqG,UAAT,CAAoBqE,eAApB,EAAqC;AACpC,OAAIC,UAAU,EAAd;AACA,UAAOD,mBAAmBC,QAAQlI,QAAR,CAAiBmI,IAAjB,CAAsBF,eAAtB,MAA2C,mBAArE;AACA;;AAEDlT;;AAEA,SAAOD,IAAP;AACA;;;;;;;;;;;;mBCrgBuBsT,K;AAXxB;AACA;;;;;;;;;;AAUe,UAASA,KAAT,CAAe9H,EAAf,EAAmBrB,SAAnB,EAA8BC,OAA9B,EAAuC;;AAErD,MAAMpK,OAAO,EAAb;;AAEA,WAASC,IAAT,GAAgB;AACfD,QAAKmK,SAAL,GAAiBA,SAAjB;AACAnK,QAAKoK,OAAL,GAAeA,OAAf;AACA9H;AACAmJ;AACA;;AAED,WAASnJ,OAAT,GAAmB;;AAElBtC,QAAKuT,OAAL,GAAepJ,UAAUkD,IAAV,CAAeT,IAAf,KAAwBzC,UAAUkD,IAAV,CAAeP,QAAf,KAA4B,CAAnE;;AAEA,OAAM0G,YAAY,SAAZA,SAAY;AAAA,WACjBpJ,QAAQiD,IAAR,CAAaT,IAAb,KAAsB5M,KAAKuT,OAAL,GAAe/H,GAAGzL,MAAH,CAAUuB,OAA/C,IACCtB,KAAKuT,OAAL,GAAepJ,UAAUkD,IAAV,CAAeT,IAAf,KAAwBpB,GAAGzL,MAAH,CAAUuB,OAFjC;AAAA,IAAlB;;AAIA,UAAMkS,WAAN,EAAmB;AAClBxT,SAAKuT,OAAL,IAAgB,EAAhB;AACA;;AAEDvT,QAAKyT,OAAL,GAAejI,GAAGzL,MAAH,CAAUc,aAAV,GAA0B2K,GAAGzL,MAAH,CAAUkB,GAAV,CAAcC,MAAxC,GACd,CAACsK,GAAGzL,MAAH,CAAUuB,OAAV,GAAoBkK,GAAGzL,MAAH,CAAUkB,GAAV,CAAcC,MAAnC,IAA6CiJ,UAAUtH,IAAV,CAAeQ,MAD9C,GAEdmI,GAAGzL,MAAH,CAAUuB,OAFX;;AAIAtB,QAAK0T,KAAL,GAAatJ,QAAQiD,IAAR,CAAaT,IAAb,KAAsBpB,GAAGzL,MAAH,CAAUuB,OAAV,GAAoB,CAAvD;AACAtB,QAAK2T,KAAL,GAAanI,GAAGzL,MAAH,CAAUc,aAAV,GAA0B2K,GAAGzL,MAAH,CAAUkB,GAAV,CAAcC,MAAd,GAAuB,CAAjD,GACZ,CAACsK,GAAGzL,MAAH,CAAUuB,OAAV,GAAoBkK,GAAGzL,MAAH,CAAUkB,GAAV,CAAcC,MAAnC,IAA6CkJ,QAAQvH,IAAR,CAAaQ,MAD9C,GAEZmI,GAAGzL,MAAH,CAAUuB,OAFX;;AAIA,OAAMsS,mBAAoBzJ,UAAUtH,IAAV,CAAeQ,MAAf,GAAwB+G,QAAQvH,IAAR,CAAaQ,MAA/D;AACArD,QAAKqB,KAAL,GAAamK,GAAGzL,MAAH,CAAUqB,KAAV,CAAgBC,KAA7B;AACArB,QAAK6T,SAAL,GAAiBD,mBAAmB,CAAnB,GAAuB,CAAxC;AACA5T,QAAK8T,OAAL,GAAeF,mBAAmB,CAAC5T,KAAKqB,KAAzB,GAAiCrB,KAAKqB,KAArD;AACArB,QAAK+T,MAAL,GAAcH,mBACb5T,KAAK2T,KAAL,GAAanI,GAAGzL,MAAH,CAAUqB,KAAV,CAAgBC,KADhB,GAEbrB,KAAK2T,KAAL,GAAanI,GAAGzL,MAAH,CAAUqB,KAAV,CAAgBC,KAF9B;;AAIArB,QAAKsI,IAAL,GACC5D,KAAK6D,MAAL,CAAY,sCACX,sDADW,GAEX,wCAFD,EAGC;AACCgL,aAASvT,KAAKuT,OADf;AAECE,aAASzT,KAAKyT,OAFf;AAGCC,WAAO1T,KAAK0T,KAHb;AAICC,WAAO3T,KAAK2T,KAJb;AAKCI,YAAQ/T,KAAK+T,MALd;AAMC1S,WAAOrB,KAAKqB,KANb;AAOCwS,eAAW7T,KAAK6T,SAPjB;AAQCC,aAAS9T,KAAK8T;AARf,IAHD,CADD;;AAeA,OAAG1J,QAAQiD,IAAR,CAAaT,IAAb,KAAsBzC,UAAUkD,IAAV,CAAeT,IAAf,KAAwBpB,GAAGzL,MAAH,CAAUuB,OAA3D,EAAoE;AACnEtB,SAAKsI,IAAL,GACC5D,KAAK6D,MAAL,CAAY,sCACZ,oDADY,GAEZ,kEAFY,GAGZ,sDAHY,GAIZ,wCAJA,EAKC;AACCgL,cAASvT,KAAKuT,OADf;AAECE,cAASzT,KAAKyT,OAFf;AAGCC,YAAO1T,KAAK0T,KAHb;AAICC,YAAO3T,KAAK2T,KAJb;AAKCK,aAAQxI,GAAGzL,MAAH,CAAUuB,OAAV,GAAoB,CAApB,GAAwBtB,KAAKqB,KALtC;AAMC4S,aAAQ7J,QAAQiD,IAAR,CAAaR,IAAb,KAAsBzC,QAAQiD,IAAR,CAAaN,SAAb,KAA2B,CAAjD,GAAqD/M,KAAK8T,OANnE;AAOCzE,WAAMjF,QAAQiD,IAAR,CAAaT,IAAb,KAAsBpB,GAAGzL,MAAH,CAAUuB,OAPvC;AAQCyS,aAAQ/T,KAAK+T,MARd;AASC1S,YAAOrB,KAAKqB,KATb;AAUCwS,gBAAW7T,KAAK6T,SAVjB;AAWCC,cAAS9T,KAAK8T;AAXf,KALD,CADD;AAmBA;AACD;;AAED,WAASrI,IAAT,GAAgB;AACfzL,QAAKH,OAAL,GAAe2L,GAAG/G,MAAH,CAAU6D,IAAV,CAAetI,KAAKsI,IAApB,EACb1C,IADa,CACR,WADQ,EACK5F,KAAKmK,SAAL,CAAetH,IAAf,CAAoBmB,EADzB,EAEb4B,IAFa,CAER,SAFQ,EAEG5F,KAAKoK,OAAL,CAAavH,IAAb,CAAkBmB,EAFrB,CAAf;AAGA;;AAED,WAASkP,MAAT,GAAkB;AAAE;AACnB5Q;AACAtC,QAAKH,OAAL,CAAa+F,IAAb,CAAkB,GAAlB,EAAuB5F,KAAKsI,IAA5B;AACA;AACDtI,OAAKkT,MAAL,GAAcA,MAAd;;AAEAjT;;AAEA,SAAOD,IAAP;AACA;;;;;;;ACxGD;AACA;AACA;AACA;AACA,EAAC,qBAAqB;;AAEtB;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA;AACA;AACA,IAAG;AACH;AACA;AACA,GAAE;AACF;AACA;;AAEA;AACA;AACA;AACA,oCAAmC;AACnC;;AAEA;AACA;AACA,GAAE;AACF;AACA;AACA,GAAE;AACF;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAE,IAAI;AACN;;AAEA;;AAEA;;AAEA,EAAC", "file": "frappe-gantt.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Gantt\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Gantt\"] = factory();\n\telse\n\t\troot[\"Gantt\"] = factory();\n})(this, function() {\nreturn \n\n\n/** WEBPACK FOOTER **\n ** webpack/universalModuleDefinition\n **/", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId])\n \t\t\treturn installedModules[moduleId].exports;\n\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\texports: {},\n \t\t\tid: moduleId,\n \t\t\tloaded: false\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.loaded = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(0);\n\n\n\n/** WEBPACK FOOTER **\n ** webpack/bootstrap 40f9d74a4b17f4d126f3\n **/", "/* global moment, Snap */\n/**\n * Gantt:\n * \telement: querySelector string, HTML DOM or SVG DOM element, required\n * \ttasks: array of tasks, required\n *   task: { id, name, start, end, progress, dependencies, custom_class }\n * \tconfig: configuration options, optional\n */\nimport './gantt.scss';\n\nimport Bar from './Bar';\nimport <PERSON> from './Arrow';\n\nexport default function Gantt(element, tasks, config = {}) {\n\n\tconst self = {};\n\n\tfunction init() {\n\t\tset_defaults();\n\n\t\t// expose methods\n\t\tself.change_view_mode = change_view_mode;\n\t\tself.unselect_all = unselect_all;\n\t\tself.view_is = view_is;\n\t\tself.get_bar = get_bar;\n\t\tself.trigger_event = trigger_event;\n\t\tself.refresh = refresh;\n\n\t\t// initialize with default view mode\n\t\tchange_view_mode(self.config.view_mode);\n\t}\n\n\tfunction set_defaults() {\n\n\t\tconst merge = require('deepmerge');\n\n\t\tconst defaults = {\n\t\t\theader_height: 50,\n\t\t\tcolumn_width: 30,\n\t\t\tstep: 24,\n\t\t\tview_modes: [\n\t\t\t\t'Quarter Day',\n\t\t\t\t'Half Day',\n\t\t\t\t'Day',\n\t\t\t\t'Week',\n\t\t\t\t'Month'\n\t\t\t],\n\t\t\tbar: {\n\t\t\t\theight: 20,\n\t\t\t\tcorner_radius: 3\n\t\t\t},\n\t\t\tarrow: {\n\t\t\t\tcurve: 5\n\t\t\t},\n\t\t\tpadding: 18,\n\t\t\tview_mode: 'Day',\n\t\t\tdate_format: 'YYYY-MM-DD',\n\t\t\tcustom_popup_html: null\n\t\t};\n\t\tself.config = merge(defaults, config);\n\n\t\treset_variables(tasks);\n\t}\n\n\tfunction reset_variables(tasks) {\n\t\tif(typeof element === 'string') {\n\t\t\tself.element = document.querySelector(element);\n\t\t} else if (element instanceof SVGElement) {\n\t\t\tself.element = element;\n\t\t} else if (element instanceof HTMLElement) {\n\t\t\tself.element = element.querySelector('svg');\n\t\t} else {\n\t\t\tthrow new TypeError('Frappé Gantt only supports usage of a string CSS selector,' +\n\t\t\t\t' HTML DOM element or SVG DOM element for the \\'element\\' parameter');\n\t\t}\n\n\t\tself._tasks = tasks;\n\n\t\tself._bars = [];\n\t\tself._arrows = [];\n\t\tself.element_groups = {};\n\t}\n\n\tfunction refresh(updated_tasks) {\n\t\treset_variables(updated_tasks);\n\t\tchange_view_mode(self.config.view_mode);\n\t}\n\n\tfunction change_view_mode(mode) {\n\t\tset_scale(mode);\n\t\tprepare();\n\t\trender();\n\t\t// fire viewmode_change event\n\t\ttrigger_event('view_change', [mode]);\n\t}\n\n\tfunction prepare() {\n\t\tprepare_tasks();\n\t\tprepare_dependencies();\n\t\tprepare_dates();\n\t\tprepare_canvas();\n\t}\n\n\tfunction prepare_tasks() {\n\n\t\t// prepare tasks\n\t\tself.tasks = self._tasks.map((task, i) => {\n\n\t\t\t// momentify\n\t\t\ttask._start = moment(task.start, self.config.date_format);\n\t\t\ttask._end = moment(task.end, self.config.date_format);\n\n\t\t\t// make task invalid if duration too large\n\t\t\tif(task._end.diff(task._start, 'years') > 10) {\n\t\t\t\ttask.end = null;\n\t\t\t}\n\n\t\t\t// cache index\n\t\t\ttask._index = i;\n\n\t\t\t// invalid dates\n\t\t\tif(!task.start && !task.end) {\n\t\t\t\ttask._start = moment().startOf('day');\n\t\t\t\ttask._end = moment().startOf('day').add(2, 'days');\n\t\t\t}\n\t\t\tif(!task.start && task.end) {\n\t\t\t\ttask._start = task._end.clone().add(-2, 'days');\n\t\t\t}\n\t\t\tif(task.start && !task.end) {\n\t\t\t\ttask._end = task._start.clone().add(2, 'days');\n\t\t\t}\n\n\t\t\t// invalid flag\n\t\t\tif(!task.start || !task.end) {\n\t\t\t\ttask.invalid = true;\n\t\t\t}\n\n\t\t\t// dependencies\n\t\t\tif(typeof task.dependencies === 'string' || !task.dependencies) {\n\t\t\t\tlet deps = [];\n\t\t\t\tif(task.dependencies) {\n\t\t\t\t\tdeps = task.dependencies\n\t\t\t\t\t\t.split(',')\n\t\t\t\t\t\t.map(d => d.trim())\n\t\t\t\t\t\t.filter((d) => d);\n\t\t\t\t}\n\t\t\t\ttask.dependencies = deps;\n\t\t\t}\n\n\t\t\t// uids\n\t\t\tif(!task.id) {\n\t\t\t\ttask.id = generate_id(task);\n\t\t\t}\n\n\t\t\treturn task;\n\t\t});\n\t}\n\n\tfunction prepare_dependencies() {\n\n\t\tself.dependency_map = {};\n\t\tfor(let t of self.tasks) {\n\t\t\tfor(let d of t.dependencies) {\n\t\t\t\tself.dependency_map[d] = self.dependency_map[d] || [];\n\t\t\t\tself.dependency_map[d].push(t.id);\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction prepare_dates() {\n\n\t\tself.gantt_start = self.gantt_end = null;\n\t\tfor(let task of self.tasks) {\n\t\t\t// set global start and end date\n\t\t\tif(!self.gantt_start || task._start < self.gantt_start) {\n\t\t\t\tself.gantt_start = task._start;\n\t\t\t}\n\t\t\tif(!self.gantt_end || task._end > self.gantt_end) {\n\t\t\t\tself.gantt_end = task._end;\n\t\t\t}\n\t\t}\n\t\tset_gantt_dates();\n\t\tsetup_dates();\n\t}\n\n\tfunction prepare_canvas() {\n\t\tif(self.canvas) return;\n\t\tself.canvas = Snap(self.element).addClass('gantt');\n\t}\n\n\tfunction render() {\n\t\tclear();\n\t\tsetup_groups();\n\t\tmake_grid();\n\t\tmake_dates();\n\t\tmake_bars();\n\t\tmake_arrows();\n\t\tmap_arrows_on_bars();\n\t\tset_width();\n\t\tset_scroll_position();\n\t\tbind_grid_click();\n\t}\n\n\tfunction clear() {\n\t\tself.canvas.clear();\n\t\tself._bars = [];\n\t\tself._arrows = [];\n\t}\n\n\tfunction set_gantt_dates() {\n\n\t\tif(view_is(['Quarter Day', 'Half Day'])) {\n\t\t\tself.gantt_start = self.gantt_start.clone().subtract(7, 'day');\n\t\t\tself.gantt_end = self.gantt_end.clone().add(7, 'day');\n\t\t} else if(view_is('Month')) {\n\t\t\tself.gantt_start = self.gantt_start.clone().startOf('year');\n\t\t\tself.gantt_end = self.gantt_end.clone().endOf('month').add(1, 'year');\n\t\t} else {\n\t\t\tself.gantt_start = self.gantt_start.clone().startOf('month').subtract(1, 'month');\n\t\t\tself.gantt_end = self.gantt_end.clone().endOf('month').add(1, 'month');\n\t\t}\n\t}\n\n\tfunction setup_dates() {\n\n\t\tself.dates = [];\n\t\tlet cur_date = null;\n\n\t\twhile(cur_date === null || cur_date < self.gantt_end) {\n\t\t\tif(!cur_date) {\n\t\t\t\tcur_date = self.gantt_start.clone();\n\t\t\t} else {\n\t\t\t\tcur_date = view_is('Month') ?\n\t\t\t\t\tcur_date.clone().add(1, 'month') :\n\t\t\t\t\tcur_date.clone().add(self.config.step, 'hours');\n\t\t\t}\n\t\t\tself.dates.push(cur_date);\n\t\t}\n\t}\n\n\tfunction setup_groups() {\n\n\t\tconst groups = ['grid', 'date', 'arrow', 'progress', 'bar', 'details'];\n\t\t// make group layers\n\t\tfor(let group of groups) {\n\t\t\tself.element_groups[group] = self.canvas.group().attr({'id': group});\n\t\t}\n\t}\n\n\tfunction set_scale(scale) {\n\t\tself.config.view_mode = scale;\n\n\t\tif(scale === 'Day') {\n\t\t\tself.config.step = 24;\n\t\t\tself.config.column_width = 38;\n\t\t} else if(scale === 'Half Day') {\n\t\t\tself.config.step = 24 / 2;\n\t\t\tself.config.column_width = 38;\n\t\t} else if(scale === 'Quarter Day') {\n\t\t\tself.config.step = 24 / 4;\n\t\t\tself.config.column_width = 38;\n\t\t} else if(scale === 'Week') {\n\t\t\tself.config.step = 24 * 7;\n\t\t\tself.config.column_width = 140;\n\t\t} else if(scale === 'Month') {\n\t\t\tself.config.step = 24 * 30;\n\t\t\tself.config.column_width = 120;\n\t\t}\n\t}\n\n\tfunction set_width() {\n\t\tconst cur_width = self.canvas.node.getBoundingClientRect().width;\n\t\tconst actual_width = self.canvas.select('#grid .grid-row').attr('width');\n\t\tif(cur_width < actual_width) {\n\t\t\tself.canvas.attr('width', actual_width);\n\t\t}\n\t}\n\n\tfunction set_scroll_position() {\n\t\tconst parent_element = self.element.parentElement;\n\n\t\tif(!parent_element) return;\n\n\t\tconst scroll_pos = get_min_date().diff(self.gantt_start, 'hours') /\n\t\t\tself.config.step * self.config.column_width - self.config.column_width;\n\t\tparent_element.scrollLeft = scroll_pos;\n\t}\n\n\tfunction get_min_date() {\n\t\tconst task = self.tasks.reduce((acc, curr) => {\n\t\t\treturn curr._start.isSameOrBefore(acc._start) ? curr : acc;\n\t\t});\n\t\treturn task._start;\n\t}\n\n\tfunction make_grid() {\n\t\tmake_grid_background();\n\t\tmake_grid_rows();\n\t\tmake_grid_header();\n\t\tmake_grid_ticks();\n\t\tmake_grid_highlights();\n\t}\n\n\tfunction make_grid_background() {\n\n\t\tconst grid_width = self.dates.length * self.config.column_width,\n\t\t\tgrid_height = self.config.header_height + self.config.padding +\n\t\t\t\t(self.config.bar.height + self.config.padding) * self.tasks.length;\n\n\t\tself.canvas.rect(0, 0, grid_width, grid_height)\n\t\t\t.addClass('grid-background')\n\t\t\t.appendTo(self.element_groups.grid);\n\n\t\tself.canvas.attr({\n\t\t\theight: grid_height + self.config.padding + 100,\n\t\t\twidth: '100%'\n\t\t});\n\t}\n\n\tfunction make_grid_header() {\n\t\tconst header_width = self.dates.length * self.config.column_width,\n\t\t\theader_height = self.config.header_height + 10;\n\t\tself.canvas.rect(0, 0, header_width, header_height)\n\t\t\t.addClass('grid-header')\n\t\t\t.appendTo(self.element_groups.grid);\n\t}\n\n\tfunction make_grid_rows() {\n\n\t\tconst rows = self.canvas.group().appendTo(self.element_groups.grid),\n\t\t\tlines = self.canvas.group().appendTo(self.element_groups.grid),\n\t\t\trow_width = self.dates.length * self.config.column_width,\n\t\t\trow_height = self.config.bar.height + self.config.padding;\n\n\t\tlet row_y = self.config.header_height + self.config.padding / 2;\n\n\t\tfor(let task of self.tasks) { // eslint-disable-line\n\t\t\tself.canvas.rect(0, row_y, row_width, row_height)\n\t\t\t\t.addClass('grid-row')\n\t\t\t\t.appendTo(rows);\n\n\t\t\tself.canvas.line(0, row_y + row_height, row_width, row_y + row_height)\n\t\t\t\t.addClass('row-line')\n\t\t\t\t.appendTo(lines);\n\n\t\t\trow_y += self.config.bar.height + self.config.padding;\n\t\t}\n\t}\n\n\tfunction make_grid_ticks() {\n\t\tlet tick_x = 0,\n\t\t\ttick_y = self.config.header_height + self.config.padding / 2,\n\t\t\ttick_height = (self.config.bar.height + self.config.padding) * self.tasks.length;\n\n\t\tfor(let date of self.dates) {\n\t\t\tlet tick_class = 'tick';\n\t\t\t// thick tick for monday\n\t\t\tif(view_is('Day') && date.day() === 1) {\n\t\t\t\ttick_class += ' thick';\n\t\t\t}\n\t\t\t// thick tick for first week\n\t\t\tif(view_is('Week') && date.date() >= 1 && date.date() < 8) {\n\t\t\t\ttick_class += ' thick';\n\t\t\t}\n\t\t\t// thick ticks for quarters\n\t\t\tif(view_is('Month') && date.month() % 3 === 0) {\n\t\t\t\ttick_class += ' thick';\n\t\t\t}\n\n\t\t\tself.canvas.path(Snap.format('M {x} {y} v {height}', {\n\t\t\t\tx: tick_x,\n\t\t\t\ty: tick_y,\n\t\t\t\theight: tick_height\n\t\t\t}))\n\t\t\t.addClass(tick_class)\n\t\t\t.appendTo(self.element_groups.grid);\n\n\t\t\tif(view_is('Month')) {\n\t\t\t\ttick_x += date.daysInMonth() * self.config.column_width / 30;\n\t\t\t} else {\n\t\t\t\ttick_x += self.config.column_width;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction make_grid_highlights() {\n\n\t\t// highlight today's date\n\t\tif(view_is('Day')) {\n\t\t\tconst x = moment().startOf('day').diff(self.gantt_start, 'hours') /\n\t\t\t\t\tself.config.step * self.config.column_width;\n\t\t\tconst y = 0;\n\t\t\tconst width = self.config.column_width;\n\t\t\tconst height = (self.config.bar.height + self.config.padding) * self.tasks.length +\n\t\t\t\tself.config.header_height + self.config.padding / 2;\n\n\t\t\tself.canvas.rect(x, y, width, height)\n\t\t\t\t.addClass('today-highlight')\n\t\t\t\t.appendTo(self.element_groups.grid);\n\t\t}\n\t}\n\n\tfunction make_dates() {\n\n\t\tfor(let date of get_dates_to_draw()) {\n\t\t\tself.canvas.text(date.lower_x, date.lower_y, date.lower_text)\n\t\t\t\t.addClass('lower-text')\n\t\t\t\t.appendTo(self.element_groups.date);\n\n\t\t\tif(date.upper_text) {\n\t\t\t\tconst $upper_text = self.canvas.text(date.upper_x, date.upper_y, date.upper_text)\n\t\t\t\t\t.addClass('upper-text')\n\t\t\t\t\t.appendTo(self.element_groups.date);\n\n\t\t\t\t// remove out-of-bound dates\n\t\t\t\tif($upper_text.getBBox().x2 > self.element_groups.grid.getBBox().width) {\n\t\t\t\t\t$upper_text.remove();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction get_dates_to_draw() {\n\t\tlet last_date = null;\n\t\tconst dates = self.dates.map((date, i) => {\n\t\t\tconst d = get_date_info(date, last_date, i);\n\t\t\tlast_date = date;\n\t\t\treturn d;\n\t\t});\n\t\treturn dates;\n\t}\n\n\tfunction get_date_info(date, last_date, i) {\n\t\tif(!last_date) {\n\t\t\tlast_date = date.clone().add(1, 'year');\n\t\t}\n\t\tconst date_text = {\n\t\t\t'Quarter Day_lower': date.format('HH'),\n\t\t\t'Half Day_lower': date.format('HH'),\n\t\t\t'Day_lower': date.date() !== last_date.date() ? date.format('D') : '',\n\t\t\t'Week_lower': date.month() !== last_date.month() ?\n\t\t\t\tdate.format('D MMM') : date.format('D'),\n\t\t\t'Month_lower': date.format('MMMM'),\n\t\t\t'Quarter Day_upper': date.date() !== last_date.date() ? date.format('D MMM') : '',\n\t\t\t'Half Day_upper': date.date() !== last_date.date() ?\n\t\t\t\tdate.month() !== last_date.month() ?\n\t\t\t\tdate.format('D MMM') : date.format('D') : '',\n\t\t\t'Day_upper': date.month() !== last_date.month() ? date.format('MMMM') : '',\n\t\t\t'Week_upper': date.month() !== last_date.month() ? date.format('MMMM') : '',\n\t\t\t'Month_upper': date.year() !== last_date.year() ? date.format('YYYY') : ''\n\t\t};\n\n\t\tconst base_pos = {\n\t\t\tx: i * self.config.column_width,\n\t\t\tlower_y: self.config.header_height,\n\t\t\tupper_y: self.config.header_height - 25\n\t\t};\n\n\t\tconst x_pos = {\n\t\t\t'Quarter Day_lower': (self.config.column_width * 4) / 2,\n\t\t\t'Quarter Day_upper': 0,\n\t\t\t'Half Day_lower': (self.config.column_width * 2) / 2,\n\t\t\t'Half Day_upper': 0,\n\t\t\t'Day_lower': self.config.column_width / 2,\n\t\t\t'Day_upper': (self.config.column_width * 30) / 2,\n\t\t\t'Week_lower': 0,\n\t\t\t'Week_upper': (self.config.column_width * 4) / 2,\n\t\t\t'Month_lower': self.config.column_width / 2,\n\t\t\t'Month_upper': (self.config.column_width * 12) / 2\n\t\t};\n\n\t\treturn {\n\t\t\tupper_text: date_text[`${self.config.view_mode}_upper`],\n\t\t\tlower_text: date_text[`${self.config.view_mode}_lower`],\n\t\t\tupper_x: base_pos.x + x_pos[`${self.config.view_mode}_upper`],\n\t\t\tupper_y: base_pos.upper_y,\n\t\t\tlower_x: base_pos.x + x_pos[`${self.config.view_mode}_lower`],\n\t\t\tlower_y: base_pos.lower_y\n\t\t};\n\t}\n\n\tfunction make_arrows() {\n\t\tself._arrows = [];\n\t\tfor(let task of self.tasks) {\n\t\t\tlet arrows = [];\n\t\t\tarrows = task.dependencies.map(dep => {\n\t\t\t\tconst dependency = get_task(dep);\n\t\t\t\tif(!dependency) return;\n\n\t\t\t\tconst arrow = Arrow(\n\t\t\t\t\tself, // gt\n\t\t\t\t\tself._bars[dependency._index], // from_task\n\t\t\t\t\tself._bars[task._index] // to_task\n\t\t\t\t);\n\t\t\t\tself.element_groups.arrow.add(arrow.element);\n\t\t\t\treturn arrow; // eslint-disable-line\n\t\t\t}).filter(arr => arr); // filter falsy values\n\t\t\tself._arrows = self._arrows.concat(arrows);\n\t\t}\n\t}\n\n\tfunction make_bars() {\n\n\t\tself._bars = self.tasks.map((task) => {\n\t\t\tconst bar = Bar(self, task);\n\t\t\tself.element_groups.bar.add(bar.group);\n\t\t\treturn bar;\n\t\t});\n\t}\n\n\tfunction map_arrows_on_bars() {\n\t\tfor(let bar of self._bars) {\n\t\t\tbar.arrows = self._arrows.filter(arrow => {\n\t\t\t\treturn (arrow.from_task.task.id === bar.task.id) ||\n\t\t\t\t\t(arrow.to_task.task.id === bar.task.id);\n\t\t\t});\n\t\t}\n\t}\n\n\tfunction bind_grid_click() {\n\t\tself.element_groups.grid.click(() => {\n\t\t\tunselect_all();\n\t\t\tself.element_groups.details\n\t\t\t\t.selectAll('.details-wrapper')\n\t\t\t\t.forEach(el => el.addClass('hide'));\n\t\t});\n\t}\n\n\tfunction unselect_all() {\n\t\tself.canvas.selectAll('.bar-wrapper').forEach(el => {\n\t\t\tel.removeClass('active');\n\t\t});\n\t}\n\n\tfunction view_is(modes) {\n\t\tif (typeof modes === 'string') {\n\t\t\treturn self.config.view_mode === modes;\n\t\t} else if(Array.isArray(modes)) {\n\t\t\tfor (let mode of modes) {\n\t\t\t\tif(self.config.view_mode === mode) return true;\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tfunction get_task(id) {\n\t\treturn self.tasks.find((task) => {\n\t\t\treturn task.id === id;\n\t\t});\n\t}\n\n\tfunction get_bar(id) {\n\t\treturn self._bars.find((bar) => {\n\t\t\treturn bar.task.id === id;\n\t\t});\n\t}\n\n\tfunction generate_id(task) {\n\t\treturn task.name + '_' + Math.random().toString(36).slice(2, 12);\n\t}\n\n\tfunction trigger_event(event, args) {\n\t\tif(self.config['on_' + event]) {\n\t\t\tself.config['on_' + event].apply(null, args);\n\t\t}\n\t}\n\n\tinit();\n\n\treturn self;\n}\n\n\n\n/** WEBPACK FOOTER **\n ** ./~/eslint-loader!./src/Gantt.js\n **/", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// add the styles to the DOM\nvar update = require(\"!../node_modules/style-loader/addStyles.js\")(content, {});\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\", function() {\n\t\t\tvar newContent = require(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./src/gantt.scss\n ** module id = 1\n ** module chunks = 0\n **/", "exports = module.exports = require(\"../node_modules/css-loader/lib/css-base.js\")();\n// imports\n\n\n// module\nexports.push([module.id, \".gantt .grid-background {\\n  fill: none; }\\n\\n.gantt .grid-header {\\n  fill: #ffffff;\\n  stroke: #e0e0e0;\\n  stroke-width: 1.4; }\\n\\n.gantt .grid-row {\\n  fill: #ffffff; }\\n\\n.gantt .grid-row:nth-child(even) {\\n  fill: #f5f5f5; }\\n\\n.gantt .row-line {\\n  stroke: #ebeff2; }\\n\\n.gantt .tick {\\n  stroke: #e0e0e0;\\n  stroke-width: 0.2; }\\n  .gantt .tick.thick {\\n    stroke-width: 0.4; }\\n\\n.gantt .today-highlight {\\n  fill: #fcf8e3;\\n  opacity: 0.5; }\\n\\n.gantt #arrow {\\n  fill: none;\\n  stroke: #666;\\n  stroke-width: 1.4; }\\n\\n.gantt .bar {\\n  fill: #b8c2cc;\\n  stroke: #8D99A6;\\n  stroke-width: 0;\\n  transition: stroke-width .3s ease; }\\n\\n.gantt .bar-progress {\\n  fill: #a3a3ff; }\\n\\n.gantt .bar-invalid {\\n  fill: transparent;\\n  stroke: #8D99A6;\\n  stroke-width: 1;\\n  stroke-dasharray: 5; }\\n  .gantt .bar-invalid ~ .bar-label {\\n    fill: #555; }\\n\\n.gantt .bar-label {\\n  fill: #fff;\\n  dominant-baseline: central;\\n  text-anchor: middle;\\n  font-size: 12px;\\n  font-weight: lighter; }\\n  .gantt .bar-label.big {\\n    fill: #555;\\n    text-anchor: start; }\\n\\n.gantt .handle {\\n  fill: #ddd;\\n  cursor: ew-resize;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity .3s ease; }\\n\\n.gantt .bar-wrapper {\\n  cursor: pointer; }\\n  .gantt .bar-wrapper:hover .bar {\\n    stroke-width: 2; }\\n  .gantt .bar-wrapper:hover .handle {\\n    visibility: visible;\\n    opacity: 1; }\\n  .gantt .bar-wrapper.active .bar {\\n    stroke-width: 2; }\\n\\n.gantt .lower-text, .gantt .upper-text {\\n  font-size: 12px;\\n  text-anchor: middle; }\\n\\n.gantt .upper-text {\\n  fill: #555; }\\n\\n.gantt .lower-text {\\n  fill: #333; }\\n\\n.gantt #details .details-container {\\n  background: #fff;\\n  display: inline-block;\\n  padding: 12px; }\\n  .gantt #details .details-container h5, .gantt #details .details-container p {\\n    margin: 0; }\\n  .gantt #details .details-container h5 {\\n    font-size: 12px;\\n    font-weight: bold;\\n    margin-bottom: 10px;\\n    color: #555; }\\n  .gantt #details .details-container p {\\n    font-size: 12px;\\n    margin-bottom: 6px;\\n    color: #666; }\\n  .gantt #details .details-container p:last-child {\\n    margin-bottom: 0; }\\n\\n.gantt .hide {\\n  display: none; }\\n\", \"\", {\"version\":3,\"sources\":[\"/Users/<USER>/Dropbox/gantt/src/src/gantt.scss\"],\"names\":[],\"mappings\":\"AAYA;EAGE,WAAU,EACV;;AAJF;EAME,cAAa;EACb,gBAjBoB;EAkBpB,kBAAiB,EACjB;;AATF;EAWE,cAAa,EACb;;AAZF;EAcE,cAvBgB,EAwBhB;;AAfF;EAiBE,gBAzB0B,EA0B1B;;AAlBF;EAoBE,gBA9BoB;EA+BpB,kBAAiB,EAIjB;EAzBF;IAuBG,kBAAiB,EACjB;;AAxBH;EA2BE,cAlCoB;EAmCpB,aAAY,EACZ;;AA7BF;EAgCE,WAAU;EACV,aAvCe;EAwCf,kBAAiB,EACjB;;AAnCF;EAsCE,cAlDiB;EAmDjB,gBAlDkB;EAmDlB,gBAAe;EACf,kCAAiC,EACjC;;AA1CF;EA4CE,cA/CY,EAgDZ;;AA7CF;EA+CE,kBAAiB;EACjB,gBA3DkB;EA4DlB,gBAAe;EACf,oBAAmB,EAKnB;EAvDF;IAqDG,WA1Dc,EA2Dd;;AAtDH;EAyDE,WAAU;EACV,2BAA0B;EAC1B,oBAAmB;EACnB,gBAAe;EACf,qBAAoB,EAMpB;EAnEF;IAgEG,WArEc;IAsEd,mBAAkB,EAClB;;AAlEH;EAsEE,WAxEiB;EAyEjB,kBAAiB;EACjB,WAAU;EACV,mBAAkB;EAClB,6BAA4B,EAC5B;;AA3EF;EA8EE,gBAAe,EAkBf;EAhGF;IAkFI,gBAAe,EACf;EAnFJ;IAsFI,oBAAmB;IACnB,WAAU,EACV;EAxFJ;IA6FI,gBAAe,EACf;;AA9FJ;EAmGE,gBAAe;EACf,oBAAmB,EACnB;;AArGF;EAuGE,WA5Ge,EA6Gf;;AAxGF;EA0GE,WA9Ge,EA+Gf;;AA3GF;EA8GE,iBAAgB;EAChB,sBAAqB;EACrB,cAAa,EAsBb;EAtIF;IAmHG,UAAS,EACT;EApHH;IAuHG,gBAAe;IACf,kBAAiB;IACjB,oBAAmB;IACnB,YA/Hc,EAgId;EA3HH;IA8HG,gBAAe;IACf,mBAAkB;IAClB,YAtIc,EAuId;EAjIH;IAoIG,iBAAgB,EAChB;;AArIH;EAyIE,cAAa,EACb\",\"file\":\"gantt.scss\",\"sourcesContent\":[\"$bar-color: #b8c2cc;\\n$bar-stroke: #8D99A6;\\n$border-color: #e0e0e0;\\n$light-bg: #f5f5f5;\\n$light-border-color: #ebeff2;\\n$light-yellow: #fcf8e3;\\n$text-muted: #666;\\n$text-light: #555;\\n$text-color: #333;\\n$blue: #a3a3ff;\\n$handle-color: #ddd;\\n\\n.gantt {\\n\\n\\t.grid-background {\\n\\t\\tfill: none;\\n\\t}\\n\\t.grid-header {\\n\\t\\tfill: #ffffff;\\n\\t\\tstroke: $border-color;\\n\\t\\tstroke-width: 1.4;\\n\\t}\\n\\t.grid-row {\\n\\t\\tfill: #ffffff;\\n\\t}\\n\\t.grid-row:nth-child(even) {\\n\\t\\tfill: $light-bg;\\n\\t}\\n\\t.row-line {\\n\\t\\tstroke: $light-border-color;\\n\\t}\\n\\t.tick {\\n\\t\\tstroke: $border-color;\\n\\t\\tstroke-width: 0.2;\\n\\t\\t&.thick {\\n\\t\\t\\tstroke-width: 0.4;\\n\\t\\t}\\n\\t}\\n\\t.today-highlight {\\n\\t\\tfill: $light-yellow;\\n\\t\\topacity: 0.5;\\n\\t}\\n\\n\\t#arrow {\\n\\t\\tfill: none;\\n\\t\\tstroke: $text-muted;\\n\\t\\tstroke-width: 1.4;\\n\\t}\\n\\n\\t.bar {\\n\\t\\tfill: $bar-color;\\n\\t\\tstroke: $bar-stroke;\\n\\t\\tstroke-width: 0;\\n\\t\\ttransition: stroke-width .3s ease;\\n\\t}\\n\\t.bar-progress {\\n\\t\\tfill: $blue;\\n\\t}\\n\\t.bar-invalid {\\n\\t\\tfill: transparent;\\n\\t\\tstroke: $bar-stroke;\\n\\t\\tstroke-width: 1;\\n\\t\\tstroke-dasharray: 5;\\n\\n\\t\\t&~.bar-label {\\n\\t\\t\\tfill: $text-light;\\n\\t\\t}\\n\\t}\\n\\t.bar-label {\\n\\t\\tfill: #fff;\\n\\t\\tdominant-baseline: central;\\n\\t\\ttext-anchor: middle;\\n\\t\\tfont-size: 12px;\\n\\t\\tfont-weight: lighter;\\n\\n\\t\\t&.big {\\n\\t\\t\\tfill: $text-light;\\n\\t\\t\\ttext-anchor: start;\\n\\t\\t}\\n\\t}\\n\\n\\t.handle {\\n\\t\\tfill: $handle-color;\\n\\t\\tcursor: ew-resize;\\n\\t\\topacity: 0;\\n\\t\\tvisibility: hidden;\\n\\t\\ttransition: opacity .3s ease;\\n\\t}\\n\\n\\t.bar-wrapper {\\n\\t\\tcursor: pointer;\\n\\n\\t\\t&:hover {\\n\\t\\t\\t.bar {\\n\\t\\t\\t\\tstroke-width: 2;\\n\\t\\t\\t}\\n\\n\\t\\t\\t.handle {\\n\\t\\t\\t\\tvisibility: visible;\\n\\t\\t\\t\\topacity: 1;\\n\\t\\t\\t}\\n\\t\\t}\\n\\n\\t\\t&.active {\\n\\t\\t\\t.bar {\\n\\t\\t\\t\\tstroke-width: 2;\\n\\t\\t\\t}\\n\\t\\t}\\n\\t}\\n\\n\\t.lower-text, .upper-text {\\n\\t\\tfont-size: 12px;\\n\\t\\ttext-anchor: middle;\\n\\t}\\n\\t.upper-text {\\n\\t\\tfill: $text-light;\\n\\t}\\n\\t.lower-text {\\n\\t\\tfill: $text-color;\\n\\t}\\n\\n\\t#details .details-container {\\n\\t\\tbackground: #fff;\\n\\t\\tdisplay: inline-block;\\n\\t\\tpadding: 12px;\\n\\n\\t\\th5, p {\\n\\t\\t\\tmargin: 0;\\n\\t\\t}\\n\\n\\t\\th5 {\\n\\t\\t\\tfont-size: 12px;\\n\\t\\t\\tfont-weight: bold;\\n\\t\\t\\tmargin-bottom: 10px;\\n\\t\\t\\tcolor: $text-light;\\n\\t\\t}\\n\\n\\t\\tp {\\n\\t\\t\\tfont-size: 12px;\\n\\t\\t\\tmargin-bottom: 6px;\\n\\t\\t\\tcolor: $text-muted;\\n\\t\\t}\\n\\n\\t\\tp:last-child {\\n\\t\\t\\tmargin-bottom: 0;\\n\\t\\t}\\n\\t}\\n\\n\\t.hide {\\n\\t\\tdisplay: none;\\n\\t}\\n}\\n\"],\"sourceRoot\":\"\"}]);\n\n// exports\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/css-loader?sourceMap!./~/sass-loader?sourceMap!./src/gantt.scss\n ** module id = 2\n ** module chunks = 0\n **/", "/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\n// css base code, injected by the css-loader\nmodule.exports = function() {\n\tvar list = [];\n\n\t// return the list of modules as css string\n\tlist.toString = function toString() {\n\t\tvar result = [];\n\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\tvar item = this[i];\n\t\t\tif(item[2]) {\n\t\t\t\tresult.push(\"@media \" + item[2] + \"{\" + item[1] + \"}\");\n\t\t\t} else {\n\t\t\t\tresult.push(item[1]);\n\t\t\t}\n\t\t}\n\t\treturn result.join(\"\");\n\t};\n\n\t// import a list of modules into the list\n\tlist.i = function(modules, mediaQuery) {\n\t\tif(typeof modules === \"string\")\n\t\t\tmodules = [[null, modules, \"\"]];\n\t\tvar alreadyImportedModules = {};\n\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\tvar id = this[i][0];\n\t\t\tif(typeof id === \"number\")\n\t\t\t\talreadyImportedModules[id] = true;\n\t\t}\n\t\tfor(i = 0; i < modules.length; i++) {\n\t\t\tvar item = modules[i];\n\t\t\t// skip already imported module\n\t\t\t// this implementation is not 100% perfect for weird media query combinations\n\t\t\t//  when a module is imported multiple times with different media queries.\n\t\t\t//  I hope this will never occur (Hey this way we have smaller bundles)\n\t\t\tif(typeof item[0] !== \"number\" || !alreadyImportedModules[item[0]]) {\n\t\t\t\tif(mediaQuery && !item[2]) {\n\t\t\t\t\titem[2] = mediaQuery;\n\t\t\t\t} else if(mediaQuery) {\n\t\t\t\t\titem[2] = \"(\" + item[2] + \") and (\" + mediaQuery + \")\";\n\t\t\t\t}\n\t\t\t\tlist.push(item);\n\t\t\t}\n\t\t}\n\t};\n\treturn list;\n};\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/css-loader/lib/css-base.js\n ** module id = 3\n ** module chunks = 0\n **/", "/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\nvar stylesInDom = {},\n\tmemoize = function(fn) {\n\t\tvar memo;\n\t\treturn function () {\n\t\t\tif (typeof memo === \"undefined\") memo = fn.apply(this, arguments);\n\t\t\treturn memo;\n\t\t};\n\t},\n\tisOldIE = memoize(function() {\n\t\treturn /msie [6-9]\\b/.test(self.navigator.userAgent.toLowerCase());\n\t}),\n\tgetHeadElement = memoize(function () {\n\t\treturn document.head || document.getElementsByTagName(\"head\")[0];\n\t}),\n\tsingletonElement = null,\n\tsingletonCounter = 0,\n\tstyleElementsInsertedAtTop = [];\n\nmodule.exports = function(list, options) {\n\tif(typeof DEBUG !== \"undefined\" && DEBUG) {\n\t\tif(typeof document !== \"object\") throw new Error(\"The style-loader cannot be used in a non-browser environment\");\n\t}\n\n\toptions = options || {};\n\t// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n\t// tags it will allow on a page\n\tif (typeof options.singleton === \"undefined\") options.singleton = isOldIE();\n\n\t// By default, add <style> tags to the bottom of <head>.\n\tif (typeof options.insertAt === \"undefined\") options.insertAt = \"bottom\";\n\n\tvar styles = listToStyles(list);\n\taddStylesToDom(styles, options);\n\n\treturn function update(newList) {\n\t\tvar mayRemove = [];\n\t\tfor(var i = 0; i < styles.length; i++) {\n\t\t\tvar item = styles[i];\n\t\t\tvar domStyle = stylesInDom[item.id];\n\t\t\tdomStyle.refs--;\n\t\t\tmayRemove.push(domStyle);\n\t\t}\n\t\tif(newList) {\n\t\t\tvar newStyles = listToStyles(newList);\n\t\t\taddStylesToDom(newStyles, options);\n\t\t}\n\t\tfor(var i = 0; i < mayRemove.length; i++) {\n\t\t\tvar domStyle = mayRemove[i];\n\t\t\tif(domStyle.refs === 0) {\n\t\t\t\tfor(var j = 0; j < domStyle.parts.length; j++)\n\t\t\t\t\tdomStyle.parts[j]();\n\t\t\t\tdelete stylesInDom[domStyle.id];\n\t\t\t}\n\t\t}\n\t};\n}\n\nfunction addStylesToDom(styles, options) {\n\tfor(var i = 0; i < styles.length; i++) {\n\t\tvar item = styles[i];\n\t\tvar domStyle = stylesInDom[item.id];\n\t\tif(domStyle) {\n\t\t\tdomStyle.refs++;\n\t\t\tfor(var j = 0; j < domStyle.parts.length; j++) {\n\t\t\t\tdomStyle.parts[j](item.parts[j]);\n\t\t\t}\n\t\t\tfor(; j < item.parts.length; j++) {\n\t\t\t\tdomStyle.parts.push(addStyle(item.parts[j], options));\n\t\t\t}\n\t\t} else {\n\t\t\tvar parts = [];\n\t\t\tfor(var j = 0; j < item.parts.length; j++) {\n\t\t\t\tparts.push(addStyle(item.parts[j], options));\n\t\t\t}\n\t\t\tstylesInDom[item.id] = {id: item.id, refs: 1, parts: parts};\n\t\t}\n\t}\n}\n\nfunction listToStyles(list) {\n\tvar styles = [];\n\tvar newStyles = {};\n\tfor(var i = 0; i < list.length; i++) {\n\t\tvar item = list[i];\n\t\tvar id = item[0];\n\t\tvar css = item[1];\n\t\tvar media = item[2];\n\t\tvar sourceMap = item[3];\n\t\tvar part = {css: css, media: media, sourceMap: sourceMap};\n\t\tif(!newStyles[id])\n\t\t\tstyles.push(newStyles[id] = {id: id, parts: [part]});\n\t\telse\n\t\t\tnewStyles[id].parts.push(part);\n\t}\n\treturn styles;\n}\n\nfunction insertStyleElement(options, styleElement) {\n\tvar head = getHeadElement();\n\tvar lastStyleElementInsertedAtTop = styleElementsInsertedAtTop[styleElementsInsertedAtTop.length - 1];\n\tif (options.insertAt === \"top\") {\n\t\tif(!lastStyleElementInsertedAtTop) {\n\t\t\thead.insertBefore(styleElement, head.firstChild);\n\t\t} else if(lastStyleElementInsertedAtTop.nextSibling) {\n\t\t\thead.insertBefore(styleElement, lastStyleElementInsertedAtTop.nextSibling);\n\t\t} else {\n\t\t\thead.appendChild(styleElement);\n\t\t}\n\t\tstyleElementsInsertedAtTop.push(styleElement);\n\t} else if (options.insertAt === \"bottom\") {\n\t\thead.appendChild(styleElement);\n\t} else {\n\t\tthrow new Error(\"Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.\");\n\t}\n}\n\nfunction removeStyleElement(styleElement) {\n\tstyleElement.parentNode.removeChild(styleElement);\n\tvar idx = styleElementsInsertedAtTop.indexOf(styleElement);\n\tif(idx >= 0) {\n\t\tstyleElementsInsertedAtTop.splice(idx, 1);\n\t}\n}\n\nfunction createStyleElement(options) {\n\tvar styleElement = document.createElement(\"style\");\n\tstyleElement.type = \"text/css\";\n\tinsertStyleElement(options, styleElement);\n\treturn styleElement;\n}\n\nfunction createLinkElement(options) {\n\tvar linkElement = document.createElement(\"link\");\n\tlinkElement.rel = \"stylesheet\";\n\tinsertStyleElement(options, linkElement);\n\treturn linkElement;\n}\n\nfunction addStyle(obj, options) {\n\tvar styleElement, update, remove;\n\n\tif (options.singleton) {\n\t\tvar styleIndex = singletonCounter++;\n\t\tstyleElement = singletonElement || (singletonElement = createStyleElement(options));\n\t\tupdate = applyToSingletonTag.bind(null, styleElement, styleIndex, false);\n\t\tremove = applyToSingletonTag.bind(null, styleElement, styleIndex, true);\n\t} else if(obj.sourceMap &&\n\t\ttypeof URL === \"function\" &&\n\t\ttypeof URL.createObjectURL === \"function\" &&\n\t\ttypeof URL.revokeObjectURL === \"function\" &&\n\t\ttypeof Blob === \"function\" &&\n\t\ttypeof btoa === \"function\") {\n\t\tstyleElement = createLinkElement(options);\n\t\tupdate = updateLink.bind(null, styleElement);\n\t\tremove = function() {\n\t\t\tremoveStyleElement(styleElement);\n\t\t\tif(styleElement.href)\n\t\t\t\tURL.revokeObjectURL(styleElement.href);\n\t\t};\n\t} else {\n\t\tstyleElement = createStyleElement(options);\n\t\tupdate = applyToTag.bind(null, styleElement);\n\t\tremove = function() {\n\t\t\tremoveStyleElement(styleElement);\n\t\t};\n\t}\n\n\tupdate(obj);\n\n\treturn function updateStyle(newObj) {\n\t\tif(newObj) {\n\t\t\tif(newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap)\n\t\t\t\treturn;\n\t\t\tupdate(obj = newObj);\n\t\t} else {\n\t\t\tremove();\n\t\t}\n\t};\n}\n\nvar replaceText = (function () {\n\tvar textStore = [];\n\n\treturn function (index, replacement) {\n\t\ttextStore[index] = replacement;\n\t\treturn textStore.filter(Boolean).join('\\n');\n\t};\n})();\n\nfunction applyToSingletonTag(styleElement, index, remove, obj) {\n\tvar css = remove ? \"\" : obj.css;\n\n\tif (styleElement.styleSheet) {\n\t\tstyleElement.styleSheet.cssText = replaceText(index, css);\n\t} else {\n\t\tvar cssNode = document.createTextNode(css);\n\t\tvar childNodes = styleElement.childNodes;\n\t\tif (childNodes[index]) styleElement.removeChild(childNodes[index]);\n\t\tif (childNodes.length) {\n\t\t\tstyleElement.insertBefore(cssNode, childNodes[index]);\n\t\t} else {\n\t\t\tstyleElement.appendChild(cssNode);\n\t\t}\n\t}\n}\n\nfunction applyToTag(styleElement, obj) {\n\tvar css = obj.css;\n\tvar media = obj.media;\n\n\tif(media) {\n\t\tstyleElement.setAttribute(\"media\", media)\n\t}\n\n\tif(styleElement.styleSheet) {\n\t\tstyleElement.styleSheet.cssText = css;\n\t} else {\n\t\twhile(styleElement.firstChild) {\n\t\t\tstyleElement.removeChild(styleElement.firstChild);\n\t\t}\n\t\tstyleElement.appendChild(document.createTextNode(css));\n\t}\n}\n\nfunction updateLink(linkElement, obj) {\n\tvar css = obj.css;\n\tvar sourceMap = obj.sourceMap;\n\n\tif(sourceMap) {\n\t\t// http://stackoverflow.com/a/26603875\n\t\tcss += \"\\n/*# sourceMappingURL=data:application/json;base64,\" + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + \" */\";\n\t}\n\n\tvar blob = new Blob([css], { type: \"text/css\" });\n\n\tvar oldSrc = linkElement.href;\n\n\tlinkElement.href = URL.createObjectURL(blob);\n\n\tif(oldSrc)\n\t\tURL.revokeObjectURL(oldSrc);\n}\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/style-loader/addStyles.js\n ** module id = 4\n ** module chunks = 0\n **/", "/* global Snap */\n/*\n\tClass: Bar\n\n\tOpts:\n\t\tgt: Gantt object\n\t\ttask: task object\n*/\n\nexport default function Bar(gt, task) {\n\n\tconst self = {};\n\n\tfunction init() {\n\t\tset_defaults();\n\t\tprepare();\n\t\tdraw();\n\t\tbind();\n\t}\n\n\tfunction set_defaults() {\n\t\tself.action_completed = false;\n\t\tself.task = task;\n\t}\n\n\tfunction prepare() {\n\t\tprepare_values();\n\t\tprepare_plugins();\n\t}\n\n\tfunction prepare_values() {\n\t\tself.invalid = self.task.invalid;\n\t\tself.height = gt.config.bar.height;\n\t\tself.x = compute_x();\n\t\tself.y = compute_y();\n\t\tself.corner_radius = gt.config.bar.corner_radius;\n\t\tself.duration = (self.task._end.diff(self.task._start, 'hours') + 24) / gt.config.step;\n\t\tself.width = gt.config.column_width * self.duration;\n\t\tself.progress_width = gt.config.column_width * self.duration * (self.task.progress / 100) || 0;\n\t\tself.group = gt.canvas.group().addClass('bar-wrapper').addClass(self.task.custom_class || '');\n\t\tself.bar_group = gt.canvas.group().addClass('bar-group').appendTo(self.group);\n\t\tself.handle_group = gt.canvas.group().addClass('handle-group').appendTo(self.group);\n\t}\n\n\tfunction prepare_plugins() {\n\t\tSnap.plugin(function (Snap, Element, Paper, global, Fragment) {\n\t\t\tElement.prototype.getX = function () {\n\t\t\t\treturn +this.attr('x');\n\t\t\t};\n\t\t\tElement.prototype.getY = function () {\n\t\t\t\treturn +this.attr('y');\n\t\t\t};\n\t\t\tElement.prototype.getWidth = function () {\n\t\t\t\treturn +this.attr('width');\n\t\t\t};\n\t\t\tElement.prototype.getHeight = function () {\n\t\t\t\treturn +this.attr('height');\n\t\t\t};\n\t\t\tElement.prototype.getEndX = function () {\n\t\t\t\treturn this.getX() + this.getWidth();\n\t\t\t};\n\t\t});\n\t}\n\n\tfunction draw() {\n\t\tdraw_bar();\n\t\tdraw_progress_bar();\n\t\tdraw_label();\n\t\tdraw_resize_handles();\n\t}\n\n\tfunction draw_bar() {\n\t\tself.$bar = gt.canvas.rect(self.x, self.y,\n\t\t\tself.width, self.height,\n\t\t\tself.corner_radius, self.corner_radius)\n\t\t\t.addClass('bar')\n\t\t\t.appendTo(self.bar_group);\n\t\tif (self.invalid) {\n\t\t\tself.$bar.addClass('bar-invalid');\n\t\t}\n\t}\n\n\tfunction draw_progress_bar() {\n\t\tif (self.invalid) return;\n\t\tself.$bar_progress = gt.canvas.rect(self.x, self.y,\n\t\t\tself.progress_width, self.height,\n\t\t\tself.corner_radius, self.corner_radius)\n\t\t\t.addClass('bar-progress')\n\t\t\t.appendTo(self.bar_group);\n\t}\n\n\tfunction draw_label() {\n\t\tgt.canvas.text(self.x + self.width / 2,\n\t\t\tself.y + self.height / 2,\n\t\t\tself.task.name)\n\t\t\t.addClass('bar-label')\n\t\t\t.appendTo(self.bar_group);\n\t\tupdate_label_position();\n\t}\n\n\tfunction draw_resize_handles() {\n\t\tif (self.invalid) return;\n\n\t\tconst bar = self.$bar,\n\t\t\thandle_width = 8;\n\n\t\tgt.canvas.rect(bar.getX() + bar.getWidth() - 9, bar.getY() + 1,\n\t\t\thandle_width, self.height - 2, self.corner_radius, self.corner_radius)\n\t\t\t.addClass('handle right')\n\t\t\t.appendTo(self.handle_group);\n\t\tgt.canvas.rect(bar.getX() + 1, bar.getY() + 1,\n\t\t\thandle_width, self.height - 2, self.corner_radius, self.corner_radius)\n\t\t\t.addClass('handle left')\n\t\t\t.appendTo(self.handle_group);\n\n\t\tif (self.task.progress && self.task.progress < 100) {\n\t\t\tgt.canvas.polygon(get_progress_polygon_points())\n\t\t\t\t.addClass('handle progress')\n\t\t\t\t.appendTo(self.handle_group);\n\t\t}\n\t}\n\n\tfunction get_progress_polygon_points() {\n\t\tconst bar_progress = self.$bar_progress;\n\t\treturn [\n\t\t\tbar_progress.getEndX() - 5, bar_progress.getY() + bar_progress.getHeight(),\n\t\t\tbar_progress.getEndX() + 5, bar_progress.getY() + bar_progress.getHeight(),\n\t\t\tbar_progress.getEndX(), bar_progress.getY() + bar_progress.getHeight() - 8.66\n\t\t];\n\t}\n\n\tfunction bind() {\n\t\tif (self.invalid) return;\n\t\tsetup_click_event();\n\t\tshow_details();\n\t\tbind_resize();\n\t\tbind_drag();\n\t\tbind_resize_progress();\n\t}\n\n\tfunction show_details() {\n\t\tconst popover_group = gt.element_groups.details;\n\t\tself.details_box = popover_group\n\t\t\t.select(`.details-wrapper[data-task='${self.task.id}']`);\n\n\t\tif (!self.details_box) {\n\t\t\tself.details_box = gt.canvas.group()\n\t\t\t\t.addClass('details-wrapper hide')\n\t\t\t\t.attr('data-task', self.task.id)\n\t\t\t\t.appendTo(popover_group);\n\n\t\t\trender_details();\n\n\t\t\tconst f = gt.canvas.filter(\n\t\t\t\tSnap.filter.shadow(0, 1, 1, '#666', 0.6));\n\t\t\tself.details_box.attr({\n\t\t\t\tfilter: f\n\t\t\t});\n\t\t}\n\n\t\tself.group.click((e) => {\n\t\t\tif (self.action_completed) {\n\t\t\t\t// just finished a move action, wait for a few seconds\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tpopover_group.selectAll('.details-wrapper')\n\t\t\t\t.forEach(el => el.addClass('hide'));\n\t\t\tself.details_box.removeClass('hide');\n\t\t});\n\t}\n\n\tfunction render_details() {\n\t\tconst {x, y} = get_details_position();\n\t\tself.details_box.transform(`t${x},${y}`);\n\t\tself.details_box.clear();\n\n\t\tconst html = get_details_html();\n\t\tconst foreign_object =\n\t\t\tSnap.parse(`<foreignObject width=\"5000\" height=\"2000\">\n\t\t\t\t<body xmlns=\"http://www.w3.org/1999/xhtml\">\n\t\t\t\t\t${html}\n\t\t\t\t</body>\n\t\t\t\t</foreignObject>`);\n\t\tself.details_box.append(foreign_object);\n\t}\n\n\tfunction get_details_html() {\n\n\t\t// custom html in config\n\t\tif(gt.config.custom_popup_html) {\n\t\t\tconst html = gt.config.custom_popup_html;\n\t\t\tif(typeof html === 'string') {\n\t\t\t\treturn html;\n\t\t\t}\n\t\t\tif(isFunction(html)) {\n\t\t\t\treturn html(task);\n\t\t\t}\n\t\t}\n\n\t\tconst start_date = self.task._start.format('MMM D');\n\t\tconst end_date = self.task._end.format('MMM D');\n\t\tconst heading = `${self.task.name}: ${start_date} - ${end_date}`;\n\n\t\tconst line_1 = `Duration: ${self.duration} days`;\n\t\tconst line_2 = self.task.progress ? `Progress: ${self.task.progress}` : null;\n\n\t\tconst html = `\n\t\t\t<div class=\"details-container\">\n\t\t\t\t<h5>${heading}</h5>\n\t\t\t\t<p>${line_1}</p>\n\t\t\t\t${\n\t\t\t\t\tline_2 ? `<p>${line_2}</p>` : ''\n\t\t\t\t}\n\t\t\t</div>\n\t\t`;\n\t\treturn html;\n\t}\n\n\tfunction get_details_position() {\n\t\treturn {\n\t\t\tx: self.$bar.getEndX() + 2,\n\t\t\ty: self.$bar.getY() - 10\n\t\t};\n\t}\n\n\tfunction bind_resize() {\n\t\tconst { left, right } = get_handles();\n\n\t\tleft.drag(onmove_left, onstart, onstop_left);\n\t\tright.drag(onmove_right, onstart, onstop_right);\n\n\t\tfunction onmove_right(dx, dy) {\n\t\t\tonmove_handle_right(dx, dy);\n\t\t}\n\t\tfunction onstop_right() {\n\t\t\tonstop_handle_right();\n\t\t}\n\n\t\tfunction onmove_left(dx, dy) {\n\t\t\tonmove_handle_left(dx, dy);\n\t\t}\n\t\tfunction onstop_left() {\n\t\t\tonstop_handle_left();\n\t\t}\n\t}\n\n\tfunction get_handles() {\n\t\treturn {\n\t\t\tleft: self.handle_group.select('.handle.left'),\n\t\t\tright: self.handle_group.select('.handle.right')\n\t\t};\n\t}\n\n\tfunction bind_drag() {\n\t\tself.bar_group.drag(onmove, onstart, onstop);\n\t}\n\n\tfunction bind_resize_progress() {\n\t\tconst bar = self.$bar,\n\t\t\tbar_progress = self.$bar_progress,\n\t\t\thandle = self.group.select('.handle.progress');\n\t\thandle && handle.drag(on_move, on_start, on_stop);\n\n\t\tfunction on_move(dx, dy) {\n\t\t\tif (dx > bar_progress.max_dx) {\n\t\t\t\tdx = bar_progress.max_dx;\n\t\t\t}\n\t\t\tif (dx < bar_progress.min_dx) {\n\t\t\t\tdx = bar_progress.min_dx;\n\t\t\t}\n\n\t\t\tbar_progress.attr('width', bar_progress.owidth + dx);\n\t\t\thandle.attr('points', get_progress_polygon_points());\n\t\t\tbar_progress.finaldx = dx;\n\t\t}\n\t\tfunction on_stop() {\n\t\t\tif (!bar_progress.finaldx) return;\n\t\t\tprogress_changed();\n\t\t\tset_action_completed();\n\t\t}\n\t\tfunction on_start() {\n\t\t\tbar_progress.finaldx = 0;\n\t\t\tbar_progress.owidth = bar_progress.getWidth();\n\t\t\tbar_progress.min_dx = -bar_progress.getWidth();\n\t\t\tbar_progress.max_dx = bar.getWidth() - bar_progress.getWidth();\n\t\t}\n\t}\n\n\tfunction onstart() {\n\t\tconst bar = self.$bar;\n\t\tbar.ox = bar.getX();\n\t\tbar.oy = bar.getY();\n\t\tbar.owidth = bar.getWidth();\n\t\tbar.finaldx = 0;\n\t\trun_method_for_dependencies('onstart');\n\t}\n\tself.onstart = onstart;\n\n\tfunction onmove(dx, dy) {\n\t\tconst bar = self.$bar;\n\t\tbar.finaldx = get_snap_position(dx);\n\t\tupdate_bar_position({x: bar.ox + bar.finaldx});\n\t\trun_method_for_dependencies('onmove', [dx, dy]);\n\t}\n\tself.onmove = onmove;\n\n\tfunction onstop() {\n\t\tconst bar = self.$bar;\n\t\tif (!bar.finaldx) return;\n\t\tdate_changed();\n\t\tset_action_completed();\n\t\trun_method_for_dependencies('onstop');\n\t}\n\tself.onstop = onstop;\n\n\tfunction onmove_handle_left(dx, dy) {\n\t\tconst bar = self.$bar;\n\t\tbar.finaldx = get_snap_position(dx);\n\t\tupdate_bar_position({\n\t\t\tx: bar.ox + bar.finaldx,\n\t\t\twidth: bar.owidth - bar.finaldx\n\t\t});\n\t\trun_method_for_dependencies('onmove', [dx, dy]);\n\t}\n\tself.onmove_handle_left = onmove_handle_left;\n\n\tfunction onstop_handle_left() {\n\t\tconst bar = self.$bar;\n\t\tif (bar.finaldx) date_changed();\n\t\tset_action_completed();\n\t\trun_method_for_dependencies('onstop');\n\t}\n\tself.onstop_handle_left = onstop_handle_left;\n\n\tfunction run_method_for_dependencies(fn, args) {\n\t\tconst dm = gt.dependency_map;\n\t\tif (dm[self.task.id]) {\n\t\t\tfor (let deptask of dm[self.task.id]) {\n\t\t\t\tconst dt = gt.get_bar(deptask);\n\t\t\t\tdt[fn].apply(dt, args);\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction onmove_handle_right(dx, dy) {\n\t\tconst bar = self.$bar;\n\t\tbar.finaldx = get_snap_position(dx);\n\t\tupdate_bar_position({width: bar.owidth + bar.finaldx});\n\t}\n\n\tfunction onstop_handle_right() {\n\t\tconst bar = self.$bar;\n\t\tif (bar.finaldx) date_changed();\n\t\tset_action_completed();\n\t}\n\n\tfunction update_bar_position({x = null, width = null}) {\n\t\tconst bar = self.$bar;\n\t\tif (x) {\n\t\t\t// get all x values of parent task\n\t\t\tconst xs = task.dependencies.map(dep => {\n\t\t\t\treturn gt.get_bar(dep).$bar.getX();\n\t\t\t});\n\t\t\t// child task must not go before parent\n\t\t\tconst valid_x = xs.reduce((prev, curr) => {\n\t\t\t\treturn x >= curr;\n\t\t\t}, x);\n\t\t\tif(!valid_x) {\n\t\t\t\twidth = null;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tupdate_attr(bar, 'x', x);\n\t\t}\n\t\tif (width && width >= gt.config.column_width) {\n\t\t\tupdate_attr(bar, 'width', width);\n\t\t}\n\t\tupdate_label_position();\n\t\tupdate_handle_position();\n\t\tupdate_progressbar_position();\n\t\tupdate_arrow_position();\n\t\tupdate_details_position();\n\t}\n\n\tfunction setup_click_event() {\n\t\tself.group.click(function () {\n\t\t\tif (self.action_completed) {\n\t\t\t\t// just finished a move action, wait for a few seconds\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (self.group.hasClass('active')) {\n\t\t\t\tgt.trigger_event('click', [self.task]);\n\t\t\t}\n\t\t\tgt.unselect_all();\n\t\t\tself.group.toggleClass('active');\n\t\t});\n\t}\n\n\tfunction date_changed() {\n\t\tconst { new_start_date, new_end_date } = compute_start_end_date();\n\t\tself.task._start = new_start_date;\n\t\tself.task._end = new_end_date;\n\t\trender_details();\n\t\tgt.trigger_event('date_change',\n\t\t\t[self.task, new_start_date, new_end_date]);\n\t}\n\n\tfunction progress_changed() {\n\t\tconst new_progress = compute_progress();\n\t\tself.task.progress = new_progress;\n\t\trender_details();\n\t\tgt.trigger_event('progress_change',\n\t\t\t[self.task, new_progress]);\n\t}\n\n\tfunction set_action_completed() {\n\t\tself.action_completed = true;\n\t\tsetTimeout(() => self.action_completed = false, 2000);\n\t}\n\n\tfunction compute_start_end_date() {\n\t\tconst bar = self.$bar;\n\t\tconst x_in_units = bar.getX() / gt.config.column_width;\n\t\tconst new_start_date = gt.gantt_start.clone().add(x_in_units * gt.config.step, 'hours');\n\t\tconst width_in_units = bar.getWidth() / gt.config.column_width;\n\t\tconst new_end_date = new_start_date.clone().add(width_in_units * gt.config.step, 'hours');\n\t\t// lets say duration is 2 days\n\t\t// start_date = May 24 00:00:00\n\t\t// end_date = May 24 + 2 days = May 26 (incorrect)\n\t\t// so subtract 1 second so that\n\t\t// end_date = May 25 23:59:59\n\t\tnew_end_date.add('-1', 'seconds');\n\t\treturn { new_start_date, new_end_date };\n\t}\n\n\tfunction compute_progress() {\n\t\tconst progress = self.$bar_progress.getWidth() / self.$bar.getWidth() * 100;\n\t\treturn parseInt(progress, 10);\n\t}\n\n\tfunction compute_x() {\n\t\tlet x = self.task._start.diff(gt.gantt_start, 'hours') /\n\t\t\tgt.config.step * gt.config.column_width;\n\n\t\tif (gt.view_is('Month')) {\n\t\t\tx = self.task._start.diff(gt.gantt_start, 'days') *\n\t\t\t\tgt.config.column_width / 30;\n\t\t}\n\t\treturn x;\n\t}\n\n\tfunction compute_y() {\n\t\treturn gt.config.header_height + gt.config.padding +\n\t\t\tself.task._index * (self.height + gt.config.padding);\n\t}\n\n\tfunction get_snap_position(dx) {\n\t\tlet odx = dx, rem, position;\n\n\t\tif (gt.view_is('Week')) {\n\t\t\trem = dx % (gt.config.column_width / 7);\n\t\t\tposition = odx - rem +\n\t\t\t\t((rem < gt.config.column_width / 14) ? 0 : gt.config.column_width / 7);\n\t\t} else if (gt.view_is('Month')) {\n\t\t\trem = dx % (gt.config.column_width / 30);\n\t\t\tposition = odx - rem +\n\t\t\t\t((rem < gt.config.column_width / 60) ? 0 : gt.config.column_width / 30);\n\t\t} else {\n\t\t\trem = dx % gt.config.column_width;\n\t\t\tposition = odx - rem +\n\t\t\t\t((rem < gt.config.column_width / 2) ? 0 : gt.config.column_width);\n\t\t}\n\t\treturn position;\n\t}\n\n\tfunction update_attr(element, attr, value) {\n\t\tvalue = +value;\n\t\tif (!isNaN(value)) {\n\t\t\telement.attr(attr, value);\n\t\t}\n\t\treturn element;\n\t}\n\n\tfunction update_progressbar_position() {\n\t\tself.$bar_progress.attr('x', self.$bar.getX());\n\t\tself.$bar_progress.attr('width', self.$bar.getWidth() * (self.task.progress / 100));\n\t}\n\n\tfunction update_label_position() {\n\t\tconst bar = self.$bar,\n\t\t\tlabel = self.group.select('.bar-label');\n\t\tif (label.getBBox().width > bar.getWidth()) {\n\t\t\tlabel.addClass('big').attr('x', bar.getX() + bar.getWidth() + 5);\n\t\t} else {\n\t\t\tlabel.removeClass('big').attr('x', bar.getX() + bar.getWidth() / 2);\n\t\t}\n\t}\n\n\tfunction update_handle_position() {\n\t\tconst bar = self.$bar;\n\t\tself.handle_group.select('.handle.left').attr({\n\t\t\t'x': bar.getX() + 1\n\t\t});\n\t\tself.handle_group.select('.handle.right').attr({\n\t\t\t'x': bar.getEndX() - 9\n\t\t});\n\t\tconst handle = self.group.select('.handle.progress');\n\t\thandle && handle.attr('points', get_progress_polygon_points());\n\t}\n\n\tfunction update_arrow_position() {\n\t\tfor (let arrow of self.arrows) {\n\t\t\tarrow.update();\n\t\t}\n\t}\n\n\tfunction update_details_position() {\n\t\tconst {x, y} = get_details_position();\n\t\tself.details_box && self.details_box.transform(`t${x},${y}`);\n\t}\n\n\tfunction isFunction(functionToCheck) {\n\t\tvar getType = {};\n\t\treturn functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n\t}\n\n\tinit();\n\n\treturn self;\n}\n\n\n\n/** WEBPACK FOOTER **\n ** ./~/eslint-loader!./src/Bar.js\n **/", "/* global Snap */\n/*\n\tClass: Arrow\n\tfrom_task ---> to_task\n\n\tOpts:\n\t\tgantt (Gantt object)\n\t\tfrom_task (Bar object)\n\t\tto_task (Bar object)\n*/\n\nexport default function Arrow(gt, from_task, to_task) {\n\n\tconst self = {};\n\n\tfunction init() {\n\t\tself.from_task = from_task;\n\t\tself.to_task = to_task;\n\t\tprepare();\n\t\tdraw();\n\t}\n\n\tfunction prepare() {\n\n\t\tself.start_x = from_task.$bar.getX() + from_task.$bar.getWidth() / 2;\n\n\t\tconst condition = () =>\n\t\t\tto_task.$bar.getX() < self.start_x + gt.config.padding &&\n\t\t\t\tself.start_x > from_task.$bar.getX() + gt.config.padding;\n\n\t\twhile(condition()) {\n\t\t\tself.start_x -= 10;\n\t\t}\n\n\t\tself.start_y = gt.config.header_height + gt.config.bar.height +\n\t\t\t(gt.config.padding + gt.config.bar.height) * from_task.task._index +\n\t\t\tgt.config.padding;\n\n\t\tself.end_x = to_task.$bar.getX() - gt.config.padding / 2;\n\t\tself.end_y = gt.config.header_height + gt.config.bar.height / 2 +\n\t\t\t(gt.config.padding + gt.config.bar.height) * to_task.task._index +\n\t\t\tgt.config.padding;\n\n\t\tconst from_is_below_to = (from_task.task._index > to_task.task._index);\n\t\tself.curve = gt.config.arrow.curve;\n\t\tself.clockwise = from_is_below_to ? 1 : 0;\n\t\tself.curve_y = from_is_below_to ? -self.curve : self.curve;\n\t\tself.offset = from_is_below_to ?\n\t\t\tself.end_y + gt.config.arrow.curve :\n\t\t\tself.end_y - gt.config.arrow.curve;\n\n\t\tself.path =\n\t\t\tSnap.format('M {start_x} {start_y} V {offset} ' +\n\t\t\t\t'a {curve} {curve} 0 0 {clockwise} {curve} {curve_y} ' +\n\t\t\t\t'L {end_x} {end_y} m -5 -5 l 5 5 l -5 5',\n\t\t\t\t{\n\t\t\t\t\tstart_x: self.start_x,\n\t\t\t\t\tstart_y: self.start_y,\n\t\t\t\t\tend_x: self.end_x,\n\t\t\t\t\tend_y: self.end_y,\n\t\t\t\t\toffset: self.offset,\n\t\t\t\t\tcurve: self.curve,\n\t\t\t\t\tclockwise: self.clockwise,\n\t\t\t\t\tcurve_y: self.curve_y\n\t\t\t\t});\n\n\t\tif(to_task.$bar.getX() < from_task.$bar.getX() + gt.config.padding) {\n\t\t\tself.path =\n\t\t\t\tSnap.format('M {start_x} {start_y} v {down_1} ' +\n\t\t\t\t'a {curve} {curve} 0 0 1 -{curve} {curve} H {left} ' +\n\t\t\t\t'a {curve} {curve} 0 0 {clockwise} -{curve} {curve_y} V {down_2} ' +\n\t\t\t\t'a {curve} {curve} 0 0 {clockwise} {curve} {curve_y} ' +\n\t\t\t\t'L {end_x} {end_y} m -5 -5 l 5 5 l -5 5',\n\t\t\t\t\t{\n\t\t\t\t\t\tstart_x: self.start_x,\n\t\t\t\t\t\tstart_y: self.start_y,\n\t\t\t\t\t\tend_x: self.end_x,\n\t\t\t\t\t\tend_y: self.end_y,\n\t\t\t\t\t\tdown_1: gt.config.padding / 2 - self.curve,\n\t\t\t\t\t\tdown_2: to_task.$bar.getY() + to_task.$bar.getHeight() / 2 - self.curve_y,\n\t\t\t\t\t\tleft: to_task.$bar.getX() - gt.config.padding,\n\t\t\t\t\t\toffset: self.offset,\n\t\t\t\t\t\tcurve: self.curve,\n\t\t\t\t\t\tclockwise: self.clockwise,\n\t\t\t\t\t\tcurve_y: self.curve_y\n\t\t\t\t\t});\n\t\t}\n\t}\n\n\tfunction draw() {\n\t\tself.element = gt.canvas.path(self.path)\n\t\t\t.attr('data-from', self.from_task.task.id)\n\t\t\t.attr('data-to', self.to_task.task.id);\n\t}\n\n\tfunction update() { // eslint-disable-line\n\t\tprepare();\n\t\tself.element.attr('d', self.path);\n\t}\n\tself.update = update;\n\n\tinit();\n\n\treturn self;\n}\n\n\n\n/** WEBPACK FOOTER **\n ** ./~/eslint-loader!./src/Arrow.js\n **/", "(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(global.deepmerge = factory());\n}(this, (function () { 'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, optionsArgument) {\n\tvar clone = !optionsArgument || optionsArgument.clone !== false;\n\n\treturn (clone && isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, optionsArgument)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, optionsArgument) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, optionsArgument)\n\t})\n}\n\nfunction mergeObject(target, source, optionsArgument) {\n\tvar destination = {};\n\tif (isMergeableObject(target)) {\n\t\tObject.keys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], optionsArgument);\n\t\t});\n\t}\n\tObject.keys(source).forEach(function(key) {\n\t\tif (!isMergeableObject(source[key]) || !target[key]) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], optionsArgument);\n\t\t} else {\n\t\t\tdestination[key] = deepmerge(target[key], source[key], optionsArgument);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, optionsArgument) {\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar options = optionsArgument || { arrayMerge: defaultArrayMerge };\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, optionsArgument)\n\t} else if (sourceIsArray) {\n\t\tvar arrayMerge = options.arrayMerge || defaultArrayMerge;\n\t\treturn arrayMerge(target, source, optionsArgument)\n\t} else {\n\t\treturn mergeObject(target, source, optionsArgument)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, optionsArgument) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, optionsArgument)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nreturn deepmerge_1;\n\n})));\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/deepmerge/dist/umd.js\n ** module id = 7\n ** module chunks = 0\n **/"], "sourceRoot": ""}
<<<<<<< HEAD
{"version":3,"sources":["webpack:///webpack/universalModuleDefinition","webpack:///frappe-gantt.min.js","webpack:///webpack/bootstrap cf9ffca37eeeec96b798","webpack:///./src/Gantt.js","webpack:///./src/gantt.scss?b27d","webpack:///./src/gantt.scss","webpack:///./~/css-loader/lib/css-base.js","webpack:///./~/style-loader/addStyles.js","webpack:///./src/Bar.js","webpack:///./src/Arrow.js","webpack:///./~/deepmerge/dist/umd.js"],"names":["root","factory","exports","module","define","amd","this","modules","__webpack_require__","moduleId","installedModules","id","loaded","call","m","c","p","_interopRequireDefault","obj","__esModule","default","Gantt","element","tasks","config","init","set_defaults","self","change_view_mode","unselect_all","view_is","get_bar","trigger_event","refresh","view_mode","merge","defaults","header_height","column_width","step","view_modes","bar","height","corner_radius","arrow","curve","padding","date_format","custom_popup_html","reset_variables","document","querySelector","SVGElement","HTMLElement","TypeError","_tasks","_bars","_arrows","element_groups","updated_tasks","mode","set_scale","prepare","render","prepare_tasks","prepare_dependencies","prepare_dates","prepare_canvas","map","task","i","_start","moment","start","_end","end","diff","_index","startOf","add","clone","invalid","dependencies","deps","split","d","trim","filter","generate_id","dependency_map","_iteratorNormalCompletion","_didIteratorError","_iteratorError","undefined","_step","_iterator","Symbol","iterator","next","done","t","value","_iteratorNormalCompletion2","_didIteratorError2","_iteratorError2","_step2","_iterator2","push","err","gantt_start","gantt_end","_iteratorNormalCompletion3","_didIteratorError3","_iteratorError3","_step3","_iterator3","set_gantt_dates","setup_dates","canvas","Snap","addClass","clear","setup_groups","make_grid","make_dates","make_bars","make_arrows","map_arrows_on_bars","set_width","set_scroll_position","bind_grid_click","subtract","endOf","dates","cur_date","groups","_iteratorNormalCompletion4","_didIteratorError4","_iteratorError4","_step4","_iterator4","group","attr","scale","cur_width","node","getBoundingClientRect","width","actual_width","select","parent_element","parentElement","scroll_pos","get_min_date","scrollLeft","reduce","acc","curr","isSameOrBefore","make_grid_background","make_grid_rows","make_grid_header","make_grid_ticks","make_grid_highlights","grid_width","length","grid_height","rect","appendTo","grid","header_width","rows","lines","row_width","row_height","row_y","_iteratorNormalCompletion5","_didIteratorError5","_iteratorError5","_step5","_iterator5","line","tick_x","tick_y","tick_height","_iteratorNormalCompletion6","_didIteratorError6","_iteratorError6","_step6","_iterator6","date","tick_class","day","month","path","format","x","y","daysInMonth","_iteratorNormalCompletion7","_didIteratorError7","_iteratorError7","_step7","_iterator7","get_dates_to_draw","text","lower_x","lower_y","lower_text","upper_text","$upper_text","upper_x","upper_y","getBBox","x2","remove","last_date","get_date_info","date_text","Quarter Day_lower","Half Day_lower","Day_lower","Week_lower","Month_lower","Quarter Day_upper","Half Day_upper","Day_upper","Week_upper","Month_upper","year","base_pos","x_pos","_iteratorNormalCompletion8","_didIteratorError8","_iteratorError8","_step8","_loop","arrows","dep","dependency","get_task","_Arrow2","arr","concat","_iterator8","_Bar2","_iteratorNormalCompletion9","_didIteratorError9","_iteratorError9","_step9","_loop2","from_task","to_task","_iterator9","click","details","selectAll","forEach","el","removeClass","modes","Array","isArray","_iteratorNormalCompletion10","_didIteratorError10","_iteratorError10","_step10","_iterator10","find","name","Math","random","toString","slice","event","args","apply","Object","defineProperty","_Bar","_Arrow","content","locals","version","sources","names","mappings","file","sourcesContent","sourceRoot","list","result","item","join","mediaQuery","alreadyImportedModules","addStylesToDom","styles","options","domStyle","stylesInDom","refs","j","parts","addStyle","listToStyles","newStyles","css","media","sourceMap","part","insertStyleElement","styleElement","head","getHeadElement","lastStyleElementInsertedAtTop","styleElementsInsertedAtTop","insertAt","nextSibling","insertBefore","appendChild","firstChild","Error","removeStyleElement","parentNode","removeChild","idx","indexOf","splice","createStyleElement","createElement","type","createLinkElement","linkElement","rel","update","singleton","styleIndex","singletonCounter","singletonElement","applyToSingletonTag","bind","URL","createObjectURL","revokeObjectURL","Blob","btoa","updateLink","href","applyToTag","newObj","index","styleSheet","cssText","replaceText","cssNode","createTextNode","childNodes","setAttribute","unescape","encodeURIComponent","JSON","stringify","blob","oldSrc","memoize","fn","memo","arguments","isOldIE","test","navigator","userAgent","toLowerCase","getElementsByTagName","newList","mayRemove","textStore","replacement","Boolean","Bar","gt","draw","action_completed","prepare_values","prepare_plugins","compute_x","compute_y","duration","progress_width","progress","custom_class","bar_group","handle_group","plugin","Element","Paper","global","Fragment","prototype","getX","getY","getWidth","getHeight","getEndX","draw_bar","draw_progress_bar","draw_label","draw_resize_handles","$bar","$bar_progress","update_label_position","handle_width","polygon","get_progress_polygon_points","bar_progress","setup_click_event","show_details","bind_resize","bind_drag","bind_resize_progress","popover_group","details_box","render_details","f","shadow","e","_get_details_position","get_details_position","transform","html","get_details_html","foreign_object","parse","append","isFunction","start_date","end_date","heading","line_1","line_2","onmove_right","dx","dy","onmove_handle_right","onstop_right","onstop_handle_right","onmove_left","onmove_handle_left","onstop_left","onstop_handle_left","_get_handles","get_handles","left","right","drag","onstart","onmove","onstop","on_move","max_dx","min_dx","owidth","handle","finaldx","on_stop","progress_changed","set_action_completed","on_start","ox","oy","run_method_for_dependencies","get_snap_position","update_bar_position","date_changed","dm","deptask","dt","_ref","_ref$x","_ref$width","xs","valid_x","prev","update_attr","update_handle_position","update_progressbar_position","update_arrow_position","update_details_position","hasClass","toggleClass","_compute_start_end_da","compute_start_end_date","new_start_date","new_end_date","new_progress","compute_progress","setTimeout","x_in_units","width_in_units","parseInt","odx","rem","position","isNaN","label","_get_details_position2","functionToCheck","getType","Arrow","start_x","condition","start_y","end_x","end_y","from_is_below_to","clockwise","curve_y","offset","down_1","down_2","isNonNullObject","isSpecial","stringValue","isReactElement","$$typeof","REACT_ELEMENT_TYPE","emptyTarget","val","cloneUnlessOtherwiseSpecified","optionsArgument","isMergeableObject","deepmerge","defaultArrayMerge","target","source","mergeObject","destination","keys","key","sourceIsArray","targetIsArray","arrayMerge","sourceAndTargetTypesMatch","canUseSymbol","all","array","deepmerge_1"],"mappings":"CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,IACA,kBAAAG,gBAAAC,IACAD,OAAA,WAAAH,GACA,gBAAAC,SACAA,QAAA,MAAAD,IAEAD,EAAA,MAAAC,KACCK,KAAA,WACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAP,OAGA,IAAAC,GAAAO,EAAAD,IACAP,WACAS,GAAAF,EACAG,QAAA,EAUA,OANAL,GAAAE,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAS,QAAA,EAGAT,EAAAD,QAvBA,GAAAQ,KAqCA,OATAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAQ,EAAA,GAGAR,EAAA,KDgBM,SAASL,EAAQD,EAASM,GAE/B,YAiBA,SAASS,GAAuBC,GAAO,MAAOA,IAAOA,EAAIC,WAAaD,GAAQE,UAASF,GE5DzE,QAASG,GAAMC,EAASC,EAAOC,GAI7C,QAASC,KACRC,IAGAC,EAAKC,iBAAmBA,EACxBD,EAAKE,aAAeA,EACpBF,EAAKG,QAAUA,EACfH,EAAKI,QAAUA,EACfJ,EAAKK,cAAgBA,EACrBL,EAAKM,QAAUA,EAGfL,EAAiBD,EAAKH,OAAOU,WAG9B,QAASR,KAER,GAAMS,GAAQ3B,EAAQ,GAEhB4B,GACLC,cAAe,GACfC,aAAc,GACdC,KAAM,GACNC,YACC,cACA,WACA,MACA,OACA,SAEDC,KACCC,OAAQ,GACRC,cAAe,GAEhBC,OACCC,MAAO,GAERC,QAAS,GACTZ,UAAW,MACXa,YAAa,aACbC,kBAAmB,KAEpBrB,GAAKH,OAASW,EAAMC,EAAUZ,GAE9ByB,EAAgB1B,GAGjB,QAAS0B,GAAgB1B,GACxB,GAAsB,gBAAZD,GACTK,EAAKL,QAAU4B,SAASC,cAAc7B,OAChC,IAAIA,YAAmB8B,YAC7BzB,EAAKL,QAAUA,MACT,MAAIA,YAAmB+B,cAG7B,KAAM,IAAIC,WAAU,6HAFpB3B,GAAKL,QAAUA,EAAQ6B,cAAc,OAMtCxB,EAAK4B,OAAShC,EAEdI,EAAK6B,SACL7B,EAAK8B,WACL9B,EAAK+B,kBAGN,QAASzB,GAAQ0B,GAChBV,EAAgBU,GAChB/B,EAAiBD,EAAKH,OAAOU,WAG9B,QAASN,GAAiBgC,GACzBC,EAAUD,GACVE,IACAC,IAEA/B,EAAc,eAAgB4B,IAG/B,QAASE,KACRE,IACAC,IACAC,IACAC,IAGD,QAASH,KAGRrC,EAAKJ,MAAQI,EAAK4B,OAAOa,IAAI,SAACC,EAAMC,GAgCnC,GA7BAD,EAAKE,OAASC,OAAOH,EAAKI,MAAO9C,EAAKH,OAAOuB,aAC7CsB,EAAKK,KAAOF,OAAOH,EAAKM,IAAKhD,EAAKH,OAAOuB,aAGtCsB,EAAKK,KAAKE,KAAKP,EAAKE,OAAQ,SAAW,KACzCF,EAAKM,IAAM,MAIZN,EAAKQ,OAASP,EAGVD,EAAKI,OAAUJ,EAAKM,MACvBN,EAAKE,OAASC,SAASM,QAAQ,OAC/BT,EAAKK,KAAOF,SAASM,QAAQ,OAAOC,IAAI,EAAG,UAExCV,EAAKI,OAASJ,EAAKM,MACtBN,EAAKE,OAASF,EAAKK,KAAKM,QAAQD,OAAQ,SAEtCV,EAAKI,QAAUJ,EAAKM,MACtBN,EAAKK,KAAOL,EAAKE,OAAOS,QAAQD,IAAI,EAAG,SAIpCV,EAAKI,OAAUJ,EAAKM,MACvBN,EAAKY,SAAU,GAIgB,gBAAtBZ,GAAKa,eAA8Bb,EAAKa,aAAc,CAC/D,GAAIC,KACDd,GAAKa,eACPC,EAAOd,EAAKa,aACVE,MAAM,KACNhB,IAAI,SAAAiB,GAAA,MAAKA,GAAEC,SACXC,OAAO,SAACF,GAAD,MAAOA,MAEjBhB,EAAKa,aAAeC,EAQrB,MAJId,GAAK1D,KACR0D,EAAK1D,GAAK6E,EAAYnB,IAGhBA,IAIT,QAASJ,KAERtC,EAAK8D,iBAF0B,IAAAC,IAAA,EAAAC,GAAA,EAAAC,EAAAC,MAAA,KAG/B,OAAAC,GAAAC,EAAapE,EAAKJ,MAAlByE,OAAAC,cAAAP,GAAAI,EAAAC,EAAAG,QAAAC,MAAAT,GAAA,EAAyB,IAAjBU,GAAiBN,EAAAO,MAAAC,GAAA,EAAAC,GAAA,EAAAC,EAAAX,MAAA,KACxB,OAAAY,GAAAC,EAAaN,EAAElB,aAAfc,OAAAC,cAAAK,GAAAG,EAAAC,EAAAR,QAAAC,MAAAG,GAAA,EAA6B,IAArBjB,GAAqBoB,EAAAJ,KAC5B1E,GAAK8D,eAAeJ,GAAK1D,EAAK8D,eAAeJ,OAC7C1D,EAAK8D,eAAeJ,GAAGsB,KAAKP,EAAEzF,KAHP,MAAAiG,GAAAL,GAAA,EAAAC,EAAAI,EAAA,aAAAN,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,MAHM,MAAAI,GAAAjB,GAAA,EAAAC,EAAAgB,EAAA,aAAAlB,GAAAK,2BAAA,WAAAJ,EAAA,KAAAC,KAWhC,QAAS1B,KAERvC,EAAKkF,YAAclF,EAAKmF,UAAY,IAFZ,IAAAC,IAAA,EAAAC,GAAA,EAAAC,EAAApB,MAAA,KAGxB,OAAAqB,GAAAC,EAAgBxF,EAAKJ,MAArByE,OAAAC,cAAAc,GAAAG,EAAAC,EAAAjB,QAAAC,MAAAY,GAAA,EAA4B,IAApB1C,GAAoB6C,EAAAb,QAEvB1E,EAAKkF,aAAexC,EAAKE,OAAS5C,EAAKkF,eAC1ClF,EAAKkF,YAAcxC,EAAKE,UAErB5C,EAAKmF,WAAazC,EAAKK,KAAO/C,EAAKmF,aACtCnF,EAAKmF,UAAYzC,EAAKK,OATA,MAAAkC,GAAAI,GAAA,EAAAC,EAAAL,EAAA,aAAAG,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,IAYxBG,IACAC,IAGD,QAASlD,KACLxC,EAAK2F,SACR3F,EAAK2F,OAASC,KAAK5F,EAAKL,SAASkG,SAAS,UAG3C,QAASzD,KACR0D,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IAGD,QAAST,KACR9F,EAAK2F,OAAOG,QACZ9F,EAAK6B,SACL7B,EAAK8B,WAGN,QAAS2D,KAELtF,GAAS,cAAe,cAC1BH,EAAKkF,YAAclF,EAAKkF,YAAY7B,QAAQmD,SAAS,EAAG,OACxDxG,EAAKmF,UAAYnF,EAAKmF,UAAU9B,QAAQD,IAAI,EAAG,QACtCjD,EAAQ,UACjBH,EAAKkF,YAAclF,EAAKkF,YAAY7B,QAAQF,QAAQ,QACpDnD,EAAKmF,UAAYnF,EAAKmF,UAAU9B,QAAQoD,MAAM,SAASrD,IAAI,EAAG,UAE9DpD,EAAKkF,YAAclF,EAAKkF,YAAY7B,QAAQF,QAAQ,SAASqD,SAAS,EAAG,SACzExG,EAAKmF,UAAYnF,EAAKmF,UAAU9B,QAAQoD,MAAM,SAASrD,IAAI,EAAG,UAIhE,QAASsC,KAER1F,EAAK0G,QAGL,KAFA,GAAIC,GAAW,KAEI,OAAbA,GAAqBA,EAAW3G,EAAKmF,WAIzCwB,EAHGA,EAGQxG,EAAQ,SAClBwG,EAAStD,QAAQD,IAAI,EAAG,SACxBuD,EAAStD,QAAQD,IAAIpD,EAAKH,OAAOe,KAAM,SAJ7BZ,EAAKkF,YAAY7B,QAM7BrD,EAAK0G,MAAM1B,KAAK2B,GAIlB,QAASZ,KAER,GAAMa,IAAU,OAAQ,OAAQ,QAAS,WAAY,MAAO,WAFrCC,GAAA,EAAAC,GAAA,EAAAC,EAAA7C,MAAA,KAIvB,OAAA8C,GAAAC,EAAiBL,EAAjBvC,OAAAC,cAAAuC,GAAAG,EAAAC,EAAA1C,QAAAC,MAAAqC,GAAA,EAAyB,IAAjBK,GAAiBF,EAAAtC,KACxB1E,GAAK+B,eAAemF,GAASlH,EAAK2F,OAAOuB,QAAQC,MAAMnI,GAAMkI,KALvC,MAAAjC,GAAA6B,GAAA,EAAAC,EAAA9B,EAAA,aAAA4B,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,KASxB,QAAS7E,GAAUkF,GAClBpH,EAAKH,OAAOU,UAAY6G,EAEX,QAAVA,GACFpH,EAAKH,OAAOe,KAAO,GACnBZ,EAAKH,OAAOc,aAAe,IACR,aAAVyG,GACTpH,EAAKH,OAAOe,KAAO,GACnBZ,EAAKH,OAAOc,aAAe,IACR,gBAAVyG,GACTpH,EAAKH,OAAOe,KAAO,EACnBZ,EAAKH,OAAOc,aAAe,IACR,SAAVyG,GACTpH,EAAKH,OAAOe,KAAO,IACnBZ,EAAKH,OAAOc,aAAe,KACR,UAAVyG,IACTpH,EAAKH,OAAOe,KAAO,IACnBZ,EAAKH,OAAOc,aAAe,KAI7B,QAAS0F,KACR,GAAMgB,GAAYrH,EAAK2F,OAAO2B,KAAKC,wBAAwBC,MACrDC,EAAezH,EAAK2F,OAAO+B,OAAO,mBAAmBP,KAAK,QAC7DE,GAAYI,GACdzH,EAAK2F,OAAOwB,KAAK,QAASM,GAI5B,QAASnB,KACR,GAAMqB,GAAiB3H,EAAKL,QAAQiI,aAEpC,IAAID,EAAJ,CAEA,GAAME,GAAaC,IAAe7E,KAAKjD,EAAKkF,YAAa,SACxDlF,EAAKH,OAAOe,KAAOZ,EAAKH,OAAOc,aAAeX,EAAKH,OAAOc,YAC3DgH,GAAeI,WAAaF,GAG7B,QAASC,KACR,GAAMpF,GAAO1C,EAAKJ,MAAMoI,OAAO,SAACC,EAAKC,GACpC,MAAOA,GAAKtF,OAAOuF,eAAeF,EAAIrF,QAAUsF,EAAOD,GAExD,OAAOvF,GAAKE,OAGb,QAASoD,KACRoC,IACAC,IACAC,IACAC,IACAC,IAGD,QAASJ,KAER,GAAMK,GAAazI,EAAK0G,MAAMgC,OAAS1I,EAAKH,OAAOc,aAClDgI,EAAc3I,EAAKH,OAAOa,cAAgBV,EAAKH,OAAOsB,SACpDnB,EAAKH,OAAOiB,IAAIC,OAASf,EAAKH,OAAOsB,SAAWnB,EAAKJ,MAAM8I,MAE9D1I,GAAK2F,OAAOiD,KAAK,EAAG,EAAGH,EAAYE,GACjC9C,SAAS,mBACTgD,SAAS7I,EAAK+B,eAAe+G,MAE/B9I,EAAK2F,OAAOwB,MACXpG,OAAQ4H,EAAc3I,EAAKH,OAAOsB,QAAU,IAC5CqG,MAAO,SAIT,QAASc,KACR,GAAMS,GAAe/I,EAAK0G,MAAMgC,OAAS1I,EAAKH,OAAOc,aACpDD,EAAgBV,EAAKH,OAAOa,cAAgB,EAC7CV,GAAK2F,OAAOiD,KAAK,EAAG,EAAGG,EAAcrI,GACnCmF,SAAS,eACTgD,SAAS7I,EAAK+B,eAAe+G,MAGhC,QAAST,KAER,GAAMW,GAAOhJ,EAAK2F,OAAOuB,QAAQ2B,SAAS7I,EAAK+B,eAAe+G,MAC7DG,EAAQjJ,EAAK2F,OAAOuB,QAAQ2B,SAAS7I,EAAK+B,eAAe+G,MACzDI,EAAYlJ,EAAK0G,MAAMgC,OAAS1I,EAAKH,OAAOc,aAC5CwI,EAAanJ,EAAKH,OAAOiB,IAAIC,OAASf,EAAKH,OAAOsB,QAE/CiI,EAAQpJ,EAAKH,OAAOa,cAAgBV,EAAKH,OAAOsB,QAAU,EAPrCkI,GAAA,EAAAC,GAAA,EAAAC,EAAArF,MAAA,KASzB,OAAAsF,GAAAC,EAAgBzJ,EAAKJ,MAArByE,OAAAC,cAAA+E,GAAAG,EAAAC,EAAAlF,QAAAC,MAAA6E,GAAA,EAA4B,CAAAG,EAAA9E,KAC3B1E,GAAK2F,OAAOiD,KAAK,EAAGQ,EAAOF,EAAWC,GACpCtD,SAAS,YACTgD,SAASG,GAEXhJ,EAAK2F,OAAO+D,KAAK,EAAGN,EAAQD,EAAYD,EAAWE,EAAQD,GACzDtD,SAAS,YACTgD,SAASI,GAEXG,GAASpJ,EAAKH,OAAOiB,IAAIC,OAASf,EAAKH,OAAOsB,SAlBtB,MAAA8D,GAAAqE,GAAA,EAAAC,EAAAtE,EAAA,aAAAoE,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,KAsB1B,QAAShB,KACR,GAAIoB,GAAS,EACZC,EAAS5J,EAAKH,OAAOa,cAAgBV,EAAKH,OAAOsB,QAAU,EAC3D0I,GAAe7J,EAAKH,OAAOiB,IAAIC,OAASf,EAAKH,OAAOsB,SAAWnB,EAAKJ,MAAM8I,OAHjDoB,GAAA,EAAAC,GAAA,EAAAC,EAAA9F,MAAA,KAK1B,OAAA+F,GAAAC,EAAgBlK,EAAK0G,MAArBrC,OAAAC,cAAAwF,GAAAG,EAAAC,EAAA3F,QAAAC,MAAAsF,GAAA,EAA4B,IAApBK,GAAoBF,EAAAvF,MACvB0F,EAAa,MAEdjK,GAAQ,QAAyB,IAAfgK,EAAKE,QACzBD,GAAc,UAGZjK,EAAQ,SAAWgK,EAAKA,QAAU,GAAKA,EAAKA,OAAS,IACvDC,GAAc,UAGZjK,EAAQ,UAAYgK,EAAKG,QAAU,IAAM,IAC3CF,GAAc,UAGfpK,EAAK2F,OAAO4E,KAAK3E,KAAK4E,OAAO,wBAC5BC,EAAGd,EACHe,EAAGd,EACH7I,OAAQ8I,KAERhE,SAASuE,GACTvB,SAAS7I,EAAK+B,eAAe+G,MAG7Ba,GADExJ,EAAQ,SACAgK,EAAKQ,cAAgB3K,EAAKH,OAAOc,aAAe,GAEhDX,EAAKH,OAAOc,cA/BE,MAAAsE,GAAA8E,GAAA,EAAAC,EAAA/E,EAAA,aAAA6E,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,KAoC3B,QAASxB,KAGR,GAAGrI,EAAQ,OAAQ,CAClB,GAAMsK,GAAI5H,SAASM,QAAQ,OAAOF,KAAKjD,EAAKkF,YAAa,SACvDlF,EAAKH,OAAOe,KAAOZ,EAAKH,OAAOc,aAC3B+J,EAAI,EACJlD,EAAQxH,EAAKH,OAAOc,aACpBI,GAAUf,EAAKH,OAAOiB,IAAIC,OAASf,EAAKH,OAAOsB,SAAWnB,EAAKJ,MAAM8I,OAC1E1I,EAAKH,OAAOa,cAAgBV,EAAKH,OAAOsB,QAAU,CAEnDnB,GAAK2F,OAAOiD,KAAK6B,EAAGC,EAAGlD,EAAOzG,GAC5B8E,SAAS,mBACTgD,SAAS7I,EAAK+B,eAAe+G,OAIjC,QAAS7C,KAAa,GAAA2E,IAAA,EAAAC,GAAA,EAAAC,EAAA5G,MAAA,KAErB,OAAA6G,GAAAC,EAAgBC,IAAhB5G,OAAAC,cAAAsG,GAAAG,EAAAC,EAAAzG,QAAAC,MAAAoG,GAAA,EAAqC,IAA7BT,GAA6BY,EAAArG,KAKpC,IAJA1E,EAAK2F,OAAOuF,KAAKf,EAAKgB,QAAShB,EAAKiB,QAASjB,EAAKkB,YAChDxF,SAAS,cACTgD,SAAS7I,EAAK+B,eAAeoI,MAE5BA,EAAKmB,WAAY,CACnB,GAAMC,GAAcvL,EAAK2F,OAAOuF,KAAKf,EAAKqB,QAASrB,EAAKsB,QAAStB,EAAKmB,YACpEzF,SAAS,cACTgD,SAAS7I,EAAK+B,eAAeoI,KAG5BoB,GAAYG,UAAUC,GAAK3L,EAAK+B,eAAe+G,KAAK4C,UAAUlE,OAChE+D,EAAYK,WAdM,MAAA3G,GAAA4F,GAAA,EAAAC,EAAA7F,EAAA,aAAA2F,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,KAoBtB,QAASG,KACR,GAAIY,GAAY,KACVnF,EAAQ1G,EAAK0G,MAAMjE,IAAI,SAAC0H,EAAMxH,GACnC,GAAMe,GAAIoI,EAAc3B,EAAM0B,EAAWlJ,EAEzC,OADAkJ,GAAY1B,EACLzG,GAER,OAAOgD,GAGR,QAASoF,GAAc3B,EAAM0B,EAAWlJ,GACnCkJ,IACHA,EAAY1B,EAAK9G,QAAQD,IAAI,EAAG,QAEjC,IAAM2I,IACLC,oBAAqB7B,EAAKK,OAAO,MACjCyB,iBAAkB9B,EAAKK,OAAO,MAC9B0B,UAAa/B,EAAKA,SAAW0B,EAAU1B,OAASA,EAAKK,OAAO,KAAO,GACnE2B,WAAchC,EAAKG,UAAYuB,EAAUvB,QACxCH,EAAKK,OAAO,SAAWL,EAAKK,OAAO,KACpC4B,YAAejC,EAAKK,OAAO,QAC3B6B,oBAAqBlC,EAAKA,SAAW0B,EAAU1B,OAASA,EAAKK,OAAO,SAAW,GAC/E8B,iBAAkBnC,EAAKA,SAAW0B,EAAU1B,OAC3CA,EAAKG,UAAYuB,EAAUvB,QAC3BH,EAAKK,OAAO,SAAWL,EAAKK,OAAO,KAAO,GAC3C+B,UAAapC,EAAKG,UAAYuB,EAAUvB,QAAUH,EAAKK,OAAO,QAAU,GACxEgC,WAAcrC,EAAKG,UAAYuB,EAAUvB,QAAUH,EAAKK,OAAO,QAAU,GACzEiC,YAAetC,EAAKuC,SAAWb,EAAUa,OAASvC,EAAKK,OAAO,QAAU,IAGnEmC,GACLlC,EAAG9H,EAAI3C,EAAKH,OAAOc,aACnByK,QAASpL,EAAKH,OAAOa,cACrB+K,QAASzL,EAAKH,OAAOa,cAAgB,IAGhCkM,GACLZ,oBAAiD,EAA3BhM,EAAKH,OAAOc,aAAoB,EACtD0L,oBAAqB,EACrBJ,iBAA8C,EAA3BjM,EAAKH,OAAOc,aAAoB,EACnD2L,iBAAkB,EAClBJ,UAAalM,EAAKH,OAAOc,aAAe,EACxC4L,UAAyC,GAA3BvM,EAAKH,OAAOc,aAAqB,EAC/CwL,WAAc,EACdK,WAA0C,EAA3BxM,EAAKH,OAAOc,aAAoB,EAC/CyL,YAAepM,EAAKH,OAAOc,aAAe,EAC1C8L,YAA2C,GAA3BzM,EAAKH,OAAOc,aAAqB,EAGlD,QACC2K,WAAYS,EAAa/L,EAAKH,OAAOU,UAAzB,UACZ8K,WAAYU,EAAa/L,EAAKH,OAAOU,UAAzB,UACZiL,QAASmB,EAASlC,EAAImC,EAAS5M,EAAKH,OAAOU,UAArB,UACtBkL,QAASkB,EAASlB,QAClBN,QAASwB,EAASlC,EAAImC,EAAS5M,EAAKH,OAAOU,UAArB,UACtB6K,QAASuB,EAASvB,SAIpB,QAASjF,KACRnG,EAAK8B,UADiB,IAAA+K,IAAA,EAAAC,GAAA,EAAAC,EAAA7I,MAAA,KAEtB,OAAA8I,GAFsBC,EAAA,cAEdvK,GAFcsK,EAAAtI,MAGjBwI,IACJA,GAASxK,EAAKa,aAAad,IAAI,SAAA0K,GAC9B,GAAMC,GAAaC,EAASF,EAC5B,IAAIC,EAAJ,CAEA,GAAMnM,IAAQ,EAAAqM,cACbtN,EACAA,EAAK6B,MAAMuL,EAAWlK,QACtBlD,EAAK6B,MAAMa,EAAKQ,QAGjB,OADAlD,GAAK+B,eAAed,MAAMmC,IAAInC,EAAMtB,SAC7BsB,KACL2C,OAAO,SAAA2J,GAAA,MAAOA,KACjBvN,EAAK8B,QAAU9B,EAAK8B,QAAQ0L,OAAON,IAdpCO,EAAgBzN,EAAKJ,MAArByE,OAAAC,cAAAuI,GAAAG,EAAAS,EAAAlJ,QAAAC,MAAAqI,GAAA,EAA4BI,IAFN,MAAAhI,GAAA6H,GAAA,EAAAC,EAAA9H,EAAA,aAAA4H,GAAAY,2BAAA,WAAAX,EAAA,KAAAC,KAoBvB,QAAS7G,KAERlG,EAAK6B,MAAQ7B,EAAKJ,MAAM6C,IAAI,SAACC,GAC5B,GAAM5B,IAAM,EAAA4M,cAAI1N,EAAM0C,EAEtB,OADA1C,GAAK+B,eAAejB,IAAIsC,IAAItC,EAAIoG,OACzBpG,IAIT,QAASsF,KAAqB,GAAAuH,IAAA,EAAAC,GAAA,EAAAC,EAAA3J,MAAA,KAC7B,OAAA4J,GAD6BC,EAAA,cACrBjN,GADqBgN,EAAApJ,KAE5B5D,GAAIoM,OAASlN,EAAK8B,QAAQ8B,OAAO,SAAA3C,GAChC,MAAQA,GAAM+M,UAAUtL,KAAK1D,KAAO8B,EAAI4B,KAAK1D,IAC3CiC,EAAMgN,QAAQvL,KAAK1D,KAAO8B,EAAI4B,KAAK1D,MAHvCkP,EAAelO,EAAK6B,MAApBwC,OAAAC,cAAAqJ,GAAAG,EAAAI,EAAA3J,QAAAC,MAAAmJ,GAAA,EAA2BI,IADE,MAAA9I,GAAA2I,GAAA,EAAAC,EAAA5I,EAAA,aAAA0I,GAAAO,2BAAA,WAAAN,EAAA,KAAAC,KAS9B,QAAStH,KACRvG,EAAK+B,eAAe+G,KAAKqF,MAAM,WAC9BjO,IACAF,EAAK+B,eAAeqM,QAClBC,UAAU,oBACVC,QAAQ,SAAAC,GAAA,MAAMA,GAAG1I,SAAS,YAI9B,QAAS3F,KACRF,EAAK2F,OAAO0I,UAAU,gBAAgBC,QAAQ,SAAAC,GAC7CA,EAAGC,YAAY,YAIjB,QAASrO,GAAQsO,GAChB,GAAqB,gBAAVA,GACV,MAAOzO,GAAKH,OAAOU,YAAckO,CAC3B,IAAGC,MAAMC,QAAQF,GAAQ,IAAAG,IAAA,EAAAC,GAAA,EAAAC,EAAA5K,MAAA,KAC/B,OAAA6K,GAAAC,EAAiBP,EAAjBpK,OAAAC,cAAAsK,GAAAG,EAAAC,EAAAzK,QAAAC,MAAAoK,GAAA,EAAwB,IAAf3M,GAAe8M,EAAArK,KACvB,IAAG1E,EAAKH,OAAOU,YAAc0B,EAAM,OAAO,GAFZ,MAAAgD,GAAA4J,GAAA,EAAAC,EAAA7J,EAAA,aAAA2J,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,IAI/B,OAAO,GAIT,QAASzB,GAASrO,GACjB,MAAOgB,GAAKJ,MAAMqP,KAAK,SAACvM,GACvB,MAAOA,GAAK1D,KAAOA,IAIrB,QAASoB,GAAQpB,GAChB,MAAOgB,GAAK6B,MAAMoN,KAAK,SAACnO,GACvB,MAAOA,GAAI4B,KAAK1D,KAAOA,IAIzB,QAAS6E,GAAYnB,GACpB,MAAOA,GAAKwM,KAAO,IAAMC,KAAKC,SAASC,SAAS,IAAIC,MAAM,EAAG,IAG9D,QAASjP,GAAckP,EAAOC,GAC1BxP,EAAKH,OAAO,MAAQ0P,IACtBvP,EAAKH,OAAO,MAAQ0P,GAAOE,MAAM,KAAMD,GApiBzC,GAAMxP,KA0iBN,OAFAF,KAEOE,EF/fP0P,OAAOC,eAAepR,EAAS,cAC9BmG,OAAO,IAERnG,aEhDuBmB,EALxBb,EAAA,EAEA,IAAA+Q,GAAA/Q,EAAA,GFyDK6O,EAAQpO,EAAuBsQ,GExDpCC,EAAAhR,EAAA,GF4DKyO,EAAUhO,EAAuBuQ,EAqvBrCrR,GAAOD,QAAUA,EAAQ,YAIpB,SAASC,EAAQD,EAASM,GG7zBhC,GAAAiR,GAAAjR,EAAA,EACA,iBAAAiR,SAAAtR,EAAAQ,GAAA8Q,EAAA,KAEAjR,GAAA,GAAAiR,KACAA,GAAAC,SAAAvR,EAAAD,QAAAuR,EAAAC,SHm1BM,SAASvR,EAAQD,EAASM,GI11BhCN,EAAAC,EAAAD,QAAAM,EAAA,KAKAN,EAAAyG,MAAAxG,EAAAQ,GAAA,+mDAAsoD,IAAQgR,QAAA,EAAAC,SAAA,mEAAAC,SAAAC,SAAA,+sBAAAC,KAAA,aAAAC,gBAAA,25EAA2vGC,WAAA,OJm2Bn4J,SAAS9R,EAAQD,GKn2BvBC,EAAAD,QAAA,WACA,GAAAgS,KA0CA,OAvCAA,GAAAlB,SAAA,WAEA,OADAmB,MACA7N,EAAA,EAAgBA,EAAAhE,KAAA+J,OAAiB/F,IAAA,CACjC,GAAA8N,GAAA9R,KAAAgE,EACA8N,GAAA,GACAD,EAAAxL,KAAA,UAAAyL,EAAA,OAAwCA,EAAA,QAExCD,EAAAxL,KAAAyL,EAAA,IAGA,MAAAD,GAAAE,KAAA,KAIAH,EAAA5N,EAAA,SAAA/D,EAAA+R,GACA,gBAAA/R,KACAA,IAAA,KAAAA,EAAA,KAEA,QADAgS,MACAjO,EAAA,EAAgBA,EAAAhE,KAAA+J,OAAiB/F,IAAA,CACjC,GAAA3D,GAAAL,KAAAgE,GAAA,EACA,iBAAA3D,KACA4R,EAAA5R,IAAA,GAEA,IAAA2D,EAAA,EAAYA,EAAA/D,EAAA8J,OAAoB/F,IAAA,CAChC,GAAA8N,GAAA7R,EAAA+D,EAKA,iBAAA8N,GAAA,IAAAG,EAAAH,EAAA,MACAE,IAAAF,EAAA,GACAA,EAAA,GAAAE,EACKA,IACLF,EAAA,OAAAA,EAAA,aAAAE,EAAA,KAEAJ,EAAAvL,KAAAyL,MAIAF,ILg3BM,SAAS/R,EAAQD,EAASM,GMn2BhC,QAAAgS,GAAAC,EAAAC,GACA,OAAApO,GAAA,EAAeA,EAAAmO,EAAApI,OAAmB/F,IAAA,CAClC,GAAA8N,GAAAK,EAAAnO,GACAqO,EAAAC,EAAAR,EAAAzR,GACA,IAAAgS,EAAA,CACAA,EAAAE,MACA,QAAAC,GAAA,EAAiBA,EAAAH,EAAAI,MAAA1I,OAA2ByI,IAC5CH,EAAAI,MAAAD,GAAAV,EAAAW,MAAAD,GAEA,MAAQA,EAAAV,EAAAW,MAAA1I,OAAuByI,IAC/BH,EAAAI,MAAApM,KAAAqM,EAAAZ,EAAAW,MAAAD,GAAAJ,QAEG,CAEH,OADAK,MACAD,EAAA,EAAiBA,EAAAV,EAAAW,MAAA1I,OAAuByI,IACxCC,EAAApM,KAAAqM,EAAAZ,EAAAW,MAAAD,GAAAJ,GAEAE,GAAAR,EAAAzR,KAA2BA,GAAAyR,EAAAzR,GAAAkS,KAAA,EAAAE,WAK3B,QAAAE,GAAAf,GAGA,OAFAO,MACAS,KACA5O,EAAA,EAAeA,EAAA4N,EAAA7H,OAAiB/F,IAAA,CAChC,GAAA8N,GAAAF,EAAA5N,GACA3D,EAAAyR,EAAA,GACAe,EAAAf,EAAA,GACAgB,EAAAhB,EAAA,GACAiB,EAAAjB,EAAA,GACAkB,GAAcH,MAAAC,QAAAC,YACdH,GAAAvS,GAGAuS,EAAAvS,GAAAoS,MAAApM,KAAA2M,GAFAb,EAAA9L,KAAAuM,EAAAvS,IAAgCA,KAAAoS,OAAAO,KAIhC,MAAAb,GAGA,QAAAc,GAAAb,EAAAc,GACA,GAAAC,GAAAC,IACAC,EAAAC,IAAAvJ,OAAA,EACA,YAAAqI,EAAAmB,SACAF,EAEGA,EAAAG,YACHL,EAAAM,aAAAP,EAAAG,EAAAG,aAEAL,EAAAO,YAAAR,GAJAC,EAAAM,aAAAP,EAAAC,EAAAQ,YAMAL,EAAAjN,KAAA6M,OACE,eAAAd,EAAAmB,SAGF,SAAAK,OAAA,qEAFAT,GAAAO,YAAAR,IAMA,QAAAW,GAAAX,GACAA,EAAAY,WAAAC,YAAAb,EACA,IAAAc,GAAAV,EAAAW,QAAAf,EACAc,IAAA,GACAV,EAAAY,OAAAF,EAAA,GAIA,QAAAG,GAAA/B,GACA,GAAAc,GAAAtQ,SAAAwR,cAAA,QAGA,OAFAlB,GAAAmB,KAAA,WACApB,EAAAb,EAAAc,GACAA,EAGA,QAAAoB,GAAAlC,GACA,GAAAmC,GAAA3R,SAAAwR,cAAA,OAGA,OAFAG,GAAAC,IAAA,aACAvB,EAAAb,EAAAmC,GACAA,EAGA,QAAA7B,GAAA9R,EAAAwR,GACA,GAAAc,GAAAuB,EAAAxH,CAEA,IAAAmF,EAAAsC,UAAA,CACA,GAAAC,GAAAC,GACA1B,GAAA2B,MAAAV,EAAA/B,IACAqC,EAAAK,EAAAC,KAAA,KAAA7B,EAAAyB,GAAA,GACA1H,EAAA6H,EAAAC,KAAA,KAAA7B,EAAAyB,GAAA,OACE/T,GAAAmS,WACF,kBAAAiC,MACA,kBAAAA,KAAAC,iBACA,kBAAAD,KAAAE,iBACA,kBAAAC,OACA,kBAAAC,OACAlC,EAAAoB,EAAAlC,GACAqC,EAAAY,EAAAN,KAAA,KAAA7B,GACAjG,EAAA,WACA4G,EAAAX,GACAA,EAAAoC,MACAN,IAAAE,gBAAAhC,EAAAoC,SAGApC,EAAAiB,EAAA/B,GACAqC,EAAAc,EAAAR,KAAA,KAAA7B,GACAjG,EAAA,WACA4G,EAAAX,IAMA,OAFAuB,GAAA7T,GAEA,SAAA4U,GACA,GAAAA,EAAA,CACA,GAAAA,EAAA3C,MAAAjS,EAAAiS,KAAA2C,EAAA1C,QAAAlS,EAAAkS,OAAA0C,EAAAzC,YAAAnS,EAAAmS,UACA,MACA0B,GAAA7T,EAAA4U,OAEAvI,MAcA,QAAA6H,GAAA5B,EAAAuC,EAAAxI,EAAArM,GACA,GAAAiS,GAAA5F,EAAA,GAAArM,EAAAiS,GAEA,IAAAK,EAAAwC,WACAxC,EAAAwC,WAAAC,QAAAC,EAAAH,EAAA5C,OACE,CACF,GAAAgD,GAAAjT,SAAAkT,eAAAjD,GACAkD,EAAA7C,EAAA6C,UACAA,GAAAN,IAAAvC,EAAAa,YAAAgC,EAAAN,IACAM,EAAAhM,OACAmJ,EAAAO,aAAAoC,EAAAE,EAAAN,IAEAvC,EAAAQ,YAAAmC,IAKA,QAAAN,GAAArC,EAAAtS,GACA,GAAAiS,GAAAjS,EAAAiS,IACAC,EAAAlS,EAAAkS,KAMA,IAJAA,GACAI,EAAA8C,aAAA,QAAAlD,GAGAI,EAAAwC,WACAxC,EAAAwC,WAAAC,QAAA9C,MACE,CACF,KAAAK,EAAAS,YACAT,EAAAa,YAAAb,EAAAS,WAEAT,GAAAQ,YAAA9Q,SAAAkT,eAAAjD,KAIA,QAAAwC,GAAAd,EAAA3T,GACA,GAAAiS,GAAAjS,EAAAiS,IACAE,EAAAnS,EAAAmS,SAEAA,KAEAF,GAAA,uDAAuDuC,KAAAa,SAAAC,mBAAAC,KAAAC,UAAArD,MAAA,MAGvD,IAAAsD,GAAA,GAAAlB,OAAAtC,IAA6BwB,KAAA,aAE7BiC,EAAA/B,EAAAe,IAEAf,GAAAe,KAAAN,IAAAC,gBAAAoB,GAEAC,GACAtB,IAAAE,gBAAAoB,GAhPA,GAAAhE,MACAiE,EAAA,SAAAC,GACA,GAAAC,EACA,mBAEA,MADA,mBAAAA,OAAAD,EAAA1F,MAAA9Q,KAAA0W,YACAD,IAGAE,EAAAJ,EAAA,WACA,qBAAAK,KAAAvV,KAAAwV,UAAAC,UAAAC,iBAEA3D,EAAAmD,EAAA,WACA,MAAA3T,UAAAuQ,MAAAvQ,SAAAoU,qBAAA,aAEAnC,EAAA,KACAD,EAAA,EACAtB,IAEAzT,GAAAD,QAAA,SAAAgS,EAAAQ,GAKAA,QAGA,mBAAAA,GAAAsC,YAAAtC,EAAAsC,UAAAiC,KAGA,mBAAAvE,GAAAmB,WAAAnB,EAAAmB,SAAA,SAEA,IAAApB,GAAAQ,EAAAf,EAGA,OAFAM,GAAAC,EAAAC,GAEA,SAAA6E,GAEA,OADAC,MACAlT,EAAA,EAAgBA,EAAAmO,EAAApI,OAAmB/F,IAAA,CACnC,GAAA8N,GAAAK,EAAAnO,GACAqO,EAAAC,EAAAR,EAAAzR,GACAgS,GAAAE,OACA2E,EAAA7Q,KAAAgM,GAEA,GAAA4E,EAAA,CACA,GAAArE,GAAAD,EAAAsE,EACA/E,GAAAU,EAAAR,GAEA,OAAApO,GAAA,EAAgBA,EAAAkT,EAAAnN,OAAsB/F,IAAA,CACtC,GAAAqO,GAAA6E,EAAAlT,EACA,QAAAqO,EAAAE,KAAA,CACA,OAAAC,GAAA,EAAkBA,EAAAH,EAAAI,MAAA1I,OAA2ByI,IAC7CH,EAAAI,MAAAD,WACAF,GAAAD,EAAAhS,OAiIA,IAAAuV,GAAA,WACA,GAAAuB,KAEA,iBAAA1B,EAAA2B,GAEA,MADAD,GAAA1B,GAAA2B,EACAD,EAAAlS,OAAAoS,SAAAtF,KAAA,WN+9BM,SAASlS,EAAQD,GAEtB,YOrpCc,SAAS0X,GAAIC,EAAIxT,GAI/B,QAAS5C,KACRC,IACAoC,IACAgU,IACAzC,IAGD,QAAS3T,KACRC,EAAKoW,kBAAmB,EACxBpW,EAAK0C,KAAOA,EAGb,QAASP,KACRkU,IACAC,IAGD,QAASD,KACRrW,EAAKsD,QAAUtD,EAAK0C,KAAKY,QACzBtD,EAAKe,OAASmV,EAAGrW,OAAOiB,IAAIC,OAC5Bf,EAAKyK,EAAI8L,IACTvW,EAAK0K,EAAI8L,IACTxW,EAAKgB,cAAgBkV,EAAGrW,OAAOiB,IAAIE,cACnChB,EAAKyW,UAAYzW,EAAK0C,KAAKK,KAAKE,KAAKjD,EAAK0C,KAAKE,OAAQ,SAAW,IAAMsT,EAAGrW,OAAOe,KAClFZ,EAAKwH,MAAQ0O,EAAGrW,OAAOc,aAAeX,EAAKyW,SAC3CzW,EAAK0W,eAAiBR,EAAGrW,OAAOc,aAAeX,EAAKyW,UAAYzW,EAAK0C,KAAKiU,SAAW,MAAQ,EAC7F3W,EAAKkH,MAAQgP,EAAGvQ,OAAOuB,QAAQrB,SAAS,eAAeA,SAAS7F,EAAK0C,KAAKkU,cAAgB,IAC1F5W,EAAK6W,UAAYX,EAAGvQ,OAAOuB,QAAQrB,SAAS,aAAagD,SAAS7I,EAAKkH,OACvElH,EAAK8W,aAAeZ,EAAGvQ,OAAOuB,QAAQrB,SAAS,gBAAgBgD,SAAS7I,EAAKkH,OAG9E,QAASoP,KACR1Q,KAAKmR,OAAO,SAAUnR,EAAMoR,EAASC,EAAOC,EAAQC,GACnDH,EAAQI,UAAUC,KAAO,WACxB,OAAQ1Y,KAAKwI,KAAK,MAEnB6P,EAAQI,UAAUE,KAAO,WACxB,OAAQ3Y,KAAKwI,KAAK,MAEnB6P,EAAQI,UAAUG,SAAW,WAC5B,OAAQ5Y,KAAKwI,KAAK,UAEnB6P,EAAQI,UAAUI,UAAY,WAC7B,OAAQ7Y,KAAKwI,KAAK,WAEnB6P,EAAQI,UAAUK,QAAU,WAC3B,MAAO9Y,MAAK0Y,OAAS1Y,KAAK4Y,cAK7B,QAASpB,KACRuB,IACAC,IACAC,IACAC,IAGD,QAASH,KACR1X,EAAK8X,KAAO5B,EAAGvQ,OAAOiD,KAAK5I,EAAKyK,EAAGzK,EAAK0K,EACvC1K,EAAKwH,MAAOxH,EAAKe,OACjBf,EAAKgB,cAAehB,EAAKgB,eACxB6E,SAAS,OACTgD,SAAS7I,EAAK6W,WACZ7W,EAAKsD,SACRtD,EAAK8X,KAAKjS,SAAS,eAIrB,QAAS8R,KACJ3X,EAAKsD,UACTtD,EAAK+X,cAAgB7B,EAAGvQ,OAAOiD,KAAK5I,EAAKyK,EAAGzK,EAAK0K,EAChD1K,EAAK0W,eAAgB1W,EAAKe,OAC1Bf,EAAKgB,cAAehB,EAAKgB,eACxB6E,SAAS,gBACTgD,SAAS7I,EAAK6W,YAGjB,QAASe,KACR1B,EAAGvQ,OAAOuF,KAAKlL,EAAKyK,EAAIzK,EAAKwH,MAAQ,EACpCxH,EAAK0K,EAAI1K,EAAKe,OAAS,EACvBf,EAAK0C,KAAKwM,MACTrJ,SAAS,aACTgD,SAAS7I,EAAK6W,WAChBmB,IAGD,QAASH,KACR,IAAI7X,EAAKsD,QAAT,CAEA,GAAMxC,GAAMd,EAAK8X,KAChBG,EAAe,CAEhB/B,GAAGvQ,OAAOiD,KAAK9H,EAAIuW,OAASvW,EAAIyW,WAAa,EAAGzW,EAAIwW,OAAS,EAC5DW,EAAcjY,EAAKe,OAAS,EAAGf,EAAKgB,cAAehB,EAAKgB,eACvD6E,SAAS,gBACTgD,SAAS7I,EAAK8W,cAChBZ,EAAGvQ,OAAOiD,KAAK9H,EAAIuW,OAAS,EAAGvW,EAAIwW,OAAS,EAC3CW,EAAcjY,EAAKe,OAAS,EAAGf,EAAKgB,cAAehB,EAAKgB,eACvD6E,SAAS,eACTgD,SAAS7I,EAAK8W,cAEZ9W,EAAK0C,KAAKiU,UAAY3W,EAAK0C,KAAKiU,SAAW,KAC9CT,EAAGvQ,OAAOuS,QAAQC,KAChBtS,SAAS,mBACTgD,SAAS7I,EAAK8W,eAIlB,QAASqB,KACR,GAAMC,GAAepY,EAAK+X,aAC1B,QACCK,EAAaX,UAAY,EAAGW,EAAad,OAASc,EAAaZ,YAC/DY,EAAaX,UAAY,EAAGW,EAAad,OAASc,EAAaZ,YAC/DY,EAAaX,UAAWW,EAAad,OAASc,EAAaZ,YAAc,MAI3E,QAAS9D,KACJ1T,EAAKsD,UACT+U,IACAC,IACAC,IACAC,IACAC,KAGD,QAASH,KACR,GAAMI,GAAgBxC,EAAGnU,eAAeqM,OAIxC,IAHApO,EAAK2Y,YAAcD,EACjBhR,OADiB,+BACqB1H,EAAK0C,KAAK1D,GAD/B,OAGdgB,EAAK2Y,YAAa,CACtB3Y,EAAK2Y,YAAczC,EAAGvQ,OAAOuB,QAC3BrB,SAAS,wBACTsB,KAAK,YAAanH,EAAK0C,KAAK1D,IAC5B6J,SAAS6P,GAEXE,GAEA,IAAMC,GAAI3C,EAAGvQ,OAAO/B,OACnBgC,KAAKhC,OAAOkV,OAAO,EAAG,EAAG,EAAG,OAAQ,IACrC9Y,GAAK2Y,YAAYxR,MAChBvD,OAAQiV,IAIV7Y,EAAKkH,MAAMiH,MAAM,SAAC4K,GACb/Y,EAAKoW,mBAITsC,EAAcrK,UAAU,oBACtBC,QAAQ,SAAAC,GAAA,MAAMA,GAAG1I,SAAS,UAC5B7F,EAAK2Y,YAAYnK,YAAY,WAI/B,QAASoK,KAAiB,GAAAI,GACVC,IAARxO,EADkBuO,EAClBvO,EAAGC,EADesO,EACftO,CACV1K,GAAK2Y,YAAYO,UAAjB,IAA+BzO,EAA/B,IAAoCC,GACpC1K,EAAK2Y,YAAY7S,OAEjB,IAAMqT,GAAOC,IACPC,EACLzT,KAAK0T,MAAL,8GAEIH,EAFJ,8CAKDnZ,GAAK2Y,YAAYY,OAAOF,GAGzB,QAASD,KAGR,GAAGlD,EAAGrW,OAAOwB,kBAAmB,CAC/B,GAAM8X,GAAOjD,EAAGrW,OAAOwB,iBACvB,IAAmB,gBAAT8X,GACT,MAAOA,EAER,IAAGK,EAAWL,GACb,MAAOA,GAAKzW,GAId,GAAM+W,GAAazZ,EAAK0C,KAAKE,OAAO4H,OAAO,SACrCkP,EAAW1Z,EAAK0C,KAAKK,KAAKyH,OAAO,SACjCmP,EAAa3Z,EAAK0C,KAAKwM,KAAvB,KAAgCuK,EAAhC,MAAgDC,EAEhDE,eAAsB5Z,EAAKyW,SAA3B,QACAoD,EAAS7Z,EAAK0C,KAAKiU,SAAV,aAAkC3W,EAAK0C,KAAKiU,SAAa,KAElEwC,0DAEEQ,EAFF,qBAGCC,EAHD,kBAKHC,QAAeA,EAAf,OAA8B,IAL3B,sBASN,OAAOV,GAGR,QAASF,KACR,OACCxO,EAAGzK,EAAK8X,KAAKL,UAAY,EACzB/M,EAAG1K,EAAK8X,KAAKR,OAAS,IAIxB,QAASiB,KAMR,QAASuB,GAAaC,EAAIC,GACzBC,EAAoBF,EAAIC,GAEzB,QAASE,KACRC,IAGD,QAASC,GAAYL,EAAIC,GACxBK,EAAmBN,EAAIC,GAExB,QAASM,KACRC,IAjBqB,GAAAC,GACEC,IAAhBC,EADcF,EACdE,KAAMC,EADQH,EACRG,KAEdD,GAAKE,KAAKR,EAAaS,EAASP,GAChCK,EAAMC,KAAKd,EAAce,EAASX,GAiBnC,QAASO,KACR,OACCC,KAAM1a,EAAK8W,aAAapP,OAAO,gBAC/BiT,MAAO3a,EAAK8W,aAAapP,OAAO,kBAIlC,QAAS8Q,KACRxY,EAAK6W,UAAU+D,KAAKE,EAAQD,EAASE,GAGtC,QAAStC,KAMR,QAASuC,GAAQjB,EAAIC,GAChBD,EAAK3B,EAAa6C,SACrBlB,EAAK3B,EAAa6C,QAEflB,EAAK3B,EAAa8C,SACrBnB,EAAK3B,EAAa8C,QAGnB9C,EAAajR,KAAK,QAASiR,EAAa+C,OAASpB,GACjDqB,EAAOjU,KAAK,SAAUgR,KACtBC,EAAaiD,QAAUtB,EAExB,QAASuB,KACHlD,EAAaiD,UAClBE,IACAC,KAED,QAASC,KACRrD,EAAaiD,QAAU,EACvBjD,EAAa+C,OAAS/C,EAAab,WACnCa,EAAa8C,QAAU9C,EAAab,WACpCa,EAAa6C,OAASna,EAAIyW,WAAaa,EAAab,WA1BrD,GAAMzW,GAAMd,EAAK8X,KAChBM,EAAepY,EAAK+X,cACpBqD,EAASpb,EAAKkH,MAAMQ,OAAO,mBAC5B0T,IAAUA,EAAOR,KAAKI,EAASS,EAAUH,GA2B1C,QAAST,KACR,GAAM/Z,GAAMd,EAAK8X,IACjBhX,GAAI4a,GAAK5a,EAAIuW,OACbvW,EAAI6a,GAAK7a,EAAIwW,OACbxW,EAAIqa,OAASra,EAAIyW,WACjBzW,EAAIua,QAAU,EACdO,EAA4B,WAI7B,QAASd,GAAOf,EAAIC,GACnB,GAAMlZ,GAAMd,EAAK8X,IACjBhX,GAAIua,QAAUQ,EAAkB9B,GAChC+B,GAAqBrR,EAAG3J,EAAI4a,GAAK5a,EAAIua,UACrCO,EAA4B,UAAW7B,EAAIC,IAI5C,QAASe,KACR,GAAMja,GAAMd,EAAK8X,IACZhX,GAAIua,UACTU,IACAP,IACAI,EAA4B,WAI7B,QAASvB,GAAmBN,EAAIC,GAC/B,GAAMlZ,GAAMd,EAAK8X,IACjBhX,GAAIua,QAAUQ,EAAkB9B,GAChC+B,GACCrR,EAAG3J,EAAI4a,GAAK5a,EAAIua,QAChB7T,MAAO1G,EAAIqa,OAASra,EAAIua,UAEzBO,EAA4B,UAAW7B,EAAIC,IAI5C,QAASO,KACR,GAAMzZ,GAAMd,EAAK8X,IACbhX,GAAIua,SAASU,IACjBP,IACAI,EAA4B,UAI7B,QAASA,GAA4BzG,EAAI3F,GACxC,GAAMwM,GAAK9F,EAAGpS,cACd,IAAIkY,EAAGhc,EAAK0C,KAAK1D,IAAK,IAAA+E,IAAA,EAAAC,GAAA,EAAAC,EAAAC,MAAA,KACrB,OAAAC,GAAAC,EAAoB4X,EAAGhc,EAAK0C,KAAK1D,IAAjCqF,OAAAC,cAAAP,GAAAI,EAAAC,EAAAG,QAAAC,MAAAT,GAAA,EAAsC,IAA7BkY,GAA6B9X,EAAAO,MAC/BwX,EAAKhG,EAAG9V,QAAQ6b,EACtBC,GAAG/G,GAAI1F,MAAMyM,EAAI1M,IAHG,MAAAvK,GAAAjB,GAAA,EAAAC,EAAAgB,EAAA,aAAAlB,GAAAK,2BAAA,WAAAJ,EAAA,KAAAC,MAQvB,QAASgW,GAAoBF,EAAIC,GAChC,GAAMlZ,GAAMd,EAAK8X,IACjBhX,GAAIua,QAAUQ,EAAkB9B,GAChC+B,GAAqBtU,MAAO1G,EAAIqa,OAASra,EAAIua,UAG9C,QAASlB,KACR,GAAMrZ,GAAMd,EAAK8X,IACbhX,GAAIua,SAASU,IACjBP,IAGD,QAASM,GAATK,GAAuD,GAAAC,GAAAD,EAAzB1R,IAAyBvG,SAAAkY,EAArB,KAAqBA,EAAAC,EAAAF,EAAf3U,QAAetD,SAAAmY,EAAP,KAAOA,EAChDvb,EAAMd,EAAK8X,IACjB,IAAIrN,EAAG,CAEN,GAAM6R,GAAK5Z,EAAKa,aAAad,IAAI,SAAA0K,GAChC,MAAO+I,GAAG9V,QAAQ+M,GAAK2K,KAAKT,SAGvBkF,EAAUD,EAAGtU,OAAO,SAACwU,EAAMtU,GAChC,MAAOuC,IAAKvC,GACVuC,EACH,KAAI8R,EAEH,YADA/U,EAAQ,KAGTiV,GAAY3b,EAAK,IAAK2J,GAEnBjD,GAASA,GAAS0O,EAAGrW,OAAOc,cAC/B8b,EAAY3b,EAAK,QAAS0G,GAE3BwQ,IACA0E,IACAC,IACAC,IACAC,IAGD,QAASxE,KACRrY,EAAKkH,MAAMiH,MAAM,WACZnO,EAAKoW,mBAILpW,EAAKkH,MAAM4V,SAAS,WACvB5G,EAAG7V,cAAc,SAAUL,EAAK0C,OAEjCwT,EAAGhW,eACHF,EAAKkH,MAAM6V,YAAY,aAIzB,QAAShB,KAAe,GAAAiB,GACkBC,IAAjCC,EADeF,EACfE,eAAgBC,EADDH,EACCG,YACxBnd,GAAK0C,KAAKE,OAASsa,EACnBld,EAAK0C,KAAKK,KAAOoa,EACjBvE,IACA1C,EAAG7V,cAAc,eACfL,EAAK0C,KAAMwa,EAAgBC,IAG9B,QAAS5B,KACR,GAAM6B,GAAeC,GACrBrd,GAAK0C,KAAKiU,SAAWyG,EACrBxE,IACA1C,EAAG7V,cAAc,mBACfL,EAAK0C,KAAM0a,IAGd,QAAS5B,KACRxb,EAAKoW,kBAAmB,EACxBkH,WAAW,iBAAMtd,GAAKoW,kBAAmB,GAAO,KAGjD,QAAS6G,KACR,GAAMnc,GAAMd,EAAK8X,KACXyF,EAAazc,EAAIuW,OAASnB,EAAGrW,OAAOc,aACpCuc,EAAiBhH,EAAGhR,YAAY7B,QAAQD,IAAIma,EAAarH,EAAGrW,OAAOe,KAAM,SACzE4c,EAAiB1c,EAAIyW,WAAarB,EAAGrW,OAAOc,aAC5Cwc,EAAeD,EAAe7Z,QAAQD,IAAIoa,EAAiBtH,EAAGrW,OAAOe,KAAM,QAOjF,OADAuc,GAAa/Z,IAAI,KAAM,YACd8Z,iBAAgBC,gBAG1B,QAASE,KACR,GAAM1G,GAAW3W,EAAK+X,cAAcR,WAAavX,EAAK8X,KAAKP,WAAa,GACxE,OAAOkG,UAAS9G,EAAU,IAG3B,QAASJ,KACR,GAAI9L,GAAIzK,EAAK0C,KAAKE,OAAOK,KAAKiT,EAAGhR,YAAa,SAC7CgR,EAAGrW,OAAOe,KAAOsV,EAAGrW,OAAOc,YAM5B,OAJIuV,GAAG/V,QAAQ,WACdsK,EAAIzK,EAAK0C,KAAKE,OAAOK,KAAKiT,EAAGhR,YAAa,QACzCgR,EAAGrW,OAAOc,aAAe,IAEpB8J,EAGR,QAAS+L,KACR,MAAON,GAAGrW,OAAOa,cAAgBwV,EAAGrW,OAAOsB,QAC1CnB,EAAK0C,KAAKQ,QAAUlD,EAAKe,OAASmV,EAAGrW,OAAOsB,SAG9C,QAAS0a,GAAkB9B,GAC1B,GAAI2D,GAAM3D,EAAI4D,SAAKC,QAenB,OAbI1H,GAAG/V,QAAQ,SACdwd,EAAM5D,GAAM7D,EAAGrW,OAAOc,aAAe,GACrCid,EAAWF,EAAMC,GACdA,EAAMzH,EAAGrW,OAAOc,aAAe,GAAM,EAAIuV,EAAGrW,OAAOc,aAAe,IAC3DuV,EAAG/V,QAAQ,UACrBwd,EAAM5D,GAAM7D,EAAGrW,OAAOc,aAAe,IACrCid,EAAWF,EAAMC,GACdA,EAAMzH,EAAGrW,OAAOc,aAAe,GAAM,EAAIuV,EAAGrW,OAAOc,aAAe,MAErEgd,EAAM5D,EAAK7D,EAAGrW,OAAOc,aACrBid,EAAWF,EAAMC,GACdA,EAAMzH,EAAGrW,OAAOc,aAAe,EAAK,EAAIuV,EAAGrW,OAAOc,eAE/Cid,EAGR,QAASnB,GAAY9c,EAASwH,EAAMzC,GAKnC,MAJAA,IAASA,EACJmZ,MAAMnZ,IACV/E,EAAQwH,KAAKA,EAAMzC,GAEb/E,EAGR,QAASgd,KACR3c,EAAK+X,cAAc5Q,KAAK,IAAKnH,EAAK8X,KAAKT,QACvCrX,EAAK+X,cAAc5Q,KAAK,QAASnH,EAAK8X,KAAKP,YAAcvX,EAAK0C,KAAKiU,SAAW,MAG/E,QAASqB,KACR,GAAMlX,GAAMd,EAAK8X,KAChBgG,EAAQ9d,EAAKkH,MAAMQ,OAAO,aACvBoW,GAAMpS,UAAUlE,MAAQ1G,EAAIyW,WAC/BuG,EAAMjY,SAAS,OAAOsB,KAAK,IAAKrG,EAAIuW,OAASvW,EAAIyW,WAAa,GAE9DuG,EAAMtP,YAAY,OAAOrH,KAAK,IAAKrG,EAAIuW,OAASvW,EAAIyW,WAAa,GAInE,QAASmF,KACR,GAAM5b,GAAMd,EAAK8X,IACjB9X,GAAK8W,aAAapP,OAAO,gBAAgBP,MACxCsD,EAAK3J,EAAIuW,OAAS,IAEnBrX,EAAK8W,aAAapP,OAAO,iBAAiBP,MACzCsD,EAAK3J,EAAI2W,UAAY,GAEtB,IAAM2D,GAASpb,EAAKkH,MAAMQ,OAAO,mBACjC0T,IAAUA,EAAOjU,KAAK,SAAUgR,KAGjC,QAASyE,KAAwB,GAAAjY,IAAA,EAAAC,GAAA,EAAAC,EAAAX,MAAA,KAChC,OAAAY,GAAAC,EAAkB/E,EAAKkN,OAAvB7I,OAAAC,cAAAK,GAAAG,EAAAC,EAAAR,QAAAC,MAAAG,GAAA,EAA+B,IAAtB1D,GAAsB6D,EAAAJ,KAC9BzD,GAAMmS,UAFyB,MAAAnO,GAAAL,GAAA,EAAAC,EAAAI,EAAA,aAAAN,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,KAMjC,QAASgY,KAA0B,GAAAkB,GACnB9E,IAARxO,EAD2BsT,EAC3BtT,EAAGC,EADwBqT,EACxBrT,CACV1K,GAAK2Y,aAAe3Y,EAAK2Y,YAAYO,UAAjB,IAA+BzO,EAA/B,IAAoCC,GAGzD,QAAS8O,GAAWwE,GACnB,GAAIC,KACJ,OAAOD,IAA8D,sBAA3CC,EAAQ5O,SAASnQ,KAAK8e,GA/fjD,GAAMhe,KAogBN,OAvOAA,GAAK6a,QAAUA,EAQf7a,EAAK8a,OAASA,EASd9a,EAAK+a,OAASA,EAWd/a,EAAKqa,mBAAqBA,EAQ1Bra,EAAKua,mBAAqBA,EAiM1Bza,IAEOE,EPipBP0P,OAAOC,eAAepR,EAAS,cAC9BmG,OAAO,IAERnG,aO1pCuB0X,EPyrDvBzX,EAAOD,QAAUA,EAAQ,YAIpB,SAASC,EAAQD,GAEtB,YQ7rDc,SAAS2f,GAAMhI,EAAIlI,EAAWC,GAI5C,QAASnO,KACRE,EAAKgO,UAAYA,EACjBhO,EAAKiO,QAAUA,EACf9L,IACAgU,IAGD,QAAShU,KAERnC,EAAKme,QAAUnQ,EAAU8J,KAAKT,OAASrJ,EAAU8J,KAAKP,WAAa,CAMnE,KAJA,GAAM6G,GAAY,iBACjBnQ,GAAQ6J,KAAKT,OAASrX,EAAKme,QAAUjI,EAAGrW,OAAOsB,SAC9CnB,EAAKme,QAAUnQ,EAAU8J,KAAKT,OAASnB,EAAGrW,OAAOsB,SAE7Cid,KACLpe,EAAKme,SAAW,EAGjBne,GAAKqe,QAAUnI,EAAGrW,OAAOa,cAAgBwV,EAAGrW,OAAOiB,IAAIC,QACrDmV,EAAGrW,OAAOsB,QAAU+U,EAAGrW,OAAOiB,IAAIC,QAAUiN,EAAUtL,KAAKQ,OAC5DgT,EAAGrW,OAAOsB,QAEXnB,EAAKse,MAAQrQ,EAAQ6J,KAAKT,OAASnB,EAAGrW,OAAOsB,QAAU,EACvDnB,EAAKue,MAAQrI,EAAGrW,OAAOa,cAAgBwV,EAAGrW,OAAOiB,IAAIC,OAAS,GAC5DmV,EAAGrW,OAAOsB,QAAU+U,EAAGrW,OAAOiB,IAAIC,QAAUkN,EAAQvL,KAAKQ,OAC1DgT,EAAGrW,OAAOsB,OAEX,IAAMqd,GAAoBxQ,EAAUtL,KAAKQ,OAAS+K,EAAQvL,KAAKQ,MAC/DlD,GAAKkB,MAAQgV,EAAGrW,OAAOoB,MAAMC,MAC7BlB,EAAKye,UAAYD,EAAmB,EAAI,EACxCxe,EAAK0e,QAAUF,GAAoBxe,EAAKkB,MAAQlB,EAAKkB,MACrDlB,EAAK2e,OAASH,EACbxe,EAAKue,MAAQrI,EAAGrW,OAAOoB,MAAMC,MAC7BlB,EAAKue,MAAQrI,EAAGrW,OAAOoB,MAAMC,MAE9BlB,EAAKuK,KACJ3E,KAAK4E,OAAO,+HAIV2T,QAASne,EAAKme,QACdE,QAASre,EAAKqe,QACdC,MAAOte,EAAKse,MACZC,MAAOve,EAAKue,MACZI,OAAQ3e,EAAK2e,OACbzd,MAAOlB,EAAKkB,MACZud,UAAWze,EAAKye,UAChBC,QAAS1e,EAAK0e,UAGdzQ,EAAQ6J,KAAKT,OAASrJ,EAAU8J,KAAKT,OAASnB,EAAGrW,OAAOsB,UAC1DnB,EAAKuK,KACJ3E,KAAK4E,OAAO,iPAMV2T,QAASne,EAAKme,QACdE,QAASre,EAAKqe,QACdC,MAAOte,EAAKse,MACZC,MAAOve,EAAKue,MACZK,OAAQ1I,EAAGrW,OAAOsB,QAAU,EAAInB,EAAKkB,MACrC2d,OAAQ5Q,EAAQ6J,KAAKR,OAASrJ,EAAQ6J,KAAKN,YAAc,EAAIxX,EAAK0e,QAClEhE,KAAMzM,EAAQ6J,KAAKT,OAASnB,EAAGrW,OAAOsB,QACtCwd,OAAQ3e,EAAK2e,OACbzd,MAAOlB,EAAKkB,MACZud,UAAWze,EAAKye,UAChBC,QAAS1e,EAAK0e,WAKnB,QAASvI,KACRnW,EAAKL,QAAUuW,EAAGvQ,OAAO4E,KAAKvK,EAAKuK,MACjCpD,KAAK,YAAanH,EAAKgO,UAAUtL,KAAK1D,IACtCmI,KAAK,UAAWnH,EAAKiO,QAAQvL,KAAK1D,IAGrC,QAASoU,KACRjR,IACAnC,EAAKL,QAAQwH,KAAK,IAAKnH,EAAKuK,MApF7B,GAAMvK,KA0FN,OAJAA,GAAKoT,OAASA,EAEdtT,IAEOE,ERmmDP0P,OAAOC,eAAepR,EAAS,cAC9BmG,OAAO,IAERnG,aQlsDuB2f,ER2xDvB1f,EAAOD,QAAUA,EAAQ,YAIpB,SAASC,EAAQD,EAASM,IS1yDhC,SAAAqY,EAAA5Y,GACAE,EAAAD,QAAAD,KAGCK,KAAA,WAAqB,YAOtB,SAAAmgB,GAAApa,GACA,QAAAA,GAAA,gBAAAA,GAGA,QAAAqa,GAAAra,GACA,GAAAsa,GAAAtP,OAAA0H,UAAA/H,SAAAnQ,KAAAwF,EAEA,2BAAAsa,GACA,kBAAAA,GACAC,EAAAva,GAOA,QAAAua,GAAAva,GACA,MAAAA,GAAAwa,WAAAC,EAGA,QAAAC,GAAAC,GACA,MAAA3Q,OAAAC,QAAA0Q,SAGA,QAAAC,GAAA5a,EAAA6a,GACA,GAAAlc,IAAAkc,KAAAlc,SAAA,CAEA,OAAAA,IAAAmc,EAAA9a,GACA+a,EAAAL,EAAA1a,KAAA6a,GACA7a,EAGA,QAAAgb,GAAAC,EAAAC,EAAAL,GACA,MAAAI,GAAAnS,OAAAoS,GAAAnd,IAAA,SAAA9C,GACA,MAAA2f,GAAA3f,EAAA4f,KAIA,QAAAM,GAAAF,EAAAC,EAAAL,GACA,GAAAO,KAaA,OAZAN,GAAAG,IACAjQ,OAAAqQ,KAAAJ,GAAArR,QAAA,SAAA0R,GACAF,EAAAE,GAAAV,EAAAK,EAAAK,GAAAT,KAGA7P,OAAAqQ,KAAAH,GAAAtR,QAAA,SAAA0R,GACAR,EAAAI,EAAAI,KAAAL,EAAAK,GAGAF,EAAAE,GAAAP,EAAAE,EAAAK,GAAAJ,EAAAI,GAAAT,GAFAO,EAAAE,GAAAV,EAAAM,EAAAI,GAAAT,KAKAO,EAGA,QAAAL,GAAAE,EAAAC,EAAAL,GACA,GAAAU,GAAAvR,MAAAC,QAAAiR,GACAM,EAAAxR,MAAAC,QAAAgR,GACA5O,EAAAwO,IAAmCY,WAAAT,GACnCU,EAAAH,IAAAC,CAEA,IAAAE,EAEE,IAAAH,EAAA,CACF,GAAAE,GAAApP,EAAAoP,YAAAT,CACA,OAAAS,GAAAR,EAAAC,EAAAL,GAEA,MAAAM,GAAAF,EAAAC,EAAAL,GALA,MAAAD,GAAAM,EAAAL,GAnEA,GAAAC,GAAA,SAAA9a,GACA,MAAAoa,GAAApa,KACAqa,EAAAra,IAgBA2b,EAAA,kBAAAhc,uBACA8a,EAAAkB,EAAAhc,cAAA,sBAyDAob,GAAAa,IAAA,SAAAC,EAAAhB,GACA,IAAA7Q,MAAAC,QAAA4R,GACA,SAAAhO,OAAA,oCAGA,OAAAgO,GAAAvY,OAAA,SAAAwU,EAAAjY,GACA,MAAAkb,GAAAjD,EAAAjY,EAAAgb,QAIA,IAAAiB,GAAAf,CAEA,OAAAe","file":"frappe-gantt.min.js","sourcesContent":["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Gantt\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Gantt\"] = factory();\n\telse\n\t\troot[\"Gantt\"] = factory();\n})(this, function() {\nreturn \n\n\n/** WEBPACK FOOTER **\n ** webpack/universalModuleDefinition\n **/","(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Gantt\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Gantt\"] = factory();\n\telse\n\t\troot[\"Gantt\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId])\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\texports: {},\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tObject.defineProperty(exports, \"__esModule\", {\n\t\tvalue: true\n\t});\n\texports.default = Gantt;\n\t\n\t__webpack_require__(1);\n\t\n\tvar _Bar = __webpack_require__(5);\n\t\n\tvar _Bar2 = _interopRequireDefault(_Bar);\n\t\n\tvar _Arrow = __webpack_require__(6);\n\t\n\tvar _Arrow2 = _interopRequireDefault(_Arrow);\n\t\n\tfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\t\n\tfunction Gantt(element, tasks, config) {\n\t\n\t\tvar self = {};\n\t\n\t\tfunction init() {\n\t\t\tset_defaults();\n\t\n\t\t\t// expose methods\n\t\t\tself.change_view_mode = change_view_mode;\n\t\t\tself.unselect_all = unselect_all;\n\t\t\tself.view_is = view_is;\n\t\t\tself.get_bar = get_bar;\n\t\t\tself.trigger_event = trigger_event;\n\t\t\tself.refresh = refresh;\n\t\n\t\t\t// initialize with default view mode\n\t\t\tchange_view_mode(self.config.view_mode);\n\t\t}\n\t\n\t\tfunction set_defaults() {\n\t\n\t\t\tvar merge = __webpack_require__(7);\n\t\n\t\t\tvar defaults = {\n\t\t\t\theader_height: 50,\n\t\t\t\tcolumn_width: 30,\n\t\t\t\tstep: 24,\n\t\t\t\tview_modes: ['Quarter Day', 'Half Day', 'Day', 'Week', 'Month'],\n\t\t\t\tbar: {\n\t\t\t\t\theight: 20,\n\t\t\t\t\tcorner_radius: 3\n\t\t\t\t},\n\t\t\t\tarrow: {\n\t\t\t\t\tcurve: 5\n\t\t\t\t},\n\t\t\t\tpadding: 18,\n\t\t\t\tview_mode: 'Day',\n\t\t\t\tdate_format: 'YYYY-MM-DD',\n\t\t\t\tcustom_popup_html: null\n\t\t\t};\n\t\t\tself.config = merge(defaults, config);\n\t\n\t\t\treset_variables(tasks);\n\t\t}\n\t\n\t\tfunction reset_variables(tasks) {\n\t\t\tif (typeof element === 'string') {\n\t\t\t\tself.element = document.querySelector(element);\n\t\t\t} else if (element instanceof SVGElement) {\n\t\t\t\tself.element = element;\n\t\t\t} else if (element instanceof HTMLElement) {\n\t\t\t\tself.element = element.querySelector('svg');\n\t\t\t} else {\n\t\t\t\tthrow new TypeError('Frappé Gantt only supports usage of a string CSS selector,' + ' HTML DOM element or SVG DOM element for the \\'element\\' parameter');\n\t\t\t}\n\t\n\t\t\tself._tasks = tasks;\n\t\n\t\t\tself._bars = [];\n\t\t\tself._arrows = [];\n\t\t\tself.element_groups = {};\n\t\t}\n\t\n\t\tfunction refresh(updated_tasks) {\n\t\t\treset_variables(updated_tasks);\n\t\t\tchange_view_mode(self.config.view_mode);\n\t\t}\n\t\n\t\tfunction change_view_mode(mode) {\n\t\t\tset_scale(mode);\n\t\t\tprepare();\n\t\t\trender();\n\t\t\t// fire viewmode_change event\n\t\t\ttrigger_event('view_change', [mode]);\n\t\t}\n\t\n\t\tfunction prepare() {\n\t\t\tprepare_tasks();\n\t\t\tprepare_dependencies();\n\t\t\tprepare_dates();\n\t\t\tprepare_canvas();\n\t\t}\n\t\n\t\tfunction prepare_tasks() {\n\t\n\t\t\t// prepare tasks\n\t\t\tself.tasks = self._tasks.map(function (task, i) {\n\t\n\t\t\t\t// momentify\n\t\t\t\ttask._start = moment(task.start, self.config.date_format);\n\t\t\t\ttask._end = moment(task.end, self.config.date_format);\n\t\n\t\t\t\t// make task invalid if duration too large\n\t\t\t\tif (task._end.diff(task._start, 'years') > 10) {\n\t\t\t\t\ttask.end = null;\n\t\t\t\t}\n\t\n\t\t\t\t// cache index\n\t\t\t\ttask._index = i;\n\t\n\t\t\t\t// invalid dates\n\t\t\t\tif (!task.start && !task.end) {\n\t\t\t\t\ttask._start = moment().startOf('day');\n\t\t\t\t\ttask._end = moment().startOf('day').add(2, 'days');\n\t\t\t\t}\n\t\t\t\tif (!task.start && task.end) {\n\t\t\t\t\ttask._start = task._end.clone().add(-2, 'days');\n\t\t\t\t}\n\t\t\t\tif (task.start && !task.end) {\n\t\t\t\t\ttask._end = task._start.clone().add(2, 'days');\n\t\t\t\t}\n\t\n\t\t\t\t// invalid flag\n\t\t\t\tif (!task.start || !task.end) {\n\t\t\t\t\ttask.invalid = true;\n\t\t\t\t}\n\t\n\t\t\t\t// dependencies\n\t\t\t\tif (typeof task.dependencies === 'string' || !task.dependencies) {\n\t\t\t\t\tvar deps = [];\n\t\t\t\t\tif (task.dependencies) {\n\t\t\t\t\t\tdeps = task.dependencies.split(',').map(function (d) {\n\t\t\t\t\t\t\treturn d.trim();\n\t\t\t\t\t\t}).filter(function (d) {\n\t\t\t\t\t\t\treturn d;\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\ttask.dependencies = deps;\n\t\t\t\t}\n\t\n\t\t\t\t// uids\n\t\t\t\tif (!task.id) {\n\t\t\t\t\ttask.id = generate_id(task);\n\t\t\t\t}\n\t\n\t\t\t\treturn task;\n\t\t\t});\n\t\t}\n\t\n\t\tfunction prepare_dependencies() {\n\t\n\t\t\tself.dependency_map = {};\n\t\t\tvar _iteratorNormalCompletion = true;\n\t\t\tvar _didIteratorError = false;\n\t\t\tvar _iteratorError = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator = self.tasks[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n\t\t\t\t\tvar t = _step.value;\n\t\t\t\t\tvar _iteratorNormalCompletion2 = true;\n\t\t\t\t\tvar _didIteratorError2 = false;\n\t\t\t\t\tvar _iteratorError2 = undefined;\n\t\n\t\t\t\t\ttry {\n\t\t\t\t\t\tfor (var _iterator2 = t.dependencies[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n\t\t\t\t\t\t\tvar d = _step2.value;\n\t\n\t\t\t\t\t\t\tself.dependency_map[d] = self.dependency_map[d] || [];\n\t\t\t\t\t\t\tself.dependency_map[d].push(t.id);\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t_didIteratorError2 = true;\n\t\t\t\t\t\t_iteratorError2 = err;\n\t\t\t\t\t} finally {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tif (!_iteratorNormalCompletion2 && _iterator2.return) {\n\t\t\t\t\t\t\t\t_iterator2.return();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\tif (_didIteratorError2) {\n\t\t\t\t\t\t\t\tthrow _iteratorError2;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError = true;\n\t\t\t\t_iteratorError = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion && _iterator.return) {\n\t\t\t\t\t\t_iterator.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError) {\n\t\t\t\t\t\tthrow _iteratorError;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction prepare_dates() {\n\t\n\t\t\tself.gantt_start = self.gantt_end = null;\n\t\t\tvar _iteratorNormalCompletion3 = true;\n\t\t\tvar _didIteratorError3 = false;\n\t\t\tvar _iteratorError3 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator3 = self.tasks[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {\n\t\t\t\t\tvar task = _step3.value;\n\t\n\t\t\t\t\t// set global start and end date\n\t\t\t\t\tif (!self.gantt_start || task._start < self.gantt_start) {\n\t\t\t\t\t\tself.gantt_start = task._start;\n\t\t\t\t\t}\n\t\t\t\t\tif (!self.gantt_end || task._end > self.gantt_end) {\n\t\t\t\t\t\tself.gantt_end = task._end;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError3 = true;\n\t\t\t\t_iteratorError3 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion3 && _iterator3.return) {\n\t\t\t\t\t\t_iterator3.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError3) {\n\t\t\t\t\t\tthrow _iteratorError3;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\tset_gantt_dates();\n\t\t\tsetup_dates();\n\t\t}\n\t\n\t\tfunction prepare_canvas() {\n\t\t\tif (self.canvas) return;\n\t\t\tself.canvas = Snap(self.element).addClass('gantt');\n\t\t}\n\t\n\t\tfunction render() {\n\t\t\tclear();\n\t\t\tsetup_groups();\n\t\t\tmake_grid();\n\t\t\tmake_dates();\n\t\t\tmake_bars();\n\t\t\tmake_arrows();\n\t\t\tmap_arrows_on_bars();\n\t\t\tset_width();\n\t\t\tset_scroll_position();\n\t\t\tbind_grid_click();\n\t\t}\n\t\n\t\tfunction clear() {\n\t\t\tself.canvas.clear();\n\t\t\tself._bars = [];\n\t\t\tself._arrows = [];\n\t\t}\n\t\n\t\tfunction set_gantt_dates() {\n\t\n\t\t\tif (view_is(['Quarter Day', 'Half Day'])) {\n\t\t\t\tself.gantt_start = self.gantt_start.clone().subtract(7, 'day');\n\t\t\t\tself.gantt_end = self.gantt_end.clone().add(7, 'day');\n\t\t\t} else if (view_is('Month')) {\n\t\t\t\tself.gantt_start = self.gantt_start.clone().startOf('year');\n\t\t\t\tself.gantt_end = self.gantt_end.clone().endOf('month').add(1, 'year');\n\t\t\t} else {\n\t\t\t\tself.gantt_start = self.gantt_start.clone().startOf('month').subtract(1, 'month');\n\t\t\t\tself.gantt_end = self.gantt_end.clone().endOf('month').add(1, 'month');\n\t\t\t}\n\t\t}\n\t\n\t\tfunction setup_dates() {\n\t\n\t\t\tself.dates = [];\n\t\t\tvar cur_date = null;\n\t\n\t\t\twhile (cur_date === null || cur_date < self.gantt_end) {\n\t\t\t\tif (!cur_date) {\n\t\t\t\t\tcur_date = self.gantt_start.clone();\n\t\t\t\t} else {\n\t\t\t\t\tcur_date = view_is('Month') ? cur_date.clone().add(1, 'month') : cur_date.clone().add(self.config.step, 'hours');\n\t\t\t\t}\n\t\t\t\tself.dates.push(cur_date);\n\t\t\t}\n\t\t}\n\t\n\t\tfunction setup_groups() {\n\t\n\t\t\tvar groups = ['grid', 'date', 'arrow', 'progress', 'bar', 'details'];\n\t\t\t// make group layers\n\t\t\tvar _iteratorNormalCompletion4 = true;\n\t\t\tvar _didIteratorError4 = false;\n\t\t\tvar _iteratorError4 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator4 = groups[Symbol.iterator](), _step4; !(_iteratorNormalCompletion4 = (_step4 = _iterator4.next()).done); _iteratorNormalCompletion4 = true) {\n\t\t\t\t\tvar group = _step4.value;\n\t\n\t\t\t\t\tself.element_groups[group] = self.canvas.group().attr({ 'id': group });\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError4 = true;\n\t\t\t\t_iteratorError4 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion4 && _iterator4.return) {\n\t\t\t\t\t\t_iterator4.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError4) {\n\t\t\t\t\t\tthrow _iteratorError4;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction set_scale(scale) {\n\t\t\tself.config.view_mode = scale;\n\t\n\t\t\tif (scale === 'Day') {\n\t\t\t\tself.config.step = 24;\n\t\t\t\tself.config.column_width = 38;\n\t\t\t} else if (scale === 'Half Day') {\n\t\t\t\tself.config.step = 24 / 2;\n\t\t\t\tself.config.column_width = 38;\n\t\t\t} else if (scale === 'Quarter Day') {\n\t\t\t\tself.config.step = 24 / 4;\n\t\t\t\tself.config.column_width = 38;\n\t\t\t} else if (scale === 'Week') {\n\t\t\t\tself.config.step = 24 * 7;\n\t\t\t\tself.config.column_width = 140;\n\t\t\t} else if (scale === 'Month') {\n\t\t\t\tself.config.step = 24 * 30;\n\t\t\t\tself.config.column_width = 120;\n\t\t\t}\n\t\t}\n\t\n\t\tfunction set_width() {\n\t\t\tvar cur_width = self.canvas.node.getBoundingClientRect().width;\n\t\t\tvar actual_width = self.canvas.select('#grid .grid-row').attr('width');\n\t\t\tif (cur_width < actual_width) {\n\t\t\t\tself.canvas.attr('width', actual_width);\n\t\t\t}\n\t\t}\n\t\n\t\tfunction set_scroll_position() {\n\t\t\tvar parent_element = self.element.parentElement;\n\t\n\t\t\tif (!parent_element) return;\n\t\n\t\t\tvar scroll_pos = get_min_date().diff(self.gantt_start, 'hours') / self.config.step * self.config.column_width - self.config.column_width;\n\t\t\tparent_element.scrollLeft = scroll_pos;\n\t\t}\n\t\n\t\tfunction get_min_date() {\n\t\t\tvar task = self.tasks.reduce(function (acc, curr) {\n\t\t\t\treturn curr._start.isSameOrBefore(acc._start) ? curr : acc;\n\t\t\t});\n\t\t\treturn task._start;\n\t\t}\n\t\n\t\tfunction make_grid() {\n\t\t\tmake_grid_background();\n\t\t\tmake_grid_rows();\n\t\t\tmake_grid_header();\n\t\t\tmake_grid_ticks();\n\t\t\tmake_grid_highlights();\n\t\t}\n\t\n\t\tfunction make_grid_background() {\n\t\n\t\t\tvar grid_width = self.dates.length * self.config.column_width,\n\t\t\t    grid_height = self.config.header_height + self.config.padding + (self.config.bar.height + self.config.padding) * self.tasks.length;\n\t\n\t\t\tself.canvas.rect(0, 0, grid_width, grid_height).addClass('grid-background').appendTo(self.element_groups.grid);\n\t\n\t\t\tself.canvas.attr({\n\t\t\t\theight: grid_height + self.config.padding + 100,\n\t\t\t\twidth: '100%'\n\t\t\t});\n\t\t}\n\t\n\t\tfunction make_grid_header() {\n\t\t\tvar header_width = self.dates.length * self.config.column_width,\n\t\t\t    header_height = self.config.header_height + 10;\n\t\t\tself.canvas.rect(0, 0, header_width, header_height).addClass('grid-header').appendTo(self.element_groups.grid);\n\t\t}\n\t\n\t\tfunction make_grid_rows() {\n\t\n\t\t\tvar rows = self.canvas.group().appendTo(self.element_groups.grid),\n\t\t\t    lines = self.canvas.group().appendTo(self.element_groups.grid),\n\t\t\t    row_width = self.dates.length * self.config.column_width,\n\t\t\t    row_height = self.config.bar.height + self.config.padding;\n\t\n\t\t\tvar row_y = self.config.header_height + self.config.padding / 2;\n\t\n\t\t\tvar _iteratorNormalCompletion5 = true;\n\t\t\tvar _didIteratorError5 = false;\n\t\t\tvar _iteratorError5 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator5 = self.tasks[Symbol.iterator](), _step5; !(_iteratorNormalCompletion5 = (_step5 = _iterator5.next()).done); _iteratorNormalCompletion5 = true) {\n\t\t\t\t\tvar task = _step5.value;\n\t\t\t\t\t// eslint-disable-line\n\t\t\t\t\tself.canvas.rect(0, row_y, row_width, row_height).addClass('grid-row').appendTo(rows);\n\t\n\t\t\t\t\tself.canvas.line(0, row_y + row_height, row_width, row_y + row_height).addClass('row-line').appendTo(lines);\n\t\n\t\t\t\t\trow_y += self.config.bar.height + self.config.padding;\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError5 = true;\n\t\t\t\t_iteratorError5 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion5 && _iterator5.return) {\n\t\t\t\t\t\t_iterator5.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError5) {\n\t\t\t\t\t\tthrow _iteratorError5;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction make_grid_ticks() {\n\t\t\tvar tick_x = 0,\n\t\t\t    tick_y = self.config.header_height + self.config.padding / 2,\n\t\t\t    tick_height = (self.config.bar.height + self.config.padding) * self.tasks.length;\n\t\n\t\t\tvar _iteratorNormalCompletion6 = true;\n\t\t\tvar _didIteratorError6 = false;\n\t\t\tvar _iteratorError6 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator6 = self.dates[Symbol.iterator](), _step6; !(_iteratorNormalCompletion6 = (_step6 = _iterator6.next()).done); _iteratorNormalCompletion6 = true) {\n\t\t\t\t\tvar date = _step6.value;\n\t\n\t\t\t\t\tvar tick_class = 'tick';\n\t\t\t\t\t// thick tick for monday\n\t\t\t\t\tif (view_is('Day') && date.day() === 1) {\n\t\t\t\t\t\ttick_class += ' thick';\n\t\t\t\t\t}\n\t\t\t\t\t// thick tick for first week\n\t\t\t\t\tif (view_is('Week') && date.date() >= 1 && date.date() < 8) {\n\t\t\t\t\t\ttick_class += ' thick';\n\t\t\t\t\t}\n\t\t\t\t\t// thick ticks for quarters\n\t\t\t\t\tif (view_is('Month') && date.month() % 3 === 0) {\n\t\t\t\t\t\ttick_class += ' thick';\n\t\t\t\t\t}\n\t\n\t\t\t\t\tself.canvas.path(Snap.format('M {x} {y} v {height}', {\n\t\t\t\t\t\tx: tick_x,\n\t\t\t\t\t\ty: tick_y,\n\t\t\t\t\t\theight: tick_height\n\t\t\t\t\t})).addClass(tick_class).appendTo(self.element_groups.grid);\n\t\n\t\t\t\t\tif (view_is('Month')) {\n\t\t\t\t\t\ttick_x += date.daysInMonth() * self.config.column_width / 30;\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttick_x += self.config.column_width;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError6 = true;\n\t\t\t\t_iteratorError6 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion6 && _iterator6.return) {\n\t\t\t\t\t\t_iterator6.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError6) {\n\t\t\t\t\t\tthrow _iteratorError6;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction make_grid_highlights() {\n\t\n\t\t\t// highlight today's date\n\t\t\tif (view_is('Day')) {\n\t\t\t\tvar x = moment().startOf('day').diff(self.gantt_start, 'hours') / self.config.step * self.config.column_width;\n\t\t\t\tvar y = 0;\n\t\t\t\tvar width = self.config.column_width;\n\t\t\t\tvar height = (self.config.bar.height + self.config.padding) * self.tasks.length + self.config.header_height + self.config.padding / 2;\n\t\n\t\t\t\tself.canvas.rect(x, y, width, height).addClass('today-highlight').appendTo(self.element_groups.grid);\n\t\t\t}\n\t\t}\n\t\n\t\tfunction make_dates() {\n\t\t\tvar _iteratorNormalCompletion7 = true;\n\t\t\tvar _didIteratorError7 = false;\n\t\t\tvar _iteratorError7 = undefined;\n\t\n\t\t\ttry {\n\t\n\t\t\t\tfor (var _iterator7 = get_dates_to_draw()[Symbol.iterator](), _step7; !(_iteratorNormalCompletion7 = (_step7 = _iterator7.next()).done); _iteratorNormalCompletion7 = true) {\n\t\t\t\t\tvar date = _step7.value;\n\t\n\t\t\t\t\tself.canvas.text(date.lower_x, date.lower_y, date.lower_text).addClass('lower-text').appendTo(self.element_groups.date);\n\t\n\t\t\t\t\tif (date.upper_text) {\n\t\t\t\t\t\tvar $upper_text = self.canvas.text(date.upper_x, date.upper_y, date.upper_text).addClass('upper-text').appendTo(self.element_groups.date);\n\t\n\t\t\t\t\t\t// remove out-of-bound dates\n\t\t\t\t\t\tif ($upper_text.getBBox().x2 > self.element_groups.grid.getBBox().width) {\n\t\t\t\t\t\t\t$upper_text.remove();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError7 = true;\n\t\t\t\t_iteratorError7 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion7 && _iterator7.return) {\n\t\t\t\t\t\t_iterator7.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError7) {\n\t\t\t\t\t\tthrow _iteratorError7;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction get_dates_to_draw() {\n\t\t\tvar last_date = null;\n\t\t\tvar dates = self.dates.map(function (date, i) {\n\t\t\t\tvar d = get_date_info(date, last_date, i);\n\t\t\t\tlast_date = date;\n\t\t\t\treturn d;\n\t\t\t});\n\t\t\treturn dates;\n\t\t}\n\t\n\t\tfunction get_date_info(date, last_date, i) {\n\t\t\tif (!last_date) {\n\t\t\t\tlast_date = date.clone().add(1, 'year');\n\t\t\t}\n\t\t\tvar date_text = {\n\t\t\t\t'Quarter Day_lower': date.format('HH'),\n\t\t\t\t'Half Day_lower': date.format('HH'),\n\t\t\t\t'Day_lower': date.date() !== last_date.date() ? date.format('D') : '',\n\t\t\t\t'Week_lower': date.month() !== last_date.month() ? date.format('D MMM') : date.format('D'),\n\t\t\t\t'Month_lower': date.format('MMMM'),\n\t\t\t\t'Quarter Day_upper': date.date() !== last_date.date() ? date.format('D MMM') : '',\n\t\t\t\t'Half Day_upper': date.date() !== last_date.date() ? date.month() !== last_date.month() ? date.format('D MMM') : date.format('D') : '',\n\t\t\t\t'Day_upper': date.month() !== last_date.month() ? date.format('MMMM') : '',\n\t\t\t\t'Week_upper': date.month() !== last_date.month() ? date.format('MMMM') : '',\n\t\t\t\t'Month_upper': date.year() !== last_date.year() ? date.format('YYYY') : ''\n\t\t\t};\n\t\n\t\t\tvar base_pos = {\n\t\t\t\tx: i * self.config.column_width,\n\t\t\t\tlower_y: self.config.header_height,\n\t\t\t\tupper_y: self.config.header_height - 25\n\t\t\t};\n\t\n\t\t\tvar x_pos = {\n\t\t\t\t'Quarter Day_lower': self.config.column_width * 4 / 2,\n\t\t\t\t'Quarter Day_upper': 0,\n\t\t\t\t'Half Day_lower': self.config.column_width * 2 / 2,\n\t\t\t\t'Half Day_upper': 0,\n\t\t\t\t'Day_lower': self.config.column_width / 2,\n\t\t\t\t'Day_upper': self.config.column_width * 30 / 2,\n\t\t\t\t'Week_lower': 0,\n\t\t\t\t'Week_upper': self.config.column_width * 4 / 2,\n\t\t\t\t'Month_lower': self.config.column_width / 2,\n\t\t\t\t'Month_upper': self.config.column_width * 12 / 2\n\t\t\t};\n\t\n\t\t\treturn {\n\t\t\t\tupper_text: date_text[self.config.view_mode + '_upper'],\n\t\t\t\tlower_text: date_text[self.config.view_mode + '_lower'],\n\t\t\t\tupper_x: base_pos.x + x_pos[self.config.view_mode + '_upper'],\n\t\t\t\tupper_y: base_pos.upper_y,\n\t\t\t\tlower_x: base_pos.x + x_pos[self.config.view_mode + '_lower'],\n\t\t\t\tlower_y: base_pos.lower_y\n\t\t\t};\n\t\t}\n\t\n\t\tfunction make_arrows() {\n\t\t\tself._arrows = [];\n\t\t\tvar _iteratorNormalCompletion8 = true;\n\t\t\tvar _didIteratorError8 = false;\n\t\t\tvar _iteratorError8 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tvar _loop = function _loop() {\n\t\t\t\t\tvar task = _step8.value;\n\t\n\t\t\t\t\tvar arrows = [];\n\t\t\t\t\tarrows = task.dependencies.map(function (dep) {\n\t\t\t\t\t\tvar dependency = get_task(dep);\n\t\t\t\t\t\tif (!dependency) return;\n\t\n\t\t\t\t\t\tvar arrow = (0, _Arrow2.default)(self, // gt\n\t\t\t\t\t\tself._bars[dependency._index], // from_task\n\t\t\t\t\t\tself._bars[task._index] // to_task\n\t\t\t\t\t\t);\n\t\t\t\t\t\tself.element_groups.arrow.add(arrow.element);\n\t\t\t\t\t\treturn arrow; // eslint-disable-line\n\t\t\t\t\t}).filter(function (arr) {\n\t\t\t\t\t\treturn arr;\n\t\t\t\t\t}); // filter falsy values\n\t\t\t\t\tself._arrows = self._arrows.concat(arrows);\n\t\t\t\t};\n\t\n\t\t\t\tfor (var _iterator8 = self.tasks[Symbol.iterator](), _step8; !(_iteratorNormalCompletion8 = (_step8 = _iterator8.next()).done); _iteratorNormalCompletion8 = true) {\n\t\t\t\t\t_loop();\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError8 = true;\n\t\t\t\t_iteratorError8 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion8 && _iterator8.return) {\n\t\t\t\t\t\t_iterator8.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError8) {\n\t\t\t\t\t\tthrow _iteratorError8;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction make_bars() {\n\t\n\t\t\tself._bars = self.tasks.map(function (task) {\n\t\t\t\tvar bar = (0, _Bar2.default)(self, task);\n\t\t\t\tself.element_groups.bar.add(bar.group);\n\t\t\t\treturn bar;\n\t\t\t});\n\t\t}\n\t\n\t\tfunction map_arrows_on_bars() {\n\t\t\tvar _iteratorNormalCompletion9 = true;\n\t\t\tvar _didIteratorError9 = false;\n\t\t\tvar _iteratorError9 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tvar _loop2 = function _loop2() {\n\t\t\t\t\tvar bar = _step9.value;\n\t\n\t\t\t\t\tbar.arrows = self._arrows.filter(function (arrow) {\n\t\t\t\t\t\treturn arrow.from_task.task.id === bar.task.id || arrow.to_task.task.id === bar.task.id;\n\t\t\t\t\t});\n\t\t\t\t};\n\t\n\t\t\t\tfor (var _iterator9 = self._bars[Symbol.iterator](), _step9; !(_iteratorNormalCompletion9 = (_step9 = _iterator9.next()).done); _iteratorNormalCompletion9 = true) {\n\t\t\t\t\t_loop2();\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError9 = true;\n\t\t\t\t_iteratorError9 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion9 && _iterator9.return) {\n\t\t\t\t\t\t_iterator9.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError9) {\n\t\t\t\t\t\tthrow _iteratorError9;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction bind_grid_click() {\n\t\t\tself.element_groups.grid.click(function () {\n\t\t\t\tunselect_all();\n\t\t\t\tself.element_groups.details.selectAll('.details-wrapper').forEach(function (el) {\n\t\t\t\t\treturn el.addClass('hide');\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\t\n\t\tfunction unselect_all() {\n\t\t\tself.canvas.selectAll('.bar-wrapper').forEach(function (el) {\n\t\t\t\tel.removeClass('active');\n\t\t\t});\n\t\t}\n\t\n\t\tfunction view_is(modes) {\n\t\t\tif (typeof modes === 'string') {\n\t\t\t\treturn self.config.view_mode === modes;\n\t\t\t} else if (Array.isArray(modes)) {\n\t\t\t\tvar _iteratorNormalCompletion10 = true;\n\t\t\t\tvar _didIteratorError10 = false;\n\t\t\t\tvar _iteratorError10 = undefined;\n\t\n\t\t\t\ttry {\n\t\t\t\t\tfor (var _iterator10 = modes[Symbol.iterator](), _step10; !(_iteratorNormalCompletion10 = (_step10 = _iterator10.next()).done); _iteratorNormalCompletion10 = true) {\n\t\t\t\t\t\tvar mode = _step10.value;\n\t\n\t\t\t\t\t\tif (self.config.view_mode === mode) return true;\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\t_didIteratorError10 = true;\n\t\t\t\t\t_iteratorError10 = err;\n\t\t\t\t} finally {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tif (!_iteratorNormalCompletion10 && _iterator10.return) {\n\t\t\t\t\t\t\t_iterator10.return();\n\t\t\t\t\t\t}\n\t\t\t\t\t} finally {\n\t\t\t\t\t\tif (_didIteratorError10) {\n\t\t\t\t\t\t\tthrow _iteratorError10;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\t\n\t\tfunction get_task(id) {\n\t\t\treturn self.tasks.find(function (task) {\n\t\t\t\treturn task.id === id;\n\t\t\t});\n\t\t}\n\t\n\t\tfunction get_bar(id) {\n\t\t\treturn self._bars.find(function (bar) {\n\t\t\t\treturn bar.task.id === id;\n\t\t\t});\n\t\t}\n\t\n\t\tfunction generate_id(task) {\n\t\t\treturn task.name + '_' + Math.random().toString(36).slice(2, 12);\n\t\t}\n\t\n\t\tfunction trigger_event(event, args) {\n\t\t\tif (self.config['on_' + event]) {\n\t\t\t\tself.config['on_' + event].apply(null, args);\n\t\t\t}\n\t\t}\n\t\n\t\tinit();\n\t\n\t\treturn self;\n\t} /* global moment, Snap */\n\t/**\n\t * Gantt:\n\t * \telement: querySelector string, HTML DOM or SVG DOM element, required\n\t * \ttasks: array of tasks, required\n\t *   task: { id, name, start, end, progress, dependencies, custom_class }\n\t * \tconfig: configuration options, optional\n\t */\n\tmodule.exports = exports['default'];\n\n/***/ },\n/* 1 */\n/***/ function(module, exports, __webpack_require__) {\n\n\t// style-loader: Adds some css to the DOM by adding a <style> tag\n\t\n\t// load the styles\n\tvar content = __webpack_require__(2);\n\tif(typeof content === 'string') content = [[module.id, content, '']];\n\t// add the styles to the DOM\n\tvar update = __webpack_require__(4)(content, {});\n\tif(content.locals) module.exports = content.locals;\n\t// Hot Module Replacement\n\tif(false) {\n\t\t// When the styles change, update the <style> tags\n\t\tif(!content.locals) {\n\t\t\tmodule.hot.accept(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\", function() {\n\t\t\t\tvar newContent = require(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\");\n\t\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\t\tupdate(newContent);\n\t\t\t});\n\t\t}\n\t\t// When the module is disposed, remove the <style> tags\n\t\tmodule.hot.dispose(function() { update(); });\n\t}\n\n/***/ },\n/* 2 */\n/***/ function(module, exports, __webpack_require__) {\n\n\texports = module.exports = __webpack_require__(3)();\n\t// imports\n\t\n\t\n\t// module\n\texports.push([module.id, \".gantt .grid-background{fill:none}.gantt .grid-header{fill:#fff;stroke:#e0e0e0;stroke-width:1.4}.gantt .grid-row{fill:#fff}.gantt .grid-row:nth-child(2n){fill:#f5f5f5}.gantt .row-line{stroke:#ebeff2}.gantt .tick{stroke:#e0e0e0;stroke-width:.2}.gantt .tick.thick{stroke-width:.4}.gantt .today-highlight{fill:#fcf8e3;opacity:.5}.gantt #arrow{fill:none;stroke:#666;stroke-width:1.4}.gantt .bar{fill:#b8c2cc;stroke:#8d99a6;stroke-width:0;transition:stroke-width .3s ease}.gantt .bar-progress{fill:#a3a3ff}.gantt .bar-invalid{fill:transparent;stroke:#8d99a6;stroke-width:1;stroke-dasharray:5}.gantt .bar-invalid~.bar-label{fill:#555}.gantt .bar-label{fill:#fff;dominant-baseline:central;text-anchor:middle;font-size:12px;font-weight:lighter}.gantt .bar-label.big{fill:#555;text-anchor:start}.gantt .handle{fill:#ddd;cursor:ew-resize;opacity:0;visibility:hidden;transition:opacity .3s ease}.gantt .bar-wrapper{cursor:pointer}.gantt .bar-wrapper:hover .bar{stroke-width:2}.gantt .bar-wrapper:hover .handle{visibility:visible;opacity:1}.gantt .bar-wrapper.active .bar{stroke-width:2}.gantt .lower-text,.gantt .upper-text{font-size:12px;text-anchor:middle}.gantt .upper-text{fill:#555}.gantt .lower-text{fill:#333}.gantt #details .details-container{background:#fff;display:inline-block;padding:12px}.gantt #details .details-container h5,.gantt #details .details-container p{margin:0}.gantt #details .details-container h5{font-size:12px;font-weight:700;margin-bottom:10px;color:#555}.gantt #details .details-container p{font-size:12px;margin-bottom:6px;color:#666}.gantt #details .details-container p:last-child{margin-bottom:0}.gantt .hide{display:none}\", \"\", {\"version\":3,\"sources\":[\"/Users/<USER>/WebstormProjects/gantt/src/src/gantt.scss\"],\"names\":[],\"mappings\":\"AAYA,wBAGE,SAAU,CAHZ,oBAME,UACA,eACA,gBAAiB,CARnB,iBAWE,SAAa,CAXf,+BAcE,YAvBgB,CASlB,iBAiBE,cAzB0B,CAQ5B,aAoBE,eACA,eAAiB,CArBnB,mBAuBG,eAAiB,CAvBpB,wBA2BE,aACA,UAAY,CA5Bd,cAgCE,UACA,YACA,gBAAiB,CAlCnB,YAsCE,aACA,eACA,eACA,gCAAiC,CAzCnC,qBA4CE,YA/CY,CAGd,oBA+CE,iBACA,eACA,eACA,kBAAmB,CAlDrB,+BAqDG,SA1Dc,CAKjB,kBAyDE,UACA,0BACA,mBACA,eACA,mBAAoB,CA7DtB,sBAgEG,UACA,iBAAkB,CAjErB,eAsEE,UACA,iBACA,UACA,kBACA,2BAA4B,CA1E9B,oBA8EE,cAAe,CA9EjB,+BAkFI,cAAe,CAlFnB,kCAsFI,mBACA,SAAU,CAvFd,gCA6FI,cAAe,CA7FnB,sCAmGE,eACA,kBAAmB,CApGrB,mBAuGE,SA5Ge,CAKjB,mBA0GE,SA9Ge,CAIjB,mCA8GE,gBACA,qBACA,YAAa,CAhHf,2EAmHG,QAAS,CAnHZ,sCAuHG,eACA,gBACA,mBACA,UA/Hc,CAKjB,qCA8HG,eACA,kBACA,UAtIc,CAMjB,gDAoIG,eAAgB,CApInB,aAyIE,YAAa,CACb\",\"file\":\"gantt.scss\",\"sourcesContent\":[\"$bar-color: #b8c2cc;\\n$bar-stroke: #8D99A6;\\n$border-color: #e0e0e0;\\n$light-bg: #f5f5f5;\\n$light-border-color: #ebeff2;\\n$light-yellow: #fcf8e3;\\n$text-muted: #666;\\n$text-light: #555;\\n$text-color: #333;\\n$blue: #a3a3ff;\\n$handle-color: #ddd;\\n\\n.gantt {\\n\\n\\t.grid-background {\\n\\t\\tfill: none;\\n\\t}\\n\\t.grid-header {\\n\\t\\tfill: #ffffff;\\n\\t\\tstroke: $border-color;\\n\\t\\tstroke-width: 1.4;\\n\\t}\\n\\t.grid-row {\\n\\t\\tfill: #ffffff;\\n\\t}\\n\\t.grid-row:nth-child(even) {\\n\\t\\tfill: $light-bg;\\n\\t}\\n\\t.row-line {\\n\\t\\tstroke: $light-border-color;\\n\\t}\\n\\t.tick {\\n\\t\\tstroke: $border-color;\\n\\t\\tstroke-width: 0.2;\\n\\t\\t&.thick {\\n\\t\\t\\tstroke-width: 0.4;\\n\\t\\t}\\n\\t}\\n\\t.today-highlight {\\n\\t\\tfill: $light-yellow;\\n\\t\\topacity: 0.5;\\n\\t}\\n\\n\\t#arrow {\\n\\t\\tfill: none;\\n\\t\\tstroke: $text-muted;\\n\\t\\tstroke-width: 1.4;\\n\\t}\\n\\n\\t.bar {\\n\\t\\tfill: $bar-color;\\n\\t\\tstroke: $bar-stroke;\\n\\t\\tstroke-width: 0;\\n\\t\\ttransition: stroke-width .3s ease;\\n\\t}\\n\\t.bar-progress {\\n\\t\\tfill: $blue;\\n\\t}\\n\\t.bar-invalid {\\n\\t\\tfill: transparent;\\n\\t\\tstroke: $bar-stroke;\\n\\t\\tstroke-width: 1;\\n\\t\\tstroke-dasharray: 5;\\n\\n\\t\\t&~.bar-label {\\n\\t\\t\\tfill: $text-light;\\n\\t\\t}\\n\\t}\\n\\t.bar-label {\\n\\t\\tfill: #fff;\\n\\t\\tdominant-baseline: central;\\n\\t\\ttext-anchor: middle;\\n\\t\\tfont-size: 12px;\\n\\t\\tfont-weight: lighter;\\n\\n\\t\\t&.big {\\n\\t\\t\\tfill: $text-light;\\n\\t\\t\\ttext-anchor: start;\\n\\t\\t}\\n\\t}\\n\\n\\t.handle {\\n\\t\\tfill: $handle-color;\\n\\t\\tcursor: ew-resize;\\n\\t\\topacity: 0;\\n\\t\\tvisibility: hidden;\\n\\t\\ttransition: opacity .3s ease;\\n\\t}\\n\\n\\t.bar-wrapper {\\n\\t\\tcursor: pointer;\\n\\n\\t\\t&:hover {\\n\\t\\t\\t.bar {\\n\\t\\t\\t\\tstroke-width: 2;\\n\\t\\t\\t}\\n\\n\\t\\t\\t.handle {\\n\\t\\t\\t\\tvisibility: visible;\\n\\t\\t\\t\\topacity: 1;\\n\\t\\t\\t}\\n\\t\\t}\\n\\n\\t\\t&.active {\\n\\t\\t\\t.bar {\\n\\t\\t\\t\\tstroke-width: 2;\\n\\t\\t\\t}\\n\\t\\t}\\n\\t}\\n\\n\\t.lower-text, .upper-text {\\n\\t\\tfont-size: 12px;\\n\\t\\ttext-anchor: middle;\\n\\t}\\n\\t.upper-text {\\n\\t\\tfill: $text-light;\\n\\t}\\n\\t.lower-text {\\n\\t\\tfill: $text-color;\\n\\t}\\n\\n\\t#details .details-container {\\n\\t\\tbackground: #fff;\\n\\t\\tdisplay: inline-block;\\n\\t\\tpadding: 12px;\\n\\n\\t\\th5, p {\\n\\t\\t\\tmargin: 0;\\n\\t\\t}\\n\\n\\t\\th5 {\\n\\t\\t\\tfont-size: 12px;\\n\\t\\t\\tfont-weight: bold;\\n\\t\\t\\tmargin-bottom: 10px;\\n\\t\\t\\tcolor: $text-light;\\n\\t\\t}\\n\\n\\t\\tp {\\n\\t\\t\\tfont-size: 12px;\\n\\t\\t\\tmargin-bottom: 6px;\\n\\t\\t\\tcolor: $text-muted;\\n\\t\\t}\\n\\n\\t\\tp:last-child {\\n\\t\\t\\tmargin-bottom: 0;\\n\\t\\t}\\n\\t}\\n\\n\\t.hide {\\n\\t\\tdisplay: none;\\n\\t}\\n}\\n\"],\"sourceRoot\":\"\"}]);\n\t\n\t// exports\n\n\n/***/ },\n/* 3 */\n/***/ function(module, exports) {\n\n\t/*\n\t\tMIT License http://www.opensource.org/licenses/mit-license.php\n\t\tAuthor Tobias Koppers @sokra\n\t*/\n\t// css base code, injected by the css-loader\n\tmodule.exports = function() {\n\t\tvar list = [];\n\t\n\t\t// return the list of modules as css string\n\t\tlist.toString = function toString() {\n\t\t\tvar result = [];\n\t\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\t\tvar item = this[i];\n\t\t\t\tif(item[2]) {\n\t\t\t\t\tresult.push(\"@media \" + item[2] + \"{\" + item[1] + \"}\");\n\t\t\t\t} else {\n\t\t\t\t\tresult.push(item[1]);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn result.join(\"\");\n\t\t};\n\t\n\t\t// import a list of modules into the list\n\t\tlist.i = function(modules, mediaQuery) {\n\t\t\tif(typeof modules === \"string\")\n\t\t\t\tmodules = [[null, modules, \"\"]];\n\t\t\tvar alreadyImportedModules = {};\n\t\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\t\tvar id = this[i][0];\n\t\t\t\tif(typeof id === \"number\")\n\t\t\t\t\talreadyImportedModules[id] = true;\n\t\t\t}\n\t\t\tfor(i = 0; i < modules.length; i++) {\n\t\t\t\tvar item = modules[i];\n\t\t\t\t// skip already imported module\n\t\t\t\t// this implementation is not 100% perfect for weird media query combinations\n\t\t\t\t//  when a module is imported multiple times with different media queries.\n\t\t\t\t//  I hope this will never occur (Hey this way we have smaller bundles)\n\t\t\t\tif(typeof item[0] !== \"number\" || !alreadyImportedModules[item[0]]) {\n\t\t\t\t\tif(mediaQuery && !item[2]) {\n\t\t\t\t\t\titem[2] = mediaQuery;\n\t\t\t\t\t} else if(mediaQuery) {\n\t\t\t\t\t\titem[2] = \"(\" + item[2] + \") and (\" + mediaQuery + \")\";\n\t\t\t\t\t}\n\t\t\t\t\tlist.push(item);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\treturn list;\n\t};\n\n\n/***/ },\n/* 4 */\n/***/ function(module, exports, __webpack_require__) {\n\n\t/*\n\t\tMIT License http://www.opensource.org/licenses/mit-license.php\n\t\tAuthor Tobias Koppers @sokra\n\t*/\n\tvar stylesInDom = {},\n\t\tmemoize = function(fn) {\n\t\t\tvar memo;\n\t\t\treturn function () {\n\t\t\t\tif (typeof memo === \"undefined\") memo = fn.apply(this, arguments);\n\t\t\t\treturn memo;\n\t\t\t};\n\t\t},\n\t\tisOldIE = memoize(function() {\n\t\t\treturn /msie [6-9]\\b/.test(self.navigator.userAgent.toLowerCase());\n\t\t}),\n\t\tgetHeadElement = memoize(function () {\n\t\t\treturn document.head || document.getElementsByTagName(\"head\")[0];\n\t\t}),\n\t\tsingletonElement = null,\n\t\tsingletonCounter = 0,\n\t\tstyleElementsInsertedAtTop = [];\n\t\n\tmodule.exports = function(list, options) {\n\t\tif(false) {\n\t\t\tif(typeof document !== \"object\") throw new Error(\"The style-loader cannot be used in a non-browser environment\");\n\t\t}\n\t\n\t\toptions = options || {};\n\t\t// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n\t\t// tags it will allow on a page\n\t\tif (typeof options.singleton === \"undefined\") options.singleton = isOldIE();\n\t\n\t\t// By default, add <style> tags to the bottom of <head>.\n\t\tif (typeof options.insertAt === \"undefined\") options.insertAt = \"bottom\";\n\t\n\t\tvar styles = listToStyles(list);\n\t\taddStylesToDom(styles, options);\n\t\n\t\treturn function update(newList) {\n\t\t\tvar mayRemove = [];\n\t\t\tfor(var i = 0; i < styles.length; i++) {\n\t\t\t\tvar item = styles[i];\n\t\t\t\tvar domStyle = stylesInDom[item.id];\n\t\t\t\tdomStyle.refs--;\n\t\t\t\tmayRemove.push(domStyle);\n\t\t\t}\n\t\t\tif(newList) {\n\t\t\t\tvar newStyles = listToStyles(newList);\n\t\t\t\taddStylesToDom(newStyles, options);\n\t\t\t}\n\t\t\tfor(var i = 0; i < mayRemove.length; i++) {\n\t\t\t\tvar domStyle = mayRemove[i];\n\t\t\t\tif(domStyle.refs === 0) {\n\t\t\t\t\tfor(var j = 0; j < domStyle.parts.length; j++)\n\t\t\t\t\t\tdomStyle.parts[j]();\n\t\t\t\t\tdelete stylesInDom[domStyle.id];\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t}\n\t\n\tfunction addStylesToDom(styles, options) {\n\t\tfor(var i = 0; i < styles.length; i++) {\n\t\t\tvar item = styles[i];\n\t\t\tvar domStyle = stylesInDom[item.id];\n\t\t\tif(domStyle) {\n\t\t\t\tdomStyle.refs++;\n\t\t\t\tfor(var j = 0; j < domStyle.parts.length; j++) {\n\t\t\t\t\tdomStyle.parts[j](item.parts[j]);\n\t\t\t\t}\n\t\t\t\tfor(; j < item.parts.length; j++) {\n\t\t\t\t\tdomStyle.parts.push(addStyle(item.parts[j], options));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tvar parts = [];\n\t\t\t\tfor(var j = 0; j < item.parts.length; j++) {\n\t\t\t\t\tparts.push(addStyle(item.parts[j], options));\n\t\t\t\t}\n\t\t\t\tstylesInDom[item.id] = {id: item.id, refs: 1, parts: parts};\n\t\t\t}\n\t\t}\n\t}\n\t\n\tfunction listToStyles(list) {\n\t\tvar styles = [];\n\t\tvar newStyles = {};\n\t\tfor(var i = 0; i < list.length; i++) {\n\t\t\tvar item = list[i];\n\t\t\tvar id = item[0];\n\t\t\tvar css = item[1];\n\t\t\tvar media = item[2];\n\t\t\tvar sourceMap = item[3];\n\t\t\tvar part = {css: css, media: media, sourceMap: sourceMap};\n\t\t\tif(!newStyles[id])\n\t\t\t\tstyles.push(newStyles[id] = {id: id, parts: [part]});\n\t\t\telse\n\t\t\t\tnewStyles[id].parts.push(part);\n\t\t}\n\t\treturn styles;\n\t}\n\t\n\tfunction insertStyleElement(options, styleElement) {\n\t\tvar head = getHeadElement();\n\t\tvar lastStyleElementInsertedAtTop = styleElementsInsertedAtTop[styleElementsInsertedAtTop.length - 1];\n\t\tif (options.insertAt === \"top\") {\n\t\t\tif(!lastStyleElementInsertedAtTop) {\n\t\t\t\thead.insertBefore(styleElement, head.firstChild);\n\t\t\t} else if(lastStyleElementInsertedAtTop.nextSibling) {\n\t\t\t\thead.insertBefore(styleElement, lastStyleElementInsertedAtTop.nextSibling);\n\t\t\t} else {\n\t\t\t\thead.appendChild(styleElement);\n\t\t\t}\n\t\t\tstyleElementsInsertedAtTop.push(styleElement);\n\t\t} else if (options.insertAt === \"bottom\") {\n\t\t\thead.appendChild(styleElement);\n\t\t} else {\n\t\t\tthrow new Error(\"Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.\");\n\t\t}\n\t}\n\t\n\tfunction removeStyleElement(styleElement) {\n\t\tstyleElement.parentNode.removeChild(styleElement);\n\t\tvar idx = styleElementsInsertedAtTop.indexOf(styleElement);\n\t\tif(idx >= 0) {\n\t\t\tstyleElementsInsertedAtTop.splice(idx, 1);\n\t\t}\n\t}\n\t\n\tfunction createStyleElement(options) {\n\t\tvar styleElement = document.createElement(\"style\");\n\t\tstyleElement.type = \"text/css\";\n\t\tinsertStyleElement(options, styleElement);\n\t\treturn styleElement;\n\t}\n\t\n\tfunction createLinkElement(options) {\n\t\tvar linkElement = document.createElement(\"link\");\n\t\tlinkElement.rel = \"stylesheet\";\n\t\tinsertStyleElement(options, linkElement);\n\t\treturn linkElement;\n\t}\n\t\n\tfunction addStyle(obj, options) {\n\t\tvar styleElement, update, remove;\n\t\n\t\tif (options.singleton) {\n\t\t\tvar styleIndex = singletonCounter++;\n\t\t\tstyleElement = singletonElement || (singletonElement = createStyleElement(options));\n\t\t\tupdate = applyToSingletonTag.bind(null, styleElement, styleIndex, false);\n\t\t\tremove = applyToSingletonTag.bind(null, styleElement, styleIndex, true);\n\t\t} else if(obj.sourceMap &&\n\t\t\ttypeof URL === \"function\" &&\n\t\t\ttypeof URL.createObjectURL === \"function\" &&\n\t\t\ttypeof URL.revokeObjectURL === \"function\" &&\n\t\t\ttypeof Blob === \"function\" &&\n\t\t\ttypeof btoa === \"function\") {\n\t\t\tstyleElement = createLinkElement(options);\n\t\t\tupdate = updateLink.bind(null, styleElement);\n\t\t\tremove = function() {\n\t\t\t\tremoveStyleElement(styleElement);\n\t\t\t\tif(styleElement.href)\n\t\t\t\t\tURL.revokeObjectURL(styleElement.href);\n\t\t\t};\n\t\t} else {\n\t\t\tstyleElement = createStyleElement(options);\n\t\t\tupdate = applyToTag.bind(null, styleElement);\n\t\t\tremove = function() {\n\t\t\t\tremoveStyleElement(styleElement);\n\t\t\t};\n\t\t}\n\t\n\t\tupdate(obj);\n\t\n\t\treturn function updateStyle(newObj) {\n\t\t\tif(newObj) {\n\t\t\t\tif(newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap)\n\t\t\t\t\treturn;\n\t\t\t\tupdate(obj = newObj);\n\t\t\t} else {\n\t\t\t\tremove();\n\t\t\t}\n\t\t};\n\t}\n\t\n\tvar replaceText = (function () {\n\t\tvar textStore = [];\n\t\n\t\treturn function (index, replacement) {\n\t\t\ttextStore[index] = replacement;\n\t\t\treturn textStore.filter(Boolean).join('\\n');\n\t\t};\n\t})();\n\t\n\tfunction applyToSingletonTag(styleElement, index, remove, obj) {\n\t\tvar css = remove ? \"\" : obj.css;\n\t\n\t\tif (styleElement.styleSheet) {\n\t\t\tstyleElement.styleSheet.cssText = replaceText(index, css);\n\t\t} else {\n\t\t\tvar cssNode = document.createTextNode(css);\n\t\t\tvar childNodes = styleElement.childNodes;\n\t\t\tif (childNodes[index]) styleElement.removeChild(childNodes[index]);\n\t\t\tif (childNodes.length) {\n\t\t\t\tstyleElement.insertBefore(cssNode, childNodes[index]);\n\t\t\t} else {\n\t\t\t\tstyleElement.appendChild(cssNode);\n\t\t\t}\n\t\t}\n\t}\n\t\n\tfunction applyToTag(styleElement, obj) {\n\t\tvar css = obj.css;\n\t\tvar media = obj.media;\n\t\n\t\tif(media) {\n\t\t\tstyleElement.setAttribute(\"media\", media)\n\t\t}\n\t\n\t\tif(styleElement.styleSheet) {\n\t\t\tstyleElement.styleSheet.cssText = css;\n\t\t} else {\n\t\t\twhile(styleElement.firstChild) {\n\t\t\t\tstyleElement.removeChild(styleElement.firstChild);\n\t\t\t}\n\t\t\tstyleElement.appendChild(document.createTextNode(css));\n\t\t}\n\t}\n\t\n\tfunction updateLink(linkElement, obj) {\n\t\tvar css = obj.css;\n\t\tvar sourceMap = obj.sourceMap;\n\t\n\t\tif(sourceMap) {\n\t\t\t// http://stackoverflow.com/a/26603875\n\t\t\tcss += \"\\n/*# sourceMappingURL=data:application/json;base64,\" + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + \" */\";\n\t\t}\n\t\n\t\tvar blob = new Blob([css], { type: \"text/css\" });\n\t\n\t\tvar oldSrc = linkElement.href;\n\t\n\t\tlinkElement.href = URL.createObjectURL(blob);\n\t\n\t\tif(oldSrc)\n\t\t\tURL.revokeObjectURL(oldSrc);\n\t}\n\n\n/***/ },\n/* 5 */\n/***/ function(module, exports) {\n\n\t'use strict';\n\t\n\tObject.defineProperty(exports, \"__esModule\", {\n\t\tvalue: true\n\t});\n\texports.default = Bar;\n\t/* global Snap */\n\t/*\n\t\tClass: Bar\n\t\n\t\tOpts:\n\t\t\tgt: Gantt object\n\t\t\ttask: task object\n\t*/\n\t\n\tfunction Bar(gt, task) {\n\t\n\t\tvar self = {};\n\t\n\t\tfunction init() {\n\t\t\tset_defaults();\n\t\t\tprepare();\n\t\t\tdraw();\n\t\t\tbind();\n\t\t}\n\t\n\t\tfunction set_defaults() {\n\t\t\tself.action_completed = false;\n\t\t\tself.task = task;\n\t\t}\n\t\n\t\tfunction prepare() {\n\t\t\tprepare_values();\n\t\t\tprepare_plugins();\n\t\t}\n\t\n\t\tfunction prepare_values() {\n\t\t\tself.invalid = self.task.invalid;\n\t\t\tself.height = gt.config.bar.height;\n\t\t\tself.x = compute_x();\n\t\t\tself.y = compute_y();\n\t\t\tself.corner_radius = gt.config.bar.corner_radius;\n\t\t\tself.duration = (self.task._end.diff(self.task._start, 'hours') + 24) / gt.config.step;\n\t\t\tself.width = gt.config.column_width * self.duration;\n\t\t\tself.progress_width = gt.config.column_width * self.duration * (self.task.progress / 100) || 0;\n\t\t\tself.group = gt.canvas.group().addClass('bar-wrapper').addClass(self.task.custom_class || '');\n\t\t\tself.bar_group = gt.canvas.group().addClass('bar-group').appendTo(self.group);\n\t\t\tself.handle_group = gt.canvas.group().addClass('handle-group').appendTo(self.group);\n\t\t}\n\t\n\t\tfunction prepare_plugins() {\n\t\t\tSnap.plugin(function (Snap, Element, Paper, global, Fragment) {\n\t\t\t\tElement.prototype.getX = function () {\n\t\t\t\t\treturn +this.attr('x');\n\t\t\t\t};\n\t\t\t\tElement.prototype.getY = function () {\n\t\t\t\t\treturn +this.attr('y');\n\t\t\t\t};\n\t\t\t\tElement.prototype.getWidth = function () {\n\t\t\t\t\treturn +this.attr('width');\n\t\t\t\t};\n\t\t\t\tElement.prototype.getHeight = function () {\n\t\t\t\t\treturn +this.attr('height');\n\t\t\t\t};\n\t\t\t\tElement.prototype.getEndX = function () {\n\t\t\t\t\treturn this.getX() + this.getWidth();\n\t\t\t\t};\n\t\t\t});\n\t\t}\n\t\n\t\tfunction draw() {\n\t\t\tdraw_bar();\n\t\t\tdraw_progress_bar();\n\t\t\tdraw_label();\n\t\t\tdraw_resize_handles();\n\t\t}\n\t\n\t\tfunction draw_bar() {\n\t\t\tself.$bar = gt.canvas.rect(self.x, self.y, self.width, self.height, self.corner_radius, self.corner_radius).addClass('bar').appendTo(self.bar_group);\n\t\t\tif (self.invalid) {\n\t\t\t\tself.$bar.addClass('bar-invalid');\n\t\t\t}\n\t\t}\n\t\n\t\tfunction draw_progress_bar() {\n\t\t\tif (self.invalid) return;\n\t\t\tself.$bar_progress = gt.canvas.rect(self.x, self.y, self.progress_width, self.height, self.corner_radius, self.corner_radius).addClass('bar-progress').appendTo(self.bar_group);\n\t\t}\n\t\n\t\tfunction draw_label() {\n\t\t\tgt.canvas.text(self.x + self.width / 2, self.y + self.height / 2, self.task.name).addClass('bar-label').appendTo(self.bar_group);\n\t\t\tupdate_label_position();\n\t\t}\n\t\n\t\tfunction draw_resize_handles() {\n\t\t\tif (self.invalid) return;\n\t\n\t\t\tvar bar = self.$bar,\n\t\t\t    handle_width = 8;\n\t\n\t\t\tgt.canvas.rect(bar.getX() + bar.getWidth() - 9, bar.getY() + 1, handle_width, self.height - 2, self.corner_radius, self.corner_radius).addClass('handle right').appendTo(self.handle_group);\n\t\t\tgt.canvas.rect(bar.getX() + 1, bar.getY() + 1, handle_width, self.height - 2, self.corner_radius, self.corner_radius).addClass('handle left').appendTo(self.handle_group);\n\t\n\t\t\tif (self.task.progress && self.task.progress < 100) {\n\t\t\t\tgt.canvas.polygon(get_progress_polygon_points()).addClass('handle progress').appendTo(self.handle_group);\n\t\t\t}\n\t\t}\n\t\n\t\tfunction get_progress_polygon_points() {\n\t\t\tvar bar_progress = self.$bar_progress;\n\t\t\treturn [bar_progress.getEndX() - 5, bar_progress.getY() + bar_progress.getHeight(), bar_progress.getEndX() + 5, bar_progress.getY() + bar_progress.getHeight(), bar_progress.getEndX(), bar_progress.getY() + bar_progress.getHeight() - 8.66];\n\t\t}\n\t\n\t\tfunction bind() {\n\t\t\tif (self.invalid) return;\n\t\t\tsetup_click_event();\n\t\t\tshow_details();\n\t\t\tbind_resize();\n\t\t\tbind_drag();\n\t\t\tbind_resize_progress();\n\t\t}\n\t\n\t\tfunction show_details() {\n\t\t\tvar popover_group = gt.element_groups.details;\n\t\t\tself.details_box = popover_group.select('.details-wrapper[data-task=\\'' + self.task.id + '\\']');\n\t\n\t\t\tif (!self.details_box) {\n\t\t\t\tself.details_box = gt.canvas.group().addClass('details-wrapper hide').attr('data-task', self.task.id).appendTo(popover_group);\n\t\n\t\t\t\trender_details();\n\t\n\t\t\t\tvar f = gt.canvas.filter(Snap.filter.shadow(0, 1, 1, '#666', 0.6));\n\t\t\t\tself.details_box.attr({\n\t\t\t\t\tfilter: f\n\t\t\t\t});\n\t\t\t}\n\t\n\t\t\tself.group.click(function (e) {\n\t\t\t\tif (self.action_completed) {\n\t\t\t\t\t// just finished a move action, wait for a few seconds\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tpopover_group.selectAll('.details-wrapper').forEach(function (el) {\n\t\t\t\t\treturn el.addClass('hide');\n\t\t\t\t});\n\t\t\t\tself.details_box.removeClass('hide');\n\t\t\t});\n\t\t}\n\t\n\t\tfunction render_details() {\n\t\t\tvar _get_details_position = get_details_position(),\n\t\t\t    x = _get_details_position.x,\n\t\t\t    y = _get_details_position.y;\n\t\n\t\t\tself.details_box.transform('t' + x + ',' + y);\n\t\t\tself.details_box.clear();\n\t\n\t\t\tvar html = get_details_html();\n\t\t\tvar foreign_object = Snap.parse('<foreignObject width=\"5000\" height=\"2000\">\\n\\t\\t\\t\\t<body xmlns=\"http://www.w3.org/1999/xhtml\">\\n\\t\\t\\t\\t\\t' + html + '\\n\\t\\t\\t\\t</body>\\n\\t\\t\\t\\t</foreignObject>');\n\t\t\tself.details_box.append(foreign_object);\n\t\t}\n\t\n\t\tfunction get_details_html() {\n\t\n\t\t\t// custom html in config\n\t\t\tif (gt.config.custom_popup_html) {\n\t\t\t\tvar _html = gt.config.custom_popup_html;\n\t\t\t\tif (typeof _html === 'string') {\n\t\t\t\t\treturn _html;\n\t\t\t\t}\n\t\t\t\tif (isFunction(_html)) {\n\t\t\t\t\treturn _html(task);\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\tvar start_date = self.task._start.format('MMM D');\n\t\t\tvar end_date = self.task._end.format('MMM D');\n\t\t\tvar heading = self.task.name + ': ' + start_date + ' - ' + end_date;\n\t\n\t\t\tvar line_1 = 'Duration: ' + self.duration + ' days';\n\t\t\tvar line_2 = self.task.progress ? 'Progress: ' + self.task.progress : null;\n\t\n\t\t\tvar html = '\\n\\t\\t\\t<div class=\"details-container\">\\n\\t\\t\\t\\t<h5>' + heading + '</h5>\\n\\t\\t\\t\\t<p>' + line_1 + '</p>\\n\\t\\t\\t\\t' + (line_2 ? '<p>' + line_2 + '</p>' : '') + '\\n\\t\\t\\t</div>\\n\\t\\t';\n\t\t\treturn html;\n\t\t}\n\t\n\t\tfunction get_details_position() {\n\t\t\treturn {\n\t\t\t\tx: self.$bar.getEndX() + 2,\n\t\t\t\ty: self.$bar.getY() - 10\n\t\t\t};\n\t\t}\n\t\n\t\tfunction bind_resize() {\n\t\t\tvar _get_handles = get_handles(),\n\t\t\t    left = _get_handles.left,\n\t\t\t    right = _get_handles.right;\n\t\n\t\t\tleft.drag(onmove_left, onstart, onstop_left);\n\t\t\tright.drag(onmove_right, onstart, onstop_right);\n\t\n\t\t\tfunction onmove_right(dx, dy) {\n\t\t\t\tonmove_handle_right(dx, dy);\n\t\t\t}\n\t\t\tfunction onstop_right() {\n\t\t\t\tonstop_handle_right();\n\t\t\t}\n\t\n\t\t\tfunction onmove_left(dx, dy) {\n\t\t\t\tonmove_handle_left(dx, dy);\n\t\t\t}\n\t\t\tfunction onstop_left() {\n\t\t\t\tonstop_handle_left();\n\t\t\t}\n\t\t}\n\t\n\t\tfunction get_handles() {\n\t\t\treturn {\n\t\t\t\tleft: self.handle_group.select('.handle.left'),\n\t\t\t\tright: self.handle_group.select('.handle.right')\n\t\t\t};\n\t\t}\n\t\n\t\tfunction bind_drag() {\n\t\t\tself.bar_group.drag(onmove, onstart, onstop);\n\t\t}\n\t\n\t\tfunction bind_resize_progress() {\n\t\t\tvar bar = self.$bar,\n\t\t\t    bar_progress = self.$bar_progress,\n\t\t\t    handle = self.group.select('.handle.progress');\n\t\t\thandle && handle.drag(on_move, on_start, on_stop);\n\t\n\t\t\tfunction on_move(dx, dy) {\n\t\t\t\tif (dx > bar_progress.max_dx) {\n\t\t\t\t\tdx = bar_progress.max_dx;\n\t\t\t\t}\n\t\t\t\tif (dx < bar_progress.min_dx) {\n\t\t\t\t\tdx = bar_progress.min_dx;\n\t\t\t\t}\n\t\n\t\t\t\tbar_progress.attr('width', bar_progress.owidth + dx);\n\t\t\t\thandle.attr('points', get_progress_polygon_points());\n\t\t\t\tbar_progress.finaldx = dx;\n\t\t\t}\n\t\t\tfunction on_stop() {\n\t\t\t\tif (!bar_progress.finaldx) return;\n\t\t\t\tprogress_changed();\n\t\t\t\tset_action_completed();\n\t\t\t}\n\t\t\tfunction on_start() {\n\t\t\t\tbar_progress.finaldx = 0;\n\t\t\t\tbar_progress.owidth = bar_progress.getWidth();\n\t\t\t\tbar_progress.min_dx = -bar_progress.getWidth();\n\t\t\t\tbar_progress.max_dx = bar.getWidth() - bar_progress.getWidth();\n\t\t\t}\n\t\t}\n\t\n\t\tfunction onstart() {\n\t\t\tvar bar = self.$bar;\n\t\t\tbar.ox = bar.getX();\n\t\t\tbar.oy = bar.getY();\n\t\t\tbar.owidth = bar.getWidth();\n\t\t\tbar.finaldx = 0;\n\t\t\trun_method_for_dependencies('onstart');\n\t\t}\n\t\tself.onstart = onstart;\n\t\n\t\tfunction onmove(dx, dy) {\n\t\t\tvar bar = self.$bar;\n\t\t\tbar.finaldx = get_snap_position(dx);\n\t\t\tupdate_bar_position({ x: bar.ox + bar.finaldx });\n\t\t\trun_method_for_dependencies('onmove', [dx, dy]);\n\t\t}\n\t\tself.onmove = onmove;\n\t\n\t\tfunction onstop() {\n\t\t\tvar bar = self.$bar;\n\t\t\tif (!bar.finaldx) return;\n\t\t\tdate_changed();\n\t\t\tset_action_completed();\n\t\t\trun_method_for_dependencies('onstop');\n\t\t}\n\t\tself.onstop = onstop;\n\t\n\t\tfunction onmove_handle_left(dx, dy) {\n\t\t\tvar bar = self.$bar;\n\t\t\tbar.finaldx = get_snap_position(dx);\n\t\t\tupdate_bar_position({\n\t\t\t\tx: bar.ox + bar.finaldx,\n\t\t\t\twidth: bar.owidth - bar.finaldx\n\t\t\t});\n\t\t\trun_method_for_dependencies('onmove', [dx, dy]);\n\t\t}\n\t\tself.onmove_handle_left = onmove_handle_left;\n\t\n\t\tfunction onstop_handle_left() {\n\t\t\tvar bar = self.$bar;\n\t\t\tif (bar.finaldx) date_changed();\n\t\t\tset_action_completed();\n\t\t\trun_method_for_dependencies('onstop');\n\t\t}\n\t\tself.onstop_handle_left = onstop_handle_left;\n\t\n\t\tfunction run_method_for_dependencies(fn, args) {\n\t\t\tvar dm = gt.dependency_map;\n\t\t\tif (dm[self.task.id]) {\n\t\t\t\tvar _iteratorNormalCompletion = true;\n\t\t\t\tvar _didIteratorError = false;\n\t\t\t\tvar _iteratorError = undefined;\n\t\n\t\t\t\ttry {\n\t\t\t\t\tfor (var _iterator = dm[self.task.id][Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n\t\t\t\t\t\tvar deptask = _step.value;\n\t\n\t\t\t\t\t\tvar dt = gt.get_bar(deptask);\n\t\t\t\t\t\tdt[fn].apply(dt, args);\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\t_didIteratorError = true;\n\t\t\t\t\t_iteratorError = err;\n\t\t\t\t} finally {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tif (!_iteratorNormalCompletion && _iterator.return) {\n\t\t\t\t\t\t\t_iterator.return();\n\t\t\t\t\t\t}\n\t\t\t\t\t} finally {\n\t\t\t\t\t\tif (_didIteratorError) {\n\t\t\t\t\t\t\tthrow _iteratorError;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction onmove_handle_right(dx, dy) {\n\t\t\tvar bar = self.$bar;\n\t\t\tbar.finaldx = get_snap_position(dx);\n\t\t\tupdate_bar_position({ width: bar.owidth + bar.finaldx });\n\t\t}\n\t\n\t\tfunction onstop_handle_right() {\n\t\t\tvar bar = self.$bar;\n\t\t\tif (bar.finaldx) date_changed();\n\t\t\tset_action_completed();\n\t\t}\n\t\n\t\tfunction update_bar_position(_ref) {\n\t\t\tvar _ref$x = _ref.x,\n\t\t\t    x = _ref$x === undefined ? null : _ref$x,\n\t\t\t    _ref$width = _ref.width,\n\t\t\t    width = _ref$width === undefined ? null : _ref$width;\n\t\n\t\t\tvar bar = self.$bar;\n\t\t\tif (x) {\n\t\t\t\t// get all x values of parent task\n\t\t\t\tvar xs = task.dependencies.map(function (dep) {\n\t\t\t\t\treturn gt.get_bar(dep).$bar.getX();\n\t\t\t\t});\n\t\t\t\t// child task must not go before parent\n\t\t\t\tvar valid_x = xs.reduce(function (prev, curr) {\n\t\t\t\t\treturn x >= curr;\n\t\t\t\t}, x);\n\t\t\t\tif (!valid_x) {\n\t\t\t\t\twidth = null;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tupdate_attr(bar, 'x', x);\n\t\t\t}\n\t\t\tif (width && width >= gt.config.column_width) {\n\t\t\t\tupdate_attr(bar, 'width', width);\n\t\t\t}\n\t\t\tupdate_label_position();\n\t\t\tupdate_handle_position();\n\t\t\tupdate_progressbar_position();\n\t\t\tupdate_arrow_position();\n\t\t\tupdate_details_position();\n\t\t}\n\t\n\t\tfunction setup_click_event() {\n\t\t\tself.group.click(function () {\n\t\t\t\tif (self.action_completed) {\n\t\t\t\t\t// just finished a move action, wait for a few seconds\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (self.group.hasClass('active')) {\n\t\t\t\t\tgt.trigger_event('click', [self.task]);\n\t\t\t\t}\n\t\t\t\tgt.unselect_all();\n\t\t\t\tself.group.toggleClass('active');\n\t\t\t});\n\t\t}\n\t\n\t\tfunction date_changed() {\n\t\t\tvar _compute_start_end_da = compute_start_end_date(),\n\t\t\t    new_start_date = _compute_start_end_da.new_start_date,\n\t\t\t    new_end_date = _compute_start_end_da.new_end_date;\n\t\n\t\t\tself.task._start = new_start_date;\n\t\t\tself.task._end = new_end_date;\n\t\t\trender_details();\n\t\t\tgt.trigger_event('date_change', [self.task, new_start_date, new_end_date]);\n\t\t}\n\t\n\t\tfunction progress_changed() {\n\t\t\tvar new_progress = compute_progress();\n\t\t\tself.task.progress = new_progress;\n\t\t\trender_details();\n\t\t\tgt.trigger_event('progress_change', [self.task, new_progress]);\n\t\t}\n\t\n\t\tfunction set_action_completed() {\n\t\t\tself.action_completed = true;\n\t\t\tsetTimeout(function () {\n\t\t\t\treturn self.action_completed = false;\n\t\t\t}, 2000);\n\t\t}\n\t\n\t\tfunction compute_start_end_date() {\n\t\t\tvar bar = self.$bar;\n\t\t\tvar x_in_units = bar.getX() / gt.config.column_width;\n\t\t\tvar new_start_date = gt.gantt_start.clone().add(x_in_units * gt.config.step, 'hours');\n\t\t\tvar width_in_units = bar.getWidth() / gt.config.column_width;\n\t\t\tvar new_end_date = new_start_date.clone().add(width_in_units * gt.config.step, 'hours');\n\t\t\t// lets say duration is 2 days\n\t\t\t// start_date = May 24 00:00:00\n\t\t\t// end_date = May 24 + 2 days = May 26 (incorrect)\n\t\t\t// so subtract 1 second so that\n\t\t\t// end_date = May 25 23:59:59\n\t\t\tnew_end_date.add('-1', 'seconds');\n\t\t\treturn { new_start_date: new_start_date, new_end_date: new_end_date };\n\t\t}\n\t\n\t\tfunction compute_progress() {\n\t\t\tvar progress = self.$bar_progress.getWidth() / self.$bar.getWidth() * 100;\n\t\t\treturn parseInt(progress, 10);\n\t\t}\n\t\n\t\tfunction compute_x() {\n\t\t\tvar x = self.task._start.diff(gt.gantt_start, 'hours') / gt.config.step * gt.config.column_width;\n\t\n\t\t\tif (gt.view_is('Month')) {\n\t\t\t\tx = self.task._start.diff(gt.gantt_start, 'days') * gt.config.column_width / 30;\n\t\t\t}\n\t\t\treturn x;\n\t\t}\n\t\n\t\tfunction compute_y() {\n\t\t\treturn gt.config.header_height + gt.config.padding + self.task._index * (self.height + gt.config.padding);\n\t\t}\n\t\n\t\tfunction get_snap_position(dx) {\n\t\t\tvar odx = dx,\n\t\t\t    rem = void 0,\n\t\t\t    position = void 0;\n\t\n\t\t\tif (gt.view_is('Week')) {\n\t\t\t\trem = dx % (gt.config.column_width / 7);\n\t\t\t\tposition = odx - rem + (rem < gt.config.column_width / 14 ? 0 : gt.config.column_width / 7);\n\t\t\t} else if (gt.view_is('Month')) {\n\t\t\t\trem = dx % (gt.config.column_width / 30);\n\t\t\t\tposition = odx - rem + (rem < gt.config.column_width / 60 ? 0 : gt.config.column_width / 30);\n\t\t\t} else {\n\t\t\t\trem = dx % gt.config.column_width;\n\t\t\t\tposition = odx - rem + (rem < gt.config.column_width / 2 ? 0 : gt.config.column_width);\n\t\t\t}\n\t\t\treturn position;\n\t\t}\n\t\n\t\tfunction update_attr(element, attr, value) {\n\t\t\tvalue = +value;\n\t\t\tif (!isNaN(value)) {\n\t\t\t\telement.attr(attr, value);\n\t\t\t}\n\t\t\treturn element;\n\t\t}\n\t\n\t\tfunction update_progressbar_position() {\n\t\t\tself.$bar_progress.attr('x', self.$bar.getX());\n\t\t\tself.$bar_progress.attr('width', self.$bar.getWidth() * (self.task.progress / 100));\n\t\t}\n\t\n\t\tfunction update_label_position() {\n\t\t\tvar bar = self.$bar,\n\t\t\t    label = self.group.select('.bar-label');\n\t\t\tif (label.getBBox().width > bar.getWidth()) {\n\t\t\t\tlabel.addClass('big').attr('x', bar.getX() + bar.getWidth() + 5);\n\t\t\t} else {\n\t\t\t\tlabel.removeClass('big').attr('x', bar.getX() + bar.getWidth() / 2);\n\t\t\t}\n\t\t}\n\t\n\t\tfunction update_handle_position() {\n\t\t\tvar bar = self.$bar;\n\t\t\tself.handle_group.select('.handle.left').attr({\n\t\t\t\t'x': bar.getX() + 1\n\t\t\t});\n\t\t\tself.handle_group.select('.handle.right').attr({\n\t\t\t\t'x': bar.getEndX() - 9\n\t\t\t});\n\t\t\tvar handle = self.group.select('.handle.progress');\n\t\t\thandle && handle.attr('points', get_progress_polygon_points());\n\t\t}\n\t\n\t\tfunction update_arrow_position() {\n\t\t\tvar _iteratorNormalCompletion2 = true;\n\t\t\tvar _didIteratorError2 = false;\n\t\t\tvar _iteratorError2 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator2 = self.arrows[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n\t\t\t\t\tvar arrow = _step2.value;\n\t\n\t\t\t\t\tarrow.update();\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError2 = true;\n\t\t\t\t_iteratorError2 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion2 && _iterator2.return) {\n\t\t\t\t\t\t_iterator2.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError2) {\n\t\t\t\t\t\tthrow _iteratorError2;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction update_details_position() {\n\t\t\tvar _get_details_position2 = get_details_position(),\n\t\t\t    x = _get_details_position2.x,\n\t\t\t    y = _get_details_position2.y;\n\t\n\t\t\tself.details_box && self.details_box.transform('t' + x + ',' + y);\n\t\t}\n\t\n\t\tfunction isFunction(functionToCheck) {\n\t\t\tvar getType = {};\n\t\t\treturn functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n\t\t}\n\t\n\t\tinit();\n\t\n\t\treturn self;\n\t}\n\tmodule.exports = exports['default'];\n\n/***/ },\n/* 6 */\n/***/ function(module, exports) {\n\n\t'use strict';\n\t\n\tObject.defineProperty(exports, \"__esModule\", {\n\t\tvalue: true\n\t});\n\texports.default = Arrow;\n\t/* global Snap */\n\t/*\n\t\tClass: Arrow\n\t\tfrom_task ---> to_task\n\t\n\t\tOpts:\n\t\t\tgantt (Gantt object)\n\t\t\tfrom_task (Bar object)\n\t\t\tto_task (Bar object)\n\t*/\n\t\n\tfunction Arrow(gt, from_task, to_task) {\n\t\n\t\tvar self = {};\n\t\n\t\tfunction init() {\n\t\t\tself.from_task = from_task;\n\t\t\tself.to_task = to_task;\n\t\t\tprepare();\n\t\t\tdraw();\n\t\t}\n\t\n\t\tfunction prepare() {\n\t\n\t\t\tself.start_x = from_task.$bar.getX() + from_task.$bar.getWidth() / 2;\n\t\n\t\t\tvar condition = function condition() {\n\t\t\t\treturn to_task.$bar.getX() < self.start_x + gt.config.padding && self.start_x > from_task.$bar.getX() + gt.config.padding;\n\t\t\t};\n\t\n\t\t\twhile (condition()) {\n\t\t\t\tself.start_x -= 10;\n\t\t\t}\n\t\n\t\t\tself.start_y = gt.config.header_height + gt.config.bar.height + (gt.config.padding + gt.config.bar.height) * from_task.task._index + gt.config.padding;\n\t\n\t\t\tself.end_x = to_task.$bar.getX() - gt.config.padding / 2;\n\t\t\tself.end_y = gt.config.header_height + gt.config.bar.height / 2 + (gt.config.padding + gt.config.bar.height) * to_task.task._index + gt.config.padding;\n\t\n\t\t\tvar from_is_below_to = from_task.task._index > to_task.task._index;\n\t\t\tself.curve = gt.config.arrow.curve;\n\t\t\tself.clockwise = from_is_below_to ? 1 : 0;\n\t\t\tself.curve_y = from_is_below_to ? -self.curve : self.curve;\n\t\t\tself.offset = from_is_below_to ? self.end_y + gt.config.arrow.curve : self.end_y - gt.config.arrow.curve;\n\t\n\t\t\tself.path = Snap.format('M {start_x} {start_y} V {offset} ' + 'a {curve} {curve} 0 0 {clockwise} {curve} {curve_y} ' + 'L {end_x} {end_y} m -5 -5 l 5 5 l -5 5', {\n\t\t\t\tstart_x: self.start_x,\n\t\t\t\tstart_y: self.start_y,\n\t\t\t\tend_x: self.end_x,\n\t\t\t\tend_y: self.end_y,\n\t\t\t\toffset: self.offset,\n\t\t\t\tcurve: self.curve,\n\t\t\t\tclockwise: self.clockwise,\n\t\t\t\tcurve_y: self.curve_y\n\t\t\t});\n\t\n\t\t\tif (to_task.$bar.getX() < from_task.$bar.getX() + gt.config.padding) {\n\t\t\t\tself.path = Snap.format('M {start_x} {start_y} v {down_1} ' + 'a {curve} {curve} 0 0 1 -{curve} {curve} H {left} ' + 'a {curve} {curve} 0 0 {clockwise} -{curve} {curve_y} V {down_2} ' + 'a {curve} {curve} 0 0 {clockwise} {curve} {curve_y} ' + 'L {end_x} {end_y} m -5 -5 l 5 5 l -5 5', {\n\t\t\t\t\tstart_x: self.start_x,\n\t\t\t\t\tstart_y: self.start_y,\n\t\t\t\t\tend_x: self.end_x,\n\t\t\t\t\tend_y: self.end_y,\n\t\t\t\t\tdown_1: gt.config.padding / 2 - self.curve,\n\t\t\t\t\tdown_2: to_task.$bar.getY() + to_task.$bar.getHeight() / 2 - self.curve_y,\n\t\t\t\t\tleft: to_task.$bar.getX() - gt.config.padding,\n\t\t\t\t\toffset: self.offset,\n\t\t\t\t\tcurve: self.curve,\n\t\t\t\t\tclockwise: self.clockwise,\n\t\t\t\t\tcurve_y: self.curve_y\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t\n\t\tfunction draw() {\n\t\t\tself.element = gt.canvas.path(self.path).attr('data-from', self.from_task.task.id).attr('data-to', self.to_task.task.id);\n\t\t}\n\t\n\t\tfunction update() {\n\t\t\t// eslint-disable-line\n\t\t\tprepare();\n\t\t\tself.element.attr('d', self.path);\n\t\t}\n\t\tself.update = update;\n\t\n\t\tinit();\n\t\n\t\treturn self;\n\t}\n\tmodule.exports = exports['default'];\n\n/***/ },\n/* 7 */\n/***/ function(module, exports, __webpack_require__) {\n\n\t(function (global, factory) {\n\t\t true ? module.exports = factory() :\n\t\ttypeof define === 'function' && define.amd ? define(factory) :\n\t\t(global.deepmerge = factory());\n\t}(this, (function () { 'use strict';\n\t\n\tvar isMergeableObject = function isMergeableObject(value) {\n\t\treturn isNonNullObject(value)\n\t\t\t&& !isSpecial(value)\n\t};\n\t\n\tfunction isNonNullObject(value) {\n\t\treturn !!value && typeof value === 'object'\n\t}\n\t\n\tfunction isSpecial(value) {\n\t\tvar stringValue = Object.prototype.toString.call(value);\n\t\n\t\treturn stringValue === '[object RegExp]'\n\t\t\t|| stringValue === '[object Date]'\n\t\t\t|| isReactElement(value)\n\t}\n\t\n\t// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\n\tvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\n\tvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\t\n\tfunction isReactElement(value) {\n\t\treturn value.$$typeof === REACT_ELEMENT_TYPE\n\t}\n\t\n\tfunction emptyTarget(val) {\n\t\treturn Array.isArray(val) ? [] : {}\n\t}\n\t\n\tfunction cloneUnlessOtherwiseSpecified(value, optionsArgument) {\n\t\tvar clone = !optionsArgument || optionsArgument.clone !== false;\n\t\n\t\treturn (clone && isMergeableObject(value))\n\t\t\t? deepmerge(emptyTarget(value), value, optionsArgument)\n\t\t\t: value\n\t}\n\t\n\tfunction defaultArrayMerge(target, source, optionsArgument) {\n\t\treturn target.concat(source).map(function(element) {\n\t\t\treturn cloneUnlessOtherwiseSpecified(element, optionsArgument)\n\t\t})\n\t}\n\t\n\tfunction mergeObject(target, source, optionsArgument) {\n\t\tvar destination = {};\n\t\tif (isMergeableObject(target)) {\n\t\t\tObject.keys(target).forEach(function(key) {\n\t\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], optionsArgument);\n\t\t\t});\n\t\t}\n\t\tObject.keys(source).forEach(function(key) {\n\t\t\tif (!isMergeableObject(source[key]) || !target[key]) {\n\t\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], optionsArgument);\n\t\t\t} else {\n\t\t\t\tdestination[key] = deepmerge(target[key], source[key], optionsArgument);\n\t\t\t}\n\t\t});\n\t\treturn destination\n\t}\n\t\n\tfunction deepmerge(target, source, optionsArgument) {\n\t\tvar sourceIsArray = Array.isArray(source);\n\t\tvar targetIsArray = Array.isArray(target);\n\t\tvar options = optionsArgument || { arrayMerge: defaultArrayMerge };\n\t\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\t\n\t\tif (!sourceAndTargetTypesMatch) {\n\t\t\treturn cloneUnlessOtherwiseSpecified(source, optionsArgument)\n\t\t} else if (sourceIsArray) {\n\t\t\tvar arrayMerge = options.arrayMerge || defaultArrayMerge;\n\t\t\treturn arrayMerge(target, source, optionsArgument)\n\t\t} else {\n\t\t\treturn mergeObject(target, source, optionsArgument)\n\t\t}\n\t}\n\t\n\tdeepmerge.all = function deepmergeAll(array, optionsArgument) {\n\t\tif (!Array.isArray(array)) {\n\t\t\tthrow new Error('first argument should be an array')\n\t\t}\n\t\n\t\treturn array.reduce(function(prev, next) {\n\t\t\treturn deepmerge(prev, next, optionsArgument)\n\t\t}, {})\n\t};\n\t\n\tvar deepmerge_1 = deepmerge;\n\t\n\treturn deepmerge_1;\n\t\n\t})));\n\n\n/***/ }\n/******/ ])\n});\n;\n\n\n/** WEBPACK FOOTER **\n ** frappe-gantt.min.js\n **/"," \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId])\n \t\t\treturn installedModules[moduleId].exports;\n\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\texports: {},\n \t\t\tid: moduleId,\n \t\t\tloaded: false\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.loaded = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(0);\n\n\n\n/** WEBPACK FOOTER **\n ** webpack/bootstrap cf9ffca37eeeec96b798\n **/","/* global moment, Snap */\n/**\n * Gantt:\n * \telement: querySelector string, HTML DOM or SVG DOM element, required\n * \ttasks: array of tasks, required\n *   task: { id, name, start, end, progress, dependencies, custom_class }\n * \tconfig: configuration options, optional\n */\nimport './gantt.scss';\n\nimport Bar from './Bar';\nimport Arrow from './Arrow';\n\nexport default function Gantt(element, tasks, config) {\n\n\tconst self = {};\n\n\tfunction init() {\n\t\tset_defaults();\n\n\t\t// expose methods\n\t\tself.change_view_mode = change_view_mode;\n\t\tself.unselect_all = unselect_all;\n\t\tself.view_is = view_is;\n\t\tself.get_bar = get_bar;\n\t\tself.trigger_event = trigger_event;\n\t\tself.refresh = refresh;\n\n\t\t// initialize with default view mode\n\t\tchange_view_mode(self.config.view_mode);\n\t}\n\n\tfunction set_defaults() {\n\n\t\tconst merge = require('deepmerge');\n\n\t\tconst defaults = {\n\t\t\theader_height: 50,\n\t\t\tcolumn_width: 30,\n\t\t\tstep: 24,\n\t\t\tview_modes: [\n\t\t\t\t'Quarter Day',\n\t\t\t\t'Half Day',\n\t\t\t\t'Day',\n\t\t\t\t'Week',\n\t\t\t\t'Month'\n\t\t\t],\n\t\t\tbar: {\n\t\t\t\theight: 20,\n\t\t\t\tcorner_radius: 3\n\t\t\t},\n\t\t\tarrow: {\n\t\t\t\tcurve: 5\n\t\t\t},\n\t\t\tpadding: 18,\n\t\t\tview_mode: 'Day',\n\t\t\tdate_format: 'YYYY-MM-DD',\n\t\t\tcustom_popup_html: null\n\t\t};\n\t\tself.config = merge(defaults, config);\n\n\t\treset_variables(tasks);\n\t}\n\n\tfunction reset_variables(tasks) {\n\t\tif(typeof element === 'string') {\n\t\t\tself.element = document.querySelector(element);\n\t\t} else if (element instanceof SVGElement) {\n\t\t\tself.element = element;\n\t\t} else if (element instanceof HTMLElement) {\n\t\t\tself.element = element.querySelector('svg');\n\t\t} else {\n\t\t\tthrow new TypeError('Frappé Gantt only supports usage of a string CSS selector,' +\n\t\t\t\t' HTML DOM element or SVG DOM element for the \\'element\\' parameter');\n\t\t}\n\n\t\tself._tasks = tasks;\n\n\t\tself._bars = [];\n\t\tself._arrows = [];\n\t\tself.element_groups = {};\n\t}\n\n\tfunction refresh(updated_tasks) {\n\t\treset_variables(updated_tasks);\n\t\tchange_view_mode(self.config.view_mode);\n\t}\n\n\tfunction change_view_mode(mode) {\n\t\tset_scale(mode);\n\t\tprepare();\n\t\trender();\n\t\t// fire viewmode_change event\n\t\ttrigger_event('view_change', [mode]);\n\t}\n\n\tfunction prepare() {\n\t\tprepare_tasks();\n\t\tprepare_dependencies();\n\t\tprepare_dates();\n\t\tprepare_canvas();\n\t}\n\n\tfunction prepare_tasks() {\n\n\t\t// prepare tasks\n\t\tself.tasks = self._tasks.map((task, i) => {\n\n\t\t\t// momentify\n\t\t\ttask._start = moment(task.start, self.config.date_format);\n\t\t\ttask._end = moment(task.end, self.config.date_format);\n\n\t\t\t// make task invalid if duration too large\n\t\t\tif(task._end.diff(task._start, 'years') > 10) {\n\t\t\t\ttask.end = null;\n\t\t\t}\n\n\t\t\t// cache index\n\t\t\ttask._index = i;\n\n\t\t\t// invalid dates\n\t\t\tif(!task.start && !task.end) {\n\t\t\t\ttask._start = moment().startOf('day');\n\t\t\t\ttask._end = moment().startOf('day').add(2, 'days');\n\t\t\t}\n\t\t\tif(!task.start && task.end) {\n\t\t\t\ttask._start = task._end.clone().add(-2, 'days');\n\t\t\t}\n\t\t\tif(task.start && !task.end) {\n\t\t\t\ttask._end = task._start.clone().add(2, 'days');\n\t\t\t}\n\n\t\t\t// invalid flag\n\t\t\tif(!task.start || !task.end) {\n\t\t\t\ttask.invalid = true;\n\t\t\t}\n\n\t\t\t// dependencies\n\t\t\tif(typeof task.dependencies === 'string' || !task.dependencies) {\n\t\t\t\tlet deps = [];\n\t\t\t\tif(task.dependencies) {\n\t\t\t\t\tdeps = task.dependencies\n\t\t\t\t\t\t.split(',')\n\t\t\t\t\t\t.map(d => d.trim())\n\t\t\t\t\t\t.filter((d) => d);\n\t\t\t\t}\n\t\t\t\ttask.dependencies = deps;\n\t\t\t}\n\n\t\t\t// uids\n\t\t\tif(!task.id) {\n\t\t\t\ttask.id = generate_id(task);\n\t\t\t}\n\n\t\t\treturn task;\n\t\t});\n\t}\n\n\tfunction prepare_dependencies() {\n\n\t\tself.dependency_map = {};\n\t\tfor(let t of self.tasks) {\n\t\t\tfor(let d of t.dependencies) {\n\t\t\t\tself.dependency_map[d] = self.dependency_map[d] || [];\n\t\t\t\tself.dependency_map[d].push(t.id);\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction prepare_dates() {\n\n\t\tself.gantt_start = self.gantt_end = null;\n\t\tfor(let task of self.tasks) {\n\t\t\t// set global start and end date\n\t\t\tif(!self.gantt_start || task._start < self.gantt_start) {\n\t\t\t\tself.gantt_start = task._start;\n\t\t\t}\n\t\t\tif(!self.gantt_end || task._end > self.gantt_end) {\n\t\t\t\tself.gantt_end = task._end;\n\t\t\t}\n\t\t}\n\t\tset_gantt_dates();\n\t\tsetup_dates();\n\t}\n\n\tfunction prepare_canvas() {\n\t\tif(self.canvas) return;\n\t\tself.canvas = Snap(self.element).addClass('gantt');\n\t}\n\n\tfunction render() {\n\t\tclear();\n\t\tsetup_groups();\n\t\tmake_grid();\n\t\tmake_dates();\n\t\tmake_bars();\n\t\tmake_arrows();\n\t\tmap_arrows_on_bars();\n\t\tset_width();\n\t\tset_scroll_position();\n\t\tbind_grid_click();\n\t}\n\n\tfunction clear() {\n\t\tself.canvas.clear();\n\t\tself._bars = [];\n\t\tself._arrows = [];\n\t}\n\n\tfunction set_gantt_dates() {\n\n\t\tif(view_is(['Quarter Day', 'Half Day'])) {\n\t\t\tself.gantt_start = self.gantt_start.clone().subtract(7, 'day');\n\t\t\tself.gantt_end = self.gantt_end.clone().add(7, 'day');\n\t\t} else if(view_is('Month')) {\n\t\t\tself.gantt_start = self.gantt_start.clone().startOf('year');\n\t\t\tself.gantt_end = self.gantt_end.clone().endOf('month').add(1, 'year');\n\t\t} else {\n\t\t\tself.gantt_start = self.gantt_start.clone().startOf('month').subtract(1, 'month');\n\t\t\tself.gantt_end = self.gantt_end.clone().endOf('month').add(1, 'month');\n\t\t}\n\t}\n\n\tfunction setup_dates() {\n\n\t\tself.dates = [];\n\t\tlet cur_date = null;\n\n\t\twhile(cur_date === null || cur_date < self.gantt_end) {\n\t\t\tif(!cur_date) {\n\t\t\t\tcur_date = self.gantt_start.clone();\n\t\t\t} else {\n\t\t\t\tcur_date = view_is('Month') ?\n\t\t\t\t\tcur_date.clone().add(1, 'month') :\n\t\t\t\t\tcur_date.clone().add(self.config.step, 'hours');\n\t\t\t}\n\t\t\tself.dates.push(cur_date);\n\t\t}\n\t}\n\n\tfunction setup_groups() {\n\n\t\tconst groups = ['grid', 'date', 'arrow', 'progress', 'bar', 'details'];\n\t\t// make group layers\n\t\tfor(let group of groups) {\n\t\t\tself.element_groups[group] = self.canvas.group().attr({'id': group});\n\t\t}\n\t}\n\n\tfunction set_scale(scale) {\n\t\tself.config.view_mode = scale;\n\n\t\tif(scale === 'Day') {\n\t\t\tself.config.step = 24;\n\t\t\tself.config.column_width = 38;\n\t\t} else if(scale === 'Half Day') {\n\t\t\tself.config.step = 24 / 2;\n\t\t\tself.config.column_width = 38;\n\t\t} else if(scale === 'Quarter Day') {\n\t\t\tself.config.step = 24 / 4;\n\t\t\tself.config.column_width = 38;\n\t\t} else if(scale === 'Week') {\n\t\t\tself.config.step = 24 * 7;\n\t\t\tself.config.column_width = 140;\n\t\t} else if(scale === 'Month') {\n\t\t\tself.config.step = 24 * 30;\n\t\t\tself.config.column_width = 120;\n\t\t}\n\t}\n\n\tfunction set_width() {\n\t\tconst cur_width = self.canvas.node.getBoundingClientRect().width;\n\t\tconst actual_width = self.canvas.select('#grid .grid-row').attr('width');\n\t\tif(cur_width < actual_width) {\n\t\t\tself.canvas.attr('width', actual_width);\n\t\t}\n\t}\n\n\tfunction set_scroll_position() {\n\t\tconst parent_element = self.element.parentElement;\n\n\t\tif(!parent_element) return;\n\n\t\tconst scroll_pos = get_min_date().diff(self.gantt_start, 'hours') /\n\t\t\tself.config.step * self.config.column_width - self.config.column_width;\n\t\tparent_element.scrollLeft = scroll_pos;\n\t}\n\n\tfunction get_min_date() {\n\t\tconst task = self.tasks.reduce((acc, curr) => {\n\t\t\treturn curr._start.isSameOrBefore(acc._start) ? curr : acc;\n\t\t});\n\t\treturn task._start;\n\t}\n\n\tfunction make_grid() {\n\t\tmake_grid_background();\n\t\tmake_grid_rows();\n\t\tmake_grid_header();\n\t\tmake_grid_ticks();\n\t\tmake_grid_highlights();\n\t}\n\n\tfunction make_grid_background() {\n\n\t\tconst grid_width = self.dates.length * self.config.column_width,\n\t\t\tgrid_height = self.config.header_height + self.config.padding +\n\t\t\t\t(self.config.bar.height + self.config.padding) * self.tasks.length;\n\n\t\tself.canvas.rect(0, 0, grid_width, grid_height)\n\t\t\t.addClass('grid-background')\n\t\t\t.appendTo(self.element_groups.grid);\n\n\t\tself.canvas.attr({\n\t\t\theight: grid_height + self.config.padding + 100,\n\t\t\twidth: '100%'\n\t\t});\n\t}\n\n\tfunction make_grid_header() {\n\t\tconst header_width = self.dates.length * self.config.column_width,\n\t\t\theader_height = self.config.header_height + 10;\n\t\tself.canvas.rect(0, 0, header_width, header_height)\n\t\t\t.addClass('grid-header')\n\t\t\t.appendTo(self.element_groups.grid);\n\t}\n\n\tfunction make_grid_rows() {\n\n\t\tconst rows = self.canvas.group().appendTo(self.element_groups.grid),\n\t\t\tlines = self.canvas.group().appendTo(self.element_groups.grid),\n\t\t\trow_width = self.dates.length * self.config.column_width,\n\t\t\trow_height = self.config.bar.height + self.config.padding;\n\n\t\tlet row_y = self.config.header_height + self.config.padding / 2;\n\n\t\tfor(let task of self.tasks) { // eslint-disable-line\n\t\t\tself.canvas.rect(0, row_y, row_width, row_height)\n\t\t\t\t.addClass('grid-row')\n\t\t\t\t.appendTo(rows);\n\n\t\t\tself.canvas.line(0, row_y + row_height, row_width, row_y + row_height)\n\t\t\t\t.addClass('row-line')\n\t\t\t\t.appendTo(lines);\n\n\t\t\trow_y += self.config.bar.height + self.config.padding;\n\t\t}\n\t}\n\n\tfunction make_grid_ticks() {\n\t\tlet tick_x = 0,\n\t\t\ttick_y = self.config.header_height + self.config.padding / 2,\n\t\t\ttick_height = (self.config.bar.height + self.config.padding) * self.tasks.length;\n\n\t\tfor(let date of self.dates) {\n\t\t\tlet tick_class = 'tick';\n\t\t\t// thick tick for monday\n\t\t\tif(view_is('Day') && date.day() === 1) {\n\t\t\t\ttick_class += ' thick';\n\t\t\t}\n\t\t\t// thick tick for first week\n\t\t\tif(view_is('Week') && date.date() >= 1 && date.date() < 8) {\n\t\t\t\ttick_class += ' thick';\n\t\t\t}\n\t\t\t// thick ticks for quarters\n\t\t\tif(view_is('Month') && date.month() % 3 === 0) {\n\t\t\t\ttick_class += ' thick';\n\t\t\t}\n\n\t\t\tself.canvas.path(Snap.format('M {x} {y} v {height}', {\n\t\t\t\tx: tick_x,\n\t\t\t\ty: tick_y,\n\t\t\t\theight: tick_height\n\t\t\t}))\n\t\t\t.addClass(tick_class)\n\t\t\t.appendTo(self.element_groups.grid);\n\n\t\t\tif(view_is('Month')) {\n\t\t\t\ttick_x += date.daysInMonth() * self.config.column_width / 30;\n\t\t\t} else {\n\t\t\t\ttick_x += self.config.column_width;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction make_grid_highlights() {\n\n\t\t// highlight today's date\n\t\tif(view_is('Day')) {\n\t\t\tconst x = moment().startOf('day').diff(self.gantt_start, 'hours') /\n\t\t\t\t\tself.config.step * self.config.column_width;\n\t\t\tconst y = 0;\n\t\t\tconst width = self.config.column_width;\n\t\t\tconst height = (self.config.bar.height + self.config.padding) * self.tasks.length +\n\t\t\t\tself.config.header_height + self.config.padding / 2;\n\n\t\t\tself.canvas.rect(x, y, width, height)\n\t\t\t\t.addClass('today-highlight')\n\t\t\t\t.appendTo(self.element_groups.grid);\n\t\t}\n\t}\n\n\tfunction make_dates() {\n\n\t\tfor(let date of get_dates_to_draw()) {\n\t\t\tself.canvas.text(date.lower_x, date.lower_y, date.lower_text)\n\t\t\t\t.addClass('lower-text')\n\t\t\t\t.appendTo(self.element_groups.date);\n\n\t\t\tif(date.upper_text) {\n\t\t\t\tconst $upper_text = self.canvas.text(date.upper_x, date.upper_y, date.upper_text)\n\t\t\t\t\t.addClass('upper-text')\n\t\t\t\t\t.appendTo(self.element_groups.date);\n\n\t\t\t\t// remove out-of-bound dates\n\t\t\t\tif($upper_text.getBBox().x2 > self.element_groups.grid.getBBox().width) {\n\t\t\t\t\t$upper_text.remove();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction get_dates_to_draw() {\n\t\tlet last_date = null;\n\t\tconst dates = self.dates.map((date, i) => {\n\t\t\tconst d = get_date_info(date, last_date, i);\n\t\t\tlast_date = date;\n\t\t\treturn d;\n\t\t});\n\t\treturn dates;\n\t}\n\n\tfunction get_date_info(date, last_date, i) {\n\t\tif(!last_date) {\n\t\t\tlast_date = date.clone().add(1, 'year');\n\t\t}\n\t\tconst date_text = {\n\t\t\t'Quarter Day_lower': date.format('HH'),\n\t\t\t'Half Day_lower': date.format('HH'),\n\t\t\t'Day_lower': date.date() !== last_date.date() ? date.format('D') : '',\n\t\t\t'Week_lower': date.month() !== last_date.month() ?\n\t\t\t\tdate.format('D MMM') : date.format('D'),\n\t\t\t'Month_lower': date.format('MMMM'),\n\t\t\t'Quarter Day_upper': date.date() !== last_date.date() ? date.format('D MMM') : '',\n\t\t\t'Half Day_upper': date.date() !== last_date.date() ?\n\t\t\t\tdate.month() !== last_date.month() ?\n\t\t\t\tdate.format('D MMM') : date.format('D') : '',\n\t\t\t'Day_upper': date.month() !== last_date.month() ? date.format('MMMM') : '',\n\t\t\t'Week_upper': date.month() !== last_date.month() ? date.format('MMMM') : '',\n\t\t\t'Month_upper': date.year() !== last_date.year() ? date.format('YYYY') : ''\n\t\t};\n\n\t\tconst base_pos = {\n\t\t\tx: i * self.config.column_width,\n\t\t\tlower_y: self.config.header_height,\n\t\t\tupper_y: self.config.header_height - 25\n\t\t};\n\n\t\tconst x_pos = {\n\t\t\t'Quarter Day_lower': (self.config.column_width * 4) / 2,\n\t\t\t'Quarter Day_upper': 0,\n\t\t\t'Half Day_lower': (self.config.column_width * 2) / 2,\n\t\t\t'Half Day_upper': 0,\n\t\t\t'Day_lower': self.config.column_width / 2,\n\t\t\t'Day_upper': (self.config.column_width * 30) / 2,\n\t\t\t'Week_lower': 0,\n\t\t\t'Week_upper': (self.config.column_width * 4) / 2,\n\t\t\t'Month_lower': self.config.column_width / 2,\n\t\t\t'Month_upper': (self.config.column_width * 12) / 2\n\t\t};\n\n\t\treturn {\n\t\t\tupper_text: date_text[`${self.config.view_mode}_upper`],\n\t\t\tlower_text: date_text[`${self.config.view_mode}_lower`],\n\t\t\tupper_x: base_pos.x + x_pos[`${self.config.view_mode}_upper`],\n\t\t\tupper_y: base_pos.upper_y,\n\t\t\tlower_x: base_pos.x + x_pos[`${self.config.view_mode}_lower`],\n\t\t\tlower_y: base_pos.lower_y\n\t\t};\n\t}\n\n\tfunction make_arrows() {\n\t\tself._arrows = [];\n\t\tfor(let task of self.tasks) {\n\t\t\tlet arrows = [];\n\t\t\tarrows = task.dependencies.map(dep => {\n\t\t\t\tconst dependency = get_task(dep);\n\t\t\t\tif(!dependency) return;\n\n\t\t\t\tconst arrow = Arrow(\n\t\t\t\t\tself, // gt\n\t\t\t\t\tself._bars[dependency._index], // from_task\n\t\t\t\t\tself._bars[task._index] // to_task\n\t\t\t\t);\n\t\t\t\tself.element_groups.arrow.add(arrow.element);\n\t\t\t\treturn arrow; // eslint-disable-line\n\t\t\t}).filter(arr => arr); // filter falsy values\n\t\t\tself._arrows = self._arrows.concat(arrows);\n\t\t}\n\t}\n\n\tfunction make_bars() {\n\n\t\tself._bars = self.tasks.map((task) => {\n\t\t\tconst bar = Bar(self, task);\n\t\t\tself.element_groups.bar.add(bar.group);\n\t\t\treturn bar;\n\t\t});\n\t}\n\n\tfunction map_arrows_on_bars() {\n\t\tfor(let bar of self._bars) {\n\t\t\tbar.arrows = self._arrows.filter(arrow => {\n\t\t\t\treturn (arrow.from_task.task.id === bar.task.id) ||\n\t\t\t\t\t(arrow.to_task.task.id === bar.task.id);\n\t\t\t});\n\t\t}\n\t}\n\n\tfunction bind_grid_click() {\n\t\tself.element_groups.grid.click(() => {\n\t\t\tunselect_all();\n\t\t\tself.element_groups.details\n\t\t\t\t.selectAll('.details-wrapper')\n\t\t\t\t.forEach(el => el.addClass('hide'));\n\t\t});\n\t}\n\n\tfunction unselect_all() {\n\t\tself.canvas.selectAll('.bar-wrapper').forEach(el => {\n\t\t\tel.removeClass('active');\n\t\t});\n\t}\n\n\tfunction view_is(modes) {\n\t\tif (typeof modes === 'string') {\n\t\t\treturn self.config.view_mode === modes;\n\t\t} else if(Array.isArray(modes)) {\n\t\t\tfor (let mode of modes) {\n\t\t\t\tif(self.config.view_mode === mode) return true;\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tfunction get_task(id) {\n\t\treturn self.tasks.find((task) => {\n\t\t\treturn task.id === id;\n\t\t});\n\t}\n\n\tfunction get_bar(id) {\n\t\treturn self._bars.find((bar) => {\n\t\t\treturn bar.task.id === id;\n\t\t});\n\t}\n\n\tfunction generate_id(task) {\n\t\treturn task.name + '_' + Math.random().toString(36).slice(2, 12);\n\t}\n\n\tfunction trigger_event(event, args) {\n\t\tif(self.config['on_' + event]) {\n\t\t\tself.config['on_' + event].apply(null, args);\n\t\t}\n\t}\n\n\tinit();\n\n\treturn self;\n}\n\n\n\n/** WEBPACK FOOTER **\n ** ./~/eslint-loader!./src/Gantt.js\n **/","// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// add the styles to the DOM\nvar update = require(\"!../node_modules/style-loader/addStyles.js\")(content, {});\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\", function() {\n\t\t\tvar newContent = require(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./src/gantt.scss\n ** module id = 1\n ** module chunks = 0\n **/","exports = module.exports = require(\"../node_modules/css-loader/lib/css-base.js\")();\n// imports\n\n\n// module\nexports.push([module.id, \".gantt .grid-background{fill:none}.gantt .grid-header{fill:#fff;stroke:#e0e0e0;stroke-width:1.4}.gantt .grid-row{fill:#fff}.gantt .grid-row:nth-child(2n){fill:#f5f5f5}.gantt .row-line{stroke:#ebeff2}.gantt .tick{stroke:#e0e0e0;stroke-width:.2}.gantt .tick.thick{stroke-width:.4}.gantt .today-highlight{fill:#fcf8e3;opacity:.5}.gantt #arrow{fill:none;stroke:#666;stroke-width:1.4}.gantt .bar{fill:#b8c2cc;stroke:#8d99a6;stroke-width:0;transition:stroke-width .3s ease}.gantt .bar-progress{fill:#a3a3ff}.gantt .bar-invalid{fill:transparent;stroke:#8d99a6;stroke-width:1;stroke-dasharray:5}.gantt .bar-invalid~.bar-label{fill:#555}.gantt .bar-label{fill:#fff;dominant-baseline:central;text-anchor:middle;font-size:12px;font-weight:lighter}.gantt .bar-label.big{fill:#555;text-anchor:start}.gantt .handle{fill:#ddd;cursor:ew-resize;opacity:0;visibility:hidden;transition:opacity .3s ease}.gantt .bar-wrapper{cursor:pointer}.gantt .bar-wrapper:hover .bar{stroke-width:2}.gantt .bar-wrapper:hover .handle{visibility:visible;opacity:1}.gantt .bar-wrapper.active .bar{stroke-width:2}.gantt .lower-text,.gantt .upper-text{font-size:12px;text-anchor:middle}.gantt .upper-text{fill:#555}.gantt .lower-text{fill:#333}.gantt #details .details-container{background:#fff;display:inline-block;padding:12px}.gantt #details .details-container h5,.gantt #details .details-container p{margin:0}.gantt #details .details-container h5{font-size:12px;font-weight:700;margin-bottom:10px;color:#555}.gantt #details .details-container p{font-size:12px;margin-bottom:6px;color:#666}.gantt #details .details-container p:last-child{margin-bottom:0}.gantt .hide{display:none}\", \"\", {\"version\":3,\"sources\":[\"/Users/<USER>/WebstormProjects/gantt/src/src/gantt.scss\"],\"names\":[],\"mappings\":\"AAYA,wBAGE,SAAU,CAHZ,oBAME,UACA,eACA,gBAAiB,CARnB,iBAWE,SAAa,CAXf,+BAcE,YAvBgB,CASlB,iBAiBE,cAzB0B,CAQ5B,aAoBE,eACA,eAAiB,CArBnB,mBAuBG,eAAiB,CAvBpB,wBA2BE,aACA,UAAY,CA5Bd,cAgCE,UACA,YACA,gBAAiB,CAlCnB,YAsCE,aACA,eACA,eACA,gCAAiC,CAzCnC,qBA4CE,YA/CY,CAGd,oBA+CE,iBACA,eACA,eACA,kBAAmB,CAlDrB,+BAqDG,SA1Dc,CAKjB,kBAyDE,UACA,0BACA,mBACA,eACA,mBAAoB,CA7DtB,sBAgEG,UACA,iBAAkB,CAjErB,eAsEE,UACA,iBACA,UACA,kBACA,2BAA4B,CA1E9B,oBA8EE,cAAe,CA9EjB,+BAkFI,cAAe,CAlFnB,kCAsFI,mBACA,SAAU,CAvFd,gCA6FI,cAAe,CA7FnB,sCAmGE,eACA,kBAAmB,CApGrB,mBAuGE,SA5Ge,CAKjB,mBA0GE,SA9Ge,CAIjB,mCA8GE,gBACA,qBACA,YAAa,CAhHf,2EAmHG,QAAS,CAnHZ,sCAuHG,eACA,gBACA,mBACA,UA/Hc,CAKjB,qCA8HG,eACA,kBACA,UAtIc,CAMjB,gDAoIG,eAAgB,CApInB,aAyIE,YAAa,CACb\",\"file\":\"gantt.scss\",\"sourcesContent\":[\"$bar-color: #b8c2cc;\\n$bar-stroke: #8D99A6;\\n$border-color: #e0e0e0;\\n$light-bg: #f5f5f5;\\n$light-border-color: #ebeff2;\\n$light-yellow: #fcf8e3;\\n$text-muted: #666;\\n$text-light: #555;\\n$text-color: #333;\\n$blue: #a3a3ff;\\n$handle-color: #ddd;\\n\\n.gantt {\\n\\n\\t.grid-background {\\n\\t\\tfill: none;\\n\\t}\\n\\t.grid-header {\\n\\t\\tfill: #ffffff;\\n\\t\\tstroke: $border-color;\\n\\t\\tstroke-width: 1.4;\\n\\t}\\n\\t.grid-row {\\n\\t\\tfill: #ffffff;\\n\\t}\\n\\t.grid-row:nth-child(even) {\\n\\t\\tfill: $light-bg;\\n\\t}\\n\\t.row-line {\\n\\t\\tstroke: $light-border-color;\\n\\t}\\n\\t.tick {\\n\\t\\tstroke: $border-color;\\n\\t\\tstroke-width: 0.2;\\n\\t\\t&.thick {\\n\\t\\t\\tstroke-width: 0.4;\\n\\t\\t}\\n\\t}\\n\\t.today-highlight {\\n\\t\\tfill: $light-yellow;\\n\\t\\topacity: 0.5;\\n\\t}\\n\\n\\t#arrow {\\n\\t\\tfill: none;\\n\\t\\tstroke: $text-muted;\\n\\t\\tstroke-width: 1.4;\\n\\t}\\n\\n\\t.bar {\\n\\t\\tfill: $bar-color;\\n\\t\\tstroke: $bar-stroke;\\n\\t\\tstroke-width: 0;\\n\\t\\ttransition: stroke-width .3s ease;\\n\\t}\\n\\t.bar-progress {\\n\\t\\tfill: $blue;\\n\\t}\\n\\t.bar-invalid {\\n\\t\\tfill: transparent;\\n\\t\\tstroke: $bar-stroke;\\n\\t\\tstroke-width: 1;\\n\\t\\tstroke-dasharray: 5;\\n\\n\\t\\t&~.bar-label {\\n\\t\\t\\tfill: $text-light;\\n\\t\\t}\\n\\t}\\n\\t.bar-label {\\n\\t\\tfill: #fff;\\n\\t\\tdominant-baseline: central;\\n\\t\\ttext-anchor: middle;\\n\\t\\tfont-size: 12px;\\n\\t\\tfont-weight: lighter;\\n\\n\\t\\t&.big {\\n\\t\\t\\tfill: $text-light;\\n\\t\\t\\ttext-anchor: start;\\n\\t\\t}\\n\\t}\\n\\n\\t.handle {\\n\\t\\tfill: $handle-color;\\n\\t\\tcursor: ew-resize;\\n\\t\\topacity: 0;\\n\\t\\tvisibility: hidden;\\n\\t\\ttransition: opacity .3s ease;\\n\\t}\\n\\n\\t.bar-wrapper {\\n\\t\\tcursor: pointer;\\n\\n\\t\\t&:hover {\\n\\t\\t\\t.bar {\\n\\t\\t\\t\\tstroke-width: 2;\\n\\t\\t\\t}\\n\\n\\t\\t\\t.handle {\\n\\t\\t\\t\\tvisibility: visible;\\n\\t\\t\\t\\topacity: 1;\\n\\t\\t\\t}\\n\\t\\t}\\n\\n\\t\\t&.active {\\n\\t\\t\\t.bar {\\n\\t\\t\\t\\tstroke-width: 2;\\n\\t\\t\\t}\\n\\t\\t}\\n\\t}\\n\\n\\t.lower-text, .upper-text {\\n\\t\\tfont-size: 12px;\\n\\t\\ttext-anchor: middle;\\n\\t}\\n\\t.upper-text {\\n\\t\\tfill: $text-light;\\n\\t}\\n\\t.lower-text {\\n\\t\\tfill: $text-color;\\n\\t}\\n\\n\\t#details .details-container {\\n\\t\\tbackground: #fff;\\n\\t\\tdisplay: inline-block;\\n\\t\\tpadding: 12px;\\n\\n\\t\\th5, p {\\n\\t\\t\\tmargin: 0;\\n\\t\\t}\\n\\n\\t\\th5 {\\n\\t\\t\\tfont-size: 12px;\\n\\t\\t\\tfont-weight: bold;\\n\\t\\t\\tmargin-bottom: 10px;\\n\\t\\t\\tcolor: $text-light;\\n\\t\\t}\\n\\n\\t\\tp {\\n\\t\\t\\tfont-size: 12px;\\n\\t\\t\\tmargin-bottom: 6px;\\n\\t\\t\\tcolor: $text-muted;\\n\\t\\t}\\n\\n\\t\\tp:last-child {\\n\\t\\t\\tmargin-bottom: 0;\\n\\t\\t}\\n\\t}\\n\\n\\t.hide {\\n\\t\\tdisplay: none;\\n\\t}\\n}\\n\"],\"sourceRoot\":\"\"}]);\n\n// exports\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/css-loader?sourceMap!./~/sass-loader?sourceMap!./src/gantt.scss\n ** module id = 2\n ** module chunks = 0\n **/","/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\n// css base code, injected by the css-loader\nmodule.exports = function() {\n\tvar list = [];\n\n\t// return the list of modules as css string\n\tlist.toString = function toString() {\n\t\tvar result = [];\n\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\tvar item = this[i];\n\t\t\tif(item[2]) {\n\t\t\t\tresult.push(\"@media \" + item[2] + \"{\" + item[1] + \"}\");\n\t\t\t} else {\n\t\t\t\tresult.push(item[1]);\n\t\t\t}\n\t\t}\n\t\treturn result.join(\"\");\n\t};\n\n\t// import a list of modules into the list\n\tlist.i = function(modules, mediaQuery) {\n\t\tif(typeof modules === \"string\")\n\t\t\tmodules = [[null, modules, \"\"]];\n\t\tvar alreadyImportedModules = {};\n\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\tvar id = this[i][0];\n\t\t\tif(typeof id === \"number\")\n\t\t\t\talreadyImportedModules[id] = true;\n\t\t}\n\t\tfor(i = 0; i < modules.length; i++) {\n\t\t\tvar item = modules[i];\n\t\t\t// skip already imported module\n\t\t\t// this implementation is not 100% perfect for weird media query combinations\n\t\t\t//  when a module is imported multiple times with different media queries.\n\t\t\t//  I hope this will never occur (Hey this way we have smaller bundles)\n\t\t\tif(typeof item[0] !== \"number\" || !alreadyImportedModules[item[0]]) {\n\t\t\t\tif(mediaQuery && !item[2]) {\n\t\t\t\t\titem[2] = mediaQuery;\n\t\t\t\t} else if(mediaQuery) {\n\t\t\t\t\titem[2] = \"(\" + item[2] + \") and (\" + mediaQuery + \")\";\n\t\t\t\t}\n\t\t\t\tlist.push(item);\n\t\t\t}\n\t\t}\n\t};\n\treturn list;\n};\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/css-loader/lib/css-base.js\n ** module id = 3\n ** module chunks = 0\n **/","/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\nvar stylesInDom = {},\n\tmemoize = function(fn) {\n\t\tvar memo;\n\t\treturn function () {\n\t\t\tif (typeof memo === \"undefined\") memo = fn.apply(this, arguments);\n\t\t\treturn memo;\n\t\t};\n\t},\n\tisOldIE = memoize(function() {\n\t\treturn /msie [6-9]\\b/.test(self.navigator.userAgent.toLowerCase());\n\t}),\n\tgetHeadElement = memoize(function () {\n\t\treturn document.head || document.getElementsByTagName(\"head\")[0];\n\t}),\n\tsingletonElement = null,\n\tsingletonCounter = 0,\n\tstyleElementsInsertedAtTop = [];\n\nmodule.exports = function(list, options) {\n\tif(typeof DEBUG !== \"undefined\" && DEBUG) {\n\t\tif(typeof document !== \"object\") throw new Error(\"The style-loader cannot be used in a non-browser environment\");\n\t}\n\n\toptions = options || {};\n\t// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n\t// tags it will allow on a page\n\tif (typeof options.singleton === \"undefined\") options.singleton = isOldIE();\n\n\t// By default, add <style> tags to the bottom of <head>.\n\tif (typeof options.insertAt === \"undefined\") options.insertAt = \"bottom\";\n\n\tvar styles = listToStyles(list);\n\taddStylesToDom(styles, options);\n\n\treturn function update(newList) {\n\t\tvar mayRemove = [];\n\t\tfor(var i = 0; i < styles.length; i++) {\n\t\t\tvar item = styles[i];\n\t\t\tvar domStyle = stylesInDom[item.id];\n\t\t\tdomStyle.refs--;\n\t\t\tmayRemove.push(domStyle);\n\t\t}\n\t\tif(newList) {\n\t\t\tvar newStyles = listToStyles(newList);\n\t\t\taddStylesToDom(newStyles, options);\n\t\t}\n\t\tfor(var i = 0; i < mayRemove.length; i++) {\n\t\t\tvar domStyle = mayRemove[i];\n\t\t\tif(domStyle.refs === 0) {\n\t\t\t\tfor(var j = 0; j < domStyle.parts.length; j++)\n\t\t\t\t\tdomStyle.parts[j]();\n\t\t\t\tdelete stylesInDom[domStyle.id];\n\t\t\t}\n\t\t}\n\t};\n}\n\nfunction addStylesToDom(styles, options) {\n\tfor(var i = 0; i < styles.length; i++) {\n\t\tvar item = styles[i];\n\t\tvar domStyle = stylesInDom[item.id];\n\t\tif(domStyle) {\n\t\t\tdomStyle.refs++;\n\t\t\tfor(var j = 0; j < domStyle.parts.length; j++) {\n\t\t\t\tdomStyle.parts[j](item.parts[j]);\n\t\t\t}\n\t\t\tfor(; j < item.parts.length; j++) {\n\t\t\t\tdomStyle.parts.push(addStyle(item.parts[j], options));\n\t\t\t}\n\t\t} else {\n\t\t\tvar parts = [];\n\t\t\tfor(var j = 0; j < item.parts.length; j++) {\n\t\t\t\tparts.push(addStyle(item.parts[j], options));\n\t\t\t}\n\t\t\tstylesInDom[item.id] = {id: item.id, refs: 1, parts: parts};\n\t\t}\n\t}\n}\n\nfunction listToStyles(list) {\n\tvar styles = [];\n\tvar newStyles = {};\n\tfor(var i = 0; i < list.length; i++) {\n\t\tvar item = list[i];\n\t\tvar id = item[0];\n\t\tvar css = item[1];\n\t\tvar media = item[2];\n\t\tvar sourceMap = item[3];\n\t\tvar part = {css: css, media: media, sourceMap: sourceMap};\n\t\tif(!newStyles[id])\n\t\t\tstyles.push(newStyles[id] = {id: id, parts: [part]});\n\t\telse\n\t\t\tnewStyles[id].parts.push(part);\n\t}\n\treturn styles;\n}\n\nfunction insertStyleElement(options, styleElement) {\n\tvar head = getHeadElement();\n\tvar lastStyleElementInsertedAtTop = styleElementsInsertedAtTop[styleElementsInsertedAtTop.length - 1];\n\tif (options.insertAt === \"top\") {\n\t\tif(!lastStyleElementInsertedAtTop) {\n\t\t\thead.insertBefore(styleElement, head.firstChild);\n\t\t} else if(lastStyleElementInsertedAtTop.nextSibling) {\n\t\t\thead.insertBefore(styleElement, lastStyleElementInsertedAtTop.nextSibling);\n\t\t} else {\n\t\t\thead.appendChild(styleElement);\n\t\t}\n\t\tstyleElementsInsertedAtTop.push(styleElement);\n\t} else if (options.insertAt === \"bottom\") {\n\t\thead.appendChild(styleElement);\n\t} else {\n\t\tthrow new Error(\"Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.\");\n\t}\n}\n\nfunction removeStyleElement(styleElement) {\n\tstyleElement.parentNode.removeChild(styleElement);\n\tvar idx = styleElementsInsertedAtTop.indexOf(styleElement);\n\tif(idx >= 0) {\n\t\tstyleElementsInsertedAtTop.splice(idx, 1);\n\t}\n}\n\nfunction createStyleElement(options) {\n\tvar styleElement = document.createElement(\"style\");\n\tstyleElement.type = \"text/css\";\n\tinsertStyleElement(options, styleElement);\n\treturn styleElement;\n}\n\nfunction createLinkElement(options) {\n\tvar linkElement = document.createElement(\"link\");\n\tlinkElement.rel = \"stylesheet\";\n\tinsertStyleElement(options, linkElement);\n\treturn linkElement;\n}\n\nfunction addStyle(obj, options) {\n\tvar styleElement, update, remove;\n\n\tif (options.singleton) {\n\t\tvar styleIndex = singletonCounter++;\n\t\tstyleElement = singletonElement || (singletonElement = createStyleElement(options));\n\t\tupdate = applyToSingletonTag.bind(null, styleElement, styleIndex, false);\n\t\tremove = applyToSingletonTag.bind(null, styleElement, styleIndex, true);\n\t} else if(obj.sourceMap &&\n\t\ttypeof URL === \"function\" &&\n\t\ttypeof URL.createObjectURL === \"function\" &&\n\t\ttypeof URL.revokeObjectURL === \"function\" &&\n\t\ttypeof Blob === \"function\" &&\n\t\ttypeof btoa === \"function\") {\n\t\tstyleElement = createLinkElement(options);\n\t\tupdate = updateLink.bind(null, styleElement);\n\t\tremove = function() {\n\t\t\tremoveStyleElement(styleElement);\n\t\t\tif(styleElement.href)\n\t\t\t\tURL.revokeObjectURL(styleElement.href);\n\t\t};\n\t} else {\n\t\tstyleElement = createStyleElement(options);\n\t\tupdate = applyToTag.bind(null, styleElement);\n\t\tremove = function() {\n\t\t\tremoveStyleElement(styleElement);\n\t\t};\n\t}\n\n\tupdate(obj);\n\n\treturn function updateStyle(newObj) {\n\t\tif(newObj) {\n\t\t\tif(newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap)\n\t\t\t\treturn;\n\t\t\tupdate(obj = newObj);\n\t\t} else {\n\t\t\tremove();\n\t\t}\n\t};\n}\n\nvar replaceText = (function () {\n\tvar textStore = [];\n\n\treturn function (index, replacement) {\n\t\ttextStore[index] = replacement;\n\t\treturn textStore.filter(Boolean).join('\\n');\n\t};\n})();\n\nfunction applyToSingletonTag(styleElement, index, remove, obj) {\n\tvar css = remove ? \"\" : obj.css;\n\n\tif (styleElement.styleSheet) {\n\t\tstyleElement.styleSheet.cssText = replaceText(index, css);\n\t} else {\n\t\tvar cssNode = document.createTextNode(css);\n\t\tvar childNodes = styleElement.childNodes;\n\t\tif (childNodes[index]) styleElement.removeChild(childNodes[index]);\n\t\tif (childNodes.length) {\n\t\t\tstyleElement.insertBefore(cssNode, childNodes[index]);\n\t\t} else {\n\t\t\tstyleElement.appendChild(cssNode);\n\t\t}\n\t}\n}\n\nfunction applyToTag(styleElement, obj) {\n\tvar css = obj.css;\n\tvar media = obj.media;\n\n\tif(media) {\n\t\tstyleElement.setAttribute(\"media\", media)\n\t}\n\n\tif(styleElement.styleSheet) {\n\t\tstyleElement.styleSheet.cssText = css;\n\t} else {\n\t\twhile(styleElement.firstChild) {\n\t\t\tstyleElement.removeChild(styleElement.firstChild);\n\t\t}\n\t\tstyleElement.appendChild(document.createTextNode(css));\n\t}\n}\n\nfunction updateLink(linkElement, obj) {\n\tvar css = obj.css;\n\tvar sourceMap = obj.sourceMap;\n\n\tif(sourceMap) {\n\t\t// http://stackoverflow.com/a/26603875\n\t\tcss += \"\\n/*# sourceMappingURL=data:application/json;base64,\" + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + \" */\";\n\t}\n\n\tvar blob = new Blob([css], { type: \"text/css\" });\n\n\tvar oldSrc = linkElement.href;\n\n\tlinkElement.href = URL.createObjectURL(blob);\n\n\tif(oldSrc)\n\t\tURL.revokeObjectURL(oldSrc);\n}\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/style-loader/addStyles.js\n ** module id = 4\n ** module chunks = 0\n **/","/* global Snap */\n/*\n\tClass: Bar\n\n\tOpts:\n\t\tgt: Gantt object\n\t\ttask: task object\n*/\n\nexport default function Bar(gt, task) {\n\n\tconst self = {};\n\n\tfunction init() {\n\t\tset_defaults();\n\t\tprepare();\n\t\tdraw();\n\t\tbind();\n\t}\n\n\tfunction set_defaults() {\n\t\tself.action_completed = false;\n\t\tself.task = task;\n\t}\n\n\tfunction prepare() {\n\t\tprepare_values();\n\t\tprepare_plugins();\n\t}\n\n\tfunction prepare_values() {\n\t\tself.invalid = self.task.invalid;\n\t\tself.height = gt.config.bar.height;\n\t\tself.x = compute_x();\n\t\tself.y = compute_y();\n\t\tself.corner_radius = gt.config.bar.corner_radius;\n\t\tself.duration = (self.task._end.diff(self.task._start, 'hours') + 24) / gt.config.step;\n\t\tself.width = gt.config.column_width * self.duration;\n\t\tself.progress_width = gt.config.column_width * self.duration * (self.task.progress / 100) || 0;\n\t\tself.group = gt.canvas.group().addClass('bar-wrapper').addClass(self.task.custom_class || '');\n\t\tself.bar_group = gt.canvas.group().addClass('bar-group').appendTo(self.group);\n\t\tself.handle_group = gt.canvas.group().addClass('handle-group').appendTo(self.group);\n\t}\n\n\tfunction prepare_plugins() {\n\t\tSnap.plugin(function (Snap, Element, Paper, global, Fragment) {\n\t\t\tElement.prototype.getX = function () {\n\t\t\t\treturn +this.attr('x');\n\t\t\t};\n\t\t\tElement.prototype.getY = function () {\n\t\t\t\treturn +this.attr('y');\n\t\t\t};\n\t\t\tElement.prototype.getWidth = function () {\n\t\t\t\treturn +this.attr('width');\n\t\t\t};\n\t\t\tElement.prototype.getHeight = function () {\n\t\t\t\treturn +this.attr('height');\n\t\t\t};\n\t\t\tElement.prototype.getEndX = function () {\n\t\t\t\treturn this.getX() + this.getWidth();\n\t\t\t};\n\t\t});\n\t}\n\n\tfunction draw() {\n\t\tdraw_bar();\n\t\tdraw_progress_bar();\n\t\tdraw_label();\n\t\tdraw_resize_handles();\n\t}\n\n\tfunction draw_bar() {\n\t\tself.$bar = gt.canvas.rect(self.x, self.y,\n\t\t\tself.width, self.height,\n\t\t\tself.corner_radius, self.corner_radius)\n\t\t\t.addClass('bar')\n\t\t\t.appendTo(self.bar_group);\n\t\tif (self.invalid) {\n\t\t\tself.$bar.addClass('bar-invalid');\n\t\t}\n\t}\n\n\tfunction draw_progress_bar() {\n\t\tif (self.invalid) return;\n\t\tself.$bar_progress = gt.canvas.rect(self.x, self.y,\n\t\t\tself.progress_width, self.height,\n\t\t\tself.corner_radius, self.corner_radius)\n\t\t\t.addClass('bar-progress')\n\t\t\t.appendTo(self.bar_group);\n\t}\n\n\tfunction draw_label() {\n\t\tgt.canvas.text(self.x + self.width / 2,\n\t\t\tself.y + self.height / 2,\n\t\t\tself.task.name)\n\t\t\t.addClass('bar-label')\n\t\t\t.appendTo(self.bar_group);\n\t\tupdate_label_position();\n\t}\n\n\tfunction draw_resize_handles() {\n\t\tif (self.invalid) return;\n\n\t\tconst bar = self.$bar,\n\t\t\thandle_width = 8;\n\n\t\tgt.canvas.rect(bar.getX() + bar.getWidth() - 9, bar.getY() + 1,\n\t\t\thandle_width, self.height - 2, self.corner_radius, self.corner_radius)\n\t\t\t.addClass('handle right')\n\t\t\t.appendTo(self.handle_group);\n\t\tgt.canvas.rect(bar.getX() + 1, bar.getY() + 1,\n\t\t\thandle_width, self.height - 2, self.corner_radius, self.corner_radius)\n\t\t\t.addClass('handle left')\n\t\t\t.appendTo(self.handle_group);\n\n\t\tif (self.task.progress && self.task.progress < 100) {\n\t\t\tgt.canvas.polygon(get_progress_polygon_points())\n\t\t\t\t.addClass('handle progress')\n\t\t\t\t.appendTo(self.handle_group);\n\t\t}\n\t}\n\n\tfunction get_progress_polygon_points() {\n\t\tconst bar_progress = self.$bar_progress;\n\t\treturn [\n\t\t\tbar_progress.getEndX() - 5, bar_progress.getY() + bar_progress.getHeight(),\n\t\t\tbar_progress.getEndX() + 5, bar_progress.getY() + bar_progress.getHeight(),\n\t\t\tbar_progress.getEndX(), bar_progress.getY() + bar_progress.getHeight() - 8.66\n\t\t];\n\t}\n\n\tfunction bind() {\n\t\tif (self.invalid) return;\n\t\tsetup_click_event();\n\t\tshow_details();\n\t\tbind_resize();\n\t\tbind_drag();\n\t\tbind_resize_progress();\n\t}\n\n\tfunction show_details() {\n\t\tconst popover_group = gt.element_groups.details;\n\t\tself.details_box = popover_group\n\t\t\t.select(`.details-wrapper[data-task='${self.task.id}']`);\n\n\t\tif (!self.details_box) {\n\t\t\tself.details_box = gt.canvas.group()\n\t\t\t\t.addClass('details-wrapper hide')\n\t\t\t\t.attr('data-task', self.task.id)\n\t\t\t\t.appendTo(popover_group);\n\n\t\t\trender_details();\n\n\t\t\tconst f = gt.canvas.filter(\n\t\t\t\tSnap.filter.shadow(0, 1, 1, '#666', 0.6));\n\t\t\tself.details_box.attr({\n\t\t\t\tfilter: f\n\t\t\t});\n\t\t}\n\n\t\tself.group.click((e) => {\n\t\t\tif (self.action_completed) {\n\t\t\t\t// just finished a move action, wait for a few seconds\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tpopover_group.selectAll('.details-wrapper')\n\t\t\t\t.forEach(el => el.addClass('hide'));\n\t\t\tself.details_box.removeClass('hide');\n\t\t});\n\t}\n\n\tfunction render_details() {\n\t\tconst {x, y} = get_details_position();\n\t\tself.details_box.transform(`t${x},${y}`);\n\t\tself.details_box.clear();\n\n\t\tconst html = get_details_html();\n\t\tconst foreign_object =\n\t\t\tSnap.parse(`<foreignObject width=\"5000\" height=\"2000\">\n\t\t\t\t<body xmlns=\"http://www.w3.org/1999/xhtml\">\n\t\t\t\t\t${html}\n\t\t\t\t</body>\n\t\t\t\t</foreignObject>`);\n\t\tself.details_box.append(foreign_object);\n\t}\n\n\tfunction get_details_html() {\n\n\t\t// custom html in config\n\t\tif(gt.config.custom_popup_html) {\n\t\t\tconst html = gt.config.custom_popup_html;\n\t\t\tif(typeof html === 'string') {\n\t\t\t\treturn html;\n\t\t\t}\n\t\t\tif(isFunction(html)) {\n\t\t\t\treturn html(task);\n\t\t\t}\n\t\t}\n\n\t\tconst start_date = self.task._start.format('MMM D');\n\t\tconst end_date = self.task._end.format('MMM D');\n\t\tconst heading = `${self.task.name}: ${start_date} - ${end_date}`;\n\n\t\tconst line_1 = `Duration: ${self.duration} days`;\n\t\tconst line_2 = self.task.progress ? `Progress: ${self.task.progress}` : null;\n\n\t\tconst html = `\n\t\t\t<div class=\"details-container\">\n\t\t\t\t<h5>${heading}</h5>\n\t\t\t\t<p>${line_1}</p>\n\t\t\t\t${\n\t\t\t\t\tline_2 ? `<p>${line_2}</p>` : ''\n\t\t\t\t}\n\t\t\t</div>\n\t\t`;\n\t\treturn html;\n\t}\n\n\tfunction get_details_position() {\n\t\treturn {\n\t\t\tx: self.$bar.getEndX() + 2,\n\t\t\ty: self.$bar.getY() - 10\n\t\t};\n\t}\n\n\tfunction bind_resize() {\n\t\tconst { left, right } = get_handles();\n\n\t\tleft.drag(onmove_left, onstart, onstop_left);\n\t\tright.drag(onmove_right, onstart, onstop_right);\n\n\t\tfunction onmove_right(dx, dy) {\n\t\t\tonmove_handle_right(dx, dy);\n\t\t}\n\t\tfunction onstop_right() {\n\t\t\tonstop_handle_right();\n\t\t}\n\n\t\tfunction onmove_left(dx, dy) {\n\t\t\tonmove_handle_left(dx, dy);\n\t\t}\n\t\tfunction onstop_left() {\n\t\t\tonstop_handle_left();\n\t\t}\n\t}\n\n\tfunction get_handles() {\n\t\treturn {\n\t\t\tleft: self.handle_group.select('.handle.left'),\n\t\t\tright: self.handle_group.select('.handle.right')\n\t\t};\n\t}\n\n\tfunction bind_drag() {\n\t\tself.bar_group.drag(onmove, onstart, onstop);\n\t}\n\n\tfunction bind_resize_progress() {\n\t\tconst bar = self.$bar,\n\t\t\tbar_progress = self.$bar_progress,\n\t\t\thandle = self.group.select('.handle.progress');\n\t\thandle && handle.drag(on_move, on_start, on_stop);\n\n\t\tfunction on_move(dx, dy) {\n\t\t\tif (dx > bar_progress.max_dx) {\n\t\t\t\tdx = bar_progress.max_dx;\n\t\t\t}\n\t\t\tif (dx < bar_progress.min_dx) {\n\t\t\t\tdx = bar_progress.min_dx;\n\t\t\t}\n\n\t\t\tbar_progress.attr('width', bar_progress.owidth + dx);\n\t\t\thandle.attr('points', get_progress_polygon_points());\n\t\t\tbar_progress.finaldx = dx;\n\t\t}\n\t\tfunction on_stop() {\n\t\t\tif (!bar_progress.finaldx) return;\n\t\t\tprogress_changed();\n\t\t\tset_action_completed();\n\t\t}\n\t\tfunction on_start() {\n\t\t\tbar_progress.finaldx = 0;\n\t\t\tbar_progress.owidth = bar_progress.getWidth();\n\t\t\tbar_progress.min_dx = -bar_progress.getWidth();\n\t\t\tbar_progress.max_dx = bar.getWidth() - bar_progress.getWidth();\n\t\t}\n\t}\n\n\tfunction onstart() {\n\t\tconst bar = self.$bar;\n\t\tbar.ox = bar.getX();\n\t\tbar.oy = bar.getY();\n\t\tbar.owidth = bar.getWidth();\n\t\tbar.finaldx = 0;\n\t\trun_method_for_dependencies('onstart');\n\t}\n\tself.onstart = onstart;\n\n\tfunction onmove(dx, dy) {\n\t\tconst bar = self.$bar;\n\t\tbar.finaldx = get_snap_position(dx);\n\t\tupdate_bar_position({x: bar.ox + bar.finaldx});\n\t\trun_method_for_dependencies('onmove', [dx, dy]);\n\t}\n\tself.onmove = onmove;\n\n\tfunction onstop() {\n\t\tconst bar = self.$bar;\n\t\tif (!bar.finaldx) return;\n\t\tdate_changed();\n\t\tset_action_completed();\n\t\trun_method_for_dependencies('onstop');\n\t}\n\tself.onstop = onstop;\n\n\tfunction onmove_handle_left(dx, dy) {\n\t\tconst bar = self.$bar;\n\t\tbar.finaldx = get_snap_position(dx);\n\t\tupdate_bar_position({\n\t\t\tx: bar.ox + bar.finaldx,\n\t\t\twidth: bar.owidth - bar.finaldx\n\t\t});\n\t\trun_method_for_dependencies('onmove', [dx, dy]);\n\t}\n\tself.onmove_handle_left = onmove_handle_left;\n\n\tfunction onstop_handle_left() {\n\t\tconst bar = self.$bar;\n\t\tif (bar.finaldx) date_changed();\n\t\tset_action_completed();\n\t\trun_method_for_dependencies('onstop');\n\t}\n\tself.onstop_handle_left = onstop_handle_left;\n\n\tfunction run_method_for_dependencies(fn, args) {\n\t\tconst dm = gt.dependency_map;\n\t\tif (dm[self.task.id]) {\n\t\t\tfor (let deptask of dm[self.task.id]) {\n\t\t\t\tconst dt = gt.get_bar(deptask);\n\t\t\t\tdt[fn].apply(dt, args);\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction onmove_handle_right(dx, dy) {\n\t\tconst bar = self.$bar;\n\t\tbar.finaldx = get_snap_position(dx);\n\t\tupdate_bar_position({width: bar.owidth + bar.finaldx});\n\t}\n\n\tfunction onstop_handle_right() {\n\t\tconst bar = self.$bar;\n\t\tif (bar.finaldx) date_changed();\n\t\tset_action_completed();\n\t}\n\n\tfunction update_bar_position({x = null, width = null}) {\n\t\tconst bar = self.$bar;\n\t\tif (x) {\n\t\t\t// get all x values of parent task\n\t\t\tconst xs = task.dependencies.map(dep => {\n\t\t\t\treturn gt.get_bar(dep).$bar.getX();\n\t\t\t});\n\t\t\t// child task must not go before parent\n\t\t\tconst valid_x = xs.reduce((prev, curr) => {\n\t\t\t\treturn x >= curr;\n\t\t\t}, x);\n\t\t\tif(!valid_x) {\n\t\t\t\twidth = null;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tupdate_attr(bar, 'x', x);\n\t\t}\n\t\tif (width && width >= gt.config.column_width) {\n\t\t\tupdate_attr(bar, 'width', width);\n\t\t}\n\t\tupdate_label_position();\n\t\tupdate_handle_position();\n\t\tupdate_progressbar_position();\n\t\tupdate_arrow_position();\n\t\tupdate_details_position();\n\t}\n\n\tfunction setup_click_event() {\n\t\tself.group.click(function () {\n\t\t\tif (self.action_completed) {\n\t\t\t\t// just finished a move action, wait for a few seconds\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (self.group.hasClass('active')) {\n\t\t\t\tgt.trigger_event('click', [self.task]);\n\t\t\t}\n\t\t\tgt.unselect_all();\n\t\t\tself.group.toggleClass('active');\n\t\t});\n\t}\n\n\tfunction date_changed() {\n\t\tconst { new_start_date, new_end_date } = compute_start_end_date();\n\t\tself.task._start = new_start_date;\n\t\tself.task._end = new_end_date;\n\t\trender_details();\n\t\tgt.trigger_event('date_change',\n\t\t\t[self.task, new_start_date, new_end_date]);\n\t}\n\n\tfunction progress_changed() {\n\t\tconst new_progress = compute_progress();\n\t\tself.task.progress = new_progress;\n\t\trender_details();\n\t\tgt.trigger_event('progress_change',\n\t\t\t[self.task, new_progress]);\n\t}\n\n\tfunction set_action_completed() {\n\t\tself.action_completed = true;\n\t\tsetTimeout(() => self.action_completed = false, 2000);\n\t}\n\n\tfunction compute_start_end_date() {\n\t\tconst bar = self.$bar;\n\t\tconst x_in_units = bar.getX() / gt.config.column_width;\n\t\tconst new_start_date = gt.gantt_start.clone().add(x_in_units * gt.config.step, 'hours');\n\t\tconst width_in_units = bar.getWidth() / gt.config.column_width;\n\t\tconst new_end_date = new_start_date.clone().add(width_in_units * gt.config.step, 'hours');\n\t\t// lets say duration is 2 days\n\t\t// start_date = May 24 00:00:00\n\t\t// end_date = May 24 + 2 days = May 26 (incorrect)\n\t\t// so subtract 1 second so that\n\t\t// end_date = May 25 23:59:59\n\t\tnew_end_date.add('-1', 'seconds');\n\t\treturn { new_start_date, new_end_date };\n\t}\n\n\tfunction compute_progress() {\n\t\tconst progress = self.$bar_progress.getWidth() / self.$bar.getWidth() * 100;\n\t\treturn parseInt(progress, 10);\n\t}\n\n\tfunction compute_x() {\n\t\tlet x = self.task._start.diff(gt.gantt_start, 'hours') /\n\t\t\tgt.config.step * gt.config.column_width;\n\n\t\tif (gt.view_is('Month')) {\n\t\t\tx = self.task._start.diff(gt.gantt_start, 'days') *\n\t\t\t\tgt.config.column_width / 30;\n\t\t}\n\t\treturn x;\n\t}\n\n\tfunction compute_y() {\n\t\treturn gt.config.header_height + gt.config.padding +\n\t\t\tself.task._index * (self.height + gt.config.padding);\n\t}\n\n\tfunction get_snap_position(dx) {\n\t\tlet odx = dx, rem, position;\n\n\t\tif (gt.view_is('Week')) {\n\t\t\trem = dx % (gt.config.column_width / 7);\n\t\t\tposition = odx - rem +\n\t\t\t\t((rem < gt.config.column_width / 14) ? 0 : gt.config.column_width / 7);\n\t\t} else if (gt.view_is('Month')) {\n\t\t\trem = dx % (gt.config.column_width / 30);\n\t\t\tposition = odx - rem +\n\t\t\t\t((rem < gt.config.column_width / 60) ? 0 : gt.config.column_width / 30);\n\t\t} else {\n\t\t\trem = dx % gt.config.column_width;\n\t\t\tposition = odx - rem +\n\t\t\t\t((rem < gt.config.column_width / 2) ? 0 : gt.config.column_width);\n\t\t}\n\t\treturn position;\n\t}\n\n\tfunction update_attr(element, attr, value) {\n\t\tvalue = +value;\n\t\tif (!isNaN(value)) {\n\t\t\telement.attr(attr, value);\n\t\t}\n\t\treturn element;\n\t}\n\n\tfunction update_progressbar_position() {\n\t\tself.$bar_progress.attr('x', self.$bar.getX());\n\t\tself.$bar_progress.attr('width', self.$bar.getWidth() * (self.task.progress / 100));\n\t}\n\n\tfunction update_label_position() {\n\t\tconst bar = self.$bar,\n\t\t\tlabel = self.group.select('.bar-label');\n\t\tif (label.getBBox().width > bar.getWidth()) {\n\t\t\tlabel.addClass('big').attr('x', bar.getX() + bar.getWidth() + 5);\n\t\t} else {\n\t\t\tlabel.removeClass('big').attr('x', bar.getX() + bar.getWidth() / 2);\n\t\t}\n\t}\n\n\tfunction update_handle_position() {\n\t\tconst bar = self.$bar;\n\t\tself.handle_group.select('.handle.left').attr({\n\t\t\t'x': bar.getX() + 1\n\t\t});\n\t\tself.handle_group.select('.handle.right').attr({\n\t\t\t'x': bar.getEndX() - 9\n\t\t});\n\t\tconst handle = self.group.select('.handle.progress');\n\t\thandle && handle.attr('points', get_progress_polygon_points());\n\t}\n\n\tfunction update_arrow_position() {\n\t\tfor (let arrow of self.arrows) {\n\t\t\tarrow.update();\n\t\t}\n\t}\n\n\tfunction update_details_position() {\n\t\tconst {x, y} = get_details_position();\n\t\tself.details_box && self.details_box.transform(`t${x},${y}`);\n\t}\n\n\tfunction isFunction(functionToCheck) {\n\t\tvar getType = {};\n\t\treturn functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n\t}\n\n\tinit();\n\n\treturn self;\n}\n\n\n\n/** WEBPACK FOOTER **\n ** ./~/eslint-loader!./src/Bar.js\n **/","/* global Snap */\n/*\n\tClass: Arrow\n\tfrom_task ---> to_task\n\n\tOpts:\n\t\tgantt (Gantt object)\n\t\tfrom_task (Bar object)\n\t\tto_task (Bar object)\n*/\n\nexport default function Arrow(gt, from_task, to_task) {\n\n\tconst self = {};\n\n\tfunction init() {\n\t\tself.from_task = from_task;\n\t\tself.to_task = to_task;\n\t\tprepare();\n\t\tdraw();\n\t}\n\n\tfunction prepare() {\n\n\t\tself.start_x = from_task.$bar.getX() + from_task.$bar.getWidth() / 2;\n\n\t\tconst condition = () =>\n\t\t\tto_task.$bar.getX() < self.start_x + gt.config.padding &&\n\t\t\t\tself.start_x > from_task.$bar.getX() + gt.config.padding;\n\n\t\twhile(condition()) {\n\t\t\tself.start_x -= 10;\n\t\t}\n\n\t\tself.start_y = gt.config.header_height + gt.config.bar.height +\n\t\t\t(gt.config.padding + gt.config.bar.height) * from_task.task._index +\n\t\t\tgt.config.padding;\n\n\t\tself.end_x = to_task.$bar.getX() - gt.config.padding / 2;\n\t\tself.end_y = gt.config.header_height + gt.config.bar.height / 2 +\n\t\t\t(gt.config.padding + gt.config.bar.height) * to_task.task._index +\n\t\t\tgt.config.padding;\n\n\t\tconst from_is_below_to = (from_task.task._index > to_task.task._index);\n\t\tself.curve = gt.config.arrow.curve;\n\t\tself.clockwise = from_is_below_to ? 1 : 0;\n\t\tself.curve_y = from_is_below_to ? -self.curve : self.curve;\n\t\tself.offset = from_is_below_to ?\n\t\t\tself.end_y + gt.config.arrow.curve :\n\t\t\tself.end_y - gt.config.arrow.curve;\n\n\t\tself.path =\n\t\t\tSnap.format('M {start_x} {start_y} V {offset} ' +\n\t\t\t\t'a {curve} {curve} 0 0 {clockwise} {curve} {curve_y} ' +\n\t\t\t\t'L {end_x} {end_y} m -5 -5 l 5 5 l -5 5',\n\t\t\t\t{\n\t\t\t\t\tstart_x: self.start_x,\n\t\t\t\t\tstart_y: self.start_y,\n\t\t\t\t\tend_x: self.end_x,\n\t\t\t\t\tend_y: self.end_y,\n\t\t\t\t\toffset: self.offset,\n\t\t\t\t\tcurve: self.curve,\n\t\t\t\t\tclockwise: self.clockwise,\n\t\t\t\t\tcurve_y: self.curve_y\n\t\t\t\t});\n\n\t\tif(to_task.$bar.getX() < from_task.$bar.getX() + gt.config.padding) {\n\t\t\tself.path =\n\t\t\t\tSnap.format('M {start_x} {start_y} v {down_1} ' +\n\t\t\t\t'a {curve} {curve} 0 0 1 -{curve} {curve} H {left} ' +\n\t\t\t\t'a {curve} {curve} 0 0 {clockwise} -{curve} {curve_y} V {down_2} ' +\n\t\t\t\t'a {curve} {curve} 0 0 {clockwise} {curve} {curve_y} ' +\n\t\t\t\t'L {end_x} {end_y} m -5 -5 l 5 5 l -5 5',\n\t\t\t\t\t{\n\t\t\t\t\t\tstart_x: self.start_x,\n\t\t\t\t\t\tstart_y: self.start_y,\n\t\t\t\t\t\tend_x: self.end_x,\n\t\t\t\t\t\tend_y: self.end_y,\n\t\t\t\t\t\tdown_1: gt.config.padding / 2 - self.curve,\n\t\t\t\t\t\tdown_2: to_task.$bar.getY() + to_task.$bar.getHeight() / 2 - self.curve_y,\n\t\t\t\t\t\tleft: to_task.$bar.getX() - gt.config.padding,\n\t\t\t\t\t\toffset: self.offset,\n\t\t\t\t\t\tcurve: self.curve,\n\t\t\t\t\t\tclockwise: self.clockwise,\n\t\t\t\t\t\tcurve_y: self.curve_y\n\t\t\t\t\t});\n\t\t}\n\t}\n\n\tfunction draw() {\n\t\tself.element = gt.canvas.path(self.path)\n\t\t\t.attr('data-from', self.from_task.task.id)\n\t\t\t.attr('data-to', self.to_task.task.id);\n\t}\n\n\tfunction update() { // eslint-disable-line\n\t\tprepare();\n\t\tself.element.attr('d', self.path);\n\t}\n\tself.update = update;\n\n\tinit();\n\n\treturn self;\n}\n\n\n\n/** WEBPACK FOOTER **\n ** ./~/eslint-loader!./src/Arrow.js\n **/","(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(global.deepmerge = factory());\n}(this, (function () { 'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, optionsArgument) {\n\tvar clone = !optionsArgument || optionsArgument.clone !== false;\n\n\treturn (clone && isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, optionsArgument)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, optionsArgument) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, optionsArgument)\n\t})\n}\n\nfunction mergeObject(target, source, optionsArgument) {\n\tvar destination = {};\n\tif (isMergeableObject(target)) {\n\t\tObject.keys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], optionsArgument);\n\t\t});\n\t}\n\tObject.keys(source).forEach(function(key) {\n\t\tif (!isMergeableObject(source[key]) || !target[key]) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], optionsArgument);\n\t\t} else {\n\t\t\tdestination[key] = deepmerge(target[key], source[key], optionsArgument);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, optionsArgument) {\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar options = optionsArgument || { arrayMerge: defaultArrayMerge };\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, optionsArgument)\n\t} else if (sourceIsArray) {\n\t\tvar arrayMerge = options.arrayMerge || defaultArrayMerge;\n\t\treturn arrayMerge(target, source, optionsArgument)\n\t} else {\n\t\treturn mergeObject(target, source, optionsArgument)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, optionsArgument) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, optionsArgument)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nreturn deepmerge_1;\n\n})));\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/deepmerge/dist/umd.js\n ** module id = 7\n ** module chunks = 0\n **/"],"sourceRoot":""}
=======
{"version":3,"sources":["webpack:///webpack/universalModuleDefinition","webpack:///frappe-gantt.min.js","webpack:///webpack/bootstrap 0eb5e1753b986f5bbe02","webpack:///./src/Gantt.js","webpack:///./src/gantt.scss?b27d","webpack:///./src/gantt.scss","webpack:///./~/css-loader/lib/css-base.js","webpack:///./~/style-loader/addStyles.js","webpack:///./src/Bar.js","webpack:///./src/Arrow.js","webpack:///./~/deepmerge/dist/umd.js"],"names":["root","factory","exports","module","define","amd","this","modules","__webpack_require__","moduleId","installedModules","id","loaded","call","m","c","p","_interopRequireDefault","obj","__esModule","default","Gantt","element","tasks","init","set_defaults","self","change_view_mode","unselect_all","view_is","get_bar","trigger_event","refresh","config","view_mode","merge","defaults","header_height","column_width","step","view_modes","bar","height","corner_radius","resize","progress","drag","arrow","curve","padding","date_format","custom_popup_html","reset_variables","document","querySelector","SVGElement","HTMLElement","TypeError","_tasks","_bars","_arrows","element_groups","updated_tasks","mode","set_scale","prepare","render","prepare_tasks","prepare_dependencies","prepare_dates","prepare_canvas","map","task","i","_start","moment","start","_end","end","diff","_index","startOf","add","clone","invalid","dependencies","deps","split","d","trim","filter","generate_id","dependency_map","_iteratorNormalCompletion","_didIteratorError","_iteratorError","undefined","_step","_iterator","Symbol","iterator","next","done","t","value","_iteratorNormalCompletion2","_didIteratorError2","_iteratorError2","_step2","_iterator2","push","err","gantt_start","gantt_end","_iteratorNormalCompletion3","_didIteratorError3","_iteratorError3","_step3","_iterator3","set_gantt_dates","setup_dates","canvas","Snap","addClass","clear","setup_groups","make_grid","make_dates","make_bars","make_arrows","map_arrows_on_bars","set_width","set_scroll_position","bind_grid_click","subtract","endOf","dates","cur_date","groups","_iteratorNormalCompletion4","_didIteratorError4","_iteratorError4","_step4","_iterator4","group","attr","scale","cur_width","node","getBoundingClientRect","width","actual_width","select","parent_element","parentElement","scroll_pos","get_min_date","scrollLeft","reduce","acc","curr","isSameOrBefore","make_grid_background","make_grid_rows","make_grid_header","make_grid_ticks","make_grid_highlights","grid_width","length","grid_height","rect","appendTo","grid","header_width","rows","lines","row_width","row_height","row_y","_iteratorNormalCompletion5","_didIteratorError5","_iteratorError5","_step5","_iterator5","line","tick_x","tick_y","tick_height","_iteratorNormalCompletion6","_didIteratorError6","_iteratorError6","_step6","_iterator6","date","tick_class","day","month","path","format","x","y","daysInMonth","_iteratorNormalCompletion7","_didIteratorError7","_iteratorError7","_step7","_iterator7","get_dates_to_draw","text","lower_x","lower_y","lower_text","upper_text","$upper_text","upper_x","upper_y","getBBox","x2","remove","last_date","get_date_info","date_text","Quarter Day_lower","Half Day_lower","Day_lower","Week_lower","Month_lower","Quarter Day_upper","Half Day_upper","Day_upper","Week_upper","Month_upper","year","base_pos","x_pos","_iteratorNormalCompletion8","_didIteratorError8","_iteratorError8","_step8","_loop","arrows","dep","dependency","get_task","_Arrow2","arr","concat","_iterator8","_Bar2","_iteratorNormalCompletion9","_didIteratorError9","_iteratorError9","_step9","_loop2","from_task","to_task","_iterator9","click","details","selectAll","forEach","el","removeClass","modes","Array","isArray","_iteratorNormalCompletion10","_didIteratorError10","_iteratorError10","_step10","_iterator10","find","name","Math","random","toString","slice","event","args","apply","arguments","Object","defineProperty","_Bar","_Arrow","content","locals","version","sources","names","mappings","file","sourcesContent","sourceRoot","list","result","item","join","mediaQuery","alreadyImportedModules","addStylesToDom","styles","options","domStyle","stylesInDom","refs","j","parts","addStyle","listToStyles","newStyles","css","media","sourceMap","part","insertStyleElement","styleElement","head","getHeadElement","lastStyleElementInsertedAtTop","styleElementsInsertedAtTop","insertAt","nextSibling","insertBefore","appendChild","firstChild","Error","removeStyleElement","parentNode","removeChild","idx","indexOf","splice","createStyleElement","createElement","type","createLinkElement","linkElement","rel","update","singleton","styleIndex","singletonCounter","singletonElement","applyToSingletonTag","bind","URL","createObjectURL","revokeObjectURL","Blob","btoa","updateLink","href","applyToTag","newObj","index","styleSheet","cssText","replaceText","cssNode","createTextNode","childNodes","setAttribute","unescape","encodeURIComponent","JSON","stringify","blob","oldSrc","memoize","fn","memo","isOldIE","test","navigator","userAgent","toLowerCase","getElementsByTagName","newList","mayRemove","textStore","replacement","Boolean","Bar","gt","draw","action_completed","prepare_values","prepare_plugins","compute_x","compute_y","duration","progress_width","custom_class","bar_group","handle_group","is_resizable","has_progress","is_draggable","plugin","Element","Paper","global","Fragment","prototype","getX","getY","getWidth","getHeight","getEndX","draw_bar","draw_progress_bar","draw_progress_handle","draw_label","draw_resize_handles","$bar","$bar_progress","update_label_position","handle_width","polygon","get_progress_polygon_points","bar_progress","setup_click_event","show_details","bind_resize","bind_resize_progress","bind_drag","popover_group","details_box","render_details","f","shadow","e","_get_details_position","get_details_position","transform","html","get_details_html","foreign_object","parse","append","isFunction","start_date","end_date","heading","line_1","line_2","onmove_right","dx","dy","onmove_handle_right","onstop_right","onstop_handle_right","onmove_left","onmove_handle_left","onstop_left","onstop_handle_left","_get_handles","get_handles","left","right","onstart","onmove","onstop","on_move","max_dx","min_dx","owidth","handle","finaldx","on_stop","progress_changed","set_action_completed","on_start","ox","oy","run_method_for_dependencies","get_snap_position","update_bar_position","date_changed","dm","deptask","dt","_ref","_ref$x","_ref$width","xs","valid_x","prev","update_attr","update_resize_handle_position","update_progress_handle_position","update_progressbar_position","update_arrow_position","update_details_position","toggleClass","_compute_start_end_da","compute_start_end_date","new_start_date","new_end_date","new_progress","compute_progress","setTimeout","x_in_units","width_in_units","parseInt","odx","rem","position","isNaN","label","_get_details_position2","functionToCheck","getType","Arrow","start_x","condition","start_y","end_x","end_y","from_is_below_to","clockwise","curve_y","offset","down_1","down_2","isNonNullObject","isSpecial","stringValue","isReactElement","$$typeof","REACT_ELEMENT_TYPE","emptyTarget","val","cloneUnlessOtherwiseSpecified","optionsArgument","isMergeableObject","deepmerge","defaultArrayMerge","target","source","mergeObject","destination","keys","key","sourceIsArray","targetIsArray","arrayMerge","sourceAndTargetTypesMatch","canUseSymbol","all","array","deepmerge_1"],"mappings":"CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,IACA,kBAAAG,gBAAAC,IACAD,OAAA,WAAAH,GACA,gBAAAC,SACAA,QAAA,MAAAD,IAEAD,EAAA,MAAAC,KACCK,KAAA,WACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAP,OAGA,IAAAC,GAAAO,EAAAD,IACAP,WACAS,GAAAF,EACAG,QAAA,EAUA,OANAL,GAAAE,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAS,QAAA,EAGAT,EAAAD,QAvBA,GAAAQ,KAqCA,OATAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAQ,EAAA,GAGAR,EAAA,KDgBM,SAASL,EAAQD,EAASM,GAE/B,YAiBA,SAASS,GAAuBC,GAAO,MAAOA,IAAOA,EAAIC,WAAaD,GAAQE,UAASF,GE5DzE,QAASG,GAAMC,EAASC,GAItC,QAASC,KACRC,IAGAC,EAAKC,iBAAmBA,EACxBD,EAAKE,aAAeA,EACpBF,EAAKG,QAAUA,EACfH,EAAKI,QAAUA,EACfJ,EAAKK,cAAgBA,EACrBL,EAAKM,QAAUA,EAGfL,EAAiBD,EAAKO,OAAOC,WAG9B,QAAST,KAER,GAAMU,GAAQ3B,EAAQ,GAEhB4B,GACLC,cAAe,GACfC,aAAc,GACdC,KAAM,GACNC,YACC,cACA,WACA,MACA,OACA,SAEDC,KACCC,OAAQ,GACRC,cAAe,EACfC,QAAQ,EACRC,UAAU,EACVC,MAAM,GAEPC,OACCC,MAAO,GAERC,QAAS,GACTf,UAAW,MACXgB,YAAa,aACbC,kBAAmB,KAEpBzB,GAAKO,OAASE,EAAMC,EAAUH,GAE9BmB,EAAgB7B,GAGjB,QAAS6B,GAAgB7B,GACxB,GAAsB,gBAAZD,GACTI,EAAKJ,QAAU+B,SAASC,cAAchC,OAChC,IAAIA,YAAmBiC,YAC7B7B,EAAKJ,QAAUA,MACT,MAAIA,YAAmBkC,cAG7B,KAAM,IAAIC,WAAU,6HAFpB/B,GAAKJ,QAAUA,EAAQgC,cAAc,OAMtC5B,EAAKgC,OAASnC,EAEdG,EAAKiC,SACLjC,EAAKkC,WACLlC,EAAKmC,kBAGN,QAAS7B,GAAQ8B,GAChBV,EAAgBU,GAChBnC,EAAiBD,EAAKO,OAAOC,WAG9B,QAASP,GAAiBoC,GACzBC,EAAUD,GACVE,IACAC,IAEAnC,EAAc,eAAgBgC,IAG/B,QAASE,KACRE,IACAC,IACAC,IACAC,IAGD,QAASH,KAGRzC,EAAKH,MAAQG,EAAKgC,OAAOa,IAAI,SAACC,EAAMC,GAgCnC,GA7BAD,EAAKE,OAASC,OAAOH,EAAKI,MAAOlD,EAAKO,OAAOiB,aAC7CsB,EAAKK,KAAOF,OAAOH,EAAKM,IAAKpD,EAAKO,OAAOiB,aAGtCsB,EAAKK,KAAKE,KAAKP,EAAKE,OAAQ,SAAW,KACzCF,EAAKM,IAAM,MAIZN,EAAKQ,OAASP,EAGVD,EAAKI,OAAUJ,EAAKM,MACvBN,EAAKE,OAASC,SAASM,QAAQ,OAC/BT,EAAKK,KAAOF,SAASM,QAAQ,OAAOC,IAAI,EAAG,UAExCV,EAAKI,OAASJ,EAAKM,MACtBN,EAAKE,OAASF,EAAKK,KAAKM,QAAQD,OAAQ,SAEtCV,EAAKI,QAAUJ,EAAKM,MACtBN,EAAKK,KAAOL,EAAKE,OAAOS,QAAQD,IAAI,EAAG,SAIpCV,EAAKI,OAAUJ,EAAKM,MACvBN,EAAKY,SAAU,GAIgB,gBAAtBZ,GAAKa,eAA8Bb,EAAKa,aAAc,CAC/D,GAAIC,KACDd,GAAKa,eACPC,EAAOd,EAAKa,aACVE,MAAM,KACNhB,IAAI,SAAAiB,GAAA,MAAKA,GAAEC,SACXC,OAAO,SAACF,GAAD,MAAOA,MAEjBhB,EAAKa,aAAeC,EAQrB,MAJId,GAAK7D,KACR6D,EAAK7D,GAAKgF,EAAYnB,IAGhBA,IAIT,QAASJ,KAER1C,EAAKkE,iBAF0B,IAAAC,IAAA,EAAAC,GAAA,EAAAC,EAAAC,MAAA,KAG/B,OAAAC,GAAAC,EAAaxE,EAAKH,MAAlB4E,OAAAC,cAAAP,GAAAI,EAAAC,EAAAG,QAAAC,MAAAT,GAAA,EAAyB,IAAjBU,GAAiBN,EAAAO,MAAAC,GAAA,EAAAC,GAAA,EAAAC,EAAAX,MAAA,KACxB,OAAAY,GAAAC,EAAaN,EAAElB,aAAfc,OAAAC,cAAAK,GAAAG,EAAAC,EAAAR,QAAAC,MAAAG,GAAA,EAA6B,IAArBjB,GAAqBoB,EAAAJ,KAC5B9E,GAAKkE,eAAeJ,GAAK9D,EAAKkE,eAAeJ,OAC7C9D,EAAKkE,eAAeJ,GAAGsB,KAAKP,EAAE5F,KAHP,MAAAoG,GAAAL,GAAA,EAAAC,EAAAI,EAAA,aAAAN,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,MAHM,MAAAI,GAAAjB,GAAA,EAAAC,EAAAgB,EAAA,aAAAlB,GAAAK,2BAAA,WAAAJ,EAAA,KAAAC,KAWhC,QAAS1B,KAER3C,EAAKsF,YAActF,EAAKuF,UAAY,IAFZ,IAAAC,IAAA,EAAAC,GAAA,EAAAC,EAAApB,MAAA,KAGxB,OAAAqB,GAAAC,EAAgB5F,EAAKH,MAArB4E,OAAAC,cAAAc,GAAAG,EAAAC,EAAAjB,QAAAC,MAAAY,GAAA,EAA4B,IAApB1C,GAAoB6C,EAAAb,QAEvB9E,EAAKsF,aAAexC,EAAKE,OAAShD,EAAKsF,eAC1CtF,EAAKsF,YAAcxC,EAAKE,UAErBhD,EAAKuF,WAAazC,EAAKK,KAAOnD,EAAKuF,aACtCvF,EAAKuF,UAAYzC,EAAKK,OATA,MAAAkC,GAAAI,GAAA,EAAAC,EAAAL,EAAA,aAAAG,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,IAYxBG,IACAC,IAGD,QAASlD,KACL5C,EAAK+F,SACR/F,EAAK+F,OAASC,KAAKhG,EAAKJ,SAASqG,SAAS,UAG3C,QAASzD,KACR0D,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IAGD,QAAST,KACRlG,EAAK+F,OAAOG,QACZlG,EAAKiC,SACLjC,EAAKkC,WAGN,QAAS2D,KAEL1F,GAAS,cAAe,cAC1BH,EAAKsF,YAActF,EAAKsF,YAAY7B,QAAQmD,SAAS,EAAG,OACxD5G,EAAKuF,UAAYvF,EAAKuF,UAAU9B,QAAQD,IAAI,EAAG,QACtCrD,EAAQ,UACjBH,EAAKsF,YAActF,EAAKsF,YAAY7B,QAAQF,QAAQ,QACpDvD,EAAKuF,UAAYvF,EAAKuF,UAAU9B,QAAQoD,MAAM,SAASrD,IAAI,EAAG,UAE9DxD,EAAKsF,YAActF,EAAKsF,YAAY7B,QAAQF,QAAQ,SAASqD,SAAS,EAAG,SACzE5G,EAAKuF,UAAYvF,EAAKuF,UAAU9B,QAAQoD,MAAM,SAASrD,IAAI,EAAG,UAIhE,QAASsC,KAER9F,EAAK8G,QAGL,KAFA,GAAIC,GAAW,KAEI,OAAbA,GAAqBA,EAAW/G,EAAKuF,WAIzCwB,EAHGA,EAGQ5G,EAAQ,SAClB4G,EAAStD,QAAQD,IAAI,EAAG,SACxBuD,EAAStD,QAAQD,IAAIxD,EAAKO,OAAOM,KAAM,SAJ7Bb,EAAKsF,YAAY7B,QAM7BzD,EAAK8G,MAAM1B,KAAK2B,GAIlB,QAASZ,KAER,GAAMa,IAAU,OAAQ,OAAQ,QAAS,WAAY,MAAO,WAFrCC,GAAA,EAAAC,GAAA,EAAAC,EAAA7C,MAAA,KAIvB,OAAA8C,GAAAC,EAAiBL,EAAjBvC,OAAAC,cAAAuC,GAAAG,EAAAC,EAAA1C,QAAAC,MAAAqC,GAAA,EAAyB,IAAjBK,GAAiBF,EAAAtC,KACxB9E,GAAKmC,eAAemF,GAAStH,EAAK+F,OAAOuB,QAAQC,MAAMtI,GAAMqI,KALvC,MAAAjC,GAAA6B,GAAA,EAAAC,EAAA9B,EAAA,aAAA4B,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,KASxB,QAAS7E,GAAUkF,GAClBxH,EAAKO,OAAOC,UAAYgH,EAEX,QAAVA,GACFxH,EAAKO,OAAOM,KAAO,GACnBb,EAAKO,OAAOK,aAAe,IACR,aAAV4G,GACTxH,EAAKO,OAAOM,KAAO,GACnBb,EAAKO,OAAOK,aAAe,IACR,gBAAV4G,GACTxH,EAAKO,OAAOM,KAAO,EACnBb,EAAKO,OAAOK,aAAe,IACR,SAAV4G,GACTxH,EAAKO,OAAOM,KAAO,IACnBb,EAAKO,OAAOK,aAAe,KACR,UAAV4G,IACTxH,EAAKO,OAAOM,KAAO,IACnBb,EAAKO,OAAOK,aAAe,KAI7B,QAAS6F,KACR,GAAMgB,GAAYzH,EAAK+F,OAAO2B,KAAKC,wBAAwBC,MACrDC,EAAe7H,EAAK+F,OAAO+B,OAAO,mBAAmBP,KAAK,QAC7DE,GAAYI,GACd7H,EAAK+F,OAAOwB,KAAK,QAASM,GAI5B,QAASnB,KACR,GAAMqB,GAAiB/H,EAAKJ,QAAQoI,aAEpC,IAAID,EAAJ,CAEA,GAAME,GAAaC,IAAe7E,KAAKrD,EAAKsF,YAAa,SACxDtF,EAAKO,OAAOM,KAAOb,EAAKO,OAAOK,aAAeZ,EAAKO,OAAOK,YAC3DmH,GAAeI,WAAaF,GAG7B,QAASC,KACR,GAAMpF,GAAO9C,EAAKH,MAAMuI,OAAO,SAACC,EAAKC,GACpC,MAAOA,GAAKtF,OAAOuF,eAAeF,EAAIrF,QAAUsF,EAAOD,GAExD,OAAOvF,GAAKE,OAGb,QAASoD,KACRoC,IACAC,IACAC,IACAC,IACAC,IAGD,QAASJ,KAER,GAAMK,GAAa7I,EAAK8G,MAAMgC,OAAS9I,EAAKO,OAAOK,aAClDmI,EAAc/I,EAAKO,OAAOI,cAAgBX,EAAKO,OAAOgB,SACpDvB,EAAKO,OAAOQ,IAAIC,OAAShB,EAAKO,OAAOgB,SAAWvB,EAAKH,MAAMiJ,MAE9D9I,GAAK+F,OAAOiD,KAAK,EAAG,EAAGH,EAAYE,GACjC9C,SAAS,mBACTgD,SAASjJ,EAAKmC,eAAe+G,MAE/BlJ,EAAK+F,OAAOwB,MACXvG,OAAQ+H,EAAc/I,EAAKO,OAAOgB,QAAU,IAC5CqG,MAAO,SAIT,QAASc,KACR,GAAMS,GAAenJ,EAAK8G,MAAMgC,OAAS9I,EAAKO,OAAOK,aACpDD,EAAgBX,EAAKO,OAAOI,cAAgB,EAC7CX,GAAK+F,OAAOiD,KAAK,EAAG,EAAGG,EAAcxI,GACnCsF,SAAS,eACTgD,SAASjJ,EAAKmC,eAAe+G,MAGhC,QAAST,KAER,GAAMW,GAAOpJ,EAAK+F,OAAOuB,QAAQ2B,SAASjJ,EAAKmC,eAAe+G,MAC7DG,EAAQrJ,EAAK+F,OAAOuB,QAAQ2B,SAASjJ,EAAKmC,eAAe+G,MACzDI,EAAYtJ,EAAK8G,MAAMgC,OAAS9I,EAAKO,OAAOK,aAC5C2I,EAAavJ,EAAKO,OAAOQ,IAAIC,OAAShB,EAAKO,OAAOgB,QAE/CiI,EAAQxJ,EAAKO,OAAOI,cAAgBX,EAAKO,OAAOgB,QAAU,EAPrCkI,GAAA,EAAAC,GAAA,EAAAC,EAAArF,MAAA,KASzB,OAAAsF,GAAAC,EAAgB7J,EAAKH,MAArB4E,OAAAC,cAAA+E,GAAAG,EAAAC,EAAAlF,QAAAC,MAAA6E,GAAA,EAA4B,CAAAG,EAAA9E,KAC3B9E,GAAK+F,OAAOiD,KAAK,EAAGQ,EAAOF,EAAWC,GACpCtD,SAAS,YACTgD,SAASG,GAEXpJ,EAAK+F,OAAO+D,KAAK,EAAGN,EAAQD,EAAYD,EAAWE,EAAQD,GACzDtD,SAAS,YACTgD,SAASI,GAEXG,GAASxJ,EAAKO,OAAOQ,IAAIC,OAAShB,EAAKO,OAAOgB,SAlBtB,MAAA8D,GAAAqE,GAAA,EAAAC,EAAAtE,EAAA,aAAAoE,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,KAsB1B,QAAShB,KACR,GAAIoB,GAAS,EACZC,EAAShK,EAAKO,OAAOI,cAAgBX,EAAKO,OAAOgB,QAAU,EAC3D0I,GAAejK,EAAKO,OAAOQ,IAAIC,OAAShB,EAAKO,OAAOgB,SAAWvB,EAAKH,MAAMiJ,OAHjDoB,GAAA,EAAAC,GAAA,EAAAC,EAAA9F,MAAA,KAK1B,OAAA+F,GAAAC,EAAgBtK,EAAK8G,MAArBrC,OAAAC,cAAAwF,GAAAG,EAAAC,EAAA3F,QAAAC,MAAAsF,GAAA,EAA4B,IAApBK,GAAoBF,EAAAvF,MACvB0F,EAAa,MAEdrK,GAAQ,QAAyB,IAAfoK,EAAKE,QACzBD,GAAc,UAGZrK,EAAQ,SAAWoK,EAAKA,QAAU,GAAKA,EAAKA,OAAS,IACvDC,GAAc,UAGZrK,EAAQ,UAAYoK,EAAKG,QAAU,IAAM,IAC3CF,GAAc,UAGfxK,EAAK+F,OAAO4E,KAAK3E,KAAK4E,OAAO,wBAC5BC,EAAGd,EACHe,EAAGd,EACHhJ,OAAQiJ,KAERhE,SAASuE,GACTvB,SAASjJ,EAAKmC,eAAe+G,MAG7Ba,GADE5J,EAAQ,SACAoK,EAAKQ,cAAgB/K,EAAKO,OAAOK,aAAe,GAEhDZ,EAAKO,OAAOK,cA/BE,MAAAyE,GAAA8E,GAAA,EAAAC,EAAA/E,EAAA,aAAA6E,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,KAoC3B,QAASxB,KAGR,GAAGzI,EAAQ,OAAQ,CAClB,GAAM0K,GAAI5H,SAASM,QAAQ,OAAOF,KAAKrD,EAAKsF,YAAa,SACvDtF,EAAKO,OAAOM,KAAOb,EAAKO,OAAOK,aAC3BkK,EAAI,EACJlD,EAAQ5H,EAAKO,OAAOK,aACpBI,GAAUhB,EAAKO,OAAOQ,IAAIC,OAAShB,EAAKO,OAAOgB,SAAWvB,EAAKH,MAAMiJ,OAC1E9I,EAAKO,OAAOI,cAAgBX,EAAKO,OAAOgB,QAAU,CAEnDvB,GAAK+F,OAAOiD,KAAK6B,EAAGC,EAAGlD,EAAO5G,GAC5BiF,SAAS,mBACTgD,SAASjJ,EAAKmC,eAAe+G,OAIjC,QAAS7C,KAAa,GAAA2E,IAAA,EAAAC,GAAA,EAAAC,EAAA5G,MAAA,KAErB,OAAA6G,GAAAC,EAAgBC,IAAhB5G,OAAAC,cAAAsG,GAAAG,EAAAC,EAAAzG,QAAAC,MAAAoG,GAAA,EAAqC,IAA7BT,GAA6BY,EAAArG,KAKpC,IAJA9E,EAAK+F,OAAOuF,KAAKf,EAAKgB,QAAShB,EAAKiB,QAASjB,EAAKkB,YAChDxF,SAAS,cACTgD,SAASjJ,EAAKmC,eAAeoI,MAE5BA,EAAKmB,WAAY,CACnB,GAAMC,GAAc3L,EAAK+F,OAAOuF,KAAKf,EAAKqB,QAASrB,EAAKsB,QAAStB,EAAKmB,YACpEzF,SAAS,cACTgD,SAASjJ,EAAKmC,eAAeoI,KAG5BoB,GAAYG,UAAUC,GAAK/L,EAAKmC,eAAe+G,KAAK4C,UAAUlE,OAChE+D,EAAYK,WAdM,MAAA3G,GAAA4F,GAAA,EAAAC,EAAA7F,EAAA,aAAA2F,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,KAoBtB,QAASG,KACR,GAAIY,GAAY,KACVnF,EAAQ9G,EAAK8G,MAAMjE,IAAI,SAAC0H,EAAMxH,GACnC,GAAMe,GAAIoI,EAAc3B,EAAM0B,EAAWlJ,EAEzC,OADAkJ,GAAY1B,EACLzG,GAER,OAAOgD,GAGR,QAASoF,GAAc3B,EAAM0B,EAAWlJ,GACnCkJ,IACHA,EAAY1B,EAAK9G,QAAQD,IAAI,EAAG,QAEjC,IAAM2I,IACLC,oBAAqB7B,EAAKK,OAAO,MACjCyB,iBAAkB9B,EAAKK,OAAO,MAC9B0B,UAAa/B,EAAKA,SAAW0B,EAAU1B,OAASA,EAAKK,OAAO,KAAO,GACnE2B,WAAchC,EAAKG,UAAYuB,EAAUvB,QACxCH,EAAKK,OAAO,SAAWL,EAAKK,OAAO,KACpC4B,YAAejC,EAAKK,OAAO,QAC3B6B,oBAAqBlC,EAAKA,SAAW0B,EAAU1B,OAASA,EAAKK,OAAO,SAAW,GAC/E8B,iBAAkBnC,EAAKA,SAAW0B,EAAU1B,OAC3CA,EAAKG,UAAYuB,EAAUvB,QAC3BH,EAAKK,OAAO,SAAWL,EAAKK,OAAO,KAAO,GAC3C+B,UAAapC,EAAKG,UAAYuB,EAAUvB,QAAUH,EAAKK,OAAO,QAAU,GACxEgC,WAAcrC,EAAKG,UAAYuB,EAAUvB,QAAUH,EAAKK,OAAO,QAAU,GACzEiC,YAAetC,EAAKuC,SAAWb,EAAUa,OAASvC,EAAKK,OAAO,QAAU,IAGnEmC,GACLlC,EAAG9H,EAAI/C,EAAKO,OAAOK,aACnB4K,QAASxL,EAAKO,OAAOI,cACrBkL,QAAS7L,EAAKO,OAAOI,cAAgB,IAGhCqM,GACLZ,oBAAiD,EAA3BpM,EAAKO,OAAOK,aAAoB,EACtD6L,oBAAqB,EACrBJ,iBAA8C,EAA3BrM,EAAKO,OAAOK,aAAoB,EACnD8L,iBAAkB,EAClBJ,UAAatM,EAAKO,OAAOK,aAAe,EACxC+L,UAAyC,GAA3B3M,EAAKO,OAAOK,aAAqB,EAC/C2L,WAAc,EACdK,WAA0C,EAA3B5M,EAAKO,OAAOK,aAAoB,EAC/C4L,YAAexM,EAAKO,OAAOK,aAAe,EAC1CiM,YAA2C,GAA3B7M,EAAKO,OAAOK,aAAqB,EAGlD,QACC8K,WAAYS,EAAanM,EAAKO,OAAOC,UAAzB,UACZiL,WAAYU,EAAanM,EAAKO,OAAOC,UAAzB,UACZoL,QAASmB,EAASlC,EAAImC,EAAShN,EAAKO,OAAOC,UAArB,UACtBqL,QAASkB,EAASlB,QAClBN,QAASwB,EAASlC,EAAImC,EAAShN,EAAKO,OAAOC,UAArB,UACtBgL,QAASuB,EAASvB,SAIpB,QAASjF,KACRvG,EAAKkC,UADiB,IAAA+K,IAAA,EAAAC,GAAA,EAAAC,EAAA7I,MAAA,KAEtB,OAAA8I,GAFsBC,EAAA,cAEdvK,GAFcsK,EAAAtI,MAGjBwI,IACJA,GAASxK,EAAKa,aAAad,IAAI,SAAA0K,GAC9B,GAAMC,GAAaC,EAASF,EAC5B,IAAIC,EAAJ,CAEA,GAAMnM,IAAQ,EAAAqM,cACb1N,EACAA,EAAKiC,MAAMuL,EAAWlK,QACtBtD,EAAKiC,MAAMa,EAAKQ,QAGjB,OADAtD,GAAKmC,eAAed,MAAMmC,IAAInC,EAAMzB,SAC7ByB,KACL2C,OAAO,SAAA2J,GAAA,MAAOA,KACjB3N,EAAKkC,QAAUlC,EAAKkC,QAAQ0L,OAAON,IAdpCO,EAAgB7N,EAAKH,MAArB4E,OAAAC,cAAAuI,GAAAG,EAAAS,EAAAlJ,QAAAC,MAAAqI,GAAA,EAA4BI,IAFN,MAAAhI,GAAA6H,GAAA,EAAAC,EAAA9H,EAAA,aAAA4H,GAAAY,2BAAA,WAAAX,EAAA,KAAAC,KAoBvB,QAAS7G,KAERtG,EAAKiC,MAAQjC,EAAKH,MAAMgD,IAAI,SAACC,GAC5B,GAAM/B,IAAM,EAAA+M,cAAI9N,EAAM8C,EAEtB,OADA9C,GAAKmC,eAAepB,IAAIyC,IAAIzC,EAAIuG,OACzBvG,IAIT,QAASyF,KAAqB,GAAAuH,IAAA,EAAAC,GAAA,EAAAC,EAAA3J,MAAA,KAC7B,OAAA4J,GAD6BC,EAAA,cACrBpN,GADqBmN,EAAApJ,KAE5B/D,GAAIuM,OAAStN,EAAKkC,QAAQ8B,OAAO,SAAA3C,GAChC,MAAQA,GAAM+M,UAAUtL,KAAK7D,KAAO8B,EAAI+B,KAAK7D,IAC3CoC,EAAMgN,QAAQvL,KAAK7D,KAAO8B,EAAI+B,KAAK7D,MAHvCqP,EAAetO,EAAKiC,MAApBwC,OAAAC,cAAAqJ,GAAAG,EAAAI,EAAA3J,QAAAC,MAAAmJ,GAAA,EAA2BI,IADE,MAAA9I,GAAA2I,GAAA,EAAAC,EAAA5I,EAAA,aAAA0I,GAAAO,2BAAA,WAAAN,EAAA,KAAAC,KAS9B,QAAStH,KACR3G,EAAKmC,eAAe+G,KAAKqF,MAAM,WAC9BrO,IACAF,EAAKmC,eAAeqM,QAClBC,UAAU,oBACVC,QAAQ,SAAAC,GAAA,MAAMA,GAAG1I,SAAS,YAI9B,QAAS/F,KACRF,EAAK+F,OAAO0I,UAAU,gBAAgBC,QAAQ,SAAAC,GAC7CA,EAAGC,YAAY,YAIjB,QAASzO,GAAQ0O,GAChB,GAAqB,gBAAVA,GACV,MAAO7O,GAAKO,OAAOC,YAAcqO,CAC3B,IAAGC,MAAMC,QAAQF,GAAQ,IAAAG,IAAA,EAAAC,GAAA,EAAAC,EAAA5K,MAAA,KAC/B,OAAA6K,GAAAC,EAAiBP,EAAjBpK,OAAAC,cAAAsK,GAAAG,EAAAC,EAAAzK,QAAAC,MAAAoK,GAAA,EAAwB,IAAf3M,GAAe8M,EAAArK,KACvB,IAAG9E,EAAKO,OAAOC,YAAc6B,EAAM,OAAO,GAFZ,MAAAgD,GAAA4J,GAAA,EAAAC,EAAA7J,EAAA,aAAA2J,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,IAI/B,OAAO,GAIT,QAASzB,GAASxO,GACjB,MAAOe,GAAKH,MAAMwP,KAAK,SAACvM,GACvB,MAAOA,GAAK7D,KAAOA,IAIrB,QAASmB,GAAQnB,GAChB,MAAOe,GAAKiC,MAAMoN,KAAK,SAACtO,GACvB,MAAOA,GAAI+B,KAAK7D,KAAOA,IAIzB,QAASgF,GAAYnB,GACpB,MAAOA,GAAKwM,KAAO,IAAMC,KAAKC,SAASC,SAAS,IAAIC,MAAM,EAAG,IAG9D,QAASrP,GAAcsP,EAAOC,GAC1B5P,EAAKO,OAAO,MAAQoP,IACtB3P,EAAKO,OAAO,MAAQoP,GAAOE,MAAM,KAAMD,GAziBiB,GAAbrP,GAAauP,UAAAhH,OAAA,GAAAxE,SAAAwL,UAAA,GAAAA,UAAA,MAEpD9P,IA6iBN,OAFAF,KAEOE,EFlgBP+P,OAAOC,eAAexR,EAAS,cAC9BsG,OAAO,IAERtG,aEhDuBmB,EALxBb,EAAA,EAEA,IAAAmR,GAAAnR,EAAA,GFyDKgP,EAAQvO,EAAuB0Q,GExDpCC,EAAApR,EAAA,GF4DK4O,EAAUnO,EAAuB2Q,EA0vBrCzR,GAAOD,QAAUA,EAAQ,YAIpB,SAASC,EAAQD,EAASM,GGl0BhC,GAAAqR,GAAArR,EAAA,EACA,iBAAAqR,SAAA1R,EAAAQ,GAAAkR,EAAA,KAEArR,GAAA,GAAAqR,KACAA,GAAAC,SAAA3R,EAAAD,QAAA2R,EAAAC,SHw1BM,SAAS3R,EAAQD,EAASM,GI/1BhCN,EAAAC,EAAAD,QAAAM,EAAA,KAKAN,EAAA4G,MAAA3G,EAAAQ,GAAA,+mDAAsoD,IAAQoR,QAAA,EAAAC,SAAA,+CAAAC,SAAAC,SAAA,+sBAAAC,KAAA,aAAAC,gBAAA,25EAAuuGC,WAAA,OJw2B/2J,SAASlS,EAAQD,GKx2BvBC,EAAAD,QAAA,WACA,GAAAoS,KA0CA,OAvCAA,GAAAnB,SAAA,WAEA,OADAoB,MACA9N,EAAA,EAAgBA,EAAAnE,KAAAkK,OAAiB/F,IAAA,CACjC,GAAA+N,GAAAlS,KAAAmE,EACA+N,GAAA,GACAD,EAAAzL,KAAA,UAAA0L,EAAA,OAAwCA,EAAA,QAExCD,EAAAzL,KAAA0L,EAAA,IAGA,MAAAD,GAAAE,KAAA,KAIAH,EAAA7N,EAAA,SAAAlE,EAAAmS,GACA,gBAAAnS,KACAA,IAAA,KAAAA,EAAA,KAEA,QADAoS,MACAlO,EAAA,EAAgBA,EAAAnE,KAAAkK,OAAiB/F,IAAA,CACjC,GAAA9D,GAAAL,KAAAmE,GAAA,EACA,iBAAA9D,KACAgS,EAAAhS,IAAA,GAEA,IAAA8D,EAAA,EAAYA,EAAAlE,EAAAiK,OAAoB/F,IAAA,CAChC,GAAA+N,GAAAjS,EAAAkE,EAKA,iBAAA+N,GAAA,IAAAG,EAAAH,EAAA,MACAE,IAAAF,EAAA,GACAA,EAAA,GAAAE,EACKA,IACLF,EAAA,OAAAA,EAAA,aAAAE,EAAA,KAEAJ,EAAAxL,KAAA0L,MAIAF,ILq3BM,SAASnS,EAAQD,EAASM,GMx2BhC,QAAAoS,GAAAC,EAAAC,GACA,OAAArO,GAAA,EAAeA,EAAAoO,EAAArI,OAAmB/F,IAAA,CAClC,GAAA+N,GAAAK,EAAApO,GACAsO,EAAAC,EAAAR,EAAA7R,GACA,IAAAoS,EAAA,CACAA,EAAAE,MACA,QAAAC,GAAA,EAAiBA,EAAAH,EAAAI,MAAA3I,OAA2B0I,IAC5CH,EAAAI,MAAAD,GAAAV,EAAAW,MAAAD,GAEA,MAAQA,EAAAV,EAAAW,MAAA3I,OAAuB0I,IAC/BH,EAAAI,MAAArM,KAAAsM,EAAAZ,EAAAW,MAAAD,GAAAJ,QAEG,CAEH,OADAK,MACAD,EAAA,EAAiBA,EAAAV,EAAAW,MAAA3I,OAAuB0I,IACxCC,EAAArM,KAAAsM,EAAAZ,EAAAW,MAAAD,GAAAJ,GAEAE,GAAAR,EAAA7R,KAA2BA,GAAA6R,EAAA7R,GAAAsS,KAAA,EAAAE,WAK3B,QAAAE,GAAAf,GAGA,OAFAO,MACAS,KACA7O,EAAA,EAAeA,EAAA6N,EAAA9H,OAAiB/F,IAAA,CAChC,GAAA+N,GAAAF,EAAA7N,GACA9D,EAAA6R,EAAA,GACAe,EAAAf,EAAA,GACAgB,EAAAhB,EAAA,GACAiB,EAAAjB,EAAA,GACAkB,GAAcH,MAAAC,QAAAC,YACdH,GAAA3S,GAGA2S,EAAA3S,GAAAwS,MAAArM,KAAA4M,GAFAb,EAAA/L,KAAAwM,EAAA3S,IAAgCA,KAAAwS,OAAAO,KAIhC,MAAAb,GAGA,QAAAc,GAAAb,EAAAc,GACA,GAAAC,GAAAC,IACAC,EAAAC,IAAAxJ,OAAA,EACA,YAAAsI,EAAAmB,SACAF,EAEGA,EAAAG,YACHL,EAAAM,aAAAP,EAAAG,EAAAG,aAEAL,EAAAO,YAAAR,GAJAC,EAAAM,aAAAP,EAAAC,EAAAQ,YAMAL,EAAAlN,KAAA8M,OACE,eAAAd,EAAAmB,SAGF,SAAAK,OAAA,qEAFAT,GAAAO,YAAAR,IAMA,QAAAW,GAAAX,GACAA,EAAAY,WAAAC,YAAAb,EACA,IAAAc,GAAAV,EAAAW,QAAAf,EACAc,IAAA,GACAV,EAAAY,OAAAF,EAAA,GAIA,QAAAG,GAAA/B,GACA,GAAAc,GAAAvQ,SAAAyR,cAAA,QAGA,OAFAlB,GAAAmB,KAAA,WACApB,EAAAb,EAAAc,GACAA,EAGA,QAAAoB,GAAAlC,GACA,GAAAmC,GAAA5R,SAAAyR,cAAA,OAGA,OAFAG,GAAAC,IAAA,aACAvB,EAAAb,EAAAmC,GACAA,EAGA,QAAA7B,GAAAlS,EAAA4R,GACA,GAAAc,GAAAuB,EAAAzH,CAEA,IAAAoF,EAAAsC,UAAA,CACA,GAAAC,GAAAC,GACA1B,GAAA2B,MAAAV,EAAA/B,IACAqC,EAAAK,EAAAC,KAAA,KAAA7B,EAAAyB,GAAA,GACA3H,EAAA8H,EAAAC,KAAA,KAAA7B,EAAAyB,GAAA,OACEnU,GAAAuS,WACF,kBAAAiC,MACA,kBAAAA,KAAAC,iBACA,kBAAAD,KAAAE,iBACA,kBAAAC,OACA,kBAAAC,OACAlC,EAAAoB,EAAAlC,GACAqC,EAAAY,EAAAN,KAAA,KAAA7B,GACAlG,EAAA,WACA6G,EAAAX,GACAA,EAAAoC,MACAN,IAAAE,gBAAAhC,EAAAoC,SAGApC,EAAAiB,EAAA/B,GACAqC,EAAAc,EAAAR,KAAA,KAAA7B,GACAlG,EAAA,WACA6G,EAAAX,IAMA,OAFAuB,GAAAjU,GAEA,SAAAgV,GACA,GAAAA,EAAA,CACA,GAAAA,EAAA3C,MAAArS,EAAAqS,KAAA2C,EAAA1C,QAAAtS,EAAAsS,OAAA0C,EAAAzC,YAAAvS,EAAAuS,UACA,MACA0B,GAAAjU,EAAAgV,OAEAxI,MAcA,QAAA8H,GAAA5B,EAAAuC,EAAAzI,EAAAxM,GACA,GAAAqS,GAAA7F,EAAA,GAAAxM,EAAAqS,GAEA,IAAAK,EAAAwC,WACAxC,EAAAwC,WAAAC,QAAAC,EAAAH,EAAA5C,OACE,CACF,GAAAgD,GAAAlT,SAAAmT,eAAAjD,GACAkD,EAAA7C,EAAA6C,UACAA,GAAAN,IAAAvC,EAAAa,YAAAgC,EAAAN,IACAM,EAAAjM,OACAoJ,EAAAO,aAAAoC,EAAAE,EAAAN,IAEAvC,EAAAQ,YAAAmC,IAKA,QAAAN,GAAArC,EAAA1S,GACA,GAAAqS,GAAArS,EAAAqS,IACAC,EAAAtS,EAAAsS,KAMA,IAJAA,GACAI,EAAA8C,aAAA,QAAAlD,GAGAI,EAAAwC,WACAxC,EAAAwC,WAAAC,QAAA9C,MACE,CACF,KAAAK,EAAAS,YACAT,EAAAa,YAAAb,EAAAS,WAEAT,GAAAQ,YAAA/Q,SAAAmT,eAAAjD,KAIA,QAAAwC,GAAAd,EAAA/T,GACA,GAAAqS,GAAArS,EAAAqS,IACAE,EAAAvS,EAAAuS,SAEAA,KAEAF,GAAA,uDAAuDuC,KAAAa,SAAAC,mBAAAC,KAAAC,UAAArD,MAAA,MAGvD,IAAAsD,GAAA,GAAAlB,OAAAtC,IAA6BwB,KAAA,aAE7BiC,EAAA/B,EAAAe,IAEAf,GAAAe,KAAAN,IAAAC,gBAAAoB,GAEAC,GACAtB,IAAAE,gBAAAoB,GAhPA,GAAAhE,MACAiE,EAAA,SAAAC,GACA,GAAAC,EACA,mBAEA,MADA,mBAAAA,OAAAD,EAAA3F,MAAAjR,KAAAkR,YACA2F,IAGAC,EAAAH,EAAA,WACA,qBAAAI,KAAA3V,KAAA4V,UAAAC,UAAAC,iBAEA1D,EAAAmD,EAAA,WACA,MAAA5T,UAAAwQ,MAAAxQ,SAAAoU,qBAAA,aAEAlC,EAAA,KACAD,EAAA,EACAtB,IAEA7T,GAAAD,QAAA,SAAAoS,EAAAQ,GAKAA,QAGA,mBAAAA,GAAAsC,YAAAtC,EAAAsC,UAAAgC,KAGA,mBAAAtE,GAAAmB,WAAAnB,EAAAmB,SAAA,SAEA,IAAApB,GAAAQ,EAAAf,EAGA,OAFAM,GAAAC,EAAAC,GAEA,SAAA4E,GAEA,OADAC,MACAlT,EAAA,EAAgBA,EAAAoO,EAAArI,OAAmB/F,IAAA,CACnC,GAAA+N,GAAAK,EAAApO,GACAsO,EAAAC,EAAAR,EAAA7R,GACAoS,GAAAE,OACA0E,EAAA7Q,KAAAiM,GAEA,GAAA2E,EAAA,CACA,GAAApE,GAAAD,EAAAqE,EACA9E,GAAAU,EAAAR,GAEA,OAAArO,GAAA,EAAgBA,EAAAkT,EAAAnN,OAAsB/F,IAAA,CACtC,GAAAsO,GAAA4E,EAAAlT,EACA,QAAAsO,EAAAE,KAAA,CACA,OAAAC,GAAA,EAAkBA,EAAAH,EAAAI,MAAA3I,OAA2B0I,IAC7CH,EAAAI,MAAAD,WACAF,GAAAD,EAAApS,OAiIA,IAAA2V,GAAA,WACA,GAAAsB,KAEA,iBAAAzB,EAAA0B,GAEA,MADAD,GAAAzB,GAAA0B,EACAD,EAAAlS,OAAAoS,SAAArF,KAAA,WNo+BM,SAAStS,EAAQD,GAEtB,YO1pCc,SAAS6X,GAAIC,EAAIxT,GAI/B,QAAShD,KACRC,IACAwC,IACAgU,IACAxC,IAGD,QAAShU,KACRC,EAAKwW,kBAAmB,EACxBxW,EAAK8C,KAAOA,EAGb,QAASP,KACRkU,IACAC,IAGD,QAASD,KACRzW,EAAK0D,QAAU1D,EAAK8C,KAAKY,QACzB1D,EAAKgB,OAASsV,EAAG/V,OAAOQ,IAAIC,OAC5BhB,EAAK6K,EAAI8L,IACT3W,EAAK8K,EAAI8L,IACT5W,EAAKiB,cAAgBqV,EAAG/V,OAAOQ,IAAIE,cACnCjB,EAAK6W,UAAY7W,EAAK8C,KAAKK,KAAKE,KAAKrD,EAAK8C,KAAKE,OAAQ,SAAW,IAAMsT,EAAG/V,OAAOM,KAClFb,EAAK4H,MAAQ0O,EAAG/V,OAAOK,aAAeZ,EAAK6W,SAC3C7W,EAAK8W,eAAiBR,EAAG/V,OAAOK,aAAeZ,EAAK6W,UAAY7W,EAAK8C,KAAK3B,SAAW,MAAQ,EAC7FnB,EAAKsH,MAAQgP,EAAGvQ,OAAOuB,QAAQrB,SAAS,eAAeA,SAASjG,EAAK8C,KAAKiU,cAAgB,IAC1F/W,EAAKgX,UAAYV,EAAGvQ,OAAOuB,QAAQrB,SAAS,aAAagD,SAASjJ,EAAKsH,OACvEtH,EAAKiX,aAAeX,EAAGvQ,OAAOuB,QAAQrB,SAAS,gBAAgBgD,SAASjJ,EAAKsH,OAC7EtH,EAAKkX,aAAeZ,EAAG/V,OAAOQ,IAAIG,OAClClB,EAAKmX,aAAeb,EAAG/V,OAAOQ,IAAII,SAClCnB,EAAKoX,aAAed,EAAG/V,OAAOQ,IAAIK,KAGnC,QAASsV,KACR1Q,KAAKqR,OAAO,SAAUrR,EAAMsR,EAASC,EAAOC,EAAQC,GACnDH,EAAQI,UAAUC,KAAO,WACxB,OAAQ/Y,KAAK2I,KAAK,MAEnB+P,EAAQI,UAAUE,KAAO,WACxB,OAAQhZ,KAAK2I,KAAK,MAEnB+P,EAAQI,UAAUG,SAAW,WAC5B,OAAQjZ,KAAK2I,KAAK,UAEnB+P,EAAQI,UAAUI,UAAY,WAC7B,OAAQlZ,KAAK2I,KAAK,WAEnB+P,EAAQI,UAAUK,QAAU,WAC3B,MAAOnZ,MAAK+Y,OAAS/Y,KAAKiZ,cAK7B,QAAStB,KACRyB,IACIhY,EAAKmX,eACRc,IACAC,KAEDC,IACInY,EAAKkX,cAAgBkB,IAG1B,QAASJ,KACRhY,EAAKqY,KAAO/B,EAAGvQ,OAAOiD,KAAKhJ,EAAK6K,EAAG7K,EAAK8K,EACvC9K,EAAK4H,MAAO5H,EAAKgB,OACjBhB,EAAKiB,cAAejB,EAAKiB,eACxBgF,SAAS,OACTgD,SAASjJ,EAAKgX,WACZhX,EAAK0D,SACR1D,EAAKqY,KAAKpS,SAAS,eAIrB,QAASgS,KACJjY,EAAK0D,UACT1D,EAAKsY,cAAgBhC,EAAGvQ,OAAOiD,KAAKhJ,EAAK6K,EAAG7K,EAAK8K,EAChD9K,EAAK8W,eAAgB9W,EAAKgB,OAC1BhB,EAAKiB,cAAejB,EAAKiB,eACxBgF,SAAS,gBACTgD,SAASjJ,EAAKgX,YAGjB,QAASmB,KACR7B,EAAGvQ,OAAOuF,KAAKtL,EAAK6K,EAAI7K,EAAK4H,MAAQ,EACpC5H,EAAK8K,EAAI9K,EAAKgB,OAAS,EACvBhB,EAAK8C,KAAKwM,MACTrJ,SAAS,aACTgD,SAASjJ,EAAKgX,WAChBuB,IAGD,QAASH,KACR,IAAIpY,EAAK0D,QAAT,CAEA,GAAM3C,GAAMf,EAAKqY,KAChBG,EAAe,CAEhBlC,GAAGvQ,OAAOiD,KAAKjI,EAAI4W,OAAS5W,EAAI8W,WAAa,EAAG9W,EAAI6W,OAAS,EAC5DY,EAAcxY,EAAKgB,OAAS,EAAGhB,EAAKiB,cAAejB,EAAKiB,eACvDgF,SAAS,gBACTgD,SAASjJ,EAAKiX,cAChBX,EAAGvQ,OAAOiD,KAAKjI,EAAI4W,OAAS,EAAG5W,EAAI6W,OAAS,EAC3CY,EAAcxY,EAAKgB,OAAS,EAAGhB,EAAKiB,cAAejB,EAAKiB,eACvDgF,SAAS,eACTgD,SAASjJ,EAAKiX,eAGjB,QAASiB,KACJlY,EAAK0D,SAEL1D,EAAK8C,KAAK3B,UAAYnB,EAAK8C,KAAK3B,SAAW,KAC9CmV,EAAGvQ,OAAO0S,QAAQC,KAChBzS,SAAS,mBACTgD,SAASjJ,EAAKiX,cAIlB,QAASyB,KACR,GAAMC,GAAe3Y,EAAKsY,aAC1B,QACCK,EAAaZ,UAAY,EAAGY,EAAaf,OAASe,EAAab,YAC/Da,EAAaZ,UAAY,EAAGY,EAAaf,OAASe,EAAab,YAC/Da,EAAaZ,UAAWY,EAAaf,OAASe,EAAab,YAAc,MAI3E,QAAS/D,KACJ/T,EAAK0D,UACTkV,IACAC,IACI7Y,EAAKkX,cAAgB4B,IACrB9Y,EAAKmX,cAAgB4B,IACrB/Y,EAAKoX,cAAgB4B,KAG1B,QAASH,KACR,GAAMI,GAAgB3C,EAAGnU,eAAeqM,OAIxC,IAHAxO,EAAKkZ,YAAcD,EACjBnR,OADiB,+BACqB9H,EAAK8C,KAAK7D,GAD/B,OAGde,EAAKkZ,YAAa,CACtBlZ,EAAKkZ,YAAc5C,EAAGvQ,OAAOuB,QAC3BrB,SAAS,wBACTsB,KAAK,YAAavH,EAAK8C,KAAK7D,IAC5BgK,SAASgQ,GAEXE,GAEA,IAAMC,GAAI9C,EAAGvQ,OAAO/B,OACnBgC,KAAKhC,OAAOqV,OAAO,EAAG,EAAG,EAAG,OAAQ,IACrCrZ,GAAKkZ,YAAY3R,MAChBvD,OAAQoV,IAIVpZ,EAAKsH,MAAMiH,MAAM,SAAC+K,GACbtZ,EAAKwW,mBAITyC,EAAcxK,UAAU,oBACtBC,QAAQ,SAAAC,GAAA,MAAMA,GAAG1I,SAAS,UAC5BjG,EAAKkZ,YAAYtK,YAAY,WAI/B,QAASuK,KAAiB,GAAAI,GACVC,IAAR3O,EADkB0O,EAClB1O,EAAGC,EADeyO,EACfzO,CACV9K,GAAKkZ,YAAYO,UAAjB,IAA+B5O,EAA/B,IAAoCC,GACpC9K,EAAKkZ,YAAYhT,OAEjB,IAAMwT,GAAOC,IACPC,EACL5T,KAAK6T,MAAL,8GAEIH,EAFJ,8CAKD1Z,GAAKkZ,YAAYY,OAAOF,GAGzB,QAASD,KAGR,GAAGrD,EAAG/V,OAAOkB,kBAAmB,CAC/B,GAAMiY,GAAOpD,EAAG/V,OAAOkB,iBACvB,IAAmB,gBAATiY,GACT,MAAOA,EAER,IAAGK,EAAWL,GACb,MAAOA,GAAK5W,GAId,GAAMkX,GAAaha,EAAK8C,KAAKE,OAAO4H,OAAO,SACrCqP,EAAWja,EAAK8C,KAAKK,KAAKyH,OAAO,SACjCsP,EAAala,EAAK8C,KAAKwM,KAAvB,KAAgC0K,EAAhC,MAAgDC,EAEhDE,eAAsBna,EAAK6W,SAA3B,QACAuD,EAASpa,EAAK8C,KAAK3B,SAAV,aAAkCnB,EAAK8C,KAAK3B,SAAa,KAElEuY,0DAEEQ,EAFF,qBAGCC,EAHD,kBAKHC,QAAeA,EAAf,OAA8B,IAL3B,sBASN,OAAOV,GAGR,QAASF,KACR,OACC3O,EAAG7K,EAAKqY,KAAKN,UAAY,EACzBjN,EAAG9K,EAAKqY,KAAKT,OAAS,IAIxB,QAASkB,KAMR,QAASuB,GAAaC,EAAIC,GACzBC,EAAoBF,EAAIC,GAEzB,QAASE,KACRC,IAGD,QAASC,GAAYL,EAAIC,GACxBK,EAAmBN,EAAIC,GAExB,QAASM,KACRC,IAjBqB,GAAAC,GACEC,IAAhBC,EADcF,EACdE,KAAMC,EADQH,EACRG,KAEdD,GAAK7Z,KAAKuZ,EAAaQ,EAASN,GAChCK,EAAM9Z,KAAKiZ,EAAcc,EAASV,GAiBnC,QAASO,KACR,OACCC,KAAMjb,EAAKiX,aAAanP,OAAO,gBAC/BoT,MAAOlb,EAAKiX,aAAanP,OAAO,kBAIlC,QAASkR,KACRhZ,EAAKgX,UAAU5V,KAAKga,EAAQD,EAASE,GAGtC,QAAStC,KAMR,QAASuC,GAAQhB,EAAIC,GAChBD,EAAK3B,EAAa4C,SACrBjB,EAAK3B,EAAa4C,QAEfjB,EAAK3B,EAAa6C,SACrBlB,EAAK3B,EAAa6C,QAGnB7C,EAAapR,KAAK,QAASoR,EAAa8C,OAASnB,GACjDoB,EAAOnU,KAAK,SAAUmR,KACtBC,EAAagD,QAAUrB,EAExB,QAASsB,KACHjD,EAAagD,UAClBE,IACAC,KAED,QAASC,KACRpD,EAAagD,QAAU,EACvBhD,EAAa8C,OAAS9C,EAAad,WACnCc,EAAa6C,QAAU7C,EAAad,WACpCc,EAAa4C,OAASxa,EAAI8W,WAAac,EAAad,WA1BrD,GAAM9W,GAAMf,EAAKqY,KAChBM,EAAe3Y,EAAKsY,cACpBoD,EAAS1b,EAAKsH,MAAMQ,OAAO,mBAC5B4T,IAAUA,EAAOta,KAAKka,EAASS,EAAUH,GA2B1C,QAAST,KACR,GAAMpa,GAAMf,EAAKqY,IACjBtX,GAAIib,GAAKjb,EAAI4W,OACb5W,EAAIkb,GAAKlb,EAAI6W,OACb7W,EAAI0a,OAAS1a,EAAI8W,WACjB9W,EAAI4a,QAAU,EACdO,EAA4B,WAI7B,QAASd,GAAOd,EAAIC,GACnB,GAAMxZ,GAAMf,EAAKqY,IACjBtX,GAAI4a,QAAUQ,EAAkB7B,GAChC8B,GAAqBvR,EAAG9J,EAAIib,GAAKjb,EAAI4a,UACrCO,EAA4B,UAAW5B,EAAIC,IAI5C,QAASc,KACR,GAAMta,GAAMf,EAAKqY,IACZtX,GAAI4a,UACTU,IACAP,IACAI,EAA4B,WAI7B,QAAStB,GAAmBN,EAAIC,GAC/B,GAAMxZ,GAAMf,EAAKqY,IACjBtX,GAAI4a,QAAUQ,EAAkB7B,GAChC8B,GACCvR,EAAG9J,EAAIib,GAAKjb,EAAI4a,QAChB/T,MAAO7G,EAAI0a,OAAS1a,EAAI4a,UAEzBO,EAA4B,UAAW5B,EAAIC,IAI5C,QAASO,KACR,GAAM/Z,GAAMf,EAAKqY,IACbtX,GAAI4a,SAASU,IACjBP,IACAI,EAA4B,UAI7B,QAASA,GAA4B1G,EAAI5F,GACxC,GAAM0M,GAAKhG,EAAGpS,cACd,IAAIoY,EAAGtc,EAAK8C,KAAK7D,IAAK,IAAAkF,IAAA,EAAAC,GAAA,EAAAC,EAAAC,MAAA,KACrB,OAAAC,GAAAC,EAAoB8X,EAAGtc,EAAK8C,KAAK7D,IAAjCwF,OAAAC,cAAAP,GAAAI,EAAAC,EAAAG,QAAAC,MAAAT,GAAA,EAAsC,IAA7BoY,GAA6BhY,EAAAO,MAC/B0X,EAAKlG,EAAGlW,QAAQmc,EACtBC,GAAGhH,GAAI3F,MAAM2M,EAAI5M,IAHG,MAAAvK,GAAAjB,GAAA,EAAAC,EAAAgB,EAAA,aAAAlB,GAAAK,2BAAA,WAAAJ,EAAA,KAAAC,MAQvB,QAASmW,GAAoBF,EAAIC,GAChC,GAAMxZ,GAAMf,EAAKqY,IACjBtX,GAAI4a,QAAUQ,EAAkB7B,GAChC8B,GAAqBxU,MAAO7G,EAAI0a,OAAS1a,EAAI4a,UAG9C,QAASjB,KACR,GAAM3Z,GAAMf,EAAKqY,IACbtX,GAAI4a,SAASU,IACjBP,IAGD,QAASM,GAATK,GAAuD,GAAAC,GAAAD,EAAzB5R,IAAyBvG,SAAAoY,EAArB,KAAqBA,EAAAC,EAAAF,EAAf7U,QAAetD,SAAAqY,EAAP,KAAOA,EAChD5b,EAAMf,EAAKqY,IACjB,IAAIxN,EAAG,CAEN,GAAM+R,GAAK9Z,EAAKa,aAAad,IAAI,SAAA0K,GAChC,MAAO+I,GAAGlW,QAAQmN,GAAK8K,KAAKV,SAGvBkF,EAAUD,EAAGxU,OAAO,SAAC0U,EAAMxU,GAChC,MAAOuC,IAAKvC,GACVuC,EACH,KAAIgS,EAEH,YADAjV,EAAQ,KAGTmV,GAAYhc,EAAK,IAAK8J,GAEnBjD,GAASA,GAAS0O,EAAG/V,OAAOK,cAC/Bmc,EAAYhc,EAAK,QAAS6G,GAG3B2Q,IACIvY,EAAKkX,cAAgB8F,IACrBhd,EAAKmX,eACR8F,IACAC,KAEDC,IACAC,IAGD,QAASxE,KACR5Y,EAAKsH,MAAMiH,MAAM,WACZvO,EAAKwW,mBAKTF,EAAGjW,cAAc,SAAUL,EAAK8C,OAChCwT,EAAGpW,eACHF,EAAKsH,MAAM+V,YAAY,aAIzB,QAAShB,KAAe,GAAAiB,GACkBC,IAAjCC,EADeF,EACfE,eAAgBC,EADDH,EACCG,YACxBzd,GAAK8C,KAAKE,OAASwa,EACnBxd,EAAK8C,KAAKK,KAAOsa,EACjBtE,IACA7C,EAAGjW,cAAc,eACfL,EAAK8C,KAAM0a,EAAgBC,IAG9B,QAAS5B,KACR,GAAM6B,GAAeC,GACrB3d,GAAK8C,KAAK3B,SAAWuc,EACrBvE,IACA7C,EAAGjW,cAAc,mBACfL,EAAK8C,KAAM4a,IAGd,QAAS5B,KACR9b,EAAKwW,kBAAmB,EACxBoH,WAAW,iBAAM5d,GAAKwW,kBAAmB,GAAO,KAGjD,QAAS+G,KACR,GAAMxc,GAAMf,EAAKqY,KACXwF,EAAa9c,EAAI4W,OAASrB,EAAG/V,OAAOK,aACpC4c,EAAiBlH,EAAGhR,YAAY7B,QAAQD,IAAIqa,EAAavH,EAAG/V,OAAOM,KAAM,SACzEid,EAAiB/c,EAAI8W,WAAavB,EAAG/V,OAAOK,aAC5C6c,EAAeD,EAAe/Z,QAAQD,IAAIsa,EAAiBxH,EAAG/V,OAAOM,KAAM,QAOjF,OADA4c,GAAaja,IAAI,KAAM,YACdga,iBAAgBC,gBAG1B,QAASE,KACR,GAAMxc,GAAWnB,EAAKsY,cAAcT,WAAa7X,EAAKqY,KAAKR,WAAa,GACxE,OAAOkG,UAAS5c,EAAU,IAG3B,QAASwV,KACR,GAAI9L,GAAI7K,EAAK8C,KAAKE,OAAOK,KAAKiT,EAAGhR,YAAa,SAC7CgR,EAAG/V,OAAOM,KAAOyV,EAAG/V,OAAOK,YAM5B,OAJI0V,GAAGnW,QAAQ,WACd0K,EAAI7K,EAAK8C,KAAKE,OAAOK,KAAKiT,EAAGhR,YAAa,QACzCgR,EAAG/V,OAAOK,aAAe,IAEpBiK,EAGR,QAAS+L,KACR,MAAON,GAAG/V,OAAOI,cAAgB2V,EAAG/V,OAAOgB,QAC1CvB,EAAK8C,KAAKQ,QAAUtD,EAAKgB,OAASsV,EAAG/V,OAAOgB,SAG9C,QAAS4a,GAAkB7B,GAC1B,GAAI0D,GAAM1D,EAAI2D,SAAKC,QAenB,OAbI5H,GAAGnW,QAAQ,SACd8d,EAAM3D,GAAMhE,EAAG/V,OAAOK,aAAe,GACrCsd,EAAWF,EAAMC,GACdA,EAAM3H,EAAG/V,OAAOK,aAAe,GAAM,EAAI0V,EAAG/V,OAAOK,aAAe,IAC3D0V,EAAGnW,QAAQ,UACrB8d,EAAM3D,GAAMhE,EAAG/V,OAAOK,aAAe,IACrCsd,EAAWF,EAAMC,GACdA,EAAM3H,EAAG/V,OAAOK,aAAe,GAAM,EAAI0V,EAAG/V,OAAOK,aAAe,MAErEqd,EAAM3D,EAAKhE,EAAG/V,OAAOK,aACrBsd,EAAWF,EAAMC,GACdA,EAAM3H,EAAG/V,OAAOK,aAAe,EAAK,EAAI0V,EAAG/V,OAAOK,eAE/Csd,EAGR,QAASnB,GAAYnd,EAAS2H,EAAMzC,GAKnC,MAJAA,IAASA,EACJqZ,MAAMrZ,IACVlF,EAAQ2H,KAAKA,EAAMzC,GAEblF,EAGR,QAASsd,KACRld,EAAKsY,cAAc/Q,KAAK,IAAKvH,EAAKqY,KAAKV,QACvC3X,EAAKsY,cAAc/Q,KAAK,QAASvH,EAAKqY,KAAKR,YAAc7X,EAAK8C,KAAK3B,SAAW,MAG/E,QAASoX,KACR,GAAMxX,GAAMf,EAAKqY,KAChB+F,EAAQpe,EAAKsH,MAAMQ,OAAO,aACvBsW,GAAMtS,UAAUlE,MAAQ7G,EAAI8W,WAC/BuG,EAAMnY,SAAS,OAAOsB,KAAK,IAAKxG,EAAI4W,OAAS5W,EAAI8W,WAAa,GAE9DuG,EAAMxP,YAAY,OAAOrH,KAAK,IAAKxG,EAAI4W,OAAS5W,EAAI8W,WAAa,GAInE,QAASmF,KACR,GAAMjc,GAAMf,EAAKqY,IACjBrY,GAAKiX,aAAanP,OAAO,gBAAgBP,MACxCsD,EAAK9J,EAAI4W,OAAS,IAEnB3X,EAAKiX,aAAanP,OAAO,iBAAiBP,MACzCsD,EAAK9J,EAAIgX,UAAY,IAIvB,QAASkF,KACR,GAAMvB,GAAS1b,EAAKsH,MAAMQ,OAAO,mBACjC4T,IAAUA,EAAOnU,KAAK,SAAUmR,KAGjC,QAASyE,KAAwB,GAAApY,IAAA,EAAAC,GAAA,EAAAC,EAAAX,MAAA,KAChC,OAAAY,GAAAC,EAAkBnF,EAAKsN,OAAvB7I,OAAAC,cAAAK,GAAAG,EAAAC,EAAAR,QAAAC,MAAAG,GAAA,EAA+B,IAAtB1D,GAAsB6D,EAAAJ,KAC9BzD,GAAMoS,UAFyB,MAAApO,GAAAL,GAAA,EAAAC,EAAAI,EAAA,aAAAN,GAAAI,2BAAA,WAAAH,EAAA,KAAAC,KAMjC,QAASmY,KAA0B,GAAAiB,GACnB7E,IAAR3O,EAD2BwT,EAC3BxT,EAAGC,EADwBuT,EACxBvT,CACV9K,GAAKkZ,aAAelZ,EAAKkZ,YAAYO,UAAjB,IAA+B5O,EAA/B,IAAoCC,GAGzD,QAASiP,GAAWuE,GACnB,GAAIC,KACJ,OAAOD,IAA8D,sBAA3CC,EAAQ9O,SAAStQ,KAAKmf,GA/gBjD,GAAMte,KAohBN,OA7OAA,GAAKmb,QAAUA,EAQfnb,EAAKob,OAASA,EASdpb,EAAKqb,OAASA,EAWdrb,EAAK4a,mBAAqBA,EAQ1B5a,EAAK8a,mBAAqBA,EAuM1Bhb,IAEOE,EPsoBP+P,OAAOC,eAAexR,EAAS,cAC9BsG,OAAO,IAERtG,aO/pCuB6X,EPwtDvB5X,EAAOD,QAAUA,EAAQ,YAIpB,SAASC,EAAQD,GAEtB,YQ5tDc,SAASggB,GAAMlI,EAAIlI,EAAWC,GAI5C,QAASvO,KACRE,EAAKoO,UAAYA,EACjBpO,EAAKqO,QAAUA,EACf9L,IACAgU,IAGD,QAAShU,KAERvC,EAAKye,QAAUrQ,EAAUiK,KAAKV,OAASvJ,EAAUiK,KAAKR,WAAa,CAMnE,KAJA,GAAM6G,GAAY,iBACjBrQ,GAAQgK,KAAKV,OAAS3X,EAAKye,QAAUnI,EAAG/V,OAAOgB,SAC9CvB,EAAKye,QAAUrQ,EAAUiK,KAAKV,OAASrB,EAAG/V,OAAOgB,SAE7Cmd,KACL1e,EAAKye,SAAW,EAGjBze,GAAK2e,QAAUrI,EAAG/V,OAAOI,cAAgB2V,EAAG/V,OAAOQ,IAAIC,QACrDsV,EAAG/V,OAAOgB,QAAU+U,EAAG/V,OAAOQ,IAAIC,QAAUoN,EAAUtL,KAAKQ,OAC5DgT,EAAG/V,OAAOgB,QAEXvB,EAAK4e,MAAQvQ,EAAQgK,KAAKV,OAASrB,EAAG/V,OAAOgB,QAAU,EACvDvB,EAAK6e,MAAQvI,EAAG/V,OAAOI,cAAgB2V,EAAG/V,OAAOQ,IAAIC,OAAS,GAC5DsV,EAAG/V,OAAOgB,QAAU+U,EAAG/V,OAAOQ,IAAIC,QAAUqN,EAAQvL,KAAKQ,OAC1DgT,EAAG/V,OAAOgB,OAEX,IAAMud,GAAoB1Q,EAAUtL,KAAKQ,OAAS+K,EAAQvL,KAAKQ,MAC/DtD,GAAKsB,MAAQgV,EAAG/V,OAAOc,MAAMC,MAC7BtB,EAAK+e,UAAYD,EAAmB,EAAI,EACxC9e,EAAKgf,QAAUF,GAAoB9e,EAAKsB,MAAQtB,EAAKsB,MACrDtB,EAAKif,OAASH,EACb9e,EAAK6e,MAAQvI,EAAG/V,OAAOc,MAAMC,MAC7BtB,EAAK6e,MAAQvI,EAAG/V,OAAOc,MAAMC,MAE9BtB,EAAK2K,KACJ3E,KAAK4E,OAAO,+HAIV6T,QAASze,EAAKye,QACdE,QAAS3e,EAAK2e,QACdC,MAAO5e,EAAK4e,MACZC,MAAO7e,EAAK6e,MACZI,OAAQjf,EAAKif,OACb3d,MAAOtB,EAAKsB,MACZyd,UAAW/e,EAAK+e,UAChBC,QAAShf,EAAKgf,UAGd3Q,EAAQgK,KAAKV,OAASvJ,EAAUiK,KAAKV,OAASrB,EAAG/V,OAAOgB,UAC1DvB,EAAK2K,KACJ3E,KAAK4E,OAAO,iPAMV6T,QAASze,EAAKye,QACdE,QAAS3e,EAAK2e,QACdC,MAAO5e,EAAK4e,MACZC,MAAO7e,EAAK6e,MACZK,OAAQ5I,EAAG/V,OAAOgB,QAAU,EAAIvB,EAAKsB,MACrC6d,OAAQ9Q,EAAQgK,KAAKT,OAASvJ,EAAQgK,KAAKP,YAAc,EAAI9X,EAAKgf,QAClE/D,KAAM5M,EAAQgK,KAAKV,OAASrB,EAAG/V,OAAOgB,QACtC0d,OAAQjf,EAAKif,OACb3d,MAAOtB,EAAKsB,MACZyd,UAAW/e,EAAK+e,UAChBC,QAAShf,EAAKgf,WAKnB,QAASzI,KACRvW,EAAKJ,QAAU0W,EAAGvQ,OAAO4E,KAAK3K,EAAK2K,MACjCpD,KAAK,YAAavH,EAAKoO,UAAUtL,KAAK7D,IACtCsI,KAAK,UAAWvH,EAAKqO,QAAQvL,KAAK7D,IAGrC,QAASwU,KACRlR,IACAvC,EAAKJ,QAAQ2H,KAAK,IAAKvH,EAAK2K,MApF7B,GAAM3K,KA0FN,OAJAA,GAAKyT,OAASA,EAEd3T,IAEOE,ERkoDP+P,OAAOC,eAAexR,EAAS,cAC9BsG,OAAO,IAERtG,aQjuDuBggB,ER0zDvB/f,EAAOD,QAAUA,EAAQ,YAIpB,SAASC,EAAQD,EAASM,ISz0DhC,SAAA0Y,EAAAjZ,GACAE,EAAAD,QAAAD,KAGCK,KAAA,WAAqB,YAOtB,SAAAwgB,GAAAta,GACA,QAAAA,GAAA,gBAAAA,GAGA,QAAAua,GAAAva,GACA,GAAAwa,GAAAvP,OAAA2H,UAAAjI,SAAAtQ,KAAA2F,EAEA,2BAAAwa,GACA,kBAAAA,GACAC,EAAAza,GAOA,QAAAya,GAAAza,GACA,MAAAA,GAAA0a,WAAAC,EAGA,QAAAC,GAAAC,GACA,MAAA7Q,OAAAC,QAAA4Q,SAGA,QAAAC,GAAA9a,EAAA+a,GACA,GAAApc,IAAAoc,KAAApc,SAAA,CAEA,OAAAA,IAAAqc,EAAAhb,GACAib,EAAAL,EAAA5a,KAAA+a,GACA/a,EAGA,QAAAkb,GAAAC,EAAAC,EAAAL,GACA,MAAAI,GAAArS,OAAAsS,GAAArd,IAAA,SAAAjD,GACA,MAAAggB,GAAAhgB,EAAAigB,KAIA,QAAAM,GAAAF,EAAAC,EAAAL,GACA,GAAAO,KAaA,OAZAN,GAAAG,IACAlQ,OAAAsQ,KAAAJ,GAAAvR,QAAA,SAAA4R,GACAF,EAAAE,GAAAV,EAAAK,EAAAK,GAAAT,KAGA9P,OAAAsQ,KAAAH,GAAAxR,QAAA,SAAA4R,GACAR,EAAAI,EAAAI,KAAAL,EAAAK,GAGAF,EAAAE,GAAAP,EAAAE,EAAAK,GAAAJ,EAAAI,GAAAT,GAFAO,EAAAE,GAAAV,EAAAM,EAAAI,GAAAT,KAKAO,EAGA,QAAAL,GAAAE,EAAAC,EAAAL,GACA,GAAAU,GAAAzR,MAAAC,QAAAmR,GACAM,EAAA1R,MAAAC,QAAAkR,GACA7O,EAAAyO,IAAmCY,WAAAT,GACnCU,EAAAH,IAAAC,CAEA,IAAAE,EAEE,IAAAH,EAAA,CACF,GAAAE,GAAArP,EAAAqP,YAAAT,CACA,OAAAS,GAAAR,EAAAC,EAAAL,GAEA,MAAAM,GAAAF,EAAAC,EAAAL,GALA,MAAAD,GAAAM,EAAAL,GAnEA,GAAAC,GAAA,SAAAhb,GACA,MAAAsa,GAAAta,KACAua,EAAAva,IAgBA6b,EAAA,kBAAAlc,uBACAgb,EAAAkB,EAAAlc,cAAA,sBAyDAsb,GAAAa,IAAA,SAAAC,EAAAhB,GACA,IAAA/Q,MAAAC,QAAA8R,GACA,SAAAjO,OAAA,oCAGA,OAAAiO,GAAAzY,OAAA,SAAA0U,EAAAnY,GACA,MAAAob,GAAAjD,EAAAnY,EAAAkb,QAIA,IAAAiB,GAAAf,CAEA,OAAAe","file":"frappe-gantt.min.js","sourcesContent":["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Gantt\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Gantt\"] = factory();\n\telse\n\t\troot[\"Gantt\"] = factory();\n})(this, function() {\nreturn \n\n\n/** WEBPACK FOOTER **\n ** webpack/universalModuleDefinition\n **/","(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Gantt\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Gantt\"] = factory();\n\telse\n\t\troot[\"Gantt\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId])\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\texports: {},\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tObject.defineProperty(exports, \"__esModule\", {\n\t\tvalue: true\n\t});\n\texports.default = Gantt;\n\t\n\t__webpack_require__(1);\n\t\n\tvar _Bar = __webpack_require__(5);\n\t\n\tvar _Bar2 = _interopRequireDefault(_Bar);\n\t\n\tvar _Arrow = __webpack_require__(6);\n\t\n\tvar _Arrow2 = _interopRequireDefault(_Arrow);\n\t\n\tfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\t\n\tfunction Gantt(element, tasks) {\n\t\tvar config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\t\n\t\n\t\tvar self = {};\n\t\n\t\tfunction init() {\n\t\t\tset_defaults();\n\t\n\t\t\t// expose methods\n\t\t\tself.change_view_mode = change_view_mode;\n\t\t\tself.unselect_all = unselect_all;\n\t\t\tself.view_is = view_is;\n\t\t\tself.get_bar = get_bar;\n\t\t\tself.trigger_event = trigger_event;\n\t\t\tself.refresh = refresh;\n\t\n\t\t\t// initialize with default view mode\n\t\t\tchange_view_mode(self.config.view_mode);\n\t\t}\n\t\n\t\tfunction set_defaults() {\n\t\n\t\t\tvar merge = __webpack_require__(7);\n\t\n\t\t\tvar defaults = {\n\t\t\t\theader_height: 50,\n\t\t\t\tcolumn_width: 30,\n\t\t\t\tstep: 24,\n\t\t\t\tview_modes: ['Quarter Day', 'Half Day', 'Day', 'Week', 'Month'],\n\t\t\t\tbar: {\n\t\t\t\t\theight: 20,\n\t\t\t\t\tcorner_radius: 3,\n\t\t\t\t\tresize: true,\n\t\t\t\t\tprogress: true,\n\t\t\t\t\tdrag: true\n\t\t\t\t},\n\t\t\t\tarrow: {\n\t\t\t\t\tcurve: 5\n\t\t\t\t},\n\t\t\t\tpadding: 18,\n\t\t\t\tview_mode: 'Day',\n\t\t\t\tdate_format: 'YYYY-MM-DD',\n\t\t\t\tcustom_popup_html: null\n\t\t\t};\n\t\t\tself.config = merge(defaults, config);\n\t\n\t\t\treset_variables(tasks);\n\t\t}\n\t\n\t\tfunction reset_variables(tasks) {\n\t\t\tif (typeof element === 'string') {\n\t\t\t\tself.element = document.querySelector(element);\n\t\t\t} else if (element instanceof SVGElement) {\n\t\t\t\tself.element = element;\n\t\t\t} else if (element instanceof HTMLElement) {\n\t\t\t\tself.element = element.querySelector('svg');\n\t\t\t} else {\n\t\t\t\tthrow new TypeError('Frappé Gantt only supports usage of a string CSS selector,' + ' HTML DOM element or SVG DOM element for the \\'element\\' parameter');\n\t\t\t}\n\t\n\t\t\tself._tasks = tasks;\n\t\n\t\t\tself._bars = [];\n\t\t\tself._arrows = [];\n\t\t\tself.element_groups = {};\n\t\t}\n\t\n\t\tfunction refresh(updated_tasks) {\n\t\t\treset_variables(updated_tasks);\n\t\t\tchange_view_mode(self.config.view_mode);\n\t\t}\n\t\n\t\tfunction change_view_mode(mode) {\n\t\t\tset_scale(mode);\n\t\t\tprepare();\n\t\t\trender();\n\t\t\t// fire viewmode_change event\n\t\t\ttrigger_event('view_change', [mode]);\n\t\t}\n\t\n\t\tfunction prepare() {\n\t\t\tprepare_tasks();\n\t\t\tprepare_dependencies();\n\t\t\tprepare_dates();\n\t\t\tprepare_canvas();\n\t\t}\n\t\n\t\tfunction prepare_tasks() {\n\t\n\t\t\t// prepare tasks\n\t\t\tself.tasks = self._tasks.map(function (task, i) {\n\t\n\t\t\t\t// momentify\n\t\t\t\ttask._start = moment(task.start, self.config.date_format);\n\t\t\t\ttask._end = moment(task.end, self.config.date_format);\n\t\n\t\t\t\t// make task invalid if duration too large\n\t\t\t\tif (task._end.diff(task._start, 'years') > 10) {\n\t\t\t\t\ttask.end = null;\n\t\t\t\t}\n\t\n\t\t\t\t// cache index\n\t\t\t\ttask._index = i;\n\t\n\t\t\t\t// invalid dates\n\t\t\t\tif (!task.start && !task.end) {\n\t\t\t\t\ttask._start = moment().startOf('day');\n\t\t\t\t\ttask._end = moment().startOf('day').add(2, 'days');\n\t\t\t\t}\n\t\t\t\tif (!task.start && task.end) {\n\t\t\t\t\ttask._start = task._end.clone().add(-2, 'days');\n\t\t\t\t}\n\t\t\t\tif (task.start && !task.end) {\n\t\t\t\t\ttask._end = task._start.clone().add(2, 'days');\n\t\t\t\t}\n\t\n\t\t\t\t// invalid flag\n\t\t\t\tif (!task.start || !task.end) {\n\t\t\t\t\ttask.invalid = true;\n\t\t\t\t}\n\t\n\t\t\t\t// dependencies\n\t\t\t\tif (typeof task.dependencies === 'string' || !task.dependencies) {\n\t\t\t\t\tvar deps = [];\n\t\t\t\t\tif (task.dependencies) {\n\t\t\t\t\t\tdeps = task.dependencies.split(',').map(function (d) {\n\t\t\t\t\t\t\treturn d.trim();\n\t\t\t\t\t\t}).filter(function (d) {\n\t\t\t\t\t\t\treturn d;\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\ttask.dependencies = deps;\n\t\t\t\t}\n\t\n\t\t\t\t// uids\n\t\t\t\tif (!task.id) {\n\t\t\t\t\ttask.id = generate_id(task);\n\t\t\t\t}\n\t\n\t\t\t\treturn task;\n\t\t\t});\n\t\t}\n\t\n\t\tfunction prepare_dependencies() {\n\t\n\t\t\tself.dependency_map = {};\n\t\t\tvar _iteratorNormalCompletion = true;\n\t\t\tvar _didIteratorError = false;\n\t\t\tvar _iteratorError = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator = self.tasks[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n\t\t\t\t\tvar t = _step.value;\n\t\t\t\t\tvar _iteratorNormalCompletion2 = true;\n\t\t\t\t\tvar _didIteratorError2 = false;\n\t\t\t\t\tvar _iteratorError2 = undefined;\n\t\n\t\t\t\t\ttry {\n\t\t\t\t\t\tfor (var _iterator2 = t.dependencies[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n\t\t\t\t\t\t\tvar d = _step2.value;\n\t\n\t\t\t\t\t\t\tself.dependency_map[d] = self.dependency_map[d] || [];\n\t\t\t\t\t\t\tself.dependency_map[d].push(t.id);\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t_didIteratorError2 = true;\n\t\t\t\t\t\t_iteratorError2 = err;\n\t\t\t\t\t} finally {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tif (!_iteratorNormalCompletion2 && _iterator2.return) {\n\t\t\t\t\t\t\t\t_iterator2.return();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\tif (_didIteratorError2) {\n\t\t\t\t\t\t\t\tthrow _iteratorError2;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError = true;\n\t\t\t\t_iteratorError = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion && _iterator.return) {\n\t\t\t\t\t\t_iterator.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError) {\n\t\t\t\t\t\tthrow _iteratorError;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction prepare_dates() {\n\t\n\t\t\tself.gantt_start = self.gantt_end = null;\n\t\t\tvar _iteratorNormalCompletion3 = true;\n\t\t\tvar _didIteratorError3 = false;\n\t\t\tvar _iteratorError3 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator3 = self.tasks[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {\n\t\t\t\t\tvar task = _step3.value;\n\t\n\t\t\t\t\t// set global start and end date\n\t\t\t\t\tif (!self.gantt_start || task._start < self.gantt_start) {\n\t\t\t\t\t\tself.gantt_start = task._start;\n\t\t\t\t\t}\n\t\t\t\t\tif (!self.gantt_end || task._end > self.gantt_end) {\n\t\t\t\t\t\tself.gantt_end = task._end;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError3 = true;\n\t\t\t\t_iteratorError3 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion3 && _iterator3.return) {\n\t\t\t\t\t\t_iterator3.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError3) {\n\t\t\t\t\t\tthrow _iteratorError3;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\tset_gantt_dates();\n\t\t\tsetup_dates();\n\t\t}\n\t\n\t\tfunction prepare_canvas() {\n\t\t\tif (self.canvas) return;\n\t\t\tself.canvas = Snap(self.element).addClass('gantt');\n\t\t}\n\t\n\t\tfunction render() {\n\t\t\tclear();\n\t\t\tsetup_groups();\n\t\t\tmake_grid();\n\t\t\tmake_dates();\n\t\t\tmake_bars();\n\t\t\tmake_arrows();\n\t\t\tmap_arrows_on_bars();\n\t\t\tset_width();\n\t\t\tset_scroll_position();\n\t\t\tbind_grid_click();\n\t\t}\n\t\n\t\tfunction clear() {\n\t\t\tself.canvas.clear();\n\t\t\tself._bars = [];\n\t\t\tself._arrows = [];\n\t\t}\n\t\n\t\tfunction set_gantt_dates() {\n\t\n\t\t\tif (view_is(['Quarter Day', 'Half Day'])) {\n\t\t\t\tself.gantt_start = self.gantt_start.clone().subtract(7, 'day');\n\t\t\t\tself.gantt_end = self.gantt_end.clone().add(7, 'day');\n\t\t\t} else if (view_is('Month')) {\n\t\t\t\tself.gantt_start = self.gantt_start.clone().startOf('year');\n\t\t\t\tself.gantt_end = self.gantt_end.clone().endOf('month').add(1, 'year');\n\t\t\t} else {\n\t\t\t\tself.gantt_start = self.gantt_start.clone().startOf('month').subtract(1, 'month');\n\t\t\t\tself.gantt_end = self.gantt_end.clone().endOf('month').add(1, 'month');\n\t\t\t}\n\t\t}\n\t\n\t\tfunction setup_dates() {\n\t\n\t\t\tself.dates = [];\n\t\t\tvar cur_date = null;\n\t\n\t\t\twhile (cur_date === null || cur_date < self.gantt_end) {\n\t\t\t\tif (!cur_date) {\n\t\t\t\t\tcur_date = self.gantt_start.clone();\n\t\t\t\t} else {\n\t\t\t\t\tcur_date = view_is('Month') ? cur_date.clone().add(1, 'month') : cur_date.clone().add(self.config.step, 'hours');\n\t\t\t\t}\n\t\t\t\tself.dates.push(cur_date);\n\t\t\t}\n\t\t}\n\t\n\t\tfunction setup_groups() {\n\t\n\t\t\tvar groups = ['grid', 'date', 'arrow', 'progress', 'bar', 'details'];\n\t\t\t// make group layers\n\t\t\tvar _iteratorNormalCompletion4 = true;\n\t\t\tvar _didIteratorError4 = false;\n\t\t\tvar _iteratorError4 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator4 = groups[Symbol.iterator](), _step4; !(_iteratorNormalCompletion4 = (_step4 = _iterator4.next()).done); _iteratorNormalCompletion4 = true) {\n\t\t\t\t\tvar group = _step4.value;\n\t\n\t\t\t\t\tself.element_groups[group] = self.canvas.group().attr({ 'id': group });\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError4 = true;\n\t\t\t\t_iteratorError4 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion4 && _iterator4.return) {\n\t\t\t\t\t\t_iterator4.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError4) {\n\t\t\t\t\t\tthrow _iteratorError4;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction set_scale(scale) {\n\t\t\tself.config.view_mode = scale;\n\t\n\t\t\tif (scale === 'Day') {\n\t\t\t\tself.config.step = 24;\n\t\t\t\tself.config.column_width = 38;\n\t\t\t} else if (scale === 'Half Day') {\n\t\t\t\tself.config.step = 24 / 2;\n\t\t\t\tself.config.column_width = 38;\n\t\t\t} else if (scale === 'Quarter Day') {\n\t\t\t\tself.config.step = 24 / 4;\n\t\t\t\tself.config.column_width = 38;\n\t\t\t} else if (scale === 'Week') {\n\t\t\t\tself.config.step = 24 * 7;\n\t\t\t\tself.config.column_width = 140;\n\t\t\t} else if (scale === 'Month') {\n\t\t\t\tself.config.step = 24 * 30;\n\t\t\t\tself.config.column_width = 120;\n\t\t\t}\n\t\t}\n\t\n\t\tfunction set_width() {\n\t\t\tvar cur_width = self.canvas.node.getBoundingClientRect().width;\n\t\t\tvar actual_width = self.canvas.select('#grid .grid-row').attr('width');\n\t\t\tif (cur_width < actual_width) {\n\t\t\t\tself.canvas.attr('width', actual_width);\n\t\t\t}\n\t\t}\n\t\n\t\tfunction set_scroll_position() {\n\t\t\tvar parent_element = self.element.parentElement;\n\t\n\t\t\tif (!parent_element) return;\n\t\n\t\t\tvar scroll_pos = get_min_date().diff(self.gantt_start, 'hours') / self.config.step * self.config.column_width - self.config.column_width;\n\t\t\tparent_element.scrollLeft = scroll_pos;\n\t\t}\n\t\n\t\tfunction get_min_date() {\n\t\t\tvar task = self.tasks.reduce(function (acc, curr) {\n\t\t\t\treturn curr._start.isSameOrBefore(acc._start) ? curr : acc;\n\t\t\t});\n\t\t\treturn task._start;\n\t\t}\n\t\n\t\tfunction make_grid() {\n\t\t\tmake_grid_background();\n\t\t\tmake_grid_rows();\n\t\t\tmake_grid_header();\n\t\t\tmake_grid_ticks();\n\t\t\tmake_grid_highlights();\n\t\t}\n\t\n\t\tfunction make_grid_background() {\n\t\n\t\t\tvar grid_width = self.dates.length * self.config.column_width,\n\t\t\t    grid_height = self.config.header_height + self.config.padding + (self.config.bar.height + self.config.padding) * self.tasks.length;\n\t\n\t\t\tself.canvas.rect(0, 0, grid_width, grid_height).addClass('grid-background').appendTo(self.element_groups.grid);\n\t\n\t\t\tself.canvas.attr({\n\t\t\t\theight: grid_height + self.config.padding + 100,\n\t\t\t\twidth: '100%'\n\t\t\t});\n\t\t}\n\t\n\t\tfunction make_grid_header() {\n\t\t\tvar header_width = self.dates.length * self.config.column_width,\n\t\t\t    header_height = self.config.header_height + 10;\n\t\t\tself.canvas.rect(0, 0, header_width, header_height).addClass('grid-header').appendTo(self.element_groups.grid);\n\t\t}\n\t\n\t\tfunction make_grid_rows() {\n\t\n\t\t\tvar rows = self.canvas.group().appendTo(self.element_groups.grid),\n\t\t\t    lines = self.canvas.group().appendTo(self.element_groups.grid),\n\t\t\t    row_width = self.dates.length * self.config.column_width,\n\t\t\t    row_height = self.config.bar.height + self.config.padding;\n\t\n\t\t\tvar row_y = self.config.header_height + self.config.padding / 2;\n\t\n\t\t\tvar _iteratorNormalCompletion5 = true;\n\t\t\tvar _didIteratorError5 = false;\n\t\t\tvar _iteratorError5 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator5 = self.tasks[Symbol.iterator](), _step5; !(_iteratorNormalCompletion5 = (_step5 = _iterator5.next()).done); _iteratorNormalCompletion5 = true) {\n\t\t\t\t\tvar task = _step5.value;\n\t\t\t\t\t// eslint-disable-line\n\t\t\t\t\tself.canvas.rect(0, row_y, row_width, row_height).addClass('grid-row').appendTo(rows);\n\t\n\t\t\t\t\tself.canvas.line(0, row_y + row_height, row_width, row_y + row_height).addClass('row-line').appendTo(lines);\n\t\n\t\t\t\t\trow_y += self.config.bar.height + self.config.padding;\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError5 = true;\n\t\t\t\t_iteratorError5 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion5 && _iterator5.return) {\n\t\t\t\t\t\t_iterator5.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError5) {\n\t\t\t\t\t\tthrow _iteratorError5;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction make_grid_ticks() {\n\t\t\tvar tick_x = 0,\n\t\t\t    tick_y = self.config.header_height + self.config.padding / 2,\n\t\t\t    tick_height = (self.config.bar.height + self.config.padding) * self.tasks.length;\n\t\n\t\t\tvar _iteratorNormalCompletion6 = true;\n\t\t\tvar _didIteratorError6 = false;\n\t\t\tvar _iteratorError6 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator6 = self.dates[Symbol.iterator](), _step6; !(_iteratorNormalCompletion6 = (_step6 = _iterator6.next()).done); _iteratorNormalCompletion6 = true) {\n\t\t\t\t\tvar date = _step6.value;\n\t\n\t\t\t\t\tvar tick_class = 'tick';\n\t\t\t\t\t// thick tick for monday\n\t\t\t\t\tif (view_is('Day') && date.day() === 1) {\n\t\t\t\t\t\ttick_class += ' thick';\n\t\t\t\t\t}\n\t\t\t\t\t// thick tick for first week\n\t\t\t\t\tif (view_is('Week') && date.date() >= 1 && date.date() < 8) {\n\t\t\t\t\t\ttick_class += ' thick';\n\t\t\t\t\t}\n\t\t\t\t\t// thick ticks for quarters\n\t\t\t\t\tif (view_is('Month') && date.month() % 3 === 0) {\n\t\t\t\t\t\ttick_class += ' thick';\n\t\t\t\t\t}\n\t\n\t\t\t\t\tself.canvas.path(Snap.format('M {x} {y} v {height}', {\n\t\t\t\t\t\tx: tick_x,\n\t\t\t\t\t\ty: tick_y,\n\t\t\t\t\t\theight: tick_height\n\t\t\t\t\t})).addClass(tick_class).appendTo(self.element_groups.grid);\n\t\n\t\t\t\t\tif (view_is('Month')) {\n\t\t\t\t\t\ttick_x += date.daysInMonth() * self.config.column_width / 30;\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttick_x += self.config.column_width;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError6 = true;\n\t\t\t\t_iteratorError6 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion6 && _iterator6.return) {\n\t\t\t\t\t\t_iterator6.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError6) {\n\t\t\t\t\t\tthrow _iteratorError6;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction make_grid_highlights() {\n\t\n\t\t\t// highlight today's date\n\t\t\tif (view_is('Day')) {\n\t\t\t\tvar x = moment().startOf('day').diff(self.gantt_start, 'hours') / self.config.step * self.config.column_width;\n\t\t\t\tvar y = 0;\n\t\t\t\tvar width = self.config.column_width;\n\t\t\t\tvar height = (self.config.bar.height + self.config.padding) * self.tasks.length + self.config.header_height + self.config.padding / 2;\n\t\n\t\t\t\tself.canvas.rect(x, y, width, height).addClass('today-highlight').appendTo(self.element_groups.grid);\n\t\t\t}\n\t\t}\n\t\n\t\tfunction make_dates() {\n\t\t\tvar _iteratorNormalCompletion7 = true;\n\t\t\tvar _didIteratorError7 = false;\n\t\t\tvar _iteratorError7 = undefined;\n\t\n\t\t\ttry {\n\t\n\t\t\t\tfor (var _iterator7 = get_dates_to_draw()[Symbol.iterator](), _step7; !(_iteratorNormalCompletion7 = (_step7 = _iterator7.next()).done); _iteratorNormalCompletion7 = true) {\n\t\t\t\t\tvar date = _step7.value;\n\t\n\t\t\t\t\tself.canvas.text(date.lower_x, date.lower_y, date.lower_text).addClass('lower-text').appendTo(self.element_groups.date);\n\t\n\t\t\t\t\tif (date.upper_text) {\n\t\t\t\t\t\tvar $upper_text = self.canvas.text(date.upper_x, date.upper_y, date.upper_text).addClass('upper-text').appendTo(self.element_groups.date);\n\t\n\t\t\t\t\t\t// remove out-of-bound dates\n\t\t\t\t\t\tif ($upper_text.getBBox().x2 > self.element_groups.grid.getBBox().width) {\n\t\t\t\t\t\t\t$upper_text.remove();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError7 = true;\n\t\t\t\t_iteratorError7 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion7 && _iterator7.return) {\n\t\t\t\t\t\t_iterator7.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError7) {\n\t\t\t\t\t\tthrow _iteratorError7;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction get_dates_to_draw() {\n\t\t\tvar last_date = null;\n\t\t\tvar dates = self.dates.map(function (date, i) {\n\t\t\t\tvar d = get_date_info(date, last_date, i);\n\t\t\t\tlast_date = date;\n\t\t\t\treturn d;\n\t\t\t});\n\t\t\treturn dates;\n\t\t}\n\t\n\t\tfunction get_date_info(date, last_date, i) {\n\t\t\tif (!last_date) {\n\t\t\t\tlast_date = date.clone().add(1, 'year');\n\t\t\t}\n\t\t\tvar date_text = {\n\t\t\t\t'Quarter Day_lower': date.format('HH'),\n\t\t\t\t'Half Day_lower': date.format('HH'),\n\t\t\t\t'Day_lower': date.date() !== last_date.date() ? date.format('D') : '',\n\t\t\t\t'Week_lower': date.month() !== last_date.month() ? date.format('D MMM') : date.format('D'),\n\t\t\t\t'Month_lower': date.format('MMMM'),\n\t\t\t\t'Quarter Day_upper': date.date() !== last_date.date() ? date.format('D MMM') : '',\n\t\t\t\t'Half Day_upper': date.date() !== last_date.date() ? date.month() !== last_date.month() ? date.format('D MMM') : date.format('D') : '',\n\t\t\t\t'Day_upper': date.month() !== last_date.month() ? date.format('MMMM') : '',\n\t\t\t\t'Week_upper': date.month() !== last_date.month() ? date.format('MMMM') : '',\n\t\t\t\t'Month_upper': date.year() !== last_date.year() ? date.format('YYYY') : ''\n\t\t\t};\n\t\n\t\t\tvar base_pos = {\n\t\t\t\tx: i * self.config.column_width,\n\t\t\t\tlower_y: self.config.header_height,\n\t\t\t\tupper_y: self.config.header_height - 25\n\t\t\t};\n\t\n\t\t\tvar x_pos = {\n\t\t\t\t'Quarter Day_lower': self.config.column_width * 4 / 2,\n\t\t\t\t'Quarter Day_upper': 0,\n\t\t\t\t'Half Day_lower': self.config.column_width * 2 / 2,\n\t\t\t\t'Half Day_upper': 0,\n\t\t\t\t'Day_lower': self.config.column_width / 2,\n\t\t\t\t'Day_upper': self.config.column_width * 30 / 2,\n\t\t\t\t'Week_lower': 0,\n\t\t\t\t'Week_upper': self.config.column_width * 4 / 2,\n\t\t\t\t'Month_lower': self.config.column_width / 2,\n\t\t\t\t'Month_upper': self.config.column_width * 12 / 2\n\t\t\t};\n\t\n\t\t\treturn {\n\t\t\t\tupper_text: date_text[self.config.view_mode + '_upper'],\n\t\t\t\tlower_text: date_text[self.config.view_mode + '_lower'],\n\t\t\t\tupper_x: base_pos.x + x_pos[self.config.view_mode + '_upper'],\n\t\t\t\tupper_y: base_pos.upper_y,\n\t\t\t\tlower_x: base_pos.x + x_pos[self.config.view_mode + '_lower'],\n\t\t\t\tlower_y: base_pos.lower_y\n\t\t\t};\n\t\t}\n\t\n\t\tfunction make_arrows() {\n\t\t\tself._arrows = [];\n\t\t\tvar _iteratorNormalCompletion8 = true;\n\t\t\tvar _didIteratorError8 = false;\n\t\t\tvar _iteratorError8 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tvar _loop = function _loop() {\n\t\t\t\t\tvar task = _step8.value;\n\t\n\t\t\t\t\tvar arrows = [];\n\t\t\t\t\tarrows = task.dependencies.map(function (dep) {\n\t\t\t\t\t\tvar dependency = get_task(dep);\n\t\t\t\t\t\tif (!dependency) return;\n\t\n\t\t\t\t\t\tvar arrow = (0, _Arrow2.default)(self, // gt\n\t\t\t\t\t\tself._bars[dependency._index], // from_task\n\t\t\t\t\t\tself._bars[task._index] // to_task\n\t\t\t\t\t\t);\n\t\t\t\t\t\tself.element_groups.arrow.add(arrow.element);\n\t\t\t\t\t\treturn arrow; // eslint-disable-line\n\t\t\t\t\t}).filter(function (arr) {\n\t\t\t\t\t\treturn arr;\n\t\t\t\t\t}); // filter falsy values\n\t\t\t\t\tself._arrows = self._arrows.concat(arrows);\n\t\t\t\t};\n\t\n\t\t\t\tfor (var _iterator8 = self.tasks[Symbol.iterator](), _step8; !(_iteratorNormalCompletion8 = (_step8 = _iterator8.next()).done); _iteratorNormalCompletion8 = true) {\n\t\t\t\t\t_loop();\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError8 = true;\n\t\t\t\t_iteratorError8 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion8 && _iterator8.return) {\n\t\t\t\t\t\t_iterator8.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError8) {\n\t\t\t\t\t\tthrow _iteratorError8;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction make_bars() {\n\t\n\t\t\tself._bars = self.tasks.map(function (task) {\n\t\t\t\tvar bar = (0, _Bar2.default)(self, task);\n\t\t\t\tself.element_groups.bar.add(bar.group);\n\t\t\t\treturn bar;\n\t\t\t});\n\t\t}\n\t\n\t\tfunction map_arrows_on_bars() {\n\t\t\tvar _iteratorNormalCompletion9 = true;\n\t\t\tvar _didIteratorError9 = false;\n\t\t\tvar _iteratorError9 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tvar _loop2 = function _loop2() {\n\t\t\t\t\tvar bar = _step9.value;\n\t\n\t\t\t\t\tbar.arrows = self._arrows.filter(function (arrow) {\n\t\t\t\t\t\treturn arrow.from_task.task.id === bar.task.id || arrow.to_task.task.id === bar.task.id;\n\t\t\t\t\t});\n\t\t\t\t};\n\t\n\t\t\t\tfor (var _iterator9 = self._bars[Symbol.iterator](), _step9; !(_iteratorNormalCompletion9 = (_step9 = _iterator9.next()).done); _iteratorNormalCompletion9 = true) {\n\t\t\t\t\t_loop2();\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError9 = true;\n\t\t\t\t_iteratorError9 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion9 && _iterator9.return) {\n\t\t\t\t\t\t_iterator9.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError9) {\n\t\t\t\t\t\tthrow _iteratorError9;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction bind_grid_click() {\n\t\t\tself.element_groups.grid.click(function () {\n\t\t\t\tunselect_all();\n\t\t\t\tself.element_groups.details.selectAll('.details-wrapper').forEach(function (el) {\n\t\t\t\t\treturn el.addClass('hide');\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\t\n\t\tfunction unselect_all() {\n\t\t\tself.canvas.selectAll('.bar-wrapper').forEach(function (el) {\n\t\t\t\tel.removeClass('active');\n\t\t\t});\n\t\t}\n\t\n\t\tfunction view_is(modes) {\n\t\t\tif (typeof modes === 'string') {\n\t\t\t\treturn self.config.view_mode === modes;\n\t\t\t} else if (Array.isArray(modes)) {\n\t\t\t\tvar _iteratorNormalCompletion10 = true;\n\t\t\t\tvar _didIteratorError10 = false;\n\t\t\t\tvar _iteratorError10 = undefined;\n\t\n\t\t\t\ttry {\n\t\t\t\t\tfor (var _iterator10 = modes[Symbol.iterator](), _step10; !(_iteratorNormalCompletion10 = (_step10 = _iterator10.next()).done); _iteratorNormalCompletion10 = true) {\n\t\t\t\t\t\tvar mode = _step10.value;\n\t\n\t\t\t\t\t\tif (self.config.view_mode === mode) return true;\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\t_didIteratorError10 = true;\n\t\t\t\t\t_iteratorError10 = err;\n\t\t\t\t} finally {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tif (!_iteratorNormalCompletion10 && _iterator10.return) {\n\t\t\t\t\t\t\t_iterator10.return();\n\t\t\t\t\t\t}\n\t\t\t\t\t} finally {\n\t\t\t\t\t\tif (_didIteratorError10) {\n\t\t\t\t\t\t\tthrow _iteratorError10;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\t\n\t\tfunction get_task(id) {\n\t\t\treturn self.tasks.find(function (task) {\n\t\t\t\treturn task.id === id;\n\t\t\t});\n\t\t}\n\t\n\t\tfunction get_bar(id) {\n\t\t\treturn self._bars.find(function (bar) {\n\t\t\t\treturn bar.task.id === id;\n\t\t\t});\n\t\t}\n\t\n\t\tfunction generate_id(task) {\n\t\t\treturn task.name + '_' + Math.random().toString(36).slice(2, 12);\n\t\t}\n\t\n\t\tfunction trigger_event(event, args) {\n\t\t\tif (self.config['on_' + event]) {\n\t\t\t\tself.config['on_' + event].apply(null, args);\n\t\t\t}\n\t\t}\n\t\n\t\tinit();\n\t\n\t\treturn self;\n\t} /* global moment, Snap */\n\t/**\n\t * Gantt:\n\t * \telement: querySelector string, HTML DOM or SVG DOM element, required\n\t * \ttasks: array of tasks, required\n\t *   task: { id, name, start, end, progress, dependencies, custom_class }\n\t * \tconfig: configuration options, optional\n\t */\n\tmodule.exports = exports['default'];\n\n/***/ },\n/* 1 */\n/***/ function(module, exports, __webpack_require__) {\n\n\t// style-loader: Adds some css to the DOM by adding a <style> tag\n\t\n\t// load the styles\n\tvar content = __webpack_require__(2);\n\tif(typeof content === 'string') content = [[module.id, content, '']];\n\t// add the styles to the DOM\n\tvar update = __webpack_require__(4)(content, {});\n\tif(content.locals) module.exports = content.locals;\n\t// Hot Module Replacement\n\tif(false) {\n\t\t// When the styles change, update the <style> tags\n\t\tif(!content.locals) {\n\t\t\tmodule.hot.accept(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\", function() {\n\t\t\t\tvar newContent = require(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\");\n\t\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\t\tupdate(newContent);\n\t\t\t});\n\t\t}\n\t\t// When the module is disposed, remove the <style> tags\n\t\tmodule.hot.dispose(function() { update(); });\n\t}\n\n/***/ },\n/* 2 */\n/***/ function(module, exports, __webpack_require__) {\n\n\texports = module.exports = __webpack_require__(3)();\n\t// imports\n\t\n\t\n\t// module\n\texports.push([module.id, \".gantt .grid-background{fill:none}.gantt .grid-header{fill:#fff;stroke:#e0e0e0;stroke-width:1.4}.gantt .grid-row{fill:#fff}.gantt .grid-row:nth-child(2n){fill:#f5f5f5}.gantt .row-line{stroke:#ebeff2}.gantt .tick{stroke:#e0e0e0;stroke-width:.2}.gantt .tick.thick{stroke-width:.4}.gantt .today-highlight{fill:#fcf8e3;opacity:.5}.gantt #arrow{fill:none;stroke:#666;stroke-width:1.4}.gantt .bar{fill:#b8c2cc;stroke:#8d99a6;stroke-width:0;transition:stroke-width .3s ease}.gantt .bar-progress{fill:#a3a3ff}.gantt .bar-invalid{fill:transparent;stroke:#8d99a6;stroke-width:1;stroke-dasharray:5}.gantt .bar-invalid~.bar-label{fill:#555}.gantt .bar-label{fill:#fff;dominant-baseline:central;text-anchor:middle;font-size:12px;font-weight:lighter}.gantt .bar-label.big{fill:#555;text-anchor:start}.gantt .handle{fill:#ddd;cursor:ew-resize;opacity:0;visibility:hidden;transition:opacity .3s ease}.gantt .bar-wrapper{cursor:pointer}.gantt .bar-wrapper:hover .bar{stroke-width:2}.gantt .bar-wrapper:hover .handle{visibility:visible;opacity:1}.gantt .bar-wrapper.active .bar{stroke-width:2}.gantt .lower-text,.gantt .upper-text{font-size:12px;text-anchor:middle}.gantt .upper-text{fill:#555}.gantt .lower-text{fill:#333}.gantt #details .details-container{background:#fff;display:inline-block;padding:12px}.gantt #details .details-container h5,.gantt #details .details-container p{margin:0}.gantt #details .details-container h5{font-size:12px;font-weight:700;margin-bottom:10px;color:#555}.gantt #details .details-container p{font-size:12px;margin-bottom:6px;color:#666}.gantt #details .details-container p:last-child{margin-bottom:0}.gantt .hide{display:none}\", \"\", {\"version\":3,\"sources\":[\"/Users/<USER>/Dropbox/gantt/src/src/gantt.scss\"],\"names\":[],\"mappings\":\"AAYA,wBAGE,SAAU,CAHZ,oBAME,UACA,eACA,gBAAiB,CARnB,iBAWE,SAAa,CAXf,+BAcE,YAvBgB,CASlB,iBAiBE,cAzB0B,CAQ5B,aAoBE,eACA,eAAiB,CArBnB,mBAuBG,eAAiB,CAvBpB,wBA2BE,aACA,UAAY,CA5Bd,cAgCE,UACA,YACA,gBAAiB,CAlCnB,YAsCE,aACA,eACA,eACA,gCAAiC,CAzCnC,qBA4CE,YA/CY,CAGd,oBA+CE,iBACA,eACA,eACA,kBAAmB,CAlDrB,+BAqDG,SA1Dc,CAKjB,kBAyDE,UACA,0BACA,mBACA,eACA,mBAAoB,CA7DtB,sBAgEG,UACA,iBAAkB,CAjErB,eAsEE,UACA,iBACA,UACA,kBACA,2BAA4B,CA1E9B,oBA8EE,cAAe,CA9EjB,+BAkFI,cAAe,CAlFnB,kCAsFI,mBACA,SAAU,CAvFd,gCA6FI,cAAe,CA7FnB,sCAmGE,eACA,kBAAmB,CApGrB,mBAuGE,SA5Ge,CAKjB,mBA0GE,SA9Ge,CAIjB,mCA8GE,gBACA,qBACA,YAAa,CAhHf,2EAmHG,QAAS,CAnHZ,sCAuHG,eACA,gBACA,mBACA,UA/Hc,CAKjB,qCA8HG,eACA,kBACA,UAtIc,CAMjB,gDAoIG,eAAgB,CApInB,aAyIE,YAAa,CACb\",\"file\":\"gantt.scss\",\"sourcesContent\":[\"$bar-color: #b8c2cc;\\n$bar-stroke: #8D99A6;\\n$border-color: #e0e0e0;\\n$light-bg: #f5f5f5;\\n$light-border-color: #ebeff2;\\n$light-yellow: #fcf8e3;\\n$text-muted: #666;\\n$text-light: #555;\\n$text-color: #333;\\n$blue: #a3a3ff;\\n$handle-color: #ddd;\\n\\n.gantt {\\n\\n\\t.grid-background {\\n\\t\\tfill: none;\\n\\t}\\n\\t.grid-header {\\n\\t\\tfill: #ffffff;\\n\\t\\tstroke: $border-color;\\n\\t\\tstroke-width: 1.4;\\n\\t}\\n\\t.grid-row {\\n\\t\\tfill: #ffffff;\\n\\t}\\n\\t.grid-row:nth-child(even) {\\n\\t\\tfill: $light-bg;\\n\\t}\\n\\t.row-line {\\n\\t\\tstroke: $light-border-color;\\n\\t}\\n\\t.tick {\\n\\t\\tstroke: $border-color;\\n\\t\\tstroke-width: 0.2;\\n\\t\\t&.thick {\\n\\t\\t\\tstroke-width: 0.4;\\n\\t\\t}\\n\\t}\\n\\t.today-highlight {\\n\\t\\tfill: $light-yellow;\\n\\t\\topacity: 0.5;\\n\\t}\\n\\n\\t#arrow {\\n\\t\\tfill: none;\\n\\t\\tstroke: $text-muted;\\n\\t\\tstroke-width: 1.4;\\n\\t}\\n\\n\\t.bar {\\n\\t\\tfill: $bar-color;\\n\\t\\tstroke: $bar-stroke;\\n\\t\\tstroke-width: 0;\\n\\t\\ttransition: stroke-width .3s ease;\\n\\t}\\n\\t.bar-progress {\\n\\t\\tfill: $blue;\\n\\t}\\n\\t.bar-invalid {\\n\\t\\tfill: transparent;\\n\\t\\tstroke: $bar-stroke;\\n\\t\\tstroke-width: 1;\\n\\t\\tstroke-dasharray: 5;\\n\\n\\t\\t&~.bar-label {\\n\\t\\t\\tfill: $text-light;\\n\\t\\t}\\n\\t}\\n\\t.bar-label {\\n\\t\\tfill: #fff;\\n\\t\\tdominant-baseline: central;\\n\\t\\ttext-anchor: middle;\\n\\t\\tfont-size: 12px;\\n\\t\\tfont-weight: lighter;\\n\\n\\t\\t&.big {\\n\\t\\t\\tfill: $text-light;\\n\\t\\t\\ttext-anchor: start;\\n\\t\\t}\\n\\t}\\n\\n\\t.handle {\\n\\t\\tfill: $handle-color;\\n\\t\\tcursor: ew-resize;\\n\\t\\topacity: 0;\\n\\t\\tvisibility: hidden;\\n\\t\\ttransition: opacity .3s ease;\\n\\t}\\n\\n\\t.bar-wrapper {\\n\\t\\tcursor: pointer;\\n\\n\\t\\t&:hover {\\n\\t\\t\\t.bar {\\n\\t\\t\\t\\tstroke-width: 2;\\n\\t\\t\\t}\\n\\n\\t\\t\\t.handle {\\n\\t\\t\\t\\tvisibility: visible;\\n\\t\\t\\t\\topacity: 1;\\n\\t\\t\\t}\\n\\t\\t}\\n\\n\\t\\t&.active {\\n\\t\\t\\t.bar {\\n\\t\\t\\t\\tstroke-width: 2;\\n\\t\\t\\t}\\n\\t\\t}\\n\\t}\\n\\n\\t.lower-text, .upper-text {\\n\\t\\tfont-size: 12px;\\n\\t\\ttext-anchor: middle;\\n\\t}\\n\\t.upper-text {\\n\\t\\tfill: $text-light;\\n\\t}\\n\\t.lower-text {\\n\\t\\tfill: $text-color;\\n\\t}\\n\\n\\t#details .details-container {\\n\\t\\tbackground: #fff;\\n\\t\\tdisplay: inline-block;\\n\\t\\tpadding: 12px;\\n\\n\\t\\th5, p {\\n\\t\\t\\tmargin: 0;\\n\\t\\t}\\n\\n\\t\\th5 {\\n\\t\\t\\tfont-size: 12px;\\n\\t\\t\\tfont-weight: bold;\\n\\t\\t\\tmargin-bottom: 10px;\\n\\t\\t\\tcolor: $text-light;\\n\\t\\t}\\n\\n\\t\\tp {\\n\\t\\t\\tfont-size: 12px;\\n\\t\\t\\tmargin-bottom: 6px;\\n\\t\\t\\tcolor: $text-muted;\\n\\t\\t}\\n\\n\\t\\tp:last-child {\\n\\t\\t\\tmargin-bottom: 0;\\n\\t\\t}\\n\\t}\\n\\n\\t.hide {\\n\\t\\tdisplay: none;\\n\\t}\\n}\\n\"],\"sourceRoot\":\"\"}]);\n\t\n\t// exports\n\n\n/***/ },\n/* 3 */\n/***/ function(module, exports) {\n\n\t/*\n\t\tMIT License http://www.opensource.org/licenses/mit-license.php\n\t\tAuthor Tobias Koppers @sokra\n\t*/\n\t// css base code, injected by the css-loader\n\tmodule.exports = function() {\n\t\tvar list = [];\n\t\n\t\t// return the list of modules as css string\n\t\tlist.toString = function toString() {\n\t\t\tvar result = [];\n\t\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\t\tvar item = this[i];\n\t\t\t\tif(item[2]) {\n\t\t\t\t\tresult.push(\"@media \" + item[2] + \"{\" + item[1] + \"}\");\n\t\t\t\t} else {\n\t\t\t\t\tresult.push(item[1]);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn result.join(\"\");\n\t\t};\n\t\n\t\t// import a list of modules into the list\n\t\tlist.i = function(modules, mediaQuery) {\n\t\t\tif(typeof modules === \"string\")\n\t\t\t\tmodules = [[null, modules, \"\"]];\n\t\t\tvar alreadyImportedModules = {};\n\t\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\t\tvar id = this[i][0];\n\t\t\t\tif(typeof id === \"number\")\n\t\t\t\t\talreadyImportedModules[id] = true;\n\t\t\t}\n\t\t\tfor(i = 0; i < modules.length; i++) {\n\t\t\t\tvar item = modules[i];\n\t\t\t\t// skip already imported module\n\t\t\t\t// this implementation is not 100% perfect for weird media query combinations\n\t\t\t\t//  when a module is imported multiple times with different media queries.\n\t\t\t\t//  I hope this will never occur (Hey this way we have smaller bundles)\n\t\t\t\tif(typeof item[0] !== \"number\" || !alreadyImportedModules[item[0]]) {\n\t\t\t\t\tif(mediaQuery && !item[2]) {\n\t\t\t\t\t\titem[2] = mediaQuery;\n\t\t\t\t\t} else if(mediaQuery) {\n\t\t\t\t\t\titem[2] = \"(\" + item[2] + \") and (\" + mediaQuery + \")\";\n\t\t\t\t\t}\n\t\t\t\t\tlist.push(item);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\treturn list;\n\t};\n\n\n/***/ },\n/* 4 */\n/***/ function(module, exports, __webpack_require__) {\n\n\t/*\n\t\tMIT License http://www.opensource.org/licenses/mit-license.php\n\t\tAuthor Tobias Koppers @sokra\n\t*/\n\tvar stylesInDom = {},\n\t\tmemoize = function(fn) {\n\t\t\tvar memo;\n\t\t\treturn function () {\n\t\t\t\tif (typeof memo === \"undefined\") memo = fn.apply(this, arguments);\n\t\t\t\treturn memo;\n\t\t\t};\n\t\t},\n\t\tisOldIE = memoize(function() {\n\t\t\treturn /msie [6-9]\\b/.test(self.navigator.userAgent.toLowerCase());\n\t\t}),\n\t\tgetHeadElement = memoize(function () {\n\t\t\treturn document.head || document.getElementsByTagName(\"head\")[0];\n\t\t}),\n\t\tsingletonElement = null,\n\t\tsingletonCounter = 0,\n\t\tstyleElementsInsertedAtTop = [];\n\t\n\tmodule.exports = function(list, options) {\n\t\tif(false) {\n\t\t\tif(typeof document !== \"object\") throw new Error(\"The style-loader cannot be used in a non-browser environment\");\n\t\t}\n\t\n\t\toptions = options || {};\n\t\t// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n\t\t// tags it will allow on a page\n\t\tif (typeof options.singleton === \"undefined\") options.singleton = isOldIE();\n\t\n\t\t// By default, add <style> tags to the bottom of <head>.\n\t\tif (typeof options.insertAt === \"undefined\") options.insertAt = \"bottom\";\n\t\n\t\tvar styles = listToStyles(list);\n\t\taddStylesToDom(styles, options);\n\t\n\t\treturn function update(newList) {\n\t\t\tvar mayRemove = [];\n\t\t\tfor(var i = 0; i < styles.length; i++) {\n\t\t\t\tvar item = styles[i];\n\t\t\t\tvar domStyle = stylesInDom[item.id];\n\t\t\t\tdomStyle.refs--;\n\t\t\t\tmayRemove.push(domStyle);\n\t\t\t}\n\t\t\tif(newList) {\n\t\t\t\tvar newStyles = listToStyles(newList);\n\t\t\t\taddStylesToDom(newStyles, options);\n\t\t\t}\n\t\t\tfor(var i = 0; i < mayRemove.length; i++) {\n\t\t\t\tvar domStyle = mayRemove[i];\n\t\t\t\tif(domStyle.refs === 0) {\n\t\t\t\t\tfor(var j = 0; j < domStyle.parts.length; j++)\n\t\t\t\t\t\tdomStyle.parts[j]();\n\t\t\t\t\tdelete stylesInDom[domStyle.id];\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t}\n\t\n\tfunction addStylesToDom(styles, options) {\n\t\tfor(var i = 0; i < styles.length; i++) {\n\t\t\tvar item = styles[i];\n\t\t\tvar domStyle = stylesInDom[item.id];\n\t\t\tif(domStyle) {\n\t\t\t\tdomStyle.refs++;\n\t\t\t\tfor(var j = 0; j < domStyle.parts.length; j++) {\n\t\t\t\t\tdomStyle.parts[j](item.parts[j]);\n\t\t\t\t}\n\t\t\t\tfor(; j < item.parts.length; j++) {\n\t\t\t\t\tdomStyle.parts.push(addStyle(item.parts[j], options));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tvar parts = [];\n\t\t\t\tfor(var j = 0; j < item.parts.length; j++) {\n\t\t\t\t\tparts.push(addStyle(item.parts[j], options));\n\t\t\t\t}\n\t\t\t\tstylesInDom[item.id] = {id: item.id, refs: 1, parts: parts};\n\t\t\t}\n\t\t}\n\t}\n\t\n\tfunction listToStyles(list) {\n\t\tvar styles = [];\n\t\tvar newStyles = {};\n\t\tfor(var i = 0; i < list.length; i++) {\n\t\t\tvar item = list[i];\n\t\t\tvar id = item[0];\n\t\t\tvar css = item[1];\n\t\t\tvar media = item[2];\n\t\t\tvar sourceMap = item[3];\n\t\t\tvar part = {css: css, media: media, sourceMap: sourceMap};\n\t\t\tif(!newStyles[id])\n\t\t\t\tstyles.push(newStyles[id] = {id: id, parts: [part]});\n\t\t\telse\n\t\t\t\tnewStyles[id].parts.push(part);\n\t\t}\n\t\treturn styles;\n\t}\n\t\n\tfunction insertStyleElement(options, styleElement) {\n\t\tvar head = getHeadElement();\n\t\tvar lastStyleElementInsertedAtTop = styleElementsInsertedAtTop[styleElementsInsertedAtTop.length - 1];\n\t\tif (options.insertAt === \"top\") {\n\t\t\tif(!lastStyleElementInsertedAtTop) {\n\t\t\t\thead.insertBefore(styleElement, head.firstChild);\n\t\t\t} else if(lastStyleElementInsertedAtTop.nextSibling) {\n\t\t\t\thead.insertBefore(styleElement, lastStyleElementInsertedAtTop.nextSibling);\n\t\t\t} else {\n\t\t\t\thead.appendChild(styleElement);\n\t\t\t}\n\t\t\tstyleElementsInsertedAtTop.push(styleElement);\n\t\t} else if (options.insertAt === \"bottom\") {\n\t\t\thead.appendChild(styleElement);\n\t\t} else {\n\t\t\tthrow new Error(\"Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.\");\n\t\t}\n\t}\n\t\n\tfunction removeStyleElement(styleElement) {\n\t\tstyleElement.parentNode.removeChild(styleElement);\n\t\tvar idx = styleElementsInsertedAtTop.indexOf(styleElement);\n\t\tif(idx >= 0) {\n\t\t\tstyleElementsInsertedAtTop.splice(idx, 1);\n\t\t}\n\t}\n\t\n\tfunction createStyleElement(options) {\n\t\tvar styleElement = document.createElement(\"style\");\n\t\tstyleElement.type = \"text/css\";\n\t\tinsertStyleElement(options, styleElement);\n\t\treturn styleElement;\n\t}\n\t\n\tfunction createLinkElement(options) {\n\t\tvar linkElement = document.createElement(\"link\");\n\t\tlinkElement.rel = \"stylesheet\";\n\t\tinsertStyleElement(options, linkElement);\n\t\treturn linkElement;\n\t}\n\t\n\tfunction addStyle(obj, options) {\n\t\tvar styleElement, update, remove;\n\t\n\t\tif (options.singleton) {\n\t\t\tvar styleIndex = singletonCounter++;\n\t\t\tstyleElement = singletonElement || (singletonElement = createStyleElement(options));\n\t\t\tupdate = applyToSingletonTag.bind(null, styleElement, styleIndex, false);\n\t\t\tremove = applyToSingletonTag.bind(null, styleElement, styleIndex, true);\n\t\t} else if(obj.sourceMap &&\n\t\t\ttypeof URL === \"function\" &&\n\t\t\ttypeof URL.createObjectURL === \"function\" &&\n\t\t\ttypeof URL.revokeObjectURL === \"function\" &&\n\t\t\ttypeof Blob === \"function\" &&\n\t\t\ttypeof btoa === \"function\") {\n\t\t\tstyleElement = createLinkElement(options);\n\t\t\tupdate = updateLink.bind(null, styleElement);\n\t\t\tremove = function() {\n\t\t\t\tremoveStyleElement(styleElement);\n\t\t\t\tif(styleElement.href)\n\t\t\t\t\tURL.revokeObjectURL(styleElement.href);\n\t\t\t};\n\t\t} else {\n\t\t\tstyleElement = createStyleElement(options);\n\t\t\tupdate = applyToTag.bind(null, styleElement);\n\t\t\tremove = function() {\n\t\t\t\tremoveStyleElement(styleElement);\n\t\t\t};\n\t\t}\n\t\n\t\tupdate(obj);\n\t\n\t\treturn function updateStyle(newObj) {\n\t\t\tif(newObj) {\n\t\t\t\tif(newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap)\n\t\t\t\t\treturn;\n\t\t\t\tupdate(obj = newObj);\n\t\t\t} else {\n\t\t\t\tremove();\n\t\t\t}\n\t\t};\n\t}\n\t\n\tvar replaceText = (function () {\n\t\tvar textStore = [];\n\t\n\t\treturn function (index, replacement) {\n\t\t\ttextStore[index] = replacement;\n\t\t\treturn textStore.filter(Boolean).join('\\n');\n\t\t};\n\t})();\n\t\n\tfunction applyToSingletonTag(styleElement, index, remove, obj) {\n\t\tvar css = remove ? \"\" : obj.css;\n\t\n\t\tif (styleElement.styleSheet) {\n\t\t\tstyleElement.styleSheet.cssText = replaceText(index, css);\n\t\t} else {\n\t\t\tvar cssNode = document.createTextNode(css);\n\t\t\tvar childNodes = styleElement.childNodes;\n\t\t\tif (childNodes[index]) styleElement.removeChild(childNodes[index]);\n\t\t\tif (childNodes.length) {\n\t\t\t\tstyleElement.insertBefore(cssNode, childNodes[index]);\n\t\t\t} else {\n\t\t\t\tstyleElement.appendChild(cssNode);\n\t\t\t}\n\t\t}\n\t}\n\t\n\tfunction applyToTag(styleElement, obj) {\n\t\tvar css = obj.css;\n\t\tvar media = obj.media;\n\t\n\t\tif(media) {\n\t\t\tstyleElement.setAttribute(\"media\", media)\n\t\t}\n\t\n\t\tif(styleElement.styleSheet) {\n\t\t\tstyleElement.styleSheet.cssText = css;\n\t\t} else {\n\t\t\twhile(styleElement.firstChild) {\n\t\t\t\tstyleElement.removeChild(styleElement.firstChild);\n\t\t\t}\n\t\t\tstyleElement.appendChild(document.createTextNode(css));\n\t\t}\n\t}\n\t\n\tfunction updateLink(linkElement, obj) {\n\t\tvar css = obj.css;\n\t\tvar sourceMap = obj.sourceMap;\n\t\n\t\tif(sourceMap) {\n\t\t\t// http://stackoverflow.com/a/26603875\n\t\t\tcss += \"\\n/*# sourceMappingURL=data:application/json;base64,\" + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + \" */\";\n\t\t}\n\t\n\t\tvar blob = new Blob([css], { type: \"text/css\" });\n\t\n\t\tvar oldSrc = linkElement.href;\n\t\n\t\tlinkElement.href = URL.createObjectURL(blob);\n\t\n\t\tif(oldSrc)\n\t\t\tURL.revokeObjectURL(oldSrc);\n\t}\n\n\n/***/ },\n/* 5 */\n/***/ function(module, exports) {\n\n\t'use strict';\n\t\n\tObject.defineProperty(exports, \"__esModule\", {\n\t\tvalue: true\n\t});\n\texports.default = Bar;\n\t/* global Snap */\n\t/*\n\t\tClass: Bar\n\t\n\t\tOpts:\n\t\t\tgt: Gantt object\n\t\t\ttask: task object\n\t*/\n\t\n\tfunction Bar(gt, task) {\n\t\n\t\tvar self = {};\n\t\n\t\tfunction init() {\n\t\t\tset_defaults();\n\t\t\tprepare();\n\t\t\tdraw();\n\t\t\tbind();\n\t\t}\n\t\n\t\tfunction set_defaults() {\n\t\t\tself.action_completed = false;\n\t\t\tself.task = task;\n\t\t}\n\t\n\t\tfunction prepare() {\n\t\t\tprepare_values();\n\t\t\tprepare_plugins();\n\t\t}\n\t\n\t\tfunction prepare_values() {\n\t\t\tself.invalid = self.task.invalid;\n\t\t\tself.height = gt.config.bar.height;\n\t\t\tself.x = compute_x();\n\t\t\tself.y = compute_y();\n\t\t\tself.corner_radius = gt.config.bar.corner_radius;\n\t\t\tself.duration = (self.task._end.diff(self.task._start, 'hours') + 24) / gt.config.step;\n\t\t\tself.width = gt.config.column_width * self.duration;\n\t\t\tself.progress_width = gt.config.column_width * self.duration * (self.task.progress / 100) || 0;\n\t\t\tself.group = gt.canvas.group().addClass('bar-wrapper').addClass(self.task.custom_class || '');\n\t\t\tself.bar_group = gt.canvas.group().addClass('bar-group').appendTo(self.group);\n\t\t\tself.handle_group = gt.canvas.group().addClass('handle-group').appendTo(self.group);\n\t\t\tself.is_resizable = gt.config.bar.resize;\n\t\t\tself.has_progress = gt.config.bar.progress;\n\t\t\tself.is_draggable = gt.config.bar.drag;\n\t\t}\n\t\n\t\tfunction prepare_plugins() {\n\t\t\tSnap.plugin(function (Snap, Element, Paper, global, Fragment) {\n\t\t\t\tElement.prototype.getX = function () {\n\t\t\t\t\treturn +this.attr('x');\n\t\t\t\t};\n\t\t\t\tElement.prototype.getY = function () {\n\t\t\t\t\treturn +this.attr('y');\n\t\t\t\t};\n\t\t\t\tElement.prototype.getWidth = function () {\n\t\t\t\t\treturn +this.attr('width');\n\t\t\t\t};\n\t\t\t\tElement.prototype.getHeight = function () {\n\t\t\t\t\treturn +this.attr('height');\n\t\t\t\t};\n\t\t\t\tElement.prototype.getEndX = function () {\n\t\t\t\t\treturn this.getX() + this.getWidth();\n\t\t\t\t};\n\t\t\t});\n\t\t}\n\t\n\t\tfunction draw() {\n\t\t\tdraw_bar();\n\t\t\tif (self.has_progress) {\n\t\t\t\tdraw_progress_bar();\n\t\t\t\tdraw_progress_handle();\n\t\t\t}\n\t\t\tdraw_label();\n\t\t\tif (self.is_resizable) {\n\t\t\t\tdraw_resize_handles();\n\t\t\t}\n\t\t}\n\t\n\t\tfunction draw_bar() {\n\t\t\tself.$bar = gt.canvas.rect(self.x, self.y, self.width, self.height, self.corner_radius, self.corner_radius).addClass('bar').appendTo(self.bar_group);\n\t\t\tif (self.invalid) {\n\t\t\t\tself.$bar.addClass('bar-invalid');\n\t\t\t}\n\t\t}\n\t\n\t\tfunction draw_progress_bar() {\n\t\t\tif (self.invalid) return;\n\t\t\tself.$bar_progress = gt.canvas.rect(self.x, self.y, self.progress_width, self.height, self.corner_radius, self.corner_radius).addClass('bar-progress').appendTo(self.bar_group);\n\t\t}\n\t\n\t\tfunction draw_label() {\n\t\t\tgt.canvas.text(self.x + self.width / 2, self.y + self.height / 2, self.task.name).addClass('bar-label').appendTo(self.bar_group);\n\t\t\tupdate_label_position();\n\t\t}\n\t\n\t\tfunction draw_resize_handles() {\n\t\t\tif (self.invalid) return;\n\t\n\t\t\tvar bar = self.$bar,\n\t\t\t    handle_width = 8;\n\t\n\t\t\tgt.canvas.rect(bar.getX() + bar.getWidth() - 9, bar.getY() + 1, handle_width, self.height - 2, self.corner_radius, self.corner_radius).addClass('handle right').appendTo(self.handle_group);\n\t\t\tgt.canvas.rect(bar.getX() + 1, bar.getY() + 1, handle_width, self.height - 2, self.corner_radius, self.corner_radius).addClass('handle left').appendTo(self.handle_group);\n\t\t}\n\t\n\t\tfunction draw_progress_handle() {\n\t\t\tif (self.invalid) return;\n\t\n\t\t\tif (self.task.progress && self.task.progress < 100) {\n\t\t\t\tgt.canvas.polygon(get_progress_polygon_points()).addClass('handle progress').appendTo(self.handle_group);\n\t\t\t}\n\t\t}\n\t\n\t\tfunction get_progress_polygon_points() {\n\t\t\tvar bar_progress = self.$bar_progress;\n\t\t\treturn [bar_progress.getEndX() - 5, bar_progress.getY() + bar_progress.getHeight(), bar_progress.getEndX() + 5, bar_progress.getY() + bar_progress.getHeight(), bar_progress.getEndX(), bar_progress.getY() + bar_progress.getHeight() - 8.66];\n\t\t}\n\t\n\t\tfunction bind() {\n\t\t\tif (self.invalid) return;\n\t\t\tsetup_click_event();\n\t\t\tshow_details();\n\t\t\tif (self.is_resizable) {\n\t\t\t\tbind_resize();\n\t\t\t}\n\t\t\tif (self.has_progress) {\n\t\t\t\tbind_resize_progress();\n\t\t\t}\n\t\t\tif (self.is_draggable) {\n\t\t\t\tbind_drag();\n\t\t\t}\n\t\t}\n\t\n\t\tfunction show_details() {\n\t\t\tvar popover_group = gt.element_groups.details;\n\t\t\tself.details_box = popover_group.select('.details-wrapper[data-task=\\'' + self.task.id + '\\']');\n\t\n\t\t\tif (!self.details_box) {\n\t\t\t\tself.details_box = gt.canvas.group().addClass('details-wrapper hide').attr('data-task', self.task.id).appendTo(popover_group);\n\t\n\t\t\t\trender_details();\n\t\n\t\t\t\tvar f = gt.canvas.filter(Snap.filter.shadow(0, 1, 1, '#666', 0.6));\n\t\t\t\tself.details_box.attr({\n\t\t\t\t\tfilter: f\n\t\t\t\t});\n\t\t\t}\n\t\n\t\t\tself.group.click(function (e) {\n\t\t\t\tif (self.action_completed) {\n\t\t\t\t\t// just finished a move action, wait for a few seconds\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tpopover_group.selectAll('.details-wrapper').forEach(function (el) {\n\t\t\t\t\treturn el.addClass('hide');\n\t\t\t\t});\n\t\t\t\tself.details_box.removeClass('hide');\n\t\t\t});\n\t\t}\n\t\n\t\tfunction render_details() {\n\t\t\tvar _get_details_position = get_details_position(),\n\t\t\t    x = _get_details_position.x,\n\t\t\t    y = _get_details_position.y;\n\t\n\t\t\tself.details_box.transform('t' + x + ',' + y);\n\t\t\tself.details_box.clear();\n\t\n\t\t\tvar html = get_details_html();\n\t\t\tvar foreign_object = Snap.parse('<foreignObject width=\"5000\" height=\"2000\">\\n\\t\\t\\t\\t<body xmlns=\"http://www.w3.org/1999/xhtml\">\\n\\t\\t\\t\\t\\t' + html + '\\n\\t\\t\\t\\t</body>\\n\\t\\t\\t\\t</foreignObject>');\n\t\t\tself.details_box.append(foreign_object);\n\t\t}\n\t\n\t\tfunction get_details_html() {\n\t\n\t\t\t// custom html in config\n\t\t\tif (gt.config.custom_popup_html) {\n\t\t\t\tvar _html = gt.config.custom_popup_html;\n\t\t\t\tif (typeof _html === 'string') {\n\t\t\t\t\treturn _html;\n\t\t\t\t}\n\t\t\t\tif (isFunction(_html)) {\n\t\t\t\t\treturn _html(task);\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\tvar start_date = self.task._start.format('MMM D');\n\t\t\tvar end_date = self.task._end.format('MMM D');\n\t\t\tvar heading = self.task.name + ': ' + start_date + ' - ' + end_date;\n\t\n\t\t\tvar line_1 = 'Duration: ' + self.duration + ' days';\n\t\t\tvar line_2 = self.task.progress ? 'Progress: ' + self.task.progress : null;\n\t\n\t\t\tvar html = '\\n\\t\\t\\t<div class=\"details-container\">\\n\\t\\t\\t\\t<h5>' + heading + '</h5>\\n\\t\\t\\t\\t<p>' + line_1 + '</p>\\n\\t\\t\\t\\t' + (line_2 ? '<p>' + line_2 + '</p>' : '') + '\\n\\t\\t\\t</div>\\n\\t\\t';\n\t\t\treturn html;\n\t\t}\n\t\n\t\tfunction get_details_position() {\n\t\t\treturn {\n\t\t\t\tx: self.$bar.getEndX() + 2,\n\t\t\t\ty: self.$bar.getY() - 10\n\t\t\t};\n\t\t}\n\t\n\t\tfunction bind_resize() {\n\t\t\tvar _get_handles = get_handles(),\n\t\t\t    left = _get_handles.left,\n\t\t\t    right = _get_handles.right;\n\t\n\t\t\tleft.drag(onmove_left, onstart, onstop_left);\n\t\t\tright.drag(onmove_right, onstart, onstop_right);\n\t\n\t\t\tfunction onmove_right(dx, dy) {\n\t\t\t\tonmove_handle_right(dx, dy);\n\t\t\t}\n\t\t\tfunction onstop_right() {\n\t\t\t\tonstop_handle_right();\n\t\t\t}\n\t\n\t\t\tfunction onmove_left(dx, dy) {\n\t\t\t\tonmove_handle_left(dx, dy);\n\t\t\t}\n\t\t\tfunction onstop_left() {\n\t\t\t\tonstop_handle_left();\n\t\t\t}\n\t\t}\n\t\n\t\tfunction get_handles() {\n\t\t\treturn {\n\t\t\t\tleft: self.handle_group.select('.handle.left'),\n\t\t\t\tright: self.handle_group.select('.handle.right')\n\t\t\t};\n\t\t}\n\t\n\t\tfunction bind_drag() {\n\t\t\tself.bar_group.drag(onmove, onstart, onstop);\n\t\t}\n\t\n\t\tfunction bind_resize_progress() {\n\t\t\tvar bar = self.$bar,\n\t\t\t    bar_progress = self.$bar_progress,\n\t\t\t    handle = self.group.select('.handle.progress');\n\t\t\thandle && handle.drag(on_move, on_start, on_stop);\n\t\n\t\t\tfunction on_move(dx, dy) {\n\t\t\t\tif (dx > bar_progress.max_dx) {\n\t\t\t\t\tdx = bar_progress.max_dx;\n\t\t\t\t}\n\t\t\t\tif (dx < bar_progress.min_dx) {\n\t\t\t\t\tdx = bar_progress.min_dx;\n\t\t\t\t}\n\t\n\t\t\t\tbar_progress.attr('width', bar_progress.owidth + dx);\n\t\t\t\thandle.attr('points', get_progress_polygon_points());\n\t\t\t\tbar_progress.finaldx = dx;\n\t\t\t}\n\t\t\tfunction on_stop() {\n\t\t\t\tif (!bar_progress.finaldx) return;\n\t\t\t\tprogress_changed();\n\t\t\t\tset_action_completed();\n\t\t\t}\n\t\t\tfunction on_start() {\n\t\t\t\tbar_progress.finaldx = 0;\n\t\t\t\tbar_progress.owidth = bar_progress.getWidth();\n\t\t\t\tbar_progress.min_dx = -bar_progress.getWidth();\n\t\t\t\tbar_progress.max_dx = bar.getWidth() - bar_progress.getWidth();\n\t\t\t}\n\t\t}\n\t\n\t\tfunction onstart() {\n\t\t\tvar bar = self.$bar;\n\t\t\tbar.ox = bar.getX();\n\t\t\tbar.oy = bar.getY();\n\t\t\tbar.owidth = bar.getWidth();\n\t\t\tbar.finaldx = 0;\n\t\t\trun_method_for_dependencies('onstart');\n\t\t}\n\t\tself.onstart = onstart;\n\t\n\t\tfunction onmove(dx, dy) {\n\t\t\tvar bar = self.$bar;\n\t\t\tbar.finaldx = get_snap_position(dx);\n\t\t\tupdate_bar_position({ x: bar.ox + bar.finaldx });\n\t\t\trun_method_for_dependencies('onmove', [dx, dy]);\n\t\t}\n\t\tself.onmove = onmove;\n\t\n\t\tfunction onstop() {\n\t\t\tvar bar = self.$bar;\n\t\t\tif (!bar.finaldx) return;\n\t\t\tdate_changed();\n\t\t\tset_action_completed();\n\t\t\trun_method_for_dependencies('onstop');\n\t\t}\n\t\tself.onstop = onstop;\n\t\n\t\tfunction onmove_handle_left(dx, dy) {\n\t\t\tvar bar = self.$bar;\n\t\t\tbar.finaldx = get_snap_position(dx);\n\t\t\tupdate_bar_position({\n\t\t\t\tx: bar.ox + bar.finaldx,\n\t\t\t\twidth: bar.owidth - bar.finaldx\n\t\t\t});\n\t\t\trun_method_for_dependencies('onmove', [dx, dy]);\n\t\t}\n\t\tself.onmove_handle_left = onmove_handle_left;\n\t\n\t\tfunction onstop_handle_left() {\n\t\t\tvar bar = self.$bar;\n\t\t\tif (bar.finaldx) date_changed();\n\t\t\tset_action_completed();\n\t\t\trun_method_for_dependencies('onstop');\n\t\t}\n\t\tself.onstop_handle_left = onstop_handle_left;\n\t\n\t\tfunction run_method_for_dependencies(fn, args) {\n\t\t\tvar dm = gt.dependency_map;\n\t\t\tif (dm[self.task.id]) {\n\t\t\t\tvar _iteratorNormalCompletion = true;\n\t\t\t\tvar _didIteratorError = false;\n\t\t\t\tvar _iteratorError = undefined;\n\t\n\t\t\t\ttry {\n\t\t\t\t\tfor (var _iterator = dm[self.task.id][Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n\t\t\t\t\t\tvar deptask = _step.value;\n\t\n\t\t\t\t\t\tvar dt = gt.get_bar(deptask);\n\t\t\t\t\t\tdt[fn].apply(dt, args);\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\t_didIteratorError = true;\n\t\t\t\t\t_iteratorError = err;\n\t\t\t\t} finally {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tif (!_iteratorNormalCompletion && _iterator.return) {\n\t\t\t\t\t\t\t_iterator.return();\n\t\t\t\t\t\t}\n\t\t\t\t\t} finally {\n\t\t\t\t\t\tif (_didIteratorError) {\n\t\t\t\t\t\t\tthrow _iteratorError;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction onmove_handle_right(dx, dy) {\n\t\t\tvar bar = self.$bar;\n\t\t\tbar.finaldx = get_snap_position(dx);\n\t\t\tupdate_bar_position({ width: bar.owidth + bar.finaldx });\n\t\t}\n\t\n\t\tfunction onstop_handle_right() {\n\t\t\tvar bar = self.$bar;\n\t\t\tif (bar.finaldx) date_changed();\n\t\t\tset_action_completed();\n\t\t}\n\t\n\t\tfunction update_bar_position(_ref) {\n\t\t\tvar _ref$x = _ref.x,\n\t\t\t    x = _ref$x === undefined ? null : _ref$x,\n\t\t\t    _ref$width = _ref.width,\n\t\t\t    width = _ref$width === undefined ? null : _ref$width;\n\t\n\t\t\tvar bar = self.$bar;\n\t\t\tif (x) {\n\t\t\t\t// get all x values of parent task\n\t\t\t\tvar xs = task.dependencies.map(function (dep) {\n\t\t\t\t\treturn gt.get_bar(dep).$bar.getX();\n\t\t\t\t});\n\t\t\t\t// child task must not go before parent\n\t\t\t\tvar valid_x = xs.reduce(function (prev, curr) {\n\t\t\t\t\treturn x >= curr;\n\t\t\t\t}, x);\n\t\t\t\tif (!valid_x) {\n\t\t\t\t\twidth = null;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tupdate_attr(bar, 'x', x);\n\t\t\t}\n\t\t\tif (width && width >= gt.config.column_width) {\n\t\t\t\tupdate_attr(bar, 'width', width);\n\t\t\t}\n\t\n\t\t\tupdate_label_position();\n\t\t\tif (self.is_resizable) {\n\t\t\t\tupdate_resize_handle_position();\n\t\t\t}\n\t\t\tif (self.has_progress) {\n\t\t\t\tupdate_progress_handle_position();\n\t\t\t\tupdate_progressbar_position();\n\t\t\t}\n\t\t\tupdate_arrow_position();\n\t\t\tupdate_details_position();\n\t\t}\n\t\n\t\tfunction setup_click_event() {\n\t\t\tself.group.click(function () {\n\t\t\t\tif (self.action_completed) {\n\t\t\t\t\t// just finished a move action, wait for a few seconds\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\n\t\t\t\tgt.trigger_event('click', [self.task]);\n\t\t\t\tgt.unselect_all();\n\t\t\t\tself.group.toggleClass('active');\n\t\t\t});\n\t\t}\n\t\n\t\tfunction date_changed() {\n\t\t\tvar _compute_start_end_da = compute_start_end_date(),\n\t\t\t    new_start_date = _compute_start_end_da.new_start_date,\n\t\t\t    new_end_date = _compute_start_end_da.new_end_date;\n\t\n\t\t\tself.task._start = new_start_date;\n\t\t\tself.task._end = new_end_date;\n\t\t\trender_details();\n\t\t\tgt.trigger_event('date_change', [self.task, new_start_date, new_end_date]);\n\t\t}\n\t\n\t\tfunction progress_changed() {\n\t\t\tvar new_progress = compute_progress();\n\t\t\tself.task.progress = new_progress;\n\t\t\trender_details();\n\t\t\tgt.trigger_event('progress_change', [self.task, new_progress]);\n\t\t}\n\t\n\t\tfunction set_action_completed() {\n\t\t\tself.action_completed = true;\n\t\t\tsetTimeout(function () {\n\t\t\t\treturn self.action_completed = false;\n\t\t\t}, 2000);\n\t\t}\n\t\n\t\tfunction compute_start_end_date() {\n\t\t\tvar bar = self.$bar;\n\t\t\tvar x_in_units = bar.getX() / gt.config.column_width;\n\t\t\tvar new_start_date = gt.gantt_start.clone().add(x_in_units * gt.config.step, 'hours');\n\t\t\tvar width_in_units = bar.getWidth() / gt.config.column_width;\n\t\t\tvar new_end_date = new_start_date.clone().add(width_in_units * gt.config.step, 'hours');\n\t\t\t// lets say duration is 2 days\n\t\t\t// start_date = May 24 00:00:00\n\t\t\t// end_date = May 24 + 2 days = May 26 (incorrect)\n\t\t\t// so subtract 1 second so that\n\t\t\t// end_date = May 25 23:59:59\n\t\t\tnew_end_date.add('-1', 'seconds');\n\t\t\treturn { new_start_date: new_start_date, new_end_date: new_end_date };\n\t\t}\n\t\n\t\tfunction compute_progress() {\n\t\t\tvar progress = self.$bar_progress.getWidth() / self.$bar.getWidth() * 100;\n\t\t\treturn parseInt(progress, 10);\n\t\t}\n\t\n\t\tfunction compute_x() {\n\t\t\tvar x = self.task._start.diff(gt.gantt_start, 'hours') / gt.config.step * gt.config.column_width;\n\t\n\t\t\tif (gt.view_is('Month')) {\n\t\t\t\tx = self.task._start.diff(gt.gantt_start, 'days') * gt.config.column_width / 30;\n\t\t\t}\n\t\t\treturn x;\n\t\t}\n\t\n\t\tfunction compute_y() {\n\t\t\treturn gt.config.header_height + gt.config.padding + self.task._index * (self.height + gt.config.padding);\n\t\t}\n\t\n\t\tfunction get_snap_position(dx) {\n\t\t\tvar odx = dx,\n\t\t\t    rem = void 0,\n\t\t\t    position = void 0;\n\t\n\t\t\tif (gt.view_is('Week')) {\n\t\t\t\trem = dx % (gt.config.column_width / 7);\n\t\t\t\tposition = odx - rem + (rem < gt.config.column_width / 14 ? 0 : gt.config.column_width / 7);\n\t\t\t} else if (gt.view_is('Month')) {\n\t\t\t\trem = dx % (gt.config.column_width / 30);\n\t\t\t\tposition = odx - rem + (rem < gt.config.column_width / 60 ? 0 : gt.config.column_width / 30);\n\t\t\t} else {\n\t\t\t\trem = dx % gt.config.column_width;\n\t\t\t\tposition = odx - rem + (rem < gt.config.column_width / 2 ? 0 : gt.config.column_width);\n\t\t\t}\n\t\t\treturn position;\n\t\t}\n\t\n\t\tfunction update_attr(element, attr, value) {\n\t\t\tvalue = +value;\n\t\t\tif (!isNaN(value)) {\n\t\t\t\telement.attr(attr, value);\n\t\t\t}\n\t\t\treturn element;\n\t\t}\n\t\n\t\tfunction update_progressbar_position() {\n\t\t\tself.$bar_progress.attr('x', self.$bar.getX());\n\t\t\tself.$bar_progress.attr('width', self.$bar.getWidth() * (self.task.progress / 100));\n\t\t}\n\t\n\t\tfunction update_label_position() {\n\t\t\tvar bar = self.$bar,\n\t\t\t    label = self.group.select('.bar-label');\n\t\t\tif (label.getBBox().width > bar.getWidth()) {\n\t\t\t\tlabel.addClass('big').attr('x', bar.getX() + bar.getWidth() + 5);\n\t\t\t} else {\n\t\t\t\tlabel.removeClass('big').attr('x', bar.getX() + bar.getWidth() / 2);\n\t\t\t}\n\t\t}\n\t\n\t\tfunction update_resize_handle_position() {\n\t\t\tvar bar = self.$bar;\n\t\t\tself.handle_group.select('.handle.left').attr({\n\t\t\t\t'x': bar.getX() + 1\n\t\t\t});\n\t\t\tself.handle_group.select('.handle.right').attr({\n\t\t\t\t'x': bar.getEndX() - 9\n\t\t\t});\n\t\t}\n\t\n\t\tfunction update_progress_handle_position() {\n\t\t\tvar handle = self.group.select('.handle.progress');\n\t\t\thandle && handle.attr('points', get_progress_polygon_points());\n\t\t}\n\t\n\t\tfunction update_arrow_position() {\n\t\t\tvar _iteratorNormalCompletion2 = true;\n\t\t\tvar _didIteratorError2 = false;\n\t\t\tvar _iteratorError2 = undefined;\n\t\n\t\t\ttry {\n\t\t\t\tfor (var _iterator2 = self.arrows[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n\t\t\t\t\tvar arrow = _step2.value;\n\t\n\t\t\t\t\tarrow.update();\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\t_didIteratorError2 = true;\n\t\t\t\t_iteratorError2 = err;\n\t\t\t} finally {\n\t\t\t\ttry {\n\t\t\t\t\tif (!_iteratorNormalCompletion2 && _iterator2.return) {\n\t\t\t\t\t\t_iterator2.return();\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tif (_didIteratorError2) {\n\t\t\t\t\t\tthrow _iteratorError2;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\n\t\tfunction update_details_position() {\n\t\t\tvar _get_details_position2 = get_details_position(),\n\t\t\t    x = _get_details_position2.x,\n\t\t\t    y = _get_details_position2.y;\n\t\n\t\t\tself.details_box && self.details_box.transform('t' + x + ',' + y);\n\t\t}\n\t\n\t\tfunction isFunction(functionToCheck) {\n\t\t\tvar getType = {};\n\t\t\treturn functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n\t\t}\n\t\n\t\tinit();\n\t\n\t\treturn self;\n\t}\n\tmodule.exports = exports['default'];\n\n/***/ },\n/* 6 */\n/***/ function(module, exports) {\n\n\t'use strict';\n\t\n\tObject.defineProperty(exports, \"__esModule\", {\n\t\tvalue: true\n\t});\n\texports.default = Arrow;\n\t/* global Snap */\n\t/*\n\t\tClass: Arrow\n\t\tfrom_task ---> to_task\n\t\n\t\tOpts:\n\t\t\tgantt (Gantt object)\n\t\t\tfrom_task (Bar object)\n\t\t\tto_task (Bar object)\n\t*/\n\t\n\tfunction Arrow(gt, from_task, to_task) {\n\t\n\t\tvar self = {};\n\t\n\t\tfunction init() {\n\t\t\tself.from_task = from_task;\n\t\t\tself.to_task = to_task;\n\t\t\tprepare();\n\t\t\tdraw();\n\t\t}\n\t\n\t\tfunction prepare() {\n\t\n\t\t\tself.start_x = from_task.$bar.getX() + from_task.$bar.getWidth() / 2;\n\t\n\t\t\tvar condition = function condition() {\n\t\t\t\treturn to_task.$bar.getX() < self.start_x + gt.config.padding && self.start_x > from_task.$bar.getX() + gt.config.padding;\n\t\t\t};\n\t\n\t\t\twhile (condition()) {\n\t\t\t\tself.start_x -= 10;\n\t\t\t}\n\t\n\t\t\tself.start_y = gt.config.header_height + gt.config.bar.height + (gt.config.padding + gt.config.bar.height) * from_task.task._index + gt.config.padding;\n\t\n\t\t\tself.end_x = to_task.$bar.getX() - gt.config.padding / 2;\n\t\t\tself.end_y = gt.config.header_height + gt.config.bar.height / 2 + (gt.config.padding + gt.config.bar.height) * to_task.task._index + gt.config.padding;\n\t\n\t\t\tvar from_is_below_to = from_task.task._index > to_task.task._index;\n\t\t\tself.curve = gt.config.arrow.curve;\n\t\t\tself.clockwise = from_is_below_to ? 1 : 0;\n\t\t\tself.curve_y = from_is_below_to ? -self.curve : self.curve;\n\t\t\tself.offset = from_is_below_to ? self.end_y + gt.config.arrow.curve : self.end_y - gt.config.arrow.curve;\n\t\n\t\t\tself.path = Snap.format('M {start_x} {start_y} V {offset} ' + 'a {curve} {curve} 0 0 {clockwise} {curve} {curve_y} ' + 'L {end_x} {end_y} m -5 -5 l 5 5 l -5 5', {\n\t\t\t\tstart_x: self.start_x,\n\t\t\t\tstart_y: self.start_y,\n\t\t\t\tend_x: self.end_x,\n\t\t\t\tend_y: self.end_y,\n\t\t\t\toffset: self.offset,\n\t\t\t\tcurve: self.curve,\n\t\t\t\tclockwise: self.clockwise,\n\t\t\t\tcurve_y: self.curve_y\n\t\t\t});\n\t\n\t\t\tif (to_task.$bar.getX() < from_task.$bar.getX() + gt.config.padding) {\n\t\t\t\tself.path = Snap.format('M {start_x} {start_y} v {down_1} ' + 'a {curve} {curve} 0 0 1 -{curve} {curve} H {left} ' + 'a {curve} {curve} 0 0 {clockwise} -{curve} {curve_y} V {down_2} ' + 'a {curve} {curve} 0 0 {clockwise} {curve} {curve_y} ' + 'L {end_x} {end_y} m -5 -5 l 5 5 l -5 5', {\n\t\t\t\t\tstart_x: self.start_x,\n\t\t\t\t\tstart_y: self.start_y,\n\t\t\t\t\tend_x: self.end_x,\n\t\t\t\t\tend_y: self.end_y,\n\t\t\t\t\tdown_1: gt.config.padding / 2 - self.curve,\n\t\t\t\t\tdown_2: to_task.$bar.getY() + to_task.$bar.getHeight() / 2 - self.curve_y,\n\t\t\t\t\tleft: to_task.$bar.getX() - gt.config.padding,\n\t\t\t\t\toffset: self.offset,\n\t\t\t\t\tcurve: self.curve,\n\t\t\t\t\tclockwise: self.clockwise,\n\t\t\t\t\tcurve_y: self.curve_y\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t\n\t\tfunction draw() {\n\t\t\tself.element = gt.canvas.path(self.path).attr('data-from', self.from_task.task.id).attr('data-to', self.to_task.task.id);\n\t\t}\n\t\n\t\tfunction update() {\n\t\t\t// eslint-disable-line\n\t\t\tprepare();\n\t\t\tself.element.attr('d', self.path);\n\t\t}\n\t\tself.update = update;\n\t\n\t\tinit();\n\t\n\t\treturn self;\n\t}\n\tmodule.exports = exports['default'];\n\n/***/ },\n/* 7 */\n/***/ function(module, exports, __webpack_require__) {\n\n\t(function (global, factory) {\n\t\t true ? module.exports = factory() :\n\t\ttypeof define === 'function' && define.amd ? define(factory) :\n\t\t(global.deepmerge = factory());\n\t}(this, (function () { 'use strict';\n\t\n\tvar isMergeableObject = function isMergeableObject(value) {\n\t\treturn isNonNullObject(value)\n\t\t\t&& !isSpecial(value)\n\t};\n\t\n\tfunction isNonNullObject(value) {\n\t\treturn !!value && typeof value === 'object'\n\t}\n\t\n\tfunction isSpecial(value) {\n\t\tvar stringValue = Object.prototype.toString.call(value);\n\t\n\t\treturn stringValue === '[object RegExp]'\n\t\t\t|| stringValue === '[object Date]'\n\t\t\t|| isReactElement(value)\n\t}\n\t\n\t// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\n\tvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\n\tvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\t\n\tfunction isReactElement(value) {\n\t\treturn value.$$typeof === REACT_ELEMENT_TYPE\n\t}\n\t\n\tfunction emptyTarget(val) {\n\t\treturn Array.isArray(val) ? [] : {}\n\t}\n\t\n\tfunction cloneUnlessOtherwiseSpecified(value, optionsArgument) {\n\t\tvar clone = !optionsArgument || optionsArgument.clone !== false;\n\t\n\t\treturn (clone && isMergeableObject(value))\n\t\t\t? deepmerge(emptyTarget(value), value, optionsArgument)\n\t\t\t: value\n\t}\n\t\n\tfunction defaultArrayMerge(target, source, optionsArgument) {\n\t\treturn target.concat(source).map(function(element) {\n\t\t\treturn cloneUnlessOtherwiseSpecified(element, optionsArgument)\n\t\t})\n\t}\n\t\n\tfunction mergeObject(target, source, optionsArgument) {\n\t\tvar destination = {};\n\t\tif (isMergeableObject(target)) {\n\t\t\tObject.keys(target).forEach(function(key) {\n\t\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], optionsArgument);\n\t\t\t});\n\t\t}\n\t\tObject.keys(source).forEach(function(key) {\n\t\t\tif (!isMergeableObject(source[key]) || !target[key]) {\n\t\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], optionsArgument);\n\t\t\t} else {\n\t\t\t\tdestination[key] = deepmerge(target[key], source[key], optionsArgument);\n\t\t\t}\n\t\t});\n\t\treturn destination\n\t}\n\t\n\tfunction deepmerge(target, source, optionsArgument) {\n\t\tvar sourceIsArray = Array.isArray(source);\n\t\tvar targetIsArray = Array.isArray(target);\n\t\tvar options = optionsArgument || { arrayMerge: defaultArrayMerge };\n\t\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\t\n\t\tif (!sourceAndTargetTypesMatch) {\n\t\t\treturn cloneUnlessOtherwiseSpecified(source, optionsArgument)\n\t\t} else if (sourceIsArray) {\n\t\t\tvar arrayMerge = options.arrayMerge || defaultArrayMerge;\n\t\t\treturn arrayMerge(target, source, optionsArgument)\n\t\t} else {\n\t\t\treturn mergeObject(target, source, optionsArgument)\n\t\t}\n\t}\n\t\n\tdeepmerge.all = function deepmergeAll(array, optionsArgument) {\n\t\tif (!Array.isArray(array)) {\n\t\t\tthrow new Error('first argument should be an array')\n\t\t}\n\t\n\t\treturn array.reduce(function(prev, next) {\n\t\t\treturn deepmerge(prev, next, optionsArgument)\n\t\t}, {})\n\t};\n\t\n\tvar deepmerge_1 = deepmerge;\n\t\n\treturn deepmerge_1;\n\t\n\t})));\n\n\n/***/ }\n/******/ ])\n});\n;\n\n\n/** WEBPACK FOOTER **\n ** frappe-gantt.min.js\n **/"," \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId])\n \t\t\treturn installedModules[moduleId].exports;\n\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\texports: {},\n \t\t\tid: moduleId,\n \t\t\tloaded: false\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.loaded = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(0);\n\n\n\n/** WEBPACK FOOTER **\n ** webpack/bootstrap 0eb5e1753b986f5bbe02\n **/","/* global moment, Snap */\n/**\n * Gantt:\n * \telement: querySelector string, HTML DOM or SVG DOM element, required\n * \ttasks: array of tasks, required\n *   task: { id, name, start, end, progress, dependencies, custom_class }\n * \tconfig: configuration options, optional\n */\nimport './gantt.scss';\n\nimport Bar from './Bar';\nimport Arrow from './Arrow';\n\nexport default function Gantt(element, tasks, config = {}) {\n\n\tconst self = {};\n\n\tfunction init() {\n\t\tset_defaults();\n\n\t\t// expose methods\n\t\tself.change_view_mode = change_view_mode;\n\t\tself.unselect_all = unselect_all;\n\t\tself.view_is = view_is;\n\t\tself.get_bar = get_bar;\n\t\tself.trigger_event = trigger_event;\n\t\tself.refresh = refresh;\n\n\t\t// initialize with default view mode\n\t\tchange_view_mode(self.config.view_mode);\n\t}\n\n\tfunction set_defaults() {\n\n\t\tconst merge = require('deepmerge');\n\n\t\tconst defaults = {\n\t\t\theader_height: 50,\n\t\t\tcolumn_width: 30,\n\t\t\tstep: 24,\n\t\t\tview_modes: [\n\t\t\t\t'Quarter Day',\n\t\t\t\t'Half Day',\n\t\t\t\t'Day',\n\t\t\t\t'Week',\n\t\t\t\t'Month'\n\t\t\t],\n\t\t\tbar: {\n\t\t\t\theight: 20,\n\t\t\t\tcorner_radius: 3,\n\t\t\t\tresize: true,\n\t\t\t\tprogress: true,\n\t\t\t\tdrag: true\n\t\t\t},\n\t\t\tarrow: {\n\t\t\t\tcurve: 5\n\t\t\t},\n\t\t\tpadding: 18,\n\t\t\tview_mode: 'Day',\n\t\t\tdate_format: 'YYYY-MM-DD',\n\t\t\tcustom_popup_html: null\n\t\t};\n\t\tself.config = merge(defaults, config);\n\n\t\treset_variables(tasks);\n\t}\n\n\tfunction reset_variables(tasks) {\n\t\tif(typeof element === 'string') {\n\t\t\tself.element = document.querySelector(element);\n\t\t} else if (element instanceof SVGElement) {\n\t\t\tself.element = element;\n\t\t} else if (element instanceof HTMLElement) {\n\t\t\tself.element = element.querySelector('svg');\n\t\t} else {\n\t\t\tthrow new TypeError('Frappé Gantt only supports usage of a string CSS selector,' +\n\t\t\t\t' HTML DOM element or SVG DOM element for the \\'element\\' parameter');\n\t\t}\n\n\t\tself._tasks = tasks;\n\n\t\tself._bars = [];\n\t\tself._arrows = [];\n\t\tself.element_groups = {};\n\t}\n\n\tfunction refresh(updated_tasks) {\n\t\treset_variables(updated_tasks);\n\t\tchange_view_mode(self.config.view_mode);\n\t}\n\n\tfunction change_view_mode(mode) {\n\t\tset_scale(mode);\n\t\tprepare();\n\t\trender();\n\t\t// fire viewmode_change event\n\t\ttrigger_event('view_change', [mode]);\n\t}\n\n\tfunction prepare() {\n\t\tprepare_tasks();\n\t\tprepare_dependencies();\n\t\tprepare_dates();\n\t\tprepare_canvas();\n\t}\n\n\tfunction prepare_tasks() {\n\n\t\t// prepare tasks\n\t\tself.tasks = self._tasks.map((task, i) => {\n\n\t\t\t// momentify\n\t\t\ttask._start = moment(task.start, self.config.date_format);\n\t\t\ttask._end = moment(task.end, self.config.date_format);\n\n\t\t\t// make task invalid if duration too large\n\t\t\tif(task._end.diff(task._start, 'years') > 10) {\n\t\t\t\ttask.end = null;\n\t\t\t}\n\n\t\t\t// cache index\n\t\t\ttask._index = i;\n\n\t\t\t// invalid dates\n\t\t\tif(!task.start && !task.end) {\n\t\t\t\ttask._start = moment().startOf('day');\n\t\t\t\ttask._end = moment().startOf('day').add(2, 'days');\n\t\t\t}\n\t\t\tif(!task.start && task.end) {\n\t\t\t\ttask._start = task._end.clone().add(-2, 'days');\n\t\t\t}\n\t\t\tif(task.start && !task.end) {\n\t\t\t\ttask._end = task._start.clone().add(2, 'days');\n\t\t\t}\n\n\t\t\t// invalid flag\n\t\t\tif(!task.start || !task.end) {\n\t\t\t\ttask.invalid = true;\n\t\t\t}\n\n\t\t\t// dependencies\n\t\t\tif(typeof task.dependencies === 'string' || !task.dependencies) {\n\t\t\t\tlet deps = [];\n\t\t\t\tif(task.dependencies) {\n\t\t\t\t\tdeps = task.dependencies\n\t\t\t\t\t\t.split(',')\n\t\t\t\t\t\t.map(d => d.trim())\n\t\t\t\t\t\t.filter((d) => d);\n\t\t\t\t}\n\t\t\t\ttask.dependencies = deps;\n\t\t\t}\n\n\t\t\t// uids\n\t\t\tif(!task.id) {\n\t\t\t\ttask.id = generate_id(task);\n\t\t\t}\n\n\t\t\treturn task;\n\t\t});\n\t}\n\n\tfunction prepare_dependencies() {\n\n\t\tself.dependency_map = {};\n\t\tfor(let t of self.tasks) {\n\t\t\tfor(let d of t.dependencies) {\n\t\t\t\tself.dependency_map[d] = self.dependency_map[d] || [];\n\t\t\t\tself.dependency_map[d].push(t.id);\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction prepare_dates() {\n\n\t\tself.gantt_start = self.gantt_end = null;\n\t\tfor(let task of self.tasks) {\n\t\t\t// set global start and end date\n\t\t\tif(!self.gantt_start || task._start < self.gantt_start) {\n\t\t\t\tself.gantt_start = task._start;\n\t\t\t}\n\t\t\tif(!self.gantt_end || task._end > self.gantt_end) {\n\t\t\t\tself.gantt_end = task._end;\n\t\t\t}\n\t\t}\n\t\tset_gantt_dates();\n\t\tsetup_dates();\n\t}\n\n\tfunction prepare_canvas() {\n\t\tif(self.canvas) return;\n\t\tself.canvas = Snap(self.element).addClass('gantt');\n\t}\n\n\tfunction render() {\n\t\tclear();\n\t\tsetup_groups();\n\t\tmake_grid();\n\t\tmake_dates();\n\t\tmake_bars();\n\t\tmake_arrows();\n\t\tmap_arrows_on_bars();\n\t\tset_width();\n\t\tset_scroll_position();\n\t\tbind_grid_click();\n\t}\n\n\tfunction clear() {\n\t\tself.canvas.clear();\n\t\tself._bars = [];\n\t\tself._arrows = [];\n\t}\n\n\tfunction set_gantt_dates() {\n\n\t\tif(view_is(['Quarter Day', 'Half Day'])) {\n\t\t\tself.gantt_start = self.gantt_start.clone().subtract(7, 'day');\n\t\t\tself.gantt_end = self.gantt_end.clone().add(7, 'day');\n\t\t} else if(view_is('Month')) {\n\t\t\tself.gantt_start = self.gantt_start.clone().startOf('year');\n\t\t\tself.gantt_end = self.gantt_end.clone().endOf('month').add(1, 'year');\n\t\t} else {\n\t\t\tself.gantt_start = self.gantt_start.clone().startOf('month').subtract(1, 'month');\n\t\t\tself.gantt_end = self.gantt_end.clone().endOf('month').add(1, 'month');\n\t\t}\n\t}\n\n\tfunction setup_dates() {\n\n\t\tself.dates = [];\n\t\tlet cur_date = null;\n\n\t\twhile(cur_date === null || cur_date < self.gantt_end) {\n\t\t\tif(!cur_date) {\n\t\t\t\tcur_date = self.gantt_start.clone();\n\t\t\t} else {\n\t\t\t\tcur_date = view_is('Month') ?\n\t\t\t\t\tcur_date.clone().add(1, 'month') :\n\t\t\t\t\tcur_date.clone().add(self.config.step, 'hours');\n\t\t\t}\n\t\t\tself.dates.push(cur_date);\n\t\t}\n\t}\n\n\tfunction setup_groups() {\n\n\t\tconst groups = ['grid', 'date', 'arrow', 'progress', 'bar', 'details'];\n\t\t// make group layers\n\t\tfor(let group of groups) {\n\t\t\tself.element_groups[group] = self.canvas.group().attr({'id': group});\n\t\t}\n\t}\n\n\tfunction set_scale(scale) {\n\t\tself.config.view_mode = scale;\n\n\t\tif(scale === 'Day') {\n\t\t\tself.config.step = 24;\n\t\t\tself.config.column_width = 38;\n\t\t} else if(scale === 'Half Day') {\n\t\t\tself.config.step = 24 / 2;\n\t\t\tself.config.column_width = 38;\n\t\t} else if(scale === 'Quarter Day') {\n\t\t\tself.config.step = 24 / 4;\n\t\t\tself.config.column_width = 38;\n\t\t} else if(scale === 'Week') {\n\t\t\tself.config.step = 24 * 7;\n\t\t\tself.config.column_width = 140;\n\t\t} else if(scale === 'Month') {\n\t\t\tself.config.step = 24 * 30;\n\t\t\tself.config.column_width = 120;\n\t\t}\n\t}\n\n\tfunction set_width() {\n\t\tconst cur_width = self.canvas.node.getBoundingClientRect().width;\n\t\tconst actual_width = self.canvas.select('#grid .grid-row').attr('width');\n\t\tif(cur_width < actual_width) {\n\t\t\tself.canvas.attr('width', actual_width);\n\t\t}\n\t}\n\n\tfunction set_scroll_position() {\n\t\tconst parent_element = self.element.parentElement;\n\n\t\tif(!parent_element) return;\n\n\t\tconst scroll_pos = get_min_date().diff(self.gantt_start, 'hours') /\n\t\t\tself.config.step * self.config.column_width - self.config.column_width;\n\t\tparent_element.scrollLeft = scroll_pos;\n\t}\n\n\tfunction get_min_date() {\n\t\tconst task = self.tasks.reduce((acc, curr) => {\n\t\t\treturn curr._start.isSameOrBefore(acc._start) ? curr : acc;\n\t\t});\n\t\treturn task._start;\n\t}\n\n\tfunction make_grid() {\n\t\tmake_grid_background();\n\t\tmake_grid_rows();\n\t\tmake_grid_header();\n\t\tmake_grid_ticks();\n\t\tmake_grid_highlights();\n\t}\n\n\tfunction make_grid_background() {\n\n\t\tconst grid_width = self.dates.length * self.config.column_width,\n\t\t\tgrid_height = self.config.header_height + self.config.padding +\n\t\t\t\t(self.config.bar.height + self.config.padding) * self.tasks.length;\n\n\t\tself.canvas.rect(0, 0, grid_width, grid_height)\n\t\t\t.addClass('grid-background')\n\t\t\t.appendTo(self.element_groups.grid);\n\n\t\tself.canvas.attr({\n\t\t\theight: grid_height + self.config.padding + 100,\n\t\t\twidth: '100%'\n\t\t});\n\t}\n\n\tfunction make_grid_header() {\n\t\tconst header_width = self.dates.length * self.config.column_width,\n\t\t\theader_height = self.config.header_height + 10;\n\t\tself.canvas.rect(0, 0, header_width, header_height)\n\t\t\t.addClass('grid-header')\n\t\t\t.appendTo(self.element_groups.grid);\n\t}\n\n\tfunction make_grid_rows() {\n\n\t\tconst rows = self.canvas.group().appendTo(self.element_groups.grid),\n\t\t\tlines = self.canvas.group().appendTo(self.element_groups.grid),\n\t\t\trow_width = self.dates.length * self.config.column_width,\n\t\t\trow_height = self.config.bar.height + self.config.padding;\n\n\t\tlet row_y = self.config.header_height + self.config.padding / 2;\n\n\t\tfor(let task of self.tasks) { // eslint-disable-line\n\t\t\tself.canvas.rect(0, row_y, row_width, row_height)\n\t\t\t\t.addClass('grid-row')\n\t\t\t\t.appendTo(rows);\n\n\t\t\tself.canvas.line(0, row_y + row_height, row_width, row_y + row_height)\n\t\t\t\t.addClass('row-line')\n\t\t\t\t.appendTo(lines);\n\n\t\t\trow_y += self.config.bar.height + self.config.padding;\n\t\t}\n\t}\n\n\tfunction make_grid_ticks() {\n\t\tlet tick_x = 0,\n\t\t\ttick_y = self.config.header_height + self.config.padding / 2,\n\t\t\ttick_height = (self.config.bar.height + self.config.padding) * self.tasks.length;\n\n\t\tfor(let date of self.dates) {\n\t\t\tlet tick_class = 'tick';\n\t\t\t// thick tick for monday\n\t\t\tif(view_is('Day') && date.day() === 1) {\n\t\t\t\ttick_class += ' thick';\n\t\t\t}\n\t\t\t// thick tick for first week\n\t\t\tif(view_is('Week') && date.date() >= 1 && date.date() < 8) {\n\t\t\t\ttick_class += ' thick';\n\t\t\t}\n\t\t\t// thick ticks for quarters\n\t\t\tif(view_is('Month') && date.month() % 3 === 0) {\n\t\t\t\ttick_class += ' thick';\n\t\t\t}\n\n\t\t\tself.canvas.path(Snap.format('M {x} {y} v {height}', {\n\t\t\t\tx: tick_x,\n\t\t\t\ty: tick_y,\n\t\t\t\theight: tick_height\n\t\t\t}))\n\t\t\t.addClass(tick_class)\n\t\t\t.appendTo(self.element_groups.grid);\n\n\t\t\tif(view_is('Month')) {\n\t\t\t\ttick_x += date.daysInMonth() * self.config.column_width / 30;\n\t\t\t} else {\n\t\t\t\ttick_x += self.config.column_width;\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction make_grid_highlights() {\n\n\t\t// highlight today's date\n\t\tif(view_is('Day')) {\n\t\t\tconst x = moment().startOf('day').diff(self.gantt_start, 'hours') /\n\t\t\t\t\tself.config.step * self.config.column_width;\n\t\t\tconst y = 0;\n\t\t\tconst width = self.config.column_width;\n\t\t\tconst height = (self.config.bar.height + self.config.padding) * self.tasks.length +\n\t\t\t\tself.config.header_height + self.config.padding / 2;\n\n\t\t\tself.canvas.rect(x, y, width, height)\n\t\t\t\t.addClass('today-highlight')\n\t\t\t\t.appendTo(self.element_groups.grid);\n\t\t}\n\t}\n\n\tfunction make_dates() {\n\n\t\tfor(let date of get_dates_to_draw()) {\n\t\t\tself.canvas.text(date.lower_x, date.lower_y, date.lower_text)\n\t\t\t\t.addClass('lower-text')\n\t\t\t\t.appendTo(self.element_groups.date);\n\n\t\t\tif(date.upper_text) {\n\t\t\t\tconst $upper_text = self.canvas.text(date.upper_x, date.upper_y, date.upper_text)\n\t\t\t\t\t.addClass('upper-text')\n\t\t\t\t\t.appendTo(self.element_groups.date);\n\n\t\t\t\t// remove out-of-bound dates\n\t\t\t\tif($upper_text.getBBox().x2 > self.element_groups.grid.getBBox().width) {\n\t\t\t\t\t$upper_text.remove();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction get_dates_to_draw() {\n\t\tlet last_date = null;\n\t\tconst dates = self.dates.map((date, i) => {\n\t\t\tconst d = get_date_info(date, last_date, i);\n\t\t\tlast_date = date;\n\t\t\treturn d;\n\t\t});\n\t\treturn dates;\n\t}\n\n\tfunction get_date_info(date, last_date, i) {\n\t\tif(!last_date) {\n\t\t\tlast_date = date.clone().add(1, 'year');\n\t\t}\n\t\tconst date_text = {\n\t\t\t'Quarter Day_lower': date.format('HH'),\n\t\t\t'Half Day_lower': date.format('HH'),\n\t\t\t'Day_lower': date.date() !== last_date.date() ? date.format('D') : '',\n\t\t\t'Week_lower': date.month() !== last_date.month() ?\n\t\t\t\tdate.format('D MMM') : date.format('D'),\n\t\t\t'Month_lower': date.format('MMMM'),\n\t\t\t'Quarter Day_upper': date.date() !== last_date.date() ? date.format('D MMM') : '',\n\t\t\t'Half Day_upper': date.date() !== last_date.date() ?\n\t\t\t\tdate.month() !== last_date.month() ?\n\t\t\t\tdate.format('D MMM') : date.format('D') : '',\n\t\t\t'Day_upper': date.month() !== last_date.month() ? date.format('MMMM') : '',\n\t\t\t'Week_upper': date.month() !== last_date.month() ? date.format('MMMM') : '',\n\t\t\t'Month_upper': date.year() !== last_date.year() ? date.format('YYYY') : ''\n\t\t};\n\n\t\tconst base_pos = {\n\t\t\tx: i * self.config.column_width,\n\t\t\tlower_y: self.config.header_height,\n\t\t\tupper_y: self.config.header_height - 25\n\t\t};\n\n\t\tconst x_pos = {\n\t\t\t'Quarter Day_lower': (self.config.column_width * 4) / 2,\n\t\t\t'Quarter Day_upper': 0,\n\t\t\t'Half Day_lower': (self.config.column_width * 2) / 2,\n\t\t\t'Half Day_upper': 0,\n\t\t\t'Day_lower': self.config.column_width / 2,\n\t\t\t'Day_upper': (self.config.column_width * 30) / 2,\n\t\t\t'Week_lower': 0,\n\t\t\t'Week_upper': (self.config.column_width * 4) / 2,\n\t\t\t'Month_lower': self.config.column_width / 2,\n\t\t\t'Month_upper': (self.config.column_width * 12) / 2\n\t\t};\n\n\t\treturn {\n\t\t\tupper_text: date_text[`${self.config.view_mode}_upper`],\n\t\t\tlower_text: date_text[`${self.config.view_mode}_lower`],\n\t\t\tupper_x: base_pos.x + x_pos[`${self.config.view_mode}_upper`],\n\t\t\tupper_y: base_pos.upper_y,\n\t\t\tlower_x: base_pos.x + x_pos[`${self.config.view_mode}_lower`],\n\t\t\tlower_y: base_pos.lower_y\n\t\t};\n\t}\n\n\tfunction make_arrows() {\n\t\tself._arrows = [];\n\t\tfor(let task of self.tasks) {\n\t\t\tlet arrows = [];\n\t\t\tarrows = task.dependencies.map(dep => {\n\t\t\t\tconst dependency = get_task(dep);\n\t\t\t\tif(!dependency) return;\n\n\t\t\t\tconst arrow = Arrow(\n\t\t\t\t\tself, // gt\n\t\t\t\t\tself._bars[dependency._index], // from_task\n\t\t\t\t\tself._bars[task._index] // to_task\n\t\t\t\t);\n\t\t\t\tself.element_groups.arrow.add(arrow.element);\n\t\t\t\treturn arrow; // eslint-disable-line\n\t\t\t}).filter(arr => arr); // filter falsy values\n\t\t\tself._arrows = self._arrows.concat(arrows);\n\t\t}\n\t}\n\n\tfunction make_bars() {\n\n\t\tself._bars = self.tasks.map((task) => {\n\t\t\tconst bar = Bar(self, task);\n\t\t\tself.element_groups.bar.add(bar.group);\n\t\t\treturn bar;\n\t\t});\n\t}\n\n\tfunction map_arrows_on_bars() {\n\t\tfor(let bar of self._bars) {\n\t\t\tbar.arrows = self._arrows.filter(arrow => {\n\t\t\t\treturn (arrow.from_task.task.id === bar.task.id) ||\n\t\t\t\t\t(arrow.to_task.task.id === bar.task.id);\n\t\t\t});\n\t\t}\n\t}\n\n\tfunction bind_grid_click() {\n\t\tself.element_groups.grid.click(() => {\n\t\t\tunselect_all();\n\t\t\tself.element_groups.details\n\t\t\t\t.selectAll('.details-wrapper')\n\t\t\t\t.forEach(el => el.addClass('hide'));\n\t\t});\n\t}\n\n\tfunction unselect_all() {\n\t\tself.canvas.selectAll('.bar-wrapper').forEach(el => {\n\t\t\tel.removeClass('active');\n\t\t});\n\t}\n\n\tfunction view_is(modes) {\n\t\tif (typeof modes === 'string') {\n\t\t\treturn self.config.view_mode === modes;\n\t\t} else if(Array.isArray(modes)) {\n\t\t\tfor (let mode of modes) {\n\t\t\t\tif(self.config.view_mode === mode) return true;\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tfunction get_task(id) {\n\t\treturn self.tasks.find((task) => {\n\t\t\treturn task.id === id;\n\t\t});\n\t}\n\n\tfunction get_bar(id) {\n\t\treturn self._bars.find((bar) => {\n\t\t\treturn bar.task.id === id;\n\t\t});\n\t}\n\n\tfunction generate_id(task) {\n\t\treturn task.name + '_' + Math.random().toString(36).slice(2, 12);\n\t}\n\n\tfunction trigger_event(event, args) {\n\t\tif(self.config['on_' + event]) {\n\t\t\tself.config['on_' + event].apply(null, args);\n\t\t}\n\t}\n\n\tinit();\n\n\treturn self;\n}\n\n\n\n/** WEBPACK FOOTER **\n ** ./~/eslint-loader!./src/Gantt.js\n **/","// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\");\nif(typeof content === 'string') content = [[module.id, content, '']];\n// add the styles to the DOM\nvar update = require(\"!../node_modules/style-loader/addStyles.js\")(content, {});\nif(content.locals) module.exports = content.locals;\n// Hot Module Replacement\nif(module.hot) {\n\t// When the styles change, update the <style> tags\n\tif(!content.locals) {\n\t\tmodule.hot.accept(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\", function() {\n\t\t\tvar newContent = require(\"!!../node_modules/css-loader/index.js?sourceMap!../node_modules/sass-loader/index.js?sourceMap!./gantt.scss\");\n\t\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\t\t\tupdate(newContent);\n\t\t});\n\t}\n\t// When the module is disposed, remove the <style> tags\n\tmodule.hot.dispose(function() { update(); });\n}\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./src/gantt.scss\n ** module id = 1\n ** module chunks = 0\n **/","exports = module.exports = require(\"../node_modules/css-loader/lib/css-base.js\")();\n// imports\n\n\n// module\nexports.push([module.id, \".gantt .grid-background{fill:none}.gantt .grid-header{fill:#fff;stroke:#e0e0e0;stroke-width:1.4}.gantt .grid-row{fill:#fff}.gantt .grid-row:nth-child(2n){fill:#f5f5f5}.gantt .row-line{stroke:#ebeff2}.gantt .tick{stroke:#e0e0e0;stroke-width:.2}.gantt .tick.thick{stroke-width:.4}.gantt .today-highlight{fill:#fcf8e3;opacity:.5}.gantt #arrow{fill:none;stroke:#666;stroke-width:1.4}.gantt .bar{fill:#b8c2cc;stroke:#8d99a6;stroke-width:0;transition:stroke-width .3s ease}.gantt .bar-progress{fill:#a3a3ff}.gantt .bar-invalid{fill:transparent;stroke:#8d99a6;stroke-width:1;stroke-dasharray:5}.gantt .bar-invalid~.bar-label{fill:#555}.gantt .bar-label{fill:#fff;dominant-baseline:central;text-anchor:middle;font-size:12px;font-weight:lighter}.gantt .bar-label.big{fill:#555;text-anchor:start}.gantt .handle{fill:#ddd;cursor:ew-resize;opacity:0;visibility:hidden;transition:opacity .3s ease}.gantt .bar-wrapper{cursor:pointer}.gantt .bar-wrapper:hover .bar{stroke-width:2}.gantt .bar-wrapper:hover .handle{visibility:visible;opacity:1}.gantt .bar-wrapper.active .bar{stroke-width:2}.gantt .lower-text,.gantt .upper-text{font-size:12px;text-anchor:middle}.gantt .upper-text{fill:#555}.gantt .lower-text{fill:#333}.gantt #details .details-container{background:#fff;display:inline-block;padding:12px}.gantt #details .details-container h5,.gantt #details .details-container p{margin:0}.gantt #details .details-container h5{font-size:12px;font-weight:700;margin-bottom:10px;color:#555}.gantt #details .details-container p{font-size:12px;margin-bottom:6px;color:#666}.gantt #details .details-container p:last-child{margin-bottom:0}.gantt .hide{display:none}\", \"\", {\"version\":3,\"sources\":[\"/Users/<USER>/Dropbox/gantt/src/src/gantt.scss\"],\"names\":[],\"mappings\":\"AAYA,wBAGE,SAAU,CAHZ,oBAME,UACA,eACA,gBAAiB,CARnB,iBAWE,SAAa,CAXf,+BAcE,YAvBgB,CASlB,iBAiBE,cAzB0B,CAQ5B,aAoBE,eACA,eAAiB,CArBnB,mBAuBG,eAAiB,CAvBpB,wBA2BE,aACA,UAAY,CA5Bd,cAgCE,UACA,YACA,gBAAiB,CAlCnB,YAsCE,aACA,eACA,eACA,gCAAiC,CAzCnC,qBA4CE,YA/CY,CAGd,oBA+CE,iBACA,eACA,eACA,kBAAmB,CAlDrB,+BAqDG,SA1Dc,CAKjB,kBAyDE,UACA,0BACA,mBACA,eACA,mBAAoB,CA7DtB,sBAgEG,UACA,iBAAkB,CAjErB,eAsEE,UACA,iBACA,UACA,kBACA,2BAA4B,CA1E9B,oBA8EE,cAAe,CA9EjB,+BAkFI,cAAe,CAlFnB,kCAsFI,mBACA,SAAU,CAvFd,gCA6FI,cAAe,CA7FnB,sCAmGE,eACA,kBAAmB,CApGrB,mBAuGE,SA5Ge,CAKjB,mBA0GE,SA9Ge,CAIjB,mCA8GE,gBACA,qBACA,YAAa,CAhHf,2EAmHG,QAAS,CAnHZ,sCAuHG,eACA,gBACA,mBACA,UA/Hc,CAKjB,qCA8HG,eACA,kBACA,UAtIc,CAMjB,gDAoIG,eAAgB,CApInB,aAyIE,YAAa,CACb\",\"file\":\"gantt.scss\",\"sourcesContent\":[\"$bar-color: #b8c2cc;\\n$bar-stroke: #8D99A6;\\n$border-color: #e0e0e0;\\n$light-bg: #f5f5f5;\\n$light-border-color: #ebeff2;\\n$light-yellow: #fcf8e3;\\n$text-muted: #666;\\n$text-light: #555;\\n$text-color: #333;\\n$blue: #a3a3ff;\\n$handle-color: #ddd;\\n\\n.gantt {\\n\\n\\t.grid-background {\\n\\t\\tfill: none;\\n\\t}\\n\\t.grid-header {\\n\\t\\tfill: #ffffff;\\n\\t\\tstroke: $border-color;\\n\\t\\tstroke-width: 1.4;\\n\\t}\\n\\t.grid-row {\\n\\t\\tfill: #ffffff;\\n\\t}\\n\\t.grid-row:nth-child(even) {\\n\\t\\tfill: $light-bg;\\n\\t}\\n\\t.row-line {\\n\\t\\tstroke: $light-border-color;\\n\\t}\\n\\t.tick {\\n\\t\\tstroke: $border-color;\\n\\t\\tstroke-width: 0.2;\\n\\t\\t&.thick {\\n\\t\\t\\tstroke-width: 0.4;\\n\\t\\t}\\n\\t}\\n\\t.today-highlight {\\n\\t\\tfill: $light-yellow;\\n\\t\\topacity: 0.5;\\n\\t}\\n\\n\\t#arrow {\\n\\t\\tfill: none;\\n\\t\\tstroke: $text-muted;\\n\\t\\tstroke-width: 1.4;\\n\\t}\\n\\n\\t.bar {\\n\\t\\tfill: $bar-color;\\n\\t\\tstroke: $bar-stroke;\\n\\t\\tstroke-width: 0;\\n\\t\\ttransition: stroke-width .3s ease;\\n\\t}\\n\\t.bar-progress {\\n\\t\\tfill: $blue;\\n\\t}\\n\\t.bar-invalid {\\n\\t\\tfill: transparent;\\n\\t\\tstroke: $bar-stroke;\\n\\t\\tstroke-width: 1;\\n\\t\\tstroke-dasharray: 5;\\n\\n\\t\\t&~.bar-label {\\n\\t\\t\\tfill: $text-light;\\n\\t\\t}\\n\\t}\\n\\t.bar-label {\\n\\t\\tfill: #fff;\\n\\t\\tdominant-baseline: central;\\n\\t\\ttext-anchor: middle;\\n\\t\\tfont-size: 12px;\\n\\t\\tfont-weight: lighter;\\n\\n\\t\\t&.big {\\n\\t\\t\\tfill: $text-light;\\n\\t\\t\\ttext-anchor: start;\\n\\t\\t}\\n\\t}\\n\\n\\t.handle {\\n\\t\\tfill: $handle-color;\\n\\t\\tcursor: ew-resize;\\n\\t\\topacity: 0;\\n\\t\\tvisibility: hidden;\\n\\t\\ttransition: opacity .3s ease;\\n\\t}\\n\\n\\t.bar-wrapper {\\n\\t\\tcursor: pointer;\\n\\n\\t\\t&:hover {\\n\\t\\t\\t.bar {\\n\\t\\t\\t\\tstroke-width: 2;\\n\\t\\t\\t}\\n\\n\\t\\t\\t.handle {\\n\\t\\t\\t\\tvisibility: visible;\\n\\t\\t\\t\\topacity: 1;\\n\\t\\t\\t}\\n\\t\\t}\\n\\n\\t\\t&.active {\\n\\t\\t\\t.bar {\\n\\t\\t\\t\\tstroke-width: 2;\\n\\t\\t\\t}\\n\\t\\t}\\n\\t}\\n\\n\\t.lower-text, .upper-text {\\n\\t\\tfont-size: 12px;\\n\\t\\ttext-anchor: middle;\\n\\t}\\n\\t.upper-text {\\n\\t\\tfill: $text-light;\\n\\t}\\n\\t.lower-text {\\n\\t\\tfill: $text-color;\\n\\t}\\n\\n\\t#details .details-container {\\n\\t\\tbackground: #fff;\\n\\t\\tdisplay: inline-block;\\n\\t\\tpadding: 12px;\\n\\n\\t\\th5, p {\\n\\t\\t\\tmargin: 0;\\n\\t\\t}\\n\\n\\t\\th5 {\\n\\t\\t\\tfont-size: 12px;\\n\\t\\t\\tfont-weight: bold;\\n\\t\\t\\tmargin-bottom: 10px;\\n\\t\\t\\tcolor: $text-light;\\n\\t\\t}\\n\\n\\t\\tp {\\n\\t\\t\\tfont-size: 12px;\\n\\t\\t\\tmargin-bottom: 6px;\\n\\t\\t\\tcolor: $text-muted;\\n\\t\\t}\\n\\n\\t\\tp:last-child {\\n\\t\\t\\tmargin-bottom: 0;\\n\\t\\t}\\n\\t}\\n\\n\\t.hide {\\n\\t\\tdisplay: none;\\n\\t}\\n}\\n\"],\"sourceRoot\":\"\"}]);\n\n// exports\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/css-loader?sourceMap!./~/sass-loader?sourceMap!./src/gantt.scss\n ** module id = 2\n ** module chunks = 0\n **/","/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\n// css base code, injected by the css-loader\nmodule.exports = function() {\n\tvar list = [];\n\n\t// return the list of modules as css string\n\tlist.toString = function toString() {\n\t\tvar result = [];\n\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\tvar item = this[i];\n\t\t\tif(item[2]) {\n\t\t\t\tresult.push(\"@media \" + item[2] + \"{\" + item[1] + \"}\");\n\t\t\t} else {\n\t\t\t\tresult.push(item[1]);\n\t\t\t}\n\t\t}\n\t\treturn result.join(\"\");\n\t};\n\n\t// import a list of modules into the list\n\tlist.i = function(modules, mediaQuery) {\n\t\tif(typeof modules === \"string\")\n\t\t\tmodules = [[null, modules, \"\"]];\n\t\tvar alreadyImportedModules = {};\n\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\tvar id = this[i][0];\n\t\t\tif(typeof id === \"number\")\n\t\t\t\talreadyImportedModules[id] = true;\n\t\t}\n\t\tfor(i = 0; i < modules.length; i++) {\n\t\t\tvar item = modules[i];\n\t\t\t// skip already imported module\n\t\t\t// this implementation is not 100% perfect for weird media query combinations\n\t\t\t//  when a module is imported multiple times with different media queries.\n\t\t\t//  I hope this will never occur (Hey this way we have smaller bundles)\n\t\t\tif(typeof item[0] !== \"number\" || !alreadyImportedModules[item[0]]) {\n\t\t\t\tif(mediaQuery && !item[2]) {\n\t\t\t\t\titem[2] = mediaQuery;\n\t\t\t\t} else if(mediaQuery) {\n\t\t\t\t\titem[2] = \"(\" + item[2] + \") and (\" + mediaQuery + \")\";\n\t\t\t\t}\n\t\t\t\tlist.push(item);\n\t\t\t}\n\t\t}\n\t};\n\treturn list;\n};\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/css-loader/lib/css-base.js\n ** module id = 3\n ** module chunks = 0\n **/","/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor Tobias Koppers @sokra\n*/\nvar stylesInDom = {},\n\tmemoize = function(fn) {\n\t\tvar memo;\n\t\treturn function () {\n\t\t\tif (typeof memo === \"undefined\") memo = fn.apply(this, arguments);\n\t\t\treturn memo;\n\t\t};\n\t},\n\tisOldIE = memoize(function() {\n\t\treturn /msie [6-9]\\b/.test(self.navigator.userAgent.toLowerCase());\n\t}),\n\tgetHeadElement = memoize(function () {\n\t\treturn document.head || document.getElementsByTagName(\"head\")[0];\n\t}),\n\tsingletonElement = null,\n\tsingletonCounter = 0,\n\tstyleElementsInsertedAtTop = [];\n\nmodule.exports = function(list, options) {\n\tif(typeof DEBUG !== \"undefined\" && DEBUG) {\n\t\tif(typeof document !== \"object\") throw new Error(\"The style-loader cannot be used in a non-browser environment\");\n\t}\n\n\toptions = options || {};\n\t// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n\t// tags it will allow on a page\n\tif (typeof options.singleton === \"undefined\") options.singleton = isOldIE();\n\n\t// By default, add <style> tags to the bottom of <head>.\n\tif (typeof options.insertAt === \"undefined\") options.insertAt = \"bottom\";\n\n\tvar styles = listToStyles(list);\n\taddStylesToDom(styles, options);\n\n\treturn function update(newList) {\n\t\tvar mayRemove = [];\n\t\tfor(var i = 0; i < styles.length; i++) {\n\t\t\tvar item = styles[i];\n\t\t\tvar domStyle = stylesInDom[item.id];\n\t\t\tdomStyle.refs--;\n\t\t\tmayRemove.push(domStyle);\n\t\t}\n\t\tif(newList) {\n\t\t\tvar newStyles = listToStyles(newList);\n\t\t\taddStylesToDom(newStyles, options);\n\t\t}\n\t\tfor(var i = 0; i < mayRemove.length; i++) {\n\t\t\tvar domStyle = mayRemove[i];\n\t\t\tif(domStyle.refs === 0) {\n\t\t\t\tfor(var j = 0; j < domStyle.parts.length; j++)\n\t\t\t\t\tdomStyle.parts[j]();\n\t\t\t\tdelete stylesInDom[domStyle.id];\n\t\t\t}\n\t\t}\n\t};\n}\n\nfunction addStylesToDom(styles, options) {\n\tfor(var i = 0; i < styles.length; i++) {\n\t\tvar item = styles[i];\n\t\tvar domStyle = stylesInDom[item.id];\n\t\tif(domStyle) {\n\t\t\tdomStyle.refs++;\n\t\t\tfor(var j = 0; j < domStyle.parts.length; j++) {\n\t\t\t\tdomStyle.parts[j](item.parts[j]);\n\t\t\t}\n\t\t\tfor(; j < item.parts.length; j++) {\n\t\t\t\tdomStyle.parts.push(addStyle(item.parts[j], options));\n\t\t\t}\n\t\t} else {\n\t\t\tvar parts = [];\n\t\t\tfor(var j = 0; j < item.parts.length; j++) {\n\t\t\t\tparts.push(addStyle(item.parts[j], options));\n\t\t\t}\n\t\t\tstylesInDom[item.id] = {id: item.id, refs: 1, parts: parts};\n\t\t}\n\t}\n}\n\nfunction listToStyles(list) {\n\tvar styles = [];\n\tvar newStyles = {};\n\tfor(var i = 0; i < list.length; i++) {\n\t\tvar item = list[i];\n\t\tvar id = item[0];\n\t\tvar css = item[1];\n\t\tvar media = item[2];\n\t\tvar sourceMap = item[3];\n\t\tvar part = {css: css, media: media, sourceMap: sourceMap};\n\t\tif(!newStyles[id])\n\t\t\tstyles.push(newStyles[id] = {id: id, parts: [part]});\n\t\telse\n\t\t\tnewStyles[id].parts.push(part);\n\t}\n\treturn styles;\n}\n\nfunction insertStyleElement(options, styleElement) {\n\tvar head = getHeadElement();\n\tvar lastStyleElementInsertedAtTop = styleElementsInsertedAtTop[styleElementsInsertedAtTop.length - 1];\n\tif (options.insertAt === \"top\") {\n\t\tif(!lastStyleElementInsertedAtTop) {\n\t\t\thead.insertBefore(styleElement, head.firstChild);\n\t\t} else if(lastStyleElementInsertedAtTop.nextSibling) {\n\t\t\thead.insertBefore(styleElement, lastStyleElementInsertedAtTop.nextSibling);\n\t\t} else {\n\t\t\thead.appendChild(styleElement);\n\t\t}\n\t\tstyleElementsInsertedAtTop.push(styleElement);\n\t} else if (options.insertAt === \"bottom\") {\n\t\thead.appendChild(styleElement);\n\t} else {\n\t\tthrow new Error(\"Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.\");\n\t}\n}\n\nfunction removeStyleElement(styleElement) {\n\tstyleElement.parentNode.removeChild(styleElement);\n\tvar idx = styleElementsInsertedAtTop.indexOf(styleElement);\n\tif(idx >= 0) {\n\t\tstyleElementsInsertedAtTop.splice(idx, 1);\n\t}\n}\n\nfunction createStyleElement(options) {\n\tvar styleElement = document.createElement(\"style\");\n\tstyleElement.type = \"text/css\";\n\tinsertStyleElement(options, styleElement);\n\treturn styleElement;\n}\n\nfunction createLinkElement(options) {\n\tvar linkElement = document.createElement(\"link\");\n\tlinkElement.rel = \"stylesheet\";\n\tinsertStyleElement(options, linkElement);\n\treturn linkElement;\n}\n\nfunction addStyle(obj, options) {\n\tvar styleElement, update, remove;\n\n\tif (options.singleton) {\n\t\tvar styleIndex = singletonCounter++;\n\t\tstyleElement = singletonElement || (singletonElement = createStyleElement(options));\n\t\tupdate = applyToSingletonTag.bind(null, styleElement, styleIndex, false);\n\t\tremove = applyToSingletonTag.bind(null, styleElement, styleIndex, true);\n\t} else if(obj.sourceMap &&\n\t\ttypeof URL === \"function\" &&\n\t\ttypeof URL.createObjectURL === \"function\" &&\n\t\ttypeof URL.revokeObjectURL === \"function\" &&\n\t\ttypeof Blob === \"function\" &&\n\t\ttypeof btoa === \"function\") {\n\t\tstyleElement = createLinkElement(options);\n\t\tupdate = updateLink.bind(null, styleElement);\n\t\tremove = function() {\n\t\t\tremoveStyleElement(styleElement);\n\t\t\tif(styleElement.href)\n\t\t\t\tURL.revokeObjectURL(styleElement.href);\n\t\t};\n\t} else {\n\t\tstyleElement = createStyleElement(options);\n\t\tupdate = applyToTag.bind(null, styleElement);\n\t\tremove = function() {\n\t\t\tremoveStyleElement(styleElement);\n\t\t};\n\t}\n\n\tupdate(obj);\n\n\treturn function updateStyle(newObj) {\n\t\tif(newObj) {\n\t\t\tif(newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap)\n\t\t\t\treturn;\n\t\t\tupdate(obj = newObj);\n\t\t} else {\n\t\t\tremove();\n\t\t}\n\t};\n}\n\nvar replaceText = (function () {\n\tvar textStore = [];\n\n\treturn function (index, replacement) {\n\t\ttextStore[index] = replacement;\n\t\treturn textStore.filter(Boolean).join('\\n');\n\t};\n})();\n\nfunction applyToSingletonTag(styleElement, index, remove, obj) {\n\tvar css = remove ? \"\" : obj.css;\n\n\tif (styleElement.styleSheet) {\n\t\tstyleElement.styleSheet.cssText = replaceText(index, css);\n\t} else {\n\t\tvar cssNode = document.createTextNode(css);\n\t\tvar childNodes = styleElement.childNodes;\n\t\tif (childNodes[index]) styleElement.removeChild(childNodes[index]);\n\t\tif (childNodes.length) {\n\t\t\tstyleElement.insertBefore(cssNode, childNodes[index]);\n\t\t} else {\n\t\t\tstyleElement.appendChild(cssNode);\n\t\t}\n\t}\n}\n\nfunction applyToTag(styleElement, obj) {\n\tvar css = obj.css;\n\tvar media = obj.media;\n\n\tif(media) {\n\t\tstyleElement.setAttribute(\"media\", media)\n\t}\n\n\tif(styleElement.styleSheet) {\n\t\tstyleElement.styleSheet.cssText = css;\n\t} else {\n\t\twhile(styleElement.firstChild) {\n\t\t\tstyleElement.removeChild(styleElement.firstChild);\n\t\t}\n\t\tstyleElement.appendChild(document.createTextNode(css));\n\t}\n}\n\nfunction updateLink(linkElement, obj) {\n\tvar css = obj.css;\n\tvar sourceMap = obj.sourceMap;\n\n\tif(sourceMap) {\n\t\t// http://stackoverflow.com/a/26603875\n\t\tcss += \"\\n/*# sourceMappingURL=data:application/json;base64,\" + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + \" */\";\n\t}\n\n\tvar blob = new Blob([css], { type: \"text/css\" });\n\n\tvar oldSrc = linkElement.href;\n\n\tlinkElement.href = URL.createObjectURL(blob);\n\n\tif(oldSrc)\n\t\tURL.revokeObjectURL(oldSrc);\n}\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/style-loader/addStyles.js\n ** module id = 4\n ** module chunks = 0\n **/","/* global Snap */\n/*\n\tClass: Bar\n\n\tOpts:\n\t\tgt: Gantt object\n\t\ttask: task object\n*/\n\nexport default function Bar(gt, task) {\n\n\tconst self = {};\n\n\tfunction init() {\n\t\tset_defaults();\n\t\tprepare();\n\t\tdraw();\n\t\tbind();\n\t}\n\n\tfunction set_defaults() {\n\t\tself.action_completed = false;\n\t\tself.task = task;\n\t}\n\n\tfunction prepare() {\n\t\tprepare_values();\n\t\tprepare_plugins();\n\t}\n\n\tfunction prepare_values() {\n\t\tself.invalid = self.task.invalid;\n\t\tself.height = gt.config.bar.height;\n\t\tself.x = compute_x();\n\t\tself.y = compute_y();\n\t\tself.corner_radius = gt.config.bar.corner_radius;\n\t\tself.duration = (self.task._end.diff(self.task._start, 'hours') + 24) / gt.config.step;\n\t\tself.width = gt.config.column_width * self.duration;\n\t\tself.progress_width = gt.config.column_width * self.duration * (self.task.progress / 100) || 0;\n\t\tself.group = gt.canvas.group().addClass('bar-wrapper').addClass(self.task.custom_class || '');\n\t\tself.bar_group = gt.canvas.group().addClass('bar-group').appendTo(self.group);\n\t\tself.handle_group = gt.canvas.group().addClass('handle-group').appendTo(self.group);\n\t\tself.is_resizable = gt.config.bar.resize;\n\t\tself.has_progress = gt.config.bar.progress;\n\t\tself.is_draggable = gt.config.bar.drag;\n\t}\n\n\tfunction prepare_plugins() {\n\t\tSnap.plugin(function (Snap, Element, Paper, global, Fragment) {\n\t\t\tElement.prototype.getX = function () {\n\t\t\t\treturn +this.attr('x');\n\t\t\t};\n\t\t\tElement.prototype.getY = function () {\n\t\t\t\treturn +this.attr('y');\n\t\t\t};\n\t\t\tElement.prototype.getWidth = function () {\n\t\t\t\treturn +this.attr('width');\n\t\t\t};\n\t\t\tElement.prototype.getHeight = function () {\n\t\t\t\treturn +this.attr('height');\n\t\t\t};\n\t\t\tElement.prototype.getEndX = function () {\n\t\t\t\treturn this.getX() + this.getWidth();\n\t\t\t};\n\t\t});\n\t}\n\n\tfunction draw() {\n\t\tdraw_bar();\n\t\tif (self.has_progress) {\n\t\t\tdraw_progress_bar();\n\t\t\tdraw_progress_handle();\n\t\t}\n\t\tdraw_label();\n\t\tif (self.is_resizable) { draw_resize_handles(); }\n\t}\n\n\tfunction draw_bar() {\n\t\tself.$bar = gt.canvas.rect(self.x, self.y,\n\t\t\tself.width, self.height,\n\t\t\tself.corner_radius, self.corner_radius)\n\t\t\t.addClass('bar')\n\t\t\t.appendTo(self.bar_group);\n\t\tif (self.invalid) {\n\t\t\tself.$bar.addClass('bar-invalid');\n\t\t}\n\t}\n\n\tfunction draw_progress_bar() {\n\t\tif (self.invalid) return;\n\t\tself.$bar_progress = gt.canvas.rect(self.x, self.y,\n\t\t\tself.progress_width, self.height,\n\t\t\tself.corner_radius, self.corner_radius)\n\t\t\t.addClass('bar-progress')\n\t\t\t.appendTo(self.bar_group);\n\t}\n\n\tfunction draw_label() {\n\t\tgt.canvas.text(self.x + self.width / 2,\n\t\t\tself.y + self.height / 2,\n\t\t\tself.task.name)\n\t\t\t.addClass('bar-label')\n\t\t\t.appendTo(self.bar_group);\n\t\tupdate_label_position();\n\t}\n\n\tfunction draw_resize_handles() {\n\t\tif (self.invalid) return;\n\n\t\tconst bar = self.$bar,\n\t\t\thandle_width = 8;\n\n\t\tgt.canvas.rect(bar.getX() + bar.getWidth() - 9, bar.getY() + 1,\n\t\t\thandle_width, self.height - 2, self.corner_radius, self.corner_radius)\n\t\t\t.addClass('handle right')\n\t\t\t.appendTo(self.handle_group);\n\t\tgt.canvas.rect(bar.getX() + 1, bar.getY() + 1,\n\t\t\thandle_width, self.height - 2, self.corner_radius, self.corner_radius)\n\t\t\t.addClass('handle left')\n\t\t\t.appendTo(self.handle_group);\n\t}\n\n\tfunction draw_progress_handle() {\n\t\tif (self.invalid) return;\n\n\t\tif (self.task.progress && self.task.progress < 100) {\n\t\t\tgt.canvas.polygon(get_progress_polygon_points())\n\t\t\t\t.addClass('handle progress')\n\t\t\t\t.appendTo(self.handle_group);\n\t\t}\n\t}\n\n\tfunction get_progress_polygon_points() {\n\t\tconst bar_progress = self.$bar_progress;\n\t\treturn [\n\t\t\tbar_progress.getEndX() - 5, bar_progress.getY() + bar_progress.getHeight(),\n\t\t\tbar_progress.getEndX() + 5, bar_progress.getY() + bar_progress.getHeight(),\n\t\t\tbar_progress.getEndX(), bar_progress.getY() + bar_progress.getHeight() - 8.66\n\t\t];\n\t}\n\n\tfunction bind() {\n\t\tif (self.invalid) return;\n\t\tsetup_click_event();\n\t\tshow_details();\n\t\tif (self.is_resizable) { bind_resize(); }\n\t\tif (self.has_progress) { bind_resize_progress(); }\n\t\tif (self.is_draggable) { bind_drag(); }\n\t}\n\n\tfunction show_details() {\n\t\tconst popover_group = gt.element_groups.details;\n\t\tself.details_box = popover_group\n\t\t\t.select(`.details-wrapper[data-task='${self.task.id}']`);\n\n\t\tif (!self.details_box) {\n\t\t\tself.details_box = gt.canvas.group()\n\t\t\t\t.addClass('details-wrapper hide')\n\t\t\t\t.attr('data-task', self.task.id)\n\t\t\t\t.appendTo(popover_group);\n\n\t\t\trender_details();\n\n\t\t\tconst f = gt.canvas.filter(\n\t\t\t\tSnap.filter.shadow(0, 1, 1, '#666', 0.6));\n\t\t\tself.details_box.attr({\n\t\t\t\tfilter: f\n\t\t\t});\n\t\t}\n\n\t\tself.group.click((e) => {\n\t\t\tif (self.action_completed) {\n\t\t\t\t// just finished a move action, wait for a few seconds\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tpopover_group.selectAll('.details-wrapper')\n\t\t\t\t.forEach(el => el.addClass('hide'));\n\t\t\tself.details_box.removeClass('hide');\n\t\t});\n\t}\n\n\tfunction render_details() {\n\t\tconst {x, y} = get_details_position();\n\t\tself.details_box.transform(`t${x},${y}`);\n\t\tself.details_box.clear();\n\n\t\tconst html = get_details_html();\n\t\tconst foreign_object =\n\t\t\tSnap.parse(`<foreignObject width=\"5000\" height=\"2000\">\n\t\t\t\t<body xmlns=\"http://www.w3.org/1999/xhtml\">\n\t\t\t\t\t${html}\n\t\t\t\t</body>\n\t\t\t\t</foreignObject>`);\n\t\tself.details_box.append(foreign_object);\n\t}\n\n\tfunction get_details_html() {\n\n\t\t// custom html in config\n\t\tif(gt.config.custom_popup_html) {\n\t\t\tconst html = gt.config.custom_popup_html;\n\t\t\tif(typeof html === 'string') {\n\t\t\t\treturn html;\n\t\t\t}\n\t\t\tif(isFunction(html)) {\n\t\t\t\treturn html(task);\n\t\t\t}\n\t\t}\n\n\t\tconst start_date = self.task._start.format('MMM D');\n\t\tconst end_date = self.task._end.format('MMM D');\n\t\tconst heading = `${self.task.name}: ${start_date} - ${end_date}`;\n\n\t\tconst line_1 = `Duration: ${self.duration} days`;\n\t\tconst line_2 = self.task.progress ? `Progress: ${self.task.progress}` : null;\n\n\t\tconst html = `\n\t\t\t<div class=\"details-container\">\n\t\t\t\t<h5>${heading}</h5>\n\t\t\t\t<p>${line_1}</p>\n\t\t\t\t${\n\t\t\t\t\tline_2 ? `<p>${line_2}</p>` : ''\n\t\t\t\t}\n\t\t\t</div>\n\t\t`;\n\t\treturn html;\n\t}\n\n\tfunction get_details_position() {\n\t\treturn {\n\t\t\tx: self.$bar.getEndX() + 2,\n\t\t\ty: self.$bar.getY() - 10\n\t\t};\n\t}\n\n\tfunction bind_resize() {\n\t\tconst { left, right } = get_handles();\n\n\t\tleft.drag(onmove_left, onstart, onstop_left);\n\t\tright.drag(onmove_right, onstart, onstop_right);\n\n\t\tfunction onmove_right(dx, dy) {\n\t\t\tonmove_handle_right(dx, dy);\n\t\t}\n\t\tfunction onstop_right() {\n\t\t\tonstop_handle_right();\n\t\t}\n\n\t\tfunction onmove_left(dx, dy) {\n\t\t\tonmove_handle_left(dx, dy);\n\t\t}\n\t\tfunction onstop_left() {\n\t\t\tonstop_handle_left();\n\t\t}\n\t}\n\n\tfunction get_handles() {\n\t\treturn {\n\t\t\tleft: self.handle_group.select('.handle.left'),\n\t\t\tright: self.handle_group.select('.handle.right')\n\t\t};\n\t}\n\n\tfunction bind_drag() {\n\t\tself.bar_group.drag(onmove, onstart, onstop);\n\t}\n\n\tfunction bind_resize_progress() {\n\t\tconst bar = self.$bar,\n\t\t\tbar_progress = self.$bar_progress,\n\t\t\thandle = self.group.select('.handle.progress');\n\t\thandle && handle.drag(on_move, on_start, on_stop);\n\n\t\tfunction on_move(dx, dy) {\n\t\t\tif (dx > bar_progress.max_dx) {\n\t\t\t\tdx = bar_progress.max_dx;\n\t\t\t}\n\t\t\tif (dx < bar_progress.min_dx) {\n\t\t\t\tdx = bar_progress.min_dx;\n\t\t\t}\n\n\t\t\tbar_progress.attr('width', bar_progress.owidth + dx);\n\t\t\thandle.attr('points', get_progress_polygon_points());\n\t\t\tbar_progress.finaldx = dx;\n\t\t}\n\t\tfunction on_stop() {\n\t\t\tif (!bar_progress.finaldx) return;\n\t\t\tprogress_changed();\n\t\t\tset_action_completed();\n\t\t}\n\t\tfunction on_start() {\n\t\t\tbar_progress.finaldx = 0;\n\t\t\tbar_progress.owidth = bar_progress.getWidth();\n\t\t\tbar_progress.min_dx = -bar_progress.getWidth();\n\t\t\tbar_progress.max_dx = bar.getWidth() - bar_progress.getWidth();\n\t\t}\n\t}\n\n\tfunction onstart() {\n\t\tconst bar = self.$bar;\n\t\tbar.ox = bar.getX();\n\t\tbar.oy = bar.getY();\n\t\tbar.owidth = bar.getWidth();\n\t\tbar.finaldx = 0;\n\t\trun_method_for_dependencies('onstart');\n\t}\n\tself.onstart = onstart;\n\n\tfunction onmove(dx, dy) {\n\t\tconst bar = self.$bar;\n\t\tbar.finaldx = get_snap_position(dx);\n\t\tupdate_bar_position({x: bar.ox + bar.finaldx});\n\t\trun_method_for_dependencies('onmove', [dx, dy]);\n\t}\n\tself.onmove = onmove;\n\n\tfunction onstop() {\n\t\tconst bar = self.$bar;\n\t\tif (!bar.finaldx) return;\n\t\tdate_changed();\n\t\tset_action_completed();\n\t\trun_method_for_dependencies('onstop');\n\t}\n\tself.onstop = onstop;\n\n\tfunction onmove_handle_left(dx, dy) {\n\t\tconst bar = self.$bar;\n\t\tbar.finaldx = get_snap_position(dx);\n\t\tupdate_bar_position({\n\t\t\tx: bar.ox + bar.finaldx,\n\t\t\twidth: bar.owidth - bar.finaldx\n\t\t});\n\t\trun_method_for_dependencies('onmove', [dx, dy]);\n\t}\n\tself.onmove_handle_left = onmove_handle_left;\n\n\tfunction onstop_handle_left() {\n\t\tconst bar = self.$bar;\n\t\tif (bar.finaldx) date_changed();\n\t\tset_action_completed();\n\t\trun_method_for_dependencies('onstop');\n\t}\n\tself.onstop_handle_left = onstop_handle_left;\n\n\tfunction run_method_for_dependencies(fn, args) {\n\t\tconst dm = gt.dependency_map;\n\t\tif (dm[self.task.id]) {\n\t\t\tfor (let deptask of dm[self.task.id]) {\n\t\t\t\tconst dt = gt.get_bar(deptask);\n\t\t\t\tdt[fn].apply(dt, args);\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction onmove_handle_right(dx, dy) {\n\t\tconst bar = self.$bar;\n\t\tbar.finaldx = get_snap_position(dx);\n\t\tupdate_bar_position({width: bar.owidth + bar.finaldx});\n\t}\n\n\tfunction onstop_handle_right() {\n\t\tconst bar = self.$bar;\n\t\tif (bar.finaldx) date_changed();\n\t\tset_action_completed();\n\t}\n\n\tfunction update_bar_position({x = null, width = null}) {\n\t\tconst bar = self.$bar;\n\t\tif (x) {\n\t\t\t// get all x values of parent task\n\t\t\tconst xs = task.dependencies.map(dep => {\n\t\t\t\treturn gt.get_bar(dep).$bar.getX();\n\t\t\t});\n\t\t\t// child task must not go before parent\n\t\t\tconst valid_x = xs.reduce((prev, curr) => {\n\t\t\t\treturn x >= curr;\n\t\t\t}, x);\n\t\t\tif(!valid_x) {\n\t\t\t\twidth = null;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tupdate_attr(bar, 'x', x);\n\t\t}\n\t\tif (width && width >= gt.config.column_width) {\n\t\t\tupdate_attr(bar, 'width', width);\n\t\t}\n\n\t\tupdate_label_position();\n\t\tif (self.is_resizable) { update_resize_handle_position(); }\n\t\tif (self.has_progress) {\n\t\t\tupdate_progress_handle_position();\n\t\t\tupdate_progressbar_position();\n\t\t}\n\t\tupdate_arrow_position();\n\t\tupdate_details_position();\n\t}\n\n\tfunction setup_click_event() {\n\t\tself.group.click(function () {\n\t\t\tif (self.action_completed) {\n\t\t\t\t// just finished a move action, wait for a few seconds\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tgt.trigger_event('click', [self.task]);\n\t\t\tgt.unselect_all();\n\t\t\tself.group.toggleClass('active');\n\t\t});\n\t}\n\n\tfunction date_changed() {\n\t\tconst { new_start_date, new_end_date } = compute_start_end_date();\n\t\tself.task._start = new_start_date;\n\t\tself.task._end = new_end_date;\n\t\trender_details();\n\t\tgt.trigger_event('date_change',\n\t\t\t[self.task, new_start_date, new_end_date]);\n\t}\n\n\tfunction progress_changed() {\n\t\tconst new_progress = compute_progress();\n\t\tself.task.progress = new_progress;\n\t\trender_details();\n\t\tgt.trigger_event('progress_change',\n\t\t\t[self.task, new_progress]);\n\t}\n\n\tfunction set_action_completed() {\n\t\tself.action_completed = true;\n\t\tsetTimeout(() => self.action_completed = false, 2000);\n\t}\n\n\tfunction compute_start_end_date() {\n\t\tconst bar = self.$bar;\n\t\tconst x_in_units = bar.getX() / gt.config.column_width;\n\t\tconst new_start_date = gt.gantt_start.clone().add(x_in_units * gt.config.step, 'hours');\n\t\tconst width_in_units = bar.getWidth() / gt.config.column_width;\n\t\tconst new_end_date = new_start_date.clone().add(width_in_units * gt.config.step, 'hours');\n\t\t// lets say duration is 2 days\n\t\t// start_date = May 24 00:00:00\n\t\t// end_date = May 24 + 2 days = May 26 (incorrect)\n\t\t// so subtract 1 second so that\n\t\t// end_date = May 25 23:59:59\n\t\tnew_end_date.add('-1', 'seconds');\n\t\treturn { new_start_date, new_end_date };\n\t}\n\n\tfunction compute_progress() {\n\t\tconst progress = self.$bar_progress.getWidth() / self.$bar.getWidth() * 100;\n\t\treturn parseInt(progress, 10);\n\t}\n\n\tfunction compute_x() {\n\t\tlet x = self.task._start.diff(gt.gantt_start, 'hours') /\n\t\t\tgt.config.step * gt.config.column_width;\n\n\t\tif (gt.view_is('Month')) {\n\t\t\tx = self.task._start.diff(gt.gantt_start, 'days') *\n\t\t\t\tgt.config.column_width / 30;\n\t\t}\n\t\treturn x;\n\t}\n\n\tfunction compute_y() {\n\t\treturn gt.config.header_height + gt.config.padding +\n\t\t\tself.task._index * (self.height + gt.config.padding);\n\t}\n\n\tfunction get_snap_position(dx) {\n\t\tlet odx = dx, rem, position;\n\n\t\tif (gt.view_is('Week')) {\n\t\t\trem = dx % (gt.config.column_width / 7);\n\t\t\tposition = odx - rem +\n\t\t\t\t((rem < gt.config.column_width / 14) ? 0 : gt.config.column_width / 7);\n\t\t} else if (gt.view_is('Month')) {\n\t\t\trem = dx % (gt.config.column_width / 30);\n\t\t\tposition = odx - rem +\n\t\t\t\t((rem < gt.config.column_width / 60) ? 0 : gt.config.column_width / 30);\n\t\t} else {\n\t\t\trem = dx % gt.config.column_width;\n\t\t\tposition = odx - rem +\n\t\t\t\t((rem < gt.config.column_width / 2) ? 0 : gt.config.column_width);\n\t\t}\n\t\treturn position;\n\t}\n\n\tfunction update_attr(element, attr, value) {\n\t\tvalue = +value;\n\t\tif (!isNaN(value)) {\n\t\t\telement.attr(attr, value);\n\t\t}\n\t\treturn element;\n\t}\n\n\tfunction update_progressbar_position() {\n\t\tself.$bar_progress.attr('x', self.$bar.getX());\n\t\tself.$bar_progress.attr('width', self.$bar.getWidth() * (self.task.progress / 100));\n\t}\n\n\tfunction update_label_position() {\n\t\tconst bar = self.$bar,\n\t\t\tlabel = self.group.select('.bar-label');\n\t\tif (label.getBBox().width > bar.getWidth()) {\n\t\t\tlabel.addClass('big').attr('x', bar.getX() + bar.getWidth() + 5);\n\t\t} else {\n\t\t\tlabel.removeClass('big').attr('x', bar.getX() + bar.getWidth() / 2);\n\t\t}\n\t}\n\n\tfunction update_resize_handle_position() {\n\t\tconst bar = self.$bar;\n\t\tself.handle_group.select('.handle.left').attr({\n\t\t\t'x': bar.getX() + 1\n\t\t});\n\t\tself.handle_group.select('.handle.right').attr({\n\t\t\t'x': bar.getEndX() - 9\n\t\t});\n\t}\n\n\tfunction update_progress_handle_position() {\n\t\tconst handle = self.group.select('.handle.progress');\n\t\thandle && handle.attr('points', get_progress_polygon_points());\n\t}\n\n\tfunction update_arrow_position() {\n\t\tfor (let arrow of self.arrows) {\n\t\t\tarrow.update();\n\t\t}\n\t}\n\n\tfunction update_details_position() {\n\t\tconst {x, y} = get_details_position();\n\t\tself.details_box && self.details_box.transform(`t${x},${y}`);\n\t}\n\n\tfunction isFunction(functionToCheck) {\n\t\tvar getType = {};\n\t\treturn functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n\t}\n\n\tinit();\n\n\treturn self;\n}\n\n\n\n/** WEBPACK FOOTER **\n ** ./~/eslint-loader!./src/Bar.js\n **/","/* global Snap */\n/*\n\tClass: Arrow\n\tfrom_task ---> to_task\n\n\tOpts:\n\t\tgantt (Gantt object)\n\t\tfrom_task (Bar object)\n\t\tto_task (Bar object)\n*/\n\nexport default function Arrow(gt, from_task, to_task) {\n\n\tconst self = {};\n\n\tfunction init() {\n\t\tself.from_task = from_task;\n\t\tself.to_task = to_task;\n\t\tprepare();\n\t\tdraw();\n\t}\n\n\tfunction prepare() {\n\n\t\tself.start_x = from_task.$bar.getX() + from_task.$bar.getWidth() / 2;\n\n\t\tconst condition = () =>\n\t\t\tto_task.$bar.getX() < self.start_x + gt.config.padding &&\n\t\t\t\tself.start_x > from_task.$bar.getX() + gt.config.padding;\n\n\t\twhile(condition()) {\n\t\t\tself.start_x -= 10;\n\t\t}\n\n\t\tself.start_y = gt.config.header_height + gt.config.bar.height +\n\t\t\t(gt.config.padding + gt.config.bar.height) * from_task.task._index +\n\t\t\tgt.config.padding;\n\n\t\tself.end_x = to_task.$bar.getX() - gt.config.padding / 2;\n\t\tself.end_y = gt.config.header_height + gt.config.bar.height / 2 +\n\t\t\t(gt.config.padding + gt.config.bar.height) * to_task.task._index +\n\t\t\tgt.config.padding;\n\n\t\tconst from_is_below_to = (from_task.task._index > to_task.task._index);\n\t\tself.curve = gt.config.arrow.curve;\n\t\tself.clockwise = from_is_below_to ? 1 : 0;\n\t\tself.curve_y = from_is_below_to ? -self.curve : self.curve;\n\t\tself.offset = from_is_below_to ?\n\t\t\tself.end_y + gt.config.arrow.curve :\n\t\t\tself.end_y - gt.config.arrow.curve;\n\n\t\tself.path =\n\t\t\tSnap.format('M {start_x} {start_y} V {offset} ' +\n\t\t\t\t'a {curve} {curve} 0 0 {clockwise} {curve} {curve_y} ' +\n\t\t\t\t'L {end_x} {end_y} m -5 -5 l 5 5 l -5 5',\n\t\t\t\t{\n\t\t\t\t\tstart_x: self.start_x,\n\t\t\t\t\tstart_y: self.start_y,\n\t\t\t\t\tend_x: self.end_x,\n\t\t\t\t\tend_y: self.end_y,\n\t\t\t\t\toffset: self.offset,\n\t\t\t\t\tcurve: self.curve,\n\t\t\t\t\tclockwise: self.clockwise,\n\t\t\t\t\tcurve_y: self.curve_y\n\t\t\t\t});\n\n\t\tif(to_task.$bar.getX() < from_task.$bar.getX() + gt.config.padding) {\n\t\t\tself.path =\n\t\t\t\tSnap.format('M {start_x} {start_y} v {down_1} ' +\n\t\t\t\t'a {curve} {curve} 0 0 1 -{curve} {curve} H {left} ' +\n\t\t\t\t'a {curve} {curve} 0 0 {clockwise} -{curve} {curve_y} V {down_2} ' +\n\t\t\t\t'a {curve} {curve} 0 0 {clockwise} {curve} {curve_y} ' +\n\t\t\t\t'L {end_x} {end_y} m -5 -5 l 5 5 l -5 5',\n\t\t\t\t\t{\n\t\t\t\t\t\tstart_x: self.start_x,\n\t\t\t\t\t\tstart_y: self.start_y,\n\t\t\t\t\t\tend_x: self.end_x,\n\t\t\t\t\t\tend_y: self.end_y,\n\t\t\t\t\t\tdown_1: gt.config.padding / 2 - self.curve,\n\t\t\t\t\t\tdown_2: to_task.$bar.getY() + to_task.$bar.getHeight() / 2 - self.curve_y,\n\t\t\t\t\t\tleft: to_task.$bar.getX() - gt.config.padding,\n\t\t\t\t\t\toffset: self.offset,\n\t\t\t\t\t\tcurve: self.curve,\n\t\t\t\t\t\tclockwise: self.clockwise,\n\t\t\t\t\t\tcurve_y: self.curve_y\n\t\t\t\t\t});\n\t\t}\n\t}\n\n\tfunction draw() {\n\t\tself.element = gt.canvas.path(self.path)\n\t\t\t.attr('data-from', self.from_task.task.id)\n\t\t\t.attr('data-to', self.to_task.task.id);\n\t}\n\n\tfunction update() { // eslint-disable-line\n\t\tprepare();\n\t\tself.element.attr('d', self.path);\n\t}\n\tself.update = update;\n\n\tinit();\n\n\treturn self;\n}\n\n\n\n/** WEBPACK FOOTER **\n ** ./~/eslint-loader!./src/Arrow.js\n **/","(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(global.deepmerge = factory());\n}(this, (function () { 'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, optionsArgument) {\n\tvar clone = !optionsArgument || optionsArgument.clone !== false;\n\n\treturn (clone && isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, optionsArgument)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, optionsArgument) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, optionsArgument)\n\t})\n}\n\nfunction mergeObject(target, source, optionsArgument) {\n\tvar destination = {};\n\tif (isMergeableObject(target)) {\n\t\tObject.keys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], optionsArgument);\n\t\t});\n\t}\n\tObject.keys(source).forEach(function(key) {\n\t\tif (!isMergeableObject(source[key]) || !target[key]) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], optionsArgument);\n\t\t} else {\n\t\t\tdestination[key] = deepmerge(target[key], source[key], optionsArgument);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, optionsArgument) {\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar options = optionsArgument || { arrayMerge: defaultArrayMerge };\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, optionsArgument)\n\t} else if (sourceIsArray) {\n\t\tvar arrayMerge = options.arrayMerge || defaultArrayMerge;\n\t\treturn arrayMerge(target, source, optionsArgument)\n\t} else {\n\t\treturn mergeObject(target, source, optionsArgument)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, optionsArgument) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, optionsArgument)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nreturn deepmerge_1;\n\n})));\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./~/deepmerge/dist/umd.js\n ** module id = 7\n ** module chunks = 0\n **/"],"sourceRoot":""}
>>>>>>> bc61601... Fix bug causing TypeError when config parameter is undefined

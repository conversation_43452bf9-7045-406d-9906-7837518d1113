<?php

$lang['free_data'] = 'Free Data';

// Permissions
$lang['free_data_permission_access'] = 'Access';
$lang['free_data_permission_view_own'] = 'View (Own)';
$lang['free_data_permission_view_global'] = 'View (Global)';
$lang['free_data_permission_approve_own'] = 'Approve request (Own)';
$lang['free_data_permission_approve_global'] = 'Approve request (Global)';
$lang['free_data_permission_create'] = 'Create request';
$lang['free_data_permission_edit'] = 'Edit request';
$lang['free_data_permission_delete'] = 'Delete request';

// Menu Items
$lang['free_data_company_list'] = 'Company list';
$lang['free_data_my_request'] = 'My requests';
$lang['free_data_pending_request'] = 'Waiting approval requests';
$lang['free_data_all_request'] = 'All Requests';

// Company Grids
$lang['free_data_company_search_input_placeholder'] = 'Search for company (ID, Company name)';
$lang['free_data_company_status'] = 'Filter by status';
$lang['free_data_company_potential'] = 'Filter by potential rate';

// Grid filter company status options
$lang['free_data_company_status_free'] = 'Open';
$lang['free_data_company_status_requested'] = 'Is requested';
$lang['free_data_company_status_ovedued'] = 'Expired';

$lang['free_data_my_request_btn'] = 'My requests';

// Company Grids
$lang['free_data_company_col_id'] = 'ID';
$lang['free_data_company_col_company_name'] = 'Company Name';
$lang['free_data_company_col_created_at'] = 'Created at';
$lang['free_data_company_col_last_contact_note'] = 'Final contact note';
$lang['free_data_company_col_potential_rate'] = 'Potential';
$lang['free_data_company_col_request_status'] = 'Status';
$lang['free_data_company_col_action'] = 'Action';

// Company Grid request status
$lang['free_data_company_row_request_status_open'] = 'Open';
$lang['free_data_company_row_request_status_requested'] = '%s request';
$lang['free_data_company_row_requests_status_requested'] = '%s requests';

// Company Grid notes
$lang['free_data_company_row_note_type'] = '- Action: %s';
$lang['free_data_company_row_note_type_contacted'] = 'Contact success note';
$lang['free_data_company_row_note_type_contact_later'] = 'Contact again note';
$lang['free_data_company_row_note_action'] = '- Note: &ldquo;%s&rdquo;';

// Company Grid action
$lang['free_data_company_row_action_request'] = 'Request';
$lang['free_data_company_row_action_requested'] = 'Requested';
$lang['free_data_company_row_action_ovedued'] = 'Expired';


// My Request Grids
$lang['free_data_my_request_col_id'] = 'ID';
$lang['free_data_my_request_col_company_name'] = 'Company name';
$lang['free_data_my_request_col_created_at'] = 'Created at';
$lang['free_data_my_request_col_reason'] = 'Request creation reason';
$lang['free_data_my_request_col_customer_type'] = 'Customer type';
$lang['free_data_my_request_col_request_total'] = 'Quanlity of requests';
$lang['free_data_my_request_col_action'] = 'Status';

// My Request Grids data
$lang['free_data_my_request_row_link'] = 'Link attachment';
$lang['free_data_my_request_row_attachment'] = 'View attached file';

// My Request Grid
$lang['free_data_request_waiting_status'] = 'Waiting';
$lang['free_data_request_sa_approved_status'] = 'SA approved';
$lang['free_data_request_rejected_status'] = 'Rejected';
$lang['free_data_request_approved_status'] = 'Approved';

// My Request customer type
$lang['free_data_request_has_job_out_of_package'] = 'Currently/Previously recruiting on TopDev, service package expired';
$lang['free_data_request_has_package_not_use'] = 'There is an unused service package at TopDev';
$lang['free_data_request_has_job_in_market'] = 'Currently recruiting in the market';
$lang['free_data_request_not_buy_has_demand'] = 'Have never purchased ads on TopDev, but have a need to recruit IT professionals';
$lang['free_data_request_contacted_before'] = 'Previous contacted customer';
$lang['free_data_request_it_company'] = 'IT/Software company';
$lang['free_data_request_recruited_before'] = 'Used to use IT job posting service in the past';
$lang['free_data_request_recruit_demand'] = 'There is a need to recruit IT in the future';

// Create Form
$lang['free_data_create_request_form'] = 'Create Request';
$lang['free_data_create_request_content_field'] = 'Detailed information description';
$lang['free_data_create_request_content_placeholder_field'] = 'Please describe the reason for requesting customer sales care from FreeDB to expedite and facilitate the approval process.
Format:
1. Reason:
2. Contact Point: Sales personnel in charge of contact/taking care - Email - Phone number';
$lang['free_data_create_request_customer_type_field'] = 'Classify Customer in Free Data';
$lang['free_data_create_request_link_field'] = 'Link (Optional)';
$lang['free_data_create_request_attachment_field'] = 'File attachment (Optional)';

// Create request success message
$lang['free_data_created_request_success'] = 'Created request successfully!';

// Exceptions message
$lang['free_data_exception_pool_not_found'] = 'This Company already assigned to another Sale, please request other company!';
$lang['free_data_exception_customer_is_ovedued'] = 'You do not have permisson to request this company';
$lang['free_data_exception_already_requested'] = 'You are already requested this company, please request other company!';


// Requests

// Requests Grids
$lang['free_data_request_col_request_id'] = 'ID Request';
$lang['free_data_request_col_id'] = 'ID';
$lang['free_data_request_col_company_name'] = 'Company name';
$lang['free_data_request_col_created_by'] = 'Requester';
$lang['free_data_request_col_created_at'] = 'Created At';
$lang['free_data_request_col_sa_approved_at'] = 'SA Approved At';
$lang['free_data_request_col_leader_approved_at'] = 'Leader Approved At';
$lang['free_data_request_col_reason'] = 'Request creation reason';
$lang['free_data_request_col_customer_type'] = 'Customer Type';
$lang['free_data_request_col_request_total'] = 'Quanlity of requests';
$lang['free_data_request_col_action'] = 'Status';
$lang['free_data_request_col_last_action'] = 'Final result';


// Request Grid - Last status
$lang['free_data_request_waiting_status'] = 'Waiting';
$lang['free_data_request_sa_approved_status'] = 'SA approved';
$lang['free_data_request_sa_rejected_status'] = 'SA rejects';
$lang['free_data_request_leader_approved_status'] = 'Approved';
$lang['free_data_request_leader_rejected_status'] = 'Leader rejects';


$lang['free_data_request_approve_confirm_message'] = "Are you sure you want to approve __created_by__\'s request?";
$lang['free_data_request_reject_confirm_message'] = "Are you sure you want to reject __created_by__\'s request?";

// Success message
$lang['free_data_request_approve_success_message'] = 'Approved request successfully!';
$lang['free_data_request_reject_success_message'] = 'Rejected request successfully!';
$lang['free_data_update_note_success_message'] = 'Update note successfully!';

// Validation exceptions
$lang['free_data_exception_request_not_found'] = 'This request is not approved by Sale Admin!';
$lang['free_data_exception_request_aproved_already'] = 'This Company already assigned to other sale member!';
$lang['free_data_exception_request_approve_member_only'] = 'You can approve request of team member only!';
$lang['free_data_exception_request_reject_member_only'] = 'You can reject request of team member only!';
$lang['free_data_exception_request_cant_edit_note'] = 'You cannot edit this note!';

$lang['free_data_exception_request_admin_not_found'] = 'This request already approved by other Sale Admin!';

// Notification
$lang['free_data_notification_for_leader_and_admin'] = 'sent 1 customer care request %s from Free Data.';
$lang['free_data_notification_for_sale_with_sale_admin_reject'] = 'Sale Admin has denied your request for customer care %s from Free Data.';
$lang['free_data_notification_for_sale_with_sale_admin_approve'] = 'Sale Admin has accepted your request for customer care %s from Free Data.';
$lang['free_data_notification_for_sale_with_sale_leader_reject'] = 'Leader has denied your request for customer care %s from Free Data.';
$lang['free_data_notification_for_sale_with_sale_leader_approve'] = 'Leader has accepted your request for customer care %s from Free Data.';

// Approval note
$lang['free_data_approval_note'] = 'Approval note';
$lang['free_data_edit_note'] = 'Edit note';
$lang['free_data_sa_note'] = 'SA note';
$lang['free_data_leader_note'] = 'Leader note';
<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller for free data module
 * 
 * @property Free_data_model $free_data_model
 */
class Free_data extends AdminController
{
    /**
     * List all company that in free data pool
     */
    public function index()
    {
        if (!free_data_can_view_company()) {
            access_denied(FREE_DATA_MODULE_NAME);
        }
        $data['title'] = _l('free_data_company_list');
        $this->load->view('free_data/companies/list', $data);
    }

    /**
     * List all company that in free data pool
     */
    public function table()
    {
        if (!free_data_can_view_company()) {
            ajax_access_denied();
        }

        // Run logic check expired data after 3 days
        $this->load->model('free_data_model');
        $this->free_data_model->remove_approved_request_doesnt_have_contact_after_3_days();

        // Load the table
        $this->app->get_table_data(module_views_path('free_data', 'companies/table'));
    }

    /**
     * List all company that in free data pool
     */
    public function requests($status = '')
    {
        if (!free_data_can_view_members() && !free_data_can_view_all()) {
            access_denied(FREE_DATA_MODULE_NAME);
        }
        $data['title'] = $status == FREE_DATA_REQUEST_STATUS_WAITING ? _l('free_data_pending_request') : _l('free_data_all_request');
        $data['status'] = $status;
        $this->load->view('free_data/requests/list', $data);
    }

    /**
     * List all company that in free data pool
     */
    public function all_request_table()
    {
        if (!free_data_can_view_members() && !free_data_can_view_all()) {
            ajax_access_denied();
        }
        $this->app->get_table_data(module_views_path('free_data', 'requests/table'));
    }

    /**
     * List all sale's requests
     */
    public function my_request()
    {
        if (!free_data_can_view_own()) {
            access_denied(FREE_DATA_MODULE_NAME);
        }
        $data['title'] = _l('free_data_pending_request');
        $this->load->view('free_data/my_requests/list', $data);
    }

    /**
     * List all company that in free data pool
     */
    public function my_request_table()
    {
        if (!free_data_can_view_own()) {
            ajax_access_denied();
        }
        $this->app->get_table_data(module_views_path('free_data', 'my_requests/table'));
    }

    /**
     * Sale create a request
     */
    public function create_request($poolId)
    {
        if (!free_data_can_create()) {
            access_denied(FREE_DATA_MODULE_NAME);
        }
        $postData = $this->input->post();
        $data = [
            'title' => _l('free_data_create_request_form'),
            'data' => $postData
        ];
        $this->load->model('free_data_model');

        // Check staff able to make request
        try {
            $this->free_data_model->can_make_request($poolId);
            if ($postData) {
                $insertedId = $this->free_data_model->create($poolId, $postData);
                if ($insertedId) {
                    $attachedFileUUID = free_data_handle_request_attachments(get_staff_user_id(), $insertedId);
                    if ($attachedFileUUID) {
                        $this->free_data_model->store_attachment($insertedId, $attachedFileUUID);
                    }

                    // Get staff admin and send notification for all admin staff
                    $this->free_data_model->notify_for_adminSales_free_data($poolId);
                }
                free_data_go_to_page(_l('free_data_created_request_success'), admin_url('free_data/my_request'), 'success');
            }
        } catch (Exception $ex) {
            free_data_go_to_page($ex->getMessage(), admin_url('free_data'));
        }

        $this->load->view('free_data/my_requests/create', $data);
    }

    /**
     * Edit note
     */
    public function edit_note($requestId)
    {
        if (is_admin() || is_sales_admin() || free_data_is_sale_leader_role()) {
            try {
                $this->load->model('free_data_model');
                $this->free_data_model->can_edit_request_note($requestId);
                $message = $this->free_data_model->edit_note($requestId, $this->input->post('note'));

                free_data_ajax_success($message);
            } catch (Exception $ex) {
                free_data_ajax_error_validation($ex->getMessage());
            }
        } else {
            ajax_access_denied();
        }
    }

    /**
     * Sale leader/admin approve request
     * @param int $requestId
     * @return json
     */
    public function approve_request($requestId)
    {
        if (!free_data_can_approve_members() && !free_data_can_approve_all()) {
            ajax_access_denied();
        }

        $this->load->model('free_data_model');
        try {
            $message = '';
            if (free_data_is_sale_leader_role()) {
                $request = $this->free_data_model->leader_can_approve_request($requestId);
                $message = $this->free_data_model->process_leader_approve($requestId, $request);
            } else {
                $request = $this->free_data_model->admin_can_approve_request($requestId);
                $message = $this->free_data_model->process_admin_approve($requestId, $request);
            }

            free_data_ajax_success($message);
        } catch(Exception $ex) {
            free_data_ajax_error_validation($ex->getMessage());
        }

    }

    /**
     * Sale leader/admin reject request
     * @param $requestId
     * @return json
     */
    public function reject_request($requestId)
    {
        if (!free_data_can_approve_members() && !free_data_can_approve_all()) {
            ajax_access_denied();
        }

        $this->load->model('free_data_model');
        try {
            $message = '';
            if (free_data_is_sale_leader_role()) {
                $this->free_data_model->leader_can_reject_request($requestId);
                $message = $this->free_data_model->process_reject_request($requestId, FREE_DATA_REQUEST_STATUS_LEADER_REJECTED);
            } else {
                $this->free_data_model->admin_can_approve_request($requestId);
                $message = $this->free_data_model->process_reject_request($requestId);
            }

            // Notifications for staff members when rejected
            if ($message) {
                $this->free_data_model->notify_for_sales_when_request($requestId);
            }

            free_data_ajax_success($message);
        } catch(Exception $ex) {
            free_data_ajax_error_validation($ex->getMessage());
        }
    }
}
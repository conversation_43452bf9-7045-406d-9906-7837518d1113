<?php

defined('BASEPATH') or exit('No direct script access allowed');

function free_data_handle_request_attachments($staffId, $requestId, $index_name = 'attachment')
{
    $path           = FREE_DATA_REQUEST_ATTACHMENTS_FOLDER . $staffId . '/';
    $CI             = &get_instance();
    $CI->load->model('misc_model');

    if (isset($_FILES[$index_name]['name']) && $_FILES[$index_name]['name'] != '') {

        // Get the temp file path
        $tmpFilePath = $_FILES[$index_name]['tmp_name'];

        // Make sure we have a filepath
        if (!empty($tmpFilePath) && $tmpFilePath != '') {
            if (_perfex_upload_error($_FILES[$index_name]['error'])
                || !_upload_extension_allowed($_FILES[$index_name]['name'])) {
                return false;
            }

            _maybe_create_upload_path(FREE_DATA_REQUEST_ATTACHMENTS_FOLDER);
            _maybe_create_upload_path($path);
            $filename    = unique_filename($path, $_FILES[$index_name]['name']);
            $newFilePath = $path . $filename;

            // Upload the file into the temp dir
            if (move_uploaded_file($tmpFilePath, $newFilePath)) {
                $insertedId = $CI->misc_model->add_attachment_to_database($requestId, 'free_data', [[
                    'file_name' => $filename,
                    'filetype'  => $_FILES[$index_name]['type'],
                    'staffid' => $staffId
                ]], false, false);

                return $insertedId ? $CI->misc_model->get_file($insertedId)->attachment_key : false;
            }
        }
    }

    return false;
}
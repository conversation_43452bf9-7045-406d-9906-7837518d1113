<?php

defined('BASEPATH') or exit('No direct script access allowed');

function free_data_get_staff($roleId = null)
{
    // If not input roleId, get logged-in user
    if (!$roleId) {
        $staff = get_staff();
        $roleId = $staff->role ?? null;
    }
    return $roleId;
}

/**
 * Check current user is sale leader or not
 * @param integer $roleId role id, if not specific will get role id of the logged in user id
 * @return boolean true|false
 */
function free_data_is_admin_role($roleId = null)
{
    return free_data_get_staff($roleId) == FREE_DATA_ADMIN_ROLE;
}

/**
 * Check current user is sale leader or not
 * @param integer $roleId role id, if not specific will get role id of the logged in user id
 * @return boolean true|false
 */
function free_data_is_sale_leader_role($roleId = null)
{
    return free_data_get_staff($roleId) == FREE_DATA_SALE_LEADER_ROLE;
}

/**
 * Check current user is sale admin or not
 * @param integer $roleId role id, if not specific will get role id of the logged in user id
 * @return boolean true|false
 */
function free_data_is_sale_admin_role($roleId = null)
{
    return free_data_get_staff($roleId) == FREE_DATA_SALE_ADMIN_ROLE;
}

/**
 * Check current user is sale or not
 * @param integer $roleId role id, if not specific will get role id of the logged in user id
 * @return boolean true|false
 */
function free_data_is_sale_role($roleId = null)
{
    return free_data_get_staff($roleId) == FREE_DATA_SALE_ROLE;
}

/**
 * Check current user have permission to view company free data or not
 * @param integer $roleId role id, if not specific will get role id of the logged in user id
 * @return boolean true|false
 */
function free_data_can_view_company($roleId = null)
{
    return has_role_permission(free_data_get_staff($roleId), 'access', FREE_DATA_MODULE_NAME);
}

/**
 * Check current user have permission to view they own request or not
 * @param integer $roleId role id, if not specific will get role id of the logged in user id
 * @return boolean true|false
 */
function free_data_can_view_own($roleId = null)
{
    $roleId = free_data_get_staff($roleId);
    return has_role_permission($roleId, 'view_own', FREE_DATA_MODULE_NAME) && free_data_is_sale_role($roleId);
}

/**
 * Check current user have permission to view they members request or not
 * @param integer $roleId role id, if not specific will get role id of the logged in user id
 * @return boolean true|false
 */
function free_data_can_view_members($roleId = null)
{
    $roleId = free_data_get_staff($roleId);
    return (has_role_permission($roleId, 'view_own', FREE_DATA_MODULE_NAME) && free_data_is_sale_leader_role($roleId));
}

/**
 * Check current user have permission to view all requests or not
 * @param integer $roleId role id, if not specific will get role id of the logged in user id
 * @return boolean true|false
 */
function free_data_can_view_all($roleId = null)
{
    return has_role_permission(free_data_get_staff($roleId), 'view_global', FREE_DATA_MODULE_NAME);
}

/**
 * Check current user have permission to approve they member's request or not
 * @param integer $roleId role id, if not specific will get role id of the logged in user id
 * @return boolean true|false
 */
function free_data_can_approve_members($roleId = null)
{
    return has_role_permission(free_data_get_staff($roleId), 'approve_own', FREE_DATA_MODULE_NAME);
}

/**
 * Check current user have permission to approve all member's request or not
 * @param integer $roleId role id, if not specific will get role id of the logged in user id
 * @return boolean true|false
 */
function free_data_can_approve_all($roleId = null)
{
    return has_role_permission(free_data_get_staff($roleId), 'approve_global', FREE_DATA_MODULE_NAME);
}

/**
 * Check current user have permission to create they request or not
 * @param integer $roleId role id, if not specific will get role id of the logged in user id
 * @return boolean true|false
 */
function free_data_can_create($roleId = null)
{
    return has_role_permission(free_data_get_staff($roleId), 'create', FREE_DATA_MODULE_NAME);
}

/**
 * Get company status which is used in the Free Data > Company search box
 * @return array
 */
function free_data_company_status()
{
    return [
        [
            'id' => 1,
            'text' => _l('free_data_company_status_free'),
        ],
        [
            'id' => 2,
            'text' => _l('free_data_company_status_requested'),
        ]
    ];
}

/**
 * Get potential rate which is used in the Free Data > Company search box
 * @return array
 */
function free_data_company_potential_rate()
{
    return [[
        'id' => 1,
        'text' => 'Tiếp cận lần đầu'
    ], [
        'id' => 2,
        'text' => '20% (1 tín hiệu)'
    ], [
        'id' => 3,
        'text' => '50% (Từ 3 tín hiệu)'
    ], [
        'id' => 4,
        'text' => '80% (Từ 4 tín hiệu)'
    ], [
        'id' => 5,
        'text' => '100%'
    ]];
}

/**
 * Get all headers of company grid, having action header if the logged user is sale role
 * @return array
 */
function free_data_get_company_grid_headers()
{
    $headers = [
        _l('free_data_company_col_id'),
        _l('free_data_company_col_company_name'),
        _l('free_data_company_col_created_at'),
        _l('free_data_company_col_last_contact_note'),
        _l('free_data_company_col_potential_rate'),
        _l('free_data_company_col_request_status'),
    ];

    // Sale role has action column
    if (free_data_is_sale_role()) {
        $headers[] = _l('free_data_company_col_action');
    }

    return $headers;
}

/**
 * Get all headers of company grid, having action header if the logged user is sale role
 * @return array
 */
function free_data_get_my_request_grid_headers()
{
    $headers = [
        _l('free_data_my_request_col_id'),
        _l('free_data_my_request_col_company_name'),
        _l('free_data_my_request_col_created_at'),
        _l('free_data_my_request_col_reason'),
        _l('free_data_my_request_col_customer_type'),
        _l('free_data_approval_note'),
        _l('free_data_my_request_col_request_total'),
        _l('free_data_my_request_col_action'),
    ];

    return $headers;
}

function free_data_get_request_statuses()
{
    return [
        [
            'id' => 'waiting',
            'text' => _l('free_data_request_waiting_status'), 
        ],
        [
            'id' => FREE_DATA_REQUEST_STATUS_SA_APPROVED,
            'text' => _l('free_data_request_sa_approved_status'), 
        ],
        [
            'id' => 'rejected',
            'text' => _l('free_data_request_rejected_status'), 
        ],
        [
            'id' => FREE_DATA_REQUEST_STATUS_LEADER_APPROVED,
            'text' => _l('free_data_request_approved_status'), 
        ]
    ];
}

function free_data_get_attachment_link($attachment)
{
    return $attachment;
}

function free_data_get_request_customer_type()
{
    return [
        [
            'id' => 'has_job_out_of_package',
            'text' => _l('free_data_request_has_job_out_of_package'), 
        ],
        [
            'id' => 'has_package_not_use',
            'text' => _l('free_data_request_has_package_not_use'), 
        ],
        [
            'id' => 'has_job_in_market',
            'text' => _l('free_data_request_has_job_in_market'), 
        ],
        [
            'id' => 'not_buy_has_demand',
            'text' => _l('free_data_request_not_buy_has_demand'), 
        ],
        [
            'id' => 'contacted_before',
            'text' => _l('free_data_request_contacted_before'), 
        ],
        [
            'id' => 'it_company',
            'text' => _l('free_data_request_it_company'), 
        ],
        [
            'id' => 'recruited_before',
            'text' => _l('free_data_request_recruited_before'), 
        ],
        [
            'id' => 'recruit_demand',
            'text' => _l('free_data_request_recruit_demand'), 
        ]
    ];
}

function free_data_get_request_customer_type_text($type)
{
    $customerTypes = free_data_get_request_customer_type();
    $types = array_column($customerTypes, 'id');
    $key = array_search($type, $types);
    return $key !== false ? $customerTypes[$key]['text'] : '';
}

function free_data_get_request_status_text($status)
{
    $statuses = [
        FREE_DATA_REQUEST_STATUS_WAITING => _l('free_data_request_waiting_status'),
        FREE_DATA_REQUEST_STATUS_SA_APPROVED => _l('free_data_request_sa_approved_status'),
        FREE_DATA_REQUEST_STATUS_SA_REJECTED => _l('free_data_request_rejected_status'),
        FREE_DATA_REQUEST_STATUS_LEADER_APPROVED => _l('free_data_request_approved_status'),
        FREE_DATA_REQUEST_STATUS_LEADER_REJECTED => _l('free_data_request_rejected_status'),
    ];

    $statusColors = [
        FREE_DATA_REQUEST_STATUS_WAITING => 'default',
        FREE_DATA_REQUEST_STATUS_SA_APPROVED => 'default',
        FREE_DATA_REQUEST_STATUS_SA_REJECTED => 'danger',
        FREE_DATA_REQUEST_STATUS_LEADER_APPROVED => 'success',
        FREE_DATA_REQUEST_STATUS_LEADER_REJECTED => 'danger',
    ];

    return $statuses[$status] ? '<button class="btn btn-'.$statusColors[$status].'">' . $statuses[$status] . '</button>' : '';
}

function free_data_go_to_page($message = '', $url = '', $type = 'danger')
{
    set_alert($type, $message);
    redirect($url ? $url : admin_url('not_found'));
}

/**
 * Get all headers of company grid, having action header if the logged user is sale role
 * @return array
 */
function free_data_get_requests_grid_headers()
{
    $headers = [
        _l('free_data_request_col_request_id'),
        _l('free_data_request_col_id'),
        _l('free_data_request_col_company_name'),
        _l('free_data_request_col_created_by'),
        _l('free_data_request_col_created_at'),
        _l('free_data_request_col_reason'),
        _l('free_data_request_col_customer_type'),
        _l('free_data_approval_note'),
        _l('free_data_request_col_request_total'),
        _l('free_data_request_col_action'),
        _l('free_data_request_col_last_action'),
        _l('free_data_request_col_sa_approved_at'),
        _l('free_data_request_col_leader_approved_at'),
    ];

    return $headers;
}


function free_data_get_request_action_dropdown($requestId, $createdBy)
{
    return '<div class="btn-group">
      <button type="button" class="btn btn-default dropdown-toggle request-action" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="fa fa-spinner fa-spin hide" style="margin-right: 2px;"></i>'._l('free_data_request_waiting_status').' <span class="caret"></span>
      </button>
      <ul class="dropdown-menu">
      <li><a onclick="changeRequestStatus('.$requestId.',\''.$createdBy.'\', \'reject\')" href="javascript:;">'._l('free_data_request_rejected_status').'</a></li>
      <li><a onclick="changeRequestStatus('.$requestId.',\''.$createdBy.'\', \'approve\')" href="javascript:;">'._l('free_data_request_approved_status').'</a></li>
      </ul>
    </div>';
}

function free_data_get_request_action($requestId, $createdBy, $status)
{
    $statuses = [
        FREE_DATA_REQUEST_STATUS_WAITING => _l('free_data_request_waiting_status'),
        FREE_DATA_REQUEST_STATUS_SA_APPROVED => _l('free_data_request_sa_approved_status'),
        FREE_DATA_REQUEST_STATUS_SA_REJECTED => _l('free_data_request_rejected_status'),
        FREE_DATA_REQUEST_STATUS_LEADER_APPROVED => _l('free_data_request_approved_status'),
        FREE_DATA_REQUEST_STATUS_LEADER_REJECTED => _l('free_data_request_rejected_status'),
    ];

    // Just show Waiting if record is approved by sale admin
    if (free_data_is_sale_leader_role()) {
        $statuses[FREE_DATA_REQUEST_STATUS_SA_APPROVED] = _l('free_data_request_waiting_status');
    }

    $statusColors = [
        FREE_DATA_REQUEST_STATUS_WAITING => 'default',
        FREE_DATA_REQUEST_STATUS_SA_APPROVED => 'default',
        FREE_DATA_REQUEST_STATUS_SA_REJECTED => 'danger',
        FREE_DATA_REQUEST_STATUS_LEADER_APPROVED => 'success',
        FREE_DATA_REQUEST_STATUS_LEADER_REJECTED => 'danger',
    ];

    // If waiting or sa_approve, then show option button instead
    if (in_array($status, [FREE_DATA_REQUEST_STATUS_WAITING, FREE_DATA_REQUEST_STATUS_SA_APPROVED]) && free_data_is_sale_leader_role()) {
        return free_data_get_request_action_dropdown($requestId, $createdBy);
    }

    // If waiting or sa_approve, then show option button instead
    if (in_array($status, [FREE_DATA_REQUEST_STATUS_WAITING])) {
        return free_data_get_request_action_dropdown($requestId, $createdBy);
    }

    return $statuses[$status] ? '<button class="btn btn-'.$statusColors[$status].'">' . $statuses[$status] . '</button>' : '';
}

function free_data_get_request_last_status($status)
{
    $statuses = [
        FREE_DATA_REQUEST_STATUS_WAITING => _l('free_data_request_waiting_status'),
        FREE_DATA_REQUEST_STATUS_SA_APPROVED => _l('free_data_request_sa_approved_status'),
        FREE_DATA_REQUEST_STATUS_SA_REJECTED => _l('free_data_request_rejected_status'),
        FREE_DATA_REQUEST_STATUS_LEADER_APPROVED => _l('free_data_request_approved_status'),
        FREE_DATA_REQUEST_STATUS_LEADER_REJECTED => _l('free_data_request_rejected_status'),
    ];

    return $statuses[$status] ? '<button class="btn btn-'.$statusColors[$status].'">' . $statuses[$status] . '</button>' : '';
}

/**
 * Throws header 400 bad request, used for ajax requests
 */
function free_data_ajax_error_validation($message)
{
    header('Content-Type: application/json');
    header('HTTP/1.0 400 Bad Request');
    echo json_encode([
        'status' => false,
        'message' => $message
    ]);
    die;
}

/**
 * Throws header 400 bad request, used for ajax requests
 */
function free_data_ajax_success($message)
{
    header('Content-Type: application/json');
    echo json_encode([
        'status' => true,
        'message' => $message
    ]);
    die;
}
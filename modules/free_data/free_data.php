<?php

defined('BASEPATH') or exit('No direct script access allowed');

/*
Module Name: Free Data
Description: Module for working client's free data feature 
Version: 1.0.0
Requires at least: 2.9.4
*/

define('FREE_DATA_MODULE_NAME', 'free_data');
define('FREE_DATA_SALE_ROLE', 1);
define('FREE_DATA_ADMIN_ROLE', 4);
define('FREE_DATA_SALE_LEADER_ROLE', 7);
define('FREE_DATA_SALE_ADMIN_ROLE', 8);
$CI = &get_instance();

/**
* Register language files, must be registered if the module is using languages
*/
register_language_files(FREE_DATA_MODULE_NAME, [FREE_DATA_MODULE_NAME]);

hooks()->add_action('admin_init', 'free_data_module_init_menu_items');
hooks()->add_action('admin_init', 'free_data_module_permissions');
hooks()->add_action('free_data_reject_request', 'free_data_reject_request');
hooks()->add_filter('before_assign_customer_admin', 'free_data_before_assign_customer_admin');
hooks()->add_action('before_remove_customer_admin', 'free_data_before_remove_customer_admin');
hooks()->add_action('move_to_free_data', 'free_data_move_to_free_data');

/**
* Register activation module hook
*/
register_activation_hook(FREE_DATA_MODULE_NAME, 'free_data_module_activation_hook');

/**
* Load the module helper
*/
$CI->load->helper(FREE_DATA_MODULE_NAME . '/free_data');
$CI->load->helper(FREE_DATA_MODULE_NAME . '/free_data_file');

function free_data_module_activation_hook()
{
    $CI = &get_instance();
    require_once(__DIR__ . '/install.php');
}

/**
 * Init free data module menu items in setup in admin_init hook
 * @return void
 */
function free_data_module_init_menu_items()
{
    $CI = &get_instance();
    $staff = get_staff();
    $roleId = $staff->role ?? null;

    if (free_data_can_view_company($roleId)) {
        $CI->app_menu->add_sidebar_menu_item('free-data-sidebar-menu-item', [
            'name'     => _l('free_data'),
            'href'     => admin_url('free_data'),
            'position' => 16,
            'icon'     => 'fa fa-user-o menu-icon'
        ]);

        $CI->app_menu->add_sidebar_children_item('free-data-sidebar-menu-item', [
            'slug'     => '',
            'name'     => _l('free_data_company_list'),
            'href'     => admin_url('free_data'),
            'position' => 1,
        ]);
    }

    // B2B Sale can access this
    if (free_data_can_view_own($roleId)) {
        $CI->app_menu->add_sidebar_children_item('free-data-sidebar-menu-item', [
                'slug'     => 'my-request',
                'name'     => _l('free_data_my_request'),
                'href'     => admin_url('free_data/my_request'),
                'position' => 2,
        ]);
    }

    // admin, B2B Sale admin/leader can view own
    if (free_data_can_view_members($roleId) || free_data_can_view_all($roleId)) {
        $CI->app_menu->add_sidebar_children_item('free-data-sidebar-menu-item', [
            'slug'     => 'waiting-requests',
            'name'     => _l('free_data_pending_request'),
            'href'     => admin_url('free_data/requests/waiting'),
            'position' => 3,
        ]);

        $CI->app_menu->add_sidebar_children_item('free-data-sidebar-menu-item', [
            'slug'     => 'requests',
            'name'     => _l('free_data_all_request'),
            'href'     => admin_url('free_data/requests'),
            'position' => 4,
        ]);
    }
}

/**
 * Init free data module permission in setup in admin_init hook
 * @return void
 */
function free_data_module_permissions()
{
    $capabilities = [];

    $capabilities['capabilities'] = [
            'access'   => _l('free_data_permission_access'),
            'view_own' => _l('free_data_permission_view_own'),
            'view_global'   => _l('free_data_permission_view_global'),
            'approve_own' => _l('free_data_permission_approve_own'),
            'approve_global' => _l('free_data_permission_approve_global'),
            'create' => _l('free_data_permission_create'),
            'edit' => _l('free_data_permission_edit'),
            'delete' => _l('free_data_permission_delete'),
    ];

    register_staff_capabilities(FREE_DATA_MODULE_NAME, $capabilities, _l('free_data'));
}

/**
 * Filter that check about client already in pool or not
 * @param int $clientId
 * @return bool
 */
function free_data_before_assign_customer_admin($clientId)
{
    $CI = &get_instance();
    $CI->load->model(FREE_DATA_MODULE_NAME . '/Free_data_model');
    return $CI->Free_data_model->allow_assign_customer_admin($clientId);
}

/**
 * Put clients in the free db pool to use in the free data feature
 * @param string $whereCondition
 */
function free_data_before_remove_customer_admin($whereCondition)
{
    $CI = &get_instance();
    $CI->load->model(FREE_DATA_MODULE_NAME . '/Free_data_model');
    $CI->Free_data_model->store_free_data($whereCondition);
}

/**
 * Reject all request due to this client is assigned to sale
 * @param integer $clientId
 */
function free_data_reject_request($clientId)
{
    $CI = &get_instance();
    $CI->load->model(FREE_DATA_MODULE_NAME . '/Free_data_model');
    $CI->Free_data_model->reject_by_direct_assign($clientId);
}

function free_data_move_to_free_data($clientId)
{
    $CI = &get_instance();
    $CI->load->model(FREE_DATA_MODULE_NAME . '/Free_data_model');
    $CI->Free_data_model->insert_free_data($clientId);
}
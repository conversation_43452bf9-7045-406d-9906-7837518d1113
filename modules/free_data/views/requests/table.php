<?php

use Entities\M3cCall;

defined('BASEPATH') or exit('No direct script access allowed');

$this->ci->db->query("SET sql_mode = ''");

$aColumns = [
    'minId',
    db_prefix() . 'clients.userid as userid',
    'company',
    'CONCAT(' . db_prefix() . 'staff.firstname, " ", ' . db_prefix() . 'staff.lastname) as created_by',
    db_prefix() . 'contact_requests.created_at as requested_at'
];

$additionalSelect = [
    db_prefix() . 'clients.userid as userid',
    'company',
    db_prefix() . 'contacts.id as contact_id',
    db_prefix() . 'contacts.fullname as fullname',
    db_prefix() . 'contacts.email as email',
    db_prefix() . 'clients.phonenumber as phonenumber',
    db_prefix() . 'contacts.phonenumber as phonenumber_contact',
    db_prefix() . 'contact_requests.client_contact_pool_id as pool_id',
    db_prefix() . 'contact_requests.id as request_id',
    db_prefix() . 'contact_requests.leader_id as leader_id',
    db_prefix() . 'contact_requests.created_at as requested_at',
    db_prefix() . 'contact_requests.admin_id as admin_id',
    db_prefix() . 'contact_requests.status as request_status',
    db_prefix() . 'contact_requests.content as request_content',
    db_prefix() . 'contact_requests.link as link',
    db_prefix() . 'contact_requests.customer_type as request_customer_type',
    'CONCAT(' . db_prefix() . 'files.rel_type, "/",' . db_prefix() . 'files.staffid, "/", ' . db_prefix() . 'files.file_name) as attachment_url',
    db_prefix() . 'contact_requests.sa_approved_at as sa_approved_at',
    'subTable.approved_at as leader_approved_at',
    db_prefix() . 'contact_requests.rejected_at as rejected_at',
    db_prefix() . 'contact_requests.sa_note as sa_note',
    db_prefix() . 'contact_requests.leader_note as leader_note',
];

$sIndexColumn = 'id';
$sTable = db_prefix() . 'contact_requests';
$where = [];
$subWhere = '';

$createdRange = '';
$createdFromDate = $this->ci->input->post('created_from_date');
$createdToDate = $this->ci->input->post('created_to_date');

$saApprovedRange = '';
$saApprovedFromDate = $this->ci->input->post('sa_approved_from_date');
$saApprovedToDate = $this->ci->input->post('sa_approved_to_date');

$leaderApprovedRange = '';
$leaderApprovedFromDate = $this->ci->input->post('leader_approved_from_date');
$leaderApprovedToDate = $this->ci->input->post('leader_approved_to_date');

//Filter by created date
if ($createdFromDate) {
    $createdRange = get_query_filter_date($createdFromDate, $createdToDate, db_prefix() . 'contact_requests.created_at');

    if ($createdRange) {
        $where[] = 'AND ' . $createdRange;
    }
}

//Filter by SA Approved date
if ($saApprovedFromDate) {
    $saApprovedRange .= get_query_filter_date($saApprovedFromDate, $saApprovedToDate, db_prefix() . 'contact_requests.rejected_at');
    $saApprovedRange .= ' OR ';
    $saApprovedRange .= get_query_filter_date($saApprovedFromDate, $saApprovedToDate, db_prefix() . 'contact_requests.sa_approved_at');

    if ($saApprovedRange) {
        $where[] = 'AND (' . $saApprovedRange . ')';
    }
}

//Filter by Leader Approved date
if ($leaderApprovedFromDate) {
    $leaderApprovedRange .= '((' . get_query_filter_date($leaderApprovedFromDate, $leaderApprovedToDate, db_prefix() . 'contact_requests.rejected_at') . ' AND ' . db_prefix() . 'contact_requests.status = "' . FREE_DATA_REQUEST_STATUS_LEADER_REJECTED . '")';
    $leaderApprovedRange .= ' OR ';
    $leaderApprovedRange .= get_query_filter_date($leaderApprovedFromDate, $leaderApprovedToDate, 'subTable.approved_at') . ')';

    if ($leaderApprovedRange) {
        $where[] = 'AND ' . $leaderApprovedRange;
    }
}

// Custom search value by request status
if ($status = $this->ci->input->post('request_status')) {
    if ($status === 'rejected') {
        $where[] = 'AND ' . db_prefix() . 'contact_requests.status IN ("' . FREE_DATA_REQUEST_STATUS_SA_REJECTED . '", "' . FREE_DATA_REQUEST_STATUS_LEADER_REJECTED . '")';
    } else if (free_data_is_sale_leader_role() && $status === FREE_DATA_REQUEST_STATUS_WAITING) { // Only see sa_approved if selected waiting
        $where[] = 'AND ' . db_prefix() . 'contact_requests.status = "' . FREE_DATA_REQUEST_STATUS_SA_APPROVED . '"';
    } else {
        $where[] = 'AND ' . db_prefix() . 'contact_requests.status = "' . $status . '"';
    }
}

// If Sale Leader, only see request of team members
if (free_data_is_sale_leader_role()) {
    $this->ci->load->model('free_data_model');
    $saleMemeberIds = $this->ci->free_data_model->get_sale_members(get_staff_user_id());
    // Include sale leader
    $saleMemeberIds[] = get_staff_user_id();
    $where[] = 'AND ' . db_prefix() . 'contact_requests.staff_id IN (' . join(',', $saleMemeberIds) . ')';

    // Only fetch request that action by sale admin
    $where[] = 'AND ' . db_prefix() . 'contact_requests.admin_id IS NOT NULL';
}

//Sub where for get first request id
if ($where) {
    $subWhere = 'AND (' . substr_replace(join(' ', $where), '', 0, 3);
    $subWhere .= ')';
}

// Custom search value by company id, name
if ($company = $this->ci->input->post('request_company')) {
    if (is_numeric($company)) {
        $where[] = 'AND ' . db_prefix() . 'clients.userid = ' . $company;
    } else {
        $where[] = 'AND company LIKE "%' . $this->ci->db->escape_str($company) . '%"';
    }
}

// Add blank where all filter can be stored
$join = [
    'JOIN (
        SELECT client_id, client_contact_pool_id, approved_at, MIN(' . db_prefix() . 'contact_requests.id) minId
        FROM ' . db_prefix() . 'contact_requests
        JOIN ' . db_prefix() . 'client_contact_pools as subTable ON client_contact_pool_id = subTable.id  ' . $subWhere . '
        GROUP BY subTable.client_id) subTable ON ' . db_prefix() . 'contact_requests.client_contact_pool_id = subTable.client_contact_pool_id',
    'JOIN ' . db_prefix() . 'clients ON ' . db_prefix() . 'clients.userid = subTable.client_id',
    'INNER JOIN ' . db_prefix() . 'staff ON ' . db_prefix() . 'staff.staffid =' . db_prefix() . 'contact_requests.staff_id',
    'LEFT JOIN ' . db_prefix() . 'contacts ON ' . db_prefix() . 'contacts.userid=' . db_prefix() . 'clients.userid AND ' . db_prefix() . 'contacts.is_primary=1',
    'LEFT JOIN ' . db_prefix() . 'files ON ' . db_prefix() . 'contact_requests.id = ' . db_prefix() . 'files.rel_id AND ' . db_prefix() . 'files.rel_type = "free_data"',
];

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, $additionalSelect);

$output  = $result['output'];
$rResult = $result['rResult'];
$lastCompanyId = '';
$rowColorCls = '';

$clientPhones = array_filter(array_pluck($rResult, 'phonenumber'));
$contactPhones = array_filter(array_pluck($rResult, 'phonenumber_contact'));
$poolIds = array_filter(array_pluck($rResult, 'pool_id'));

$clientPhoneCalls = [];
$contactPhoneCalls = [];
$requestTotal = [];
$myRequests = [];
if (count($clientPhones)) {
    $calls = $this->ci->db
        ->select('caller, talk_time as 3c_duration, MAX(start_time) AS 3c_time')
        ->where_in(db_prefix() . '3c_call.caller', $clientPhones)
        ->where(db_prefix() . '3c_call.call_type', M3cCall::CALL_OUT_TYPE)
        ->group_by(db_prefix() . '3c_call.caller')
        ->get(db_prefix() . '3c_call')
        ->result_array();


    foreach ($calls as $call) {
        $clientPhoneCalls[$call['caller']] = $call;
    }
}

if (count($contactPhones)) {
    $calls = $this->ci->db
        ->select('caller, talk_time as 3c_duration_contact, MAX(start_time) AS 3c_time_contact')
        ->where_in(db_prefix() . '3c_call.caller', $contactPhones)
        ->where(db_prefix() . '3c_call.call_type', M3cCall::CALL_OUT_TYPE)
        ->group_by(db_prefix() . '3c_call.caller')
        ->get(db_prefix() . '3c_call')
        ->result_array();

    foreach ($calls as $call) {
        $contactPhoneCalls[$call['caller']] = $call;
    }
}

if (count($poolIds)) {
    $requests = $this->ci->db
        ->select('COUNT(id) as total, client_contact_pool_id')
        ->where_in(db_prefix() . 'contact_requests.client_contact_pool_id', $poolIds)
        ->group_by(db_prefix() . 'contact_requests.client_contact_pool_id')
        ->get(db_prefix() . 'contact_requests')
        ->result_array();

    foreach ($requests as $request) {
        $requestTotal[$request['client_contact_pool_id']] = $request['total'];
    }
}

foreach ($rResult as $aRow) {
    $row = [];

    //Request ID
    $row[] = $aRow['request_id'];

    // Company ID
    $row[] = $aRow['userid'];
    $lastCompanyId = $lastCompanyId == '' ? $aRow['userid'] : $lastCompanyId;

    // Company
    $company  = $aRow['company'];
    $isPerson = false;

    if ($company == '') {
        $company  = _l('no_company_view_profile');
        $isPerson = true;
    }

    $phonenumber = $aRow['phonenumber'] != null ? $aRow['phonenumber'] : $aRow['phonenumber_contact'];

    $clientPhoneCall = $aRow['phonenumber'] != null ? $clientPhoneCalls[$aRow['phonenumber']] ?? [] : [];
    $contactPhoneCall = $aRow['phonenumber_contact'] != null ? $contactPhoneCalls[$aRow['phonenumber_contact']] ?? [] : [];

    $call_time = isset($clientPhoneCall['3c_time']) ? $clientPhoneCall['3c_time'] : ($contactPhoneCall['3c_time_contact'] ?? '');
    $duration = isset($clientPhoneCall['3c_duration']) ? $clientPhoneCall['3c_duration'] : ($contactPhoneCall['3c_duration_contact'] ?? '');

    $url = admin_url('clients/client/' . $aRow['userid']);

    if ($isPerson && $aRow['contact_id']) {
        $url .= '?contactid=' . $aRow['contact_id'];
    }

    $company = '<a href="' . $url . '">' . $company . '</a><br>' .
        ($aRow['contact_id'] != null ? '<b>Contact: </b>' . $aRow['fullname'] . '<br>'  : '') .
        ($aRow['email'] != null ? '<b>Email: </b>' . $aRow['email'] . '<br>' : '') .
        ($phonenumber != null ? '<b>Phone: </b>' . $phonenumber . '<br>' : '') .
        ($call_time != null ? '<b>Last call: </b>' . $call_time . ' &#124; Duration: ' . $duration : '');

    $row[] = $company;

    // Created by
    $row[]  = $aRow['created_by'];

    // Requested Date
    $row[] = _dt($aRow['requested_at']);

    // Latest note contact
    $reason = str_replace("\r\n", "<br>", $aRow['request_content']);
    if ($aRow['link']) {
        $reason .= '<br><a target="_blank" href="' . $aRow['link'] . '">' . _l('free_data_my_request_row_link') . '</a>';
    }
    if ($aRow['attachment_url']) {
        $reason .= '<br><a target="_blank" href="' . site_url('uploads/' . $aRow['attachment_url']) . '">' . _l('free_data_my_request_row_attachment') . '</a>';
    }
    $row[] = $reason;

    // Customer Type
    $row[] = free_data_get_request_customer_type_text($aRow['request_customer_type']);

    // Approval Note
    $row[] = _l('free_data_sa_note') . ': ' . $aRow['sa_note'] . '<br>' .
    _l('free_data_leader_note') . ': ' . $aRow['leader_note'] . '<br>' .
    (in_array($aRow['request_status'], array_merge(
        [FREE_DATA_REQUEST_STATUS_WAITING],
        free_data_is_sale_leader_role() ? [
            FREE_DATA_REQUEST_STATUS_SA_APPROVED,
            FREE_DATA_REQUEST_STATUS_SA_REJECTED
        ] : []
    )) ? '' : '<a data-note="' . htmlspecialchars(free_data_is_sale_leader_role() ? $aRow['leader_note'] : $aRow['sa_note']) . '" onclick=\'showNoteForm(' . $aRow['request_id'] . ', null, this)\' href="javascript:;">' . _l('free_data_edit_note') . '</a>'
    );

    // Num of requests
    $row[] = $requestTotal[$aRow['pool_id']] ?? 0;

    // Request Status
    $row[] = free_data_get_request_action($aRow['request_id'], $aRow['created_by'], $aRow['request_status']);

    // Change row color based on group of company
    if ($lastCompanyId != $aRow['userid']) {
        $rowColorCls = $rowColorCls == '' ? 'info' : '';
        $lastCompanyId = $aRow['userid'];
    }
    $row['DT_RowClass'] = 'request-' . $aRow['request_id'] . ' has-row-options ' . $rowColorCls;

    // Last status action
    $row[] = _l('free_data_request_' . $aRow['request_status'] . '_status');

    // SA approved at
    $row[] = _dt($aRow['request_status'] == FREE_DATA_REQUEST_STATUS_SA_REJECTED ? $aRow['rejected_at'] : $aRow['sa_approved_at']);

    // SA approved at
    $row[] = _dt($aRow['request_status'] == FREE_DATA_REQUEST_STATUS_LEADER_REJECTED ? $aRow['rejected_at'] : $aRow['leader_approved_at']);

    $output['aaData'][] = $row;
}

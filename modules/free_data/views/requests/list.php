<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
   <div class="modal fade" id="add_note_modal" tabindex="-1" role="dialog" aria-labelledby="addNoteModalLabel">
      <div class="modal-dialog" role="document">
         <div class="modal-content">
            <div class="modal-header">
               <button group="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
               <h4 class="modal-title" id="addNoteModalLabel"><?= _l('free_data_approval_note') ?></h4>
            </div>
            <?= form_open('', ['id' => 'add_note_form']); ?>
            <div class="modal-body">
               <div class="row">
                  <div class="col-md-12">
                     <?= render_textarea('note', _l('note_description'), '', ['rows' => 20]); ?>
                  </div>
               </div>
            </div>
            <div class="modal-footer">
               <button group="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
               <button group="submit" class="btn btn-info btn-submit"><?= _l('submit'); ?></button>
               <?= form_close(); ?>
            </div>
         </div>
      </div>
   </div>

   <div class="content">
      <div class="row">
         <div class="col-md-12">
            <div class="panel_s">
               <div class="panel-body" id="free_data">
                  <div class="request-search _filters mbot15">
                     <div class="row">
                        <div class="col-md-4 form-group">
                           <div class="input-group">
                              <span class="input-group-addon"><span class="fa fa-search"></span></span>
                              <input type="search" id="request_company" name="request_company" class="form-control input-sm" placeholder="<?= _l('free_data_company_search_input_placeholder') ?>">
                           </div>
                        </div>
                     </div>
                     <div class="row <?php echo $status != '' ? 'hidden' : '' ?>">
                        <div class="col-md-2">
                           <?= render_select('request_status', free_data_get_request_statuses(), ['id', 'text'], _l('free_data_company_status'), $status); ?>
                        </div>
                     </div>
                     <div class="row">
                        <div class="col-md-4">
                           <div id="date-range">
                              <div class="row">
                                 <div class="col-md-6">
                                    <label for="created_from_date" class="control-label"><?= _l('free_data_request_col_created_at') . ' (' . _l('from') . ')' ?></label>
                                    <div class="input-group date">
                                       <input type="text" class="form-control datepicker" id="created_from_date" name="created_from_date" autocomplete="off">
                                       <div class="input-group-addon">
                                          <i class="fa fa-calendar calendar-icon"></i>
                                       </div>
                                    </div>
                                 </div>
                                 <div class="col-md-6">
                                    <label for="created_to_date" class="control-label"><?= _l('free_data_request_col_created_at') . ' (' . _l('to') . ')' ?></label>
                                    <div class="input-group date">
                                       <input type="text" class="form-control datepicker" id="created_to_date" name="created_to_date" autocomplete="off">
                                       <div class="input-group-addon">
                                          <i class="fa fa-calendar calendar-icon"></i>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div class="col-md-4">
                           <div id="date-range">
                              <div class="row">
                                 <div class="col-md-6">
                                    <label for="sa_approved_from_date" class="control-label"><?= _l('free_data_request_col_sa_approved_at') . ' (' . _l('from') . ')' ?></label>
                                    <div class="input-group date">
                                       <input type="text" class="form-control datepicker" id="sa_approved_from_date" name="sa_approved_from_date" autocomplete="off">
                                       <div class="input-group-addon">
                                          <i class="fa fa-calendar calendar-icon"></i>
                                       </div>
                                    </div>
                                 </div>
                                 <div class="col-md-6">
                                    <label for="sa_approved_to_date" class="control-label"><?= _l('free_data_request_col_sa_approved_at') . ' (' . _l('to') . ')' ?></label>
                                    <div class="input-group date">
                                       <input type="text" class="form-control datepicker" id="sa_approved_to_date" name="sa_approved_to_date" autocomplete="off">
                                       <div class="input-group-addon">
                                          <i class="fa fa-calendar calendar-icon"></i>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div class="col-md-4">
                           <div id="date-range">
                              <div class="row">
                                 <div class="col-md-6">
                                    <label for="leader_approved_from_date" class="control-label"><?= _l('free_data_request_col_leader_approved_at') . ' (' . _l('from') . ')' ?></label>
                                    <div class="input-group date">
                                       <input type="text" class="form-control datepicker" id="leader_approved_from_date" name="leader_approved_from_date" autocomplete="off">
                                       <div class="input-group-addon">
                                          <i class="fa fa-calendar calendar-icon"></i>
                                       </div>
                                    </div>
                                 </div>
                                 <div class="col-md-6">
                                    <label for="leader_approved_to_date" class="control-label"><?= _l('free_data_request_col_leader_approved_at') . ' (' . _l('to') . ')' ?></label>
                                    <div class="input-group date">
                                       <input type="text" class="form-control datepicker" id="leader_approved_to_date" name="leader_approved_to_date" autocomplete="off">
                                       <div class="input-group-addon">
                                          <i class="fa fa-calendar calendar-icon"></i>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <?php render_datatable(free_data_get_requests_grid_headers(), 'all-requests'); ?>
               </div>
            </div>
         </div>
      </div>
   </div>
   <?php init_tail(); ?>
   <script>
      var tAPI;
      var data = {
         requestId: null,
         status: null,
         note: null
      }
      const modal = $('#add_note_modal');
      const note = modal.find('#note')

      $(function() {
         var customSearchParams = {};
         $.each($('#free_data ._filters select'), function() {
            customSearchParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
         });
         $.each($('#free_data ._filters input'), function() {
            customSearchParams[$(this).attr('name')] = '[name="' + $(this).attr('name') + '"]';
         });

         tAPI = initDataTable('.table-all-requests', '<?= admin_url('free_data/all_request_table'); ?>', [4, 5, 6, 7, 8], [1, 2, 3, 4, 5, 6, 7, 8], customSearchParams, [0, 'asc']);
         $('input[name="request_company"], select[name="request_status"], input[name=created_from_date], input[name=created_to_date], input[name=sa_approved_from_date], input[name=sa_approved_to_date], input[name=leader_approved_from_date], input[name=leader_approved_to_date]').on('change', function() {
            tAPI.ajax.reload();
         });
      });

      function toggleActionBtn(button, action) {
         if (action == 'show') {
            button.find('i.fa-spinner').removeClass('hide');
            button.addClass('disabled');
         } else {
            button.find('i.fa-spinner').addClass('hide');
            button.removeClass('disabled');
         }
      }

      function changeRequestStatus(requestId, createdBy, status) {
         var confirmMsg = status == 'approve' ? '<?php echo _l('free_data_request_approve_confirm_message'); ?>' : '<?php echo _l('free_data_request_reject_confirm_message'); ?>';
         cf = confirm(confirmMsg.replace('__created_by__', createdBy));

         if (cf == true) {
            var row = $('.table-all-requests tr.request-' + requestId),
               button = row.find('button.request-action');
            toggleActionBtn(button, 'show');
            showNoteForm(requestId, status)
         }
      }

      function showNoteForm(requestId, status, aTag) {
         data.requestId = requestId;
         data.status = status;

         if (aTag) {
            data.note = String($(aTag).data('note')).replace(/(<|&lt;)br\s*\/*(>|&gt;)/g, " ");
         } else {
            data.note = null
         }

         modal.modal('show');
         note.val(data.note)
      }

      modal
         .on('hide.bs.modal', function() {
            modal.find('.modal-footer > .btn-submit').prop('disabled', false);
            toggleActionBtn($('button.request-action.disabled'));
         })
         .on("submit", function(event) {
            event.preventDefault();
            const {
               status,
               requestId
            } = data
            if (requestId) {
               $.ajax({
                  type: 'post',
                  url: admin_url + 'free_data/' + (status ? (status + '_request') : 'edit_note') + '/' + requestId,
                  dataType: 'json',
                  data: {
                     note: note.val()
                  },
                  success: function(response) {
                     alert_float('success', response.message);
                  },
                  error: function(response) {
                     alert_float('danger', response.responseJSON.message ?? 'Unknown Error!');
                  },
                  complete: function() {
                     modal.modal('hide');
                     tAPI.ajax.reload();
                  }
               });
            }
         });
   </script>
   </body>

   </html>
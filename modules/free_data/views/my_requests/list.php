<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
<div class="content">
   <div class="row">
      <div class="col-md-12">
         <div class="panel_s">
            <div class="panel-body">
               <div class="row request-search">
                  <div class="col-md-12 no-padding-left no-padding-right">
                     <div class="col-md-10 no-padding-left">
                        <div class="col-md-6 form-group">
                           <div class="input-group">
                              <span class="input-group-addon"><span class="fa fa-search"></span></span>
                              <input type="search" id="request_company" name="request_company" class="form-control input-sm" placeholder="<?= _l('free_data_company_search_input_placeholder') ?>">
                           </div>
                        </div>
                        <div class="col-md-12 no-padding-left">
                            <div class="col-md-3">
                                <?= render_select('request_status', free_data_get_request_statuses(), ['id', 'text'], _l('free_data_company_status'), ''); ?>
                            </div>
                        </div>
                     </div>
                  </div>
               </div>
               <?php render_datatable(free_data_get_my_request_grid_headers(), 'my-request'); ?>
            </div>
         </div>
      </div>
   </div>
</div>
<?php init_tail(); ?>
<script>
    $(function() {
      var customSearchParams = {};
      $.each($('._hidden_inputs._filters input'),function() {
         customSearchParams[$(this).attr('name')] = '[name="'+$(this).attr('name')+'"]';
      });
      customSearchParams['request_company'] = '[name="request_company"]';
      customSearchParams['request_status'] = '[name="request_status"]';
      var tAPI = initDataTable('.table-my-request', '<?= admin_url('free_data/my_request_table'); ?>', [3,4,5,6], [3,4,5,6], customSearchParams, [2, 'desc']);
      $('input[name="request_company"], select[name="request_status"]').on('change',function() {
           tAPI.ajax.reload();
       });
    });
</script>
</body>
</html>
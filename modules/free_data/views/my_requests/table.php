<?php

use Entities\M3cCall;

defined('BASEPATH') or exit('No direct script access allowed');

$this->ci->db->query("SET sql_mode = ''");

$aColumns = [
   db_prefix() . 'clients.userid as userid',
   'business_name as company_name',
   db_prefix() . 'contact_requests.created_at as requested_at'
];

$additionalSelect = [
    db_prefix() . 'clients.userid as userid',
   'company',
   db_prefix() . 'contacts.id as contact_id',
   db_prefix() . 'contacts.fullname as fullname',
   db_prefix() . 'contacts.email as email',
   db_prefix() . 'clients.phonenumber as phonenumber',
   db_prefix() . 'contacts.phonenumber as phonenumber_contact',
   db_prefix() . 'contact_requests.client_contact_pool_id as pool_id',
   db_prefix() . 'contact_requests.leader_id as leader_id',
   db_prefix() . 'contact_requests.created_at as requested_at',
   db_prefix() . 'contact_requests.admin_id as admin_id',
   db_prefix() . 'contact_requests.status as request_status',
   db_prefix() . 'contact_requests.content as request_content',
   db_prefix() . 'contact_requests.link as link',
   db_prefix() . 'contact_requests.customer_type as request_customer_type',
   db_prefix() . 'contact_requests.sa_note as sa_note',
   db_prefix() . 'contact_requests.leader_note as leader_note',
   'CONCAT('.db_prefix().'files.rel_type, "/",'.db_prefix().'files.staffid, "/", '.db_prefix().'files.file_name) as attachment_url'
];

$sIndexColumn = 'id';
$sTable       = db_prefix().'contact_requests';
$where        = [
    'AND ' . db_prefix() . 'contact_requests.staff_id = ' . get_staff_user_id()
];
// Add blank where all filter can be stored

$join = [
    'INNER JOIN ' . db_prefix() . 'client_contact_pools ON '. db_prefix() . 'client_contact_pools.id =' . db_prefix() . 'contact_requests.client_contact_pool_id',
    'INNER JOIN ' . db_prefix() . 'clients ON '. db_prefix() . 'clients.userid =' . db_prefix() . 'client_contact_pools.client_id AND '. db_prefix() . 'clients.`active` = 1',
    'LEFT JOIN ' . db_prefix() . 'contacts ON ' . db_prefix() . 'contacts.userid=' . db_prefix() . 'clients.userid AND ' . db_prefix() . 'contacts.is_primary=1',
    'LEFT JOIN '.db_prefix().'files ON '.db_prefix().'contact_requests.id = '.db_prefix().'files.rel_id AND '.db_prefix().'files.rel_type = "free_data"',
];

// Custom search value by company id, name
if ($company = $this->ci->input->post('request_company')) {
    if (is_numeric($company)) {
        $where[] = 'AND ' . db_prefix() . 'clients.userid = ' . $company;
    } else {
        $where[] = 'AND company LIKE "%' . $this->ci->db->escape_str($company) . '%"';
    }
}

// Custom search value by request status
if ($status = $this->ci->input->post('request_status')) {
    if ($status === 'rejected') {
        $where[] = 'AND '. db_prefix() . 'contact_requests.status IN ("'.FREE_DATA_REQUEST_STATUS_SA_REJECTED.'", "'.FREE_DATA_REQUEST_STATUS_LEADER_REJECTED.'")';
    } else {
        $where[] = 'AND '. db_prefix() . 'contact_requests.status = "' . $status . '"';
    }
}


$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, $additionalSelect, '');

$output  = $result['output'];
$rResult = $result['rResult'];

$clientPhones = array_filter(array_pluck($rResult, 'phonenumber'));
$contactPhones = array_filter(array_pluck($rResult, 'phonenumber_contact'));
$poolIds = array_filter(array_pluck($rResult, 'pool_id'));
$clientPhoneCalls = [];
$contactPhoneCalls = [];
$requestTotal = [];

if (count($clientPhones)) {
    $calls = $this->ci->db
       ->select('caller, talk_time as 3c_duration, MAX(start_time) AS 3c_time')
       ->where_in(db_prefix() . '3c_call.caller', $clientPhones)
       ->where(db_prefix() . '3c_call.call_type', M3cCall::CALL_OUT_TYPE)
       ->group_by(db_prefix() . '3c_call.caller')
       ->get(db_prefix() . '3c_call')
       ->result_array();
       
       
       foreach ($calls as $call) {
           $clientPhoneCalls[$call['caller']] = $call;
        }
    }
    
 if (count($contactPhones)) {
    $calls = $this->ci->db
       ->select('caller, talk_time as 3c_duration_contact, MAX(start_time) AS 3c_time_contact')
       ->where_in(db_prefix() . '3c_call.caller', $contactPhones)
       ->where(db_prefix() . '3c_call.call_type', M3cCall::CALL_OUT_TYPE)
       ->group_by(db_prefix() . '3c_call.caller')
       ->get(db_prefix() . '3c_call')
       ->result_array();
 
    foreach ($calls as $call) {
       $contactPhoneCalls[$call['caller']] = $call;
    }
    
 }
 
if (count($poolIds)) {
    $requests = $this->ci->db
       ->select('COUNT(id) as total, client_contact_pool_id')
       ->where_in(db_prefix() . 'contact_requests.client_contact_pool_id', $poolIds)
       ->group_by(db_prefix() . 'contact_requests.client_contact_pool_id')
       ->get(db_prefix() . 'contact_requests')
       ->result_array();
 
    foreach ($requests as $request) {
       $requestTotal[$request['client_contact_pool_id']] = $request['total'];
    }
 }

foreach ($rResult as $aRow) {
    $row = [];
    
    // Company ID
    $row[] = $aRow['userid'];

    // Company
    $company  = $aRow['company_name'];
    $isPerson = false;

    if ($company == '') {
        $company  = _l('no_company_view_profile');
        $isPerson = true;
    }

    $phonenumber = $aRow['phonenumber'] != null ? $aRow['phonenumber'] : $aRow['phonenumber_contact'];

    $clientPhoneCall = $aRow['phonenumber'] != null ? $clientPhoneCalls[$aRow['phonenumber']] ?? [] : [];
    $contactPhoneCall = $aRow['phonenumber_contact'] != null ? $contactPhoneCalls[$aRow['phonenumber_contact']] ?? [] : [];

    $call_time = isset($clientPhoneCall['3c_time']) ? $clientPhoneCall['3c_time'] : ($contactPhoneCall['3c_time_contact'] ?? '');
    $duration = isset($clientPhoneCall['3c_duration']) ? $clientPhoneCall['3c_duration'] : ($contactPhoneCall['3c_duration_contact'] ?? '');

    $url = admin_url('clients/client/' . $aRow['userid']);
    if ($isPerson && $aRow['contact_id']) {
        $url .= '?contactid=' . $aRow['contact_id'];
    }
  
    $company = '<a href="' . $url . '">' . $company . '</a><br>' .
        ($aRow['contact_id'] != null ? '<b>Contact: </b>' . $aRow['fullname'] . '<br>'  : '') .
        ($aRow['email'] != null ? '<b>Email: </b>' . $aRow['email'] . '<br>' : '').
        ($phonenumber != null ? '<b>Phone: </b>' . $phonenumber . '<br>' : '').
        ($call_time != null ? '<b>Last call: </b>' . $call_time . ' &#124; Duration: ' . $duration : '');

    $row[] = $company;

    // Requested Date
    $row[] = _dt($aRow['requested_at']);

    // Latest note contact
    $reason = str_replace("\r\n", "<br>", $aRow['request_content']);
    if ($aRow['link']) {
        $reason .= '<br><a target="_blank" href="'.$aRow['link'].'">'._l('free_data_my_request_row_link').'</a>';
    }
    if ($aRow['attachment_url']) {
        $reason .= '<br><a target="_blank" href="'.site_url('uploads/' . $aRow['attachment_url']).'">'._l('free_data_my_request_row_attachment').'</a>';
    }
    $row[] = $reason;

    // Customer Type
    $row[] = free_data_get_request_customer_type_text($aRow['request_customer_type']);

    // Approval Note
    $row[] = _l('free_data_sa_note') . ': '. $aRow['sa_note'] .'<br>' . _l('free_data_leader_note') . ': ' . $aRow['leader_note'];

    // Num of requests
    $row[] = $requestTotal[$aRow['pool_id']] ?? 0;

    // Request Status
    $row[] = free_data_get_request_status_text($aRow['request_status']);

    $output['aaData'][] = $row;
}
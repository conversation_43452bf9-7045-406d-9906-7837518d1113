<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
   <div class="content">
      <div class="row">
         <div class="col-md-12">
            <div class="panel_s">
               <div class="panel-body">
                  <h4 class="no-margin"><?php echo $title; ?></h4>
                  <hr class="hr-panel-heading" />
                  <?php echo form_open_multipart($this->uri->uri_string(), ['class' => 'disable-on-submit']); ?>
                     <?php $placeHolder = _l('free_data_create_request_content_placeholder_field'); ?>
                     <?php echo render_textarea('content', 'free_data_create_request_content_field', $data['content'] ?? '', ['rows' => 30, 'placeholder' => $placeHolder]); ?>
                     <?php echo render_select('customer_type', free_data_get_request_customer_type(), ['id', 'text'], _l('free_data_create_request_customer_type_field'), $data['customer_type'] ?? ''); ?>
                     <div class="col-md-12 no-padding-left no-padding-right">
                           <div class="col-md-6 no-padding-left">
                              <?php echo render_input('link', 'free_data_create_request_link_field', $data['link'] ?? '', 'text'); ?>
                           </div>
                           <div class="col-md-6 no-padding-left  no-padding-right">
                              <?php echo render_input('attachment', 'free_data_create_request_attachment_field', '', 'file', ['accept' => '.png, .jpeg, .pdf']); ?>
                           </div>
                     </div>
                     <button type="submit" class="btn btn-info pull-right"><?php echo _l('submit'); ?></button>
                     <a href="<?php echo admin_url('free_data') ?>" class="btn btn-default pull-right mright10"><?php echo _l('close'); ?></button>
                  <?php echo form_close(); ?>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
<?php init_tail(); ?>
<script>
   $(function(){
      appValidateForm($('form'), {
         content: 'required',
         customer_type: 'required',
         link: 'url',
         attachment: {
            extension: 'png|jpg|pdf'
         }
      });
   });
</script>
</body>

</html>
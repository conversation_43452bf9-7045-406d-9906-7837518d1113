<?php

use Entities\Client;
use Entities\Note;
use Entities\M3cCall;
use Entities\ContactTerminated;
use Illuminate\Database\Eloquent\Builder;

defined('BASEPATH') or exit('No direct script access allowed');

$sortFilterCols = [
    'clients.userid',
    'business_name',
    'clients.datecreated',
];

$selects = [
    'clients.userid',
    'business_name as company',
    'clients.datecreated',
    'client_contact_pools.id as pool_id',
    'contacts.id as contact_id',
    'contacts.fullname as fullname',
    'contacts.email as email',
    'clients.phonenumber as phonenumber',
    'contacts.phonenumber as phonenumber_contact',
    DB::raw("null as last_call")
];

$query = Client::select($selects)
    ->where('clients.userid', '>', 0)
    ->where('clients.active', 1)
    ->whereNull('client_contact_pools.approved_at')
    ->whereDoesntHave('customerAdmin')
    ->whereDoesntHave('contact_requests', function ($query) {
        $query->whereIn('contact_requests.status', [
            FREE_DATA_REQUEST_STATUS_WAITING,
            FREE_DATA_REQUEST_STATUS_SA_APPROVED
        ]);
    });

$query
    ->join('client_contact_pools', 'clients.userid', '=', 'client_contact_pools.client_id')
    ->leftJoin('contacts', function ($join) {
        $join->on('contacts.userid', '=', 'clients.userid')
            ->where('contacts.is_primary', 1);
    });

// Eager load
$query->with([
    'latest_note' => function ($query) {
        $query->select('id', 'notes.rel_id', 'potential_rate', 'type', 'action', 'description', 'dateadded', 'addedfrom')
            ->whereIn('type', [FREE_DATA_NOTE_CONTACT_SUCCESS_TYPE, FREE_DATA_NOTE_CONTACT_LATER_TYPE])
            ->with('author:staffid,firstname,lastname');
    },
])
->withCount('contact_requests');

// Custom search value by company id, name
if ($company = $this->ci->input->post('company')) {
    if (is_numeric($company)) {
        $query->where('clients.userid', $company);
    } else {
        $query->where('clients.business_name', 'like', '%'.$company.'%');
    }
}
 
// Custom search value by request status
if ($status = $this->ci->input->post('company_status')) {
    $query->where(function ($query) {
        $query->select(DB::raw('COUNT(1)'))
            ->from('contact_requests')
            ->whereColumn('client_contact_pools.id', 'client_contact_pool_id');
    }, $status == 1 ? '=' : '>', 0);
}

// Custom search value by potential rate
if ($rate = $this->ci->input->post('potential')) {
    $query->whereHas('latest_note', function ($builder) use ($rate) {
        $builder->whereIn('type', [FREE_DATA_NOTE_CONTACT_SUCCESS_TYPE, FREE_DATA_NOTE_CONTACT_LATER_TYPE])
            ->where('potential_rate', $rate);
    });
}

$result = data_tables_eloquent_init($query, $sortFilterCols);

$overdueClientIds = [];
if (free_data_is_sale_role()) {
    $overdueClientIds = ContactTerminated::select('customer_id')
        ->where('staff_id', get_staff_user_id())
        ->whereRaw('datediff(created_at, DATE_SUB(now(), INTERVAL 60 DAY)) > ?', [0])
        ->get()->pluck('customer_id')->toArray();
}

$output  = $result['output'];
$rResult = $result['rResult'];
$noteActions = array_combine(array_pluck(ACTION_NOTE_OPTIONS, 'value'), array_pluck(ACTION_NOTE_OPTIONS, 'text'));
foreach ($rResult as $aRow) {
    $row = [];

    $row[] = $aRow['userid'];

    // Company
    $company  = $aRow['company'];
    $isPerson = false;

    if ($company == '') {
        $company  = _l('no_company_view_profile');
        $isPerson = true;
    }

    $phonenumber = $aRow['phonenumber'] != null ? $aRow['phonenumber'] : $aRow['phonenumber_contact'];

    $call_time = null;
    $duration = null;
    if ($aRow->last_call) {
        [$duration, $call_time] = explode('_', $aRow->last_call);
    }

    $url = admin_url('clients/client/' . $aRow['userid']);

    if ($isPerson && $aRow['contact_id']) {
        $url .= '?contactid=' . $aRow['contact_id'];
    }

    $company = '<a href="' . $url . '">' . $company . '</a><br>' .
        ($aRow['contact_id'] != null ? '<b>Contact: </b>' . $aRow['fullname'] . '<br>'  : '') .
        ($aRow['email'] != null ? '<b>Email: </b>' . $aRow['email'] . '<br>' : '') .
        ($phonenumber != null ? '<b>Phone: </b>' . $phonenumber . '<br>' : '') .
        ($call_time != null ? '<b>Last call: </b>' . $call_time . ' &#124; Duration: ' . $duration : '');

    $row[] = $company;

    // datecreated
    $row[] = _dt($aRow['datecreated']);

   $noteActions = array_combine(array_pluck(ACTION_NOTE_OPTIONS, 'value'), array_pluck(ACTION_NOTE_OPTIONS, 'text'));

    // Latest note contact
    $noteContact = '';
    if ($aRow->latest_note) {
        $note = $aRow->latest_note;
        $author = $note->author;
        $noteContact = ($author->firstname . ' ' . $author->lastname) . ' - ' . _d($note->dateadded) . '<br>' .
            ($note->type == FREE_DATA_NOTE_CONTACT_SUCCESS_TYPE ? _l('free_data_company_row_note_type', $noteActions[$note->action] ?? '') . '<br>' : '') .
            ($note->description != null ? _l('free_data_company_row_note_action', $note->description) . '<br>' : '');
    }
    $row[] = $noteContact;

    // Potential Rate
    $row[] = $aRow->latest_note && $aRow->latest_note->potential_rate ? FREE_DATA_NOTE_VISIBLE_POTENTIAL_RATES[$aRow->latest_note->potential_rate] : '';

    // Request status
    $isOvedued = in_array($aRow['userid'], $overdueClientIds);
    $requestStatus = '';
    if ($isOvedued) {
        $requestStatus = '<button class="btn btn-danger">' . _l('free_data_company_status_ovedued') . '</button>';
    } else {
        $requestTotals = $aRow['contact_requests_count'] ?? 0;
        if ($requestTotals) {
            $requestStatus = '<button class="btn btn-info">' . _l('free_data_company_row_' . ($requestTotals > 1 ? 'requests' : 'request') . '_status_requested', $requestTotals) . '</button>';
        } else {
            $requestStatus = '<button class="btn btn-success">' . _l('free_data_company_row_request_status_open') . '</button>';
        }
    }

    $row[] = $requestStatus;

    // Action: Only show if it's sale role
    if (free_data_is_sale_role()) {
        $requestAction = '';
        if ($isOvedued) {
            $requestAction = '<button class="btn btn-default" disabled="disabled">' . _l('free_data_company_row_action_ovedued') . '</button>';
        } else {
            $requestAction = '<a href="' . admin_url('free_data/create_request/' . $aRow['pool_id']) . '" class="btn btn-info">' . _l('free_data_company_row_action_request') . '</a>';
        }
        $row[] = $requestAction;
    }

    $output['aaData'][] = $row;
}

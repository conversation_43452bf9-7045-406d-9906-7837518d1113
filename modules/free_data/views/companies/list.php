<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
<div class="content">
   <div class="row">
      <div class="col-md-12">
         <div class="panel_s">
            <div class="panel-body">
               <div class="row company-search">
                  <div class="col-md-12 no-padding-left no-padding-right">
                     <div class="col-md-10 no-padding-left">
                        <div class="col-md-6 form-group">
                           <div class="input-group">
                              <span class="input-group-addon"><span class="fa fa-search"></span></span>
                              <input type="search" id="company" name="company" class="form-control input-sm" placeholder="<?= _l('free_data_company_search_input_placeholder') ?>">
                           </div>
                        </div>
                        <div class="col-md-12">
                           <div class="col-md-3" style="padding-left: 0">
                              <?= render_select('company_status', free_data_company_status(), ['id', 'text'], _l('free_data_company_status'), '', [/**'onchange' => "reloadDataTable('.table-invoices')" */]); ?>
                           </div>
                           <div class="col-md-3" style="padding-left: 0">
                              <?= render_select('potential', free_data_company_potential_rate(), ['id', 'text'], _l('free_data_company_potential'), '', [/**'onchange' => "reloadDataTable('.table-invoices')" */]); ?>
                           </div>
                        </div>
                     </div>
                     <?php if (free_data_is_sale_role()) : ?>
                     <div class="col-md-2 text-right">
                        <a href="<?= admin_url('free_data/my_request'); ?>" class="btn btn-info mbot15"><?= _l('free_data_my_request_btn') ?></a>
                     </div>
                     <?php endif; ?>
                  </div>
               </div>
               <?php render_datatable(free_data_get_company_grid_headers(), 'free-data'); ?>
            </div>
         </div>
      </div>
   </div>
</div>
<?php init_tail(); ?>
<script>
    $(function() {
      var customSearchParams = {};
      $.each($('._hidden_inputs._filters input'),function() {
         customSearchParams[$(this).attr('name')] = '[name="'+$(this).attr('name')+'"]';
      });
      customSearchParams['company'] = '[name="company"]';
      customSearchParams['company_status'] = '[name="company_status"]';
      customSearchParams['potential'] = '[name="potential"]';
      var tAPI
      // function initDataTable(selector, url, notsearchable, notsortable, fnserverparams, defaultorder);
      <?php if (free_data_is_sale_role()) : ?>
         tAPI = initDataTable('.table-free-data', window.location.href + '/table', [3,4,5,6], [3,4,5,6], customSearchParams, [0, 'desc']);
      <?php else: ?>
         tAPI = initDataTable('.table-free-data', window.location.href + '/table', [3,4,5], [3,4,5], customSearchParams, [0, 'desc']);
      <?php endif ?>;

      $('input[name="company"], select[name="company_status"], select[name="potential"]').on('change',function() {
           tAPI.ajax.reload();
       });
    });
</script>
</body>
</html>

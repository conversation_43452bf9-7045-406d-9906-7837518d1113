<?php

use Entities\ActivityLog;
use Entities\ClientContactPool;
use Entities\CustomerAdmin;

defined('BASEPATH') or exit('No direct script access allowed');

class Free_data_model extends App_Model
{
    protected string $clientContactTbl;
    protected string $requestTbl;
    protected string $contactTerminatedTbl;

    public function __construct()
    {
        parent::__construct();
        $this->clientContactTbl = db_prefix() . 'client_contact_pools';
        $this->requestTbl = db_prefix() . 'contact_requests';
        $this->contactTerminatedTbl = db_prefix() . 'contact_terminated';
    }

    /**
     * Check company is able to request
     * 1. Not ovedue
     * 2. Already requested
     * 3. Already approved
     * 
     * @param int $poolId client will be requested
     * 
     * @return boolean True: Able to request
     * @throws Exception
     */
    public function can_make_request($poolId)
    {
        $pool = $this->db
            ->where('id', $poolId)
            ->where('approved_at IS NULL')
            ->get($this->clientContactTbl)->row();

        if (!$pool) {
            throw new Exception(_l('free_data_exception_pool_not_found'));
        }
        
        $contractTerminated = $this->db
            ->select('customer_id')
            ->where([
                'customer_id' => $pool->client_id,
                'staff_id' => get_staff_user_id(),
                'datediff(created_at, DATE_SUB(now(), INTERVAL 60 DAY)) >' => 0
            ])
            ->get($this->contactTerminatedTbl)->row();

        // If have record that just created within 60 days then throw an exception
        // to prevent making request
        if ($contractTerminated) {
            throw new Exception(_l('free_data_exception_customer_is_ovedued'));
        }
        
        // $requested = $this->db
        //     ->select('id')
        //     ->where([
        //         'client_contact_pool_id' => $poolId,
        //         'staff_id' => get_staff_user_id(),
        //     ])
        //     ->get($this->requestTbl)->row();

        // // If already requested, then throw an exception to prevent making request
        // if ($requested) {
        //     throw new Exception(_l('free_data_exception_already_requested'));
        // }

        return true;
    }

    /**
     * Create new request that submit from the sale's form
     * 
     * @param int $poolId client pool id that will be requested
     * @param mixed $data form data
     * 
     * @return int|bool return inserted id
     */
    public function create($poolId, $data)
    {
        $insertData = [
            'client_contact_pool_id' => $poolId,
            'staff_id' => get_staff_user_id(),
            'content' => $data['content'],
            'customer_type' => $data['customer_type'],
            'link' => $data['link'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
        ];
        $this->db->insert($this->requestTbl, $insertData);
        $insert_id = $this->db->insert_id();
        if ($insert_id) {
            log_activity('New Free Data Request Added [ID:' . $insert_id . ']');
            return $insert_id;
        }
        return false;
    }

    /**
     * Update attachment if request has an attachment
     * @param int $requestId request id
     * @param string $requestUuid
     * @return bool
     */
    public function store_attachment($requestId, $attachmentUuid)
    {
        $this->db
            ->where('id', $requestId)
            ->update($this->requestTbl, [
                'attachment' => $attachmentUuid
            ]);

        if ($this->db->affected_rows() > 0) {
            log_activity('Free Data Request [ID:' . $requestId . ']');
            return true;
        }
        return false;
    }

    /**
     * Get all members of the leader by it's departments
     * @param int $leaderId
     * @return array staff ids
     */
    public function get_sale_members($leaderId)
    {
        $deptTable = db_prefix().'staff_departments';
        $staffIds = $this->db->select($deptTable . '.staffid as member_id')
        ->where($deptTable . '.departmentid IN (SELECT '.$deptTable . '.departmentid FROM '.$deptTable . ' WHERE staffid = ' . $leaderId . ')')
        ->where([
            db_prefix().'staff.role' => FREE_DATA_SALE_ROLE,
            db_prefix().'staff.active' => 1
            ])
            ->join($deptTable, $deptTable.'.staffid = '.db_prefix().'staff.staffid')
            ->get(db_prefix().'staff')
            ->result_array();
            
        return array_pluck($staffIds, 'member_id');
    }

    /**
     * Get all leader sale of the staff department
     * @param int $staffId
     * @return array leaderSale
     */
    public function get_sale_leaders($staffId)
    {
        $deptTable = db_prefix().'staff_departments';
        $leaderSale = $this->db
        ->where($deptTable . '.departmentid IN (SELECT '.$deptTable . '.departmentid FROM '.$deptTable . ' WHERE staffid = ' . $staffId . ')')
        ->where([
            db_prefix().'staff.role' => FREE_DATA_SALE_LEADER_ROLE,
            db_prefix().'staff.active' => 1
            ])
            ->join($deptTable, $deptTable.'.staffid = '.db_prefix().'staff.staffid')
            ->get(db_prefix().'staff')
            ->result_array();
            
        return $leaderSale;
    }

    /**
     * Check sale permission to approve request
     * @param $requestId request will be checked permission
     */
    public function admin_can_approve_request($requestId)
    {
        $request = $this->db
            ->select(
                join(',', [
                    'staff_id',
                    'client_contact_pool_id',
                    'client_id',
                    $this->clientContactTbl.'.approved_at'
                ])
            )
            ->join(
                $this->clientContactTbl,
                $this->clientContactTbl.'.id = '.$this->requestTbl.'.client_contact_pool_id'
            )
            ->where([
                $this->requestTbl.'.id' => $requestId,
                $this->requestTbl.'.status' => FREE_DATA_REQUEST_STATUS_WAITING,
            ])
            ->get($this->requestTbl)
            ->row();

        // Request is not approved by Sale Admin
        if (!$request) {
            throw new Exception(_l('free_data_exception_request_admin_not_found'));
        }

        // Request already approved by other
        if ($request->approved_at) {
            throw new Exception(_l('free_data_exception_request_aproved_already'));
        }

        return $request;
    }

    /**
     * Check sale permission to approve request
     * @param $requestId request will be checked permission
     */
    public function leader_can_approve_request($requestId)
    {
        $request = $this->db
            ->select(
                join(',', [
                    'staff_id',
                    'client_contact_pool_id',
                    'client_id',
                    $this->clientContactTbl.'.approved_at'
                ])
            )
            ->join(
                $this->clientContactTbl,
                $this->clientContactTbl.'.id = '.$this->requestTbl.'.client_contact_pool_id'
            )
            ->where([
                $this->requestTbl.'.id' => $requestId,
                $this->requestTbl.'.status' => FREE_DATA_REQUEST_STATUS_SA_APPROVED,
            ])
            ->get($this->requestTbl)
            ->row();

        // Request is not approved by Sale Admin
        if (!$request) {
            throw new Exception(_l('free_data_exception_request_not_found'));
        }

        // Request already approved by other
        if ($request->approved_at) {
            throw new Exception(_l('free_data_exception_request_aproved_already'));
        }

        $memberIds = $this->get_sale_members(get_staff_user_id());
        // Check leader can approve to members only
        if (!in_array($request->staff_id, $memberIds)) {
            throw new Exception(_l('free_data_exception_request_approve_member_only'));
        }

        return $request;
    }

    /**
     * Process approve status
     * @param int $requestId
     * @param mixed $request
     */
    public function process_leader_approve($requestId, $request)
    {
        // Change request status to approved
        $this->db
            ->where('id', $requestId)
            ->update($this->requestTbl, [
                'status' => FREE_DATA_REQUEST_STATUS_LEADER_APPROVED,
                'leader_id' => get_staff_user_id(),
                'leader_note' => nl2br($this->input->post('note'))
            ]);

        // Change client's pool to approved
        $resultChangeClients = $this->db
            ->where('id', $request->client_contact_pool_id)
            ->update($this->clientContactTbl, [
                'approved_at' => date('Y-m-d H:i:s')
            ]);
        
        // Send notification approved for staff members
        if ($resultChangeClients) {
            $company = $this->get_company_name_free_data($request->client_contact_pool_id);
            $this->notify_free_data(
                'free_data_notification_for_sale_with_sale_leader_approve',
                $request->staff_id,
                'free_data/my_request',
                [
                    $company
                ]
            );
        }

        // Assign customer admin to the client
        $this->load->model('clients_model');
        $this->clients_model->assign_admins(['customer_admins' => [$request->staff_id]], $request->client_id);

        // Reject other request of the same client
        $resultRejectRequest = $this->db
            ->where([
                'client_contact_pool_id' => $request->client_contact_pool_id,
                'id !=' => $requestId
            ])
            ->update($this->requestTbl, [
                'status' => FREE_DATA_REQUEST_STATUS_LEADER_REJECTED
            ]);
        
        // Send notification rejected for staff members
        if ($resultRejectRequest) {
            $getRejectRequest = $this->db
            ->where([
                'client_contact_pool_id' => $request->client_contact_pool_id,
                'id !=' => $requestId,
                'status' => FREE_DATA_REQUEST_STATUS_LEADER_REJECTED,
            ])
            ->get($this->requestTbl)
            ->result_array();

            if (!empty($getRejectRequest)) {
                $company = $this->get_company_name_free_data($request->client_contact_pool_id);
                foreach ($getRejectRequest as $value) {
                    $this->notify_free_data(
                        'free_data_notification_for_sale_with_sale_leader_reject',
                        $value['staff_id'],
                        'free_data/my_request',
                        [
                           $company 
                        ]
                    );
                }
            }
        }

        return _l('free_data_request_approve_success_message');
    }

    /**
     * Process approve status
     * @param int $requestId
     * @param mixed $request
     */
    public function process_admin_approve($requestId)
    {
        $dataContactRequests = $this->admin_can_approve_request($requestId);

        $res = $this->db
            ->where('id', $requestId)
            ->update($this->requestTbl, [
                'status' => FREE_DATA_REQUEST_STATUS_SA_APPROVED,
                'sa_approved_at' => date('Y-m-d H:i:s'),
                'admin_id' => get_staff_user_id(),
                'sa_note' => nl2br($this->input->post('note'))
            ]);

        if ($res && $dataContactRequests) {
            // Notification approval for staff member of free_data_request
            // Send notification for staff member of free_data_request
            $this->notify_for_sales_when_request($requestId, 'approved');

            //Send notification for staff leader of free_data_request
            $dataLeaders = $this->get_sale_leaders($dataContactRequests->staff_id);
            if (isset($dataLeaders)) {
                $fullName = get_staff_full_name($dataContactRequests->staff_id);
                $company = $this->get_company_name_free_data($dataContactRequests->client_contact_pool_id);
                foreach ($dataLeaders as $value) {
                    $this->notify_free_data(
                        'free_data_notification_for_leader_and_admin',
                        $value['staffid'],
                        'free_data/requests/waiting',
                        [
                            $company
                        ],
                        $fullName
                    );
                }
                unset($fullName);
            }
        }

        return _l('free_data_request_approve_success_message');
    }

    /**
     * Check sale permission to reject request
     * @param $requestId request will be checked permission
     */
    public function admin_can_reject_request($requestId)
    {
        $request = $this->db
            ->select(
                join(',', [
                    'staff_id',
                    'client_contact_pool_id',
                    'client_id',
                    $this->clientContactTbl.'.approved_at'
                ])
            )
            ->join(
                $this->clientContactTbl,
                $this->clientContactTbl.'.id = '.$this->requestTbl.'.client_contact_pool_id'
            )
            ->where([
                $this->requestTbl.'.id' => $requestId,
                $this->requestTbl.'.status' => FREE_DATA_REQUEST_STATUS_WAITING,
            ])
            ->get($this->requestTbl)
            ->row();

        // Request is not approved by Sale Admin
        if (!$request) {
            throw new Exception(_l('free_data_exception_request_admin_not_found'));
        }

        // Request already approved by other
        if ($request->approved_at) {
            throw new Exception(_l('free_data_exception_request_aproved_already'));
        }
    }

    /**
     * Check sale permission to reject request
     * @param $requestId request will be checked permission
     */
    public function leader_can_reject_request($requestId)
    {
        $request = $this->db
            ->select(
                join(',', [
                    'staff_id',
                    'client_contact_pool_id',
                    'client_id',
                    $this->clientContactTbl.'.approved_at'
                ])
            )
            ->join(
                $this->clientContactTbl,
                $this->clientContactTbl.'.id = '.$this->requestTbl.'.client_contact_pool_id'
            )
            ->where([
                $this->requestTbl.'.id' => $requestId,
                $this->requestTbl.'.status' => FREE_DATA_REQUEST_STATUS_SA_APPROVED,
            ])
            ->get($this->requestTbl)
            ->row();

        // Request is not approved by Sale Admin
        if (!$request) {
            throw new Exception(_l('free_data_exception_request_not_found'));
        }

        // Request already approved by other
        if ($request->approved_at) {
            throw new Exception(_l('free_data_exception_request_aproved_already'));
        }

        $memberIds = $this->get_sale_members(get_staff_user_id());
        // Check leader can reject to members only
        if (!in_array($request->staff_id, $memberIds)) {
            throw new Exception(_l('free_data_exception_request_reject_member_only'));
        }
    }

    /**
     * Check permission edit note
     * @param integer $requestId
     */
    public function can_edit_request_note($requestId)
    {
        $hasRecord = total_rows($this->requestTbl, [
            'id' => $requestId,
            (free_data_is_sale_leader_role() ? 'leader_id' : 'admin_id') => get_staff_user_id(),
        ]);

        if (!$hasRecord) {
            throw new Exception(_l('free_data_exception_request_cant_edit_note'));
        }
    }

    /**
     * Update note
     * @param integer $requestId request will be checked permission
     * @param string $note
     */
    public function edit_note($requestId, $note)
    {
        $this->db
            ->where('id', $requestId)
            ->update($this->requestTbl, [
                (free_data_is_sale_leader_role() ? 'leader_note' : 'sa_note') => nl2br($note)
            ]);

        return _l('free_data_update_note_success_message');
    }

    /**
     * Check sale permission to reject request
     * @param $requestId request will be checked permission
     */
    public function process_reject_request($requestId, $status = FREE_DATA_REQUEST_STATUS_SA_REJECTED)
    {
        $isSaRejected = $status == FREE_DATA_REQUEST_STATUS_SA_REJECTED;
        $this->db
            ->where('id', $requestId)
            ->update($this->requestTbl, [
                'status' => $status,
                'rejected_at' => date('Y-m-d H:i:s'),
                ($isSaRejected ? 'admin_id' : 'leader_id') => get_staff_user_id(),
                ($isSaRejected ? 'sa_note' : 'leader_note') => nl2br($this->input->post('note'))
            ]);
        
        return _l('free_data_request_reject_success_message');
    }

    /**
     * Check client already in the pool or not then allow assign or reject
     * @param int $clientId
     * @return bool True: Allow
     *              False: Not allow asign
     */
    public function allow_assign_customer_admin($clientId)
    {
        $clientPool = $this->db
            ->select('id')
            ->where([
                'client_id' => $clientId,
                'approved_at IS NOT NULL'
            ])
            ->get(db_prefix().'client_contact_pools')
            ->row();

        return empty($clientPool);
    }

    /**
     * Put clients in the free db pool to use in the free data feature
     * @param string $whereCondition condition is used to insert to table
     */
    public function store_free_data($whereCondition)
    {
        try {
            $sql = '
                INSERT INTO '.db_prefix().'client_contact_pools(client_id, created_at) 
                SELECT customer_id, "'.date('Y-m-d H:i:s').'" 
                    FROM '.db_prefix().'customer_admins WHERE '.$whereCondition.' 
                    GROUP BY customer_id
            ';
            $this->db->query($sql);
        } catch(Exception $ex) {
            log_message('error', $ex->getMessage());
        }
    }

    /**
     * Reject all request due to this client is assigned to sale
     * @param integer $clientId
     */
    public function reject_by_direct_assign($clientId)
    {
        $pool = $this->db
            ->select('id')
            ->where([
                'client_id' => $clientId
            ])
            ->where('approved_at IS NULL', null, false)
            ->get($this->clientContactTbl)
            ->row();

        if ($pool) {
            // Reject all requests
            $this->db
                ->where('client_contact_pool_id', $pool->id)
                ->where_in('status', ['waiting', 'sa_approved'])
                ->update($this->requestTbl, [
                    'status' => 'sa_rejected',
                    'admin_id' => get_staff_user_id()
                ]);

            // Change client's pool to approved
            $this->db
                ->where('id', $pool->id)
                ->update($this->clientContactTbl, [
                    'approved_at' => date('Y-m-d H:i:s')
                ]);
        }
    }

    /**
     * Get all stafff have to role admin
     * @param int $role
     * @return array
     */
    public function get_staff_admins()
    {
        $this->load->model('roles_model');
        return $this->roles_model->get_role_staff(FREE_DATA_SALE_ADMIN_ROLE);
    }

    /**
     * Get name company name from clinent contact table
     * @param int $poolId
     * @return string
     */
    public function get_company_name_free_data($poolId = null)
    {
        $dataClient = $this->db
            ->select(get_sql_select_client_company())
            ->from($this->clientContactTbl)
            ->join(db_prefix() . 'clients', $this->clientContactTbl.'.client_id = '.db_prefix() . 'clients.userid')
            ->where($this->clientContactTbl . '.id', $poolId)
            ->get()
            ->row();

        return !empty($dataClient) ? $dataClient->company ?? '' : '';
    }

    /**
     * @param int $id
     * @return array
     */
    public function get_contact_requests_free_data($id = null)
    {
        return $this->db
        ->select('client_contact_pool_id,staff_id')
        ->where('id', $id)
        ->get($this->requestTbl)
        ->row();
    }

    /**
     * Send pusher and add data to table notification
     * @param string $description
     * @param int $touserid
     * @param string $linkNoti
     * @param array $data
     * @param string $formFullname
     */
    public function notify_free_data(
        $description = '', 
        $touserid = null, 
        $linkNoti = '', 
        $data = [],
        $formFullname = ''
    )
    {
        $notified = add_notification([
            'fromuserid' => get_staff_user_id(),
            'description' => $description,
            'link' => $linkNoti,
            'touserid' => $touserid,
            'additional_data' => serialize($data),
            'from_fullname_notification_free_data' => $formFullname,
        ]);

        if ($notified) {
            pusher_trigger_notification([$touserid]);
        }
    }

    /**
     * Send the notification for admin sale
     * @param int $poolId
     */
    public function notify_for_adminSales_free_data($poolId = null)
    {
        $staffAdmins = $this->get_staff_admins();
        if (empty($staffAdmins)) return;
        $company = $this->get_company_name_free_data($poolId);
        foreach ($staffAdmins as $value) {
            $this->notify_free_data(
                'free_data_notification_for_leader_and_admin',
                $value['staffid'],
                'free_data/requests/waiting',
                [
                    $company
                ]
            );
        }
    }

    /**
     * Send the notification for admin sale
     * @param int $requestId
     * @param string $status rejected | approved
     */
    public function notify_for_sales_when_request($requestId = null, $status = 'rejected')
    {
        $dataContactRequests = $this->get_contact_requests_free_data($requestId);
        if (empty($dataContactRequests))
            return;
        $descriptionNoti = free_data_is_sale_leader_role() ?
            'free_data_notification_for_sale_with_sale_leader_reject' :
            'free_data_notification_for_sale_with_sale_admin_reject';

        if ($status != 'rejected') {
            $descriptionNoti = free_data_is_sale_leader_role() ?
                'free_data_notification_for_sale_with_sale_leader_approve' :
                'free_data_notification_for_sale_with_sale_admin_approve';
        }
        $company = $this->get_company_name_free_data($dataContactRequests->client_contact_pool_id);
        $this->notify_free_data(
            $descriptionNoti,
            $dataContactRequests->staff_id,
            'free_data/my_request',
            [
                $company
            ]
        );
    }

    public function insert_free_data($clientId)
    {
        $this->db->insert($this->clientContactTbl, [
            'client_id' => $clientId,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        $insert_id = $this->db->insert_id();
        if ($insert_id) {
            log_activity('New Free Data Request Added [ID:' . $insert_id . ']', null, $clientId, [], $insert_id, 'free_data');
            return $insert_id;
        }
        return false;
    }

    public function remove_approved_request_doesnt_have_contact_after_3_days()
    {
        $pools = ClientContactPool::query()
            ->select('client_id')
            ->whereHas('requests', function ($query) {
                $query->where('status', CLIENT_REQUEST_STATUS_LEADER_APPROVED);
            })
            ->whereNotNull('approved_at')
            ->where('approved_at', '>=', '2025-07-11 17:30:00')
            ->whereRaw('NOW() >= DATE_ADD(approved_at, INTERVAL 3 DAY)')
            ->whereNotExists(function ($query) {
                $query->from('notes')
                    ->whereRaw(db_prefix() . 'client_contact_pools.client_id = ' . db_prefix() . 'notes.rel_id')
                    ->where('notes.rel_type', 'customer')
                    ->where('notes.type', 1)
                    ->whereRaw(db_prefix() . 'notes.dateadded <= DATE_ADD(' . db_prefix() . 'client_contact_pools.approved_at, INTERVAL 3 DAY)');
            })
            ->get();

        $clientIds = $pools->pluck('client_id');

        if ($clientIds->isEmpty()) {
            return true;
        }

        try {
            DB::beginTransaction();

            // Update client's pool approved to null
            ClientContactPool::query()
                ->whereIn('client_id', $clientIds)
                ->update([
                    'approved_at' => null
                ]);

            // Remove customer admin
            CustomerAdmin::query()
                ->whereIn('customer_id', $clientIds)
                ->delete();

            // Log Activity
            ActivityLog::create([
                'description' => 'Free Data Remove Approved Request Doesnt Have Success Contact After 3 Days when Access Table',
                'date' => date('Y-m-d H:i:s'),
                'staffid' => get_staff_user_id(),
                'data' => $clientIds,
            ]);

            DB::commit();
        } catch (Exception $ex) {
            DB::rollBack();
            log_message('error', $ex->getMessage());
            return false;
        }

        return true;
    }
}
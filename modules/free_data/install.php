<?php

defined('BASEPATH') or exit('No direct script access allowed');

if (!$CI->db->table_exists(db_prefix() . 'client_contact_pools')) {
    $CI->db->query('CREATE TABLE `' . db_prefix() . "client_contact_pools` (
        `id` int NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT 'Primary Key',
        `client_id` int(11) NOT NULL COMMENT 'Foregin key to tblclients table',
        `created_at` DATETIME NOT NULL DEFAULT NOW() COMMENT 'Created date time',
        `approved_at` DATETIME DEFAULT NULL COMMENT 'Leader approved at',
        INDEX `client_contact_pools_client_id_idx` (`client_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set . ';
    ');

    // Import all active companies do not have customer admin to pool
    $CI->db->query('
        INSERT INTO '.db_prefix().'client_contact_pools(client_id)
        SELECT userid
        FROM '.db_prefix().'clients
        WHERE
            `active` = 1
            AND userid NOT IN (
                SELECT customer_id
                FROM tblcustomer_admins
                GROUP BY customer_id
            )
    ');

    // Create index for the notes
    $CI->db->query('CREATE INDEX idx_type ON ' . db_prefix() . 'notes(`type`)');
}
    
if (!$CI->db->table_exists(db_prefix() . 'contact_requests')) {
    $CI->db->query('CREATE TABLE `' . db_prefix() . "contact_requests` (
            `id` int NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT 'Primary Key',
            `client_contact_pool_id` int(11) NOT NULL COMMENT 'Foregin key to tblclient_contact_pools table',
            `staff_id` int(11) NOT NULL COMMENT 'Foregin key to tblstaff table',
            `leader_id` int(11) NULL COMMENT 'Foregin key to tblstaff table',
            `admin_id` int(11) NULL COMMENT 'Foregin key to tblstaff table',
            `status` enum('waiting', 'sa_approved', 'sa_rejected', 'leader_approved', 'leader_rejected') DEFAULT 'waiting' NOT NULL COMMENT 'Allow values: waiting, sa_approved, sa_rejected, leader_approved, leader_rejected',
            `content` text NOT NULL,
            `customer_type` enum('has_job_out_of_package', 'has_package_not_use', 'has_job_in_market', 'not_buy_has_demand', 'contacted_before', 'it_company', 'recruited_before', 'recruit_demand') NOT NULL COMMENT 'Allow values: has_job_out_of_package, has_package_not_use, has_job_in_market, not_buy_has_demand, contacted_before, it_company, recruited_before, recruit_demand',
            `link` varchar(255) NULL,
            `attachment` varchar(255) NULL,
            `created_at` DATETIME NOT NULL DEFAULT NOW() COMMENT 'Created date time',
        INDEX `contact_requests_client_contact_pool_id_idx` (`client_contact_pool_id`),
        INDEX `contact_requests_staff_id_idx` (`staff_id`),
        INDEX `contact_requests_leader_id_idx` (`leader_id`),
        INDEX `contact_requests_admin_id_idx` (`admin_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set . ';');
}

// Set permission based on group
$CI->load->model('roles_model');

// Sale role
$saleRole = json_decode(json_encode($CI->roles_model->get(FREE_DATA_SALE_ROLE)), true);
$saleRolePermission = $saleRole['permissions'];
if (!isset($saleRolePermission[FREE_DATA_MODULE_NAME])) {
    $saleRolePermission[FREE_DATA_MODULE_NAME] = [
        'access',
        'view_own',
        'create',
        'edit',
        'delete'
    ];
    $saleRole['permissions'] = $saleRolePermission;
    $CI->roles_model->update($saleRole, FREE_DATA_SALE_ROLE);
}

// Sale Leader role
$saleLeaderRole = json_decode(json_encode($CI->roles_model->get(FREE_DATA_SALE_LEADER_ROLE)), true);
$saleLeaderRolePermission = $saleLeaderRole['permissions'];
if (!isset($saleLeaderRolePermission[FREE_DATA_MODULE_NAME])) {
    $saleLeaderRolePermission[FREE_DATA_MODULE_NAME] = [
        'access',
        'view_own',
        'approve_own',
    ];
    $saleLeaderRole['permissions'] = $saleLeaderRolePermission;
    $CI->roles_model->update($saleLeaderRole, FREE_DATA_SALE_LEADER_ROLE);
}

// Sale Admin role
$saleAdminRole = json_decode(json_encode($CI->roles_model->get(FREE_DATA_SALE_ADMIN_ROLE)), true);
$saleAdminRolePermission = $saleAdminRole['permissions'];
if (!isset($saleAdminRolePermission[FREE_DATA_MODULE_NAME])) {
    $saleAdminRolePermission[FREE_DATA_MODULE_NAME] = [
        'access',
        'view_global',
        'approve_global',
    ];
    $saleAdminRole['permissions'] = $saleAdminRolePermission;
    $CI->roles_model->update($saleAdminRole, FREE_DATA_SALE_ADMIN_ROLE);
}

// Admin role
$adminRole = json_decode(json_encode($CI->roles_model->get(FREE_DATA_ADMIN_ROLE)), true);
$adminRolePermission = $adminRole['permissions'];
if (!isset($adminRolePermission[FREE_DATA_MODULE_NAME])) {
    $adminRolePermission[FREE_DATA_MODULE_NAME] = [
        'access',
        'view_own',
        'view_global',
        'approve_own',
        'approve_global',
        'create',
        'edit',
        'delete'
    ];
    $adminRole['permissions'] = $adminRolePermission;
    $CI->roles_model->update($adminRole, FREE_DATA_ADMIN_ROLE);
}
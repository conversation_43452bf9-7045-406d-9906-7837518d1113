<?php

# Version 2.3.0

$lang['auto_backup_options_updated']     = 'Auto backup alternativ uppdateras';
$lang['auto_backup_every']               = 'Skapa en backup varje X dag';
$lang['auto_backup_enabled']             = 'Aktiv (Kräver Cron)';
$lang['auto_backup']                     = 'Auto backup';
$lang['backup_delete']                   = 'Backup Raderad';
$lang['backup_success']                  = 'Backup är gjord';
$lang['utility_backup']                  = 'Databas Backup';
$lang['utility_create_new_backup_db']    = 'Skapa Databas Backup';
$lang['utility_backup_table_backupname'] = 'Backup';
$lang['utility_backup_table_backupsize'] = 'Backup storlek';
$lang['utility_backup_table_backupdate'] = 'Datum';
$lang['utility_db_backup_note']          = 'Obs: På grund av den tid och det minne som är tillgängligt för PHP är möjligheten att utföra backup begränsad, att säkerhetskopiera mycket stora databaser kan ibland inte vara möjligt. Om databasen är mycket stor kan du behöva säkerhetskopiera direkt från din SQL Server via kommandoraden , eller låta din server admin göra det åt dig om du inte har administratörsbehörighet..';
$lang['delete_backups_older_then']       = 'Automatiskt ta bort säkerhetskopior äldre än X dagar (ange 0 för att avaktivera)';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

<?php

# Version 2.3.0

$lang['auto_backup_options_updated']     = 'Auto Backup Optionen aktualisiert';
$lang['auto_backup_every']               = 'Backup alle X Tage erstellen';
$lang['auto_backup_enabled']             = 'Aktiviert (braucht Cron)';
$lang['auto_backup']                     = 'Auto Backup';
$lang['backup_delete']                   = 'Backup gelöscht';
$lang['backup_success']                  = 'Backup erfolgreich erstellt';
$lang['utility_backup']                  = 'Datenbank Backup';
$lang['utility_create_new_backup_db']    = 'Datenbank Backup erstellen';
$lang['utility_backup_table_backupname'] = 'Backup';
$lang['utility_backup_table_backupsize'] = 'Backup Größe';
$lang['utility_backup_table_backupdate'] = 'Datum';
$lang['utility_db_backup_note']          = 'Notiz: Wegen Limits der Ausführungszeit und verfügbarem PHP-RAM, könnte das Backup von sehr große DBs nicht funktionieren. Wenn die Datenbank sehr groß ist, empfiehlt es sich, das Backup direkt vom SQL Server oder via CLI zu erstellen. Alternativ sollte das der Server Admin tun.';
$lang['delete_backups_older_then']       = 'Backups älter als X Tage automatisch löschen (0 zum deaktivieren)';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

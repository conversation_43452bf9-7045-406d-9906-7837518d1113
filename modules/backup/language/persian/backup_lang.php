<?php

# Version 2.3.0

$lang['auto_backup_options_updated']     = 'گزينه پشتيبان گيري خودکار به روز رساني شد';
$lang['auto_backup_every']               = 'پشتيان گيري را هر X روز يکبار انجام بده';
$lang['auto_backup_enabled']             = 'فعال (موردنياز Cron)';
$lang['auto_backup']                     = 'پشتيبان گيري خودکار';
$lang['backup_delete']                   = 'پشتيبان گيري حذف شد';
$lang['backup_success']                  = 'پشتيبان گيري با موفقيت انجام شد';
$lang['utility_backup']                  = 'پشتيبان گيري پايگاه داده';
$lang['utility_create_new_backup_db']    = 'ايجاد پشتيبان گيري پايگاه داده';
$lang['utility_backup_table_backupname'] = 'پشتيبان گيري';
$lang['utility_backup_table_backupsize'] = 'اندازه پشتيبان';
$lang['utility_backup_table_backupdate'] = 'تاريخ';
$lang['utility_db_backup_note']          = 'توجه: به دليل زمان و حافظه محدود موجود در پي اچ پي پشتيبان گيري از پايگاه هاي داده بسيار بزرگ امکان پذير نيست. اگر پايگاه داده شما بسيار بزرگ است بهتر است به طور مستقيم از SQL Server با استفاده از خط فرمان پشتيبان تهيه کنيد يا اگر دسترسي نداريد مدير سرور اين کار را براي شما انجام دهد.  ، ';
$lang['delete_backups_older_then']       = 'Auto delete backups older then X days (set 0 to disable)';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

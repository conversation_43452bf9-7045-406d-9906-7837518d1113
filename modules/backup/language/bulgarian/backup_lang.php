<?php

# Version 2.3.0

$lang['auto_backup_options_updated']     = 'Auto backup options updated';
$lang['auto_backup_every']               = 'Create backup every X days';
$lang['auto_backup_enabled']             = 'Enabled (Requires Cron)';
$lang['auto_backup']                     = 'Автоматичен бекъп';
$lang['backup_delete']                   = 'Backup Deleted';
$lang['backup_success']                  = 'Backup is made successfuly';
$lang['utility_backup']                  = 'Бекъп Мениджър';
$lang['utility_create_new_backup_db']    = 'Направи Бекъп';
$lang['utility_backup_table_backupname'] = 'Backup';
$lang['utility_backup_table_backupsize'] = 'Backup size';
$lang['utility_backup_table_backupdate'] = 'Date';
$lang['utility_db_backup_note']          = 'Note: Due to the limited execution time and memory available to PHP, backing up very large databases may not be possible. If your database is very large you might need to backup directly from your SQL server via the command line, or have your server admin do it for you if you do not have root privileges.';
$lang['delete_backups_older_then']       = 'Auto delete backups older then X days (set 0 to disable)';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

<?php

# Version 2.3.0

$lang['delete_backups_older_then']       = 'Automatycznie kasuj kopie zapasowe starsze niż X dni (wpisz 0 aby wy<PERSON><PERSON><PERSON>)';
$lang['auto_backup_options_updated']     = 'Opcje automatycznej kopii zapasowej zostały zaktualizowane';
$lang['auto_backup_every']               = 'Utwórz kopię zapasową co X dni';
$lang['auto_backup_enabled']             = 'Właczone (wymaga terminarza zadań)';
$lang['auto_backup']                     = 'Automatyczna kopia zapasowa';
$lang['backup_delete']                   = 'Kopia zapasowa została usunięta';
$lang['backup_success']                  = 'Kopia zapasowa została utworzona';
$lang['utility_backup']                  = 'Kopia zapasowa bazy danych';
$lang['utility_create_new_backup_db']    = 'Utwórz kopię zapasową bazy danych';
$lang['utility_backup_table_backupname'] = 'Kopia zapasowa';
$lang['utility_backup_table_backupsize'] = 'Rozmiar kopii zapasowej';
$lang['utility_backup_table_backupdate'] = 'Data';
$lang['utility_db_backup_note']          = 'Uwaga: Z powodu ograniczonego czasu wykonania i pamięci dostępnej dla PHP, tworzenie kopii zapasowych bardzo dużych baz danych może nie być możliwe. Jeśli twoja baza danych jest bardzo duża, możesz potrzebować kopii zapasowej bezpośrednio z serwera SQL za pomocą wiersza poleceń lub zlecić administratorowi serwera, jeśli nie masz uprawnień roota.';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

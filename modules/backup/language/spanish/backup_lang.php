<?php

# Version 2.3.0

$lang['auto_backup_options_updated']     = 'Opciones de copia de seguridad automática actualizada';
$lang['auto_backup_every']               = 'Crear copia de seguridad cada X días';
$lang['auto_backup_enabled']             = 'Habilitado (requiere Cron)';
$lang['auto_backup']                     = 'Copia de seguridad automática';
$lang['backup_delete']                   = 'Copia eliminado';
$lang['backup_success']                  = 'La copia de seguridad se realizo con éxito';
$lang['utility_backup']                  = 'Copia de seguridad de base de datos';
$lang['utility_create_new_backup_db']    = 'Crear copia de seguridad de base de datos';
$lang['utility_backup_table_backupname'] = 'Copia de seguridad';
$lang['utility_backup_table_backupsize'] = 'El tamaño de la copia de seguridad';
$lang['utility_backup_table_backupdate'] = 'Fecha';
$lang['utility_db_backup_note']          = 'Nota: Debido al tiempo de ejecución limitado y memoria disponible para pHP, las copias de seguridad de una base de datos muy grande pueden no ser posibles. Si su base de datos es muy grande, es posible que necesite hacer copias de seguridad directamente desde su servidor sQL a través de la línea de comandos, o que su administrador del servidor lo haga por usted.';
$lang['delete_backups_older_then']       = 'Borrado automático de backups con más de X días de antigüedad (0 para desactivar)';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

<?php

# Version 2.3.0
$lang['auto_backup_options_updated']     = 'Nastavení automatických záloh aktualizováno';
$lang['auto_backup_every']               = 'Vytvořit zálohu každých X dní';
$lang['auto_backup_enabled']             = 'Povoleno (vyž<PERSON><PERSON><PERSON>)';
$lang['auto_backup']                     = 'Automatická záloha';
$lang['backup_delete']                   = 'Záloha odstraněna';
$lang['backup_success']                  = 'Záloha úspěšně vytvořena';
$lang['utility_backup']                  = 'Záloha databáze';
$lang['utility_create_new_backup_db']    = 'Vytvořit zálohu databáze';
$lang['utility_backup_table_backupname'] = 'Záloha';
$lang['utility_backup_table_backupsize'] = 'Velikost zálohy';
$lang['utility_backup_table_backupdate'] = 'Datum';
$lang['utility_db_backup_note']          = 'Pozn.: Vzhledem k limitům PHP nemusí být zálohování příliš velké databáze možné. Pokud je databáze příliš velká, budete muset provést zálohu přímo z SQL serveru pomocí příkazového řádku nebo pořádat svého administrátora.';
$lang['delete_backups_older_then']       = 'Automaticky odstranit zálohu starší než X dní (nastavte 0 pro vypnutí)';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

<?php

# Version 2.3.0
$lang['delete_backups_older_then']       = 'Eliminazione automatica di backup più vecchi di X giorni (impostare 0 per disabilitare)';
$lang['auto_backup_every']               = 'Crea backup ogni X giorni';
$lang['auto_backup_enabled']             = '<PERSON><PERSON><PERSON> (<PERSON><PERSON>)';
$lang['auto_backup']                     = 'Auto backup';
$lang['backup_delete']                   = 'Backup Eliminato';
$lang['backup_success']                  = 'Backup eseguito con successo';
$lang['utility_backup']                  = 'Backup Database';
$lang['utility_create_new_backup_db']    = 'Crea Backup Database';
$lang['utility_backup_table_backupname'] = 'Backup';
$lang['utility_backup_table_backupsize'] = 'Dimensione Backup';
$lang['utility_backup_table_backupdate'] = 'Data';
$lang['utility_db_backup_note']          = 'Nota: Dato il limite del tempo di esecuzione e la memoria disponibile per PHP, potrebbe non essere possibile eseguire il backup di un database molto grande. Se il database è molto grande potrebbe essere necessario eseguire il backup direttamente dal server SQL tramite la riga di comando, o il vostro amministratore di server potrebbe farlo per voi, se non si dispone di privilegi di root.';
$lang['auto_backup_options_updated']     = 'Opzione backup automatico aggiornata';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

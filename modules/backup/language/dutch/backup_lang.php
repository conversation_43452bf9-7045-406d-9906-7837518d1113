<?php

# Version 2.3.0
$lang['delete_backups_older_then']       = 'Verwijder automatisch back-ups ouder dan (dagen) </br><small>gebruik 0 om uit te schakelen</small>';
$lang['auto_backup_every']               = 'Maak back-up elke (dagen)';
$lang['auto_backup_enabled']             = 'Automatische back-up inschakelen (heeft cronjob nodig)';
$lang['auto_backup']                     = 'Automatische back-up';
$lang['backup_delete']                   = 'Back-up is verwijderd';
$lang['backup_success']                  = 'Back-up is gemaakt';
$lang['utility_backup']                  = 'Database back-up';
$lang['utility_create_new_backup_db']    = 'Maak database back-up';
$lang['utility_backup_table_backupname'] = 'Back-up';
$lang['utility_backup_table_backupsize'] = 'Back-up grootte';
$lang['utility_backup_table_backupdate'] = 'Datum';
$lang['utility_db_backup_note']          = 'Let op: vanwege de beperkte uitvoeringstijd en het beschikbare geheugen voor PHP, is het maken van back-ups van zeer grote databases in sommige gevallen niet mogelijk. Wanneer de database erg groot is, moet er mogelijk een rechtstreekse back-up vanaf de SQL-server gemaakt worden. Laat je server beheerder dit doen wanneer je geen root rechten hebt.';
$lang['auto_backup_options_updated']     = 'Automatische back-up bijgewerkt';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

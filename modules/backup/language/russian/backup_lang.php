<?php

# Version 2.3.0

$lang['auto_backup_options_updated']     = 'Обновлены параметры автоматического резервного копирования';
$lang['auto_backup_every']               = 'Создавать резервную копию каждые (в днях)';
$lang['auto_backup_enabled']             = 'Включен (необходим Cron)';
$lang['auto_backup']                     = 'Автобэкап';
$lang['backup_delete']                   = 'Резервная копия удалена';
$lang['backup_success']                  = 'Резервное копирование выполнено успешно';
$lang['utility_backup']                  = 'Резервное копирование базы данных';
$lang['utility_create_new_backup_db']    = 'Создать резервную копию базы данных';
$lang['utility_backup_table_backupname'] = 'Резервная копия';
$lang['utility_backup_table_backupsize'] = 'Размер резервной копии';
$lang['utility_backup_table_backupdate'] = 'Дата';
$lang['utility_db_backup_note']          = 'Примечание: Из-за ограничений времени выполнения и памяти доступных для PHP, резервное копирование очень больших баз данных может оказаться невозможным. Если ваша база данных очень велика, вам может потребоваться резервное копирование непосредственно с вашего SQL-сервера через командную строку, а если у вас нет root-привилегий, попросите администратора вашего сервера сделать это для вас.';
$lang['delete_backups_older_then']       = 'Через сколько дней удалять копии (установите 0 для отключения)?';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

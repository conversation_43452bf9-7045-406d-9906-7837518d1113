<?php

# Version 2.3.0

$lang['delete_backups_older_then']       = 'Suppression automatique des backup vieux de X jours ( Mettre 0 pour désactiver)';
$lang['auto_backup_options_updated']     = 'Sauvegarde automatique des options mise à jour';
$lang['auto_backup_every']               = 'Créer une sauvegarde tous les X jours';
$lang['auto_backup_enabled']             = 'Activer (beso<PERSON> <PERSON>)';
$lang['auto_backup']                     = 'Sauvegarde automatique';
$lang['backup_delete']                   = 'Sauvegarde supprimée';
$lang['backup_success']                  = 'Sauvegarde effectuée avec succès';
$lang['utility_backup']                  = 'Sauvegarde';
$lang['utility_create_new_backup_db']    = 'Créer la sauvegarde de la base de données';
$lang['utility_backup_table_backupname'] = 'Sauvegarde';
$lang['utility_backup_table_backupsize'] = 'Taille';
$lang['utility_backup_table_backupdate'] = 'Date';
$lang['utility_db_backup_note']          = 'Remarque: en raison du temps d\'exécution limité et de la mémoire PHP disponible, sauvegarder de très grandes bases de données peut être impossible. Si votre base de données est très grande vous pourriez avoir à la sauvegarder à partir de votre panneau d\'administration serveur SQLle font pour vous si vous n\'avez pas de privilèges de racine.';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

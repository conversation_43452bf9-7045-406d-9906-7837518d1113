<?php

# Version 2.3.0
$lang['auto_backup_options_updated']     = 'Otomatik Yedekleme seçenekleri güncellendi';
$lang['auto_backup_every']               = 'Her X günde yedekleme oluştur';
$lang['auto_backup_enabled']             = 'Aktif (Cron Gerekli)';
$lang['auto_backup']                     = 'Otomatik Yedekleme';
$lang['backup_delete']                   = 'Yedek Silindi';
$lang['backup_success']                  = 'Yedekleme Başarılı';
$lang['utility_backup']                  = 'Veritabanı Yedekleme';
$lang['utility_create_new_backup_db']    = 'Veritabanı Yedekleme Oluştur';
$lang['utility_backup_table_backupname'] = 'Yedekleme';
$lang['utility_backup_table_backupsize'] = 'Yedekleme Boyutu';
$lang['utility_backup_table_backupdate'] = 'Tarih';
$lang['utility_db_backup_note']          = 'Not: PHP için sınırlı yürütme süresi ve belleği nedeniyle, çok büyük veritabanlarının yedeklenmesi mümkün olmayabilir. Veritabanınız çok büyükse komut satırından doğrudan SQL sunucunuzdan yedekleme yapmanız gerekebilir veya root yetkiniz yoksa sunucu yöneticiniz sizin için yapabilir.';
$lang['delete_backups_older_then']       = 'Yedeklemeleri daha sonra X gününden önce otomatik olarak sil (devre dışı bırakmak için 0 olarak ayarlayın)';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

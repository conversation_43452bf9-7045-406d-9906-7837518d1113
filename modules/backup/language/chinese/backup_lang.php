<?php

# Version 2.3.0
$lang['auto_backup_options_updated']     = '自动备份选项更新';
$lang['auto_backup_every']               = '每隔几天创建一个备份';
$lang['auto_backup_enabled']             = '启用(需要Cron)';
$lang['auto_backup']                     = '自动备份';
$lang['backup_delete']                   = '备份删除';
$lang['backup_success']                  = '备份是成功的';
$lang['utility_backup']                  = '数据库备份';
$lang['utility_create_new_backup_db']    = '创建数据库备份';
$lang['utility_backup_table_backupname'] = '备份';
$lang['utility_backup_table_backupsize'] = '备份大小';
$lang['utility_backup_table_backupdate'] = '日期';
$lang['utility_db_backup_note']          = '注意:由于有限的执行时间和内存可用的PHP,备份非常大的数据库是不可能的。如果您的数据库非常大,您可能需要备份通过命令行直接从您的SQL服务器,或有你的服务器管理员帮你，如果你没有根特权。';
$lang['delete_backups_older_then']       = 'Auto delete backups older then X days (set 0 to disable)';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

<?php

# Version 2.3.0

$lang['auto_backup_options_updated']     = 'Nastavenie automatických záloh bolo aktualizované';
$lang['auto_backup_every']               = 'Vytvoriť zálohu každých X dní';
$lang['auto_backup_enabled']             = 'Povolené (v<PERSON><PERSON><PERSON><PERSON><PERSON>)';
$lang['auto_backup']                     = 'Automatická záloha';
$lang['backup_delete']                   = 'Záloha bola odstránená';
$lang['backup_success']                  = 'Z<PERSON>loha bola úspešne vytvorená';
$lang['utility_backup']                  = 'Záloha databázy';
$lang['utility_create_new_backup_db']    = 'Vytvoriť zálohu databázy';
$lang['utility_backup_table_backupname'] = 'Záloha';
$lang['utility_backup_table_backupsize'] = 'Veľkosť zálohy';
$lang['utility_backup_table_backupdate'] = 'Dátum';
$lang['utility_db_backup_note']          = 'Pozn.: Vzhľadom k limitom PHP nemusí byť zálohovanie príliš veľkej databázy možné. Ak je databáza príliš veľká, budete musieť vykonať zálohu priamo z SQL servera pomocou príkazového riadku alebo požiadať svojho administrátora.';
$lang['delete_backups_older_then']       = 'Automaticky odstrániť zálohu staršiu ako X dní (nastavte 0 pre vypnutie)';
$lang['auto_backup_hour']                = 'Hour of day to perform backup';

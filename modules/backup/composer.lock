{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "64f5f2ff7fde7911ffe7e20d85b56de6", "packages": [{"name": "backup-manager/backup-manager", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/backup-manager/backup-manager.git", "reference": "2176c335e0a3a3f65da4f3966a5336856206a86b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/backup-manager/backup-manager/zipball/2176c335e0a3a3f65da4f3966a5336856206a86b", "reference": "2176c335e0a3a3f65da4f3966a5336856206a86b", "shasum": ""}, "require": {"league/flysystem": "^1.0", "php": "^5.5.9 || ^7.0", "symfony/process": "^2.1 || ^3.0 || ^4.0"}, "require-dev": {"aws/aws-sdk-php": "~3.0", "dropbox/dropbox-sdk": "~1.1", "league/flysystem-aws-s3-v3": "~1.0", "league/flysystem-dropbox": "~1.0", "league/flysystem-rackspace": "~1.0", "league/flysystem-sftp": "~1.0", "mhetreramesh/flysystem-backblaze": "~1.0", "mockery/mockery": "~0.9", "phpspec/phpspec": "~2.1", "satooshi/php-coveralls": "~0.6", "srmklive/flysystem-dropbox-v2": "~1.0", "superbalist/flysystem-google-storage": "^7.0"}, "suggest": {"league/flysystem-aws-s3-v3": "AWS S3 adapter support.", "league/flysystem-rackspace": "Rackspace adapter support.", "league/flysystem-sftp": "SFTP adapter support.", "mhetreramesh/flysystem-backblaze": "B2 adapter support.", "srmklive/flysystem-dropbox-v2": "Dropbox API v2 adapter support.", "superbalist/flysystem-google-storage": "Google Cloud Storage adapter support."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"BackupManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://heybigname.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://heybigname.com/"}], "description": "A framework agnostic database backup manager with user-definable procedures and support for S3, Dropbox, FTP, SFTP, and more with drivers for popular frameworks.", "time": "2018-10-16T19:36:05+00:00"}, {"name": "league/flysystem", "version": "1.0.50", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "dab4e7624efa543a943be978008f439c333f2249"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/dab4e7624efa543a943be978008f439c333f2249", "reference": "dab4e7624efa543a943be978008f439c333f2249", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": ">=5.5.9"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7.10"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "time": "2019-02-01T08:50:36+00:00"}, {"name": "symfony/process", "version": "v3.4.22", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "009f8dda80930e89e8344a4e310b08f9ff07dd2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/009f8dda80930e89e8344a4e310b08f9ff07dd2e", "reference": "009f8dda80930e89e8344a4e310b08f9ff07dd2e", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2019-01-16T13:27:11+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": []}
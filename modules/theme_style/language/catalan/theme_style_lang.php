<?php

#  Version 2.3.0
$lang['theme_style']                            = 'Estil del Tema';

# Version 2.3.5
$lang['theme_style_reset_info']            = 'This action does not reset the Custom CSS section.';
$lang['theme_style_admin']                 = 'Admin Area';
$lang['theme_style_customers']             = 'Customers Area';
$lang['theme_style_buttons']               = 'Buttons';
$lang['theme_style_tabs']                  = 'Tabs';
$lang['theme_style_modals']                = 'Modals';
$lang['theme_style_general']               = 'General';
$lang['theme_style_custom_css']            = 'Custom CSS';
$lang['theme_style_example_modal_heading'] = 'Example Modal Heading';
$lang['theme_style_sample_text']           = 'Sample Text';
$lang['theme_style_modal_body']            = 'Modal Body';
$lang['theme_style_customers_and_admin']   = 'Customers and Admin Area';
$lang['theme_style_ca_info']               = 'Custom CSS to use in both admin and customers area.';

# Be careful how you translate the translations below to prevent any confusions
# if you don't know what you are doing, leave them un-translated

$lang['theme_style_sidebar_bg_color']                    = 'Sidebar Menu/Setup Menu Background Color';
$lang['theme_style_sidebar_open_bg_color']               = 'Sidebar Menu/Setup Menu Submenu Open Background Color';
$lang['theme_style_sidebar_links_color']                 = 'Sidebar Menu/Setup Menu Links Color';
$lang['theme_style_sidebar_user_welcome_text_color']     = 'Sidebar Menu User Welcome Text Color';
$lang['theme_style_sidebar_active_item_bg_color']        = 'Sidebar Menu/Setup Active Item Background Color';
$lang['theme_style_sidebar_active_item_color']           = 'Sidebar Menu/Setup Active Item Color';
$lang['theme_style_sidebar_active_sub_item_bg_color']    = 'Sidebar Menu/Setup Active Subitem Background Color';
$lang['theme_style_sidebar_active_sub_item_links_color'] = 'Sidebar Menu/Setup Submenu Links Color';
$lang['theme_style_top_header_bg_color']                 = 'Top Header Background Color';
$lang['theme_style_top_header_bg_links_color']           = 'Top Header Links Color';
$lang['theme_style_navigation_bg_color']                 = 'Navigation Background Color';
$lang['theme_style_navigation_link_color']               = 'Navigation Links Color';
$lang['theme_style_footer_background']                   = 'Footer Background';
$lang['theme_style_footer_text_color']                   = 'Footer Text Color';
$lang['theme_style_links']                               = 'Links';
$lang['theme_style_color']                               = 'Color';
$lang['theme_style_link_hover_color']                    = 'Links Hover/Focus Color';
$lang['theme_style_table_headings_color']                = 'Table Headings Color';
$lang['theme_style_example_table_heading']               = 'Example Heading';
$lang['theme_style_admin_login_background']              = 'Admin Login Background';
$lang['theme_style_text_muted']                          = 'Text Muted';
$lang['theme_style_text_danger']                         = 'Text Danger';
$lang['theme_style_text_warning']                        = 'Text Warning';
$lang['theme_style_text_info']                           = 'Text Info';
$lang['theme_style_text_success']                        = 'Text Success';
$lang['theme_style_example_text']                        = 'Example %s';
$lang['theme_style_tabs_bg_color']                       = 'Tabs Background Color';
$lang['theme_style_tabs_links_color']                    = 'Tabs Links Color';
$lang['theme_style_tabs_active_links_color']             = 'Tabs Link Active/Hover Color';
$lang['theme_style_tabs_active_border_color']            = 'Tabs Active Border Color';
$lang['theme_style_modal_heading_bg']                    = 'Heading Background';
$lang['theme_style_modal_heading_color']                 = 'Heading Color';
$lang['theme_style_modal_close_btn_color']               = 'Close Button Color';
$lang['theme_style_modal_white_text_color']              = 'Modal White Text Color';
$lang['theme_style_button_default']                      = 'Button Default';
$lang['theme_style_button_info']                         = 'Button Info';
$lang['theme_style_button_success']                      = 'Button Success';
$lang['theme_style_button_danger']                       = 'Button Danger';
$lang['theme_style_modal_header_text_color']             = 'Modal Header Text Color';

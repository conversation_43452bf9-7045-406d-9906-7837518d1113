<?php

use Entities\Item;
use Entities\M3cCall;
use Entities\Note;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Class Goals_model
 * @property CI_DB $db
 */
class Goals_model extends App_Model
{
    protected $role_sale = 1;
    protected $role_sales = [1, 4, 7];

    public function __construct()
    {

        parent::__construct();
    }

    public function data_convert_sale_agent($data)
    {
        // Sử dụng array_column() để tạo một mảng chỉ bao gồm giá trị staff_id
        $staffIds = array_column($data, 'staff_id');

        // Kết hợp mảng $staffIds và $data để tạo mảng mới
        $result = array_combine($staffIds, $data);

        return $result;
    }


    public function staticst_day_off_day($time)
    {

        $this->db->select('staff_id, sum(total_days) as total_days');
        $this->db->from(db_prefix() . 'kpi_day_off as o');

        $this->db->where('date >= "'. $time['from'].'"');
        $this->db->where('date <= "'. $time['to'].'"');
        $this->db->group_by('staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);
        return $data;
    }

    // Thống kê theo người tạo
    public function closed_revenue($time)
    {

        $this->db->select('e.addedfrom as staff_id, count(id) as count, sum(total-total_tax) as total');
        $this->db->from(db_prefix() . 'invoices as e');
        $this->db->where('e.date >= "'. $time['from'].'"');
        $this->db->where('e.date <= "'. $time['to'].'"');
        $this->db->where('e.total >', 0);
        $this->db->where('e.status !=', 5);

        $this->db->group_by('staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);
        return $data;
    }


    public function closed_revenue_new($time)
    {

        $this->db->select('i.addedfrom as staff_id, count(i.id) as count');
        $this->db->from(db_prefix() . 'invoices as i');

        $this->db->where('i.type_of_customer = "NEW"');
        $this->db->where('i.date >= "'. $time['from'].'"');
        $this->db->where('i.date <= "'. $time['to'].'"');
        $this->db->where('i.total >', 0);
        $this->db->where('i.status !=', 5);

        $this->db->group_by('staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);
        return $data;
    }

    public function billing_revenue($time)
    {
        $this->db->select('
            i.addedfrom as staff_id, count(i.id) as count, sum(total-total_tax) as total,
            sum((
                select sum(p.qty)
                from ' . db_prefix() . 'itemable as p
                where i.id = p.rel_id and
                    p.rel_type = "invoice" and
                    p.rate > 0 and
                    p.item_id IN (select id from ' . db_prefix() . 'items where group_id IN (' . Item::SME_GROUP_ID . ', ' . Item::ENTERPRISE_GROUP_ID . ', ' . Item::NEW_2024_GROUP_ID . ', ' . Item::CREDIT_GROUP_ID . '))
            )) as total_qty
        ');
        $this->db->from(db_prefix() . 'invoices as i');

        $this->db->where('i.invoice_closing_date >= "'. $time['from'].'"');
        $this->db->where('i.invoice_closing_date <= "'. $time['to'].'"');
        $this->db->where('i.total >', 0);

        $this->db->group_by('staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);
        return $data;
    }

    public function billing_revenue_new($time)
    {
        $this->db->select('i.addedfrom as staff_id, count(i.id) as count');
        $this->db->from(db_prefix() . 'invoices as i');

        $this->db->where('i.type_of_customer = "NEW"');

        $this->db->where('i.invoice_closing_date >= "'. $time['from'].'"');
        $this->db->where('i.invoice_closing_date <= "'. $time['to'].'"');
        $this->db->where('i.total >', 0);

        $this->db->group_by('staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);
        return $data;
    }



    public function paid_revenue($time, $arr_team_sales)
    {
        $this->db->select('i.addedfrom as staff_id, sum(i.total-i.total_tax) as total, d.departmentid');
        $this->db->from(db_prefix() . 'invoices as i');
        $this->db->join(db_prefix() . 'invoicepaymentrecords as p', 'i.id = p.invoiceid');
        $this->db->join(db_prefix() . 'staff_departments as d', 'i.addedfrom = d.staffid');
        $this->db->where_in('d.departmentid', $arr_team_sales);

        $this->db->where('p.date >= "'. $time['from'].'"');
        $this->db->where('p.date <= "'. $time['to'].'"');
        $this->db->where('i.total >', 0);

        $this->db->group_by('staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);
        return $data;
    }


    public function opportunities_revenue($time)
    {
        $this->db->select('e.addedfrom as staff_id, count(id) as count, sum(total-total_tax) as total');
        $this->db->from(db_prefix() . 'estimates as e');

        $this->db->where('date >= "'. $time['from'].'"');
        $this->db->where('date <= "'. $time['to'].'"');
        $this->db->where('e.total >', 0);

        $this->db->where('e.parent_id is null');
        $this->db->group_by('staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);
        return $data;
    }


    public function kpi_rev($time)
    {
        $this->db->select('d.staff_id, k.total_call_per_day, k.total_talk_per_day, k.total_note_per_day, k.total_f2f_meeting_note_per_day, k.total_new_client_month, k.total_new_contact_month, d.total_working_days, d.target_amount');
        $this->db->from(db_prefix() . 'kpi_settings as k');
        $this->db->join(db_prefix() . 'kpi_staff_items as d', 'k.id = d.kpi_id');

        $this->db->where('k.month_kpi >= "'. $time['from'].'"');
        $this->db->where('k.month_kpi <= "'. $time['to'].'"');

        $this->db->group_by('d.staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);
        return $data;
    }

    /**
     * get kpi team revenue on month
     * @param  array $time
     * @return boolean
     */
    public function kpi_team_revenue($time)
    {
        $this->db->select('d.kpi_id, d.team_id, d.target_amount');
        $this->db->from(db_prefix() . 'kpi_settings as k');
        $this->db->join(db_prefix() . 'kpi_team_revenue as d', 'k.id = d.kpi_id');

        $this->db->where('k.month_kpi >= "'. $time['from'].'"');
        $this->db->where('k.month_kpi <= "'. $time['to'].'"');

        $result = $this->db->get()->result_array();

        // Sử dụng array_column() để tạo một mảng chỉ bao gồm giá trị staff_id
        $staffIds = array_column($result, 'team_id');

        // Kết hợp mảng $staffIds và $data để tạo mảng mới
        $result = array_combine($staffIds, $result);

        return $result;
    }


    public function kpi_rev_down_month($month)
    {
        // Danh sách nhân viên nghỉ ngay
        // day_off tổng số ngày nghỉ trước đó, trừ ngày hiện tại
        $this->db->select('d.staff_id, k.total_call_per_day, k.total_talk_per_day, k.total_note_per_day, d.total_working_days, d.target_amount, o.date, sum(o.total_days) as day_off');
        $this->db->from(db_prefix() . 'kpi_settings as k');
        $this->db->join(db_prefix() . 'kpi_staff_items as d', 'k.id = d.kpi_id');
        $this->db->join(db_prefix() . 'kpi_day_off as o', 'k.id = o.kpi_id AND d.staff_id = o.staff_id');


        $this->db->where('o.date >= "'. $month['from'].'"');
        $this->db->where('o.date <= "'. $month['to'].'"');

        $this->db->group_by('d.staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);

        // KPI còn lại là
        foreach($data as $staff)
        {
            $nghi_phep_dc_giam = 0;


            if($staff['staff_id'])
            {
                if($staff['day_off'] > 3)
                {
                    $nghi_phep_dc_giam = 3;
                }
                else{
                    $nghi_phep_dc_giam = $staff['day_off'];
                }
            }


            if($nghi_phep_dc_giam)
            {
                // kpi được giảm
                $staff['permitted'] = $nghi_phep_dc_giam;
                $staff['total_call_per_day_down'] = $staff['total_call_per_day'] * $nghi_phep_dc_giam;
                $staff['total_talk_per_day_down'] = $staff['total_talk_per_day'] * $nghi_phep_dc_giam;
                $staff['total_note_per_day_down'] = $staff['total_note_per_day'] * $nghi_phep_dc_giam;

            }
            else
            {
                $staff['total_call_per_day'] = 0;
                $staff['total_talk_per_day'] = 0;
                $staff['total_note_per_day'] = 0;
                $staff['target_amount'] = 0;
                $staff['permitted'] = 0;
                $staff['total_call_per_day_down'] = 0;
                $staff['total_talk_per_day_down'] = 0;
                $staff['total_note_per_day_down'] = 0;
            }

            $data[$staff['staff_id']] = $staff;
        }

        return $data;
    }

    // kpi rev down ngày đầu tháng nhỏ hơn ngày hiện tại - ngày
    public function kpi_rev_down_to_from_day($today_00, $month)
    {
        // Danh sách nhân viên nghỉ ngay
        // day_off tổng số ngày nghỉ trước đó, trừ ngày hiện tại
        $this->db->select('o.staff_id, sum(o.total_days) as day_off');
        $this->db->from(db_prefix() . 'kpi_day_off as o');

        $this->db->where('o.date >= "'. $month['from'].'"');
        $this->db->where('o.date < "'. $today_00.'"');

        $this->db->group_by('o.staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);

        return $data;
    }

    public function kpi_rev_down_to_from($day, $month)
    {
        // Danh sách nhân viên nghỉ ngay
        // day_off tổng số ngày nghỉ trước đó, trừ ngày hiện tại
        $this->db->select('o.staff_id, sum(o.total_days) as day_off');
        $this->db->from(db_prefix() . 'kpi_day_off as o');

        $this->db->where('o.date >= "'. $month['from'].'"');
        $this->db->where('o.date <= "'. $day['to'].'"');

        $this->db->group_by('o.staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);

        return $data;
    }

    public function kpi_rev_down($day, $month)
    {
        // Danh sách nhân viên nghỉ ngay
        // day_off tổng số ngày nghỉ trước đó, trừ ngày hiện tại
        $this->db->select('d.staff_id, k.total_call_per_day, k.total_talk_per_day, k.total_note_per_day, d.total_working_days, d.target_amount, o.date, o.total_days, (select sum(k.total_days) from tblkpi_day_off as k where k.staff_id = d.staff_id and k.date >= "'. $month['from'] .'" and k.date <"'. $day['from'] .'" ) as day_off');
        $this->db->from(db_prefix() . 'kpi_settings as k');
        $this->db->join(db_prefix() . 'kpi_staff_items as d', 'k.id = d.kpi_id');
        $this->db->join(db_prefix() . 'kpi_day_off as o', 'k.id = o.kpi_id AND d.staff_id = o.staff_id');


        $this->db->where('o.date >= "'. $day['from'].'"');
        $this->db->where('o.date <= "'. $day['to'].'"');

        $this->db->group_by('d.staff_id');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);

        // KPI còn lại là
        foreach($data as $staff)
        {
            $ngay_nghi_phep_con = 0;
            $nghi_phep_dc_giam = 0;


            if($staff['staff_id'])
            {
                $staff['day_off'] = ($staff['day_off']) ? $staff['day_off'] : 0;
                $ngay_nghi_phep_con = 3 - $staff['day_off'];

                // Ngày nghỉ phép còn lại > 0
                if($ngay_nghi_phep_con > 0)
                {
                    if($ngay_nghi_phep_con >= $staff['total_days'])
                    {
                        $nghi_phep_dc_giam = $staff['total_days'];
                    }
                    else
                    {
                        $nghi_phep_dc_giam = $ngay_nghi_phep_con;
                    }
                }
            }


            if($nghi_phep_dc_giam)
            {
                // kpi được giảm
                $staff['total_call_per_day'] = $staff['total_call_per_day'] * ($nghi_phep_dc_giam/1);
                $staff['total_talk_per_day'] = $staff['total_talk_per_day'] * ($nghi_phep_dc_giam/1);
                $staff['total_note_per_day'] = $staff['total_note_per_day'] * ($nghi_phep_dc_giam/1);
                $staff['permitted'] = $nghi_phep_dc_giam;
            }
            else
            {
                $staff['total_call_per_day'] = 0;
                $staff['total_talk_per_day'] = 0;
                $staff['total_note_per_day'] = 0;
                $staff['permitted'] = 0;
            }

            $data[$staff['staff_id']] = $staff;
        }

        return $data;
    }

    /**
     * Param team_id, array_month
     * @return array
     * Get list staff in team - in setting kpi
     */
    public function list_staff_team($team_id, $array_date, $active_only = false)
    {
        // Danh sách nhân viên trong kpi - nhân viên cũ
        // Danh sách nhân viên mới - ngày tạo nhân viên trong tháng trở về trước đó được active = 1
        $arr = explode('-',$array_date['to']);
        $month_kpi = $arr[0] . '-' . $arr[1];
        $this->db->select('s.staffid as staff_id, s.firstname , s.lastname, s.datecreated, s.active');
        $this->db->from(db_prefix() . 'kpi_settings as k');
        $this->db->join(db_prefix() . 'kpi_staff_items as d', 'k.id = d.kpi_id');
        $this->db->join(db_prefix() . 'staff_departments as p', 'd.staff_id  = p.staffid');
        $this->db->join(db_prefix() . 'staff as s', 's.staffid  = p.staffid');
        $this->db->where('p.departmentid', $team_id);

        $this->db->where('DATE_FORMAT(month_kpi, "%Y-%m") = "'. $month_kpi .'"');
        $query1 = $this->db->get_compiled_select();

        // Danh sách nhân viên mới - ngày tạo nhân viên trong tháng trở về trước đó được active = 1, chỉ nhân viên sale, admin, sale lead (sale admin, sale manager không được hiển thị lên)
        // Danh sách nhân viên trong tháng đó trở về trước
        $this->db->select('s.staffid as staff_id, s.firstname , s.lastname, s.datecreated, s.active');
        $this->db->from(db_prefix() . 'staff as s');
        // For activities, only get active staff only
        if ($active_only) {
            $this->db->where('s.active', 1);
        }
        $this->db->where('s.datecreated <=', $array_date['to']);

        $this->db->where_In('s.role', $active_only ? [$this->role_sale] : $this->role_sales);
        $this->db->where('d.departmentid', $team_id);
        $this->db->join(db_prefix() . 'staff_departments as d', 's.staffid = d.staffid');
        $this->db->order_by('staff_id', 'ASC');

        $query2 = $this->db->get_compiled_select();

        $sql = "($query1) UNION ($query2)";
        $query = $this->db->query($sql);

        $result = $query->result_array();

        return $result;
    }

    private function get_call_talk_data($time, $call_type = null)
    {
        $this->db->select('s.staffid as staff_id, count(c.id) as count, sum(TIME_TO_SEC(c.talk_time)) AS total_giay');
        $this->db->from(db_prefix() . '3c_call as c');
        $this->db->join(db_prefix() . 'staff as s', 'c.staff_id = s.staffid');

        $this->db->where('c.agent_id > 0');
        $this->db->where('TIME_TO_SEC(c.talk_time) >=5');
        $this->db->where('c.call_status = "' . M3cCall::MEET_AGENT . '"');
        $this->db->where('c.start_time >= "'. $time['from'].'"');
        $this->db->where('c.start_time <= "'. $time['to'].'"');

        if ($call_type !== null) {
            $this->db->where('c.call_type', $call_type);
        }

        $this->db->group_by('staffid');

        $result = $this->db->get()->result_array();
        return $this->data_convert_sale_agent($result);
    }

    // Số lượng cuộc gọi, Tổng thời gian gọi (thành công >=5s) (only call out)
    public function call_talk($time)
    {
        return $this->get_call_talk_data($time, M3cCall::CALL_OUT_TYPE);
    }

    // Get the number of successful calls, the total call time (successful >=5s) (call out and call in)
    public function call_talk_all($time)
    {
        return $this->get_call_talk_data($time);
    }

    public function connect_talk($time)
    {
        // type = 1, liên hệ thành công, type = 2 liên hệ lại
        $this->db->select('addedfrom as staff_id, count(DISTINCT rel_id) as count');
        $this->db->from(db_prefix() . 'notes');
        $this->db->where('type',1);

        $this->db->where('dateadded >= "'. $time['from'].'"');
        $this->db->where('dateadded <= "'. $time['to'].'"');

        $this->db->group_by('addedfrom');

        $result = $this->db->get()->result_array();
        $data = $this->data_convert_sale_agent($result);
        return $data;
    }

    public function social_chat($time)
    {
        return Note::query()
            ->select(DB::raw('addedfrom as staff_id, count(rel_id) as count'))
            ->success()
            ->customerNote()
            ->where('dateadded', '>=', $time['from'])
            ->where('dateadded', '<=', $time['to'])
            ->where('contact_channel', '<>', 7) // F2F meeting
            ->has('attachment')
            ->groupBy('staff_id')
            ->get()
            ->keyBy('staff_id')
            ->toArray();
    }

    public function f2f_meeting($time)
    {
        return Note::query()
            ->select(DB::raw('addedfrom as staff_id, count(rel_id) as count'))
            ->success()
            ->customerNote()
            ->where('dateadded', '>=', $time['from'])
            ->where('dateadded', '<=', $time['to'])
            ->where('contact_channel', 7) // F2F meeting
            ->groupBy('staff_id')
            ->get()
            ->keyBy('staff_id')
            ->toArray();
    }

    /**
     * @param  integer (optional)
     * @return object
     * Get single goal
     */
    public function get($id = '', $exclude_notified = false)
    {
        if (is_numeric($id)) {
            $this->db->where('id', $id);

            return $this->db->get(db_prefix() . 'goals')->row();
        }

        if ($exclude_notified == true) {
            $this->db->where('notified', 0);
        }

        return $this->db->get(db_prefix() . 'goals')->result_array();
    }

    public function get_all_goals($exclude_notified = true)
    {
        if ($exclude_notified) {
            $this->db->where('notified', 0);
        }

        $this->db->select('goals.*, staff.firstname, staff.lastname');
        $this->db->order_by('goals.achievement', 'desc');
        $this->db->join(db_prefix() . 'staff', '' . db_prefix() . 'staff.staffid=' . db_prefix() . 'goals.staff_id');
        $goals = $this->db->get(db_prefix() . 'goals')->result_array();

        foreach ($goals as $key => $val) {
            $goal = get_goal_type($val['goal_type']);

            if (!$goal || $goal && isset($goal['dashboard']) && $goal['dashboard'] === false) {
                unset($goals[$key]);
                continue;
            }

            $goals[$key]['achievement']    = $this->calculate_goal_achievement($val['id']);
            $goals[$key]['goal_type_name'] = format_goal_type($val['goal_type']);
        }

        return array_values($goals);
    }

    public function get_staff_goals($staff_id, $exclude_notified = true)
    {
        $this->db->where('staff_id', $staff_id);

        if ($exclude_notified) {
            $this->db->where('notified', 0);
        }

        $this->db->order_by('achievement', 'desc');
        $goals = $this->db->get(db_prefix() . 'goals')->result_array();

        foreach ($goals as $key => $val) {
            $goals[$key]['achievement']    = $this->calculate_goal_achievement($val['id']);
            $goals[$key]['goal_type_name'] = format_goal_type($val['goal_type']);
        }

        return $goals;
    }

    /**
     * Add new goal
     * @param mixed $data All $_POST dat
     * @return mixed
     */
    public function add($data)
    {
        $data['notify_when_fail']    = isset($data['notify_when_fail']) ? 1 : 0;
        $data['notify_when_achieve'] = isset($data['notify_when_achieve']) ? 1 : 0;

        $data['contract_type'] = $data['contract_type'] == '' ? 0 : $data['contract_type'];
        $data['staff_id']      = $data['staff_id'] == '' ? 0 : $data['staff_id'];
        $data['start_date']    = to_sql_date($data['start_date']);
        $data['end_date']      = to_sql_date($data['end_date']);
        $this->db->insert(db_prefix() . 'goals', $data);
        $insert_id = $this->db->insert_id();
        if ($insert_id) {
            log_activity('New Goal Added [ID:' . $insert_id . ']');

            return $insert_id;
        }

        return false;
    }

    /**
     * Update goal
     * @param  mixed $data All $_POST data
     * @param  mixed $id   goal id
     * @return boolean
     */
    public function update($data, $id)
    {
        $data['notify_when_fail']    = isset($data['notify_when_fail']) ? 1 : 0;
        $data['notify_when_achieve'] = isset($data['notify_when_achieve']) ? 1 : 0;

        $data['contract_type'] = $data['contract_type'] == '' ? 0 : $data['contract_type'];
        $data['staff_id']      = $data['staff_id'] == '' ? 0 : $data['staff_id'];
        $data['start_date']    = to_sql_date($data['start_date']);
        $data['end_date']      = to_sql_date($data['end_date']);

        $goal = $this->get($id);

        if ($goal->notified == 1 && date('Y-m-d') < $data['end_date']) {
            // After goal finished, user changed/extended date? If yes, set this goal to be notified
            $data['notified'] = 0;
        }

        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'goals', $data);
        if ($this->db->affected_rows() > 0) {
            log_activity('Goal Updated [ID:' . $id . ']');

            return true;
        }

        return false;
    }

    /**
     * Delete goal
     * @param  mixed $id goal id
     * @return boolean
     */
    public function delete($id)
    {
        $this->db->where('id', $id);
        $this->db->delete(db_prefix() . 'goals');
        if ($this->db->affected_rows() > 0) {
            log_activity('Goal Deleted [ID:' . $id . ']');

            return true;
        }

        return false;
    }

    /**
     * Notify staff members about goal result
     * @param  mixed $id          goal id
     * @param  string $notify_type is success or failed
     * @param  mixed $achievement total achievent (Option)
     * @return boolean
     */
    public function notify_staff_members($id, $notify_type, $achievement = '')
    {
        $goal = $this->get($id);
        if ($achievement == '') {
            $achievement = $this->calculate_goal_achievement($id);
        }
        if ($notify_type == 'success') {
            $goal_desc = 'not_goal_message_success';
        } else {
            $goal_desc = 'not_goal_message_failed';
        }

        if ($goal->staff_id == 0) {
            $this->load->model('staff_model');
            $staff = $this->staff_model->get('', ['active' => 1]);
        } else {
            $this->db->where('active', 1)
            ->where('staffid', $goal->staff_id);
            $staff = $this->db->get(db_prefix() . 'staff')->result_array();
        }

        $notifiedUsers = [];
        foreach ($staff as $member) {
            if (is_staff_member($member['staffid'])) {
                $notified = add_notification([
                    'fromcompany'     => 1,
                    'touserid'        => $member['staffid'],
                    'description'     => $goal_desc,
                    'additional_data' => serialize([
                        format_goal_type($goal->goal_type),
                        $goal->achievement,
                        $achievement['total'],
                        _d($goal->start_date),
                        _d($goal->end_date),
                    ]),
                ]);
                if ($notified) {
                    array_push($notifiedUsers, $member['staffid']);
                }
            }
        }

        pusher_trigger_notification($notifiedUsers);
        $this->mark_as_notified($goal->id);

        if (count($staff) > 0 && $this->db->affected_rows() > 0) {
            return true;
        }

        return false;
    }

    /**
     * Calculate goal achievement
     * @param  mixed $id goal id
     * @return array
     */
    public function calculate_goal_achievement($id)
    {
        $goal       = $this->get($id);
        $start_date = $goal->start_date;
        $end_date   = $goal->end_date;
        $type       = $goal->goal_type;
        $total      = 0;
        $percent    = 0;
        if ($type == 1) {
            $sql = 'SELECT SUM(amount) as total FROM ' . db_prefix() . 'invoicepaymentrecords';

            if ($goal->staff_id != 0) {
                $sql .= ' JOIN ' . db_prefix() . 'invoices ON ' . db_prefix() . 'invoices.id = ' . db_prefix() . 'invoicepaymentrecords.invoiceid';
            }

            $sql .= ' WHERE ' . db_prefix() . "invoicepaymentrecords.date BETWEEN '" . $start_date . "' AND '" . $end_date . "'";

            if ($goal->staff_id != 0) {
                $sql .= ' AND (sale_agent=' . $goal->staff_id . ')';
            }
        } elseif ($type == 8) {
            $sql = 'SELECT SUM(total) as total FROM ' . db_prefix() . 'invoices';

            $sql .= ' WHERE ' . db_prefix() . "invoices.date BETWEEN '" . $start_date . "' AND '" . $end_date . "'";

            if ($goal->staff_id != 0) {
                $sql .= ' AND (sale_agent=' . $goal->staff_id . ')';
            }
        } elseif ($type == 2) {
            $sql = 'SELECT COUNT(' . db_prefix() . 'leads.id) as total FROM ' . db_prefix() . "leads WHERE DATE(date_converted) BETWEEN '" . $start_date . "' AND '" . $end_date . "' AND status = 1 AND " . db_prefix() . 'leads.id IN (SELECT leadid FROM ' . db_prefix() . 'clients WHERE leadid=' . db_prefix() . 'leads.id)';
            if ($goal->staff_id != 0) {
                $sql .= ' AND CASE WHEN assigned=0 THEN addedfrom=' . $goal->staff_id . ' ELSE assigned=' . $goal->staff_id . ' END';
            }
        } elseif ($type == 3) {
            $sql = 'SELECT COUNT(' . db_prefix() . 'clients.userid) as total FROM ' . db_prefix() . "clients WHERE DATE(datecreated) BETWEEN '" . $start_date . "' AND '" . $end_date . "' AND leadid IS NULL";
            if ($goal->staff_id != 0) {
                $sql .= ' AND addedfrom=' . $goal->staff_id;
            }
        } elseif ($type == 4) {
            $sql = 'SELECT COUNT(' . db_prefix() . 'clients.userid) as total FROM ' . db_prefix() . "clients WHERE DATE(datecreated) BETWEEN '" . $start_date . "' AND '" . $end_date . "'";
            if ($goal->staff_id != 0) {
                $sql .= ' AND addedfrom=' . $goal->staff_id;
            }
        } elseif ($type == 5 || $type == 7) {
            $column = 'dateadded';
            if ($type == 7) {
                $column = 'datestart';
            }
            $sql = 'SELECT count(id) as total FROM ' . db_prefix() . 'contracts WHERE ' . $column . " BETWEEN '" . $start_date . "' AND '" . $end_date . "' AND contract_type = " . $goal->contract_type . ' AND trash = 0';
            if ($goal->staff_id != 0) {
                $sql .= ' AND addedfrom=' . $goal->staff_id;
            }
        } elseif ($type == 6) {
            $sql = 'SELECT count(id) as total FROM ' . db_prefix() . "estimates WHERE parent_id IS NULL AND DATE(invoiced_date) BETWEEN '" . $start_date . "' AND '" . $end_date . "' AND invoiceid IS NOT NULL AND invoiceid NOT IN (SELECT id FROM " . db_prefix() . 'invoices WHERE status=5)';
            if ($goal->staff_id != 0) {
                $sql .= ' AND (addedfrom=' . $goal->staff_id . ' OR sale_agent=' . $goal->staff_id . ')';
            }
        } else {
            $sql = hooks()->apply_filters('calculate_goal_achievement_sql', '', $goal);

            if ($sql === '') {
                return;
            }
        }

        $total = floatval($this->db->query($sql)->row()->total);

        if ($total >= floatval($goal->achievement)) {
            $percent = 100;
        } else {
            if ($total !== 0) {
                $percent = number_format(($total * 100) / $goal->achievement, 2);
            }
        }
        $progress_bar_percent = $percent / 100;

        return [
            'total'                => $total,
            'percent'              => $percent,
            'progress_bar_percent' => $progress_bar_percent,
        ];
    }

    public function mark_as_notified($id)
    {
        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'goals', [
            'notified' => 1,
        ]);
    }
}

<?php defined('BASEPATH') or exit('No direct script access allowed');

$day_off_total = $kpi_targets_total = $closed_revenue_count_total = $closed_revenue_new_total = $closed_revenue_total_total = $billing_revenue_count_total = $billing_revenue_new_total = $billing_revenue_total_qty_total = $billing_revenue_total_total = $paid_revenue_value_total = $opportunities_count_total = $opportunities_total_total = 0;

foreach ($group as $g) {
    $total_staff = count($g['revenue_day']);
    if (!$total_staff) {
        continue;
    }

    $flag = true;
    $day_off = $closed_revenue_count = $closed_revenue_new = $closed_revenue_total = $billing_revenue_count = $billing_revenue_new = $billing_revenue_total_qty = $billing_revenue_total = $paid_revenue_value = $opportunities_count = $opportunities_total = $kpi_targets = 0;

    foreach ($g['revenue_day'] as $staff) {

        $kpi_closed_revenue = ($staff['kpi_targets']) ? round(($staff['closed_revenue_total'] / $staff['kpi_targets']) * 100, 2, PHP_ROUND_HALF_UP) : '100';
        $kpi_billing_revenue_total = ($staff['kpi_targets']) ? round(($staff['billing_revenue_total'] / $staff['kpi_targets']) * 100, 2, PHP_ROUND_HALF_UP) : '100';
        $kpi_paid_revenue_value = ($staff['kpi_targets']) ? round(($staff['paid_revenue_value'] / $staff['kpi_targets']) * 100, 2, PHP_ROUND_HALF_UP) : '100';

?>
        <tr>
            <?php
            if ($flag && $total_staff) {
            ?>
                <td class="title_team_group" rowspan="<?php echo $total_staff + 1 ?>"><?php echo $g['text'] ?></td>
            <?php
                $flag = false;
            }
            ?>
            <td staff_id="<?php echo $staff['staff_id'] ?>" class="font-weight-bold">
                <?php echo $staff['firstname'] . ' ' . $staff['lastname'] ?>
                <?php
                if (isset($paid_revenue_top[$staff['staff_id']]) && $paid_revenue_top[$staff['staff_id']] != '') {
                    echo '<span class="paid_revenue_top"><img src="' . $paid_revenue_top[$staff['staff_id']] . '" /></span>';
                }
                ?>
            </td>
            <td><?php echo $staff['day_off'] ?></td>
            <td class="number_format kpi_targets"><?php echo $staff['kpi_targets'] ?></td>
            <!-- <td class="number_format"><?php echo $staff['closed_revenue_count'] ?></td>
            <td class="number_format"><?php echo $staff['closed_revenue_new'] ?></td>
            <td class="closed_revenue_total">
                <span class="number_format kpi_renenue_number"><?php echo $staff['closed_revenue_total'] ?></span>
                <span class="kpi_renenue">
                    <span class="kpi_renenue_value"><?php echo $kpi_closed_revenue ?></span>%
                </span>
            </td> -->
            <td class="number_format"><?php echo $staff['opportunities_count'] ?></td>
            <td class="number_format"><?php echo $staff['opportunities_total'] ?></td>
            <td class="number_format"><?php echo $staff['billing_revenue_count'] ?></td>
            <td class="number_format"><?php echo $staff['billing_revenue_new'] ?></td>
            <td><?php echo $staff['billing_revenue_total_qty'] ?></td>
            <td class="closed_revenue_total">
                <span class="number_format kpi_renenue_number"><?php echo $staff['billing_revenue_total'] ?></span>
                <span class="kpi_renenue">
                    <span class="kpi_renenue_value"><?php echo $kpi_billing_revenue_total ?></span>%
                </span>
            </td>
            <!-- <td class="closed_revenue_total">
                <span class="number_format kpi_renenue_number"><?php echo $staff['paid_revenue_value'] ?></span>
                <span class="kpi_renenue">
                    <span class="kpi_renenue_value"><?php echo $kpi_paid_revenue_value ?></span>%
                </span>
            </td> -->
        </tr>
    <?php

        $day_off += $staff['day_off'];
        $closed_revenue_count += $staff['closed_revenue_count'];
        $closed_revenue_new += $staff['closed_revenue_new'];
        $closed_revenue_total += $staff['closed_revenue_total'];
        $billing_revenue_count += $staff['billing_revenue_count'];
        $billing_revenue_new += $staff['billing_revenue_new'];
        $billing_revenue_total_qty += $staff['billing_revenue_total_qty'];
        $billing_revenue_total += $staff['billing_revenue_total'];
        $paid_revenue_value += $staff['paid_revenue_value'];
        $opportunities_count += $staff['opportunities_count'];
        $opportunities_total += $staff['opportunities_total'];
    }

    // Toàn team
    $kpi_targets = $g['kpi_team_revenue'];
    $kpi_closed_revenue_team = ($kpi_targets) ? round(($closed_revenue_total / $kpi_targets) * 100, 2, PHP_ROUND_HALF_UP) : '100';
    $kpi_billing_revenue_total_team = ($kpi_targets) ? round(($billing_revenue_total / $kpi_targets) * 100, 2, PHP_ROUND_HALF_UP) : '100';
    $kpi_paid_revenue_value_team = ($kpi_targets) ? round(($paid_revenue_value / $kpi_targets) * 100, 2, PHP_ROUND_HALF_UP) : '100';
    ?>
    <tr class="full_team_kpi">
        <td><?php echo _l('full_team_kpi') ?></td>
        <td class="number_format"><?php echo $day_off ?></td>
        <td class="number_format kpi_targets"><?php echo $kpi_targets ?></td>
        <!-- <td class="number_format"><?php echo $closed_revenue_count ?></td>
        <td class="number_format"><?php echo $closed_revenue_new ?></td>
        <td class="closed_revenue_total">
            <span class="number_format kpi_renenue_number"><?php echo $closed_revenue_total ?></span>
            <span class="kpi_renenue">
                <span class="kpi_renenue_value"><?php echo $kpi_closed_revenue_team ?></span>%
            </span>
        </td> -->
        <td class="number_format"><?php echo $opportunities_count ?></td>
        <td class="number_format"><?php echo $opportunities_total ?></td>
        <td class="number_format"><?php echo $billing_revenue_count ?></td>
        <td class="number_format"><?php echo $billing_revenue_new ?></td>
        <td class="number_format"><?php echo $billing_revenue_total_qty ?></td>
        <td class="closed_revenue_total">
            <span class="number_format kpi_renenue_number"><?php echo $billing_revenue_total ?></span>
            <span class="kpi_renenue">
                <span class="kpi_renenue_value"><?php echo $kpi_billing_revenue_total_team ?></span>%
            </span>
        </td>
        <!-- <td class="closed_revenue_total">
            <span class="number_format kpi_renenue_number"><?php echo $paid_revenue_value ?></span>
            <span class="kpi_renenue">
                <span class="kpi_renenue_value"><?php echo $kpi_paid_revenue_value_team ?></span>%
            </span>
        </td> -->
    </tr>
<?php

    // Tất cả
    $day_off_total += $day_off;
    $kpi_targets_total += $kpi_targets;
    $closed_revenue_count_total += $closed_revenue_count;
    $closed_revenue_new_total += $closed_revenue_new;
    $closed_revenue_total_total += $closed_revenue_total;
    $billing_revenue_count_total += $billing_revenue_count;
    $billing_revenue_new_total += $billing_revenue_new;
    $billing_revenue_total_qty_total += $billing_revenue_total_qty;
    $billing_revenue_total_total += $billing_revenue_total;
    $paid_revenue_value_total += $paid_revenue_value;
    $opportunities_count_total += $opportunities_count;
    $opportunities_total_total += $opportunities_total;
}

// KPI tất cả
$kpi_closed_revenue_team_total = ($kpi_targets_total) ? round(($closed_revenue_total_total / $kpi_targets_total) * 100, 2, PHP_ROUND_HALF_UP) : '100';
$kpi_billing_revenue_total_team_total = ($kpi_targets_total) ? round(($billing_revenue_total_total / $kpi_targets_total) * 100, 2, PHP_ROUND_HALF_UP) : '100';
$kpi_paid_revenue_value_team_total = ($kpi_targets_total) ? round(($paid_revenue_value_total / $kpi_targets_total) * 100, 2, PHP_ROUND_HALF_UP) : '100';

?>

<tr class="full_all_team_kpi">
    <td class="text-center" colspan="2"><?php echo _l('full_all_team_kpi') ?></td>
    <td class="number_format"><?php echo $day_off_total  ?></td>
    <td class="number_format kpi_targets"><?php echo $kpi_targets_total  ?></td>
    <!-- <td class="number_format"><?php echo $closed_revenue_count_total  ?></td>
    <td class="number_format"><?php echo $closed_revenue_new_total  ?></td>
    <td class="closed_revenue_total">
        <span class="number_format kpi_renenue_number"><?php echo $closed_revenue_total_total  ?></span>
        <span class="kpi_renenue">
            <span class="kpi_renenue_value"><?php echo $kpi_closed_revenue_team_total  ?></span>%
        </span>
    </td> -->
    <td class="number_format"><?php echo $opportunities_count_total  ?></td>
    <td class="number_format"><?php echo $opportunities_total_total  ?></td>
    <td class="number_format"><?php echo $billing_revenue_count_total  ?></td>
    <td class="number_format"><?php echo $billing_revenue_new_total ?></td>
    <td class="number_format"><?php echo $billing_revenue_total_qty_total  ?></td>
    <td class="closed_revenue_total">
        <span class="number_format kpi_renenue_number"><?php echo $billing_revenue_total_total  ?></span>
        <span class="kpi_renenue">
            <span class="kpi_renenue_value"><?php echo $kpi_billing_revenue_total_team_total  ?></span>%
        </span>
    </td>
    <!-- <td class="closed_revenue_total">
        <span class="number_format kpi_renenue_number"><?php echo $paid_revenue_value_total  ?></span>
        <span class="kpi_renenue">
            <span class="kpi_renenue_value"><?php echo $kpi_paid_revenue_value_team_total  ?></span>%
        </span>
    </td> -->
</tr>
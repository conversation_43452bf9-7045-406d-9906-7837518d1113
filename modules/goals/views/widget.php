<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php
$goals = [];
//if (is_staff_member()) {
   $this->load->model('goals/goals_model');
   $goals = $this->goals_model->get_all_goals();
//}
?>
<div class="widget<?php if(count($goals) == 0){echo ' hide';} ?>" id="widget-<?php echo create_widget_id('goals'); ?>">
   <div class="row">
      <div class="col-md-12">
         <div class="panel_s">
            <div class="panel-body padding-10">
               <div class="widget-dragger"></div>
               <p class="padding-5">
                  <?php echo _l('goals'); ?>
               </p>
               <hr class="hr-panel-heading-dashboard">
               <div class="row">
               <?php foreach($goals as $goal){
                  ?>
                  <div class="col-md-6">
                     <div class="goal padding-5" style="height: 90px;margin-bottom: 5px;">
                        <h4 class="pull-left font-medium no-mtop">
                           <?php echo !empty($goal['firstname']) || !empty($goal['lastname']) ? @$goal['firstname'] .' '. @$goal['lastname'] . ' - ' : ''; ?> <?php echo $goal['subject']; ?>
                        </h4>
                        <div class="clearfix"></div>
                        <div>
                           <h4 class="pull-left bold no-mtop text-primary text-left">
                              <?php echo $goal['achievement']['percent']; ?>%
                           </h4>
                           <h4 class="pull-right bold no-mtop text-success text-right">
                              <?php echo number_format($goal['achievement']['total'],0,'.',','); ?>
                           </h4>
                        </div>
                        <div class="clearfix"></div>
                        <div class="progress no-margin progress-bar-mini">
                           <div class="progress-bar progress-bar-danger no-percent-text not-dynamic" role="progressbar" aria-valuenow="<?php echo $goal['achievement']['percent']; ?>" aria-valuemin="0" aria-valuemax="100" style="width: 0%" data-percent="<?php echo $goal['achievement']['percent']; ?>">
                           </div>
                        </div>
                        <hr style="color: #ccc">
                     </div>
                     <div class="clearfix"></div>
                  </div>
               <?php } ?>
            </div>
            </div>
         </div>
      </div>
   </div>
</div>

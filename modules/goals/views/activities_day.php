<?php
defined('BASEPATH') or exit('No direct script access allowed');

$day_off_total = $call_total = $talk_total = $total_talk_per_day_total = $kpi_total_call_per_day_total =
$kpi_total_talk_per_day_total = $social_chat_total = $f2f_meeting_total = $total_sales_activity_total =
$kpi_total_sales_activity_total = $kpi_total_f2f_meeting_per_day_total =
$total_sales_activity_team = $total_sales_kpi_per_day_team = $kpi_total_sales_activity_per_day_team = 0;

foreach ($group as $g) {
    $total_staff = count($g['activies_day']);
    if (!$total_staff) {
        continue;
    }

    $flag = true;
    $day_off = $call = $talk = $kpi_total_call_per_day = $kpi_total_f2f_meeting_per_day = $kpi_total_note_per_day =
    $kpi_total_talk_per_day = $social_chat = $kpi_social_chat = $f2f_meeting =
    $total_sales_activity = $kpi_total_sales_activity = $kpi_f2f_meeting = $total_sales_kpi_per_day = $kpi_total_sales_activity_per_day = 0;

    foreach ($g['activies_day'] as $staff) {
        // $kpi_new_db = ($staff['kpi']['new_db']) ? round(($staff['new_db'] / $staff['kpi']['new_db']) * 100, 2, PHP_ROUND_HALF_UP) : '100';
        // $kpi_new_contact = ($staff['kpi']['new_contact']) ? round(($staff['new_contact'] / $staff['kpi']['new_contact']) * 100, 2, PHP_ROUND_HALF_UP) : '100';
        $kpi_call =
            ($staff['kpi']['total_call_per_day']) ? round(($staff['call'] / $staff['kpi']['total_call_per_day']) * 100,
                2, PHP_ROUND_HALF_UP) : '100';
        $kpi_talk =
            ($staff['kpi']['total_talk_per_day']) ? round(($staff['talk'] / $staff['kpi']['total_talk_per_day']) * 100,
                2, PHP_ROUND_HALF_UP) : '100';
        $social_chat_count = max(0, ($staff['social_chat'] ?? 0));
        $kpi_social_chat =
            ($staff['kpi']['total_note_per_day']) ? round(($social_chat_count / $staff['kpi']['total_note_per_day'])
                * 100, 2, PHP_ROUND_HALF_UP) : '100';
        $kpi_f2f_meeting =
            (isset($staff['kpi']['total_f2f_meeting_note_per_day']) && $staff['kpi']['total_f2f_meeting_note_per_day'])
                ? round(($staff['f2f_meeting'] / $staff['kpi']['total_f2f_meeting_note_per_day']) * 100, 2,
                PHP_ROUND_HALF_UP) : '0';


        // Calculate Total Sales Activity and its KPI
        $total_sales_activity_value = ($staff['call'] ?? 0) + $social_chat_count + (($staff['f2f_meeting'] ?? 0) * 10);
        $staff_total_sales_kpi_per_day =
            ($staff['kpi']['total_call_per_day'] ?? 0) + ($staff['kpi']['total_note_per_day'] ?? 0);
        $kpi_total_sales_activity_per_day =
            $staff_total_sales_kpi_per_day > 0 ? round(($total_sales_activity_value / $staff_total_sales_kpi_per_day) * 100, 2,
                PHP_ROUND_HALF_UP) : 0;

        ?>
        <tr>
            <?php
            if ($flag && $total_staff) {
                ?>
                <td class="title_team_group" rowspan="<?php
                echo $total_staff + 1 ?>"><?php
                    echo $g['text'] ?></td>
                <?php
                $flag = false;
            }
            ?>
            <td staff_id="<?php
            echo $staff['staff_id'] ?>" class="font-weight-bold">
                <?php
                echo $staff['firstname'].' '.$staff['lastname'] ?>
                <?php
                if (isset($paid_revenue_top[$staff['staff_id']]) && $paid_revenue_top[$staff['staff_id']]) {
                    echo '<span class="paid_revenue_top"><img src="'.$paid_revenue_top[$staff['staff_id']]
                        .'" /></span>';
                }
                ?>
            </td>
            <td class="number_format"><?php
                echo $staff['rank'] ?></td>
            <td class="number_format"><?php
                echo $staff['day_off'] ?></td>
            <td>
                <span class="kpi_renenue_number">
                    <span class="number_format"><?php
                        echo $staff['call'] ?></span>
                    <span class="kpi_activities">
                        /<span class="number_format"><?php
                            echo $staff['kpi']['total_call_per_day'] ?></span></span>
                </span>
                <span class="kpi_renenue">
                    <span class="kpi_renenue_value"><?php
                        echo $kpi_call ?></span>%
                </span>
            </td>
            <td>
                <span class="kpi_renenue_number">
                    <span class="number_format"><?php
                        echo $staff['talk'] ?></span><span class="kpi_activities">
                        /<span class="number_format"><?php
                            echo $staff['kpi']['total_talk_per_day'] ?></span></span>
                </span>
                <span class="kpi_renenue">
                    <span class="kpi_renenue_value"><?php
                        echo $kpi_talk ?></span>%
                </span>
            </td>
            <td>
                <span class="kpi_renenue_number">
                    <span class="number_format"><?php
                        echo $staff['social_chat'] ?? 0 ?></span><span class="kpi_activities">
                        /<span class="number_format"><?php
                            echo $staff['kpi']['total_note_per_day'] ?></span></span>
                </span>
                <span class="kpi_renenue">
                    <span class="kpi_renenue_value"><?php
                        echo $kpi_social_chat ?></span>%
                </span>
            </td>
            <td>
                <span class="kpi_renenue_number">
                    <span class="number_format"><?php
                        echo $staff['f2f_meeting'] ?? 0 ?></span><span class="kpi_activities">
                        /<span class="number_format"><?php
                            echo isset($staff['kpi']['total_f2f_meeting_note_per_day'])
                                ? $staff['kpi']['total_f2f_meeting_note_per_day'] : '0' ?></span></span>
                </span>
                <span class="kpi_renenue">
                    <span class="kpi_renenue_value"><?php
                        echo $kpi_f2f_meeting ?></span>%
                </span>
            </td>
            <td>
                <span class="kpi_renenue_number">
                    <span class="number_format"><?php
                        echo $total_sales_activity_value; ?></span>
                    <span class="kpi_activities">
                        /<span class="number_format"><?php
                            echo $staff_total_sales_kpi_per_day; ?></span>
                    </span>
                </span>
                <span class="kpi_renenue">
                    <span class="kpi_renenue_value"><?php
                        echo $kpi_total_sales_activity_per_day; ?></span>%
                </span>
            </td>
        </tr>
        <?php

        $day_off += $staff['day_off'];
        // $new_db += $staff['new_db'];
        // $new_contact += $staff['new_contact'];
        $call += $staff['call'];
        $talk += $staff['talk'];
        // $connect += $staff['connect'];
        $social_chat += $staff['social_chat'];
        $f2f_meeting += $staff['f2f_meeting'];

        $kpi_total_call_per_day += $staff['kpi']['total_call_per_day'];
        $kpi_total_talk_per_day += $staff['kpi']['total_talk_per_day'];
        $kpi_total_note_per_day += $staff['kpi']['total_note_per_day'];
        $kpi_total_f2f_meeting_per_day += $staff['kpi']['total_f2f_meeting_note_per_day'] ?? 0;

        $total_sales_activity += $total_sales_activity_value;
        $total_sales_kpi_per_day += $staff_total_sales_kpi_per_day;
        $kpi_total_sales_activity_per_day =
            $total_sales_kpi_per_day > 0 ? round(($total_sales_activity / $total_sales_kpi_per_day)
                * 100, 2, PHP_ROUND_HALF_UP) : 0;
    }

    // Toàn team
    // $kpi_new_db = ($kpi_total_call_per_day) ? round(($call / $kpi_total_call_per_day) * 100, 2, PHP_ROUND_HALF_UP) : '100';
    $kpi_call_team =
        ($kpi_total_call_per_day) ? round(($call / $kpi_total_call_per_day) * 100, 2, PHP_ROUND_HALF_UP) : '100';
    $kpi_total_talk_per_day_team =
        ($kpi_total_talk_per_day) ? round(($talk / $kpi_total_talk_per_day) * 100, 2, PHP_ROUND_HALF_UP) : '100';
    $kpi_total_note_per_day_team =
        ($kpi_total_note_per_day) ? round(($social_chat / $kpi_total_note_per_day) * 100, 2, PHP_ROUND_HALF_UP) : '100';
    $kpi_total_f2f_meeting_per_day_team =
        ($kpi_total_f2f_meeting_per_day) ? round(($f2f_meeting / $kpi_total_f2f_meeting_per_day) * 100, 2,
            PHP_ROUND_HALF_UP) : '100';
    // $kpi_total_new_db_team = ($kpi_total_new_db) ? round(($new_db / $kpi_total_new_db) * 100, 2, PHP_ROUND_HALF_UP) : '100';
    // $kpi_total_new_contact_team = ($kpi_total_new_contact) ? round(($new_contact / $kpi_total_new_contact) * 100, 2, PHP_ROUND_HALF_UP) : '100';

    ?>
    <tr <?php
    echo $total_staff ?> class="full_team_kpi">
        <td><?php
            echo _l('full_team_kpi') ?></td>
        <td class="number_format"><?php
            echo $g['rank_total'] ?></td>
        <td class="number_format"><?php
            echo $day_off ?></td>
        <td>
            <span class="kpi_renenue_number">
                <span class="number_format"><?php
                    echo $call ?></span><span class="kpi_activities">
                    /<span class="number_format"><?php
                        echo $kpi_total_call_per_day ?></span></span>
            </span>
            <span class="kpi_renenue">
                <span class="kpi_renenue_value"><?php
                    echo $kpi_call_team ?></span>%
            </span>
        </td>
        <td>
            <span class="kpi_renenue_number">
                <span class="number_format"><?php
                    echo $talk ?></span><span class="kpi_activities">
                    /<span class="number_format"><?php
                        echo $kpi_total_talk_per_day ?></span></span>
            </span>
            <span class="kpi_renenue">
                <span class="kpi_renenue_value"><?php
                    echo $kpi_total_talk_per_day_team ?></span>%
            </span>
        </td>
        <td>
            <span class="kpi_renenue_number">
                <span class="number_format"><?php
                    echo $social_chat ?? 0 ?></span><span class="kpi_activities">
                    /<span class="number_format"><?php
                        echo $kpi_total_note_per_day ?></span></span>
            </span>
            <span class="kpi_renenue">
                <span class="kpi_renenue_value"><?php
                    echo $kpi_total_note_per_day_team ?></span>%
            </span>
        </td>
        <td>
            <span class="kpi_renenue_number">
                <span class="number_format"><?php
                    echo $f2f_meeting ?? 0 ?></span><span class="kpi_activities">
                    /<span class="number_format"><?php
                        echo $kpi_total_f2f_meeting_per_day ?></span></span>
            </span>
            <span class="kpi_renenue">
                <span class="kpi_renenue_value"><?php
                    echo $kpi_total_f2f_meeting_per_day_team ?></span>%
            </span>
        </td>
        <td>
            <span class="kpi_renenue_number">
                <span class="number_format"><?php
                    echo $total_sales_activity; ?></span>
                <span class="kpi_activities">
                    /<span class="number_format"><?php
                        echo $total_sales_kpi_per_day; ?></span>
                </span>
            </span>
            <span class="kpi_renenue">
                <span class="kpi_renenue_value">
                    <?php
                    echo $kpi_total_sales_activity_per_day; ?>%
                </span>
            </span>
        </td>
    </tr>
    <?php

    // Tất cả
    $day_off_total += $day_off;
    // $new_db_total += $new_db;
    $call_total += $call;
    $talk_total += $talk;
    // $connect_total += $connect;
    // $new_contact_total += $new_contact;
    $kpi_total_call_per_day_total += $kpi_total_call_per_day;
    $kpi_total_talk_per_day_total += $kpi_total_talk_per_day;
    $kpi_total_note_per_day_total += $kpi_total_note_per_day;
    // $kpi_total_new_db_total += $kpi_total_new_db;
    // $kpi_total_new_contact_total += $kpi_total_new_contact;
    $social_chat_total += $social_chat;
    $f2f_meeting_total += $f2f_meeting;
    $kpi_total_f2f_meeting_per_day_total += $kpi_total_f2f_meeting_per_day;

    $total_sales_activity_total += $total_sales_activity;
    $kpi_total_sales_activity_total += $total_sales_kpi_per_day;
}

// KPI tất cả
$kpi_total_call_per_day_team_total =
    ($kpi_total_call_per_day_total) ? round(($call_total / $kpi_total_call_per_day_total) * 100, 2, PHP_ROUND_HALF_UP)
        : '100';
$kpi_total_talk_per_day_team_total =
    ($kpi_total_talk_per_day_total) ? round(($talk_total / $kpi_total_talk_per_day_total) * 100, 2, PHP_ROUND_HALF_UP)
        : '100';
$kpi_total_note_per_day_team_total =
    ($kpi_total_note_per_day_total) ? round(($social_chat_total / $kpi_total_note_per_day_total) * 100, 2,
        PHP_ROUND_HALF_UP) : '100';
$kpi_total_f2f_meeting_per_day_team_total =
    ($kpi_total_f2f_meeting_per_day_total) ? round(($f2f_meeting_total / $kpi_total_f2f_meeting_per_day_total) * 100, 2,
        PHP_ROUND_HALF_UP) : '100';
$kpi_total_sales_activity_team_total =
    ($kpi_total_sales_activity_total) ? round(($total_sales_activity_total / $kpi_total_sales_activity_total) * 100, 2,
        PHP_ROUND_HALF_UP) : '100';

// $kpi_total_new_db_team_total = ($kpi_total_new_db_total) ? round(($new_db_total / $kpi_total_new_db_total) * 100, 2, PHP_ROUND_HALF_UP) : '100';
// $kpi_total_new_contact_team_total = ($kpi_total_new_contact_total) ? round(($new_contact_total / $kpi_total_new_contact_total) * 100, 2, PHP_ROUND_HALF_UP) : '100';

?>
<tr class="full_all_team_kpi">
    <td class="text-center" colspan="2"><?php
        echo _l('full_all_team_kpi') ?></td>
    <td class="number_format"><?php
        echo $ranking_index_all ?></td>
    <td class="number_format"><?php
        echo $day_off_total ?></td>
    <td>
        <span class="kpi_renenue_number">
            <span class="number_format"><?php
                echo $call_total ?></span><span class="kpi_activities">
                /<span class="number_format"><?php
                    echo $kpi_total_call_per_day_total ?></span></span>
        </span>
        <span class="kpi_renenue">
            <span class="kpi_renenue_value"><?php
                echo $kpi_total_call_per_day_team_total ?></span>%
        </span>
    </td>
    <td>
        <span class="kpi_renenue_number">
            <span class="number_format"><?php
                echo $talk_total ?></span><span class="kpi_activities">
                /<span class="number_format"><?php
                    echo $kpi_total_talk_per_day_total ?></span></span>
        </span>
        <span class="kpi_renenue">
            <span class="kpi_renenue_value"><?php
                echo $kpi_total_talk_per_day_team_total ?></span>%
        </span>
    </td>
    <td>
        <span class="kpi_renenue_number">
            <span class="number_format"><?php
                echo $social_chat_total ?></span><span class="kpi_activities">
                /<span class="number_format"><?php
                    echo $kpi_total_note_per_day_total ?></span></span>
        </span>
        <span class="kpi_renenue">
            <span class="kpi_renenue_value"><?php
                echo $kpi_total_note_per_day_team_total ?></span>%
        </span>
    </td>
    <td>
        <span class="kpi_renenue_number">
            <span class="number_format"><?php
                echo $f2f_meeting_total ?></span><span class="kpi_activities">
                /<span class="number_format"><?php
                    echo $kpi_total_f2f_meeting_per_day_total ?></span></span>
        </span>
        <span class="kpi_renenue">
            <span class="kpi_renenue_value"><?php
                echo $kpi_total_f2f_meeting_per_day_team_total ?></span>%
        </span>
    </td>
    <td>
        <span class="kpi_renenue_number">
            <span class="number_format"><?php
                echo $total_sales_activity_total; ?></span>
            <span class="kpi_activities">
                /<span class="number_format"><?php
                    echo $kpi_total_sales_activity_total; ?></span>
            </span>
        </span>
        <span class="kpi_renenue">
            <span class="kpi_renenue_value">
                <?php
                echo $kpi_total_sales_activity_total > 0 ?
                    round(($total_sales_activity_total / $kpi_total_sales_activity_total) * 100, 2) : '0'; ?>%
            </span>
        </span>
    </td>
</tr>

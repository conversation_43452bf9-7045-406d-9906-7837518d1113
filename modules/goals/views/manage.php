<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
    <div class="panel_s">
    <div class="panel-body">
    
    <?php if(has_permission('kpi','','edit')){ ?>
                     <div class="_buttons">
                        <a href="<?php echo admin_url('ranking/kpi'); ?>" class="btn btn-info pull-left display-block"><?php echo _l('setting_kpi'); ?></a>
                    </div>
                    <div class="clearfix"></div>
                    <hr class="hr-panel-heading" />
                    <?php } ?>
   
    <div class="row">
            <div class="col-md-12">
                <div class="result_revenue">
                    <div class="result_revenue_title"><?php echo _l('result_revenue_title'); ?></div>
                    <div class="content_tab_revenue">
                        <ul class="nav nav-tabs">
                            <li class="active activity-tab">
                                <a data-toggle="tab" href="#revenue_day"><?php echo _l('view_date'); ?></a>
                            </li>
                            <li class="plan-tab">
                                <a data-toggle="tab" href="#revenue_month"><?php echo _l('month'); ?></a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <div id="revenue_day" class="tab-pane fade in active">
                                <div class="content_search_day">
                                <div class="col-ng-date">
                                    <label>Ngày</label>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group date ">
                                        <input autocomplete="off" value="<?php echo $current_date ?>" placeholder="<?php echo _l('select_day') ?>"
                                            type="text" autocomplete="off" class="form-control datepicker datepicker_revenue"/>
                                        <div class="input-group-addon">
                                            <i class="fa fa-calendar calendar-icon"></i>
                                        </div>
                                    </div>
                                </div>
                                </div>

                                <div class="result_get_revenue_day result_get_table">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <td rowspan="2" style="width:150px">Team</td>
                                                <td rowspan="2">Salesperson</td>
                                                <td rowspan="2">Day-offs</td>
                                                <!-- <td colspan="3">Closed Revenue</td> -->
                                                <td colspan="2">Opportunities</td>
                                                <td colspan="4">Billing Revenue</td>
                                                <!-- <td rowspan="2">Paid Revenue</td> -->
                                            </tr>
                                            <tr>
                                                <!-- <td>Case</td>
                                                <td>New</td>
                                                <td>Rev</td> -->
                                                <td>Case</td>
                                                <td>Proposal Amount</td>
                                                <td>Case</td>
                                                <td>New</td>
                                                <td>JDs</td>
                                                <td>Rev</td>
                                            </tr>
                                        </thead>

                                        <tbody>
                                            
                                        </tbody>
                                            
                                    </table>
                                </div>

                            </div>

                            <div id="revenue_month" class="tab-pane fade">
                            <div class="content_search_day">
                                <div class="col-ng-date">
                                    <label><?php echo _l('month') ?></label>
                                </div>
                                <div class="col-md-2">
                                <div class="input-group date"><input type="text" id="month_kpi_1" name="month_kpi"
                                class="form-control datepicker_my" value="<?php echo date('m.Y',time()) ?>" autocomplete="off">
                            <input type="hidden" id="kpi_id" name="kpi_id" value="">
                            <div class="input-group-addon">
                                <i class="fa fa-calendar calendar-icon"></i>
                            </div>
                        </div>
                                </div>
                                </div>

                                <div class="result_get_revenue_month result_get_table">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <td rowspan="2" style="width:150px">Team</td>
                                                <td rowspan="2">Salesperson</td>
                                                <td rowspan="2">Day-offs</td>
                                                <td rowspan="2">KPI Rev</td>
                                                <!-- <td colspan="3">Closed Revenue</td> -->
                                                <td colspan="2">Opportunities</td>
                                                <td colspan="4">Billing Revenue</td>
                                                <!-- <td rowspan="2">Paid Revenue</td> -->
                                            </tr>
                                            <tr>
                                                <!-- <td>Case</td>
                                                <td>New</td>
                                                <td>Rev</td> -->
                                                <td>Case</td>
                                                <td>Proposal Amount</td>
                                                <td>Case</td>
                                                <td>New</td>
                                                <td>JDs</td>
                                                <td>Rev</td>
                                            </tr>
                                        </thead>

                                        <tbody>
                                            
                                        </tbody>
                                            
                                    </table>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>
    </div>

    <div class="row">
            <div class="col-md-12">
                <div class="result_revenue">
                    <div class="result_revenue_title"><?php echo _l('result_activities_title'); ?></div>
                    <div class="content_tab_revenue">
                        <ul class="nav nav-tabs">
                            <li class="active activity-tab">
                                <a data-toggle="tab" href="#activities_day"><?php echo _l('view_date'); ?></a>
                            </li>
                            <li class="plan-tab">
                                <a data-toggle="tab" href="#activities_month"><?php echo _l('month'); ?></a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <div id="activities_day" class="tab-pane fade in active">
                                <div class="content_search_day">
                                <div class="col-ng-date">
                                    <label>Ngày</label>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group date ">
                                        <input autocomplete="off" value="<?php echo $current_date ?>" placeholder="<?php echo _l('select_day') ?>"
                                            type="text" autocomplete="off" class="form-control datepicker datepicker_activities"/>
                                        <div class="input-group-addon">
                                            <i class="fa fa-calendar calendar-icon"></i>
                                        </div>
                                    </div>
                                </div>
                                </div>

                                <div class="result_get_activities_day result_get_table">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <td style="width:150px">Team</td>
                                                <td>Salesperson</td>
                                                <td>Ranking index</td>
                                                <td>Day-offs</td>
                                                <td>
                                                    <div>Call</div>
                                                    <div class="note_thead_activities">(<?php echo _l('call_number') ?>)</div>
                                                </td>
                                                <td>
                                                    <div>Talk</div>
                                                    <div class="note_thead_activities">(<?php echo _l('minutes') ?>)</div>
                                                </td>
                                                <td>
                                                    <div>Social Chat</div>
                                                    <div class="note_thead_activities">
                                                        (Without Call contact channel)<br>
                                                        (Has an attachment)
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>F2F meeting</div>
                                                    <div class="note_thead_activities">(<?php echo _l('f2f_meeting_note'); ?>)</div>
                                                </td>
                                                <td>
                                                    <div>Total Sales Activity</div>
                                                </td>
                                            </tr>
                                        </thead>

                                        <tbody>
                                           
                                        </tbody>
                                            
                                    </table>
                                </div>

                            </div>

                            <div id="activities_month" class="tab-pane fade">
                            <div class="content_search_day">
                                <div class="col-ng-date">
                                    <label><?php echo _l('month') ?></label>
                                </div>
                                <div class="col-md-2">
                                <div class="input-group date"><input type="text" id="month_kpi_2" name="month_kpi"
                                class="form-control datepicker_my" value="<?php echo date('m.Y',time()) ?>" autocomplete="off">
                            <input type="hidden" id="kpi_id" name="kpi_id" value="">
                            <div class="input-group-addon">
                                <i class="fa fa-calendar calendar-icon"></i>
                            </div>
                        </div>
                                </div>
                                </div>

                                <div class="result_get_activities_month result_get_table">
                                    <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <td style="width:150px">Team</td>
                                                    <td>Salesperson</td>
                                                    <td>Ranking index</td>
                                                    <td>Day-offs</td>
                                                    <td>
                                                        <div>Call</div>
                                                        <div class="note_thead_activities">(<?php echo _l('call_number') ?>)</div>
                                                    </td>
                                                    <td>
                                                        <div>Talk</div>
                                                        <div class="note_thead_activities">(<?php echo _l('minutes') ?>)</div>
                                                    </td>
                                                    <td>
                                                        <div>Social Chat</div>
                                                        <div class="note_thead_activities">
                                                            (Without Call contact channel)<br>
                                                            (Has an attachment)
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>F2F meeting</div>
                                                    </td>
                                                    <td>
                                                        <div>Total Sales Activity</div>
                                                    </td>
                                                </tr>
                                            </thead>

                                            <tbody>
                                            
                                            </tbody>
                                                
                                        </table>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>
    </div>



    </div>
    </div>

    
        <div class="row hidden">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                     <?php if(has_permission('goals','','create')){ ?>
                     <div class="_buttons">
                        <a href="<?php echo admin_url('goals/goal'); ?>" class="btn btn-info pull-left display-block"><?php echo _l('new_goal'); ?></a>
                    </div>
                    <div class="clearfix"></div>
                    <hr class="hr-panel-heading" />
                    <?php } ?>
                    <?php render_datatable(array(
                        _l('goal_subject'),
                        _l('staff_member'),
                        _l('goal_achievement'),
                        _l('goal_start_date'),
                        _l('goal_end_date'),
                        _l('goal_type'),
                        _l('goal_progress'),
                        ),'goals'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php init_tail(); ?>

<script>

    $(function(){
        init_datepicker();
        get_revenue_month();
        get_revenue_day();

        $('.datepicker_revenue').change(function(){
            get_revenue_day();
        });

        var value_revenue_day = '';
        var value_activities_day = '';

        function get_revenue_day()
        {
            var value = $('.datepicker_revenue').val();
            if(value != '' && value != value_revenue_day)
            {
                value_revenue_day = value;
                $.ajax({
                type: 'get',
                dataType: "json",
                url: site_url + 'admin/goals/get_revenue_day',
                data: {
                    revenue_day: value
                },
                success: function(res) {
                    $('.result_get_revenue_day tbody').html(res.html);
                    number_format();
                }
            });
            }
        }

        function get_revenue_month()
        {
            var value = $('#revenue_month .datepicker_my').val();
            if(value != '')
            {
                $.ajax({
                type: 'get',
                dataType: "json",
                url: site_url + 'admin/goals/get_revenue_month',
                data: {
                    revenue_month: value
                },
                success: function(res) {
                    $('.result_get_revenue_month tbody').html(res.html);
                    kpi_null_revenue();
                    number_format();
                    kpi_format();
                }
            });
            }
        }

         
        // Activities
        get_activities_day();
        get_activities_month();

        $('.datepicker_activities').change(function(){
            get_activities_day();
        });

        function get_activities_day()
        {
            var value = $('.datepicker_activities').val();
            if(value != '' && value != value_activities_day)
            {
                value_activities_day = value;
                $.ajax({
                type: 'get',
                dataType: "json",
                url: site_url + 'admin/goals/get_activities_day',
                data: {
                    activities_day: value
                },
                success: function(res) {
                    $('.result_get_activities_day tbody').html(res.html);
                    number_format();
                    kpi_format();
                    kpi_null('.result_get_activities_day');
                }
            });
            }
        }

        
        function get_activities_month()
        {
            var value = $('#activities_month .datepicker_my').val();
            if(value != '')
            {
                $.ajax({
                type: 'get',
                dataType: "json",
                url: site_url + 'admin/goals/get_activities_month',
                data: {
                    activities_month: value
                },
                success: function(res) {
                    $('.result_get_activities_month tbody').html(res.html);
                    number_format();
                    kpi_format();
                    kpi_null('.result_get_activities_month');
                }
            });
            }
        }


    // Genarate datapicker month and year
    var stt_month = 1;
    $('.datepicker_my').map(function() {
        var render_datapicker_my = '<div id="calendar_my_'+ stt_month +'" class="datepicker-months"><table class="table-condensed"><thead><tr><th colspan="7" class="datepicker-title" style="display: none;"></th></tr><tr><th class="prev">«</th><th colspan="5" class="datepicker-switch">2023</th><th class="next">»</th></tr></thead><tbody><tr><td colspan="7"><span month="01" class="month m01">Jan</span><span month="02" class="month m02">Feb</span><span month="03" class="month m03">Mar</span><span month="04" class="month m04">Apr</span><span month="05" class="month m05">May</span><span month="06" class="month m06">Jun</span><span month="07" class="month m07">Jul</span><span month="08" class="month m08">Aug</span><span month="09" class="month m09">Sep</span><span month="10" class="month m10">Oct</span><span month="11" class="month m11">Nov</span><span month="12" class="month m12">Dec</span></td></tr></tbody><tfoot><tr><th colspan="7" class="today" style="display: none;">Today</th></tr><tr><th colspan="7" class="clear" style="display: none;">Clear</th></tr></tfoot></table></div>';
        $(this).parent().append(render_datapicker_my);
        stt_month++;
        // Gán giá trị
        var value = $(this).val();
        selected_month_year(value)
    });


    function selected_month_year(string)
    {
        var arr = string.split('.');
        $('.datepicker-months .datepicker-switch').html(arr[1]);
        $('.datepicker-months .month').removeClass('active');
        $('.datepicker-months .m'+ arr[0]).addClass('active');
    }

    // Button next
    $(document).on("click", ".datepicker-months .next", function() {
        var year_current = parseInt($('.datepicker-months .datepicker-switch').html());
        $('.datepicker-months .datepicker-switch').html(year_current + 1);
        $('.datepicker-months .month').removeClass('active');
    });

    // Button prev
    $(document).on("click", ".datepicker-months .prev", function() {
        var year_current = parseInt($('.datepicker-months .datepicker-switch').html());
        $('.datepicker-months .datepicker-switch').html(year_current - 1);
        $('.datepicker-months .month').removeClass('active');
    });

    // Chọn tháng revenue
    $(document).on("click", "#revenue_month .datepicker-months .month", function(event) {
        event.preventDefault()
        var month_current = $(this).attr('month');
        $('#revenue_month .datepicker-months .month').removeClass('active');
        $(this).addClass('active');

        var year_current = parseInt($('#revenue_month .datepicker-months .datepicker-switch').html());

        var day_current = month_current + '.' + year_current;
        $('#revenue_month .datepicker_my').val(day_current);
        $('#revenue_month .datepicker-months').toggle();

        get_revenue_month();        
    });

    // Chọn tháng activities
    $(document).on("click", "#activities_month .datepicker-months .month", function(event) {
        event.preventDefault()
        var month_current = $(this).attr('month');
        $('#activities_month .datepicker-months .month').removeClass('active');
        $(this).addClass('active');

        var year_current = parseInt($('#activities_month .datepicker-months .datepicker-switch').html());

        var day_current = month_current + '.' + year_current;
        $('#activities_month .datepicker_my').val(day_current);

        $('#activities_month .datepicker-months').toggle();

        get_activities_month();
        
    });

    $(document).on("click", "#revenue_month .datepicker_my", function() {
        $('#revenue_month .datepicker-months').toggle();
    });

    $(document).on("click", "#activities_month .datepicker_my", function() {
        $('#activities_month .datepicker-months').toggle();
    });

    const calendar = document.getElementById('calendar_my_1');
    const showCalendarButton = document.getElementById('month_kpi_1');
    document.addEventListener('click', (event) => {
      if (!calendar.contains(event.target) && event.target !== showCalendarButton) {
        calendar.style.display = 'none';
      }
    });

    const calendar2 = document.getElementById('calendar_my_2');
    const showCalendarButton2 = document.getElementById('month_kpi_2');
    document.addEventListener('click', (event) => {
      if (!calendar2.contains(event.target) && event.target !== showCalendarButton2) {
        calendar2.style.display = 'none';
      }
    });
    

        initDataTable('.table-goals', window.location.href, [6], [6]);
        $('.table-goals').DataTable().on('draw', function() {
            var rows = $('.table-goals').find('tr');
            $.each(rows, function() {
                var td = $(this).find('td').eq(6);
                var percent = $(td).find('input[name="percent"]').val();
                $(td).find('.goal-progress').circleProgress({
                    value: percent,
                    size: 45,
                    animation: false,
                    fill: {
                        gradient: ["#28b8da", "#059DC1"]
                    }
                })
            })
        })
    });
</script>


<script type="text/javascript">

// kpi = 0 %kpi null cell border #ccc  
function kpi_null_revenue()
{ 
    $('.result_get_revenue_month td.number_format.kpi_targets').map(function() {
        var value = parseFloat($(this).html());
        if(value == 0){
            var kpi_null = $(this).parent().find('.kpi_renenue').addClass('kpi_null');
            kpi_null.html('');
        }
    });
}

function kpi_null(class_content)
{ 
    $(class_content + ' .kpi_activities .number_format').map(function() {
        var value = parseFloat($(this).html());
        if(value == 0){
            var kpi_null = $(this).parent().parent().parent().find('.kpi_renenue').addClass('kpi_null');
            kpi_null.html('');
        }
    });
}

function kpi_format()
{ 
    $('.kpi_renenue_value').map(function() {
        var value = parseInt($(this).html());
        if(value > 100){
            $(this).parent().addClass('kpi_1');
        }else if(value == 100){
            $(this).parent().addClass('kpi_2');
        }else if(value > 49){
            $(this).parent().addClass('kpi_3');
        }else{
            $(this).parent().addClass('kpi_4');
        }
    });
}

function number_format() {
    $('.number_format').map(function() {
        $(this).html(FormatNumber($(this).html()));
    });
}

function FormatNumber(str) {
    str = str.toString();
    var strTemp = GetNumber(str);
    if (strTemp.length <= 3)
        return strTemp;
    strResult = "";
    for (var i = 0; i < strTemp.length; i++)
        strTemp = strTemp.replace(",", "");
    var m = strTemp.lastIndexOf(".");
    if (m == -1) {
        for (var i = strTemp.length; i >= 0; i--) {
            if (strResult.length > 0 && (strTemp.length - i - 1) % 3 == 0)
                strResult = "," + strResult;
            strResult = strTemp.substring(i, i + 1) + strResult;
        }
    } else {
        var strphannguyen = strTemp.substring(0, strTemp.lastIndexOf("."));
        var strphanthapphan = strTemp.substring(strTemp.lastIndexOf("."), strTemp.length);
        var tam = 0;
        for (var i = strphannguyen.length; i >= 0; i--) {

            if (strResult.length > 0 && tam == 4) {
                strResult = "," + strResult;
                tam = 1;
            }

            strResult = strphannguyen.substring(i, i + 1) + strResult;
            tam = tam + 1;
        }
        strResult = strResult + strphanthapphan;
    }
    strResult = strResult.replace(/\.00$/, '');
    return strResult;
}

function GetNumber(str) {
    var count = 0;
    for (var i = 0; i < str.length; i++) {
        var temp = str.substring(i, i + 1);
        if (!(temp == "," || temp == "." || (temp >= 0 && temp <= 9))) {
            alert_float('danger', "Vui lòng nhập số");
            return str.substring(0, i);
        }
        if (temp == " ")
            return str.substring(0, i);
        if (temp == ".") {
            if (count > 0)
                return str.substring(0, ipubl_date);
            count++;
        }
    }
    return str;
}

function IsNumberInt(str) {
    for (var i = 0; i < str.length; i++) {
        var temp = str.substring(i, i + 1);
        if (!(temp == "." || (temp >= 0 && temp <= 9))) {
            alert_float('danger', "Vui lòng nhập số");
            return str.substring(0, i);
        }
        if (temp == ",") {
            alert_float('danger', "Vui lòng nhập số");
            return str.substring(0, i);
        }
    }
    return str;
}
</script>

</body>
</html>

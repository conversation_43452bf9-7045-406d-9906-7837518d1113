<?php defined('BASEPATH') or exit('No direct script access allowed');

$day_off_total = $closed_revenue_count_total = $closed_revenue_new_total = $closed_revenue_total_total = $billing_revenue_count_total = $billing_revenue_new_total = $billing_revenue_total_qty_total = $billing_revenue_total_total = $paid_revenue_value_total = $opportunities_count_total = $opportunities_total_total = 0;

foreach ($group as $g) {

    $total_staff = count($g['revenue_day']);
    if (!$total_staff) {
        continue;
    }

    $flag = true;
    $day_off = $closed_revenue_count = $closed_revenue_new = $closed_revenue_total = $billing_revenue_count = $billing_revenue_new = $billing_revenue_total_qty = $billing_revenue_total = $paid_revenue_value = $opportunities_count = $opportunities_total = 0;

    foreach ($g['revenue_day'] as $staff) {
?>
        <tr>
            <?php
            if ($flag && $total_staff) {
            ?>
                <td class="title_team_group" rowspan="<?php echo $total_staff + 1 ?>"><?php echo $g['text'] ?></td>
            <?php
                $flag = false;
            }
            ?>
            <td staff_id="<?php echo $staff['staff_id'] ?>" class="font-weight-bold">
                <?php echo $staff['firstname'] . ' ' . $staff['lastname'] ?>
                <?php
                if (isset($paid_revenue_top[$staff['staff_id']]) && $paid_revenue_top[$staff['staff_id']]) {
                    echo '<span class="paid_revenue_top"><img src="' . $paid_revenue_top[$staff['staff_id']] . '" /></span>';
                }
                ?>
            </td>
            <td class="number_format"><?php echo $staff['day_off'] ?></td>
            <!-- <td class="number_format"><?php echo $staff['closed_revenue_count'] ?></td>
            <td class="number_format"><?php echo $staff['closed_revenue_new'] ?></td>
            <td class="number_format"><?php echo $staff['closed_revenue_total'] ?></td> -->
            <td class="number_format"><?php echo $staff['opportunities_count'] ?></td>
            <td class="number_format"><?php echo $staff['opportunities_total'] ?></td>
            <td class="number_format"><?php echo $staff['billing_revenue_count'] ?></td>
            <td class="number_format"><?php echo $staff['billing_revenue_new'] ?></td>
            <td class="number_format"><?php echo $staff['billing_revenue_total_qty'] ?></td>
            <td class="number_format"><?php echo $staff['billing_revenue_total'] ?></td>
            <!-- <td class="number_format"><?php echo $staff['paid_revenue_value'] ?></td> -->
        </tr>
    <?php

        $day_off += $staff['day_off'];
        $closed_revenue_count += $staff['closed_revenue_count'];
        $closed_revenue_new += $staff['closed_revenue_new'];
        $closed_revenue_total += $staff['closed_revenue_total'];
        $billing_revenue_count += $staff['billing_revenue_count'];
        $billing_revenue_new += $staff['billing_revenue_new'];
        $billing_revenue_total_qty += $staff['billing_revenue_total_qty'];
        $billing_revenue_total += $staff['billing_revenue_total'];
        $paid_revenue_value += $staff['paid_revenue_value'];
        $opportunities_count += $staff['opportunities_count'];
        $opportunities_total += $staff['opportunities_total'];
    }

    // Toàn team
    ?>
    <tr class="full_team_kpi">
        <td><?php echo _l('full_team_kpi') ?></td>
        <td class="number_format"><?php echo $day_off ?></td>
        <!-- <td class="number_format"><?php echo $closed_revenue_count ?></td>
        <td class="number_format"><?php echo $closed_revenue_new ?></td>
        <td class="number_format"><?php echo $closed_revenue_total ?></td> -->
        <td class="number_format"><?php echo $opportunities_count ?></td>
        <td class="number_format"><?php echo $opportunities_total ?></td>
        <td class="number_format"><?php echo $billing_revenue_count ?></td>
        <td class="number_format"><?php echo $billing_revenue_new ?></td>
        <td class="number_format"><?php echo $billing_revenue_total_qty ?></td>
        <td class="number_format"><?php echo $billing_revenue_total ?></td>
        <!-- <td class="number_format"><?php echo $paid_revenue_value ?></td> -->
    </tr>
<?php

    // Tất cả
    $day_off_total += $day_off;
    $closed_revenue_count_total += $closed_revenue_count;
    $closed_revenue_new_total += $closed_revenue_new;
    $closed_revenue_total_total += $closed_revenue_total;
    $billing_revenue_count_total += $billing_revenue_count;
    $billing_revenue_new_total += $billing_revenue_new;
    $billing_revenue_total_qty_total += $billing_revenue_total_qty;
    $billing_revenue_total_total += $billing_revenue_total;
    $paid_revenue_value_total += $paid_revenue_value;
    $opportunities_count_total += $opportunities_count;
    $opportunities_total_total += $opportunities_total;
}
?>
<tr class="full_all_team_kpi">
    <td class="text-center" colspan="2"><?php echo _l('full_all_team_kpi') ?></td>
    <td class="number_format"><?php echo $day_off_total  ?></td>
    <!-- <td class="number_format"><?php echo $closed_revenue_count_total  ?></td>
    <td class="number_format"><?php echo $closed_revenue_new_total  ?></td>
    <td class="number_format"><?php echo $closed_revenue_total_total  ?></td> -->
    <td class="number_format"><?php echo $opportunities_count_total  ?></td>
    <td class="number_format"><?php echo $opportunities_total_total  ?></td>
    <td class="number_format"><?php echo $billing_revenue_count_total  ?></td>
    <td class="number_format"><?php echo $billing_revenue_new_total ?></td>
    <td class="number_format"><?php echo $billing_revenue_total_qty_total  ?></td>
    <td class="number_format"><?php echo $billing_revenue_total_total  ?></td>
    <!-- <td class="number_format"><?php echo $paid_revenue_value_total  ?></td> -->
</tr>
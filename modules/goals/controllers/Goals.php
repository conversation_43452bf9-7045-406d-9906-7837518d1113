<?php

use Entities\M3cCall;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Class Goals
 * @property Goals_model $goals_model
 * @property Staff_model $staff_model
 */
class Goals extends AdminController
{
    public $team_sales;
    public $arr_team_sales;
    protected $CI;

    public function __construct()
    {
        parent::__construct();
        $this->load->model('goals_model');

        $this->CI = &get_instance();
        $this->CI->load->helper('date');
        $this->load->helper(array('client_requests'));
        $this->team_sales = get_group_team_sale();
        $this->arr_team_sales = get_group_team_id_new();
    }

    /* List all announcements */
    public function index()
    {
        if (!has_permission('goals', '', 'view')) {
            access_denied('goals');
        }
        if ($this->input->is_ajax_request()) {
            $this->app->get_table_data(module_views_path('goals', 'table'));
        }
        $this->app_scripts->add('circle-progress-js', 'assets/plugins/jquery-circle-progress/circle-progress.min.js');
        $data['title']                 = _l('goals_tracking');

        $data['current_date'] = date('d.m.Y', time());

        $this->load->view('manage', $data);
    }


    public function get_activities_day()
    {
        $data = $this->input->get();

        if (!$data['activities_day'])
            return $this->reponse_error(_l('day_null'));


        // Sale Activities DAY //

        // Thống kê kết quả kinh doanh, daily
        $string_day = $data['activities_day'];

        $today_00 = date('Y-m-d 00:00:00', strtotime($string_day));

        $array_day = convert_day_to_from($string_day);
        $array_month = convert_month_to_from($string_day);

        // Day off
        $data['day_off'] = $this->goals_model->staticst_day_off_day($array_day);

        // Số lượng cuộc gọi, Tổng thời gian gọi (thành công >=5s), only call out
        $data['call_talk'] = $this->goals_model->call_talk($array_day);

        // Get the number of successful calls, the total call time (successful >=5s) (call out and call in)
        $data['call_talk_all'] = $this->goals_model->call_talk_all($array_day);

        // Social chat Đếm số công ty có ghi chú liên hệ thành công, không phải call, có attachment.
        $data['social_chat'] = $this->goals_model->social_chat($array_day);

        // F2F meeting - Đếm số cuộc họp trực tiếp (contact_channel = 7)
        $data['f2f_meeting'] = $this->goals_model->f2f_meeting($array_day);

        // KPI Rev Chỉ tiêu doanh thu bán hàng mỗi tháng của từng salesperson (chỉ có trong bảng Monthly)
        $data['kpi_rev'] = $this->goals_model->kpi_rev($array_month);

        // KPI được giảm của nhân viên theo ngày
        $data['kpi_rev_down'] = $this->goals_model->kpi_rev_down($array_day, $array_month);

        // KPI được giảm của nhân viên từ ngày đầu tháng đến ngày hiện tại
        $data['kpi_rev_down_to_from'] = $this->goals_model->kpi_rev_down_to_from_day($today_00, $array_month);

        // -----------------------

        // Danh sách nhân viên trong team

        // Tất cả nhân viên
        $arr_staff = [];

        $call_all = $call_kpi_all = $talk_all = $talk_kpi_all = $social_chat_all = $social_chat_kpi_all = $social_chat_call_all = $f2f_meeting_all = 0;

        foreach ($this->team_sales as $team) {
            if ($team['value']) {
                // Lấy danh sách nhân viên trong team
                $list_staff = $this->goals_model->list_staff_team($team['value'], $array_month, true);
                $arr_day = [];
                $call_total = $call_kpi_total = $talk_total = $talk_kpi_total = $social_chat_total = $social_chat_kpi_total = $social_chat_call_total = $f2f_meeting_total = 0;
                foreach ($list_staff as $staff) {
                    $row_day = [];
                    // Gán giá trị thống kê cho từng nhân viên theo ngày
                    $row_day['staff_id'] = $staff['staff_id'];
                    $row_day['firstname'] = $staff['firstname'];
                    $row_day['lastname'] = $staff['lastname'];
                    $row_day['datecreated'] = $staff['datecreated'];

                    $row_day['day_off'] = (isset($data['day_off'][$staff['staff_id']]['total_days'])) ? $data['day_off'][$staff['staff_id']]['total_days'] : 0;

                    $row_day['call'] = (isset($data['call_talk'][$staff['staff_id']]['count'])) ? $data['call_talk'][$staff['staff_id']]['count'] : 0;
                    $row_day['talk'] = (isset($data['call_talk_all'][$staff['staff_id']]['total_giay'])) ? $data['call_talk_all'][$staff['staff_id']]['total_giay'] : 0;
                    $row_day['talk'] = round($row_day['talk'] / 60, 2, PHP_ROUND_HALF_UP);

                    $row_day['social_chat'] = $data['social_chat'][$staff['staff_id']]['count'] ?? 0;
                    $row_day['f2f_meeting'] = $data['f2f_meeting'][$staff['staff_id']]['count'] ?? 0;

                    $row_day['kpi'] = (isset($data['kpi_rev'][$staff['staff_id']])) ? $data['kpi_rev'][$staff['staff_id']] : array(
                        'total_call_per_day' => 0,
                        'total_talk_per_day' => 0,
                        'total_note_per_day' => 0,
                        'total_working_days' => 0,
                        'target_amount' => 0,
                    );

                    // KPI giảm trừ cho ngày nghỉ phép nếu có
                    if (isset($data['kpi_rev_down'][$staff['staff_id']])) {
                        $row_day['kpi']['total_call_per_day'] = $row_day['kpi']['total_call_per_day'] - $data['kpi_rev_down'][$staff['staff_id']]['total_call_per_day'];
                        $row_day['kpi']['total_talk_per_day'] = $row_day['kpi']['total_talk_per_day'] - $data['kpi_rev_down'][$staff['staff_id']]['total_talk_per_day'];
                        $row_day['kpi']['total_note_per_day'] = $row_day['kpi']['total_note_per_day'] - $data['kpi_rev_down'][$staff['staff_id']]['total_note_per_day'];
                        $row_day['kpi']['target_amount'] = $row_day['kpi']['target_amount'] - $data['kpi_rev_down'][$staff['staff_id']]['target_amount'];
                    }

                    //// %kpi Call 25%
                    $kpi_call = ($row_day['kpi']['total_call_per_day']) ? ($row_day['call'] / $row_day['kpi']['total_call_per_day']) * 50 : 0;

                    // %kpi Talk 25%
                    $kpi_talk = ($row_day['kpi']['total_talk_per_day']) ? ($row_day['talk'] / $row_day['kpi']['total_talk_per_day']) * 30 : 0;

                    // %kpi note 20%
                    $kpi_social_chat = ($row_day['kpi']['total_note_per_day']) ? ($row_day['social_chat'] / $row_day['kpi']['total_note_per_day']) * 20 : 0;

                    $row_day['rank'] = round($kpi_call + $kpi_talk + $kpi_social_chat, 2, PHP_ROUND_HALF_UP);

                    $call_total += $row_day['call'];
                    $call_kpi_total += $row_day['kpi']['total_call_per_day'];

                    $talk_total += $row_day['talk'];
                    $talk_kpi_total += $row_day['kpi']['total_talk_per_day'];

                    $social_chat_total += $row_day['social_chat'];
                    $social_chat_kpi_total += $row_day['kpi']['total_note_per_day'];
                    $social_chat_call_total += $row_day['social_chat'] + $row_day['call'];
                    $f2f_meeting_total += $row_day['f2f_meeting'];

                    // end total
                    $arr_staff[] = array(
                        'staff_id' => $staff['staff_id'],
                        'rank_total' => $row_day['rank'],
                        'talk' => $row_day['talk'],
                        'call' => $row_day['call'],
                    );

                    $arr_day[$staff['staff_id']] = $row_day;
                }

                // Sắp xếp nhân viên trong group call + talk + connect theo thứ tự giảm dần trong team theo ngày, nếu bằng nhau set tiếp Talk, Call
                usort($arr_day, function ($a, $b) {
                    if ($a['rank'] !== $b['rank']) {
                        return ($a['rank'] < $b['rank']) ? 1 : -1;
                    } elseif ($a['talk'] !== $b['talk']) {
                        return ($a['talk'] < $b['talk']) ? 1 : -1;
                    } elseif ($a['call'] !== $b['call']) {
                        return ($a['call'] < $b['call']) ? 1 : -1;
                    } elseif ($a['staff_id'] !== $b['staff_id']) {
                        return $a['staff_id'] - $b['staff_id'];
                    }
                });

                $team['call_total'] = $call_total;
                $team['call_kpi_total'] = $call_kpi_total;
                $team['call_kpi'] = ($call_kpi_total) ? ($call_total / $call_kpi_total) * 50 : 0;

                $team['talk_total'] = $talk_total;
                $team['talk_kpi_total'] = $talk_kpi_total;
                $team['talk_kpi'] = ($talk_kpi_total) ? ($talk_total / $talk_kpi_total) * 30 : 0;

                $team['social_chat_total'] = $social_chat_total;
                $team['social_chat_kpi'] = ($social_chat_kpi_total) ? ($social_chat_total / $social_chat_kpi_total) * 20 : 0;
                $team['social_chat_call_total'] = $social_chat_call_total;
                $team['f2f_meeting_total'] = $f2f_meeting_total;

                $team['rank_total'] = round($team['call_kpi'] + $team['talk_kpi'] + $team['social_chat_kpi'], 2, PHP_ROUND_HALF_UP);


                $team['activies_day'] = $arr_day;

                $data['group'][$team['value']] = $team;

                $call_all += $call_total;
                $call_kpi_all += $call_kpi_total;

                $talk_all += $talk_total;
                $talk_kpi_all += $talk_kpi_total;

                $social_chat_all += $social_chat_total;
                $social_chat_kpi_all += $social_chat_kpi_total;
                $social_chat_call_all += $social_chat_call_total;
                $f2f_meeting_all += $f2f_meeting_total;
            }
        }

        // % Tất cả Ranking index total kpi
        $call_kpi = ($call_kpi_all) ? ($call_all / $call_kpi_all) * 50 : 0;
        $talk_kpi = ($talk_kpi_all) ? ($talk_all / $talk_kpi_all) * 30 : 0;
        $social_chat_kpi = ($social_chat_kpi_all) ? ($social_chat_all / $social_chat_kpi_all) * 20 : 0;

        $data['ranking_index_all'] = round($call_kpi + $talk_kpi + $social_chat_kpi, 2, PHP_ROUND_HALF_UP);
        $data['f2f_meeting_all'] = $f2f_meeting_all;

        // Sắp xếp team call + talk + connect theo thứ tự giảm dần trong team theo ngày, nếu bằng nhau set tiếp Talk, Call
        usort($data['group'], function ($a, $b) {
            if ($a['rank_total'] !== $b['rank_total']) {
                return ($a['rank_total'] < $b['rank_total']) ? 1 : -1;
            } elseif ($a['talk_total'] !== $b['talk_total']) {
                return ($a['talk_total'] < $b['talk_total']) ? 1 : -1;
            } elseif ($a['call_total'] !== $b['call_total']) {
                return ($a['call_total'] < $b['call_total']) ? 1 : -1;
            }
        });


        // Top 10
        $data['paid_revenue_top'] = [];  // Xử lý
        // Sắp xếp tất cả Paid Revenue, Billing Rev, Case của Opportunities
        usort($arr_staff, function ($a, $b) {
            if ($a['rank_total'] !== $b['rank_total']) {
                return ($a['rank_total'] < $b['rank_total']) ? 1 : -1;
            } elseif ($a['talk'] !== $b['talk']) {
                return ($a['talk'] < $b['talk']) ? 1 : -1;
            } elseif ($a['call'] !== $b['call']) {
                return ($a['call'] < $b['call']) ? 1 : -1;
            } elseif ($a['staff_id'] !== $b['staff_id']) {
                return $a['staff_id'] - $b['staff_id'];
            }
        });

        // Lấy danh sách 10 nhân viên top
        $top = 0;

        foreach ($arr_staff as $staff) {
            if (!$staff['staff_id'])
                continue;

            if (($staff['rank_total'] || $staff['talk'] || $staff['call'])  && $top < 10) {
                $top++;
                $data['paid_revenue_top'][$staff['staff_id']] = base_url('assets/images/kpi_top_' . $top . '.png');
            } else {
                break;
            }
        }

        $result = json_encode(array(
            'error' => 0,
            'html' => $this->load->view('activities_day', $data, true),
            'mess' => _l('success_json')
        ));

        print_r($result);
        die;
    }



    public function get_activities_month()
    {
        $data = $this->input->get();

        if (!$data['activities_month'])
            return $this->reponse_error(_l('day_null'));


        // Sale Activities DAY

        // Thống kê kết quả kinh doanh, daily
        $string_day = '01.' . $data['activities_month'];
        $array_month = convert_month_to_from($string_day);

        // Day off
        $data['day_off'] = $this->goals_model->staticst_day_off_day($array_month);

        // Số lượng cuộc gọi, Tổng thời gian gọi (thành công >=5s), only call out
        $data['call_talk'] = $this->goals_model->call_talk($array_month);

        // Get the number of successful calls, the total call time (successful >=5s) (call out and call in)
        $data['call_talk_all'] = $this->goals_model->call_talk_all($array_month);

        // Social chat Đếm số công ty có ghi chú liên hệ thành công, không phải call, có attachment.
        $data['social_chat'] = $this->goals_model->social_chat($array_month);

        // F2F meeting - Đếm số cuộc họp trực tiếp (contact_channel = 7)
        $data['f2f_meeting'] = $this->goals_model->f2f_meeting($array_month);

        // KPI Rev Chỉ tiêu doanh thu bán hàng mỗi tháng của từng salesperson (chỉ có trong bảng Monthly)
        $data['kpi_rev'] = $this->goals_model->kpi_rev($array_month);

        // KPI được giảm của nhân viên theo ngày
        $data['kpi_rev_down'] = $this->goals_model->kpi_rev_down_month($array_month);

        // -----------------------

        // Danh sách nhân viên trong team

        // Tất cả nhân viên
        $arr_staff = [];
        $call_all = $call_kpi_all = $talk_all = $talk_kpi_all = $social_chat_all = $social_chat_kpi_all = $social_chat_call_all = $f2f_meeting_all = 0;
        foreach ($this->team_sales as $team) {
            if ($team['value']) {
                // Lấy danh sách nhân viên trong team
                $list_staff = $this->goals_model->list_staff_team($team['value'], $array_month, true);
                $arr_day = [];
                $call_total = $call_kpi_total = $talk_total = $talk_kpi_total = $social_chat_total = $social_chat_kpi_total = $social_chat_call_total = $f2f_meeting_total = 0;
                foreach ($list_staff as $staff) {
                    $row_day = [];
                    // Gán giá trị thống kê cho từng nhân viên theo ngày
                    $row_day['staff_id'] = $staff['staff_id'];
                    $row_day['firstname'] = $staff['firstname'];
                    $row_day['lastname'] = $staff['lastname'];
                    $row_day['datecreated'] = $staff['datecreated'];

                    $row_day['day_off'] = (isset($data['day_off'][$staff['staff_id']]['total_days'])) ? $data['day_off'][$staff['staff_id']]['total_days'] : 0;

                    $row_day['call'] = (isset($data['call_talk'][$staff['staff_id']]['count'])) ? $data['call_talk'][$staff['staff_id']]['count'] : 0;
                    $row_day['talk'] = (isset($data['call_talk_all'][$staff['staff_id']]['total_giay'])) ? $data['call_talk_all'][$staff['staff_id']]['total_giay'] : 0;
                    $row_day['talk'] = round($row_day['talk'] / 60, 2, PHP_ROUND_HALF_UP);

                    $row_day['social_chat'] = $data['social_chat'][$staff['staff_id']]['count'] ?? 0;
                    $row_day['f2f_meeting'] = $data['f2f_meeting'][$staff['staff_id']]['count'] ?? 0;

                    $row_day['kpi'] = (isset($data['kpi_rev'][$staff['staff_id']])) ? $data['kpi_rev'][$staff['staff_id']] : array(
                        'total_call_per_day' => 0,
                        'total_talk_per_day' => 0,
                        'total_note_per_day' => 0,
                        'total_working_days' => 0,
                        'target_amount' => 0,
                    );

                    // KPI giảm trừ cho ngày nghỉ phép nếu có
                    if (isset($data['kpi_rev_down'][$staff['staff_id']])) {
                        $row_day['kpi']['total_call_per_day'] = ($row_day['kpi']['total_call_per_day'] * $row_day['kpi']['total_working_days']) - $data['kpi_rev_down'][$staff['staff_id']]['total_call_per_day_down'];
                        $row_day['kpi']['total_talk_per_day'] = ($row_day['kpi']['total_talk_per_day'] * $row_day['kpi']['total_working_days']) - $data['kpi_rev_down'][$staff['staff_id']]['total_talk_per_day_down'];
                        $row_day['kpi']['total_note_per_day'] = ($row_day['kpi']['total_note_per_day'] * $row_day['kpi']['total_working_days']) - $data['kpi_rev_down'][$staff['staff_id']]['total_note_per_day_down'];
                    } else {
                        $row_day['kpi']['total_call_per_day'] = ($row_day['kpi']['total_call_per_day'] * $row_day['kpi']['total_working_days']);
                        $row_day['kpi']['total_talk_per_day'] = ($row_day['kpi']['total_talk_per_day'] * $row_day['kpi']['total_working_days']);
                        $row_day['kpi']['total_note_per_day'] = ($row_day['kpi']['total_note_per_day'] * $row_day['kpi']['total_working_days']);
                    }

                    // %kpi Call 50%
                    $kpi_call = ($row_day['kpi']['total_call_per_day']) ? ($row_day['call'] / $row_day['kpi']['total_call_per_day']) * 50 : 0;

                    // %kpi Talk 30%
                    $kpi_talk = ($row_day['kpi']['total_talk_per_day']) ? ($row_day['talk'] / $row_day['kpi']['total_talk_per_day']) * 30 : 0;

                    // %kpi note 20%
                    $kpi_social_chat = ($row_day['kpi']['total_note_per_day']) ? ($row_day['social_chat'] / $row_day['kpi']['total_note_per_day']) * 20 : 0;

                    $row_day['rank'] = round($kpi_call + $kpi_talk + $kpi_social_chat, 2, PHP_ROUND_HALF_UP);

                    $call_total += $row_day['call'];
                    $call_kpi_total += $row_day['kpi']['total_call_per_day'];

                    $talk_total += $row_day['talk'];
                    $talk_kpi_total += $row_day['kpi']['total_talk_per_day'];

                    $social_chat_total += $row_day['social_chat'];
                    $social_chat_kpi_total += $row_day['kpi']['total_note_per_day'];
                    $social_chat_call_total += $row_day['social_chat'] + $row_day['call'];
                    $f2f_meeting_total += $row_day['f2f_meeting'];

                    // end total
                    $arr_staff[] = array(
                        'staff_id' => $staff['staff_id'],
                        'rank_total' => $row_day['rank'],
                        'talk' => $row_day['talk'],
                        'call' => $row_day['call'],
                    );

                    $arr_day[$staff['staff_id']] = $row_day;
                }

                // Sắp xếp nhân viên trong group call + talk + connect theo thứ tự giảm dần trong team theo ngày, nếu bằng nhau set tiếp Talk, Call
                usort($arr_day, function ($a, $b) {
                    if ($a['rank'] !== $b['rank']) {
                        return ($a['rank'] < $b['rank']) ? 1 : -1;
                    } elseif ($a['talk'] !== $b['talk']) {
                        return ($a['talk'] < $b['talk']) ? 1 : -1;
                    } elseif ($a['call'] !== $b['call']) {
                        return ($a['call'] < $b['call']) ? 1 : -1;
                    } elseif ($a['staff_id'] !== $b['staff_id']) {
                        return $a['staff_id'] - $b['staff_id'];
                    }
                });

                $team['call_total'] = $call_total;
                $team['call_kpi_total'] = $call_kpi_total;
                $team['call_kpi'] = ($call_kpi_total) ? ($call_total / $call_kpi_total) * 50 : 0;

                $team['talk_total'] = $talk_total;
                $team['talk_kpi_total'] = $talk_kpi_total;
                $team['talk_kpi'] = ($talk_kpi_total) ? ($talk_total / $talk_kpi_total) * 30 : 0;
                
                $team['social_chat_total'] = $social_chat_total;
                $team['social_chat_kpi'] = ($social_chat_kpi_total) ? ($social_chat_total / $social_chat_kpi_total) * 20 : 0;
                $team['social_chat_call_total'] = $social_chat_call_total;
                
                $team['rank_total'] = round($team['call_kpi'] + $team['talk_kpi'] + $team['social_chat_kpi'], 2, PHP_ROUND_HALF_UP);

                $team['activies_day'] = $arr_day;

                $data['group'][$team['value']] = $team;

                $call_all += $call_total;
                $call_kpi_all += $call_kpi_total;

                $talk_all += $talk_total;
                $talk_kpi_all += $talk_kpi_total;

                $social_chat_all += $social_chat_total;
                $social_chat_kpi_all += $social_chat_kpi_total;
                $social_chat_call_all += $social_chat_call_total;
                $f2f_meeting_all += $f2f_meeting_total;
            }
        }

        $call_kpi = ($call_kpi_all) ? ($call_all / $call_kpi_all) * 50 : 0;
        $talk_kpi = ($talk_kpi_all) ? ($talk_all / $talk_kpi_all) * 30 : 0;
        $social_chat_kpi = ($social_chat_kpi_all) ? ($social_chat_all / $social_chat_kpi_all) * 20 : 0;

        $data['ranking_index_all'] = round($call_kpi + $talk_kpi + $social_chat_kpi, 2, PHP_ROUND_HALF_UP);
        $data['f2f_meeting_all'] = $f2f_meeting_all;

        // Sắp xếp team giảm dần call + talk + connect
        usort($data['group'], function ($a, $b) {
            if ($a['rank_total'] !== $b['rank_total']) {
                return ($a['rank_total'] < $b['rank_total']) ? 1 : -1;
            } elseif ($a['talk_total'] !== $b['talk_total']) {
                return ($a['talk_total'] < $b['talk_total']) ? 1 : -1;
            } elseif ($a['call_total'] !== $b['call_total']) {
                return ($a['call_total'] < $b['call_total']) ? 1 : -1;
            }
        });

        // Top 10
        $data['paid_revenue_top'] = [];  // Xử lý
        // Sắp xếp tất cả Paid Revenue, Billing Rev, Case của Opportunities
        usort($arr_staff, function ($a, $b) {
            if ($a['rank_total'] !== $b['rank_total']) {
                return ($a['rank_total'] < $b['rank_total']) ? 1 : -1;
            } elseif ($a['talk'] !== $b['talk']) {
                return ($a['talk'] < $b['talk']) ? 1 : -1;
            } elseif ($a['call'] !== $b['call']) {
                return ($a['call'] < $b['call']) ? 1 : -1;
            } elseif ($a['staff_id'] !== $b['staff_id']) {
                return $a['staff_id'] - $b['staff_id'];
            }
        });

        // Lấy danh sách 10 nhân viên top
        $top = 0;

        foreach ($arr_staff as $staff) {
            if (!$staff['staff_id'])
                continue;

            if (($staff['rank_total'] || $staff['talk'] || $staff['call'])  && $top < 10) {
                $top++;
                $data['paid_revenue_top'][$staff['staff_id']] = base_url('assets/images/kpi_top_' . $top . '.png');
            } else {
                break;
            }
        }

        $result = json_encode(array(
            'error' => 0,
            'html' => $this->load->view('activities_day', $data, true),
            'mess' => _l('success_json')
        ));

        print_r($result);
        die;
    }


    public function get_revenue_day()
    {
        $data = $this->input->get();

        if (!$data['revenue_day'])
            return $this->reponse_error(_l('day_null'));


        // Thống kê kết quả kinh doanh, daily
        $string_day = $data['revenue_day'];

        $array_day = convert_day_to_from($string_day);
        $array_month = convert_month_to_from($string_day);


        // Day off
        $data['day_off'] = $this->goals_model->staticst_day_off_day($array_day);

        // Closed Revenue
        // // Closed Revenue: Case: Ngày tạo hóa đơn - Rev: Tổng thành tiền của Ngày tạo hóa đơn
        // $data['closed_revenue'] = $this->goals_model->closed_revenue($array_day);

        // // Closed Revenue: New: Ngày tạo hóa đơn từ công ty New* hoặc Fresh (Theo Loại khách hàng TopDev)
        // $data['closed_revenue_new'] = $this->goals_model->closed_revenue_new($array_day);


        // Billing Revenue Tính theo ngày chốt hóa đơn
        // Billing Revenue: Case: Số lượng hóa đơn - Rev: Tổng thành tiền của báo giá được chuyển thành hóa đơn
        $data['billing_revenue'] = $this->goals_model->billing_revenue($array_day);

        // Billing Revenue: New: Số lượng báo giá được chuyển thành hóa đơn, báo giá đến từ công ty New* hoặc Fresh (Theo Loại khách hàng TopDev)
        $data['billing_revenue_new'] = $this->goals_model->billing_revenue_new($array_day);

        // Paid Revenue Tính theo ngày thanh toán
        // Rev: Tổng thành tiền của hóa đơn đã thanh toán
        // $data['paid_revenue'] = $this->goals_model->paid_revenue($array_day, $this->arr_team_sales);

        // Opportunities Revenue Tính theo ngày tạo báo giá
        // Case: Số lượng báo giá được tạo - Amount: Tổng thành tiền của báo giá
        $data['opportunities'] = $this->goals_model->opportunities_revenue($array_day);

        // Danh sách nhân viên
        $arr_staff = [];

        foreach ($this->team_sales as $team) {
            if ($team['value']) {
                // Lấy danh sách nhân viên trong team
                $list_staff = $this->goals_model->list_staff_team($team['value'], $array_month);
                $paid_revenue_value_team = 0;
                $billing_revenue_total_team = 0;
                $opportunities_count_team = 0;
                $arr_day = [];
                foreach ($list_staff as $staff) {
                    $row_day = [];
                    // Gán giá trị thống kê cho từng nhân viên theo ngày
                    $row_day['staff_id'] = $staff['staff_id'];
                    $row_day['firstname'] = $staff['firstname'];
                    $row_day['lastname'] = $staff['lastname'];
                    $row_day['datecreated'] = $staff['datecreated'];

                    $row_day['day_off'] = (isset($data['day_off'][$staff['staff_id']]['total_days'])) ? $data['day_off'][$staff['staff_id']]['total_days'] : 0;

                    $row_day['closed_revenue_count'] = (isset($data['closed_revenue'][$staff['staff_id']]['count'])) ? $data['closed_revenue'][$staff['staff_id']]['count'] : 0;
                    $row_day['closed_revenue_total'] = (isset($data['closed_revenue'][$staff['staff_id']]['total'])) ? round($data['closed_revenue'][$staff['staff_id']]['total'], 0, PHP_ROUND_HALF_UP) : 0;
                    $row_day['closed_revenue_new'] = (isset($data['closed_revenue_new'][$staff['staff_id']]['count'])) ? round($data['closed_revenue_new'][$staff['staff_id']]['count'], 0, PHP_ROUND_HALF_UP) : 0;

                    $row_day['billing_revenue_count'] = (isset($data['billing_revenue'][$staff['staff_id']]['count'])) ? $data['billing_revenue'][$staff['staff_id']]['count'] : 0;
                    $row_day['billing_revenue_total'] = (isset($data['billing_revenue'][$staff['staff_id']]['total'])) ? round($data['billing_revenue'][$staff['staff_id']]['total'], 0, PHP_ROUND_HALF_UP) : 0;
                    $row_day['billing_revenue_total_qty'] = (isset($data['billing_revenue'][$staff['staff_id']]['total_qty'])) ? round($data['billing_revenue'][$staff['staff_id']]['total_qty'], 0, PHP_ROUND_HALF_UP) : 0;
                    $row_day['billing_revenue_new'] = (isset($data['billing_revenue_new'][$staff['staff_id']]['count'])) ? $data['billing_revenue_new'][$staff['staff_id']]['count'] : 0;


                    $row_day['opportunities_count'] = (isset($data['opportunities'][$staff['staff_id']]['count'])) ? $data['opportunities'][$staff['staff_id']]['count'] : 0;
                    $row_day['opportunities_total'] = (isset($data['opportunities'][$staff['staff_id']]['total'])) ? round($data['opportunities'][$staff['staff_id']]['total'], 0, PHP_ROUND_HALF_UP) : 0;

                    $row_day['paid_revenue_value'] = (isset($data['paid_revenue'][$staff['staff_id']]['total'])) ? round($data['paid_revenue'][$staff['staff_id']]['total'], 0, PHP_ROUND_HALF_UP) : 0;
                    $paid_revenue_value_team += $row_day['paid_revenue_value'];
                    $billing_revenue_total_team += $row_day['billing_revenue_total'];
                    $opportunities_count_team += $row_day['opportunities_count'];

                    // Nhân viên inactive, trạng thái hóa đơn được cập nhật - inactive but number billing revenue OK
                    if (!$row_day['closed_revenue_count'] && !$row_day['closed_revenue_new'] && !$row_day['billing_revenue_count'] && !$row_day['billing_revenue_new'] && !$row_day['opportunities_count'] && !$row_day['paid_revenue_value']) {
                        continue;
                    }

                    $arr_day[$staff['staff_id']] = $row_day;

                    // Đưa thông tin ranking nhân viên vào danh sách arr_staff cho công việc xếp hạng tất cả nhân viên
                    // Sắp xếp nhân viên giảm dần theo tiêu chí ưu tiên giảm dần Paid Revenue, Billing Rev, Case của Opportunities
                    $arr_staff[] = array(
                        'staff_id' => $staff['staff_id'],
                        'paid_revenue' => $row_day['paid_revenue_value'],
                        'billing_revenue_total' => $row_day['billing_revenue_total'],
                        'opportunities_count' => $row_day['opportunities_count'],
                    );
                }

                // Sắp xếp nhân viên trong group Paid Revenue, Billing Rev, Case của Opportunities
                usort($arr_day, function ($a, $b) {
                    if ($a['paid_revenue_value'] !== $b['paid_revenue_value']) {
                        return ($a['paid_revenue_value'] < $b['paid_revenue_value']) ? 1 : -1;
                    } elseif ($a['billing_revenue_total'] !== $b['billing_revenue_total']) {
                        return ($a['billing_revenue_total'] < $b['billing_revenue_total']) ? 1 : -1;
                    } elseif ($a['opportunities_count'] !== $b['opportunities_count']) {
                        return ($a['opportunities_count'] < $b['opportunities_count']) ? 1 : -1;
                    } elseif ($a['staff_id'] !== $b['staff_id']) {
                        return $a['staff_id'] - $b['staff_id'];
                    }
                });

                $team['paid_revenue_value'] = $paid_revenue_value_team;
                $team['billing_revenue_total'] = $billing_revenue_total_team;
                $team['opportunities_count'] = $opportunities_count_team;
                $team['revenue_day'] = $arr_day;

                $data['group'][$team['value']] = $team;
            }
        }

        // Sắp xếp các group với nhau Paid Revenue, Billing Rev, Case của Opportunities
        usort($data['group'], function ($a, $b) {
            if ($a['paid_revenue_value'] !== $b['paid_revenue_value']) {
                return ($a['paid_revenue_value'] < $b['paid_revenue_value']) ? 1 : -1;
            } elseif ($a['billing_revenue_total'] !== $b['billing_revenue_total']) {
                return ($a['billing_revenue_total'] < $b['billing_revenue_total']) ? 1 : -1;
            } elseif ($a['opportunities_count'] !== $b['opportunities_count']) {
                return ($a['opportunities_count'] < $b['opportunities_count']) ? 1 : -1;
            }
        });

        // Paid Revenue Top 10
        $data['paid_revenue_top'] = [];  // Xử lý
        // Sắp xếp tất cả Paid Revenue, Billing Rev, Case của Opportunities

        usort($arr_staff, function ($a, $b) {
            if ($a['paid_revenue'] !== $b['paid_revenue']) {
                return ($a['paid_revenue'] < $b['paid_revenue']) ? 1 : -1;
            } elseif ($a['billing_revenue_total'] !== $b['billing_revenue_total']) {
                return ($a['billing_revenue_total'] < $b['billing_revenue_total']) ? 1 : -1;
            } elseif ($a['opportunities_count'] !== $b['opportunities_count']) {
                return ($a['opportunities_count'] < $b['opportunities_count']) ? 1 : -1;
            } elseif ($a['staff_id'] !== $b['staff_id']) {
                return $a['staff_id'] - $b['staff_id'];
            }
        });

        // Top 10
        $top = 0;

        foreach ($arr_staff as $staff) {
            if (!$staff['staff_id'])
                continue;

            if (($staff['paid_revenue'] || $staff['billing_revenue_total'] || $staff['opportunities_count'])  && $top < 10) {
                $top++;
                $data['paid_revenue_top'][$staff['staff_id']] = base_url('assets/images/kpi_top_' . $top . '.png');
            } else {
                break;
            }
        }

        $data['current_date'] = date('d.m.Y', time());

        $result = json_encode(array(
            'error' => 0,
            'html' => $this->load->view('revenue_day', $data, true),
            'mess' => _l('success_json')
        ));

        print_r($result);
        die;
    }


    public function get_revenue_month()
    {
        $data = $this->input->get();

        if (!$data['revenue_month'])
            return $this->reponse_error(_l('month_null'));


        // Thống kê kết quả kinh doanh, daily
        $string_day = '01.' . $data['revenue_month'];
        $array_month = convert_month_to_from($string_day);

        // Thống kê kết quả kinh doanh, Monthly

        // Monthly day off
        $data['day_off'] = $this->goals_model->staticst_day_off_day($array_month);

        // Closed Revenue
        // Closed Revenue: Case: Số lượng báo giá được chuyển thành hóa đơn - Rev: Tổng thành tiền của báo giá được chuyển thành hóa đơn
        // $data['closed_revenue'] = $this->goals_model->closed_revenue($array_month);

        // Closed Revenue: New: Số lượng báo giá được chuyển thành hóa đơn, báo giá đến từ công ty New* hoặc Fresh (Theo Loại khách hàng TopDev)
        // $data['closed_revenue_new'] = $this->goals_model->closed_revenue_new($array_month);


        // Billing Revenue Tính theo ngày chốt hóa đơn
        // Billing Revenue: Case: Số lượng hóa đơn - Rev: Tổng thành tiền của báo giá được chuyển thành hóa đơn
        $data['billing_revenue'] = $this->goals_model->billing_revenue($array_month);

        // Billing Revenue: New: Số lượng báo giá được chuyển thành hóa đơn, báo giá đến từ công ty New* hoặc Fresh (Theo Loại khách hàng TopDev)
        $data['billing_revenue_new'] = $this->goals_model->billing_revenue_new($array_month);


        // Paid Revenue Tính theo ngày thanh toán
        // Rev: Tổng thành tiền của hóa đơn đã thanh toán
        // $data['paid_revenue'] = $this->goals_model->paid_revenue($array_month, $this->arr_team_sales);


        // Opportunities Revenue Tính theo ngày tạo báo giá
        // Case: Số lượng báo giá được tạo - Amount: Tổng thành tiền của báo giá
        $data['opportunities'] = $this->goals_model->opportunities_revenue($array_month);

        // KPI Rev Chỉ tiêu doanh thu bán hàng mỗi tháng của từng salesperson (chỉ có trong bảng Monthly)
        $data['kpi_rev'] = $this->goals_model->kpi_rev($array_month);

        // KPI team revenue
        $data['kpi_team_revenue'] = $this->goals_model->kpi_team_revenue($array_month);

        // Danh sách nhân viên
        $arr_staff = [];

        foreach ($this->team_sales as $team) {
            if ($team['value']) {
                // KPI team revenue cho từng team
                $team['kpi_team_revenue'] = (isset($data['kpi_team_revenue'][$team['value']]['target_amount'])) ? $data['kpi_team_revenue'][$team['value']]['target_amount'] : 0;
                // Lấy danh sách nhân viên trong team
                $list_staff = $this->goals_model->list_staff_team($team['value'], $array_month);
                $paid_revenue_value_team = 0;
                $billing_revenue_total_team = 0;
                $opportunities_count_team = 0;
                $arr_month = [];
                foreach ($list_staff as $staff) {
                    $row_month = [];
                    // Gán giá trị thống kê cho từng nhân viên theo ngày
                    $row_month['staff_id'] = $staff['staff_id'];
                    $row_month['firstname'] = $staff['firstname'];
                    $row_month['lastname'] = $staff['lastname'];
                    $row_month['datecreated'] = $staff['datecreated'];

                    $row_month['day_off'] = (isset($data['day_off'][$staff['staff_id']]['total_days'])) ? $data['day_off'][$staff['staff_id']]['total_days'] : 0;

                    $row_month['closed_revenue_count'] = (isset($data['closed_revenue'][$staff['staff_id']]['count'])) ? $data['closed_revenue'][$staff['staff_id']]['count'] : 0;
                    $row_month['closed_revenue_total'] = (isset($data['closed_revenue'][$staff['staff_id']]['total'])) ? round($data['closed_revenue'][$staff['staff_id']]['total'], 0, PHP_ROUND_HALF_UP) : 0;
                    $row_month['closed_revenue_new'] = (isset($data['closed_revenue_new'][$staff['staff_id']]['count'])) ? $data['closed_revenue_new'][$staff['staff_id']]['count'] : 0;


                    $row_month['kpi_targets'] = (isset($data['kpi_rev'][$staff['staff_id']]['target_amount'])) ? $data['kpi_rev'][$staff['staff_id']]['target_amount'] : 0;

                    $row_month['billing_revenue_count'] = (isset($data['billing_revenue'][$staff['staff_id']]['count'])) ? $data['billing_revenue'][$staff['staff_id']]['count'] : 0;
                    $row_month['billing_revenue_total'] = (isset($data['billing_revenue'][$staff['staff_id']]['total'])) ? round($data['billing_revenue'][$staff['staff_id']]['total'], 0, PHP_ROUND_HALF_UP) : 0;
                    $row_month['billing_revenue_total_qty'] = (isset($data['billing_revenue'][$staff['staff_id']]['total_qty'])) ? round($data['billing_revenue'][$staff['staff_id']]['total_qty'], 1, PHP_ROUND_HALF_UP) : 0;
                    $row_month['billing_revenue_new'] = (isset($data['billing_revenue_new'][$staff['staff_id']]['count'])) ? $data['billing_revenue_new'][$staff['staff_id']]['count'] : 0;

                    $row_month['opportunities_count'] = (isset($data['opportunities'][$staff['staff_id']]['count'])) ? $data['opportunities'][$staff['staff_id']]['count'] : 0;
                    $row_month['opportunities_total'] = (isset($data['opportunities'][$staff['staff_id']]['total'])) ? round($data['opportunities'][$staff['staff_id']]['total'], 0, PHP_ROUND_HALF_UP) : 0;

                    $row_month['paid_revenue_value'] = (isset($data['paid_revenue'][$staff['staff_id']]['total'])) ? round($data['paid_revenue'][$staff['staff_id']]['total'], 0, PHP_ROUND_HALF_UP) : 0;
                    $paid_revenue_value_team += $row_month['paid_revenue_value'];
                    $billing_revenue_total_team += $row_month['billing_revenue_total'];
                    $opportunities_count_team += $row_month['opportunities_count'];

                    // Nhân viên inactive, trạng thái hóa đơn được cập nhật - inactive but number billing revenue OK
                    if (!$row_month['closed_revenue_count'] && !$row_month['closed_revenue_new'] && !$row_month['billing_revenue_count'] && !$row_month['billing_revenue_new'] && !$row_month['opportunities_count'] && !$row_month['paid_revenue_value']) {
                        continue;
                    }

                    $arr_month[$staff['staff_id']] = $row_month;

                    // Đưa thông tin ranking nhân viên vào danh sách arr_staff cho công việc xếp hạng tất cả nhân viên
                    // Sắp xếp nhân viên giảm dần theo tiêu chí ưu tiên giảm dần Paid Revenue, Billing Rev, Case của Opportunities
                    $arr_staff[] = array(
                        'staff_id' => $staff['staff_id'],
                        'paid_revenue' => $row_month['paid_revenue_value'],
                        'billing_revenue_total' => $row_month['billing_revenue_total'],
                        'opportunities_count' => $row_month['opportunities_count'],
                    );
                }

                // Sắp xếp nhân viên trong group Paid Revenue, Billing Rev, Case của Opportunities
                usort($arr_month, function ($a, $b) {
                    if ($a['paid_revenue_value'] !== $b['paid_revenue_value']) {
                        return ($a['paid_revenue_value'] < $b['paid_revenue_value']) ? 1 : -1;
                    } elseif ($a['billing_revenue_total'] !== $b['billing_revenue_total']) {
                        return ($a['billing_revenue_total'] < $b['billing_revenue_total']) ? 1 : -1;
                    } elseif ($a['opportunities_count'] !== $b['opportunities_count']) {
                        return ($a['opportunities_count'] < $b['opportunities_count']) ? 1 : -1;
                    } elseif ($a['staff_id'] !== $b['staff_id']) {
                        return $a['staff_id'] - $b['staff_id'];
                    }
                });

                $team['paid_revenue_value'] = $paid_revenue_value_team;
                $team['billing_revenue_total'] = $billing_revenue_total_team;
                $team['opportunities_count'] = $opportunities_count_team;
                $team['revenue_day'] = $arr_month;

                $data['group'][$team['value']] = $team;
            }
        }

        // Sắp xếp các group với nhau Paid Revenue, Billing Rev, Case của Opportunities
        usort($data['group'], function ($a, $b) {
            if ($a['paid_revenue_value'] !== $b['paid_revenue_value']) {
                return ($a['paid_revenue_value'] < $b['paid_revenue_value']) ? 1 : -1;
            } elseif ($a['billing_revenue_total'] !== $b['billing_revenue_total']) {
                return ($a['billing_revenue_total'] < $b['billing_revenue_total']) ? 1 : -1;
            } elseif ($a['opportunities_count'] !== $b['opportunities_count']) {
                return ($a['opportunities_count'] < $b['opportunities_count']) ? 1 : -1;
            }
        });

        // Paid Revenue Top 10
        $data['paid_revenue_top'] = [];  // Xử lý
        // Sắp xếp tất cả Paid Revenue, Billing Rev, Case của Opportunities
        usort($arr_staff, function ($a, $b) {
            if ($a['paid_revenue'] !== $b['paid_revenue']) {
                return ($a['paid_revenue'] < $b['paid_revenue']) ? 1 : -1;
            } elseif ($a['billing_revenue_total'] !== $b['billing_revenue_total']) {
                return ($a['billing_revenue_total'] < $b['billing_revenue_total']) ? 1 : -1;
            } elseif ($a['opportunities_count'] !== $b['opportunities_count']) {
                return ($a['opportunities_count'] < $b['opportunities_count']) ? 1 : -1;
            } elseif ($a['staff_id'] !== $b['staff_id']) {
                return $a['staff_id'] - $b['staff_id'];
            }
        });

        // Top 10
        $top = 0;

        foreach ($arr_staff as $staff) {
            if (!$staff['staff_id'])
                continue;

            if (($staff['paid_revenue'] || $staff['billing_revenue_total'] || $staff['opportunities_count'])  && $top < 10) {
                $top++;
                $data['paid_revenue_top'][$staff['staff_id']] = base_url('assets/images/kpi_top_' . $top . '.png');
            } else {
                break;
            }
        }


        $result = json_encode(array(
            'error' => 0,
            'html' => $this->load->view('revenue_month', $data, true),
            'mess' => _l('success_json')
        ));

        print_r($result);
        die;
    }


    public function reponse_error($mess, $field = '')
    {
        print_r(json_encode(array(
            'error' => 1,
            'mess' => $mess,
            'field' => $field
        )));
        die;
    }

    public function reponse_json($mess)
    {
        print_r(json_encode(array(
            'error' => 0,
            'mess' => $mess
        )));
        die;
    }


    public function convert_string_to_day($string)
    {
        if (!$string)
            return array();

        $result = [];
        $timestamp = strtotime($string);
        $result['day'] = date('d', $timestamp);
        $result['month'] = date('m', $timestamp);
        $result['year'] = date('Y', $timestamp);

        return $result;
    }

    public function goal($id = '')
    {
        if (!has_permission('goals', '', 'view')) {
            access_denied('goals');
        }
        if ($this->input->post()) {
            if ($id == '') {
                if (!has_permission('goals', '', 'create')) {
                    access_denied('goals');
                }
                $id = $this->goals_model->add($this->input->post());
                if ($id) {
                    set_alert('success', _l('added_successfully', _l('goal')));
                    redirect(admin_url('goals/goal/' . $id));
                }
            } else {
                if (!has_permission('goals', '', 'edit')) {
                    access_denied('goals');
                }
                $success = $this->goals_model->update($this->input->post(), $id);
                if ($success) {
                    set_alert('success', _l('updated_successfully', _l('goal')));
                }
                redirect(admin_url('goals/goal/' . $id));
            }
        }
        if ($id == '') {
            $title = _l('add_new', _l('goal_lowercase'));
        } else {
            $data['goal']        = $this->goals_model->get($id);
            $data['achievement'] = $this->goals_model->calculate_goal_achievement($id);

            $title = _l('edit', _l('goal_lowercase'));
        }

        $this->load->model('staff_model');
        $data['members'] = $this->staff_model->get('', ['is_not_staff' => 0, 'active' => 1]);

        $this->load->model('contracts_model');
        $data['contract_types']        = $this->contracts_model->get_contract_types();
        $data['title']                 = $title;
        $this->app_scripts->add('circle-progress-js', 'assets/plugins/jquery-circle-progress/circle-progress.min.js');
        $this->load->view('goal', $data);
    }

    /* Delete announcement from database */
    public function delete($id)
    {
        if (!has_permission('goals', '', 'delete')) {
            access_denied('goals');
        }
        if (!$id) {
            redirect(admin_url('goals'));
        }
        $response = $this->goals_model->delete($id);
        if ($response == true) {
            set_alert('success', _l('deleted', _l('goal')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('goal_lowercase')));
        }
        redirect(admin_url('goals'));
    }

    public function notify($id, $notify_type)
    {
        if (!has_permission('goals', '', 'edit') && !has_permission('goals', '', 'create')) {
            access_denied('goals');
        }
        if (!$id) {
            redirect(admin_url('goals'));
        }
        $success = $this->goals_model->notify_staff_members($id, $notify_type);
        if ($success) {
            set_alert('success', _l('goal_notify_staff_notified_manually_success'));
        } else {
            set_alert('warning', _l('goal_notify_staff_notified_manually_fail'));
        }
        redirect(admin_url('goals/goal/' . $id));
    }
}

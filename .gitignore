/vendor
/application/vendor
/node_modules
.DS_Store
/.idea
/.junie

# Backup folders
/backups/*
!/backups/index.html

# Media folder
/media/*
!/media/index.html

# Session folder
/sessions/*
!/sessions/index.html

# Uploads folder
/uploads/*
!/uploads/.gitkeep

# Log folder
/application/logs/*
!/application/logs/index.html

# Config file
application/config/app-config.php
application/config/app-config-*.php
!application/config/app-config-sample.php
env.php
http-client.private.env.json
temp/es_indexes
temp/es_index_result

.htaccess
.vscode
temp

application/storage/taxonomies.json

# Document
docs/cache

# Assets
# Need to remove min.js when you build some file not include here
assets/themes/perfex/css/style.min.css
assets/css/bs-overides.min.css
assets/css/forms.min.css
assets/css/style.min.css
assets/builds/vendor-admin.css
assets/js/main.min.js
assets/js/map.min.js
assets/js/projects.min.js

/chart
/gl-auto-build-variables.env
/.gitlab/*.volume.yaml

crm-call-center-extension.zip
crmdev-call-extension.zip
/application/cache

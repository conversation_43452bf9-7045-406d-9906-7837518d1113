{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "e8feb5eb575a9f6b654a3541d261e428", "packages": [{"name": "authorizenet/authorizenet", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/AuthorizeNet/sdk-php.git", "reference": "a3e76f96f674d16e892f87c58bedb99dada4b067"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/AuthorizeNet/sdk-php/zipball/a3e76f96f674d16e892f87c58bedb99dada4b067", "reference": "a3e76f96f674d16e892f87c58bedb99dada4b067", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=5.6"}, "type": "library", "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["proprietary"], "description": "Official PHP SDK for Authorize.Net", "homepage": "http://developer.authorize.net", "keywords": ["authorize.net", "authorizenet", "ecommerce", "payment"], "support": {"issues": "https://github.com/AuthorizeNet/sdk-php/issues", "source": "https://github.com/AuthorizeNet/sdk-php/tree/2.0.2"}, "time": "2021-03-31T18:22:14+00:00"}, {"name": "bainternet/php-hooks", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/bainternet/PHP-Hooks.git", "reference": "c45a4d1b1c630c52cf7483424d222049e1b7d341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bainternet/PHP-Hooks/zipball/c45a4d1b1c630c52cf7483424d222049e1b7d341", "reference": "c45a4d1b1c630c52cf7483424d222049e1b7d341", "shasum": ""}, "type": "library", "autoload": {"files": ["php-hooks.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://en.bainternet.info/"}], "description": "A fork of the WordPress filters hook system rolled in to a class to be ported into any PHP-based system", "homepage": "http://bainternet.github.io/PHP-Hooks/", "keywords": ["actions", "filters", "hooks"], "support": {"issues": "https://github.com/bainternet/PHP-Hooks/issues", "source": "https://github.com/bainternet/PHP-Hooks/tree/master"}, "time": "2015-09-07T12:11:17+00:00"}, {"name": "braintree/braintree_php", "version": "5.5.0", "source": {"type": "git", "url": "https://github.com/braintree/braintree_php.git", "reference": "8902a072ac04c9eea2996f2683cb56259cbe46fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/braintree/braintree_php/zipball/8902a072ac04c9eea2996f2683cb56259cbe46fa", "reference": "8902a072ac04c9eea2996f2683cb56259cbe46fa", "shasum": ""}, "require": {"ext-curl": "*", "ext-dom": "*", "ext-hash": "*", "ext-openssl": "*", "ext-xmlwriter": "*", "php": ">=7.2.0"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Braintree\\": "lib/Braintree"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Braintree", "homepage": "https://www.braintreepayments.com"}], "description": "Braintree PHP Client Library", "support": {"issues": "https://github.com/braintree/braintree_php/issues", "source": "https://github.com/braintree/braintree_php/tree/5.5.0"}, "time": "2021-01-25T21:53:32+00:00"}, {"name": "clue/stream-filter", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "d6169430c7731d8509da7aecd0af756a5747b78e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/d6169430c7731d8509da7aecd0af756a5747b78e", "reference": "d6169430c7731d8509da7aecd0af756a5747b78e", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/php-stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.6.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-02-21T13:15:14+00:00"}, {"name": "cocur/slugify", "version": "v4.4.0", "source": {"type": "git", "url": "https://github.com/cocur/slugify.git", "reference": "4c6ed14a087ca061b220ffda640c07644946e2a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cocur/slugify/zipball/4c6ed14a087ca061b220ffda640c07644946e2a0", "reference": "4c6ed14a087ca061b220ffda640c07644946e2a0", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ~8.0.0 || ~8.1.0 || ~8.2.0"}, "conflict": {"symfony/config": "<3.4 || >=4,<4.3", "symfony/dependency-injection": "<3.4 || >=4,<4.3", "symfony/http-kernel": "<3.4 || >=4,<4.3", "twig/twig": "<2.12.1"}, "require-dev": {"laravel/framework": "^5.0|^6.0|^7.0|^8.0", "latte/latte": "~2.2", "league/container": "^2.2.0", "mikey179/vfsstream": "~1.6.8", "mockery/mockery": "^1.3", "nette/di": "~2.4", "pimple/pimple": "~1.1", "plumphp/plum": "~0.1", "symfony/config": "^3.4 || ^4.3 || ^5.0 || ^6.0", "symfony/dependency-injection": "^3.4 || ^4.3 || ^5.0 || ^6.0", "symfony/http-kernel": "^3.4 || ^4.3 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.4 || ^6.0", "twig/twig": "^2.12.1 || ~3.0", "zendframework/zend-modulemanager": "~2.2", "zendframework/zend-servicemanager": "~2.2", "zendframework/zend-view": "~2.2"}, "type": "library", "autoload": {"psr-4": {"Cocur\\Slugify\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://florian.ec"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Converts a string into a slug.", "keywords": ["slug", "slugify"], "support": {"issues": "https://github.com/cocur/slugify/issues", "source": "https://github.com/cocur/slugify/tree/v4.4.0"}, "time": "2023-08-05T09:42:11+00:00"}, {"name": "ddeboer/imap", "version": "1.12.2", "source": {"type": "git", "url": "https://github.com/ddeboer/imap.git", "reference": "5f1d8ed40b365386c028e038658376410c59e8a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ddeboer/imap/zipball/5f1d8ed40b365386c028e038658376410c59e8a8", "reference": "5f1d8ed40b365386c028e038658376410c59e8a8", "shasum": ""}, "require": {"ext-iconv": "*", "ext-imap": "*", "ext-mbstring": "*", "php": "^7.4 || ^8.0.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v3.1.0", "laminas/laminas-mail": "^2.15.0", "malukenho/mcbumpface": "^1.1.5", "phpstan/phpstan": "^0.12.99", "phpstan/phpstan-phpunit": "^0.12.22", "phpstan/phpstan-strict-rules": "^0.12.11", "phpunit/phpunit": "^9.5.9"}, "type": "library", "autoload": {"psr-4": {"Ddeboer\\Imap\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Community contributors", "homepage": "https://github.com/ddeboer/imap/graphs/contributors"}], "description": "Object-oriented IMAP for PHP", "keywords": ["email", "imap", "mail"], "support": {"issues": "https://github.com/ddeboer/imap/issues", "source": "https://github.com/ddeboer/imap/tree/1.12.2"}, "funding": [{"url": "https://github.com/Slamdunk", "type": "github"}, {"url": "https://github.com/ddeboer", "type": "github"}], "time": "2021-09-23T05:56:38+00:00"}, {"name": "doctrine/inflector", "version": "2.0.8", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "f9301a5b2fb1216b2b08f02ba04dc45423db6bff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/f9301a5b2fb1216b2b08f02ba04dc45423db6bff", "reference": "f9301a5b2fb1216b2b08f02ba04dc45423db6bff", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.8"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2023-06-16T13:40:37+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v7.17.2", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "2d302233f2bb0926812d82823bb820d405e130fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/2d302233f2bb0926812d82823bb820d405e130fc", "reference": "2d302233f2bb0926812d82823bb820d405e130fc", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0", "LGPL-2.1-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v7.17.2"}, "time": "2023-04-21T15:31:12+00:00"}, {"name": "erusev/parsedown", "version": "1.7.4", "source": {"type": "git", "url": "https://github.com/erusev/parsedown.git", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/erusev/parsedown/zipball/cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35"}, "type": "library", "autoload": {"psr-0": {"Parsedown": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://erusev.com"}], "description": "<PERSON><PERSON>r for <PERSON>down.", "homepage": "http://parsedown.org", "keywords": ["markdown", "parser"], "support": {"issues": "https://github.com/erusev/parsedown/issues", "source": "https://github.com/erusev/parsedown/tree/1.7.x"}, "time": "2019-12-30T22:54:17+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/b4b5a025dfee70d6cd34c780e07330eb93d5b997", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.1.0"}, "time": "2022-10-24T12:58:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/7887fc8488013065f72f977dcb281994f5fde9f4", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.2.2"}, "time": "2022-12-07T11:28:53+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.16.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "523407fb06eb9e5f3d59889b3978d5bfe94299c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/523407fb06eb9e5f3d59889b3978d5bfe94299c8", "reference": "523407fb06eb9e5f3d59889b3978d5bfe94299c8", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.16.0"}, "time": "2022-09-18T07:06:19+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.8.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/41042bc7ab002487b876a0683fc8dce04ddce104", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2023-12-03T20:35:24+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "111166291a0f8130081195ac4556a5587d7f1b5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/111166291a0f8130081195ac4556a5587d7f1b5d", "reference": "111166291a0f8130081195ac4556a5587d7f1b5d", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-08-03T15:11:55+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "be45764272e8873c72dbe3d2edcfdfcc3bc9f727"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/be45764272e8873c72dbe3d2edcfdfcc3bc9f727", "reference": "be45764272e8873c72dbe3d2edcfdfcc3bc9f727", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.6.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-08-27T10:13:57+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/8f06e92b95405216b237521cc64c804dd44c4a81", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7||^2.0", "php": ">=7.3", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/psr7": "Includes an HTTP factory starting in version 2.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "support": {"issues": "https://github.com/http-interop/http-factory-guzzle/issues", "source": "https://github.com/http-interop/http-factory-guzzle/tree/1.2.0"}, "time": "2021-07-21T13:50:14+00:00"}, {"name": "illuminate/bus", "version": "v8.83.27", "source": {"type": "git", "url": "https://github.com/illuminate/bus.git", "reference": "d2a8ae4bfd881086e55455e470776358eab27eae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/bus/zipball/d2a8ae4bfd881086e55455e470776358eab27eae", "reference": "d2a8ae4bfd881086e55455e470776358eab27eae", "shasum": ""}, "require": {"illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/pipeline": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0"}, "suggest": {"illuminate/queue": "Required to use closures when chaining jobs (^7.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Bus\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Bus package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2022-03-07T15:02:42+00:00"}, {"name": "illuminate/collections", "version": "v8.83.27", "source": {"type": "git", "url": "https://github.com/illuminate/collections.git", "reference": "705a4e1ef93cd492c45b9b3e7911cccc990a07f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/collections/zipball/705a4e1ef93cd492c45b9b3e7911cccc990a07f4", "reference": "705a4e1ef93cd492c45b9b3e7911cccc990a07f4", "shasum": ""}, "require": {"illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "php": "^7.3|^8.0"}, "suggest": {"symfony/var-dumper": "Required to use the dump method (^5.4)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Collections package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2022-06-23T15:29:49+00:00"}, {"name": "illuminate/container", "version": "v8.83.27", "source": {"type": "git", "url": "https://github.com/illuminate/container.git", "reference": "14062628d05f75047c5a1360b9350028427d568e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/container/zipball/14062628d05f75047c5a1360b9350028427d568e", "reference": "14062628d05f75047c5a1360b9350028427d568e", "shasum": ""}, "require": {"illuminate/contracts": "^8.0", "php": "^7.3|^8.0", "psr/container": "^1.0"}, "provide": {"psr/container-implementation": "1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Container\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Container package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2022-02-02T21:03:35+00:00"}, {"name": "illuminate/contracts", "version": "v8.83.27", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "5e0fd287a1b22a6b346a9f7cd484d8cf0234585d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/5e0fd287a1b22a6b346a9f7cd484d8cf0234585d", "reference": "5e0fd287a1b22a6b346a9f7cd484d8cf0234585d", "shasum": ""}, "require": {"php": "^7.3|^8.0", "psr/container": "^1.0", "psr/simple-cache": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2022-01-13T14:47:47+00:00"}, {"name": "illuminate/database", "version": "v8.83.27", "source": {"type": "git", "url": "https://github.com/illuminate/database.git", "reference": "1a5b0e4e6913415464fa2aab554a38b9e6fa44b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/database/zipball/1a5b0e4e6913415464fa2aab554a38b9e6fa44b1", "reference": "1a5b0e4e6913415464fa2aab554a38b9e6fa44b1", "shasum": ""}, "require": {"ext-json": "*", "illuminate/collections": "^8.0", "illuminate/container": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0", "symfony/console": "^5.4"}, "suggest": {"doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.13.3|^3.1.4).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "illuminate/console": "Required to use the database commands (^8.0).", "illuminate/events": "Required to use the observers with Eloquent (^8.0).", "illuminate/filesystem": "Required to use the migrations (^8.0).", "illuminate/pagination": "Required to paginate the result set (^8.0).", "symfony/finder": "Required to use Eloquent model factories (^5.4)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Database\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Database package.", "homepage": "https://laravel.com", "keywords": ["database", "laravel", "orm", "sql"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2022-08-31T16:16:06+00:00"}, {"name": "illuminate/events", "version": "v8.83.27", "source": {"type": "git", "url": "https://github.com/illuminate/events.git", "reference": "b7f06cafb6c09581617f2ca05d69e9b159e5a35d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/events/zipball/b7f06cafb6c09581617f2ca05d69e9b159e5a35d", "reference": "b7f06cafb6c09581617f2ca05d69e9b159e5a35d", "shasum": ""}, "require": {"illuminate/bus": "^8.0", "illuminate/collections": "^8.0", "illuminate/container": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"files": ["functions.php"], "psr-4": {"Illuminate\\Events\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Events package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2021-09-15T14:32:50+00:00"}, {"name": "illuminate/macroable", "version": "v8.83.27", "source": {"type": "git", "url": "https://github.com/illuminate/macroable.git", "reference": "aed81891a6e046fdee72edd497f822190f61c162"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/macroable/zipball/aed81891a6e046fdee72edd497f822190f61c162", "reference": "aed81891a6e046fdee72edd497f822190f61c162", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Macroable package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2021-11-16T13:57:03+00:00"}, {"name": "illuminate/pagination", "version": "v8.83.27", "source": {"type": "git", "url": "https://github.com/illuminate/pagination.git", "reference": "16fe8dc35f9d18c58a3471469af656a02e9ab692"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/pagination/zipball/16fe8dc35f9d18c58a3471469af656a02e9ab692", "reference": "16fe8dc35f9d18c58a3471469af656a02e9ab692", "shasum": ""}, "require": {"ext-json": "*", "illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Pagination\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pagination package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2022-06-27T13:26:06+00:00"}, {"name": "illuminate/pipeline", "version": "v8.83.27", "source": {"type": "git", "url": "https://github.com/illuminate/pipeline.git", "reference": "23aeff5b26ae4aee3f370835c76bd0f4e93f71d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/pipeline/zipball/23aeff5b26ae4aee3f370835c76bd0f4e93f71d2", "reference": "23aeff5b26ae4aee3f370835c76bd0f4e93f71d2", "shasum": ""}, "require": {"illuminate/contracts": "^8.0", "illuminate/support": "^8.0", "php": "^7.3|^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Pipeline\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Pipeline package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2021-03-26T18:39:16+00:00"}, {"name": "illuminate/support", "version": "v8.83.27", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "1c79242468d3bbd9a0f7477df34f9647dde2a09b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/support/zipball/1c79242468d3bbd9a0f7477df34f9647dde2a09b", "reference": "1c79242468d3bbd9a0f7477df34f9647dde2a09b", "shasum": ""}, "require": {"doctrine/inflector": "^1.4|^2.0", "ext-json": "*", "ext-mbstring": "*", "illuminate/collections": "^8.0", "illuminate/contracts": "^8.0", "illuminate/macroable": "^8.0", "nesbot/carbon": "^2.53.1", "php": "^7.3|^8.0", "voku/portable-ascii": "^1.6.1"}, "conflict": {"tightenco/collect": "<5.5.33"}, "suggest": {"illuminate/filesystem": "Required to use the composer class (^8.0).", "league/commonmark": "Required to use Str::markdown() and Stringable::markdown() (^1.3|^2.0.2).", "ramsey/uuid": "Required to use Str::uuid() (^4.2.2).", "symfony/process": "Required to use the composer class (^5.4).", "symfony/var-dumper": "Required to use the dd function (^5.4).", "vlucas/phpdotenv": "Required to use the Env class and env helper (^5.4.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2022-09-21T21:30:03+00:00"}, {"name": "instamojo/instamojo-php", "version": "0.3", "source": {"type": "git", "url": "https://github.com/Instamojo/instamojo-php.git", "reference": "8b5a7f76c74d156b23f91c64413bda00a2329ea5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Instamojo/instamojo-php/zipball/8b5a7f76c74d156b23f91c64413bda00a2329ea5", "reference": "8b5a7f76c74d156b23f91c64413bda00a2329ea5", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-4": {"Instamojo\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Insta<PERSON>jo and contributors", "email": "<EMAIL>", "homepage": "https://github.com/instamojo/instamojo-php/contributors"}], "description": "This is composer wrapper for instamojo-php", "homepage": "https://github.com/instamojo/instamojo-php/", "keywords": ["api", "instamojo", "php", "wrapper"], "support": {"docs": "https://github.com/instamojo/instamojo-php/", "email": "<EMAIL>", "issues": "https://github.com/instamojo/instamojo-php/issues", "source": "https://github.com/Instamojo/instamojo-php/tree/0.3"}, "time": "2016-09-25T21:46:32+00:00"}, {"name": "itsgoingd/clockwork", "version": "v5.1.12", "source": {"type": "git", "url": "https://github.com/itsgoingd/clockwork.git", "reference": "c9dbdbb1f0efd19bb80f1080ef63f1b9b1bc3b1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/itsgoingd/clockwork/zipball/c9dbdbb1f0efd19bb80f1080ef63f1b9b1bc3b1b", "reference": "c9dbdbb1f0efd19bb80f1080ef63f1b9b1bc3b1b", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6"}, "type": "library", "extra": {"laravel": {"providers": ["Clockwork\\Support\\Laravel\\ClockworkServiceProvider"], "aliases": {"Clockwork": "Clockwork\\Support\\Laravel\\Facade"}}}, "autoload": {"psr-4": {"Clockwork\\": "Clockwork/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "itsgoingd", "email": "<EMAIL>", "homepage": "https://twitter.com/itsgoingd"}], "description": "php dev tools in your browser", "homepage": "https://underground.works/clockwork", "keywords": ["Devtools", "debugging", "laravel", "logging", "lumen", "profiling", "slim"], "support": {"issues": "https://github.com/itsgoingd/clockwork/issues", "source": "https://github.com/itsgoingd/clockwork/tree/v5.1.12"}, "funding": [{"url": "https://github.com/itsgoingd", "type": "github"}], "time": "2022-12-13T00:04:12+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "ae547e455a3d8babd07b96966b17d7fd21d9c6af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/ae547e455a3d8babd07b96966b17d7fd21d9c6af", "reference": "ae547e455a3d8babd07b96966b17d7fd21d9c6af", "shasum": ""}, "require": {"composer-runtime-api": "^2.0.0", "php": "^7.1|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.17", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^0.12.66", "phpunit/phpunit": "^7.5|^8.5|^9.4", "vimeo/psalm": "^4.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.0.5"}, "time": "2021-10-08T21:21:46+00:00"}, {"name": "lastguest/murmurhash", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/lastguest/murmurhash-php.git", "reference": "0150ba26fb7025d1f936983a167cdc74149f87c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lastguest/murmurhash-php/zipball/0150ba26fb7025d1f936983a167cdc74149f87c8", "reference": "0150ba26fb7025d1f936983a167cdc74149f87c8", "shasum": ""}, "require": {"php": "^7||^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7||^9"}, "type": "library", "autoload": {"psr-4": {"lastguest\\": "src/lastguest/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/lastguest/murmurhash-php"}], "description": "MurmurHash3 Hash", "homepage": "https://github.com/lastguest/murmurhash-php", "keywords": ["hash", "hashing", "murmur"], "support": {"issues": "https://github.com/lastguest/murmurhash-php/issues", "source": "https://github.com/lastguest/murmurhash-php/tree/2.1.1"}, "time": "2021-04-13T16:23:45+00:00"}, {"name": "league/omnipay", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/thephpleague/omnipay.git", "reference": "38f66a0cc043ed51d6edf7956d6439a2f263501f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/omnipay/zipball/38f66a0cc043ed51d6edf7956d6439a2f263501f", "reference": "38f66a0cc043ed51d6edf7956d6439a2f263501f", "shasum": ""}, "require": {"omnipay/common": "^3.1", "php": "^7.2|^8.0", "php-http/discovery": "^1.14", "php-http/guzzle7-adapter": "^1"}, "require-dev": {"omnipay/tests": "^3|^4"}, "type": "metapackage", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Omnipay payment processing library", "homepage": "https://omnipay.thephpleague.com/", "keywords": ["checkout", "creditcard", "omnipay", "payment"], "support": {"issues": "https://github.com/thephpleague/omnipay/issues", "source": "https://github.com/thephpleague/omnipay/tree/v3.2.1"}, "funding": [{"url": "https://github.com/barryvdh", "type": "github"}], "time": "2021-06-05T11:34:12+00:00"}, {"name": "moneyphp/money", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/moneyphp/money.git", "reference": "0dc40e3791c67e8793e3aa13fead8cf4661ec9cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/moneyphp/money/zipball/0dc40e3791c67e8793e3aa13fead8cf4661ec9cd", "reference": "0dc40e3791c67e8793e3aa13fead8cf4661ec9cd", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6"}, "require-dev": {"cache/taggable-cache": "^0.4.0", "doctrine/instantiator": "^1.0.5", "ext-bcmath": "*", "ext-gmp": "*", "ext-intl": "*", "florianv/exchanger": "^1.0", "florianv/swap": "^3.0", "friends-of-phpspec/phpspec-code-coverage": "^3.1.1 || ^4.3", "moneyphp/iso-currencies": "^3.2.1", "php-http/message": "^1.4", "php-http/mock-client": "^1.0.0", "phpspec/phpspec": "^3.4.3", "phpunit/phpunit": "^5.7.27 || ^6.5.14 || ^7.5.18 || ^8.5", "psr/cache": "^1.0", "symfony/phpunit-bridge": "^4"}, "suggest": {"ext-bcmath": "Calculate without integer limits", "ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Money\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP implementation of Fowler's Money pattern", "homepage": "http://moneyphp.org", "keywords": ["Value Object", "money", "vo"], "support": {"issues": "https://github.com/moneyphp/money/issues", "source": "https://github.com/moneyphp/money/tree/v3.3.3"}, "time": "2022-09-21T07:43:36+00:00"}, {"name": "neitanod/forceutf8", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/neitanod/forceutf8.git", "reference": "c1fbe70bfb5ad41b8ec5785056b0e308b40d4831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/neitanod/forceutf8/zipball/c1fbe70bfb5ad41b8ec5785056b0e308b40d4831", "reference": "c1fbe70bfb5ad41b8ec5785056b0e308b40d4831", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"ForceUTF8\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP Class Encoding featuring popular Encoding::toUTF8() function --formerly known as forceUTF8()-- that fixes mixed encoded strings.", "homepage": "https://github.com/neitanod/forceutf8", "support": {"issues": "https://github.com/neitanod/forceutf8/issues", "source": "https://github.com/neitanod/forceutf8/tree/master"}, "time": "2019-12-10T14:09:14+00:00"}, {"name": "nesbot/carbon", "version": "2.71.0", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "98276233188583f2ff845a0f992a235472d9466a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/98276233188583f2ff845a0f992a235472d9466a", "reference": "98276233188583f2ff845a0f992a235472d9466a", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2023-09-25T11:31:05+00:00"}, {"name": "omnipay/braintree", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/thephpleague/omnipay-braintree.git", "reference": "b60a754ad2b33e638ffd9cfc29611f372e8d531a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/omnipay-braintree/zipball/b60a754ad2b33e638ffd9cfc29611f372e8d531a", "reference": "b60a754ad2b33e638ffd9cfc29611f372e8d531a", "shasum": ""}, "require": {"braintree/braintree_php": "^5.0", "omnipay/common": "^3"}, "require-dev": {"omnipay/tests": "^3"}, "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Omnipay\\Braintree\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}, {"name": "Omnipay Contributors", "homepage": "https://github.com/thephpleague/omnipay-braintree/contributors"}], "description": "Braintree gateway for Omnipay payment processing library", "homepage": "https://github.com/thephpleague/omnipay-braintree", "keywords": ["braintree", "gateway", "merchant", "omnipay", "pay", "payment", "purchase"], "support": {"issues": "https://github.com/thephpleague/omnipay-braintree/issues", "source": "https://github.com/thephpleague/omnipay-braintree/tree/master"}, "time": "2022-02-16T13:54:35+00:00"}, {"name": "omnipay/common", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/thephpleague/omnipay-common.git", "reference": "80545e9f4faab0efad36cc5f1e11a184dda22baf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/omnipay-common/zipball/80545e9f4faab0efad36cc5f1e11a184dda22baf", "reference": "80545e9f4faab0efad36cc5f1e11a184dda22baf", "shasum": ""}, "require": {"moneyphp/money": "^3.1|^4.0.3", "php": "^7.2|^8", "php-http/client-implementation": "^1", "php-http/discovery": "^1.14", "php-http/message": "^1.5", "php-http/message-factory": "^1.1", "symfony/http-foundation": "^2.1|^3|^4|^5|^6"}, "require-dev": {"omnipay/tests": "^4.1", "php-http/guzzle7-adapter": "^1", "php-http/mock-client": "^1", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"league/omnipay": "The default Omnipay package provides a default HTTP Adapter."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"Omnipay\\Common\\": "src/Common"}, "classmap": ["src/Omnipay.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Del"}, {"name": "Omnipay Contributors", "homepage": "https://github.com/thephpleague/omnipay-common/contributors"}], "description": "Common components for Omnipay payment processing library", "homepage": "https://github.com/thephpleague/omnipay-common", "keywords": ["gateway", "merchant", "omnipay", "pay", "payment", "purchase"], "support": {"issues": "https://github.com/thephpleague/omnipay-common/issues", "source": "https://github.com/thephpleague/omnipay-common/tree/v3.2.1"}, "funding": [{"url": "https://github.com/barryvdh", "type": "github"}], "time": "2023-05-30T12:44:03+00:00"}, {"name": "omnipay/mollie", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/thephpleague/omnipay-mollie.git", "reference": "e9231442b67315017ec14ae3b0e13ff619276fd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/omnipay-mollie/zipball/e9231442b67315017ec14ae3b0e13ff619276fd8", "reference": "e9231442b67315017ec14ae3b0e13ff619276fd8", "shasum": ""}, "require": {"omnipay/common": "^3.1", "php": "^7.2|^8.0"}, "require-dev": {"omnipay/tests": "^4.1", "squizlabs/php_codesniffer": "^3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5.x-dev"}}, "autoload": {"psr-4": {"Omnipay\\Mollie\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}, {"name": "Omnipay Contributors", "homepage": "https://github.com/thephpleague/omnipay-mollie/contributors"}], "description": "Mollie driver for the Omnipay payment processing library", "homepage": "https://github.com/thephpleague/omnipay-mollie", "keywords": ["gateway", "merchant", "mollie", "omnipay", "pay", "payment"], "support": {"issues": "https://github.com/thephpleague/omnipay-mollie/issues", "source": "https://github.com/thephpleague/omnipay-mollie/tree/v5.5.0"}, "funding": [{"url": "https://github.com/barryvdh", "type": "github"}], "time": "2022-06-28T18:26:20+00:00"}, {"name": "omnipay/paypal", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/thephpleague/omnipay-paypal.git", "reference": "519db61b32ff0c1e56cbec94762b970ee9674f65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/omnipay-paypal/zipball/519db61b32ff0c1e56cbec94762b970ee9674f65", "reference": "519db61b32ff0c1e56cbec94762b970ee9674f65", "shasum": ""}, "require": {"omnipay/common": "^3"}, "require-dev": {"omnipay/tests": "^3", "phpro/grumphp": "^0.14", "squizlabs/php_codesniffer": "^3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Omnipay\\PayPal\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Omnipay Contributors", "homepage": "https://github.com/thephpleague/omnipay-paypal/contributors"}], "description": "PayPal gateway for Omnipay payment processing library", "homepage": "https://github.com/thephpleague/omnipay-paypal", "keywords": ["gateway", "merchant", "omnipay", "pay", "payment", "paypal", "purchase"], "support": {"issues": "https://github.com/thephpleague/omnipay-paypal/issues", "source": "https://github.com/thephpleague/omnipay-paypal/tree/v3.0.2"}, "time": "2018-05-15T10:35:58+00:00"}, {"name": "ongr/elasticsearch-dsl", "version": "v7.2.2", "source": {"type": "git", "url": "https://github.com/ongr-io/ElasticsearchDSL.git", "reference": "c0789c35e8738c2b1138c8d33ec9fbcd740c909d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ongr-io/ElasticsearchDSL/zipball/c0789c35e8738c2b1138c8d33ec9fbcd740c909d", "reference": "c0789c35e8738c2b1138c8d33ec9fbcd740c909d", "shasum": ""}, "require": {"elasticsearch/elasticsearch": "^7.0", "php": "^7.4 || ^8.0", "symfony/serializer": "^5.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.2-dev"}}, "autoload": {"psr-4": {"ONGR\\ElasticsearchDSL\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ONGR team", "homepage": "http://www.ongr.io"}], "description": "Elasticsearch DSL library", "homepage": "http://ongr.io", "support": {"issues": "https://github.com/ongr-io/ElasticsearchDSL/issues", "source": "https://github.com/ongr-io/ElasticsearchDSL/tree/v7.2.2"}, "time": "2021-04-27T10:58:40+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "58c3f47f650c94ec05a151692652a868995d2938"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/58c3f47f650c94ec05a151692652a868995d2938", "reference": "58c3f47f650c94ec05a151692652a868995d2938", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2022-06-14T06:56:20+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "paragonie/sodium_compat", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/paragonie/sodium_compat.git", "reference": "e592a3e06d1fa0d43988c7c7d9948ca836f644b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/sodium_compat/zipball/e592a3e06d1fa0d43988c7c7d9948ca836f644b6", "reference": "e592a3e06d1fa0d43988c7c7d9948ca836f644b6", "shasum": ""}, "require": {"paragonie/random_compat": ">=1", "php": "^5.2.4|^5.3|^5.4|^5.5|^5.6|^7|^8"}, "require-dev": {"phpunit/phpunit": "^3|^4|^5|^6|^7|^8|^9"}, "suggest": {"ext-libsodium": "PHP < 7.0: Better performance, password hashing (Argon2i), secure memory management (memzero), and better security.", "ext-sodium": "PHP >= 7.0: Better performance, password hashing (Argon2i), secure memory management (memzero), and better security."}, "type": "library", "autoload": {"files": ["autoload.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Pure PHP implementation of libsodium; uses the PHP extension if it exists", "keywords": ["Authentication", "BLAKE2b", "ChaCha20", "ChaCha20-Poly1305", "Chapoly", "Curve25519", "Ed25519", "EdDSA", "Edwards-curve Digital Signature Algorithm", "Elliptic Curve <PERSON>-<PERSON><PERSON>", "Poly1305", "Pure-PHP cryptography", "RFC 7748", "RFC 8032", "Salpoly", "Salsa20", "X25519", "XChaCha20-Poly1305", "XSalsa20-Poly1305", "Xchacha20", "Xsalsa20", "aead", "cryptography", "ecdh", "elliptic curve", "elliptic curve cryptography", "encryption", "libsodium", "php", "public-key cryptography", "secret-key cryptography", "side-channel resistant"], "support": {"issues": "https://github.com/paragonie/sodium_compat/issues", "source": "https://github.com/paragonie/sodium_compat/tree/v1.20.0"}, "time": "2023-04-30T00:54:53+00:00"}, {"name": "paypal/paypal-checkout-sdk", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/paypal/Checkout-PHP-SDK.git", "reference": "ed6a55075448308b87a8b59dcb7fedf04a048cb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paypal/Checkout-PHP-SDK/zipball/ed6a55075448308b87a8b59dcb7fedf04a048cb1", "reference": "ed6a55075448308b87a8b59dcb7fedf04a048cb1", "shasum": ""}, "require": {"paypal/paypalhttp": "1.0.0"}, "require-dev": {"phpunit/phpunit": "^5.7"}, "type": "library", "autoload": {"psr-4": {"Sample\\": "samples/", "PayPalCheckoutSdk\\": "lib/PayPalCheckoutSdk"}}, "notification-url": "https://packagist.org/downloads/", "license": ["https://github.com/paypal/Checkout-PHP-SDK/blob/master/LICENSE"], "authors": [{"name": "PayPal", "homepage": "https://github.com/paypal/Checkout-PHP-SDK/contributors"}], "description": "PayPal's PHP SDK for Checkout REST APIs", "homepage": "http://github.com/paypal/Checkout-PHP-SDK/", "keywords": ["checkout", "orders", "payments", "paypal", "rest", "sdk"], "support": {"issues": "https://github.com/paypal/Checkout-PHP-SDK/issues", "source": "https://github.com/paypal/Checkout-PHP-SDK/tree/1.0.1"}, "abandoned": true, "time": "2019-11-07T23:16:44+00:00"}, {"name": "paypal/paypalhttp", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/paypal/paypalhttp_php.git", "reference": "1ad9b846a046f09d6135cbf2cbaa7701bbc630a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paypal/paypalhttp_php/zipball/1ad9b846a046f09d6135cbf2cbaa7701bbc630a3", "reference": "1ad9b846a046f09d6135cbf2cbaa7701bbc630a3", "shasum": ""}, "require": {"ext-curl": "*"}, "require-dev": {"phpunit/phpunit": "^5.7", "wiremock-php/wiremock-php": "1.43.2"}, "type": "library", "autoload": {"psr-4": {"PayPalHttp\\": "lib/PayPalHttp"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PayPal", "homepage": "https://github.com/paypal/paypalhttp_php/contributors"}], "support": {"issues": "https://github.com/paypal/paypalhttp_php/issues", "source": "https://github.com/paypal/paypalhttp_php/tree/1.0.0"}, "time": "2019-11-06T21:27:12+00:00"}, {"name": "php-amqplib/php-amqplib", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/php-amqplib/php-amqplib.git", "reference": "fb84e99589de0904a25861451b0552f806284ee5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-amqplib/php-amqplib/zipball/fb84e99589de0904a25861451b0552f806284ee5", "reference": "fb84e99589de0904a25861451b0552f806284ee5", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-sockets": "*", "php": "^7.2||^8.0", "phpseclib/phpseclib": "^2.0|^3.0"}, "conflict": {"php": "7.4.0 - 7.4.1"}, "replace": {"videlalvaro/php-amqplib": "self.version"}, "require-dev": {"ext-curl": "*", "nategood/httpful": "^0.2.20", "phpunit/phpunit": "^7.5|^9.5", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"PhpAmqpLib\\": "PhpAmqpLib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "Original Maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Formerly videlalvaro/php-amqplib.  This library is a pure PHP implementation of the AMQP protocol. It's been tested against RabbitMQ.", "homepage": "https://github.com/php-amqplib/php-amqplib/", "keywords": ["message", "queue", "rabbitmq"], "support": {"issues": "https://github.com/php-amqplib/php-amqplib/issues", "source": "https://github.com/php-amqplib/php-amqplib/tree/v3.6.0"}, "time": "2023-10-22T15:02:02+00:00"}, {"name": "php-http/client-common", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "880509727a447474d2a71b7d7fa5d268ddd3db4b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/880509727a447474d2a71b7d7fa5d268ddd3db4b", "reference": "880509727a447474d2a71b7d7fa5d268ddd3db4b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "symfony/options-resolver": "~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0 || ^6.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.20 || ^8.5.33 || ^9.6.7"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "support": {"issues": "https://github.com/php-http/client-common/issues", "source": "https://github.com/php-http/client-common/tree/2.7.0"}, "time": "2023-05-17T06:46:59+00:00"}, {"name": "php-http/discovery", "version": "1.19.1", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "57f3de01d32085fea20865f9b16fb0e69347c39e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/57f3de01d32085fea20865f9b16fb0e69347c39e", "reference": "57f3de01d32085fea20865f9b16fb0e69347c39e", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "symfony/phpunit-bridge": "^6.2"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.19.1"}, "time": "2023-07-11T07:02:26+00:00"}, {"name": "php-http/guzzle7-adapter", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-http/guzzle7-adapter.git", "reference": "fb075a71dbfa4847cf0c2938c4e5a9c478ef8b01"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/guzzle7-adapter/zipball/fb075a71dbfa4847cf0c2938c4e5a9c478ef8b01", "reference": "fb075a71dbfa4847cf0c2938c4e5a9c478ef8b01", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.0", "php": "^7.2 | ^8.0", "php-http/httplug": "^2.0", "psr/http-client": "^1.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0", "psr/http-client-implementation": "1.0"}, "require-dev": {"php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.0|^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.2.x-dev"}}, "autoload": {"psr-4": {"Http\\Adapter\\Guzzle7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Guzzle 7 HTTP Adapter", "homepage": "http://httplug.io", "keywords": ["Guzzle", "http"], "support": {"issues": "https://github.com/php-http/guzzle7-adapter/issues", "source": "https://github.com/php-http/guzzle7-adapter/tree/1.0.0"}, "time": "2021-03-09T07:35:15+00:00"}, {"name": "php-http/httplug", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/625ad742c360c8ac580fcc647a1541d29e257f67", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.0"}, "time": "2023-04-14T15:10:03+00:00"}, {"name": "php-http/message", "version": "1.16.0", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "47a14338bf4ebd67d317bf1144253d7db4ab55fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/47a14338bf4ebd67d317bf1144253d7db4ab55fd", "reference": "47a14338bf4ebd67d317bf1144253d7db4ab55fd", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.0"}, "time": "2023-05-17T06:43:38+00:00"}, {"name": "php-http/message-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/1.1.0"}, "abandoned": "psr/http-factory", "time": "2023-04-14T14:16:17+00:00"}, {"name": "php-http/promise", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "44a67cb59f708f826f3bec35f22030b3edb90119"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/44a67cb59f708f826f3bec35f22030b3edb90119", "reference": "44a67cb59f708f826f3bec35f22030b3edb90119", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.2.1"}, "time": "2023-11-08T12:57:08+00:00"}, {"name": "phpmailer/phpmailer", "version": "v6.8.1", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "e88da8d679acc3824ff231fdc553565b802ac016"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/e88da8d679acc3824ff231fdc553565b802ac016", "reference": "e88da8d679acc3824ff231fdc553565b802ac016", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "doctrine/annotations": "^1.2.6 || ^1.13.3", "php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.7.2", "yoast/phpunit-polyfills": "^1.0.4"}, "suggest": {"ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "ext-openssl": "Needed for secure SMTP sending and DKIM signing", "greew/oauth2-azure-provider": "Needed for Microsoft Azure XOAUTH2 authentication", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)", "thenetworg/oauth2-azure": "Needed for Microsoft XOAUTH2 authentication"}, "type": "library", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v6.8.1"}, "funding": [{"url": "https://github.com/Synchro", "type": "github"}], "time": "2023-08-29T08:26:30+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.33", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "33fa69b2514a61138dd48e7a49f99445711e0ad0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/33fa69b2514a61138dd48e7a49f99445711e0ad0", "reference": "33fa69b2514a61138dd48e7a49f99445711e0ad0", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.33"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2023-10-21T14:00:39+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "e616d01114759c4c489f93b099585439f795fe35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/e616d01114759c4c489f93b099585439f795fe35", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/1.0.2"}, "time": "2023-04-10T20:10:41+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "pusher/pusher-php-server", "version": "7.2.3", "source": {"type": "git", "url": "https://github.com/pusher/pusher-http-php.git", "reference": "416e68dd5f640175ad5982131c42a7a666d1d8e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pusher/pusher-http-php/zipball/416e68dd5f640175ad5982131c42a7a666d1d8e9", "reference": "416e68dd5f640175ad5982131c42a7a666d1d8e9", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "guzzlehttp/guzzle": "^7.2", "paragonie/sodium_compat": "^1.6", "php": "^7.3|^8.0", "psr/log": "^1.0|^2.0|^3.0"}, "require-dev": {"overtrue/phplint": "^2.3", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"Pusher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Library for interacting with the Pusher REST API", "keywords": ["events", "messaging", "php-pusher-server", "publish", "push", "pusher", "real time", "real-time", "realtime", "rest", "trigger"], "support": {"issues": "https://github.com/pusher/pusher-http-php/issues", "source": "https://github.com/pusher/pusher-http-php/tree/7.2.3"}, "time": "2023-05-17T16:00:06+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "react/promise", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.11.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-16T16:16:50+00:00"}, {"name": "sentry/sdk", "version": "3.5.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php-sdk.git", "reference": "cd91b752f07c4bab9fb3b173f81af68a78a78d6d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php-sdk/zipball/cd91b752f07c4bab9fb3b173f81af68a78a78d6d", "reference": "cd91b752f07c4bab9fb3b173f81af68a78a78d6d", "shasum": ""}, "require": {"http-interop/http-factory-guzzle": "^1.0", "sentry/sentry": "^3.19", "symfony/http-client": "^4.3|^5.0|^6.0"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "This is a metapackage shipping sentry/sentry with a recommended HTTP client.", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php-sdk/issues", "source": "https://github.com/getsentry/sentry-php-sdk/tree/3.5.0"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2023-06-12T17:50:36+00:00"}, {"name": "sentry/sentry", "version": "3.22.1", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "8859631ba5ab15bc1af420b0eeed19ecc6c9d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/8859631ba5ab15bc1af420b0eeed19ecc6c9d81d", "reference": "8859631ba5ab15bc1af420b0eeed19ecc6c9d81d", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "guzzlehttp/promises": "^1.5.3|^2.0", "jean85/pretty-package-versions": "^1.5|^2.0.4", "php": "^7.2|^8.0", "php-http/async-client-implementation": "^1.0", "php-http/client-common": "^1.5|^2.0", "php-http/discovery": "^1.15", "php-http/httplug": "^1.1|^2.0", "php-http/message": "^1.5", "php-http/message-factory": "^1.1", "psr/http-factory": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "symfony/options-resolver": "^3.4.43|^4.4.30|^5.0.11|^6.0|^7.0", "symfony/polyfill-php80": "^1.17"}, "conflict": {"php-http/client-common": "1.8.0", "raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19|3.4.*", "guzzlehttp/psr7": "^1.8.4|^2.1.1", "http-interop/http-factory-guzzle": "^1.0", "monolog/monolog": "^1.6|^2.0|^3.0", "nikic/php-parser": "^4.10.3", "php-http/mock-client": "^1.3", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.3", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5.14|^9.4", "symfony/phpunit-bridge": "^5.2|^6.0", "vimeo/psalm": "^4.17"}, "suggest": {"monolog/monolog": "Allow sending log messages to Sentry by using the included Monolog handler."}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Sentry\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "A PHP SDK for Sentry (http://sentry.io)", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php/issues", "source": "https://github.com/getsentry/sentry-php/tree/3.22.1"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2023-11-13T11:47:28+00:00"}, {"name": "sonata-project/google-authenticator", "version": "2.3.1", "source": {"type": "git", "url": "https://github.com/sonata-project/GoogleAuthenticator.git", "reference": "71a4189228f93a9662574dc8c65e77ef55061b59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sonata-project/GoogleAuthenticator/zipball/71a4189228f93a9662574dc8c65e77ef55061b59", "reference": "71a4189228f93a9662574dc8c65e77ef55061b59", "shasum": ""}, "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"symfony/phpunit-bridge": "^5.1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Google\\Authenticator\\": "src/", "Sonata\\GoogleAuthenticator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "http://www.devnetwork.net/viewtopic.php?f=50&t=94989"}], "description": "Library to integrate Google Authenticator into a PHP project", "homepage": "https://github.com/sonata-project/GoogleAuthenticator", "keywords": ["google authenticator"], "support": {"issues": "https://github.com/sonata-project/GoogleAuthenticator/issues", "source": "https://github.com/sonata-project/GoogleAuthenticator/tree/2.3.1"}, "funding": [{"url": "https://github.com/OskarStark", "type": "github"}, {"url": "https://github.com/VincentLanglet", "type": "github"}, {"url": "https://github.com/core23", "type": "github"}, {"url": "https://github.com/wbloszyk", "type": "github"}], "abandoned": true, "time": "2021-02-15T19:23:18+00:00"}, {"name": "spatie/crypto", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/spatie/crypto.git", "reference": "afaa9968bbd8e066d4e73b31671e3cc45467c4e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/crypto/zipball/afaa9968bbd8e066d4e73b31671e3cc45467c4e0", "reference": "afaa9968bbd8e066d4e73b31671e3cc45467c4e0", "shasum": ""}, "require": {"ext-openssl": "*", "php": "^7.4|^8.0"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/var-dumper": "^5.1"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Crypto\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Encrypting and signing data using private/public keys", "homepage": "https://github.com/spatie/crypto", "keywords": ["crypto", "spatie"], "support": {"source": "https://github.com/spatie/crypto/tree/2.0.2"}, "funding": [{"url": "https://github.com/sponsors/spatie", "type": "github"}, {"url": "https://spatie.be/open-source/support-us", "type": "other"}], "time": "2023-01-25T09:26:10+00:00"}, {"name": "stripe/stripe-php", "version": "v7.128.0", "source": {"type": "git", "url": "https://github.com/stripe/stripe-php.git", "reference": "c704949c49b72985c76cc61063aa26fefbd2724e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stripe/stripe-php/zipball/c704949c49b72985c76cc61063aa26fefbd2724e", "reference": "c704949c49b72985c76cc61063aa26fefbd2724e", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.6.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.5.0", "phpstan/phpstan": "^1.2", "phpunit/phpunit": "^5.7 || ^9.0", "squizlabs/php_codesniffer": "^3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Stripe\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Stripe and contributors", "homepage": "https://github.com/stripe/stripe-php/contributors"}], "description": "Stripe PHP Library", "homepage": "https://stripe.com/", "keywords": ["api", "payment processing", "stripe"], "support": {"issues": "https://github.com/stripe/stripe-php/issues", "source": "https://github.com/stripe/stripe-php/tree/v7.128.0"}, "time": "2022-05-05T17:18:02+00:00"}, {"name": "studio-42/elfinder", "version": "2.1.62", "source": {"type": "git", "url": "https://github.com/Studio-42/elFinder.git", "reference": "91133c14a341158beca82aede0b2138236a96bc8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Studio-42/elFinder/zipball/91133c14a341158beca82aede0b2138236a96bc8", "reference": "91133c14a341158beca82aede0b2138236a96bc8", "shasum": ""}, "require": {"php": ">=5.2"}, "suggest": {"barryvdh/elfinder-flysystem-driver": "VolumeDriver for elFinder to use Flysystem as a root.", "google/apiclient": "VolumeDriver GoogleDrive require `google/apiclient:^2.0.", "kunalvarma05/dropbox-php-sdk": "VolumeDriver `Dropbox`2 require `kunalvarma05/dropbox-php-sdk.", "nao-pon/flysystem-google-drive": "require in GoogleDrive network volume mounting with Flysystem."}, "type": "library", "autoload": {"classmap": ["php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://std42.ru"}, {"name": "Troex Nevelin", "email": "<EMAIL>", "homepage": "http://std42.ru"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://xoops.hypweb.net"}, {"name": "Community contributions", "homepage": "https://github.com/Studio-42/elFinder/contributors"}], "description": "File manager for web", "homepage": "http://elfinder.org", "support": {"issues": "https://github.com/Studio-42/elFinder/issues", "source": "https://github.com/Studio-42/elFinder/tree/2.1.62"}, "funding": [{"url": "https://github.com/nao-pon", "type": "github"}], "time": "2023-06-13T16:39:07+00:00"}, {"name": "symfony/cache", "version": "v5.4.34", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "b17f28169f7a2f2c0cddf2b044d729f5b75efe5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/b17f28169f7a2f2c0cddf2b044d729f5b75efe5a", "reference": "b17f28169f7a2f2c0cddf2b044d729f5b75efe5a", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^1.1.7|^2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<4.4", "symfony/http-kernel": "<4.4", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0|2.0", "symfony/cache-implementation": "1.0|2.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6|^2.0", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1", "psr/simple-cache": "^1.0|^2.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v5.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-12-18T14:56:06+00:00"}, {"name": "symfony/cache-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "64be4a7acb83b6f2bf6de9a02cee6dad41277ebc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/64be4a7acb83b6f2bf6de9a02cee6dad41277ebc", "reference": "64be4a7acb83b6f2bf6de9a02cee6dad41277ebc", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0|^3.0"}, "suggest": {"symfony/cache-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/console", "version": "v5.4.31", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "11ac5f154e0e5c4c77af83ad11ead9165280b92a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/11ac5f154e0e5c4c77af83ad11ead9165280b92a", "reference": "11ac5f154e0e5c4c77af83ad11ead9165280b92a", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.4.31"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-10-31T07:58:33+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.4.34", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "e3bca343efeb613f843c254e7718ef17c9bdf7a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/e3bca343efeb613f843c254e7718ef17c9bdf7a3", "reference": "e3bca343efeb613f843c254e7718ef17c9bdf7a3", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-12-27T21:12:56+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "f98b54df6ad059855739db6fcbc2d36995283fe1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/f98b54df6ad059855739db6fcbc2d36995283fe1", "reference": "f98b54df6ad059855739db6fcbc2d36995283fe1", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/http-client", "version": "v5.4.31", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "6cdf6cdf48101454f014a9ab4e0905f0b902389d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/6cdf6cdf48101454f014a9ab4e0905f0b902389d", "reference": "6cdf6cdf48101454f014a9ab4e0905f0b902389d", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-client-contracts": "^2.4", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2|^3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "2.4"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4.13|^5.1.5|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.31"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-10-29T12:33:05+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "ba6a9f0e8f3edd190520ee3b9a958596b6ca2e70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/ba6a9f0e8f3edd190520ee3b9a958596b6ca2e70", "reference": "ba6a9f0e8f3edd190520ee3b9a958596b6ca2e70", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-04-12T15:48:08+00:00"}, {"name": "symfony/http-foundation", "version": "v5.4.31", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "f84fd4fd8311a541ceb2ae3f257841d002450a90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/f84fd4fd8311a541ceb2ae3f257841d002450a90", "reference": "f84fd4fd8311a541ceb2ae3f257841d002450a90", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "~1.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/rate-limiter": "^5.2|^6.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v5.4.31"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-06T22:05:57+00:00"}, {"name": "symfony/options-resolver", "version": "v5.4.21", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "4fe5cf6ede71096839f0e4b4444d65dd3a7c1eb9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/4fe5cf6ede71096839f0e4b4444d65dd3a7c1eb9", "reference": "4fe5cf6ede71096839f0e4b4444d65dd3a7c1eb9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v5.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-02-14T08:03:56+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "6de50471469b8c9afc38164452ab2b6170ee71c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/6de50471469b8c9afc38164452ab2b6170ee71c1", "reference": "6de50471469b8c9afc38164452ab2b6170ee71c1", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "875e90aeea2777b6f135677f618529449334a612"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/875e90aeea2777b6f135677f618529449334a612", "reference": "875e90aeea2777b6f135677f618529449334a612", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92", "reference": "8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "42292d99c55abe617799667f454222c54c60e229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/42292d99c55abe617799667f454222c54c60e229", "reference": "42292d99c55abe617799667f454222c54c60e229", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-07-28T09:04:16+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "fe2f306d1d9d346a7fee353d0d5012e401e984b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/fe2f306d1d9d346a7fee353d0d5012e401e984b5", "reference": "fe2f306d1d9d346a7fee353d0d5012e401e984b5", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "6caa57379c4aec19c0a12a38b59b26487dcfe4b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/6caa57379c4aec19c0a12a38b59b26487dcfe4b5", "reference": "6caa57379c4aec19c0a12a38b59b26487dcfe4b5", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/serializer", "version": "v5.4.31", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "15574cfa408a6082b6d66c2b6922f95db6cab26d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/15574cfa408a6082b6d66c2b6922f95db6cab26d", "reference": "15574cfa408a6082b6d66c2b6922f95db6cab26d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/annotations": "<1.12", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<4.4", "symfony/property-access": "<5.4", "symfony/property-info": "<5.4.24|>=6,<6.2.11", "symfony/uid": "<5.3", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/form": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4.24|^6.2.11", "symfony/uid": "^5.3|^6.0", "symfony/validator": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0", "symfony/var-exporter": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/mime": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/var-exporter": "For using the metadata compiler.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v5.4.31"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-10-31T07:58:33+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-30T19:17:29+00:00"}, {"name": "symfony/string", "version": "v5.4.31", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "2765096c03f39ddf54f6af532166e42aaa05b24b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/2765096c03f39ddf54f6af532166e42aaa05b24b", "reference": "2765096c03f39ddf54f6af532166e42aaa05b24b", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v5.4.31"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-09T08:19:44+00:00"}, {"name": "symfony/translation", "version": "v5.4.31", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "ba72f72fceddf36f00bd495966b5873f2d17ad8f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/ba72f72fceddf36f00bd495966b5873f2d17ad8f", "reference": "ba72f72fceddf36f00bd495966b5873f2d17ad8f", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^2.3"}, "conflict": {"symfony/config": "<4.4", "symfony/console": "<5.3", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4"}, "provide": {"symfony/translation-implementation": "2.3"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v5.4.31"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-03T16:16:43+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "136b19dd05cdf0709db6537d058bcab6dd6e2dbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/136b19dd05cdf0709db6537d058bcab6dd6e2dbe", "reference": "136b19dd05cdf0709db6537d058bcab6dd6e2dbe", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T16:58:25+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.29", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "6172e4ae3534d25ee9e07eb487c20be7760fcc65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/6172e4ae3534d25ee9e07eb487c20be7760fcc65", "reference": "6172e4ae3534d25ee9e07eb487c20be7760fcc65", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/uid": "^5.1|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v5.4.29"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-09-12T10:09:58+00:00"}, {"name": "symfony/var-exporter", "version": "v5.4.32", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "fdb022f0d3d41df240c18e2eb9a117c430f06add"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/fdb022f0d3d41df240c18e2eb9a117c430f06add", "reference": "fdb022f0d3d41df240c18e2eb9a117c430f06add", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/var-dumper": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v5.4.32"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-16T19:33:05+00:00"}, {"name": "tecnickcom/tcpdf", "version": "6.6.5", "source": {"type": "git", "url": "https://github.com/tecnickcom/TCPDF.git", "reference": "5fce932fcee4371865314ab7f6c0d85423c5c7ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/TCPDF/zipball/5fce932fcee4371865314ab7f6c0d85423c5c7ce", "reference": "5fce932fcee4371865314ab7f6c0d85423c5c7ce", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"classmap": ["config", "include", "tcpdf.php", "tcpdf_parser.php", "tcpdf_import.php", "tcpdf_barcodes_1d.php", "tcpdf_barcodes_2d.php", "include/tcpdf_colors.php", "include/tcpdf_filters.php", "include/tcpdf_font_data.php", "include/tcpdf_fonts.php", "include/tcpdf_images.php", "include/tcpdf_static.php", "include/barcodes/datamatrix.php", "include/barcodes/pdf417.php", "include/barcodes/qrcode.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "TCPDF is a PHP class for generating PDF documents and barcodes.", "homepage": "http://www.tcpdf.org/", "keywords": ["PDFD32000-2008", "TCPDF", "barcodes", "datamatrix", "pdf", "pdf417", "qrcode"], "support": {"issues": "https://github.com/tecnickcom/TCPDF/issues", "source": "https://github.com/tecnickcom/TCPDF/tree/6.6.5"}, "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_donations&currency_code=GBP&business=<EMAIL>&item_name=donation%20for%20tcpdf%20project", "type": "custom"}], "time": "2023-09-06T15:09:26+00:00"}, {"name": "twilio/sdk", "version": "6.44.4", "source": {"type": "git", "url": "https://github.com/twilio/twilio-php.git", "reference": "08aad5f377e2245b9cd7508e7762d95e7392fa4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twilio/twilio-php/zipball/08aad5f377e2245b9cd7508e7762d95e7392fa4d", "reference": "08aad5f377e2245b9cd7508e7762d95e7392fa4d", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.3 || ^7.0", "phpunit/phpunit": ">=7.0 < 10"}, "suggest": {"guzzlehttp/guzzle": "An HTTP client to execute the API requests"}, "type": "library", "autoload": {"psr-4": {"Twilio\\": "src/<PERSON>wi<PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Twilio API Team", "email": "<EMAIL>"}], "description": "A PHP wrapper for Twilio's API", "homepage": "https://github.com/twilio/twilio-php", "keywords": ["api", "sms", "twi<PERSON>"], "time": "2023-02-22T19:59:53+00:00"}, {"name": "unleash/client", "version": "v2.2.074", "source": {"type": "git", "url": "https://github.com/Unleash/unleash-client-php.git", "reference": "16ec8d41a5a3691d7bf3edce385267c0067d2eac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Unleash/unleash-client-php/zipball/16ec8d41a5a3691d7bf3edce385267c0067d2eac", "reference": "16ec8d41a5a3691d7bf3edce385267c0067d2eac", "shasum": ""}, "require": {"ext-json": "*", "lastguest/murmurhash": "^2.1", "php": "^7.4", "php-http/discovery": "^1.14", "psr/http-client": "^1.0", "psr/http-client-implementation": "^1.0", "psr/http-factory": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/http-message": "^1.0 | ^2.0", "psr/simple-cache": "^1.0 | ^2.0 | ^3.0", "psr/simple-cache-implementation": "^1.0 | ^2.0 | ^3.0", "symfony/event-dispatcher": "^5.0 | ^6.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "guzzlehttp/guzzle": "^7.0", "jetbrains/phpstorm-attributes": "^1.0", "nyholm/psr7": "^1.0", "phpstan/phpstan": "^1.2", "phpunit/phpunit": "^9.5", "rector/rector": "^0.14.8", "symfony/cache": "^5.0 | ^6.0", "symfony/http-client": "^5.0 | ^6.0"}, "suggest": {"guzzlehttp/guzzle": "A http client implementation (PSR-17 and PSR-18)", "nyholm/psr7": "Needed when you use symfony/http-client", "symfony/event-dispatcher": "Needed when you want to react to events from Unleash", "symfony/http-client": "A http client implementation (PSR-17 and PSR-18)", "unleash/symfony-client-bundle": "The Symfony bundle for this library"}, "type": "library", "autoload": {"psr-4": {"Unleash\\Client\\": "src/", "Unleash\\Client\\PhpstanRules\\": "phpstan/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/Unleash/unleash-client-php/issues", "source": "https://github.com/Unleash/unleash-client-php/tree/v2.2.074"}, "time": "2023-11-27T08:31:22+00:00"}, {"name": "voku/portable-ascii", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "87337c91b9dfacee02452244ee14ab3c43bc485a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/87337c91b9dfacee02452244ee14ab3c43bc485a", "reference": "87337c91b9dfacee02452244ee14ab3c43bc485a", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/1.6.1"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "time": "2022-01-24T18:55:24+00:00"}, {"name": "will<PERSON><PERSON>/email-reply-parser", "version": "2.10.0", "source": {"type": "git", "url": "https://github.com/willdurand/EmailReplyParser.git", "reference": "f25f8c6c3cb876112e3857eb3da15e38e44e4725"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/EmailReplyParser/zipball/f25f8c6c3cb876112e3857eb3da15e38e44e4725", "reference": "f25f8c6c3cb876112e3857eb3da15e38e44e4725", "shasum": ""}, "require": {"php": ">=7.4.0"}, "require-dev": {"symfony/phpunit-bridge": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"EmailReplyParser\\": "src/EmailReplyParser"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Port of the cool GitHub's EmailReplyParser library in PHP", "keywords": ["email", "reply-parser"], "support": {"issues": "https://github.com/willdurand/EmailReplyParser/issues", "source": "https://github.com/willdurand/EmailReplyParser/tree/2.10.0"}, "time": "2022-01-30T20:56:36+00:00"}, {"name": "xemlock/htmlpurifier-html5", "version": "v0.1.11", "source": {"type": "git", "url": "https://github.com/xemlock/htmlpurifier-html5.git", "reference": "f0d563f9fd4a82a3d759043483f9a94c0d8c2255"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/xemlock/htmlpurifier-html5/zipball/f0d563f9fd4a82a3d759043483f9a94c0d8c2255", "reference": "f0d563f9fd4a82a3d759043483f9a94c0d8c2255", "shasum": ""}, "require": {"ezyang/htmlpurifier": "^4.8", "php": ">=5.2"}, "require-dev": {"php-coveralls/php-coveralls": "^1.1|^2.1", "phpunit/phpunit": ">=4.7 <8.0"}, "type": "library", "autoload": {"classmap": ["library/HTMLPurifier/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "xem<PERSON>", "email": "<EMAIL>"}], "description": "HTML5 element definitions for HTML Purifier", "keywords": ["HTML5", "Purifier", "html", "htmlpurifier", "security", "tidy", "validator", "xss"], "support": {"issues": "https://github.com/xemlock/htmlpurifier-html5/issues", "source": "https://github.com/xemlock/htmlpurifier-html5/tree/master"}, "time": "2019-08-07T17:19:21+00:00"}, {"name": "z<PERSON>son/mail-mime-parser", "version": "1.3.3", "source": {"type": "git", "url": "https://github.com/zbateson/mail-mime-parser.git", "reference": "244b70963945293b5225da2553239a06987d1a11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zbateson/mail-mime-parser/zipball/244b70963945293b5225da2553239a06987d1a11", "reference": "244b70963945293b5225da2553239a06987d1a11", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7.0|^2.0", "php": ">=5.4", "zbateson/mb-wrapper": "^1.0.1", "zbateson/stream-decorators": "^1.0.6"}, "require-dev": {"mikey179/vfsstream": "^1.6.0", "sanmai/phpunit-legacy-adapter": "^6.3 || ^8"}, "suggest": {"ext-iconv": "For best support/performance", "ext-mbstring": "For best support/performance"}, "type": "library", "autoload": {"psr-4": {"ZBateson\\MailMimeParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Contributors", "homepage": "https://github.com/zbateson/mail-mime-parser/graphs/contributors"}], "description": "MIME email message parser", "homepage": "https://mail-mime-parser.org", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email", "mail", "mailparse", "mime", "mimeparse", "parser", "php-imap"], "support": {"docs": "https://mail-mime-parser.org/#usage-guide", "issues": "https://github.com/zbateson/mail-mime-parser/issues", "source": "https://github.com/zbateson/mail-mime-parser"}, "funding": [{"url": "https://github.com/zbateson", "type": "github"}], "time": "2021-11-06T00:47:49+00:00"}, {"name": "zbateson/mb-wrapper", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/zbateson/mb-wrapper.git", "reference": "faf35dddfacfc5d4d5f9210143eafd7a7fe74334"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zbateson/mb-wrapper/zipball/faf35dddfacfc5d4d5f9210143eafd7a7fe74334", "reference": "faf35dddfacfc5d4d5f9210143eafd7a7fe74334", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-iconv": "^1.9", "symfony/polyfill-mbstring": "^1.9"}, "require-dev": {"friendsofphp/php-cs-fixer": "*", "phpstan/phpstan": "*", "phpunit/phpunit": "<=9.0"}, "suggest": {"ext-iconv": "For best support/performance", "ext-mbstring": "For best support/performance"}, "type": "library", "autoload": {"psr-4": {"ZBateson\\MbWrapper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Wrapper for mbstring with fallback to iconv for encoding conversion and string manipulation", "keywords": ["charset", "encoding", "http", "iconv", "mail", "mb", "mb_convert_encoding", "mbstring", "mime", "multibyte", "string"], "support": {"issues": "https://github.com/zbateson/mb-wrapper/issues", "source": "https://github.com/zbateson/mb-wrapper/tree/1.2.0"}, "funding": [{"url": "https://github.com/zbateson", "type": "github"}], "time": "2023-01-11T23:05:44+00:00"}, {"name": "zbateson/stream-decorators", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/zbateson/stream-decorators.git", "reference": "783b034024fda8eafa19675fb2552f8654d3a3e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zbateson/stream-decorators/zipball/783b034024fda8eafa19675fb2552f8654d3a3e9", "reference": "783b034024fda8eafa19675fb2552f8654d3a3e9", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.9 | ^2.0", "php": ">=7.2", "zbateson/mb-wrapper": "^1.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "*", "phpstan/phpstan": "*", "phpunit/phpunit": "<10.0"}, "type": "library", "autoload": {"psr-4": {"ZBateson\\StreamDecorators\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "PHP psr7 stream decorators for mime message part streams", "keywords": ["base64", "charset", "decorators", "mail", "mime", "psr7", "quoted-printable", "stream", "uuencode"], "support": {"issues": "https://github.com/zbateson/stream-decorators/issues", "source": "https://github.com/zbateson/stream-decorators/tree/1.2.1"}, "funding": [{"url": "https://github.com/zbateson", "type": "github"}], "time": "2023-05-30T22:51:52+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {"omnipay/braintree": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.4.0", "ext-imap": "*", "ext-sockets": "*"}, "platform-dev": [], "plugin-api-version": "2.6.0"}
stages:
  - deploy
  - merge

variables:
  GIT_STRATEGY: none

# Deploy testing on stg server
deploy tesing:
  stage: deploy
  tags:
    - 102-stg
  only:
    - develop
  script:
    - cd /var/www/crm
    - git pull origin develop
    - /usr/bin/php7.4 /usr/bin/composer install
    - source "$HOME/.nvm/nvm.sh"
    - nvm use 15
    - npm install
    - npm run build-assets
  environment:
    name: testing
    url: https://crmdev.topdev.asia

# Deploy production on inhouse server
deploy production:
  stage: deploy
  tags:
    - 20-inhouse
  only:
    - main
  before_script:
    - source "$HOME/.nvm/nvm.sh"
    - nvm use 15
  script:
    - cd /var/www/html/crm
    - git pull origin main
    - npm install
    - npm run build-assets
    - /usr/bin/php8.2 /opt/cachetool/cachetool.phar opcache:reset --fcgi=/run/php/php-crm.sock
  environment:
    name: production
    url: https://crm.topdev.asia

# Merge main into develop
merge main to develop:
  stage: merge
  when: on_success
  tags:
    - 102-stg
  only:
    - main
  script:
    - rm -rf /var/www/crm-sync
    - git clone ssh://*******************:8229/applancer/ams.git /var/www/crm-sync
    - cd /var/www/crm-sync
    - git checkout develop
    - |
      git merge --no-ff main -m "chore: auto-merge main into develop after deployment" || { echo "Merge conflict detected!"; exit 1; }
    - git push origin develop

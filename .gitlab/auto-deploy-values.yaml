hpa:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 600

ingress:
  className: nginx
  annotations:
    nginx.ingress.kubernetes.io/client-body-buffer-size: "10m"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
  path: "/"
  tls:
    enabled: false
#  canary:
#    weight: 0

livenessProbe:
  enabled: true
  path: "/healthz"
readinessProbe:
  enabled: false
  path: "/readiness"

#hostAliases: &hostAliases
#- ip: X.X.X.X
#  hostnames:
#  - dns1.DOMAIN1
#  - dns2.DOMAIN2

resources:
  requests:
   cpu: 100m
   memory: 128Mi

## Configure extra Volumes
## ref: https://kubernetes.io/docs/concepts/storage/volumes/
#
extraVolumes: &extraVolumes
- name: app-config-secret-volume
  secret:
    secretName: "{{ .Values.application.secretName }}"
- name: env-php-secret-volume
  secret:
      secretName: "{{ .Values.application.secretName }}"
# - name: config-volume
#   configMap:
#     name: test-config

## Configure extra Volumes mounts
## ref: https://kubernetes.io/docs/concepts/storage/volumes/
#
extraVolumeMounts: &extraVolumeMounts
- name: app-config-secret-volume
  mountPath: /workspace/application/config/app-config.php
  subPath: APP_CONFIG
- name: env-php-secret-volume
  mountPath: /workspace/env.php
  subPath: ENV_PHP
# - name: config-volume
#   mountPath: /app/config.yaml
#   subPath: config.yaml

#extraEnvFrom:
#- secretRef:
#    name: extra-{{ .Values.gitlab.env }}-secret


workers:
  crm-worker:
    replicaCount: 1
    # terminationGracePeriodSeconds: 60
    # hostAliases: *hostAliases
    command: [ "/cnb/process/crm-worker" ]
    # nodeSelector: {}
    # tolerations: []
    # initContainers: []
    livenessProbe: &workerLivenessProbe
      #command: [ "/bin/sh", "-c", "ps -efww | grep -v grep | grep 'RabbitMq'" ]
      command: [ "ps", "-f", "1" ]
      initialDelaySeconds: 15
      timeoutSeconds: 15
      probeType: "exec"
    readinessProbe: *workerLivenessProbe
    extraVolumes: *extraVolumes
    extraVolumeMounts: *extraVolumeMounts

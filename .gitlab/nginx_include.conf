    index index.php index.html;

    charset utf-8;
    client_body_buffer_size 10M;
    client_max_body_size 100m;

    location / {
        autoindex off;
        try_files $uri $uri/ /index.php?$args;
    }

    location = /favicon.ico { access_log off; log_not_found off; }

    # for people with app root as doc root, restrict access to a few things
    location ~ ^/(composer\.(json|lock|phar)$|Procfile$|application/vendor/bin/) {
        deny all;
    }

    # Allow access to Clockwork web UI files but deny access to other vendor files
    location ~ ^/application/vendor/(?!itsgoingd/clockwork).* {
        # Deny access to all other vendor files
        deny all;
    }

     # deny access to .htaccess files
     location ~ /\.(?!well-known).* {
         deny all;
     }

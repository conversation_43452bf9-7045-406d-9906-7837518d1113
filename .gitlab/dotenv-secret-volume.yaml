## Configure extra Volumes
## ref: https://kubernetes.io/docs/concepts/storage/volumes/
# sed "s/secretName: env\.secret/secretName: ${CI_ENVIRONMENT_SLUG}-secret/g ${SED_SCRIPT}" .gitlab/dotenv-secret-volume.yaml > .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml
extraVolumes: &extraVolumes
  - name: app-config-secret-volume
    secret:
      secretName: env.secret
  - name: env-php-secret-volume
    secret:
      secretName: env.secret

.worker: &worker
  extraVolumes: *extraVolumes

workers:
  crm-worker: *worker
